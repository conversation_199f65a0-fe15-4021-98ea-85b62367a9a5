* {
  box-sizing: border-box;
  word-break: break-all;
}

body {
  font-family: pingfang sc;
  color: #333;
  font-size: 28rpx;
}

image {
  width: 100%;
}

uni-input {
  font-size: 28rpx;
}

$color1: #ee4133;
$color2: #b0b0b0;
$color3: #f1e201;
$color4: #fff;
$color5: #000;

$bgc1: #0a0a0a;
$bgc2: #2e2e2e;
$bgc3: #212121;
$bgc4: #fff;
$bgc5: #000;

$up: #42c467;
$down: #ef4034;

$padding: 0 30rpx 60rpx;

.color1 {
  color: $color1;
}
.color2 {
  color: $color2;
}
.color3 {
  color: $color3;
}
.color4 {
  color: $color4;
}
.color5 {
  color: $color5;
}
.color6 {
  color: #61541d;
}
.color7 {
  color: #323232;
}
.bgc1 {
  background-color: $bgc1;
}
.bgc2 {
  background-color: $bgc2;
}
.bgc3 {
  background-color: $bgc3;
}
.bgc4 {
  background-color: $bgc4;
}
.bgc5 {
  background-color: $bgc5;
}

.up {
  color: $up;
}
.down {
  color: $down;
}

.bdr1 {
  border-radius: 24rpx;
}
.bdr2 {
  border-radius: 24rpx;
}
.bdr3 {
  border-radius: 30rpx;
}
.bdr4 {
  border-radius: 200rpx;
}

.bold {
  font-weight: bold;
}
.initial {
  font-weight: initial;
}

.fl {
  float: left;
}
.fr {
  float: right;
}

.cell-hover-class {
  background-color: #f7f8f9;
}

.u-relative {
  position: relative;
}

.u-absolute {
  position: absolute;
}

.u-flex {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
}

.u-flex-wrap {
  flex-wrap: wrap;
}

.u-flex-nowrap {
  flex-wrap: nowrap;
}

.u-col-center {
  align-items: center;
}

.u-col-top {
  align-items: flex-start;
}

.u-col-bottom {
  align-items: flex-end;
}

.u-row-center {
  justify-content: center;
}

.u-row-left {
  justify-content: flex-start;
}

.u-row-right {
  justify-content: flex-end;
}

.u-row-between {
  justify-content: space-between;
}

.u-row-around {
  justify-content: space-around;
}

.u-text-left {
  text-align: left;
}

.u-text-center {
  text-align: center;
}

.u-text-right {
  text-align: right;
}

.u-flex-col {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
}

// 定义字体(rpx)单位
@for $i from 20 through 60 {
  .u-font-#{$i} {
    font-size: $i + rpx;
  }
}

// 定义flex等分
@for $i from 0 through 12 {
  .u-flex-#{$i} {
    flex: $i;
  }
}

// 定义内外边距
@for $i from 0 through 80 {
  // 只要双数和能被5除尽的数
  @if $i % 2 == 0 or $i % 5 == 0 {
    // 得出：u-margin-30或者u-m-30
    .u-m-#{$i} {
      margin: $i + rpx;
    }

    // 得出：u-padding-30或者u-p-30
    .u-p-#{$i} {
      padding: $i + rpx;
    }

    @each $short, $long in l left, t top, r right, b bottom {
      // 缩写版，结果如： u-m-l-30
      // 定义外边距
      .u-m-#{$short}-#{$i} {
        margin-#{$long}: $i + rpx !important;
      }

      // 定义内边距
      .u-p-#{$short}-#{$i} {
        padding-#{$long}: $i + rpx !important;
      }
    }
  }
}
.u-border-bottom {
  border-bottom-color: #403c3c !important;
}
.bgc6 {
  background-color: #f1e201;
}
