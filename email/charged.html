<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <title>PinkWallet</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        background-color: #ffffff;
        font-family: "Arial", sans-serif;
        color: #000000;
      }
      .container {
        max-width: 795px;
        /* margin: 0 auto; */
        /* padding: 24px; */

        background-color: #ffffff;
        border-radius: 8px;
        box-sizing: border-box;
      }
      .header {
        background-color: #17191e;
        padding: 89px 0 47px 53px;
        text-align: left;
      }
      .header img {
        height: 36px;
      }
      .content {
        /* padding: 40px 24px; */
        padding: 92px 0 0 53px;
        text-align: left;
      }
      .type {
        font-family: MiSans;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        color: #000;
      }
      .content .title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 24px;
        line-height: 42px;
      }
      .content .text {
        /* margin-bottom: 12px; */
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        color: #000;
        opacity: 0.5;
      }
      .over {
        margin-top: 16px;
        width: 513px;
        white-space: wrap;
      }
      .code {
        font-family: MiSans;
        font-weight: 500;
        font-size: 43.19px;
        line-height: 100%;
        margin: 6px 0 16px 0;
        color: #ff7ea0;
      }
      .footer {
        text-align: center;
        padding: 32px 0;
      }
      .app-links img {
        width: 130px;
        margin: 8px;
      }
      .store-buttons {
        display: flex;
        gap: 8px;
        /* justify-content: center; */
        margin: 18px 0 0 53px;
      }

      .store-button {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 1);
        width: 213px;
        height: 76px;
        overflow: hidden;
        border-radius: 12px;
        background: transparent;
        cursor: pointer;
        text-decoration: none;
      }

      /* .store-button:hover {
          background: #fff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .store-button:hover div span:nth-of-type(1) {
          color: rgba(0, 0, 0, 0.7);
      }

      .store-button:hover div span:nth-of-type(2) {
          color: #000;
      } */

      .store-button img {
        width: 31px;
        height: 32px;
        transition: opacity 0.3s ease-in-out;
      }

      .store-button div {
        margin-left: 12px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .store-button div span:nth-of-type(1) {
        font-family: MiSans-normal, Arial, sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0px;
        text-transform: capitalize;
        color: rgba(0, 0, 0, 0.7);
        transition: color 0.3s ease;
      }

      .store-button div span:nth-of-type(2) {
        font-family: MiSans-medium, Arial, sans-serif;
        font-weight: 500;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: 0px;
        text-transform: capitalize;
        color: #000;
        transition: color 0.3s ease;
      }
      .text2 {
        font-family: MiSans;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        color: #000;
        opacity: 0.5;
        margin: 153px 0 0 53px;
      }
      .notice {
        font-family: MiSans;
        font-weight: 500;
        font-size: 28px;
        line-height: 100%;
        color: #000;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <img
      src=https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379525576384667648.png
      alt="PinkWallet Logo" />
    </div>
    <div class="container">
      <div class="content">
        <div class="title">PinkWallet充值到账通知</div>
        <div class="notice">充值 999.99USDT，资金已经到账。</div>
        <div class="text over"> PinkWallet 团队 </div>
      </div>
      <div class="text2">下载PinkWallet APP，开始随时交易。</div>

      <div class="store-buttons">
        <div class="store-button">
          <img
            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250327/4e3624591d6c397f1559893919411032_124x128.png"
            alt="App Store"
          />
          <div>
            <span>Download on the</span>
            <span>App Store</span>
          </div>
        </div>
        <div class="store-button">
          <img
            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250324/b11ff143f668e2b79d0899aa7b7fbdf4_124x128.png"
            alt="Google Play"
          />
          <div>
            <span>get on</span>
            <span>Google play</span>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
