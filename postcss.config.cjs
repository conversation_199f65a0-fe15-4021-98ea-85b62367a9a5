module.exports = {
  plugins: {
    // "postcss-pxtorem": {
    //   rootValue: 192, // 基准值，通常根据设计稿宽度计算（例如 750px 设计稿可以用 75）
    //   propList: ["*"], // 允许所有属性转换为 rem
    //   selectorBlackList: [".norem"], // 过滤掉 .norem 开头的 class
    //   minPixelValue: 2, // 只转换大于 2px 的数值
    //   exclude: [/node_modules/], // 排除 node_modules
    //   // propBlackList: ['font-size'], // 如果不想转换某些属性，可以使用黑名单
    // },
  },
};
