import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  //适配屏幕大小
  css: {
    loaderOptions: {
      postcss: {
        postcssOptions: {
          plugins: [
            // require("postcss-pxtorem")({
              // rootValue: 100,
              // propList: ["*"],
              // selectorBlackList: ["weui", "mu"],
            // }),
          ],
        },
      },   
    },
  },
});   
