# Pink Wallet

## 项目介绍
Pink Wallet 是一个基于 Vue 3 的现代化钱包应用，提供安全、便捷的数字资产管理服务。

## 技术栈
- 核心框架：Vue 3.5.13
- 构建工具：Vite 3.0.0
- 路由管理：Vue Router 4.0.3
- 国际化：Vue I18n 11.1.2
- 样式处理：SCSS
- 移动端适配：amfe-flexible + lib-flexible-computer
- 动画效果：vue3-marquee 4.2.2

## 项目结构
```
src/
├── assets/        # 静态资源
├── components/    # 公共组件
├── views/         # 页面组件
├── router/        # 路由配置
├── locales/       # 国际化文件
└── style.css      # 全局样式
```

## 开发环境要求
- Node.js >= 14.0.0
- Yarn >= 1.22.0

## 安装依赖
```bash
yarn install
```

## 开发命令
```bash
# 启动开发服务器
yarn dev

# 构建生产版本
yarn build

# 预览生产构建
yarn preview
```

## 注意事项
1. 样式规范
   - 使用 SCSS 预处理器
   - 类名使用下划线命名法（例如：`user_info`）
   - 移动端适配使用 rem 单位，通过 postcss-pxtorem 自动转换

2. 开发规范
   - 组件使用 `<script setup>` 语法
   - 路由采用 Hash 模式
   - 页面切换会自动滚动到顶部

3. 国际化
   - 所有文本内容需要支持多语言
   - 语言文件统一放在 `src/locales` 目录下

4. 性能优化
   - 路由组件使用懒加载
   - 静态资源需要合理压缩
   - 注意控制包体积大小

## 浏览器支持
- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79

## 贡献指南
1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 许可证
[MIT License](LICENSE)
