{"name": "d2-admin", "version": "1.20.1", "scripts": {"serve": "vue-cli-service serve --open", "start": "npm run serve", "dev": "npm run serve", "build:prod": "vue-cli-service build --mode prod", "build:test": "vue-cli-service build --mode test", "build:preview": "NODE_OPTIONS=--max_old_space_size=4096 vue-cli-service build --mode preview", "lint": "vue-cli-service lint --fix", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"axios": "^0.19.0", "axios-mock-adapter": "^1.18.1", "better-scroll": "^1.15.2", "blueimp-md5": "^2.19.0", "core-js": "^3.4.3", "dayjs": "^1.8.17", "echarts": "^5.3.3", "element-ui": "2.15.6", "faker": "^4.1.0", "flex.css": "^1.1.7", "fuse.js": "^5.2.3", "hotkeys-js": "^3.7.3", "html2canvas": "^1.4.1", "js-audio-recorder": "^1.0.7", "js-cookie": "^2.2.1", "jsrsasign": "^10.5.20", "lodash": "^4.17.19", "lowdb": "^1.0.0", "nprogress": "^0.2.0", "screenfull": "^5.0.2", "sortablejs": "^1.10.1", "three": "^0.139.0", "three-obj-mtl-loader": "^1.0.3", "three-orbit-controls": "^82.1.0", "ua-parser-js": "^0.7.20", "video.js": "^7.18.1", "vue": "^2.6.11", "vue-i18n": "^8.15.1", "vue-router": "^3.1.3", "vuex": "^3.1.2", "wangeditor": "^4.7.15"}, "devDependencies": {"@d2-projects/vue-filename-injector": "^1.1.0", "@kazupon/vue-i18n-loader": "^0.5.0", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-jest": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.2", "babel-eslint": "^10.0.3", "compression-webpack-plugin": "^3.0.1", "cz-conventional-changelog": "^3.2.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "sass": "^1.23.7", "sass-loader": "^8.0.0", "svg-sprite-loader": "^4.1.6", "text-loader": "^0.0.1", "vue-cli-plugin-i18n": "^1.0.1", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "^1.3.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "repository": {"type": "git", "url": "https://github.com/d2-projects/d2-admin.git"}}