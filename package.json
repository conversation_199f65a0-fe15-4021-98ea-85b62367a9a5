{"name": "pinkwallet_active", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"@aliyun-sls/web-track-browser": "^0.0.3", "axios": "^0.27.2", "blueimp-md5": "^2.19.0", "clipboard": "^2.0.11", "core-js": "^3.8.3", "dom-to-image": "^2.6.0", "echarts": "^5.5.0", "element-plus": "^2.2.6", "howler": "^2.2.4", "html2canvas": "^1.4.1", "jsrsasign": "^10.5.25", "moment": "^2.29.3", "qrcode": "^1.5.4", "qrcode.vue": "^3.3.3", "tim-js-sdk": "^2.26.6", "tim-upload-plugin": "^1.1.0", "vant": "^3.5.2", "vconsole": "^3.14.6", "vue": "^3.2.13", "vue-i18n": "^11.1.5", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-router": "~4.5.19", "@vue/cli-plugin-vuex": "~4.5.19", "@vue/cli-service": "~4.5.19", "eslint-config-prettier": "^8.3.0", "javascript-obfuscator": "^4.0.0", "lint-staged": "^11.1.2", "prettier": "^2.4.1", "sass": "^1.32.7", "sass-loader": "^10.1.1", "webpack-obfuscator": "^2"}, "gitHooks": {}}