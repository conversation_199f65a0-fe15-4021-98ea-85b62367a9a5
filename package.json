{"name": "pink_pc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@mdi/font": "^7.4.47", "amfe-flexible": "^2.2.1", "axios": "^1.9.0", "blueimp-md5": "^2.19.0", "echarts": "^5.6.0", "html2canvas": "^1.4.1", "jsrsasign": "^11.1.0", "lib-flexible-computer": "^1.0.2", "qrcode": "^1.5.4", "swiper": "^11.2.8", "vue": "^3.5.13", "vue-i18n": "^11.1.2", "vue-router": "^4.0.3", "vue3-marquee": "^4.2.2", "vuetify": "^3.8.7"}, "devDependencies": {"@vitejs/plugin-vue": "2.3.3", "postcss-pxtorem": "^6.1.0", "sass": "^1.89.0", "sass-loader": "^10.1.1", "vite": "^3.0.0"}}