<!DOCTYPE html>
<html>
  <head>
    <title>PinkWallet-kyc</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <body style="margin: 0">
    <script src="https://static.sumsub.com/idensic/static/sns-websdk-builder.js"></script>
    <div id="sumsub-websdk-container" style="height: 100vh;width: 100vw;"></div>
  </body>
</html>
<script>
  function launchWebSdk(accessToken, applicantEmail, applicantPhone) {
    let snsWebSdkInstance = snsWebSdk
      .init(accessToken, () => this.getNewAccessToken())
      .withConf({
        email: applicantEmail,
        phone: applicantPhone,
      })
      .withOptions({ addViewportTag: false, adaptIframeHeight: true })
      .on("idCheck.onStepCompleted", (payload) => {
        console.log("onStepCompleted", payload);
      })
      .on("idCheck.onError", (error) => {
        console.log("onError", error);
      })
      .on("idCheck.onApplicantSubmitted", () => {
        console.log("onApplicantSubmitted");
      })
      .on("idCheck.onApplicantResubmitted", () => {
        console.log("onApplicantResubmitted");
      })
      .onMessage((type, payload) => {
        console.log("onMessage", type, payload);
      })
      .build();
    snsWebSdkInstance.launch("#sumsub-websdk-container");
  }

  function getNewAccessToken() {
    return Promise.resolve('_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC1iMDA3NjBkNC1hODE3LTQ2MzUtOTNjYi05OTI5OGI2MjQ4NWEtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2');
  }

  launchWebSdk("token", "email", "phone");
</script>