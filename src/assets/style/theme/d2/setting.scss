// 主题名称
$theme-name: 'd2';
// 主题背景颜色
$theme-bg-color: #ebf1f6;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// 消息提示
$theme-message-info-background-color: $color-bg;
$theme-message-info-text-color: $color-text-normal;
$theme-message-info-border-color: $color-border-1;

// container组件
$theme-container-background-color: rgba(#FFF, 1);
$theme-container-header-footer-background-color: #FFF;
$theme-container-border-inner: 1px solid #cfd7e5;
$theme-container-border-outer: 1px solid #cfd7e5;

$theme-multiple-page-control-color: $color-text-normal;
$theme-multiple-page-control-color-active: #2f74ff;
$theme-multiple-page-control-nav-prev-color: #cfd7e5;
$theme-multiple-page-control-nav-next-color: #cfd7e5;
$theme-multiple-page-control-border-color: #cfd7e5;
$theme-multiple-page-control-border-color-active: #FFF;
$theme-multiple-page-control-background-color: rgba(#000, .03);
$theme-multiple-page-control-background-color-active: #FFF;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #ecf5ff;

// 顶栏上的文字颜色
$theme-header-item-color: $color-text-normal;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #2f74ff;
$theme-header-item-background-color-hover: rgba(#FFF, .5);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #2f74ff;
$theme-header-item-background-color-focus: rgba(#FFF, .5);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #2f74ff;
$theme-header-item-background-color-active: rgba(#FFF, .5);

// 侧边栏上的文字颜色
$theme-aside-item-color: $color-text-normal;
$theme-aside-item-background-color: transparent;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #2f74ff;
$theme-aside-item-background-color-hover: rgba(#FFF, .5);
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: #2f74ff;
$theme-aside-item-background-color-focus: rgba(#FFF, .5);
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #2f74ff;
$theme-aside-item-background-color-active: rgba(#FFF, .5);

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: $color-text-normal;
$theme-aside-menu-empty-text-color: $color-text-normal;
$theme-aside-menu-empty-background-color: rgba(#000, .03);
$theme-aside-menu-empty-icon-color-hover: $color-text-main;
$theme-aside-menu-empty-text-color-hover: $color-text-main;
$theme-aside-menu-empty-background-color-hover: rgba(#000, .05);