// element 样式补丁
.el-card {
  &.is-always-shadow {
    box-shadow: 0 0 8px 0 rgba(232,237,250,.6), 0 2px 4px 0 rgba(232,237,250,.5);
  }
  &.is-hover-shadow {
    &:hover {
      box-shadow: 0 0 8px 0 rgba(232,237,250,.6), 0 2px 4px 0 rgba(232,237,250,.5);
    }
  }
}

.el-menu--horizontal {
  border-bottom: none !important;
}

.el-tabs__item:focus.is-active.is-focus:not(:active) {
  box-shadow: none !important;
}

// 修复IE宽度不能撑满
.el-table__body,
.el-table__header {
  width: 100% !important;
}

// Chrome下表格头部错位修复
.el-table th.gutter,
.el-table colgroup.gutter {
  display: table-cell !important;
}
