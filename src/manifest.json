{
    "name" : "Bigverse",
    "appid" : "__UNI__83B433C",
    "description" : "Bigverse",
    "versionName" : "2.0.46",
    "versionCode" : 351,
    "transformPx" : false,
    "safearea" : {
        //iOS平台的安全区域
        "background" : "#35333E",
        "backgroundDark" : "#35333E" // HX 3.1.19+支持
    },
    "app-plus" : {
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {
            "Messaging" : {},
            "Share" : {},
            "OAuth" : {},
            "Payment" : {},
            "Statistic" : {},
            "Push" : {},
            "VideoPlayer" : {},
            "Camera" : {}
        },
        "compatible" : {
            "ignoreVersion" : true
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>"
                ],
                "writePhotosAlbum" : {
                    "desc" : "保存图片到相册"
                },
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "schemes" : "",
                "targetSdkVersion" : 30
            },
            "ios" : {
                "idfa" : false,
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "从相册中选择图片作为用户头像",
                    "NSPhotoLibraryAddUsageDescription" : "保存海报到我的相册"
                }
            },
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {},
                "push" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxad69c7f16fdcf595",
                        "UniversalLinks" : "https://www.nftcn.com.cn/share/prefecture/"
                    },
                    "qq" : {
                        "appid" : "102016838",
                        "UniversalLinks" : "https://www.nftcn.com.cn/qq_conn/"
                    }
                },
                "payment" : {
                    "appleiap" : {}
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxad69c7f16fdcf595",
                        "UniversalLinks" : "https://www.nftcn.com.cn/share/prefecture/"
                    }
                },
                "maps" : {},
                "statics" : {
                    "umeng" : {
                        "appkey_ios" : "62b957a788ccdf4b7eada473",
                        "channelid_ios" : "ios",
                        "appkey_android" : "62bad27605844627b5cd6df6",
                        "channelid_android" : "android"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "xxhdpi" : "src/static/build/cover/1080x1882.png",
                    "hdpi" : "src/static/build/cover/480x762.png",
                    "xhdpi" : "src/static/build/cover/730x1242.png"
                },
                "iosStyle" : "storyboard",
                "useOriginalMsgbox" : true,
                "ios" : {
                    "storyboard" : "D:/bigverse/bv2.0证书/Chuxueyun_launchscreen.zip"
                }
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true
        },
        "safearea" : {
            "background" : "#35333E", //背景色
            "bottom" : {
                "offset" : "none"
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "nativePlugins" : {
            "Netease-QiyuModule" : {
                "appKey" : "3d1f1a45bc64cc1aab8aca825ff89c13",
                "__plugin_info__" : {
                    "name" : "网易七鱼",
                    "description" : "网易七鱼APP原生插件，封装七鱼sdk功能供开发者使用",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=9262",
                    "android_package_name" : "com.nftcn.bigverse",
                    "ios_bundle_id" : "com.Bigverse.nftcn",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "9262",
                    "parameters" : {
                        "appKey" : {
                            "des" : "七鱼appKey",
                            "key" : "Netease-QiyuModule_appKey",
                            "value" : ""
                        }
                    }
                }
            },
            "AliCloud-NirvanaPns" : {
                "__plugin_info__" : {
                    "name" : "阿里云号码认证SDK",
                    "description" : "阿里云号码认证SDK，包含一键登录和本机号码校验两个功能。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4297",
                    "android_package_name" : "com.nftcn.bigverse",
                    "ios_bundle_id" : "com.Bigverse.nftcn",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4297",
                    "parameters" : {}
                }
            },
            "AP-FaceDetectModule" : {
                "__plugin_info__" : {
                    "name" : "APFaceDetectPlugin",
                    "description" : "阿里云金融级实人认证SDK",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "spx-rsa" : {
                "__plugin_info__" : {
                    "name" : "spx-rsa",
                    "description" : "rsa签名/加密",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "spx-umpush" : {
                "honor_app_id" : "",
                "huawei_app_id" : "",
                "meizu_app_id" : "",
                "meizu_app_key" : "",
                "oppo_app_key" : "",
                "oppo_master_secret" : "",
                "um_appkey_android" : "62bad27605844627b5cd6df6",
                "um_appkey_ios" : "62b957a788ccdf4b7eada473",
                "um_log_flag_android" : "",
                "um_track_android" : "",
                "umeng_message_secret" : "26d3def12946195a5c07f889916aec2f",
                "vivo_app_id" : "",
                "vivo_app_key" : "",
                "xiaomi_app_id" : "",
                "xiaomi_app_key" : "",
                "__plugin_info__" : {
                    "name" : "友盟消息推送",
                    "description" : "友盟消息推送原生插件",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=16800",
                    "android_package_name" : "com.nftcn.bigverse",
                    "ios_bundle_id" : "com.Bigverse.nftcn",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "16800",
                    "parameters" : {
                        "honor_app_id" : {
                            "des" : "荣耀通道，AppID，从荣耀开放平台获取：https://developer.hihonor.com/cn",
                            "key" : "com.hihonor.push.app_id",
                            "value" : ""
                        },
                        "huawei_app_id" : {
                            "des" : "华为通道，AppID，格式：appid=1234，从华为开发者联盟获取：https://developer.huawei.com",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        },
                        "meizu_app_id" : {
                            "des" : "魅族通道，AppID，格式：appid=1234，从魅族开放平台获取：https://open.flyme.cn/open-web/views/push.html",
                            "key" : "UM_MEIZU_APP_ID",
                            "value" : ""
                        },
                        "meizu_app_key" : {
                            "des" : "魅族通道，AppKey，格式：appkey=1234，从魅族开放平台获取：https://open.flyme.cn/open-web/views/push.html",
                            "key" : "UM_MEIZU_APP_KEY",
                            "value" : ""
                        },
                        "oppo_app_key" : {
                            "des" : "OPPO通道，AppKey，从OPPO开放平台获取：https://open.oppomobile.com/newservice/capability?pagename=push",
                            "key" : "UM_OPPO_APP_KEY",
                            "value" : ""
                        },
                        "oppo_master_secret" : {
                            "des" : "OPPO通道，App Master Secret，从OPPO开放平台获取：https://open.oppomobile.com/newservice/capability?pagename=push",
                            "key" : "UM_OPPO_MASTER_SECRET",
                            "value" : ""
                        },
                        "um_appkey_android" : {
                            "des" : "友盟Android AppKey",
                            "key" : "UMENG_PUSH_APPKEY",
                            "value" : ""
                        },
                        "um_appkey_ios" : {
                            "des" : "友盟ios appkey",
                            "key" : "UMENG_APPKEY",
                            "value" : ""
                        },
                        "um_log_flag_android" : {
                            "des" : "是否开启日志打印,默认为不打印false,如果在测试阶段，先要分析异常问题可设置为true，可以抓取日志分析错误。生产模式请务必关闭设置为false或者不设置",
                            "key" : "UM_LOG_FLAG",
                            "value" : ""
                        },
                        "um_track_android" : {
                            "des" : "友盟消息统计接口，可选值为:true打开,false关闭,参考：https://developer.umeng.com/docs/67966/detail/98583#h2--10",
                            "key" : "UM_PUSH_TRACK",
                            "value" : ""
                        },
                        "umeng_message_secret" : {
                            "des" : "友盟Message Secret",
                            "key" : "UMENG_MESSAGE_SECRET",
                            "value" : ""
                        },
                        "vivo_app_id" : {
                            "des" : "VIVO通道，AppID，从VIVO开放平台获取：https://vpush.vivo.com.cn",
                            "key" : "com.vivo.push.app_id",
                            "value" : ""
                        },
                        "vivo_app_key" : {
                            "des" : "VIVO通道，AppKey，从VIVO开放平台获取：https://vpush.vivo.com.cn",
                            "key" : "com.vivo.push.api_key",
                            "value" : ""
                        },
                        "xiaomi_app_id" : {
                            "des" : "小米通道，AppID，格式：appid=1234，从小米开放平台获取：https://dev.mi.com/console",
                            "key" : "UM_XIAOMI_APP_ID",
                            "value" : ""
                        },
                        "xiaomi_app_key" : {
                            "des" : "小米通道，AppKey，格式：appkey=1234，从小米开放平台获取：https://dev.mi.com/console",
                            "key" : "UM_XIAOMI_APP_KEY",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false,
            "es6" : false,
            "postcss" : true,
            "minified" : false
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : ""
            }
        }
    },
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "template" : "baidu_statistics.html",
        "uniStatistics" : {
            "enable" : false
        },
        "domain" : "",
        "devServer" : {
            "https" : false
        },
        "title" : "NFT艺术品交易平台",
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "unipush" : {
            "enable" : false
        },
        "async" : {
            //页面js异步加载配置
            "loading" : "AsyncLoading", //页面js加载时使用的组件（需注册为全局组件）
            "error" : "asyncErrorNew", //页面js加载失败时使用的组件（需注册为全局组件）
            "delay" : 200, //展示 loading 加载组件的延时时间（页面 js 若在 delay 时间内加载完成，则不会显示 loading 组件）
            "timeout" : 3000 //页面js加载超时时间（超时后展示 error 对应的组件）
        }
    },
    "uniStatistics" : {
        "version" : "1"
    },
    "locale" : "auto",
    "mp-toutiao" : {
        "setting" : {
            "urlCheck" : false
        }
    }
}
