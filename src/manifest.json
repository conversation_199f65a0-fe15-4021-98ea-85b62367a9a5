{
    "name" : "PinkWallet",
    "appid" : "__UNI__F9DEEFF",
    "description" : "PinkWallet",
    "versionName" : "1.0.2",
    "versionCode" : 111,
    "transformPx" : false,
    "app-plus" : {
        "nvueCompiler" : "uni-app",
        "compilerVersion" : "3.3.2",
        "modules" : {
            "Messaging" : {},
            "Share" : {},
            "Statistic" : {},
            "Payment" : {},
            "Push" : {},
            "Camera" : {},
            "FaceID" : {},
            "Fingerprint" : {},
            "FacialRecognitionVerify" : {},
            "Barcode" : {}
        },
        "webview" : {
            // 允许 JavaScript 执行
            "javascriptEnabled" : true,
            // 启用 DOM 存储 API
            "domStorageEnabled" : true,
            // 允许访问文件
            "allowFileAccess" : true,
            // 允许通过 file url 加载的 JavaScript 可以访问其他的源，包括其他的文件和 http，https 等其他的源
            "allowFileAccessFromFileURLs" : true,
            "allowUniversalAccessFromFileURLs" : true,
            // 允许缓存
            "cacheEnabled" : true
        },
        "permissions" : {
            "Camera" : {
                "description" : "摄像头权限"
            },
            "PhotoLibrary" : {
                "description" : "相册权限"
            },
            "Storage" : {},
            "Webview" : {}
        },
        "distribute" : {
            "compatible" : {
                "ignoreVersion" : true
            },
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>"
                ],
                "writePhotosAlbum" : {
                    "desc" : "保存图片到相册"
                },
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "schemes" : ""
            },
            "ios" : {
                "idfa" : false,
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSCameraUsageDescription" : "是否允许PinkWalletAPP获取您的相机权限进行拍照设置头像",
                    "NSPhotoLibraryUsageDescription" : "是否允许PinkWalletAPP获取您的相册权限进行选取头像设置头像",
                    "NSPhotoLibraryAddUsageDescription" : "是否允许PinkWalletAPP获取您的相册权限进行选取头像设置头像",
                    "NSFaceIDUsageDescription" : "我们使用 Face ID 来验证您的身份，以便快速登录"
                }
            },
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {},
                "push" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx4517974f4adcfb05",
                        "UniversalLinks" : "https://www.nftcn.com.cn/share/prefecture/"
                    }
                },
                "payment" : {
                    "appleiap" : {}
                },
                "oauth" : {},
                "maps" : {},
                "statics" : {
                    "umeng" : {
                        "appkey_ios" : "67f76f9a6127170d1b3ec54a",
                        "channelid_ios" : "ios",
                        "appkey_android" : "67e4ebfe4ad7683d44d4925a",
                        "channelid_android" : "android"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "android" : {
                    "xxhdpi" : "src/static/build/appCover/3.png",
                    "hdpi" : "src/static/build/appCover/1.png",
                    "xhdpi" : "src/static/build/appCover/2.png"
                },
                "iosStyle" : "storyboard",
                "useOriginalMsgbox" : true,
                "ios" : {
                    "storyboard" : "src/static/build/ios/pink_wallet.zip"
                }
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : true
        },
        "safearea" : {
            "background" : "#fff",
            "bottom" : {
                "offset" : "none"
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "nativePlugins" : {
            "AP-FaceDetectModule" : {
                "__plugin_info__" : {
                    "name" : "APFaceDetectPlugin",
                    "description" : "阿里云金融级实人认证SDK",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "spx-rsa" : {
                "__plugin_info__" : {
                    "name" : "spx-rsa",
                    "description" : "rsa签名/加密",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "spx-umpush" : {
                "honor_app_id" : "",
                "huawei_app_id" : "",
                "meizu_app_id" : "",
                "meizu_app_key" : "",
                "oppo_app_key" : "",
                "oppo_master_secret" : "",
                "um_appkey_android" : "67e4ebfe4ad7683d44d4925a",
                "um_appkey_ios" : "67f76f9a6127170d1b3ec54a",
                "um_log_flag_android" : "",
                "um_track_android" : "",
                "umeng_message_secret" : "47f1771fa5fc957060c6cbfa6944818e",
                "vivo_app_id" : "",
                "vivo_app_key" : "",
                "xiaomi_app_id" : "",
                "xiaomi_app_key" : "",
                "__plugin_info__" : {
                    "name" : "友盟消息推送 - [试用版，仅用于自定义调试基座]",
                    "description" : "友盟消息推送原生插件",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=16800",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 0,
                    "pid" : "16800",
                    "parameters" : {
                        "honor_app_id" : {
                            "des" : "荣耀通道，AppID，从荣耀开放平台获取：https://developer.hihonor.com/cn",
                            "key" : "com.hihonor.push.app_id",
                            "value" : ""
                        },
                        "huawei_app_id" : {
                            "des" : "华为通道，AppID，格式：appid=1234，从华为开发者联盟获取：https://developer.huawei.com",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        },
                        "meizu_app_id" : {
                            "des" : "魅族通道，AppID，格式：appid=1234，从魅族开放平台获取：https://open.flyme.cn/open-web/views/push.html",
                            "key" : "UM_MEIZU_APP_ID",
                            "value" : ""
                        },
                        "meizu_app_key" : {
                            "des" : "魅族通道，AppKey，格式：appkey=1234，从魅族开放平台获取：https://open.flyme.cn/open-web/views/push.html",
                            "key" : "UM_MEIZU_APP_KEY",
                            "value" : ""
                        },
                        "oppo_app_key" : {
                            "des" : "OPPO通道，AppKey，从OPPO开放平台获取：https://open.oppomobile.com/newservice/capability?pagename=push",
                            "key" : "UM_OPPO_APP_KEY",
                            "value" : ""
                        },
                        "oppo_master_secret" : {
                            "des" : "OPPO通道，App Master Secret，从OPPO开放平台获取：https://open.oppomobile.com/newservice/capability?pagename=push",
                            "key" : "UM_OPPO_MASTER_SECRET",
                            "value" : ""
                        },
                        "um_appkey_android" : {
                            "des" : "友盟Android AppKey",
                            "key" : "UMENG_PUSH_APPKEY",
                            "value" : ""
                        },
                        "um_appkey_ios" : {
                            "des" : "友盟ios appkey",
                            "key" : "UMENG_APPKEY",
                            "value" : ""
                        },
                        "um_log_flag_android" : {
                            "des" : "是否开启日志打印,默认为不打印false,如果在测试阶段，先要分析异常问题可设置为true，可以抓取日志分析错误。生产模式请务必关闭设置为false或者不设置",
                            "key" : "UM_LOG_FLAG",
                            "value" : ""
                        },
                        "um_track_android" : {
                            "des" : "友盟消息统计接口，可选值为:true打开,false关闭,参考：https://developer.umeng.com/docs/67966/detail/98583#h2--10",
                            "key" : "UM_PUSH_TRACK",
                            "value" : ""
                        },
                        "umeng_message_secret" : {
                            "des" : "友盟Message Secret",
                            "key" : "UMENG_MESSAGE_SECRET",
                            "value" : ""
                        },
                        "vivo_app_id" : {
                            "des" : "VIVO通道，AppID，从VIVO开放平台获取：https://vpush.vivo.com.cn",
                            "key" : "com.vivo.push.app_id",
                            "value" : ""
                        },
                        "vivo_app_key" : {
                            "des" : "VIVO通道，AppKey，从VIVO开放平台获取：https://vpush.vivo.com.cn",
                            "key" : "com.vivo.push.api_key",
                            "value" : ""
                        },
                        "xiaomi_app_id" : {
                            "des" : "小米通道，AppID，格式：appid=1234，从小米开放平台获取：https://dev.mi.com/console",
                            "key" : "UM_XIAOMI_APP_ID",
                            "value" : ""
                        },
                        "xiaomi_app_key" : {
                            "des" : "小米通道，AppKey，格式：appkey=1234，从小米开放平台获取：https://dev.mi.com/console",
                            "key" : "UM_XIAOMI_APP_KEY",
                            "value" : ""
                        }
                    }
                }
            },
            "sn-flutter" : {
                "__plugin_info__" : {
                    "name" : "Flutter插件",
                    "description" : "提供Flutter框架集成",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false,
            "es6" : false,
            "postcss" : true,
            "minified" : false
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : ""
            }
        }
    },
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "./"
        },
        "template" : "baidu_statistics.html",
        "uniStatistics" : {
            "enable" : false
        },
        "domain" : "",
        "devServer" : {
            "https" : false
        },
        "title" : "NFT艺术品交易平台",
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "unipush" : {
            "enable" : false
        },
        "async" : {
            //页面js异步加载配置
            "loading" : "AsyncLoading", //页面js加载时使用的组件（需注册为全局组件）
            "error" : "asyncErrorNew", //页面js加载失败时使用的组件（需注册为全局组件）
            "delay" : 200, //展示 loading 加载组件的延时时间（页面 js 若在 delay 时间内加载完成，则不会显示 loading 组件）
            "timeout" : 3000 //页面js加载超时时间（超时后展示 error 对应的组件）
        }
    },
    "uniStatistics" : {
        "version" : "1"
    },
    "locale" : "auto"
}
