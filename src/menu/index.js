import { uniqueId } from "lodash";

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
function supplementPath(menu) {
  return menu.map((e) => ({
    ...e,
    path: e.path || uniqueId("d2-menu-empty-"),
    ...(e.children
      ? {
          children: supplementPath(e.children),
        }
      : {}),
  }));
}
// 头部
export const menuHeader = supplementPath([]);
// 侧边栏
export const menuAside = supplementPath([
  {
    path: "/index",
    title: "首页",
    icon: "home",
  },

  // {
  //   title: "推广管理",
  //   children: [
      {
        path: "/EarningsSummaryQuery",
        title: "收益汇总查询",
      },
      {
        path: "/EarningsRecord",
        title: "收益记录查询",
      },
      // {
      //   path: "/PartnerApplicationManagement",
      //   title: "代理商申请记录",
      // },
      {
        path: "/InvitationLink",
        title: "邀请链接管理",
      },
      {
        path: "/PartnerConfiguration",
        title: "代理商配置",
      },
      {
        path: "/EntrustSummary",
        title: "委托汇总",
      },
      {
        path: "/DepositWithdrawal",
        title: "出入金统计",
      },
      {
        path: "/DepositWithdrawalDetails",
        title: "出入金明细",
      },
  //   ],
  // },

  // {
  //   path: "financeUserView",
  //   title: "用户余额",
  // },
  // {
  //   path: '/shareKverse',
  //   title: '分享K-Verse配置'
  // },

  // {
  //   title: '市场中心',
  //   icon: 'folder-o',
  //   children: [
  //     { path: '/marketTab', title: '市场Tab' }
  //   ]
  // },
  // {
  //   title: '上链',
  //   icon: 'folder-o',
  //   children: [
  //     { path: '/cochain', title: '用户上链失败列表' },
  //     { path: '/contract', title: '用户部署合约失败列表' },
  //     { path: '/transfer', title: '转移token失败列表' },
  //   ]
  // },
]);
