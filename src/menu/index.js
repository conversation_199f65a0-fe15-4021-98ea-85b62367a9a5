import { uniqueId } from "lodash";

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
function supplementPath(menu) {
  return menu.map((e) => ({
    ...e,
    path: e.path || uniqueId("d2-menu-empty-"),
    ...(e.children
      ? {
          children: supplementPath(e.children),
        }
      : {}),
  }));
}
// 头部
export const menuHeader = supplementPath([]);
// 侧边栏
export const menuAside = supplementPath([
  {
    path: "/index",
    title: "首页",
    icon: "home",
  },
  {
    title: "运营",
    icon: "folder-o",
    children: [
      {
        title: "商品列表",
        icon: "folder-o",
        path: "/indexOperation",
      },
      {
        path: "/seriesList",
        title: "系列列表",
        icon: "folder-o",
      },
      {
        title: "订单管理(运营)",
        icon: "folder-o",
        path: "/indexOperationOrder",
      },
      {
        path: "/todayNotice",
        title: "今日公告",
      },
      {
        path: "/tomorrowNotice",
        title: "明日公告",
      },
      {
        path: "/activity",
        title: "活动配置",
      },
      {
        path: "/scan",
        title: "扫描白名单",
      },
      {
        path: "/casting",
        title: "铸造作品",
      },
      {
        title: "一级空投",
        path: "/stairAirDrop",
      },
      {
        title: "二级空投管理",
        path: "twoDrop",
      },
      {
        title: "求购列表",
        icon: "folder-o",
        path: "/buyList",
      },
      {
        title: "竞价列表",
        path: "/Bidding",
      },
      {
        title: "边玩边赚",
        path: "/player",
      },
      {
        path: "/batchList",
        title: "寄售/限价/销毁",
      },
      {
        path: "/userList",
        title: "用户列表(app)",
      },
      {
        path: "/tableUnionSearch",
        title: "导出信息",
      },
      {
        path: "/floorConfig",
        title: "楼层区配置",
      },
      {
        path: "/floorConfigKv",
        title: "楼层区配置-kverse",
      },
      {
        path: "/priorityBuy",
        title: "优先购",
      },
      {
        path: "/createOrder",
        title: "创建订单",
      },
      {
        path: "/bannerConfig",
        title: "banner配置",
      },
      // {
      //   title: '交易',
      //   icon: 'folder-o',
      //   children: [{
      //       path: '/deal',
      //       title: '设置卖出'
      //     },
      //     {
      //       path: '/volIndex',
      //       title: '自动成交'
      //     },
      //     {
      //       path: 'payBroadcast',
      //       title: '交易播报'
      //     },
      //     {
      //       title: '合成空投管理',
      //       path: '/air-drop-compound'
      //     },
      //     {
      //       title: '批量上架',
      //       path: '/batchListing'
      //     },
      //   ]
      // },
      {
        // IMgroup
        title: "IM群聊",
        icon: "folder-o",
        children: [
          {
            path: "/ChatRecord",
            title: "群聊记录",
          },
          {
            path: "/IMgroupUser",
            title: "群聊用户管理",
          },
          {
            path: "/IMgroup",
            title: "所有群聊",
          },
          {
            path: "/IMsendMessage",
            title: "群发消息",
          },
          {
            path: "/plugSet",
            title: "推流设置",
          },
          {
            path: "/ReportListNew",
            title: "举报列表",
          },
        ],
      },
      {
        title: "群聊管理",
        icon: "folder-o",
        children: [
          {
            path: "/weChatCode",
            title: "微信二维码",
          },
          {
            path: "/ChatList",
            title: "群聊列表",
          },
          {
            path: "/ChatRecord",
            title: "群聊记录",
          },
          // {
          //   path: '/ReportList',
          //   title: '举报列表'
          // },
          {
            path: "/ReportListNew",
            title: "举报列表",
          },
        ],
      },
      {
        title: "报表",
        icon: "folder-o",
        children: [
          {
            title: "PV/UV",
            icon: "folder-o",
            path: "/pv_uv",
          },
          {
            path: "/proportion",
            title: "查换手",
          },
          {
            path: "/gzz",
            title: "仅供收藏数据看板",
          },
          {
            path: "/freightSpace",
            title: "仓位看板",
          },
          {
            path: "/data_list",
            title: "浏览最多",
          },
        ],
      },
      {
        title: "审核管理",
        icon: "folder-o",
        children: [
          {
            path: "/examine",
            title: "作品审核",
          },
          {
            path: "/series",
            title: "系列审核",
          },
          {
            path: "/workReview",
            title: "评论审核",
          },
        ],
      },
      // {
      //   title: '用户列表(app)',
      //   icon: 'folder-o',
      //   children: [{
      //       path: '/userList',
      //       title: '用户列表(app)'
      //     },
      //     {
      //       path: '/userBalance',
      //       title: '用户链路记录'
      //     },
      //     {
      //       path: '/fuelStone',
      //       title: '用户燃料石列表'
      //     }
      //   ]
      // },
      {
        title: "其他配置",
        icon: "folder-o",
        children: [
          {
            path: "/turnover",
            title: "流通量配置",
          },
          {
            path: "/informationConfig",
            title: "资讯配置",
          },
          {
            path: "/tagManage",
            title: "标签管理",
          },
          {
            title: "消息中心",
            icon: "folder-o",
            children: [
              {
                path: "/activityMessage",
                title: "消息盒子",
              },
              {
                path: "/communique",
                title: "事件消息模板",
              },
              {
                path: "/systematicNotification",
                title: "消息推送任务",
              },
            ],
          },
          {
            title: "飞跃计划",
            icon: "folder-o",
            children: [
              // { path: '/chat', title: '群聊全局配置' },
              {
                path: "/leapPlan",
                title: "签约用户",
              },
            ],
          },

          {
            title: "虫子商城",
            icon: "folder-o",
            children: [
              {
                path: "/worm-shop-list",
                title: "商品列表",
              },
              {
                path: "/exchange",
                title: "兑换记录",
              },
              {
                path: "/wormshop-balance",
                title: "虫子余额",
              },
              // { path: '/workReview', title: '评论审核' },
            ],
          },
          {
            title: "游戏",
            icon: "folder-o",
            children: [
              {
                path: "/configure",
                title: "游戏配置",
              },
              {
                path: "/duration",
                title: "游戏时长",
              },
              {
                path: "/bodeConfig",
                title: "伯德全局配置",
              },
              {
                path: "/bodeList",
                title: "伯德列表",
              },
              // { path: '/Administration', title: 'token ID配置' },
            ],
          },
          {
            title: "黑/白名单",
            icon: "folder-o",
            children: [
              // {
              //   path: "/buyBlack",
              //   title: "购买黑名单",
              // },
              // {
              //   path: "/withdrawBlack",
              //   title: "提现黑名单",
              // },
              // {
              //   path: "/sendBlack",
              //   title: "购买转赠黑名单",
              // },
              // {
              //   path: "/lockWhite",
              //   title: "锁单白名单",
              // },
              // {
              //   path: "/audit",
              //   title: "作品需审名单",
              // },
              // {
              //   path: "/verificationCode",
              //   title: "购买验证码白名单",
              // },
              // {
              //   path: "/auditNoAudit",
              //   title: "作品免审名单",
              // },
              // {
              //   path: "/target-duty",
              //   title: "求购任务白名单",
              // },
              {
                path: "/BlackList",
                title: "黑名单",
              },
              {
                path: "/WhiteList",
                title: "白名单",
              },
              {
                path: "/idCard-black",
                title: "身份证禁止实名名单",
              },
            ],
          },
          {
            title: "平台公告",
            icon: "folder-o",
            children: [
              // { path: '/chat', title: '群聊全局配置' },
              {
                path: "/platform",
                title: "公告列表",
              },
              {
                path: "/platformPublish",
                title: "发布公告",
              },
            ],
          },
          {
            title: "空投管理",
            icon: "folder-o",
            children: [
              {
                title: "创作空投管理",
                path: "/air-drop",
              },
              // {
              //   title: '藏品空投管理',
              //   path: '/air-drop-collection'
              // },
            ],
          },
          {
            title: "更多其他配置",
            icon: "folder-o",
            children: [
              {
                title: "市场页配置",
                icon: "folder-o",
                children: [
                  {
                    path: "/bannerConfig",
                    title: "banner配置",
                  },
                  {
                    path: "/kingKong",
                    title: "金刚区配置",
                  },
                  {
                    path: "/floorConfig",
                    title: "楼层区配置",
                  },
                  {
                    path: "/tileConfig",
                    title: "瓷片区配置",
                  },
                  {
                    path: "/subject",
                    title: "专题区配置",
                  },
                  {
                    path: "/brand",
                    title: "品牌区配置",
                  },
                  {
                    path: "/selection",
                    title: "精选ip维护",
                  },
                  {
                    path: "/module",
                    title: "模块全局管理",
                  },
                  {
                    path: "/WorkTheme",
                    title: "作品主题页管理",
                  },
                ],
              },

              // {
              //   title: '活动管理',
              //   icon: 'folder-o',
              //   children: [

              //   ]
              // },
              {
                title: "bigverse配置",
                icon: "folder-o",
                path: "/bigverse-manage",
              },
              {
                title: "短链接管理",
                icon: "folder-o",
                path: "/short-link",
              },
              {
                title: "原生配置工具",
                icon: "folder-o",
                path: "/native-config",
              },
              {
                title: "定时任务",
                icon: "folder-o",
                children: [
                  {
                    path: "/group_number",
                    title: "群聊人数",
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        title: "群聊（小程序）",
        icon: "folder-o",
        children: [
          //   {
          //   path: '/record',
          //   title: '群聊记录'
          // },
          {
            path: "/userLimits",
            title: "用户权限",
          },
          {
            path: "/plugSet",
            title: "推流设置",
          },
        ],
      },
      {
        title: "经纪商",
        icon: "folder-o",
        children: [
          {
            path: "/economyIndex",
            title: "新入驻申请",
          },
          {
            path: "/AutomaticExamine",
            title: "自动考核",
          },
          {
            path: "/economyClass",
            title: "经纪商分组",
          },
        ],
      },

      {
        title: "开杠",
        icon: "folder-o",

        children: [
          {
            path: "BITconfig",
            title: "对标物铺单参数",
          },
          {
            path: "/BITShopOrderParameters",
            title: "BIT指数铺单参数",
          },
          {
            path: "BITlist",
            title: "对标物配置",
          },
          // {
          //   title: "BIT指数",
          //   icon: "folder-o",

          //   children: [
          //     {
          //       path: "/BITCard",
          //       title: "风控看板",
          //     },
          //     {
          //       path: "/BITTrade",
          //       title: "交易大赛",
          //     },
          //     {
          //       path: "/Experience",
          //       title: "体验金",
          //     },
          //     {
          //       path: "/BITvisit",
          //       title: "访问信息",
          //     },
          //     // {
          //     //   path:'/BITprofit',
          //     //   title:'每日盈利'
          //     // },
          //     {
          //       path: "/BITShopOrderParameters",
          //       title: "铺单参数",
          //     },
          //     {
          //       path: "/TradeDetails",
          //       title: "成交明细",
          //     },
          //     {
          //       path: "/BIThandicapLog",
          //       title: "盘口日志",
          //     },
          //     {
          //       path: "/BITMain",
          //       title: "主力委托",
          //     },
          //     {
          //       path: "/BIThold",
          //       title: "持仓记录",
          //     },
          //     {
          //       path: "/BITboom",
          //       title: "爆仓记录",
          //     },
          //     // {
          //     //   path: '/BITtransactionBuyRecord',
          //     //   title: '开仓明细'
          //     // },
          //     // {
          //     //   path: '/BITtransactionSellRecord',
          //     //   title: '平仓明细'
          //     // },
          //     {
          //       path: "/BITopenWareHouseRecond",
          //       title: "开仓记录",
          //     },
          //     {
          //       path: "/BITcloseHouseRecond",
          //       title: "平仓记录",
          //     },
          //   ],
          // },
          {
            title: "历史记录",
            icon: "folder-o",
            children: [
              {
                path: "BITconfigcloseHouseRecond",
                title: "平仓记录",
              },
              {
                path: "BITconfigOpen",
                title: "开仓记录",
              },
              {
                path: "BITconfigboom",
                title: "爆仓记录",
              },
              {
                path: "BITconfigPosition",
                title: "持仓记录",
              },
              {
                path: "BITconfigDeal",
                title: "成交明细",
              },
              {
                path: "BITconfigBoard",
                title: "盘口记录",
              },
              {
                path: "BITconfigMain",
                title: "对标物主力委托",
              },
              {
                path: "/BITMain",
                title: "BIT指数主力委托",
              },
            ],
          },
          {
            title: "活动",
            icon: "folder-o",
            children: [
              {
                path: "/BITTrade",
                title: "交易大赛",
              },
              {
                title: "奖券",
                icon: "folder-o",
                children: [
                  {
                    path: "/Experience",
                    title: "体验金",
                  },
                  {
                    path: "/MagnumGold",
                    title: "万能金",
                  },
                ],
              },
            ],
          },
          {
            title: "用户增长",
            icon: "folder-o",
            children: [
              {
                path: "BITconfigVisit",
                title: "访问信息",
              },
              {
                path: "/FlowArrangement",
                title: "流量配置",
              },
              {
                path: "/InvitationList",
                title: "邀请列表",
              },
              {
                path:"/PartnerRebates",
                title:"合伙人返佣"
              },
              {
                path:'/AutoWelfare',
                title:"自动发福利"
              },
            ],
          },
          {
            title: "风控",
            icon: "folder-o",
            children: [
              {
                path: "BITconfigCard",
                title: "风控看板",
              },
              {
                path: "BITconfigriskBoard",
                title: "收益最高",
              },
              // BITconfigDeal
            ],
          },
        ],
      },
      // {
      //   path: "/seriesUs",
      //   title: "模拟美股",
      // },
    ],
  },
  {
    title: "财务",
    icon: "folder-o",
    children: [
      {
        title: "交易流水",
        icon: "folder-o",
        children: [
          {
            title: "订单管理(财务)",
            icon: "folder-o",
            path: "/cw_order",
          },
          {
            title: "用户交易",
            icon: "folder-o",
            children: [
              {
                path: "/accounting",
                title: "用户流水",
              },
              {
                path: "/financeUserView",
                title: "用户余额",
              },
            ],
          },
          {
            title: "用户提现",
            icon: "folder-o",
            children: [
              {
                path: "/withdraw",
                title: "银行卡提现",
              },
              {
                path: "/alipay",
                title: "支付宝提现",
              },
            ],
          },
        ],
      },
      {
        title: "财务报表",
        icon: "folder-o",
        children: [
          // {
          //   title: '收入汇总',
          //   path: '/nft_income',
          // },
          {
            path: "/particulars_commission",
            title: "佣金明细",
          },
          {
            path: "/particulars_rotate",
            title: "版税明细",
          },
          {
            path: "/particulars_withdraw",
            title: "提现明细",
          },
        ],
      },
      {
        path: "/particulars_channel",
        title: "渠道成本",
      },
      {
        title: "平安清分",
        icon: "folder-o",
        children: [
          {
            path: "/safeAccountList",
            title: "账户列表",
          },
          {
            path: "/accountList",
            title: "记账信息列表",
          },
          {
            path: "/withdrawalList",
            title: "手动处理提现列表",
          },
          {
            path: "/poolList",
            title: "记账需求池列表",
          },
          {
            path: "/transferWithdrawList",
            title: "提现记录列表",
          },
        ],
      },
      {
        title: "支付相关",
        icon: "folder-o",
        children: [
          {
            path: "/PaymentRelated/bankList",
            title: "绑卡列表",
          },
          {
            path: "/PaymentRelated/depositList",
            title: "充值列表",
          },
        ],
      },
      {
        title: "飞跃计划",
        icon: "folder-o",
        children: [
          // { path: '/chat', title: '群聊全局配置' },
          {
            path: "/leapPlan",
            title: "签约用户",
          },
        ],
      },
      {
        title: "保证金",
        icon: "folder-o",
        children: [
          {
            path: "/moneyLowest",
            title: "保证金最低配置",
          },
          {
            path: "/moneyList",
            title: "保证金列表",
          },
          {
            path: "/moenyOrder",
            title: "保证金订单",
          },
          {
            path: "/moneyPay",
            title: "保证金支付审核",
          },
          {
            path: "/moneyBack",
            title: "保证金退还审核",
          },
        ],
      },
      // {
      //   path: 'finance',
      //   title: '财务核算'
      // },
      {
        title: "用户列表(app)",
        icon: "folder-o",
        children: [
          {
            path: "/userList",
            title: "用户列表(app)",
          },
          {
            path: "/userBalance",
            title: "用户链路记录",
          },
          {
            path: "/fuelStone",
            title: "用户燃料石列表",
          },
        ],
      },
      {
        title: "黑/白名单",
        icon: "folder-o",
        children: [
          {
            path: "/BITbuyBlack",
            title: "BIT购买黑名单",
          },
          {
            path: "/buyBlack",
            title: "购买黑名单",
          },
          {
            path: "/withdrawBlack",
            title: "提现黑名单",
          },
          {
            path: "/sendBlack",
            title: "购买转赠黑名单",
          },
          {
            path: "/lockWhite",
            title: "锁单白名单",
          },
          {
            path: "/audit",
            title: "作品需审名单",
          },
          {
            path: "/verificationCode",
            title: "购买验证码白名单",
          },
          {
            path: "/auditNoAudit",
            title: "作品免审名单",
          },
          {
            path: "/target-duty",
            title: "求购任务白名单",
          },
          {
            path: "/idCard-black",
            title: "身份证禁止实名名单",
          },
        ],
      },
      // { path: '/workReview', title: '评论审核' },
    ],
  },
  {
    title: "法务",
    icon: "folder-o",
    children: [
      {
        path: "/withdraw",
        title: "银行卡提现",
      },
      {
        path: "/alipay",
        title: "支付宝提现",
      },
      {
        path: "/accounting",
        title: "用户流水",
      },
      {
        path: "/riskBoard",
        title: "对战收益最高",
      },
      {
        path: "/bitriskBoard",
        title: "BIT收益最高",
      },
      {
        title: "订单管理",
        icon: "folder-o",
        path: "/order",
      },
      {
        title: "飞跃计划",
        icon: "folder-o",
        children: [
          // { path: '/chat', title: '群聊全局配置' },
          {
            path: "/leapPlan",
            title: "签约用户",
          },
        ],
      },
      {
        title: "保证金",
        icon: "folder-o",
        children: [
          {
            path: "/moneyLowest",
            title: "保证金最低配置",
          },
          {
            path: "/moneyList",
            title: "保证金列表",
          },
          {
            path: "/moenyOrder",
            title: "保证金订单",
          },
          {
            path: "/moneyPay",
            title: "保证金支付审核",
          },
          {
            path: "/moneyBack",
            title: "保证金退还审核",
          },
        ],
      },
      {
        title: "用户列表(app)",
        icon: "folder-o",
        children: [
          {
            path: "/userList",
            title: "用户列表(app)",
          },
          {
            path: "/userBalance",
            title: "用户链路记录",
          },
          {
            path: "/fuelStone",
            title: "用户燃料石列表",
          },
        ],
      },
      {
        title: "黑/白名单",
        icon: "folder-o",
        children: [
          {
            path: "/BlackList",
            title: "黑名单",
          },
          {
            path: "/WhiteList",
            title: "白名单",
          },
          {
            path: "/BITbuyBlack",
            title: "BIT购买黑名单",
          },
          {
            path: "/BITRevenueRiskControl",
            title: "BIT收益风控",
          },
          // {
          //   path: "/buyBlack",
          //   title: "购买黑名单",
          // },
          // {
          //   path: "/withdrawBlack",
          //   title: "提现黑名单",
          // },
          // {
          //   path: "/sendBlack",
          //   title: "购买转赠黑名单",
          // },
          // {
          //   path: "/lockWhite",
          //   title: "锁单白名单",
          // },
          // {
          //   path: "/audit",
          //   title: "作品需审名单",
          // },
          // {
          //   path: "/verificationCode",
          //   title: "购买验证码白名单",
          // },
          // {
          //   path: "/auditNoAudit",
          //   title: "作品免审名单",
          // },
          // {
          //   path: "/target-duty",
          //   title: "求购任务白名单",
          // },
          {
            path: "/idCard-black",
            title: "身份证禁止实名名单",
          },
        ],
      },
      // {
      //   title: "风控列表",
      //   icon: "folder-o",
      //   children: [
      //     {
      //       icon: "folder-o",
      //       title: "用户信息",
      //       children: [
      //         {
      //           icon: "folder-o",
      //           title: "用户列表(风控)",
      //           children: [
      //             {
      //               path: "/Riskuserlist",
      //               title: "用户列表",
      //             },
      //           ],
      //         },
      //       ],
      //     },
      //     {
      //       icon: "folder-o",
      //       title: "名单库",
      //       children: [
      //         {
      //           icon: "folder-o",
      //           title: "黑名单",
      //           children: [
      //             {
      //               path: "/blacklist",
      //               title: "提现黑名单",
      //             },
      //             {
      //               path: "/reallist",
      //               title: "实名黑名单",
      //             },
      //             {
      //               path: "/buylistBlack",
      //               title: "购买黑名单",
      //             },
      //             {
      //               path: "/givelist",
      //               title: "购买转增黑名单",
      //             },
      //             {
      //               path: "/accountBan",
      //               title: "用户账号禁用黑名单",
      //             },
      //           ],
      //         },
      //         {
      //           icon: "folder-o",
      //           title: "灰名单",
      //           children: [
      //             {
      //               path: "/ipgray",
      //               title: "ip灰名单",
      //             },
      //             {
      //               path: "/workerGray",
      //               title: "工作人员灰名单",
      //             },
      //           ],
      //         },
      //       ],
      //     },
      //   ],
      // },
      // { path: '/workReview', title: '评论审核' },
    ],
  },
  {
    title: "配置",
    icon: "folder-o",
    children: [
      {
        path: "/configuration",
        title: "基础配置",
      },
      {
        path: "/vipConfig",
        title: "富豪榜配置",
      },
     {
          path: "/indexHash",
          title: "生成Hash",
      },
      {
        path: "/userTransfer",
        title: "账号转移",
      },
      {
        path: "/configurationConfig",
        title: "后台配置",
      },
      {
        path: "/configurationRiskIp",
        title: "风控ip",
      },
      {
        path: "/appCover",
        title: "APP开屏页配置",
      },
      {
        path: "/configurationApp",
        title: "App基础配置",
      },
      {
        path: "/configurationDict",
        title: "字典配置",
      },
      {
        path: "/ugcCreation",
        title: "UGC代铸造",
      },
      {
        path: "/ugcOrder",
        title: "UGC创建订单",
      },
      {
        title: "用户权限管理",
        icon: "folder-o",
        children: [
          {
            path: "/user_list",
            title: "用户列表(后台)",
          },
          {
            path: "/authority_list",
            title: "权限列表",
          },
        ],
      },
      // {
      //   title: '树藏助手',
      //   icon: 'folder-o',
      //   children: [{
      //       path: '/digital-collection/setting',
      //       title: '全局配置',
      //       children: [{
      //           path: '/digital-collection/setting/banner?businessLine=10',
      //           title: 'banner配置'
      //         },
      //         {
      //           path: '/digital-collection/setting/kingKong?businessLine=10',
      //           title: '金刚区配置'
      //         },
      //         {
      //           path: '/digital-collection/setting/platform',
      //           title: '平台配置'
      //         },
      //         {
      //           path: '/digital-collection/setting/detailsTips',
      //           title: '商品详情提示'
      //         },
      //         {
      //           title: '消息中心',
      //           icon: 'folder-o',
      //           children: [{
      //               path: '/digital-collection/activityMessage?platform=2',
      //               title: '消息盒子'
      //             },
      //             {
      //               path: '/digital-collection/communique?platform=2',
      //               title: '事件消息模板'
      //             },
      //             {
      //               path: '/digital-collection/systematicNotification?platform=2',
      //               title: '消息推送任务'
      //             }
      //           ]
      //         },
      //       ]
      //     },
      //     {
      //       path: '/digital-collection/publish',
      //       title: '商品发布列表'
      //     },
      //     {
      //       path: '/digital-collection/order',
      //       title: '订单列表'
      //     },
      //     {
      //       path: '/digital-collection/template-publish',
      //       title: '一键发布模版管理'
      //     },
      //   ]
      // },
      {
        path: "/send_email",
        title: "邮箱管理",
        icon: "home",
      },
      {
        path: "/user_details?type=user&id=",
        title: "修改密码",
        icon: "home",
      },
      {
        path: "/synchronization",
        title: "数据同步",
        icon: "home",
      },
      {
        title: "开发工具",
        icon: "folder-o",
        children: [
          {
            path: "/listOfWorks",
            title: "商品列表",
          },
          {
            path: "/trace",
            title: "商品维度",
          },
          {
            path: "/payControl",
            title: "支付回调",
          },
          {
            title: "开发工具",
            path: "DevTools",
          },
        ],
      },
      {
        title: "日志列表",
        icon: "folder-o",
        children: [
          {
            path: "/logs/admin-login",
            title: "后台登录日志",
          },
          {
            path: "/logs/nftcn-login",
            title: "用户登录日志",
          },
          {
            path: "/logs/request-log",
            title: "后台操作日志",
          },
        ],
      },
      {
        title: "openAPI接口",
        icon: "folder-o",
        children: [
          {
            path: "/open-api-list",
            title: "openAPI列表",
          },
        ],
      },
      {
        title: "注销",
        icon: "folder-o",
        children: [
          {
            path: "/cancellation",
            title: "注销管理",
          },
        ],
      },
    ],
  },
  {
    title: "暴躁龙",
    icon: "folder-o",
    children: [
      {
        path: "/Longcasting",
        title: "铸造藏品",
      },
      {
        path: "/FirstRelease",
        title: "首发藏品",
      },
      {
        path: "/SeriesListlong",
        title: "系列列表",
      },
      {
        path: "/Application",
        title: "申请记录",
      },
      {
        path: "/AllianceList",
        title: "联盟列表",
      },
      {
        path: "/PFPconfig",
        title: "基础配置",
      },
      {
        path: "/PFPshop",
        title: "PFP商品列表",
      },
      {
        path: "/PFPuser",
        title: "PFP用户列表",
      },
      {
        path: "/PFPshare",
        title: "分享入群二维码",
      },
      {
        path: "/PFPinvite",
        title: "邀请历史",
      },
      {
        path: "/PFPorder",
        title: "转移记录",
      },
      {
        path: "/floorConfig2?moduleIds=7",
        title: "市场页配置",
      },
      {
        path: "/bannerConfig2?businessLine=2",
        title: "banner配置",
      },
      {
        path: "/PFPorder",
        title: "活动配置",
        children: [
          {
            path: "bzl_icebound",
            title: "质押配置",
          },
          {
            path: "/staking",
            title: "质押记录",
          },
          {
            path: "activity2?platformType=2",
            title: "合成配置",
          },
        ],
      },
      {
        title: "一级空投",
        path: "/stairAirDrop",
      },
      {
        path: "/scan",
        title: "扫描白名单",
      },
      {
        path: "/createOrder",
        title: "创建订单",
      },
      {
        path: "/platformBzl",
        title: "公告列表",
      },
      {
        path: "/todayNoticeBzl",
        title: "暴躁龙今日公告",
      },
    ],
  },
  // {
  //   title: '市场中心',
  //   icon: 'folder-o',
  //   children: [
  //     { path: '/marketTab', title: '市场Tab' }
  //   ]
  // },
  // {
  //   title: '上链',
  //   icon: 'folder-o',
  //   children: [
  //     { path: '/cochain', title: '用户上链失败列表' },
  //     { path: '/contract', title: '用户部署合约失败列表' },
  //     { path: '/transfer', title: '转移token失败列表' },
  //   ]
  // },
]);
