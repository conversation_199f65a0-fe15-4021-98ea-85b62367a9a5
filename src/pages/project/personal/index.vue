<template>
	<view>
		<view class="user_head">
			<view class="barHeight"></view>
			<view class="top_IP">
				<view class="gz">
					总资产估值
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240308/8812274a59ce41c3cc2753903ac889bc_72x72.png"
						mode="widthFix" @click="look" v-show="show"></image>
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240308/fe895b568b5e4befab21850132e60ad3_72x72.png"
						mode="widthFix" @click="look" v-show="!show"></image>
				</view>
				<view class="asset">
					￥<text v-show="show">{{ assetInfo.totalAmount }}</text>
					<text v-show="!show">{{ hiddenStars }}</text>
				</view>
				<view class="shouyi">
					<view class="left" v-show="show">
						<text v-show="null_type">{{ Flg ? '￥+' : '￥' }}{{ assetInfo.changeAmount }}（{{ Flg ? '+' : ''
							}}{{ assetInfo.changeRatio }}%）</text>
						<text v-show="!null_type">无</text>
					</view>
					<view class="left" v-show="!show">
						<text v-show="null_type">{{ Flg ? '￥+' : '￥' }}{{ hiddenAmount }}（{{ Flg ? '+' : '' }}{{
							hiddenRatio }}%）</text>
						<text v-show="!null_type">无</text>
					</view>
					<view class="right">
						<text @click="timShow = true">{{ timText }}</text>
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240308/a2b5d2b401cd814d39437de0c8ae63ae_40x40.png"
							mode="widthFix" @click="timShow = true"></image>
						<view class="timBox" v-if="timShow">
							<view v-for="item in tim" :key="item.id" @click="timClick(item)">{{ item.name }}</view>
						</view>
					</view>
				</view>
				<view class="right_ip" @click="showIp">
					<image v-if="ipShow"
						src="https://cdn-lingjing.nftcn.com.cn/image/20241223/7099357307fbd9e1586687698eb5f10d_300x480.png"
						mode="widthFix"></image>
					<image v-else
						src="https://cdn-lingjing.nftcn.com.cn/image/20241223/67599b67f0609c1eb999d8acece3479a_300x480.gif"
						mode="widthFix"></image>
				</view>
			</view>
			<view class="data_cart" v-if="jShow">
				<view class="li">
					<view>
						<text>高流通藏品</text>
						<view class="price" v-show="show">￥{{ assetInfo.highAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenHighAmountt ? hiddenHighAmountt : "*" }}</view>

					</view>
				</view>
				<view class="li">
					<view>
						<text>钱包余额</text>
						<view class="price" v-show="show">￥{{ assetInfo.walletAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenWalletAmount ? hiddenWalletAmount : "*" }}</view>
					</view>
				</view>
				<view class="li">
					<view>
						<text>吉物仓藏品</text>
						<view class="price" v-show="show">￥{{ assetInfo.jwcAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenJwcAmount ? hiddenJwcAmount : "*" }}</view>

					</view>
				</view>
				<view class="li" @click="nav_broker()">
					<view>
						<text>经纪商收益</text>
						<view class="price">
							<text v-show="show">￥{{ jjsNum }} </text>
							<text v-show="!show">￥{{ hiddenJjsNum ? hiddenJjsNum : "*" }} </text>

							<image v-show="show" src="@/static/imgs/public/right.png" mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="data_cart1" v-else>
				<view class="li">
					<view>
						<text>高流通藏品</text>
						<view class="price" v-show="show">￥{{ assetInfo.highAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenHighAmountt ? hiddenHighAmountt : "*" }}</view>
					</view>
				</view>
				<view class="li">
					<view>
						<text>吉物仓藏品</text>
						<view class="price" v-show="show">￥{{ assetInfo.jwcAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenJwcAmount ? hiddenJwcAmount : "*" }}</view>
					</view>
				</view>
				<view class="li">
					<view>
						<text>钱包余额</text>
						<view class="price" v-show="show">￥{{ assetInfo.walletAmount }}</view>
						<view class="price" v-show="!show">￥{{ hiddenWalletAmount ? hiddenWalletAmount : "*" }}</view>
					</view>
				</view>
			</view>
			<view class="cart_view">
				<view class="li" @tap="nav_to('invite')">
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240307/617740448f376eeb42f78deec78256c4_240x240.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						<text>邀请用户</text>
					</view>
				</view>
				<view class="li" @tap="nav_to('order')">
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240307/351b731362ab714483c54ade4534f559_240x240.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						<text>订单</text>
					</view>
				</view>
				<view class="li" @tap="nav_to('myBalance')">
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240307/8d105bda8965cfa39b244b06449488f2_240x240.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						<text>钱包</text>
					</view>
				</view>
				<view class="li" @click="nav_to('warehouse')">
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1896cea8a314c961ba5a7a5bc7f1b77a_240x240.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						<text>宝藏统计</text>
					</view>
				</view>
			</view>
		</view>
		<view class="tabbar_view">
			<view class="tabber">
				<u-tabs name="cate_name" :bar-style="barStyle" :list="list" bold :is-scroll="false" :item-width="200"
					:active-item-style="itemStyle" inactive-color="#fff" bg-color="#35333E" active-color="#fff"
					:current="current" @change="change"></u-tabs>
			</view>
			<view class="sousuo" @tap="nav_to('userSearch')">
				<view class="icon">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240307/54361d6bb4db9e9480dba85e33bf152d_64x64.png"
						mode="widthFix"></image>
				</view>
			</view>
		</view>
		<view class="sort" v-show="current != 2">
			<view class="sort_item">
				<view class="time" :class="{ 'active': isCheckend == 0 }" @click="checkSort(0)">
					{{ isActiveTime == 1 ? '时间倒序' : '时间顺序' }}
					<image v-show="isCheckend == 1"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/198ae65238f91ff7395a737095a36c2f_56x56.png"
						mode="widthFix"></image>
					<image v-show="isActiveTime == 1 && isCheckend == 0"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/b7adee838541fbb4cbb0d2f1d685c8b6_56x56.png"
						mode="widthFix"></image>
					<image v-show="isActiveTime == 2 && isCheckend == 0"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/c7c4d95411cf96dc876201da9bff065d_56x56.png"
						mode="widthFix"></image>
				</view>
			</view>
			<view class="sort_item" @click="checkSort(1)">
				<view class="price" :class="{ 'active': isCheckend == 1 }">
					{{ isActivePrice == 1 ? '价格降序' : '价格升序' }}
					<image v-show="isCheckend == 0"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/198ae65238f91ff7395a737095a36c2f_56x56.png"
						mode="widthFix"></image>
					<image v-show="isActivePrice == 1 && isCheckend == 1"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/b7adee838541fbb4cbb0d2f1d685c8b6_56x56.png"
						mode="widthFix"></image>
					<image v-show="isActivePrice == 2 && isCheckend == 1"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240425/c7c4d95411cf96dc876201da9bff065d_56x56.png"
						mode="widthFix"></image>
				</view>
			</view>
		</view>
		<!-- <scroll-view ref="scrollView" scroll-top="scrollTop" class="scroll-Y" scroll-y :refresher-triggered="triggered"
			refresher-default-style="none" :refresher-enabled="true" @refresherrefresh="refresher" 
			@scrolltolower="lower"> -->
		<view class="loading_list" v-show="isLoadingStatus == 0">
			<view>
				<view class="flex">
					<view class="balls"></view>
				</view>
				<view class="text">
					玩命加载中...
				</view>
			</view>
		</view>
		<view class="collection" v-show="seriesList != '' && isLoadingStatus == 1">
			<view class="li" v-for="(item, index) in seriesList">
				<view class="cover">
					<image :src="item.smallCover" v-show="current == 0 || current == 1" mode="aspectFill"
						@tap="open_sale(item)">
					</image>
					<image :src="item.cover" v-show="current == 2" mode="aspectFill" @tap="nav_details(item)"></image>
				</view>
				<view class="title oneOver">
					{{ item.title }}
				</view>
				<view class="total_text">
					<view class="" v-show="current == 2">售价:￥<text>{{ item.price }}</text></view>
					<view class="" v-show="current == 1">共<text>{{ item.allCount }}</text>个 寄售<text>{{ item.saleSum
							}}</text>个
					</view>
					<view class="" v-show="current == 0">共<text>{{ item.allCount }}</text>个</view>
					<view class="jishou" v-show="current == 0 && item.notSaleSign == 1">
						未开放寄售
					</view>
					<view class="" v-show="current == 0 && item.notSaleSign == 0">
						{{ item.isExitMarket == 1 ? '退市:' : '地板价:' }}<text>￥{{ item.floorPrice ? item.floorPrice : '-'
							}}</text>
					</view>
				</view>
			</view>
		</view>

		<view class="null_body" v-show="seriesList == '' && isLoadingStatus == 2">
			<view class="null">
				<view class="img">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						mode="widthFix"></image>
				</view>
				<view class="text">
					哎呦 您还没有藏品哦
				</view>
			</view>
		</view>
		<!-- </scroll-view>  打开藏品popup -->
		<u-popup ref="popup" class="solePopup" v-model="solePopupBox" :border-radius="40" height="1000rpx"
			mode="bottom">
			<view class="popup-content">
				<view class="tx_view">
					<image :src="popupHead.smallCover" mode="aspectFill"></image>
				</view>
				<view class="content_view">
					<view class="right_font_view">
						<view class="right_icon"></view>
						<view class="title twoOver">
							{{ popupHead.title }}
						</view>
						<view class="num">
							<view class="gs">共{{ popupHead.allCount }}个</view>
							<view class="jj">均价<text>￥{{ popupHead.avgBuyPrice }}</text></view>
						</view>
						<view class="mall_right" @click="nav_series()">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240424/c8c4d01ae296db728fe9039da002bb3a_48x45.png"
								mode="widthFix"></image>
							<text>
								市场
							</text>
						</view>
					</view>

					<scroll-view ref="scrollView" scroll-top="scrollTop" class="scroll-Y" scroll-y
						:refresher-triggered="triggered" :refresher-enabled="true" @refresherrefresh="refresher"
						@scrolltolower="lower">
						<view class="sole_ul_view">
							<view class="sole_ul_li" :class="{ 'active': item.sale == 1 }"
								v-for="(item, index) in popupList">
								<view class="type" :class="{ 'active': item.sale == 1 }" @tap="openMore(item, index)">
									<text v-show="item.sale == 1">寄售中</text>
									<text v-show="item.sale == 0">未寄售</text>
								</view>
								<view class="price_tid" @tap="nav_details(item)">
									<view class="price">
										<text v-show="item.sale == 1">
											￥{{ item.price }}
										</text>
									</view>
									<view class="tid_view">
										<text>TID:</text>
										<text>{{ item.tidd }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- <view slot="refresher">
							<view class="loadding">
								<view class="gif">
									<image
										src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
										mode="widthFix"></image>
								</view>
							</view>
						</view> -->
					</scroll-view>
					<view class="sale_view" v-if="current == 1" @tap="batchUnSaleCollectionList">
						批量取消寄售
					</view>
				</view>
			</view>
		</u-popup>
		<TabBar :initialActiveIndex="9"></TabBar>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
		<!-- 更多操作popup -->
		<u-popup v-model="isMore" mode="bottom" @close="closeMore" border-radius="36">
			<view class="mall_more_ul">
				<view class="close" @click="isMore = false">
					<image 
						src="https://cdn-lingjing.nftcn.com.cn/image/20240411/d989685a9e1809a9396823f629ec2236_160x160.png"
						mode="widthFix"></image>
				</view>
				<view>
					<view class="li">
						<text>更多</text>
					</view>
					<view class="li" @click="openSell()" v-show="popupHead.notSaleSign == 3">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_up.png" mode="widthFix"></image>
						</view>
						<text>转售作品</text>
					</view>
					<view class="li" @click="openUp()" v-show="moreInfo.sale == 0 && popupHead.notSaleSign !== 3">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_up.png" mode="widthFix"></image>
						</view>
						<text>寄售作品</text>
					</view>
					<view class="li" @click="openDown()" v-show="moreInfo.sale == 1">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_dow.png" mode="widthFix"></image>
						</view>
						<text>停售作品</text>
					</view>
					<view class="li" @click="openSet()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_set.png" mode="widthFix"></image>
						</view>
						<text>设为头像</text>
					</view>
					<view class="li" @click="openDestroy()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_del.png" mode="widthFix"></image>
						</view>
						<text>销毁作品</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 转售作品popup -->
		<u-popup v-model="sellPopup" mode="center" border-radius="30" duration='300'>
			<view class="collect_card">
				<view class="collect_card_head">
					<image class="bg1"
						src='https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png'
						mode="">
					</image>
					<view class="title">转售作品</view>
				</view>
				<view class="collect_card_content">
					<view class="body">
						<view class="text">
							转售地址
						</view>
						<view class="input">
							<u-input v-model="sellAddress" placeholder="请输入转售地址" type="text" />
						</view>
					</view>
					<view class="body">
						<view class="text">
							转售价格
						</view>
						<view class="input">
							<u-input v-model="sellPrice" placeholder="请输入转售价格" type="number" />
						</view>
					</view>
				</view>
				<view class="btn" @click="openResell()">
					确定
				</view>
			</view>
		</u-popup>

		<!-- 设置支付密码popup -->
		<u-modal v-model="isPassword" border-radius="30" :show-title="false" width="630" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					设置密码
				</view>
				<view class="modal-content">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isPassword = false">取消</view>
					<view class="img_reasale" @click="setPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>

		<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
			email="333" :mode="mode" @pay="password" @createSuccess="createSuccess" />

		<modalPop ref="modalPop" :tid="moreInfo.tid" :isCreation="0" :isMoreShow="true" @downSucceed="downSucceed"
			@upSucceed="upSucceed" @destroySucceed="destroySucceed"></modalPop>
	</view>
</template>
<script>
import {
	desensitizeMobile
} from '@/utils/utils'; // 假设这是你存放公共方法的路径
import uniCopy from '@/js_sdk/uni-copy.js'
import TabBar from "@/components/public/TabBar";
import * as head from "@/static/lottie/head/head.json";
import payPopup from "@/components/payPopup/index.vue";

import modalPop from "@/components/public/modalPop";
export default {
	data() {
		return {
			sellAddress: '',
			phoneInfo: '',

			sellPrice: '',
			passwordTitle: "确认转售",
			passwordMsg: "请输入余额支付密码，用于转售",
			isPasswordImport: false,
			isPassword: false,//设置交易密码弹唱
			sellPopup: false, // 转售作品popup
			show: true, //是否可以看到数字
			jShow: false, //是否是经销商
			timText: '今日',
			timShow: false, //是否展开选择日子
			tim: [{
				id: 0,
				name: '今日',
				value: '0'
			}, {
				id: 1,
				name: '三日',
				value: '3'
			}, {
				id: 2,
				name: '一周',
				value: '7'
			}, {
				id: 3,
				name: '一月',
				value: '30'
			}],
			type: '',
			percentage: '',
			num: '',
			percentage1: '',
			num1: '',
			list: [{
				cate_name: '我的藏品'
			}, {
				cate_name: '寄售中'
			}, {
				cate_name: '已出售',
				cate_count: 5
			}],
			current: 0,
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				'width': '66rpx',
				'height': '16rpx',
				'border-radius': '0rpx',
				'bottom': '0rpx',
				'z-index': '1'
			},
			itemStyle: {
				'font-size': '32rpx',
				'min-width': '120rpx',
				'z-index': '2'
			},
			info: {},
			seriesList: [],
			option: {
				path: 'https://cdn-lingjing.nftcn.com.cn/h5/xCase/xCaseIP/ip.json',
				loop: false,
				autoplay: false
			},
			option1: {
				data: head,
				loop: false,
				autoplay: false
			},
			solePopupBox: false,
			triggered: false,
			ctid: "",
			popupHead: "",
			popupList: [],
			sale: "",
			pulling: false,
			showAnimation: false,
			animationInstance: null,
			isFooter: true, //没有更多了
			isRequest: false, //多次请求频繁拦截
			pageNum: 1,
			popuePageNum: 1,
			assetInfo: "",
			totalAmount: '',
			isLoadding: false,
			Flg: false,
			isMore: false,
			moreInfo: "",
			sunIndex: -1,
			ipShow: true,
			suo: false,
			null_type: true,
			isLoadingStatus: 0,
			isCheckend: 0,
			isActivePrice: 1,
			isActiveTime: 1,
			mode: "pay",

		}
	},
	onLoad(options) {
		let _this = this
		uni.getSystemInfo({
			success: function (res) {
				_this.phoneInfo = res
				console.log('当前平台？？？？？', res.statusBarHeight)
				console.log("res---------", res)

			}
		});
		this.holdSeriesList()
		// this.userInfo()
	},
	onShow() {
		this.showIp()
		this.get_assetInfo()
		// this.toggleAnimation()
		// setTimeout(() => {
		// 	this.$refs.lottieWeb_ip.call('play');
		// }, 300)
	},
	onHide() {
		// this.$refs.lottieWeb_ip.call('stop');
		// this.toggleAnimation()
	},
	onPullDownRefresh() {
		setTimeout(() => {
			// this.suo = true
			// this.ipShow = false
			this.showIp()
			this.isFooter = true
			this.isLoadingStatus = 0
			this.get_assetInfo()

			if (this.current == 2) {
				this.seriesList = []
				this.pageNum = 1
				this.saleSeriesList()
			} else {
				this.pageNum = 1
				if (this.current == 1) {
					this.sale = 1
				} else {
					this.sale = ""
				}
				this.seriesList = []
				this.holdSeriesList()
			}
			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 1000);
	},
	onReachBottom() {
		if (this.isFooter) {
			if (this.isRequest == false) {
				if (this.current == 2) {
					this.saleSeriesList()
				} else {
					this.holdSeriesList()
				}
			} else {
				console.log("请求超时，已经拦截")
			}
		} else {
			console.log("已经到底了")
		}
	},
	computed: {
		hiddenStars() {
			if (this.assetInfo.totalAmount) {
				return new Array(this.assetInfo.totalAmount.toString().length).fill('*').join('');
			}
		},
		hiddenAmount() {
			if (this.assetInfo.changeAmount) {
				return new Array(this.assetInfo.changeAmount.toString().length).fill('*').join('');
			}
		},
		hiddenRatio() {
			if (this.assetInfo.changeRatio) {
				return new Array(this.assetInfo.changeRatio.toString().length).fill('*').join('');
			}
		},
		hiddenWalletAmount() {
			if (this.assetInfo.walletAmount) {
				return new Array(this.assetInfo.walletAmount.toString().length).fill('*').join('');
			}
		},
		hiddenHighAmountt() {
			if (this.assetInfo.highAmount) {
				return new Array(this.assetInfo.highAmount.toString().length).fill('*').join('');
			}
		},
		hiddenJwcAmount() {
			if (this.assetInfo.walletAmount) {
				return new Array(this.assetInfo.jwcAmount.toString().length).fill('*').join('');
			}
		},
		hiddenJjsNum() {
			if (this.jjsNum) {
				return new Array(this.jjsNum.toString().length).fill('*').join('');
			}
		},
	},
	methods: {
		downSucceed() {
			this.popupList[this.sunIndex].sale = 0
		},
		upSucceed() {
			this.popupList[this.sunIndex].sale = 1
		},
		destroySucceed() {
			// this.popupList.splice(this.sunIndex, 1);
		},
		createSuccess(psw) {
			uni.setStorageSync("isSetTradePassword", 1)
			this.isSetTradePassword = 1
			this.isPasswordImport = false
			// this.checkUp()
			// this.finishPay(psw)
		},
		password(e) {
			console.log(e)
			this.isPasswordImport = false
			this.payPassword = e
			this.resell()

		},
		async resell() {
			console.log(this.moreInfo)
			console.log("phoneInfo----", this.phoneInfo)
			console.log(this.payPassword)
			let paymentScene;

			if (this.phoneInfo.platform == 'ios') {
				paymentScene = 3
			} else if (this.phoneInfo.platform == "android") {
				paymentScene = 4
			} else if (this.phoneInfo.platform == "h5") {
				paymentScene = 1
			} else {
				paymentScene = 2
			}
			let data = {
				tid: this.moreInfo.tid,
				contractAddress: this.sellAddress,
				price: this.sellPrice,
				paymentScene,
				tradePassword: this.payPassword
			}
			console.log(data)

			let res = await this.$api.CreateResale(data);
			console.log(res)
			this.sellPopup = false
			this.solePopupBox = false;
			this.sellAddress = ''
			this.sellPrice = ''
			if (res.status.code == 0) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});

			} else if (res.status.code == 9999) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			} else if (res.status.code == 510) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		//批量转售
		async openResell() {
			console.log(this.moreInfo)
			console.log("phoneInfo----", this.phoneInfo)
			let payClient;
			this.isPasswordImport = true
		},

		setPayPassword() {
			this.isPassword = false
			this.mode = "set"
			this.isPasswordImport = true
		},
		//转售作品
		openSell() {
			this.isMore = false
			if (uni.getStorageSync('isSetTradePassword') == 1) {
				this.sellPopup = true
			} else {
				this.isPassword = true
			}
		},
		look() {
			this.show = !this.show
		},
		regist(arr) { // 字符串转成 **
			console.log(arr, 'arr');
			if (arr == '0' || arr == 0 || !arr) {
				return '*'
			}
			let ary = arr.split('')
			let length = ary.length
			ary.forEach(item => {
				ary.push('*')
			})
			ary.splice(0, length)
			let str = ary.join('')
			return str
		},
		timClick(item) {
			this.timShow = false
			this.timText = item.name
			this.type = item.value
			this.get_assetInfo()
		},

		change(index) {
			this.isFooter = true
			this.isLoadingStatus = 0
			this.isCheckend = 0,
				this.isActivePrice = 1
			this.isActiveTime = 1
			if (index == 2) {
				this.current = index
				this.seriesList = []
				this.pageNum = 1
				this.saleSeriesList()
			} else {
				this.current = index
				this.pageNum = 1
				if (index == 1) {
					this.sale = 1
				} else {
					this.sale = ""
				}
				this.seriesList = []
				this.holdSeriesList()
			}
		},
		nav_login() {
			this.$Router.push({
				name: "mainLogin"
			})
		},
		nav_to(name) {
			this.$Router.push({
				name
			})
		},
		open_sale(item) {
			console.log(item)
			this.appUrl = getApp().globalData.url
			let link = `${this.appUrl}pagesA/project/personal/nostalgic`
			if (item.ctid == "-1") {
				// #ifdef APP
				this.$Router.push({
					name: "webView",
					params: {
						url: link,
					}
				})
				// #endif
				// #ifdef H5
				this.$Router.push({
					name: "nostalgic"
				})
				// #endif
			} else {
				this.popupList = []
				this.popuePageNum = 1
				this.solePopupBox = true
				this.ctid = item.ctid
				this.getSaleHead()
			}

		},
		async userInfo() {
			let res = await this.$api.userInfo({});
			if (res.status.code == 0) {
				console.log(res)
				this.info = res.result
				let certification;
				if (res.result.authStatus == 31 && res.result.authType == 1) {
					certification = 1
				} else if (res.result.authStatus == 31 && res.result.authType == 2) {
					certification = 2
				} else {
					certification = 0
				}
				uni.setStorageSync("certification", certification);
				uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword);
				uni.setStorageSync("uid", res.result.userId);
				uni.setStorageSync("contract_address", res.result.contractAddress);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
				setTimeout(() => {
					this.$Router.push({
						name: 'mainLogin',
					})
				}, 500)
			}
		},
		async holdSeriesList() {
			let sortOrder;
			if (this.isCheckend == 0) {
				sortOrder = this.isActiveTime == 1 ? 'desc' : 'asc'
			} else {
				sortOrder = this.isActivePrice == 1 ? 'desc' : 'asc'
			}
			const {
				status,
				result
			} = await this.$api.getUserHoldSeriesList({
				pageNum: this.pageNum,
				pageSize: 10,
				sale: this.sale,
				sortField: this.isCheckend == 0 ? 1 : 2,
				sortOrder
			});
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.seriesList == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.seriesList.push(item)
					})
				}
			} else if (status.code == 1002) {
				this.isLoadding = true
				setTimeout(() => {
					this.isLoadding = false
					this.$Router.push({
						name: "mainLogin"
					})
				}, 1500);
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getSaleHead() {
			let res = await this.$api.getAppUserHoldSeriesCountAndAvgPriceVO({
				ctid: this.ctid
			});
			if (res.status.code == 0) {
				this.popupHead = res.result
				this.getUserSeries()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getUserSeries() {
			let res = await this.$api.userSeriesCollectionList({
				ctid: this.ctid,
				pageNum: this.popuePageNum
			});
			if (res.status.code == 0) {
				if (res.result.list != '') {
					this.popuePageNum++
					res.result.list.forEach((item) => {
						item.tidd = desensitizeMobile(item.tid)
						this.popupList.push(item)
					})
					console.log(this.popupList, 'popuipliast');
				} else {

				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		lower() {
			console.log("触底了")
			this.getUserSeries()
		},
		scroll(e) {
			console.log('下拉了2')
		},
		refresher() {
			this.triggered = true
			this.popuePageNum = 1
			this.popupList = []
			this.getUserSeries()
			setTimeout(() => {
				this.triggered = false
			}, 1000)
		},
		copy() {
			uniCopy({
				content: this.info.contractAddress,
				success: (res) => {
					uni.showToast({
						title: res,
						icon: 'none'
					})
				},
				error: (e) => {
					uni.showToast({
						title: e,
						icon: 'none',
						duration: 3000,
					})
				}
			})
		},
		async batchUnSaleCollectionList() {
			let res = await this.$api.batchUnSaleCollectionList({
				ctid: this.ctid
			});
			if (res.status.code == 0) {
				this.solePopupBox = false
				this.seriesList = []
				this.holdSeriesList()
				uni.showToast({
					title: '批量取消寄售成功',
					icon: 'none',
					duration: 3000
				});
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_details(item) {
			this.$Router.push({
				name: "detailsShop",
				params: {
					tid: item.tid,
					sale: item.sale
				}
			})
		},
		toggleAnimation() {
			this.showAnimation = !this.showAnimation;
			if (this.showAnimation && !this.animationInstance) {
				// 当动画显示且尚未创建实例时，创建并初始化
				this.$nextTick(() => {
					this.animationInstance = this.$refs.lottieWeb.player;
					this.$refs.lottieWeb.call('play')
				});
			} else if (!this.showAnimation && this.animationInstance) {
				// 当动画隐藏且已存在实例时，停止并销毁
				this.$refs.lottieWeb.call('stop')
				this.$refs.lottieWeb.call('destroy')
				this.animationInstance = null;
			}
		},
		onReadyy() {
			if (this.showAnimation) {
				this.$refs.lottieWeb.call('play')
			}
		},
		onReadyy_ip() {
			if (this.showAnimation) {
				this.$refs.lottieWeb_ip.call('play')
			}
		},
		async saleSeriesList() {
			const {
				status,
				result
			} = await this.$api.soldList({
				pageNum: this.pageNum,
				pageSize: 10,
				keyword: ""
			});
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.seriesList == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.seriesList.push(item)
					})
				}
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_assetInfo() {
			let res = await this.$api.assetInfo({
				type: this.type || 0
			});
			if (res.status.code == 0) {
				this.assetInfo = res.result
				this.percentage = res.result.changeRatio
				if (res.result.changeAmount == null) {
					this.null_type = false
				} else {
					this.null_type = true
				}
				if (res.result.changeAmount < 0) {
					this.Flg = false
				} else {
					this.Flg = true
				}
				this.userInfo()
				this.getAccumulated()
			} else if (res.status.code == 1002) {
				this.isLoadding = true
				setTimeout(() => {
					this.isLoadding = false
					this.$Router.push({
						name: "mainLogin"
					})
				}, 1500);
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getAccumulated() {
			let res = await this.$api.accumulated_can_withdrawal({

			});
			if (res.status.code == 0) {
				this.jShow = true
				this.jjsNum = res.result.allWithdrawal
			}
		},
		nav_broker() {
			this.$Router.push({
				name: "broker"
			})
		},
		openMore(item, index) {
			console.log(item, '12222');

			this.sunIndex = index
			this.moreInfo = item
			this.isMore = true

		},
		closeMore() {
			this.isMore = false
		},

		// 调用设置头像
		openSet() {
			this.isMore = false
			this.$refs.modalPop.openPop('set');
		},
		// 调用停售作品
		openDown() {
			this.isMore = false
			this.$refs.modalPop.openPop('down');
		},
		// 调用寄售售作品
		openUp() {
			this.isMore = false
			this.$refs.modalPop.openPop('up');
		},
		// 调用销毁作品
		openDestroy() {
			this.isMore = false
			this.$refs.modalPop.openPop('destroy');
		},
		downSucceed() {
			this.popupList[this.sunIndex].sale = 0
		},
		upSucceed() {
			this.popupList[this.sunIndex].sale = 1
		},
		destroySucceed() {
			this.popupList.splice(this.sunIndex, 1);
		},
		showIp() {
			if (this.ipShow && !this.suo) {
				console.log(1111)
				this.suo = true
				this.ipShow = false
				let timeout = setTimeout(() => {
					this.ipShow = true
					this.suo = false
					clearInterval(timeout)
				}, 5100)
			}
			console.log(this.ipShow)
		},
		nav_series() {
			this.$Router.push({
				name: "seriesList",
				params: {
					ctid: this.ctid
				}
			})
		},
		checkSort(type) {
			this.isLoadingStatus = 0
			this.seriesList = []
			this.pageNum = 1
			this.isFooter = true
			if (type == 1) {
				this.isCheckend = 1
				this.isActiveTime = 1
				if (this.isActivePrice == 1) {
					this.isActivePrice = 2
				} else {
					this.isActivePrice = 1
				}
				this.holdSeriesList()
			} else {
				this.isCheckend = 0
				this.isActivePrice = 1
				if (this.isActiveTime == 1) {
					this.isActiveTime = 2
				} else {
					this.isActiveTime = 1
				}
				this.holdSeriesList()
			}
			console.log(this.isActiveTime, this.isCheckend)
		}
	},
	components: {
		TabBar,
		modalPop,
		payPopup,

	}
}
</script>
<style lang="scss">
::v-deep .solePopup {
	.u-drawer-bottom {
		background-color: transparent !important;
	}
}

::v-deep .uni-scroll-view-refresher {
	background-color: transparent !important;
}

::v-deep .u-input__input {

	color: #63EAEE !important;
	font-weight: 600;
	padding: 0px 10rpx;
	border-radius: 7rpx;

	&::placeholder {}
}

::v-deep .u-input__placeholder {
	font-size: 14px;
	/* 修改placeholder字体大小 */
	color: #00E4FF !important;

	/* 修改placeholder字体颜色 */
}

.new-modal-content {
	padding: 35rpx 40rpx;

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 160rpx;
			height: 160rpx;
		}
	}

	.modal-content {
		padding: 35rpx 0rpx;
		border-bottom: 1rpx solid #EDEDED;
		font-size: 28rpx;
		color: #fff;
		text-align: center;
	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 266rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #fff;
			border-radius: 40rpx;
		}

		.img_cancel {
			border: 1px solid #B6B6B6;
		}

		.img_reasale {
			background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);

			border-radius: 40rpx;
			color: #FFF;
		}
	}
}

.collect_card {
	width: 630rpx;
	height: 506rpx;
	background: #35333E;
	border-radius: 30rpx;
	font-weight: bold;
	font-size: 32rpx;
	color: #000000;
	font-style: normal;
	text-transform: none;


	.collect_card_head {
		width: 100%;
		height: 80rpx;
		// border: 1rpx solid red;
		position: relative;

		.bg1 {
			width: 250rpx;
			height: 8rpx;
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			margin: auto;
		}

		.title {
			text-align: center;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #fff;
			font-style: normal;
			text-transform: none;
		}




	}

	.collect_card_content {
		padding: 10rpx 50rpx 50rpx 50rpx;

		.body {
			display: flex;
			align-items: center;
			padding-top: 30rpx;

			.text {

				font-weight: 400;
				font-size: 28rpx;
				color: #fff;

				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.input {
				color: #63EAEE;
				font-weight: 600;
				font-size: 17px;
				background-color: #25232D;

				width: 380rpx;
				height: 90rpx;
				border-radius: 14rpx;
				padding-left: 30rpx;
				padding-top: 10rpx;
				padding-right: 20rpx;
				margin-left: 20rpx;
			}
		}
	}

	.btn {
		width: 520rpx;
		height: 90rpx;
		background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 45rpx;
		color: #121212;
		line-height: 90rpx;
		text-align: center;
		margin: auto;
	}
}

.popup-content {
	height: 100%;
	display: flex;
	align-items: flex-end;
	position: relative;

	.tx_view {
		width: 260rpx;
		height: 260rpx;
		position: absolute;
		top: 0;
		left: 40rpx;
		z-index: 99;

		image {
			width: 260rpx;
			border-radius: 30rpx;
			height: 260rpx;
		}
	}

	.content_view {
		width: 100%;
		height: 920rpx;
		background: linear-gradient(180deg, #25232D 0%, #111116 100%);
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		position: relative;

		.right_icon {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240424/e03a47db708213812446d72957dc867e_702x676.png);
			width: 351rpx;
			height: 338rpx;
			background-size: 100% 100%;
			position: absolute;
			right: 0;
			top: 0;
			z-index: 0;
		}

		.right_font_view {
			padding: 20rpx 50rpx 60rpx 340rpx;
			z-index: 2;

			.title {
				color: #fff;
				font-size: 28rpx;
				width: 100%;
				height: 70rpx;
			}

			.num {
				color: #A6A6A6;
				font-size: 28rpx;

				.gs {
					margin-bottom: 10rpx;
				}

				.jj {
					text {
						color: #63EAEE;
					}
				}
			}

			.mall_right {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 22rpx;
				background-color: #35333E;
				border: 1px solid #fff;
				border-radius: 6rpx;
				padding: 8rpx;
				width: 100rpx;
				position: absolute;
				top: 66rpx;
				right: 50rpx;
				z-index: 2;

				text {
					margin-left: 6rpx;
					color: #fff;
					font-size: 22rpx;

				}

				image {
					width: 30rpx;
				}
			}
		}

		.sole_ul_view {
			padding: 0rpx 40rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;

			.sole_ul_li {
				width: 200rpx;
				height: 150rpx;
				margin-bottom: 35rpx;
				margin-right: 35rpx;
				border-radius: 24rpx;
				overflow: hidden;

				&.active {
					border: 1px solid #63EAEE;
				}

				.type {
					height: 60rpx;
					background-color: #35333E;
					display: flex;
					justify-content: center;
					align-items: center;
					color: #fff;
					font-size: 22rpx;

					&.active {
						color: #25232D;
						background-color: #63EAEE;
					}
				}

				.price_tid {
					height: 90rpx;
					background: #46454F;

					.price {
						width: 100%;
						font-size: 28rpx;
						line-height: 34rpx;
						font-weight: 600;
						color: #63EAEE;
						text-align: center;
						font-weight: 600;
						padding-top: 10rpx;
					}

					.tid_view {
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 20rpx;
						color: var(--default-color3);
						margin-top: 10rpx;
					}
				}
			}

			.sole_ul_li:nth-child(3n) {
				margin-right: 0rpx;
			}

		}

		.sale_view {
			width: 670rpx;
			height: 120rpx;
			border: 1px solid #63EAEE;
			color: #63EAEE;
			font-size: 34rpx;
			font-weight: 600;
			position: fixed;
			bottom: 20rpx;
			left: 40rpx;
			right: 40rpx;
			border-radius: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #111116;
		}
	}
}

.head_bg {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	height: 500rpx;
	z-index: -1;
	background-color: #fff;
	will-change: transform;
	transform: translateZ(0);
}

.user_head {
	padding: 60rpx 36rpx 0rpx 36rpx;

	.logo {
		width: 168rpx;
		height: 60rpx;
		margin-bottom: 60rpx;

		image {
			width: 100%;
		}
	}

	.top_IP {
		padding-top: 40rpx;
		position: relative;

		.right_ip {
			position: absolute;
			bottom: 0;
			right: 0rpx;
		
			image {
				width: 160rpx;
			}
		}
		.gz {
			font-size: 28rpx;
			color: #fff;
			margin-bottom: 30rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			image {
				width: 36rpx;
				height: 36rpx;
				margin-left: 30rpx;
			}
		}

		.asset {
			font-size: 44rpx;
			color: #fff;
			font-weight: 600;
			margin-bottom: 20rpx;
		}

		.shouyi {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			font-size: 24rpx;

			>.left {
				margin-right: 30rpx;
				color: #EC4068;
			}

			>.right {
				color: #EC4068;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				position: relative;

				image {
					width: 20rpx;
					margin-left: 10rpx;
				}

				.timBox {
					position: absolute;
					top: 0;
					left: 0;
					width: 120rpx;
					height: 230rpx;
					background: #35333E;
					box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(0, 0, 0, 0.3);
					border-radius: 18rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;

					>view {
						width: 100%;
						height: calc(230rpx / 4);
						line-height: calc(230rpx / 4);
						text-align: center;
					}
				}
			}
		}
	}

	.data_cart {
		background: #25232D;
		width: 100%;
		height: 240rpx;
		border-radius: 24rpx;
		display: flex;
		flex-wrap: wrap;
		margin-top: 40rpx;

		.li {
			width: 50%;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;

			text {
				color: var(--default-color3);
				font-size: 24rpx;
			}

			.price {
				color: #fff;
				font-size: 28rpx;
				font-weight: 600;
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 24rpx;
				}

				text {
					font-size: 28rpx;
					color: #fff;
				}
			}
		}
	}

	.data_cart1 {
		width: 678rpx;
		height: 160rpx;
		background: #25232D;
		box-shadow: 0rpx 6rpx 24rpx 1rpx rgba(74, 75, 99, 0.1);
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin-top: 40rpx;

		.li {
			min-width: 20%;
			display: flex;
			align-items: center;
			text-align: center;

			text {
				color: var(--default-color3);
				font-size: 24rpx;
			}

			.price {
				color: #fff;
				font-size: 28rpx;
				font-weight: 600;
				margin-top: 20rpx;
			}

		}
	}

	.user_info {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;

		.img {
			width: 120rpx;
			height: 120rpx;
			margin-right: 30rpx;
			border-radius: 50%;
			overflow: hidden;

			image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
			}
		}

		.info {
			.name {
				font-size: 36rpx;
				font-weight: 600;
				line-height: 48rpx;
				display: flex;
				align-items: center;

				text {
					margin-right: 10rpx;
					display: block;
					/* 确保元素为块级元素，以便设置宽度 */
					overflow: hidden;
					/* 隐藏超出部分的内容 */
					text-overflow: ellipsis;
					/* 当内容溢出时以省略号显示 */
					white-space: nowrap;
					/* 强制文本在同一行内不换行 */
					width: max-content;
					/* 允许内容决定最小宽度 */
					max-width: 300rpx;
					/* 设置最大宽度为200px，超过此宽度时应用省略号 */
				}
			}

			.label {
				font-size: 24rpx;
				color: #A6A6A6;
			}

			.nft {
				font-size: 24rpx;
				color: #A6A6A6;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				text {
					margin-right: 10rpx;
				}
			}
		}
	}

	.ip {
		position: absolute;
		right: -56rpx;
		top: -100rpx;
		// background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/xCase/xCaseIP/xiaogou%20%285%29.png);
		// animation: example 3.5s steps(1, end) forwards;
		// animation-delay: 1s;
		z-index: -1;
		width: 180rpx;
		height: 240rpx;
		background-size: 100% 100%;
	}

	.animation {}
}

.cart_view {
	width: 100%;
	height: 190rpx;
	border-radius: 24rpx;
	opacity: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 20rpx;
	color: #fff;

	.li {
		width: 120rpx;

		.icon {
			display: flex;
			justify-content: center;

			image {
				width: 120rpx;
			}

			margin-bottom:12rpx;
		}

		.text {
			text-align: center;
			font-size: 24rpx;
			width: 120rpx;
		}
	}
}

.tabbar_view {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 28rpx;
	padding: 0rpx 36rpx;

	.tabber {
		width: 500rpx;
	}

	.sousuo {
		width: 120rpx;
		height: 56rpx;
		background-color: #35333E;
		border-radius: 28rpx;
		border: 1px solid #fff;

		.icon {
			height: 56rpx;
			display: flex;
			align-items: center;
			padding-left: 14rpx;

			image {
				width: 36rpx;
				height: 36rpx;
			}
		}
	}

}

.sort {
	padding: 24rpx 36rpx 0rpx 36rpx;
	display: flex;
	justify-content: flex-end;
	align-items: center;

	.sort_item {
		margin-left: 40rpx;

		.time,
		.price {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			width: 140rpx;
			color: rgba(255, 255, 255, 0.5);
			font-size: 24rpx;

			&.active {
				color: var(--active-color1);
			}

			image {
				width: 36rpx;
				margin-left: 6rpx;
			}
		}

	}
}

.collection {
	padding: 0rpx 36rpx;
	margin-top: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	padding-bottom: 80rpx;

	.li {
		width: 320rpx;
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #25232D;
		border-radius: 30rpx;

		.cover {
			position: relative;
			width: 280rpx;
			height: 280rpx;
			border-radius: 20rpx;

			image {
				width: 280rpx;
				height: 280rpx;
				border-radius: 20rpx;
			}

			.left_bottom_icon {
				position: absolute;
				bottom: 0;
				left: 0;
				min-width: 140rpx;
				height: 44rpx;
				border-radius: 0px 20rpx 0px 30rpx;
				text-align: center;
				font-size: 20rpx;
				color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
				background: rgba(20, 20, 20, 0.7);
				padding: 0rpx 30rpx;
				border-radius: 0px 20px 0px 30px;
			}
		}

		.title {
			width: 100%;
			font-size: 24rpx;
			color: #fff;
			margin-top: 20rpx;
		}

		.total_text {
			color: #fff;
			font-size: 24rpx;
			margin-top: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.jishou {
				width: 118rpx;
				height: 36rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240424/6ea361dba85c051ba1bbbbe9b092ef5f_236x72.png);
				background-size: 100% 100%;
				color: #141816;
				font-size: 18rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 600;
			}

			text {
				color: #63EAEE;
			}
		}
	}
}

.null_body {
	.null {

		.img {
			display: flex;
			justify-content: center;

			image {
				width: 242rpx;
			}
		}

	}

	.text {
		color: #A6A6A6;
		font-size: 28rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	width:100%;
	height: 40vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

.scroll-Y {
	max-height: 670rpx;
}

::v-deep .u-drawer-bottom {
	background-color: #35333E !important;
}

.mall_more_header {
	height: 100rpx;
	line-height: 100rpx;
	color: #FFFFFF;
	width: 100%;
	text-align: center;
	position: relative;
	font-size: 32rpx;

	.close {
		position: absolute;
		right: 30rpx;
		top: 26rpx;
		z-index: 99;

		image {
			width: 42rpx;
			height: 42rpx;
		}
	}
}

.mall_more_ul {
	position: relative;

	.close {
		position: absolute;
		right: 0rpx;
		top: 0rpx;

		image {
			width: 80rpx;
		}
	}

	.li {
		display: flex;
		justify-content: center;
		align-items: center;
		color: #FFFFFF;
		border-bottom: 1px solid #53505D;
		font-size: 28rpx;
		padding: 38rpx 0rpx;

		.icon {
			margin-right: 20rpx;

			image {
				width: 44rpx;
			}
		}

		text {
			margin-right: 20rpx;
		}
	}

	.li:last-child {
		border-bottom: none;
	}
}

.footer_bottom_price {
	min-width: 200rpx;
	font-size: 44rpx;
	color: #00E4FF;
	font-weight: 600;
}
</style>