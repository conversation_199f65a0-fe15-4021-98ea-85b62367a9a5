<template>
    <view class="wallet-page">
        <view class="barHeight"></view>
        <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false" :is-back="false"
            :background="{ backgroundColor: 'var(--main-bg-color)' }" title="钱包" title-color="var(--default-color1)"
            title-bold>
        </u-navbar>
        <!-- 顶部余额部分 -->
        <view class="wallet-header">
            <view class="balance-box">
                <view class="balance">账户余额</view>
                <view class="amount">¥{{ balance || 0.00 }}</view>
                <view class="withdraw">提现中:¥{{ withdrawing || 0.00 }}</view>
            </view>

            <view class="buttons">
                <view class="button" @click="nav_pay">充值</view>
                <view class="button" @click="nav_withdraw">提现</view>
            </view>
        </view>

        <!-- 图标导航 -->
        <view class="cart_view">
            <view class="li" @tap="nav_to('bank')">
                <view class="icon">
                    <image
                        src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/********/394252d6e8935e9e957896326d40d70d_240x240.png"
                        mode="widthFix"></image>
                </view>
                <view class="text">
                    <text>银行卡</text>
                </view>
            </view>
            <view class="li" @tap="nav_to('CouponPacks')">
                <view class="icon">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/********/8d105bda8965cfa39b244b06449488f2_240x240.png"
                        mode="widthFix"></image>
                </view>
                <view class="text">
                    <text>券包</text>
                </view>
            </view>
            <view class="li" @tap="nav_to('myBalanceYs')">
                <view class="icon">
                    <image
                        src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/********/bd3e4aa1668e1121cb67cc17298d94f9_240x240.png"
                        mode="widthFix"></image>
                </view>
                <view class="text">
                    <text>对账单</text>
                </view>
            </view>
        </view>

        <!-- Tab 切换 -->
        <view class="tabs">
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: activeTab === index }]"
                @click="switchTab(index)">
                {{ tab }}
            </view>
            <view class="tab-indicator"
                :style="{ left: activeTab == 0 ? '50rpx' : activeTab == 1 ? '240rpx' : activeTab == 2 ? '425rpx' : '590rpx' }">
            </view>
        </view>

        <!-- 内容部分 -->
        <view class="content">
            <view v-if="activeTab === 0" class="tab-content">
                <!-- 等待成交 -->
                <!-- 当前委托 List-->
                <view class="weituo">
                    <view class="li" v-for="(item, index) in entrustList" :key="index">
                        <!-- -->
                        <view class="heads">
                            <view class="left">
                                <image v-if="item.side == 'SELL'"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                    alt="" mode="widthFix"></image>
                                <!-- -->
                                <image v-else
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                    alt="" mode="widthFix"></image>
                                <text class="symbol">{{ item.symbol || '--' }}</text>
                                <text class="level" :style="{ color: item.side == 'SELL' ? '#6CFF8A' : '#FF526F' }">{{
                                    item.leverageLevel }}x</text>
                                <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                                <text class="sub">{{ item.ctime || '--' }}</text>
                                <!-- {{ item.createAt }} -->

                                <view class="cd">

                                    <view class="progress">
                                        <text>{{ dealprogress(item) }}%</text>
                                        <view class="bg">
                                            <view class="bar"
                                                :style="{ width: (item.dealMoney / item.money) * 100 + '%' }">
                                            </view>
                                        </view>
                                    </view>
                                    <!-- @click="openPop(item, 1, index)" v-if="item.canRevoke" -->
                                    <view v-if="item.status == 5" class="cancel">
                                        <image mode="widthFix" class="rotate-image"
                                            src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                                    </view>
                                    <view class="che" @click="backuporder(item)" v-else><text>撤单</text></view>
                                </view>
                            </view>

                        </view>
                        <!-- card 底部 -->
                        <view class="data_view">
                            <view class="li_view">
                                <view class="label">成本</view>
                                <view class="num" :style="{ marginTop: item.experienceMoney > 0 ? '10rpx' : '10rpx' }">
                                    <text>{{ '￥' + formatNumber(item.payMoney - item.experienceMoney) }}</text>
                                    <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}({{ item.trailType ==
                                        1
                                        ? '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                                    <!-- <text>+￥{{ item.experienceMoney }}(体验金)</text> -->
                                </view>
                            </view>
                            <view class="li_view">
                                <view class="label">成交金额/下单金额</view>
                                <view class="num">{{ '￥' + formatNumber(item.dealMoney, 3) + ' / ' + '￥' +
                                    formatNumber(item.money, 3) }}</view>
                                <!-- {{ item.filledQuantity }} -->
                            </view>
                            <view class="li_view">
                                <view class="label">
                                    <text>下单价格</text>
                                    <!-- <text v-show="item.longShort == 2">唱空价</text>
                                <text v-show="item.longShort == 1">唱多价</text> -->
                                </view>
                                <view class="num" v-if="item.price">{{ '￥' + item.price.toFixed(3) || '--' }}</view>
                                <view class="num" v-else>{{ '市价' }}</view>
                                <!-- {{ item.price }} -->
                            </view>
                        </view>
                    </view>

                    <view class="nodata" v-if="!entrustList.length">
                        <image mode="widthFix"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                        <text>{{ token ? '暂时无数据' : '您还未登录' }}</text>
                        <view class="nav_login" @tap="nav_login" v-if="!token">
                            登录/注册
                        </view>
                    </view>
                </view>
            </view>
            <view v-else-if="activeTab === 1" class="tab-content">
                <!-- 持仓 List -->
                <view class="cangwei">
                    <!-- <transition name="van-fade"> -->
                    <view class="li" v-for="(item, index) in positionsList" :key="index">
                        <!-- :class="{ 'active': item.isActive }" v-for="(item, index) in positionsList"
                    :key="index" -->
                        <view class="heads">
                            <view class="left">
                                <!-- v-if="item.longShort == 2" -->
                                <image v-if="item.side == 'SELL'"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                    alt="" mode="widthFix" />
                                <image v-else
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                    alt="" mode="widthFix"></image>
                                <text class="symbol">{{ item.symbol || '--' }}</text>
                                <text class="level" :style="{ color: item.side == 'SELL' ? '#6CFF8A' : '#FF526F' }">{{
                                    item.leverageLevel + 'x' }}</text>
                                <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                            </view>
                            <!-- <view class="live_data">
                                <view class="sy">
                                    <text>收益</text>
                                    <text :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"> {{
                                        calculateEarnings(item) }}
                                    </text>
                                </view>

                                <view class="syl red">
                                    <text>收益率 </text>
                                    <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"> {{ calculateYield(item)
                                        }}
                                    </text>
                                </view>
                            </view> -->
                        </view>
                        <view class="right_fenx">
                            <!-- @click="openShare(item)" -->
                            <image @click="openShare(item)"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240729/0d72a446e4fdb9f704719716b76e3622_72x80.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>



                        <view class="data_views">
                            <view class="li_view">
                                <view class="label">开仓金额</view>
                                <!-- {{ item.longShort == 2backuporder ? "唱空" : "唱多" }}  :style="{ marginTop: item.experienceMoney > 0 ? '12rpx' : '20rpx' }"-->
                                <view class="num">
                                    <text>{{ '￥' + formatNumber(item.money - item.experienceMoney) }}</text>
                                    <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}({{ item.trailType ==
                                        1
                                        ? '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                                    <!-- <text>+￥{{ item.experienceMoney }}(体验金)</text> -->
                                </view>
                                <!-- {{ item.price }} -->
                            </view>
                            <view class="li_view">
                                <view class="label">{{ item.side == 'SELL' ? '唱空均价' : '唱多均价' }}</view>
                                <view class="num">{{ '￥' + formatNumber(item.price, 3) }}</view>
                                <!-- {{ item.quantity }} -->
                            </view>
                            <view class="li_view">
                                <view class="label">强平价</view>
                                <view class="num">{{ item.reducePrice ? '￥' + formatNumber(item.reducePrice, 3) : '--'
                                    }}
                                </view>
                                <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                            </view>
                            <!-- <view class="li_view" v-if="item.takeProfitPrice || item.stopLossPrice">
                            <view class="label">止盈价</view>
                            <view class="num" v-if="item.takeProfitPrice">{{ '￥' + item.takeProfitPrice || '--' }}
                            </view>
                            <view class="num" v-else>--</view>

                        </view>
                        <view class="li_view" v-if="item.stopLossPrice || item.takeProfitPrice">
                            <view class="label">止损价</view>
                            <view class="num" v-if="item.stopLossPrice">{{ '￥' + item.stopLossPrice }}</view>
                            <view class="num" v-else>--</view>
                        </view> -->
                        </view>
                        <view class="stoploss" v-if="item.takeProfitPrice || item.stopLossPrice">
                            <text class="left">止盈止损</text>
                            <view class="right">
                                <text>{{ item.takeProfitPrice || '--' }}</text>
                                <view style="color: #fff;">/</view>
                                <text>{{ item.stopLossPrice || '--' }}</text>
                                <!-- <image mode="widthFix"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240825/12d33c892712b44c2740d7f8064f7b5c_43x43.png" /> -->
                            </view>
                        </view>
                        <view class="close" :style="{ marginTop: '27rpx' }">
                            <!-- @click="openPop(item, 2, index)" v-if="item.canClose" -->
                            <!-- <view @tap="setProLoss(item)"
                                v-if="item.stopLossPrice == null && item.takeProfitPrice == null && EnableproLoss">
                                设置止盈止损</view>
                            <view @tap="resetProLoss(item)"
                                v-if="(item.stopLossPrice || item.takeProfitPrice) && EnableproLoss">重置止盈止损</view>
                            <view @tap="nobalance = true" class="unlock"
                                v-if="!EnableproLoss && item.stopLossPrice == null && item.takeProfitPrice == null">
                                解锁止盈止损
                            </view> -->
                            <view v-if="(item.pendingCloseVolume != item.closeVolume)" @click="toastPC"
                                style="margin-left: 30rpx;opacity: .5;">平仓中
                                <image mode="widthFix" class="rotate-image"
                                    style="width: 18rpx;height: 24rpx;margin-left: 10rpx;"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                            </view>
                            <!-- margin-left: 30rpx; -->
                            <view @tap="closeonce(item)" style="display: flex;align-items: center;" v-else>
                                <text style="font-size: 22rpx;font-weight: 400;">一键平仓</text>
                                <text style="font-size: 14rpx;font-weight: 400;margin-left: 5rpx;"
                                    v-if="item.autoCloseTime">最晚至{{ item.autoCloseTime.split('.')[0] }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="nodata" v-if="!positionsList.length">
                        <image mode="widthFix"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                        <text>{{ token ? '暂时无数据' : '您还未登录' }}</text>
                        <view class="nav_login" @tap="nav_login" v-if="!token">
                            登录/注册
                        </view>
                    </view>
                    <!-- </transition> -->
                </view>
            </view>
            <view v-else-if="activeTab === 2" class="tab-content">
                <view class="podetails">
                    <!-- 当前委托 List-->
                    <view class="weituoAll">
                        <view class="li" v-for="(item, index) in entrustListAll" :key="index">
                            <!-- -->
                            <view class="heads">
                                <view class="left">
                                    <text class="symbol">{{ item.symbol || '--' }}</text>
                                    <image v-if="item.side == 'SELL'"
                                        src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                        alt="" mode="widthFix"></image>
                                    <!-- -->
                                    <image v-else
                                        src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                        alt="" mode="widthFix"></image>

                                    <text class="level">{{ item.leverageLevel }}X</text>
                                    <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                                    <text class="sub">{{ item.ctime || '--' }}</text>
                                    <!-- {{ item.createAt }} -->
                                </view>
                                <view class="cd">

                                    <view class="progress" v-if="item.status != 2 && item.status != 4">
                                        <text>{{ dealprogress(item) }}%</text>

                                        <view class="bg">
                                            <view class="bar"
                                                :style="{ width: (item.dealMoney / item.money) * 100 + '%' }">
                                            </view>
                                        </view>
                                    </view>
                                    <!-- @click="openPop(item, 1, index)" v-if="item.canRevoke" -->

                                    <view v-if="item.status != 2 && item.status != 4" class="che ml20"
                                        @click="backuporder(item)">
                                        <text>撤单</text>
                                    </view>
                                    <view v-if="item.status == 5" class="cancel">
                                        <image mode="widthFix" class="rotate-image"
                                            src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                                    </view>
                                    <view class="hasdeal " v-if="item.status == 2" style="color: #fff">已成交</view>
                                    <view class="hasdeal " v-if="item.status == 4" style="border:1rpx solid #6B6B6B">已撤单
                                    </view>

                                </view>
                            </view>
                            <!-- card 底部 -->
                            <view class="data_view">
                                <view class="li_view">
                                    <view class="label">成本</view>
                                    <view class="num">
                                        <text>{{ '￥' + formatNumber(item.payMoney - item.experienceMoney, 3, 'exper')
                                            }}</text>
                                        <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}({{ item.trailType
                                            == 1 ? '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                                        <!-- <text >+￥{{ item.experienceMoney }}(体验金)</text> -->
                                    </view>
                                </view>
                                <view class="li_view">
                                    <view class="label">成交金额<text
                                            v-if="item.status != 2 && item.status != 4">/下单金额</text>
                                    </view>
                                    <view class="num" v-if="item.status != 2 && item.status != 4">
                                        {{ '￥' + formatNumber(item.dealMoney, 3) + ' / ￥' + formatNumber(item.money, 3)
                                        }}
                                    </view>
                                    <view class="num" v-else>{{ '￥' + formatNumber(item.dealMoney, 3) }}</view>
                                </view>
                                <view class="li_view">
                                    <view class="label">
                                        <text>下单价格</text>
                                        <!-- <text v-show="item.longShort == 2">唱空价</text>
                                <text v-show="item.longShort == 1">唱多价</text> -->
                                    </view>
                                    <!-- <view class="num">￥{{ formatNumber(item.avgPrice, 3) || '--' }}</view> -->
                                    <!-- {{ item.price }} -->
                                    <view class="num" v-if="item.price">{{ '￥' + item.price.toFixed(3) || '--' }}</view>
                                    <view class="num" v-else>{{ '市价' }}</view>

                                </view>
                            </view>
                        </view>
                        <view class="nodata" v-if="!entrustListAll.length">
                            <image mode="widthFix"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                            <text>暂时无数据</text>
                        </view>
                    </view>
                </view>
            </view>
            <view v-else-if="activeTab === 3" class="tab-content">
                <!-- 持仓 List -->
                <view class="podetails">
                    <view class="cangweiAll">
                        <view class="li" v-for="(item, index) in positionsListAll" :key="index">
                            <view class="heads">
                                <view class="left">
                                    <!-- v-if="item.longShort == 2" -->
                                    <text class="symbol">{{ item.symbol || '--' }}</text>
                                    <image v-if="item.side == 'SELL'"
                                        src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                        alt="" mode="widthFix" />
                                    <image v-else
                                        src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                        alt="" mode="widthFix"></image>
                                    <view class="rightinfo">
                                        <text class="level">x {{ item.leverageLevel }}</text>
                                        <!-- <text class="times">{{ item.openTime + ' - ' + item.closeTime }}</text> -->
                                        <!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                        <view class="live_data" v-if="item.status == 0"
                                            :style="{ borderRadius: (item.trailType && item.epProfit != null) ? '20rpx' : '' }">
                                            <view class="sy red">
                                                <text class="text">收益</text>
                                                <view
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">
                                                    <text class="text" v-if="item.status == 0"
                                                        style="margin-left: 4rpx;"
                                                        :class="[item.trailType == 2 || item.trailType == 1 ? 'textline' : '']"
                                                        :style="{ color: (item.profit) >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            (item.profit) >= 0 ? '+' : '' }} {{ item.profit.toFixed(2) || 0
                                                        }}
                                                    </text>
                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>
                                                </view>
                                                <!-- <text style="margin-left: 10rpx;"
                                                    :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateEarnings(item) }}
                                                </text> -->

                                            </view>
                                            <view class="syl red">
                                                <!-- :class="{ 'red': item.red2 }" -->
                                                <text>收益率</text>
                                                <!-- <text v-if="item.status == 0"
                                                    :style="{ color: item.profit >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                        item.profit >= 0 ? '+'
                                                            : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                </text> -->

                                                <view v-if="item.status == 0"
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">

                                                    <text v-if="item.status == 0"
                                                        :class="[item.trailType == 2 || item.trailType == 1 ? 'textline' : '']"
                                                        :style="{ color: item.profitRate >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            item.profitRate >= 0 ? '+'
                                                                : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                    </text>

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>
                                                </view>
                                                <!-- <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateYield(item) }}
                                                </text> -->
                                            </view>
                                        </view>
                                    </view>

                                    <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                                </view>
                            </view>
                            <view class="right_fenx">
                                <image @click="openShares(item)"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240729/0d72a446e4fdb9f704719716b76e3622_72x80.png"
                                    alt="" srcset="" mode="widthFix"></image>
                            </view>

                            <view class="qiang" v-if="item.closeType == 2">
                                <image mode="widthFix"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240807/f33447ea72684920c1bebb719a3c79f9_144x64.png" />
                            </view>
                            <view class="qiang ADL" v-if="item.closeType == 3" @click="bitshow = true">
                                ADL
                            </view>

                            <view class="data_views" :style="{ marginTop: item.closeType == 2 ? '12rpx' : '29rpx' }">

                                <view class="li_view">
                                    <view class="label">{{ item.status == 0 ? '成本' : '开仓金额' }}</view>
                                    <view class="num">
                                        <text>{{ '￥' + formatNumber((item.money - item.experienceMoney), 3, 'exper')
                                            }}</text>
                                        <text v-if="item.experienceMoney"> +￥{{ item.experienceMoney }}({{
                                            item.trailType
                                                == 1 ? '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                                    </view>
                                </view>

                                <view class="li_view">
                                    <view class="label">{{ item.side == 'SELL' ? '唱空均价' : '唱多均价' }}</view>
                                    <view class="num">{{ '￥' + formatNumber(item.price, 3) }}</view>
                                    <!-- <view>1</view> -->
                                </view>

                                <view class="li_view">
                                    <view class="label">{{ item.status == 0 ? positionType(item) : '强平价' }}</view>
                                    <view class="num" v-if="item.status == 0">{{ '￥' + formatNumber(item.closePrice, 3)
                                        }}
                                    </view>
                                    <view class="num" v-else>{{ '￥' + formatNumber(item.reducePrice, 3) }}</view>
                                    <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                                </view>


                                <view class="li_view">
                                    <view class="label">资金费用</view>
                                    <!-- color: #6CFF8A;
                            color: #FF5270; -->
                                    <view class="num"
                                        :style="{ color: item.capitalFee > 0 ? '#FF5270' : (item.capitalFee < 0 ? '#6CFF8A' : '') }">
                                        {{
                                            (item.capitalFee > 0 ? '+' : (item.capitalFee < 0 ? '-' : '')) + '￥' +
                                            formatNumber(Math.abs(item.capitalFee), 2, 'fund') }} </view>
                                            <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                                    </view>
                                </view>
                                <!-- 
                     :style="{ marginTop: item.experienceMoney > 0 ? '23rpx' : '23rpx' }"
                    <view class="fee" :style="{ marginTop: item.experienceMoney > 0 ? '16rpx' : '26rpx' }"> -->

                                <view :style="{ marginTop: item.experienceMoney > 0 ? '20rpx' : '20rpx' }"
                                    style="display: flex;height:100%;justify-content: space-between;align-items: center;">
                                    <view class="fee">
                                        <text class="fees">开仓手续费: ¥ {{ item.openFee || '--' }}</text>
                                        <view style="width: 234rpx;"></view>
                                        <text class="time" v-if="item.openTime">
                                            开仓时间： {{ item.openTime }}
                                        </text>

                                    </view>
                                    <view class="potime">
                                        <!-- v-if="item.closeFee" -->
                                        <view style="width: 64rpx;"></view>
                                        <text class="fees" v-if="item.closeFee">平仓手续费: ¥ {{ item.closeFee || 0.22
                                            }}</text>
                                        <text v-else>{{ "\xa0" }}</text>

                                        <text class="time" v-if="item.closeTime">
                                            平仓时间： {{ item.closeTime }}
                                        </text>
                                        <text v-else>{{ "\xa0" }}</text>
                                    </view>
                                </view>
                            </view>
                            <view class="nodata" v-if="!positionsListAll.length">
                                <image mode="widthFix"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                                <text>暂时无数据</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <TabBar :initialActiveIndex="2"></TabBar>
            <!-- 撤单 -->
            <u-modal class="" v-model="cancelOrder" width="600rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="newmoneys">
                    <text>当前下单使用了体验金并已有成交，若撤单则不返还体验金</text>
                    <view class="btns">
                        <view @click="backorder()">撤单</view>
                        <view @click="cancelOrder = false">取消</view>
                    </view>
                </view>
            </u-modal>

            <!-- 当前余额不足 拥有指定藏品开启止损止盈-->
            <u-modal class="" v-model="nobalance" width="600rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="nomoney" style="margin: 120rpx 0;">
                    <text>{{ code == 1 ? '当前余额不足' : '持有' + aimobj.otoAskTitle + '藏品即可开通止盈止损功能' }}</text>
                    <view class="img"
                        style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 40rpx;">
                        <image :src="aimobj.otoAskImg" mode="aspectFill"
                            style="width: 160rpx;height: 160rpx;border-radius: 30rpx;" />
                        <text style="margin-top: 40rpx">当前地板价为：{{ '¥' + aimobj.otoAskFloorPrice }}</text>
                    </view>
                    <view class="btn" @click="goBuy()">{{ code == 1 ? '去充值' : '去支付' }}</view>
                </view>
            </u-modal>

            <u-modal class="" v-model="isSubmit" width="40%" :show-title="false" :show-confirm-button="false">
                <div class='sk-wave'></div>
                <view class="text_msg"
                    style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                    支付中...
                </view>
            </u-modal>

            <!-- 止盈止损弹窗 -->
            <u-modal class="" v-model="proLoss" width="468rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="modalpro">
                    <view class="m_head">
                        <view class="left">
                            <image v-if="profitLossobj.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <view>
                                <!-- {{ profitLossobj.side == 'SELL' }} -->
                                <text class="price">价值</text>
                                <text class="num">￥{{ profitLossobj.money }} <text class="num2"
                                        :style="{ color: profitLossobj.red ? '#EC4068' : '#6CFF8A' }">({{
                                            (calculateEarnings(profitLossobj)) }}) </text></text>
                            </view>
                        </view>
                        <view class="right">{{ profitLossobj.leverageLevel }}x</view>
                    </view>

                    <view class="m_line"></view>

                    <view class="m_openclose">
                        <view class="top">
                            <text>市场价</text>
                            <text>￥{{ market }}</text>
                        </view>
                        <view>
                            <text>止盈价</text>
                            <text class="rb">￥</text>
                            <!-- profitLossobj stopLossPrice -->
                            <input v-model="profit" type="digit" @input="onprofitInputChange" />
                        </view>
                        <view>
                            <text>止损价</text>
                            <text class="rb">￥</text>
                            <input v-model="loss" type="digit" @input="onlossInputChange" />
                        </view>

                    </view>

                    <view class="m_btn">
                        <view @tap="proLoss = false">取消</view>
                        <view @tap="confirmProLoss">确认</view>
                    </view>
                </view>

            </u-modal>

            <!-- 分享 -->
            <u-popup v-model="isShare" mode="bottom">
                <view class="share_body">
                    <!-- <view class="back" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                        alt="" srcset="" mode="widthFix"></image>
                </view> -->
                    <view class="cart_div" id="test-id">
                        <view class="title_image">
                            开杠吧
                        </view>
                        <view class="toux_image_div">
                            <view class="toux_border">
                                <view class="image_div">
                                    <view class="gray">
                                        <image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="toux_name">
                            {{ shareUser.addr }}
                            <!-- 123123112321312 -->
                        </view>

                        <view class="buysell">
                            <image v-if="shareUser.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <!-- <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240729/2dfb9593a1cd98273aaeccbf45da2168_100x100.png" /> -->
                            <text>BIT指数</text>
                        </view>
                        <!-- color: #6CFF8A;
                    &.red {
                        color: #FF5270; -->
                        <view class="yield" :style="{ color: shareUser.red ? '#EC4068' : '#6CFF8A' }">
                            <text></text>{{ shareUser.zNum }}
                        </view>
                        <view class="shouyi" :style="{ color: shareUser.red ? '#FF5270' : '#6CFF8A' }">
                            <text></text>{{ '(' + shareUser.gNum + ')' }}
                        </view>
                        <view class="info_div">
                            <p>开仓均价：￥{{ shareUser.price }}</p>
                            <!-- <p>卖出价：￥3.45</p> -->
                            <p>开仓金额：￥{{ shareUser.money }}</p>
                        </view>
                        <view class="yqm">
                            邀请码：{{ shareUser.invitationCode }}
                            <image class="copy-img" style="width: 20rpx;height: 22rpx;margin-left: 10rpx;"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240822/86ee3166817515f270891ab2e1973722_40x44.png"
                                mode="widthFix" @click="copy(shareUser.invitationCode)" />
                        </view>
                        <view class="msg_text">
                            扫码注册，及时上车
                        </view>
                        <view class="icon_bg">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <view class="qr_code">
                            <view class="right">
                                <view class="qr_div">
                                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx"
                                        :options="options"></uv-qrcode>
                                </view>
                            </view>
                            <view class="time">分享于{{ shareUser.currentDateTime }}</view>
                        </view>
                    </view>
                    <view class="share_to_div">
                        <!-- #ifdef APP -->
                        <!-- <view class="li" @click="fenx_weixin()">
                        <view class="icon_image">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <p>微信</p>
                    </view> -->
                        <!-- #endif -->

                        <!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
                    </view>
                    <view class="colse_div" @click="isShare = false">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
                            mode="widthFix"></image>
                    </view>
                </view>
            </u-popup>

            <u-modal class="" v-model="bitshow" width="600rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="nomoneys">
                    <text class="bittitle">{{ '&nbsp;&nbsp;' }}ADL（自动减仓机制）</text>
                    <text class="bittitle2">用于在市场极端波动时保护交易平台的稳定性。</text>
                    <view class="bitline"></view>

                    <view class="bitbody">
                        ADL是一种风险管理工具，当市场出现剧烈波动并导致部分用户爆仓时，ADL机制会自动从当前市场上获利最多的用户开始减持他们的仓位，以弥补爆仓带来的影响。
                        这意味着在极端情况下，您可能会被自动减仓。ADL的目的是确保平台整体风险的平衡和市场的正常运作，帮助所有用户维持一个更加安全的交易环境。
                    </view>
                    <view class="bitbtn">
                        <view @click="bitshow = false">返回</view>
                    </view>
                </view>
            </u-modal>

            <!-- 分享 -->
            <u-popup v-model="isShares" mode="bottom">
                <view class="share_bodys">
                    <!-- <view class="back" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                        alt="" srcset="" mode="widthFix"></image>
                </view> -->
                    <view class="cart_div" id="test-id">
                        <view class="title_image">
                            开杠吧
                        </view>
                        <view class="toux_image_div">
                            <view class="toux_border">
                                <view class="image_div">
                                    <view class="gray">
                                        <image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
                                    </view>
                                </view>
                                <!-- <text>{{ shareUser.name }}</text> -->
                                <!-- <text>张三.mata</text> -->
                            </view>
                        </view>
                        <view class="toux_name">
                            {{ shareUser.addr }}
                        </view>

                        <view class="buysell">
                            <image v-if="shareUser.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <text>BIT指数</text>
                        </view>

                        <view class="yield" :style="{ color: shareUser.profit >= 0 ? '#FF5270' : '#6CFF8A' }">
                            <text v-if="shareUser.profit != null">{{ shareUser.profit >= 0 ? '+' : '' }}{{
                                shareUser.profit.toFixed(2) }}</text>
                        </view>
                        <view class="shouyi" :style="{ color: shareUser.profitRate >= 0 ? '#FF5270' : '#6CFF8A' }">
                            <text></text>{{ shareUser.profitRate >= 0 ? '+' : '' }}{{ '(' + accMul(shareUser.profitRate,
                                100) +
                                '%)' }}
                        </view>
                        <view class="info_div">
                            <p>开仓均价：￥{{ shareUser.price }}</p>
                            <!-- <p>卖出价：￥3.45</p> -->
                            <p>平仓均价：￥{{ shareUser.close }}</p>
                        </view>
                        <view class="yqm">
                            邀请码：{{ shareUser.invitationCode }}
                            <image class="copy-img" style="width: 20rpx;height: 22rpx;margin-left: 10rpx;"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240822/86ee3166817515f270891ab2e1973722_40x44.png"
                                mode="widthFix" @click="copy(shareUser.invitationCode)" />
                        </view>
                        <view class="msg_text">
                            扫码注册，及时上车
                        </view>
                        <view class="icon_bg">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <view class="qr_code">
                            <view class="right">
                                <view class="qr_div">
                                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx"
                                        :options="options"></uv-qrcode>
                                </view>
                            </view>
                            <view class="time">分享于{{ shareUser.currentDateTime }}</view>
                        </view>
                    </view>
                    <view class="share_to_div">
                        <!-- #ifdef APP -->
                        <!-- <view class="li" @click="fenx_weixin()">
                        <view class="icon_image">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <p>微信</p>
                    </view> -->
                        <!-- #endif -->

                        <!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
                    </view>
                    <view class="colse_div" @click="isShares = false">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
                            mode="widthFix"></image>
                    </view>
                </view>
            </u-popup>
            <u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
                <div class='sk-wave'></div>
                <view class="text_msg"
                    style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                    跳转登录中...
                </view>
            </u-modal>

            <popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false" @confirm="nav_realName()">
            </popup-bar>
        </view>
</template>

<script>
import TabBar from "@/components/public/TabBar";
import uniCopy from "@/js_sdk/uni-copy.js";
import antiShake from "@/common/public"
import popupBar from "@/components/public/PopupBar";

export default {
    components: {
        TabBar,
        popupBar
    },
    data() {
        return {
			isRegistration: false,
            isLoadding: false,
            certification: "",
            withdrawing: '',
            bitshow: false,
            balance: '',
            code: 2,
            entrustListAll: [],
            positionsListAll: [],
            aimobj: {},
            isSubmit: false, isShares: false,
            token: "",
            entrustList: [],
            profit: '',
            cancelOrder: false, // 撤单
            loss: '',
            positionsList: [],
            activeTab: 0, // 当前激活的 Tab
            // 模拟订单数据
            pendingOrders: [
                {
                    time: '2024/12/11 11:48:58',
                    amount: '9.48',
                    closeAmount: '12.66',
                    price: '4,220',
                },
                {
                    time: '2024/12/11 11:48:58',
                    amount: '9.48',
                    closeAmount: '12.66',
                    price: '4,220',
                },
            ],
            entrusthasnext: false,
            tradedPageNum: 1,
            isShare: false,
            entrusttotal: 0,
            positionhasnext: false,
            pageNum: 1,
            positionsList: [],
            positiontotal: 0,
            nobalance: false,  // 当前余额不足 拥有指定藏品开启止损止盈弹窗
            marketPrice: "",
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            interval: null,
            isIntervalSet: null,
            EnableproLoss: false,
            shareUser: {},
            qrcodeUrl: "",
            entrusthasnextall: false,
            positionhasnextall: false,
            waitorderNowobj: {},
            profitLossobj: {},
            proLoss: false,    // 止盈止损弹窗
            appUrl: ""

        };
    },
    onShow() {
        this.certification = uni.getStorageSync("certification")
    },
    onReachBottom() {
        if (this.activeTab == 0 && this.entrusthasnext) {
            this.tradedPageNum += 1
            this.fetchEntrust()
        } else if (this.activeTab == 1 && this.positionhasnext) {
            this.pageNum += 1
            this.fetchPositions()
        } else if (this.activeTab == 2 && this.entrusthasnextall) {
            this.tradedPageNum += 1
            this.fetchEntrustAll()
        } else if (this.activeTab == 3 && this.positionhasnextall) {
            this.pageNum += 1
            this.fetchPositionsAll()
        }
        // entrusthasnextall: false,
        // positionhasnextall: false,
    },
    computed: {
        tabs() {
            return [`等待成交(${this.entrusttotal})`, `当前持仓(${this.positiontotal})`, '历史下单', '历史仓位']
        },
        market() {
            let a = Number(this.marketPrice)
            let b = a.toFixed(3)
            return b
        }
    },
    onLoad() {
        this.token = uni.getStorageSync("token")
        this.fetchEntrust();
        this.fetchPositions()
        this.fetchEntrustAll()
        this.fetchPositionsAll()

        this.fetchUser()
        this.getBalance()
        this.fetchwhatgobuy()
        this.waitorderNowobj = {}

    },
    watch: {
        "$store.state.realprice"(val) {
            this.marketPrice = val;
            let price = Number(val);
            console.log(price, '价格变化了', this.entrustList.length);

            if (this.entrustList.length == 0) {
                clearInterval(this.interval);
                this.interval = null;
            }
            let shouldFetchPositions = false; // 每次进入 watch 监听器时，重置标志位

            if (this.positionsList.length > 0) {
                for (let order of this.positionsList) {
                    if (
                        (order.side === 'BUY' && (order.takeProfitPrice || order.stopLossPrice) && price <= Number(order.stopLossPrice)) ||
                        (order.side === 'BUY' && (order.takeProfitPrice || order.stopLossPrice) && price >= Number(order.takeProfitPrice)) ||
                        (order.side === 'SELL' && (order.takeProfitPrice || order.stopLossPrice) && price <= Number(order.takeProfitPrice)) ||
                        (order.side === 'SELL' && (order.takeProfitPrice || order.stopLossPrice) && price >= Number(order.stopLossPrice)) ||
                        (order.pendingCloseVolume && order.closeVolume && order.pendingCloseVolume === order.closeVolume) // 新增判断条件
                    ) {
                        this.fetchPositions(); // 在循环结束后只调用一次
                        // shouldFetchPositions = true; // 设置标志位为 true
                        break; // 找到符合条件的订单后立即退出循环
                    }
                }
            }
        },

        entrustList: {
            handler(newValue, oldValue) {
                // 如果新数组长度小于旧数组长度，调用 fetchPositions()
                if (newValue.length < oldValue.length) {
                    this.fetchPositions();
                }
                console.log(newValue.length, '委托单变化了');

                // 检查标志位并处理定时任务
                if (newValue && newValue.length > 0) {
                    if (!this.isIntervalSet) {  // 如果定时任务尚未设置
                        this.isIntervalSet = true;  // 设置标志位为 true
                        // this.interval = setInterval(() => {
                        //     this.tradedPageNum = 1;
                        //     this.fetchEntrust();
                        // }, 3000);
                    }
                } else {
                    // 如果数组为空，清除定时任务
                    clearInterval(this.interval);
                    this.interval = null;
                    this.isIntervalSet = false;  // 重置标志位
                }

                if (newValue.length === 0) {
                    clearInterval(this.interval);
                    this.interval = null;
                    this.isIntervalSet = false;  // 确保在数组为空时，标志位被重置
                }

                // 处理数组变动后的逻辑
            },
            deep: true, // 监听数组内部对象的变化
            immediate: true // 组件加载时立即执行 handler
        },

        // positionsList: {
        //     handler(newList, oldValue) {

        //     }
        // },
        proLoss(newval, oldval) {
            if (!newval) {
                this.profitLossobj = {}
                this.profit = ''
                this.loss = ''
            }
        },
    },
    methods: {
        nav_realName() {
			this.isRegistration = false
			console.log(uni.getStorageSync('authStatus'))
			if (uni.getStorageSync('authStatus') == 30) {
				this.$Router.push({
					name: "authentication"
				})
			} else {
				this.$Router.push({
					name: "realName"
				})
			}
		},
        async getBalance() {
            let res = await this.$api.java_balance_info();
            if (res.status.code == 0) {
                this.balance = res.result.available
                this.withdrawing = res.result.withdrawing
                // this.bankCardCount = res.result.bankCardCount
                // this.freeSystemFeeNumText = res.result.freeSystemFeeNumText
                // this.freeSystemFeeNum = res.result.freeSystemFeeNum || 0
                // this.iversion = res.result.iversion
                console.log(res)
            }

        },
        nav_withdraw() {
            // uni.showToast({
            // 	title: '敬请期待',
            // 	icon: 'none ',
            // 	duration: 3000
            // });
            console.log(this.certification)
            if (this.certification == 1) {
                this.$Router.push({
                    name: "withdraw"
                })
            } else {
                this.isRegistration = true
            }
        },
        nav_pay() {
            if (this.certification == 0) {
                this.isRegistration = true
                return false
            }
            this.appUrl = getApp().globalData.urlZf
            let url = `${this.appUrl}pagesA/project/security/pay`
            console.log(url)
            // #ifdef APP
            if (uni.getSystemInfoSync().platform == 'ios') {
                let curV = uni.getSystemInfoSync().appVersion
                let reqV = this.iversion
                if (curV == reqV) {
                    this.$Router.push({
                        name: "iosPay",
                    })
                } else {
                    this.$Router.push({
                        name: "webView",
                        params: {
                            url,
                        }
                    })
                }
            } else {
                this.$Router.push({
                    name: "webView",
                    params: {
                        url,
                    }
                })
            }
            // #endif
            // #ifdef H5
            let {
                origin
            } = window.location
            window.location.href = `${origin}/orderView/#/pagesA/project/security/pay`
            // #endif
        },
        /**
   * 去购买
   */
        goBuy() {
            if (this.aimobj.otoAskCtid) {
                this.submitOrder()
            }
        },
        /**
 * 解锁止盈止损的条件
 */
        async fetchwhatgobuy() {
            const contractName = uni.getStorageSync('currentIndictor');
            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetcoinExchangeParam({ ...(contractName ? { contractName } : {}), });
            } else {
                res = await this.$api.GetExchangeParam({ ...(contractName ? { contractName } : {}), });
            }
            if (res.status.code == 0) {
                this.aimobj = res.result
            }
        },
        nav_login() {
            this.$Router.push({
                name: 'mainLogin',
            })
        },
        // 去支付
        async submitOrder() {
            uni.setStorageSync('isfromEx', 1)
            this.isSubmit = true;
            let res = await this.$api.java_create_item({
                paymentScene: 1,
                ctid: this.aimobj.otoAskCtid
            });
            setTimeout(() => {
                this.isSubmit = false;
            }, 10000)
            if (res.status.code == 0) {
                uni.showToast({
                    title: "下单成功~",
                    icon: "none",
                    duration: 3000,
                });
                this.isSubmit = false;
                console.log(res.result.orderNo)
                // #ifdef APP
                this.appUrl = getApp().globalData.urlZf

                let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
                console.log(url)
                this.$Router.push({
                    name: "webView",
                    params: {
                        url,
                    }
                })
                // #endif
                // #ifdef H5
                let { origin } = window.location
                window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
                // #endif
            } else if (res.status.code == 1002) {
                this.nav_login()
            } else {
                this.isSubmit = false;
                uni.showToast({
                    title: res.status.msg,
                    icon: "none",
                    duration: 3000,
                });
            }
        },
        goreals() {
            this.$Router.push({
                name: "realName"
            })
        },
        //复制
        copy(val) {
            uniCopy({
                content: val,
                success: (res) => {
                    uni.showToast({
                        title: res,
                        icon: "none",
                    });
                },
                error: (e) => {
                    uni.showToast({
                        title: e,
                        icon: "none",
                        duration: 3000,
                    });
                },
            });
        },
        // 重置止盈止损
        async resetProLoss(item) {
            const contractName = uni.getStorageSync('currentIndictor');

            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.ResetcoinOtoPosition({ ...(contractName ? { contractName } : {}), id: item.id, });
            } else {
                res = await this.$api.ResetOtoPosition({ ...(contractName ? { contractName } : {}), id: item.id, });
            }

            // const res = await this.$api.ResetOtoPosition({
            //     id: item.id,
            //     ...(contractName ? { contractName } : {}),
            // })
            if (res.status.code == 0) {
                uni.showToast({
                    title: '重置止盈止损成功',
                    icon: 'none',
                    duration: 3000
                })
                this.fetchPositions()
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
            // this.proLoss = false

        },
        // 点击止盈止损
        setProLoss(item) {
            this.profitLossobj = item
            this.proLoss = true
        },
        // 确认止盈止损
        async confirmProLoss() {
            if (!this.profit && !this.loss) {
                uni.showToast({
                    title: '请输入止盈止损价格',
                    icon: 'none',
                    duration: 3000
                })
                return
            }


            if (this.profitLossobj.side == 'BUY') {
                // 如果设置了止损价，且止损价大于市价
                if (this.loss && Number(this.loss) > Number(this.marketPrice)) {
                    uni.showToast({
                        title: '止损价需小于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.loss = ''
                    return;
                }

                // 如果设置了止盈价，且止盈价小于市价
                if (this.profit && Number(this.profit) < Number(this.marketPrice)) {
                    uni.showToast({
                        title: '止盈价需大于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.profit = ''
                    return;
                }
            }


            console.log(this.marketPrice, '市场', this.profit);

            if (this.profitLossobj.side == 'SELL') {
                // 如果设置了止盈价，且止盈价大于市价
                if (this.profit && Number(this.profit) > Number(this.marketPrice)) {
                    uni.showToast({
                        title: '止盈价需小于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.profit = ''
                    return;
                }

                // 如果设置了止损价，且止损价小于市价
                if (this.loss && Number(this.loss) < Number(this.marketPrice)) {
                    uni.showToast({
                        title: '止损价需大于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.loss = ''
                    return;
                }
            }

            const contractName = uni.getStorageSync('currentIndictor');

            let data = {
                ...(contractName ? { contractName } : {}),
                id: this.profitLossobj.id,
                stopLossPrice: this.loss ? Number(this.loss) : '',
                takeProfitPrice: this.profit ? Number(this.profit) : ''
            }
            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.OtocoinPosition(data);
            } else {
                res = await this.$api.OtoPosition(data);
            }
            // let res = await this.$api.OtoPosition(data)
            if (res.status.code == 0) {
                this.profitLossobj = {}
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
                this.proLoss = false
                this.loss = ''
                this.profit = ''
                this.fetchPositions()
                // this.$Router.push({
                //     name: 'position'
                // })
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }

        },
        // 点击撤单
        backuporder(item) {
            console.log(item);
            this.waitorderNowobj = item

            if (item.experienceMoney > 0 && item.dealMoney > 0) {
                this.cancelOrder = true
            } else {
                this.backorder()
            }

        },
        // 撤单
        async backorder() {
            const contractName = uni.getStorageSync('currentIndictor');


            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.RevokecoinOrders({
                    ...(contractName ? { contractName } : {}),
                    id: this.waitorderNowobj.id,
                });
            } else {
                res = await this.$api.RevokeOrders({
                    ...(contractName ? { contractName } : {}),
                    id: this.waitorderNowobj.id,
                });
            }

            // let res = await this.$api.RevokeOrders({
            //     id: this.waitorderNowobj.id,
            //     ...(contractName ? { contractName } : {}),
            // })
            this.cancelOrder = false

            if (res.status.code == 0) {
                uni.showToast({
                    title: '撤单成功',
                    icon: 'none',
                    duration: 3000
                })
                setTimeout(() => {
                    this.fetchUser()
                }, 2000);
                this.cancelOrder = false
                if (this.activeTab == 0) {
                    this.tradedPageNum = 1
                    this.fetchEntrust()
                } else if (this.activeTab == 1) {
                    this.pageNum = 1
                    this.fetchPositions()
                }


            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }

        },
        /**
   * 判断类型
   */
        positionType(item) {
            if (item.side == 'SELL') {
                return '平空均价'
            } else {
                return '平多均价'
            }
            // if (item.side == 'SELL' && item.open == 'OPEN') {
            //     return '开空均价'
            // } else if (item.side == 'SELL' && item.open == 'CLOSE') {
            //     return '平多均价'
            // } else if (item.side == 'BUY' && item.open == 'OPEN') {
            //     return '开多均价'
            // } else if (item.side == 'BUY' && item.open == 'CLOSE') {
            //     return '平空均价'
            // }
        },
        accMul(arg1, arg2) {
            arg1 = Number(arg1);
            arg2 = Number(arg2);
            var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
            try { m += s1.split(".")[1].length } catch (e) { }
            try { m += s2.split(".")[1].length } catch (e) { }
            return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
        },
        /**
 * 获取委托单
 */
        async fetchEntrustAll() {
            this.isLinkLoadding = true

            let res = await this.$api.getHistoryOrderAll({
                pageNum: this.tradedPageNum,
                pageSize: 10
            });
            setTimeout(() => {
                this.isLinkLoadding = false
            }, 300);
            if (res.status.code == 0) {
                this.entrusthasnextall = res.result.hasNext
                if (this.tradedPageNum != 1) {
                    this.entrustListAll = this.entrustListAll.concat(res.result.list)
                } else {
                    this.entrustListAll = res.result.list
                }
            }
        },
        // GetHistoryOrder: 'bvexchange/appApi/exchange/getHistoryOrder', // 查询历史下单
        // GetHistoryPosition: 'bvexchange/appApi/exchange/getHistoryPosition', // 查询历史持仓
        /**
         * 获取我的仓位
         */
        async fetchPositionsAll() {
            this.isLinkLoadding = true
            let res = await this.$api.getHistoryPositionAll({
                pageNum: this.pageNum,
                pageSize: 10
            });
            if (res.status.code == 0) {
                this.positionhasnextall = res.result.hasNext
                if (this.pageNum != 1) {
                    this.positionsListAll = this.positionsListAll.concat(res.result.list)
                } else {
                    this.positionsListAll = res.result.list
                }

            }
        },
        async fetchUser() {
            const contractName = uni.getStorageSync('currentIndictor');

            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetcoinExchangeUserInfo({ ...(contractName ? { contractName } : {}), });
            } else {
                res = await this.$api.GetExchangeUserInfo({ ...(contractName ? { contractName } : {}), });
            }
            if (res.status.code == 0) {
                this.shareUser = res.result
                const jumpUrl =
                    `${getApp().globalData.url}pagesA/project/personal/appDownload`;
                this.get_share(jumpUrl)
                this.EnableproLoss = res.result.enableOto
            }
        },
        async get_share(jumpUrl) {
            console.error(jumpUrl)
            let res = await this.$api.getShortLink({
                longLink: jumpUrl
            });
            if (res.status.code == 0) {
                this.qrcodeUrl = res.result.shortUrl;

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        closeonce: antiShake._debounce(function (item) {
            this.cheorder(item)
        }, 1000),
        //平仓动作
        async cheorder(item) {
            // const contractName = uni.getStorageSync('currentIndictor');\
            let contractName
            if (item.symbol == 'BIT') {
                contractName = ''
            } else {
                contractName = 'E-' + item.symbol + '-USDT'
            }


            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.ClosecoinPositions({
                    ...(contractName ? { contractName } : {}),
                    id: item.id
                });
            } else {
                res = await this.$api.ClosePositions({
                    ...(contractName ? { contractName } : {}),
                    id: item.id
                });
            }

            // let data = {
            //     ...(contractName ? { contractName } : {}),
            //     id: item.id
            // }
            // let res = await this.$api.ClosePositions(data)
            if (res.status.code == 0) {
                // let result = await this.$api.GetPositonStatus({ positionId: item.positionId })
                // if(result.result.status == 0){

                // }
                // uni.showToast({
                //     title: res.status.msg,
                //     icon: 'none',
                //     duration: 3000
                // })
                // this.pageNum = 1

                uni.showToast({
                    title: '平仓委托提交成功',
                    icon: 'none',
                    duration: 1000
                })
                const contractName = uni.getStorageSync('currentIndictor');

                setTimeout(async () => {

                    let result;
                    if (contractName) {
                        // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                        result = await this.$api.GetcoinorderStatus({
                            ...(contractName ? { contractName } : {}),
                            id: res.result,
                        });
                    } else {
                        result = await this.$api.GetorderStatus({
                            ...(contractName ? { contractName } : {}),
                            id: res.result,
                        });
                    }

                    // let result = await this.$api.GetorderStatus({
                    //     id: res.result,
                    //     ...(contractName ? { contractName } : {}),
                    // })
                    if (result.status.code != 0) {
                        uni.showToast({
                            title: '对手盘不足，平仓失败',
                            icon: 'none',
                            duration: 2000
                        })
                        return
                    } else {

                    }
                }, 5000);

                this.getOrderInfo(item.id)

                this.fetchPositions()

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        //我的仓位 订单查询
        async getOrderInfo(orderId) {
            const contractName = uni.getStorageSync('currentIndictor');

            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetbitPositonStatus({
                    ...(contractName ? { contractName } : {}),
                    positionId: orderId,
                });
            } else {
                res = await this.$api.GetPositonStatus({
                    ...(contractName ? { contractName } : {}),
                    positionId: orderId,
                });
            }
            // let res = await this.$api.GetPositonStatus({
            //     ...(contractName ? { contractName } : {}),
            //     positionId: orderId,
            // });


            if (res.status.code == 0) {
                if (res.result.closeVolume == res.result.pendingCloseVolume) {
                    this.fetchPositions()
                    if (res.result.status == 0) {
                        clearInterval(setInterval);
                        let index = this.positionsList.findIndex(item => item.id === orderId);
                        if (index !== -1) {
                            this.positionsList.splice(index, 1);
                        }

                        this.positiontotal = this.positiontotal - 1
                        if (this.positiontotal < 0) return this.positiontotal = 0
                        // this.tabList[1].name = `我的仓位(${this.positionsTotal - 1})`
                    }
                    return
                } else {
                    // 平仓中
                    let setInterval = setTimeout(() => {
                        this.getOrderInfo(orderId)
                    }, 1000)
                }
                console.log(res)

            } else {
                this.Toast(res.status.msg);
            }
        },
        dealprogress(item) {
            let chu = parseFloat(item.dealMoney / item.money) * 100
            let yu = chu.toFixed(2)
            return yu
        },
        /**
   * 获取我的仓位
   */
        async fetchPositions() {
            let res = await this.$api.getCurPositionAll({
                pageNum: this.pageNum,
                pageSize: 10
            });
            if (res.status.code == 0) {
                this.positionhasnext = res.result.hasNext
                if (this.pageNum != 1) {
                    this.positionsList = this.positionsList.concat(res.result.list)
                } else {
                    this.positionsList = res.result.list
                }
                this.positiontotal = res.result.totalCount

            }
        },
        /**
* 获取委托单
*/
        async fetchEntrust() {
            const contractName = uni.getStorageSync('currentIndictor');

            let res = await this.$api.getDelegatingOrderAll({
                pageNum: this.tradedPageNum,
                pageSize: 10
            });
            setTimeout(() => {
                // this.isLinkLoadding = false
            }, 300);
            if (res.status.code == 0) {
                // this.tabList[1].name = '我的仓位' + ' (' + this.positionsList.length + ')'
                this.entrusthasnext = res.result.hasNext
                if (this.tradedPageNum != 1) {
                    this.entrustList = this.entrustList.concat(res.result.list)
                } else {
                    this.entrustList = res.result.list
                }

                this.entrusttotal = res.result.totalCount
            } else if (res.status.code == 1002) {
                this.isLoadding = true
                setTimeout(() => {
                    this.isLoadding = false
                    this.$Router.push({
                        name: "mainLogin"
                    })
                }, 1500);
            }
        },
        nav_to(name) {
            this.$Router.push({
                name
            })
        },
        switchTab(index) {
            console.log(index);
            this.positionsList = []
            this.positionsListAll = []
            this.entrustList = []
            this.entrustListAll = []
            this.activeTab = index;
            this.pageNum = 1;
            this.tradedPageNum = 1
            const requestMap = {
                0: this.fetchEntrust,
                1: this.fetchPositions,
                2: this.fetchEntrustAll,
                3: this.fetchPositionsAll
            };

            // 判断是否存在对应的请求方法，如果存在则调用
            if (requestMap[index]) {
                requestMap[index]();
            }
        },
        formatNumber(value, decimalPlaces = 2) {
            if (value <= 0) {
                return 0.00
            }
            // 判断是否为整数
            if (Number.isInteger(value)) {
                return value.toString();
            }
            value = Number(value)
            // 如果是整数，则直接返回
            // 如果不是整数，则将其限制为指定的小数位数
            return value.toFixed(decimalPlaces);
        },
        isred(item) {
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str

            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            // this.$forceUpdate()
            return str >= 0 ? true : false


        },
        //计算战损/战利
        calculateEarnings(item) {

            // 收益 
            // buy =  (marketPrice - price ) * volume - dept
            // sell =  (price - marketPrice) * volume - dept
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str
            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            str = str.toFixed(2)

            // this.$forceUpdate()
            return str >= 0 ? ' + ' + Math.abs(str) : ' - ' + Math.abs(str)

        },
        //计算收益率
        calculateYield(item) {

            // 收益率
            // 开仓价 price * volume  / leverageLevel 
            // buy = buy的收益 / 开仓价
            // sell = sell 的收益 / 开仓价

            let str;
            const {
                price,
                side,
                volume,
                leverageLevel,
                income
            } = item

            // if (side == 'BUY') { //唱多
            //     let shouyiNum = (this.marketPrice - price) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // } else { //唱空
            //     let shouyiNum = (price - this.marketPrice) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // }
            // if(side == 'BUY'){
            let open = price * volume / leverageLevel
            str = income / open
            // } else {

            // }

            str = Math.floor(str * 10000) / 100
            if (str >= 0) {
                item.red2 = true
            } else {
                item.red2 = false
            }
            str = str.toFixed(2)
            return str >= 0 ? `+${str}%` : `${str}%`
        },
        async openShare(item) {
            uni.showToast({
                title: '该仓位未平仓',
                icon: 'none',
                duration: 2000
            });
            return
            let pages = getCurrentPages(); // 获取当前页面栈
            let currentPage = pages[pages.length - 1]; // 获取当前页面
            let currentRoute = currentPage.route; // 获取当前页面的路径

            let res = await this.$api.VisitorShare({
                module: "BIT_EXCHANGE",
                from: 'h5',
                page: '/shareProfit'
            });

            // this.shareUser = {}
            let zNum = this.calculateEarnings(item);
            let gNum = this.calculateYield(item)
            console.log(item)
            this.isShare = true
            this.shareUser = {
                ...this.shareUser,
                money: item.money,
                side: item.side,
                zNum,
                gNum,
                red: item.red,
                price: item.price,
                lever: item.lever,
                currentDateTime: this.getDate()
            }
            console.log(this.shareUser)
        },
        async openShares(item) {

            let pages = getCurrentPages(); // 获取当前页面栈
            let currentPage = pages[pages.length - 1]; // 获取当前页面
            let currentRoute = currentPage.route; // 获取当前页面的路径

            let res = await this.$api.VisitorShare({
                module: "BIT_EXCHANGE",
                from: 'h5',
                page: '/shareProfit'
            });

            if (item.status == 1) {
                uni.showToast({
                    title: '该仓位未平仓',
                    icon: 'none',
                    duration: 2000
                })
                return
            }
            // let item = {
            //     orderId: "78468682465386496",
            //     direction: 1,
            //     offsets: 1,
            //     price: "10.9700",
            //     quantity: 10,
            //     lever: 2,
            //     forceClosePrice: "8.2275",
            //     canClose: true,
            //     longShort: 1,
            //     status: 1,
            //     red: true,
            //     red2: true,
            //     name: '张三.meta'
            // }
            // this.$emit('openShare', item)
            let zNum = this.calculateEarnings(item);
            let gNum = this.calculateYield(item)
            console.log(item)
            this.isShares = true
            this.shareUser = {
                ...this.shareUser,
                zNum,
                gNum,
                red: item.side == 'SELL',
                profit: (item.epProfit != null && item.trailType == 2) ? item.epProfit : item.profit,
                profitRate: (item.epProfitRate != null && item.trailType == 2) ? item.epProfitRate : item.profitRate,
                close: item.closePrice,
                price: item.price,
                side: item.side,
                lever: item.lever,
                currentDateTime: this.getDate()
            }
            console.log(this.shareUser)
        },
        getDate() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (`0${now.getMonth() + 1}`).slice(-2);
            const date = (`0${now.getDate()}`).slice(-2);
            // 设置格式化的当前日期与时间
            return `${year}年${month}月${date}日`;
        },
    },
};
</script>

<style scoped lang="scss">
.nomoneys {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 55rpx 0 40rpx 0;


    .bittitle {
        background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
        background-size: 100% 100%;

        width: 360rpx;
        height: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #FFFFFF;

        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: bold;
        font-size: 28rpx;
    }

    .bittitle2 {
        margin: 35rpx 0 21rpx 0;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        opacity: .5;
    }

    .bitline {
        height: 1rpx;
        width: 100%;
        background: #53505D;
    }

    .bitbody {
        margin: 29rpx 34rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #FFFFFF;
        line-height: 44rpx;
        text-align: left;

        .midcolor {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 26rpx;
            line-height: 44rpx;
            text-align: left;
            color: #63EAEE;
        }
    }

    .bitbtn {
        display: flex;
        width: 100%;
        padding: 0 40rpx;
        justify-content: center;
        align-items: center;

        view {
            &:nth-of-type(1) {
                width: 300rpx;
                height: 80rpx;
                line-height: 80rpx;
                text-align: center;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50rpx;
                border: 1rpx solid #FFFFFF;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
            }

            &:nth-of-type(2) {
                width: 220rpx;
                height: 70rpx;
                line-height: 70rpx;
                text-align: center;
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                border-radius: 40rpx;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                font-size: 24rpx;
                color: #141414;
            }

        }
    }

    text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .charge {
        margin-top: 61rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;
    }
}

.textline {
    text-decoration: line-through;
}

.share_bodys {
    width: 100%;
    background-color: #111111;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 160rpx 55rpx 55rpx;
    height: 100vh;

    .cart_div {
        background-color: #2B2B2B;
        border-radius: 36rpx;
        width: 640rpx;
        height: 940rpx;
        padding: 40rpx 36rpx;
        position: relative;

        .buysell {
            display: flex;
            align-items: center;

            image {
                width: 40rpx;
                height: 40rpx
            }

            text {
                margin-left: 12rpx;
                font-weight: bold;
                font-size: 28rpx;
                color: #FFFFFF;
            }
        }

        .title_image {

            font-family: DOUYUFont, DOUYUFont;
            font-weight: 400;
            font-size: 40rpx;
            color: #FFFFFF;

            background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
            width: 100%;
            height: 111rpx;
            background-size: 100% 100%;
            line-height: 111rpx;
            text-align: center;
            letter-spacing: 2rpx;
        }

        .toux_image_div {
            width: 259rpx;
            margin: 20rpx auto;

            .toux_border {
                width: 100%;
                // height:116rpx;
                background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
                background-size: 100%;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                padding-top: 40rpx;

                >text {
                    margin-top: 10rpx;
                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                .image_div {

                    width: 120rpx;
                    height: 120rpx;
                    background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
                    border-radius: 40rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .gray {
                        width: 116rpx;
                        height: 116rpx;
                        border-radius: 40rpx;
                        background: #2B2B2B;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    image {
                        border-radius: 30rpx;
                        width: 104rpx;
                        height: 104rpx;
                        object-fit: cover;
                    }

                }
            }
        }

        .toux_name {
            margin-top: 30rpx;
            text-align: center;
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .yield {
            font-size: 90rpx;
            color: #6CFF8A;
            font-weight: 600;
            margin-top: 40rpx;

            text {
                font-size: 55rpx;
            }

            &.red {

                color: #FF5270;
            }
        }

        .shouyi {
            font-size: 48rpx;
            color: #6CFF8A;

            &.red {
                color: #FF5270;
            }
        }

        .info_div {
            color: #fff;
            margin-top: 30rpx;

            p {
                font-size: 28rpx;
                line-height: 38rpx;
            }
        }

        .yqm {
            background-color: rgb(255, 255, 255, 0.2);
            border: 1px solid #fff;
            width: 280rpx;
            height: 54rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 40rpx;
            border-radius: 36rpx;
            margin-bottom: 30rpx;
        }

        .msg_text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .icon_bg {
            width: 292rpx;
            position: absolute;
            top: 450rpx;
            right: -30rpx;

            image {
                width: 292rpx;
            }
        }

        .qr_code {
            position: absolute;
            top: 724rpx;
            right: 36rpx;

            .right {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .qr_div {
                    width: 140rpx;
                    height: 140rpx;
                    border-radius: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #fff;
                    padding: 10rpx;
                }
            }

            .time {
                color: rgb(255, 255, 255, 0.3);
                margin-top: 30rpx;
                font-size: 22rpx;
            }
        }
    }

    .share_to_div {
        margin-top: 140rpx;
        display: flex;
        justify-content: center;

        >.li {
            width: 25%;
            text-align: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 10rpx;

            .icon_image {
                display: flex;
                justify-content: center;

                image {
                    width: 90rpx;
                    margin-bottom: 20rpx;
                }
            }
        }
    }

    .colse_div {
        margin-top: 46rpx;
        display: flex;
        justify-content: center;

        image {
            width: 80rpx;
        }
    }
}

.share_body {
    width: 100%;
    background-color: #111111;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 160rpx 55rpx 55rpx;
    height: 100vh;

    .cart_div {
        background-color: #2B2B2B;
        border-radius: 36rpx;
        width: 640rpx;
        height: 940rpx;
        padding: 40rpx 36rpx;
        position: relative;

        .buysell {
            margin-top: 30rpx;
            display: flex;
            align-items: center;

            image {
                width: 40rpx;
                height: 40rpx
            }

            text {
                margin-left: 12rpx;
                font-weight: bold;
                font-size: 28rpx;
                color: #FFFFFF;
            }
        }

        .title_image {

            font-family: DOUYUFont, DOUYUFont;
            font-weight: 400;
            font-size: 40rpx;
            color: #FFFFFF;

            background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
            width: 100%;
            height: 111rpx;
            background-size: 100% 100%;
            line-height: 111rpx;
            text-align: center;
            letter-spacing: 2rpx;
        }

        .toux_image_div {
            width: 259rpx;
            margin: 20rpx auto;

            .toux_border {
                width: 100%;
                // height:116rpx;
                background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
                background-size: 100%;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                padding-top: 40rpx;

                >text {
                    margin-top: 10rpx;
                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                .image_div {

                    width: 120rpx;
                    height: 120rpx;
                    background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
                    border-radius: 40rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .gray {
                        width: 116rpx;
                        height: 116rpx;
                        border-radius: 40rpx;
                        background: #2B2B2B;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    image {
                        border-radius: 30rpx;
                        width: 104rpx;
                        height: 104rpx;
                        object-fit: cover;
                    }
                }
            }
        }

        .toux_name {
            text-align: center;
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .yield {
            font-size: 90rpx;
            color: #6CFF8A;
            font-weight: 600;
            margin-top: 40rpx;

            text {
                font-size: 55rpx;
            }

            &.red {
                color: #FF5270;
            }
        }

        .shouyi {
            font-size: 48rpx;
            color: #6CFF8A;

            &.red {
                color: #FF5270;
            }
        }

        .info_div {
            color: #fff;
            margin-top: 30rpx;

            p {
                font-size: 28rpx;
                line-height: 38rpx;
            }
        }

        .yqm {
            background-color: rgb(255, 255, 255, 0.2);
            border: 1px solid #fff;
            width: 280rpx;
            height: 54rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 40rpx;
            border-radius: 36rpx;
            margin-bottom: 30rpx;
        }

        .msg_text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .icon_bg {
            width: 292rpx;
            position: absolute;
            top: 450rpx;
            right: -30rpx;

            image {
                width: 292rpx;
            }
        }

        .qr_code {
            position: absolute;
            top: 724rpx;
            right: 36rpx;

            .right {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .qr_div {
                    width: 140rpx;
                    height: 140rpx;
                    border-radius: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #fff;
                    padding: 10rpx;
                }
            }

            .time {
                color: rgb(255, 255, 255, 0.3);
                margin-top: 30rpx;
                font-size: 22rpx;
            }
        }
    }

    .share_to_div {
        margin-top: 140rpx;
        display: flex;
        justify-content: center;

        >.li {
            width: 25%;
            text-align: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 10rpx;

            .icon_image {
                display: flex;
                justify-content: center;

                image {
                    width: 90rpx;
                    margin-bottom: 20rpx;
                }
            }
        }
    }

    .colse_div {
        margin-top: 46rpx;
        display: flex;
        justify-content: center;

        image {
            width: 80rpx;
        }
    }
}

.modalpro {
    // height: 586rpx;
    background: #2B2B2B;
    border-radius: 36rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 30rpx 20rpx 20rpx;

    .m_head {
        width: 100%;
        display: flex;
        align-items: center;
        // flex-direction: row;
        justify-content: space-between;

        .left {
            display: flex;

            image {
                width: 50rpx;
                height: 50rpx;
            }

            >view {
                display: flex;
                flex-direction: column;
                // align-items: center;
                margin-left: 20rpx;

                .price {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #a6a6a6;
                }

                .num {
                    margin-top: 10rpx;

                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;

                    .num2 {
                        margin-left: 10rpx;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #EC4068;
                    }
                }
            }
        }

        .right {
            margin-top: 10rpx;
            font-weight: bold;
            font-size: 30rpx;
            color: #FFFFFF;
        }
    }

    .m_line {
        margin: 31rpx 0 39rpx 0;
        width: 100%;
        height: 1rpx;
        background: #414141;
    }

    .m_openclose {
        .top {
            text {
                display: flex;
                align-items: center;

                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                }

                &:nth-of-type(2) {
                    margin-left: 50rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #FFFFFF;
                }
            }
        }

        >view {
            margin-bottom: 26rpx;
            display: flex;
            align-items: center;
            position: relative;

            &:last-child {
                margin-bottom: 0;
            }

            display: flex;

            text {
                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 22rpx;

                    color: #FFFFFF;
                }


            }

            .rb {
                z-index: 1;
                position: absolute;
                right: 200rpx;
                top: 12rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #FFFFFF;

            }

            input {
                text-indent: 40rpx;
                margin-left: 36rpx;
                width: 237rpx;
                height: 51rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 18rpx;
                border: 1rpx solid #FFFFFF;

                font-weight: 400;
                font-size: 22rpx;
                color: #FFFFFF;
            }
        }
    }

    .m_btn {
        margin-top: 30rpx;
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;

        >view {
            width: 200rpx;
            height: 60rpx;
            background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
            border-radius: 50rpx;
            font-weight: bold;
            text-align: center;
            line-height: 60rpx;
            font-size: 26rpx;
            color: #141816;

            &:nth-of-type(1) {
                background: rgba(255, 255, 255, 0.2);
                border: 1rpx solid #FFFFFF;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                color: #FFFFFF;
            }
        }
    }
}

.nomoney {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 300rpx;

    text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .btn {
        margin-top: 61rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;
    }
}

// 撤单
.newmoneys {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    // height: 367rpx;
    text-align: center;
    padding: 70rpx 60rpx 40rpx 60rpx;

    >text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 38rpx;
    }

    .btnreal {
        width: 100%;
        margin-top: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .goreal {
            width: 300rpx;
            line-height: 80rpx;
            height: 80rpx;
            background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
            border-radius: 40rpx;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            font-size: 24rpx;
            color: #141414;
        }
    }

    >.btns {
        width: 100%;
        margin-top: 44rpx;
        display: flex;
        justify-content: space-between;

        >view {
            text-align: center;
            line-height: 70rpx;
            width: 220rpx;
            height: 70rpx;

            &:nth-of-type(1) {


                background: rgba(255, 255, 255, 0.2);
                border-radius: 50rpx;
                border: 1rpx solid #FFFFFF;
                font-weight: bold;
                font-size: 24rpx;
                color: #FFFFFF;
            }

            &:nth-of-type(2) {
                font-weight: bold;
                font-size: 24rpx;
                color: #141414;
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                border-radius: 40rpx
            }
        }


    }
}

.hasdeal {
    font-weight: 400;
    font-size: 22rpx !important;
    color: #6B6B6B;
    background: #35333E;
    border-radius: 18rpx;
    border: 1rpx solid #FFFFFF;
    padding: 8rpx 12rpx 9rpx 13rpx;
}

.times {
    font-weight: 400;
    font-size: 20rpx;
    color: #959595;
}

.rotate-image {
    animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(180deg);
    }
}

.ml20 {
    margin-left: 20rpx;
}

.che {
    display: inline-block;
    // padding: 8rpx 22rpx;
    width: 83rpx;
    height: 36rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    /* 按钮内边距 */
    font-size: 16px;
    /* 字体大小 */
    color: #00e0ff;
    /* 按钮文字颜色 */
    text-align: center;
    border-radius: 50px;
    /* 圆角 */
    background: linear-gradient(90deg, #d976ff, #00e0ff);
    /* 渐变边框颜色 */
    position: relative;
    cursor: pointer;
    transition: all 0.3s;
}

.che text {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 20rpx;
    color: #40F8EC;
    z-index: 1;

}

.che::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border-radius: 50px;
    background: #35333E;
    /* 按钮内部背景色 */
    z-index: 0;

}

.podetails {
    .weituoAll {
        min-height: 800rpx;

        .li {
            // padding: 30rpx;
            padding: 19rpx 13rpx 20rpx 20rpx;
            background-color: rgba(37, 35, 45, .49);
            border-radius: 25rpx;
            margin-bottom: 20rpx;
            position: relative;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 21rpx;
                top: 25rpx;

                image {
                    width: 22rpx;
                    height: 25rpx
                }
            }

            .live_data {
                background-color: #141816;
                border-radius: 40rpx;
                height: 80rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 20rpx;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;
                // width: 500rpx;
                // color: #6CFF8A;

                text {
                    color: #fff;
                }

                .sy {
                    margin-right: 50rpx;
                    font-weight: 600;

                    text {
                        &:nth-of-type(1) {
                            font-weight: bold;
                            color: #fff;

                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-size: 28rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: bold;
                            font-size: 28rpx;
                            // color: #EC4068;
                        }
                    }

                    // .red {
                    //     // color: #EC4068;
                    // }

                    // .green {
                    //     // color: #6CFF8A;
                    // }
                }

                .syl {
                    text {
                        &:nth-of-type(1) {
                            font-weight: 400;
                            font-size: 30rpx;
                            color: #fff;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: 400;
                            font-size: 30rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .symbol {
                        font-family: HarmonyOS Sans SC;
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #FFFFFF;
                    }

                    image {
                        width: 36rpx;
                        margin: 0 30rpx 0 20rpx;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        margin-right: 20rpx;
                    }

                    .sub {
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #FFFFFF;
                        opacity: 0.5;
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 20rpx;

                        image {
                            width: 18rpx;
                            height: 24rpx;
                        }
                    }

                    .progress {
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 20rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 4rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {
                    text-align: center;

                    // width: 33.33%;
                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: rgba(255, 255, 255, .5);
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        font-weight: 500;
                        font-size: 22rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 500;
                                font-size: 22rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }


            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 20rpx 0 0 0;

                .li_view {
                    text-align: center;
                    // width: 33.33%;
                    display: flex;
                    justify-content: center;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 21rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .close {
                margin-top: 20rpx;
                display: flex;
                justify-content: space-between;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none
                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 300rpx;
                    height: 60rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                }
            }
        }
    }

    .cangweiAll {
        min-height: 800rpx;

        .li {
            padding: 26rpx 27rpx 23rpx 30rpx;
            background-color: rgba(37, 35, 45, .49);
            border-radius: 25rpx;
            margin-bottom: 20rpx;
            position: relative;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 21rpx;
                top: 33rpx;

                image {
                    width: 22rpx;
                    height: 25rpx
                }
            }

            .qiang {
                image {
                    width: 72rpx;
                    height: 32rpx;
                }


            }

            .ADL {
                width: 70rpx;
                height: 28rpx;
                background: #D88839;
                border-radius: 36rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 18rpx;
                color: #FFFFFF;
            }

            .live_data {
                // margin: 0 34rpx 0 26rpx;
                padding: 10rpx;

                margin-left: 12rpx;
                width: 392rpx;
                // height: 45rpx;
                background: rgba(0, 0, 0, .5);
                border-radius: 20rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;

                // width: 500rpx;
                // color: #6CFF8A;
                // padding: 0 56rpx;
                text {
                    color: #fff;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 20rpx;
                }

                .sy {
                    display: flex;
                    justify-content: center;
                    font-weight: 600;
                    align-items: center;

                    .text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: 400;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            // color: #EC4068;
                        }
                    }
                }

                .syl {
                    margin-left: 20rpx;
                    display: flex;
                    align-items: center;

                    >text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: 400;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;

                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: 400;
                            font-size: 22rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                // justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;



                    >.rightinfo {
                        display: flex;
                        align-items: center;

                        // flex-direction: column;
                        // align-items: flex-start;
                        // justify-content: space-between;
                        .level {
                            color: #fff;
                            font-size: 28rpx;
                            font-weight: 600;
                        }
                    }

                    image {
                        width: 36rpx;
                        // margin-right: 14rpx;
                        margin: 0 24rpx 0 18rpx;
                    }

                    .symbol {
                        font-family: HarmonyOS Sans SC;
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #FFFFFF;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        // margin-right: 20rpx;
                    }

                    .sub {
                        font-size: 24rpx;
                        color: rgb(255, 255, 255, 0.3);
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 35rpx;
                    }

                    .progress {
                        margin-right: 3px;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 6rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {
                    // display: flex;
                    // flex-direction: column;
                    // align-items: center;
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #FFFFFF;
                        opacity: 0.5;
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 500;
                        font-size: 22rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 500;
                                font-size: 22rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }

            .potime {

                // margin-top: 10rpx;
                align-items: flex-start;
                display: flex;
                flex-direction: column;


                .fees {
                    line-height: 29rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    opacity: .5
                }

                .time {
                    margin-top: 10rpx;
                    line-height: 24rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 18rpx;
                    color: #FFFFFF;
                    opacity: .5
                }
            }

            .fee {
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                // justify-content: space-between;
                .fees {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    opacity: .5;
                    width: 300rpx;
                    line-height: 29rpx;
                }

                .time {
                    line-height: 24rpx;
                    margin-top: 12rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 18rpx;
                    color: #FFFFFF;
                    opacity: .5
                }

            }

            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                // margin: 0 33rpx;

                .li_view {
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #FFFFFF;
                        opacity: .5;
                        line-height: 29rpx;
                    }

                    .num {
                        margin-top: 4rpx;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        line-height: 32rpx;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;


                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

                                // margin-top: 4rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .close {
                // margin-top: 20rpx;
                display: flex;
                justify-content: flex-end;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 140rpx;
                    height: 50rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    color: #FFFFFF;

                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                    background: #2B2B2B;
                    border-radius: 12rpx;
                }
            }
        }
    }
}

.cangwei {
    min-height: 600rpx;


    .li {
        // padding: 30rpx;
        padding: 25rpx 21rpx 23rpx 30rpx;
        background: rgba(37, 35, 45, .49);
        border-radius: 25rpx;
        margin-bottom: 20rpx;
        position: relative;
        transition: opacity 1s ease-out;

        &.active {
            opacity: 0;
        }

        .right_fenx {
            position: absolute;
            right: 21rpx;
            top: 33rpx;

            image {
                width: 22rpx;
                height: 25rpx
            }
        }

        .live_data {
            margin: 0 29rpx 0 12rpx;
            width: 392rpx;
            height: 42rpx;
            background: rgba(0, 0, 0, .5);
            border-radius: 20rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            // padding: 0rpx 78rpx;
            font-size: 24rpx;
            // width: 500rpx;
            // color: #6CFF8A;

            text {
                color: #fff;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 20rpx;
            }

            .sy {
                // margin-right: 56rpx;
                font-weight: 600;

                text {
                    &:nth-of-type(1) {
                        color: #fff;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: bold;
                        font-size: 20rpx;
                    }

                    &:nth-of-type(2) {
                        margin-left: 4rpx;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        // color: #EC4068;
                    }
                }

                // .red {
                //     // color: #EC4068;
                // }

                // .green {
                //     // color: #6CFF8A;
                // }
            }

            .syl {
                margin-left: 20rpx;

                text {
                    &:nth-of-type(1) {
                        color: #fff;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                    }

                    &:nth-of-type(2) {
                        margin-left: 10rpx;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        // color: #EC4068;
                    }
                }

                // &.red {
                //     // color: #EC4068;
                // }
            }
        }

        >.heads {
            display: flex;
            // justify-content: space-between;
            align-items: center;

            .left {
                display: flex;
                justify-content: space-between;
                align-items: center;

                image {
                    width: 36rpx;
                    margin-right: 24rpx;
                }

                .symbol {
                    font-family: HarmonyOS Sans SC;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                >.level {
                    font-size: 28rpx;
                    font-weight: 600;
                    margin-left: 15rpx;
                    // margin-right: 20rpx;
                }

                .sub {
                    font-size: 24rpx;
                    color: rgb(255, 255, 255, 0.3);
                }
            }

            .cd {
                display: flex;
                align-items: center;

                .cancel {
                    width: 80rpx;
                    height: 44rpx;
                    font-size: 24rpx;
                    color: #fff;
                    background-color: rgb(255, 255, 255, 0.3);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 36rpx;
                    margin-left: 35rpx;

                }

                .progress {
                    margin-right: 3px;
                    display: flex;
                    // flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    >text {
                        // margin-bottom: 8rpx;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #FFFFFF;
                    }

                    .bg {
                        margin-left: 12rpx;
                        width: 80rpx;
                        height: 6rpx;
                        background: #6B6B6B;
                        border-radius: 36rpx;

                        .bar {

                            background: #63EAEE;
                            border-radius: 36rpx;
                            height: 6rpx;
                        }
                    }
                }

            }
        }

        .data_view {
            display: flex;
            justify-content: space-between;
            // align-items: center;
            margin: 18rpx 0 0 0;

            .li_view {
                text-align: center;

                // width: 33.33%;
                // display: flex;
                // justify-content: center;
                // flex
                &.button {
                    width: 144rpx;
                    height: 48rpx;
                    border-radius: 36rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 22rpx;
                    background-color: #fff;
                }

                .label {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #a6a6a6;
                }

                .num {
                    margin-top: 10rpx;
                    line-height: 32rpx;
                    display: flex;
                    flex-direction: column;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;

                    >text {
                        &:nth-of-type(1) {
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #FFFFFF;
                        }

                        &:nth-of-type(2) {
                            // margin-top: 10rpx;
                            color: #EC4068;
                            font-weight: 400;
                            font-size: 20rpx;
                        }
                    }


                }
            }
        }


        .data_views {
            display: flex;
            justify-content: space-between;
            // align-items: center;
            margin: 12rpx 0 0 0;

            .li_view {
                text-align: center;
                // width: 33.33%;

                &.button {
                    width: 144rpx;
                    height: 48rpx;
                    border-radius: 36rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 22rpx;
                    background-color: #fff;
                }

                .label {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #FFFFFF;
                    opacity: .5;
                    line-height: 29rpx;
                }

                .num {
                    margin-top: 4rpx;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    font-weight: 500;
                    font-size: 22rpx;
                    color: #FFFFFF;

                    line-height: 32rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;


                    >text {
                        &:nth-of-type(1) {
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #FFFFFF;
                        }

                        &:nth-of-type(2) {
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

                            margin-top: 4rpx;
                            color: #EC4068;
                            font-weight: 400;
                            font-size: 20rpx;
                        }
                    }
                }
            }
        }

        .stoploss {
            margin-top: 16rpx;
            height: 50rpx;
            background: #404040;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20rpx;

            .left {
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 22rpx;
                color: #FFFFFF;
                opacity: .5;
            }

            .right {
                display: flex;
                align-items: center;

                text {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 24rpx;

                    &:nth-of-type(1) {
                        color: #EC4068;
                    }

                    &:nth-of-type(2) {
                        color: #6CFF8A;

                    }

                }

                image {
                    width: 19.8rpx;
                    height: 19.8rpx;
                }
            }
        }

        .close {
            // margin-top: 20rpx;
            display: flex;
            justify-content: space-between;
            width: 100%;

            .unlock {
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                font-weight: 400;
                font-size: 24rpx;
                color: #141816;
                border: none;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-size: 20rpx;

            }

            >view {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                // width: 286rpx;
                height: 45rpx;
                font-weight: 400;
                color: #FFFFFF;
                background: #3C3A46;
                border-radius: 8rpx;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-size: 22rpx;

            }
        }
    }
}

.nav_login {
    width: 300rpx;
    height: 90rpx;
    background: var(--main-bg-color)FFF;
    border-radius: 24rpx;
    border: 2rpx solid #63EAEE;
    color: #63EAEE;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;
    font-weight: 600;
}

.nodata {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    image {
        width: 240rpx;
        height: 240rpx;
    }

    text {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
    }
}

.weituo {
    min-height: 600rpx;


    .li {
        // padding: 30rpx;
        padding: 19rpx 13rpx 20rpx 29rpx;
        margin-bottom: 20rpx;
        position: relative;
        transition: opacity 1s ease-out;

        background: rgba(37, 35, 45, .49);
        border-radius: 25rpx;

        &.active {
            opacity: 0;
        }

        .right_fenx {
            position: absolute;
            right: 34rpx;
            top: 22rpx;

            image {
                width: 20rpx;
                height: 22.5rpx
            }
        }

        .live_data {
            background-color: #141816;
            border-radius: 40rpx;
            height: 80rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20rpx;
            // padding: 0rpx 78rpx;
            font-size: 24rpx;
            // width: 500rpx;
            // color: #6CFF8A;

            text {
                color: #fff;
            }

            .sy {
                margin-right: 50rpx;
                font-weight: 600;

                text {
                    &:nth-of-type(1) {
                        font-weight: bold;
                        color: #fff;

                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-size: 28rpx;
                    }

                    &:nth-of-type(2) {
                        margin-left: 10rpx;
                        font-weight: bold;
                        font-size: 28rpx;
                        // color: #EC4068;
                    }
                }

                // .red {
                //     // color: #EC4068;
                // }

                // .green {
                //     // color: #6CFF8A;
                // }
            }

            .syl {
                display: flex;
                align-items: center;

                text {
                    &:nth-of-type(1) {
                        font-weight: bold;
                        font-size: 30rpx;
                        color: #fff;
                    }

                    &:nth-of-type(2) {
                        margin-left: 10rpx;
                        font-weight: bold;
                        font-size: 30rpx;
                        // color: #EC4068;
                    }
                }

                // &.red {
                //     // color: #EC4068;
                // }
            }
        }

        >.heads {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .left {
                display: flex;
                justify-content: space-between;
                align-items: center;

                image {
                    width: 36rpx;
                    margin-right: 24rpx;
                }

                .symbol {
                    font-family: HarmonyOS Sans SC;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                >.level {
                    font-size: 28rpx;
                    font-weight: 500;
                    margin: 0 11rpx 0 15rpx;
                }

                .sub {


                    font-family: HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #FFFFFF;
                }

                .cd {
                    display: flex;
                    align-items: center;

                    // .cancel {
                    //     width: 80rpx;
                    //     height: 44rpx;
                    //     font-size: 24rpx;
                    //     color: #fff;
                    //     background-color: rgb(255, 255, 255, 0.3);
                    //     display: flex;
                    //     justify-content: center;
                    //     align-items: center;
                    //     border-radius: 36rpx;
                    //     margin-left: 35rpx;

                    //     image {
                    //         width: 18rpx;
                    //         height: 24rpx;
                    //     }
                    // }
                    .che {
                        display: inline-block;
                        // padding: 8rpx 22rpx;
                        width: 83rpx;
                        height: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        /* 按钮内边距 */
                        font-size: 16px;
                        /* 字体大小 */
                        color: #00e0ff;
                        /* 按钮文字颜色 */
                        text-align: center;
                        border-radius: 50px;
                        /* 圆角 */
                        background: linear-gradient(90deg, #d976ff, #00e0ff);
                        /* 渐变边框颜色 */
                        position: relative;
                        cursor: pointer;
                        transition: all 0.3s;
                    }

                    .che text {
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #40F8EC;
                        z-index: 1;

                    }

                    .che::before {
                        content: '';
                        position: absolute;
                        top: 1px;
                        left: 1px;
                        right: 1px;
                        bottom: 1px;
                        border-radius: 50px;
                        background: #35333E;
                        /* 按钮内部背景色 */
                        z-index: 0;

                    }

                    .progress {
                        margin: 0 28rpx 0 18rpx;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            color: #FFFFFF;
                            font-size: 20rpx;
                        }

                        .bg {
                            margin-left: 8rpx;
                            width: 80rpx;
                            height: 4rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }


        }

        .data_view {
            display: flex;
            justify-content: space-between;
            // align-items: center;
            margin: 18rpx 0 0 0;

            .li_view {

                text-align: center;
                // width: 33.33%;

                &.button {
                    width: 144rpx;
                    height: 48rpx;
                    border-radius: 36rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 22rpx;
                    background-color: #fff;
                }

                .label {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: rgba(255, 255, 255, .5);
                }

                .num {
                    margin-top: 10rpx;
                    line-height: 32rpx;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;

                    align-items: center;
                    font-weight: 500;
                    font-size: 22rpx;
                    color: #FFFFFF;

                    >text {
                        &:nth-of-type(1) {
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #FFFFFF;
                        }

                        &:nth-of-type(2) {
                            // margin-top: 10rpx;
                            color: #EC4068;
                            font-weight: 400;
                            font-size: 20rpx;
                        }
                    }


                }
            }
        }


        .data_views {
            display: flex;
            justify-content: space-between;
            // align-items: center;
            margin: 20rpx 0 0 0;

            .li_view {
                text-align: center;
                // width: 33.33%;

                &.button {
                    width: 144rpx;
                    height: 48rpx;
                    border-radius: 36rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 22rpx;
                    background-color: #fff;
                }

                .label {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #a6a6a6;
                }

                .num {
                    margin-top: 21rpx;
                    display: flex;
                    flex-direction: column;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;

                    >text {
                        &:nth-of-type(1) {
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #FFFFFF;
                        }

                        &:nth-of-type(2) {
                            margin-top: 10rpx;
                            color: #EC4068;
                            font-weight: 400;
                            font-size: 20rpx;
                        }
                    }
                }
            }
        }

        .close {
            margin-top: 20rpx;
            display: flex;
            justify-content: space-between;

            .unlock {
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                font-weight: 400;
                font-size: 24rpx;
                color: #141816;
                border: none
            }

            >view {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 300rpx;
                height: 60rpx;
                border-radius: 14rpx;
                border: 1rpx solid #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
            }
        }
    }
}

.cart_view {
    width: 100%;
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 53rpx 0 62rpx 0;
    color: #fff;
    gap: 100rpx;

    .li {
        width: 120rpx;

        .icon {
            display: flex;
            justify-content: center;

            image {
                width: 120rpx;
            }

            margin-bottom:12rpx;
        }

        .text {
            margin-top: 22rpx;
            text-align: center;
            font-size: 24rpx;
            width: 120rpx;
        }
    }
}

/* 页面整体样式 */
.wallet-page {
    color: #fff;
    height: 100%;
    padding: 0 35rpx;
}

/* 顶部余额部分 */
.wallet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    margin: 52rpx 0 20rpx 0;
}

.balance-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.balance {
    font-size: 28rpx;
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    color: #FFFFFF;
}

.amount {
    font-family: HarmonyOS Sans SC;
    font-weight: bold;
    font-size: 44rpx;
    color: #FFFFFF;
    margin: 37rpx 0 21rpx;
}

.withdraw {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
    opacity: 0.5;
}

.buttons {
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 50rpx;
}

.button {
    width: 171rpx;
    height: 50rpx;
    border-radius: 25rpx;
    border: 1rpx solid #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: HarmonyOS Sans SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #FFFFFF;
}

/* 图标导航 */
.wallet-navigation {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20rpx;
}

.nav-item {
    text-align: center;
}

.nav-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 10rpx;
}

.nav-item text {
    font-size: 24rpx;
    color: #ccc;
}

/* Tab 栏 */
.tabs {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20rpx 0;
}

.tab-item {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #FFFFFF;
    padding: 10rpx 0;
    transition: color 0.3s ease;
    cursor: pointer;
}

.tab-item.active {
    color: #fff;
    font-weight: bold;
    font-size: 28rpx;
}

.tab-indicator {
    position: absolute;
    bottom: -11rpx;
    background-color: #fff;
    transition: left 0.3s ease;


    width: 61rpx;
    height: 6rpx;
    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
    border-radius: 3rpx;
}

/* 内容区域 */
.content {
    margin-top: 40rpx;
    padding-bottom: 150rpx;
}

.order-card {
    background-color: #2a2a2a;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    padding: 20rpx;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
}

.order-symbol {
    font-size: 30rpx;
    font-weight: bold;
}

.order-time {
    font-size: 24rpx;
    color: #ccc;
}

.order-body {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
}

.order-detail {
    font-size: 24rpx;
    color: #aaa;
}

.order-value {
    font-size: 24rpx;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;


}

.order-price {
    font-size: 24rpx;
    color: #aaa;
}

.button.green {
    background-color: #4caf50;
    color: #fff;
    padding: 8rpx 20rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
}
</style>