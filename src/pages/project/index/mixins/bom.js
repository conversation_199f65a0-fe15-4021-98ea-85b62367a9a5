import * as echarts from "@/uni_modules/lime-echart/static/echarts.min";

export default {
  data() {
    return {
      globalOption: {
        animation: false,
        grid: {
          borderWidth: 0, // 隐藏网格边界线
          bottom: "15%", // 增加底部空间，防止标签遮挡
          top: "25%", // 增加顶部空间，防止标题遮挡
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],
          axisLine: {
            // 隐藏X轴线
            show: false,
          },
          axisTick: {
            // 隐藏X轴刻度
            show: false,
          },
          axisLabel: {
            // 隐藏X轴标签
            show: false,
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: false,
          },
          axisLine: {
            // 隐藏Y轴线
            show: false,
          },
          axisTick: {
            // 隐藏Y轴刻度
            show: false,
          },
          axisLabel: {
            // 隐藏Y轴标签
            show: false,
          },
        },
        series: [
          {
            data: [100, 222, 9999, 444, 888, 666],
            type: "line",
            smooth: true,
            symbol: "none", // 隐藏折线上的点
            lineStyle: {
              color: "rgba(124, 228, 81, 1)", // 折线颜色
              width: 1,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(124, 228, 81, 1)",
                },
                {
                  offset: 1,
                  color: "rgba(34, 161, 235, 0)",
                },
              ]),
            },
          },
        ],
      },
      fiatbalance: "",
      cryptobalance: "",
      userCoinPage: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        list: [],
      },
      CryptouserCoinPage: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        list: [],
      },
      coins: [
        {
          name: "USD",
          change: 0.34,
          values: [95, 96, 97, 98, 96, 97, 99, 80, 100, 70, 20],
          legal: true,
          symbol: "US Dollar",
          price: "$6,987.80",
          approx: "",
          icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
        },
        {
          name: "RMB",
          change: 0.73,
          values: [50, 52, 51, 53, 54, 55, 56, 80, 100, 70, 20],
          legal: true,
          symbol: "Chinese Yuan",
          price: "¥987.80",
          approx: "≈182.20",
          icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250206/79911b35a470393bf3e5ec939d5eacd1_200x200.png",
        },
        {
          name: "AED",
          change: -2.9,
          values: [120, 115, 112, 110, 108, 107, 105, 80, 100, 70, 20],
          legal: true,
          symbol: "UAE Dirham",
          price: "¥987.80",
          approx: "≈182.20",
          icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250206/a0dfda6231297a7813359f478d3519ed_200x200.png",
        },
      ],
      cryptos: [
        {
          name: "Bitcoin",
          symbol: "BTC",
          price: "$59,044.00",
          change: 2.43,
          values: [0, 10, 20, 30, 40, 50, 60, 70, 80, 100, 120],
          icon: "https://cryptologos.cc/logos/bitcoin-btc-logo.png",
        },
        {
          name: "Ethereum",
          symbol: "ETH",
          price: "$3,900.00",
          change: -2.43,
          values: [95, 96, 97, 98, 96, 97, 99, 80, 100, 70, 20],
          icon: "https://cryptologos.cc/logos/ethereum-eth-logo.png",
        },
        {
          name: "USD Coin",
          symbol: "USD",
          price: "$7.00",
          change: 2.43,
          values: [50, 52, 51, 53, 54, 55, 56, 80, 100, 70, 20],
          icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250206/743cead7d19e23be05ecff11512cc1b4_200x200.png",
        },
        {
          name: "PEPE",
          symbol: "PEPE",
          price: "$0,00000001",
          change: -2.43,
          values: [120, 115, 112, 110, 108, 107, 105, 80, 100, 70, 20],
          icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250206/a236ce03b723e8958f27f4eb62970070_200x200.png",
        },
      ],
      currencies: [
        {
          name: "USD",
          code: "US Dollar",
          value: "$1,500.00",
          icon: "https://your-url.com/usd.png",
        },
        {
          name: "EUR",
          code: "EURO",
          value: "€1,200",
          icon: "https://your-url.com/eur.png",
        },
        {
          name: "GBP",
          code: "British Pound",
          value: "£800",
          icon: "https://your-url.com/gbp.png",
        },
      ],
    };
  },
  methods: {
    formatPercentageChange(value) {
      if (value === undefined || value === null) {
        return "--";
      }
      return value > 0 ? `+${value}%` : `${value}%`;
    },
    extendArrayWithNull(arr, targetLength) {
      // 确保目标长度大于等于当前数组长度
      const fillLength = Math.max(0, targetLength - arr.length);
      const extendedArray = arr.concat(Array(fillLength).fill(null));
      return extendedArray;
    },

    formatPriceAndChange(crypto) {
      if (
        !crypto ||
        (crypto.price === undefined && crypto.percentageChange === undefined)
      ) {
        return "--";
      }

      const price = crypto.price !== undefined ? crypto.price : "--";
      const percentageChange =
        crypto.percentageChange !== undefined
          ? `${crypto.percentageChange}%`
          : "--";
      return `${Number(price).toLocaleString("en-US")} (${percentageChange})`;
    },

    nav_to(e, names, fiat) {
      this.$Router.push({
        name: e,
        params: {
          symbol: names,
          fiat: fiat,
        },
      });
    },
  },
};
