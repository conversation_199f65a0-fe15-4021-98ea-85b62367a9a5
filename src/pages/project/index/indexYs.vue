<template>
  <view>
    <view class="index_body grayscale" :style="{ paddingBottom: 'env(safe-area-inset-bottom)' }" @scroll="handleScroll">
      <view class="barHeight"></view>
      <fpop ref="fpop"></fpop>
      <view class="coreContent">
        <view class="header">
          <view class="left_img">
            <view class="border" @click="personJump">
              <image :src="`${portrait}?x-oss-process=image/resize,m_lfit,h_100,w_100`" mode=" aspectFill">
              </image>
            </view>
          </view>
          <view class="input" @click="nav_search()">
            <view class="img">
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20240409/6e9ef6dc56f1a9216a196870e29633a7_88x88.png"
                mode="widthFix"></image>
            </view>
            <input :disabled="appDisabled" type="text" />
          </view>
          <view class="right_icon">
            <view class="icon" @click="communicate">
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20240301/dcfd9961dd6f74b40a0246cc8cdffd8a_70x70.png"
                mode="widthFix"></image>
            </view>
            <view class="icon" @tap="nav_msg">
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20240304/e8bb13e0dbfb034ad262d8792a99b295_70x70.png"
                mode="widthFix"></image>
            </view>
          </view>
        </view>
        <view class="banner">
          <view class="banner_view">
            <u-swiper :border-radius="24" :height="280" :list="swiper_list" @click="bannerClick"></u-swiper>
          </view>
          <!-- <view class="backgroundCart">
          </view> -->
        </view>
        <view class="notice">
          <u-notice-bar mode="vertical" type="none" @click="nav_details" @getMore="getMore" color="#fff" font-size="24"
            volume-size="33" :more-icon="true" :list="noticelist">
          </u-notice-bar>
        </view>

        <!-- 交易涨幅 -->
        <view class="head_title">
          <view class="title">
            对标物
          </view>
          <view class="zhangfu" @click="checkIcon('zf')">
            涨跌幅
            <view class="icon" v-if="checkSun == 1">
              <image style="margin-bottom:6rpx;"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241017/322b54c9542aba067a7f0db6d7087663_14x9.png"
                mode="widthFix"></image>
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/d94b4baee4356bc77c059735b7cf1dd0_14x9.png"
                mode="widthFix"></image>
            </view>
            <view class="" v-else>
              <view class="icon" v-if="showZf">
                <image style="margin-bottom:6rpx;"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241017/eb1ec064959f4550bbbaa35e2a7976dc_14x9.png"
                  mode="widthFix"></image>
                <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/d94b4baee4356bc77c059735b7cf1dd0_14x9.png"
                  mode="widthFix"></image>
              </view>
              <view class="icon" v-else>
                <image style="margin-bottom:6rpx;"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241017/322b54c9542aba067a7f0db6d7087663_14x9.png"
                  mode="widthFix"></image>
                <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/e15ed7478a3b6947bd42cbf3e17b1de9_14x9.png"
                  mode="widthFix"></image>
              </view>
            </view>

          </view>
          <view class="price" @click="checkIcon('price')">
            最新价
            <view class="icon" v-if="checkSun == 0">
              <image style="margin-bottom:6rpx;"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241017/322b54c9542aba067a7f0db6d7087663_14x9.png"
                mode="widthFix"></image>
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/d94b4baee4356bc77c059735b7cf1dd0_14x9.png"
                mode="widthFix"></image>
            </view>
            <view v-else>
              <view class="icon" v-if="showPrice">
                <image style="margin-bottom:6rpx;"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241017/eb1ec064959f4550bbbaa35e2a7976dc_14x9.png"
                  mode="widthFix"></image>
                <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/d94b4baee4356bc77c059735b7cf1dd0_14x9.png"
                  mode="widthFix"></image>
              </view>
              <view class="icon" v-else>
                <image style="margin-bottom:6rpx;"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241017/322b54c9542aba067a7f0db6d7087663_14x9.png"
                  mode="widthFix"></image>
                <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/e15ed7478a3b6947bd42cbf3e17b1de9_14x9.png"
                  mode="widthFix"></image>
              </view>
            </view>
          </view>
          <view style="width: 15%;">

          </view>
        </view>
        <view class="list_view_ul">
          <view style="height: 33rpx;"></view>
          <view class="list_viewss" v-for="(item, index) in allList" @click="nav_mgus(item)" :key="index">
            <view class="left_title">
              <image :src="item.icon" class="icon" />
              <view class="title">{{ item.title || 'BIT指数' }}</view>
              <!-- <view class="icon">
                <view class="but">US</view>
                <text class="msg">{{ item.titleCn || item.title || 'BIT指数' }}</text>
              </view> -->
            </view>
            <view class="echarts_view">
              <l-echart class="klink" ref="chartRef"></l-echart>
            </view>
            <view class="rose_view">
              <view class="num">
                ¥{{ Number(item.lastPrice).toFixed(2) || 0.000 }}
                <text style="font-size: 16rpx;">/份</text>
              </view>
              <view class="percentage">
                {{ formatCurrency(item.markPrice, item.title) }}
              </view>
            </view>
            <view class="trade">
              <view class="body">交易</view>
            </view>
          </view>

          <view class="reachbottom" v-if="showbottom">
            <view class="line"></view>
            <view class="tips">已经到底了</view>
            <view class="line"></view>

          </view>
          <!--  <view class="down" @tap="checkShowDow(0)" v-show="allList.length > 2">
            <image v-show="isDownShow"
              src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1af04d46580dfcd5fa0fd33787663f96_88x72.png"
              mode="widthFix"></image>
            <image v-show="!isDownShow"
              src="https://cdn-lingjing.nftcn.com.cn/image/20240301/97d0d1f2163359a196526ed555ababc9_44x36.png"
              mode="widthFix"></image>
          </view>
        -->
          <view class="null_body" v-if="allList.length == 0">
            <view class="null">
              <view class="img">
                <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                  mode="widthFix"></image>
              </view>
              <view class="text">
                暂无数据
              </view>
            </view>
          </view>
        </view>

        <!-- tabs -->
        <!-- <view class="tabbar_view">
          <u-tabs :list="tabList" :is-scroll="true" font-size='28' inactive-color='#ccc' active-color='#fff'
            :current="current" bg-color="#35333E" :bar-style='barStyle' @change="change"></u-tabs>
        </view> -->
        <!-- 列表  -->
        <!-- <view class="loading_list" v-show="isLoadingStatus == 0">
          <view>
            <view class="flex">
              <view class="balls"></view>
            </view>
            <view class="text">
              玩命加载中...
            </view>
          </view>
        </view>
      -->

        <!--
        <scroll-view scroll-y="true" style="width: 100%;padding-bottom: 100rpx; "
          v-show="list.length > 0 && isLoadingStatus == 1">
          <view class="list_view">
            <view class="list_li" @click="nav_details(item)" v-for="(item, index) in list" :key="index">
              <view class="left_img">
                <image :src="item.cover.src" mode="heightFix"></image>
              </view>
              <view class="right_font">
                <view class="title twoOver">
                  {{ item.title }}
                </view>

                <view class="time">
                  <view>
                    <view v-if="item.templateType == 1">白名单</view>
                    <view v-if="item.templateType == 2">合成</view>
                    <view v-if="item.templateType == 3">寄售</view>
                    <view v-if="item.templateType == 4">上新预告</view>
                    <view v-if="item.templateType == 5">空投</view>
                    <view v-if="item.templateType == 6">上新提醒</view>
                    <view v-if="item.templateType == 7">合成提醒</view>
                    <view v-if="item.templateType == 8">活动</view>
                    <view v-if="item.templateType == 9">运营</view>
                    <view v-if="item.templateType == 10">限额调整</view>
                    <view v-if="item.templateType == 11">分解</view>
                    <view v-if="item.templateType == 12">置换</view>
                    <view v-if="item.templateType == 13">提醒</view>
                    <view v-if="item.templateType == 14">展示位变更</view>
                    <view v-if="item.templateType == 15">竞价</view>
                    <view v-if="item.templateType == 16">冰封活动</view>
                    <view v-if="item.templateType == 17">分区调整</view>
                    <view v-if="item.templateType == 18">突袭公告</view>
                    <view v-if="item.templateType == 19">膨胀公告</view>
                    <view v-if="item.templateType == 20">开放转售</view>
                    <view v-if="item.templateType == 21">委托购买</view>
                  </view>
                  <view>
                    {{ item.createdAt }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <view class="null_body" v-show="list.length == 0 && isLoadingStatus == 2">
          <view class="null">
            <image src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
              mode="widthFix"></image>
            <view class="text">
              暂无数据
            </view>
          </view>
        </view>
        -->

      </view>
    </view>
    <TabBar ref="TabBar" :initialActiveIndex="0"></TabBar>

    <!-- 答题弹窗 -->
    <u-popup v-model="showPopup" mode="center" :maskCloseAble="false">
      <view class="popup-container">
        <view class="popup-title">开杠开通确认</view>
        <view class="popup-title1">尊敬的用户：</view>
        <view class="popup-content">
          为了便于您了解自身的风险承受能力，选择合适的投资产品和服务，请您填写以下风险承受能力评估问卷。下列问题可协助评估您对投资产品和服务的风险承受能力，请您根据自身情况认真选择。评估结果仅供参考，不构成投资建议。
        </view>
        <u-button type="primary" class="popup-button" @click="startTest">
          进行测试
        </u-button>
      </view>
    </u-popup>
  </view>
</template>

<script>
import TabBar from '@/components/public/TabBar.vue'
import fpop from '@/components/force-updates/force-updates.vue'

import {
  base64
} from 'js-md5';
// import Umeng from 'umeng/lib/umeng_push';
// 获取 module
// #ifdef H5
import {
  nav_contactService
} from '@/utils/utils'
// #endif
// #ifdef APP
var qiyuModule = uni.requireNativePlugin("Netease-QiyuModule")
// #endif
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'

export default {
  components: {
    TabBar,
    fpop,
  },
  computed: {
    filterUsList() { //涨幅
      if (this.allList) {
        return this.allList.slice(0, this.currentlyDisplayed);
      }
    },
  },
  data() {
    return {
      noticelist: [

      ],
      showbottom: false,
      lastScrollTop: 0, // 记录上一次的滚动
      allList: [],
      IndexList: [],
      showPopup: false,
      swiper_list: [],
      swiperCurrent: 0,
      portrait: "https://cdn-lingjing.nftcn.com.cn/image/20240310/a902bce56e63d929629524dea93e23af_120x120.png",
      appDowShow: true,
      appDisabled: false,
      tabList: [{
        name: '全部公告',
        value: '',
      }, {
        name: '开杠',
        value: '2',
      }, {
        name: '模拟美股',
        value: '3',
      }],
      current: 0,
      barStyle: {
        'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
      },
      list: [],
      pageNum: 1,
      isFooter: true, //没有更多了
      isRequest: false, //多次请求频繁拦截
      usList: [],
      globalOption: {
        grid: {
          borderWidth: 0, // 隐藏网格边界线
          bottom: '15%', // 增加底部空间，防止标签遮挡
          top: '25%', // 增加顶部空间，防止标题遮挡
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
          axisLine: { // 隐藏X轴线
            show: false
          },
          axisTick: { // 隐藏X轴刻度
            show: false
          },
          axisLabel: { // 隐藏X轴标签
            show: false
          }
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: false
          },
          axisLine: { // 隐藏Y轴线
            show: false
          },
          axisTick: { // 隐藏Y轴刻度
            show: false
          },
          axisLabel: { // 隐藏Y轴标签
            show: false
          }
        },
        series: [{
          data: [100, 222, 9999, 444, 888, 666],
          type: 'line',
          smooth: true,
          symbol: 'none', // 隐藏折线上的点
          lineStyle: {
            color: 'rgba(124, 228, 81, 1)',// 折线颜色
            width: 1
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [{
                offset: 0,
                color: 'rgba(124, 228, 81, 1)'
              },
              {
                offset: 1,
                color: 'rgba(34, 161, 235, 0)'
              }
              ]
            )
          },
        }],
      },
      showZf: false,
      showPrice: false,
      currentlyDisplayed: 3, // 当前显示的数据条数
      isDownShow: false, //涨幅 open/close
      bitInof: {
        kline: [],
        aimPrice: "",
        title: 'BIT指数',
        aimRatio: "",
        lastPrice: "",
        icon: "https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png"
      },
      checkSun: 0,
      isLoadingStatus: 0,
      bitInfo: {}
    }
  },
  onShow() {
    this.getUserInfo()
  },
  onLoad() {
    uni.hideTabBar();
    this.firstVisit()
    this.getBanners()
    this.getUserInfo()
    this.get_tab() //载入衍生配置
    this.getList()//公告列表


    this.get_recommendList(1, 'desc')//
    // this.getBitKline()//查询bit指数
    // this.getParam()
    this.get_bit_info()//bit配置
    // #ifdef APP
    this.appDisabled = true
    // #endif

    this.makeUpTogther()

  },
  onPullDownRefresh() {
    setTimeout(() => {
      this.isLoadingStatus = 0
      this.isFooter = true
      this.isRequest = false
      this.pageNum = 1
      this.firstVisit()
      this.list = []
      this.getBanners()
      this.getUserInfo()
      this.get_tab() //载入衍生配置
      this.getList()//公告列表
      this.get_recommendList(1, 'desc')//
      // this.getBitKline()//查询bit指数
      // this.getParam()
      this.get_bit_info()//bit配置
      uni.stopPullDownRefresh(); //停止下拉刷新动画
      this.makeUpTogther()

    }, 1000);
  },
  onReachBottom() {
    if (this.isFooter) {
      if (this.isRequest == false) {
        this.getList()
      } else {
        console.log("请求超时，已经拦截")
      }
    } else {
      console.log("已经到底了")
    }
    this.showbottom = true
  },
  watch: {},
  methods: {
    handleScroll(event) {
      console.log(event);

      const scrollTop = event.detail.scrollTop; // 获取当前滚动条的垂直滚动位置

      if (scrollTop > this.lastScrollTop) {
        // 当前滚动位置大于上一次滚动位置，表示上滑
        this.showbottom = true;  // 页面滚动到底部
      }

      // 更新 lastScrollTop
      this.lastScrollTop = scrollTop;

    },
    formatCurrency(amount, title) {
      let initstr = Number(amount)
      // 保证只有2位小数
      let fixedAmount = initstr.toFixed(2);

      // 将整数部分和小数部分分开
      let [integerPart, decimalPart] = fixedAmount.split('.');

      // 使用正则添加千位分隔符
      let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      // 返回格式化后的金额
      if (title == 'BIT指数') {
        return '¥' + formattedIntegerPart + '.' + decimalPart;
      } else {
        return '＄' + formattedIntegerPart + '.' + decimalPart;
      }
    },
    getMore() {
      // this.$refs.TabBar.bv_platform = false
      // this.$refs.TabBar.checkPlatform()
      this.$Router.push({
        name: 'noticeYs',
        params: {
          Derivation: true
        }
      })
    },
    async firstVisit() {

      let pages = getCurrentPages(); // 获取当前页面栈
      let currentPage = pages[pages.length - 1]; // 获取当前页面
      let currentRoute = currentPage.route; // 获取当前页面的路径

      let res = await this.$api.VisitorShare({
        module: "HOME_PAGE_YANSHENG",
        from: 'h5',
        page: currentRoute
      });
      console.log('request' + currentRoute);

    },
    async makeUpTogther() {
      // await this.getBitKline()
      await this.getParam()
      setTimeout(() => {
        // ...this.usList
        const result = [this.bitInfo.showBit ? this.bitInof : '', ...this.IndexList,];
        console.log(result);

        this.allList = result;
        this.allList = this.sortByChange([...this.allList]).reverse();
        setTimeout(() => {
          this.allList.forEach((item, index) => {
            if (item.aimRatio) {
              item.lastPrice = item.aimPrice
              item.increaseRatio = item.aimRatio
            }
            if (item.lastTradePrice) {
              item.lastPrice = item.lastTradePrice
            }
            if (!item.increaseRatio && item.cover) {
              item.increaseRatio = 0.00
            }
            if (!item.lastPrice && item.cover) {
              item.lastPrice = 0.00
            }
            // if (item.increaseRatio == null) {
            //   item.increaseRatio = 0
            // }
            // if (item.lastTradePrice == null) {
            //   item.lastPrice = 0
            // }
            // let isRed = item.increaseRatio >= 0 && item.increaseRatio != null ? true : false
            let isRed = (item.aimRatio !== undefined && item.aimRatio !== null ? item.aimRatio : item.increaseRatio) >= 0;
            this.init(index, item.kline || item.prices, isRed)
          })
          console.log(this.allList, '所有的12312312');
        }, 100);
      }, 0);
    },
    // 排序函数：按涨跌幅（aimRatio或increaseRatio）
    sortByChange(arr) {
      return arr.sort((a, b) => {
        const aRatio = a.increaseRatio || 0;
        const bRatio = b.increaseRatio || 0;
        return aRatio - bRatio;
      });
    },

    // 排序函数：按价格（aimPrice或lastPrice）
    sortByPrice(arr) {
      return arr.sort((a, b) => {
        const aPrice = a.lastPrice || 0
        const bPrice = b.lastPrice || 0
        return aPrice - bPrice;
      });
    },
    startTest() {
      this.showPopup = false;
      this.$Router.push({
        name: 'riskAssessment'
      })
      // 在此处添加开始测试的逻辑
      console.log("开始测试");
    },
    // #ifdef H5
    nav_contactService,
    // #endif
    communicate() {
      if (this.isLogin) {
        // // #ifdef H5
        // this.$Router.push({
        //   name: 'helpCenter'
        // })
        // // #endif
        //
        // // #ifdef APP
        // let link = `${getApp().globalData.url}pagesA/project/helpCenter/index`
        // this.$Router.push({
        //   name: 'webView',
        //   params: {
        //     url: link
        //   }
        // })
        //  // #endif


        // #ifdef H5
        this.$Router.push({
          name: 'helpCenter'
        })
        // #endif

        // #ifdef APP-PLUS
        let link = `${getApp().globalData.url}pagesA/project/helpCenter/index`
        this.$Router.push({
          name: 'webView',
          params: {
            url: link
          }
        })
        // #endif


        // #ifdef H5
        // this.nav_contactService()
        // #endif
        // #ifdef APP
        // this.testOpenService()
        // #endif
      } else {
        this.$Router.push({
          name: 'mainLogin'
        })
      }
      console.log('沟通')

      // 调用同步方法

    },
    //七鱼客服设置信息
    qiyu_setInfo(info) {
      console.log("触发了")
      qiyuModule.initSdk({
        appKey: "3d1f1a45bc64cc1aab8aca825ff89c13",
        appName: "Bigverse",
        pkCerName: "PushKit推送证书名；对应云信的pkCername"
      })
      const {
        contractAddress,
        userId,
        name,
        email,
        phone,
      } = info
      const data = [{
        label: 'contractAddress',
        key: 'contractAddress',
        value: contractAddress
      }]
      console.log(JSON.stringify(data))
      qiyuModule.setUserInfo({
        userId,
        data: JSON.stringify(data),
      }, (ret) => {
        if (ret.code == 200) {
          console.log("七鱼设置信息成功")
          //成功
        } else {
          console.log("失败")
          console.log(ret)
          //失败
        }
      })
    },
    testOpenService() {
      qiyuModule.openServiceActivity({
        title: 'NFTCN客服',
        source: {
          title: 'NFTCN客服',
          // vipLevel: 1, // 设置用户VIP等级
          // robotId: 2222, //分配机器人
          // staffId: 3444, //分配客服
          // groupId: 345, //客服组id
          // groupTmpId: 455, //分流客服组id
          // robotFirst: false, //是否客服组优先
          //访客头像
          ios_sendProduct: false,
        },
        //iOS打开界面的方式，push/present,默认push
        openMode: 'push',

      });
    },
    personJump() { //个人信息
      uni.navigateTo({
        url: '/pagesA/project/person/person'
      })
    },
    async getBanners() {
      let res = await this.$api.java_pgc_tab1({
        tapType: 1
      });
      if (res.status.code == 0) {
        // console.log(res.result.banners)
        this.swiper_list = res.result.banners.map((item) => {
          return {
            ...item,
            image: item.image.src,
          };
        });
        // console.log(this.swiper_list)
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    bannerClick(index) {
      const {
        needLogin,
        linkType,
        link
      } = this.swiper_list[index]
      console.log(this.swiper_list[index].link)
      if (linkType == 0) {
        return false
      } else {
        if (needLogin == 1) {
          if (uni.getStorageSync('token')) {
            // #ifdef APP
            this.$Router.push({
              name: "webView",
              params: {
                url: link,
              }
            })
            // #endif
            // #ifdef H5
            window.location.href = link
            // #endif
          } else {
            this.$Router.push({
              name: "mainLogin"
            })
          }
        } else {
          // #ifdef APP
          this.$Router.push({
            name: "webView",
            params: {
              url: link,
            }
          })
          // #endif
          // #ifdef H5
          window.location.href = link
          // #endif
        }
      }
    },
    async getUserInfo() {
      let res = await this.$api.userInfo({});
      if (res.status.code == 0) {
        this.isLogin = true
        this.portrait = res.result.avatar
        let data = res.result
        uni.setStorageSync("uid", data.userId)
        uni.setStorageSync("contract_address", data.contractAddress)
        uni.setStorageSync("nickname", data.name)
        uni.setStorageSync("avatar", data.avatar)
        // this.showPopup = !data.questionStatus;
        let certification;
        if (res.result.authStatus == 31 && res.result.authType == 1) {
          certification = 1
        } else if (res.result.authStatus == 31 && res.result.authType == 2) {
          certification = 2
        } else {
          // uni.showToast({
          // 	title: res.status.msg,
          // 	icon: 'none',
          // 	duration: 3000
          // });
        }
        uni.setStorageSync("certification", certification)
        // #ifdef APP
        this.qiyu_setInfo(data)
        // #endif
      } else {
        this.isLogin = false
      }
    },
    nav_search() {
      this.$Router.push({
        name: 'Desearch'
      })
    },
    nav_personal() {
      this.$Router.pushTab({
        name: "personal",
      })
    },
    async get_tab() {
      console.log('获取配置pay_tabbar')
      let res = await this.$api.java_commonconfigInfo({
        name: 'pay_tabbar_new'
      });
      if (res.status.code == 0) {
        let val = JSON.parse(res.result.value)
        uni.setStorageSync('pay_tabbarList', val)
        uni.setStorageSync('currentTabbar', 'pay')
        this.$refs.TabBar.get_config()
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    change(index) {
      this.current = index
      this.list = [];
      this.pageNum = 1;
      this.isLoadingStatus = 0
      this.isFooter = true
      this.getList()
    },
    async get_recommendList(sortField, sortOrder) {
      let res = await this.$api.scaleIncreaseList({
        pageNum: 1,
        pgaeSize: "",
        sortField,
        sortOrder
      });
      if (res.status.code == 0) {
        this.usList = res.result.list
        // setTimeout(() => {
        //   this.usList.forEach((item, index) => {
        //     let isRed = item.increaseRatio >= 0 && item.increaseRatio != null ? true : false
        //     this.init(index, item.kline, isRed)
        //   })
        //   console.log(this.usList, '美股');
        // }, 100)

      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    async getList() {
      this.isRequest = true
      if (this.pageNum == 1) {
        this.list = [];
      }
      const {
        status,
        result
      } = await this.$api.java_officialArticleList({
        pageSize: 10,
        pageNum: this.pageNum,
        templateType: this.templateType,
        title: this.title,
        tapType: this.current == 0 ? -1 : this.current
      });
      if (status.code === 0) {
        this.isRequest = false
        if (result.list == null || result.list == "") {
          this.isFooter = false
          if (this.list == "") {
            this.isLoadingStatus = 2
          }
        } else {
          this.isLoadingStatus = 1
          this.pageNum++
          result.list.forEach((item) => {
            this.list.push(item)
          })
          this.noticelist = result.list

          // this.noticelist = result.list.map(item => item.title);
        }
      } else {
        uni.showToast({
          title: status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    //初始化k线图，循环加载折线图，通过下标传递过来
    async init(index, globalOption, isRed) {
      let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
      // chart 图表实例不能存在data里
      // 计算最大值和最小值
      const minValue = Math.min(...globalOption);
      const maxValue = Math.max(...globalOption);

      // 设置 y 轴的最小值和最大值
      data.yAxis.min = minValue;
      data.yAxis.max = maxValue;
      if (isRed) {
        data.series[0].lineStyle.color = 'rgba(236, 63, 103, 1)'
        data.series[0].areaStyle.color.colorStops[0].color = 'rgba(215, 57, 94, 1)'
        data.series[0].areaStyle.color.colorStops[1].color = 'rgba(255, 90, 117, 0)'
      }
      data.series[0].data = []
      // 扩展数组并填充 null
      const extendedData = this.extendArrayWithNull(globalOption, 11);
      data.series[0].data = extendedData
      if (index == 99) {
        const lEchart = await this.$refs.chartRefBit.init(echarts);
        lEchart.setOption(data)
      } else {
        const lEchart = await this.$refs.chartRef[index].init(echarts);
        lEchart.setOption(data)
      }
    },
    extendArrayWithNull(arr, targetLength) {
      // 确保目标长度大于等于当前数组长度
      const fillLength = Math.max(0, targetLength - arr.length);
      const extendedArray = arr.concat(Array(fillLength).fill(null));
      return extendedArray;
    },
    checkIcon(type) {
      if (type == 'zf') {
        if (this.checkSun == 1) {
          console.log("涨幅首次选中")
          this.checkSun = 0
          this.showZf = false
          let sortField = this.checkSun == 0 ? 1 : 2
          let sortOrder = this.showZf ? 'asc' : 'desc'
          this.pageNum = 1;
          // this.usList = []
          // this.get_recommendList(sortField, sortOrder)

          this.allList = this.sortByChange([...this.allList]).reverse();
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
          return

        }
        this.showZf = !this.showZf
        this.checkSun = 0
        if (!this.showZf) {
          this.allList = this.sortByChange([...this.allList]).reverse();
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
        } else {
          this.allList = this.sortByChange([...this.allList])
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
        }


        let sortField = this.checkSun == 0 ? 1 : 2
        let sortOrder = this.showZf ? 'asc' : 'desc'
        console.log('sortField', sortField)
        console.log('sortOrder', sortOrder)
        this.pageNum = 1;
        // this.usList = []
        // this.get_recommendList(sortField, sortOrder)
      } else {
        if (this.checkSun == 0) {
          console.log("价格首次选中")
          this.checkSun = 1
          this.showPrice = false
          let sortField = this.checkSun == 0 ? 1 : 2
          let sortOrder = this.showPrice ? 'asc' : 'desc'
          // this.pageNum = 1;
          // this.usList = []
          // this.get_recommendList(sortField, sortOrder)
          console.log(this.allList, '排序后');

          this.allList = this.sortByPrice([...this.allList]).reverse();
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
          return
        }
        // this.allList = this.sortByPrice([...this.allList]).reverse();


        this.checkSun = 1
        if (this.showPrice) {
          this.allList = this.sortByPrice([...this.allList]).reverse();
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
        } else {
          this.allList = this.sortByPrice([...this.allList])
          setTimeout(() => {
            this.allList.forEach((item, index) => {
              let isRed = Number(item.increaseRatio) >= 0;
              this.init(index, item.kline || item.prices, isRed)
            })
          }, 100);
        }
        this.showPrice = !this.showPrice

        let sortField = this.checkSun == 0 ? 1 : 2
        let sortOrder = this.showPrice ? 'asc' : 'desc'
        // this.pageNum = 1;
        // this.usList = []
        // this.get_recommendList(sortField, sortOrder)
        console.log('sortField', sortField)
        console.log('sortOrder', sortOrder)
      }
    },
    checkShowDow(index) {
      this.isDownShow = !this.isDownShow
      if (this.isDownShow) {
        this.get_recommendList()
        this.currentlyDisplayed = this.allList.length;
      } else {
        this.currentlyDisplayed = 3
      }
      setTimeout(() => {
        this.allList.forEach((item, index) => {
          let isRed = Number(item.increaseRatio) >= 0;
          this.init(index, item.kline || item.prices, isRed)
        })
      }, 100);
    },
    async getBitKline() {
      // getMutilCoin
      let resIndex = await this.$api.indexs()
      let resAll = await this.$api.getMutilCoin()   // 拿价格
      console.log(resAll.result);
      let coinData2 = resAll.result
      let coinDataIcon = resIndex.result.list
      console.log(coinDataIcon, '1231231231212');

      if (resIndex.status.code === 0) {
        try {
          const responses = await this.$api.exchangeGetKlineCoin() // 拿k线
          let kData = responses.result
          // 整合数据
          const combinedData = Object.keys(kData).map(coinKey => {
            const coinInfo = coinDataIcon.find(item => item.contractName === coinKey);
            if (!coinInfo) return {}; // 如果没有匹配到，返回空对象
            return {
              markPrice: coinInfo.markPrice,  // 从 coinDataIcon 获取 markPrice
              icon: coinInfo.icon,            // 从 coinDataIcon 获取 icon
              title: coinKey.split('-')[1],
              aimPrice: coinData2[coinKey].aimPrice,
              lastPrice: coinData2[coinKey].lastPrice,
              prices: kData[coinKey],
              increaseRatio: (Number(coinData2[coinKey].aimPrice) - Number(coinData2[coinKey].lastPrice)) / Number(coinData2[coinKey].lastPrice)
            };
          });
          this.IndexList = combinedData;
        } catch (error) {
          console.error("请求出错", error);
        }
      }

      let res = await this.$api.exchangeGetKline({});
      if (res.status.code == 0) {
        this.bitInof.kline = res.result
        console.log(this.bitInof.kline, 'bit');

      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    async getParam() {
      await this.getBitKline()//查询bit指数
      let res = await this.$api.GetExchangeParam({});
      if (res.status.code == 0) {
        let { aimPrice, lastPrice } = res.result
        let aimRatio = ((Number(aimPrice) - Number(lastPrice)) / lastPrice).toFixed(2)
        console.log(aimRatio * 100)
        this.bitInof.aimPrice = res.result.aimPrice
        this.bitInof.aimRatio = aimRatio
        this.bitInof.markPrice = res.result.aimPrice

        let isRed = aimRatio >= 0 && aimRatio != null ? true : false
        console.log(this.bitInof.kline, 'kkkkk');

        // setTimeout(() => {
        //   this.init(99, this.bitInof.kline, isRed)
        // }, 100)
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    nav_details(item) {
      console.log(item)
      if (item.linkType == "NO_LINK") {
        return
      } else if (item.linkType == "WEB_LINK" && item.h5Link) {
        this.$Router.push({
          name: "webView",
          params: {
            url: item.h5Link
          }
        })
        return
      } else if (item.linkType == "DETAIL_LINK") {
        // #ifdef H5
        this.$Router.push({
          name: "officialDetail",
          params: {
            id: item.id,
            linkType: item.linkType
          }
        })
        // #endif
        // #ifdef APP
        let url = `${getApp().globalData.url}pagesA/project/official/detail?id=${item.id}`
        this.$Router.push({
          name: "webView",
          params: {
            url
          }
        })
        // #endif
        return
      }
      if (this.isApp) {
        this.$Router.push({
          name: "officialDetail",
          params: {
            id: item.id,
            platform: this.platform,
            token: this.token
          }
        })
      } else {
        this.$Router.push({
          name: "officialDetail",
          params: {
            id: item.id,
          }
        })
      }
    },
    nav_msg() {
      this.$Router.push({
        name: "message"
      })
    },
    nav_bit() {
      this.$Router.pushTab({
        name: "contract-BITindex"
      })
    },
    nav_mgus(e) {
      if (e.titleCn) {
        this.$Router.pushTab({
          name: "mgUs"
        })
      } else {
        this.$Router.pushTab({
          name: "contract-BITindex"
        })
      }

    },
    async get_bit_info() {
      let res = await this.$api.java_commonconfigInfo({
        name: 'bit_show'
      });
      if (res.status.code == 0) {
        let info = JSON.parse(res.result.value)
        this.bitInfo = info
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url(https://cdn-lingjing.nftcn.com.cn/h5/ttf/YouSheBiaoTiHei-2.ttf);
}

.notice {
  height: 66rpx;
  background: #46454F;
  border-radius: 33rpx;
}

.popup-container {
  margin: 0 45rpx;
  background: #34323E;
  border-radius: 25rpx;
  border: 1rpx solid #50EFED;
  overflow: hidden;

  .popup-button {
    width: 287rpx;
    height: 70rpx;
    background: linear-gradient(0deg, #EF91FB, #40F8EC);
    border-radius: 38rpx;

    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #000000;
    line-height: 45rpx;
    margin-bottom: 42rpx;
  }

  .popup-title1 {
    display: flex;
    justify-content: flex-start;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 34rpx;
    padding: 35rpx 0rpx 0rpx 63rpx;

  }

  .popup-title {
    text-align: center;
    margin-top: 30rpx;
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #63EAEE;
    line-height: 45rpx;
  }

  .popup-content {
    padding: 0 51rpx 55rpx 63rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 34rpx;
  }
}

.page {
  background-color: #000;
  overflow: hidden;
  /* 防止背景色溢出 */
}

.popup-box::v-deep {
  .u-drawer__scroll-view {
    background-color: #2B2B2B;
  }
}

.pop_body {
  padding: 40rpx;

  .title {
    color: #fff;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .content {
    min-height: 450rpx;
    color: #616161;
    font-size: 24rpx;
    overflow: auto;

    .text {
      line-height: 40rpx;

      text {
        color: #1FEDF0;
      }
    }
  }

  .footer {
    .agreement {
      font-size: 24rpx;
      color: #fff;
      text-align: center;
      padding: 24rpx 0rpx;

      text {
        color: #1FEDF0;
      }
    }

    .but {
      .active {
        width: 100%;
        background: linear-gradient(132deg, #F6AAF2 0%, #8CC9F3 51%, #00FBEF);
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        color: #121212;
        font-size: 28rpx;
        font-weight: 600;
      }

      .quit {
        color: #616161;
        font-size: 24rpx;
        text-align: center;
        margin-top: 30rpx;
      }
    }
  }
}

.index_body {
  height: 100%;
  overflow: scroll;
  /* 必须设置为scroll才能触发滚动事件 */
  padding: 36rpx;
}

.coreContent {
  padding-bottom: 170rpx;
}

// ::v-deep .u-tab-item {
// 	background-color: var(--main-bg-color);
// }
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left_img {
    border-radius: 40rpx;
    width: 96rpx;
    height: 96rpx;
    background: var(--primary-button-color);
    padding: 4rpx;

    .border {
      width: 88rpx;
      height: 88rpx;
      background-color: var(--main-bg-color);
      border-radius: 36rpx;
      padding: 4rpx 6rpx 5rpx 5rpx;

      image {
        width: 78rpx;
        height: 78rpx;
        border-radius: var(--medium-font-size);
      }
    }

  }

  .input {
    border: 1px solid rgba(255, 255, 255, 0.5);
    width: 378rpx;
    height: 70rpx;
    border-radius: var(--medium-font-size);
    position: relative;

    .img {
      position: absolute;
      left: 10rpx;
      top: 8rpx;

      image {
        width: 50rpx;
      }
    }
  }

  .right_icon {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .icon {
      width: 70rpx;
      height: 70rpx;
      margin-left: 10rpx;

      image {
        width: 70rpx;
        height: 70rpx;
      }
    }
  }
}

.banner {
  position: relative;
  height: 324rpx;
  margin-top: 50rpx;

  .banner_view {
    // width: 676rpx;
    height: 284rpx;
    // position: absolute;
    // left: 0;
    // top: 0;
    z-index: 2;
    background: var(--primary-button-color);
    // padding: 1px;
    border-radius: var(--small-font-size);
  }

  .backgroundCart {
    position: absolute;
    top: 36rpx;
    left: 0;
    right: 0;
    height: 308rpx;
    margin: 0 auto;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240304/67f227ca07e1233ee7f27d0412be580b_700x338.png);
    background-size: 100% 100%;
    z-index: 1;
  }
}

.null_body {
  height: 400rpx;

  .null {
    .img {
      display: flex;
      justify-content: center;

      image {
        width: 140rpx;
      }
    }

  }

  .text {
    color: #A6A6A6;
    font-size: 28rpx;
    text-align: center;
    margin-top: 30rpx;
  }

  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.null_body_view {
  height: 550rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255);
  font-size: 25rpx;
  // height: 80rpx;
  margin-top: 42rpx;

  .title {
    // width: 300rpx;
    padding-left: 30rpx;
  }

  .zhangfu {
    width: 174rpx;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;

    .icon {
      width: 14rpx;
      margin-left: 6rpx;

      image {
        width: 14rpx;
      }
    }
  }

  .price {
    width: 140rpx;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;

    .icon {
      width: 14rpx;
      margin-left: 6rpx;

      image {
        width: 14rpx;
      }
    }
  }
}

.list_view_ul {
  min-height: 400rpx;

  .list_viewss {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    margin-bottom: 42rpx;

    .left_title {
      width: 190rpx;
      display: flex;
      align-items: center;

      .icon {
        width: 67rpx;
        height: 67rpx;
        border-radius: 50%;
        margin-right: 17rpx;
      }

      .title {
        font-size: 26rpx;
        // margin-bottom: 14rpx;
        font-weight: 400;
      }

      .icon {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 24rpx;

        .but {
          color: #fff;
          background-color: #8D76DB;
          border-radius: 4rpx;
          font-size: 22rpx;
          width: 44rpx;
          height: 28rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 10rpx;
        }

        .msg {
          color: rgba(255, 255, 255, 0.5);
        }

      }
    }

    .echarts_view {
      width: 175rpx;
      height: 56rpx;
    }

    .trade {
      width: 140rpx;
      height: 50rpx;
      border-radius: 25rpx;
      background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);

      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .body {
        width: 136rpx;
        height: 48rpx;
        background: #2D2B36;
        border-radius: 25rpx;
        display: flex;
        align-items: center;
        overflow: hidden;
        justify-content: center;
        color: #45F5EC;
        overflow: hidden;
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 24rpx;
      }
    }

    .rose_view {
      width: 170rpx;
      display: flex;
      align-items: center;
      flex-direction: column;

      .num {
        font-size: 30rpx;
        font-weight: 600;
        text-align: right;
      }

      .percentage {
        font-weight: 400;
        font-size: 18rpx;
        color: rgba(255, 255, 255, 0.5);
        line-height: 7px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 12rpx;

        image {
          width: 28rpx;
        }

        text {
          margin-left: 15rpx;
          color: #7CE451;
          font-size: 22rpx;

          &.red {
            color: #EC3F67;
          }
        }
      }
    }
  }

  .reachbottom {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 47rpx;

    .line {
      width: 152rpx;
      height: 2rpx;
      background: #46454F;
    }

    .tips {
      margin: 0 24rpx;
      font-family: HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #FFFFFF;
      opacity: 0.5;
    }
  }

  .down {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;

    image {
      width: 44rpx;
    }
  }
}



.list_view {
  width: 100%;
  margin-top: 40rpx;
}

.list_li {
  width: 100%;
  // height: 200rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 40rpx;
  background: #35333E;
  border-radius: 16px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  border: 1rpx solid #717171;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.left_img {
  // margin: 0 40rpx 0 0;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  >image {
    height: 190rpx;
  }
}

.right_font {
  position: absolute;
  right: 20rpx;
  width: 361rpx;

  .title {
    width: 361rpx;
    line-height: 50rpx;
    font-family: 'YouSheBiaoTiHei';
    font-weight: 400;
    color: #FFFFFF;
    font-size: 36rpx;
  }
}


.time {
  display: flex;
  align-items: center;
  margin-top: 10rpx;

  >view:nth-child(1) {
    height: 30rpx;
    line-height: 30rpx;
    background: #FFFFFF;
    text-align: center;
    font-weight: 400;
    font-size: 20rpx;
    color: #35333E;
    transform: skewX(30deg);
    margin-right: 30rpx;
    padding: 0rpx 10rpx;

    >view {
      transform: skewX(-30deg);
    }
  }

  >view:nth-child(2) {
    color: $uni-color-gray;
    font-size: $uni-font-size-h4;
    line-height: 44rpx;
    font-weight: 400;
    font-size: 22rpx;
  }
}

.null_body {
  .null {
    image {
      width: 242rpx;
    }
  }

  .text {
    color: #A6A6A6;
    font-size: 28rpx;
    text-align: center;
    margin-top: 30rpx;
  }

  width: 100%;
  height: 30vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
