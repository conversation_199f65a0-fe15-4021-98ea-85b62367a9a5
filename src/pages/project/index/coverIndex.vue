<template>
	<view class="openCoverImg" v-show="isAdvertising">
		<image :src="coverImg" mode="widthFix"></image>
		<view class="right_view" @click="clickSkip()">
			跳过 <text v-if="count>0">{{count}}</text>
		</view>
	</view>
</template>
 
<script>
	export default {
		data() {
			return {
				isAdvertising: true,  
				coverImg: "",
				count: 0,
				intervalId:"",
				showActive:true
			}
		},
		onLoad() {
			let platform = uni.getSystemInfoSync().platform
			if(platform == 'ios'){
				this.get_version()
			}
			if(platform == 'android'){
				this.get_android_version()
			}
			this.getAppCover()
		},
		methods: {
			async get_android_version() {
			  let res = await this.$api.java_commonconfigInfo({
			    name: 'android_store_pay_version',
			  });
			  if (res.status.code == 0) {
			    let curV = uni.getSystemInfoSync().appVersion
			    let reqV = res.result.value
			    console.log(curV)
			    console.log(reqV)
			    if (curV == reqV) {
					this.showActive = false
			    } else {
			      this.showActive = true
			    }
			    console.log(this.showActive)
			  } else {
			    uni.showToast({
			      title: res.status.msg,
			      icon: 'none',
			      duration: 3000
			    });
			  }
			},
			async get_version() {
			  let res = await this.$api.java_commonconfigInfo({
			    name: 'ios_apple_pay_version',
			  });
			  if (res.status.code == 0) {
			    let curV = uni.getSystemInfoSync().appVersion
			    let reqV = res.result.value
			    console.log(curV)
			    console.log(reqV)
			    if (curV == reqV) {
			      this.showActive = false
			    } else {
			      this.showActive = true
			    }
			    console.log(this.showActive)
			  } else {
			    uni.showToast({
			      title: res.status.msg,
			      icon: 'none',
			      duration: 3000
			    });
			  }
			},
			async getAppCover() {
				let res = await this.$api.java_commonconfigInfo({
					name: 'advertisement',
				});
				if (res.status.code == 0) {
					this.info = JSON.parse(res.result.value)
					if (this.info.duration == 0) {
						clearInterval(intervalId);
						if(this.showActive){
							this.$Router.pushTab({
								name: 'index'
							})
						}else{
							this.$Router.push({
								name: 'storeIndex'
							})
						}
					} else {
						this.coverImg = this.info.advertisement
						console.log(this.coverImg)
						this.count = this.info.duration
						this.startCountdown()
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			startCountdown() {
				this.intervalId = setInterval(() => {
					this.count--;
					if (this.count <= 0) {
						clearInterval(this.intervalId);
						let currentTabbar = uni.getStorageSync('currentTabbar')
						if(this.showActive){
							if(currentTabbar == 'pay'){
								this.$Router.pushTab({
									name:'indexYs'
								})
							}else{
								this.$Router.pushTab({
									name:'index'
								})
							}
						}else{
							this.$Router.push({
								name: 'storeIndex'
							})
						}
					}
				}, 1000);
			},
			clickSkip() {
				clearInterval(this.intervalId);
				let currentTabbar = uni.getStorageSync('currentTabbar')
				if(this.showActive){
					if(currentTabbar == 'pay'){
						this.$Router.pushTab({
							name:'indexYs'
						})
					}else{
						this.$Router.pushTab({
							name:'index'
						})
					}
				}else{
					this.$Router.push({
						name: 'storeIndex'
					})
				}
			},
			onNavigationBarBackPress() {
			  // 阻止默认的返回行为
			  return false;
			},
		}
	}
</script>

<style lang="scss">
	.openCoverImg {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		overflow: hidden;
		z-index: 99;
		display: flex;
		justify-content: center;
		align-items: center;

		.right_view {
			font-size: 26rpx;
			width: 150rpx;
			height: 50rpx;
			line-height: 50rpx;
			text-align: center;
			background-color: rgba(0, 0, 0, 0.3);
			color: #fff;
			border-radius: 25rpx;
			position: absolute;
			right: 60rpx;
			top: var(--status-bar-height);

			text {
				color: peru;
				margin-left: 10rpx;
			}
		}

		image {
			width: 100%;
		}
	}
</style>