<template>
	<view class="body">
	   <view class="bg">			
		   <image src="https://cdn.yanjie.art/image/20241217/3436e9a06851b1b40097e350611c4577_750x2514.png" mode="widthFix"></image>
	   </view>
	   <view class="button" @click="download()" v-show="show">
		   <!-- <image src="https://cdn.yanjie.art/image/20240411/7b721f54b66ffe161baca731f1c6af29_1800x386.png" mode="widthFix"></image> -->
	   </view>
	   <view class="loadding" v-show="!show">
		   <view class="flex" >
			   <svg viewBox="25 25 50 50" v-show="showLoading">
				 <circle cx="50" cy="50" r="20"></circle>
			   </svg>
		   </view>
		   <view class="text">
			   下载时请勿离开页面(如未自动安装,请手动安装)
		   </view>
	   </view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				show:true,
			   showLoading:true
			}
		},
		onLoad(options) {
			 
		},
		methods: {
			download(){
			   this.show= false
			   setTimeout(()=>{
				   this.showLoading= false
			   },5000)
			   window.location.href = 'https://cdn.yanjie.art/h5/yanjie/yanjie.mobileconfig'
		   } 
		}
	}
</script>
<style lang="scss">
	.body{
		position: relative;
		.bg{
			width:100%;
			image{
				width:100%;
			}
		}
		.button{
			width:600rpx;
			position: absolute;
			top:346rpx;
			left:0;
			right:0;
			margin:0 auto;
			height:120rpx;
			image{
				width:100%;
			}
		}
		.loadding{
			width:600rpx;
			position: absolute;
			top:326rpx;
			left:0;
			right:0;
			margin:0 auto;
			display: block;
			.flex{
				display: flex;
				justify-content: center;
				height:130rpx;
			}
			.text{
				text-align: center;
				color:#D8B662;
				font-size:26rpx;
				margin-top: 20rpx;
			}
		}
	}
	svg {
	  width: 3.75em;
	  transform-origin: center;
	  animation: rotate 2s linear infinite;
	}
	
	circle {
	  fill: none;
	  stroke: #D8B662;
	  stroke-width: 2;
	  stroke-dasharray: 1, 200;
	  stroke-dashoffset: 0;
	  stroke-linecap: round;
	  animation: dash 1.5s ease-in-out infinite;
	}
	
	@keyframes rotate {
	  100% {
		transform: rotate(360deg);
	  }
	}
	
	@keyframes dash {
	  0% {
		stroke-dasharray: 1, 200;
		stroke-dashoffset: 0;
	  }
	  50% {
		stroke-dasharray: 90, 200;
		stroke-dashoffset: -35px;
	  }
	  100% {
		stroke-dashoffset: -125px;
	  }
	}
</style>
