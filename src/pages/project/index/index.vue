<template>
  <view>
    <view class="index_body grayscale" :style="{ paddingBottom: 'env(safe-area-inset-bottom)' }">
      <view class="barHeight"></view>
      <fpop ref="fpop"></fpop>
      <!-- #ifdef APP -->
      <view class="video_open" v-if="isFirstOpen">
        <video id="welcome-video" src="https://cdn-lingjing.nftcn.com.cn/bv2_0_video/logo.mp4" autoplay
               controls="false" @ended="onVideoEnded"></video>
      </view>
      <u-popup v-model="isAgreement" :mask-close-able="false" class="popup-box" mode="center" width="590rpx"
               border-radius="0">
        <view class="pop_body">
          <view class="title">
            温馨提示
          </view>
          <view class="content">
            <view class="text">欢迎使用Bigverse！</view>
            <view class="text">Bigverse一贯重视用户个人信息保护，您可以通过阅读
              <text
                  @tap="nav_link('Bigverse平台服务协议', 1)">《Bigverse用户协议》
              </text>
              和
              <text
                  @tap="nav_link('Bigverse法律声明及隐私政策', 2)">《Bigverse隐私协议》
              </text>
              了解我们为您提供的服务，以及我们收集、使用个人信息的范围、方式和目的；请您仔细阅读并充分理解前述协议和以下特别说明，在点击“同意”后开始使用。
            </view>
            <view class="text">1、为给您提供铸造服务，我们可能会申请手机存储权限、相册权限；</view>
            <view class="text">2、为了信息推送和账号安全，我们会申请系统设备权限收集设备信息、日志信息；</view>
            <view class="text">
              3、我们会努力采取各种安全技术保护您的个人信息，未经您同意，我们不会从第三方获取、共享或对外提供您的信息；
            </view>
            <view class="text">4、您还可以访问、更正、删除您的个人信息，我们也将提供注销、投诉方式。</view>
            <view class="text">
              5、我们重视青少年/儿童的个人信息保护，若您是未满18周岁的未成年人，欢迎在平台发布作品，请勿参与购买！
            </view>
          </view>
          <view class="footer">
            <view class="agreement">
              您可以阅读完整版
              <text @tap="nav_link('Bigverse平台服务协议', 1)">《用户协议》</text>
              和
              <text
                  @tap="nav_link('Bigverse法律声明及隐私政策', 2)">《隐私协议》
              </text>
            </view>
            <view class="but">
              <view class="active" @click="submitAgreement()">
                同意并继续
              </view>
              <view class="quit" @click="quitApp()">
                退出
              </view>
            </view>
          </view>
        </view>
      </u-popup>
      <!-- #endif -->
      <view class="coreContent">
        <view class="header">
          <view class="left_img">
            <view class="border" @click="personJump">
              <image :src="`${portrait}?x-oss-process=image/resize,m_lfit,h_100,w_100`"
                     mode=" aspectFill">
              </image>
            </view>
          </view>
          <view class="input" @click="nav_search()">
            <view class="img">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240409/6e9ef6dc56f1a9216a196870e29633a7_88x88.png"
                  mode="widthFix"></image>
            </view>
            <input :disabled="appDisabled" type="text"/>
          </view>
          <view class="right_icon">
            <view class="icon" @click="communicate">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240301/dcfd9961dd6f74b40a0246cc8cdffd8a_70x70.png"
                  mode="widthFix"></image>
            </view>
            <view class="icon" @tap="nav_msg">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240304/e8bb13e0dbfb034ad262d8792a99b295_70x70.png"
                  mode="widthFix"></image>
            </view>
          </view>
        </view>
        <!-- 占位符 -->
        <!-- #ifdef H5 -->
        <view style="height: 70rpx;" v-show="appDowShow"></view>
        <view class="app_donw" v-show="appDowShow">
          <view class="left">
            下载APP应用获得更好体验！
          </view>
          <view class="right">
            <view class="but" @click="app_donw">
              下载APP
            </view>
            <image @click="closeAppDow"
                   src="https://cdn-lingjing.nftcn.com.cn/image/20240428/516465c762bbc9436d03dede6beaa9bb_38x38.png"
                   mode="widthFix"></image>
          </view>
        </view>
        <!-- #endif -->
        <!-- <view class="advertising" v-show="showActive">
          <view class="cart" style="margin-bottom:18rpx;">
            <view class="left_top" @click="nav_mgUs()">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241014/b22f9caf9f21a5e88cd5945ef2c3f847_398x160.gif"
                  mode="widthFix"></image>

            </view>
            <view class="right_top" @click="nav_mall()">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241014/b3fb86a1cad4cb56e7c486ce99ca57aa_275x160.gif"
                  mode="widthFix"></image>
            </view>

          </view>
          <view class="cart">
            <view class="left_bottom" @click="nav()">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241014/feedcd7c54eec10d37c28ee1d8a4915f_256x160.gif"
                  mode="widthFix"></image>

            </view>
            <view class="right_bottom" @click="nav_takeOffIndex()">
              <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20241014/c7bed859b18c3feeb1c3fbb2cca2cb7e_418x160.gif"
                  mode="widthFix"></image>
            </view>
          </view>
          <!-- <view class="cart" >
          <image
            src="https://cdn-lingjing.nftcn.com.cn/image/20240409/eb6c85a7a25da892a75bc9af5d7211c5_322x160.gif"
            mode="widthFix"></image>
            <image
              src="https://cdn-lingjing.nftcn.com.cn/image/20240409/9458830a362635e090ba86ee61033509_322x160.gif"
              mode="widthFix"></image>
        </view> -->
        <!-- </view> -->
        <view style="height:30rpx;" v-show="!showActive"></view>
        <view class="rank" style="margin-top: 30rpx;">
          <view class="rank_head">
            <view class="tabs">
              <u-tabs :list="list" bg-color="var(--main-bg-color)" active-color="var(--default-color1)"
                      :show-bar="false" inactive-color="rgb(255,255,255,0.5)"
                      :current="current" @change="change"></u-tabs>
            </view>
            <view class="help">

              <image v-if="current == 2 && !setShow"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240301/069fb21a209947cfc2920f5d6506db52_28x27.png"
                     mode="widthFix" @click="setShow = true"></image>
              <image src="@/static/imgs/public/set.png" mode="widthFix"
                     v-else-if="current == 2 && setShow" @click="setShow = false">
              </image>
              <image v-if="current == 0 || current == 1" @click="openRankMsg()"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240301/0cdcc7f3a69c7b2dfd0a1d035064d386_28x28.png"
                     mode="widthFix"></image>
            </view>
          </view>
          <view class="optional" v-if="current == 2">
            <!-- 示例list v-if="setShow" -->
            <view class="li_view" v-if="qiFeiInFo">

              <view class="left">
                <view class="longs" v-show="setShow">
                  <!--  v-if="setShow" -->
                  <image src="@/static/imgs/public/longs.png" mode="aspectFill"></image>
                </view>
                <view class="img" @click="nav_active(0)">
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn/image/20240702/6974dde697c5ca5495aae364c5576dd5_400x400.jpg?x-oss-process=image/resize,m_lfit,h_100,w_100"
                      mode="widthFix"></image>
                </view>
                <view class="font_view" @click="nav_active(0)">
                  <view class="title">
                    BV开杠（BV指数）
                  </view>
                  <view class="num_view">
                    <view class="price" v-if="qiFeiInFo">
                      ￥{{ qiFeiInFo.marketPrice }}
                    </view>
                    <view class="range" v-if="qiFeiInFo">
                      {{ (qiFeiInFo.percent * 100).toFixed(2) }}%
                    </view>
                  </view>
                </view>
              </view>
              <view class="right">
                <view class="top1" v-if="!setShow">
                  <view class="duo" @click.stop="nav_active(0)">唱多</view>
                  <view class="kong" @click.stop="nav_active(1)">唱空</view>
                </view>
              </view>
            </view>
            <!-- 不可拖拽list -->
            <view>
              <view class="li_view" v-if="!setShow" v-for="(element, index) in filterSelfSelectionList"
                    :key="element.ctid">
                <view class="left">
                  <view class="img">
                    <image
                        :src="`${element.cover}?x-oss-process=image/resize,m_lfit,h_100,w_100` || 'https://cdn-lingjing.nftcn.com.cn/image/20231102/888bd0b89ae0ee5d2c38e82f7659fb13_400x400.png'"
                        mode="aspectFill">
                    </image>
                  </view>
                  <view class="font_view">
                    <view class="title">
                      {{ element.name }}
                    </view>
                    <view class="num_view">
                      <view class="price">
                        ￥{{ element.floorPrice || 0 }}
                      </view>
                      <view class="range">
                        {{ element.ratio < 0 ? '' : '+' }}{{ element.ratio }}%
                      </view>
                    </view>
                  </view>
                </view>
                <view class="delet" v-if="setShow">
                  <image src="@/static/imgs/public/delet.png" mode="aspectFill">
                  </image>
                </view>
                <view class="right" v-else>
                  <!-- <view class="top1" v-show="index == 0">
                 <view class="duo">唱多</view>
                 <view class="kong">唱空</view>
               </view> -->
                  <view class="but">
                    <view class="buy" @click="nav_series(element)">买入</view>
                    <view class="sale" @click="nav_personal()">卖出</view>
                    <!-- <view class="sale huise" v-show="element.ownerCrrentSerices==0" >卖出</view> -->
                  </view>
                </view>
              </view>
            </view>
            <!-- 可拖拽list -->
            <basic-drag v-model="filterSelfSelectionList" itemKey="ctid" :column="1" itemHeight="60rpx"
                        @end='endMove' v-if="setShow">
              <template #item="{ element, index }">
                <view class="li_view">
                  <view class="left">
                    <view class="longs">
                      <!--  v-if="setShow" -->
                      <image src="@/static/imgs/public/longs.png" mode="aspectFill"></image>
                    </view>
                    <view class="img">
                      <image
                          :src="element.cover || 'https://cdn-lingjing.nftcn.com.cn/image/20231102/888bd0b89ae0ee5d2c38e82f7659fb13_400x400.png'"
                          mode="aspectFill"></image>
                    </view>
                    <view class="font_view">
                      <view class="title">
                        {{ element.name }}
                      </view>
                      <view class="num_view">
                        <view class="price">
                          ￥{{ element.floorPrice || 0 }}
                        </view>
                        <view class="range">
                          {{ element.ratio < 0 ? '' : '+' }}{{ element.ratio }}%
                        </view>
                      </view>
                    </view>
                  </view>
                  <view class="delet" @click="scope(element.ctid, 1)">
                    <!-- v-if="setShow" -->
                    <image src="@/static/imgs/public/delet.png" mode="aspectFill">
                    </image>
                  </view>
                  <!-- <view class="right" v-else>
               <view class="top1" v-show="index == 0">
                 <view class="duo">唱多</view>
                 <view class="kong">唱空</view>
               </view>
               <view class="but" v-show="index !== 0">
                 <view class="buy">买入</view>
                 <view class="sale">卖出</view>
               </view>
             </view> -->
                </view>
              </template>
            </basic-drag>
            <view class="down" @tap="checkShowDow(0)" v-show="filterSelfSelectionList.length > 2">
              <image v-show="isDownShow1"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1af04d46580dfcd5fa0fd33787663f96_88x72.png"
                     mode="widthFix"></image>
              <image v-show="!isDownShow1"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240301/97d0d1f2163359a196526ed555ababc9_44x36.png"
                     mode="widthFix"></image>
            </view>
            <view class="cbTit" v-show="filterSelfSelectionList == ''">
              <view>
                <image src="@/static/imgs/public/cbLef.png" mode="widthFix"></image>
              </view>
              <view>
                <view>浏览作品时点击右上角“加入自选”</view>
                <view>您就可以在这里看到心仪的藏品了</view>
              </view>
              <view>
                <image src="@/static/imgs/public/cbRig.png" mode="widthFix"></image>
              </view>
            </view>
          </view>
          <view class="hot" v-if="current == 1">
            <view class="li" v-for="(item, index) in filterHotList" v-show="filterHotList"
                  @click="nav_series(item)">
              <view class="img_font">
                <image :src="`${item.cover}?x-oss-process=image/resize,m_lfit,h_100,w_100`"
                       mode="aspectFill"></image>
                <view class="title oneOver">
                  {{ item.name }}
                </view>
              </view>
              <view class="price">
                ￥{{ item.price }}
              </view>
              <view class="range">
                换手
                <text>{{ item.count }}</text>
                次
              </view>
            </view>
            <view class="null_body" v-if="filterHotList == ''">
              <view class="null">
                <view class="img">
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                      mode="widthFix"></image>
                </view>
              </view>
            </view>
            <view class="down" @tap="checkShowDow(1)" v-show="filterHotList.length > 2">
              <image v-show="isDownShow2"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1af04d46580dfcd5fa0fd33787663f96_88x72.png"
                     mode="widthFix"></image>
              <image v-show="!isDownShow2"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240301/97d0d1f2163359a196526ed555ababc9_44x36.png"
                     mode="widthFix"></image>
            </view>

          </view>
          <view class="rise" v-if="current == 0">
            <view class="li" v-for="(item, index) in filterIncreaseList" v-show="filterIncreaseList"
                  @click="nav_series(item)">
              <view class="img_font">
                <image :src="`${item.cover}?x-oss-process=image/resize,m_lfit,h_100,w_100`"
                       mode="aspectFill"></image>
                <view class="title oneOver">
                  {{ item.name }}
                </view>
              </view>
              <view class="price">
                ￥{{ item.floorPrice }}
              </view>
              <view class="range" :class="{ 'lvse': item.ratio < 0 }" style="padding:0rpx 22rpx;">
                {{ item.ratio >= 0 ? `+${item.ratio}` : `${item.ratio}` }}%
                <image v-if="item.ratio > 0 || item.ratio == 0"
                       src="https://cdn-lingjing.nftcn.com.cn/image/20240301/cfac456e1ed31297cba5971cc5ac01e2_22x22.png"
                       mode="widthFix"></image>
                <image v-else
                       src="https://cdn-lingjing.nftcn.com.cn/image/20240507/81a2dad3f6a56cfd5163523456d040f4_22x22.png"
                       mode="widthFix"></image>
              </view>
            </view>
            <view class="null_body" v-if="filterIncreaseList == ''">
              <view class="null">
                <view class="img">
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                      mode="widthFix"></image>
                </view>
              </view>
            </view>
            <view class="down" @tap="checkShowDow(2)" v-show="filterIncreaseList.length > 2">
              <image v-show="isDownShow"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1af04d46580dfcd5fa0fd33787663f96_88x72.png"
                     mode="widthFix"></image>
              <image v-show="!isDownShow"
                     src="https://cdn-lingjing.nftcn.com.cn/image/20240301/97d0d1f2163359a196526ed555ababc9_44x36.png"
                     mode="widthFix"></image>
            </view>
          </view>
          <view class="fight_view" v-if="current == 3">
            <view class="left">
              <view class="price">
                <text>价格:</text>
                <view class="input">
                  <input type="digit" @focus="handleFocus" @input="onInputChange"
                         v-model="importMarketPrice" placeholder="填写价格"/>
                </view>
                <view class="button" @click="clickGetPrice">
                  市价
                </view>
              </view>
              <view class="num_lever">
                <text>份数:</text>
                <view class="input" style="width: 180rpx;margin-right:30rpx;" @click="openCheck(1)">
                  <text>{{ importNum }}份</text>
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn//image/20240613/4fadee4582cf674d21864081097e8621_200x200.png"
                      mode="widthFix"></image>
                  <view class="model_open_num" v-if="numShow">
                    <view class="li" v-for="(item, index) in [10, 50, 100, 200, 500]"
                          @click.stop="checkValue(item, 1)">
                      {{ item }}份
                    </view>
                  </view>
                </view>
                <text>杠杆:</text>
                <view class="input" @click="openCheck(2)">
                  <text>x{{ importLever }}</text>
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn//image/20240613/4fadee4582cf674d21864081097e8621_200x200.png"
                      mode="widthFix"></image>
                  <view class="model_open" v-if="leverShow">
                    <view class="li" v-for="(item, index) in [1, 2, 5, 10]"
                          @click.stop="checkValue(item, 2)">
                      x{{ item }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view class="footer_button_view">
              <view class="but active" @click="nav_active(0)">
                我唱多
              </view>
              <view class="but" @click="nav_active(1)">
                我唱空
              </view>
            </view>
          </view>
        </view>
        <view class="consult_view">
          <view class="consult_head">
            <view class="tabs">
              <u-tabs :list="consult_list" bg-color="var(--main-bg-color)"
                      active-color="var(--default-color1)" :show-bar="false"
                      inactive-color="rgb(255,255,255,0.5)" :current="consult_current"
                      @change="consult_change"></u-tabs>
            </view>
            <view class="help" @click="more">
              更多
              <image src="@/static/imgs/public/right.png" mode="widthFix"></image>
            </view>
          </view>
          <!-- 活动 -->
          <view class="actives" v-if="consult_current == 0">
            <view class="li" v-for="(item, index) in activesList" :key="index" @click="actives">
              <view class="left_img">
                <image :src="item.activityNewBannerImage" mode="heightFix"></image>
              </view>
              <view class="font_view">
                <view class="title">
                  {{ item.title }}
                </view>
                <view class="msg">
                  开始时间：{{ item.startTime }}
                </view>
              </view>
              <view class="right" v-if="item.onlineStatus == 1">
                <image src="@/static/imgs/public/right.png" mode="widthFix"></image>
              </view>
              <view class="right_icon">
                <view class="status1" v-if="item.onlineStatus == 0">
                  即将开始
                </view>
                <view class="status2" v-if="item.onlineStatus == 1">
                  正在进行
                </view>
                <view class="status3" v-if="item.onlineStatus == 2">
                  已结束
                </view>
              </view>
            </view>
            <view class="null_body" v-if="activesList.length == 0">
              <view class="null">
                <view class="img">
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                      mode="widthFix"></image>
                </view>
              </view>
            </view>
          </view>
          <!-- 名人堂 -->
          <view class="celebrity" v-if="consult_current == 1">
            <!-- 富豪榜 -->
            <view class="plutocratBox">
              <view class="tit">
                <image src="@/static/imgs/public/haoBang.png" mode="widthFix"></image>
              </view>
              <swiper :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
                      circular class="swiper" :current="swiperCurrent" @click="plutocrat"
                      v-if="plutocratList.length !== 0">
                <swiper-item class="siwperItem" v-for="(items, index) in 3" :key="items">
                  <view class="list"
                        v-for="item in plutocratList.slice((index * 5), (index + 1) * 5)"
                        :key="item.index">
                    <view>
                      <view>
                        <image :src="item.avatar" mode="aspectFill"></image>
                      </view>
                      <view :class="[item.index <= 5 ? 'topHead' : 'topFoot']">TOP{{
                          item.index
                        }}
                      </view>
                    </view>
                    <view>
                      <view>{{ item.userMark }}</view>
                      <view>￥{{ item.score.toLocaleString('en-US') }}</view>
                    </view>
                  </view>
                </swiper-item>
              </swiper>
              <view class="null_body_view" v-else>
                <view class="null_body">
                  <view class="null">
                    <view class="img">
                      <image
                          src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                          mode="widthFix"></image>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 专业买手榜 -->
            <view class="plutocratBox">
              <view class="tit">
                <image src="@/static/imgs/public/buyBang.png" mode="widthFix"></image>
              </view>
              <swiper :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
                      circular class="swiper" :current="swiperCurrent" @click="plutocrat"
                      v-if="incomeList.length !== 0">
                <swiper-item class="siwperItem" v-for="(items, index) in 3" :key="items">
                  <view class="list"
                        v-for="item in incomeList.slice((index * 5), (index + 1) * 5)"
                        :key="item.index">
                    <view>
                      <view>
                        <image :src="item.avatar" mode="aspectFill"></image>
                      </view>
                      <view :class="[item.index <= 5 ? 'topHead' : 'topFoot']">TOP{{
                          item.index
                        }}
                      </view>
                    </view>
                    <view>
                      <view>{{ item.userMark }}</view>
                      <view>￥{{ item.score.toLocaleString('en-US') }}</view>
                    </view>
                  </view>
                </swiper-item>
              </swiper>
              <view class="null_body_view" v-else>
                <view class="null_body">
                  <view class="null">
                    <view class="img">
                      <image
                          src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                          mode="widthFix"></image>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 资讯 -->
          <view class="advisory" v-if="consult_current == 2">
            <view class="li" v-for="(item, index) in imformentList" :key="item.id" @click="imforment"
                  v-if="imformentList.length != 0">
              <view class="left_img">
                <image :src="item.iconUrl" mode="widthFix"></image>
              </view>
              <view class="font_view">
                <view class="title">
                  {{ item.title }}
                </view>
                <view class="msg">
                  开始时间：{{ item.showTime }}
                </view>
              </view>
              <view class="right">
                <image src="@/static/imgs/public/right.png" mode="widthFix"></image>
              </view>
            </view>
            <view class="null_body" v-if="imformentList.length == 0">
              <view class="null">
                <view class="img">
                  <image
                      src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                      mode="widthFix"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="banner">
          <view class="banner_view">
            <u-swiper :border-radius="24" :height="280" :list="swiper_list"
                      @click="bannerClick"></u-swiper>
          </view>
          <view class="backgroundCart">

          </view>
        </view>
      </view>
      <introducePop :title="msgTitle" :introduce="msgIntroduce" :showTitle="false" :show.sync="isMsg">
      </introducePop>
      <view class="mask" v-show="isMaskShow" @click="closeMask"></view>
      <popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false"
                 @confirm="nav_realName()">
      </popup-bar>
    </view>
    <TabBar ref="TabBar" :initialActiveIndex="0"></TabBar>
  </view>
</template>

<script>
import TabBar from '@/components/public/TabBar.vue'
import basicDrag from '@/components/basic-drag/index.vue'
import fpop from '@/components/force-updates/force-updates.vue'
import introducePop from '@/components/public/introducePop.vue'
import popupBar from "@/components/public/PopupBar";

import {
  base64
} from 'js-md5';
// import Umeng from 'umeng/lib/umeng_push';
// 获取 module
// #ifdef H5
import {
  nav_contactService
} from '@/utils/utils'
// #endif
// #ifdef APP
var qiyuModule = uni.requireNativePlugin("Netease-QiyuModule")
// #endif
export default {
  components: {
    TabBar,
    basicDrag,
    fpop,
    introducePop,
    popupBar
  },
  computed: {
    filterIncreaseList() { //涨幅
      if (this.increaseList) {
        return this.increaseList.slice(0, this.currentlyDisplayed);
      }
    },
    filterHotList() { //热门
      if (this.hotList) {
        return this.hotList.slice(0, this.currentlyDisplayed2);
      }
    },
    filterSelfSelectionList() { //自选
      if (this.selfSelectionList) {
        return this.selfSelectionList.slice(0, this.currentlyDisplayed1);
      }
    },
  },
  data() {
    return {
      isRegistration: false,
      list: [{
        name: '涨幅',
      }, {
        name: '热门'
      }, {
        name: '自选'
      }],
      current: 0,
      consult_list: [{
        name: '活动'
      }, {
        name: '名人堂'
      }, {
        name: '资讯',
      }],
      consult_current: 0,
      swiper_list: [],
      hotList: [], //热门
      increaseList: [], //涨幅
      selfSelectionList: [], //自选帮
      activesList: [], //活动列表
      imformentList: [], //资讯列表
      plutocratList: [], //富豪榜
      incomeList: [], //买手榜
      isDownShow: false, //涨幅 open/close
      isDownShow1: false, //自选 open/close
      isDownShow2: false, //热门 open/close
      currentlyDisplayed: 3, // 涨幅 当前显示的数据条数
      currentlyDisplayed1: 3, //自选
      currentlyDisplayed2: 3, //热门
      swiperCurrent: 0,
      setShow: false, //自选  设置
      portrait: "https://cdn-lingjing.nftcn.com.cn/image/20240310/a902bce56e63d929629524dea93e23af_120x120.png",
      qiFeiInFo: "",
      isMsg: false,
      msgTitle: "",
      msgIntroduce: "",
      autoPlayOnLoad: true, // 标记是否首次加载时自动播放
      coverImg: "",
      isFirstOpen: false,
      isLogin: false,
      appDowShow: true,
      isAgreement: false,
      isAgreement2: true,

      appDisabled: false,
      isMaskShow: false,//遮罩
      numShow: false,//份数弹窗
      leverShow: false,//杠杆弹窗

			marketPrice: "",//市价
			importMarketPrice: "",//输入的市价
			importNum: "10",//份数
			importLever: "1",//杠杆
			isUserFocus: false,
			timeout: "",
			showActive: false
		}
	},
	onShow() {
		// this.getAppCover()//获取广告封面
		// this.communicate()
		if (this.current == 3) {
			this.getPrice() //获取市价
		}
	},
	onLoad(e) {
		console.log(e);
		if (e.newname) {
			this.isRegistration = true
		}
		this.firstVisit()
		uni.hideTabBar();
		// this.getHotBroadcast() //热门
		this.getIncreaseList() //涨幅
		this.getActives() //活动列表
		// this.getImforment() //资讯
		// this.getPlutocrat() //富豪榜
		// this.getIncomeList() //买手榜
		this.getBanners()
		this.getUserInfo()
		this.getSelfSelectionList() //自选
		// // #ifdef H5
		// let currentTabbar = uni.getStorageSync('currentTabbar')
		// if (currentTabbar == 'pay') {
		// 	this.$Router.pushTab({
		// 		name: 'indexYs'
		// 	})
		// 	return
		// }
		// // #endif
		this.get_tab()//载入配置
		this.get_tab_pay()//提前载入衍生配置
		console.log(uni.getStorageSync('isFirstOpen'))
		if (uni.getStorageSync('isFirstOpen')) {
			if (uni.getSystemInfoSync().platform == 'ios') {
				if (uni.getStorageSync('isAgreement')) {

        } else {
          this.isAgreement = true
        }
      }
    } else {
      this.isFirstOpen = true
      uni.setStorageSync('isFirstOpen', true)
      // #ifdef APP
      this.zhendong()
      // #endif
    }
    // #ifdef APP
    this.appDisabled = true
    if (uni.getSystemInfoSync().platform == 'ios') {
      this.get_version()
    } else {
      this.showActive = true
    }
    // #endif
    // #ifdef H5
    this.showActive = true
    // #endif

  },
  onHide() {
    //销毁定时
    if (this.timeout) {
      console.log("销毁定时器")
      clearTimeout(this.timeout)
      this.timeout = null
    }
  },
  onPullDownRefresh() {
    setTimeout(() => {
      this.getSelfSelectionList() //自选
      this.getActives() //活动列表
      this.getBanners()
      this.getUserInfo()
      // #ifdef APP
      if (uni.getSystemInfoSync().platform == 'ios') {
        this.get_version()
      }
      // #endif
      uni.stopPullDownRefresh(); //停止下拉刷新动画

    }, 1000);
  },
  onPageScroll(e) {

  },
  onReachBottom() {

  },
  watch: {},
  methods: {
    async firstVisit() {
      let res = await this.$api.dayFirstVisit({
        module: "HOME_PAGE",
        from: 'h5'
      });

    },
    nav_realName() {
      this.isRegistration = false;
      console.log(uni.getStorageSync("authStatus"));
      if (uni.getStorageSync("authStatus") == 30) {
        this.$Router.push({
          name: "authentication",
        });
      } else {
        this.$Router.push({
          name: "realName",
        });
      }
    },
    // testInitSdk() {

    // },
    // #ifdef H5
    nav_contactService,
    // #endif
    communicate() {
      if (this.isLogin) {
        // #ifdef H5
        this.nav_contactService()
        // #endif
        // #ifdef APP
        this.testOpenService()
        // #endif
      } else {
        this.$Router.push({
          name: 'mainLogin'
        })
      }
      console.log('沟通')

      // 调用同步方法

    },
    //七鱼客服设置信息
    qiyu_setInfo(info) {
      console.log("触发了")
      qiyuModule.initSdk({
        appKey: "3d1f1a45bc64cc1aab8aca825ff89c13",
        appName: "Bigverse",
        pkCerName: "PushKit推送证书名；对应云信的pkCername"
      })
      const {
        contractAddress,
        userId,
        name,
        email,
        phone,
      } = info
      const data = [{
        label: 'contractAddress',
        key: 'contractAddress',
        value: contractAddress
      }]
      console.log(JSON.stringify(data))
      qiyuModule.setUserInfo({
        userId,
        data: JSON.stringify(data),
      }, (ret) => {
        if (ret.code == 200) {
          console.log("七鱼设置信息成功")
          //成功
        } else {
          console.log("失败")
          console.log(ret)
          //失败
        }
      })
    },
    testOpenService() {
      qiyuModule.openServiceActivity({
        title: 'NFTCN客服',
        source: {
          title: 'NFTCN客服',
          // vipLevel: 1, // 设置用户VIP等级
          // robotId: 2222, //分配机器人
          // staffId: 3444, //分配客服
          // groupId: 345, //客服组id
          // groupTmpId: 455, //分流客服组id
          // robotFirst: false, //是否客服组优先
          //访客头像
          ios_sendProduct: false,
        },
        //iOS打开界面的方式，push/present,默认push
        openMode: 'push',

      });
    },
    async getIncomeList() { //买手榜
      let {
        result,
        status
      } = await this.$api.incomeList({
        pageNum: 1,
        pageSize: 15,
      })
      if (status.code == 0) {
        this.incomeList = result.list
      }
      // console.log(result, '买手榜');
    },
    async getActives() { //活动列表
      console.log("请求了活动")
      let {
        status,
        result
      } = await this.$api.java_activityNewActivityPageList({
        pageNum: 1,
        pageSize: 3
      });
      if (status.code == 0) {
        this.activesList = result.list
      }
    },
    async getImforment() { //资讯
      let {
        status,
        result
      } = await this.$api.java_moreInquiriesList({
        pageNum: 1,
        pageSize: 3
      })
      if (status.code == 0) {
        this.imformentList = result.list
      }
    },
    async getPlutocrat() { //富豪榜列表
      let {
        status,
        result,
      } = await this.$api.java_rankingList({
        pageNum: 1,
        pageSize: 15,
      })
      if (status.code == 0) {
        this.plutocratList = result.list
      }
    },

    more() { //更多
      if (this.consult_current == 0) {
        this.actives()
      } else if (this.consult_current == 1) {
        this.plutocrat()
      } else if (this.consult_current == 2) {
        this.imforment()
      }
    },
    actives() { //活动
      this.$Router.push({
        name: 'actives'
      })
    },
    nav() { //边玩边赚
      if (this.isLogin) {
        this.$Router.push({
          name: 'player'
        })
      } else {
        this.$Router.push({
          name: 'mainLogin'
        })
      }

    },
    imforment() { //资讯
      this.$Router.push({
        name: 'information'
      })
    },
    personJump() { //个人信息
      uni.navigateTo({
        url: '/pagesA/project/person/person'
      })
    },
    plutocrat() { //名人堂
      // #ifdef H5
      this.$Router.push({
        name: 'plutocrat'
      })
      // #endif
      // #ifdef APP
      let link = `${getApp().globalData.url}pagesA/project/plutocrat/plutocrat`
      // #ifdef APP
      this.$Router.push({
        name: "webView",
        params: {
          url: link,
        }
      })
      // #endif
      // #endif
    },
    change(index) {
      this.current = index;
      console.log(index)
      if (index != 3) {
        if (this.timeout) {
          clearTimeout(this.timeout)
        }
      }
      if (index == 0) {
        this.getIncreaseList() //涨幅
      } else if (index == 1) {
        this.getHotBroadcast() //热门
      } else if (index == 2) {
        this.getSelfSelectionList() //自选
      } else if (index == 3) {
        this.getPrice() //获取市价
      }
    },
    consult_change(index) {
      // this.getImforment() //资讯

      this.consult_current = index;
      if (index == 0) {
        this.getActives() //活动列表
      } else if (index == 1) {
        this.getPlutocrat() //富豪榜
        this.getIncomeList() //买手榜
      } else if (index == 2) {
        this.getImforment()
      }
    },

    swiper_change(index) {

    },
    //热门
    async getHotBroadcast() {
      let res = await this.$api.java_hotBroadcast({
        pageSize: 10,
      });
      if (res.status.code == 0) {
        this.hotList = res.result.list
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    //涨幅
    async getIncreaseList() {
      let res = await this.$api.java_increaseList({
        pageSize: 10,
      });
      if (res.status.code == 0) {
        this.increaseList = res.result.list
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    //自选
    async getSelfSelectionList() {
      let res = await this.$api.java_selfSelectionList({
        pageNum: 1,
        pageSize: 30,
      });
      if (res.status.code == 0) {
        this.selfSelectionList = res.result.pageData ? res.result.pageData.list : []
        this.qiFeiInFo = res.result.qiFeiInFo
        // if (this.qiFeiInFo) {
        // 	this.list = [{
        // 		name: '涨幅',
        // 	}, {
        // 		name: '热门'
        // 	}, {
        // 		name: '自选'
        // 	}
        // 	// , {
        // 	// 	name: "开杠",
        // 	// 	isFire:true
        // 	// }
        // 	]
        // }
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    checkShowDow(index) {
      if (index == 2) { //涨幅
        this.isDownShow = !this.isDownShow
        if (this.isDownShow) {
          this.currentlyDisplayed = this.increaseList.length;
        } else {
          this.currentlyDisplayed = 3
        }
      } else if (index == 1) { //热门
        this.isDownShow2 = !this.isDownShow2
        if (this.isDownShow2) {
          this.currentlyDisplayed2 = this.hotList.length;
        } else {
          this.currentlyDisplayed2 = 3
        }
      } else if (index == 0) { //自选
        this.isDownShow1 = !this.isDownShow1
        if (this.isDownShow1) {
          this.currentlyDisplayed1 = this.selfSelectionList.length;
        } else {
          this.currentlyDisplayed1 = 3
        }
      }
    },
    endMove(obj) {
      // index: 比 原本该位置的list的order低一位
      // oldIndex: 目标位置
      // activeIndex: 本来位置
      // endList: 移动后的list列表
      // let index = null

      // if (obj.activeIndex - obj.oldIndex > 0) {
      //	// 从下往上  ctid 当前位置 order 目标位置
      let order = this.filterSelfSelectionList[obj.oldIndex].ctid
      this.scope(obj.endList[obj.oldIndex].ctid, 2, order)
      // 	console.log(obj.endList[obj.oldIndex].name, '当前');
      // 	console.log(this.filterSelfSelectionList[obj.oldIndex].name, '目标');
      // } else {
      // 	// 从上往下  ctid 目标位置   order 当前位置
      // 	let order = this.filterSelfSelectionList[obj.oldIndex].ctid
      // 	this.scope(order, 2, obj.endList[obj.oldIndex].ctid)
      // 	console.log(obj.endList[obj.oldIndex].name, '当前');
      // 	console.log(this.filterSelfSelectionList[obj.oldIndex].name, '目标');
      // }
    },
    // async scope(ctid, type, order = '') {
    //   // type 操作类型 0—添加 1-删除 2-调整order
    //   //order 移动后的位置在列表中排第几位
    //   let {
    //     result,
    //     status
    //   } = await this.$api.java_operationSelfSelection({
    //     ctid: ctid,
    //     order: order,
    //     type: type,
    //     uid: ''
    //   })
    //   if (status.code == 0) {
    //     if (type == 1) {
    //       uni.showToast({
    //         title: '移除成功',
    //         icon: 'none'
    //       })
    //     }
    //     this.getSelfSelectionList()
    //   }
    //
		// 	// if (obj.activeIndex - obj.oldIndex > 0) {
		// 	//	// 从下往上  ctid 当前位置 order 目标位置
		// 	let order = this.filterSelfSelectionList[obj.oldIndex].ctid
		// 	this.scope(obj.endList[obj.oldIndex].ctid, 2, order)
		// 	// 	console.log(obj.endList[obj.oldIndex].name, '当前');
		// 	// 	console.log(this.filterSelfSelectionList[obj.oldIndex].name, '目标');
		// 	// } else {
		// 	// 	// 从上往下  ctid 目标位置   order 当前位置
		// 	// 	let order = this.filterSelfSelectionList[obj.oldIndex].ctid
		// 	// 	this.scope(order, 2, obj.endList[obj.oldIndex].ctid)
		// 	// 	console.log(obj.endList[obj.oldIndex].name, '当前');
		// 	// 	console.log(this.filterSelfSelectionList[obj.oldIndex].name, '目标');
		// 	// }
		// },
		async scope(ctid, type, order = '') {
			// type 操作类型 0—添加 1-删除 2-调整order
			//order 移动后的位置在列表中排第几位
			let {
				result,
				status
			} = await this.$api.java_operationSelfSelection({
				ctid: ctid,
				order: order,
				type: type,
				uid: ''
			})
			if (status.code == 0) {
				if (type == 1) {
					uni.showToast({
						title: '移除成功',
						icon: 'none'
					})
				}
				this.getSelfSelectionList()
			}

		},
		async getBanners() {
			let res = await this.$api.java_pgc_tab1({

			});
			if (res.status.code == 0) {
				// console.log(res.result.banners)
				this.swiper_list = res.result.banners.map((item) => {
					return {
						...item,
						image: item.image.src,
					};
				});
				// console.log(this.swiper_list)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		bannerClick(index) {
			const {
				needLogin,
				linkType,
				link
			} = this.swiper_list[index]
			console.log(this.swiper_list[index].link)
			if (linkType == 0) {
				return false
			} else {
				if (needLogin == 1) {
          if (uni.getStorageSync('token')) {
						// #ifdef APP
						this.$Router.push({
							name: "webView",
							params: {
								url: link,
							}
						})
						// #endif
						// #ifdef H5
						window.location.href = link
						// #endif
					} else {
						this.$Router.push({
							name: "mainLogin"
						})
					}
				} else {
					// #ifdef APP
					this.$Router.push({
						name: "webView",
						params: {
							url: link,
						}
					})
					// #endif
					// #ifdef H5
					window.location.href = link
					// #endif
				}
			}
		},
		async getUserInfo() {
			let res = await this.$api.userInfo({});
			if (res.status.code == 0) {
				this.isLogin = true
				this.portrait = res.result.avatar
				let data = res.result
				uni.setStorageSync("uid", data.userId)
				uni.setStorageSync("contract_address", data.contractAddress)
				uni.setStorageSync("nickname", data.name)
				uni.setStorageSync("avatar", data.avatar)
				let certification;
				if (res.result.authStatus == 31 && res.result.authType == 1) {
					certification = 1
				} else if (res.result.authStatus == 31 && res.result.authType == 2) {
					certification = 2
				} else {
					// uni.showToast({
					// 	title: res.status.msg,
					// 	icon: 'none',
					// 	duration: 3000
					// });
				}
				uni.setStorageSync("certification", certification)
				// #ifdef APP
				this.qiyu_setInfo(data)
				// #endif
			} else {
				this.isLogin = false
			}
		},
		nav_msg() {
			this.$Router.push({
				name: "message"
			})
		},
		nav_mall() {
			this.$Router.pushTab({
				name: "mall"
			})
		},
		nav_takeOffIndex() {
			this.$refs.TabBar.bv_platform = true
			this.$refs.TabBar.checkPlatform()
			this.$Router.pushTab({
				name: "contract-BITindex"
			})
		},
		nav_mgUs() {
			this.$Router.push({
				name: "mgUs"
			})
		},
		nav_active(isActive) {
			let importMarketPrice, importNum, importLever;
			importMarketPrice = this.importMarketPrice
			importNum = this.importNum
			importLever = this.importLever
			this.$Router.pushTab({
				name: "takeOffIndex",
				params: {
					importMarketPrice,
					importNum,
					importLever,
					isType: isActive
				}
			})
			// let importMarketPrice,importNum,importLever;
			// importMarketPrice = this.importMarketPrice
			// importNum = this.importNum
			// importLever = this.importLever
			// let link = `${getApp().globalData.activeUrl}#/takeOff?activityNo=A70038988068069377&isType=${isActive}&importMarketPriceQuery=${importMarketPrice}&importNumQuery=${importNum}&importLeverQuery=${importLever}`
			// console.log(link)
			// // #ifdef APP
			// this.$Router.pushTab({
			// 	name: "takeOffIndex",
			// })
			// // #endif

      // // #ifdef H5
      // window.location.href = link
      // // #endif

    },
    nav_series(item) {
      console.log(item)
      if (item.ctid) {
        this.$Router.push({
          name: 'seriesList',
          params: {
            ctid: item.ctid
          }
        })
      }
    },
    nav_search() {
      this.$Router.push({
        name: 'search'
      })
    },
    nav_personal() {
      this.$Router.pushTab({
        name: "personal",
      })
    },
    openRankMsg() {
      this.isMsg = true
      if (this.current == 1) {
        this.msgIntroduce = '每5分钟更新一次汇总近24小时换手次数'
      } else {
        this.msgIntroduce = '每5分钟更新一次相比24小时前地板价'
      }
    },
    onVideoEnded() {
      this.isFirstOpen = false
      if (uni.getSystemInfoSync().platform == 'ios') {
        if (uni.getStorageSync('isAgreement')) {

        } else {
          this.isAgreement = true
        }
      }
    },
    app_donw() {
      this.$Router.push({
        name: "appDownload"
      })
    },
    closeAppDow() {
      this.appDowShow = false
    },
    nav_link(title, index) {
      if (index === 1) {
        this.$Router.push({
          name: "generalAgreement",
          params: {
            title: title,
            link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
          }
        })
      } else {
        this.$Router.push({
          name: "generalAgreement",
          params: {
            title: title,
            link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
          }
        })
      }
    },
    submitAgreement() {
      this.isAgreement = false
      uni.setStorageSync('isAgreement', true)
    },
    quitApp() {
      // #ifdef APP
      plus.os.name == "Android" ? plus.runtime.quit() : plus.ios.import("UIApplication").sharedApplication()
          .performSelector("exit");
      uni.removeStorageSync('isAgreement')
      // #endif
    },
    openCheck(type) {
      //type  1.份数  2.杠杆
      if (type == 1) {
        this.numShow = true
        this.isMaskShow = true
      } else {
        this.leverShow = true
        this.isMaskShow = true
      }
      console.log(this.leverShow)
    },
    checkValue(item, type) {
      if (type == 1) {
        this.importNum = item
      } else {
        this.importLever = item
      }
      this.closeMask()
    },
    closeMask() {
      this.isMaskShow = false
      this.leverShow = false
      this.numShow = false
      this.$forceUpdate()
    },
    //获取市价
    async getPrice() {
      let res = await this.$api.getMarketPrice({});
      if (res.status.code == 0) {
        this.marketPrice = res.result.marketPrice
        if (!this.isUserFocus) {
          this.importMarketPrice = this.marketPrice
        }
        this.timeout = setTimeout(() => {
          this.getPrice()
        }, 3000)
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    // 监听input获取焦点事件
    handleFocus() {
      this.isUserFocus = true
    },
    clickGetPrice() {
      this.importMarketPrice = this.marketPrice
      this.isUserFocus = false
    },
    onInputChange(event) {
      event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,2})/g)[0]) || ""
      this.$nextTick(() => {
        this.importMarketPrice = event.detail.value
      })
    },
    zhendong() {
      // 4.45秒震动，持续时间0.5秒
      // 5.50秒震动，持续时间0.1秒
      // 6秒震动，持续时间0.1秒
      // 6.16秒震动，持续时间0.1秒
      // 6.31秒震动，持续时间0.2秒
      // 6.52秒震动，持续时间0.1秒
      // 7.46秒震动，持续时间0.3秒
      setTimeout(() => {
        uni.vibrateLong()
      }, 4450);
      setTimeout(() => {
        uni.vibrateShort()
      }, 5500);
      setTimeout(() => {
        uni.vibrateShort()
      }, 6000);
      setTimeout(() => {
        uni.vibrateLong()
      }, 6160);
      setTimeout(() => {
        uni.vibrateLong()
      }, 6310);
      setTimeout(() => {
        uni.vibrateLong()
      }, 6520);
      setTimeout(() => {
        uni.vibrateLong()
      }, 7460);
      setTimeout(() => {
        uni.vibrateLong()
      }, 8460);
      setTimeout(() => {
        uni.vibrateLong()
      }, 9460);
    },
    async get_version() {
      let res = await this.$api.java_commonconfigInfo({
        name: 'ios_apple_pay_version',
      });
      if (res.status.code == 0) {
        let curV = uni.getSystemInfoSync().appVersion
        let reqV = res.result.value
        console.log(curV)
        console.log(reqV)
        if (curV == reqV) {
          if (uni.getSystemInfoSync().platform == 'ios') {
            this.showActive = false
          } else {
            this.showActive = true
          }
        } else {
          this.showActive = true
        }
        console.log(this.showActive)
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
		async get_tab() {
			console.log('获取配置bv_tabbar')

			let res = await this.$api.java_commonconfigInfo({
				name: 'bv_tabbar'
			});
			if (res.status.code == 0) {
				let val = JSON.parse(res.result.value)
				uni.setStorageSync('bv_tabbarList', val)
				uni.setStorageSync('currentTabbar', 'bv')
				this.$refs.TabBar.get_config()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_tab_pay() {
		  console.log('获取配置pay_tabbar')
		  let res = await this.$api.java_commonconfigInfo({
		    name: 'pay_tabbar_new'
		  });
		  if (res.status.code == 0) {
		    let val = JSON.parse(res.result.value)
		    uni.setStorageSync('pay_tabbarList', val)
		  } else {
		    uni.showToast({
		      title: res.status.msg,
		      icon: 'none',
		      duration: 3000
		    });
		  }
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .u-tab-item {
  padding: 0 25rpx 0 24rpx !important;
}

.page {
  background-color: #000;
  overflow: hidden;
  /* 防止背景色溢出 */
}

.popup-box::v-deep {
  .u-drawer__scroll-view {
    background-color: #2B2B2B;
  }
}

.pop_body {
  padding: 40rpx;

  .title {
    color: #fff;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .content {
    min-height: 450rpx;
    color: #616161;
    font-size: 24rpx;
    overflow: auto;

    .text {
      line-height: 40rpx;

      text {
        color: #1FEDF0;
      }
    }
  }

  .footer {
    .agreement {
      font-size: 24rpx;
      color: #fff;
      text-align: center;
      padding: 24rpx 0rpx;

      text {
        color: #1FEDF0;
      }
    }

    .but {
      .active {
        width: 100%;
        background: linear-gradient(132deg, #F6AAF2 0%, #8CC9F3 51%, #00FBEF);
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        color: #121212;
        font-size: 28rpx;
        font-weight: 600;
      }

      .quit {
        color: #616161;
        font-size: 24rpx;
        text-align: center;
        margin-top: 30rpx;
      }
    }
  }
}

.index_body {
  padding: 36rpx;
}

.coreContent {
  padding-bottom: 170rpx;
}

// ::v-deep .u-tab-item {
// 	background-color: var(--main-bg-color);
// }
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left_img {
    border-radius: 34rpx;
    width: 96rpx;
    height: 96rpx;
    /* background: var(--primary-button-color); */
    /* padding: 4rpx; */
    display: flex;
    justify-content: center;
    align-items: center;

    background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
    overflow: hidden;

    .border {
      overflow: hidden;

      /* width: 88rpx;
            height: 88rpx; */
      width: 94rpx;
      height: 94rpx;
      background-color: var(--main-bg-color);
      /* border-radius: 36rpx; */
      border-radius: 34rpx;


      display: flex;
      justify-content: center;
      align-items: center;
      /* padding: 4rpx 6rpx 5rpx 5rpx; */

      image {
        width: 80rpx;
        height: 80rpx;
        border-radius: var(--medium-font-size);
      }
    }

  }

  .input {
    border: 1px solid rgba(255, 255, 255, 0.5);
    width: 378rpx;
    height: 70rpx;
    border-radius: var(--medium-font-size);
    position: relative;

    .img {
      position: absolute;
      left: 10rpx;
      top: 8rpx;

      image {
        width: 50rpx;
      }
    }
  }

  .right_icon {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .icon {
      width: 70rpx;
      height: 70rpx;
      margin-left: 10rpx;

      image {
        width: 70rpx;
        height: 70rpx;
      }
    }
  }
}

.app_donw {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70rpx;
  background: rgba(20, 20, 20, 0.3);
  padding: 18rpx 40rpx;
  position: absolute;
  top: 150rpx;
  left: 0rpx;
  color: #fff;
  width: 100%;

  .left {
    font-size: 26rpx;
  }

  .right {
    display: flex;
    justify-content: center;
    align-items: center;

    .but {
      width: 120rpx;
      height: 40rpx;
      background: var(--primary-button-color);
      border-radius: 24rpx;
      color: #fff;
      font-size: 20rpx;
      line-height: 40rpx;
      color: #000;
      text-align: center;
      margin-right: 20rpx;
    }

    image {
      width: 16rpx;
    }
  }
}

.advertising {
  margin: 50rpx 0rpx 30rpx 0rpx;

  .cart {
    display: flex;
    justify-content: space-between;

    .left_top {
      margin-right: 5rpx;
      width: 398rpx;
      height: 160rpx;

      image {
        width: 398rpx;
        height: 160rpx;
      }
    }

    .right_top {
      width: 274rpx;
      height: 160rpx;

      image {
        width: 274rpx;
        height: 160rpx;
      }
    }

    .left_bottom {
      margin-right: 5rpx;
      width: 254rpx;
      height: 160rpx;

      image {
        width: 254rpx;
        height: 160rpx;
      }
    }

    .right_bottom {
      width: 416rpx;
      height: 160rpx;

      image {
        width: 416rpx;
        height: 160rpx;
      }
    }
  }
}

.rank {
  .rank_head {
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tabs {
      /* width: 500rpx; */
    }

    .help {
      font-size: var(--small-font-size);

      image {
        width: var(--medium-font-size);
        margin-left: 20rpx;
      }
    }
  }

  .rise,
  .hot {
    width: 100%;

    .li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--medium-font-size);
      margin-top: 40rpx;

      &:first-child {
        margin-top: 0;
      }

      .img_font {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 30rpx;
        }

        .title {
          color: var(--default-color1);
          width: 187rpx;
        }
      }

      .price {
        color: var(--default-color3)
      }

      .range {
        color: var(--active-color2);
        height: 60rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        position: relative;
        min-width: 220rpx;

        // padding: 0rpx 22rpx;
        &.lvse {
          color: #6CFF8A;
        }

        image {
          width: 22rpx;
          height: 22rpx;
          position: absolute;
          top: 0rpx;
          right: 0rpx;
        }
      }
    }

    .down {
      margin-top: 11rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 44rpx;
      }
    }


  }

  .cbTit {
    width: 100%;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #6C697B !important;
    justify-content: center;

    > view:nth-child(2) {
      margin: 0 10rpx;
    }

    image {
      width: 40rpx;
      height: 33rpx;
    }
  }

  .optional {
    width: 100%;

    .li_view {
      width: 680rpx;
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;
      box-sizing: border-box;

      .left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .longs {
          width: 32rpx;
          height: 32rpx;
          margin-right: 30rpx;

          > image {
            width: 100%;
            height: 100%;
          }
        }

        .img {
          margin-right: 30rpx;
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          overflow: hidden;
          transform: rotate(0deg);
          -webkit-transform: rotate(0deg);

          image {
            width: 80rpx;
            height: 80rpx;
          }
        }

        > .font_view {
          .title {
            font-size: 28rpx;
            width: 250rpx;
          }

          .num_view {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-top: 10rpx;
            font-size: 24rpx;

            > .price {
              margin-right: 20rpx;
              color: #63EAEE;
            }

            > .range {
              color: #EC4068;
            }
          }
        }
      }

      .delet {
        width: 36rpx;
        height: 36rpx;

        > image {
          width: 100%;
          height: 100%;
        }
      }

      .right {
        .top1 {
          display: flex;
          justify-content: flex-start;

          > view {
            width: 120rpx;
            height: 50rpx;
            font-size: 24rpx;
            background: var(--primary-button-color);
            color: #141816;
            border-radius: 30rpx;
            text-align: center;
            line-height: 50rpx;
            margin-left: 20rpx;
          }
        }

        .but {
          display: flex;
          justify-content: flex-start;

          > view {
            width: 120rpx;
            height: 50rpx;
            font-size: 24rpx;
            color: --default-color1;
            border: 1px solid var(--default-color1);
            border-radius: 30rpx;
            text-align: center;
            line-height: 50rpx;
            margin-left: 20rpx;
          }

          .buy {
          }

          .sale {
            &.huise {
              border: 1px solid rgba(255, 255, 255, 0.5);
              color: rgba(255, 255, 255, 0.5);
            }
          }
        }
      }
    }

    .down {
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 44rpx;
      }
    }
  }

  .hot {
    .li {
      .range {
        color: var(--default-color1);

        text {
          color: var(--active-color1);
        }
      }
    }
  }

  .fight_view {
    color: #fff;
    background-color: #25232D;
    padding: 40rpx 57rpx;
    border-radius: 24rpx;

    .left {
      width: 100%;

      > .title {
        text-align: center;
        margin: 20rpx 0rpx 48rpx 0rpx;
        font-size: 29rpx;
        font-weight: 600;
      }

      .price {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 24rpx;

        text {
          margin-right: 30rpx;
        }

        > .input {
          width: 300rpx;
          height: 50rpx;
          border: 1px solid #FFFFFF;
          border-radius: 25rpx;
          background-color: #514F57;

          padding: 0rpx 20rpx;

          input {
            flex: 1;
            height: 100%;
            font-size: 24rpx;
            text-align: center;
          }
        }

        .button {
          width: 116rpx;
          height: 50rpx;
          background: #FFFFFF;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 36rpx;
          margin-left: 60rpx;
          color: #35333E;
          font-size: 24rpx;
        }
      }

      .num_lever {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 24rpx;
        margin-top: 47rpx;

        text {
        }

        .input {
          width: 180rpx;
          height: 50rpx;
          border: 1px solid #FFFFFF;
          border-radius: 25rpx;
          background-color: #514F57;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          position: relative;
          margin-left: 30rpx;
          position: relative;
          padding-left: 20rpx;

          image {
            position: absolute;
            right: 10rpx;
            width: 24rpx;
          }

          .model_open_num {
            position: absolute;
            top: 70rpx;
            right: 0rpx;
            width: 180rpx;
            background-color: #46454F;
            color: #fff;
            z-index: 100;
            border-radius: 6rpx;

            > .li {
              text-align: center;
              padding: 20rpx 0rpx;
            }
          }

          .model_open {
            position: absolute;
            top: 70rpx;
            right: 0rpx;
            width: 180rpx;
            background-color: #46454F;
            color: #fff;
            z-index: 100;
            border-radius: 6rpx;

            > .li {
              text-align: center;
              padding: 20rpx 0rpx;
            }
          }
        }
      }

    }

    .footer_button_view {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 50rpx;

      .but {
        width: 260rpx;
        height: 60rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
        text-align: center;
        line-height: 60rpx;
        color: #141816;
        background: linear-gradient(143deg, #53E571 0%, #82E44D 100%);

        &.active {
          background: linear-gradient(143deg, #FF5270 0%, #FB6F46 100%);
        }
      }
    }

  }
}

.consult_view {
  margin-top: 30rpx;

  .consult_head {
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tabs {
      /* width: 400rpx; */
    }

    .help {
      color: var(--default-color3);
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: var(--small-font-size);

      image {
        width: var(--medium-font-size);
        margin-left: 8rpx;
      }
    }
  }

  .actives,
  .advisory {
    .li {
      border-radius: var(--medium-font-size);
      border: 1px solid #717171;
      /* height: 148rpx; */
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 14rpx;
      position: relative;
      margin-bottom: 40rpx;


      .left_img {
        width: 220rpx;
        /* height: 160rpx; */
        margin-right: 20rpx;
        /* display: flex; */
        /* align-items: center; */

        image {
          width: 220rpx;
          height: 100rpx;
          border-radius: var(--small-font-size);
        }
      }

      .font_view {
        .title {
          color: var(--default-color1);
          font-size: var(--medium-font-size);
          margin-bottom: 20rpx;
          width: 380rpx;
        }

        .msg {
          color: var(--default-color3);
          font-size: var(--small-font-size);
        }
      }

      > .right {
        display: flex;
        justify-content: center;
        align-items: center;

        image {
          width: var(--medium-font-size);
        }
      }

      > .right_icon {
        position: absolute;
        right: 0rpx;
        top: 0rpx;
        width: 112rpx;
        height: 40rpx;

        .status1 {
          color: var(--default-color1);
          line-height: 40rpx;
          text-align: center;
          font-size: 18rpx;
          background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240301/525cd89376c4fbc77fc3d24ae16b82c1_112x40.png);
          background-size: 100% 100%;
        }

        .status2 {
          color: var(--default-color1);
          line-height: 40rpx;
          text-align: center;
          font-size: 18rpx;
          background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240301/9aaa725eaeb6cb21b2725fa5925f4349_112x40.png);
          background-size: 100% 100%;
        }

        .status3 {
          color: var(--default-color1);
          line-height: 40rpx;
          text-align: center;
          font-size: 18rpx;
          background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240301/b7b7f458f76aacd022c93c9b6994efbd_112x40.png);
          background-size: 100% 100%;
        }
      }
    }
  }

  .celebrity {
    //名人堂
    color: var(--default-color1);
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .plutocratBox {
      width: 324rpx;
      height: 648rpx;
      background: #35333E;
      border-radius: 36rpx;
      padding-top: 16rpx;
      box-sizing: border-box;
      background: url('@/static/imgs/public/border.png') no-repeat;
      background-size: 100% 100%;

      .tit {
        width: 172rpx;
        height: 48rpx;
        margin: 0 auto 22rpx auto;

        > image {
          width: 100%;
          height: 100%;
        }
      }

      .swiper {
        height: 550rpx !important;

        .siwperItem {
          height: 100%;
        }

        ::v-deep .uni-swiper-dot {
          width: 6rpx;
          height: 6rpx;
          background: rgba(255, 255, 255, 0.5);
          opacity: 0.5;
        }

        ::v-deep .uni-swiper-dot-active {
          width: 20rpx;
          height: 6rpx;
          background: #FFFFFF;
          border-radius: 4rpx;
        }
      }

      .list {
        width: fit-content;
        display: flex;
        align-items: center;
        margin: 0 auto 30rpx auto;

        > view:nth-child(1) {
          width: 70rpx;
          margin-right: 20rpx;

          > view:nth-child(1) {
            width: 60rpx;
            height: 60rpx;
            margin: 0 auto;

            > image {
              width: 100%;
              height: 100%;
              border-radius: 18rpx;
            }
          }

          .topHead {
            background: url('@/static/imgs/plutocrat/topHead.png') no-repeat;
          }

          .topFoot {
            background: url('@/static/imgs/plutocrat/topFoot.png') no-repeat;
          }

          > view:nth-child(2) {
            font-weight: bold;
            font-size: 20rpx;
            color: #FFFFFF;
            text-align: center;
            background-size: cover;
          }
        }

        > view:nth-child(2) {

          font-weight: 400;
          font-size: 24rpx;
          color: #63EAEE;

          > view:nth-child(1) {
            width: 109rpx;
            color: #FFFFFF;
            margin-bottom: 8rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.banner {
  position: relative;
  height: 284rpx;

  .banner_view {
    width: 676rpx;
    height: 284rpx;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    background: var(--primary-button-color);
    padding: 1px;
    border-radius: var(--small-font-size);
  }

  .backgroundCart {
    position: absolute;
    top: 36rpx;
    left: 0;
    right: 0;
    height: 308rpx;
    margin: 0 auto;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240304/67f227ca07e1233ee7f27d0412be580b_700x338.png);
    background-size: 100% 100%;
    z-index: 1;
  }
}

.null_body {
  height: 300rpx;

  .null {
    .img {
      display: flex;
      justify-content: center;

      image {
        width: 140rpx;
      }
    }

  }

  .text {
    color: #A6A6A6;
    font-size: 28rpx;
    text-align: center;
    margin-top: 30rpx;
  }

  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.null_body_view {
  height: 550rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.video_open {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 99;
  background-color: #000;

  video {
    width: 100%;
    height: 100vh;
  }
}

.openCoverImg {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;

  .right_view {
    font-size: 26rpx;
    width: 150rpx;
    height: 50rpx;
    line-height: 50rpx;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    border-radius: 25rpx;
    position: absolute;
    right: 60rpx;
    top: 60rpx;

    text {
      color: peru;
      margin-left: 10rpx;
    }
  }

  image {
    width: 100%;
  }
}

.mask {
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
</style>
