<template>
    <view class="transaction-history-container">
        <view class="titleTR">{{ $t("index.TransactionHistory.Title") }}</view>
        <!-- Tab 部分 -->
        <view class="tab-filter1" v-if="subpage">
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: activeTab === tab.value }]"
                @click="confirmType(tab)">
                <text> {{ tab.label }}</text>
            </view>
        </view>
        <view class="tab-filter" v-else>
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: activeTab === tab.value }]"
                @click="confirmType(tab)">
                <text> {{ tab.label }}</text>
            </view>
        </view>
        <!-- 内容区域 -->
        <div class="transaction-history-content">
            <!-- 缺省状态 -->
            <div v-if="transactions.length == 0" class="transaction-history-empty">
                <image v-if="!subpage"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250402/beddb1e64a619981d03712ee26f6bc42_715x761.png" />
                <image v-else
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250423/24f87c835f745e09853af267c8ac9364_717x716.png" />
                <div class="transaction-history-empty-text">
                    <view class="t">{{ $t("index.TransactionHistory.Empty") }}</view>
                    <view class="b">{{ $t("index.TransactionHistory.EmptyDescription") }}</view>
                </div>
            </div>

            <!-- 有数据状态 -->
            <transition-group name="slide-up" tag="div" class="transaction-history-list" v-else>
                <div v-for="(item, index) in transactions.slice(0, 5)" :key="index" class="transaction-history-item">
                    <div class="transaction-history-item-left">
                        <div class="transaction-history-item-type">
                            <!-- <image :src="item.type === 'deposit' || item.type === 'receive' ? 'https://cdn-icons-png.flaticon.com/512/545/545674.png' : 'https://cdn-icons-png.flaticon.com/512/545/545682.png'"
										alt="Arrow" class="transaction-history-item-arrow" /> -->
                            <image class="transaction-history-item-arrow"
                                :style="{ transform: (item.type === 'receive' || item.type === 'send') ? 'rotate(180deg)' : 'rotate(0deg)' }"
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250402/e68f459aff31ff40c41524dc211c73d6_168x168.png" />
                        </div>
                        <div class="transaction-history-item-address">
                            <text class="type">{{ item.type }}</text>
                            <!-- <text class="address" v-if="item.ctime">{{ item.address ||
                                `${item.ctime.split('T')[0]} ${item.ctime.split('T')[1].split('.')[0]}`
                                }}</text> -->
                            <text class="address">{{ formatTimestamp(item.createAt) }}</text>
                        </div>
                    </div>
                    <div class="transaction-history-item-right">
                        <div class="transaction-history-item-amount"
                            :class="{ 'transaction-history-item-amount-positive': item.amount > 0 }">
                            {{ item.amount > 0 ? '+' : '' }}{{ item.amount }} {{ item.symbol }}
                        </div>
                        <div class="transaction-history-item-balance">{{ item.balance }} {{ item.symbol }}</div>
                    </div>
                </div>
            </transition-group>
        </div>
        <zero-loading type="sword" v-if="loading"></zero-loading>

    </view>
</template>

<script>
export default {
    props: ['nowsymbol', 'subpage'],
    data() {
        return {
            loading: false,
            transactions: [],
            activeTab: 'all', // 默认选中 'All'

        }
    },
    computed: {
        tabs() {
            return [
                { label: this.$t("index.TransactionHistory.All"), value: 'all' },
                { label: this.$t("Types.Deposit"), value: 'deposit' },
                // { label: 'Send', value: 'send' },
                // { label: 'Received', value: 'received' },
                { label: this.$t("Types.Swap"), value: 'swap' },
                { label: this.$t("Types.Withdrawal"), value: 'withdraw' },
            ]
        }
    },
    watch: {
        nowsymbol: {
            handler(val) {
                this.getList()
            },
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatTimestamp2(seconds) {
            return Math.floor(seconds);
        },
        confirmType(tab) {
            this.activeTab = tab.value
            this.getList()
        },
        async getList() {
            this.loading = true
            let res = await this.$api.getTransactionPaged({
                pageNum: 1,
                pageSize: 10,
                symbol: this.nowsymbol,
                type: this.activeTab,
                // beginTime: "",
                // endTime: ""
            })
            if (res.code == 200) {
                // if (this.page.pageNum) {
                this.transactions = res.result.data
                this.loading = false
                // } else {
                // this.transactions = this.list.concat(res.result.data)
                // }
            } else {
                this.loading = false
                this.$u.toast(res.msg)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.transaction-history-container {
    margin-top: 48rpx;
    width: 100%;
    // padding: 20px;
    box-sizing: border-box;

    .titleTR {
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 36rpx;
        line-height: 44rpx;
        letter-spacing: 0%;
        color: #000;
        margin-bottom: 28rpx;
    }

    .tab-filter1 {
        display: flex;
        // padding: 10px;
        // justify-content: center;
        gap: 12rpx;
        // margin: 40rpx 32rpx;
        width: 100%;
        overflow-x: auto;

        .tab-item {
            padding: 8rpx 24rpx;
            border-radius: 40rpx;
            background: #FFF3F6;
            transition: all 0.3s;
            // min-width:120rpx;
            width: fit-content;
            display: flex;
            color: #000;
            align-items: center;
            justify-content: center;

            &:first-child {
                padding: 8rpx 40rpx;

            }

            text {
                display: block;
                // width: 90rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 160%;
                letter-spacing: 0%;
                text-align: center;

            }

            &.active {
                background: #FF82A3;
                color: #fff;
            }
        }
    }

    .tab-filter {
        display: flex;
        // padding: 10px;
        // justify-content: center;
        gap: 12rpx;
        // margin: 40rpx 32rpx;
        width: 100%;
        overflow-x: auto;

        .tab-item {
            padding: 8rpx 24rpx;
            border-radius: 40rpx;
            background: #FFB4C8;
            transition: all 0.3s;
            // min-width:120rpx;
            width: fit-content;
            display: flex;
            color: #000;
            align-items: center;
            justify-content: center;

            &:first-child {
                padding: 8rpx 40rpx;

            }

            text {
                display: block;
                // width: 90rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 160%;
                letter-spacing: 0%;
                text-align: center;

            }

            &.active {
                background: #FFE6ED;
                color: #000;
            }
        }
    }

    .transaction-history-content {
        .transaction-history-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 92rpx;

            image {
                width: 178*2rpx;
                height: 190*2rpx;

            }

            .transaction-history-empty-icon {
                position: relative;
                width: 100px;
                height: 100px;

                .transaction-history-icon-btc,
                .transaction-history-icon-usd {
                    position: absolute;
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: #fff;
                    padding: 5px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                }
            }

            .transaction-history-empty-text {
                margin-top: 56rpx;
                text-align: center;

                .t {
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 36rpx;
                    line-height: 44rpx;
                    letter-spacing: 0%;
                    color: #000;
                }

                .b {
                    margin-top: 20rpx;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 38rpx;
                    letter-spacing: 0%;
                    color: #000;
                }
            }
        }

        .transaction-history-list {
            .transaction-history-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);

                .transaction-history-item-left {
                    display: flex;
                    align-items: center;

                    .transaction-history-item-type {
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        font-weight: bold;
                        color: #333;

                        .transaction-history-item-arrow {
                            width: 84rpx;
                            height: 84rpx;
                        }
                    }

                    .transaction-history-item-address {
                        margin-left: 28rpx;
                        display: flex;
                        flex-direction: column;
                        font-size: 12px;
                        color: #666;

                        .type {
                            font-family: Gilroy-SemiBold;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 44rpx;
                            color: #000;
                        }

                        .address {
                            font-family: Gilroy-SemiBold;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 44rpx;
                            color: #333;
                        }
                    }
                }

                .transaction-history-item-right {
                    text-align: right;

                    .transaction-history-item-amount {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 32rpx;
                        line-height: 38rpx;
                        letter-spacing: 0%;
                        text-align: right;
                        vertical-align: middle;
                        color: #000;

                        &.transaction-history-item-amount-positive {
                            color: #02B632;
                        }
                    }

                    .transaction-history-item-balance {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 44rpx;
                        letter-spacing: 0%;
                        text-align: right;
                        vertical-align: middle;

                    }
                }
            }
        }
    }
}
</style>