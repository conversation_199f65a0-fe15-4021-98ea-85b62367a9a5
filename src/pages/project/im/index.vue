<template>
  <view class="im">
    <view class="view">
      <view class="im_logo">
        <image
            src="https://cdn-lingjing.nftcn.com.cn/image/20240408/06893a22023f39f3ec36bf72712dc0e8_144x144.png"
            mode="widthFix"></image>
      </view>
      <!-- #ifdef APP -->
      <view class="text">点击下方按钮进入玩家交流群</view>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view class="text">点击下方按钮进入玩家交流群</view>
      <!-- #endif -->
      <view class="but" @click="nav_weixin">
        进入玩家交流群
      </view>
    </view>
    <TabBar :initialActiveIndex="3" v-show="!isApp"></TabBar>
    <u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
      <div class='sk-wave'></div>
      <view class="text_msg"
            style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
        加载中...
      </view>
    </u-modal>
  </view>
</template>

<script>
import TabBar from '@/components/public/TabBar.vue'

export default {
  data() {
    return {
      list: [],
      pageNum: 1,
      totalCount: 0,
      appUrl: "",
      token: "",
      isApp: false,
      isLoadding: false
    }
  },
  onReachBottom() {

  },
  onLoad(options) {
    this.appUrl = getApp().globalData.url
    if (options.token) {
      this.token = options.token
      this.isApp = true
      // this.nav_weixin()
      this.isLoadding = true
      setTimeout(() => {
        this.isLoadding = false
      }, 3000)
    } else {
      // this.nav_weixin()
    }

  },
  onShow() {
    this.nav_weixin()
  },
  components: {
    TabBar
  },
  methods: {
    nav_weixin() {
      let token = uni.getStorageSync('token')

      if (!token) {
        this.$Router.push({
          name: 'mainLogin',
        })
        return
      }
      // #ifdef APP
      this.appUrl = getApp().globalData.Imurl
      console.log(getApp().globalData.Imurl);
      let link = `${this.appUrl}im/#/pages/index/index`
      console.log(link)
      this.$Router.push({
        name: 'webView',
        params: {
          url: link,
          token: token

        }
      })
      // #endif

      // #ifdef H5
      this.appUrl = getApp().globalData.Imurl
      window.location.href = `${this.appUrl}im/#/pages/index/index?token=${token}`
      // #endif
      // return


      // // #ifdef APP
      // console.log("1.我先走app")
      // this.isLogin()
      // // #endif
      // // #ifdef H5
      // // 第一步获取token
      // console.log("2.我再走h5")
      // let token;
      // if (this.isApp) {
      //  token = this.token
      // } else {
      //  token = uni.getStorageSync('token')
      // }
      // console.log("用户token", token)
      // if (token) {
      //  const obj = {
      //      token,
      //  }
      //  let queryString = Object.keys(obj).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`).join('&') // 把对象转换成 a=1&b=2&c=3&d=4 的字符串格式
      //  const res = encodeURIComponent(queryString)
      //  console.log(`weixin://dl/business/?appid=wxd0691df783ac2a18&path=pages/index/index&query=${res}&env_version=release`)
      //  window.location.href = `weixin://dl/business/?appid=wxd0691df783ac2a18&path=pages/index/index&query=${res}&env_version=release`
      // } else {
      //  if (this.isApp) {
      //      this.myUni.webView.navigateTo({
      //          url: `/pages/project/login/mainLogin`
      //      });
      //  } else {
      //      this.$Router.push({
      //          name: "mainLogin"
      //      })
      //  }
      // }
      // #endif

    },
    nav_im() {
      uni.showToast({
        title: '敬请期待',
        icon: 'none',
        duration: 3000
      });
    },
    async isLogin() {
      let res = await this.$api.baseInfo({});
      if (res.status.code == 0) {
        let url = `${this.appUrl}pages/project/im/index`
        this.$Router.push({
          name: "webView",
          params: {
            url,
          }
        })
      } else if (res.status.code == 1002) {
        this.$Router.push({
          name: "mainLogin"
        })
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.im {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  .view {
    height: 600rpx;

    .im_logo {
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 200rpx;
        border-radius: 30rpx;
      }
    }

    .text {
      color: #fff;
      margin-top: 60rpx;
      text-align: center;
      font-size: 28rpx;
    }

    .but {
      width: 478rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
      border-radius: 10rpx;
      font-size: 32rpx;
      color: #141414;
      margin-top: 200rpx;
    }
  }
}
</style>
