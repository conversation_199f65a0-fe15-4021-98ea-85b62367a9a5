<template>
	<view class="im">
		<view class="left_back" @click="nav_index">
			<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
		</view>
		<view class="view">
			<view class="im_logo">
				<image
					:src="groupInfo.groupIcon"
					mode="aspectFill"></image>
			</view>
			<view class="im_name oneOver">{{groupInfo.groupName}}</view>
			<view class="border"></view>
			<view class="but" @click="joinGroup()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240610/4ffe953f141ff502220ee68a668bf246_708x192.png" mode="widthFix"></image>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				加载中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				pageNum: 1,
				totalCount: 0,
				appUrl:"",
				token:"",
				isApp:false,
				isLoadding:false,
				groupID:"",
				groupInfo:{}
			}
		},
		onReachBottom() {

		},
		onLoad(options) {
			this.groupID = options.groupId
			this.getQueryGroup()
			
			// this.isLogin()
		},
		onShow(){
			// this.nav_weixin()
		},
		components: {
		},
		methods: {
			nav_weixin() {
				console.log(1)
				// #ifdef H5
					let token = uni.getStorageSync('token')
					const obj = {
						token,
					 }
					 let queryString = Object.keys(obj).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`).join('&') // 把对象转换成 a=1&b=2&c=3&d=4 的字符串格式
					        const res = encodeURIComponent(queryString)
					console.log(`weixin://dl/business/?appid=wxd0691df783ac2a18&path=pages/index/index&query=${res}&env_version=release`)
					window.location.href = `weixin://dl/business/?appid=wxd0691df783ac2a18&path=pages/index/index&query=${res}&env_version=release`
				// #endif
				
			},
			async getQueryGroup() {
				let res = await this.$api.queryGroup({
					groupAccount:this.groupID
				});
				if (res.status.code == 0) {
					console.log(res)
					this.groupInfo = res.result
				}else if(res.status.code ==1002){
					this.$Router.push({
						name:"mainLogin"
					})
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async joinGroup() {
				let res = await this.$api.applyJoinGroupV2({
					groupId:this.groupID,
					applyUser:uni.getStorageSync('uid')
				});
				if (res.status.code == 0) {
					this.nav_weixin()
				}else if(res.status.code ==1002){
					this.$Router.push({
						name:"mainLogin"
					})
				}else{
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async isLogin() {
				let res = await this.$api.baseInfo({
					
				});
				if (res.status.code == 0) {
					// let url = `${this.appUrl}pages/project/im/index`
					// this.$Router.push({
					// 	name:"webView",
					// 	params:{
					// 		url,
					// 	}
					// })
				} else if(res.status.code ==1002){
					this.$Router.push({
						name:"mainLogin"
					})
				}else{
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_index(){
				this.$Router.pushTab({
					name:"index"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.im {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100vh;
		.left_back{
			position: absolute;
			left:40rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:50rpx;
			/* #endif */
			image{
				width:50rpx;
			}
		}
		.view {
			height: 600rpx;

			.im_logo {
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 200rpx;
					height:200rpx;
					border-radius: 10rpx;
				}
			}
			.border{
				width:663rpx;
				height:1px;
				background-color:rgba(255, 255, 255, 0.2);
				margin-top:106rpx;
			}
			.im_name{
				color: #fff;
				margin-top: 60rpx;
				text-align: center;
				font-size: 28rpx;
				width:100%;
			}

			.but {
				width: 354rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 10rpx;
				font-size: 32rpx;
				color: #141414;
				margin:108rpx auto;
			}
		}
	}
</style>