<template>
    <view class="container">
        <view class="barHeight"></view>
        <view class="head_top">
            <view v-if="platform"></view>
            <view class="back" @click="back" v-if="!platform">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                    alt="" srcset="" />
            </view>
            <view class="shareright" @click="isShare = true">
                <text>分享</text>
                <image
                    src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/747bcd9cd8ebb10fa817f12fe2156c60_50x50.png"
                    alt="" srcset="" />
            </view>
        </view>
        <view class="countdown-container">
            <text class="countdown-title">距离结束还有</text>
            <view class="countdown-wrapper">
                <view class="countdown-box" v-for="(unit, index) in countdownUnits" :key="index">
                    <view class="countdown-line"></view>
                    <view class="countdown-value">{{ unit.value }}</view>
                    <text class="countdown-label">{{ unit.label }}</text>
                </view>
            </view>
        </view>
        <view class="btn" style="margin-top: 41rpx;" @click="jointrade" v-if="!tradeinfo.isJoined">立即参与</view>

        <!-- 比赛奖池 -->
        <view class="match_box">
            <view class="prize-pool-container">
                <view class="prize-pool-header">
                    <text class="title">比赛奖池</text>
                </view>

                <view class="current-prize-container">
                    <view class="current-prize-label">
                        <!-- <text class="label">当前奖池：</text> -->
                        <text class="amount">¥{{ tradeinfo.reward }}</text>
                    </view>

                    <view class="prize-amount">
                        <image class="coin-icon" mode="widthFix"
                            src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/469a38bb1dbc4de8fc762f438c656838_166x142.png" />
                    </view>
                </view>

                <view class="progress-container" v-if="tradeinfo.bonusType == 2">
                    <view class="progress-column">
                        <text class="progress-title">交易量</text>
                        <text class="progress-unit"> {{ formatCurrency(tradeinfo.tradeAmount) }}</text>
                        <view class="progress-wrapper">
                            <view class="progress-bar">
                                <!-- <text >{{ item.tradeAmount }}</text> -->

                                <view class="ball" :style="{ background: item.isActive ? '#00d7f9' : '#959595' }"
                                    v-for="(item, index) in tradeinfo.bonusRuleList"></view>
                                <!-- <view class="ball"></view>
                                <view class="ball"></view>
                                <view class="ball"></view>
                                <view class="ball"></view> -->
                                <view class="progress" :style="{ height: progressHeight }"></view>
                            </view>

                            <view class="progress-values">
                                <text v-for="(item, index) in tradeinfo.bonusRuleList" :key="index">{{ item.tradeAmount
                                    }}</text>
                                <!-- <text>0</text>
                                <text>50M</text>
                                <text>100M</text>
                                <text>500M</text>
                                <text>1B</text> -->
                            </view>

                        </view>

                        <!-- <text class="progress-amount">¥1,000,000</text> -->
                    </view>

                    <view class="progress-column">
                        <text class="progress-title">总奖池</text>
                        <text class="progress-unit"> {{ formatCurrency(tradeinfo.reward) }} </text>

                        <view class="progress-wrapper">
                            <view class="progress-bar">
                                <!-- <view class="ball"></view>
                                <view class="ball"></view>  
                                <view class="ball"></view>
                                <view class="ball"></view>
                                <view class="ball"></view> -->
                                <view class="ball" :style="{ background: item.isActive ? '#00d7f9' : '#959595' }"
                                    v-for="(item, index) in tradeinfo.bonusRuleList"></view>

                                <view class="progress" :style="{ height: progressHeight }"></view>
                            </view>
                            <view class="progress-values">
                                <!-- <text>5K</text>
                                <text>10K</text>
                                <text>20K</text>
                                <text>30K</text>
                                <text>50K</text> -->
                                <text v-for="(item, index) in tradeinfo.bonusRuleList" :key="index">{{ item.bonus
                                    }}</text>

                            </view>

                        </view>
                        <!-- <text class="progress-amount">¥50,000</text> -->
                    </view>
                </view>
            </view>
        </view>

        <!-- 排名 -->
        <view class="ranking-containerOuter">
            <view class="ranking-container">
                <view class="ranking-title">
                    <text class="left">{{ tradeinfo.activityType == 1 ? '交易量' : tradeinfo.activityType == 2 ? '收益额'
                        :
                        '收益率'
                        }}排行榜</text>
                    <text class="right">每小时更新一次</text>
                </view>

                <!-- 表头 -->
                <view class="ranking-header">
                    <text class="header-cell" style="margin-left: -40rpx;">排名</text>
                    <text class="header-cell" style="margin-left: 40rpx;">用户</text>
                    <text class="header-cell" style="margin-left: 90rpx;">{{ tradeinfo.activityType == 1 ? '交易量' :
                        tradeinfo.activityType == 2 ? '收益额' :
                            '收益率'
                        }}</text>
                    <text class="header-cell" style="margin-right :-60rpx;">预计奖励</text>
                </view>

                <!-- 排行数据 -->
                <view class="ranking-body">
                    <view class="ranking-row highlighted bold" v-if="result.userRankInfo">
                        <text class="ranking-cell">{{ result.userRankInfo.rank }}</text>
                        <text class="ranking-cell">{{ result.userRankInfo.name }}</text>
                        <text class="ranking-cell price" style="text-align: end;">{{ tradeinfo.activityType == 3 ? '' :
                            '¥' }}{{ tradeinfo.activityType == 3 ? formatNumber(result.userRankInfo.num) :
                                result.userRankInfo.num }}</text>
                        <text class="ranking-cell price" style="text-align: end;">¥{{ result.userRankInfo.reward
                            }}</text>
                    </view>

                    <view class="ranking-row" v-for="(item, index) in result.rankInfoList" :key="index">
                        <!-- <image :src="item.icon" class="rank-icon" v-if="item.rankIcon" /> -->
                        <view v-if="item.rankIcon" style="width: 440rpx;">
                            <image :src="item.icon" style="width: 70rpx;height: 26rpx;" alt="Rank Icon" />
                        </view>
                        <text v-else class="ranking-cell">{{ item.rank }}</text>
                        <!-- <text class="ranking-cell">{{ item.rank }}</text> -->
                        <text class="ranking-cell">{{ item.name }}</text>
                        <text class="ranking-cell price" style="text-align: end;">{{ tradeinfo.activityType == 3 ? '' :
                            '¥' }}{{ tradeinfo.activityType == 3 ? formatNumber(item.num) : item.num
                            }}</text>
                        <text class="ranking-cell price" style="text-align: end;">¥{{ item.reward }}</text>
                    </view>

                    <view v-if="result.rankInfoList.length == 0" class="nodata">
                        <image mode="widthFix"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240813/0168b66bda9880ba60c05ca8f52b6981_480x480.png" />
                        <text>暂时没有数据</text>
                    </view>
                </view>

                <!-- 分页 -->
                <view class="pagination">
                    <!-- <view class="pagination-item" v-if="pageNum > 1" @click="prev">{{ '<' }}</view> -->
                    <u-icon name="arrow-left" size="24" v-if="(pageNum > 1) && result.maxPage && pageNum != 1"
                        color="#fff" @click="prev" />
                    <view class="pagination-item" @click="changePage(item)" :class="{ active: item == result.pageNum }"
                        v-for="item in result.maxPage" :key="item.id">{{ item }}</view>

                    <!-- <view class="pagination-item active">1</view> -->
                    <!-- <view class="pagination-item">2</view>
                    <view class="pagination-item">3</view>
                    <view class="pagination-item">4</view> -->
                    <!-- <view class="pagination-item" v-if="pageNum >= result.pageNum" @click="next">{{ '>' }}</view> -->
                    <u-icon v-if="(pageNum <= (result.pageNum)) && result.maxPage && pageNum != 1" name="arrow-right"
                        size="24" color="#fff" @click="next" />
                    <!-- {{ pageNum + result.rankInfoList }} -->
                </view>
            </view>
        </view>

        <!-- 规则 -->
        <view class="rule-container" v-html="tradeinfo.activityRule">

        </view>
        <!-- <view class="activity-rules">
                <view class="rules-title">活动规则</view>
                <view class="rule-item">
                    <text class="rule-index">1.起止时间：</text>
                    <br>
                    <text class="rule-content">2024/10/01 00:00:00 ~ 2024/11/30 23:59:59</text>
                </view>
                <view class="rule-item">
                    <text class="rule-index">2.比赛奖池：</text>
                    <br>
                    <text class="rule-content">根据总交易量逐步提升奖池金额；</text>
                </view>
                <view class="rule-item">
                    <text class="rule-index">3.奖励发放：</text>
                    <br>
                    <view class="rule-content">
                        根据收益排名瓜分奖池；
                        <br />前三名根据排名瓜分奖池的60%；
                        <br />第4名到第10名瓜分奖池的20%；
                        <br />奖金将在活动结束后5个工作日内发放。
                    </view>
                </view>
                <view class="rule-item">
                    <text class="rule-index">4.参与限制：</text>
                    <br>
                    <text class="rule-content">同样的实名认证身份仅能参与一次。</text>
                </view>
            </view> -->
        <view style="height: 159rpx"></view>

        <!-- 分享 -->
        <u-popup v-model="isShare" mode="center">
            <view class="share_body">
                <view class="cart_div">
                    <view class="top">
                        <image :src="shareUser.avatar" />
                        <text class="name">{{ username }}</text>
                        <text class="content">邀请您参与Bit指数交易大赛</text>
                    </view>
                    <view class="bom">
                        <view class="title">
                            <text class="titleup">瓜分 <text style="color: #FFE4BA;margin: 0 10rpx;">¥10,000</text>
                                奖池快来交易吧！</text>
                            <text class="time">分享于{{ new Date().toISOString().split('T')[0] }}</text>
                        </view>
                    </view>
                    <view class="line"></view>

                    <view class="code">
                        <view class="left">
                            <text class="mycode">我的邀请码</text>
                            <view class="codes">{{ shareUser.invitationCode }}</view>
                            <text class="tips">扫码注册，及时上车</text>
                        </view>
                        <view class="right">
                            <view class="qr_code">
                                <view class="right">
                                    <view class="qr_div">
                                        <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="98rpx"
                                            :options="options"></uv-qrcode>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="colse_div" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
                        mode="widthFix"></image>
                </view>
            </view>
        </u-popup>

    </view>
</template>

<script>
import uniCopy from "@/js_sdk/uni-copy.js";
// 1 "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/a4d17b3ab20b04e82767525d82448e74_140x52.png"
// 2 "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/22e165f7c3bdaace8389708fc5895f48_140x52.png"\
// 3 "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/7e25133c780a598a48875f18c5515b31_140x52.png"
export default {
    data() {
        return {
            result: {},
            pageNum: 1,
            username: '',
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            qrcodeUrl: "",
            shareUser: {},
            isShare: false,
            currentTime: '',
            endTime: '',
            // currentTime: new Date().getTime(),
            // endTime: new Date("2024-11-08T00:00:00").getTime(), // 设定倒计时结束的时间
            countdownUnits: [
                { label: "天", value: "00" },
                { label: "时", value: "00" },
                { label: "分", value: "00" },
                { label: "秒", value: "00" },
            ],
            rankings: [
                { rankIcon: true, rank: "TOP1", user: "张****a", revenue: "¥1,000,00.00", reward: "¥3,000.00", },
                { rankIcon: true, rank: "TOP2", user: "张****a", revenue: "¥900,00.00", reward: "¥2,000.00", },
                { rankIcon: true, rank: "TOP3", user: "张****a", revenue: "¥800,00.00", reward: "¥1,000.00", },
                { rankIcon: false, rank: "TOP4", user: "张****a", revenue: "¥10,000.00", reward: "¥400.00" },
                { rankIcon: false, rank: "TOP5", user: "张****a", revenue: "¥10,000.00", reward: "¥400.00" },
                { rankIcon: false, rank: "TOP6", user: "张****a", revenue: "¥10,000.00", reward: "¥400.00" },
            ],
            tradeinfo: {},
            isApp: false,
            platform: false
        }
    },
    onLoad(options) {
        const { token, platform } = options
        if (token) {
            uni.setStorageSync('token', token)
            // this.isApp = true
        }
        if (platform) {
            this.platform = platform

            uni.setStorageSync('is_platform', platform)
        }
        this.username = uni.getStorageSync('nickname')
        this.startCountdown();
        this.getUserShare()
        this.fetchTrade()
        // this.fetchRank()
        // console.log(this.result.userRankInfo);

    },
    computed: {
        // 计算 isActive 为 true 的数量
        activeCount() {
            if (this.tradeinfo.bonusRuleList) {
                return this.tradeinfo.bonusRuleList.filter(item => item.isActive).length;
            } else {
                return 0
            }
        },
        // 计算进度条的高度
        progressHeight() {
            const totalCount = this.tradeinfo.bonusRuleList.length;
            const activeRatio = this.activeCount / totalCount; // 比例
            return `${activeRatio * 100}%`; // 计算高度，转为百分比
        }
    },
    methods: {
        formatCurrency(amount) {
            let initstr = Number(amount);

            // 保证整数部分
            let integerPart = Math.floor(initstr); // 取整数部分

            // 使用正则添加千位分隔符
            let formattedIntegerPart = integerPart.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            // 返回格式化后的金额
            return '¥' + formattedIntegerPart;
        },
        formatNumber(value, decimalPlaces = 2, arg = 'other') {
            if (arg == 'fund' && value == 0) {
                return '--'
            }
            // if (value <= 0) {
            //     return 0.00
            // }
            if (!value && value !== 0) {
                return '--'
            }
            if (value == 0) {
                return '0.00' + '%'
            }
            // 判断是否为整数
            if (Number.isInteger(value)) {
                return value.toString();
            }
            value = Number(value * 100)
            // 如果是整数，则直接返回
            // 如果不是整数，则将其限制为指定的小数位数
            return value.toFixed(decimalPlaces) + '%';
        },
        async jointrade() {
            let res = await this.$api.TradeJoin({ idStr: this.tradeinfo.id })
            if (res.status.code == 0) {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none'
                })
                this.fetchTrade()

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none'
                })
            }
        },

        async fetchTrade() {
            let res = await this.$api.TradeInfo()
            if (res.status.code == 0) {
                this.tradeinfo = res.result
                this.endTime = this.getTimestamp(res.result.endTime)
                this.fetchRank(res.result.id)
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none'
                })
            }
            console.log(res)
        },
        prev() {
            this.pageNum = this.pageNum - 1
            this.fetchRank(this.tradeinfo.id)
        },
        next() {
            console.log(this.pageNum, this.result.pageNum);

            this.pageNum = this.pageNum + 1

            this.fetchRank(this.tradeinfo.id)
        },
        changePage(e) {
            console.log(123, this.result.pageNum, e);

            if (this.result.pageNum == e) return
            this.pageNum = e
            this.fetchRank(this.tradeinfo.id)
        },
        async fetchRank(e) {
            let a = JSON.stringify(e)
            let res = await this.$api.TradeRank({ activityIdStr: e, pageNum: this.pageNum })
            if (res.status.code == 0) {
                this.result = res.result
                this.addIconsToRankings()

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none'
                })
            }
            console.log(res)
        },
        async getUserShare() {
            let res = await this.$api.GetExchangeUserInfo({

            });
            console.log(res, '321');
            if (res.status.code == 0) {
                this.shareUser = res.result
                // const jumpUrl =
                //     `${getApp().globalData.url}pagesA/project/personal/appDownload`;
                // this.get_share(jumpUrl)
                console.log(this.shareUser)

                // #ifdef APP
                // APP端邀请地址
                this.qrcodeUrl = `${getApp().globalData.url}pages/project/login/register?code=${res.result.invitationCode}`
                // #endif
                // #ifdef H5
                // H5端邀请地址
                let { origin } = window.location
                this.qrcodeUrl = `${origin}/h5/#/pages/project/login/register?code=${res.result.invitationCode}`
                // #endif
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        startCountdown() {
            setInterval(() => {
                const now = new Date().getTime();
                const distance = this.endTime - now;

                if (distance > 0) {
                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    this.countdownUnits[0].value = String(days).padStart(2, "0");
                    this.countdownUnits[1].value = String(hours).padStart(2, "0");
                    this.countdownUnits[2].value = String(minutes).padStart(2, "0");
                    this.countdownUnits[3].value = String(seconds).padStart(2, "0");
                }
            }, 1000);
        },
        back() {
            this.$Router.back()

        },
        addIconsToRankings() {
            // 为前三名分别添加不同的 icon
            const icons = [
                "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/a4d17b3ab20b04e82767525d82448e74_140x52.png", // TOP1 图标路径
                "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/22e165f7c3bdaace8389708fc5895f48_140x52.png", // TOP2 图标路径
                "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241009/7e25133c780a598a48875f18c5515b31_140x52.png"  // TOP3 图标路径
            ];
            if (this.pageNum == 1) {
                this.result.rankInfoList.forEach((item, index) => {
                    if (index < 3) {
                        item.icon = icons[index];
                        item.rankIcon = true; // 添加 rankIcon 属性
                    }
                });
            }
            // 遍历前三名，添加 icon 键值对

        },
        getTimestamp(dateString) {
            // 创建一个新的 Date 对象，并将其转换为时间戳
            const timestamp = new Date(dateString).getTime();
            return timestamp;
        }
    }
}
</script>

<style lang="scss" scoped>
.nodata {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 50rpx 0rpx;

    image {
        height: 240rpx;
        width: 240rpx;
    }

    text {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #9A999F;
    }
}

.share_body {

    width: 600rpx;
    // margin-bottom: 200px;
    // background-color: #111111;
    // background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/e319063b91316c0a29dce409165d5324_1344x2064.png");
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    // padding: 160rpx 55rpx 55rpx;
    // height: 960rpx;
    // position: rela/tive;
    // display: flex;


    .cart_div {
        // width: 100%;
        // background-color: #2B2B2B;
        // border-radius: 36rpx;
        // width: 640rpx;
        // height: 940rpx;
        // padding: 40rpx 36rpx;
        position: relative;
        height: 990rpx;

        background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/e319063b91316c0a29dce409165d5324_1344x2064.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .code {
            display: flex;
            justify-content: space-between;
            margin: 18rpx 80rpx 80rpx 80rpx;

            .left {
                .mycode {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                    opacity: .5;
                    line-height: 32rpx;
                }

                .codes {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 900;
                    font-size: 54rpx;
                    color: #FFFFFF;
                    line-height: 72rpx;
                    margin-bottom: 9rpx;
                }

                .tips {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                    line-height: 32rpx;
                    opacity: .5;
                }
            }

            .right {
                .qr_code {
                    width: 140rpx;
                    height: 140rpx;
                    background: #EDEDED;
                    border-radius: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }

        .top {
            // position: absolute;
            padding-top: 89rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            image {
                width: 64rpx;
                height: 64rpx;
                border-radius: 50%;
            }

            .name {
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #41F7EC;
                line-height: 32rpx;
                margin: 0 10rpx 0 20rpx;
            }

            .content {
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
                line-height: 32rpx;
            }
        }

        .line {
            height: 2rpx;
            background: #FFFFFF;
            border: 1rpx solid #707070;
            opacity: 0.38;
            margin: 0 74rpx;
        }

        .bom {
            margin-top: 400rpx;
            // position: absolute;
            bottom: 301rpx;

            .title {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .time {
                    margin: 15rpx 0 13rpx 0;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    line-height: 29rpx;
                    opacity: .5;
                }

                .titleup {
                    display: flex;
                    justify-content: center;
                    // text-align: center;
                    margin: 0 auto;
                    width: 421rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 50rpx;
                    color: #FFFFFF;
                    line-height: 67rpx;
                }
            }

        }
    }

    .share_to_div {
        margin-top: 140rpx;
        display: flex;
        justify-content: center;

        >.li {
            width: 25%;
            text-align: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 10rpx;

            .icon_image {
                display: flex;
                justify-content: center;

                image {
                    width: 90rpx;
                    margin-bottom: 20rpx;
                }
            }
        }
    }

    .colse_div {
        text-align: center;
        margin: 0 auto;
        // margin-top: -100px;
        // position: absolute;
        // bottom: -110px;
        // margin-top: 146rpx;
        display: flex;
        justify-content: center;
        align-items: flex-end;

        image {
            width: 80rpx;
        }
    }
}

.rule-container {
    margin: 0 35rpx 30rpx 35rpx;
    background: #312958;
    border-radius: 36rpx;
    border: 1rpx solid #ED90F9;
    overflow: hidden;


}

.activity-rules {
    background-color: #2e184e;
    padding: 0 38rpx 45rpx 40rpx;
    border-radius: 10px;
    max-width: 400px;
    margin: 0 auto;
    color: #9F8BFF;
    // color: #fff;
}

.rules-title {
    color: #fff;
    padding: 35rpx 0 36rpx 0rpx;
    line-height: 48rpx;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: bold;
    font-size: 36rpx;
}

.rule-item {
    margin-bottom: 15px;
}

.rule-index {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: bold;
    font-size: 28rpx;
    color: #9F8BFF;
    line-height: 40rpx;
}

.rule-content {
    margin-left: 42rpx;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #9F8BFF;
    line-height: 40rpx;
}

.ranking-containerOuter {
    margin: 0 35rpx 30rpx 35rpx;
    background: #312958;
    border-radius: 36rpx;
    border: 1rpx solid #ED90F9;
    overflow: hidden;

    .ranking-container {
        // background-color: #2e184e;
        // padding: 20px;

    }

    .ranking-title {
        padding: 35rpx 53rpx 36rpx 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .left {
            line-height: 48rpx;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            font-size: 36rpx;
            color: #FFFFFF;
        }

        .right {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #9894AC;
            line-height: 32rpx;
        }
    }

    .ranking-header {
        padding: 36rpx 64rpx 16rpx 64rpx;
        display: flex;
        justify-content: space-around;
        border-radius: 5px;
        font-size: 16px;
        color: #89e4ff;
    }

    .header-cell {
        // flex: 1; 
        text-align: center;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        opacity: .5;
        color: #FFFFFF;
    }

    .ranking-body {
        padding: 29rpx 53rpx 40rpx 62rpx;

    }

    .ranking-row {
        display: flex;
        height: 90rpx;
        justify-content: space-between;
        padding: 10px 0;
        align-items: center;
        // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .ranking-row.highlighted {
        padding: 0 30rpx;
        margin: 0 -30rpx;
        // background-color: #6a28a6;
        height: 90rpx;
        background: linear-gradient(270deg, rgba(121, 15, 226, 0) 0%, #513A9F 100%);
        border-radius: 14rpx
    }

    .ranking-cell {
        width: 440rpx;
        // text-align: center;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
    }

    .rank-icon {
        width: 50px;
        height: 20px;
        margin-right: 10px;
    }

    .price {
        color: #00d7f9;
        font-weight: bold;
    }

    .pagination {
        display: flex;
        justify-content: center;
        padding-bottom: 40rpx;
    }

    .pagination-item {
        width: 44rpx;
        height: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        // padding: 5px 10px;
        // background-color: rgba(0, 0, 0, 0.2);
        margin: 0 6rpx;
        border-radius: 50%;
        color: #fff;
        font-size: 14px;
    }

    .pagination-item.active {
        color: #000;
        background-color: #6a28a6;
    }

}

.match_box {
    margin: 40rpx 35rpx 30rpx 35rpx;
    background: #312958;
    border-radius: 36rpx;
    border: 1px solid #ED90F9;
    overflow: hidden;

    .prize-pool-container {
        // background-color: #443A76;
        border-radius: 10px;
        // padding: 20px;
        color: #fff;
        max-width: 400px;
        margin: 0 auto;

        .prize-pool-header {
            padding: 35rpx 0 19rpx 40rpx;

            .title {
                display: flex;
                flex-direction: column;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                font-size: 36rpx;
                color: #FFFFFF;
            }
        }

        .current-prize-container {
            margin: 19rpx 24rpx 26rpx 24rpx;
            // background-color: rgba(0, 0, 0, 0.2);
            background: #443A76;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .current-prize-label {
                display: flex;
                flex-direction: column;

                // font-size: 16px;
                // color: #89e4ff;
                .label {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #FFFFFF;
                    opacity: .5;
                }

                .amount {
                    text-align: center;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: bold;
                    font-size: 66rpx;
                    width: 292rpx;
                    color: #63EAEE;
                    line-height: 88rpx;
                }
            }

            .prize-amount {
                display: flex;
                align-items: center;

                .amount {
                    font-size: 30px;
                    font-weight: bold;
                    color: #00d7f9;
                }

                .coin-icon {
                    width: 83rpx;
                    height: 71rpx;
                    margin-right: 17rpx;
                    // margin-left: 10px;
                }
            }
        }

        .progress-container {
            display: flex;
            justify-content: space-between;
            padding: 0 0 31rpx 0;

            .progress-column {
                width: 50%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                margin: 0 40rpx;

                .progress-wrapper {
                    margin-left: 10rpx;
                    display: flex;

                    .progress-values {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 30rpx;
                        color: #FFFFFF;
                        line-height: 30rpx;
                        margin-left: 24rpx;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        height: 120px;
                    }
                }

                .progress-unit {
                    margin: 7rpx 0 14rpx;
                    line-height: 50rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: bold;
                    font-size: 38rpx;
                    color: #FFFFFF;
                }

                .progress-title {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    opacity: .5;
                    color: #FFFFFF;
                }



                .progress-bar {
                    width: 3rpx;
                    background-color: #9894AC;
                    height: 229rpx;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: space-between;

                    .progress {
                        // width: 100%;
                        // height: 40%; // 可根据进度动态调整
                        background-color: #00d7f9;
                        position: absolute;
                        // height: 100px;
                        width: 3rpx;
                        // height: 140rpx;
                        top: 0;
                        z-index: 99;
                        border-radius: 5px;
                    }

                    .ball {
                        width: 10rpx;
                        height: 10rpx;
                        border-radius: 50%;
                        background: #959595;

                        // margin-left: -3rpx;
                        &:first-of-type {
                            width: 20rpx;
                            height: 20rpx;
                        }
                    }


                }

                .progress-amount {
                    font-size: 16px;
                    color: #ffffff;
                    margin-top: 10px;
                }
            }
        }
    }

}

.btn {
    // padding-top: 41rpx;
    height: 93rpx;
    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
    border-radius: 60rpx;
    margin: 0 63rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 500;
    font-size: 34rpx;
    color: #000000;
}

.head_top {
    display: flex;
    justify-content: space-between;
    padding: 33rpx;

    // position: fixed;
    // top: 0;
    // left: 0;
    z-index: 99;
    width: 100%;
    /* #ifdef APP */
    padding-top: 40rpx;
    /* #endif */

    .shareright {
        width: 141rpx;
        height: 48rpx;
        background: #A76FFF;
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            font-size: 28rpx;
            color: #1F0858;
            line-height: 0rpx;
            margin-right: 9rpx;
        }

        image {
            width: 25rpx;
            height: 25rpx;
        }
    }

    .back {
        // position: absolute;
        /* #ifdef APP */
        // top: var(--status-bar-height);
        // top: calc(32rpx + var(--status-bar-height));

        /* #endif */
        /* #ifdef H5 */
        // top: 64rpx;
        /* #endif */
        // left: 30rpx;

        img {
            width: 50rpx;
        }
    }

    .tabs_div {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 46rpx 55rpx 10rpx 55rpx;

        .left {
            // width: 460rpx;
        }

        .right {
            img {
                width: 36rpx;
            }
        }
    }
}

page {
    background: #070313;
    height: 100%;
    /* 确保html和body高度占满视口 */
    margin: 0;
    padding: 0;
}

.container {
    width: 100%;
    /* 设置宽度为视口宽度 */
    height: 1715rpx;
    /* 高度为父元素的100%，即整个视口 */
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/05bd2f9c4cfe150fc4fd1513fc3e7402_1512x3430.png");
    background-size: 100% 100%;
    /* 背景图片按比例缩放，覆盖整个容器 */
    background-position: center;
    /* 背景图片居中显示 */
    background-repeat: no-repeat;
    /* 防止图片重复 */
    position: relative;
    /* 可以根据需要使用 absolute/fixed 也可以使用 relative */
}

.countdown-container {
    padding-top: 520rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    // background: linear-gradient(to bottom, #3e0169, #020216);
    /* 背景渐变色 */
    // padding: 20px;
    border-radius: 10px;
}

.countdown-title {
    margin-bottom: 13rpx;
    line-height: 32rpx;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #63EAEE;
}

.countdown-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.countdown-box {
    display: flex;
    // flex-direction: column;
    align-items: center;
    // padding: 10px;
    // margin: 0 5px;
    padding: 0 16rpx;
    border-radius: 5px;
    margin-top: 13rpx;
    position: relative;
    width: 100%;

    .countdown-line {
        width: 47rpx;
        height: 1.6rpx;
        background: #000;
        z-index: 99999;
        // margin: 0 8rpx;
        position: absolute;
        // left: 50%;

    }

    .countdown-value {
        display: flex;
        justify-content: center;
        align-items: center;
        // background-color: #00d7f9;
        width: 47rpx;
        height: 52rpx;
        background: #63EAEE;
        border-radius: 3rpx;
        font-family: DIN Alternate, DIN Alternate;
        font-weight: bold;
        font-size: 33rpx;
        color: #000000;

        // margin-bottom: 5px;
    }

    .countdown-label {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 20rpx;
        color: #63EAEE;
        line-height: 0rpx;
        margin-left: 8rpx;
    }
}

.bold {
    font-weight: bold;
}
</style>