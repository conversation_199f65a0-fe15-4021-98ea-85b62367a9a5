<template>
	<view class="main">
		<view class="carousel">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20240613/3a75533f95b724910424b865cbe26dfe_1005x450.png" mode="widthFix">
</image>
		</view>
		<view class="title">
			限定福利,先到先得 
		</view>
		<view class="sub_title">
			完成全部活动有机会赢取Oner奖励 
		</view>
		<view class="ul">
			<view class="li" v-for="(item,index) in list">
				<view class="nunBg">
					{{index+1}}
				</view>
				<view class="right">
					<view class="tit">
						{{item.title}}
					</view>
					<view class="sub_tit">
						{{item.subTitle}}
					</view>
				</view>
			</view>
		</view>
		<view class="button" @click="nav_study()">
			<view class="bg" >开始检测</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				list:[
					{
						title:"交易手续费还是4%吗?",
						subTitle:"开杠吧模式手续费买卖均为0.5%。不收取版税。",
					},
					{
						title:"唱多意味着什么?",
						subTitle:"唱多意味着你看好《开杠藏品》未来的发展，以当前价格买入，在未来以高价卖出即可获得收益。",
					},
					{
						title:"唱空意味着什么?",
						subTitle:"唱空意味着你不看好《开杠藏品》未来的发展，以当前价格买入，在未来以低价卖出即可获得收益。",
					},
					{
						title:"杠杆如何使用?",
						subTitle:"举例:如果您非常想为开杠藏品“打call”，但你只想花费10元，如果用“10X”，即可操作100元的资金为开杠藏品“打call”",
					},
					{
						title:"什么是强制闭嘴价?",
						subTitle:"如果事与愿违，开杠藏品价格走势和你预期相去甚 远。如果您使用了杠杆或者唱空开杠藏品，到达强 制闭嘴价时，平台会认为您无法继续游戏，将会强制闭嘴。闭嘴后的收益将用于优先支付手续费。",
					}
				]
			}
		},
		onLoad(options) {

		},
		onReachBottom() {

		},
		methods: {
			nav_study(){
				this.$Router.push({
					name:"takeOffStudy"
				})
			},
		},
		components: {

		}
	}
</script>
<style lang="scss" scoped>
	.nunBg{ 
		background: linear-gradient(135deg, #EF91FB, #40F8EC);
		width:50rpx;
		height:50rpx;
		border-radius:50%;
		display: flex;
		justify-content: center;
		align-items: center;
		color:#36343F;
		font-weight:700;
		margin-bottom:25rpx;
	}
	.main {
		color:#fff;
		padding-bottom:120rpx;
		.carousel{
			background: linear-gradient(135deg, #EF91FB, #40F8EC);
			padding:2rpx;
			width:678rpx;
			margin:30rpx auto 60rpx auto;
			border-radius:25rpx;
			image{
				width:100%;
				border-radius:25rpx;
			}
		}
		.title{
			font-size:56rpx;
			margin-bottom:30rpx;
			text-align: center;
		}
		.sub_title{
			font-size:28rpx;
			margin-bottom:30rpx;
			color:rgba(255, 255, 255, 0.5);
			text-align: center;
		}
		.ul{
			padding:37rpx;
			margin-top:70rpx;
			.li{
				display: flex;
				justify-content: flex-start;
				border-bottom:1px solid rgba(255, 255, 255, 0.3);
				padding-bottom:26rpx;
				margin-bottom:32rpx;
				.right{
					margin-left:13rpx;
					width:600rpx;
					.tit{
						font-size: 28rpx;
					}
					.sub_tit{
						font-size:22rpx;
						color:rgba(255, 255, 255, 0.5);
						line-height:30rpx;
						margin-top:20rpx;
					}
				}
				
			}
		}
		.button{
			width:678rpx;
			height:88rpx;
			border-radius:44rpx;
			position: relative;
			  /* 添加内边距以避免伪元素覆盖内容 */
			padding: 4rpx;
			margin:46rpx auto;
			background:linear-gradient(0deg, #EF91FB, #40F8EC);
			display: flex;
			justify-content: center;
			align-items:center;
			color:#36343F;
		}
	}
</style>