.head_bg {
  background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240810/779f43c53404b06bc06fe014ca5607d0_1500x1800.png");
  background-size: cover;
  background-position: center;
  // min-height: 900rpx;
  max-height: 90vh;

  width: 100%;
  height: 90vh;

  .title {
    // // #ifdef APP-PLUS
    // margin-top: var(--status-bar-height);
    // // #endif
    padding: 26rpx 36rpx 28rpx 36rpx;
    display: flex;
    justify-content: space-between;
    position: relative;

    .left {
      display: flex;
      align-items: center;

      image {
        width: 100rpx;
        height: 100rpx;
        margin-right: 43rpx;
      }

      .topinfo {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;

        .top {
          display: flex;
          align-items: center;

          .checkindex {
            background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241219/24fcccbfc56ac69a6aa77c1915417e62_282x88.png");

            // #ifdef H5
            width: 143rpx;
            // #endif

            // #ifndef H5
            width: 148rpx;
            // #endif
            height: 46rpx;
            background-size: cover;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            text {
              font-weight: 800;
              font-size: 24rpx;
              color: #49ffff;
            }
          }

          // image {
          //   width: 141rpx;
          //   height: 44rpx;
          //   margin: 0 10rpx 0 2rpx;
          // }
        }

        > view {
          display: flex;
          align-items: center;
          width: 200rpx;

          .firstprice {
            font-weight: 800;
            font-size: 29rpx;
            // color: #ffffff;
            margin-right: 16rpx;
          }

          .rates {
            // text {
            //     font-weight: 400;
            //     font-size: 28rpx;
            // }
            font-weight: 400 !important;
            font-size: 21rpx;
            // color: #FE556C;
          }
        }
      }
    }

    .right {
      position: relative;

      image {
        z-index: 1;
        width: 35rpx;
        height: 8rpx;
      }

      display: flex;
      align-items: center;
    }

    .rights {
      width: 140rpx;
      height: 48rpx;
      background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240807/b40ead2af6ec908ad135fe76043697bc_280x96.png");
      background-size: cover;
      background-position: center;
      // position: relative;

      > text {
        margin-left: 62rpx;
        line-height: 48rpx;
        // position: absolute;
        font-weight: bold;
        font-size: 24rpx;
        // top: 8rpx;
        right: 28rpx;
        color: #111111;
      }
    }

    .container {
      position: relative;
      height: 50rpx; /* 根据实际需求调整 */
      //   overflow: hidden;
      display: flex;
      //   width: 120rpx;
      padding-right: 24rpx;
    }

    .tiyanBouns {
      //   margin-right: 24rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: absolute;
      left: -80px;
      top: 6rpx;
      transition: 0.5s all;

      opacity: 0;
      transition: opacity 1s ease-in-out, transform 1s ease-in-out;
      transform: translateY(100%);

      image {
        width: 38rpx;
        height: 38rpx;
        margin-right: 7rpx;
      }

      text {
        display: block;
        width: 110rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 22rpx;
        color: #ffffff;
      }
    }

    .tiyanBouns.active {
      opacity: 1;
      transform: translateY(0);
    }

    .tiyanBouns:nth-child(2) {
      text {
        // width: fit-content;
      }

      left: -60px;
      top: 8rpx; /* 调整第二个元素的位置 */
    }

    /* 动画效果 */
    .slide-fade-enter-active,
    .slide-fade-leave-active {
      transition: all 1s ease;
    }

    .slide-fade-enter,
    .slide-fade-leave-to {
      opacity: 0;
      transform: translateY(100%);
    }
  }

  .Kline {
    display: flex;
    // justify-content: center;
    margin-left: 20rpx;
    // height: 320rpx;
    // background-color: #222;
    // border: 1px solid red;
    // margin: 0 0 0  20rpx;
    // border-radius: 26rpx;
  }

  .activeIndex {
    margin-top: 26rpx;
    color: #49ffff;
    border-bottom: 4rpx solid #49ffff;
    padding-bottom: 22rpx; /* 调整下划线与文字的间距 */
  }

  .activeIndex2 {
    margin-top: 26rpx;
    color: #49ffff;
    border-bottom: 4rpx solid #49ffff;
    padding-bottom: 22rpx; /* 调整下划线与文字的间距 */
  }

  .index {
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 25rpx 80rpx 24rpx 45rpx;
    border-bottom: 2rpx solid #434343;

    .inc {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 22rpx;
      display: flex;
      align-items: center;
      line-height: 7rpx;
    }

    .separator {
      width: 3rpx;
      height: 29rpx;
      background-color: #49ffff; /* 蓝色 */
      //margin-right: 37rpx;
    }
  }

  .newProfit {
    padding: 0 23rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;

      image {
        width: 16rpx;
        height: 20rpx;
      }

      font-family: PingFang SC;
      font-weight: 400;
      font-size: 22rpx;
      line-height: 7rpx;

      .addr {
        margin-left: 22rpx;
        color: #ffffff;
      }

      .profits {
        margin-left: 10rpx;

        color: #ff5567;
      }
    }

    .rate {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      //width: 190rpx;

      text {
        line-height: 24rpx;

        &:nth-of-type(1) {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 18rpx;
          color: #ffffff;
          opacity: 0.5;
        }

        &:nth-of-type(2) {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 18rpx;
          color: #ffffff;
        }
      }
    }
  }

  .order {
    margin: 30rpx 20rpx 0rpx 20rpx;

    background: #2b2b2b !important;
    border-radius: 36rpx;

    .head {
      // height: 184rpx;
      padding: 25rpx 37rpx 0rpx 30rpx;

      .notice {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .pro {
          width: 100%;
          margin: 20rpx 62rpx 18rpx -2rpx;
          height: 70rpx;
          background: #1b1b1b;
          border-radius: 14rpx;
          display: flex;
          justify-content: center;
          align-items: center;

          image {
            width: 30rpx;
            height: 30rpx;
          }

          text {
            &:nth-of-type(1) {
              margin: 0 12rpx;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 22rpx;
              color: #ffffff;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              width: 82rpx;
              line-height: 70rpx;
              /* 设置元素宽度，可以根据需要调整 */
            }

            &:nth-of-type(2) {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 22rpx;
              color: #ec4068;
            }
          }
        }

        .rate {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          width: 190rpx;

          text {
            line-height: 24rpx;

            &:nth-of-type(1) {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 18rpx;
              color: #ffffff;
              opacity: 0.5;
            }

            &:nth-of-type(2) {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: 400;
              font-size: 18rpx;
              color: #ffffff;
            }
          }
        }
      }
    }

    .buy2 {
      // height: 100%;
      width: 100%;
      background-size: 100% 100%;
      background-position: top;
      background-repeat: no-repeat;
      display: flex;
      justify-content: space-between;
      padding: 80rpx 31rpx 52rpx 31rpx;

      .left {
        width: 280rpx;
        height: 550rpx;
        margin-top: 0;

        // border: 1px solid red;
        .parent {
          padding-top: 7rpx;
          position: relative;
          display: flex;
          justify-content: space-between;
          width: 240rpx;

          .Long {
            z-index: 1;
            width: 44rpx;
            height: 44rpx;
            background: #1b1b1b;
            border-radius: 8rpx;
            border: 1rpx solid #6cff8a;
            position: absolute;
            top: 0rpx;
            // left: -100rpx;
            font-weight: bold;
            font-size: 23rpx;
            color: #6cff8a;

            display: flex;
            justify-content: center;
            align-items: center;
          }

          .short {
            width: 44rpx;
            height: 44rpx;
            background: #1b1b1b;
            border-radius: 8rpx;
            border: 1rpx solid #ec4068;

            position: absolute;
            top: 0rpx;
            right: -30rpx;
            color: #ec4068;

            font-weight: bold;
            font-size: 23rpx;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .left {
            min-width: 80rpx;
            max-width: 140rpx;
            // width: 140rpx;
            margin-left: 45rpx;
            // width: 1130rpx;
            height: 0;
            border-right: 30rpx solid transparent;
            border-bottom: 30rpx solid #578456;
            display: flex;
            justify-content: flex-start;

            // align-items: center;
            text {
              line-height: 30rpx;
              // margin-left: 32rpx;
              margin-left: 7rpx;
              font-weight: 400;
              font-size: 22rpx;
              color: #6cff8a;
            }
          }

          .right {
            min-width: 80rpx;
            max-width: 140rpx;

            // width: 100rpx;
            position: absolute;
            // top: -7rpx;
            right: -0;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 0;
            border-left: 30rpx solid transparent;
            border-top: 30rpx solid #8b3748;

            text {
              margin-right: 24rpx;
              margin-bottom: 30rpx;
              font-weight: 400;
              font-size: 22rpx;
              color: #ec4068;
            }
          }
        }

        .buyorder {
          height: 200rpx;
          margin-top: 36rpx;
          display: flex;
          flex-direction: column;
          width: 100%;
          justify-content: flex-end;

          .item {
            margin-bottom: 12rpx;
            width: 270rpx !important;
            display: flex;
            // justify-content: space-between;
            position: relative;

            > text {
              position: absolute;
              right: 4rpx;
              top: 1rpx;
              margin-top: 5rpx;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              float: right;
              font-weight: 400;
              font-size: 22rpx;
              color: #ffffff;
            }

            > view {
              width: 390rpx;
              height: 30rpx;
              display: flex;
              align-items: center;
              border-radius: 0px 15rpx 15rpx 0px;
              font-weight: 400;
              font-size: 22rpx;
              background: #458051;
              color: #6cff8a;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              text {
                margin-left: 4rpx;
              }
            }
          }
        }

        .mid {
          margin: 12rpx 0 24rpx 0;
          display: flex;
          justify-content: center;
          //
          font-weight: bold;
          font-size: 32rpx;
        }

        .sellorder {
          height: 200rpx;
          justify-content: flex-start;
          // margin-top: 36rpx;
          display: flex;
          flex-direction: column;
          width: 100%;

          .item {
            margin-bottom: 12rpx;
            width: 270rpx !important;
            display: flex;
            align-items: center;
            // justify-content: space-between;
            position: relative;

            > text {
              position: absolute;
              right: 4rpx;
              top: 1rpx;

              margin-top: 5rpx;
              float: right;
              font-weight: 400;
              font-size: 22rpx;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              color: #ffffff;
            }

            > view {
              width: 390rpx;
              height: 30rpx;
              display: flex;
              align-items: center;
              border-radius: 0px 15rpx 15rpx 0px;
              font-weight: 400;
              font-size: 22rpx;
              background: #833645;
              color: #dd3e63;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              text {
                margin-left: 4rpx;
              }
            }
          }
        }
      }

      .right {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        width: 100%;
        // height: 300rpx;

        // border: 1px solid red;
        .Trading_pair-tab {
          // margin: 39rpx 27rpx 0 21rpx;

          font-weight: bold;
          font-size: 28rpx;
          color: #555555;

          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          border-radius: 30rpx;
          width: 100%;
          background: #1b1b1b;

          .item {
            display: flex;
            align-items: center;
            justify-content: center;
            // text-align: center;
            width: 373rpx;
            height: 29rpx;
            position: relative;
            z-index: 2;

            text {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: bold;
              font-size: 28rpx;
              // color: #555555;
              //   color: #464646;
              // line-height: 60rpx;
            }

            .item {
              &:nth-of-type(1) {
                border-right: none;
              }

              &:nth-of-type(2) {
                border-left: none;
              }
            }
          }

          .bgleft {
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            transition: all 0.5s;
            width: 324rpx;
            height: 60rpx;
            border-radius: 50rpx;
          }
        }

        .left2 {
          margin-left: 324rpx;
        }

        .acleft {
          font-weight: bold;
          font-size: 28rpx;
          color: #141816;
        }

        .tips {
          margin: 14rpx 0 18rpx 10rpx;
          width: 310rpx;
          height: 44rpx;
          background: #1b1b1b;
          border-radius: 8rpx;
          display: flex;
          align-items: center;

          image {
            width: 22rpx;
            height: 22rpx;
            margin: 0 6rpx 0 12rpx;
          }

          text {
            margin-top: 2rpx;
            // line-height: 44rpx;
            font-weight: 400;
            font-size: 20rpx;
          }
        }

        .prices {
          margin-top: 39rpx;
          // height: 100%;
          display: flex;

          .left {
            background: #1b1b1b;
            border-radius: 15rpx;
            width: 451rpx;
            height: 64rpx;
            border: 1rpx solid #ffffff;
            display: flex;
            //flex-direction: column;
            align-items: center;
            // padding-top: 4rpx;
            justify-content: flex-start;

            .inputs {
              width: 260rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              // margin-left: 40rpx;

              text {
                font-family: PingFang SC;
                font-weight: 500;
                color: #ffffff;
                line-height: 7px;
                margin-top: 4rpx;
                // transition: margin-right 0.3s;
                /* 添加动画效果 */
                font-size: 28rpx;
              }
            }

            > text {
              &:nth-of-type(1) {
                //font-weight: 400;
                //font-size: 18rpx;
                //color: #a6a6a6;

                font-family: PingFang SC;
                margin-left: 23rpx;
                font-weight: 500;
                color: #ffffff;
                line-height: 7px;
                // transition: margin-right 0.3s;
                /* 添加动画效果 */
                font-size: 28rpx;
              }

              &:nth-of-type(2) {
                font-weight: 400;
                font-size: 28rpx;
                line-height: 38rpx;
                color: #ffffff;
              }
            }

            input {
              width: 400rpx;
              font-weight: 400;
              font-size: 28rpx;
              color: #ffffff;
            }
          }

          .right {
            width: 165rpx;
            height: 64rpx;
            background: #1b1b1b;
            border-radius: 15rpx;
            border: 1rpx solid #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            text {
              font-weight: 400;
              font-size: 24rpx;
              color: #ffffff;
              margin-right: 6rpx;
            }

            image {
              margin-top: 4rpx;
              width: 14rpx;
              height: 12rpx;
            }
          }

          .float {
            position: absolute;
            left: 50rpx;
            padding: 22rpx 0rpx;
            display: flex;
            background: #1b1b1b;
            border-radius: 15rpx;
            border: 1px solid #ffffff;
            align-items: center;
            flex-direction: column;
            justify-content: space-between;
            width: 165rpx;
            height: 138rpx;

            text {
              font-weight: 400;
              font-size: 24rpx;
              color: #ffffff;
            }
          }
        }

        .balance {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-top: 30rpx;

          font-family: PingFang SC;
          font-weight: 400;
          font-size: 22rpx;
          line-height: 7rpx;

          .lefts {
            margin-right: 10rpx;
            //font-weight: 400;
            //font-size: 18rpx;
            color: #ffffff;
            //color: #a6a6a6;
          }

          > .rights {
            //font-weight: 400;
            //font-size: 18rpx;
            //color: #ffffff;
            color: #ffffff;
          }

          .inside {
            color: #ec4068;
          }
        }

        .amount {
          background: #1b1b1b;
          border-radius: 15rpx;
          border: 1px solid #ffffff;
          margin-top: 39rpx;
          height: 64rpx;
          // border: 1rpx solid red;

          display: flex;
          //flex-direction: column;
          align-items: center;
          justify-content: flex-start;

          > text {
            font-family: PingFang SC;
            margin-left: 23rpx;
            font-weight: 500;
            color: #ffffff;
            line-height: 7px;
            // transition: margin-right 0.3s;
            /* 添加动画效果 */
            font-size: 28rpx;
          }

          view {
            width: 80%;
            margin-left: 51rpx;
            // margin-left: 46rpx;
            display: flex;
            /* 使用 Flexbox 布局 */
            align-items: center;
            /* 垂直居中 */
            // justify-content: center; /* 水平居中 */
            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;

            text {
              margin-right: 10rpx;
            }
          }

          input {
            text-align: left;
            /* 文本左对齐 */
            //width: 900rpx;
            /* 设置宽度为自动，根据输入内容变化 */
            min-width: 50rpx;
            /* 设置最小宽度 */
            //max-width: 200rpx;
            /* 设置最大宽度 */
            outline: none;
            /* 移除默认的聚焦样式 */

            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;
            // margin-left: -300rpx;
            // border: 1px solid red;
          }
        }
        
        .sliderBox {
          margin: 15rpx 0 27rpx 0;
          //text-align: center;
          // display: flex;
          // align-items: center;
          // justify-content: space-between;
          position: relative;

          > text {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 7rpx;

            //font-weight: 400;
            //font-size: 18rpx;
            //color: #a6a6a6;
          }

          .slidelevel {
            margin-top: 26rpx;
            width: 100%;
            display: flex;
            justify-content: space-between;
            flex: 1;
            font-weight: 400;
            font-size: 28rpx;
            color: #a6a6a6;
            text {
              display: block;
              width: 50rpx;
              // margin: 0 10rpx 0 10rpx;
            }
          }
        }

        .sliderTooltip {
          max-width: 500px;
          color: #333 !important;
          line-height: 18px !important;
          border: 1px solid #ebeef5 !important;
        }

        .btn {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 18rpx;
          height: 70rpx;
          border-radius: 50rpx;

          text {
            font-weight: bold;
            font-size: 28rpx;
            color: #141816;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          }
        }

        .nomoney {
        }
      }
    }

    .buy {
      // height: 100%;
      width: 100%;
      background-size: 100% 100%;
      background-position: top;
      background-repeat: no-repeat;
      display: flex;
      justify-content: space-between;
      padding: 60rpx 30rpx 10rpx 30rpx;

      .left {
        width: 280rpx;
        height: 550rpx;
        margin-top: 0rpx;

        // border: 1px solid red;
        .parent {
          padding-top: 7rpx;
          position: relative;
          display: flex;
          width: 240rpx;
          display: flex;
          justify-content: space-between;

          .Long {
            z-index: 1;
            width: 44rpx;
            height: 44rpx;
            background: #1b1b1b;
            border-radius: 8rpx;
            border: 1rpx solid #6cff8a;
            position: absolute;
            top: 0rpx;
            // left: -100rpx;
            font-weight: bold;
            font-size: 23rpx;
            color: #6cff8a;

            display: flex;
            justify-content: center;
            align-items: center;
          }

          .short {
            width: 44rpx;
            height: 44rpx;
            background: #1b1b1b;
            border-radius: 8rpx;
            border: 1rpx solid #ec4068;

            position: absolute;
            top: 0rpx;
            right: -30rpx;
            color: #ec4068;

            font-weight: bold;
            font-size: 23rpx;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .left {
            min-width: 80rpx;
            max-width: 140rpx;
            // width: 140rpx;
            margin-left: 45rpx;
            // width: 1130rpx;
            height: 0;
            border-right: 30rpx solid transparent;
            border-bottom: 30rpx solid #578456;
            display: flex;
            justify-content: flex-start;

            // align-items: center;
            text {
              line-height: 30rpx;
              // margin-left: 32rpx;
              margin-left: 7rpx;
              font-weight: 400;
              font-size: 22rpx;
              color: #6cff8a;
            }
          }

          .right {
            min-width: 80rpx;
            max-width: 140rpx;

            // width: 100rpx;
            position: absolute;
            // top: -7rpx;
            right: -0;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 0;
            border-left: 30rpx solid transparent;
            border-top: 30rpx solid #8b3748;

            text {
              margin-right: 24rpx;
              margin-bottom: 30rpx;
              font-weight: 400;
              font-size: 22rpx;
              color: #ec4068;
            }
          }
        }

        .buyorder {
          height: 200rpx;
          margin-top: 36rpx;
          display: flex;
          flex-direction: column;
          width: 100%;
          justify-content: flex-end;

          .item {
            margin-bottom: 12rpx;
            width: 270rpx !important;
            display: flex;
            // justify-content: space-between;
            position: relative;

            > text {
              position: absolute;
              right: 4rpx;
              top: 1rpx;
              margin-top: 5rpx;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              float: right;
              font-weight: 400;
              font-size: 22rpx;
              color: #ffffff;
            }

            > view {
              width: 390rpx;
              height: 30rpx;
              display: flex;
              align-items: center;
              border-radius: 0px 15rpx 15rpx 0px;
              font-weight: 400;
              font-size: 22rpx;
              background: #458051;
              color: #6cff8a;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              text {
                margin-left: 4rpx;
              }
            }
          }
        }

        .markprice {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16rpx;
          color: #ffffff;
          line-height: 7rpx;
          margin-top: 8rpx;
        }

        .mid {
          margin: 12rpx 0 24rpx 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-family: PingFang SC;
          font-weight: 800;
          font-size: 34rpx;
        }

        .sellorder {
          height: 200rpx;
          justify-content: flex-start;
          // margin-top: 36rpx;
          display: flex;
          flex-direction: column;
          width: 100%;

          .item {
            margin-bottom: 12rpx;
            width: 270rpx !important;
            display: flex;
            align-items: center;
            // justify-content: space-between;
            position: relative;

            > text {
              position: absolute;
              right: 4rpx;
              top: 1rpx;

              margin-top: 5rpx;
              float: right;
              font-weight: 400;
              font-size: 22rpx;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              color: #ffffff;
            }

            > view {
              width: 390rpx;
              height: 30rpx;
              display: flex;
              align-items: center;
              border-radius: 0px 15rpx 15rpx 0px;
              font-weight: 400;
              font-size: 22rpx;
              background: #833645;
              color: #dd3e63;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

              text {
                margin-left: 4rpx;
              }
            }
          }
        }
      }

      .right {
        margin-top: 0rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        margin-left: 33rpx;
        width: 330rpx;
        // height: 300rpx;

        // border: 1px solid red;
        .Trading_pair-tab {
          // margin: 39rpx 27rpx 0 21rpx;

          font-weight: bold;
          font-size: 28rpx;
          color: #555555;

          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          border-radius: 30rpx;
          width: 330rpx;
          background: #1b1b1b;

          .item {
            display: flex;
            align-items: center;
            justify-content: center;
            // text-align: center;
            width: 373rpx;
            height: 29rpx;
            position: relative;
            z-index: 2;

            text {
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
              font-weight: bold;
              font-size: 28rpx;
              // color: #555555;
              //   color: #464646;
              // line-height: 60rpx;
            }

            .item {
              &:nth-of-type(1) {
                border-right: none;
              }

              &:nth-of-type(2) {
                border-left: none;
              }
            }
          }

          .bgleft {
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            transition: all 0.5s;
            width: 160rpx;
            height: 60rpx;
            border-radius: 50rpx;
          }
        }

        .left2 {
          margin-left: 170rpx;
        }

        .acleft {
          font-weight: bold;
          font-size: 28rpx;
          color: #141816;
        }

        .tips {
          margin: 14rpx 0 18rpx 10rpx;
          width: 310rpx;
          height: 44rpx;
          background: #1b1b1b;
          border-radius: 8rpx;
          display: flex;
          align-items: center;

          image {
            width: 22rpx;
            height: 22rpx;
            margin: 0 6rpx 0 12rpx;
          }

          text {
            margin-top: 2rpx;
            // line-height: 44rpx;
            font-weight: 400;
            font-size: 20rpx;
          }
        }

        .prices {
          // height: 100%;
          display: flex;
          gap: 13rpx;

          .left {
            // width: 180rpx;
            width: 100%;

            height: 76rpx;
            //background: rgba(255, 255, 255, 0.2);
            //border-radius: 18rpx;
            border: 1rpx solid #ffffff;
            background: #1b1b1b;
            overflow: hidden;
            border-radius: 15rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            // padding-top: 4rpx;
            justify-content: center;

            .inputs {
              width: 114rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              // margin-left: 40rpx;
              text {
                // transition: margin-right 0.3s;
                /* 添加动画效果 */
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #ffffff;
              }
            }

            > text {
              &:nth-of-type(1) {
                font-weight: 400;
                font-size: 18rpx;
                color: #a6a6a6;
              }

              &:nth-of-type(2) {
                font-weight: 400;
                font-size: 28rpx;
                line-height: 38rpx;
                color: #ffffff;
              }
            }

            input {
              width: 100rpx;
              font-weight: 400;
              font-size: 28rpx;
              color: #ffffff;
            }
          }

          .rights {
            // width: 140rpx;
            height: 76rpx;
            width: 100%;
            border: 1rpx solid #ffffff;
            background: #1b1b1b;
            overflow: hidden;
            border-radius: 15rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            text {
              font-weight: 400;
              font-size: 24rpx;
              color: #ffffff;
              margin-right: 6rpx;
            }

            image {
              margin-top: 4rpx;
              width: 14rpx;
              height: 12rpx;
            }
          }

          .float {
            position: absolute;
            right: 50rpx;
            padding: 22rpx 0rpx;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: space-between;
            width: 140rpx;
            height: 138rpx;
            border: 1rpx solid #ffffff;
            background: #1b1b1b;
            overflow: hidden;
            border-radius: 15rpx;

            text {
              font-weight: 400;
              font-size: 24rpx;
              color: #ffffff;
            }
          }
        }

        .balance {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 20rpx;

          .lefts {
            font-weight: 400;
            font-size: 18rpx;
            color: #fff;
          }

          > .rights {
            font-weight: 400;
            font-size: 18rpx;
            color: #ffffff;
          }

          .inside {
            color: #ec4068;
          }
        }
        .makeorer {
          display: flex;
          align-items: center;
          gap: 13rpx;
        }
        .amount {
          width: 100%;
          margin-top: 20rpx;
          height: 76rpx;
          //background: rgba(255, 255, 255, 0.2);
          //border-radius: 18rpx;
          // border: 1rpx solid red;

          border: 1rpx solid #ffffff;
          background: #1b1b1b;
          overflow: hidden;
          border-radius: 15rpx;

          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .Bouns {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 22rpx;
            color: #ffffff;
          }
          > text {
            font-weight: 400;
            font-size: 18rpx;
            color: #a6a6a6;
          }

          view {
            // margin-left: 46rpx;
            display: flex;
            /* 使用 Flexbox 布局 */
            align-items: center;
            /* 垂直居中 */
            // justify-content: center; /* 水平居中 */
            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;

            text {
              // margin-right: 10rpx;
            }
          }

          input {
            text-align: left;
            /* 文本左对齐 */
            width: 100rpx;
            /* 设置宽度为自动，根据输入内容变化 */
            min-width: 50rpx;
            /* 设置最小宽度 */
            max-width: 200rpx;
            /* 设置最大宽度 */
            outline: none;
            /* 移除默认的聚焦样式 */

            font-weight: 400;
            font-size: 28rpx;
            color: #ffffff;
            // margin-left: -300rpx;
            // border: 1px solid red;
          }
        }

        .sliderBox {
          margin-top: 10rpx;
          text-align: center;
          // display: flex;
          // align-items: center;
          // justify-content: space-between;
          position: relative;

          > text {
            font-weight: 400;
            font-size: 18rpx;
            color: #a6a6a6;
          }

          .slidelevel {
            margin-top: 20rpx;
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-weight: 400;
            font-size: 20rpx;
            color: #a6a6a6;
            text-align: center;
            text {
              &:first-child {
                margin: 0;
              }
              &:nth-of-type(2) {
                // #ifdef H5
                margin: 0 10rpx 0 20rpx;
                // #endif

                // #ifndef H5
                margin: 0 10rpx 0 20rpx;
                // #endif
              }

              &:nth-of-type(3) {
                margin: 0 10rpx 0 5rpx;
              }
              &:nth-of-type(4) {
                margin: 0 0rpx 0 0rpx;
              }
              &:nth-of-type(5) {
                // #ifdef H5

                margin: 0 0 0 10rpx;

                // #endif
                // #ifndef H5
                margin: 0 0 0 8rpx;

                // #endif
              }
              &:last-child {
                margin-right: 0rpx;
              }
              display: inline-block;
              // width: 40rpx;
              // margin: 0 10rpx 0 10rpx;
            }
          }
        }

        .sliderTooltip {
          max-width: 500px;
          color: #333 !important;
          line-height: 18px !important;
          border: 1px solid #ebeef5 !important;
        }

        .btn {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 18rpx;
          height: 70rpx;
          border-radius: 50rpx;

          text {
            font-weight: bold;
            font-size: 28rpx;
            color: #141816;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          }
        }

        .nomoney {
        }
      }
    }
  }
}
.box {
  min-height: 800rpx;
  padding: 120rpx 20rpx 104rpx 20rpx;
  overflow-y: auto;
  .box-item {
    display: flex;
    background: #434343;
    border-radius: 15rpx;
    height: 153rpx;
    margin-bottom: 18rpx;

    .right {
      padding: 27rpx 20rpx 32rpx 24rpx;
      display: flex;
      width: calc(100% - 173rpx);
      align-items: center;
      justify-content: space-between;
      position: relative;

      .ball {
        position: absolute;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        top: 59rpx;
        right: -15rpx;
        background: transparent;
        z-index: 1;
        background: #2b2b2b;
      }

      .item-left {
        display: flex;
        flex-direction: column;

        .top {
          display: flex;
          align-items: center;

          > text {
            font-family: HarmonyOS Sans SC;
            font-weight: 500;
            font-size: 32rpx;
            color: #ffffff;
          }

          > view {
            margin-left: 20rpx;
            width: 71rpx;
            height: 25rpx;
            border-radius: 13rpx;
            border: 1rpx solid #ffffff;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 18rpx;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .obj {
          margin: 18rpx 0;
        }

        .obj,
        .timeexp {
          font-family: HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 20rpx;
          color: #ffffff;
        }
      }

      .item-right {
        width: 148rpx;
        height: 50rpx;
        // padding: 14rpx 26rpx 13rpx 29rpx;
        background: #49ffff;
        border-radius: 25rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #000000;
      }
    }

    .left {
      width: 173rpx;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1rpx dashed #fff;
      position: relative;

      .ball {
        position: absolute;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        top: 59rpx;
        left: -15rpx;
        background: transparent;
        z-index: 1;
        background: #2b2b2b;
      }

      .symbol {
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #43f6ec;
      }

      .num {
        margin-left: 8rpx;
        font-family: HarmonyOS Sans SC;
        font-weight: 900;
        font-size: 46rpx;
        color: #43f6ec;
      }
    }
  }
}
.Bouns1 {
  font-weight: 500 !important;
  font-size: 18rpx !important;
  color: #ffffff !important;

}
.Bouns2 {
  font-weight: bold !important;
  font-size: 28rpx !important;
  color: #ffffff !important;
  margin-top: -20rpx !important;
}
.gray {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  opacity: 1 !important;
  background: #6b6b6b !important;
  border-radius: 15rpx !important;
  border: none !important;
}
.fast-modal-content {
  background: #2b2b2b;
  border-radius: 35rpx;
  // margin: 0 35rpx;
  .right_close {
    position: absolute;
    right: 0rpx;
    top: 0rpx;

    image {
      width: 80rpx;
    }
  }
}
.nav_login {
  width: 300rpx;
  height: 90rpx;
  background: var(--main-bg-color) FFF;
  border-radius: 24rpx;
  border: 2rpx solid #63eaee;
  color: #63eaee;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;
  font-weight: 600;
}
.nodata {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  image {
    width: 240rpx;
    height: 240rpx;
  }

  text {
    margin-top: 10rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
  }
}
page {
  background: #111 !important;
}
.tiyan1 {
  // position: fixed;

  width: 254rpx;
  height: 92rpx;
}
.tiyan {
  // position: fixed;

  width: 173rpx;
  height: 138rpx;
}
.tiyanx {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  left: 150rpx;
  top: -26rpx;
}
.tiyanx2 {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  left: 220rpx;
  top: -0rpx;
}
.time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.43);
  border-radius: 20rpx;
  height: 39rpx;
  margin: 0 22rpx 40rpx 22rpx;

  text {
    //background-color: #2b2b2b;
    padding: 19rpx 19rpx;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    //color: #FFFFFF;
    line-height: 7rpx;
  }
}

/* 动画效果 */
.expand-slide-enter-active,
.expand-slide-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 打开时的初始状态，缩小并从右上角开始 */
.expand-slide-enter {
  opacity: 0;
  transform: scale(0.8) translate(50%, -50%);
}

/* 关闭时的最终状态，收缩回到右上角 */
.expand-slide-leave-to {
  opacity: 0;
  transform: scale(0.5) translate(50%, -50%);
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 10; /* 确保蒙版在最上层 */
}

.mainindex {
  height: 100%;
  width: 100%;
  background: #111;
  padding: 88rpx 34rpx 0 28rpx;

  .comp {
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 36rpx;
    color: #49ffff;
    line-height: 7rpx;

    &-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 67rpx;
      align-items: center;

      .img {
        image {
          width: 38rpx;
        }

        width: 15%;
      }

      .name {
        width: 22%;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26rpx;
        color: #ffffff;
        display: flex;
        justify-content: flex-start;
      }
      .price-box {
        text-align: center;
        margin-top: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        width: 30%;
        .bom {
          margin-top: 10rpx;

          font-family: PingFang SC;
          font-weight: 400;
          font-size: 22rpx;
          color: #ffffff;
        }
      }
      .price {
        // width: 25%;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 24rpx;
        display: flex;
        justify-content: flex-end;
      }

      .change {
        display: flex;
        justify-content: flex-end;
        width: 15%;
        // width: fit-content;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18rpx;
      }
    }

    &-item:last-child {
      margin-bottom: 0;
    }
  }
}

.helpoption {
  z-index: 11;
  position: absolute;
  top: 70rpx;
  right: 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 10px;
  opacity: 1;
  //padding: 100rpx;
  width: 382rpx;
  // height: 446rpx;
  display: flex;
  align-items: center;

  > view {
    .Roptions {
      margin-bottom: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 345rpx;
      height: 79rpx;
      background: #1b1b1b;
      border-radius: 40rpx;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 7rpx;
      position: relative;

      .Rimg {
        position: absolute;
        top: 20rpx;
        left: 19rpx;
        width: 43rpx;
        height: 43rpx;
      }
    }
  }
}

.isapp {
  // transform: scale(0.8);
  // transform-origin: top left;
  background: #111;
}

.wrap {
  padding: 30rpx;
}

.badge-button {
  z-index: 99;
  width: 20rpx;
  height: 20rpx;
  background: #ffffff;
  border: 2rpx solid #555555;
  border-radius: 50%;
}

.timeActive {
  background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
  font-weight: 800;
  font-size: 24rpx;
  color: #000000;
  line-height: 7rpx;
  border-radius: 20rpx;
}

.modalpro {
  width: 360rpx;
  height: 420rpx;
  background: #2b2b2b;
  border-radius: 36rpx;
  display: flex;
  flex-direction: column;

  align-items: center;
  // justify-content: center;
  // padding: 43rpx 33rpx 40rpx 39rpx;

  .modalhead {
    margin-top: 43rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }

    text {
      line-height: 34rpx;
      display: block;
      /* 确保文本块级显示 */
      margin-left: 12rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #ffffff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 240rpx;
      // width: fit-content;
    }
  }

  .profit {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 37rpx;

    text {
      &:nth-of-type(1) {
        font-weight: 400;
        font-size: 26rpx;
        color: #ec4068;
      }

      &:nth-of-type(2) {
        margin-top: 5rpx;
        line-height: 58rpx;
        font-weight: 400;
        font-size: 44rpx;
        color: #ec4068;
      }
    }
  }

  .bom {
    width: 100%;
    padding: 33rpx 33rpx 0 39rpx;

    > view {
      margin-bottom: 20rpx;
      display: flex;
      justify-content: space-between;

      text {
        &:nth-of-type(1) {
          line-height: 32rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #a6a6a6;
        }

        &:nth-of-type(2) {
          line-height: 32rpx;

          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
        }
      }
    }
  }
}

::v-deep .el-slider__stop {
  width: 10rpx;
  height: 10rpx;
  background: #959595;
  // margin: -5rpx;
  border-radius: 50%;
  margin-top: -3.8rpx;
  // border-left: 4rpx solid #fff;
  // border-right: 4rpx solid #fff;
}

::v-deep .el-slider__button {
  width: 20rpx;
  height: 20rpx;
  background: #ffffff;
  border: 2px solid #555555;

  margin-top: -8rpx;
  border-radius: 50%;
  // border: 6rpx solid $down-color;
  box-sizing: border-box;
}

::v-deep .el-slider__bar {
  background: #959595;
  height: 5rpx;
}

::v-deep .el-slider__runway {
  background: #959595;
  height: 5rpx;
  // margin-top: -5rpx;
}

page {
  background-color: #111;
}

::v-deep .u-slider {
}

::v-deep .guidance_body {
  width: 100%;
  min-height: 1424rpx;
  // overflow: hidden;
  background-color: transparent;

  .image {
    img {
      width: 100%;
    }
  }
}

.buybg {
  background: linear-gradient(144deg, #53e571 0%, #82e44d 100%);
}

.sellbg {
  background: linear-gradient(144deg, #ff5270 0%, #fb6f46 100%);
}

.nomoneybg {
  background: linear-gradient(90deg, #ffe32f 0%, #feb22f 100%);
}

.nomoney {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  // height: 300rpx;
  margin: 55rpx 0 40rpx 0;

  .bittitle {
    background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
    background-size: 100% 100%;
    width: 420rpx;
    height: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;

    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: bold;
    font-size: 28rpx;
  }

  .bittitle2 {
    margin: 35rpx 0 21rpx 0;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #ffffff;
    opacity: 0.5;
  }

  .bitline {
    height: 1rpx;
    width: 100%;
    background: #53505d;
  }

  .bitbody {
    margin: 29rpx 34rpx;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: left;

    .midcolor {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 26rpx;
      line-height: 44rpx;
      text-align: left;
      color: #63eaee;
    }
  }

  .bitbtn {
    display: flex;
    width: 100%;
    padding: 0 40rpx;
    justify-content: space-between;
    align-items: center;

    view {
      &:nth-of-type(1) {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50rpx;
        border: 1rpx solid #ffffff;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #ffffff;
      }

      &:nth-of-type(2) {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
        border-radius: 40rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #141414;
      }
    }
  }

  text {
    font-weight: bold;
    font-size: 28rpx;
    color: #ffffff;
  }

  .charge {
    margin-top: 61rpx;
    width: 300rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
    border-radius: 40rpx;
    text-align: center;
    line-height: 80rpx;
    font-weight: bold;
    font-size: 24rpx;
    color: #141414;
  }
}
.closenotice {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  // height: 300rpx;
  margin: 48rpx 0 62rpx 0;

  .bittitle {
    background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
    background-size: 100% 100%;
    width: 226rpx;
    height: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;

    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: bold;
    font-size: 28rpx;
  }

  .bitline {
    margin: 50rpx 0 0rpx 0;
    height: 1rpx;
    width: 100%;
    background: #53505d;
  }
  .midcolor {
    text-align: left;

    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 26rpx;
    // line-height: 44rpx;
    text-align: left;
    color: #63eaee;
    margin: 41rpx 0 58rpx 0;
  }
  .bitbody {
    // margin: 29rpx 34rpx;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 26rpx;
    // color: #ffffff;
    line-height: 44rpx;
    text-align: left;
  }

  .bitbtn {
    display: flex;
    width: 100%;
    padding: 0 40rpx;
    justify-content: space-between;
    align-items: center;

    view {
      &:nth-of-type(1) {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50rpx;
        border: 1rpx solid #ffffff;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #ffffff;
      }

      &:nth-of-type(2) {
        width: 220rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
        border-radius: 40rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #141414;
      }
    }
  }

  text {
    font-weight: bold;
    font-size: 28rpx;
    color: #ffffff;
  }

  .charge {
    margin-top: 61rpx;
    width: 300rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
    border-radius: 40rpx;
    text-align: center;
    line-height: 80rpx;
    font-weight: bold;
    font-size: 24rpx;
    color: #141414;
  }
}
.newuser {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 640rpx;
  text-align: center;
  width: 600rpx;
  background: transparent;

  .card {
    height: 447rpx;
    padding: 0 56rpx;
    width: 100%;
    background: #34323d;
    border-radius: 30rpx;
    position: relative;
  }

  .tops {
    z-index: 1;
    position: absolute;
    top: -127rpx;
    left: 168rpx;

    width: 261rpx;
    height: 218rpx;
  }

  .info {
    margin-top: 115rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .titles {
      font-weight: bold;
      font-size: 34rpx;
      color: #63eaee;
    }

    .money {
      background: linear-gradient(144deg, #ef91fb 0%, #40f8ec 100%);
      border-radius: 30%;
      width: 80rpx;
      height: 80rpx;
      margin-left: 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .grey {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 30%;
        background: #34323d;
        width: 76rpx;
        height: 76rpx;
      }

      image {
        height: 70rpx;
        width: 70rpx;
        border-radius: 30%;
      }
    }

    .tips {
      margin-left: 20rpx;

      font-weight: bold;
      font-size: 28rpx;
      color: #ffffff;
    }
  }

  .msg {
    // width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    margin-top: 25rpx;

    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 38rpx;
    // width: 256rpx;
  }

  .btn {
    margin: 0 auto;
    margin-top: 36rpx;
    width: 300rpx;
    height: 80rpx;
    background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
    border-radius: 40rpx;
    font-weight: bold;
    font-size: 24rpx;
    line-height: 80rpx;

    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    color: #141816;
  }
}

.rightscale {
  width: 0;
  height: 0;
  transition: 3.5s;
  transform: scale(0.1, 0.1);
  transform: translate(100px, 200px);
  -webkit-transform: translate(100px, 200px);
  -moz-transform: translate(100px, 200px);
}

.BigOrder {
  padding: 60rpx;

  .top {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 40rpx;
  }

  .bombtn {
    display: flex;
    flex-direction: column;

    .sure,
    .canl {
      display: flex;
      // justify-content: space-between;
      padding: 15rpx 18rpx 18rpx 17rpx;
      height: 90rpx;
      background: #34323d;
      border-radius: 45rpx;
      border: 1rpx solid #ffffff;

      image {
        width: 56rpx;
        height: 56rpx;
        margin-right: 12rpx;
      }

      .btnright {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        text {
          &:nth-of-type(1) {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            font-size: 24rpx;
            color: #ffffff;
          }

          &:nth-of-type(2) {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 22rpx;
            color: #959595;
          }
        }
      }
    }
  }
}

.input-error {
  border: 1px solid #ec4068;
}

/* 警告文字样式，黄色字体 */
.warning-text {
  color: #ec4068;
  font-size: 18rpx;
  /* 根据需要调整字体大小 */
  margin-top: 5rpx;
  /* 设置适当的间距 */
}

.normal-border {
  border: 1rpx solid #ffffff;
}
