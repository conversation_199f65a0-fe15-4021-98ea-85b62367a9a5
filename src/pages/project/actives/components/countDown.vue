<template>
  <view class="time-wrap">
    <view 
      class="text" 
      :class="[{ bg: /\d/.test(text), colon: /\:/.test(text) }]" 
      v-for="(text, index) in result" 
      :key="index"
    >
      {{ text }}
    </view>
  </view>
</template>

<script>
  let _TIMER = null;
  
  const M = 60;
  const H = 60 * M;
  const D = 24 * H;
  
  export default {
    props: {
      timestamp: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        countTime: 0,
        result: []
      }
    },
    watch: {
      timestamp() {
        this.destroyTimer()
        this.countTime = this.timestamp;
        this.createTimer()
      },
      countTime(newVal) {
        if(newVal >= 0) {
          
          let count = newVal;
          const d = Math.floor(count / D);
          count -= d * D;
          const h = Math.floor(count / H);
          count -= h * H;
          const m = Math.floor(count / M);
          const s = newVal % M;
          
          
          this.result = [
            ...d.toString().padStart(2, '0').split(''), '天',
            ...h.toString().padStart(2, '0').split(''), ':', 
            ...m.toString().padStart(2, '0').split(''), ':', 
            ...s.toString().padStart(2, '0').split('')
          ]
          
        }
      }
    },
    created() {
      this.countTime = this.timestamp;
      this.createTimer()
    },
    beforeDestroy() {
      this.destroyTimer()
    },
    methods: {
      
      createTimer() {
        _TIMER = setInterval(() => {
          if(this.countTime <= 0) {
            this.$emit('end')
            this.destroyTimer()
          } else {
            this.countTime --;
          }
        }, 1000)
      },
      
      destroyTimer() {
        _TIMER && clearInterval(_TIMER);
        _TIMER && (_TIMER = null);
      }
    }
  }
</script>

<style scoped lang="scss">
  .time-wrap {
    color: #05FCF0;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    
    .text {
      width: 32rpx;
      height: 40rpx;
      line-height: 40rpx;
      text-align: center;
      
      &.bg {
        background: url('/static/imgs/invite/takeOffInviteTime.png') no-repeat;
        background-size: 100% 100%;
      }
      
      &.colon {
        width: 20rpx;
      }
    }
  }
</style>