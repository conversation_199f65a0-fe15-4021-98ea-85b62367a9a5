<template>
  <view class="main">
    
    <view class="banner-wrap">
      <u-swiper :list="banner" :border-radius="25" bg-color="#35333E"></u-swiper>
    </view>

    <view class="u-flex u-flex-col u-col-center">
      <view class="title">让影响力变现</view>
      <view class="">如果您是内容创作者、社区群主、渠道等意见</view>
      <view class="">领袖，添加上方联系方式，申请加入</view>
	  <view class="">BV“开杠吧”活动开拓计划、推广平台</view>
      <view class="">即赚取高额福利。</view>
      
      <view class="button u-m-t-26 u-m-b-50" @click="handleTakeOffRouteTo(0)">
      	<view class="bg">查看我的对战开拓好友</view>
      </view>
      
      <view class="line"></view>
      
      <view class="title">如何赚取开拓收益</view>
      
      <view class="">1.创建链接-加入BV开拓计划后,您页面底部的</view>
      <view class="">邀请链接会成为默认链接。生成专属邀请链接。</view>
      <view class="">节点也可创建新链接,并给您和好友分配开拓</view>
      <view class="">收益比例。</view>
      
      <view class="u-m-t-50"></view>
      
      <view class="">2.好友加入并交易-新用户通过链接注册并完成交易。</view>
      
      <view class="u-m-t-50"></view>
      <view class="">3.分享链接-活动期间拥有"B"、"V"空投的用户或加入</view>
      <view class="">开拓计划的白名单用户才拥有分享邀请资格。</view>
      <view class="">快去将邀请链接或邀请码分享给好友或粉丝，</view>
	  <view class="">或在社交媒体以及其他渠道进行推广吧。</view>
    </view>
    
    <view class="fixed-footer" @click="copy()">
      立即邀请
    </view>

  </view>
</template>

<script>
  import api from '@/common/api/index.js';
  export default {
    data() {
      return {
        banner: [
          '/static/imgs/invite/takeOffInviteBanner.png'
        ],
		shareUri:"",
		isCopy:false
      };
    },
    onLoad() {
      this.fetchInviteUri()
    },
    methods: {
      async fetchInviteUri() {
        const {
        	status,
        	result
        } = await api.getContractAddress({});
        if (status.code === 0) {
          
          let baseURL = '';
		
          // #ifdef APP
          baseURL = getApp().globalData.url
          // #endif
          
          // #ifdef H5
          baseURL = window.location.origin + '/h5/#'
          // #endif
		
          this.shareUri = `${baseURL}/pages/project/actives/takeOffInvite?takeOffSource=${result}`
		  this.isCopy = true
        } else {
          // uni.showToast({
          // 	title: status.msg,
          // 	icon: 'none',
          // 	duration: 3000
          // });
        }
      },
      
      handleTakeOffRouteTo(type = 0) {
        const pathName = ["takeOffInviteData"]
        
        this.$Router.push({
        	name: pathName[type]
        })
      },
	  copy(){
		  if(this.isCopy){
			  console.log(this.shareUri)
			  uni.setClipboardData({
			  	data: this.shareUri,
			  	success() {
			  		uni.showToast({
			  			title: '复制链接成功！',
			  			icon: 'none'
			  		})
			  	}
			  })
		  }else{
			  uni.showToast({
			  	title: '您还未获得邀新资格！',
			  	icon: 'none'
			  })
		  }
	  }
    }
  }
</script>

<style lang="scss" scoped>
  .main {
    position: relative;
    padding: 36rpx 36rpx 200rpx;
    font-size: 24rpx;
    color: #fff;
    line-height: 1.8;
    
    .banner-wrap {
      padding: 2rpx;
      background-image: linear-gradient(0deg, #EF91FB, #40F8EC);
      border-radius: 25rpx;
    }
    
    .title {
      padding: 50rpx 0;
      font-size: 56rpx;
    }
    
    .line {
      width: 100%;
      height: 2rpx;
      background: #6e6d75;
    }
    
    .button{
    	// width:100%;
    	height:88rpx;
    	border-radius:44rpx;
    	position: relative;
    	/* 添加内边距以避免伪元素覆盖内容 */
    	padding: 4rpx;
    	background:linear-gradient(135deg, #EF91FB, #40F8EC);
      font-size: 34rpx;
      color: #63EAEE;
    	.bg{
        padding: 28rpx 54rpx;
    		background-color:#36343F;
    		height:100%;
    		border-radius:40rpx;
    		display: flex;
    		justify-content: center;
    		align-items: center;
    	}
    }
    
    .fixed-footer {
      position: fixed;
      left: 50%;
      bottom: 90rpx;
      
      width: 520rpx;
      height: 88rpx;
      background:linear-gradient(0deg, #EF91FB, #40F8EC);
      display: flex;
      justify-content: center;
      align-items:center;
      font-size: 32rpx;
      color: #000;
      border-radius: 44rpx;
      transform: translate(-50%, 0)
    }
  }
</style>