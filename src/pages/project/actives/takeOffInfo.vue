<template>
	<view class="main">
		<u-navbar v-if="!isApp" back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title='“开杠吧”活动详解'
			title-color="var(--default-color1)" title-bold>
		</u-navbar>
		<view class="container">
			<view class="content">

				<view class="title u-m-b-10">1.</view>
				<view class="">用户参与对战玩法,并在对战模式中入金大于等于100,将获得以下空投之一</view>

				<view class="u-flex u-row-center u-m-t-30 u-m-b-26">
					<image style="width: 230rpx;" src="/static/imgs/invite/takeOffInfoTitle.png" mode="widthFix">
					</image>
				</view>

				<view class="mark u-m-b-24">(空投在活动期间不会开启寄售,作为活动身份象征。)</view>
				<view class="u-m-b-12">您持有的队伍身份,将导致您邀请的好友入金后,必定会获得对应的身份空投。</view>
				<view class="desc">例:您获得B队空投,邀请好友参与对战模式,该好友入金大于等于100,他将算作您的有效邀请,并且该好友必定获得B队空投。</view>

				<view class="title u-m-b-10 u-m-t-34">2.</view>
				<view class="">当活动时间倒计时结束时,若B队人数多,B队将平分完整奖金蓄水池。当活动时间倒计时结束时,若V队人数多,最后一位加入V队的用户以及他的邀请链上所有的用户,平分奖金蓄水池
				</view>

				<view class="line"></view>

				<view class="title u-m-b-10">活动时间:</view>
				<view class="">
					活动时长为12个小时倒计时,每当有一位新老用户加入到对战玩法中来时,倒计时增加10分钟。在对战页面可查看当前倒计时长。<text
						class="mark">(在对战模式中入金大于等于100,算作加入成功)</text>
				</view>

				<view class="line"></view>

				<view class="title u-m-b-10">活动奖励:</view>
				<view class="u-m-b-10">1.平台奖励,在活动时间段内,平台会将对战模式产生手续费的30%拿出放入蓄水池中，并且放置5w底池奖金。</view>


				<view class="">2. 用户邀请新老用户体验对战玩法可获得邀请好友的入金手续费（2%～15%）返佣。</view>


				<view class="line"></view>

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isApp: false
			};
		},
		onLoad(options) {
			const {
				platform,
				token,
				contract_address
			} = options;
			if (platform) {
				this.isApp = true
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		padding: 36rpx;
		font-size: 24rpx;
		color: #fff;
	}

	.container {
		padding: 2rpx;
		background-image: linear-gradient(0deg, #EF91FB, #40F8EC);
		border-radius: 25rpx;
	}

	.content {
		border-radius: 25rpx;
		padding: 30rpx;
		background: #46454F;
		line-height: 1.3;

		.title {
			font-size: 34rpx;
		}

		.mark {
			color: #7BFFFD;
			font-size: 22rpx;
		}

		.desc {
			font-size: 20rpx;
			color: #a8a8a8;
		}

		.line {
			margin: 30rpx -10rpx;
			height: 2rpx;
			background: #6e6d75;
		}
	}
</style>