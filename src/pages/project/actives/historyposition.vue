<template>
    <view class="body">
        <view class="barHeight"></view>

        <view class="head_top">
            <view class="back" @click="back">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                    alt="" srcset="" />
            </view>
            <view class="tabs_div">
                <view class="left">
                    <u-tabs name="cate_name" bg-color="" :bar-style="barStyle" :list="tabList" bold
                        inactive-color="var(--default-color3)" active-color="var(--default-color1)" :current="current"
                        @change="change"></u-tabs>
                </view>
            </view>
        </view>

        <view class="podetails">
            <!-- 当前委托 List-->
            <view class="weituo" v-show="current == 0">
                <view class="li" v-for="(item, index) in entrustList" :key="index">
                    <!-- -->
                    <view class="heads">
                        <view class="left">
                            <image v-if="item.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix"></image>
                            <!-- -->
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>

                            <text class="level">{{ item.leverageLevel }}X</text>
                            <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                            <text class="sub">{{ item.ctime || '--' }}</text>
                            <!-- {{ item.createAt }} -->
                        </view>
                        <view class="cd">

                            <view class="progress" v-if="item.status != 2 && item.status != 4">
                                <text>{{ dealprogress(item) }}%</text>

                                <view class="bg">
                                    <view class="bar" :style="{ width: (item.dealMoney / item.money) * 100 + '%' }">
                                    </view>
                                </view>
                            </view>
                            <!-- @click="openPop(item, 1, index)" v-if="item.canRevoke" -->

                            <view v-if="item.status != 2 && item.status != 4" class="cancel" @click="backuporder(item)">
                                撤单</view>
                            <view v-if="item.status == 5" class="cancel">
                                <image mode="widthFix" class="rotate-image"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                            </view>
                            <view class="hasdeal" v-if="item.status == 2" style="color: #fff">已成交</view>
                            <view class="hasdeal" v-if="item.status == 4">已撤单</view>

                        </view>
                    </view>
                    <!-- card 底部 -->
                    <view class="data_view">
                        <view class="li_view">
                            <view class="label">成本</view>
                            <view class="num">
                                <text>{{ '￥' + formatNumber(item.payMoney - item.experienceMoney, 3, 'exper') }}</text>
                                <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}({{ item.trailType == 1 ?
                                    '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                                <!-- <text >+￥{{ item.experienceMoney }}(体验金)</text> -->
                            </view>
                        </view>
                        <view class="li_view">
                            <view class="label">成交金额<text v-if="item.status != 2 && item.status != 4">/下单金额</text>
                            </view>
                            <view class="num" v-if="item.status != 2 && item.status != 4">
                                {{ '￥' + formatNumber(item.dealMoney, 3) + ' / ￥' + formatNumber(item.money, 3) }}
                            </view>
                            <view class="num" v-else>{{ '￥' + formatNumber(item.dealMoney, 3) }}</view>
                        </view>
                        <view class="li_view">
                            <view class="label">
                                <text>下单价格</text>
                                <!-- <text v-show="item.longShort == 2">唱空价</text>
                                <text v-show="item.longShort == 1">唱多价</text> -->
                            </view>
                            <!-- <view class="num">￥{{ formatNumber(item.avgPrice, 3) || '--' }}</view> -->
                            <!-- {{ item.price }} -->
                            <view class="num" v-if="item.price">{{ '￥' + item.price.toFixed(3) || '--' }}</view>
                            <view class="num" v-else>{{ '市价' }}</view>

                        </view>
                    </view>
                </view>
                <view class="nodata" v-if="!entrustList.length">
                    <image mode="widthFix"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                    <text>暂时无数据</text>
                </view>
            </view>

            <!-- 持仓 List -->
            <view class="cangwei" v-show="current == 1">
                <!-- <transition name="van-fade"> -->
                <view class="li" v-for="(item, index) in positionsList" :key="index">
                    <!-- :class="{ 'active': item.isActive }" v-for="(item, index) in positionsList"
                    :key="index" -->
                    <view class="heads">
                        <view class="left">
                            <!-- v-if="item.longShort == 2" -->
                            <image v-if="item.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <view class="rightinfo">
                                <text class="level">x {{ item.leverageLevel }}</text>
                                <!-- <text class="times">{{ item.openTime + ' - ' + item.closeTime }}</text> -->
                                <view class="live_data"
                                    :style="{ borderRadius: (item.trailType && item.epProfit != null) ? '20rpx' : '' }">
                                    <view class="sy red" style="display: flex;align-items: center;">
                                        <!-- :class="{ 'red': item.red }" -->
                                        <text class="text">收益</text>
                                        <view
                                            style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">
                                            <text class="text" v-if="item.status == 0" style="margin-left: 4rpx;"
                                                :class="[item.trailType == 2 || item.trailType == 1 ? 'textline' : '']"
                                                :style="{ color: (item.profit) >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                    (item.profit) >= 0 ? '+' : '' }} {{ item.profit.toFixed(2) || 0 }}
                                            </text>
                                            <!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
                                            <text style="color: #fff;"
                                                class="text"
                                                v-if="item.epProfit != null && item.status == 0 && item.trailType == 2">{{
                                                    (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                }}</text>
                                                <!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
                                            <text style="color: #fff;"
                                                class="text"
                                                v-if="item.epProfit != null && item.status == 0 && item.trailType == 1">{{
                                                    (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                }}</text>
                                        </view>

                                        <!-- <text class="text" v-if="item.status == 0 && item.trailType == 2"
                                            :style="{ color: item.epProfit >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                item.epProfit >= 0 ? '+'
                                                    : '' }} {{ item.epProfit.toFixed(2) || 0
                                            }}
                                        </text> -->
                                        <text style="margin-left: 10rpx;"
                                            :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"
                                            v-if="item.status == 1">
                                            {{ calculateEarnings(item) }}
                                        </text>

                                    </view>
                                    <view class="syl red">
                                        <!-- :class="{ 'red': item.red2 }" -->
                                        <text>收益率</text>
                                        <view v-if="item.status == 0"
                                            style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">

                                            <text v-if="item.status == 0"
                                                :class="[item.trailType == 2 || item.trailType == 1 ? 'textline' : '']"
                                                :style="{ color: item.profitRate >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                    item.profitRate >= 0 ? '+'
                                                        : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                            </text>
                                            <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
                                            <text style="color: #fff;"
                                                class="text"
                                                v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 2">{{
                                                    (item.epProfitRate) >= 0 ? '+' : '' }} {{ (accMul(item.epProfitRate,
                                                    100)).toFixed(2) + '%'
                                                }}</text>
                                                <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
                                            <text style="color: #fff;"
                                                class="text"
                                                v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 1">{{
                                                    (item.epProfitRate) >= 0 ? '+' : '' }} {{ (accMul(item.epProfitRate,
                                                    100)).toFixed(2) + '%'
                                                }}</text>
                                        </view>
                                        <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"
                                            v-if="item.status == 1">
                                            {{ calculateYield(item) }}
                                        </text>
                                        <!-- <text v-if="item.status == 0 && item.trailType == 2"
                                            :style="{ color: item.epProfitRate>= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                item.epProfitRate >= 0 ? '+'
                                                    : '' }}{{ (accMul(item.epProfitRate, 100)).toFixed(2) + '%' }}
                                        </text> -->

                                    </view>
                                </view>
                            </view>

                            <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                        </view>
                    </view>
                    <view class="right_fenx">
                        <!-- @click="openShare(item)" -->
                        <image @click="openShare(item)"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240729/0d72a446e4fdb9f704719716b76e3622_72x80.png"
                            alt="" srcset="" mode="widthFix"></image>
                    </view>

                    <view class="qiang" v-if="item.closeType == 2">
                        <image mode="widthFix"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240807/f33447ea72684920c1bebb719a3c79f9_144x64.png" />
                    </view>
                    <view class="qiang ADL" v-if="item.closeType == 3" @click="bitshow = true">
                        ADL
                    </view>

                    <view class="data_views" :style="{ marginTop: item.closeType == 2 ? '12rpx' : '29rpx' }">

                        <view class="li_view">
                            <view class="label">{{ item.status == 0 ? '成本' : '开仓金额' }}</view>
                            <view class="num">
                                <text>{{ '￥' + formatNumber((item.money - item.experienceMoney), 3, 'exper') }}</text>
                                <text v-if="item.experienceMoney"> +￥{{ item.experienceMoney }}({{ item.trailType == 1 ?
                                    '体验金' : item.trailType == 2 ? '万能金' : '体验金' }})</text>
                            </view>
                        </view>

                        <view class="li_view">
                            <view class="label">{{ item.side == 'SELL' ? '唱空均价' : '唱多均价' }}</view>
                            <view class="num">{{ '￥' + formatNumber(item.price, 3) }}</view>
                            <!-- <view>1</view> -->
                        </view>

                        <view class="li_view">
                            <view class="label">{{ item.status == 0 ? positionType(item) : '强平价' }}</view>
                            <view class="num" v-if="item.status == 0">{{ '￥' + formatNumber(item.closePrice, 3) }}
                            </view>
                            <view class="num" v-else>{{ '￥' + formatNumber(item.reducePrice, 3) }}</view>
                            <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                        </view>


                        <view class="li_view">
                            <view class="label">资金费用</view>
                            <!-- color: #6CFF8A;
                            color: #FF5270; -->
                            <view class="num"
                                :style="{ color: item.capitalFee > 0 ? '#FF5270' : (item.capitalFee < 0 ? '#6CFF8A' : '') }">
                                {{
                                    (item.capitalFee > 0 ? '+' : (item.capitalFee < 0 ? '-' : '')) + '￥' +
                                    formatNumber(Math.abs(item.capitalFee), 2, 'fund') }} </view>
                                    <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                            </view>
                        </view>
                        <!-- 
                     :style="{ marginTop: item.experienceMoney > 0 ? '23rpx' : '23rpx' }"
                    <view class="fee" :style="{ marginTop: item.experienceMoney > 0 ? '16rpx' : '26rpx' }"> -->

                        <view :style="{ marginTop: item.experienceMoney > 0 ? '20rpx' : '20rpx' }"
                            style="display: flex;height:100%;justify-content: space-between;align-items: center;">
                            <view class="fee">
                                <text class="fees">开仓手续费: ¥ {{ item.openFee || '--' }}</text>
                                <view style="width: 234rpx;"></view>
                                <text class="time" v-if="item.openTime">
                                    开仓时间： {{ item.openTime }}
                                </text>

                            </view>
                            <view class="potime">
                                <!-- v-if="item.closeFee" -->
                                <view style="width: 64rpx;"></view>
                                <text class="fees" v-if="item.closeFee">平仓手续费: ¥ {{ item.closeFee || 0.22 }}</text>
                                <text v-else>{{ "\xa0" }}</text>

                                <text class="time" v-if="item.closeTime">
                                    平仓时间： {{ item.closeTime }}
                                </text>
                                <text v-else>{{ "\xa0" }}</text>
                            </view>
                        </view>

                        <!-- <view class="close">
                        @click="openPop(item, 2, index)" v-if="item.canClose"
                        <view @tap="setProLoss(item)">设置止盈止损</view>
                        <view @tap="resetProLoss">重置止盈止损</view>
                        <view @tap="nobalance = true" class="unlock">解锁止盈止损</view>
                        <view @tap="closeonce(item)">一键平仓</view>
                    </view> -->

                    </view>
                    <view class="nodata" v-if="!positionsList.length">
                        <image mode="widthFix"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                        <text>暂时无数据</text>
                    </view>
                    <!-- </transition> -->
                </view>
            </view>

            <!-- 止盈止损弹窗 -->
            <u-modal class="" v-model="proLoss" width="710rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">

                <view class="modalpro">
                    <view class="m_head">
                        <view class="left">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240729/2dfb9593a1cd98273aaeccbf45da2168_100x100.png" />
                            <view>
                                <text class="price">价值</text>
                                <text class="num">￥100 <text class="num2"> (+7.83)</text></text>
                            </view>
                        </view>
                        <view class="right">100x</view>
                    </view>
                    <view class="m_line"></view>

                    <view class="m_openclose">
                        <view class="top">
                            <text>开仓价</text>
                            <text>￥100.00</text>
                        </view>
                        <view>
                            <text>止盈价</text>
                            <text class="rb">￥</text>
                            <input v-model="profit" />
                        </view>
                        <view>
                            <text>止损价</text>
                            <text class="rb">￥</text>
                            <input v-model="loss" />
                        </view>

                    </view>

                    <view class="m_btn" @tap="confirmProLoss">
                        确认
                    </view>
                </view>

            </u-modal>


            <!-- 老用户进来邀新 -->
            <!-- <u-modal class="" v-model="oldusershow" width="600rpx" borderRadius="36" :show-title="false"
            :mask-close-able="true" :show-confirm-button="false">
            <transition name="fade" @before-enter="beforeEnter" @enter="enter" @leave="leave">
                <view class="newmoney box" v-if="show">
                    <text>领取体验金任务</text>
                    <text>邀请3位新用户可免费获得￥15体验金</text>
                    <view @click="gomission()" class="anim">去完成</view>
                </view>
            </transition>
        </u-modal> -->

            <!-- 撤单 -->
            <u-modal class="" v-model="cancelOrder" width="600rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="newmoneys">
                    <text>当前下单使用了体验金并已有成交，若撤单则不返还体验金</text>
                    <view class="btns">
                        <view @click="backorder()">撤单</view>
                        <view @click="cancelOrder = false">取消</view>
                    </view>
                </view>
            </u-modal>


            <!-- 分享 -->
            <u-popup v-model="isShare" mode="bottom">
                <view class="share_body">
                    <!-- <view class="back" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                        alt="" srcset="" mode="widthFix"></image>
                </view> -->
                    <view class="cart_div" id="test-id">
                        <view class="title_image">
                            开杠吧
                        </view>
                        <view class="toux_image_div">
                            <view class="toux_border">
                                <view class="image_div">
                                    <view class="gray">
                                        <image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
                                    </view>
                                </view>
                                <!-- <text>{{ shareUser.name }}</text> -->
                                <!-- <text>张三.mata</text> -->
                            </view>
                        </view>
                        <view class="toux_name">
                            {{ shareUser.addr }}
                        </view>

                        <view class="buysell">
                            <image v-if="shareUser.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <text>BIT指数</text>
                        </view>

                        <view class="yield" :style="{ color: shareUser.profit >= 0 ? '#FF5270' : '#6CFF8A' }">
                            <text v-if="shareUser.profit">{{ shareUser.profit >= 0 ? '+' : '' }}{{
                                shareUser.profit.toFixed(2) }}</text>
                        </view>
                        <view class="shouyi" :style="{ color: shareUser.profit >= 0 ? '#FF5270' : '#6CFF8A' }">
                            <text></text>{{ shareUser.profit >= 0 ? '+' : '' }}{{ '(' + accMul(shareUser.profitRate,
                                100) +
                                '%)' }}
                        </view>
                        <view class="info_div">
                            <p>开仓均价：￥{{ shareUser.price }}</p>
                            <!-- <p>卖出价：￥3.45</p> -->
                            <p>平仓均价：￥{{ shareUser.close }}</p>
                        </view>
                        <view class="yqm">
                            邀请码：{{ shareUser.invitationCode }}
                            <image class="copy-img" style="width: 20rpx;height: 22rpx;margin-left: 10rpx;"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240822/86ee3166817515f270891ab2e1973722_40x44.png"
                                mode="widthFix" @click="copy(shareUser.invitationCode)" />
                        </view>
                        <view class="msg_text">
                            扫码注册，及时上车
                        </view>
                        <view class="icon_bg">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <view class="qr_code">
                            <view class="right">
                                <view class="qr_div">
                                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx"
                                        :options="options"></uv-qrcode>
                                </view>
                            </view>
                            <view class="time">分享于{{ shareUser.currentDateTime }}</view>
                        </view>
                    </view>
                    <view class="share_to_div">
                        <!-- #ifdef APP -->
                        <!-- <view class="li" @click="fenx_weixin()">
                        <view class="icon_image">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <p>微信</p>
                    </view> -->
                        <!-- #endif -->

                        <!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
                    </view>
                    <view class="colse_div" @click="isShare = false">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
                            mode="widthFix"></image>
                    </view>
                </view>
            </u-popup>

            <!-- 新用户被邀请盈利 -->
            <!-- <u-popup mode="center" v-model="newuserreward">
            <view class="newuser">
                <view class="card">
                    <image mode="widthFix" class="tops"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

                    <view class="info">
                        <view class="titles">恭喜</view>
                        <view class="money">
                            <image mode="widthFix"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

                        </view>
                        <view class="tips">username</view>

                    </view>

                    <view class="msg">
                        <text :style="{ width: old ? '506rpx' : '260rpx' }">
                            {{ old ? '您已经完成任务，免费获得￥15体验金' : '免费获得￥15体验金' }}
                        </text>
                    </view>

                    <view class="btn" @tap="getbonus">领取</view>
                </view>
            </view>
        </u-popup> -->

            <!-- 加载中loading -->
            <u-modal class="" v-model="isLinkLoadding" width="40%" :show-title="false" :show-confirm-button="false">
                <div class="sk-wave"></div>
                <view class="text_msg" style="
            padding: 10rpx 20rpx 40rpx 20rpx;
            text-align: center;
            font-size: 26rpx;
            line-height: 40rpx;
          ">
                    加载中...
                </view>
            </u-modal>

            <!-- bit指数 -->
            <u-modal class="" v-model="bitshow" width="600rpx" borderRadius="36" :show-title="false"
                :mask-close-able="true" :show-confirm-button="false">
                <view class="nomoney">
                    <text class="bittitle">{{ '&nbsp;&nbsp;' }}ADL（自动减仓机制）</text>
                    <text class="bittitle2">用于在市场极端波动时保护交易平台的稳定性。</text>
                    <view class="bitline"></view>

                    <view class="bitbody">
                        ADL是一种风险管理工具，当市场出现剧烈波动并导致部分用户爆仓时，ADL机制会自动从当前市场上获利最多的用户开始减持他们的仓位，以弥补爆仓带来的影响。
                        这意味着在极端情况下，您可能会被自动减仓。ADL的目的是确保平台整体风险的平衡和市场的正常运作，帮助所有用户维持一个更加安全的交易环境。
                    </view>
                    <view class="bitbtn">
                        <view @click="bitshow = false">返回</view>
                    </view>
                </view>
            </u-modal>
        </view>
</template>

<script>
import uniCopy from "@/js_sdk/uni-copy.js";

import Positions from "./components/position.vue";

export default {
    components: {
        Positions
    },
    onLoad() {
        this.getInfo()

        if (uni.getStorageSync('historyTab')) {
            this.current = uni.getStorageSync('historyTab')
        }
        this.getUserShare()
        // this.getPositionInfo()
        this.fetchPositions()
        this.fetchEntrust()
        this.checkpopups()
    },
    onReachBottom() {
        console.log('333', this.entrusthasnext);

        if (this.current == 0 && this.entrusthasnext) {
            this.tradedPageNum += 1
            this.fetchEntrust()
        } else if (this.current == 1 && this.positionhasnext) {
            this.pageNum += 1
            this.fetchPositions()
        }
    },
    data() {
        return {
            bitshow: false,
            isLinkLoadding: false, // 加载中
            waitorderNowobj: {},// 撤单
            entrusthasnext: false,
            positionhasnext: false,
            unlockLoss: false,
            profit: '',
            loss: '',
            cancelOrder: false, // 撤单
            popupShow: '', // 弹窗类型
            tradedPageNum: 1, //分页
            pageNum: 1, //分页
            old: false,
            newuserreward: false,
            show: true,
            oldusershow: false,
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            qrcodeUrl: "",

            shareUser: {},

            isShare: false,
            code: 2,
            nobalance: false,  // 当前余额不足 拥有指定藏品开启止损止盈弹窗
            proLoss: false,    // 止盈止损弹窗
            val: 50,
            current: 0,
            entrustList: [],
            positionsList: [],
            barStyle: {
                'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
                'width': '80rpx',
                'height': '8rpx',
                'border-radius': '0rpx',
                'bottom': '6rpx',
                'z-index': '1'
            },
            itemStyle: {
                'font-size': '28rpx',
                // 'min-width': '120rpx',
                'z-index': '2',
            },
            marketPrice: '', //市价

            tabList: [
                {
                    name: "等待成交",
                    value: 0
                }, {
                    name: "我的仓位",
                    value: 1
                }
            ],

            orderList: [],
            cangweiList: [],
            orderPageNum: 1,
            cangweiPageNum: 1,
            shareUser: "",
            current: 0,
            tabList: [{
                name: '历史下单',
                value: 0,
            }, {
                name: '历史仓位',
                value: 1,
            }],
            // barStyle: {
            //     'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
            // },
            isFooter: true,
            isRequest: false,
            isShare: false,
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            qrcodeUrl: "",
            isUp: false
        }
    },
    methods: {
        //复制
        copy(val) {
            uniCopy({
                content: val,
                success: (res) => {
                    uni.showToast({
                        title: res,
                        icon: "none",
                    });
                },
                error: (e) => {
                    uni.showToast({
                        title: e,
                        icon: "none",
                        duration: 3000,
                    });
                },
            });
        },
        formatNumber(value, decimalPlaces = 2, arg = 'other') {
            if (arg == 'fund' && value == 0) {
                return '--'
            }
            if (arg == 'exper' && value == 0) {
                return 0
            }
            // if (value <= 0) {
            //     return 0.00
            // }
            if (!value) {
                return '--'
            }
            // 判断是否为整数
            if (Number.isInteger(value)) {
                return value.toString();
            }
            value = Number(value)
            // 如果是整数，则直接返回
            // 如果不是整数，则将其限制为指定的小数位数
            return value.toFixed(decimalPlaces);
        },
        accMul(arg1, arg2) {
            arg1 = Number(arg1);
            arg2 = Number(arg2);
            var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
            try { m += s1.split(".")[1].length } catch (e) { }
            try { m += s2.split(".")[1].length } catch (e) { }
            return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
        },
        dealprogress(item) {
            let chu = parseFloat(item.dealMoney / item.money) * 100
            let yu = chu.toFixed(2)
            return yu
        },
        /**
         * 判断类型
         */
        positionType(item) {
            if (item.side == 'SELL') {
                return '平空均价'
            } else {
                return '平多均价'
            }
            // if (item.side == 'SELL' && item.open == 'OPEN') {
            //     return '开空均价'
            // } else if (item.side == 'SELL' && item.open == 'CLOSE') {
            //     return '平多均价'
            // } else if (item.side == 'BUY' && item.open == 'OPEN') {
            //     return '开多均价'
            // } else if (item.side == 'BUY' && item.open == 'CLOSE') {
            //     return '平空均价'
            // }
        },
        // 点击撤单
        backuporder(item) {
            this.waitorderNowobj = item


            if (item.experienceMoney > 0 && item.dealMoney > 0) {
                this.cancelOrder = true
            } else {
                this.backorder()
            }

        },
        back() {
            this.$Router.back()
            // this.$Router.push({
            //     name: 'Exchange'
            // })
        },
        isred(item) {
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str

            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            // this.$forceUpdate()
            return str >= 0 ? true : false


        },
        async resetProLoss() {
            const res = await this.$api.ResetOtoPosition({ id: 12312312 })
            if (res.status.code == 0) {
                uni.showToast({
                    title: '重置止盈止损成功',
                    icon: 'none',
                    duration: 3000
                })
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
            // this.proLoss = false

        },
        // 撤单
        async backordershow() {

            this.cancelOrder = true;
            return


        },
        async backorder() {
            let res = await this.$api.RevokeOrders({ id: this.waitorderNowobj.id })
            this.cancelOrder = false

            if (res.status.code == 0) {
                uni.showToast({
                    title: '撤单成功',
                    icon: 'none',
                    duration: 3000
                })
                this.cancelOrder = false
                // this.getPositionInfo()
                if (this.current == 0) {
                    this.tradedPageNum = 1
                    this.fetchEntrust()
                } else if (this.current == 1) {
                    this.pageNum = 1
                    this.fetchPositions()
                }

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        async getbonus() {
            let res = await this.$api.GetReward({ activityNo: "A81001000211587072", userType: 2 })
            if (res.status.code == 0) {
                this.newuserreward = false
                // this.popupShow = 0
                uni.showToast({
                    title: '领取成功',
                    icon: 'none',
                    duration: 3000
                })
            } else {
                this.newuserreward = false

                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
            //  请求参数 activityNo=A81001000211587072
            // userType 用户类型 1-邀请者 2-被邀请者

        },
        async checkpopups() {
            let res = await this.$api.CheckPopup({ activityNo: "A81001000211587072" })
            if (res.status.code == 0) {
                if (res.result.isActive == 1) {
                    this.popupShow = res.result.status
                    if (this.popupShow == 2) {
                        this.newuserreward = true
                    }

                    if (this.popupShow == 1) {
                        this.oldusershow = true
                    }
                } else {
                    // uni.showToast({
                    //     title:'活动已结束',
                    //     icon: 'none',
                    //     duration: 3000
                    // })
                }
            }
            //  响应参数
            // isActive 活动状态 1-活动中 0-不在活动中
            // status 0-不显示弹窗 1-邀请者可领取任务 2-被邀请者任务完成领取奖励
        },
        async closeonce(item) {
            let data = {
                id: item.id
            }
            let res = await this.$api.ClosePositions(data)
            if (res.status.code == 0) {
                uni.showToast({
                    title: '平仓成功',
                    icon: 'none',
                    duration: 3000
                })
                this.getPositionInfo()
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        /**
         * 获取委托单
         */
        async fetchEntrust() {
            this.isLinkLoadding = true
            const contractName = uni.getStorageSync('currentIndictor');

            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetcoinHistoryOrder({
                    ...(contractName ? { contractName } : {}), pageNum: this.tradedPageNum,
                    pageSize: 10
                });
            } else {
                // 当 contractName 为空时调用 GetExchangeUserInfo 接口
                res = await this.$api.GetHistoryOrder({
                    ...(contractName ? { contractName } : {}), pageNum: this.tradedPageNum,
                    pageSize: 10
                });
            }

            // this.$api.GetDelegatingOrder() // 查询委托单 等待成交
            // let res = await this.$api.GetHistoryOrder({
            //     ...(contractName ? { contractName } : {}),
            //     pageNum: this.tradedPageNum,
            //     pageSize: 10
            // })
            setTimeout(() => {
                this.isLinkLoadding = false
            }, 300);
            if (res.status.code == 0) {
                this.entrusthasnext = res.result.hasNext
                if (this.tradedPageNum != 1) {
                    this.entrustList = this.entrustList.concat(res.result.list)
                } else {
                    this.entrustList = res.result.list
                }
            }
        },
        // GetHistoryOrder: 'bvexchange/appApi/exchange/getHistoryOrder', // 查询历史下单
        // GetHistoryPosition: 'bvexchange/appApi/exchange/getHistoryPosition', // 查询历史持仓
        /**
         * 获取我的仓位
         */
        async fetchPositions() {
            this.isLinkLoadding = true
            const contractName = uni.getStorageSync('currentIndictor');
            // this.$api.GetCurPosition() // 查询我的仓位

            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetcoinHistoryPosition({
                    ...(contractName ? { contractName } : {}), pageNum: this.pageNum,
                    pageSize: 10
                });
            } else {
                // 当 contractName 为空时调用 GetExchangeUserInfo 接口
                res = await this.$api.GetHistoryPosition({
                    ...(contractName ? { contractName } : {}), pageNum: this.pageNum,
                    pageSize: 10
                });
            }
            // let res = await this.$api.GetHistoryPosition({
            //     ...(contractName ? { contractName } : {}),
            //     pageNum: this.pageNum,
            //     pageSize: 10
            // })
            if (res.status.code == 0) {
                setTimeout(() => {
                    this.isLinkLoadding = false
                }, 300);
                this.positionhasnext = res.result.hasNext
                if (this.pageNum != 1) {
                    this.positionsList = this.positionsList.concat(res.result.list)
                } else {
                    this.positionsList = res.result.list
                }

            }
        },
        async getPositionInfo() {
            // this.$api.GetDelegatingOrder() // 查询委托单 等待成交
            let res = await this.$api.GetHistoryOrder({
                pageNum: this.tradedPageNum,
                pageSize: 10
            })
            if (res.status.code == 0) {
                this.entrustList = res.result.list
            }


            // this.$api.GetCurPosition() // 查询我的仓位
            let res1 = await this.$api.GetHistoryPosition({
                pageNum: this.pageNum,
                pageSize: 10
            })
            if (res1.status.code == 0) {
                this.positionsList = res1.result.list
            }
        },

        beforeEnter(el) {
            el.style.transform = 'scale(1)';
            el.style.opacity = 1;
            el.style.position = 'absolute';
            el.style.top = '0';
            el.style.right = '0';
        },
        enter(el, done) {
            el.offsetHeight; // trigger reflow
            el.style.transition = 'transform 0.5s ease, opacity 0.5s ease';
            el.style.transform = 'scale(0)';
            el.style.opacity = 0;
            done();
        },
        leave(el, done) {
            el.style.transition = 'transform 0.5s ease, opacity 0.5s ease';
            el.style.transform = 'scale(0)';
            el.style.opacity = 0;
            done();
        },
        gomission() {
            // this.show = !this.show;
            // console.log(this.show);
            // this.oldusershow = false
            this.$Router.push({
                name: 'mission'
            })

        },
        async getUserShare() {
            let res = await this.$api.GetExchangeUserInfo({

            });
            console.log(res, '321');
            if (res.status.code == 0) {
                this.shareUser = res.result
                const jumpUrl =
                    `${getApp().globalData.url}pagesA/project/personal/appDownload`;
                this.get_share(jumpUrl)
                console.log(this.shareUser)
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async get_share(jumpUrl) {
            console.error(jumpUrl)
            let res = await this.$api.getShortLink({
                longLink: jumpUrl
            });
            if (res.status.code == 0) {
                this.qrcodeUrl = res.result.shortUrl;
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        getDate() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (`0${now.getMonth() + 1}`).slice(-2);
            const date = (`0${now.getDate()}`).slice(-2);
            // 设置格式化的当前日期与时间
            return `${year}年${month}月${date}日`;
        },
        // 确认止盈止损
        async confirmProLoss() {
            let data = {
                id: '123',
                stopLossPrice: Number(this.loss),
                takeProfitPrice: Number(this.profit)
            }

            let res = await this.$api.OtoPosition(data)
            if (res.status.code == 0) {
                this.proLoss = false
                this.loss = ''
                this.profit = ''
                // this.$Router.push({
                //     name: 'position'
                // })
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }

            this.proLoss = false
        },
        setProLoss() {
            this.proLoss = true
        },
        historyTakeOff() {
            this.$Router.push({
                name: "historyposition"
            })
        },
        change(index) {
            uni.setStorageSync('historyTab', index)
            this.current = index
            if (index == 0) {
                this.tradedPageNum = 1
                this.entrustList = []
                this.fetchEntrust()
            } else {
                this.pageNum = 1
                this.positionsList = []
                this.fetchPositions()
            }
        },
        openPop(item, type, index) {
            this.indexTag = index
            this.orderId = item.orderId
            this.status = item.status
            this.typePop = type
            if (type == 1) {
                this.pop_msg = "确认撤单？"
            } else {
                this.pop_msg = "确认不唱了？"
            }
            this.isConfirmation = true
        },
        async getInfo() {
            const contractName = uni.getStorageSync('currentIndictor');
            let res;
            if (contractName) {
                // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
                res = await this.$api.GetcointradePrcie({ ...(contractName ? { contractName } : {}), });
            } else {
                // 当 contractName 为空时调用 GetExchangeUserInfo 接口
                res = await this.$api.GettradePrcie({ ...(contractName ? { contractName } : {}), });
            }
            if (res.status.code == 0) {
                this.marketPrice = res.result.price
            }
        },
        openPop(item, type, index) {
            this.$emit('openPop', item, type, index)
        },
        //计算战损/战利
        calculateEarnings(item) {

            // 收益 
            // buy =  (marketPrice - price ) * volume - dept
            // sell =  (price - marketPrice) * volume - dept
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str
            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            str = str.toFixed(2)

            // this.$forceUpdate()
            return str >= 0 ? '+' + Math.abs(str) : '-' + Math.abs(str)

        },
        //计算收益率
        calculateYield(item) {

            // 收益率
            // 开仓价 price * volume  / leverageLevel 
            // buy = buy的收益 / 开仓价
            // sell = sell 的收益 / 开仓价

            let str;
            const {
                price,
                side,
                volume,
                leverageLevel,
                income
            } = item

            // if (side == 'BUY') { //唱多
            //     let shouyiNum = (this.marketPrice - price) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // } else { //唱空
            //     let shouyiNum = (price - this.marketPrice) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // }
            // if(side == 'BUY'){
            let open = price * volume / leverageLevel
            str = income / open
            // } else {

            // }

            str = Math.floor(str * 10000) / 100
            if (str >= 0) {
                item.red2 = true
            } else {
                item.red2 = false
            }
            str = str.toFixed(2)
            return str >= 0 ? `+${str}%` : `${str}%`
        },
        async openShare(item) {

            let pages = getCurrentPages(); // 获取当前页面栈
            let currentPage = pages[pages.length - 1]; // 获取当前页面
            let currentRoute = currentPage.route; // 获取当前页面的路径

            let res = await this.$api.VisitorShare({
                module: "BIT_EXCHANGE",
                from: 'h5',
                page: '/shareProfit'
            });

            if (item.status == 1) {
                uni.showToast({
                    title: '该仓位未平仓',
                    icon: 'none',
                    duration: 2000
                })
                return
            }
            // let item = {
            //     orderId: "78468682465386496",
            //     direction: 1,
            //     offsets: 1,
            //     price: "10.9700",
            //     quantity: 10,
            //     lever: 2,
            //     forceClosePrice: "8.2275",
            //     canClose: true,
            //     longShort: 1,
            //     status: 1,
            //     red: true,
            //     red2: true,
            //     name: '张三.meta'
            // }
            // this.$emit('openShare', item)
            let zNum = this.calculateEarnings(item);
            let gNum = this.calculateYield(item)
            console.log(item)
            this.isShare = true
            this.shareUser = {
                ...this.shareUser,
                zNum,
                gNum,
                red: item.side == 'SELL',
                profit: item.profit,
                profitRate: item.profitRate,
                close: item.closePrice,
                price: item.price,
                side: item.side,
                lever: item.lever,
                currentDateTime: this.getDate()
            }
            console.log(this.shareUser)
        }
    },
    watch: {
        "$store.state.realprice"(val) {
            this.marketPrice = val
        },
    }
}
</script>

<style lang="scss" scoped>
.textline {
    text-decoration: line-through;
}

.body {
    background-color: #111111;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
    background-size: 100%;
    background-repeat: no-repeat;
    min-height: 1624rpx;
    position: relative;

    .head_top {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 99;
        width: 100%;
        background-color: rgb(0, 0, 0);
        /* #ifdef APP */
        padding-top: 40rpx;
        /* #endif */

        .back {
            position: absolute;
            /* #ifdef APP */
            // top: var(--status-bar-height);
            top: calc(32rpx + var(--status-bar-height));
            ;
            /* #endif */
            /* #ifdef H5 */
            top: 64rpx;
            /* #endif */
            left: 30rpx;

            img {
                width: 50rpx;
            }
        }

        .tabs_div {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 46rpx 55rpx 10rpx 55rpx;

            .left {
                // width: 460rpx;
            }

            .right {
                img {
                    width: 36rpx;
                }
            }
        }
    }

    .details {
        padding: 200rpx 55rpx;

        // .weituo {
        //     .li {
        //         padding: 30rpx 20rpx;
        //         background-color: #2B2B2B;
        //         border-radius: 36rpx;
        //         margin-bottom: 40rpx;
        //         position: relative;

        //         .right_fenx {
        //             position: absolute;
        //             right: 0rpx;
        //             top: 0rpx;

        //             img {
        //                 width: 80rpx;
        //             }
        //         }

        //         .live_data {
        //             background-color: #141816;
        //             border-radius: 27rpx;
        //             height: 54rpx;
        //             display: flex;
        //             justify-content: flex-start;
        //             align-items: center;
        //             padding: 0rpx 18rpx;
        //             font-size: 24rpx;
        //             width: 500rpx;
        //             color: #6CFF8A;

        //             span {
        //                 color: #fff;
        //             }

        //             .sy {
        //                 margin-right: 20rpx;
        //                 min-width: 200rpx;
        //                 font-weight: 600;

        //                 &.red {
        //                     color: #EC4068;
        //                 }
        //             }

        //             .syl {
        //                 font-weight: 600;

        //                 &.red {
        //                     color: #EC4068;
        //                 }
        //             }
        //         }

        //         .head {
        //             display: flex;
        //             justify-content: space-between;
        //             align-items: center;


        //             .left {
        //                 display: flex;
        //                 justify-content: space-between;
        //                 align-items: center;

        //                 img {
        //                     width: 50rpx;
        //                     margin-right: 30rpx;
        //                 }

        //                 .title {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     font-weight: 600;
        //                     margin-right: 20rpx;
        //                 }

        //                 .sub {
        //                     font-size: 24rpx;
        //                     color: rgb(255, 255, 255, 0.3);
        //                 }
        //             }

        //             .cd {
        //                 font-size: 24rpx;

        //                 .red {
        //                     color: #FF5270;
        //                 }

        //                 .huise {
        //                     color: rgb(255, 255, 255, 0.5);
        //                 }

        //                 .white {
        //                     color: #fff
        //                 }


        //             }
        //         }

        //         .data_view {
        //             display: flex;
        //             justify-content: space-between;
        //             align-items: center;
        //             margin-top: 31rpx;

        //             .li_view {
        //                 text-align: center;

        //                 &.width33 {
        //                     width: 33.33%;
        //                 }

        //                 &.button {
        //                     width: 144rpx;
        //                     height: 48rpx;
        //                     border-radius: 36rpx;
        //                     display: flex;
        //                     justify-content: center;
        //                     align-items: center;
        //                     font-size: 22rpx;
        //                     background-color: #fff;
        //                 }

        //                 .label {
        //                     color: rgb(255, 255, 255, 0.5);
        //                     font-size: 24rpx;
        //                 }

        //                 .num {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     margin-top: 21rpx;
        //                     font-weight: 600;
        //                     display: flex;
        //                     flex-direction: column;

        //                     >text {
        //                         &:nth-of-type(1) {
        //                             font-weight: 400;
        //                             font-size: 24rpx;
        //                             color: #FFFFFF;
        //                         }

        //                         &:nth-of-type(2) {
        //                             margin-top: 10rpx;
        //                             color: #EC4068;
        //                             font-weight: 400;
        //                             font-size: 20rpx;
        //                         }
        //                     }
        //                 }
        //             }
        //         }

        //         .footer {
        //             margin-top: 30rpx;
        //             display: flex;
        //             justify-content: space-between;

        //             >.left {
        //                 width: 180rpx;
        //                 display: flex;
        //                 justify-content: flex-start;
        //                 align-items: center;

        //                 &.noMargin {
        //                     img {
        //                         margin-right: 6rpx;
        //                     }
        //                 }

        //                 img {
        //                     width: 50rpx;
        //                     height: 50rpx;
        //                     margin-right: 20rpx;
        //                 }

        //                 .qp {
        //                     width: 74rpx;
        //                     height: 36rpx;
        //                 }

        //                 .title {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     font-weight: 600;
        //                     margin-right: 20rpx;
        //                 }
        //             }

        //             >.right {
        //                 width: 380rpx;
        //                 font-size: 22rpx;
        //                 color: rgb(255, 255, 255, 0.5);

        //                 div:last-child {
        //                     margin-top: 10rpx;
        //                 }
        //             }
        //         }
        //     }
        // }

        // .cangwei {
        //     .li {
        //         padding: 30rpx 20rpx;
        //         background-color: #2B2B2B;
        //         border-radius: 36rpx;
        //         margin-bottom: 40rpx;
        //         position: relative;

        //         .right_fenx {
        //             position: absolute;
        //             right: 0rpx;
        //             top: 0rpx;

        //             img {
        //                 width: 80rpx;
        //             }
        //         }

        //         .live_data {
        //             background-color: #141816;
        //             border-radius: 27rpx;
        //             height: 54rpx;
        //             display: flex;
        //             justify-content: flex-start;
        //             align-items: center;
        //             padding: 0rpx 18rpx;
        //             font-size: 24rpx;
        //             width: 500rpx;
        //             color: #6CFF8A;

        //             span {
        //                 color: #fff;
        //             }

        //             .sy {
        //                 margin-right: 20rpx;
        //                 min-width: 200rpx;
        //                 font-weight: 600;

        //                 &.red {
        //                     color: #EC4068;
        //                 }
        //             }

        //             .syl {
        //                 font-weight: 600;

        //                 &.red {
        //                     color: #EC4068;
        //                 }
        //             }
        //         }

        //         .head {
        //             display: flex;
        //             justify-content: space-between;
        //             align-items: center;


        //             .left {
        //                 display: flex;
        //                 justify-content: space-between;
        //                 align-items: center;

        //                 img {
        //                     width: 50rpx;
        //                     margin-right: 30rpx;
        //                 }

        //                 .title {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     font-weight: 600;
        //                     margin-right: 20rpx;
        //                 }

        //                 .sub {
        //                     font-size: 24rpx;
        //                     color: rgb(255, 255, 255, 0.3);
        //                 }
        //             }

        //             .cd {
        //                 font-size: 24rpx;

        //                 .red {
        //                     color: #FF5270;
        //                 }

        //                 .huise {
        //                     color: rgb(255, 255, 255, 0.5);
        //                 }

        //                 .white {
        //                     color: #fff
        //                 }


        //             }
        //         }

        //         .data_view {
        //             display: flex;
        //             justify-content: space-between;
        //             align-items: center;
        //             margin-top: 31rpx;

        //             .li_view {
        //                 text-align: center;

        //                 &.width33 {
        //                     width: 33.33%;
        //                 }

        //                 &.button {
        //                     width: 144rpx;
        //                     height: 48rpx;
        //                     border-radius: 36rpx;
        //                     display: flex;
        //                     justify-content: center;
        //                     align-items: center;
        //                     font-size: 22rpx;
        //                     background-color: #fff;
        //                 }

        //                 .label {
        //                     color: rgb(255, 255, 255, 0.5);
        //                     font-size: 24rpx;
        //                 }

        //                 .num {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     margin-top: 21rpx;
        //                     font-weight: 600;
        //                     display: flex;
        //                     flex-direction: column;

        //                     >text {
        //                         &:nth-of-type(1) {
        //                             font-weight: 400;
        //                             font-size: 24rpx;
        //                             color: #FFFFFF;
        //                         }

        //                         &:nth-of-type(2) {
        //                             margin-top: 10rpx;
        //                             color: #EC4068;
        //                             font-weight: 400;
        //                             font-size: 20rpx;
        //                         }
        //                     }
        //                 }
        //             }
        //         }

        //         .footer {
        //             margin-top: 30rpx;
        //             display: flex;
        //             justify-content: space-between;

        //             >.left {
        //                 width: 180rpx;
        //                 display: flex;
        //                 justify-content: flex-start;
        //                 align-items: center;

        //                 &.noMargin {
        //                     img {
        //                         margin-right: 6rpx;
        //                     }
        //                 }

        //                 img {
        //                     width: 50rpx;
        //                     height: 50rpx;
        //                     margin-right: 20rpx;
        //                 }

        //                 .qp {
        //                     width: 74rpx;
        //                     height: 36rpx;
        //                 }

        //                 .title {
        //                     color: #fff;
        //                     font-size: 28rpx;
        //                     font-weight: 600;
        //                     margin-right: 20rpx;
        //                 }
        //             }

        //             >.right {
        //                 width: 380rpx;
        //                 font-size: 22rpx;
        //                 color: rgb(255, 255, 255, 0.5);

        //                 div:last-child {
        //                     margin-top: 10rpx;
        //                 }
        //             }
        //         }
        //     }
        // }

    }
}

.newuser {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 800rpx;
    text-align: center;
    width: 600rpx;
    background: transparent;


    .card {

        height: 447rpx;
        padding: 0 56rpx;
        width: 100%;
        background: #34323D;
        border-radius: 30rpx;
        position: relative;

    }

    .tops {
        z-index: 1;
        position: absolute;
        top: -127rpx;
        left: 168rpx;

        width: 261rpx;
        height: 218rpx;

    }

    .info {
        margin-top: 115rpx;
        display: flex;
        align-items: center;
        justify-content: center;



        .titles {

            font-weight: bold;
            font-size: 34rpx;
            color: #63EAEE;
        }

        .money {
            margin-left: 20rpx;

            image {
                height: 80rpx;
                width: 80rpx;
                border-radius: 20%;
            }
        }

        .tips {
            margin-left: 20rpx;

            font-weight: bold;
            font-size: 28rpx;
            color: #FFFFFF;
        }
    }

    .msg {
        // width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        margin-top: 25rpx;

        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 38rpx;
        // width: 256rpx;



    }

    .btn {
        margin: 0 auto;
        margin-top: 36rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        line-height: 80rpx;

        color: #141414;
    }
}



.share_body {
    width: 100%;
    background-color: #111111;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 160rpx 55rpx 55rpx;
    height: 100vh;

    .cart_div {
        background-color: #2B2B2B;
        border-radius: 36rpx;
        width: 640rpx;
        height: 940rpx;
        padding: 40rpx 36rpx;
        position: relative;

        .buysell {
            display: flex;
            align-items: center;

            image {
                width: 40rpx;
                height: 40rpx
            }

            text {
                margin-left: 12rpx;
                font-weight: bold;
                font-size: 28rpx;
                color: #FFFFFF;
            }
        }

        .title_image {

            font-family: DOUYUFont, DOUYUFont;
            font-weight: 400;
            font-size: 40rpx;
            color: #FFFFFF;

            background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
            width: 100%;
            height: 111rpx;
            background-size: 100% 100%;
            line-height: 111rpx;
            text-align: center;
            letter-spacing: 2rpx;
        }

        .toux_image_div {
            width: 259rpx;
            margin: 20rpx auto;

            .toux_border {
                width: 100%;
                // height:116rpx;
                background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
                background-size: 100%;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                padding-top: 40rpx;

                >text {
                    margin-top: 10rpx;
                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                .image_div {

                    width: 120rpx;
                    height: 120rpx;
                    background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
                    border-radius: 40rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .gray {
                        width: 116rpx;
                        height: 116rpx;
                        border-radius: 40rpx;
                        background: #2B2B2B;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    image {
                        border-radius: 30rpx;
                        width: 104rpx;
                        height: 104rpx;
                        object-fit: cover;
                    }

                }
            }
        }

        .toux_name {
            margin-top: 30rpx;
            text-align: center;
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .yield {
            font-size: 90rpx;
            color: #6CFF8A;
            font-weight: 600;
            margin-top: 40rpx;

            text {
                font-size: 55rpx;
            }

            &.red {

                color: #FF5270;
            }
        }

        .shouyi {
            font-size: 48rpx;
            color: #6CFF8A;

            &.red {
                color: #FF5270;
            }
        }

        .info_div {
            color: #fff;
            margin-top: 30rpx;

            p {
                font-size: 28rpx;
                line-height: 38rpx;
            }
        }

        .yqm {
            background-color: rgb(255, 255, 255, 0.2);
            border: 1px solid #fff;
            width: 280rpx;
            height: 54rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 40rpx;
            border-radius: 36rpx;
            margin-bottom: 30rpx;
        }

        .msg_text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .icon_bg {
            width: 292rpx;
            position: absolute;
            top: 450rpx;
            right: -30rpx;

            image {
                width: 292rpx;
            }
        }

        .qr_code {
            position: absolute;
            top: 724rpx;
            right: 36rpx;

            .right {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .qr_div {
                    width: 140rpx;
                    height: 140rpx;
                    border-radius: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #fff;
                    padding: 10rpx;
                }
            }

            .time {
                color: rgb(255, 255, 255, 0.3);
                margin-top: 30rpx;
                font-size: 22rpx;
            }
        }
    }

    .share_to_div {
        margin-top: 140rpx;
        display: flex;
        justify-content: center;

        >.li {
            width: 25%;
            text-align: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 10rpx;

            .icon_image {
                display: flex;
                justify-content: center;

                image {
                    width: 90rpx;
                    margin-bottom: 20rpx;
                }
            }
        }
    }

    .colse_div {
        margin-top: 46rpx;
        display: flex;
        justify-content: center;

        image {
            width: 80rpx;
        }
    }
}

// .nomoney {
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     flex-direction: column;
//     height: 300rpx;

//     text {
//         font-weight: bold;
//         font-size: 28rpx;
//         color: #FFFFFF;
//     }

//     view {
//         margin-top: 61rpx;
//         width: 300rpx;
//         height: 80rpx;
//         background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
//         border-radius: 40rpx;
//         text-align: center;
//         line-height: 80rpx;
//         font-weight: bold;
//         font-size: 24rpx;
//         color: #141414;
//     }
// }

.nomoney {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 55rpx 0 40rpx 0;


    .bittitle {
        background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
        background-size: 100% 100%;

        width: 360rpx;
        height: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #FFFFFF;

        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: bold;
        font-size: 28rpx;
    }

    .bittitle2 {
        margin: 35rpx 0 21rpx 0;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        opacity: .5;
    }

    .bitline {
        height: 1rpx;
        width: 100%;
        background: #53505D;
    }

    .bitbody {
        margin: 29rpx 34rpx;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #FFFFFF;
        line-height: 44rpx;
        text-align: left;

        .midcolor {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 26rpx;
            line-height: 44rpx;
            text-align: left;
            color: #63EAEE;
        }
    }

    .bitbtn {
        display: flex;
        width: 100%;
        padding: 0 40rpx;
        justify-content: center;
        align-items: center;

        view {
            &:nth-of-type(1) {
                width: 300rpx;
                height: 80rpx;
                line-height: 80rpx;
                text-align: center;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50rpx;
                border: 1rpx solid #FFFFFF;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
            }

            &:nth-of-type(2) {
                width: 220rpx;
                height: 70rpx;
                line-height: 70rpx;
                text-align: center;
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                border-radius: 40rpx;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                font-size: 24rpx;
                color: #141414;
            }

        }
    }

    text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .charge {
        margin-top: 61rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;
    }
}

// 撤单
.newmoneys {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    // height: 367rpx;
    text-align: center;
    padding: 70rpx 60rpx 40rpx 60rpx;

    >text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 38rpx;
    }

    >.btns {
        width: 100%;
        margin-top: 44rpx;
        display: flex;
        justify-content: space-between;

        view {
            text-align: center;
            line-height: 70rpx;
            width: 220rpx;
            height: 70rpx;

            &:nth-of-type(1) {


                background: rgba(255, 255, 255, 0.2);
                border-radius: 50rpx;
                border: 1rpx solid #FFFFFF;
                font-weight: bold;
                font-size: 24rpx;
                color: #FFFFFF;
            }

            &:nth-of-type(2) {
                font-weight: bold;
                font-size: 24rpx;
                color: #141414;
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                border-radius: 40rpx
            }
        }
    }
}

.newmoney {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    height: 367rpx;
    text-align: center;

    text {
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;

        &:nth-of-type(1) {
            margin-top: 50rpx;
            font-weight: bold;
            font-size: 34rpx;
            color: #63EAEE;
        }

        &:nth-of-type(2) {
            // margin-top: 30rpx;

        }
    }

    view {
        // margin-top: 61rpx;
        margin-bottom: 35rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;

    }
}

.modalpro {
    // height: 586rpx;
    background: #2B2B2B;
    border-radius: 36rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 40rpx 30rpx 30rpx;

    .m_head {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .left {
            display: flex;

            image {
                width: 50rpx;
                height: 50rpx;
            }

            >view {
                display: flex;
                flex-direction: column;
                margin-left: 20rpx;

                .price {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #a6a6a6;
                }

                .num {
                    margin-top: 10rpx;

                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;

                    .num2 {
                        margin-left: 10rpx;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #EC4068;
                    }
                }
            }
        }

        .right {
            margin-top: 10rpx;
            font-weight: bold;
            font-size: 30rpx;
            color: #FFFFFF;
        }
    }

    .m_line {
        margin: 31rpx 0 39rpx 0;
        width: 100%;
        height: 1rpx;
        background: #414141;
    }

    .m_openclose {
        .top {
            text {
                display: flex;
                align-items: center;

                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                &:nth-of-type(2) {
                    margin-left: 50rpx;
                    font-weight: 400;
                    font-size: 34rpx;
                    color: #FFFFFF;
                }
            }

        }

        >view {
            margin-bottom: 26rpx;
            display: flex;
            align-items: center;
            position: relative;

            &:last-child {
                margin-bottom: 0;
            }

            display: flex;

            text {
                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }


            }

            .rb {
                z-index: 1;
                position: absolute;
                right: 320rpx;
                top: 28rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #FFFFFF;

            }

            input {
                text-indent: 50rpx;
                margin-left: 36rpx;
                width: 360rpx;
                height: 76rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 18rpx;
                border: 1rpx solid #FFFFFF;

                font-weight: 400;
                font-size: 28rpx;
                color: #FFFFFF;
            }
        }
    }

    .m_btn {
        margin-top: 50rpx;
        width: 100%;
        height: 90rpx;
        background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 50rpx;
        font-weight: bold;
        text-align: center;
        line-height: 90rpx;
        font-size: 34rpx;
        color: #141816;
    }
}

::v-deep .u-tab-bar {
    margin-left: -18rpx;
}

.tabs_div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 55rpx 0 55rpx;
    // margin-top: 10rpx;

    .left {
        // margin-left: -50rpx;
        // width: 460rpx;
    }

    .right {
        image {
            width: 36rpx;
            margin-top: 14rpx;
        }
    }
}

.podetails {
    // padding: 20rpx;
    // margin-top: 20rpx;
    padding: 162rpx 20rpx;

    // .weituo,
    // .cangwei {
    //     min-height: 800rpx;

    //     .li {
    //         padding: 30rpx;
    //         background-color: #2B2B2B;
    //         border-radius: 36rpx;
    //         margin-bottom: 20rpx;
    //         position: relative;
    //         opacity: 1;
    //         transition: opacity 1s ease-out;

    //         &.active {
    //             opacity: 0;
    //         }

    //         .right_fenx {
    //             position: absolute;
    //             right: 30rpx;
    //             top: 30rpx;

    //             image {
    //                 width: 32rpx;
    //                 height: 36rpx
    //             }
    //         }

    //         .live_data {
    //             background-color: #141816;
    //             border-radius: 27rpx;
    //             height: 54rpx;
    //             display: flex;
    //             justify-content: space-between;
    //             align-items: center;
    //             margin-top: 20rpx;
    //             padding: 0rpx 78rpx;
    //             font-size: 24rpx;
    //             // width: 500rpx;
    //             color: #6CFF8A;

    //             text {
    //                 color: #fff;
    //             }

    //             .sy {
    //                 margin-right: 50rpx;
    //                 font-weight: 600;

    //                 text {
    //                     &:nth-of-type(1) {
    //                         font-weight: bold;
    //                         font-size: 30rpx;
    //                         color: #fff;
    //                     }

    //                     &:nth-of-type(2) {
    //                         margin-left: 10rpx;
    //                         font-weight: bold;
    //                         font-size: 30rpx;
    //                         // color: #EC4068;
    //                     }
    //                 }

    //                 &.red {
    //                     color: #EC4068;
    //                 }

    //                 &.green {
    //                     color: #6CFF8A;
    //                 }
    //             }

    //             .syl {
    //                 text {
    //                     &:nth-of-type(1) {
    //                         font-weight: bold;
    //                         font-size: 30rpx;
    //                         color: #fff;
    //                     }

    //                     &:nth-of-type(2) {
    //                         margin-left: 10rpx;
    //                         font-weight: bold;
    //                         font-size: 30rpx;
    //                         // color: #EC4068;
    //                     }
    //                 }

    //                 &.red {
    //                     color: #EC4068;
    //                 }
    //             }
    //         }

    //         >.heads {
    //             display: flex;
    //             justify-content: space-between;
    //             align-items: center;

    //             .qiang {
    //                 margin-top: 30rpx;
    //                 margin-left: 20rpx;

    //                 image {
    //                     width: 72rpx;
    //                     height: 32rpx;
    //                 }
    //             }

    //             .rightinfo {
    //                 display: flex;
    //                 flex-direction: column;
    //                 align-items: flex-start;
    //                 justify-content: space-between;

    //                 text {
    //                     &:nth-of-type(1) {
    //                         color: #fff;
    //                         font-size: 28rpx;
    //                         font-weight: 600;
    //                         margin-right: 20rpx;
    //                     }

    //                     &:nth-of-type(2) {
    //                         margin-top: 10rpx;
    //                         font-weight: 400;
    //                         font-size: 20rpx;
    //                         color: #959595;
    //                     }

    //                 }
    //             }

    //             .left {
    //                 display: flex;
    //                 justify-content: space-between;
    //                 align-items: center;

    //                 image {
    //                     width: 50rpx;
    //                     margin-right: 30rpx;
    //                 }

    //                 >.level {
    //                     color: #fff;
    //                     font-size: 28rpx;
    //                     font-weight: 600;
    //                     margin-right: 20rpx;
    //                 }

    //                 .sub {
    //                     font-size: 24rpx;
    //                     color: rgb(255, 255, 255, 0.3);
    //                 }
    //             }

    //             .cd {
    //                 display: flex;
    //                 align-items: center;

    //                 .cancel {
    //                     width: 80rpx;
    //                     height: 44rpx;
    //                     font-size: 24rpx;
    //                     color: #fff;
    //                     background-color: rgb(255, 255, 255, 0.3);
    //                     display: flex;
    //                     justify-content: center;
    //                     align-items: center;
    //                     border-radius: 36rpx;
    //                     margin-left: 30rpx;
    //                 }

    //                 .progress {
    //                     margin-right: 3px;
    //                     display: flex;
    //                     flex-direction: column;
    //                     align-items: center;

    //                     >text {
    //                         margin-bottom: 8rpx;
    //                         font-weight: 400;
    //                         font-size: 22rpx;
    //                         color: #FFFFFF;
    //                     }

    //                     .bg {
    //                         width: 80rpx;
    //                         height: 6rpx;
    //                         background: #6B6B6B;
    //                         border-radius: 36rpx;

    //                         .bar {

    //                             background: #63EAEE;
    //                             border-radius: 36rpx;
    //                             height: 6rpx;
    //                         }
    //                     }
    //                 }

    //             }
    //         }

    //         .data_view {
    //             display: flex;
    //             justify-content: space-between;
    //             align-items: center;
    //             margin: 56rpx 0 14rpx 0;

    //             .li_view {
    //                 text-align: center;
    //                 width: 33.33%;

    //                 &.button {
    //                     width: 144rpx;
    //                     height: 48rpx;
    //                     border-radius: 36rpx;
    //                     display: flex;
    //                     justify-content: center;
    //                     align-items: center;
    //                     font-size: 22rpx;
    //                     background-color: #fff;
    //                 }

    //                 .label {
    //                     font-weight: 400;
    //                     font-size: 22rpx;
    //                     color: #a6a6a6;
    //                 }

    //                 .num {
    //                     margin-top: 21rpx;

    //                     font-weight: 400;
    //                     font-size: 24rpx;
    //                     color: #FFFFFF;
    //                     display: flex;
    //                     flex-direction: column;

    //                     >text {
    //                         &:nth-of-type(1) {
    //                             font-weight: 400;
    //                             font-size: 24rpx;
    //                             color: #FFFFFF;
    //                         }

    //                         &:nth-of-type(2) {
    //                             margin-top: 10rpx;
    //                             color: #EC4068;
    //                             font-weight: 400;
    //                             font-size: 20rpx;
    //                         }
    //                     }
    //                 }
    //             }
    //         }


    //         .data_views {
    //             display: flex;
    //             justify-content: space-between;
    //             align-items: center;
    //             margin: 31rpx 0 14rpx 0;

    //             .li_view {
    //                 text-align: center;
    //                 // width: 33.33%;

    //                 &.button {
    //                     width: 144rpx;
    //                     height: 48rpx;
    //                     border-radius: 36rpx;
    //                     display: flex;
    //                     justify-content: center;
    //                     align-items: center;
    //                     font-size: 22rpx;
    //                     background-color: #fff;
    //                 }

    //                 .label {
    //                     font-weight: 400;
    //                     font-size: 22rpx;
    //                     color: #a6a6a6;
    //                 }

    //                 .num {
    //                     margin-top: 21rpx;

    //                     font-weight: 400;
    //                     font-size: 24rpx;
    //                     color: #FFFFFF;
    //                     display: flex;
    //                     flex-direction: column;

    //                     >text {
    //                         &:nth-of-type(1) {
    //                             font-weight: 400;
    //                             font-size: 24rpx;
    //                             color: #FFFFFF;
    //                         }

    //                         &:nth-of-type(2) {
    //                             margin-top: 10rpx;
    //                             color: #EC4068;
    //                             font-weight: 400;
    //                             font-size: 20rpx;
    //                         }
    //                     }
    //                 }
    //             }
    //         }

    //         .close {
    //             margin-top: 40rpx;
    //             display: flex;
    //             justify-content: space-between;

    //             .unlock {
    //                 background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
    //                 font-weight: 400;
    //                 font-size: 24rpx;
    //                 color: #141816;
    //                 border: none
    //             }

    //             >view {
    //                 display: flex;
    //                 justify-content: center;
    //                 align-items: center;
    //                 width: 300rpx;
    //                 height: 60rpx;
    //                 border-radius: 14rpx;
    //                 border: 1rpx solid #FFFFFF;
    //                 font-weight: 400;
    //                 font-size: 24rpx;
    //                 color: #FFFFFF;
    //             }
    //         }
    //     }
    // }
    .weituo {
        min-height: 800rpx;

        .li {
            // padding: 30rpx;
            padding: 16rpx 30rpx 16rpx 30rpx;
            background-color: #2B2B2B;
            border-radius: 36rpx;
            margin-bottom: 20rpx;
            position: relative;
            opacity: 1;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 34rpx;
                top: 22rpx;

                image {
                    width: 20rpx;
                    height: 22.5rpx
                }
            }

            .live_data {
                background-color: #141816;
                border-radius: 40rpx;
                height: 80rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 20rpx;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;
                // width: 500rpx;
                // color: #6CFF8A;

                text {
                    color: #fff;
                }

                .sy {
                    margin-right: 50rpx;
                    font-weight: 600;

                    text {
                        &:nth-of-type(1) {
                            font-weight: bold;
                            color: #fff;

                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-size: 28rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: bold;
                            font-size: 28rpx;
                            // color: #EC4068;
                        }
                    }

                    // .red {
                    //     // color: #EC4068;
                    // }

                    // .green {
                    //     // color: #6CFF8A;
                    // }
                }

                .syl {
                    text {
                        &:nth-of-type(1) {
                            font-weight: bold;
                            font-size: 30rpx;
                            color: #fff;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: bold;
                            font-size: 30rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    image {
                        width: 50rpx;
                        margin-right: 20rpx;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        margin-right: 20rpx;
                    }

                    .sub {
                        font-size: 24rpx;
                        color: rgb(255, 255, 255, 0.3);
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 35rpx;

                        image {
                            width: 18rpx;
                            height: 24rpx;
                        }
                    }

                    .progress {
                        margin-right: 3px;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 6rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {
                    text-align: center;

                    // width: 33.33%;
                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }


            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 20rpx 0 0 0;

                .li_view {
                    text-align: center;
                    // width: 33.33%;
                    display: flex;
                    justify-content: center;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 21rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .close {
                margin-top: 20rpx;
                display: flex;
                justify-content: space-between;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none
                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 300rpx;
                    height: 60rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                }
            }
        }
    }

    .cangwei {
        min-height: 800rpx;

        .li {
            // padding: 30rpx;
            padding: 10rpx 30rpx 20rpx 30rpx;
            background-color: #2B2B2B;
            border-radius: 36rpx;
            margin-bottom: 20rpx;
            position: relative;
            opacity: 1;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 34rpx;
                top: 22rpx;

                image {
                    width: 20rpx;
                    height: 22.5rpx
                }
            }

            .qiang {
                image {
                    width: 72rpx;
                    height: 32rpx;
                }


            }

            .ADL {
                width: 70rpx;
                height: 28rpx;
                background: #D88839;
                border-radius: 36rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 18rpx;
                color: #FFFFFF;
            }

            .live_data {
                margin: 0 34rpx 0 26rpx;
                width: 450rpx;
                // height: 50rpx;
                background: #141816;
                border-radius: 40rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;
                padding: 10rpx;

                // width: 500rpx;
                // color: #6CFF8A;
                // padding: 0 56rpx;
                text {
                    color: #fff;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: bold;
                    font-size: 24rpx;
                }

                .sy {
                    display: flex;
                    justify-content: center;
                    font-weight: 600;

                    .text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                            // color: #EC4068;
                        }
                    }

                    // .red {
                    //     // color: #EC4068;
                    // }

                    // .green {
                    //     // color: #6CFF8A;
                    // }
                }

                .syl {
                    display: flex;
                    align-items: center;
                    margin-left: 20rpx;

                    >text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;

                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                // justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;


                    >.rightinfo {
                        display: flex;
                        align-items: center;

                        // flex-direction: column;
                        // align-items: flex-start;
                        // justify-content: space-between;
                        .level {
                            color: #fff;
                            font-size: 28rpx;
                            font-weight: 600;
                            margin-right: 20rpx;
                        }

                        // text {
                        //     &:nth-of-type(1) {
                        //         color: #fff;
                        //         font-size: 28rpx;
                        //         font-weight: 600;
                        //         margin-right: 20rpx;
                        //     }

                        //     &:nth-of-type(2) {
                        //         margin-top: 10rpx;
                        //         font-weight: 400;
                        //         font-size: 20rpx;
                        //         color: #959595;
                        //     }

                        // }
                    }

                    image {
                        width: 36rpx;
                        margin-right: 14rpx;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        // margin-right: 20rpx;
                    }

                    .sub {
                        font-size: 24rpx;
                        color: rgb(255, 255, 255, 0.3);
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 35rpx;
                    }

                    .progress {
                        margin-right: 3px;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 6rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {
                    // display: flex;
                    // flex-direction: column;
                    // align-items: center;
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }

            .potime {

                // margin-top: 10rpx;
                align-items: flex-start;
                display: flex;
                flex-direction: column;


                .fees {
                    line-height: 29rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    opacity: .5
                }

                .time {
                    margin-top: 10rpx;
                    line-height: 24rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 18rpx;
                    color: #FFFFFF;
                    opacity: .5
                }
            }

            .fee {
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                // justify-content: space-between;
                .fees {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    opacity: .5;
                    width: 300rpx;
                    line-height: 29rpx;
                }

                .time {
                    line-height: 24rpx;
                    margin-top: 12rpx;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 18rpx;
                    color: #FFFFFF;
                    opacity: .5
                }

            }

            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                // margin: 0 33rpx;

                .li_view {
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #FFFFFF;
                        opacity: .5;
                        line-height: 29rpx;
                    }

                    .num {
                        margin-top: 4rpx;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        line-height: 32rpx;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;


                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

                                // margin-top: 4rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .close {
                // margin-top: 20rpx;
                display: flex;
                justify-content: flex-end;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 140rpx;
                    height: 50rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    color: #FFFFFF;

                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                    background: #2B2B2B;
                    border-radius: 12rpx;
                }
            }
        }
    }
}

.nodata {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    image {
        width: 240rpx;
        height: 240rpx;
    }

    text {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
    }
}

.hasdeal {
    font-weight: 400;
    font-size: 22rpx !important;
    color: #6B6B6B;
}

.times {
    font-weight: 400;
    font-size: 20rpx;
    color: #959595;
}

.rotate-image {
    animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(180deg);
    }
}
</style>