<template>
	<view>
		<u-navbar :border-bottom="false" :background="{ backgroundColor: '#35333E' }" title="任务" title-color="#fff"
			:custom-back="goback" back-icon-color="#fff">
		</u-navbar>
		<view class="title">
			<text>邀请好友赢取￥15体验金</text>
			<view>
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240802/3883c0d326984f6ddac86b734e35d3ad_48x48.png"
					mode="widthFix"></image>
				<text>{{ startEndTime || '--' }}</text>
			</view>
		</view>

		<view class="banner">
			<image style="height: 300rpx" mode="widthFix"
				src="https://cdn-lingjing.nftcn.com.cn/image/20240802/5f740e9081f4ab33989eba1372792e48_1500x744.png" />
		</view>

		<view class="invite">
			<view>
				<text>1.邀请3位新用户体验BIT指数</text>
				<text>2.三位好友均可获得¥15现金体验金</text>
				<text>3.体验金有效时间：{{ startEndTime }}</text>
			</view>
			<text>{{ success || 0 }}/3</text>
		</view>

		<view style="height: 40rpx"></view>
		<view class="btn bg1" v-if="btnshow == 1" @tap="isShare = true">
			<text>去邀请</text>
		</view>

		<view class="btn bg2" @tap="goExchange" v-else-if="btnshow == 2">
			<text>领取体验金</text>
		</view>

		<view class="btn bg3" v-else-if="btnshow == 3">
			<text>已领取</text>
		</view>

		<view class="list">
			<view class="listtitle"><text>我的邀请记录</text>
				<view class="slide"></view>
			</view>

			<view style="margin: 0 36rpx;padding-bottom: 100rpx;">
				<scroll-view :scroll-y="true" class="listitem" @scrolltolower="bottomOut()" ref="scrollContainer">
					<view class="item" v-for="(item, index) in inviteFriends" :key="index">
						<!-- 已完成的右边icon -->
						<image class="tips" mode="widthFix" style="width: 110rpx;" v-if="item.inviteStatus >= 1"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240802/97c7f8bcf0b291e1cf3ab90ae7b68ac2_256x124.png" />

						<view class="info">
							<image :src="item.avatar" />
							<text>{{ item.nickname }}</text>
						</view>
						<!-- // inviteStatus 邀请进度 -2待实名认证, -1实名过其他账号, 0已实名认证, 大于等于1完成 -->

						<!-- 已完成 item.inviteStatus >= 1 -->
						<view class="progress" v-if="item.inviteStatus >= 1">
							<view class="name">
								<text>已注册</text>
								<text>已实名认证</text>
								<text>已开仓</text>
							</view>
							<view class="line">
								<view class="balls">
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>


									<view class="balls2">
									</view>
								</view>
							</view>
							<!-- <view class="tip">
                            <text>请提醒好友认证</text>
                            <text>请提醒好友开仓</text>
                        </view> -->
						</view>

						<!-- 已实名 item.inviteStatus == 0 -->
						<view class="progress1" v-else-if="item.inviteStatus == 0">
							<view class="name">
								<text>已注册</text>
								<text>已实名认证</text>
								<text style="color: #63EAEE;">未开仓</text>
							</view>
							<view class="line">
								<view class="balls">
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>
									<view class="ball">
										<!-- <view class="boll"> -->
										<view></view>
										<!-- </view> -->
									</view>


									<view class="balls2">
									</view>
								</view>
							</view>

							<view class="tip">
								<!-- <text >请提醒好友认证</text> -->
								<text>请提醒好友开仓</text>
							</view>
						</view>

						<!-- 未实名 -->
						<view class="progress2" v-else-if="item.inviteStatus == -2 || item.inviteStatus == -1">
							<view class="name">
								<text style="color: #929196">已注册</text>
								<text style="color: #44868D;">待实名认证</text>
								<text style="color:#929196 ">未开仓</text>
							</view>
							<view class="line">
								<view class="balls">
									<view class="ball">
										<view class="boll">
											<view></view>
										</view>
									</view>
									<view class="ball">
										<!-- <view class="boll"> -->
										<view></view>
										<!-- </view> -->
									</view>
									<view class="ball">
										<!-- <view class="boll"> -->
										<view></view>
										<!-- </view> -->
									</view>


									<view class="balls2">
									</view>
								</view>
							</view>
							<view class="tip">
								<text v-if="item.inviteStatus == -2">请提醒好友认证</text>
								<text v-else-if="item.inviteStatus == -1">已实名过其他账号</text>
							</view>
						</view>
					</view>

					<view v-if="inviteFriends.length == 0" class="nodata">
						<image mode="widthFix"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240813/0168b66bda9880ba60c05ca8f52b6981_480x480.png" />
						<text>暂时没有数据</text>
					</view>

					<!-- <u-loadmore :status="status" /> -->
					<view class="loadmores" v-if="loading && inviteFriends.length > 0 && hasNext">
						<image mode="widthFix" class="arrow"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240806/28b4b0aff2142335f900869fecc9c9ec_44x56.png" />
						<text class="more">上划加载更多...</text>
						<image mode="widthFix" class="arrow"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240806/28b4b0aff2142335f900869fecc9c9ec_44x56.png" />
					</view>
				</scroll-view>
			</view>


		</view>

		<!-- 领取体验金 -->
		<u-popup mode="center" v-model="newuserreward">
			<view class="newuser">
				<view class="card">
					<image mode="widthFix" class="tops"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

					<view class="info">
						<view class="titles">恭喜</view>
						<view class="money">
							<image mode="widthFix"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

						</view>
						<view class="tips">username</view>

					</view>

					<view class="msg">
						<text style="width:506rpx">
							{{ '您已经完成任务，免费获得￥15体验金' }}
						</text>
					</view>

					<view class="btn" @tap="getbonus">领取</view>
				</view>
			</view>
		</u-popup>


		<!-- 分享 -->
		<u-popup v-model="isShare" mode="center">
			<view class="share_body" @click="isShare = false">
				<view class="imgtop"></view>
				<!-- <view style="height: 33rpx;"></view> -->
				<view class="share_content">
					<view class="left">
						<view>
							<text>邀请码：{{ inviteCode || '--' }}</text>
							<image class="copy-img" style="width: 20rpx;height: 22rpx;margin-left: 10rpx;"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240822/86ee3166817515f270891ab2e1973722_40x44.png"
                                mode="widthFix" @click.stop="copy(inviteCode)" />
						</view>
						<text class="leftbom">抓住机会，现在行动！</text>
					</view>
					<view class="right">
						<view class="qr_div">
							<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="160rpx"
								:options="options"></uv-qrcode>
						</view>
					</view>
				</view>
			</view>
			<!-- "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240923/f31981ddb7f062ef51774072d134d95e_1502x3334.png" -->
		</u-popup>
	</view>
</template>

<script>
import uniCopy from "@/js_sdk/uni-copy.js";

name: 'mission'
export default {
	data() {
		return {
			total: 6,
			status: 'loadmore', //初始加载状态
			loading: false,
			inviteStatus: -1,
			currentTime: '',
			qrcodeUrl: "",
			options: {
				useDynamicSize: false,
				errorCorrectLevel: 'Q',
				// margin: 10,
				areaColor: "#fff",
				// 指定二维码前景，一般可在中间放logo
				// foregroundImageSrc: require('static/image/logo.png')
			},
			isShare: false,
			newuserreward: false,
			btnshow: 1,
			pageSize: 5,
			pageNum: 1,
			startEndTime: '', // 活动时间
			// successNum 邀请成功的人数
			// button 按钮状态 1-去邀请 2-去领取 3-已领取
			// nickname 用户昵称
			// avatar 用户头像
			// inviteCode 邀请码
			success: '', // 邀请成功的人数
			inviteFriends: [], // 邀请好友列表
			btnstatus: 1, // 按钮状态
			nickname: '', // 用户昵称
			avatar: '', // 用户头像
			inviteCode: '', // 邀请码
			hasNext: true,
		}
	},
	created() {
		this.fetchActivity()
		this.getInviteFriends()
		this.getCurrentTime()
		// this.getUserShare()
	},
	methods: {
		//复制
		copy(val) {
			uniCopy({
				content: val,
				success: (res) => {
					uni.showToast({
						title: res,
						icon: "none",
					});
				},
				error: (e) => {
					uni.showToast({
						title: e,
						icon: "none",
						duration: 3000,
					});
				},
			});
		},
		async getUserShare() {
			let res = await this.$api.getUserInfotake({

			});
			if (res.status.code == 0) {
				// this.shareUser = res.result
				const jumpUrl =
					`${getApp().globalData.url}pagesA/project/personal/appDownload`;
				this.get_share(jumpUrl)
				// console.log(this.shareUser)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_share(jumpUrl) {
			console.error(jumpUrl)
			let res = await this.$api.getShortLink({
				longLink: jumpUrl
			});
			if (res.status.code == 0) {
				this.qrcodeUrl = res.result.shortUrl;

			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		goExchange() {
			// this.newuserreward = true
			this.$Router.push({
				name: 'contract-BITindex',
				params: {
					ispop: true
				}
			})
		},
		goback() {
			// this.$Router.push({
			//     name: 'Exchange'
			// })
			this.$Router.back()

		},
		//触底加载数据
		bottomOut() {
			console.log('123123312');

			if (this.hasNext && this.inviteFriends.length > 0) {
				this.loading = true; //滑到底部的时候显示状态为加载中
				this.pageNum += 1; //请求页数+1
				//判断没数据后停止请求接口数据，并修改显示状态为没有更多
				// if (this.page >= 6) {
				//     this.loading = false
				//     return;
				// }
				this.getInviteFriends(); //调用数据请求
			} else {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 2000

				})
			}
		},

		getCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			this.currentTime = `分享于 ${year}年${month}月${day}日`;
		},
		async getbonus() {
			let res = await this.$api.GetReward({
				activityNo: "A81001000211587072",
				userType: 1
			})
			if (res.status.code == 0) {
				this.newuserreward = false
				// this.popupShow = 0
				uni.showToast({
					title: '领取成功',
					icon: 'none',
					duration: 3000
				})
			} else {
				this.newuserreward = false

				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				})
			}
			//  请求参数 activityNo=A81001000211587072
			// userType 用户类型 1-邀请者 2-被邀请者

		},

		async fetchActivity() {
			let res = await this.$api.inviteActivity({
				activityNo: 'A81001000211587072'
			})
			console.log(res)
			if (res.status.code == 0) {
				this.inviteCode = res.result.inviteCode
				this.startEndTime = res.result.startEndTime
				this.success = res.result.successNum
				this.btnshow = res.result.button
				this.nickname = res.result.nickname
				this.avatar = res.result.avatar
				this.inviteCode = res.result.inviteCode
				// #ifdef APP
				// APP端邀请地址
				this.qrcodeUrl = `${getApp().globalData.url}pages/project/login/register?code=${this.inviteCode}`
				// #endif
				// #ifdef H5
				// H5端邀请地址
				let { origin } = window.location
				this.qrcodeUrl = `${origin}/h5/#/pages/project/login/register?code=${this.inviteCode}`
				// #endif

				// console.log(this.qrcodeUrl);

			}
			// 响应参数
			// startEndTime 活动时间
			// successNum 邀请成功的人数
			// button 按钮状态 1-去邀请 2-去领取 3-已领取
			// nickname 用户昵称
			// avatar 用户头像
			// inviteCode 邀请码
		},
		async getInviteFriends() {
			let data = {
				activityNo: 'A81001000211587072',
				pageSize: this.pageSize,
				pageNum: this.pageNum
			}
			let res = await this.$api.inviteFriends(data)
			console.log(res)

			if (this.loading) {
				setTimeout(() => {
					this.loading = false
				}, 1000);
			}


			if (res.status.code == 0) {
				this.hasNext = res.result.hasNext
				this.total = res.result.totalCount
				// if (res.result.hasNext) {
				if (this.pageNum == 1) {
					this.inviteFriends = res.result.list
				} else {
					this.inviteFriends = this.inviteFriends.concat(res.result.list)
				}
				// }
				// console.log();

				// this.inviteFriends = res.result.list

			}
			// 响应参数
			// avatar 用户头像
			// nickname 用户昵称
			// inviteStatus 邀请进度 -2待实名认证, -1实名过其他账号, 0已实名认证, 大于等于1完成

			//     this.inviteFriends = [{
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     }, {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     }, {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq4412331231spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },
			//      {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },    {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },    {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },    {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },    {
			//         avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//         nickname: "UnnEq44spf",
			//         inviteTime: "2024-08-06 10:29:25.000",
			//         failReason: "记得提醒TA完成购买任务",
			//         inviteStatus: 0,
			//     },
			//     //  {
			//     //     avatar: "https://cdn-lingjing.nftcn.com.cn/photo/avatar.png",
			//     //     nickname: "UnnEq44spf",
			//     //     inviteTime: "2024-08-06 10:29:25.000",
			//     //     failReason: "记得提醒TA完成购买任务",
			//     //     inviteStatus: 0,
			//     // }
			// ]
		},
	}
}
</script>

<style lang="scss" scoped>
.nodata {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	margin-top: 200rpx;

	image {
		height: 240rpx;
		width: 240rpx;
	}

	text {
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 24rpx;
		color: #9A999F;
	}
}

.share_body {
	// width: 640rpx;
	// height: 900rpx;
	width: 100vw;
	height: 100vh;
	// background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240923/5a726efef0390f9e590265a5de7225d9_1512x3334.png");
	// background-size: 100% 100%;
	// position: relative;

	.imgtop {
		height: 866rpx;
		background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240923/ad86455020d53ee412b40cf68c9b3712_1502x1734.png");
		background-size: 100% 866rpx;
		// height: 800rpx;
		width: 100%;
	}

	// background: #2B2B2B;
	// border-radius: 36rpx;
	.share_content {
		width: 100%;
		display: flex;
		justify-content: space-between;
		background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240923/96083600212aa6c74aebbf9ad447a921_1502x1600.png");
		background-size: 100% 100%;
		// position: absolute;
		// bottom: 570rpx;
		height: 799rpx;
		padding: 0 70rpx;
		padding-top: 33rpx;


		.left {
			margin: 41rpx 0 0 0;

			>view {
				margin-bottom: 39rpx;
				padding: 0 0 0 29rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				width: 292rpx;
				height: 62rpx;
				background: rgba(119, 68, 171, 0.1);
				border-radius: 31rpx;
				border: 2rpx solid #B742FF;
			}


			font-family: Alibaba PuHuiTi;
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;

			.leftbom {
				// padding-top: 39rpx;
				font-family: Alibaba PuHuiTi;
				font-weight: 500;
				font-size: 29rpx;
				color: #FFFFFF;
			}
		}

		.right {
			.qr_div {
				width: 190rpx;
				height: 190rpx;

				border-radius: 22px;
				border: 2px solid #B742FF;

				border-radius: 28rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #fff;
				padding: 15rpx;
			}
		}
	}

	.invitecode {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 50rpx;

		.left {
			margin-top: -24rpx;

			view {
				display: flex;
				flex-direction: column;
				align-items: center;

				&:nth-of-type(1) {
					padding: 0 20rpx;
					flex-direction: row;
					display: flex;
					align-items: center;
					text-align: center;
					// line-height: 54rpx;
					// width: 280rpx;
					height: 54rpx;
					background: rgba(255, 255, 255, 0.2);
					border-radius: 36rpx;
					border: 1rpx solid #FFFFFF;
					font-weight: 400;
					font-size: 28rpx;
					color: #FFFFFF;
				}

				&:nth-of-type(2) {
					margin-top: 24rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #FFFFFF;
				}
			}

		}

		.right {
			// display: flex;
			// justify-content: flex-end;
			// align-items: center;
			margin-top: -24rpx;

			.qr_div {
				width: 170rpx;
				height: 170rpx;
				border-radius: 28rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #fff;
				padding: 15rpx;
			}
		}
	}

	.shareline {
		height: 1rpx;
		width: 100%;
		background: #a6a6a6;
		opacity: 0.1;
		margin: 20rpx 0 30rpx 0;
	}

	.date {
		margin-top: 26rpx;
		text-align: center;
		display: flex;
		flex-direction: column;

		text {
			&:nth-of-type(1) {
				line-height: 45rpx;
				font-weight: 400;
				font-size: 34rpx;
				color: #FFFFFF;
			}

			&:nth-of-type(2) {
				margin-top: 11rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #a6a6a6;
			}

		}
	}

	.infos {
		display: flex;
		justify-content: center;
		align-items: center;

		.cai {
			width: 80rpx;
			height: 80rpx;
			background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 40%;

			.gray {
				width: 74rpx;
				border-radius: 40%;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 74rpx;
				background: #2B2B2B;
			}
		}

		.banner {
			height: 68rpx;
			width: 68rpx;
			border-radius: 40%;

		}

		>view {
			display: flex;
			flex-direction: column;
			align-items: start;
			margin-left: 20rpx;

			text {
				&:nth-of-type(1) {
					font-weight: bold;
					font-size: 28rpx;
					color: #FFFFFF;
				}

				&:nth-of-type(2) {
					line-height: 38rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #FFFFFF;
				}

			}
		}
	}

	.bouns {
		margin: 0 auto;
		width: 180rpx;
		height: 165rpx;
	}

	.banner {
		height: 257rpx;
		width: 100%;
	}

	.cart_div {
		background-color: #2B2B2B;
		border-radius: 36rpx;
		width: 640rpx;
		height: 940rpx;
		padding: 40rpx 36rpx;
		position: relative;

		.buysell {
			display: flex;
			align-items: center;

			image {
				width: 40rpx;
				height: 40rpx
			}

			text {
				margin-left: 12rpx;
				font-weight: bold;
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}

		.title_image {

			font-family: DOUYUFont, DOUYUFont;
			font-weight: 400;
			font-size: 40rpx;
			color: #FFFFFF;

			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
			width: 100%;
			height: 111rpx;
			background-size: 100% 100%;
			line-height: 111rpx;
			text-align: center;
			letter-spacing: 2rpx;
		}

		.toux_image_div {
			width: 259rpx;
			margin: 20rpx auto;

			.toux_border {
				width: 100%;
				// height:116rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
				background-size: 100%;
				background-repeat: no-repeat;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				padding-top: 40rpx;

				>text {
					margin-top: 10rpx;
					font-weight: bold;
					font-size: 28rpx;
					color: #FFFFFF;
				}

				.image_div {

					width: 120rpx;
					height: 120rpx;
					background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
					border-radius: 40rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						border-radius: 40rpx;
						width: 112rpx;
						height: 112rpx;
						object-fit: cover;
					}
				}
			}
		}

		.toux_name {
			text-align: center;
			font-size: 28rpx;
			color: #fff;
			font-weight: 600;
		}

		.yield {
			font-size: 90rpx;
			color: #6CFF8A;
			font-weight: 600;
			margin-top: 40rpx;

			text {
				font-size: 55rpx;
			}

			&.red {
				color: #FF5270;
			}
		}

		.shouyi {
			font-size: 48rpx;
			color: #6CFF8A;

			&.red {
				color: #FF5270;
			}
		}

		.info_div {
			color: #fff;
			margin-top: 30rpx;

			p {
				font-size: 28rpx;
				line-height: 38rpx;
			}
		}

		.yqm {
			background-color: rgb(255, 255, 255, 0.2);
			border: 1px solid #fff;
			width: 280rpx;
			height: 54rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
			font-size: 28rpx;
			margin-top: 40rpx;
			border-radius: 36rpx;
			margin-bottom: 30rpx;
		}

		.msg_text {
			font-size: 28rpx;
			color: #fff;
			font-weight: 600;
		}

		.icon_bg {
			width: 292rpx;
			position: absolute;
			top: 450rpx;
			right: -30rpx;

			image {
				width: 292rpx;
			}
		}

		.qr_code {
			position: absolute;
			top: 724rpx;
			right: 36rpx;



			.time {
				color: rgb(255, 255, 255, 0.3);
				margin-top: 30rpx;
				font-size: 22rpx;
			}
		}
	}

	.share_to_div {
		margin-top: 140rpx;
		display: flex;
		justify-content: center;

		>.li {
			width: 25%;
			text-align: center;
			color: #fff;
			font-size: 28rpx;
			margin-top: 10rpx;

			.icon_image {
				display: flex;
				justify-content: center;

				image {
					width: 90rpx;
					margin-bottom: 20rpx;
				}
			}
		}
	}

	.colse_div {
		margin-top: 46rpx;
		display: flex;
		justify-content: center;

		image {
			width: 80rpx;
		}
	}
}

.newuser {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	height: 800rpx;
	text-align: center;
	width: 600rpx;
	background: transparent;


	.card {

		height: 447rpx;
		padding: 0 56rpx;
		width: 100%;
		background: #34323D;
		border-radius: 30rpx;
		position: relative;

	}

	.tops {
		z-index: 1;
		position: absolute;
		top: -127rpx;
		left: 168rpx;

		width: 261rpx;
		height: 218rpx;

	}

	.info {
		margin-top: 115rpx;
		display: flex;
		align-items: center;
		justify-content: center;



		.titles {

			font-weight: bold;
			font-size: 34rpx;
			color: #63EAEE;
		}

		.money {
			margin-left: 20rpx;

			image {
				height: 80rpx;
				width: 80rpx;
				border-radius: 20%;
			}
		}

		.tips {
			margin-left: 20rpx;

			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}

	.msg {
		// width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;

		margin-top: 25rpx;

		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 38rpx;
		// width: 256rpx;



	}

	.btn {
		margin: 0 auto;
		margin-top: 36rpx;
		width: 300rpx;
		height: 80rpx;
		background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 40rpx;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 80rpx;

		color: #141414;
	}
}

.list {
	margin: 40rpx 0 63rpx 0;




	.listtitle {
		display: flex;
		// justify-content: space-between;
		flex-direction: column;
		align-items: center;
		// 

		>.slide {
			margin-top: 10rpx;
			width: 40rpx;
			height: 6rpx;
			background: #63EAEE;
		}

		text {
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}

	.listitem {
		overflow-y: auto;
		margin-top: 30rpx;
		// margin: 30rpx 36rpx 0 36rpx;
		height: 774rpx;
		background: #25232D;
		border-radius: 30rpx;

		.item {
			display: flex;
			align-items: center;
			// margin: 40rpx;
			padding: 40rpx;
			border-bottom: 1rpx solid #3B3942;
			position: relative;

			.itemline {
				background: #3B3942;
				height: 1rpx;
				width: 100%;
				margin: 40rpx 27rpx;
			}

			.tips {

				position: absolute;
				top: 72rpx;
				right: -6rpx;

			}

			.info {
				width: 120rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;

				image {
					width: 60rpx;
					border-radius: 50%;
					height: 60rpx;
				}

				text {
					font-weight: 400;
					font-size: 22rpx;
					color: #FFFFFF;
					line-height: 29rpx;
					margin-top: 4rpx;
					width: 160rpx;
					/* 设置容器的宽度 */
					overflow: hidden;
					/* 隐藏溢出的内容 */
					white-space: nowrap;
					/* 防止文本换行 */
					text-overflow: ellipsis;
				}
			}

			.progress {
				margin-left: 53rpx;
				width: 300rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				// align-items: center;
				// margin-left: 30rpx;
				.name {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #929196;
					margin-bottom: 20rpx;
				}

				.tip {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #FFFFFF;
					// margin-bottom: 20rpx; 
					margin-top: 20rpx;
				}

				.line {
					.balls {
						display: flex;
						justify-content: space-between;
						align-items: center;
						width: 100%;
						height: 8rpx;
						background: #67656C;
						position: relative;

						.ball {
							width: 20rpx;
							height: 20rpx;
							background: #67656C;
							border-radius: 50%;
							display: flex;
							justify-content: center;
							align-items: center;

							.boll {
								// margin-left: 4rpx;
								width: 14rpx;
								height: 14rpx;
								border-radius: 50%;
								background: #63EAEE;
								display: flex;
								justify-content: center;
								align-items: center;

								view {
									width: 6rpx;
									height: 6rpx;
									border-radius: 50%;
									background: #fff;
									z-index: 999;
									// margin-left: 5rpx;
								}
							}
						}

						.balls2 {
							// display: flex;
							// justify-content: space-between;
							// align-items: center;
							// margin-top: 2rpx;
							margin-left: 6rpx;
							position: absolute;
							left: 0;
							// top: 1rpx;
							width: 280rpx;
							height: 4rpx;
							background: #63EAEE;
						}
					}
				}
			}

			// 已实名
			.progress1 {
				margin-left: 53rpx;
				width: 300rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				// align-items: center;
				// margin-left: 30rpx;
				.name {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #929196;
					margin-bottom: 20rpx;
				}

				.tip {
					margin-right: -50rpx;
					display: flex;
					justify-content: flex-end;
					// align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #FFFFFF;
					// margin-bottom: 20rpx; 
					margin-top: 20rpx;
				}

				.line {
					.balls {
						display: flex;
						justify-content: space-between;
						align-items: center;
						width: 100%;
						height: 8rpx;
						background: #67656C;
						position: relative;

						.ball {
							width: 20rpx;
							height: 20rpx;
							background: #67656C;
							border-radius: 50%;
							display: flex;
							justify-content: center;
							align-items: center;

							view {
								width: 6rpx;
								height: 6rpx;
								border-radius: 50%;
								background: #fff;
								z-index: 999;
								// margin-left: 5rpx;
							}

							.boll {
								// margin-left: 4rpx;
								width: 14rpx;
								height: 14rpx;
								border-radius: 50%;
								background: #63EAEE;
								display: flex;
								justify-content: center;
								align-items: center;

								view {
									width: 6rpx;
									height: 6rpx;
									border-radius: 50%;
									background: #fff;
									z-index: 999;
									// margin-left: 5rpx;
								}
							}
						}

						.balls2 {
							// display: flex;
							// justify-content: space-between;
							// align-items: center;
							// margin-top: 2rpx;
							margin-left: 6rpx;
							position: absolute;
							left: 0;
							// top: 1rpx;
							width: 270rpx;
							height: 4rpx;
							background: #63EAEE;
						}
					}
				}
			}

			// 没实名
			.progress2 {
				margin-left: 53rpx;
				width: 300rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				// align-items: center;
				// margin-left: 30rpx;
				.name {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #929196;
					margin-bottom: 20rpx;
				}

				.tip {
					display: flex;
					justify-content: center;
					// align-items: center;
					font-weight: 400;
					font-size: 18rpx;
					color: #FFFFFF;
					// margin-bottom: 20rpx; 
					margin-top: 20rpx;
				}

				.line {
					.balls {
						display: flex;
						justify-content: space-between;
						align-items: center;
						width: 100%;
						height: 8rpx;
						background: #67656C;
						position: relative;

						.ball {
							width: 20rpx;
							height: 20rpx;
							background: #67656C;
							border-radius: 50%;
							display: flex;
							justify-content: center;
							align-items: center;

							view {
								width: 6rpx;
								height: 6rpx;
								border-radius: 50%;
								background: #fff;
								z-index: 999;
								// margin-left: 5rpx;
							}

							.boll {
								// margin-left: 4rpx;
								width: 14rpx;
								height: 14rpx;
								border-radius: 50%;
								background: #63EAEE;
								display: flex;
								justify-content: center;
								align-items: center;

								view {
									width: 6rpx;
									height: 6rpx;
									border-radius: 50%;
									background: #fff;
									z-index: 999;
									// margin-left: 5rpx;
								}
							}
						}

						.balls2 {
							// display: flex;
							// justify-content: space-between;
							// align-items: center;
							// margin-top: 2rpx;
							margin-left: 6rpx;
							position: absolute;
							left: 0;
							// top: 1rpx;
							width: 130rpx;
							height: 4rpx;
							background: #63EAEE;
						}
					}
				}
			}
		}

		.loadmores {
			height: 90rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.arrow {
				height: 28rpx;
				width: 22rpx;
			}

			.more {
				margin: 0 6rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #66656C;
			}
		}
	}


}

.btn {
	margin: 0 50rpx;
	line-height: 100rpx;
	text-align: center;
	height: 100rpx;
	border-radius: 50rpx;
	font-weight: bold;
	font-size: 34rpx;
	color: #141414;
}

.bg1 {
	background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
}

.bg2 {
	background: linear-gradient(90deg, #FFE32F 0%, #FEB22F 100%);
}

.bg3 {
	background: #53505D;
}

.banner {

	>image {
		width: 100%;

		height: 300rpx;

	}
}

.invite {
	height: 120rpx;
	background: #141816;
	border-radius: 24rpx;
	margin: 0 50rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	>text {
		font-weight: bold;
		font-size: 34rpx;
		color: #63EAEE;
		margin-right: 40rpx;
	}

	>view {
		display: flex;
		flex-direction: column;
		width: 100%;

		text {
			margin-left: 28rpx;
			// display: block;
			// word-wrap: break-word;
			// /* 旧版本的浏览器支持 */
			// overflow-wrap: break-word;
			// /* 标准属性 */
			line-height: 32rpx;
			// width: 280rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}
}

.title {
	margin: 44rpx 0 0 50rpx;

	>text {
		font-weight: 400;
		font-size: 34rpx;
		color: #FFFFFF;
	}

	view {
		margin-top: 19rpx;
		display: flex;
		align-items: center;

		image {
			width: 24rpx;
			height: 24rpx;
		}

		text {
			margin-left: 12rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #a6a6a6;
		}
	}
}


.anim {
	animation: hidetip 5s 1;
	animation-fill-mode: forwards;
	-webkit-animation: showtip 5s 1;
	-webkit-animation-fill-mode: forwards;
}

@keyframes hidetip {
	from {
		opcity: 1;
	}

	to {
		opacity: 0;
	}
}

@-webkit-keyframes hidetip {
	from {
		opcity: 1;
	}

	to {
		opacity: 0;
	}
}

.box {
	position: absolute;
	top: 0;
	right: 0;
}

.fade-enter-active,
.fade-leave-active {
	transition: transform 0.5s ease, opacity 0.5s ease;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
	{
	transform: scale(0);
	opacity: 0;
}
</style>