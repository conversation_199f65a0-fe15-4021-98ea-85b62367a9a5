<template>
  <view :style="{ paddingBottom: 'env(safe-area-inset-bottom)', }" class="isapp">
    <!-- 遮罩层 -->
    <view v-if="helpoptionsShow" class="mask" @click="helpoptionsShow = false"></view>
    <view class="head_bg" @touchstart="checkClickOutside">
      <view class="barHeight"></view>
      <view class="title" id='contentTop'>
        <view class="left">
          <image :src="indictor.icon" v-if="indictor.icon"></image>
          <image v-else
            src="https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png" />
          <view class="topinfo">
            <view class="top">
              <!--              bitshow = true-->
              <!-- <image @click="indexS = true"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241219/24fcccbfc56ac69a6aa77c1915417e62_282x88.png">
              </image> -->

              <view class="checkindex" @click="indexS = true">
                <text v-if="indictor.name && indictor.name.length < 4" style="margin-right: 40rpx;">{{ indictor.name
                  }}</text>
                <text v-else-if="indictor.name && indictor.name.length >= 4" style="margin-right: 28rpx;">{{
                  indictor.name }}</text>

                <text v-else style="margin-right: 15rpx;">BIT指数</text>
              </view>

            </view>
            <view style="display: flex;align-items: center;">
              <!-- <text> -->
              <view class="firstprice" :style="{ color: indictor.rate >= 0 ? '#FE556C' : '#6CFF8A' }"
                v-if="indictor.rate != null"> ￥{{ indictor.price }}</view>
              <view class="firstprice" :style="{ color: latestPriceBVexchange - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }"
                v-else> ￥{{ latestPrice }}</view>

              <!-- <view style="width: 16rpx;"></view> -->

              <view class="rates" v-if="indictor.rate != null"
                :style="{ color: indictor.rate >= 0 ? '#FE556C' : '#6CFF8A' }">
                <text v-if="indictor.rate >= 0">+</text>
                <text v-else>-</text>
                {{ Math.abs(indictor.rate) }}%
              </view>

              <view class="rates" v-else
                :style="{ color: latestPriceBVexchange - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }">
                <text v-if="latestPriceBVexchange - aimPrice >= 0">+</text>
                <text v-else>-</text>
                {{ fixrate }}%
              </view>
              <!-- </text> -->
            </view>
          </view>
        </view>
        <view class="right">
          <image @click="makerightshow"
            src="https://cdn-lingjing.nftcn.com.cn/image/20241111/a8f1ad2f079a149534ec30cbc1c576d1_70x16.png">
          </image>
          <transition name="expand-slide">
            <view class="helpoption" v-show="helpoptionsShow">
              <view>
                <view v-for="(item, index) in RightOption" :key="index" class="Roptions" @click="handleRight(item)">
                  <image class="Rimg" :src="item.img"></image>
                  <text>{{ item.text }}</text>
                </view>
              </view>
            </view>
          </transition>
          <!--          <view class="container">-->
          <!--            <transition name="slide">-->
          <!--              <view class="tiyanBouns" @click="gomission('mission')"-->
          <!--                    :class="{ active: activeIndex === 0 }" v-show="activeIndex === 0 && isrightCorner">-->
          <!--                <image mode="widthFix"-->
          <!--                       src="https://cdn-lingjing.nftcn.com.cn/image/20240807/02f728123243a172006017e84374cde5_68x70.png"/>-->
          <!--                <text>领取体验金</text>-->
          <!--              </view>-->
          <!--            </transition>-->
          <!--            <transition name="slide">-->
          <!--              <view class="tiyanBouns" @click="gomission('TradingCompetition')"-->
          <!--                    :class="{ active: activeIndex === 1 }" v-show="activeIndex === 1 && isInTransitionVS">-->
          <!--                <image mode="widthFix"-->
          <!--                       src="https://cdn-lingjing.nftcn.com.cn/image/20241010/eb0beff6fdfc0dc6c579c66fad5f007c_88x88.png"/>-->
          <!--                <text>交易赛</text>-->
          <!--              </view>-->
          <!--            </transition>-->
          <!--          </view>-->

          <!--          <view class="rights" @click="bitshow = true" style="position: relative;">-->
          <!--            <text>{{ bit || '0.00' }}</text>-->
          <!--          </view>-->
          <!-- <view class="tiyanBouns" v-if="isrightCorner" @click="gomission">
              <image mode="widthFix"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240807/02f728123243a172006017e84374cde5_68x70.png" />
              <text>领取体验金</text>
          </view> -->
        </view>
      </view>

      <view class="time">
        <text v-for="(item, index) in times" :key="index" @click="checks(item, index)"
          :class="{ timeActive: nowTime == index }">{{ item.name }}
        </text>
      </view>

      <view class="Kline">
        <KForm ref="child" :message="clickData" :period="period" :selectTopHeight='selectTopHeight'
          :currentIndicator="currentIndicator" :selectedLeft="selectedLeft" :selectedRight="selectedRight"
          :selectBottomHeight='selectBottomHeight' :statusBarHeight="statusBarHeight" :componentKey="componentKey"
          v-if="isStartForm">
        </KForm>
        <!-- :selectTopHeight='selectTopHeight' :selectBottomHeight='selectBottomHeight' -->
      </view>

      <!--        指标切换-->
      <view class="index">
        <view v-for="(item) in leftIndex" :key="item.id" class="inc" @click="selectLeftItem(item)">
          <text :class="{ activeIndex: selectedLeft === item }">
            {{ item }}
          </text>
        </view>

        <view class="separator"></view>

        <!-- 右边部分：VOL, MACD, KDJ, RSI -->
        <view v-for="(item, index) in rightIndex" :key="index" class="inc" @click="selectRightItem(item)">
          <text :class="{ activeIndex2: selectedRight === item }">
            {{ item }}
          </text>
        </view>
      </view>

      <view class="newProfit">
        <view class="left" @tap="showNotice" v-if="showprofit">
          <image src="https://cdn-lingjing.nftcn.com.cn/image/20241114/1a07b5134a4e31f91cd8f901eac260e0_32x40.png"
            mode="widthFix"></image>
          <text class="addr">{{ profitobj.addr }}</text>
          <text class="profits">{{
            '盈利￥' + Math.ceil(profitobj.closeProfit) + '(' +
            accMul(profitobj.closeProfitRate, 100) +
            '%)'
          }}
          </text>
        </view>
        <view class="left" v-else></view>
        <view class="rate">
          <text>资金费率/倒计时</text>
          <text>{{ ((fundrate * 100 * 100) / 100).toFixed(2) || '0.00' }}%/{{ countdown }}</text>
        </view>
      </view>

      <!-- :style="{ height: lower ? '810rpx' : '780rpx' }"-->
      <view class="order" id='contentBottom' :style="{ height: lower ? '640rpx' : '640rpx' }">
        <!--        <view class="head">-->
        <!--          <view class="time">-->
        <!--            <text v-for="(item, index) in times" :key="index" @click="checks(item, index)"-->
        <!--                  :class="{ timeActive: nowTime == index }">{{ item.name }}-->
        <!--            </text>-->
        <!--          </view>-->
        <!--          <view class="notice">-->
        <!--            <view class="pro" @tap="showNotice">-->
        <!--              <image style="border-radius: 50%;" :src='profitobj.image'/>-->
        <!--              <text>{{ profitobj.addr }}</text>-->
        <!--              <text>{{-->
        <!--                  '盈利 ￥ ' + Math.ceil(profitobj.closeProfit) + '(' +-->
        <!--                  accMul(profitobj.closeProfitRate, 100) +-->
        <!--                  '%)'-->
        <!--                }}-->
        <!--              </text>-->
        <!--            </view>-->
        <!--            <view class="rate">-->
        <!--              <text>资金费率/倒计时</text>-->
        <!--              <text>{{ ((fundrate * 100 * 100) / 100).toFixed(2) || '0.00' }}%/{{ countdown }}</text>-->
        <!--            </view>-->
        <!--          </view>-->
        <!--        </view>-->

        <!--        专业版-->
        <view class="buy" v-if="!foolPartent" :style="{
          backgroundImage: isBuy
            ? 'url(' + buyImageUrl + ')'
            : 'url(' + sellImageUrl + ')',
        }">
          <view class="left">
            <view class="parent">
              <view class="Long">S</view>
              <view class="left" :style="{ width: getlsWidth(sellTotalAmount) * 2.18 + 'rpx' }">
                <text>{{ getlsnum(sellTotalAmount) + '%' }}</text>
              </view>
              <view class="right" :style="{ width: getlsWidth(buyTotalAmount) * 2.18 + 'rpx' }">
                <text>{{ getlsnum(buyTotalAmount) + '%' }}</text>
              </view>
              <view class="short">L</view>
            </view>

            <!-- :style="{ width: item.price * 40 + 'rpx' }" :style="{ width: item.price + 'rpx' }"-->

            <view class="buyorder" v-if="orderBook.sell && orderBook.sell.length > 0">
              <view class="item" v-for="(item, index) in orderBook.sell.slice(-5)" :key="item.id"
                @click="makeprice('sell', item)">
                <view :style="{ width: getWidth(item.amount, 'sell') + '%' }">
                  <text>{{ formatNumber(item.price, 3) }}</text>
                </view>
                <text>{{ item.amount }}</text>
              </view>
            </view>
            <view v-else style="height: 200rpx;"></view>

            <view class="mid">
              <view :style="{ color: midside == 'BUY' ? '#EC4068' : '#6CFF8A' }">￥{{ (latestPrice) || 0 }}</view>
              <view class="markprice" @click=" bitshow = true">
                <text>标记价格:{{ bit || '0.00' }}</text>
              </view>
            </view>

            <!-- ￥{{ Number(latestPrice).toFixed(2) || 0 }}</view> -->

            <view class="sellorder" v-if="orderBook.buy && orderBook.buy.length > 0">
              <view class="item" v-for="(item, index) in orderBook.buy.slice(0, 5)" :key="item.id"
                @click="makeprice('buy', item)">
                <view :style="{ width: getWidth(item.amount, 'buy') + '%' }">
                  <text>{{ formatNumber(item.price, 3) }}</text>
                </view>
                <text>{{ item.amount }}</text>
              </view>
            </view>
          </view>
          <view class="right">
            <view class="Trading_pair-tab">
              <view class="item" :class="{ acleft: leftactive == 1 }" @click="changeTab(1)">
                <text>唱多</text>
              </view>
              <view class="item" :class="{ acleft: leftactive == 2 }" @click="changeTab(2)">
                <text>唱空</text>
              </view>
              <!-- background: linear-gradient(144deg, #FF5270 0%, #FB6F46 100%); -->

              <view class="bgleft" :style="{ marginLeft: isBuy ? ' 170rpx' : '0' }" :class="[
                isBuy == true ? 'buybg' : 'sellbg',
                'left2',
                leftactive == 2,
              ]">
              </view>
            </view>

            <view class="tips">
              <!-- color: #EC4068; -->
              <!-- color: #6CFF8A; -->
              <image
                :src="!isBuy ? 'https://cdn-lingjing.nftcn.com.cn/image/20240729/52fbc4c115a25a14f0c93ee5f0cd93e5_46x46.png' : 'https://cdn-lingjing.nftcn.com.cn/image/20240807/547cdf1fe599652f04f370ba9bf0fe14_46x46.png'" />
              <text :style="{ color: isBuy ? '#6CFF8A' : '#EC4068' }"> {{
                isBuy ? '唱空：后续下跌则盈利' :
                  '唱多：后续上涨则盈利'
              }}
              </text>
            </view>

            <!-- 输入价格 -->
            <view class="prices">
              <view class="left">
                <text>{{ !isBuy ? '唱多价' : '唱空价' }}</text>
                <view class="inputs" :style="{ marginLeft: marginRight + 'rpx' }">
                  <text :style="{ color: markCss ? '#A6A6A6' : '' }">￥</text>
                  <input :style="{ color: markCss ? '#A6A6A6' : '' }" type="digit" ref="priceInput" :focus="focus"
                    @input="onInputChange" :disabled="marktype == 2" v-model="orderprice" />
                </view>
                <!-- @input="limitDecimalPlaces" -->
              </view>
              <view class="rights" @click="marktypeshow = true">
                <text>{{ marktype == 1 ? '限价' : '市价' }}</text>
                <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240812/fc05e6697a58a675ae2390474282cb59_28x24.png" />
              </view>

              <view class="float" v-if="marktypeshow" @touchstart.stop>
                <text @tap="checkmarkettype(2)">市价单</text>
                <text @tap="checkmarkettype(1)">限价单</text>
              </view>
            </view>

            <!-- 可用余额 -->
            <view class="balance">
              <text class="lefts">可用余额</text>
              <text class="rights">￥{{ userinfo.balance || 0 }}
                <text class="inside" v-if="userinfo.experienceMoney > 0">
                  {{ ' + 体验金¥' + userinfo.experienceMoney }}
                </text>
              </text>
            </view>


            <view class="makeorer">


              <view v-if="!isEmptyObject(allgold)" class="amount gray" @click="cancelbonus">
                <text class="Bouns1">万能金</text><br>
                <text class="Bouns2">¥{{ allgold.amount }}</text>
              </view>
              <!-- 金额 {{ userinfo.experienceMoney }}-->
              <view class="amount" :class="lower ? 'input-error' : 'normal-border'" v-else>
                <text>金额</text>
                <!-- <label>¥ style="text-align: center"</label> -->
                <!-- dir="rtl" -->
                <view :style="{ marginLeft: marginLeftVol + 'rpx' }">
                  <text>¥</text>
                  <input v-model="orderamount" @input="onVolInputChange" type="digit" />
                </view>
              </view>



              <view class="amount" v-if="usingList.length" :class="lower ? 'input-error' : 'normal-border'"
                @click="popupshow = true">
                <text class="Bouns">用万能金下单</text>
              </view>
            </view>
            <!-- 提示文字，当金额小于20时显示 -->
            <text v-if="lower" class="warning-text">
              最低下单金额15元
            </text>
            <!-- 杠杆 -->
            <view class="sliderBox">
              <text>杠杆</text>
              <view style="height: 22rpx;"></view>
              <!-- <u-slider style="margin-top: 20rpx;" max="100" @end="endMove" v-model="sliderValue" @tap="onClick()"
                inactive-color="#434343" active-color="#959595" step="20" :use-slot="true">
                <view class="" style="display: flex;justify-content: space-between;">
                  <view class="badge-button"></view>
                </view>
              </u-slider>

              <view class="slidelevel">
                <text v-for="(item, index) in levelList" :style="{ color: index == currentfff ? '#fff' : '' }"
                  :key="index">{{ item }}
                </text>
              </view> -->
              <custom-slider v-model="sliderValue" :points="levelList" :disabledPoints="disabledPoints" />
              <view style="height: 34rpx;"></view>

            </view>


            <view v-if="isshowbalance" class="btn" :class="[isBuy ? 'buybg' : 'sellbg']" @click="submitOrder">
              <text :style="{ fontSize: '28rpx' }">{{ isBuy ? '唱空' : '唱多' }}</text>
            </view>
            <!-- nobalance = true -->
            <view v-if="!isshowbalance" class="nomoneybg btn" @click="goCharge">
              <text :style="{ fontSize: '24rpx' }">{{ '余额不足，去充值' }}</text>
            </view>
          </view>
        </view>

        <!--简易版-->
        <view class="buy2" v-else :style="{
          backgroundImage: isBuy
            ? 'url(' + buyImageUrl + ')'
            : 'url(' + sellImageUrl + ')'
        }">
          <view class="right">
            <view class="Trading_pair-tab">
              <view class="item" :class="{ acleft: leftactive == 1 }" @click="changeTab(1)">
                <text>唱多</text>
              </view>
              <view class="item" :class="{ acleft: leftactive == 2 }" @click="changeTab(2)">
                <text>唱空</text>
              </view>
              <!-- background: linear-gradient(144deg, #FF5270 0%, #FB6F46 100%); -->

              <view class="bgleft" :style="{ marginLeft: isBuy ? ' 324rpx' : '0' }" :class="[
                isBuy == true ? 'buybg' : 'sellbg',
                'left2',
                leftactive == 2,
              ]">
              </view>
            </view>

            <!-- 输入价格 -->
            <view class="prices">
              <view class="right" @click="marktypeshow = true">
                <text>{{ marktype == 1 ? '限价' : '市价' }}</text>
                <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240812/fc05e6697a58a675ae2390474282cb59_28x24.png" />
              </view>
              <view style="width: 32rpx"></view>
              <view class="left">
                <text :style="{ color: markCss ? '#A6A6A6' : '' }">{{ !isBuy ? '唱多价' : '唱空价' }}</text>
                <view style="width: 67rpx;"></view>
                <!--                :style="{ marginLeft: marginRight + 'rpx' }"-->
                <view class="inputs">
                  <text :style="{ color: markCss ? '#A6A6A6' : '' }">￥</text>
                  <input :style="{ color: markCss ? '#A6A6A6' : '' }" type="digit" ref="priceInput" :focus="focus"
                    @input="onInputChange" :disabled="marktype == 2" v-model="orderprice" />
                </view>
                <!-- @input="limitDecimalPlaces" -->
              </view>


              <view class="float" v-if="marktypeshow" @touchstart.stop>
                <text @tap="checkmarkettype(2)">市价单</text>
                <text @tap="checkmarkettype(1)">限价单</text>
              </view>
            </view>
            <!-- 金额 {{ userinfo.experienceMoney }}-->

            <view v-if="!isEmptyObject(allgold)" class="amount gray" @click="cancelbonus">
              <text class="Bouns1">万能金</text><br>
              <text class="Bouns2" style="margin-top: -12rpx;line-height: 16rpx;">¥{{ allgold.amount }}</text>
            </view>

            <view class="amount" :class="lower ? 'input-error' : 'normal-border'" v-else>
              <text>金额</text>

              <view>
                <text>¥</text>
                <input v-model="orderamount" @input="onVolInputChange" type="digit" />
              </view>
            </view>



            <!-- 提示文字，当金额小于20时显示 -->
            <text v-if="lower" class="warning-text">
              最低下单金额15元
            </text>
            <!-- 可用余额 -->
            <view class="balance">
              <text class="lefts">可用余额</text>
              <text class="rights">￥{{ userinfo.balance || 0 }}
                <text class="inside" v-if="userinfo.experienceMoney > 0">
                  {{ ' + 体验金¥' + userinfo.experienceMoney }}
                </text>
              </text>
            </view>


            <!-- 杠杆 -->
            <view class="sliderBox">
              <text>杠杆</text>
              <view style="height: 35rpx"></view>
              <custom-slider v-model="sliderValue" :easy="true" :points="levelList" :disabledPoints="disabledPoints" />

              <view style="height: 55rpx;"></view>

            </view>


            <view v-if="isshowbalance" class="btn" :class="[isBuy ? 'buybg' : 'sellbg']" @click="submitOrder">
              <text :style="{ fontSize: '28rpx' }">{{ isBuy ? '唱空' : '唱多' }}</text>
            </view>
            <!-- nobalance = true -->
            <view v-if="!isshowbalance" class="nomoneybg btn" @click="goCharge">
              <text :style="{ fontSize: '24rpx' }">{{ '余额不足，去充值' }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 持仓 -->
      <Positions @getaimobj="getaimobj" @popupClose="popupClose" @getInvite="getInvite" @fetchUsers="fetchUsers"
        :timetofetchPosition="timetofetchPosition" :isreach="isreach" @checkRightCon="checkRightCon"
        @Bogetbonus="getbonus" :ispull="ispull" :newprice="newprice" :currentIndicator="currentIndicator" />
    </view>


    <!-- 加载中loading -->
    <u-modal class="" v-model="isLinkLoadding" width="40%" :show-title="false" :show-confirm-button="false">
      <view class="sk-wave"></view>
      <view class="text_msg" style="
            padding: 10rpx 20rpx 40rpx 20rpx;
            text-align: center;
            font-size: 26rpx;
            line-height: 40rpx;
          ">
        加载中...
      </view>
    </u-modal>

    <!-- 给用户看大户盈亏 -->
    <u-modal class="" v-model="proLoss" width="360rpx" borderRadius="36" :show-title="false" :mask-close-able="true"
      :show-confirm-button="false">
      <view class="modalpro">
        <view class="modalhead">
          <image style="border-radius: 50%;" :src='profitobj.image' />
          <text>{{ profitobj.addr }}</text>
        </view>

        <view class="profit">
          <text>盈利</text>
          <text>+￥{{ Math.ceil(profitobj.closeProfit) }}</text>
        </view>

        <view class="bom">
          <view>
            <text>{{ profitobj.side == 'BUY' ? '唱多价' : '唱空价' }}</text>
            <text>{{ '¥' + profitobj.openPrice }}</text>
          </view>
          <view>
            <text>{{ profitobj.side == 'BUY' ? '平多价' : '平空价' }}</text>
            <text>{{ '¥' + profitobj.closePrice }}</text>
          </view>
          <view>
            <text>倍数</text>
            <text>{{ profitobj.leverageLevel + 'x' }}</text>
          </view>
        </view>
      </view>
    </u-modal>

    <!-- 新手指导 -->
    <u-popup v-model="isGuidance" mode="center" class="guidance_body">
      <view class="image" v-for="(item, index) in guidanceList" :key="index">
        <img :src="item.src" v-show="index == guidanceShow" mode="widthFix" @click="guidanceCheck(index)">
      </view>
    </u-popup>

    <!-- 当前余额不足-->
    <u-modal class="" v-model="nobalance" width="600rpx" borderRadius="36" :show-title="false" :mask-close-able="true"
      :show-confirm-button="false">
      <view class="nomoney">
        <text>当前余额不足</text>
        <view class="charge" @click="goCharge">去充值</view>
      </view>
    </u-modal>

    <!-- bit指数 -->
    <u-modal class="" v-model="bitshow" width="600rpx" borderRadius="36" :show-title="false" :mask-close-able="true"
      :show-confirm-button="false">
      <view class="nomoney">
        <text class="bittitle">BIT指数标记价格：
          <text style="color: #63EAEE;">{{ bit }}</text>
        </text>
        <text class="bittitle2">BIT指数标记价格反映加密货币市场行情。</text>
        <view class="bitline"></view>
        <view class="bitbody">
          由以下6种加密货币以及相应权重拟合而成：
          <text class="midcolor"> BTC(40%)、 ETH(20%)、BNB(10%)、SOL(10%)、DOGE(10%)、XRP(10%)。</text>
          <br />
          <br />

          每个货币价格变动百分比*对应权重，6个数据加总*3后得到BIT指数标记价格变动百分比。
        </view>

        <view class="bitbtn">
          <view @click="bitshow = false">返回</view>
          <view @click="gonotice">帮助中心</view>
        </view>
      </view>
    </u-modal>

    <!-- 30s后弹出关闭bit通知 -->
    <u-modal class="" v-model="showSetting" :maskCloseAble="false" width="600rpx" borderRadius="36" :show-title="false"
      :mask-close-able="true" :show-confirm-button="false">
      <view class="closenotice">
        <text class="bittitle">衍生</text>
        <view class="bitline"></view>
        <text class="midcolor">是否接受"衍生"板块相关公告提醒?</text>
        <!-- <text class="bitbody ">
        </text> -->

        <view class="bitbtn">
          <view @click="showSetting = false">不，下次</view>
          <view @click="gosettingnotice">好的，关注</view>
        </view>
      </view>
    </u-modal>

    <!-- 确认大单下单 -->
    <u-modal class="" v-model="isBigOrder" width="600rpx" borderRadius="36" :show-title="false" :mask-close-able="true"
      :show-confirm-button="false">
      <view class="BigOrder">
        <view class="top">
          本次下单金额较大，可能滑点过高给您带来损失，确定要下单吗？
        </view>
        <view style="height: 60rpx;"></view>
        <view class="bombtn">
          <view @click="confirmBigOrder" class="sure">
            <image mode="widthFix"
              src="https://cdn-lingjing.nftcn.com.cn/image/20240826/05ff910ae8daa898c0778ae23e1fcb98_112x112.png" />
            <view class="btnright">
              <text>确定下单</text>
              <text>我希望尽快成交，愿意承担滑点损失</text>
            </view>
          </view>
          <view style="height: 30rpx;"></view>
          <view @click="isBigOrder = false" class="canl">
            <image mode="widthFix"
              src="https://cdn-lingjing.nftcn.com.cn/image/20240826/9d90ccc0b3097dd81b95c0c589ab88ba_112x112.png" />
            <view class="btnright">
              <text>取消下单</text>
              <text> 建议拆成较小金额，多次下单</text>
            </view>
          </view>
        </view>
      </view>
    </u-modal>
    <!--  新用户被邀请盈利 领取体验金 邀请者 userType 1-->
    <u-popup mode="center" :duration='700' v-model="newuserreward">
      <view class="newuser" :class="!newuserreward ? 'rightscale' : ''">
        <view class="card">
          <image mode="widthFix" class="tops"
            src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

          <view class="info">
            <view class="titles">恭喜</view>
            <view class="money">
              <view class="grey">
                <image mode="widthFix" :src="info.avatar" />
              </view>

            </view>
            <view class="tips">{{ info.nickname }}</view>

          </view>

          <view class="msg">
            <text style="width:506rpx">
              {{ '您已经完成任务，免费获得￥15体验金盈利可以免费提现！' }}
            </text>
          </view>

          <view class="btn" @tap="fetchgetbonus">领取</view>
        </view>
      </view>
    </u-popup>

    <TabBar @changeSymbol="changeSymbolTab" :initialActiveIndex="initialActiveIndex" :outside="outside"></TabBar>
    <popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false" @confirm="nav_realName()">
    </popup-bar>

    <u-popup v-model="indexS" width="484rpx" mode="left">
      <view class="mainindex">
        <text class="comp">对标物</text>
        <view style="height: 69rpx"></view>

        <view class="comp-item" @click="changeSymbol('')">
          <view class="img">
            <image src="https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png"
              mode="widthFix"></image>
          </view>


          <view class="name">BIT指数</view>
          <view class="price-box">
            <text class="price"
              :style="{ color: Number(latestPriceBVexchange) - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }">

              <text style="margin-right: 4rpx;">¥</text>{{
                (latestPriceBVexchange) || 0 }}<text style="font-size: 18rpx;">/份</text></text>
            <text class="bom"><text style="margin-right: 5rpx;">¥</text>{{ bitFix
              }}</text>
          </view>

          <view class="change"
            :style="{ color: Number(latestPriceBVexchange) - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }">
            <text v-if="Number(latestPriceBVexchange) - aimPrice >= 0">+</text>
            <text v-else>-</text>
            {{ fixrate }}%
          </view>
        </view>

        <view class="comp-item" v-for="(item, index) in indexList" :key="index" @click="changeSymbol(item)">

          <view class="img">
            <image :src="item.icon" mode="widthFix"></image>
          </view>

          <view class="name">{{ item.name }}</view>
          <view class="price-box">
            <text class="price" :style="{ color: item.rate >= 0 ? '#FE556C' : '#6CFF8A' }"><text
                style="margin-right: 4rpx;">¥</text>{{
                  item.price.toFixed(2)
                }}<text style="font-size: 18rpx;">/份</text></text>
            <text class="bom" style="margin-left: -4rpx;">{{
              formatCurrency(item.markPrice, item.contractName)
            }}</text>
          </view>
          <text class="change" :style="{ color: item.rate >= 0 ? '#FE556C' : '#6CFF8A' }">
            <text v-if="item.rate >= 0">+</text>
            <text v-else>-</text>
            {{ Math.abs(item.rate) }}%</text>
        </view>
      </view>
    </u-popup>

    <view style="position: fixed; left: 0;bottom: 376rpx;z-index: 2;" v-if="isInTransitionVS">
      <image @click="gocom()"
        src="https://cdn-lingjing.nftcn.com.cn/image/20241119/8f5f3a60de7607ddc45b1992b98216db_512x184.png"
        class="tiyan1" />
      <image @click="isInTransitionVS = false"
        src="https://cdn-lingjing.nftcn.com.cn/image/20241119/9e13632f6b90eabde2ec7080e58b5602_200x200.png"
        class="tiyanx2" />
    </view>

    <view style="position: fixed; left: 0;bottom: 196rpx;z-index: 2" v-if="isrightCorner">
      <image @click="gomiss()"
        src="https://cdn-lingjing.nftcn.com.cn/image/20241119/009ee45a2ce7a5c70206fc5eced3652c_350x276.png"
        class="tiyan" />
      <image @click="isrightCorner = false"
        src="https://cdn-lingjing.nftcn.com.cn/image/20241119/9e13632f6b90eabde2ec7080e58b5602_200x200.png"
        class="tiyanx" />
    </view>

    <!-- 答题弹窗 -->
    <u-popup v-model="showPopup" mode="center" :maskCloseAble="false">
      <view class="popup-container">
        <view class="popup-title">开杠开通确认</view>
        <view class="popup-title1">尊敬的用户：</view>
        <view class="popup-content">
          为了便于您了解自身的风险承受能力，选择合适的投资产品和服务，请您填写以下风险承受能力评估问卷。下列问题可协助评估您对投资产品和服务的风险承受能力，请您根据自身情况认真选择。评估结果仅供参考，不构成投资建议。
        </view>
        <u-button type="primary" class="popup-button" @click="startTest">
          进行测试
        </u-button>
      </view>
    </u-popup>

    <u-popup v-model="popupshow" mode="center" width="710" border-radius="30" :show-title="false"
      :show-confirm-button="false">
      <view class="fast-modal-content">
        <view class="right_close">
          <image @click="popupshow = false" src="https://cdn-lingjing.nftcn.com.cn/image/20240920/01a0a376de633fba5c470a604f187804_80x80.png"
            mode="widthFix"></image>
        </view>
        <view class="box">
          <view class="nodata" v-if="!usingList.length">
            <image mode="widthFix"
              src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
            <text>{{ token ? '暂时无数据' : '您还未登录' }}</text>
            <view class="nav_login" @tap="nav_login" v-if="!token">
              登录/注册
            </view>
          </view>
          <view class="box-item" v-for="(item, index) in usingList" :key="index">
            <view class="left">
              <view>
                <text class="symbol">¥</text>
                <text class="num">{{ item.amount }}</text>
              </view>

              <view class="ball"></view>
            </view>
            <view class="right">
              <view class="item-left">
                <view class="timeexp">
                  支持杠杆:{{ formatSupportContract(item.supportLever) }}
                </view>
                <view class="obj">
                  持仓时长:<={{ item.maxPositionTime }}h </view>

                    <view class="timeexp" v-if="item.expireTime">过期时间:{{ item.expireTime.slice(0, 10) || '--' }}
                    </view>
                </view>
                <view class="item-right" @click="gobit(item)">
                  立即使用
                </view>

              </view>
            </view>


          </view>

        </view>
    </u-popup>
  </view>
</template>

<script>

import popupBar from "@/components/public/PopupBar";
import TabBar from "@/components/public/TabBar";
import Positions from "./components/position.vue";
import CustomSlider from "@/components/public/CustomSlider.vue";
import { startSocket, onSocketOpen } from "@/utils/websockets"
import store from '@/store'
// #ifdef H5
const form = (resolve) => require(["./components/form/index"], resolve);
// #endif

// #ifdef APP-PLUS
import form from "./components/form/index";
// #endif

import antiShake from "../../../common/public"

export default {
  name: "contract-BITindex",
  components: {
    Positions,
    popupBar,
    TabBar,
    KForm: form,
    CustomSlider
  },
  onReachBottom() {
    this.isreach = !this.isreach
  },
  onPullDownRefresh() {
    this.resetTimers();
    this.firstVisit(); // 收集访问人数

    this.ispull = !this.ispull; // 下拉刷新传值到仓位组件
    setTimeout(() => {
      this.refreshData();
      this.setOrderPrice();
      uni.stopPullDownRefresh(); // 停止下拉刷新动画
    }, 1000);
  },
  data() {
    return {
      disabledPoints: [],
      allgold: {},
      token: uni.getStorageSync("token"),
      usingList: [],
      componentKey: 0, // 用于刷新组件
      usingListTotal: 0,
      popupshow: false,
      showPopup: false,
      newprice: "",
      bitFix: "",
      timerSetting: null,
      showSetting: false,
      aimPrice2: "",
      latestPriceBVexchange: '',
      showprofit: false,
      Indextimer: null,
      showtiyan: false,
      currentIndicator: uni.getStorageSync('currentIndictor') || null, // 初始化
      indictor: {},
      indexList: [
      ],
      indexS: false,
      leftIndex: ['MA', 'BOLL', 'EMA'], // 左边的选项
      rightIndex: ['VOL', 'MACD', 'KDJ', 'RSI'], // 右边的选项
      selectedLeft: '', // 默认左边选中的项
      selectedRight: 'VOL', // 默认右边选中的项
      foolPartent: false,
      isInTransitionVS: false,
      startY: 0, // 记录触摸开始时的 Y 坐标
      endY: 0, // 记录触摸结束时的 Y 坐标
      threshold: 50, // 滑动的阈值
      activeIndex: 1, // 当前显示的 index
      intervalId: null, // 保存定时器ID
      itemsCount: 2, // 轮播项的数量
      fundrate2: 0.000666666666,
      fundrate: 0.0000,
      focus: false,
      LimitPrice: '',
      helpoptionsShow: false, // 初始化
      currentTime: '',
      nextTime: '',
      countdown: '',
      timeNodes: [0, 8, 16], // 时间节点
      lastNodeChecked: null, // 上一次检查的节点时间
      isshowbalance: true,
      marginLeftVol: '',
      lower: false,
      markCss: true,
      isbigordergo: true,
      isBigOrder: false,
      isRegistration: false,
      bitshow: false,
      ispull: false,
      bittimer: null,
      bit: '',
      marginRight: 70,
      profitTimer: null,
      newuserreward: false,
      outside: false,
      timer: null,
      success: 0, // 邀请成功人数
      info: {},
      userinfo: {},
      isrightCorner: false,
      isreach: false,// 判断是否触底
      appUrl: "", // app支付路径
      timetofetchPosition: false, // 下单之后获取持仓
      latestPrice: 0, // 盘口最新价格
      period: '',  // 传过去的周期
      nobalance: false, // 余额不足popup
      orderamount: '',
      orderprice: "",
      bvbalance: 0,
      marktype: 2,
      marktypeshow: false,
      clickData: '',
      guidanceShow: 0,
      guidanceList: [
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20240904/c0aa4c93b2d41498dde04380b511fe72_750x1624.png"
        },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20240904/00b46cd7798496760ed1bd0c5899d3d9_750x1624.png"
        },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20240904/420682d7824510ebce7402529f9c7c35_750x1624.png"
        },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20240904/0e1b62505438170487b326c8478839a1_750x1624.png"
        },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20240904/83918018f4bc3d075ad8ddd19d0a11d7_750x1624.png"
        },
        // {
        //   src: "https://cdn-lingjing.nftcn.com.cn/image/20240826/cd415bfd52d0c8a8f8d65229776c353c_750x1624.png"
        // },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20241119/e11e37d8d1c2872dc9d95eca565abd37_1125x2436.png"
        },
        {
          src: "https://cdn-lingjing.nftcn.com.cn/image/20241119/b2a6b5de8221b6c9c1fd958b26c52c5b_1125x2436.png"
        }
      ],
      isGuidance: false,
      currentfff: 0,
      levelList: ['1X', '2X', '5X', '10X', '20X', '50X'],
      statusBarHeight: 0, //手机状态栏的高度
      isStartForm: false, //用它来开启from表格组件是否开始渲染
      proLoss: false,
      isLinkLoadding: false,
      marks: {
        1: {
          style: {
            fontSize: "20rpx",
          },
          label: "1X",
        },
        33: {
          style: {
            fontSize: "20rpx",
          },
          label: "2X",
        },
        66: {
          style: {
            fontSize: "20rpx",
          },
          label: "5X",
        },
        100: {
          style: {
            fontSize: "20rpx",
          },
          label: "10X",
        },
      },
      sliderValue: 0, //数字 滑动选择器
      leftactive: 1,
      nowTime: 0,
      certification: '',
      times: [
        {
          name: '分时图',
          value: 'area'
        },
        {
          name: "5分钟",
          value: 5,
        },
        {
          name: "30分钟",
          value: 7,
        },
        {
          name: "1小时",
          value: 8,
        },
        {
          name: "4小时",
          value: 4,
        },
        {
          name: "1天",
          value: 12,
        },
        {
          name: "1周",
          value: 6,
        },
      ],
      sellImageUrl:
        "https://cdn-lingjing.nftcn.com.cn/image/20241111/59ec7cb5c98b76d7ef24b7abdbf714ef_1420x1376.png",
      buyImageUrl:
        "https://cdn-lingjing.nftcn.com.cn/image/20241111/46815728d076a74fba84eb572334e180_1420x1376.png",
      isBuy: false,
      orderBook: {
        buy: [],
        sell: [],
      },
      selectTopHeight: 0,
      selectBottomHeight: 0,
      midside: 'BUY',
      aimPrice: '',
      profitobj: {},
      initialActiveIndex: 1
    };
  },
  onLoad(options) {
    this.getList()
    this.initPage();
    let nowcoin = uni.getStorageSync('IndictorInfo')
    if (nowcoin) {
      this.indictor = nowcoin
      console.log(this.indictor, '标记');
    }

    if (!uni.getStorageSync('currentIndictor')) {
      this.indictor = {}
    }

    this.getNewPrice()
    setTimeout(() => {
      this.checkTimeToUpdateIndexS()
    }, 500);

    // 清空定时器
    this.clearTimers();

    // 获取用户配置信息
    this.fetchwhatgobuy();
    this.fetchIndex();
    this.fetchuserInfo();
    this.fetchbit();
    this.fetchUser();
    this.fetchProfit();

    // 获取存储信息
    this.initializeStorage();

    // 判断是否需要弹出设置
    this.checkPushSettings();

    // 设置用户信息相关
    this.updateTime();
    this.Zijintimer = setInterval(this.updateTime, 1000);

    // 其他功能
    this.getSelectheight();
    this.firstVisit(); // 收集访问人数

    // 页面初始化状态
    if (options) {
      this.newuserreward = options.ispop;
    }


    // 收集订单数据
    this.loadOrderBookData();
  },
  methods: {
    cancelbonus() {
      this.allgold = {}
      this.disabledPoints = []
    },
    isEmptyObject(obj) {
      // 判断是否为空对象
      return Object.keys(obj).length === 0 && obj.constructor === Object;
    },
    getRemainingIndexes(fixedIndexes, fixedValues, availableValues) {
      // 找到后端返回值在固定值数组中的索引
      const enabledIndexes = availableValues.map(value => fixedValues.indexOf(value));
      // 返回第一个数组中排除掉 enabledIndexes 的结果
      return fixedIndexes.filter(index => !enabledIndexes.includes(index));
    },
    getEnabledIndexesIncludes(fixedIndexes, fixedValues, availableValues) {
      // 找到后端返回值在固定值数组中的索引
      const enabledIndexes = availableValues.map(value => fixedValues.indexOf(value));
      // 返回第一个数组中排除掉 enabledIndexes 的结果
      return fixedIndexes.filter(index => enabledIndexes.includes(index));
    },
    gobit(item) {
      console.log(item);
      if (item.supportLever.includes(0)) {
        this.disabledPoints = []
        this.sliderValue = 0
      } else {
        const fixedArray = [0, 1, 2, 3, 4, 5]; // 固定的索引
        const fixedValues = [1, 2, 5, 10, 20, 50]; // 固定的值数组
        this.disabledPoints = this.getRemainingIndexes(fixedArray, fixedValues, item.supportLever)
        this.sliderValue = this.getEnabledIndexesIncludes(fixedArray, fixedValues, item.supportLever)[0]
      }
      this.popupshow = false
      this.allgold = item
    },
    nav_login() {
      this.$Router.push({
        name: 'mainLogin',
      })
    },
    // 格式化 supportContract 数组
    formatSupportContract(arr) {
      return arr
        .map((item, index) => {
          return item == 0 ? '无限制' : item + 'X';  // 处理 0 为 '无限制'
        })
        .join('、') // 将数组用 '/' 拼接
        .replace(/\/$/, ''); // 去掉结尾的 '/'
    },
    async getList() {
      const contractName = uni.getStorageSync('currentIndictor');

      let res = await this.$api.couponList({
        contract: contractName ? contractName.split('-')[1] : 'BIT',
        pageNum: 1,
        pageSize: 10,
        status: 1
      })
      if (res.status.code == 0) {
        this.usingList = res.result.list
        this.usingListTotal = res.result.totalCount
      }
    },
    startTest() {
      this.showPopup = false;
      this.$Router.push({
        name: 'riskAssessment'
      })
      // 在此处添加开始测试的逻辑
    },
    changeSymbolTab(e) {
      console.log(e, '出演过来的');
      if (!e) { this.indictor = {}; this.currentIndicator = ""; return }
      this.currentIndicator = e
      for (let i = 0; i < this.indexList.length; i++) {
        const element = this.indexList[i];
        if (e == element.contractName) {
          this.indictor = element
          uni.setStorageSync('IndictorInfo', element)
        }
      }
    },
    fetchUsers(e) {
      this.bvbalance = e.balance
      this.userinfo = e
      if (e.experienceMoney) {
        if (e.balance >= 0) {
          this.isshowbalance = true

        } else {
          this.isshowbalance = false
        }
      } else {
        if (e.balance >= 15) {
          this.isshowbalance = true
        } else {
          this.isshowbalance = false
        }
      }
    },
    getInvite(e) {
      this.success = e.successNum
      this.info = e
    },

    checkRightCon(e) {
      this.isrightCorner = e
    },
    formatCurrency(amount, contractName) {
      let initstr = Number(amount)
      // if (contractName == 'E-BNB-USDT' || contractName == 'E-SOL-USDT') {
      //   // bnb/10, sol/10
      //   fixedAmount = (Number(initstr) / 10).toFixed(2);
      // } else if (contractName == 'E-DOGE-USDT' || contractName == 'E-XRP-USDT') {
      //   // , doge * 10, xrp * 10
      //   fixedAmount = (Number(initstr) * 10).toFixed(2);
      // } else if (contractName == 'E-BTC-USDT') {
      //   // btc / 1000
      //   fixedAmount = (Number(initstr) / 1000).toFixed(2);
      // } else if (contractName == 'E-ETH-USDT') {
      //   // eth / 100
      //   fixedAmount = (Number(initstr) / 100).toFixed(2);
      // } else {
      //   // 默认处理其他情况
      //   fixedAmount = initstr.toFixed(2);
      // }

      // 保证只有2位小数
      let fixedAmount = initstr.toFixed(2);

      // 将整数部分和小数部分分开
      let [integerPart, decimalPart] = fixedAmount.split('.');

      // 使用正则添加千位分隔符
      let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      // 返回格式化后的金额
      return '＄' + formattedIntegerPart + '.' + decimalPart;
    },
    resetTimers() {
      clearInterval(this.profitTimer);
      this.profitTimer = null;
      this.profitTimer = setInterval(() => {
        this.fetchProfit()
      }, 3000)
      if (this.bittimer || this.timer) {
        clearInterval(this.bittimer);
        clearInterval(this.timer);
        this.timer = null;
        this.bittimer = null;
      }
      this.Indextimer = null;
    },

    refreshData() {
      this.fetchbit();
      this.getSelectheight();
      this.fetchUser();

      if (!uni.getStorageSync('isbitGuidanceStorage')) {
        this.isGuidance = true;
      }
      this.midside = uni.getStorageSync('side') || 'BUY';
      this.getNewPrice(); // 获取 latestPrice
      this.fetchProfit();

      const depth = uni.getStorageSync('depthStorage');
      if (depth) {
        this.orderBook = depth;
      }
    },

    setOrderPrice() {
      let price;
      if (this.marktype === 1) {
        price = this.isBuy
          ? this.orderBook.buy[0].price
          : this.orderBook.sell[this.orderBook.sell.length - 1].price;
      } else {
        price = this.latestPrice;
      }

      this.orderprice = price;
      this.marginRight = this.calculateMarginRight(price.toString());
    },

    calculateMarginRight(price) {
      if (price.length >= 1 && price.length <= 2) {
        return '70';
      } else if (price.length >= 3 && price.length < 4) {
        return '40';
      } else {
        return '20';
      }
    },
    // 清空定时器
    clearTimers() {
      clearInterval(this.intervalId);
      clearInterval(this.Zijintimer);
      clearInterval(this.bittimer);
      clearInterval(this.timer);
      clearInterval(this.profitTimer);
      this.timer = null;
      this.profitTimer = null;
      this.bittimer = null;
    },
    // 获取存储数据并初始化相关变量
    initializeStorage() {
      this.midside = uni.getStorageSync('side') || 'BUY';
      this.appUrl = getApp().globalData.urlZf;
      uni.setStorageSync('isbitpage', 1);

      // 判断是否为首次使用引导
      if (!uni.getStorageSync('isbitGuidanceStorage')) {
        this.isGuidance = true;
      } else {
        this.checkInfo();
      }
    },

    // 判断是否弹出设置窗口
    checkPushSettings() {
      //#ifdef APP-PLUS
      let isPushBv = uni.getStorageSync('isPushBv');
      let isPushBit = uni.getStorageSync('isPushBit');
      if (isPushBv || !isPushBit) {
        this.timerSetting = setTimeout(() => {
          this.showSetting = true;
        }, 30000); // 30秒
      }
      // #endif
    },

    // 页面初始化设置
    initPage() {
      // uni.setStorageSync('currentIndictor', "");
      // store.commit("changeOPtionSymbol", 'aaveusdt');
      this.orderprice = this.marktype == 1 ? this.calculateOrderPrice() : Number(this.latestPrice).toFixed(3);
      this.marginRight = this.calculatePriceMargin(this.orderprice);
    },

    // 计算订单价格
    calculateOrderPrice() {
      let price = this.isBuy ? this.orderBook.buy[0].price : this.orderBook.sell[this.orderBook.sell.length - 1].price;
      // price计算逻辑
      return price;
    },

    // 计算价格的右边距
    calculatePriceMargin(price) {
      price = price.toString();
      if (price.length >= 1 && price.length <= 2) {
        return '70';
      } else if (price.length >= 3 && price.length < 4) {
        return '40';
      } else if (price.length >= 4) {
        return '20';
      }
      return '70';
    },

    // 加载订单簿数据
    loadOrderBookData() {
      let depth = uni.getStorageSync('depthStorage');
      if (depth) {
        this.orderBook = depth;
      }

      let currentTabbar = uni.getStorageSync('currentTabbar');
      if (currentTabbar == 'pay') {
        this.initialActiveIndex = 1;
      }
    },
    clearTimerSetting() {
      if (this.timerSetting) {
        clearTimeout(this.timerSetting);
        this.timerSetting = null; // 避免重复清除
      }
    },
    startFetching() {
      // 确保定时器未重复启动
      if (!this.Indextimer) {
        this.Indextimer = setInterval(() => {
          this.fetchIndex();
        }, 3000);
      }
    },
    stopFetching() {
      if (this.Indextimer) {
        clearInterval(this.Indextimer);
        this.Indextimer = null;
      }
    },
    async get_tag_list() {
      let deviceToken = uni.getStorageSync('deviceToken')
      let phoneType = uni.getSystemInfoSync().platform
      let res = await this.$api.tag_list({
        deviceToken,
        phoneType
      });
      if (res.status.code == 0) {
        let tags = res.result.tags
        if (tags) {
          let isPushBv = this.containsCharacter(tags, 'NO_COLLECTIBLES_TAG')
          let isPushBit = this.containsCharacter(tags, 'TRADE_PRICE_CHANGE_TAG')
          uni.setStorageSync('isPushBit', isPushBit)
          this.$forceUpdate()
        }
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    containsCharacter(str, char) {
      // 使用 String.prototype.includes 方法检查字符串 str 是否包含字符 char
      return str.includes(char);
    },
    async gosettingnotice() {
      let token = uni.getStorageSync('token')
      if (!token) {
        this.$Router.push({
          name: 'mainLogin',
        })
        return
      }
      await this.get_tag_list()
      let isPushBit = uni.getStorageSync('isPushBit')
      let isPushBv = uni.getStorageSync('isPushBv')

      let deviceToken = uni.getStorageSync('deviceToken')
      let phoneType = uni.getSystemInfoSync().platform
      let params = {
        deviceToken,
        operationType: 1,
        phoneType,
        tag: 'TRADE_PRICE_CHANGE_TAG'
      }
      let params2 = {
        deviceToken,
        operationType: 0,
        phoneType,
        tag: 'NO_COLLECTIBLES_TAG'
      }
      let res = await this.$api.subscribe(params);
      let res2 = await this.$api.subscribe(params2);

      if (res.status.code == 0 && res2.status.code == 0) {
        this.showSetting = false
        uni.showToast({
          title: '成功',
          icon: 'none',
          duration: 3000
        });
        // this.get_tag_list()
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    getMarketData(e) {
      try {
        // 获取所有存储的键
        const keys = uni.getStorageInfoSync().keys;

        // 构造正则匹配要查找的键
        const regex = new RegExp(`market_e_${e}_trade_ticker_realPrice`);

        // 找到匹配的键
        const matchKey = keys.find((key) => regex.test(key));

        if (matchKey) {
          // this.marketKey = matchKey;
          this.latestPrice = uni.getStorageSync(matchKey); // 获取对应的值
        } else {
          this.marketKey = "未找到匹配的键";
          this.marketValue = "未找到对应的值";
        }
      } catch (error) {
        console.error("获取存储信息失败", error);
      }
    },
    gochangeSymbol() {
      this.$Router.pushTab({
        name: 'takeOffIndex'
      })
    },
    gocom() {
      this.gomission('TradingCompetition')
    },
    gomiss() {
      this.gomission('mission')
    },
    changeSymbol(e) {
      if (!e) {
        this.indexS = false
        this.indictor = {}
        store.commit("changeOPtionSymbol", 'aaveusdt')
        this.currentIndicator = ""
        uni.setStorageSync('currentIndictor', this.currentIndicator)
        uni.setStorageSync('IndictorInfo', '')
        return
      }
      store.commit("changeOPtionSymbol", (e.name + 'USDT').toLocaleLowerCase())
      this.currentIndicator = e.contractName
      uni.setStorageSync('currentIndictor', this.currentIndicator)
      uni.setStorageSync('IndictorInfo', e)

      this.indictor = e
      this.indexS = false
    },
    async fetchIndex() {
      let res = await this.$api.indexs()
      if (res.status.code === 0) {
        this.indexList = res.result.list
      }
    },
    selectLeftItem(item) {
      this.selectedLeft = item; // 设置左边选中项
      // this.selectedRight = null; // 清除右边的选中项
    },
    selectRightItem(item) {
      this.selectedRight = item; // 设置右边选中项
      // this.selectedLeft = null; // 清除左边的选中项
    },
    handleRight(item) {
      if (item.text == '帮助中心') {
        // #ifdef H5
        this.$Router.push({
          name: 'helpCenter'
        })
        // #endif

        // #ifdef APP-PLUS
        let link = `${getApp().globalData.url}pagesA/project/helpCenter/index`
        this.$Router.push({
          name: 'webView',
          params: {
            url: link
          }
        })
        // #endif


      } else if (item.text == '体验金') {
        let token = uni.getStorageSync('token')
        if (!token) {
          this.$Router.push({
            name: 'mainLogin',
          })
          return
        }
        if (this.isrightCorner) {
          this.$Router.push({
            name: 'mission'
          })
        } else {
          uni.showToast({
            title: '敬请期待',
            icon: 'none'

          })
        }

      } else if (item.text == '交易大赛') {
        let token = uni.getStorageSync('token')
        if (!token) {
          this.$Router.push({
            name: 'mainLogin',
          })
          return
        }

        if (this.isInTransitionVS) {
          // #ifdef H5
          this.$Router.push({
            name: 'TradingCompetition'
          })
          // #endif

          // #ifdef APP-PLUS
          let link = `${getApp().globalData.url}pages/project/actives/TradingCompetition`
          this.$Router.push({
            name: 'webView',
            params: {
              url: link
            }
          })
          // #endif
        } else {
          uni.showToast({
            title: '敬请期待',
            icon: 'none'
          })
        }

      } else if (item.text == '邀请好友') {
        this.$Router.push({
          name: 'inviteYs'
        })
      } else {
        this.foolPartent = !this.foolPartent
        this.helpoptionsShow = false
      }
    },
    makerightshow() {
      this.helpoptionsShow = true
    },
    stopCarousel() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    handleTouchStart(e) {
      this.startY = e.touches[0].pageY; // 记录触摸开始时的 Y 坐标
    },
    handleTouchMove(e) {
      this.endY = e.touches[0].pageY; // 记录触摸移动时的 Y 坐标
    },
    handleTouchEnd() {
      const deltaY = this.endY - this.startY; // 计算触摸的位移
      if (Math.abs(deltaY) > this.threshold) {
        if (deltaY > 0) {
          // 向下滑动
          this.showPrevious();
        } else {
          // 向上滑动
          this.showNext();
        }
      }
    },
    showPrevious() {
      this.activeIndex = (this.activeIndex - 1 + this.itemsCount) % this.itemsCount;
    },
    showNext() {
      this.activeIndex = (this.activeIndex + 1) % this.itemsCount;
    },
    updateTime() {
      const now = new Date();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const seconds = now.getSeconds();

      this.currentTime = this.formatTime(hours, minutes, seconds);

      // 检查是否到达新的时间节点
      this.checkTimeNode(hours, minutes, seconds);

      const currentNode = this.timeNodes.find(time => hours < time);
      const nextNode = currentNode !== undefined ? currentNode : this.timeNodes[0];

      const nextDate = new Date(now);
      nextDate.setHours(nextNode, 0, 0, 0);
      if (hours >= nextNode) {
        nextDate.setDate(nextDate.getDate() + 1);
      }

      const diff = Math.floor((nextDate - now) / 1000);
      this.countdown = this.formatCountdown(diff);
      this.nextTime = this.formatTime(nextNode, 0, 0);
    },
    checkTimeNode(hours, minutes, seconds) {
      if (minutes === 0 && seconds === 0) {
        if (this.timeNodes.includes(hours) && hours !== this.lastNodeChecked) {
          this.lastNodeChecked = hours;
          this.triggerNodeAction(hours);
        }
      }
    },
    triggerNodeAction(node) {
      // 根据时间节点执行相应操作
      switch (node) {
        case 0:
          console.log('执行0:00的操作');
          break;
        case 8:
          console.log('执行8:00的操作');
          break;
        case 16:
          console.log('执行16:00的操作');
          break;
      }
    },
    formatTime(hours, minutes, seconds) {
      return `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(seconds)}`;
    },
    formatCountdown(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(secs)}`;
    },
    padZero(num) {
      return num < 10 ? '0' + num : num;
    },
    onInputChange(event) {
      event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,3})/g)[0]) || ""
      this.$nextTick(() => {
        this.orderprice = event.detail.value
      })
    },
    onVolInputChange(event) {
      event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,3})/g)[0]) || ""
      this.$nextTick(() => {
        this.orderamount = event.detail.value
      })
    },
    nav_realName() {
      this.isRegistration = false;
      if (uni.getStorageSync("authStatus") == 30) {
        this.$Router.push({
          name: "authentication",
        });
      } else {
        this.$Router.push({
          name: "realName",
        });
      }
    },
    gonotice() {
      // uni.switchTab({
      //     url: "/pages/project/notice/index?ise=true",
      //     params:{

      //     }
      // })
      this.$Router.push({
        name: 'helpCenter'
      })

      // this.$Router.push({
      //   name: "official",
      //   params: {
      //     title: 'BIT指数'
      //   }
      // });
      this.bitshow = false
      // this.$Router.pushTab({
      //         name: "notice",
      //         params:{
      //             ise:true
      //         }
      //     });
    },
    async checkInfo() {
      let res = await this.$api.userInfo({});
      if (res.status.code == 0) {
        this.showPopup = !res.result.questionStatus;
      }
    },
    async fetchuserInfo() {
      let res = await this.$api.userInfo({});
      if (res.status.code == 0) {
        this.showPopup = !res.result.questionStatus;
        // this.showPopup = true
        // 查询用户实名
        if (res.result.authStatus == 31 && res.result.authType == 1) {
          this.certification = 1
        } else if (res.result.authStatus == 31 && res.result.authType == 2) {
          this.certification = 2
        } else {
          this.certification = 0
        }

        uni.setStorageSync("this.certification", this.certification);
        uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword);
        uni.setStorageSync("uid", res.result.userId);

        this.bittimer = setInterval(() => {
          this.fetchbit()
        }, 1000 * 60)
        this.profitTimer = setInterval(() => {
          this.fetchProfit()
        }, 3000)

        this.timer = setInterval(() => {
          this.SendWSHeartMessage()
        }, 5000)
      }
    },
    // 获取bit指数
    async fetchbit() {
      const contractName = uni.getStorageSync('currentIndictor');

      let res2 = await this.$api.GetBit({ ...(contractName ? { contractName } : {}), });
      if (res2.status.code == 0) {
        this.bitFix = res2.result.toFixed(2)
      }

      if (contractName) {
        // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
        let res = await this.$api.GetcoinBit({ ...(contractName ? { contractName } : {}), });
        if (res.status.code == 0) {
          // bnb/10，sol/10，doge*10，xrp*10 btc是/1000，eth是/100
          if (contractName == 'E-BNB-USDT' || contractName == 'E-SOL-USDT') {
            // bnb/10, sol/10
            this.bit = (Number(res.result) / 10).toFixed(3);
          } else if (contractName == 'E-DOGE-USDT' || contractName == 'E-XRP-USDT') {
            // , doge * 10, xrp * 10
            this.bit = (Number(res.result) * 10).toFixed(3);
          } else if (contractName == 'E-BTC-USDT') {
            // btc / 1000
            this.bit = (Number(res.result) / 1000).toFixed(3);
          } else if (contractName == 'E-ETH-USDT') {
            // eth / 100
            this.bit = (Number(res.result) / 100).toFixed(3);
          } else {
            // 默认处理其他情况
            this.bit = Number(res.result).toFixed(3);
          }
          // this.bit = Number(res.result).toFixed(3)
        }
      } else {
        let res2 = await this.$api.GetBit({ ...(contractName ? { contractName } : {}), });
        if (res2.status.code == 0) {
          this.bit = Number(res2.result).toFixed(3)
          this.bitFix = res2.result.toFixed(2)
          // this.indexS = true

        }
      }
      // let res = await this.$api.GetBit({
      //   ...(contractName ? { contractName } : {}),
      // })

    },
    // limitDecimalPlaces(event) {
    //     event.detail.value = event.detail.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    //     this.orderprice = event.detail.value;

    // },
    formatNumber(value, decimalPlaces = 2) {
      if (value == 0) {
        return 0
      }
      // 判断是否为整数
      if (Number.isInteger(value)) {
        return value.toFixed(3);
      }
      value = Number(value)
      // 如果是整数，则直接返回
      // 如果不是整数，则将其限制为指定的小数位数
      return value.toFixed(decimalPlaces);
    },
    onClick() {
    },
    makeprice(side, item) {
      if (Number.isInteger(item.price)) {
        return item.price.toFixed(3);
      }
      item.price = Number(item.price).toFixed(3);

      if (this.marktype == 2) {
        return
      }
      if (side == 'BUY') {
        this.orderprice = item.price
      } else {
        this.orderprice = item.price
      }
      // 获取this.orderBook.buy或者this.orderBook.sell的买一卖一价
      // let price = this.isBuy ? this.orderBook.buy[0].price : this.orderBook.sell[this.orderBook.sell.length - 1].price;

      // this.orderprice = price
      // this.aimPrice = price
    },
    adjustMargin() {
      const inputWidth = this.orderprice.length * 1; // 假设每个字符的宽度为10px
      this.marginRight = inputWidth;
    },
    toonehun() {
      return 2.18 * 100
    },
    accMul(arg1, arg2) {
      arg1 = Number(arg1);
      arg2 = Number(arg2);
      var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
      try {
        m += s1.split(".")[1].length
      } catch (e) {
      }
      try {
        m += s2.split(".")[1].length
      } catch (e) {
      }
      let res = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
      return res.toFixed(2)
    },
    // 获取收益
    async fetchProfit() {
      const contractName = uni.getStorageSync('currentIndictor');


      let res;
      if (contractName) {
        res = await this.$api.ShowcoinLuckySpectator({ ...(contractName ? { contractName } : {}), });
      } else {
        res = await this.$api.ShowLuckySpectator({ ...(contractName ? { contractName } : {}), });
      }


      if (!res.result) {
        this.showprofit = false
      } else {
        this.showprofit = true
      }
      if (res.status.code == 0 && res.result) {
        this.profitobj = res.result

      }

    },
    // 新用户领取体验金
    async fetchgetbonus() {
      let res = await this.$api.GetReward({ activityNo: "A81001000211587072", userType: 1 })
      // this.$emit('Bogetbonus', false)
      this.fetchUser()

      if (res.status.code == 0) {
        this.newuserreward = false
        uni.showToast({
          title: '领取成功',
          icon: 'none',
          duration: 3000
        })
      } else {
        this.newuserreward = false

        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        })
      }
      //  请求参数 activityNo=A81001000211587072
      // userType 用户类型 1-邀请者 2-被邀请者

    },
    checkClickOutside(event) {
      this.marktypeshow = false;
    },
    // 拿到新用户点击领取体验金回调 , 拿到体验金
    getbonus(e) {
      this.fetchUser()
    },
    /**
     * 最新价格
     */
    async getNewPrice() {
      const contractName = uni.getStorageSync('currentIndictor');

      let res = await this.$api.GettradePrcie({ ...(contractName ? { contractName } : {}), });
      if (res.status.code == 0) {
        let a = Number(res.result.price)
        let b = a.toFixed(3)
        let d = a.toFixed(2)

        this.latestPrice = b
        this.latestPriceBVexchange = d
        this.orderprice = b
        this.newprice = b // 传组件

        let price = b
        price = price.toString()

        if (price.length == 1) {
          this.marginRight = '70'
        } else if (price.length == 2) {
          this.marginRight = '60'
        } else if (price.length == 3) {
          this.marginRight = '40'
        } else if (price.length == 4) {
          this.marginRight = '20'
        } else if (price.length == 5) {
          this.marginRight = '10'
        }
      }

      // let res1;
      // let res
      if (contractName) {
        // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
        let res = await this.$api.GetcointradePrcie({ ...(contractName ? { contractName } : {}), });

        if (res.status.code == 0) {
          let a = Number(res.result.price)
          let b = a.toFixed(3)
          this.latestPrice = b
          this.orderprice = b
          this.newprice = b // 传组件
          let price = b
          price = price.toString()

          if (price.length == 1) {
            this.marginRight = '70'
          } else if (price.length == 2) {
            this.marginRight = '60'
          } else if (price.length == 3) {
            this.marginRight = '40'
          } else if (price.length == 4) {
            this.marginRight = '20'
          } else if (price.length == 5) {
            this.marginRight = '10'
          }
        }
      } else {

      }


    },
    // getbalance(e) {
    //   this.bvbalance = e
    //   this.fetchUser()
    // },
    /** long short宽度 */
    getlsWidth(amount) {

      //     dealprogress(item) {
      //     let chu = parseFloat(item.dealMoney / item.money) * 100
      //     let yu = chu.toFixed(2)
      //     return yu
      // },

      const totalAmountSum = this.buyTotalAmount + this.sellTotalAmount;
      const ratio = (parseFloat(amount / totalAmountSum)) * 100

      const fixedRatio = ratio.toFixed(0)

      return fixedRatio

    },
    getlsnum(amount) {
      const totalAmountSum = this.buyTotalAmount + this.sellTotalAmount;
      const ratio = (parseFloat(amount / totalAmountSum)) * 100;

      // 四舍五入并限制范围在 1 到 99 之间
      const limitedRatio = Math.min(Math.max(Math.round(ratio), 1), 99);
      if (limitedRatio) {
        return limitedRatio;
      } else {
        return 0
      }

    },
    /**
     * 计算宽度
     */
    getWidth(amounts, side) {
      if (side == 'buy') {
        if (this.orderBook?.buy && this.orderBook?.buy?.length) {


          let maxAmount = this.orderBook.buy.length === 1 ? this.orderBook.buy[0].amount : Math.max(...this.orderBook.buy.slice(0, 5).map(item => item.amount));
          return (amounts / maxAmount) * 100;
        }

      } else {
        if (this.orderBook?.sell && this.orderBook?.sell?.length) {

          let maxAmount = this.orderBook.sell.length === 1 ? this.orderBook.sell[0].amount : Math.max(...this.orderBook.sell.slice(-5).map(item => item.amount));
          return (amounts / maxAmount) * 100;
        }

      }
      // 在这里计算宽度
      // 如果buy或sell数组只有一个元素，那么就将该元素的amount值作为最大长度
    },
    /**
     * 查询用户信息
     */
    async fetchUser() {

      this.ispull = !this.ispull // 触发子组件更新
      // const contractName = uni.getStorageSync('currentIndictor');

      // let res;
      // if (contractName) {
      //   // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
      //   res = await this.$api.GetcoinExchangeUserInfo({ ...(contractName ? { contractName } : {}), });
      // } else {
      //   res = await this.$api.GetExchangeUserInfo({ ...(contractName ? { contractName } : {}), });
      // }
      // if (res.status.code == 0) {
      //   this.userinfo = res.result
      //   if (res.result.experienceMoney) {
      //     if (res.result.balance >= 0) {
      //       this.isshowbalance = true

      //     } else {
      //       this.isshowbalance = false
      //     }
      //   } else {
      //     if (res.result.balance >= 15) {
      //       this.isshowbalance = true
      //     } else {
      //       this.isshowbalance = false
      //     }
      //   }
      // }
    },
    async fetchwhatgobuy() {
      const contractName = uni.getStorageSync('currentIndictor');

      let res;
      let res1
      // if (contractName) {
      // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
      res1 = await this.$api.GetcoinExchangeParam({ ...(contractName ? { contractName } : {}), });
      // } else {
      res = await this.$api.GetExchangeParam({ ...(contractName ? { contractName } : {}), });
      // }
      if (res1.status.code == 0) {
        this.aimPrice = res.result.aimPrice

        // this.aimobj = res.result
        // this.$emit('getaimobj', res.result)
      }
      if (res.status.code == 0) {
        this.aimPrice2 = res.result.aimPrice

        // this.aimobj = res.result
        // this.$emit('getaimobj', res.result)
      }
    },
    /**
     * 获取exchangeparams
     */
    getaimobj(e) {
      this.aimPrice = e.aimPrice

      this.LimitPrice = e.perMaxLimit
      this.isInTransitionVS = e.isInTransitionVS
    },
    popupClose() {
      // this.isrightCorner = true
    },
    /**
     * 右上角去任务
     */
    gomission(e) {

      let token = uni.getStorageSync('token')
      if (!token) {
        this.$Router.push({
          name: 'mainLogin',
        })
        return
      }
      // #ifdef H5
      this.$Router.push({
        name: e
      })
      // #endif

      // #ifdef APP-PLUS
      if (e == 'TradingCompetition') {
        let link = `${getApp().globalData.url}pages/project/actives/TradingCompetition`
        this.$Router.push({
          name: 'webView',
          params: {
            url: link
          }
        })
      } else {
        this.$Router.push({
          name: e
        })
      }

      // #endif
    },
    /**
     * 去充值
     */
    async goCharge() {
      let token = uni.getStorageSync('token')
      if (!token) {
        this.$Router.push({
          name: 'mainLogin',
        })
        return
      }
      if (this.certification == 0) {
        // uni.showToast({
        //     title: '您还未实名，请先去实名',
        //     icon: 'none',
        //     duration: 3000
        // });
        this.$Router.push({
          name: "realName"
        })
        return
      }
      let url = `${this.appUrl}pagesA/project/security/pay`
      // #ifdef APP
      if (uni.getSystemInfoSync().platform == 'ios') {
        let res = await this.$api.java_commonconfigInfo({
          name: 'ios_apple_pay_version',
        });

        let curV = uni.getSystemInfoSync().appVersion
        let reqV = res.result.value
        if (curV == reqV) {
          this.$Router.push({
            name: "iosPay",
          })
        } else {
          this.$Router.push({
            name: "webView",
            params: {
              url,
            }
          })
        }
      } else {
        this.$Router.push({
          name: "webView",
          params: {
            url,
          }
        })
      }
      // #endif
      // #ifdef H5
      let { origin } = window.location
      window.location.href = `${origin}/orderView/#/pagesA/project/security/pay`
      // #endif
    },
    //心跳包
    SendWSHeartMessage() {
      //   if (this.SocketOpen) {
      var pong = { "ping": new Date().getTime() }
      var message = JSON.stringify(pong)
      uni.sendSocketMessage({ data: message })
      //   }
    },
    // 转化orderbook 数据格式
    transformData(data) {

      const result = {
        buy: [],
        sell: []
      };

      data.buys.forEach(([price, amount]) => {
        result.buy.push({ price, amount });
      });

      data.asks.forEach(([price, amount]) => {
        result.sell.push({ price, amount });
      });

      const results = {
        buy: [...result.buy].sort((a, b) => b.amount - a.amount),
        sell: [...result.sell].sort((a, b) => b.amount - a.amount)
      };
      return results;
    },
    // 下单
    submitOrder: antiShake._debounce(function () {

      const leverage = this.formattedSliderValue(this.sliderValue);
      const maxAmount1x = this.LimitPrice / 1;
      const maxAmount2x = this.LimitPrice / 2;
      const maxAmount5x = this.LimitPrice / 5;
      const maxAmount10x = this.LimitPrice / 10;

      let maxAmount;
      let message = '';

      if (leverage === 1) {
        maxAmount = maxAmount1x;
        message = `1倍杠杆下，最大金额为 ${maxAmount1x}`;
      } else if (leverage === 2) {
        maxAmount = maxAmount2x;
        message = `2倍杠杆下，最大金额为 ${maxAmount2x}`;
      } else if (leverage === 5) {
        maxAmount = maxAmount5x;
        message = `5倍杠杆下，最大金额为 ${maxAmount5x}`;
      } else if (leverage === 10) {
        maxAmount = maxAmount10x;
        message = `10倍杠杆下，最大金额为 ${maxAmount10x}`;
      }

      if (this.orderamount > maxAmount) {
        uni.showToast({
          title: `超出最大限额。${message}`,
          icon: 'none',
          duration: 3000
        });
        return
      } else {
        // 继续执行下单操作
        // your order submission logic here
      }

      // 杠杆 * 金额 大于25000 就提示大单modal
      if (this.formattedSliderValue(this.sliderValue) * this.orderamount > 25000) {
        this.isBigOrder = true
        return
      }
      this.makeorder()
    }, 200),
    async makeorder() {
      // if (!this.bvbalance) {
      //     this.nobalance = true  // 跳转充值
      //     return
      // }
      if (!this.orderprice && this.marktype == 1) {
        uni.showToast({
          title: '请输入' + this.ordertip(),
          icon: 'none',
          duration: 2000
        })
        return
      }
      // if (!this.orderamount) {
      //   uni.showToast({
      //     title: '请输入金额',
      //     icon: 'none',
      //     duration: 2000
      //   })
      //   return
      // }

      const contractName = uni.getStorageSync('currentIndictor');


      let data = {
        ...(contractName ? { contractName } : {}),

        leverageLevel: this.formattedSliderValue(this.sliderValue),
        open: 'OPEN',
        // price: this.marktype == 2 ? this.latestPrice : Number(this.orderprice),
        price: Number(this.orderprice),
        side: this.isBuy ? 'SELL' : 'BUY',
        type: this.marktype,
        volume: !this.isEmptyObject(this.allgold) ? this.allgold.amount : Number(this.orderamount),
        trailAmount: "",
        trailIdStr: !this.isEmptyObject(this.allgold) ? this.allgold.id : "",
      }
      // let res = await this.$api.CreateOrderDer(data)
      let res;
      if (contractName) {
        // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
        res = await this.$api.CreatecoinOrderDer(data);
      } else {
        res = await await this.$api.CreateOrderDer(data)
      }
      if (res.status.code == 0) {
        this.getList()
        this.allgold = {}
        this.disabledPoints = []
        if (this.marktype == 2) {
          // GetorderStatus
          setTimeout(async () => {

            if (contractName) {
              // 当 contractName 有值时调用 GetcoinExchangeUserInfo 接口
              let result = await this.$api.GetcoinorderStatus({
                id: res.result,
                ...(contractName ? { contractName } : {}),

              })

            } else {
              let result = await this.$api.GetorderStatus({ id: res.result })

            }

            if (result.status.code != 0) {
              uni.showToast({
                title: '对手盘不足，未成交委托已撤单',
                icon: 'none',
                duration: 2000
              })
            }
          }, 5000);

        }
        this.fetchUser()
        this.timetofetchPosition = !this.timetofetchPosition
        this.sliderValue = 0
        // this.orderprice = ''
        this.orderamount = ''
        // uni.showToast({
        //     title: res.status.msg,
        //     icon: 'none',
        //     duration: 2000
        // })

      } else if (res.status.code == 930001) {
        this.isRegistration = true;
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 2000
        })

      }
    },
    // 确认下大单
    confirmBigOrder() {
      this.isBigOrder = false
      this.isbigordergo = false
      this.makeorder()
    },
    // formattedSliderValue(e) {
    //   switch (e) {
    //     case 0:
    //       return 1;
    //     case 33:
    //       return 2;
    //     case 66:
    //       return 5;
    //     case 99:
    //       return 10;
    //     default:
    //       return this.sliderValue;
    //   }
    // },
    formattedSliderValue(e) {
      switch (e) {
        case 0:
          return 1;
        case 1:
          return 2;
        case 2:
          return 5;
        case 3:
          return 10;
        case 4:
          return 20;
        case 5:
          return 50;
        default:
          return this.sliderValue;
      }
    },
    checkmarkettype(e) {
      this.marktypeshow = false
      this.marktype = e
      if (e == 1) {
        this.$nextTick(() => {
          this.focus = false
          this.$nextTick(() => {
            this.focus = true
          })
          // #ifdef H5
          // this.$refs.priceInput._focus()
          // #endif
          // #ifdef APP-PLUS
          // this.$refs.priceInput.focus()
          // #endif
          // this.$refs.priceInput._focus()
        });
      }
    },
    guidanceCheck(index) {
      if (index == 6) {
        this.isGuidance = false
        uni.setStorageSync('isbitGuidanceStorage', 1)
        this.checkInfo()
      } else {
        this.guidanceShow = index + 1
      }
    },
    endMove(e) {
      if (e < 10) {
        this.currentfff = 0
      } else if (e > 10 && e < 30) {
        this.currentfff = 1
      } else if (e > 30 && e < 65) {
        this.currentfff = 2
      } else {
        this.currentfff = 3
      }
    },
    // 获取手机bar的高度
    getDeviceBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          // res.statusBarHeight
          this.statusBarHeight = res.statusBarHeight;
          // #ifdef H5
          this.statusBarHeight = 10;
          // #endif
        },
      });
    },
    // 获取元素高度
    getSelectheight() {

      // #ifdef H5
      const DomTopHeight = uni.getStorageSync("selectTopHeight")
      const DomBottomHeight = uni.getStorageSync("selectBottomHeight")
      if (DomTopHeight && DomBottomHeight) {
        this.selectTopHeight = DomTopHeight
        this.selectBottomHeight = DomBottomHeight
        this.isStartForm = true
        return
      }
      // #endif

      // #ifdef APP-PLUS
      setTimeout(() => {
        uni.createSelectorQuery().in(this).select("#contentTop").boundingClientRect(data => {
          // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
          this.selectTopHeight = JSON.parse(JSON.stringify(data)).height

          uni.setStorageSync("selectTopHeight", this.selectTopHeight)
        }).exec()

        uni.createSelectorQuery().in(this).select("#contentBottom").boundingClientRect(data => {
          // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
          this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
          uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
        }).exec()

        this.isStartForm = true
      }, 1500)
      // #endif

      // #ifdef H5
      this.$nextTick(() => {
        setTimeout(() => {
          uni.createSelectorQuery().select("#contentBottom").boundingClientRect(data => {
            this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
            uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
          }).exec()
          uni.createSelectorQuery().select("#contentTop").boundingClientRect(data => {
            // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
            this.selectTopHeight = JSON.parse(JSON.stringify(data)).height
            uni.setStorageSync("selectTopHeight", this.selectTopHeight)
          }).exec()
          this.isStartForm = true
        }, 1500)
      })
      // #endif

    },
    showNotice() {
      this.proLoss = true;
    },
    changSliderFn(e) {
      uni.showToast({
        title: e + "%",
        icon: "none",
        duration: 1000,
      });
    },
    // 切换k线周期
    checks(item, index) {
      if (item.value == 'area') {
        this.selectedLeft = ''
      } else {
        this.selectedLeft = 'MA'
      }
      this.period = item.value
      this.nowTime = index;
      this.selectedRight = 'VOL'
    },
    ordertip() {
      return this.isBuy ? '唱空价' : '唱多价'
    },
    changeTab(e) {
      if (this.leftactive == e) return
      console.log(this.disabledPoints, '12312312');
      if (this.disabledPoints) {
      } else {
        this.sliderValue = 0
      }
      this.orderamount = ''
      this.leftactive = e;
      this.isBuy = !this.isBuy;
      if (this.marktype == 2) {
        return
      } else {
        let price = e == 2 ? this.orderBook.buy[0].price : this.orderBook.sell[this.orderBook.sell.length - 1].price;
        this.orderprice = price
      }
    },
    async firstVisit() {
      let res = await this.$api.dayFirstVisit({
        module: "BIT_EXCHANGE",
        from: 'h5'
      });
      let pages = getCurrentPages(); // 获取当前页面栈
      let currentPage = pages[pages.length - 1]; // 获取当前页面
      let currentRoute = currentPage.route; // 获取当前页面的路径

      let res2 = await this.$api.VisitorShare({
        module: "BIT_EXCHANGE",
        from: 'h5',
        page: currentRoute
      });
    },
    // 检查当前时间是否在 0:00-24:00 之间，如果是且 indexS 为 false 则设置为 true
    checkTimeToUpdateIndexS() {
      const currentTime = new Date();
      const currentHour = currentTime.getHours();

      // 判断是否是新的一天
      const lastUpdate = uni.getStorageSync('lastUpdate');  // 读取上次更新时间
      const currentDate = currentTime.toLocaleDateString(); // 当前日期（不包括时间）
      if (lastUpdate !== currentDate) {
        // 如果是新的一天，更新 indexS 为 true，并记录当前日期
        this.indexS = true;
        uni.setStorageSync('lastUpdate', currentDate);  // 保存当前日期
      }
    },
  },
  onUnload() {
    clearInterval(this.profitTimer);
    this.profitTimer = null
    this.clearTimerSetting();

  },
  onHide() {
    // 页面隐藏时清除定时器
    this.clearTimerSetting();
    clearInterval(this.profitTimer);
    this.profitTimer = null;
  },
  onShow() {
    console.log('onshow');

    const contractName = uni.getStorageSync('currentIndictor');
    if (!contractName) {
      this.componentKey++; // bit指数
      // 如果 contractName 为假，将 componentKey 设为基数
      this.componentKey = this.componentKey % 2 == 0 ? this.componentKey + 1 : this.componentKey;
    } else {
      this.componentKey++;
      // 如果 contractName 为真，将 componentKey 设为偶数
      this.componentKey = this.componentKey % 2 == 1 ? this.componentKey + 1 : this.componentKey;
    }

    this.allgold = {}
    this.disabledPoints = []
    onSocketOpen()
    startSocket()
    clearInterval(this.profitTimer);
    this.profitTimer = null;
  },
  watch: {
    indexS(newVal) {
      if (newVal) {
        // 开始执行定时器
        this.startFetching();
      } else {
        // 停止定时器
        this.stopFetching();
      }
    },
    "$store.state.optionSymbol"(val) {
      console.log('变化了');
      store.commit("changeKline", []);
      this.fetchwhatgobuy()
      this.getList()
      this.nowTime = 0
      onSocketOpen()
      startSocket()
      this.disabledPoints = []
      this.allgold = {}
      this.fetchbit()
      this.fetchUser()
      uni.setStorageSync('depthStorage', [])
      this.latestPrice = uni.getStorageSync(`market_e_${val}_trade_ticker_realPrice`)
      this.getMarketData(val);
      this.getNewPrice() // 获取lastestPrice
      clearInterval(this.intervalId);
      clearInterval(this.Zijintimer);
      clearInterval(this.bittimer)
      clearInterval(this.timer)
      clearInterval(this.profitTimer)
      this.timer = null
      this.profitTimer = null
      this.bittimer = null
      this.fetchuserInfo()
      this.orderBook = {
        buy: [],

        sell: [],
      },
        this.isLinkLoadding = true
      setTimeout(() => {
        this.isLinkLoadding = false
      }, 2000);
    },
    "$store.state.fundrate"(val) {
      let roundedNum = val
      this.fundrate = roundedNum
    },
    "$store.state.indexprice"(val) {
      let roundedNum = parseFloat(val.toFixed(3)); // 四舍五入到三位小数
      const contractName = uni.getStorageSync('currentIndictor');
      if (contractName == 'E-BNB-USDT' || contractName == 'E-SOL-USDT') {
        // bnb/10, sol/10
        this.bit = (Number(roundedNum) / 10).toFixed(3);
      } else if (contractName == 'E-DOGE-USDT' || contractName == 'E-XRP-USDT') {
        // , doge * 10, xrp * 10
        this.bit = (Number(roundedNum) * 10).toFixed(3);
      } else if (contractName == 'E-BTC-USDT') {
        // btc / 1000
        this.bit = (Number(roundedNum) / 1000).toFixed(3);
      } else if (contractName == 'E-ETH-USDT') {
        // eth / 100
        this.bit = (Number(roundedNum) / 100).toFixed(3);
      } else {
        // 默认处理其他情况
        this.bit = Number(roundedNum)
      }

      // this.bit = roundedNum
    },
    "$store.state.side"(val) {
      this.midside = val
    },
    "$store.state.realprice"(val) {
      let a = Number(val)
      let b = a.toFixed(3)
      this.latestPrice = b
      if (this.marktype == 2) {
        this.orderprice = val
        let price = b.toString()
        if (price.length == 1) {
          this.marginRight = '70'
        } else if (price.length == 2) {
          this.marginRight = '60'
        } else if (price.length == 3) {
          this.marginRight = '40'
        } else if (price.length == 4) {
          this.marginRight = '30'
        } else if (price.length == 5) {
          this.marginRight = '10'
        }
      }

    },
    "$store.state.Depths"(val) {
      this.orderBook = val
    },
    allgold: {
      handler(newName, oldName) {
        console.log(!this.isEmptyObject(newName), this.bvbalance > 0, '万能金');

        if (!this.isEmptyObject(newName) && this.userinfo.balance >= 0) {
          this.isshowbalance = true
        }
      },
      immediate: true,
      deep: true
    },
    orderamount: {
      handler(newName, oldName) {
        if (this.userinfo.experienceMoney) { // 有体验金
          console.log('有体验金', (Number(newName)), this.bvbalance, Number(newName) > (this.bvbalance + 15), this.bvbalance >= 5);


          // 体验金 + 余额 > 输入的数量
          if (Number(this.userinfo.experienceMoney) + this.bvbalance >= Number(newName)) {
            this.isshowbalance = true
          } else {
            this.isshowbalance = false
          }

          // if (Number(newName) >= (this.bvbalance + 15) && this.bvbalance >= 0) {
          //   this.isshowbalance = true
          // } else {
          //   this.isshowbalance = false   // 显示余额不足
          // }

          // if (this.bvbalance + 15 >= 15 && Number(newName) <= (this.bvbalance + 15)) {
          //   this.isshowbalance = true
          // } else {
          //   this.isshowbalance = false // 显示余额不足
          // }
        } else {
          console.log('没有体验金', Number(newName), this.bvbalance);
          if (Number(newName) > this.bvbalance && this.bvbalance >= 15) {
            this.isshowbalance = true
          } else {
            this.isshowbalance = false
          }


          if (this.bvbalance >= 15 && Number(newName) <= (this.bvbalance)) {
            this.isshowbalance = true
          } else {
            this.isshowbalance = false // 显示余额不足
          }
        }


        // if (Number(Number(newName) + 15) < 20) {
        //     this.isshowbalance = false
        // }
        if (this.userinfo.experienceMoney) {
          console.log('有体验金数为0');
          if (Number(newName) == 0) {
            if (this.bvbalance >= 0) {
              this.isshowbalance = true
            } else {
              this.isshowbalance = false
            }
          }
        } else {
          console.log('没有体验金数为0');

          if (Number(newName) == 0) {
            if (this.bvbalance >= 15) {
              this.isshowbalance = true
            }
          }
        }

        if (newName < 15 && newName !== '') {
          this.lower = true
        } else {
          this.lower = false
        }
        if (newName.length == 1) {
          this.marginLeftVol = '80'
        } else if (newName.length == 2) {
          this.marginLeftVol = '56'
        } else if (newName.length == 3) {
          this.marginLeftVol = '50'
        } else if (newName.length == 4) {
          this.marginLeftVol = '40'
        } else if (newName.length == 5) {
          this.marginLeftVol = '25'
        } else if (newName.length == 6) {
          this.marginLeftVol = '10'
        } else if (newName.length == 7) {
          this.marginLeftVol = '0'
        }
      },
      // immediate: true,
      deep: true
    },
    sliderValue: {
      handler(newName, oldName) {
        console.log(newName, 3333333);

        if (newName == 0) {
          this.currentfff = 0
        } else if (newName == 20) {
          this.currentfff = 1
        } else if (newName == 40) {
          this.currentfff = 2
        } else if (newName == 60) {
          this.currentfff = 3
        } else if (newName == 80) {
          this.currentfff = 4
        } else if (newName >= 100) {
          this.currentfff = 5
        }
        console.log(this.currentfff, 3333333 + 'this.currentfff');
      },
      immediate: true,

    },
    marktype: {
      handler(newName, oldName) {
        if (newName == 2) {
          this.markCss = true
          this.orderprice = this.latestPrice
        } else {
          this.markCss = false
          this.orderprice = ''
        }
      },
      deep: true
    },
    orderprice: {
      handler(newName, oldName) {
        newName = newName.toString()
        if (newName.length == 1) {
          this.marginRight = '50'
        } else if (newName.length == 2) {
          this.marginRight = '40'
        } else if (newName.length == 3) {
          this.marginRight = '25'
        } else if (newName.length == 4) {
          this.marginRight = '10'
        } else if (newName.length == 5) {
          this.marginRight = '0'
        } else if (newName.length == 6) {
          this.marginRight = '0'
        } else if (newName.length == 7) {
          this.marginRight = '0'
        }
      },
      immediate: true,
      deep: true
    },
    leftactive: {
      handler(newName, oldName) {
        if (this.marktype == 1) {
          this.orderprice = ''
        }
      }
    },
    bvbalance: {
      handler(newName, oldName) {
        this.fetchUser()
        if (this.userinfo.experienceMoney) {
          if (newName + 15 >= 15) {
            this.isshowbalance = true
          } else {
            this.isshowbalance = false
          }
        } else {
          if (newName >= 15) {
            this.isshowbalance = true
          } else {
            this.isshowbalance = false
          }
        }
      },
      // immediate: true,
      deep: true
    },
  },
  computed: {
    fixrate() {
      if (!this.aimPrice2) {
        return 0.00; // 或者你可以返回一个默认值，比如 "0.00"
      }
      let a = (this.latestPriceBVexchange - this.aimPrice2);
      let b = a / this.aimPrice2;
      b = Math.abs(b);
      let d = Number((b * 100)).toFixed(2)
      // let c = Math.trunc(b * 100);
      // let d = c.toFixed(2);
      return d;
    },
    buyTotalAmount() {
      if (this.orderBook?.buy && this.orderBook.buy.length > 0) {
        return this.orderBook ? this.orderBook?.buy.slice(0, 5).reduce((sum, item) => sum + item.amount, 0) : [];
      }

    },
    sellTotalAmount() {
      // return

      if (this.orderBook?.sell && this.orderBook.sell.length > 0) {

        return this.orderBook ? this.orderBook?.sell.slice(-5).reduce((sum, item) => sum + item.amount, 0) : [];
      }

    },
    RightOption() {
      const baseOptions = [
        {
          img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/5cf0c8e517b34c1032afb6b91d8abde1_86x86.png",
          text: "帮助中心",
        },
        {
          img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/c61a988d36276583c7ecffd8d860e9ad_82x82.png",
          text: this.foolPartent ? "切换专业版" : "切换简易版",
        },
        {
          img: "https://cdn-lingjing.nftcn.com.cn/image/20241227/48e731b8c498d675bdda5b13eee891a5_78x82.png",
          text: "邀请好友",
        }
      ];
      // 条件添加体验金
      if (this.isrightCorner) {
        baseOptions.push({
          img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/2312eabf8d86e2bd978ae5a7285255c4_86x86.png",
          text: "体验金",
        });
      }

      // 条件添加交易大赛
      if (this.isInTransitionVS) {
        baseOptions.push({
          img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/9390e3ab6bb03f88f6ee23f48c2b7a45_82x84.png",
          text: "交易大赛",
        });
      }

      return baseOptions;
      // return [
      //   {
      //     img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/5cf0c8e517b34c1032afb6b91d8abde1_86x86.png",
      //     text: "帮助中心",
      //   },
      //   {
      //     img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/2312eabf8d86e2bd978ae5a7285255c4_86x86.png",
      //     text: "体验金",
      //   },
      //   {
      //     img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/9390e3ab6bb03f88f6ee23f48c2b7a45_82x84.png",
      //     text: "交易大赛",
      //   },
      //   {
      //     img: "https://cdn-lingjing.nftcn.com.cn/image/20241111/c61a988d36276583c7ecffd8d860e9ad_82x82.png",
      //     text: this.foolPartent ? '切换专业版' : '切换简易版',
      //   }
      // ]
    }
  },

};
</script>

<style lang="scss" scoped>
@import './Exchange.scss';

.popup-container {
  margin: 0 45rpx;
  background: #34323E;
  border-radius: 25rpx;
  border: 1rpx solid #50EFED;
  overflow: hidden;

  .popup-button {
    width: 287rpx;
    height: 70rpx;
    background: linear-gradient(0deg, #EF91FB, #40F8EC);
    border-radius: 38rpx;

    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #000000;
    line-height: 45rpx;
    margin-bottom: 42rpx;
  }

  .popup-title1 {
    display: flex;
    justify-content: flex-start;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 34rpx;
    padding: 35rpx 0rpx 0rpx 63rpx;

  }

  .popup-title {
    text-align: center;
    margin-top: 30rpx;
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #63EAEE;
    line-height: 45rpx;
  }

  .popup-content {
    padding: 0 51rpx 55rpx 63rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 34rpx;
  }
}
</style>
