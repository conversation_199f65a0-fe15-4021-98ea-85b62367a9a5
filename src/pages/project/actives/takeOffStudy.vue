<template>
	<view class="main">
		<view class="top">
			<view>
				{{(sun+1)<6?sun+1:6}}/6
			</view>
			<view>
				选择一项并继续
			</view>
		</view>
		<view class="title">
			{{info.title}}
		</view>
		<view class="xuanze_ul">
			<view class="li" :class="{'is_error':item.isError}" v-for="(item,index) in info.sList" @click="check(item)">
				<view class="bg" :class="{'is_win':item.isActive}">
					{{item.name}}
					<view class="error"  v-show="item.isError">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240527/c4effba31196325d00c55cd59c942b78_96x98.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<!-- <view class="li is_error"> 
				<view class="bg">
					公平正义 
					<view class="error">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240527/c4effba31196325d00c55cd59c942b78_96x98.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="li">
				<view class="bg">
					谦逊怜悯
				</view>
			</view>
			<view class="li">
				<view class="bg">
					唯我独尊
				</view>
			</view> -->
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				current:1,
				list:[
					{
						title:"Bigverse的核心原则是什么？",
						sList:[
							{
								name:"以人为本",
								isWin:true
							},
							{
								name:"谨小慎微",
								isWin:false
							},
							{
								name:"谦逊怜悯",
								isWin:false
							},
							{
								name:"唯我独尊",
								isWin:false
							}
						]
					},{
						title:"对战模式有何独特之处？",
						sList:[
							{
								name:"可以利用杠杆",
								isWin:false
							},
							{
								name:"可以唱多唱空",
								isWin:false
							},
							{
								name:"可以增加玩法多样化",
								isWin:false
							},
							{
								name:"以上皆是",
								isWin:true
							}
						]  
					},{
						title:"唱多收益如何计算？",
						sList:[
							{
								name:"开仓价 — 平仓价",
								isWin:false
							},
							{
								name:"平仓价 — 开仓价",
								isWin:false
							},
							{
								name:"（平仓价 — 开仓价）x 份数",
								isWin:true
							},
							{
								name:"（开仓价 — 平仓价）x 份数",
								isWin:false
							}
						]
					},{
						title:"唱空收益如何计算？",
						sList:[
							{
								name:"开仓价 — 平仓价",
								isWin:false
							},
							{
								name:"平仓价 — 开仓价",
								isWin:false
							},
							{
								name:"（平仓价 — 开仓价）x 份数",
								isWin:false
							},
							{
								name:"（开仓价 — 平仓价）x 份数",
								isWin:true
							}
						]
					},{
						title:"下单金额如何计算？",
						sList:[
							{
								name:"市价 x 份数",
								isWin:false
							},
							{
								name:"杠杆 x 份数",
								isWin:false
							},
							{
								name:"开仓价 x 份数 x 杠杆",
								isWin:false
							},
							{
								name:"开仓价 x 份数 / 杠杆",
								isWin:true
							}
						]
					},{
						title:"对战模式买卖手续费是多少？",
						sList:[
							{
								name:"0.5%",
								isWin:true
							},
							{
								name:"5%",
								isWin:false
							},
							{
								name:"4%",
								isWin:false
							},
							{
								name:"10%",
								isWin:false
							}
						]
					},
				],
				info:{},
				sun:0
				
			}
		},
		onLoad(options) {
			this.info = this.list[this.sun]
		},
		onReachBottom() {
			
		},
		methods: {
			check(item){
				if(item.isWin){
					item.isActive = true
					this.$forceUpdate()
					setTimeout(()=>{
						if(this.sun<5){
							this.sun++
							this.info = this.list[this.sun ]
						}else{
							this.submitInfo()
							console.log("结束了")
						}
					},500)
				}else{
					item.isError = true
					this.$forceUpdate()
					console.log(item) 
				}
			},
			async submitInfo() {
				let res = await this.$api.updateUserInfo({
					questionStatus:1
				});
				if (res.status.code == 0) {
					this.nav_study()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_study(){
				this.$Router.pushTab({
					name:"takeOff"
				})
			},
		},
		components: {
			
		}
	} 
</script>
<style lang="scss" scoped>
	.main{
		color:#fff;
		padding:186rpx 38rpx 0rpx 38rpx;
		
		.top{
			font-size:28rpx;
			color:#fff;
			display: flex;
			justify-content: space-between;
		}
		.title{
			margin-top:124rpx;
			text-align: center;
			font-size:37rpx;
		}
		.xuanze_ul{
			margin-top:92rpx;
			.li{
				width:482rpx;
				height:108rpx;
				background:linear-gradient(0deg, #EF91FB, #40F8EC);
				margin-bottom:46rpx;
				padding:4rpx;
				border-radius:54rpx;
				margin:0 auto 46rpx auto;
				&.is_error{
					background:#EC4068;
					color:#EC4068;
				}
				.bg{
					background-color:#36343F;
					height:100%;
					border-radius:50rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					position:relative;
					&.is_win{
						color:#36343F;
						background:transparent;
					}
					.error{
						position: absolute;
						right:30rpx;
						top:50%;
						transform: translateY(-50%);
						image{
							width:48rpx;
						}
					}
				}
			}
		}
	}
</style>