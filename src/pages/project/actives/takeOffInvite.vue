<template>
  <view class="main">
    
    <image class="fixed-bg" src="/static/imgs/invite/takeOffInviteBg.png" mode="aspectFill"></image>
    
    <view class="title">
      <image class="logo" src="/static/imgs/invite/logo-v3_02.png" mode="widthFix"></image>
    </view>
    
    
    <view class="btn-join" @click="handleJoinInvite">
      加入开拓队伍
    </view>
    
    <view class="download">
      <view class="u-text-center">
        <image class="img-qrcode" src="/static/imgs/invite/downloadQrcode.png" mode="widthFix"></image>
        
        <view class="u-font-24 u-m-t-24" style="color: #fff;">扫码下载APP</view>
      </view>
    </view>
    
  </view>
</template>

<script>
  import api from '@/common/api/index.js';
  export default {
    data() {
      return {
        takeOffSource: ''
      };
    },
    onLoad(options) {
      
      const { takeOffSource } = options;
      this.takeOffSource = takeOffSource;
      
      this.fetchBaseInfo()
      
    },
    methods: {
      async fetchBaseInfo() {
        const {
        	status,
        	result
        } = await api.baseInfo({});
        
        // status.code = 1002
        if (status.code === 0) {
          // 已登录
        } else if(status.code == 1002){
          // 未登录
					this.$Router.push({
						name:"mainLogin",
            params: {
            	url: location.href
            }
					})
				} else {
          uni.showToast({
          	title: status.msg,
          	icon: 'none',
          	duration: 3000
          });
        }
      },
      async handleJoinInvite() {
        const {
        	status,
        	result
        } = await api.inviteJoin({
          contractAddress: this.takeOffSource,
        });
        if (status.code === 0) {
          uni.showToast({
          	title: '成功加入队伍!',
          	icon: 'none',
          	duration: 3000
          });
          setTimeout(() => {
          	this.isLoadding = false
          	this.$Router.pushTab({
          		name: "takeOff",
          	})
          }, 1500);
        } else {
          uni.showToast({
          	title: status.msg,
          	icon: 'none',
          	duration: 3000
          });
        }
        
      }
    }
  }
</script>

<style lang="scss" scoped>
.main {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  z-index: 9;
  
  // background: url('/static/imgs/invite/takeOffInviteBg.png') no-repeat center;
  // background-size: cover;
}

.fixed-bg {
  position: fixed;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}

.title {
  padding: 80rpx 90rpx;
  width: 100%;
	color: #000000;
  
  .logo {
    width: 280rpx;
  }
}


.btn-join {
  width: 544rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(0deg, #EF91FB, #40F8EC);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #000;
}

.download {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 620rpx;
  height: 280rpx;
  background: rgba(18, 20, 56, 0.4);
  border-radius: 25rpx;
  margin-bottom: 50rpx;
  
  .img-qrcode {
    width: 166rpx;
    height: 166rpx;
  }
}
</style>
