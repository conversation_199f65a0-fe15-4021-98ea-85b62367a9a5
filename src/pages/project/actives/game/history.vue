<template>
	<view class="body">
		<view class="head_top">
			<view class="back" @click="back">
				<img src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
					alt="" srcset="" />
			</view>
			<view class="tabs_div">
				<view class="left">
					<u-tabs name="cate_name"  bg-color="" :bar-style="barStyle" :list="tabList" bold
						 inactive-color="var(--default-color3)" 
						active-color="var(--default-color1)" :current="current" @change="change"></u-tabs>
				</view>
			</view>
		</view>
		<view class="details">
			<view class="weituo" v-if="current == 0">
				<view class="li" v-for="(item,index) in orderList" :key="index
				">
					<view class="head">
						<view class="left">
							<img v-if="item.longShort==2"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
								alt="" />
							<img v-else	
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
								alt="" />
							<span class="title">{{item.lever==1?'1X':item.lever+'X'}}</span>
							<span class="sub">{{item.createAt}}</span>
						</view>
						<view class="cd">
							<!-- <view class="red">部分成交已撤销</view> -->
							<view class="huise" v-show="item.status == 0">委托中</view>
							<view class="huise" v-show="item.status == 1">完全成交</view>
							<view class="white" v-show="item.status == 2">已撤销</view>
							<view class="white" v-show="item.status == 3">委托失败</view>
							<view class="huise" v-show="item.status == 4">平仓中</view>
							<view class="white" v-show="item.status == 5">已平仓</view>
							<view class="white" v-show="item.status == 6">部分成交已撤销</view>
						</view>
					</view>
					<view class="data_view">
						<view class="li_view width33">
							<view class="label">份数</view>
							<view class="num">{{item.quantity}}</view>
						</view>
						<view class="li_view width33">
							<view class="label">已成交</view>
							<view class="num">{{item.filledQuantity}}</view>
						</view>
						<view class="li_view width33">
							<view class="label">唱{{item.longShort==2?"空":"多"}}价</view>
							<view class="num">{{item.price}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cangwei" v-if="current == 1">
				<view class="li" v-for="(item,index) in cangweiList" :key="index">
					<view class="right_fenx" @click="openShare(item)">
						<img src="https://cdn-lingjing.nftcn.com.cn/image/20240216/9db5eea18043165f547f2f7775c6eb98_80x80.png"
							alt="" srcset="" />
					</view>
					<view class="live_data">
						<view class="sy" :class="{'red':item.isUp}">
							<span>收益</span>
							
							￥{{item.isUp?'+':''}}{{item.income}}
						</view>
						<view class="syl" :class="{'red':item.isUp}">
							<span>收益率</span>
							{{item.isUp?'+':''}}
							{{(item.incomeRate*100).toFixed(2)}}%
						</view>
					</view>
					<view class="data_view">
						<view class="li_view">
							<view class="label">份数</view>
							<view class="num">{{item.quantity}}</view>
						</view>
						<view class="li_view">
							<view class="label">开唱价￥</view>
							<view class="num">{{item.price}}</view>
						</view>
						<view class="li_view">
							<view class="label">闭嘴均价￥</view>
							<view class="num">{{item.closeAvgPrice}}</view>
						</view>
						<view class="li_view">
							<view class="label">手续费(开/平)￥</view>
							<view class="num">{{item.openFee}}/{{item.closeFee}}</view>
						</view>
					</view>
					<view class="footer">
						<view class="left" :class="{'noMargin':item.closeType==2}">
							<img v-if="item.longShort == 2"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
								alt="" />
							<img v-if="item.longShort == 1"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
								alt="" />
							<span class="title">{{item.lever}}X</span>
							<img class="qp" v-if="item.closeType==2"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/9feb4a9a829a408fec09e0042c92964f_74x36.png"
								alt="" />
						</view>
						<view class="right">
							<view>开唱时间：{{item.openTime}}</view>
							<view>我不唱了：{{item.closeTime}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-popup v-model="isShare" mode="bottom">
			<view class="share_body">
				<view class="back" @click="isShare = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
				<view class="cart_div" id="test-id">
					<view class="title_image">
						BV“开杠吧”战报
					</view>
					<view class="toux_image_div">
						<view class="toux_border">
							<view class="image_div">
								<image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
							</view>
						</view>
					</view>
					<view class="toux_name">
						{{ shareUser.addr }}
					</view>
					<view class="yield" :class="{ 'red': shareUser.red }">
						<text></text>{{shareUser.red?'+':''}}{{ shareUser.zNum }}
					</view>
					<view class="shouyi" :class="{ 'red': shareUser.red }">
						<text></text>{{shareUser.red?'+':''}}{{ shareUser.gNum }}
					</view>
					<view class="info_div">
						<p>买入价：￥{{ shareUser.price }}</p>
						<!-- <p>卖出价：￥3.45</p> -->
						<p>杠杆：{{ shareUser.lever }}X</p>
					</view>
					<view class="yqm">
						邀请码：{{ shareUser.invitationCode }}
					</view>
					<view class="msg_text">
						扫码注册，尽情开杠
					</view>
					<view class="icon_bg">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
							alt="" srcset="" mode="widthFix"></image>
					</view>
					<view class="qr_code">
						<view class="right">
							<view class="qr_div">
								<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx" :options="options"></uv-qrcode>
							</view>
						</view>
						<view class="time">分享于{{ shareUser.currentDateTime }}</view>
					</view>
				</view>
				<view class="share_to_div">
					<!-- #ifdef APP -->
					<view class="li" @click="fenx_weixin()" >
						<view class="icon_image">
							<image src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
								alt="" srcset="" mode="widthFix" ></image>
						</view>
						<p>微信</p>
					</view>
					<!-- #endif -->
					
					<!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
				</view>
				<view class="colse_div" @click="isShare = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
						mode="widthFix"></image>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderList:[],
				cangweiList:[],
				orderPageNum:1,
				cangweiPageNum:1,
				shareUser:"",
				current:0,
				tabList:[{
					name: '开唱历史',
					value: 0,
				},{
					name: '对战历史',
					value: 1,
				}],
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				},
				isFooter:true,
				isRequest:false,
				isShare:false,
				options: {
					useDynamicSize: false,
					errorCorrectLevel: 'Q',
					// margin: 10,
					areaColor: "#fff",
					// 指定二维码前景，一般可在中间放logo
					// foregroundImageSrc: require('static/image/logo.png')
				},
				qrcodeUrl:"",
				isUp:false
			}
		},
		onLoad() {
			this.getOrder()
			this.getUserShare()
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					if(this.current==0){
						this.getOrder()
					}else{
						this.getCangwei()
					}
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			change(index) {
				this.current = index
				if(index==0){
					this.orderPageNum = 1
					this.orderList = []
					this.getOrder()
				}else{
					this.cangweiPageNum = 1
					this.cangweiList = []
					this.getCangwei()
				}
			},
			async getOrder() {
				this.isRequest = true
				let res = await this.$api.getHistoryEntrustOrder({
					pageNum: this.orderPageNum,
					pageSize: 15
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						this.isFooter = true
					} else {
						this.orderPageNum++
						res.result.list.forEach((item) => {
							this.orderList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getCangwei() {
				this.isRequest = true
				let res = await this.$api.getHistoryCompletedOrder({
					pageNum: this.cangweiPageNum,
					pageSize: 15
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						this.isFooter = true
					} else {
						this.cangweiPageNum++
						res.result.list.forEach((item) => {
							item.isUp = this.isPositiveNumber(item.income)
							this.cangweiList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			back(){
				this.$Router.back()
			},
			openShare(item) {
				let zNum = `￥${item.income}`
				let gNum = `${Number(item.incomeRate*100).toFixed(2)}%`
				console.log(item)
				this.isShare = true
				this.shareUser = {
					...this.shareUser,
					zNum,
					gNum,
					red: item.isUp,
					price: item.price,
					lever: item.lever,
					currentDateTime: this.getDate()
				}
				console.log(this.shareUser)
			},
			async getUserShare() {
				let res = await this.$api.getUserInfotake({
			
				});
				if (res.status.code == 0) {
					this.shareUser = res.result
					const jumpUrl =`${getApp().globalData.url}pagesA/project/personal/appDownload`;
					this.get_share(jumpUrl)
					console.log(this.shareUser)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			getDate() {
				const now = new Date();
				const year = now.getFullYear();
				const month = (`0${now.getMonth() + 1}`).slice(-2);
				const date = (`0${now.getDate()}`).slice(-2);
				// 设置格式化的当前日期与时间
				return `${year}年${month}月${date}日`;
			},
			async get_share(jumpUrl) {
				console.log(jumpUrl)
				let res = await this.$api.getShortLink({
					longLink: jumpUrl
				});
				if (res.status.code == 0) {
					this.qrcodeUrl = res.result.shortUrl;
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			fenx_weixin(){
				// 分享到微信
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					href: this.qrcodeUrl,
					title: "Bigverse |" + 'BV开杠吧活动战报',
					summary: "Bigverse |" + 'BV开杠吧活动战报',
					success: function(res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function(err) {
						console.log("fail:" + JSON.stringify(err));
					},
					complete() {
						uni.hideLoading()
					}
				});
			},
			isPositiveNumber(num){
				if (Number(num) >= 0) {
					return true;
				} else {
					return false;
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	body {
		background: #111111;
	}

	.body {
		background-color: #111111;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
		background-size: 100%;
		background-repeat: no-repeat;
		min-height: 1624rpx;
		position: relative;

		.head_top {
			position: fixed;
			top: 0;
			left: 0;
			z-index: 99;
			width: 100%;
			background-color: rgb(0, 0, 0, 0.5);
			/* #ifdef APP */
			padding-top:40rpx;
			/* #endif */
			
			.back {
				position: absolute;
				/* #ifdef APP */
				top:var(--status-bar-height);
				/* #endif */
				/* #ifdef H5 */
				top: 64rpx;
				/* #endif */
				left: 30rpx;

				img {
					width: 50rpx;
				}
			}

			.tabs_div {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 46rpx 55rpx 30rpx 55rpx;

				.left {
					// width: 460rpx;
				}

				.right {
					img {
						width: 36rpx;
					}
				}
			}
		}

		.details {
			padding: 200rpx 55rpx;

			.weituo,
			.cangwei {
				.li {
					padding: 30rpx 20rpx;
					background-color: #2B2B2B;
					border-radius: 36rpx;
					margin-bottom: 40rpx;
					position: relative;

					.right_fenx {
						position: absolute;
						right: 0rpx;
						top: 0rpx;

						img {
							width: 80rpx;
						}
					}

					.live_data {
						background-color: #141816;
						border-radius: 27rpx;
						height: 54rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						padding: 0rpx 18rpx;
						font-size: 24rpx;
						width: 500rpx;
						color: #6CFF8A;

						span {
							color: #fff;
						}

						.sy {
							margin-right: 20rpx;
							min-width: 200rpx;
							font-weight: 600;

							&.red {
								color: #EC4068;
							}
						}

						.syl {
							font-weight: 600;

							&.red {
								color: #EC4068;
							}
						}
					}

					.head {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.left {
							display: flex;
							justify-content: space-between;
							align-items: center;

							img {
								width: 50rpx;
								margin-right: 30rpx;
							}

							.title {
								color: #fff;
								font-size: 28rpx;
								font-weight: 600;
								margin-right: 20rpx;
							}

							.sub {
								font-size: 24rpx;
								color: rgb(255, 255, 255, 0.3);
							}
						}

						.cd {
							font-size: 24rpx;

							.red {
								color: #FF5270;
							}

							.huise {
								color: rgb(255, 255, 255, 0.5);
							}

							.white {
								color: #fff
							}
						}
					}

					.data_view {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top: 31rpx;

						.li_view {
							text-align: center;

							&.width33 {
								width: 33.33%;
							}

							&.button {
								width: 144rpx;
								height: 48rpx;
								border-radius: 36rpx;
								display: flex;
								justify-content: center;
								align-items: center;
								font-size: 22rpx;
								background-color: #fff;
							}

							.label {
								color: rgb(255, 255, 255, 0.5);
								font-size: 24rpx;
							}

							.num {
								color: #fff;
								font-size: 28rpx;
								margin-top: 21rpx;
								font-weight: 600;
							}
						}
					}

					.footer {
						margin-top: 30rpx;
						display: flex;
						justify-content: space-between;

						>.left {
							width: 180rpx;
							display: flex;
							justify-content: flex-start;
							align-items: center;

							&.noMargin {
								img {
									margin-right: 6rpx;
								}
							}

							img {
								width: 50rpx;
								height: 50rpx;
								margin-right: 20rpx;
							}

							.qp {
								width: 74rpx;
								height: 36rpx;
							}

							.title {
								color: #fff;
								font-size: 28rpx;
								font-weight: 600;
								margin-right: 20rpx;
							}
						}

						>.right {
							width: 380rpx;
							font-size: 22rpx;
							color: rgb(255, 255, 255, 0.5);

							div:last-child {
								margin-top: 10rpx;
							}
						}
					}
				}
			}
		}
	}

	.van-tabs ::v-deep .van-tabs__line {
		background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 0;
	}

	.van-tabs ::v-deep .van-tab--line {
		font-size: 28rpx;
	}

	.van-tabs ::v-deep .van-tab--active {
		font-weight: 600;
	}

	.van-cell {
		background-color: transparent;
		padding: 0;
		border-bottom: 0;
	}

	.van-cell::after {
		border-bottom: none !important;
	}
	.back {
		position: absolute;
		top: 64rpx;
		left: 30rpx;
	
		image {
			width: 50rpx;
		}
	}
	.share_body {
		width: 100%;
		background-color: #111111;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
		background-size: 100%;
		background-repeat: no-repeat;
		padding: 160rpx 55rpx 55rpx;
		height:100vh;
		.cart_div {
			background-color: #2B2B2B;
			border-radius: 36rpx;
			width: 640rpx;
			height: 1000rpx;
			padding: 40rpx 36rpx;
			position: relative;
	
			.title_image {
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
				width:100%;
				height:111rpx; 
				background-size:100% 100%;
				line-height:111rpx;
				font-size:36rpx;
				color:#fff;
				text-align: center;
				letter-spacing:2rpx;
			}
	
			.toux_image_div {
				width: 259rpx;
				margin: 20rpx auto;
	
				.toux_border {
					width: 100%;
					// height:116rpx;
					background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
					background-size: 100%;
					background-repeat: no-repeat;
					display: flex;
					justify-content: center;
					align-items: center;
	
					padding-top: 40rpx;
	
					.image_div {
						width: 120rpx;
						height: 120rpx;
						background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
						border-radius: 40rpx;
						display: flex;
						justify-content: center;
						align-items: center;
	
						image {
							border-radius: 40rpx;
							width: 112rpx;
							height: 112rpx;
							object-fit: cover;
						}
					}
				}
			}
	
			.toux_name {
				text-align: center;
				font-size: 28rpx;
				color: #fff;
				font-weight: 600;
			}
	
			.yield {
				font-size: 90rpx;
				color: #6CFF8A;
				font-weight: 600;
				margin-top: 40rpx;
	
				text {
					font-size: 55rpx;
				}
	
				&.red {
					color: #FF5270;
				}
			}
	
			.shouyi {
				font-size: 48rpx;
				color: #6CFF8A;
	
				&.red {
					color: #FF5270;
				}
			}
	
			.info_div {
				color: #fff;
				margin-top: 30rpx;
	
				p {
					font-size: 28rpx;
					line-height: 38rpx;
				}
			}
	
			.yqm {
				background-color: rgb(255, 255, 255, 0.2);
				border: 1px solid #fff;
				width: 280rpx;
				height: 54rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #fff;
				font-size: 28rpx;
				margin-top: 40rpx;
				border-radius: 36rpx;
				margin-bottom: 30rpx;
			}
	
			.msg_text {
				font-size: 28rpx;
				color: #fff;
				font-weight: 600;
			}
	
			.icon_bg {
				width: 292rpx;
				position: absolute;
				top: 460rpx;
				right: -30rpx;
	
				image {
					width: 292rpx;
				}
			}
	
			.qr_code {
				position: absolute;
				top: 764rpx;
				right: 36rpx;
	
				.right {
					display: flex;
					justify-content: flex-end;
					align-items: center;
	
					.qr_div {
						width: 140rpx;
						height: 140rpx;
						border-radius: 28rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: #fff;
						padding:10rpx;
					}
				}
	
				.time {
					color: rgb(255, 255, 255, 0.3);
					margin-top: 30rpx;
					font-size: 22rpx;
				}
			}
		}
	
		.share_to_div {
			margin-top: 140rpx;
			display: flex;
			justify-content: center;
	
			>.li {
				width: 25%;
				text-align: center;
				color: #fff;
				font-size: 28rpx;
				margin-top: 10rpx;
	
				.icon_image {
					display: flex;
					justify-content: center;
					image {
						width: 90rpx;
						margin-bottom:20rpx;
					}
				}
			}
		}
	
		.colse_div {
			margin-top: 26rpx;
			display: flex;
			justify-content: center;
	
			image {
				width: 80rpx;
			}
		}
	}
</style>