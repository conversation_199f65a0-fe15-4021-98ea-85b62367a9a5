<template>
	<view class="body">
		<!-- 	<view class="back" @click="back">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
				alt="" srcset="" mode="widthFix"></image>
		</view> -->
		<view style="display: flex;align-items: center;">
			<image @click="indexS = true" style="width: 36rpx;
            height: 24rpx;
            margin: 45rpx 20rpx 0 46rpx;"
				src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/512d559af100d5fd1a4f310e19c2332c_36x24.png">
			</image>

			<view class="leftButton">
				BV指数:{{ exponent }}
			</view>
		</view>

		<u-popup v-model="indexS" width="653rpx" mode="left">
			<view class="mainindex">
				<text class="comp">对标物</text>
				<view style="height: 69rpx"></view>

				<view class="comp-item">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png"
							mode="widthFix"></image>
					</view>
					<text class="name">BV指数</text>
					<text class="price" :style="{ color: Number(bvIn.percent) >= 0 ? '#FE556C' : '#6CFF8A' }">{{ '¥' +
						bvIn.marketPrice || 0.00 }}</text>
					<!--          <text class="change" style="color:#6CFF8A "></text>-->
					<view class="change" :style="{ color: Number(bvIn.percent) >= 0 ? '#FE556C' : '#6CFF8A' }">
						<text v-if="Number(bvIn.percent) >= 0">+</text>
						<text v-else>-</text>
						{{ bvIn.percent || 0.00 }}%
					</view>
				</view>

				<view class="comp-item" @click="changeSymbol('')">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png"
							mode="widthFix"></image>
					</view>
					<text class="name">BIT指数</text>
					<text class="price" :style="{ color: latestPrice - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }">{{ '¥' +
						(latestPrice) || 0 }}</text>
					<!--          <text class="change" style="color:#6CFF8A "></text>-->
					<view class="change" :style="{ color: latestPrice - aimPrice >= 0 ? '#FE556C' : '#6CFF8A' }">
						<text v-if="latestPrice - aimPrice >= 0">+</text>
						<text v-else>-</text>
						{{ fixrate }}%
					</view>
				</view>

				<view class="comp-item" v-for="(item, index) in indexList" :key="index" @click="changeSymbol(item)">

					<view class="img">
						<image :src="item.icon" mode="widthFix"></image>
					</view>

					<text class="name">{{ item.name }}</text>
					<text class="price" :style="{ color: item.price >= 0 ? '#FE556C' : '#6CFF8A' }">{{ '¥' + item.price
						}}</text>
					<text class="change" :style="{ color: item.rate >= 0 ? '#FE556C' : '#6CFF8A' }">
						<text v-if="item.rate >= 0">+</text>
						<text v-else>-</text>
						{{ Math.abs(item.rate) }}</text>
				</view>
			</view>
		</u-popup>

		<view class="rightButton" :class="{ 'active': isUp }">
			<image v-if="isUp"
				src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9c637c67b4df9096ee1acabd1db2326b_22x22.png"
				mode="widthFix"></image>
			<image v-else
				src="https://cdn-lingjing.nftcn.com.cn/image/20240204/770ae60fb30f898fac176d9eef8cac71_22x22.png"
				mode="widthFix"></image>
			{{ isUp ? '+' : '' }}{{ percent }}%
		</view>
		<view class="IP_cover" v-show="!isShowKlink">
			<view class="view">
				<image class="ip"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240702/7c8d453c41b7663a8241ddbf7949285b_680x441.png"
					alt="" srcset="" mode="widthFix"></image>
				<view class="help" @click="help()">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240204/6a42c586759978e65ef795acb90b5f71_28x28.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
			</view>
			<view class="link_view">
				<view class="linkBut" @click="checkLink()">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240607/1095f3b534563f962a403013c8e9ace3_41x41.png"
						alt="" srcset="" mode="widthFix"></image>
					<text>
						查看走势
					</text>
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240607/fe56908d0b409d44167c652c25326c62_37x37.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
			</view>
			<view class="num_view">
				<text class="rmb">￥</text>
				<text class="num">{{ marketPrice }}</text>
			</view>
			<view class="num_msg">
				每3秒刷新一次
			</view>
		</view>
		<view class="tabView" v-if="isShowKlink">
			<view class="leftTabView">
				<u-tabs name="cate_name" bg-color="" :list="tabListKlink" inactive-color="rgb(255,255,255,0.3)"
					active-color="#fff" font-size="24" height="60" :current="klinkStatus" @change="changeKlink"
					:bar-style="barStyleLink" :active-item-style="itemStyleLink"></u-tabs>
			</view>
			<view class="rightClose" @click="checkLink()">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20240321/1fadae1784c07774d675ecc2583e8c53_120x120.png"
					alt="" srcset="" mode="widthFix"></image>
			</view>
		</view>
		<view class="klink_div" v-if="isShowKlink" :class="{ 'fullscreen': isFullScreen }">
			<!-- <lime-echart></lime-echart> -->
			<l-echart class="klink" ref="chartRef" @finished="init"></l-echart>
			<view class="customTooltips"
				:style="{ 'left': positionTip[0] + 'rpx', 'top': positionTip[1] + 'rpx', 'border': '1px solid ' + paramsTip.color }"
				v-show="showTip">
				<view class="time">{{ paramsTip.date }}</view>
				<view class="li_tooltip">
					<view class="view red"></view>
					<text>起始均价:{{ paramsTip.open }}</text>
				</view>
				<view class="li_tooltip">
					<view class="view red"></view>
					<text>结尾均价:{{ paramsTip.close }}</text>
				</view>
				<view class="li_tooltip">
					<view class="view lvse"></view>
					<text>最小均价:{{ paramsTip.lowest }}</text>
				</view>
				<view class="li_tooltip">
					<view class="view lvse"></view>
					<text>最大均价:{{ paramsTip.highest }}</text>
				</view>
			</view>
			<!-- <view class="fullscreen_icom" @click="toggleFullScreen()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240702/99e44fdf108d1e3e11acf2cba3fb3273_50x50.png"
					alt="" srcset="" mode="widthFix"></image>
			</view> -->
		</view>
		<view class="tabs">
			<view class="ul">
				<view class="li" :class="{ 'active': isActive == index }" v-for="(item, index) in tabsList" :key="index"
					@click="checkTabs(item, index)">
					{{ item.name }}
				</view>
			</view>
		</view>
		<view class="quick_view">
			<view class="li">
				<view class="label">
					<text v-if="isActive == 0">唱多价:</text>
					<text v-if="isActive == 1">唱空价:</text>
				</view>
				<view class="input_view">
					<input type="digit" @input="onInputChange" @focus="handleFocus" @blur="handleBlur"
						v-model="importMarketPrice" mode="widthFix" />
					<text @click="setMarketPrice()">市价</text>
				</view>
			</view>
			<view class="li">
				<view class="label">
					份数:
				</view>
				<view class="input_view" @click="sheetShow = true">
					{{ importNum }}
					<!-- <input type="number" min="1" @input="handleInput" v-model="importNum" mode="widthFix" ></image> -->
				</view>
			</view>
			<view class="li">
				<view class="label">
					总价:
				</view>
				<view class="price_view">
					￥{{ ((importMarketPrice * importNum) / importLever).toFixed(2) }}
				</view>
			</view>
			<view class="li noMarginBottom">
				<view class="label">
					加倍:
				</view>
				<view class="ul_view">
					<view class="li" :class="{ 'active': index == isLever }" v-for="(item, index) in leverList"
						@click="checkLever(item, index)">
						{{ item.name }}
					</view>
				</view>
			</view>
			<!-- <view class="qp_msg">强制闭嘴价：￥3.45</view> -->
			<view class="bbutton_view">
				<view class="button" @click="checkSubmit()">
					我唱{{ isActive == 0 ? '多' : '空' }}
				</view>
			</view>
		</view>
		<view class="tabs_div">
			<view class="left">
				<u-tabs name="cate_name" bg-color="" :bar-style="barStyle" :list="tabList" bold
					inactive-color="rgb(255,255,255,0.3)" :active-item-style="itemStyle" active-color="#fff"
					:current="current" @change="changeTab"></u-tabs>
			</view>
			<view class="right" @click="historyTakeOff">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240401/d167aafc2dc95c79dde6fc493795d491_96x96.png"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="details">
			<view class="weituo" v-show="current == 0">
				<view class="li" v-for="(item, index) in entrustList" :key="index">
					<view class="head">
						<view class="left">
							<image v-if="item.longShort == 2"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
								alt="" mode="widthFix"></image>
							<image v-else
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
								alt="" mode="widthFix"></image>
							<text class="title">{{ item.lever == 1 ? '1X' : item.lever + 'X' }}</text>
							<text class="sub">{{ item.createAt }}</text>
						</view>
						<view class="cd" @click="openPop(item, 1, index)" v-if="item.canRevoke">
							放弃
						</view>
					</view>
					<view class="data_view">
						<view class="li_view">
							<view class="label">份数</view>
							<view class="num">{{ item.quantity }}</view>
						</view>
						<view class="li_view">
							<view class="label">已成交</view>
							<view class="num">{{ item.filledQuantity }}</view>
						</view>
						<view class="li_view">
							<view class="label">
								<text v-show="item.longShort == 2">唱空价</text>
								<text v-show="item.longShort == 1">唱多价</text>
							</view>
							<view class="num">{{ item.price }}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cangwei" v-show="current == 1">
				<!-- <transition name="van-fade"> -->
				<view class="li " :class="{ 'active': item.isActive }" v-for="(item, index) in positionsList"
					:key="index">
					<view class="head">
						<view class="left">
							<image v-if="item.longShort == 2"
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
								alt="" mode="widthFix"></image>
							<image v-else
								src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
								alt="" mode="widthFix"></image>
							<text class="title">{{ item.lever == 1 ? '1X' : item.lever + 'X' }}</text>
						</view>
					</view>
					<view class="right_fenx" @click="openShare(item)">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240216/9db5eea18043165f547f2f7775c6eb98_80x80.png"
							alt="" srcset="" mode="widthFix"></image>
					</view>
					<view class="live_data">
						<view class="sy" :class="{ 'red': item.red }">
							<text>{{ item.red ? '战利' : '战损' }}</text>
							{{ calculateEarnings(item) }}
						</view>
						<view class="syl" :class="{ 'red': item.red2 }">
							<text>评估</text>
							{{ calculateYield(item) }}
						</view>
					</view>
					<view class="data_view">
						<view class="li_view">
							<view class="label">{{ item.longShort == 2 ? "唱空" : "唱多" }}均价</view>
							<view class="num">{{ item.price }}</view>
						</view>
						<view class="li_view">
							<view class="label">份数</view>
							<view class="num">{{ item.quantity }}</view>
						</view>
						<view class="li_view">
							<view class="label">强制闭嘴价</view>
							<view class="num">{{ item.forceClosePrice ? item.forceClosePrice : '-' }}</view>
						</view>
						<view class="li_view button" @click="openPop(item, 2, index)" v-if="item.canClose">
							我不唱了
						</view>
					</view>
				</view>
				<!-- </transition> -->
			</view>
		</view>
		<u-popup v-model="isAgreement" mode="center">
			<view class="bodyPop_xieyi">
				<view class="msg_div">
					<view>首次参与 《BV开杠吧活动》请阅读并同意</view>
					<text @click="nav_agreement">《BV开杠吧活动服务协议》</text>
				</view>
				<view class="button">
					<view class="cancel" @click="isAgreement = false">取消</view>
					<view class="submit" @click="consent()">我已阅读并已同意<text v-if="countDown > 0">({{ countDown }})</text>
					</view>
				</view>
			</view>
		</u-popup>
		<u-popup v-model="isOrderCheck" class="popup_body_bottom" mode="bottom">
			<view class="bodyPop">
				<view class="right_colse" @click="isOrderCheck = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240218/82101063938685dbb1b68833f31c70a7_80x80.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
				<view class="head">
					<view class="title">
						下单确认
					</view>
					<view class="sub_title">
						<p><text>我唱{{ isActive == 0 ? '多' : '空' }} {{ importLever }}X</text>BV藏品</p>
					</view>
				</view>
				<view class="body_view">
					<view class="padding40">
						<view class="item">
							<view class="name">唱{{ isActive == 0 ? '多' : '空' }}价格</view>
							<view class="valUue">￥{{ importMarketPrice }}</view>

						</view>
						<view class="item">
							<view class="name">数量</view>
							<view class="valUue">{{ importNum }}份</view>
						</view>
						<view class="item">
							<view class="name">实际支付</view>
							<view class="valUue">¥{{ (importMarketPrice * importNum / importLever).toFixed(2) }}</view>
						</view>
					</view>
					<view class="msg" @click="check_pop_bzts()">
						<image v-if="!checkSubmitCheckend"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240219/db14230ff1d7d28fcbbf2dba0f130fc0_30x30.png"
							alt="" mode="widthFix"></image>
						<image v-if="checkSubmitCheckend"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240219/5d90396a0f4cd34e351c91a17a732ec8_30x30.png"
							mode="widthFix"></image>
						不再提示
					</view>
					<view class="but" @click="submitAll()">
						确认
					</view>
				</view>
			</view>
		</u-popup>
		<u-popup v-model="isConfirmation" mode="center">
			<view class="bodyPop_msg">
				<view class="msg_view">
					<!-- <p>提示</p> -->
					<text>{{ pop_msg }}</text>
				</view>
				<view class="button_msg">
					<view class="cancel" @click="allColse()">取消</view>
					<button class="submit" @click="submit_open()">确认</button>
				</view>
			</view>
		</u-popup>
		<u-popup v-model="isShare" mode="bottom">
			<view class="share_body">
				<view class="back" @click="isShare = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
				<view class="cart_div" id="test-id">
					<view class="title_image">
						BV“开杠吧”战报
					</view>
					<view class="toux_image_div">
						<view class="toux_border">
							<view class="image_div">
								<image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
							</view>
						</view>
					</view>
					<view class="toux_name">
						{{ shareUser.addr }}
					</view>
					<view class="yield" :class="{ 'red': shareUser.red }">
						<text></text>{{ shareUser.zNum }}
					</view>
					<view class="shouyi" :class="{ 'red': shareUser.red }">
						<text></text>{{ shareUser.gNum }}
					</view>
					<view class="info_div">
						<p>买入价：￥{{ shareUser.price }}</p>
						<!-- <p>卖出价：￥3.45</p> -->
						<p>杠杆：{{ shareUser.lever }}X</p>
					</view>
					<view class="yqm">
						邀请码：{{ shareUser.invitationCode }}
					</view>
					<view class="msg_text">
						扫码注册，尽情开杠
					</view>
					<view class="icon_bg">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
							alt="" srcset="" mode="widthFix"></image>
					</view>
					<view class="qr_code">
						<view class="right">
							<view class="qr_div">
								<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx"
									:options="options"></uv-qrcode>
							</view>
						</view>
						<view class="time">分享于{{ shareUser.currentDateTime }}</view>
					</view>
				</view>
				<view class="share_to_div">
					<!-- #ifdef APP -->
					<view class="li" @click="fenx_weixin()">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>微信</p>
					</view>
					<!-- #endif -->

					<!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
				</view>
				<view class="colse_div" @click="isShare = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
						mode="widthFix"></image>
				</view>
			</view>
		</u-popup>
		<u-popup v-model="isGuidance" mode="center" class="guidance_body">
			<view class="image" v-for="(item, index) in guidanceList">
				<img :src="item.src" v-show="index == guidanceShow" mode="widthFix" @click="guidanceCheck(index)">
			</view>
		</u-popup>
		<u-action-sheet :cancel-btn="false" border-radius="24" :list="actions" @click="onSelect"
			v-model="sheetShow"></u-action-sheet>
		<pay-popup :popup-show.sync="isPayPassword" order-type="" title="支付密码" message="请输入余额支付密码，用于购买"
			@pay="paySubmit" />
		<u-modal class="" v-model="isLinkLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				加载中...
			</view>
		</u-modal>
		<u-popup v-model="isTest" mode="center">
			<view class="bodyPop_msg">
				<view class="msg_view">
					<!-- <p>提示</p> -->
					<view>当前为测试阶段</view>
					<view>仅校验钱包余额，买卖不花钱</view>
				</view>
				<view class="button_msg">
					<button class="submit" @click="isTest = false">我知道了</button>
				</view>
			</view>
		</u-popup>
		<TabBar :initialActiveIndex="4"></TabBar>
	</view>
</template>
<script>
import payPopup from "@/components/payPopup/index.vue";
import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue';
import store from '@/store'
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import TabBar from "@/components/public/TabBar";
export default {
	data() {
		return {
			aimPrice: '',
			indexList: [
			],
			latestPrice: 0, // 盘口最新价格
			bvIn: {}, // bv指数信息
			indexS: false,
			isUp: false,
			isLever: 0,
			leverList: [{
				name: "1X",
				value: 1
			}, {
				name: "2X",
				value: 2
			}, {
				name: "5X",
				value: 5
			}, {
				name: "10X",
				value: 10
			}],
			tabsList: [{
				name: "我唱多"
			},
			{
				name: "我唱空"
			}
			],
			current: 0,
			isActive: 0, //唱多 唱空
			isAgreement: false,
			countDown: 3,
			isOrderCheck: false,
			entrustList: [], //当前委托 列表
			positionsList: [], //我的仓位 列表
			loading: false, //分页加载中
			finished: false, //没有更多了
			pageNum: 1, //分页
			marketPrice: '', //市价
			tradedLoading: false, //分页加载中
			tradedFinished: false, //没有更多了
			tradedPageNum: 1, //分页
			isConfirmation: false,
			pop_msg: "确认放弃？",
			orderId: "", //订单号
			status: "", //订单状态
			typePop: 1, //pop类型识别
			checkSubmitCheckend: false, //不再提示状态
			showPassword: true, //是否需要密码框
			importMarketPrice: '', //输入的市价
			importNum: '10', //输入的份数
			importTotalPrices: '', //总价
			importLever: '1', //选择的杠杆
			isPayPassword: false,
			chartRef: null,
			isShowKlink: false,
			isShare: false,
			shareUser: {},
			chartInstance: null,
			setTimeoutNo: null,
			kLineData: [],
			timeData: [],
			kLineTimeData: [],
			kMin: "",
			kMax: "",
			currentDateTime: "",
			isSubmitMsgType: false, //下单软提示
			isUserFocus: false,
			isGuidance: false,
			guidanceShow: 0,
			guidanceList: [{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_1.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_2.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_3.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_4.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_5.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_6.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_7.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_8.png"
			},
			{
				src: "https://cdn-lingjing.nftcn.com.cn/h5/takeOff/xs_9.png"
			}
			],
			percent: "",
			wdcw: "我的仓位",
			ddcj: "等待成交",
			indexTag: "",
			entrustTotal: 0, //当前委托 列表
			oldEntrustTotal: 0,
			positionsTotal: 0, //我的仓位 列表
			localStorage: "",
			isSuo: false,
			actions: [{
				text: '10份',
				value: 10
			},
			{
				text: '50份',
				value: 50
			},
			{
				text: '100份',
				value: 100
			},
			{
				text: '200份',
				value: 200
			},
			{
				text: '500份',
				value: 500
			}
			],
			sheetShow: false,
			klinkStatus: 0,
			interval: 15, //k线图 时间
			pageSize: 288,
			percentCheck: 0,
			delNum: 0,
			isReq: false,
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				'width': '80rpx',
				'height': '6rpx',
				'border-radius': '0rpx',
				'bottom': '6rpx',
				'z-index': '1'
			},
			itemStyle: {
				'font-size': '28rpx',
				// 'min-width': '120rpx',
				'z-index': '2',
				'width': '240rpx'
			},
			tabList: [{
				name: "等待成交",
				value: 0
			}, {
				name: "我的仓位",
				value: 1
			}],
			debounceTimeout: "",
			globalOption: {
				animation: false,
				grid: {
					// 调整右侧的距离，增大该值可以增加右侧的安全距离
					top: "5%",
					left: "4%",
					right: '16%', // 默认是'5%'
					bottom: '20%'
				},
				tooltip: {
					trigger: 'item', // 触发类型为'item'，表示鼠标 hover 到具体的 K 线上时显示 tooltip
					axisPointer: {
						animation: false,
						type: 'cross',
						lineStyle: {
							color: '#376df4',
							width: 2,
							opacity: 1
						}
					},
					position: (point, params, dom, rect, size) => {
						// 假设自定义的tooltips尺寸
						const box = [170, 107]
						// 偏移
						const offsetX = point[0] < size.viewSize[0] / 2 ? 10 : -box[0] - 10;
						const offsetY = point[1] < size.viewSize[1] / 2 ? 10 : -box[1] - 10;
						const x = point[0] + offsetX;
						const y = point[1] + offsetY;

						this.positionTip = [x, y]
					},
					formatter: (params, ticket, callback) => {
						let min = 15;
						if (this.klinkStatus == 0) {
							min = 15
						} else if (this.klinkStatus == 1) {
							min = 60
						} else if (this.klinkStatus == 2) {
							min = 60 * 24
						}
						const now = new Date(params.data[5] * 1000);
						const oldTime = new Date((new Date(now).getTime()) + (min * 60000))
						const year = now.getFullYear();
						const month = ("0" + (now.getMonth() + 1)).slice(-2); // 月份是从0开始计数的，需要加1
						const day = ("0" + now.getDate()).slice(-2);
						const hours = ("0" + now.getHours()).slice(-2);
						const minutes = ("0" + now.getMinutes()).slice(-2);
						const hoursOld = ("0" + oldTime.getHours()).slice(-2);
						const minutesOld = ("0" + oldTime.getMinutes()).slice(-2);

						// // params 包含了当前被 hover 的数据项的信息
						var date = ""
						if (this.klinkStatus < 2) {
							date = `时间:${hours}:${minutes}-${hoursOld}:${minutesOld}`;
						} else {
							date = `时间:${year}-${month}-${day}`;
						}

						var open = params.data[1];
						var close = params.data[2];
						var highest = params.data[4];
						var lowest = params.data[3];
						var color = params.color;
						this.paramsTip = {
							date,
							open,
							close,
							highest,
							lowest,
							color
						}
						// 自定义 tooltip 显示的内容 
						// return `${date}
						// 		  起始均价:￥${open}
						// 		  结尾均价:￥${close}
						// 		  最小均价:￥${lowest}
						// 		  最大均价:￥${highest}`
						// return date + '<br />' +
						// 	redDiv + '起始均价: ' + open + '<br />' +
						// 	redDiv + '结尾均价: ' + close + '<br />' +
						// 	lvseDiv + '最小均价: ' + lowest + '<br />' +
						// 	lvseDiv + '最大均价: ' + highest;
					},
					backgroundColor: 'rgba(20, 24, 22, 0.8)', // 自定义背景色
					textStyle: {
						color: '#fff', // 自定义 tooltip 文字颜色为深灰色
						fontSize: 20,
					}
				},
				xAxis: {
					type: 'category',
					boundaryGap: true, // 防止数据点与轴的边界重合
					data: [],
				},
				yAxis: {
					type: 'value',
					boundaryGap: true, // 防止数据点与轴的边界重合
					position: 'right', // 将y轴位置设置为右侧
					// min: kMin, // 根据实际情况设置最小值
					// max: kMax, // 根据实际情况设置最大值
				},
				series: [{
					type: 'candlestick',
					data: [],
				}],
				dataZoom: [{
					type: 'inside',
					zoomLock: false,
					zoomSmooth: true,
					start: 90,
					end: 100
				}],
			},
			chartInstance: "",
			options: {
				useDynamicSize: false,
				errorCorrectLevel: 'Q',
				// margin: 10,
				areaColor: "#fff",
				// 指定二维码前景，一般可在中间放logo
				// foregroundImageSrc: require('static/image/logo.png')
			},
			qrcodeUrl: "",
			isFooter: true,
			isRequest: false,
			positionTip: [],
			paramsTip: {},
			showTip: false,
			tabListKlink: [{
				name: "15分",
				value: 0
			}, {
				name: "1小时",
				value: 1
			}, {
				name: "24小时",
				value: 2
			},],
			barStyleLink: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				'width': '40rpx',
				'height': '6rpx',
				'border-radius': '0rpx',
				'bottom': '-6rpx',
				'z-index': '1'
			},
			itemStyleLink: {
				'font-size': '24rpx',
				'z-index': '2',
			},
			isLinkLoadding: false,
			isTest: false,
			longPressTimer: '',
			isLongPressing: false,
			dataZoomStart: 90,
			dataZoomEnd: 100,
			exponent: '',
			isFullScreen: false,
			lEchart: "",
			currentIndicator: uni.getStorageSync('currentIndictor') || null, // 初始化
			indictor: {},

		}
	},
	components: {
		payPopup,
		TabBar
	},
	onHide() {
		if (this.setTimeoutNo) {
			console.log("销毁定时器")
			clearTimeout(this.setTimeoutNo)
			this.setTimeoutNo = null
		}
	},
	onShow() {
		console.log("this.setTimeoutNo", this.setTimeoutNo)
		if (!this.setTimeoutNo) {
			this.getInfo();
		}
	},
	onLoad(options) {
		uni.setStorageSync('isbitpage', 2)
		this.fetchwhatgobuy()
		this.fetchIndex()
		if (!uni.getStorageSync('isGuidanceStorage')) {
			this.isGuidance = true
		}
		const {
			isType,
			importLever,
			importNum,
			importMarketPrice
		} = options;
		this.isActive = isType ? isType : 0
		if (importMarketPrice) {
			this.isUserFocus = true
			this.importNum = importNum
			this.importLever = importLever

			if (this.importLever == 1) {
				this.isLever = 0
			} else if (this.importLever == 2) {
				this.isLever = 1
			} else if (this.importLever == 5) {
				this.isLever = 2
			} else if (this.importLever == 10) {
				this.isLever = 3
			}
			this.importMarketPrice = importMarketPrice
		}
		this.getUserShare()
		this.getDelegating()

		this.getTraded()
		this.firstVisit()
		this.getPercent()
	},
	onPullDownRefresh() {
		setTimeout(() => {
			if (this.setTimeoutNo) {
				console.log("成功")
				clearTimeout(this.setTimeoutNo)
			}
			this.entrustList = []
			this.pageNum = 1
			this.delNum = 0
			this.positionsList = []
			this.tradedPageNum = 1
			this.isFooter = true
			this.kLineData = []
			this.kLineTimeData = []
			if (this.current == 0) {
				this.getDelegating()
			} else {
				this.getTraded()
			}
			this.getInfo();
			this.getUserShare()
			this.firstVisit()
			this.getPercent()
			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 500);
	},
	onReachBottom() {
		if (this.isFooter) {
			if (this.isRequest == false) {
				if (this.current == 1) {
					this.getTraded()
				}
			} else {
				console.log("请求超时，已经拦截")
			}
		} else {
			console.log("已经到底了")
		}
	},
	watch: {
		"$store.state.realprice"(val) {
			let a = Number(val)
			let b = a.toFixed(3)
			this.latestPrice = b
		}
	},
	methods: {
		async fetchIndex() {
			let res = await this.$api.indexs()
			if (res.status.code === 0) {
				this.indexList = res.result.list
			}
		},
		async fetchwhatgobuy() {
			let res;
			// 当 contractName 为空时调用 GetExchangeUserInfo 接口
			res = await this.$api.GetExchangeParam();
			if (res.status.code == 0) {
				this.aimPrice = res.result.aimPrice
			}
		},
		changeSymbol(e) {
			if (!e) {
				this.indexS = false
				this.indictor = {}
				store.commit("changeOPtionSymbol", 'aaveusdt')
				this.currentIndicator = ""
				uni.setStorageSync('currentIndictor', this.currentIndicator)
				this.$Router.pushTab({
				name:'contract-BITindex'
			})
				return
			}
			console.log(e.name, '12312312')
			store.commit("changeOPtionSymbol", (e.name + 'USDT').toLocaleLowerCase())
			this.currentIndicator = e.contractName
			uni.setStorageSync('currentIndictor', this.currentIndicator)
			this.indictor = e
			this.indexS = false

			this.$Router.pushTab({
				name:'contract-BITindex'
			})
		},
		async get_KLine() {
			let marketPrice = this.marketPrice
			let res = await this.$api.getKLine({
				interval: this.interval,
				pageSize: this.pageSize
			});
			if (res.status.code == 0) {
				let arr = [],
					time = [];
				res.result.list.forEach((item) => {
					let arr = [item.openPrice, item.closePrice, item.lowPrice, item.highPrice, item
						.startTime * 60
					]
					let arr2 = []
					if (this.klinkStatus == 0) {
						arr2 = [this.timestampToDateHours(item.startTime * 60)]
					} else if (this.klinkStatus == 1) {
						arr2 = [this.timestampToDateHours(item.startTime * 60)]
					} else {
						arr2 = [this.timestampToDate(item.startTime * 60)]
					}
					this.kLineData.push(arr)
					this.kLineTimeData.push(arr2)
				})
				// kMin = res.list[0].minPrice
				// kMax = res.list[0].maxPrice
				// // 设置图表的配置项
				this.globalOption.yAxis = {
					type: 'value',
					position: 'right',
					axisLabel: {
						align: 'center',
						fontSize: 10
					},
					scale: true
					// min: kMin, // 根据实际情况设置最小值
					// max: kMax, // 根据实际情况设置最大值
				}
				this.globalOption.xAxis = {
					data: this.kLineTimeData,
				}
				this.globalOption.series = [{
					type: 'candlestick',
					data: this.kLineData,
					markLine: {
						label: {
							show: true,
							fontSize: 10, // 字体大小
							color: '#FFF',
							formatter() {
								return `${marketPrice}市价`
							},
							align: 'left',
						},
						symbol: ['none', 'none'],
						data: [{
							yAxis: `${marketPrice}`
						}],
						lineStyle: {
							width: 1,
							color: '#FF5270',
							fontSize: '10px'
						}
					},
				}],
					console.log("数据=============")
				console.log(this.globalOption)
				if (this.isShowKlink) {
					this.setlEchart()
					// let chart = await this.$refs.chartRef.init(echarts);
					// console.log("this.globalOption")
					// this.lEchart.setOption(this.globalOption)
					this.isLinkLoadding = false
				}
				// this.chartInstance.setOption(this.globalOption, true);
			} else {
				if (res.status.code === 1002) {
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin",
							// #ifdef H5
							params: {
								url: window.location.hash,
							},
							// #endif
						})
					}, 1500);
				} else {
					this.Toast(res.status.msg);
				}
			}
		},
		async getInfo() {
			let res = await this.$api.getMarketPrice({

			});
			if (res.status.code == 0) {
				this.bvIn = res.result
				this.marketPrice = res.result.marketPrice
				this.percent = (res.result.percent * 100).toFixed(2)
				this.exponent = res.result.exponent
				if (this.isPositiveNumber(res.result.percent)) {
					this.isUp = true
				} else {
					this.isUp = false
				}
				if (!this.isUserFocus) {
					this.importMarketPrice = this.marketPrice
				}
				this.oldEntrustTotal = this.entrustList.length
				if (this.isShowKlink) {
					let marketPrice = this.marketPrice
					// #ifdef H5
					this.globalOption.series[0].markLine = {
						label: {
							show: true,
							fontSize: 10, // 字体大小
							color: '#FFF',
							formatter() {
								return `${marketPrice}市价`
							},
							align: 'left',
						},
						symbol: ['none', 'none'],
						data: [{
							yAxis: `${marketPrice}`
						}],
						lineStyle: {
							width: 1,
							color: '#FF5270',
							fontSize: '10px'
						}
					}
					this.lEchart.setOption(this.globalOption);
					// #endif
					// #ifdef APP
					this.lEchart = await this.$refs.chartRef.init(echarts);
					this.globalOption.series[0].markLine = {
						label: {
							show: true,
							fontSize: 10, // 字体大小
							color: '#FFF',
							formatter() {
								return `${marketPrice}市价`
							},
							align: 'left',
						},
						symbol: ['none', 'none'],
						data: [{
							yAxis: `${marketPrice}`
						}],
						lineStyle: {
							width: 1,
							color: '#FF5270',
							fontSize: '10px'
						}
					}
					this.lEchart.on('dataZoom', (params) => {
						if (params.batch && params.batch[0]) {
							const {
								start,
								end
							} = params.batch[0];
							this.dataZoomStart = start
							this.dataZoomEnd = end
							// 这里可以执行更多基于start和end的操作 例如动态加载数据等
						}
					});
					this.globalOption.dataZoom = [{
						moveOnMouseMove: true,
						type: 'inside',
						zoomLock: false,
						zoomSmooth: true,
						start: this.dataZoomStart,
						end: this.dataZoomEnd
					}]
					this.lEchart.on('showTip', (params) => {
						if (params.dataIndex == 0) {
							this.showTip = false
						} else {
							this.showTip = true
						}
					});
					this.lEchart.on('hideTip', (params) => {
						this.showTip = false
						// console.log('hideTip::', params)
					});
					// #endif
					this.lEchart.setOption(this.globalOption);
				}
				this.setTimeoutNo = setTimeout(() => {
					if (this.entrustTotal > 0 && this.current == 0) {
						this.getDelegating()
					} else {
						this.delList()
					}
					this.getInfo()
				}, 3000)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getDelegating() {
			let res = await this.$api.getDelegatingOrder({
				pageNum: 1,
				pageSize: 100
			});
			if (res.status.code == 0) {
				this.entrustTotal = res.result.totalCount
				this.tabList[0].name = `等待成交(${res.result.totalCount})`
				this.loading = false
				this.entrustList = res.result.list
				if (this.oldEntrustTotal != this.entrustTotal) {
					this.positionsList = []
					this.tradedPageNum = 1
					this.getTraded()
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getTraded() {
			this.isRequest = true
			let res = await this.$api.getTradedOrderder({
				pageNum: this.tradedPageNum,
				pageSize: 10
			});
			if (res.status.code == 0) {
				this.isRequest = false
				this.positionsTotal = res.result.totalCount
				this.tabList[1].name = `我的仓位(${res.result.totalCount})`
				// 数据初始化
				if (res.result.list == null || res.result.list == "") {
					this.isFooter = false
				} else {
					this.tradedPageNum++
					res.result.list.forEach((item) => {
						let str, str2;
						const {
							price,
							quantity,
							longShort,
							lever
						} = item
						if (longShort == 1) {
							str = (this.marketPrice - price) * quantity
							let shouyiNum = (this.marketPrice - price) * quantity //收益
							let baozhenjinNum = (price * quantity) / lever //保证金
							str2 = shouyiNum / baozhenjinNum
						} else {
							str = (price - this.marketPrice) * quantity
							let shouyiNum = (this.marketPrice - price) * quantity //收益
							let baozhenjinNum = (price * quantity) / lever //保证金
							str2 = shouyiNum / baozhenjinNum
						}
						str = Math.floor(str * 100) / 100
						str2 = Math.floor(str2 * 10000) / 100
						if (str >= 0) {
							item.red = true
						} else {
							item.red = false
						}
						if (str2 >= 0) {
							item.red2 = true
						} else {
							item.red2 = false
						}
						this.positionsList.push(item)
					})
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		check_select() {
			this.check = !this.check
		},
		checkLever(item, index) {
			this.isLever = index
			this.importLever = item.value
		},
		twoTypeCheck(type) {
			this.twoType = 2
			this.identity_type = type
		},
		checkTabs(item, index) {
			this.isActive = index
			this.setMarketPrice()
		},
		back() {
			if (this.setTimeoutNo) {
				console.log("成功")
				clearTimeout(this.setTimeoutNo)
			}
			this.$Router.back()
		},
		countDownTimer(res) {
			if (this.countDown > 0) {
				setTimeout(() => {
					this.countDown -= 1
					this.countDownTimer(res)
				}, 1000)
			} else {
				console.log("结束了")
				// countDown = 3
			}
		},
		consent() {
			if (this.countDown > 0) {

			} else {
				this.isAgreement = false
				this.isOrderCheck = true
			}
		},
		//点击我唱多
		checkSubmit() {
			this.debounce(() => {
				let notifyPercent = Number(this.percentCheck)
				let beilv;
				console.log(notifyPercent)
				if (this.isActive == 0) {
					beilv = ((this.marketPrice * (notifyPercent + 1)))
					if (this.importMarketPrice >= beilv) {
						this.isSubmitMsgType = true
						this.isConfirmation = true
						this.pop_msg = '当前价格相比市价差距较大，请再次确认'
						return
					}
				}
				if (this.isActive == 1) {
					beilv = ((this.marketPrice * (1 - notifyPercent)))
					if (this.importMarketPrice <= beilv) {
						this.isSubmitMsgType = true
						this.isConfirmation = true
						this.pop_msg = '当前价格相比市价差距较大，请再次确认'
						return
					}
				}
				console.log("门槛", beilv)
				if (this.importMarketPrice == "") {
					this.Toast(`请输入唱${this.isActive == 0 ? "多" : "空"}价格`);
				} else {
					if (!this.isReq) {
						this.get_userInfo()
						setTimeout(() => {
							this.isReq = false
						}, 3000)
					}
				}
			}, 300)
		},
		//
		async updateType() {
			let res = await this.$api.updateUserInfo({
				agreeStatus: 1,
				tipStatus: this.checkSubmitCheckend ? 1 : 0,
			});
			if (res.status.code == 0) {
				this.submitEntrustOrder()
			} else {
				this.Toast(res.status.msg);
			}
		},
		//等待成交
		async submitEntrustOrder() {
			let res = await this.$api.entrustOrder({
				lever: this.importLever,
				longShort: this.isActive == 0 ? 1 : 2,
				offsets: "1",
				orderPriceType: "2",
				price: this.importMarketPrice,
				quantity: this.importNum,
				activityNo: 'A70038988068069377',
				isPreserve:1
			});
			if (res.status.code == 0) {
				this.isOrderCheck = false
				this.pageNum = 1
				this.entrustList = []
				this.positionsList = []
				this.tradedPageNum = 1
				this.finished = false;
				this.Toast(`唱${this.isActive == 0 ? '多' : '空'}成功`);
				this.importMarketPrice = ""
				this.isUserFocus = true
				this.getDelegating()
				this.getTraded()
			} else {

				this.isOrderCheck = false
				this.isSubmitMsgType = false
				if (res.status.code === 1002) {
					this.Toast(res.status.msg);
					setTimeout(() => {
						sessionStorage.clear();
						this.goLogin();
					}, 2000)
				} else {
					this.Toast(res.status.msg);
				}
			}
		},
		//强制平仓价
		async getClosePrice() {
			let res = await this.$api.getForClosePrice({
				lever: this.importLever,
				longShort: this.isActive == 0 ? 1 : 2,
				offsets: "1",
				orderPriceType: "2",
				price: this.importMarketPrice,
				quantity: this.importNum,
				activityNo: 'A70038988068069377'
			});
			if (res.status.code == 0) {

			} else {
				if (res.status.code === 1002) {
					this.Toast(res.status.msg);
					setTimeout(() => {
						sessionStorage.clear();
						goLogin();
					}, 2000)
				} else {
					this.Toast(res.status.msg);
				}
			}
		},
		submit_open() {
			this.debounce(() => {
				if (this.isSubmitMsgType) {
					this.get_userInfo()
				} else {
					if (this.typePop == 1) {
						this.submitRevokeOrder()
					} else {
						uni.showLoading({
							title: '平仓中',
							mask: true,
							duration: 3000
						});
						this.submitCloseOrder()
					}
				}
			}, 300)
		},
		submitAll() {
			this.debounce(() => {
				if (this.showPassword) {
					this.isPayPassword = true
					this.isOrderCheck = false
				} else {
					this.updateType()
				}
			}, 300)
		},
		setMarketPrice() {
			this.importMarketPrice = this.marketPrice
		},
		help() {
			// #ifdef APP
			let link = `${getApp().globalData.activeUrl}#/takeOffIntroduce`
			this.$Router.push({
				name: "webView",
				params: {
					url: link,
				}
			})
			// #endif
			// #ifdef H5
			let {
				origin
			} = window.location
			window.location.href = `${origin}/active/#/takeOffIntroduce`
			// #endif
		},
		historyTakeOff() {
			this.$Router.push({
				name: "history"
			})
		},
		//计算战损/战利
		calculateEarnings(item) {
			let str;
			const {
				price,
				quantity,
				longShort,
				lever
			} = item
			if (longShort == 1) {
				str = (this.marketPrice - price) * quantity
			} else {
				str = (price - this.marketPrice) * quantity
			}
			str = Math.floor(str * 100) / 100
			// console.log(str)
			if (str >= 0) {
				item.red = true
			} else {
				item.red = false
			}
			return str >= 0 ? '+￥' + Math.abs(str) : '-￥' + Math.abs(str)
		},
		//计算收益率
		calculateYield(item) {
			let str;
			const {
				price,
				longShort,
				quantity,
				lever
			} = item
			if (longShort == 1) { //唱多
				let shouyiNum = (this.marketPrice - price) * quantity //收益
				let baozhenjinNum = (price * quantity) / lever //保证金
				str = shouyiNum / baozhenjinNum
			} else { //唱空
				let shouyiNum = (price - this.marketPrice) * quantity //收益
				let baozhenjinNum = (price * quantity) / lever //保证金
				str = shouyiNum / baozhenjinNum
			}
			str = Math.floor(str * 10000) / 100
			if (str >= 0) {
				item.red2 = true
			} else {
				item.red2 = false
			}
			return str >= 0 ? `+${str}%` : `${str}%`
		},
		// 数据过滤删除
		delList() {
			this.positionsList.forEach((item, index) => {
				if (item.forceClosePrice) {
					if (item.longShort == 1) { //唱多
						if (Number(this.marketPrice) <= Number(item.forceClosePrice)) {
							this.positionsList.splice(index, 1)
							this.delNum++
						}
					} else { //唱空
						if (Number(this.marketPrice) >= Number(item.forceClosePrice)) {
							this.positionsList.splice(index, 1)
							this.delNum++
						}
					}
				}
			})
			console.log('delNum', this.delNum)
			this.tabList[1].name = `我的仓位(${(this.positionsTotal - this.delNum) > 0 ? this.positionsTotal - this.delNum : 0})`
		},
		//放弃
		async submitRevokeOrder() {
			let res = await this.$api.revokeOrder({
				orderId: this.orderId,
				status: this.status
			});
			if (res.status.code == 0) {
				this.isConfirmation = false
				this.entrustList[this.indexTag].canRevoke = false
				this.getOrderInfo_dd(this.orderId)
			} else {
				this.isConfirmation = false
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		//一键平仓
		async submitCloseOrder() {
			this.isConfirmation = false
			let res = await this.$api.closeOrder({
				orderId: this.orderId,
				status: this.status
			});
			if (res.status.code == 0) {
				// console.log(positionsList[])
				// Toast.clear();
				// positionsList[indexTag].canClose = false
				this.positionsList[this.indexTag].isActive = true
				setTimeout(() => {
					this.positionsList.splice(this.indexTag, 1)
					this.delNum++
					this.Toast('平仓成功');
				}, 1000)
				// getOrderInfo(orderId)
			} else {
				this.isConfirmation = false
				this.Toast(res.status.msg);
			}
		},
		//我的仓位 订单查询
		async getOrderInfo(orderId) {
			let res = await this.$api.getSingleOrderInfo({
				orderId,
			});
			if (res.status.code == 0) {
				console.log(res)
				if (res.status == 5) {
					clearInterval(setInterval);
					let index = this.positionsList.findIndex(item => item.orderId === orderId);
					if (index !== -1) {
						this.positionsList.splice(index, 1);
					}
					console.log(positionsTotal)
					this.tabList[1].name = `我的仓位(${this.positionsTotal - 1})`
				} else {
					let setInterval = setTimeout(() => {
						this.getOrderInfo(orderId)
					}, 3000)
				}
			} else {
				this.Toast(res.status.msg);
			}
		},
		//等待成交 订单查询
		async getOrderInfo_dd(orderId) {
			let res = await this.$api.getSingleOrderInfo({
				orderId
			});
			if (res.status.code == 0) {
				console.log("状态", res.status)
				if (res.status != 0) {
					clearInterval(setInterval);
					let index = this.entrustList.findIndex(item => item.orderId === orderId);
					if (index !== -1) {
						this.entrustList.splice(index, 1);
					}
					// entrustTotal = entrustTotal>0?entrustTotal-1:0
					console.log(this.entrustTotal)
					this.tabList[0].name = `等待成交(${this.entrustTotal})`
				} else {
					let setInterval = setTimeout(() => {
						this.getOrderInfo_dd(orderId)
					}, 3000)
				}
			} else {
				this.Toast(res.status.msg);
			}
		},
		confirm() {
			this.isConfirmation = false
		},
		openPop(item, type, index) {
			this.indexTag = index
			this.orderId = item.orderId
			this.status = item.status
			this.typePop = type
			if (type == 1) {
				this.pop_msg = "确认放弃？"
			} else {
				this.pop_msg = "确认不唱了？"
			}
			this.isConfirmation = true
		},
		check_pop_bzts() {
			this.checkSubmitCheckend = !this.checkSubmitCheckend
		},
		paySubmit(pass) {
			this.isPayPassword = false
			this.check_pass(pass)
		},
		async check_pass(pass) {
			let res = await this.$api.checkPass({
				pass
			});
			if (res.status.code == 0) {
				this.updateType()
			} else {
				this.Toast(res.status.msg);
			}
		},
		allColse() {
			this.isConfirmation = false
		},
		//获取弹窗状态
		async get_userInfo() {
			this.isConfirmation = false
			this.isSubmitMsgType = false
			this.isReq = true
			let res = await this.$api.getUserInfotake({

			});
			if (res.status.code == 0) {
				this.isReq = false
				if (res.result.agreeStatus == 0) {
					//需要协议提示委托
					this.getClosePrice()
					this.isAgreement = true
					this.countDownTimer()
				} else if (res.result.passStatus == 0) {
					this.isOrderCheck = true
					this.showPassword = true
					//需要密码委托
				} else if (res.result.tipStatus == 0) {
					this.showPassword = false
					this.isOrderCheck = true
					//弹出提示委托
				} else {
					this.submitEntrustOrder()
					//直接委托
				}
			} else {
				this.Toast(res.status.msg);
			}
		},
		changeTab(e) {
			this.current = e
			if (e == 0) {
				this.entrustList = []
				this.pageNum = 1
				this.getDelegating()
			} else if (e == 1) {
				this.delNum = 0
				this.positionsList = []
				this.tradedPageNum = 1
				this.isFooter = true
				this.getTraded()
			}
		},
		changeKlink(e) {
			this.kLineData = []
			this.kLineTimeData = []
			this.klinkStatus = e
			this.isLinkLoadding = true
			this.showTip = false
			this.dataZoomStart = 90
			this.dataZoomEnd = 100
			if (e == 0) {
				//15分钟
				this.interval = 15
				this.pageSize = 288
				this.get_KLine()
			} else if (e == 1) {
				//1小时
				this.interval = 60
				this.pageSize = 240
				this.get_KLine()
			} else if (e == 2) {
				//24小时
				this.interval = 60 * 24
				this.pageSize = 365
				this.get_KLine()
			}
		},
		checkLink() {
			if (!this.isShowKlink) {
				this.kLineData = []
				this.kLineTimeData = []
				this.dataZoomStart = 90
				this.dataZoomEnd = 100
				this.get_KLine()
			}
			this.isShowKlink = !this.isShowKlink
			// 	let chart = this.$refs.chartRef.init(echarts);
			// 	this.lEchart.dispose();
			// }
			// // console.log(isShowKlink)
		},
		async getUserShare() {
			let res = await this.$api.getUserInfotake({

			});
			if (res.status.code == 0) {
				this.shareUser = res.result
				const jumpUrl =
					`${getApp().globalData.url}pagesA/project/personal/appDownload`;
				this.get_share(jumpUrl)
				console.log(this.shareUser)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		openShare(item) {
			let zNum = this.calculateEarnings(item);
			let gNum = this.calculateYield(item)
			console.log(item)
			this.isShare = true
			this.shareUser = {
				...this.shareUser,
				zNum,
				gNum,
				red: item.red,
				price: item.price,
				lever: item.lever,
				currentDateTime: this.getDate()
			}
			console.log(this.shareUser)
		},
		isPositiveNumber(num) {
			if (Number(num) >= 0) {
				return true;
			} else {
				return false;
			}
		},
		getDate() {
			const now = new Date();
			const year = now.getFullYear();
			const month = (`0${now.getMonth() + 1}`).slice(-2);
			const date = (`0${now.getDate()}`).slice(-2);
			// 设置格式化的当前日期与时间
			return `${year}年${month}月${date}日`;
		},
		async get_share(jumpUrl) {
			console.error(jumpUrl)
			let res = await this.$api.getShortLink({
				longLink: jumpUrl
			});
			if (res.status.code == 0) {
				this.qrcodeUrl = res.result.shortUrl;
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		fenx_weixin() {
			// 分享到微信
			uni.share({
				provider: "weixin",
				scene: "WXSceneSession",
				type: 0,
				href: this.qrcodeUrl,
				title: "Bigverse |" + 'BV开杠吧活动战报',
				summary: "Bigverse |" + 'BV开杠吧活动战报',
				success: function (res) {
					console.log("success:" + JSON.stringify(res));
				},
				fail: function (err) {
					console.log("fail:" + JSON.stringify(err));
				},
				complete() {
					uni.hideLoading()
				}
			});
		},
		// generate(){
		// 	Toast.loading({
		// 		message: '生成中...',
		// 		forbidClick: true,
		// 		duration: 11000
		// 	});
		// 	let el = document.getElementById("test-id");
		// 	let options = {
		// 		// width: el.offsetWidth* 4,
		// 		// height: el.offsetHeight* 4,
		// 		// scale:4
		// 		useCORS: true, // 是否尝试使用 CORS 从服务器加载图像
		// 		allowTaint: false, // 是否允许跨源图像污染画布
		// 	}
		// 	html2canvas(el, options).then((canvas) => {
		// 		let imgData = canvas.toDataURL("image/jpeg"); // 转base64
		// 		console.log(imgData)
		// 		console.log(platform)
		// 		if (platform) {
		// 			updateImg(imgData)
		// 		} else {
		// 			fileDownload(imgData);
		// 		}
		// 	});
		// },
		// fileDownload(downloadUrl){
		// 	Toast.clear();
		// 	let aLink = document.createElement("a");
		// 	aLink.style.display = "none";
		// 	// 以防base64过长导致下载失败，所以将base64转成blob格式
		// 	aLink.href = URL.createObjectURL(dataURIToBlob(downloadUrl));
		// 	aLink.download = "BV对战战报.png";
		// 	document.body.appendChild(aLink);
		// 	aLink.click();
		// 	document.body.removeChild(aLink);
		// },
		// // base64转blob方法
		// dataURIToBlob(dataURI){
		// 	let binStr = atob(dataURI.split(",")[1]),
		// 		len = binStr.length,
		// 		arr = new Uint8Array(len);

		// 	for (var i = 0; i < len; i++) {
		// 		arr[i] = binStr.charCodeAt(i);
		// 	}
		// 	return new Blob([arr]);
		// },
		// base64ToFile(urlData, fileName){
		// 	const arr = urlData.split(',')
		// 	const mime = arr[0].match(/:(.*?);/)[1]
		// 	const bytes = atob(arr[1])
		// 	let n = bytes.length
		// 	const ia = new Uint8Array(n)
		// 	while (n--) {
		// 		ia[n] = bytes.charCodeAt(n)
		// 	}
		// 	return new File([ia], fileName, {
		// 		type: mime
		// 	})
		// },
		// updateImg(url){
		// 	const formData = new FormData()
		// 	let fileOfBlob = new File([base64ToFile(url, 'file')], new Date() + '.jpg')
		// 	formData.append('image', fileOfBlob)
		// 	uploadImgToOss(formData).then((res) => {
		// 			let link =
		// 				`https://cdn-lingjing.nftcn.com.cn/${res.result.url.split('https://cdn-lingjing.nftcn.com.cn/')[1]}`
		// 			console.log(link)
		// 			Toast.clear();
		// 			share({
		// 				pic: "https://cdn-lingjing.nftcn.com.cn/image/20240326/2ec9fc265ec3377da35bdee84a081f3b_111x110.png",
		// 				title: "BV开杠吧活动",
		// 				subtitle: "BV开杠吧活动",
		// 				link
		// 			})
		// 		})
		// 		.catch((err) => {
		// 			Toast(res.status.msg);
		// 		});
		// },
		async firstVisit() {
			let res = await this.$api.dayFirstVisit({
				module: "BATTLE_QIFEI",
				from: 'ios'
			});
			if (res.status.code == 0) {

			} else {

				if (res.status.code === 1002) {
					this.Toast(res.status.msg);
					setTimeout(() => {
						sessionStorage.clear();
						goLogin();
					}, 2000)
				} else {
					this.Toast(res.status.msg);
				}
			}
		},
		guidanceCheck(index) {
			if (index == 8) {
				this.isGuidance = false
				uni.setStorageSync('isGuidanceStorage', 1)
			} else {
				this.guidanceShow = index + 1
			}
		},
		nav_agreement(title, index) {
			this.$Router.push({
				name: "generalAgreement",
				params: {
					title: 'BV开杠吧活动服务协议',
					link: "https://www.nftcn.com.cn/link/#/pages/index/takeOffAgreement"
				}
			})
		},
		//月-日
		timestampToDate(secondsTimestamp) {
			const timestamp = secondsTimestamp * 1000; // 将秒转换为毫秒
			const date = new Date(timestamp);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}-${day}`;
		},
		//时分
		timestampToDateHours(secondsTimestamp) {
			const date = new Date(secondsTimestamp * 1000); // 将秒转换为毫秒
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${hours}:${minutes}`;
		},
		onSelect(index) {
			this.importNum = this.actions[index].value
			this.sheetShow = false
		},
		async getPercent() {
			let res = await this.$api.getParam({
				isPreserve:1
			});
			if (res.status.code == 0) {
				this.percentCheck = res.result.notifyPercent
				this.isTest = res.result.isTest
				console.log(this.isTest)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		// 防抖函数
		debounce(func, wait) {
			// 清除之前的计时器，如果存在的话
			clearTimeout(this.debounceTimeout);
			// 设置新的计时器，等待指定的时间后执行func
			this.debounceTimeout = setTimeout(() => func.apply(this), wait);
		},
		Toast(title) {
			uni.showToast({
				title,
				icon: 'none',
				duration: 3000
			});
		},
		// 监听input获取焦点事件
		handleFocus() {
			this.isUserFocus = true
			this.isSuo = true
			console.log('Input gained focus');
		},
		// 监听input失去焦点事件
		handleBlur() {
			console.log('Input lost focus');
		},
		onInputChange(event) {
			event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,2})/g)[0]) || ""
			this.$nextTick(() => {
				this.importMarketPrice = event.detail.value
			})
		},
		//操作k线图
		async setlEchart() {
			// #ifdef APP
			this.lEchart = await this.$refs.chartRef.init(echarts);
			// #endif
			this.lEchart.setOption(this.globalOption)
			this.lEchart.on('showTip', (params) => {
				if (params.dataIndex == 0) {
					this.showTip = false
				} else {
					this.showTip = true
				}
			});
			this.lEchart.on('hideTip', (params) => {
				this.showTip = false
				// console.log('hideTip::', params)
			});
			// // 监听dataZoom的滑动和缩放事件
			// this.lEchart.on('dataZoom', (params) => {
			// 	if (params.batch && params.batch[0]) {
			// 		const {
			// 			start,
			// 			end
			// 		} = params.batch[0];
			// 		this.dataZoomStart = start
			// 		this.dataZoomEnd = end
			// 		// 这里可以执行更多基于start和end的操作 例如动态加载数据等
			// 	}
			// });
			// this.lEchart.on('mousedown', () => {
			// 	// 开始长按时记录时间
			// 	this.longPressTimer = setTimeout(() => {
			// 		this.isLongPressing = true;
			// 		this.handleLongPress();
			// 	}, 1000); // 这里的500ms是长按的判定时间，可以根据需要调整
			// });
			// this.lEchart.on('mouseup', () => {
			// 	// 松开k线图
			// 	this.endLongPress();
			// });
			// this.lEchart.on('mouseout', () => {
			// 	// 松开k线图
			// 	this.endLongPress();
			// });
		},
		//初始化k线图
		async init() {
			// chart 图表实例不能存在data里
			this.lEchart = await this.$refs.chartRef.init(echarts);
		},
		async handleLongPress() {
			console.log('长按事件触发');
			this.globalOption.dataZoom = [{
				moveOnMouseMove: false,
				type: 'inside',
				zoomLock: false,
				zoomSmooth: true,
				start: this.dataZoomStart,
				end: this.dataZoomEnd
			}]
			this.setlEchart()
			// 在这里执行你的长按逻辑
		},
		async endLongPress() {
			clearTimeout(this.longPressTimer);
			if (this.isLongPressing) {
				console.log('长按结束啊111111111111');
				// 在这里执行长按结束后的逻辑
				this.globalOption.dataZoom = [{
					moveOnMouseMove: true,
					type: 'inside',
					zoomLock: false,
					zoomSmooth: true,
					start: this.dataZoomStart,
					end: this.dataZoomEnd
				}]
				this.setlEchart()
				this.isLongPressing = false;
			} else {
				this.isLongPressing = false;
			}
		},
		toggleFullScreen() {
			this.$Router.push({
				name: "chart"
			})
			//     if (!this.isFullScreen) {
			// this.isFullScreen = true;
			//       // 调整ECharts的尺寸
			// this.init()
			//     } else {
			//       uni.hideFullScreen();
			//       this.isFullScreen = false;
			//       this.$refs.chartRef.classList.remove('fullscreen');
			//       this.$refs.chartRef.style.transform = '';
			//       this.$refs.chartRef.style.width = '';
			//       this.$refs.chartRef.style.height = '';
			//       // 调整ECharts的尺寸
			//       this.chartInstance.resize();
			//     }
		},
	},
	computed: {
		fixrate() {
			if (!this.aimPrice) {
				return 0.00; // 或者你可以返回一个默认值，比如 "0.00"
			}

			let a = (this.latestPrice - this.aimPrice);
			let b = a / this.aimPrice;

			b = Math.abs(b);

			let d = Number((b * 100)).toFixed(2)
			// let c = Math.trunc(b * 100);
			// let d = c.toFixed(2);
			return d;
		},
	}
}
</script>
<style lang="scss" scoped>
body {
	background: #111111;
}
.mainindex {
  height: 100%;
  width: 100%;
  background: #111;
  padding: 88rpx 52rpx 0 47rpx;

  .comp {
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 36rpx;
    color: #49FFFF;
    line-height: 7rpx;

    &-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 67rpx;
      align-items: center;

      .img {
        image {
          width: 38rpx
        }

        width: 25%;

      }

      .name {
        width: 25%;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26rpx;
        color: #FFFFFF;
        display: flex;
        justify-content: flex-start;
      }

      .price {
        width: 25%;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 26rpx;
        display: flex;
        justify-content: flex-end;
      }

      .change {
        display: flex;
        justify-content: flex-end;
        width: 25%;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 18rpx;
      }
    }

    &-item:last-child {
      margin-bottom: 0;
    }
  }
}
::v-deep .u-tab-bar:last-child {
	transform: translate(147px, -100%);
}

// ::v-deep .u-tab-bar:last-child{
// 	transform: translate(147px, -100%) !important;
// }
::v-deep .u-action-sheet-item {
	background: #2b2b2b;
	border: none;
	color: #fff;
}

.body {
	background-color: #111111;
	background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
	background-size: 100%;
	background-repeat: no-repeat;
	min-height: 1624rpx;
	position: relative;
	padding-top: 40rpx;
	padding-bottom: 140rpx;

	.back {
		position: absolute;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 64rpx;
		/* #endif */
		left: 30rpx;

		image {
			width: 50rpx;
		}
	}

	.leftButton {
		position: absolute;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 70rpx;
		/* #endif */
		left: 100rpx;
		width: 140rpx;
		height: 48rpx;
		background: linear-gradient(138deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 14rpx;
		opacity: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 22rpx;
		font-weight: 600;
	}

	.rightButton {
		position: absolute;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 64rpx;
		/* #endif */
		right: 30rpx;
		width: 160rpx;
		height: 54rpx;
		background: linear-gradient(138deg, #53E571 0%, #82E44D 100%);
		border-radius: 40rpx;
		opacity: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		font-weight: 600;

		&.active {
			background: linear-gradient(138deg, #FF5270 0%, #FB6F46 100%);
		}

		image {
			width: 22rpx;
			margin-right: 6rpx;
		}
	}

	.IP_cover {
		.view {
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;

			.ip {
				width: 567rpx;
				z-index: 0;
			}

			.help {
				position: absolute;
				top: 180rpx;
				right: 120rpx;
				font-size: 20rpx;
				text-align: center;
				color: #9c9c9c;
				border-radius: 25rpx;

				image {
					width: 40rpx;
					margin-right: 10rpx;
				}

			}
		}

		.num_view {
			margin-top: 30rpx;
			color: #fff;
			text-align: center;

			.rmb {
				font-size: 65rpx;
				font-weight: 600;
			}

			.num {
				font-size: 87rpx;
				margin-right: 40rpx;
				font-weight: 600;
			}

			image {
				width: 50rpx;

			}
		}

		.link_view {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: -100rpx;
			position: relative;

			.linkBut {
				width: 174rpx;
				height: 48rpx;
				border: 1px solid #fff;
				font-size: 22rpx;
				border-radius: 24rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #fff;

				text {
					padding: 0 6rpx;
				}

				image {
					width: 20rpx;
				}
			}
		}

		.num_msg {
			margin-top: 20rpx;
			text-align: center;
			font-size: 24rpx;
			color: rgb(255, 255, 255, 0.5);

		}
	}

	.chart {
		width: 100%;
		height: 500rpx;
	}

	.tabs {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.ul {
			width: 400rpx;
			height: 80rpx;
			background-color: #2b2b2b;
			border-radius: 50rpx;
			padding: 10rpx;
			display: flex;
			justify-content: flex-start;

			.li {
				width: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 34rpx;

				&.active {
					background-color: rgb(255, 255, 255, 0.2);
					border-radius: 40rpx;
					font-weight: 600;
					width: 65%;
					border: 1px solid rgb(255, 255, 255, 0.8);
				}
			}
		}
	}

	.quick_view {
		background-color: #2b2b2b;
		border-radius: 36rpx;
		width: 640rpx;
		// min-height: 617rpx;
		margin: 0 auto;
		margin-top: 20rpx;
		padding: 40rpx 59rpx;

		>.li {
			margin-bottom: 26rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			height: 50rpx;

			&.noMarginBottom {
				margin-bottom: 0;
				height: 40rpx;
			}

			.label {
				width: 170rpx;
				color: #fff;
				font-size: 28rpx;
			}

			.input_view {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				width: 380rpx;
				background-color: rgb(255, 255, 255, 0.2);
				border-radius: 40rpx;
				border: 1px solid rgb(255, 255, 255, 0.8);
				height: 60rpx;
				padding-left: 30rpx;
				color: #fff;

				input {
					width: 240rpx;
					background-color: transparent;
					border: none;
					height: 80rpx;
					color: #fff;
					padding: 0rpx;
				}

				text {
					text-align: center;
					color: rgb(255, 255, 255, 0.5);
					font-size: 26rpx;
				}
			}

			.price_view {
				color: #fff;
				padding-left: 20rpx;
			}

			.ul_view {
				display: flex;
				justify-content: flex-start;
				align-items: center;

				>.li {
					width: 80rpx;
					background-color: rgb(255, 255, 255, 0.2);
					border-radius: 40rpx;
					border: 1px solid rgb(255, 255, 255, 0.8);
					margin-right: 20rpx;
					color: #fff;
					font-size: 24rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					height: 40rpx;

					&.active {
						background-color: #fff;
						color: #111111;
						font-weight: 600;
					}
				}

				>.li:last-child {
					margin-right: 0
				}
			}
		}

		.qp_msg {
			text-align: center;
			color: rgb(255, 255, 255, 0.5);
			font-size: 24rpx;
			margin-top: 30rpx;
		}

		.bbutton_view {
			margin-top: 40rpx;

			.button {
				width: 412rpx;
				height: 70rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 30rpx;
				font-weight: 600;
				color: #141816;
				background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
				margin: 0 auto;
				border-radius: 50rpx;
			}
		}
	}

	.tabs_div {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0rpx 55rpx;
		margin-top: 10rpx;

		.left {
			width: 460rpx;
		}

		.right {
			image {
				width: 36rpx;
				margin-top: 14rpx;
			}
		}
	}

	.details {
		padding: 20rpx 55rpx 40rpx 55rpx;

		.weituo,
		.cangwei {
			min-height: 800rpx;

			.li {
				padding: 30rpx;
				background-color: #2B2B2B;
				border-radius: 36rpx;
				margin-bottom: 40rpx;
				position: relative;
				opacity: 1;
				transition: opacity 1s ease-out;

				&.active {
					opacity: 0;
				}

				.right_fenx {
					position: absolute;
					right: 0rpx;
					top: 0rpx;

					image {
						width: 80rpx;
					}
				}

				.live_data {
					background-color: #141816;
					border-radius: 27rpx;
					height: 54rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					margin-top: 20rpx;
					padding: 0rpx 18rpx;
					font-size: 24rpx;
					width: 500rpx;
					color: #6CFF8A;

					text {
						color: #fff;
					}

					.sy {
						margin-right: 50rpx;
						font-weight: 600;

						&.red {
							color: #EC4068;
						}
					}

					.syl {
						font-weight: 600;

						&.red {
							color: #EC4068;
						}
					}
				}

				.head {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						display: flex;
						justify-content: space-between;
						align-items: center;

						image {
							width: 50rpx;
							margin-right: 30rpx;
						}

						.title {
							color: #fff;
							font-size: 28rpx;
							font-weight: 600;
							margin-right: 20rpx;
						}

						.sub {
							font-size: 24rpx;
							color: rgb(255, 255, 255, 0.3);
						}
					}

					.cd {
						width: 80rpx;
						height: 44rpx;
						font-size: 24rpx;
						color: #fff;
						background-color: rgb(255, 255, 255, 0.3);
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 36rpx;
					}
				}

				.data_view {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 31rpx;

					.li_view {
						text-align: center;
						width: 33.33%;

						&.button {
							width: 144rpx;
							height: 48rpx;
							border-radius: 36rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 22rpx;
							background-color: #fff;
						}

						.label {
							color: rgb(255, 255, 255, 0.5);
							font-size: 24rpx;
						}

						.num {
							color: #fff;
							font-size: 28rpx;
							margin-top: 21rpx;
							font-weight: 600;
						}
					}
				}
			}
		}
	}
}

::v-deep .van-action-sheet__content {
	background-color: #2B2B2B;

	.van-action-sheet__item {
		background-color: #2B2B2B;
		color: #FFF;
	}
}

::v-deep .popup_body {
	height: 312rpx;
}

.bodyPop_msg {
	background-color: #2B2B2B;
	z-index: 2030 !important;
	width: 640rpx;
	border-radius: 36rpx;
	padding: 50rpx;

	>.msg_view {
		background-size: 100% 100%;
		font-size: 28rpx;
		text-align: center;

		view {
			color: #fff;
			font-size: 32rpx;
			margin-bottom: 20rpx;
		}

		text {
			font-size: 28rpx;
			color: #fff;
			text-decoration: none;
		}
	}

	.button_msg {
		margin-top: 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-weight: 600;

		.cancel {
			width: 250rpx;
			height: 70rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50rpx;
			border: 1px solid #FFFFFF;
			text-align: center;
			line-height: 70rpx;
			font-weight: 600;
			color: #fff;
		}

		.submit {
			width: 250rpx;
			height: 70rpx;
			background: linear-gradient(142deg, #EF91FB 0%, #40F8EC 100%);
			border-radius: 50px 50px 50px 50px;
			font-size: 28rpx;
			text-align: center;
			line-height: 70rpx;
			font-weight: 600;
			border: none;
		}
	}

}

::v-deep .u-drawer-bottom {
	background: transparent;
}

::v-deep .popup_body_bottom {
	z-index: 2030 !important;

	.bodyPop {
		padding: 0 55rpx;
		background-color: #2B2B2B;
		z-index: 2030 !important;
		border-radius: 36rpx 36rpx 0 0;
		padding-bottom: 30rpx;

		.head {
			padding: 55rpx 0;
			text-align: center;
			border-bottom: 1px solid #616161;
			color: #fff;

			.title {
				color: #fff;
				font-weight: 600;
				font-size: 34rpx;
			}

			.sub_title {
				font-size: 28rpx;
				margin-top: 30rpx;

				text {
					color: #63EAEE;
					margin-right: 10rpx;
				}
			}
		}

		>.body_view {
			.padding40 {
				padding: 40rpx 0rpx;
			}

			.item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 30rpx;

				.name {
					color: #fff;
					font-weight: 600;
					margin-bottom: 20rpx;
				}

				.valUue {
					font-weight: 600;
					color: #63EAEE;
				}
			}

			.msg {
				display: flex;
				align-items: center;

				image {
					width: 30rpx;
					margin-right: 15rpx;
				}

				color: #fff;
				font-size: 24rpx;
			}

			.but {
				width: 640rpx;
				height: 100rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 50rpx;
				margin: 40rpx auto 31rpx auto;
				font-weight: 600;
			}
		}
	}

	.right_colse {
		position: absolute;
		right: 0rpx;
		top: 0rpx;

		image {
			width: 80rpx;
		}
	}


	.button {
		margin-top: 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-weight: 600;

	}
}

.van-cell {
	background-color: #111111;
	padding: 0;
	border-bottom: 0;
}

.van-cell::after {
	border-bottom: none !important;
}

.share_body {
	width: 100%;
	background-color: #111111;
	background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
	background-size: 100%;
	background-repeat: no-repeat;
	padding: 160rpx 55rpx 55rpx;
	height: 100vh;

	.cart_div {
		background-color: #2B2B2B;
		border-radius: 36rpx;
		width: 640rpx;
		height: 1000rpx;
		padding: 40rpx 36rpx;
		position: relative;

		.title_image {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
			width: 100%;
			height: 111rpx;
			background-size: 100% 100%;
			line-height: 111rpx;
			font-size: 36rpx;
			color: #fff;
			text-align: center;
			letter-spacing: 2rpx;
		}

		.toux_image_div {
			width: 259rpx;
			margin: 20rpx auto;

			.toux_border {
				width: 100%;
				// height:116rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
				background-size: 100%;
				background-repeat: no-repeat;
				display: flex;
				justify-content: center;
				align-items: center;

				padding-top: 40rpx;

				.image_div {
					width: 120rpx;
					height: 120rpx;
					background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
					border-radius: 40rpx;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						border-radius: 40rpx;
						width: 112rpx;
						height: 112rpx;
						object-fit: cover;
					}
				}
			}
		}

		.toux_name {
			text-align: center;
			font-size: 28rpx;
			color: #fff;
			font-weight: 600;
		}

		.yield {
			font-size: 90rpx;
			color: #6CFF8A;
			font-weight: 600;
			margin-top: 40rpx;

			text {
				font-size: 55rpx;
			}

			&.red {
				color: #FF5270;
			}
		}

		.shouyi {
			font-size: 48rpx;
			color: #6CFF8A;

			&.red {
				color: #FF5270;
			}
		}

		.info_div {
			color: #fff;
			margin-top: 30rpx;

			p {
				font-size: 28rpx;
				line-height: 38rpx;
			}
		}

		.yqm {
			background-color: rgb(255, 255, 255, 0.2);
			border: 1px solid #fff;
			width: 280rpx;
			height: 54rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
			font-size: 28rpx;
			margin-top: 40rpx;
			border-radius: 36rpx;
			margin-bottom: 30rpx;
		}

		.msg_text {
			font-size: 28rpx;
			color: #fff;
			font-weight: 600;
		}

		.icon_bg {
			width: 292rpx;
			position: absolute;
			top: 460rpx;
			right: -30rpx;

			image {
				width: 292rpx;
			}
		}

		.qr_code {
			position: absolute;
			top: 764rpx;
			right: 36rpx;

			.right {
				display: flex;
				justify-content: flex-end;
				align-items: center;

				.qr_div {
					width: 140rpx;
					height: 140rpx;
					border-radius: 28rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #fff;
					padding: 10rpx;
				}
			}

			.time {
				color: rgb(255, 255, 255, 0.3);
				margin-top: 30rpx;
				font-size: 22rpx;
			}
		}
	}

	.share_to_div {
		margin-top: 140rpx;
		display: flex;
		justify-content: center;

		>.li {
			width: 25%;
			text-align: center;
			color: #fff;
			font-size: 28rpx;
			margin-top: 10rpx;

			.icon_image {
				display: flex;
				justify-content: center;

				image {
					width: 90rpx;
					margin-bottom: 20rpx;
				}
			}
		}
	}

	.colse_div {
		margin-top: 46rpx;
		display: flex;
		justify-content: center;

		image {
			width: 80rpx;
		}
	}
}

.tabView {
	width: 640rpx;
	margin: 140rpx auto 0rpx auto;
	font-size: 24rpx;
	background-color: #2B2B2B;
	border-top-left-radius: 36rpx;
	border-top-right-radius: 36rpx;
	position: relative;
	border-bottom: 1px solid #515151;
	display: flex;
	justify-content: center;
	align-items: center;

	.leftTabView {}

	.rightClose {
		position: absolute;
		right: 0;
		top: 0;
		z-index: 1001;

		image {
			width: 60rpx;
			height: 60rpx;
		}
	}
}

.klink_div {
	background-color: #2B2B2B;
	width: 640rpx;
	// border-radius: 36rpx;
	height: 400rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	position: relative;
	border-bottom-left-radius: 36rpx;
	border-bottom-right-radius: 36rpx;

	.fullscreen_icom {
		position: absolute;
		left: 20rpx;
		bottom: 20rpx;
		z-index: 1001;

		image {
			width: 60rpx;
			height: 60rpx;
		}
	}

	.customTooltips {
		background-color: rgba(20, 24, 22, 0.8);
		position: absolute;
		top: 0;
		left: 0;
		z-index: 10000;
		color: #fff;
		font-size: 24rpx;
		border-radius: 30rpx;
		padding: 30rpx;

		// border:1px solid #fff;
		.time {
			margin-bottom: 20rpx;
		}

		.li_tooltip {
			display: flex;
			justify-content: flex-start;
			margin-bottom: 20rpx;

			>.view {
				width: 6rpx;
				height: 20rpx;
				background-color: #fff;
				margin-right: 10rpx;

				&.red {
					background-color: rgb(235, 84, 84);
				}

				&.lvse {
					background-color: rgb(71, 178, 98);
				}
			}
		}

		.li_tooltip:last-child {
			margin-bottom: 0rpx;
		}
	}

	.klink {
		width: 100%;
		// height: 500rpx;
	}
}

::v-deep .guidance_body {
	width: 100%;
	min-height: 1424rpx;
	// overflow: hidden;
	background-color: transparent;

	.image {
		img {
			width: 100%;
		}
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateY(-10rpx);
}

.bodyPop_xieyi {
	background-color: #2B2B2B;
	z-index: 2030 !important;
	padding: 50rpx;
	width: 640rpx;
	border-radius: 36rpx;

	.msg_div {
		background-size: 100% 100%;
		font-size: 28rpx;
		text-align: center;

		view {
			color: #fff;
			font-size: 28rpx;
			margin-bottom: 20rpx;
		}

		text {
			color: #63EAEE;
			font-size: 28rpx;
			text-decoration: underline;
		}
	}

	.button {
		margin-top: 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-weight: 600;

		.cancel {
			width: 200rpx;
			height: 70rpx;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50rpx;
			border: 1px solid #FFFFFF;
			text-align: center;
			line-height: 70rpx;
			font-weight: 600;
			color: #fff;
		}

		.submit {
			width: 300rpx;
			height: 70rpx;
			background: linear-gradient(142deg, #EF91FB 0%, #40F8EC 100%);
			border-radius: 50px 50px 50px 50px;
			font-size: 28rpx;
			text-align: center;
			line-height: 70rpx;
			font-weight: 600;
			border: none;
		}
	}
}

.fullscreen {
	position: fixed !important;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(90deg);
	z-index: 9999;
	overflow: hidden;
	width: 100vh;
	height: 100vw;
	background-color: #111111;
}
</style>