option = {
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [820, 111, 901, 934, 1290, 333, 1320],
      type: 'line',
       smooth: true,
       symbol: 'none', // 隐藏折线上的点
        lineStyle: {
              color: 'rgb(255, 104, 104)' // 折线颜色
            },
       areaStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  { offset: 0, color: 'rgba(255, 104, 104, 0.3)' },
                  { offset: 1, color: 'rgba(255, 104, 104, 0.05)' }
                ]
              )
            },
    }
  ]
};