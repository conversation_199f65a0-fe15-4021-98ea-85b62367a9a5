<template>
	<div class="body">
		<view class="fullscreen" :class="{'isHenpin':!isHenpin}">
			<view class="tabView">
				<u-tabs name="cate_name" bg-color="" :list="tabListKlink" inactive-color="rgb(255,255,255,0.3)"
					active-color="#fff" font-size="24" height="60" :current="klinkStatus" @change="changeKlink"
					:bar-style="barStyleLink" :active-item-style="itemStyleLink"></u-tabs>
			</view>
			<view class="klink_div">
				<!-- <lime-echart></lime-echart> -->
				<l-echart class="klink" ref="chartRef" :isLandscapeScreen="!0" @finished="init"></l-echart>
				<view class="customTooltips"
					:style="{'left': positionTip[0] + 'rpx','top': positionTip[1] + 'rpx','border':'1px solid '+paramsTip.color}"
					v-show="showTip">
					<view class="time">{{paramsTip.date}}</view>
					<view class="li_tooltip">
						<view class="view red"></view>
						<text>起始均价:{{paramsTip.open}}</text>
					</view>
					<view class="li_tooltip">
						<view class="view red"></view>
						<text>结尾均价:{{paramsTip.close}}</text>
					</view>
					<view class="li_tooltip">
						<view class="view lvse"></view>
						<text>最小均价:{{paramsTip.lowest}}</text>
					</view>
					<view class="li_tooltip">
						<view class="view lvse"></view>
						<text>最大均价:{{paramsTip.highest}}</text>
					</view>
				</view>
			</view>
			<view class="exit" @click="back()">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20240702/58c981ff556254dc335e20b76eef5662_70x70.png"
					mode="widthFix"></image>
			</view>
		</view>
		<!-- <u-popup v-model="isHenpin" mode="center">
			<view class="bodyPop_msg">
				<view class="msg_view">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240702/120995e3fd16a0476af480f1e969aa40_68x64.png"
							mode="widthFix"></image>
					</view>
					<text>请先将手机横屏</text>
				</view>
			</view>
		</u-popup> -->
	</div>
</template>

<script>
	import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
	export default {
		data() {
			return {
				globalOption: {
					grid: {
						// 调整右侧的距离，增大该值可以增加右侧的安全距离
						top: "5%",
						left: "4%",
					},
					tooltip: {
						trigger: 'item', // 触发类型为'item'，表示鼠标 hover 到具体的 K 线上时显示 tooltip
						axisPointer: {
							animation: false,
							type: 'cross',
							lineStyle: {
								color: '#376df4',
								width: 2,
								opacity: 1
							}
						},
						position: (point, params, dom, rect, size) => {
							console.error(size.viewSize)
							// 假设自定义的tooltips尺寸
							const box = [170, 107]
							// 偏移
							const offsetX = point[0] < size.viewSize[0] / 2 ? 10 : -box[0];
							const offsetY = point[1] < size.viewSize[1] / 2 ? 10 : -box[1];
							const x = point[0] + offsetX;
							const y = point[1] + offsetY;

							this.positionTip = [x * 2, y * 2]
						},
						formatter: (params, ticket, callback) => {
							let min = 1;
							if (this.klinkStatus == 0) {
								min = 1
							} else if (this.klinkStatus == 1) {
								min = 3
							} else if (this.klinkStatus == 2) {
								min = 5
							} else if (this.klinkStatus == 3) {
								min = 60
							} else if (this.klinkStatus == 4) {
								min = 240
							} else if (this.klinkStatus == 5) {
								min = 1440
							}
							const now = new Date(params.data[5] * 1000);
							const oldTime = new Date((new Date(now).getTime()) + (min * 60000))
							const year = now.getFullYear();
							const month = ("0" + (now.getMonth() + 1)).slice(-2); // 月份是从0开始计数的，需要加1
							const day = ("0" + now.getDate()).slice(-2);
							const hours = ("0" + now.getHours()).slice(-2);
							const minutes = ("0" + now.getMinutes()).slice(-2);
							const hoursOld = ("0" + oldTime.getHours()).slice(-2);
							const minutesOld = ("0" + oldTime.getMinutes()).slice(-2);

							// // params 包含了当前被 hover 的数据项的信息
							var date = ""
							if (this.klinkStatus < 4) {
								date = `时间:${hours}:${minutes}-${hoursOld}:${minutesOld}`;
							} else if (this.klinkStatus < 5) {
								date = `时间:${month}-${day} ${hours}:${minutes}-${hoursOld}:${minutesOld}`;
							} else {
								date = `时间:${year}-${month}-${day}`;
							}

							var open = params.data[1];
							var close = params.data[2];
							var highest = params.data[4];
							var lowest = params.data[3];
							var color = params.color;
							this.paramsTip = {
								date,
								open,
								close,
								highest,
								lowest,
								color
							}
							// 自定义 tooltip 显示的内容 
							// return `${date}
							// 		  起始均价:￥${open}
							// 		  结尾均价:￥${close}
							// 		  最小均价:￥${lowest}
							// 		  最大均价:￥${highest}`
							// return date + '<br />' +
							// 	redDiv + '起始均价: ' + open + '<br />' +
							// 	redDiv + '结尾均价: ' + close + '<br />' +
							// 	lvseDiv + '最小均价: ' + lowest + '<br />' +
							// 	lvseDiv + '最大均价: ' + highest;
						},
						backgroundColor: 'rgba(20, 24, 22, 0.8)', // 自定义背景色
						textStyle: {
							color: '#fff', // 自定义 tooltip 文字颜色为深灰色
							fontSize: 20,
						}
					},
					xAxis: {
						type: 'category',
						boundaryGap: true, // 防止数据点与轴的边界重合
						data: [],
					},
					yAxis: {
						type: 'value',
						boundaryGap: true, // 防止数据点与轴的边界重合
						position: 'right', // 将y轴位置设置为右侧
						// min: kMin, // 根据实际情况设置最小值
						// max: kMax, // 根据实际情况设置最大值
					},
					series: [{
						type: 'candlestick',
						data: [],
					}],
					dataZoom: [{
						type: 'inside',
						zoomLock: false,
						zoomSmooth: true,
						start: 80,
						end: 100
					}],
				},
				positionTip: [],
				paramsTip: {},
				showTip: false,
				tabListKlink: [{
					name: "1分",
					value: 0
				}, {
					name: "3分",
					value: 1
				}, {
					name: "5分",
					value: 2
				}, {
					name: "1小时",
					value: 3
				}, {
					name: "4小时",
					value: 4
				}, {
					name: "日",
					value: 5
				}],
				barStyleLink: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
					'width': '40rpx',
					'height': '6rpx',
					'border-radius': '0rpx',
					'bottom': '-6rpx',
					'z-index': '1'
				},
				itemStyleLink: {
					'font-size': '24rpx',
					'z-index': '2',
				},
				isLinkLoadding: false,
				klinkStatus: 0,
				interval: 1, //k线图 时间
				pageSize: 180,
				kLineData: [],
				kLineTimeData: [],
				isHenpin: false
			}
		},
		onLoad() {
			this.getInfo()
			setTimeout(() => {
				this.get_KLine()
			}, 500)
			// #ifdef H5
			this.setNoScaleMeta();
			// #endif

		},
		onShow() {
			uni.onWindowResize((res) => {
				const {
					windowHeight,
					windowWidth
				} = res.size;
				console.log(res)
				// 判断横竖屏，这里以宽度大于高度作为横屏的标准
				if (windowWidth > windowHeight) {
					console.log('横屏');
					this.isHenpin = true
					this.$nextTick(() => {
						if (this.$refs.chartRef) {
							this.$refs.chartRef.resize()
							this.init()
						}
					});
					// 在这里执行横屏时的操作
				} else {
					console.log('竖屏');
					this.isHenpin = false
					// this.$refs.chartRef.resize()
					// this.init()
					// 在这里执行竖屏时的操作
				}
			});

		},
		onHide() {
			// 页面隐藏时，移除监听器以避免内存泄漏
			uni.offWindowResize();
		},
		onReachBottom() {

		},
		methods: {
			async get_KLine() {
				let marketPrice = this.marketPrice
				let res = await this.$api.getKLine({
					interval: this.interval,
					pageSize: this.pageSize
				});
				if (res.status.code == 0) {
					let arr = [],
						time = [];
					res.result.list.forEach((item) => {
						let arr = [item.openPrice, item.closePrice, item.lowPrice, item.highPrice, item
							.startTime * 60
						]
						let arr2 = []
						if (this.klinkStatus == 0) {
							arr2 = [this.timestampToDateHours(item.startTime * 60)]
						} else if (this.klinkStatus == 1) {
							arr2 = [this.timestampToDateHours(item.startTime * 60)]
						} else if (this.klinkStatus == 2) {
							arr2 = [this.timestampToDateHours(item.startTime * 60)]
						} else if (this.klinkStatus == 3) {
							arr2 = [this.timestampToDateHours(item.startTime * 60)]
						} else if (this.klinkStatus == 4) {
							arr2 = [this.timestampToDateHours(item.startTime * 60)]
						} else {
							arr2 = [this.timestampToDate(item.startTime * 60)]
						}
						this.kLineData.push(arr)
						this.kLineTimeData.push(arr2)
					})
					// kMin = res.list[0].minPrice
					// kMax = res.list[0].maxPrice
					// // 设置图表的配置项
					this.globalOption.yAxis = {
						type: 'value',
						position: 'right',
						axisLabel: {
							align: 'center',
							fontSize: 10
						},
						scale: true
						// min: kMin, // 根据实际情况设置最小值
						// max: kMax, // 根据实际情况设置最大值
					}
					this.globalOption.xAxis = {
						data: this.kLineTimeData,
					}
					this.globalOption.series = [{
							type: 'candlestick',
							data: this.kLineData,
							markLine: {
								label: {
									show: true,
									fontSize: 10, // 字体大小
									color: '#FFF',
									formatter() {
										return `${marketPrice}市价`
									},
									align: 'left',
								},
								data: [{
									yAxis: `${marketPrice}`
								}],
								lineStyle: {
									width: 1,
									color: '#FF5270',
									fontSize: '10px'
								}
							},
						}, ],
						console.log("数据=============")
					console.log(this.globalOption)
					this.$refs.chartRef.resize()
					this.init()
					this.isLinkLoadding = false
				} else {
					if (res.status.code === 1002) {
						setTimeout(() => {
							this.isLoadding = false
							this.$Router.push({
								name: "mainLogin",
								// #ifdef H5
								params: {
									url: window.location.hash,
								},
								// #endif
							})
						}, 1500);
					} else {
						this.Toast(res.status.msg);
					}
				}
			},
			changeKlink(e) {
				this.kLineData = []
				this.kLineTimeData = []
				this.klinkStatus = e
				this.isLinkLoadding = true
				this.showTip = false
				if (e == 0) {
					//1分钟
					this.interval = 1
					this.pageSize = 180
					this.get_KLine()
				} else if (e == 1) {
					//3小时
					this.interval = 3
					this.pageSize = 180
					this.get_KLine()
				} else if (e == 2) {
					//5分钟
					this.interval = 5
					this.pageSize = 288
					this.get_KLine()
				} else if (e == 3) {
					//1小时
					this.interval = 60
					this.pageSize = 72
					this.get_KLine()
				} else if (e == 4) {
					//4小时
					this.interval = 60 * 4
					this.pageSize = 288
					this.get_KLine()
				} else if (e == 5) {
					//24小时
					this.interval = 60 * 24
					this.pageSize = 365
					this.get_KLine()
				}
			},
			async init() {
				// chart 图表实例不能存在data里
				let chart = await this.$refs.chartRef.init(echarts);
				console.log(this.globalOption)
				chart.setOption(this.globalOption,true)
				chart.on('showTip', (params) => {
					console.log(params)
					if (params.dataIndex == 0) {
						this.showTip = false
					} else {
						this.showTip = true
					}
				});
				chart.on('hideTip', (params) => {
					// this.showTip = false
					// console.log('hideTip::', params)
				});
				// // 监听dataZoom的滑动和缩放事件
				// chart.on('dataZoom', (params) => {
				// 	if (params.batch && params.batch[0]) {
				// 		const {
				// 			start,
				// 			end
				// 		} = params.batch[0];
				// 		this.dataZoomStart = start
				// 		this.dataZoomEnd = end
				// 		// 这里可以执行更多基于start和end的操作，例如动态加载数据等
				// 	}
				// });
				// chart.on('mousedown', () => {
				// 	// 开始长按时记录时间
				// 	this.longPressTimer = setTimeout(() => {
				// 		this.isLongPressing = true;
				// 		this.handleLongPress();
				// 	}, 1000); // 这里的500ms是长按的判定时间，可以根据需要调整
				// });
				// chart.on('mouseup', () => {
				// 	// 松开k线图
				// 	this.endLongPress();
				// });
				// chart.on('mouseout', () => {
				// 	// 松开k线图
				// 	this.endLongPress();
				// });
				// chart.on('mouseover', function(params) {
				// 	var pointInPixel = [
				// 		params.event.offsetY, // 旋转后，原来的X轴坐标变成了Y轴
				// 		chart.getHeight() - params.event.offsetX // Y轴坐标需要转换
				// 	];
				// 	console.log(111)

				// });

				// ['mouseout'].forEach(eventName => {
				// 	chart.on(eventName, () => {
				// 		clearTimeout(longPressTimer);
				// 	});
				// });
			},
			async handleLongPress() {
				console.log('长按事件触发');
				this.globalOption.dataZoom = [{
					moveOnMouseMove: false,
					type: 'inside',
					zoomLock: false,
					zoomSmooth: true,
					start: this.dataZoomStart,
					end: this.dataZoomEnd
				}]
				this.init()
				// 在这里执行你的长按逻辑
			},
			async endLongPress() {
				clearTimeout(this.longPressTimer);
				if (this.isLongPressing) {
					console.log('长按结束啊111111111111');
					// 在这里执行长按结束后的逻辑
					this.globalOption.dataZoom = [{
						moveOnMouseMove: true,
						type: 'inside',
						zoomLock: false,
						zoomSmooth: true,
						start: this.dataZoomStart,
						end: this.dataZoomEnd
					}]
					this.init()
					this.isLongPressing = false;
				} else {
					this.isLongPressing = false;
				}
			},
			//月-日
			timestampToDate(secondsTimestamp) {
				const timestamp = secondsTimestamp * 1000; // 将秒转换为毫秒
				const date = new Date(timestamp);
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${month}-${day}`;
			},
			//时分
			timestampToDateHours(secondsTimestamp) {
				const date = new Date(secondsTimestamp * 1000); // 将秒转换为毫秒
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				return `${hours}:${minutes}`;
			},
			back() {
				this.$Router.back()
			},
			async getInfo() {
				let res = await this.$api.getMarketPrice({

				});
				if (res.status.code == 0) {
					this.marketPrice = res.result.marketPrice
					setTimeout(() => {
						this.getInfo()
					}, 3000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			setNoScaleMeta() {
				let meta = document.createElement('meta');
				meta.name = 'viewport';
				meta.content =
					'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no';
				if (document.head.querySelector('meta[name="viewport"]')) {
					document.head.querySelector('meta[name="viewport"]').setAttribute('content',
						'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'
					);
				} else {
					document.head.appendChild(meta);
				}
			},
			handleTouchMove(e) {
				console.log(e)
			},
		},
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #2B2B2B;
	}

	.body {
		-webkit-text-size-adjust: 100% !important;

		.fullscreen {
			background-color: #2B2B2B;

			&.isHenpin {
				position: fixed !important;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%) rotate(90deg);
				overflow: hidden;
				width: 100vh;
				height: 100vw;
				background-color: #111111;
				pointer-events: auto;
			}

			.tabView {
				width: 100%;
				margin: 0rpx auto 0rpx auto;
				font-size: 24rpx;
				background-color: #2B2B2B;
				position: relative;
				padding-right: 100rpx;
				border-bottom: 1px solid #515151;
				padding: 10rpx 50rpx;
			}

			.exit {
				position: fixed;
				left: 30rpx;
				bottom: 30rpx;
				z-index: 1001;

				image {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.klink_div {
				background-color: #2B2B2B;
				width: 100%;
				// border-radius: 36rpx;
				height: 90%;
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 0 auto;
				position: relative;

				.customTooltips {
					background-color: rgba(20, 24, 22, 0.8);
					position: absolute;
					top: 0;
					left: 0;
					z-index: 100000 !important;
					color: #fff;
					font-size: 24rpx;
					border-radius: 30rpx;
					padding: 30rpx;

					// border:1px solid #fff;
					.time {
						margin-bottom: 20rpx;
					}

					.li_tooltip {
						display: flex;
						justify-content: flex-start;
						margin-bottom: 20rpx;

						>.view {
							width: 6rpx;
							height: 20rpx;
							background-color: #fff;
							margin-right: 10rpx;

							&.red {
								background-color: rgb(235, 84, 84);
							}

							&.lvse {
								background-color: rgb(71, 178, 98);
							}
						}
					}

					.li_tooltip:last-child {
						margin-bottom: 0rpx;
					}
				}

				.klink {
					width: 100%;
					height: 90%;
					position: relative;
					z-index:999;
					// height: 500rpx;
				}
			}
		}
	}

	.bodyPop_msg {
		background-color: #2B2B2B;
		z-index: 2030 !important;
		width: 500rpx;
		border-radius: 36rpx;
		padding: 80rpx 50rpx;

		>.msg_view {
			background-size: 100% 100%;
			font-size: 28rpx;
			text-align: center;

			view {
				color: #fff;
				font-size: 32rpx;
				margin-bottom: 20rpx;
			}

			.img {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 40rpx;

				image {
					width: 80rpx;
				}
			}

			text {
				font-size: 24rpx;
				color: #fff;
				text-decoration: none;
			}
		}

		.button_msg {
			margin-top: 50rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 28rpx;
			font-weight: 600;

			.cancel {
				width: 250rpx;
				height: 70rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50rpx;
				border: 1px solid #FFFFFF;
				text-align: center;
				line-height: 70rpx;
				font-weight: 600;
				color: #fff;
			}

			.submit {
				width: 250rpx;
				height: 70rpx;
				background: linear-gradient(142deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 50px 50px 50px 50px;
				font-size: 28rpx;
				text-align: center;
				line-height: 70rpx;
				font-weight: 600;
				border: none;
			}
		}

	}
</style>