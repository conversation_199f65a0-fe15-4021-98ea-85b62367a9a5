<template>
  <view class="main">

    <view class="total-data">
      <view class="li">当前累计好友 <text class="num">{{ count }}</text> 人</view>
      <view class="line"></view>
      <view class="li">当前累计收益 <text class="num">{{ totalProfit }}</text> 元</view>
    </view>


    <view class="container">
      <view class="button">
        <view class="bg">开拓好友</view>
      </view>

      <view class="content">

        <scroll-view style="height: 100%;" ref="scrollView" :refresher-threshold="50"
          refresher-background="var(--main-bg-color)" scroll-top="scrollTop" class="scroll-Y" scroll-y
          :refresher-triggered="triggered" refresher-default-style="none" :refresher-enabled="true"
          @refresherrefresh="refresher" @scrolltolower="lower">
          
          <view class="item-invite u-flex u-row-between u-text-center">
            <view class="u-flex-1">用户地址</view>
			<view class="u-flex-1">消费金额</view>
            <view class="u-flex-1">收益</view>
          </view>
          
          <view class="item-invite u-flex u-row-between u-text-center" v-for="(item, index) in inviteList" :key="index">
            <view class="u-flex-1">{{ item.conAdd }}</view>
			<view class="u-flex-1">{{ item.totalMoney }}</view>
            <view class="u-flex-1">{{ item.profit }}</view>
          </view>

          <view class="loading_list" v-show="isLoadingStatus == 0">
            <view>
              <view class="flex">
                <view class="balls"></view>
              </view>
              <view class="text">
                玩命加载中...
              </view>
            </view>
          </view>
          
          <view class="empty-wrap" v-show="inviteList==''&&isLoadingStatus == 2">
            <view class="u-m-b-40" style="color: #e9e9e9;">您还没有好友</view>
            <view class="">快去将邀请链接或邀请码分享给好友或粉丝,</view>
            <view class="">或在社交媒体以及其他渠道进行推广吧。</view>
          </view>

          <view slot="refresher">
            <view class="loadding">
              <view class="gif">
                <image
                  src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
                  mode="widthFix"></image>
              </view>
            </view>
          </view>
        </scroll-view>
        
      </view>
    </view>

    <view class="fixed-footer" @click="copy()">
      立即邀请
    </view>

  </view>
</template>

<script>
  import api from '@/common/api/index.js';
  export default {
    data() {
      return {
        count: 0,
        totalProfit: 0,

        triggered: false,

        inviteList: [],
        pageNum: 1,
        isFooter: true, //没有更多了
        isRequest: false, //多次请求频繁拦截
        isLoadingStatus: 0, //0 加载中  1 正常载入  2无数据
		shareUri:""
      };
    },
    onLoad() {
      this.fetchTotalData()
      this.fetchInviteList()
	  this.fetchInviteUri()
    },
    methods: {
      async fetchTotalData() {
        const {
          status,
          result
        } = await api.inviteInfo({});
        if (status.code === 0) {
          const {
            count,
            totalProfit
          } = result;
          this.count = count || 0;
          this.totalProfit = totalProfit || 0;
        }
      },
      async fetchInviteList() {
        const pageSize = 50;
        const {
          status,
          result
        } = await api.invitedList({
          pageNum: this.pageNum,
          pageSize
        });
        
        this.triggered = false
        if (status.code == 0) {
          this.isRequest = false
          if (result.list == null || result.list == "") {
            this.isFooter = false
            if (this.inviteList == "") {
              this.isLoadingStatus = 2
            }
          } else {
            if (result.list.length < pageSize) {
              this.isFooter = false
            }
            
            this.isLoadingStatus = 1
            this.pageNum++
            result.list.forEach((item) => {
              this.inviteList.push(item)
            })

          }
        } else {
          uni.showToast({
            title: status.msg,
            icon: 'none',
            duration: 3000
          });
        }

      },
      async fetchInviteUri() {
        const {
          status,
          result
        } = await api.getContractAddress({});
        if (status.code === 0) {
          let baseURL = '';
          
          // #ifdef APP
          baseURL = getApp().globalData.url
          // #endif
          
          // #ifdef H5
          baseURL = window.location.origin + '/h5/#'
          // #endif
          
          this.shareUri = `${baseURL}/pages/project/actives/takeOffInvite?takeOffSource=${result}`
        } else {
          uni.showToast({
            title: status.msg,
            icon: 'none',
            duration: 3000
          });
        }
      },
      lower() {
        console.log("触底了")
        if (this.isFooter) {
          if (this.isRequest == false) {
            this.fetchInviteList()
          } else {
            console.log("请求超时，已经拦截")
          }
        } else {
          console.log("已经到底了")
        }
      },
      refresher() {
        this.pageNum = 1
        this.triggered = true
        this.inviteList = []
        console.log('下拉了1')
        this.fetchInviteList()

      },
	  copy(){
	  		  console.log(this.shareUri)
	  		  uni.setClipboardData({
	  		  	data: this.shareUri,
	  		  	success() {
	  		  		uni.showToast({
	  		  			title: '复制链接成功！',
	  		  			icon: 'none'
	  		  		})
	  		  	}
	  		  })
	  }
    }
  }
</script>

<style lang="scss" scoped>
  .main {
    position: relative;
    padding: 36rpx 36rpx 200rpx;
    font-size: 24rpx;
    color: #fff;
  }

  .line {
    width: 100%;
    height: 2rpx;
    background: #6e6d75;
  }

  .total-data {
    padding: 20rpx;
    background: #46454F;
    border-radius: 25rpx;
    font-size: 36rpx;
    color: #fff;

    .li {
      padding: 40rpx;
      display: flex;
      align-items: baseline;
      justify-content: center;
    }

    .num {
      font-size: 60rpx;
      color: #63EAEE;
      margin: 0 8rpx;
    }
  }

  .container {
    position: relative;
    margin-top: 100rpx;
    padding: 2rpx;
    background-image: linear-gradient(0deg, #EF91FB, #40F8EC);
    border-radius: 25rpx;

    .button {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translate(-50%, -50%);
      width: 50%;
    }

    .content {
      border-radius: 25rpx;
      padding: 40rpx 30rpx;
      height: 70vh;
      background: var(--main-bg-color);
    }
    
    .item-invite {
      height: 70rpx;
    }

    .empty-wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 22rpx;
      color: #63EAEE;
      line-height: 1.3;
      height: 70%;
    }
  }

  .button {
    // width:100%;
    height: 88rpx;
    border-radius: 44rpx;
    position: relative;
    /* 添加内边距以避免伪元素覆盖内容 */
    padding: 4rpx;
    background: linear-gradient(135deg, #EF91FB, #40F8EC);
    font-size: 34rpx;
    color: #63EAEE;

    .bg {
      padding: 28rpx 54rpx;
      background-color: #36343F;
      height: 100%;
      border-radius: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .fixed-footer {
    position: fixed;
    left: 50%;
    bottom: 60rpx;

    width: 520rpx;
    height: 88rpx;
    background: linear-gradient(0deg, #EF91FB, #40F8EC);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #000;
    border-radius: 44rpx;
    transform: translate(-50%, 0)
  }
</style>