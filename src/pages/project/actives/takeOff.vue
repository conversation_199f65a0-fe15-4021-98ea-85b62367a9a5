<template>
	<view class="main">
		<!-- <view class="barHeight"></view> -->
		<!-- <view class="top_data">
			<view class="top_title">
				<text>ONE账户</text>
				<image v-show="show" @click="check" src="https://cdn-lingjing.nftcn.com.cn/image/20240527/2d1fe1b1922fd9efee459e3ee7cafc98_94x54.png" mode="widthFix"></image>
				<image v-show="!show" @click="check" src="https://cdn-lingjing.nftcn.com.cn/image/20240527/008996aae75c775d8ba295c6304af7c5_90x58.png" mode="widthFix"></image>
			</view>
			<view class="data">
				<view class="left">
					<text v-show="show">
						{{info.num}}
					</text>
					<text v-show="!show">
						{{amount?amount:"*"}}
					</text>
					份
				</view>
				<view class="right">
					<text class="num" :class="{'lvse':info.percent<0}" v-show="show">
						{{info.percent>=0?`+${info.percent}`:info.percent}}%
					</text>
					<text class="num" :class="{'lvse':info.percent<0}" v-show="!show">
						{{percent?percent:"*"}}
					</text>
					<image v-if="info.percent>=0" src="https://cdn-lingjing.nftcn.com.cn/image/20240301/cfac456e1ed31297cba5971cc5ac01e2_22x22.png" mode="widthFix"></image>
					<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/20240507/81a2dad3f6a56cfd5163523456d040f4_22x22.png" mode="widthFix"></image>
					<text>今日</text>
				</view>
			</view>
		</view> -->
		<view class="label_title" style="margin-top:84rpx;">
			<view class="title">探索您自己的杠精计划</view>
			<!-- <view class="sub_title">账户模式:单币种保证金模式 </view> -->
		</view>
		<view class="nunBg" style="margin-top:40rpx;" v-show="buttonStatus"> 
			1
		</view>
		<view class="label_title2" v-show="buttonStatus">
			<view class="title">什么是“开杠吧”</view>
			<view class="sub_title">学习后有机会完成任务获得奖励</view>
		</view>
		<view class="button border" @click="nav_study()" v-show="buttonStatus">
			<view class="bg">开始学习</view>
		</view>
		<view class="nunBg" style="margin-top:47rpx;margin-bottom:0rpx;" v-show="buttonStatus">
			{{buttonStatus?'2':'1'}}
		</view>
		<view class="label_title2" >
			<view class="title">成为Oner,参与“开杠吧”,赚取One大奖</view>
		</view>
		<view class="button active" :class="{'huise':buttonStatus}" @click="nav_active()">
			开始“开杠吧”
		</view>
		<view class="label_title" style="margin-top:94rpx;">
			<view class="title">“开杠吧”活动</view>
      
     <view class="u-flex u-row-between u-col-bottom">
        <view class="sub_title">距离活动结束还有：</view>
        <!-- <u-count-down ref="uCountDown" :timestamp="countDownTime" @change="onCountDownTimeChange"></u-count-down> -->
        
        <count-down :timestamp="countDownTime" @end="countDownTime = 0" />
        
      </view>
		</view>
		<view class="fenx_view" style="margin-top:60rpx;">
			<view class="left">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240527/996991689cd7cd84f27b6a5e5e0f2ec1_132x130.png" mode="widthFix"></image>
				<view class="title_p">
					<view>BV“开杠吧”惊喜活动,参与“开杠吧”可获得队伍空投(B队V队)</view>
				</view>
			</view>
			<view class="right"  @click="handleTakeOffRouteTo(0)">
				活动详解
			</view>
		</view>
		<view class="fenx_view">
			<view class="left">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240527/e7830cc322ada353217e332571f2f506_126x126.png" mode="widthFix"></image>
				<view class="title_p">
					<view>成功推荐你的好友体验“开杠吧”玩法</view>
					<text>(3/10/20位) </text>
				</view>
			</view>
			<view class="right" @click="handleTakeOffRouteTo(1)">
				去邀请
			</view>
		</view>
		<!-- <view class="label_title" style="margin-top:94rpx;">
			<view class="title">热门“开杠吧”助力工具</view>
		</view>
		<view class="tool_view">
			<view class="li">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240527/2c719d440cf71ab260f602eb49100aac_100x90.png" mode="widthFix"></image>
				<view>借贷功能</view>
			</view>
			<view class="li">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240528/3b37e1996e961a5cacda8205dc968a95_94x98.png" mode="widthFix"></image>
				<view>赚One</view>
			</view>
			<view class="li">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240528/86793ca97080d0e4806a37429385c706_112x100.png" mode="widthFix"></image>
				<view>定投功能</view>
			</view>
			<view class="li">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240528/629e2b0e652880f152d8d28a73e45994_98x98.png" mode="widthFix"></image>
				<view>数据分析</view>
			</view>
		</view> -->
		
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
	</view>
	
</template>
<script>
	import api from '@/common/api/index.js';
	import * as head from "@/static/lottie/head/head.json";

	import CountDown from './components/countDown.vue';
	export default {
		data() {
			return {
				info: "",
				buttonStatus: true,
				show: true,
				isLoadding: false,

				countDownTime: -1,
			}
		},
		onLoad(options) {
			this.get()
			this.getStudyInfo()
			this.fetchRestTime()
		},
		onReachBottom() {

		},
		computed: {
			amount() {
				if (this.info.num) {
					return new Array(this.info.num.toString().length).fill('*').join('');
				}
			},
			percent() {
				if (this.info.percent) {
					return new Array(this.info.percent.toString().length).fill('*').join('');
				}
			}
		},
		methods: {

			handleTakeOffRouteTo(type) {
				// #ifdef APP
					if(type == 0){
						let link = `${getApp().globalData.url}pages/project/actives/takeOffInfo`
						this.$Router.push({
							name: "webView",
							params: {
								url: link,
							}
						})
						return false
					}
					const pathName = ["takeOffInfo", "takeOffPlan"]
					
					this.$Router.push({
						name: pathName[type]
					})
				// #endif
				// #ifdef H5
				const pathName = ["takeOffInfo", "takeOffPlan"]
				
				this.$Router.push({
					name: pathName[type]
				})
				// #endif
				
			},
			async fetchRestTime() {
				const {
					status,
					result
				} = await api.getRestTime({});
				if (status.code === 0) {
					this.countDownTime = result || 0;
					console.log(this.countDownTime);
				} else {
					// uni.showToast({
					// 	title: status.msg,
					// 	icon: 'none',
					// 	duration: 3000
					// });
				}
			},
			async get() {
				const {
					status,
					result
				} = await api.getHoldInfo({});
				if (status.code === 0) {
					this.info = result
				} else {
					// uni.showToast({
					// 	title: status.msg,
					// 	icon: 'none',
					// 	duration: 3000
					// });
				}
			},
			async getStudyInfo() {
				const {
					status,
					result
				} = await api.getUserInfo({});
				if (status.code === 0) {
					if (result.questionStatus == 1) {
						this.buttonStatus = false
					} else {
						this.buttonStatus = true
					}
				} else if (status.code == 1002) {
					this.isLoadding = true
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin",
						})
					}, 1500);
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_study() {
				this.$Router.push({
					name: "takeOffDetails"
				})
			},
			nav_active() {
				if (!this.buttonStatus) {
					// #ifdef APP
					let link = `${getApp().globalData.activeUrl}#/takeOff?activityNo=A70038988068069377`
					this.$Router.push({
						name: "takeOffIndex",
					})
					// #endif
					// #ifdef H5
					let {
						origin
					} = window.location
					window.location.href = `${origin}/active/#/takeOff?activityNo=A70038988068069377`
					// #endif
				} else {
					uni.showToast({
						title: "请先完成上方学习计划！",
						icon: 'none',
						duration: 3000
					});
				}
			},
			check() {
				this.show = !this.show
			}
		},
		components: {
			CountDown
		}
	}
</script>
<style lang="scss" scoped>
	.nunBg {
		background: linear-gradient(135deg, #EF91FB, #40F8EC);
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #36343F;
		font-weight: 700;
		margin-bottom: 25rpx;
	}

	.button {
		width: 100%;
		height: 88rpx;
		border-radius: 44rpx;
		position: relative;
		/* 添加内边距以避免伪元素覆盖内容 */
		padding: 4rpx;
		background: linear-gradient(135deg, #EF91FB, #40F8EC);
		margin-top: 46rpx;

		.bg {
			background-color: #36343F;
			height: 100%;
			border-radius: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		&.active {
			background: linear-gradient(0deg, #EF91FB, #40F8EC);
			display: flex;
			justify-content: center;
			align-items: center;
			color: #36343F;
		}

		&.huise {
			background: #36343F;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #9A999E;
			border: 1px solid #9A999E;
		}

	}

	.main {
		padding: 36rpx;
		color: #fff;
		// padding-top:164rpx;
		padding-bottom: 150rpx;

		.label_title {
			.title {
				font-size: 37rpx;
			}

			.sub_title {
				color: rgba(255, 255, 255, 0.5);
				font-size: 22rpx;
				margin-top: 16rpx;
			}
		}

		.label_title2 {
			margin-top: 30rpx;

			.title {
				font-size: 28rpx;
			}

			.sub_title {
				color: rgba(255, 255, 255, 0.5);
				font-size: 22rpx;
				margin-top: 16rpx;
			}
		}

		.top_data {
			.top_title {
				display: flex;
				color: #fff;

				image {
					width: 47rpx;
					margin-left: 25rpx;
				}
			}

			.data {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;
				margin-top: 54rpx;

				.left {
					font-weight: 60;
					font-size: 38rpx;
					width: 50%;

					text {
						font-size: 64rpx;
						margin-right: 32rpx;
					}
				}

				.right {
					display: flex;
					justify-content: flex-end;
					width: 50%;
					font-size: 28rpx;
					color: #fff;

					.num {
						color: #EC4068;

						&.lvse {
							color: #6CFF8A;
						}
					}

					image {
						width: 24rpx;
						margin: 0rpx 10rpx 0rpx 12rpx;
					}
				}
			}
		}

		.fenx_view {
			border: 1px solid rgba(255, 255, 255, 0.3);
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx 20rpx;
			border-radius: 30rpx;
			margin-bottom: 30rpx;
			height: 133rpx;

			.left {
				display: flex;
				justify-content: flex-start;
				font-size: 24rpx;

				.title_p {
					width: 400rpx;
					margin-left: 20rpx;
					line-height: 34rpx;

					text {
						color: rgba(255, 255, 255, 0.5);
						font-size: 22rpx;
						margin-top: 10rpx;
					}
				}

				image {
					width: 65rpx;
				}
			}

			.right {
				width: 118rpx;
				height: 49rpx;
				background: linear-gradient(90deg, #EF91FB, #40F8EC);
				border-radius: 15rpx;
				color: #36343F;
				font-size: 24rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}

		.tool_view {
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
			margin-top: 66rpx;

			.li {
				width: 50%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-bottom: 50rpx;

				image {
					width: 50rpx;
					margin-right: 24rpx;
				}
			}
		}
	}
</style>