<template>
	<view class="main">
		<view class="position_top">
			<!-- <view class="barHeight"></view> -->
			<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
				:background="{ backgroundColor: 'var(--main-bg-color)' }" title="衍生公告"
				title-color="var(--default-color1)" title-bold>
			</u-navbar>
			<!-- <view class="head_title ">
				<view class="title_1 ">
					<text>公告</text>
				</view>
			</view> -->
			<view class="search_view">
				<u-search placeholder="" search-icon="/static/imgs/notic/search.png" v-model="title" bg-color='#35333E'
					color="#fff" :clearabled='false' @search="search" @clear="clear" :show-action="false"
					:input-style='searchStyle'></u-search>
				<view class="right" @click="search">搜索</view>
			</view>
			<!-- tabs -->
			<view class="tabbar_view" v-if="!Derivation">
				<u-tabs :list="tabList" :is-scroll="true" font-size='28rpx' inactive-color='#9A999F' active-color='#fff'
					:current="current" bg-color="#35333E" :bar-style='barStyle' @change="change"></u-tabs>
			</view>
		</view>
		<!-- :style="{ 'padding-top': `${280 + height}rpx` }" -->
		<view class="content padding_lr" >
			<!-- 列表  -->
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<scroll-view style="padding-bottom: 100rpx; " v-if="list.length > 0 && isLoadingStatus == 1"
				ref="scrollView" refresher-threshold="50" refresher-background="var(--main-bg-color)"
				scroll-top="scrollTop" class="scroll-Y" scroll-y :refresher-triggered="triggered"
				refresher-default-style="none" :refresher-enabled="true" @refresherrefresh="refresher"
				@scrolltolower="lower">
				<view class="list_view">
					<view class="list_li" @click="nav_details(item)" v-for="(item, index) in list" :key="index">
						<view class="left_img">
							<image :src="item.cover.src" mode="heightFix"></image>
						</view>
						<view class="right_font">
							<view class="title twoOver">
								{{ item.title }}
							</view>

							<view class="time">
								<view>
									<view v-if="item.templateType == 1">白名单</view>
									<view v-if="item.templateType == 2">合成</view>
									<view v-if="item.templateType == 3">寄售</view>
									<view v-if="item.templateType == 4">上新预告</view>
									<view v-if="item.templateType == 5">空投</view>
									<view v-if="item.templateType == 6">上新提醒</view>
									<view v-if="item.templateType == 7">合成提醒</view>
									<view v-if="item.templateType == 8">活动</view>
									<view v-if="item.templateType == 9">运营</view>
									<view v-if="item.templateType == 10">限额调整</view>
									<view v-if="item.templateType == 11">分解</view>
									<view v-if="item.templateType == 12">置换</view>
									<view v-if="item.templateType == 13">提醒</view>
									<view v-if="item.templateType == 14">展示位变更</view>
									<view v-if="item.templateType == 15">竞价</view>
									<view v-if="item.templateType == 16">冰封活动</view>
									<view v-if="item.templateType == 17">分区调整</view>
									<view v-if="item.templateType == 18">突袭公告</view>
									<view v-if="item.templateType == 19">膨胀公告</view>
									<view v-if="item.templateType == 20">开放转售</view>
									<view v-if="item.templateType == 21">委托购买</view>
								</view>
								<view>
									{{ item.createdAt }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view slot="refresher">
					<view class="loadding">
						<view class="gif">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="null_body" v-show="list.length == 0 && isLoadingStatus == 2">
				<view class="null">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						mode="widthFix"></image>
					<view class="text">
						暂无数据
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import api from '@/common/api/index.js';
import TabBar from "@/components/public/TabBar";
import * as head from "@/static/lottie/head/head.json";
import {
	number
} from 'echarts';
export default {
	data() {
		return {
			Derivation: false,
			height: "0",
			title: "",
			tabList: [{
				name: '全部公告',
				value: '',
			}, {
				name: '合成',
				value: '2',
			}, {
				name: '寄售',
				value: '3',
			}, {
				name: '上新',
				value: '4',
			}, {
				name: '空投',
				value: '5',
			}, {
				name: '活动',
				value: '8',
			}, {
				name: '运营',
				value: '9',
			}],
			list: [],
			show: true,
			pageNum: 1,
			isFooter: true, //没有更多了
			isRequest: false, //多次请求频繁拦截
			current: 0,
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				'width': '60rpx',
				height: '6rpx'
			},
			templateType: "",
			searchStyle: {
				'background': '#35333E',
			},
			option: {
				data: head,
				loop: false,
				autoplay: false
			},
			showAnimation: false,
			animationInstance: null,
			title: "",
			isLoadingStatus: 0,
			triggered: false,
			refresherState: true,
		}
	},
	onLoad(options) {
		this.title = options.title
		this.Derivation = options.Derivation

		let _this = this
		uni.getSystemInfo({
			success: function (res) {
				_this.height = res.statusBarHeight * 2
			}
		});
		if (options.Derivation) {
			// _this.height = -100
		}
	},
	onShow() {
		// 页面重新显示时也处理参数
		// const { query, params } = this.$route;
		// this.handleRouteChange(query);
		this.pageNum = 1
		this.getList()

	},
	// onPullDownRefresh() {
	// 	setTimeout(() => {
	// 		this.pageNum = 1
	// 		this.list = []
	// 		this.getList()
	// 		uni.stopPullDownRefresh(); //停止下拉刷新动画
	// 	}, 1000);
	// },
	methods: {
		handleRouteChange(query) {
			if (query.Derivation) {
				this.Derivation = query.Derivation;
				this.height = -50
			} else {
				this.Derivation = false;
				this.height = 0
			}
		},
		change(index) {
			this.current = index
			this.list = [];
			this.pageNum = 1;
			this.isLoadingStatus = 0
			this.templateType = this.tabList[index].value
			this.getList()
		},
		search() {
			this.list = [];
			this.isLoadingStatus = 0
			this.pageNum = 1
			this.getList()
		},
		clear() {
			this.title = ""
			this.pageNum = 1
			this.getList()
		},
		async getList() {
			this.isRequest = true
			if (this.pageNum == 1) {
				this.list = [];
			}
			const {
				status,
				result,
				traceId
				// java_officialArticleList
			} = await api.java_officialArticleList({
				type: this.type,
				pageSize: 10,
				pageNum: this.pageNum,
				templateType: this.templateType,
				title: this.title,
				tapType: this.Derivation ? -1 : 0
			});
			console.log("traceId：" + traceId)
			if (status.code === 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == "") {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.list.push(item)
					})
				}
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_search() {
			this.pageNum = 1
			this.type = null
			this.getList()
		},
		nav_details(item) {
			console.log(item.id)

			if (item.linkType == "NO_LINK") {
				return
			} else if (item.linkType == "WEB_LINK" && item.h5Link) {
				this.$Router.push({
					name: "webView",
					params: {
						url: item.h5Link
					}
				})
				return
			} else if (item.linkType == "DETAIL_LINK") {
				// #ifdef H5
				this.$Router.push({
					name: "officialDetail",
					params: {
						id: item.id,
						linkType: item.linkType
					}
				})
				// #endif
				// #ifdef APP
				let url = `${getApp().globalData.url}pagesA/project/official/detail?id=${item.id}&linkType=${item.linkType}`
				this.$Router.push({
					name: "webView",
					params: {
						url
					}
				})
				// #endif
				return
			}
			this.$Router.push({
				name: "officialDetail",
				params: {
					id: item.id
				}
			})
		},
		lower() {
			console.log("触底了")
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		scroll(e) {
			console.log('下拉了2')
		},
		refresher() {
			this.triggered = true
			console.log('下拉了1')
			setTimeout(() => {
				this.triggered = false
				this.pageNum = 1
				this.list = []
				this.getList()
			}, 1000)
		},
	},
	components: {
		TabBar
	}
}
</script>
<style lang="scss" scoped>
@font-face {
	font-family: 'YouSheBiaoTiHei';
	src: url(https://cdn-lingjing.nftcn.com.cn/h5/ttf/YouSheBiaoTiHei-2.ttf);
}

::v-deep .u-tab-bar {
	margin-left: -8rpx !important;
}

.head_bg {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	height: 500rpx;
	z-index: -1;
	background-color: #fff;
	will-change: transform;
	transform: translateZ(0);
}

.padding_lr {
	padding: 0rpx 56rpx;
}

.main {
	flex: 1;
}

.head_title {
	// height: 170rpx;
	// line-height: 100rpx;
	// padding:86rpx 0 0 0;
}

.title_1 {
	color: #fff;
	font-weight: 600;
	font-size: 44rpx;
	text-align: center;
}

.content {
	// #ifdef H5
	padding-top: 240rpx;
	// #endif

	// #ifndef H5
	padding-top: 300rpx;
	// #endif
}

.position_top {
	position: fixed;
	top: 0rpx;
	left: 0;
	width: 100%;
	background-color: var(--main-bg-color);
	z-index: 99;
	padding: 10rpx 40rpx 0 40rpx;
}

.search_view {
	margin-top: 30rpx;
	position: relative;
	border-radius: 28rpx;
	border: 1px solid rgba(255, 255, 255, 0.5);

	.right {
		font-size: 28rpx;
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #fff;
	}
}

.tabbar_view {
	margin-top: 30rpx;
}

.list_view {
	width: 638rpx;
	margin-top: 0rpx;
}

.list_li {
	width: 100%;
	// height: 200rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 40rpx;
	background: #35333E;
	border-radius: 16px;
	transform: rotate(0deg);
	-webkit-transform: rotate(0deg);
	border: 1rpx solid #717171;
	box-sizing: border-box;
	overflow: hidden;
	position: relative;
}

.left_img {
	// margin: 0 40rpx 0 0;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;

	>image {
		height: 190rpx;
	}
}

.right_font {
	position: absolute;
	right: 20rpx;
	width: 361rpx;

	.title {
		width: 361rpx;
		line-height: 50rpx;
		font-family: 'YouSheBiaoTiHei';
		font-weight: 400;
		color: #FFFFFF;
		font-size: 36rpx;
	}
}



.time {
	display: flex;
	align-items: center;
	margin-top: 10rpx;

	>view:nth-child(1) {
		height: 30rpx;
		line-height: 30rpx;
		background: #FFFFFF;
		text-align: center;
		font-weight: 400;
		font-size: 20rpx;
		color: #35333E;
		transform: skewX(30deg);
		margin-right: 30rpx;
		padding: 0rpx 10rpx;

		>view {
			transform: skewX(-30deg);
		}
	}

	>view:nth-child(2) {
		color: $uni-color-gray;
		font-size: $uni-font-size-h4;
		line-height: 44rpx;
		font-weight: 400;
		font-size: 22rpx;
	}
}

.null_body {
	.null {
		image {
			width: 242rpx;
		}
	}

	.text {
		color: #A6A6A6;
		font-size: 28rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	width:100%;
	height: 60vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

.scroll-Y {
	max-height: 90vh;
}
</style>
