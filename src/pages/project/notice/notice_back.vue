<template>
    <div>
        <!-- <view class="head_title padding_lr" v-if="false">
			<view style="height:86rpx;"></view>
			<view class="title_1 ">
				<text>官方公告</text>
			</view>
		</view> -->
        <view class="content padding_lr" v-if="false">
            <view class="search_view">
                <u-search placeholder=""
                    search-icon="https://cdn.yanjie.art/image/20240118/9b03ce9292767ae8eec9722d8eac0ebf_44x44.png"
                    shape="square" v-model="title" @search="search" @clear="clear" :show-action="false"></u-search>
                <view class="right">搜索</view>
            </view>
            <view class="tabbar_view">
                <!-- <u-tabs :list="tabList" :current="current" @change="change" lineWidth="20" lineHeight="6" :inactiveStyle="{color: '#A6A6A6'}"
					:activeStyle="{color: '#141414','font-size':'44rpx'}" 
					:itemStyle="{height: '64px'}"> -->
                <u-tabs name="cate_name" :bar-style="barStyle" :list="tabList" bold :active-item-style="itemStyle"
                    active-color="#141414" :current="current" @change="change"></u-tabs>
            </view>
            <view class="loading_list" v-show="isLoadingStatus == 0">
                <view>
                    <view class="flex">
                        <view class="balls">
							<view></view>
							<view></view>
							<view></view>
						</view>
                    </view>
                    <view class="text">
                        玩命加载中...
                    </view>
                </view>
            </view>
            <view class="list_view" v-show="list != '' && isLoadingStatus == 1">
                <view class="list_li" @click="nav_details(item)" v-for="(item, index) in list" :key="index">
                    <view class="left_img">
                        <u-image :showLoading="false" :src="item.cover" width="276rpx" height="144rpx"
                            radius="24rpx"></u-image>
                    </view>
                    <view class="right_font">
                        <view class="title twoOver">
                            {{ item.title }}
                        </view>
                        <view class="sub_title">
                            <text class="sub_title_text">
                                Pink Wallet
                            </text>
                        </view>
                        <view class="time">
                            {{ item.publishTime }}
                        </view>
                    </view>
                </view>
            </view>
            <view class="null_body" v-show="list == '' && isLoadingStatus == 2">
                <view class="null">
                    <image
                        src="https://cdn.yanjie.art/image/20241024/cd8c8d85f67939b0f155d189ad269484_200x200.png"
                        mode="widthFix"></image>
                    <view class="text">
                        暂无数据
                    </view>
                </view>
            </view>
        </view>
    </div>
</template>

<script>
export default {

}
</script>

<style lang="scss" scoped></style>