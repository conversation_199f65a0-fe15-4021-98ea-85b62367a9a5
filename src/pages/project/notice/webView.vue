<template>
	<view class="work_index">
		<web-view  class="webView_class"  :webview-styles="webviewStyles" :src="link" @onPostMessage="message" @load="onWebViewLoad" ></web-view>
	</view>
</template>

<script>
	// 
	export default {
		data() {
			return {
				webviewStyles: { // 加载进度颜色 
					progress: {
						color: '#63EAEE'
					}
				},
				optionData:{ // 传输的参数
					appLink:''
				},
				link:""
			}
		},
		// onReady() {
		//    window.xXX = () => {
		//      //do something
		// 	 console.log('这里是原生方法')
		//    };
		//    //window.yYY = this.yYY;
		//  },
		onLoad(option) {
			console.log('进入到了跳转页面')
			console.log(option.url)
			let platform,appVersion;
			uni.getSystemInfo({
				success: function(res) {
					console.log('当前平台？？？？？', res.statusBarHeight)
					platform = res.platform
					appVersion = res.appVersion
					console.log(platform)
				}
			});
			if(option.url){
				console.log(uni.getStorageSync('token'))
				if(option.url.indexOf("?")!=-1){
					console.log("跳转url 带?号",`${option.url}&platform=${platform}&appVersion=${appVersion}&token=${uni.getStorageSync('token')}`)
					this.link = `${option.url}&platform=${platform}&appVersion=${appVersion}&token=${uni.getStorageSync('token')}`
				}else{
					console.log("跳转url",`${option.url}?platform=${platform}&appVersion=${appVersion}&token=${uni.getStorageSync('token')}`)
					this.link = `${option.url}?platform=${platform}&appVersion=${appVersion}&token=${uni.getStorageSync('token')}`
				}
			}
		},
		mounted(){
			//this.getWorkList(); // 获取工作台列表
		},
		methods: {
			message(e) {
				console.log(e.detail.data)
			 },
			async onWebViewLoad(e) {
			  setTimeout(() => {
				uni.setNavigationBarTitle({
				  title: 'xCase', // 替换为你想展示的具体标题
				});
			  }, 500); // 这里假设网页需要2秒加载完成，根据实际情况调整
			},
		},
		onBackPress(e) {
			console.log(e);
			// if (e.from == 'backbutton') {
			// 	console.log('点了返回按钮')
			// 	// // 返回到上一层
			// 	// // uni.navigateBack({
			// 	// //     delta: 2
			// 	// // });
				
			// 	return false; //阻止默认返回行为
			// }
		}
	}
</script>

<style  >
	page{
		background-color: #35333E;
	}
	.work_index{
		flex: 1;
	}
	.webView_class{
		flex: 1;
	}
</style>

