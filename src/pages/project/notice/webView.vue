<template>
	<view class="work_index">
		<web-view class="webView_class" :webview-styles="webviewStyles" :src="link" @onPostMessage="message"
			@load="onWebViewLoad"></web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			webviewStyles: { // 加载进度颜色
				progress: {
					color: '#FF82A3'
				}
			},
			optionData: { // 传输的参数
				appLink: ''
			},
			link: ""
		}
	},
	// onReady() {
	//    window.xXX = () => {
	//      //do something
	// 	 console.log('这里是原生方法')
	//    };
	//    //window.yYY = this.yYY;
	//  },
	onLoad(option) {
		console.log('进入到了跳转页面')
		console.log(option.url)
		let platform, appVersion;
		uni.getSystemInfo({
			success: function (res) {
				console.log('当前平台？？？？？', res.statusBarHeight)
				platform = res.platform
				appVersion = res.appVersion
				console.log(platform)
			}
		});
		if (option.url) {
			if (option.url.indexOf("?") != -1) {
				// this.link = `${option.url}&platform=${platform}&token=${uni.getStorageSync('token')}&appVersion=${appVersion}`
				console.log(1111)
				let url = `${option.url}&platform=${platform}&token=${uni.getStorageSync('token')}&appVersion=${appVersion}&NEW_ACCESS_TOKEN=${option.NEW_ACCESS_TOKEN}`
				console.log(url)
				this.link = url
			} else {
				console.log(222)
				let url = `${option.url}?platform=${platform}&token=${uni.getStorageSync('token')}&appVersion=${appVersion}&NEW_ACCESS_TOKEN=${option.NEW_ACCESS_TOKEN}`
				console.log(url)
				this.link = url
				// console.log(`${option.url}&platform=${platform}&token=${uni.getStorageSync('token')}&appVersion=${appVersion}`)
			}
			console.log(this.link);
		}
		// if (option.url && option.url.includes('https://cdn.yanjie.art/')) {
		// 	setTimeout(() => {
		// 		uni.setNavigationBarTitle({
		// 			title: ' '
		// 		});
		// 	}, 300); // 这里假设网页需要2秒加载完成，根据实际情况调整
		// }
		// if (option.url && option.url.includes('https://yvwallet.yeepay.com/')) {
		// 	setTimeout(() => {
		// 		uni.setNavigationBarTitle({
		// 			title: '易宝钱包'
		// 		});
		// 	}, 300); // 这里假设网页需要2秒加载完成，根据实际情况调整
		// }
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: 'Pink Wallet'
			});
		}, 300); // 这里假设网页需要2秒加载完成，根据实际情况调整

	},
	mounted() {
		//this.getWorkList(); // 获取工作台列表
	},
	methods: {
		message(e) {
			console.log(e.detail.data)
		},
		async onWebViewLoad(e) {
			console.log(e)
			//  setTimeout(() => {
			// uni.setNavigationBarTitle({
			//   title: '衍界', // 替换为你想展示的具体标题
			// });
			//  }, 500); // 这里假设网页需要2秒加载完成，根据实际情况调整
		},
	},
	onBackPress(e) {
		console.log(e);
		if (e.from == 'backbutton') {
			console.log('点了返回按钮')
			// // 返回到上一层
			// // uni.navigateBack({
			// //     delta: 2
			// // });

			return false; //阻止默认返回行为
		}
	}
}
</script>

<style>
.work_index {
	width: 100%;
	height: 100%;
	flex: 1;
}

.webView_class {
	flex: 1;
}
</style>