<template>
	<view>
		公告
		<TabBar></TabBar>
	</view>
</template>
<script>
import TabBar from "@/components/public/TabBar";
export default {
	computed: {

	},
	data() {
		return {
			
		}
	},

	onLoad(options) {
		
	},
	onPullDownRefresh() {
		setTimeout(() => {
			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 1000);
	},
	onShow() {

	},
	onHide() {
	},
	onReachBottom() {
		
	},
	onPageScroll(res) {

	},
	watch: {

	},
	methods: {
		
	},
	components: {
		TabBar,
	}
}
</script>
<style lang="scss" scoped>
</style>