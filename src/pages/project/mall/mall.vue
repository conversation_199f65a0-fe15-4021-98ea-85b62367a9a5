<template>
	<view class="main">
		<view class="position_top ">
			<view class="barHeight"></view>
			<view class="head_view">
				<view class="search_view" @tap="userInfo()">
					<u-search placeholder="" bg-color="var(--main-bg-color)"
						search-icon="https://cdn-lingjing.nftcn.com.cn/image/20240304/6a6f38079eb083388f9d7246ec631693_44x44.png"
						shape="square" :disabled="appDisabled" v-model="title" @search="search" @clear="clear" color="#fff"
						:show-action="false"></u-search>
					<view class="right">搜索</view>
				</view>
			</view>
			<view class="tabbar_view">
				<u-tabs name="cate_name" v-if="scrollFlag" bg-color="var(--main-bg-color)" :bar-style="barStyle"
					:list="tabList" bold :is-scroll="scrollFlag" inactive-color="var(--default-color3)"
					:active-item-style="itemStyle" active-color="var(--default-color1)" :current="current"
					@change="change"></u-tabs>
			</view>
		</view>
		<view class="content padding_lr" :style="{ 'padding-top': `${200 + height}rpx` }">
			<scroll-view ref="scrollView" :refresher-threshold="50" refresher-background="var(--main-bg-color)"
				scroll-top="scrollTop" class="scroll-Y" scroll-y :refresher-triggered="triggered"
				refresher-default-style="none" :refresher-enabled="true" @refresherrefresh="refresher"
				@scrolltolower="lower">
				<view class="loading_list" v-show="isLoadingStatus == 0">
					<view>
						<view class="flex">
							<view class="balls"></view>
						</view>
						<view class="text">
							玩命加载中...
						</view>
					</view>
				</view>
				<view class="collection" :style="{ 'padding-bottom': `${paddingBottom}rpx` }"
					v-show="seriesList != '' && isLoadingStatus == 1">
					<view class="li" v-for="(item, index) in seriesList" @tap="nav_seriesList(item)">
						<view class="bg">
							<view class="cover">
								<image :src="item.cover.src" mode="aspectFill"></image>
								<view class="left_bottom_icon">
									流通{{ item.activeNum }}份
								</view>
								<!-- <view class="right_top_icon">
								<image src="https://cdn-lingjing.nftcn.com.cn/image/20240114/5c0c251af8f94f2c5f071fa03881e909_110x44.png" mode="widthFix"></image>
							</view> -->
								<view class="tuishi" v-if="item.isExitMarket == 1">
									<image
										src="https://cdn-lingjing.nftcn.com.cn/image/20240310/89a70b6d15a2a87fcc8fd4628db673c2_274x274.png"
										mode="widthFix"></image>
								</view>
								<view class="right_active">
									<view class="li_view" v-for="(itemm, index) in item.tagList"
										:style="{ 'background': `url(${itemm.tagImage})`, 'backgroundSize': '100% 100%' }">
										{{ itemm.tagName }}
									</view>
								</view>
							</view>
							<view class="new_icon" v-if="item.newFlag == 1">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240507/50e1f2e8af60b46eae85f74fd6c554b2_121x40.png"
									mode="widthFix"></image>
							</view>
							<view class="ji_icon" v-if="item.tag > 6">
								<image :src="`../../../static/imgs/public/kun/list_ji_${item.tag}.png`"
									mode="heightFix">
								</image>
							</view>
							<view class="dan_icon" v-else>
								<image :src="`../../../static/imgs/public/kun/list_dan_${item.tag}.png`"
									mode="heightFix">
								</image>
							</view>
						</view>
						<view class="font_view">
							<view class="title oneOver">
								{{ item.title }}
							</view>
							<view class="sub_title" @click.stop="isModel = true">
								总量：{{ item.createNum }}份
							</view>
							<view class="flex icon_price">
								<view class="left_huo" @click.stop="isModel = true">
									<image v-for="(item, index) in item.heat"
										src="https://cdn-lingjing.nftcn.com.cn/image/20240114/2b3bb71487aa70afc402885f23810a58_22x22.png"
										mode="widthFix"></image>
								</view>
								<view class="right_price">
									<view v-if="item.minPrice > 0">
										<text>￥</text>
										{{ item.minPrice }}
										<text class="qi">起</text>
									</view>
									<view v-else style="color:var(--default-color1)">
										<text>{{ item.isLimitPrice === 1 ? "限价:￥" : "￥" }}</text>
										<text>{{ item.isLimitPrice === 1 ? item.limitPrice : "—" }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="null_body" v-show="seriesList == '' && isLoadingStatus == 2">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							暂无数据
						</view>
					</view>
				</view>
				<view slot="refresher">
					<view class="loadding">
						<view class="gif">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
				<!-- #ifdef APP -->
				<view class="footerText" v-show="!isFooter && platform == 'ios' && seriesList != ''">我也是有底线的~</view>
				<!-- #endif -->
			</scroll-view>
		</view>
		<u-modal class="model" width="600" v-model="isModel" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject" :mask-close-able="true">
			<view class="colse" @click="isModel = false">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="title">
				小火苗表示藏品火爆程度
			</view>
			<view class="introduce">
				<view>1个小火苗表示较前一日上涨>=10%</view>
				<view>2个小火苗表示较前一日上涨>=20%</view>
				<view>3个小火苗表示较前一日上涨>=30%</view>
				<view>4个小火苗表示较前一日上涨>=40%</view>
				<view>5个小火苗表示较前一日上涨>=50%</view>
			</view>
		</u-modal>
		<TabBar :initialActiveIndex="1"></TabBar>

		<!-- 30s后弹出关闭bit通知 -->
		<u-modal class="" v-model="showSetting" :maskCloseAble="false" width="600rpx" borderRadius="36"
			:show-title="false" :mask-close-able="true" :show-confirm-button="false">
			<view class="closenotice">
				<text class="bittitle">图片</text>
				<view class="bitline"></view>
				<text class="midcolor">是否接受"图片"板块相关公告提醒?</text>

				<!-- <text class="bitbody ">
        </text> -->

				<view class="bitbtn">
					<view @click="showSetting = false">不，下次</view>
					<view @click="gosettingnotice">好的，关注</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>
<script>
import api from '@/common/api/index.js';
import TabBar from "@/components/public/TabBar";
import * as head from "@/static/lottie/head/head.json";
export default {
	data() {
		return {
			timerSetting: null,
			showSetting: false,
			height: "",
			title: "",
			scrollFlag: true,
			tabList: [],
			seriesList: [],
			show: true,
			pageNum: 1,
			isFooter: true, //没有更多了
			isRequest: false, //多次请求频繁拦截
			current: 0,
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				'width': '54rpx',
				'height': '16rpx',
				'border-radius': '0rpx',
				'bottom': '6rpx',
				'z-index': '1'
			},
			itemStyle: {
				'font-size': '32rpx',
				'min-width': '120rpx',
				'z-index': '2'
			},
			option: {
				data: head,
				loop: false,
				autoplay: false
			},
			marketTabId: "",
			triggered: false,
			refresherState: true,
			showAnimation: false,
			animationInstance: null,
			isLoadingStatus: 0, //0 加载中  1 正常载入  2无数据
			appDisabled: false,
			platform: "",
			paddingBottom: 100,
			isBv: 1,
			isModel: false,
			titleObject: {
				'color': '#F9F9F9',
				'padding': '40rpx 40rpx 0rpx 40rpx',
				'font-size': '32rpx',
				'font-weight': '600'
			},
		}
	},
	onLoad(options) {

		let _this = this
		uni.getSystemInfo({
			success: function (res) {
				console.log('当前平台？？？？？', res.statusBarHeight)
				_this.height = res.statusBarHeight * 2
				_this.platform = res.platform
				// #ifdef APP
				if (res.platform == 'ios') {
					_this.paddingBottom = 400
				}
				// #endif
				console.log(_this.height)
			}
		});
		// #ifdef APP
		this.appDisabled = true
		// #endif
		console.log(uni.getStorageSync('isBv'))
		if (uni.getStorageSync('isBv')) {
			this.isBv = 1
			uni.removeStorageSync('isBv')
		}
		this.getTab(() => {
			if (this.tabList.length > 0) {
				this.marketTabId = this.tabList[0].marketTabId
			}
			this.getList()
		})
	},
	onShow() {
		//#ifdef APP-PLUS
		let isPushBv = uni.getStorageSync('isPushBv')
		if (isPushBv) {
			this.timerSetting = setTimeout(() => {
				this.showSetting = true; // 设置变量为 true，弹窗显示
			}, 30000); // 30秒
		}
		// #endif
	},
	onHide() {
		// 页面隐藏时清除定时器
		this.clearTimerSetting();
	},
	onUnload() {
		// 页面卸载时清除定时器
		this.clearTimerSetting();
	},
	// onPullDownRefresh() {
	// 	setTimeout(() => {
	// 		if (this.marketTabId) {
	// 			this.seriesList = []
	// 			this.pageNum = 1
	// 			this.isFooter = true
	// 			this.getList()
	// 		}
	// 		uni.stopPullDownRefresh(); //停止下拉刷新动画
	// 	}, 1000);
	// },
	onReachBottom() {
		// if (this.isFooter) {
		// 	if (this.isRequest == false) {
		// 		this.getList()
		// 	} else {
		// 		console.log("请求超时，已经拦截")
		// 	}
		// } else {
		// 	console.log("已经到底了")
		// }
	},
	methods: {
		clearTimerSetting() {
			if (this.timerSetting) {
				clearTimeout(this.timerSetting);
				this.timerSetting = null; // 避免重复清除
			}
		},
		async get_tag_list() {
			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform
			let res = await this.$api.tag_list({
				deviceToken,
				phoneType
			});
			if (res.status.code == 0) {
				let tags = res.result.tags
				if (tags) {
					let isPushBv = this.containsCharacter(tags, 'NO_COLLECTIBLES_TAG')
					let isPushBit = this.containsCharacter(tags, 'TRADE_PRICE_CHANGE_TAG')
					uni.setStorageSync('isPushBit', isPushBit)
					this.$forceUpdate()
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		containsCharacter(str, char) {
			// 使用 String.prototype.includes 方法检查字符串 str 是否包含字符 char
			return str.includes(char);
		},
		async gosettingnotice() {
			let token = uni.getStorageSync('token')
			if (!token) {
				this.$Router.push({
					name: 'mainLogin',
				})
				return
			}
			await this.get_tag_list()
			let isPushBit = uni.getStorageSync('isPushBit')
			let isPushBv = uni.getStorageSync('isPushBv')

			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform

			let params2 = {
				deviceToken,
				operationType: 0,
				phoneType,
				tag: 'NO_COLLECTIBLES_TAG'
			}
			let res2 = await this.$api.subscribe(params2);

			if (res2.status.code == 0) {
				this.showSetting = false
				uni.showToast({
					title: '成功',
					icon: 'none',
					duration: 3000
				});
				// this.get_tag_list()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		change(index) {
			this.current = index
			this.marketTabId = this.tabList[index].value
			this.seriesList = []
			this.pageNum = 1
			this.isLoadingStatus = 0
			this.isFooter = true
			this.getList()
		},
		search() {
			this.pageNum = 1
			this.getList()
		},
		clear() {
			this.title = ""
			this.pageNum = 1
			this.getList()
		},
		async getTab(callback) {
			this.isLoadingStatus = 0
			const {
				status,
				result
			} = await api.java_recommendTabList({
				moduleId: this.isBv == 1 ? 1 : 6
			});
			if (status.code === 0) {

				if (result.list == null) {
					this.scrollFlag = false
					this.isLoadingStatus = 2
					setTimeout(() => {
						this.triggered = false
					}, 1000)
					return
				}
				const tabData = result.list.map((item) => {
					return {
						...item,
						name: item.title,
						value: item.marketTabId,
						isFire: item.fire == 1 ? true : false,
						// xxx: new Date().getTime() // 本地测试tab数据变动
					}
				})

				const oldTabData = JSON.stringify(this.tabList);
				const newTabData = JSON.stringify(tabData);

				const hasChange = oldTabData != newTabData;

				if (hasChange) {
					/*
						数据发生变动时, scrollFlag 必须先修改为false, 销毁 uTabs 组件
						然后再修改为 true, 重新用新数据渲染 uTabs 组件
						否则 uTabs 组件底部滑块会错位 
					*/
					this.scrollFlag = false;
					this.tabList = tabData;
				}

				setTimeout(() => {
					this.scrollFlag = true
					callback && callback(hasChange);
				}, 1000)

			} else if (status.code == 1002) {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
				setTimeout(() => {
					this.$Router.pushTab({
						name: "mainLogin",
					})
				}, 1500);
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_search() {
			this.pageNum = 1
			this.type = null
			this.getList()
		},
		nav_details(item) {
			console.log(item.id)
			this.$router.push({
				name: "webView",
				params: {
					url: 'http://web-test.nftcn.com/h5/#/pagesA/project/official/detail_art?id=' + item.id
				}
			})
		},
		nav_to(name) {
			this.$Router.push({
				name
			})
		},
		async getList() {
			// this.isLoadingStatus = 0
			const {
				status,
				result
			} = await this.$api.java_marketRecommendList({
				marketTabId: this.marketTabId,
				pageNum: this.pageNum
			});
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.seriesList == "") {
						this.isLoadingStatus = 2
					}
				} else {
					if (result.list.length < 14) {
						this.isFooter = false
					}
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.seriesList.push(item)
					})
					console.log(this.seriesList)
				}
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_seriesList(item) {
			if (item.ctid) {
				this.$Router.push({
					name: 'seriesList',
					params: {
						ctid: item.ctid
					}
				})
			}
		},
		lower() {
			console.log("触底了")
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		scroll(e) {
			console.log('下拉了2')
		},
		refresher() {
			this.triggered = true

			this.seriesList = []
			console.log('下拉了1')

			this.getTab((hasChange) => {
				this.triggered = false
				if (hasChange) {
					this.change(0)
				} else {
					this.change(this.current)
				}
			})

		},
		async userInfo() {
			let res = await this.$api.userInfo({});
			if (res.status.code == 0) {
				this.$Router.push({
					name: 'search'
				})
			} else if (res.status.code == 1002) {
				setTimeout(() => {
					this.$Router.push({
						name: 'mainLogin',
					})
				}, 1500)
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		getLoading() {
			this.seriesList = []
			this.getTab((hasChange) => {
				if (hasChange) {
					this.change(0)
				} else {
					this.change(this.current)
				}
			})
		}
	},
	components: {
		TabBar
	},
}
</script>
<style lang="scss">
.padding_lr {
	padding: 0rpx 40rpx;
}

.closenotice {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	// height: 300rpx;
	margin: 48rpx 0 62rpx 0;

	.bittitle {
		background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
		background-size: 100% 100%;
		width: 226rpx;
		height: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #ffffff;

		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: bold;
		font-size: 28rpx;
	}

	.bitline {
		margin: 50rpx 0 0rpx 0;
		height: 1rpx;
		width: 100%;
		background: #53505d;
	}

	.midcolor {
		text-align: left;

		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 26rpx;
		// line-height: 44rpx;
		text-align: left;
		color: #63eaee;
		margin: 41rpx 0 58rpx 0;

	}

	.bitbody {
		// margin: 29rpx 34rpx;
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 26rpx;
		// color: #ffffff;
		line-height: 44rpx;
		text-align: left;


	}

	.bitbtn {
		display: flex;
		width: 100%;
		padding: 0 40rpx;
		justify-content: space-between;
		align-items: center;

		view {
			&:nth-of-type(1) {
				width: 220rpx;
				height: 70rpx;
				line-height: 70rpx;
				text-align: center;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50rpx;
				border: 1rpx solid #ffffff;
				font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
			}

			&:nth-of-type(2) {
				width: 220rpx;
				height: 70rpx;
				line-height: 70rpx;
				text-align: center;
				background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
				border-radius: 40rpx;
				font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #141414;
			}
		}
	}

	text {
		font-weight: bold;
		font-size: 28rpx;
		color: #ffffff;
	}

	.charge {
		margin-top: 61rpx;
		width: 300rpx;
		height: 80rpx;
		background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
		border-radius: 40rpx;
		text-align: center;
		line-height: 80rpx;
		font-weight: bold;
		font-size: 24rpx;
		color: #141414;
	}
}

.head_bg {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	height: 500rpx;
	z-index: -1;
	background-color: #fff;
	will-change: transform;
	transform: translateZ(0);
}

.main {
	flex: 1;
}

.head_title {
	height: 170rpx;
	// padding:86rpx 0 0 0;
}

.title_1 {
	color: #141414;
	font-weight: 600;
	font-size: 44rpx;
}

.content {
	padding-top: 200rpx;
}

.position_top {
	position: fixed;
	top: 0rpx;
	left: 0;
	width: 100%;
	background-color: var(--main-bg-color);
	z-index: 99;
	padding: 20rpx 40rpx;
}

.head_view {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.search_view {
	position: relative;
	border-radius: 16rpx;
	border: 1px solid rgba(255, 255, 255, 0.5);
	overflow: hidden;
	width: 100%;
	.right {
		font-size: 28rpx;
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #fff;
	}
}

.tabbar_view {
	margin-top: 30rpx;
}

.list_view {
	width: 638rpx;
	margin-top: 40rpx;
}

.list_li {
	display: flex;
	flex-direction: row;
	align-items: center;
	background-color: #F5F5F5;
	padding: 32rpx;
	border-radius: 24rpx;
	margin-bottom: 40rpx;
}

.left_img {
	margin: 0 40rpx 0 0;
}

.right_font {
	&_title {
		color: #141414;
		font-size: 38rpx;
		font-weight: 600;
	}
}

.sub_title {
	&_text {
		color: $uni-color-gray;
		font-size: $uni-font-size-h4;
		line-height: 44rpx;
	}
}

.time {
	&_text {
		color: $uni-color-gray;
		font-size: $uni-font-size-h4;
		line-height: 44rpx;
	}
}

.collection {
	margin-top: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	/* #ifdef APP */
	padding-bottom: 400rpx;
	/* #endif */
	/* #ifdef H5 */
	padding-bottom: 100rpx;
	/* #endif */

	.li {
		width: 320rpx;
		height: 450rpx;
		background-color: #25232D;
		margin-bottom: 30rpx;
		border-radius: 30rpx;
		padding: 20rpx;

		.bg {
			position: relative;
			width: 288rpx;
			height: 288rpx;

			.cover {
				position: absolute;
				top: 0rpx;
				left: 0rpx;
				width: 280rpx;
				height: 280rpx;
				border-radius: 30rpx;

				>image {
					width: 280rpx;
					height: 280rpx;
					border-radius: 30rpx;
				}

				.left_bottom_icon {
					position: absolute;
					bottom: 0;
					left: -2rpx;
					background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240104/79eb4cb92625dd836b2749f657f1437d_140x44.png);
					background-size: 100% 100%;
					width: 140rpx;
					height: 44rpx;
					border-radius: 0px 30rpx 0px 30rpx;
					text-align: center;
					font-size: 20rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.right_top_icon {
					position: absolute;
					right: 0rpx;
					top: 0rpx;
					width: 110rpx;
					height: 44rpx;

					image {
						width: 110rpx;
						height: 44rpx;
					}
				}

				.tuishi {
					position: absolute;
					right: 0rpx;
					bottom: 0rpx;
					width: 137rpx;
					height: 137rpx;

					image {
						width: 137rpx;
						height: 137rpx;
					}
				}
			}
		}

		.font_view {
			padding: 12rpx 0rpx 24rpx 0rpx;

			.title {
				width: 100%;
				font-size: 24rpx;
				font-weight: 600;
				color: #fff;
			}

			.sub_title {
				font-size: 22rpx;
				color: #FFFFFF;
				margin-top: 10rpx;
			}

			.icon_price {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 10rpx;

				.left_huo {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					image {
						width: 22rpx;
						height: 22rpx;
					}
				}

				.right_price {
					color: var(--active-color1);
					font-weight: 600;
					font-size: 34rpx;
					min-width: 50%;
					text-align: right;

					text {
						font-size: 24rpx;
					}

					.qi {
						color: var(--active-color1);
					}
				}
			}
		}
	}
}

.null_body {
	.null {

		.img {
			display: flex;
			justify-content: center;

			image {
				width: 242rpx;
			}
		}

	}

	.text {
		color: #A6A6A6;
		font-size: 28rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	width:100%;
	height: 40vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

.scroll-Y {
	max-height: 80vh;

	.footerText {
		text-align: center;
		color: rgba(255, 255, 255, 0.5);
		font-size: 24rpx;
	}
}

.new_icon {
	position: absolute;
	top: 11rpx;
	left: 15rpx;

	image {
		width: 60rpx;
	}
}

.ji_icon {
	position: absolute;
	width: 120rpx;
	height: 36rpx;
	top: 0;
	right: 0;
	left: 0;
	margin: 0 auto;
	text-align: center;
	font-size: 24rpx;
	color: #DEDEDE;
	line-height: 38rpx;
	overflow: hidden;

	image {
		width: 120rpx;
		height: 36rpx;
	}
}

.dan_icon {
	position: absolute;
	width: 120rpx;
	height: 36rpx;
	top: 0;
	right: 0;
	left: 0;
	margin: 0 auto;
	text-align: center;
	font-size: 24rpx;
	color: #DEDEDE;
	line-height: 38rpx;

	image {
		width: 120rpx;
		height: 36rpx;
	}
}

.right_active {
	position: absolute;
	right: 0rpx;
	top: 20rpx;

	.li_view {
		width: 70rpx;
		height: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 20rpx;
		color: #fff;
		margin-top: 10rpx;
	}
}

.model {

	// font-family: 'fonts';
	.colse {
		image {
			position: absolute;
			right: 12rpx;
			top: 12rpx;
			width: 48rpx;
			height: 48rpx;
		}
	}

	.title {
		text-align: center;
		color: #fff;
		margin-bottom: 40rpx;
		font-size: 30rpx;
	}

	.introduce {
		color: rgba(255, 255, 255, 0.5);
		font-size: 26rpx;
		text-align: center;
		padding: 0rpx 40rpx 28rpx 40rpx;

		>view {
			margin-bottom: 14rpx;
		}
	}
}
</style>