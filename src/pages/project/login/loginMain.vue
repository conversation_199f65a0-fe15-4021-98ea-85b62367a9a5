<template>
	<view>
		<view class="head_bg_view">
			<!-- <hx-lottie ref="lottieWeb" :loop="false" :autoplay="false" :options="option1" /> -->
		</view>
		<view class="head_bg">
			<!-- <view class="logo">
				<image
					src="https://cdn.yanjie.art/image/20240122/7f179413469b95f40e89753384f1bc0a_264x131.png"
					mode="widthFix"></image>
			</view> -->
			<!-- <view class="back" @tap="nav_mall">
				<image
					src="https://cdn.yanjie.art/image/20240118/f9c205ddd8763103bfbfeebfcd60689d_200x200.png"
					mode="widthFix"></image>
			</view> -->
		</view>
		<view class="button_login">
			<view class="li phone" @click="nav_to('loginPwd')">
				<image style="width:36rpx;"
					src="https://cdn.yanjie.art/image/20240715/217ef301211e16c572560f8abd5239e1_80x65.png"
					mode="widthFix"></image>
				微信登录

			</view>
			<view class="li phone" @click="nav_to('login')">
				<image
					src="https://cdn.yanjie.art/image/20240715/e59172baa36ee0f28c826a228ab6036c_40x60.png"
					mode="widthFix"></image>
				手机号登录
			</view>
			<view class="li register" @click="nav_to('register')">
				注册
			</view>
		</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			option1: {
				path: 'https://cdn.yanjie.art/h5/xCase/head/head.json',
				loop: false,
				autoplay: true
			},
		}
	},
	onLoad(options) {
		console.log(uni.$u.config.v);
	},
	methods: {
		nav_to(name) {
			this.$Router.push({
				name
			})
		},
		nav_mall() {
			this.$Router.pushTab({
				name: "index"
			})
		},
	}
}
</script>
<style lang="scss">
page {
	background: linear-gradient(180deg, #2A2930 0%, #111111 100%);
	height: 100%;
}

.head_bg {
	height: 640rpx;
	padding-top: 440rpx;
	position: relative;

	.back {
		position: absolute;
		left: 40rpx;
		top: 40rpx;

		image {
			width: 40rpx;
		}
	}

	.logo {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 280rpx;
		}
	}
}

.button_login {
	margin-top: 244rpx;

	.li {
		width: 540rpx;
		height: 90rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		// font-weight: 400;
		color: #FFFFFF;
		margin: 0 auto;
		border-radius: 50rpx;


		font-weight: 400;
		-webkit-text-stroke: 1px rgba(0, 0, 0, 0);

		image {
			width: 20rpx;
			margin-right: 14rpx;
		}

		margin-bottom:40rpx;

		&.phone {
			background: linear-gradient(90deg, #F2AD20 0%, #F45A33 100%);

			background-color: #D8B662;
		}

		&.register {
			border: 1px solid #fff;
			color: #fff;
		}
	}
}

.head_bg_view {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	height: 500rpx;
	z-index: -1;
}
</style>