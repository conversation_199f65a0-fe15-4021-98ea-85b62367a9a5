<template>
    <view class="centent">
        <view style="height: 60rpx;"></view>
        <view class="flex_view">
            <view>
                <view class="head_bg">
                    <view class="back flex_all" @tap="nav_back()">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250303/52e1ce7cdecd6d6ae3040a8ae821b0d6_21x36.png">
                        </image>
                    </view>
                </view>
                <view class="SignUp-title flex-column">
                    <text class="SignUp-title-text">请设置密码</text>
                    <text class="SignUp-title-text2">设置登录密码</text>

                </view>
            </view>
        </view>
        <view class="register-page">
            <view class="register-container">
                <view class="input-group">
                    <view class="input-title">{{ "密码" }}</view>
                    <view class="phone-input">
                        <u-input type="password" :clearable="false" v-model="password" placeholder="" height="102"
                            class="phone-number-input" />
                    </view>
                </view>

                <view class="input-group">
                    <view class="input-title">{{ '再次输入密码密码' }}</view>
                    <view class="phone-input">
                        <u-input type="password" :clearable="false" v-model="repwd" placeholder="" height="102"
                            class="phone-number-input" />
                    </view>
                </view>

                <!-- :disabled="!isFormValid" -->
                <view class="btn">
                    <u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSignUp">
                        {{ '确定' }}
                    </u-button>
                </view>



            </view>
        </view>


    </view>
</template>

<script>
import store from "@/store/index.js"
const { VUE_APP_URL, VUE_APP_CLIENT_ID, VUE_APP_CLIENT_SECRET } = process.env;

export default {
    data() {
        return {
            repwd: "",
            isChecked: false, // 初始未选中
            uncheckedImage: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250409/406ccb4765d421b4a9d8cff5a66518d5_80x80.png", // 未勾选图片（空方框）
            checkedImage: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370356051609214976.png", // 勾选图片（绿色勾）
            region: "86",
            showRegion: false,
            password: '',
            verificationCode: '',
            agreementAccepted: true,
            isGettingCode: false,
            countdown: 59,
            timer: null,
            isRotated: false, // 控制旋转状态
            currentIndex: null, // 当前打开的项
            link: "../../../static/serve.html",
            isFixedBottom: true

        }
    },
    onReady() {
        // this.$nextTick(() => {
        // 	this.checkHeight()
        // })
    },
    onLoad() {
        console.log(VUE_APP_URL);
        uni.setNavigationBarTitle({
            title: this.$t("page.register") // 切换语言后重新设置标题
        })
    },
    computed: {
        isFormValid() {
            // return this.phoneNumber &&
            //     this.verificationCode &&
            //     this.agreementAccepted;
        }
    },
    methods: {
        checkHeight() {
            // 获取整个页面容器高度
            const query = uni.createSelectorQuery().in(this)
            query.select('.centent').boundingClientRect()
            query.selectViewport().boundingClientRect()
            query.exec(res => {
                const pageHeight = res[0]?.height
                const screenHeight = res[1]?.height
                console.log(pageHeight, screenHeight);
                // this.isFixedBottom = pageHeight < screenHeight
                this.isFixedBottom = screenHeight >= 933
            })
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        nav_to(e, name) {
            this.$Router.push({
                name: e,
                params: {
                    phoneNumber: name
                }
            })
        },
        toggleCheckbox() {
            this.isChecked = !this.isChecked; // 点击时切换状态
        },
        handleRight(item) {
            this.region = item.value
            this.showRegion = false // 切换显示状态

        },

        toggleRotate() {
            this.showRegion = !this.showRegion; // 切换显示状态
            this.isRotated = !this.isRotated; // 点击时切换状态
        },
        nav_back() {
            this.$Router.back()
        },
        async handleSignUp() {
            // 校验密码是否为空
            if (!this.password) {
                uni.showToast({
                    title: '请输入密码',
                    icon: 'none'
                });
                return;
            }

            // 校验确认密码是否为空
            if (!this.repwd) {
                uni.showToast({
                    title: '请再次输入密码',
                    icon: 'none'
                });
                return;
            }

            // 校验两次密码是否一致
            if (this.password !== this.repwd) {
                uni.showToast({
                    title: '两次输入的密码不一致',
                    icon: 'none'
                });
                return;
            }

            try {
                let res = await this.$api.setLoginPassword({
                    password: this.password
                });

                if (res.code === 200) {
                    uni.showToast({
                        title: '密码设置成功',
                        icon: 'success'
                    });

                    // 延迟返回上一页，让用户看到成功提示
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                } else {
                    uni.showToast({
                        title: res.msg || '密码设置失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('设置密码出错:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.centent {
    min-height: 100vh;
    position: relative;
    // padding-bottom: 80rpx;

    .fixed {
        position: fixed;
        bottom: 0;
    }

    .bottom-text {
        // position: fixed;
        // bottom: 0;
        height: 84*2rpx;
        width: 100vw;
        // opacity: 0.3;
        border-top-left-radius: 30*2rpx;
        border-top-right-radius: 30*2rpx;
        background: rgba(217, 214, 214, .3);

        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 16*2rpx;
        line-height: 19.2*2rpx;
        color: #666666;
    }

    .register-page {
        padding: 60rpx 0 0 0;

        .register-container {
            // padding: 0 32rpx;
        }

        .input-group {
            margin-bottom: 16px;
            padding: 0 32rpx;

            .input-title {
                font-family: Gilroy-SemiBold;
                line-height: 19.2*2rpx;
                color: #000;
                margin-bottom: 20rpx;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 120%;

            }

            .phone-input {
                display: flex;
                border-radius: 8px;
                // overflow: hidden;
                position: relative;

                .helpoption {
                    width: 85*2rpx;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 122rpx;
                    left: 0;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;
                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                .country-code {
                    // overflow: hidden;
                    width: 85*2rpx;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    border-width: 2rpx;
                    // padding: 16px;
                    border: 2rpx solid #999999;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;


                    display: flex;
                    align-items: center;



                    // padding: 12px 16px;
                    // border-right: 1px solid #e5e7eb;
                    // font-size: 14px;
                    // color: #374151;
                    // gap: 8px;
                    .arrow {
                        /* 图片宽度 */
                        /* 图片高度 */
                        transition: transform 0.3s ease;
                        /* 动画效果：0.3秒平滑旋转 */
                    }

                    .rotated {
                        transform: rotate(180deg);
                        /* 旋转180度 */
                    }

                    image {
                        margin-left: 22rpx;
                        width: 28rpx;
                        height: 14rpx;
                    }
                }

                .phone-number-input {
                    border: 2rpx solid #999999 !important;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    // margin-left: 20rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                    padding: 0 32rpx !important;
                }
            }

            .verification-input {
                height: 51*2rpx;
                display: flex;
                border: 2rpx solid #999999 !important;
                border-radius: 10*2rpx;
                padding: 0 32rpx !important;
                overflow: hidden;
                position: relative;

                .verification-code-input {
                    flex: 1;
                    padding: 12px 16px;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                }

                .get-code-btn {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    color: #000;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;
                    // background: #008E28;

                    text-decoration: underline;

                    &:disabled {
                        // background: #FF82A3;
                    }
                }

                .get-code-btn2 {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;
                    background: rgba(255, 155, 181, .1);
                    color: #FF82A3;

                }
            }
        }

        .btn {
            width: 100vw;
            height: fit-content;
            position: fixed;
            bottom: 80rpx;
            padding: 0 32rpx;

            // margin: 0 32rpx;
            .signup-btn {
                width: 100%;
                background-color: #FF82A3;
                color: white;
                border: none;
                border-radius: 64*2rpx;
                height: 100rpx;
                font-family: Gilroy-Bold;
                font-weight: 500;
                font-size: 28rpx;


                &:disabled {
                    // background-color: #e5e7eb;
                }
            }
        }



        .divider {
            text-align: center;
            position: relative;
            margin: 42rpx 0;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                width: calc(50% - 100rpx);
                height: 2rpx;
                background-color: #e5e7eb;
            }

            &::before {
                left: 0;
            }

            &::after {
                right: 0;
            }

            text {
                padding: 0 7rpx;
                color: #6b7280;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                text-align: center;

            }
        }

        .form-item {
            .label {
                margin-left: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                // line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .icon_serve {
                width: 40rpx;
                height: 40rpx;
            }
        }

        .google-btn {
            height: 50*2rpx;
            border-radius: 64*2rpx;
            border-width: 1*2rpx;

            width: 100%;
            // padding: 14px;
            background-color: white;
            border: 2rpx solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20rpx;
            color: #374151;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;

            image {
                width: 52rpx;
                height: 52rpx;
            }
        }


    }

    .flex_view {
        margin: 0 32rpx;

        .SignUp-title {
            margin-top: 30rpx;

            .SignUp-title-text {
                font-family: Gilroy-Bold;
                color: #000;
                font-weight: 600;
                font-size: 52rpx;
                line-height: 120%;
            }

            .SignUp-title-text2 {
                margin-top: 20rpx;
                font-family: Gilroy-Medium;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                margin-top: 17rpx;
                line-height: 120%;
                color: #000;

            }
        }


        .head_bg {
            // height: 600rpx;
            // padding-top: 200rpx;
            // position: relative;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            z-index: 0;

            .back {
                // position: absolute;
                left: 40rpx;
                background: #F1F1F1;
                border-radius: 50%;
                width: 100rpx;
                height: 100rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                /* #ifdef APP */
                top: 80rpx;
                /* #endif */
                /* #ifdef H5 */
                top: 40rpx;

                /* #endif */
                image {
                    // padding: 25rpx;
                    width: 15rpx;
                    height: 25rpx;
                }
            }

            .logo {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 400rpx;
                border: 2rpx solid #ccc;

                image {
                    height: 400rpx;
                }
            }
        }

    }

    /* 动画效果 */
    .expand-slide-enter-active,
    .expand-slide-leave-active {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    /* 打开时的初始状态，缩小并从右上角开始 */
    .expand-slide-enter {
        opacity: 0;
        // transform: scale(0.8) translate(50%, -50%);
        transform: scaleY(1) translateY(0%);

    }

    /* 关闭时的最终状态，收缩回到右上角 */
    .expand-slide-leave-to {

        opacity: 0;
        // transform: scale(0.5) translate(50%, -50%);
        transform: scaleY(0) translateY(-100%);

    }

}
</style>