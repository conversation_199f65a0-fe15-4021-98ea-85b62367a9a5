<template>
    <view>
        <view class="center">
            <image
                src="https://cdn.yanjie.art/image/20240725/6f484a968ae12652cdc68d8e18e37f77_200x126.png" />
            <text>让数藏“哇”出新高度</text>
        </view>


        <view class="download">
            <view>
                <image
                    src="https://cdn.yanjie.art/image/20240725/421be2f4ac53b10cf24c0a00270da2ed_64x79.png" />
                <text>iOS版本下载</text>
            </view>
            <view>
                <image
                    src="https://cdn.yanjie.art/image/20240725/7ea4d7495ff67ebd29187f8e64d72b28_72x77.png" />
                <text>安卓版本下载</text>
            </view>

        </view>
    </view>
</template>

<script>
export default {

}
</script>

<style lang="scss" scoped>
.download {
    position: fixed;
    bottom: 100rpx;
    padding: 0 50rpx;
    display: flex;
    width: 100%;
    flex-direction: column;

    >view {
        height: 100rpx;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50rpx;
        border: 1rpx solid #FAFAFA;
        display: flex;
        justify-content: center;
        align-items: center;
        &:nth-of-type(1){
            margin-bottom: 60rpx;
        }
        image {
            width: 32rpx;
            height: 39rpx;
        }

        text {
            margin-left: 10rpx;
            font-weight: 400;
            font-size: 36rpx;
            color: #FFFFFF;
        }
    }
}

page {
    background-image: url("https://cdn.yanjie.art/image/20240725/ad7b921f9fc5519e69c36381e78d4fc5_1500x3248.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    height: 100vh;
    width: 100vw;
    display: flex;
    // justify-content: center;
    align-items: center
}

.center {
    padding-left: 219rpx;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    image {
        width: 200rpx;
        height: 126rpx;
        // margin-bottom: 40rpx;
    }

    text {
        font-weight: 300;
        font-size: 28rpx;
        color: #FFFFFF;
    }
}

</style>