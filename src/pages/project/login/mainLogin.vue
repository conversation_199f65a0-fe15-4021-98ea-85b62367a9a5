<template>
	<view class="index_body">
		<view class="left_back" @click="nav_index">
			<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
		</view>
		<view class="contents">
			<view>
				<view class="index_body_img">
					<image src="@/static/login/logo.png" mode="widthFix"></image>
					<view class="index_body_text">给数藏一片净土</view>
				</view>
			</view>
			<!--操作 按钮-->
			<view class="index_body_btn">
				<!-- #ifdef APP -->
				<view class="btn_wx" @click="wxLogin" v-show="showWxLogin">
					<image src="@/static/login/wx.png" mode="heightFix"></image>
					<text>微信登录</text>
				</view>
				<!-- #endif -->
				<view class="btn_sj" @click="toappPhoneLogin">
					<image src="@/static/login/sj.png" mode="heightFix"></image>
					<text>手机号一键登录</text>
				</view>
				<view class="btn_sj" @click="moreLogin">
					<image src="@/static/login/userLogin.png" mode="heightFix"></image>
					<text>账号密码登录</text>
				</view>
				<view class="btn_zc" @click="toRegister">
					<image src="@/static/login/userLogin.png" mode="heightFix"></image>
					<text>注册</text>
				</view>
				<!-- <view class="btn_zc" @click="setSgin()">
					<text>设置密钥</text>
				</view>
				<view class="btn_zc" @click="check()">
					<text>环境检查</text>
				</view>
				<view class="btn_zc" @click="getLoginToken_ali()">
					<text>一键登录</text>
				</view> -->
			</view>
		</view>
	</view>

</template>

<script>
// #ifdef APP
var aLiSDKModule = uni.requireNativePlugin('AliCloud-NirvanaPns')
let platform = uni.getSystemInfoSync().platform
import uiConfig from '@/common/ui-config.js'
// #endif
export default {
	name: "index",
	data() {
		return {
			authUiConfig: '', // 调起的页面样式配置项，官方有专门的修改授权页主题配置参数
			obj: {},
			platform: '',
			showWxLogin: false
		}
	},
	onLoad(options) {
		this.platform = uni.getSystemInfoSync().platform
		if (options.url === undefined) {
			this.return_url = ""
		} else {
			this.return_url = options.url
			if (/^#/.test(options.url)) {
				this.return_url = `/h5/${options.url}`;
				console.log(this.return_url)
			} else {
				// this.return_url = options.url;
			}
		}
		this.get_version()
	},
	methods: {
		async wxLogin() { //微信登录 
			uni.removeStorageSync('wx_access_token');
			uni.removeStorageSync('wx_openid');
			try {
				const loginRes = await uni.login({
					provider: 'weixin',
					onlyAuthorize: true,
					success: async (res) => {
						if (res.code) {
							const accessTokenInfo = await this.getWxAccessToken(res.code);

							// 保存用户信息到 vuex 或本地存储，跳转到首页等操作...
							console.log('登录成功，用户信息:', userInfo);
						} else {
							console.error('微信登录失败，未获取到 code');
						}
					},
					fail: (err) => {
						console.error('微信登录失败:', err);
					},
				});
			} catch (error) {
				console.error('微信登录异常:', error);
			}
		},
		async getWxAccessToken(code) {
			const wxApiUrl = 'https://api.weixin.qq.com/sns/oauth2/access_token';
			const params = {
				appid: 'wxad69c7f16fdcf595', // 替换为您的微信 AppID
				secret: '3fcaeac966a12a7ec20c2af681e8e866', // 替换为您的微信 AppSecret
				code,
				grant_type: 'authorization_code',
			};

			const response = await uni.request({
				url: wxApiUrl,
				method: 'GET',
				data: params,
			});
			console.log(response)
			console.log(response[1].statusCode)
			if (response[1].statusCode === 200) {
				console.log(response[1].data.access_token)
				this.getWxUserInfo(response[1].data.access_token, response[1].data.openid);
				return response[1].data;
			} else {
				throw new Error(`获取微信 access_token 失败: ${JSON.stringify(response.data)}`);
			}
		},

		async getWxUserInfo(accessToken, openid) {
			const wxUserInfoUrl = 'https://api.weixin.qq.com/sns/userinfo';
			const params = {
				access_token: accessToken,
				openid,
				lang: 'zh_CN',
			};
			const response = await uni.request({
				url: wxUserInfoUrl,
				method: 'GET',
				data: params,
			});
			console.error(response[1])
			if (response[1].statusCode === 200) {
				let data = response[1].data
				this.obj = {
					openid: data.openid,
					unionId: data.unionid,
					nickname: data.nickname,
					avatar: data.headimgurl
				}
				this.apiLogin()
			} else {
				throw new Error(`获取微信用户信息失败: ${JSON.stringify(response.data)}`);
			}
		},
		async apiLogin() {
			let {
				result,
				status
			} = await this.$api.java_wechatLogin({
				...this.obj
			})
			uni.showToast({
				title: status.msg,
				icon: 'none'
			})
			console.log(result, status, '登录请求');
			if (status.code == 0) {
				uni.setStorageSync(
					'token', result.accessToken
				)
				this.$Router.pushTab({
					name: 'index'
				})
			} else if (status.code == 1005) {

			} else if (status.code == 120001) {
				// 微信 还没有绑定手机号
				uni.navigateTo({
					url: '/pages/project/login/bindPhone?obj=' + JSON.stringify(this.obj)
				})
			}

		},
		// 手机号注册
		toRegister() {
			this.$Router.push({
				name: 'register',
				params: {
					url: this.return_url
				}
			})
		}, // 账号密码登录
		moreLogin() {
			this.$Router.push({
				name: 'moreLogin',
				params: {
					url: this.return_url
				}
			})
		},
		// tologinAndRegister() {
		// 	uni.navigateTo({
		// 		url: "/pages/project/login/registerAndLogin"
		// 	})
		// },
		// app手机一键登录
		toappPhoneLogin() {
			// #ifdef H5
			this.$Router.push({
				name: 'h5PhoneLogin',
				params: {
					url: this.return_url
				}
			})
			// #endif
			// #ifdef APP-PLUS
			this.setSgin()
			// #endif
		},
		setSgin() {
			let sdkInfo =
				'sKIuo1xT7Y3NelNGKWxAAiMoDKiolEFRiLrMOd/t7X09Wu987lu4B4XF3a8/zuVx1vTqm27exguvERd61E6D8SAdUbdz1cTYge4x2hiI0alnrim0V+hpsob8reIleSuu8Aud5sOH4PjQWgdSnBxGUOR1HlFWeTfI8o2Z/l5wEae7aDSu7QGRqkZqtYYgNVBtm+MyO0DEdmUE0rY8+ax1Gt7dgGrYYUuXTJTlt9ZPZTNfcbyoEBimJsAB4KqmNMTibozkOu0i/HrFotbJXLNz24yDJldtwmd3tEvdWgwvJsc+eNpBe4vD7w==';
			//设置秘钥
			if (this.platform == 'android') {
				sdkInfo =
					'sKIuo1xT7Y3NelNGKWxAAiMoDKiolEFRiLrMOd/t7X09Wu987lu4B4XF3a8/zuVx1vTqm27exguvERd61E6D8SAdUbdz1cTYge4x2hiI0alnrim0V+hpsob8reIleSuu8Aud5sOH4PjQWgdSnBxGUOR1HlFWeTfI8o2Z/l5wEae7aDSu7QGRqkZqtYYgNVBtm+MyO0DEdmUE0rY8+ax1Gt7dgGrYYUuXTJTlt9ZPZTNfcbyoEBimJsAB4KqmNMTibozkOu0i/HrFotbJXLNz24yDJldtwmd3tEvdWgwvJsc+eNpBe4vD7w==';
				aLiSDKModule.setAuthSDKInfo(sdkInfo);
				console.log("设置秘钥完成");
				this.getLoginToken_ali()
			} else if (platform == 'ios') {
				sdkInfo =
					'sKIuo1xT7Y3NelNGKWxAAiMoDKiolEFRiLrMOd/t7X09Wu987lu4B4XF3a8/zuVx1vTqm27exguvERd61E6D8SAdUbdz1cTYge4x2hiI0alnrim0V+hpsob8reIleSuu8Aud5sOH4PjQWgdSnBxGUOR1HlFWeTfI8o2Z/l5wEae7aDSu7QGRqkZqtYYgNVBtm+MyO0DEdmUE0rY8+ax1Gt7dgGrYYUuXTJTlt9ZPZTNfcbyoEBimJsAB4KqmNMTibozkOu0i/HrFotbJXLNz24yDJldtwmd3tEvdWgwvJsc+eNpBe4vD7w==';
				aLiSDKModule.setAuthSDKInfo(sdkInfo, result => {
					console.log(result);
					if (result.resultCode == '600000') {
						console.log("设置成功");
						this.getLoginToken_ali()
					} else {
						uni.navigateTo({
							url: "/pages/project/login/h5PhoneLogin"
						})
						console.log("设置失败：" + result.msg);
					}
				});
			}
		},
		check() {
			aLiSDKModule.checkEnvAvailable(1, result => {
				console.log(result);
				if (result.resultCode == '600000') {
					console.log("环境检查成功，可用");
				} else {
					console.log("环境检查失败，不可用，原因：" + result.msg);
				}
			});
		},

		/**
		 * 阿里云一键登录 阿里云号码认证SDK
		 * **/
		getLoginToken_ali() {
			// 这里我是用了一个按钮触发这个方法，你也可以选择页面加载后就执行。
			let that = this
			const config = uiConfig.buildFullscreen();
			aLiSDKModule.accelerateLoginPage(5000, result => {
				console.log(result.resultCode)
				if (result.resultCode == '600008') {
					this.$Router.push({
						name: 'h5PhoneLogin',
					})
					return false
				}
				if ("600000" == result.resultCode) {
					console.log("加速成功")
					aLiSDKModule.getLoginToken(
						5000,
						config,
						tokenResult => {
							console.log(tokenResult, 'tokenResult-TOKEN');
							if ("600001" == tokenResult.resultCode) {
								console.log("授权页拉起成功")
							} else if ("600000" == tokenResult.resultCode) {
								console.log("获取Token成功，接下来拿着结果里面的Token去服务端换取手机号码，SDK服务到此结束")
								this.submitOneClickLoginRegister(tokenResult.token, '')
								//手动关闭授权页
								aLiSDKModule.quitLoginPage()
							} else {
								//其他失败情况，手动关闭授权页
								aLiSDKModule.quitLoginPage()
							}
						},
						clickResult => {
							switch (clickResult.resultCode) {
								case "700000":
									console.log("用户点击返回按钮")
									break
								case "700001":
									console.log("用户切换其他登录方式")
									break
								case "700002":
									console.log("用户点击登录按钮")
									break
								case "700003":
									console.log("用户点击checkBox")
									break
								case "700004":
									console.log("用户点击协议")
									break
							}
						},
						customUiResult => {
							//这里回调的是自定义控件的点击事件，通过 customUiResult.widgetId 来识别自定义控件，然后做一些自己的处理
						}
					)
				}
			})

		},
		async getMobile(accessToken) {
			let res = await this.$api.getMobile({
				accessToken
			});
			if (res.status.code == 0) {
				this.submitOneClickLoginRegister(accessToken, res.result.phoneNum)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async submitOneClickLoginRegister(accessToken, phoneNum) {
			console.log(accessToken, phoneNum);
			let res = await this.$api.oneClickLoginRegister({
				accessToken,
				phoneNum,
				registerPoint: 'App',
				spToken: ''
			});
			console.log(res)
			if (res.status.code == 0) {
				console.log(res.result)
				if (res.result.loginToken) {
					uni.setStorageSync("token", res.result?.loginToken)
					this.$u.toast('登录成功');
					// #ifdef APP
					this.bindDevice()
					// #endif
					this.$Router.pushTab({
						name: 'index'
					})
				} else {
					this.$Router.push({
						name: 'h5PhoneLogin',
					})
					return false
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async bindDevice() {
			let appType = uni.getSystemInfoSync().platform
			console.error(appType, uni.getStorageSync('deviceToken'))
			let res = await this.$api.bindDevice({
				appType: appType == 'ios' ? 2 : 1,
				deviceId: uni.getStorageSync('deviceToken')
			});
			if (res.status.code == 0) {
				console.log(res)
			} else {
				console.log(res)
			}
		},
		async get_version() {
			let res = await this.$api.java_commonconfigInfo({
				name: 'ios_apple_pay_version',
			});
			if (res.status.code == 0) {

				let curV = uni.getSystemInfoSync().appVersion
				let reqV = res.result.value
				// console.log(curV)
				// console.log(reqV)
				if (curV == reqV) {
					if (uni.getSystemInfoSync().platform == 'ios') {
						this.showWxLogin = false
					} else {
						this.showWxLogin = true
					}
				} else {
					this.showWxLogin = true
				}
				// console.log(this.showWxLogin)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		/**
		 * 比较两个UniApp版本号字符串
		 * @param {string} curV - 当前版本号字符串（例如 "2.0.1"）
		 * @param {string} reqV - 待比较版本号字符串（例如 "1.2.3"）
		 * @returns {boolean} - 如果当前版本号等于或高于待比较版本号，返回true；否则返回false
		 */
		compareVersions(curV, reqV) {
			const partsCur = curV.split('.').map(Number);
			const partsReq = reqV.split('.').map(Number);

			for (let i = 0; i < Math.max(partsCur.length, partsReq.length); i++) {
				const curPart = partsCur[i] || 0;
				const reqPart = partsReq[i] || 0;

				if (curPart > reqPart) {
					return true;
				} else if (curPart < reqPart) {
					return false;
				}
			}

			// 如果所有对应部分都相等，则当前版本等于待比较版本，视为满足条件
			return true;
		},
		nav_index() {
			this.$Router.pushTab({
				name: "index"
			})
		}
	}
}
</script>

<style scoped lang="scss">
.contents {
	justify-content: flex-end;
	display: flex;
	flex-direction: column;
	height: 100%;
}

.index_body {
	background: var(--login-bg-color);
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	max-height: 1624rpx;

	// padding-top: 24vh;
	// justify-content: center;
	.left_back {
		position: absolute;
		left: 40rpx;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 50rpx;

		/* #endif */
		image {
			width: 50rpx;
		}
	}

	.index_body_img {
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
		width: auto;
		height: 108.14rpx;

		image {
			width: 340rpx;
			height: 59.65rpx;
		}

		.index_body_text {
			padding: 5rpx 0 0 18rpx;
			font-size: 32rpx;
			font-weight: 300;
			letter-spacing: 12.8rpx;
			color: rgba(255, 255, 255, 1);
			text-align: center;
			font-family: HarmonyOS Sans SC;
		}

	}

	.index_body_btn {
		// position: fixed;
		// bottom: 100rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		// margin-top: 386rpx;
		margin-top: 426rpx;

		.btn_wx {
			margin-bottom: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 540rpx;
			height: 90rpx;
			border-radius: 50rpx;
			background: linear-gradient(180deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);

			image {
				width: 40rpx;
				height: 32.24rpx;
				opacity: 1;

			}

			text {
				font-size: 28rpx;
				font-weight: 400;
				letter-spacing: 0px;
				line-height: 37rpx;
				color: rgba(20, 20, 20, 1);
				text-align: center;
				vertical-align: top;
				font-family: HarmonyOS Sans SC;
				margin-left: 14rpx;

			}
		}

		.btn_sj {
			margin-bottom: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 540rpx;
			height: 90rpx;
			border-radius: 50rpx;
			background: linear-gradient(180deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);

			image {
				width: 20rpx;
				height: 30rpx;
				opacity: 1;
			}

			text {
				font-size: 28rpx;
				font-weight: 400;
				letter-spacing: 0px;
				line-height: 37rpx;
				color: rgba(20, 20, 20, 1);
				text-align: center;
				vertical-align: top;
				font-family: HarmonyOS Sans SC;
				margin-left: 14rpx;

			}
		}

		.btn_zc {
			width: 540rpx;
			height: 90rpx;
			opacity: 1;
			border-radius: 50rpx;
			background: rgba(53, 51, 62, 1);
			border: 2rpx solid rgba(99, 234, 238, 1);
			/** 文本1 */
			font-size: 28rpx;
			font-weight: 400;
			letter-spacing: 0px;
			line-height: 90rpx;
			color: rgba(99, 234, 238, 1);
			text-align: center;
			vertical-align: top;
			margin-bottom: 100rpx;

		}
	}
}
</style>