<template>
    <view class="page-container">
        <!-- 返回按钮 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="忘记密码">
        </u-navbar>

        <!-- 手机号输入 -->
        <view class="input-section">
            <view class="input-item">
                <view class="label">手机号：</view>
                <!-- <u-select v-model="countryCode" :range="countryCodes" class="country-select">
                    <view class="country-code">{{ countryCode }}</view>
                </u-select> -->
                <u-input border v-model="phoneNumber" placeholder="请输入手机号" type="number" maxlength="11"></u-input>
            </view>

            <!-- 验证码输入 -->
            <view class="input-item">
                <view class="label">验证码：</view>
                <u-input border v-model="verificationCode" placeholder="请输入验证码" maxlength="6"></u-input>
                <view class="right" :class="[status == 0 ? 'inactive' : 'active']" @tap="getCode">
                    {{ tips }}
                </view>
                <u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end"
                    @start="start" ref="uCode" @change="codeChange"></u-verification-code>
                <!-- <u-button class="get-code-btn" type="text" @click="getVerificationCode">获取</u-button> -->
            </view>

            <!-- 设置登录密码输入 -->
            <view class="input-item">
                <view class="label">设置登录密码：</view>
                <u-input border v-model="password" placeholder="请输入新密码" password :maxlength="16"
                    :show-clear="true"></u-input>
            </view>
            <view class="password-info">8位长度，至少包含1个小写字母和1个大写字母；</view>

            <!-- 操作按钮 -->
            <u-button hover-class="none" class="reset-btn" type="primary" @click="resetPassword">重设密码</u-button>
            <u-button hover-class="none" class="cancel-btn" type="default" @click="goBack">取消</u-button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            countryCode: '+86', // 默认国家码
            phoneNumber: '', // 手机号
            seconds: 60,
            status: 1,
            changeText: '重新获取(x)',
            tips: '',
            verificationCode: '', // 验证码
            password: '', // 新密码
            countryCodes: ['+86', '+1', '+44'], // 国家码列表
        };
    },
    methods: {
        codeChange(text) {
            this.tips = text;
        },
        getCode() {
            if (this.$refs.uCode.canGetCode) {
                // 模拟向后端请求验证码
                if (this.$u.test.mobile(this.phone)) {
                    uni.showLoading({
                        title: '正在获取验证码'
                    })
                    this.sendPhoneVerifyCode()
                } else {
                    this.$u.toast('请输入手机号再获取');
                }

            } else {
                this.$u.toast('倒计时结束后再发送');
            }
        },
        end() {
            this.status = 0
        },
        start() {
            this.status = 1
        },
        goBack() {
            uni.navigateBack();
        },
        getVerificationCode() {
            console.log('获取验证码');
            // 在此可以添加获取验证码的逻辑
        },
        resetPassword() {
            console.log('重设密码');
            // 在此可以添加重设密码的逻辑
        },
    },
};
</script>

<style scoped lang="scss">
.page-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.back-btn {
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

.input-section {
    margin-top: 20px;
}

.input-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .right {
        width: 160rpx;
        height: 70rpx;
        background-color: #fff;
        margin-left: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 400;
        color: #e6f0ff;
        background: #2b85e4;
        border-radius: 10rpx;
        // &.active {
        // 	background-image: url(https://cdn.yanjie.art/image/20241023/440a49818a3ea497ed29d11f48fcdfbf_220x90.png);
        // 	background-size: 100%;
        // }

        // &.inactive {
        // 	background-image: url(https://cdn.yanjie.art/image/20241023/d6b3804438563c7aa609712ddbc026ec_220x90.png);
        // 	background-size: 100%;
        // }
    }
}

.label {
    font-size: 14px;
    color: #666;
}

.country-select {
    width: 80px;
}

.u-select {
    margin-bottom: 10px;
}

.get-code-btn {
    position: absolute;
    right: 10px;
    top: 6px;
    font-size: 14px;
    color: #007aff;
}

.password-info {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.reset-btn {
    margin-top: 20px;
    width: 100%;
}

.cancel-btn {
    margin-top: 10px;
    width: 100%;
    background-color: #ffffff;
    color: #007aff;
    border: 1px solid #007aff;
}
</style>