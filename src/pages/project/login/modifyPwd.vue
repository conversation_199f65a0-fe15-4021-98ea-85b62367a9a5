<template>
	<view class="main">
		<u-verification-code :seconds="60" ref="uCode" @change="codeChange" change-text="重新获取 (xs)"
			@start="startStyle = true" @end="startStyle = false"></u-verification-code>
		<u-navbar back-icon-color="var(--default-color1)" title-color="var(--main-text-color)" title-bold
			:border-bottom="false" :background="{backgroundColor: 'var(--main-bg-color)'}">
		</u-navbar>
		<!-- 顶部提示 -->
		<u-top-tips ref="warningTips"></u-top-tips>
		<!-- toast提示 -->
		<u-toast ref="uToast" />

		<view class="content">
			<view class="title">
				{{type === 'change' ? '修改' : '忘记'}}密码
			</view>
			<view class="form">
				<u-form :model="form" ref="uForm">
					<u-form-item>
						<u-input v-model="form.account" placeholder="请输入邮箱号/手机号" @click="showFooter = false"
							@blur="blurFooter()" :disabled="disabled" />
						<view slot="right" v-if="showWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="请输入正确的手机号或邮箱号"></tooltip>
						</view>
					</u-form-item>
					<u-form-item>
						<u-input v-model="form.verifyCode" placeholder="请输入验证码" @click="showFooter = false"
							@blur="blurFooter()" />
						<span slot="right" class="get-code"
							:style="{color:startStyle ? 'var(--info-front-color)' : 'var(--active-color1)'}"
							@click="startCode">{{ tips }}</span>
					</u-form-item>
					<u-form-item>
						<u-input type="password" password-icon v-model="form.password" placeholder="请输入密码"
							@click="showFooter = false" @blur="blurFooter()" />
						<view slot="right" v-if="showPwdWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="密码长度6位以上"></tooltip>
						</view>
					</u-form-item>
					<u-form-item>
						<u-input type="password" password-icon v-model="form.rePassword" placeholder="请确认新密码"
							@click="showFooter = false" @blur="blurFooter()" />
						<view slot="right" v-if="showRePwdWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="两次密码输入不一致"></tooltip>
						</view>
					</u-form-item>
				</u-form>
			</view>
			<view class="submit">
				<button-bar @click="forgetPassword()">确认</button-bar>
			</view>

		</view>
		<view class="footer"  @click="isAgree = !isAgree">
			<u-image mode="widthFix" width="28rpx"
				:src="`../../../static/login/${isAgree?'jxs2x':'jx'}.png`">
			</u-image>
			<view class="msg">
				我已阅读并同意
				<text @click="nav_link('Bigverse平台服务协议',1)">《Bigverse平台服务协议》</text>
				和
				<text @click="nav_link('法律声明及隐私政策',2)">《法律声明及隐私政策》
				</text>
			</view>
		</view>
		<!-- <view class="footer" v-show="showFooter">
			收不到验证码?请发邮件至官方邮箱
			<view class="active"><EMAIL></view>
		</view> -->
	</view>
</template>

<script>
	import tooltip from "@/components/public/Tooltip";
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		components: {
			tooltip,
			ButtonBar
		},
		data() {
			return {
				form: {
					email: '',
					verification: '',
					newPwd: ''
				},
				showWarn: false,
				showRePwdWarn: false,
				showPwdWarn: false,
				startStyle: '',
				tips: '获取验证码',
				repeatPwd: '',
				is: {
					isShowVerificationTip1: true,
					isShowVerificationTip2: false
				},
				type: '',
				timeout: 60,
				showFooter: true,
				disabled: false,
				isAgree: false,
			};
		},
		watch: {
			'form.account'() {
				this.showWarn = false
			},
			'form.password'() {
				this.showPwdWarn = false
				this.showRePwdWarn = false
			},
			'form.rePassword'() {
				this.showRePwdWarn = false
			},
		},
		onLoad(options) {
			this.type = options.type;
			if (this.type == 'change') {
				if (options.mode == 'email') {
					this.form.account = uni.getStorageSync("email")
				} else {
					this.form.account = uni.getStorageSync("phone")
				}
				this.disabled = true
				this.startCode()
			}
		},
		methods: {
			blurFooter() {
				this.showFooter = true
			},
			back() {
				if (this.type === 'change') {
					this.$Router.back(1);
				} else {
					this.$Router.back(1);
					// this.$Router.pushTab({
					// 	name: "mainLogin"
					// })
				}
			},
			codeChange(text) {
				this.tips = text;
			},
			// 判断账号类型 获取验证码
			async startCode() {
				if (!this.startStyle) {
					const {
						form: {
							account
						},
						$u: {
							test
						}
					} = this;
					let isEmail = test.email(account),
						isPhone = test.mobile(account);
					if (!isEmail && !isPhone) {
						this.showWarn = true;
						return;
					}
					if (isEmail) {
						await this.codeAction('email', {
							emailAddress: account,
							emailType: "FORGET_PASSWORD"
						});
					} else if (isPhone) {
						await this.codeAction('phone', {
							mobPhone: account,
							aliYumSmsType: "FORGET_PASSWORD"
						});
					}
				}
			},
			// 获取验证码
			async codeAction(type, obj) {
				let url = {
					email: 'java_sendAliYunEmail',
					phone: 'java_sendAliYunSms',
				}
				let {
					status: {
						code,
						msg
					}
				} = await this.$api[url[type]](obj)
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.$refs.uCode.start();
				}
			},
			PasswordStrength(pwd) {
				if (pwd.length >= 6) {
					return true
				}
			},
			// 找回密码接口
			async forgetPassword() {
				const {
					form: {
						account,
						verifyCode,
						password,
						rePassword
					},
					$u: {
						test
					},
					PasswordStrength
				} = this;
				// 校验账号密码正则
				let pwdReg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,16}$/s,
					character = password?.replace(/[\da-zA-Z]/g, '') || ''
				// 校验账号是否为手机号或者邮箱号
				let isEmail = test.email(account),
					isPhone = test.mobile(account);
				if (!isEmail && !isPhone) {
					this.showWarn = true;
					return;
				}
				// 正则校验账号密码是否满足要求
				if (!PasswordStrength(password)) {
					this.showPwdWarn = true
					return
				}
				// 校验两次密码是否一致
				if (rePassword !== password) {
					this.showRePwdWarn = true
					return
				}
				// 判断使用手机号接口还是邮箱接口
				let url = isEmail ? 'java_appUserForgetPasswordVerifyEmailCode' :
					'java_appUserForgetPasswordVerifyPhoneSmsCode';
				let obj = {
					code: verifyCode,
					password
				}
				// 判断字段
				obj[isEmail ? 'email' : 'mobPhone'] = account;
				// 发送请求
				if (this.isAgree) {
					let {
						result,
						status: {
							code,
							msg
						}
					} = await this.$api[url](obj)
					if (code !== 0) {
						this.$refs.uToast.show({
							title: msg,
							type: 'error',
						})
					} else {
						this.$refs.uToast.show({
							title: msg,
							type: 'success',
						})
						uni.setStorageSync("token", result?.accessToken)
						uni.removeStorageSync("posterCover")
						if (this.type === 'change') {
							this.$Router.back(1);
						} else {
							setTimeout(() => {
								this.$Router.pushTab({
									name: 'index'
								});
							}, 1500)
						}
					}
				} else {
					this.$refs.uToast.show({
						title: "请先勾选协议",
						type: 'error',
					})
				}

			},
			nav_link(title, index) {
				if (index === 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com.cn/link/#/pages/index/userAgreement"
						}
					})
				} else {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com.cn/link/#/pages/index/PrivacyAgreement"
						}
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		.title {
			color: var(--default-color1);
			font-size: 44rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: bold;
			text-align: center;
		}

		.content {
			margin: 40rpx 80rpx 0;

			.form {
				margin-top: 70rpx;
				margin-bottom: 64rpx;

				::v-deep {
					.u-input__input {
						color: var(--default-color1);
					}
				}

				.uni-input-placeholder {
					color: #BCBEC8 !important;
					font-size: 28rpx;
				}

				.u-border-bottom::after {
					border: none;
					border-bottom: 1px solid var(--form-border-color);
				}
			}

			.forget {
				margin-top: 28rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: var(--secondary-front-color);
				text-align: center;
			}
		}

		.footer {
			position: fixed;
			bottom: 155rpx;
			left: 0rpx;
			margin: auto 10%;
			display: flex;
			justify-content: center;

			image {
				width: 30rpx;
			}

			.u-image {
				margin-right: 10rpx;
				margin-top: 4rpx;
			}

			.msg {
				font-size: 24rpx;
				color: var(--default-color1);
				text-align: left;
				line-height: 40rpx;

				text {
					color: var(--active-color1);
				}
			}
		}
	}
</style>