<template>
	<view class="index_body">
		<u-navbar back-icon-color="var(--default-color1)" title-color="var(--main-text-color)" title-bold
			:border-bottom="false"  :background="{backgroundColor: 'var(--main-bg-color)'}">
		</u-navbar>
		<view class="index_body_img">
			<image src="@/static/login/logo.png" mode="widthFix"></image>
		</view>
		<view class="index_body_box">
			<view class="index_body_content1">
				<text class="phone_num">{{phone||'获取手机号码失败'}}</text>
				<text class="tip">中国电信提供认证服务</text>
			</view>
			<view class="index_body_content2">
				<view class="btn_wx">
					<text>一键登录</text>
				</view>
				<view class="blue">
					新用户将为您自动注册
				</view>
			</view>
		</view>
		<view class="agreement">
				<u-image mode="widthFix" width="28rpx" @click="isAgreement = !isAgreement"
					:src="`../../../static/login/${isAgreement?'jxs2x':'jx'}.png`">
				</u-image>
				
			<view class="text">
				我已阅读并同意BIGVERSE<span @tap="nav_link('Bigverse平台服务协议',1)">《用户协议》</span>与 <span
					@tap="nav_link('Bigverse法律声明及隐私政策',2)">《隐私协议》</span>
			</view>
		</view>


	</view>
</template>
<script>
	export default {
		name: "appPhoneLogin",

		data() {
			return {
				phone: '139****5123',
				isAgreement: false,
			}
		},
		methods: {
		}
		// components: {UInput},
	}
</script>

<style scoped lang="scss">
	.index_body {
		background: var(--login-bg-color);
		padding-top: 376rpx;

		.index_body_img {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			width: auto;
			height: 108.14rpx;

			image {
				width: 340rpx;
				height: 59.65rpx;
			}

			.index_body_text {
				padding: 5rpx 0 0 18rpx;
				font-size: 32rpx;
				font-weight: 300;
				letter-spacing: 12.8rpx;
				line-height: 42rpx;
				color: rgba(255, 255, 255, 1);
				text-align: center;
				font-family: HarmonyOS Sans SC;

			}

		}

		.index_body_box {
			margin-top: 100rpx;
			display: flex;
			align-items: center;
			flex-direction: column;

			.index_body_content1 {
				display: flex;
				justify-content: center;
				flex-wrap: wrap;
				margin-top: 20rpx;

				.phone_num {
					width: 100%;
					padding: 5rpx 18rpx;
					font-size: 38rpx;
					font-weight: bold;
					letter-spacing: 2rpx;
					line-height: 42rpx;
					color: rgba(255, 255, 255, 1);
					text-align: center;
					font-family: HarmonyOS Sans SC;
				}

				.tip {
					margin-top: 20rpx;
					font-size: 24rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 31px;
					color: rgba(166, 166, 166, 1);
				}
			}

			.index_body_content2 {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-wrap: wrap;
				margin-top: 80rpx;
				padding: 20rpx 0;

				.btn_wx {
					margin-bottom: 14rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					width: 540rpx;
					height: 90rpx;
					border-radius: 50rpx;
					background: linear-gradient(180deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
				}

				text {
					font-size: 28rpx;
					font-weight: bold;
					letter-spacing: 0px;
					line-height: 37rpx;
					color: rgba(20, 20, 20, 1);
					text-align: center;
					vertical-align: top;
					font-family: HarmonyOS Sans SC;
					margin-left: 14rpx;

				}

				.blue {
					color: var(--active-color1);
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 90rpx;
					color: rgba(99, 234, 238, 1);
					text-align: center;
					vertical-align: top;
					margin-bottom: 100rpx;
				}

			}

		}

		.agreement {
			position: fixed;
			padding: 0rpx 105rpx;
			bottom: 80rpx;
			display: flex;

			.text {
				font-size: 24rpx;
				color: #A6A6A6;
				line-height: 32rpx;

				span {
					text-decoration: underline;
					color: var(--active-color1);
				}
			}
		}

	}

	:v-deep(.uni-input-input) {
		text-align: center;
	}
</style>