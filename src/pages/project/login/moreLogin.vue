<template>
	<view class="index_body">
		<u-navbar back-icon-color="var(--default-color1)" title-color="var(--main-text-color)" title-bold
			:border-bottom="false" :background="{backgroundColor: 'var(--main-bg-color)'}">
		</u-navbar>
		<view>
			<view class="index_body_img">
				<view class="img_box">
					<image src="@/static/login/moreLogin.png" mode="widthFix"></image>
				</view>
				<text class="index_body_text">账号密码登录</text>
			</view>
			<view class="index_body_box">
				<view class="index_body_content1">
					<!--    输入手机号-->
					<view class="top">
						<view class="right">
							<u-input class="modal-resale-input" v-model="phone" placeholder="请输入账号"  />
						</view>
					</view>
				</view>
				<view class="index_body_content1 mt40">
					<view class="top">
						<view class="right">
							<u-input type="password" class="modal-resale-input" v-model="password" placeholder="请输入密码"  />
						</view>
					</view>
				</view>
			</view>
			<view class="agreement">
				<u-image mode="widthFix" width="28rpx" @click="isAgreement = !isAgreement"
					:src="`../../../static/login/${isAgreement?'jxs2x':'jx'}.png`">
				</u-image>
				<view class="text">
					<text @click="isAgreement = !isAgreement">我已阅读并同意Bigverse</text><span @tap="nav_link('Bigverse平台服务协议',1)">《用户协议》</span>与 <span
						@tap="nav_link('Bigverse法律声明及隐私政策',2)">《隐私协议》</span>
				</view>
			</view>
			<view class="submit_login" @tap="login">
				登录
			</view>
			<view class="blue" @click="goforget">忘记密码？</view>
		</view>
		<!-- <view  class="blue" @click="bindDevice">绑定设备</view> -->
	</view>
</template>

<script>
	export default {
		name: "register",
		components: {},
		data() {
			return {
				region: '86',
				phone: '', //账号
				password: '', //密码
				isAgreement: false,
				return_url:""
			}
		},
		onLoad(options){
			
			if (options.url === undefined) {
				this.return_url = ""
			} else {
				this.return_url = options.url
				if (/^#/.test(options.url)){
					this.return_url = `/h5/${options.url}`;
					console.log(this.return_url)
				}else{
					this.return_url = options.url;
				}
			}
		},
		methods: {
			async login() {
				// 判断不同账号类型请求不同接口
				if (this.phone == "") {
					this.$u.toast('请输入账号');
				} else if (this.password == "") {
					this.$u.toast('请输入密码');
				} else if (!this.isAgreement) {
					this.$u.toast('请先勾选协议');
				} else {
					// 邮箱
					if (this.$u.test.email(this.phone)) {
						console.log('邮箱')
						let eres = await this.$api.java_emailPasswordLogin({
							email: this.phone,
							password: this.password,
						});
						this.getsuss(eres)
					} else if (this.$u.test.mobile(this.phone)) {
						// 手机
						console.log('手机')
						let pres = await this.$api.java_phonePasswordLogin({
							mobPhone: this.phone,
							password: this.password,
						});
						this.getsuss(pres)
					} else {
						let res = await this.$api.java_usernamePasswordLogin({
							userName: this.phone,
							password: this.password,
						});
						this.getsuss(res)
					}


				}

			},
			getsuss(res) {
				if (res.status.code == 0) {
					uni.setStorageSync("token", res.result?.accessToken)
					// #ifdef APP
						this.bindDevice()
					// #endif
					this.$u.toast('登录成功');
					// #ifdef APP
					setTimeout(() => {
						this.$Router.pushTab({
							name: 'index'
						})
					}, 300);
					// #endif
					// #ifdef H5
					if(this.return_url){
						window.location.href = this.return_url
					}else{
						setTimeout(() => {
							this.$Router.pushTab({
								name: 'index'
							})
						}, 300);
					}
					// #endif
					
				} else {
					if (res.status.code == 1005) {
						this.$Router.push({
							name: 'register',
							params: {
								phone: this.phone
							}
						})
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000
						});
					}

				}
			},
			nav_link(title, index) {
				if (index === 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
						}
					})
				} else {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
						}
					})
				}
			},

			codeChange(text) {
				this.tips = text;
			},
			goforget() {
				this.$Router.pushTab({
					name: "loginfyPwd"
				})
			},
			async bindDevice() {
				let appType = uni.getSystemInfoSync().platform
				console.error(appType,uni.getStorageSync('deviceToken'))
				let res = await this.$api.bindDevice({
					appType:appType=='ios'?2:1,
					deviceId:uni.getStorageSync('deviceToken')
				});
				if (res.status.code == 0) {
					console.log(res)
				} else {
					console.log(res)
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.index_body {
		background: var(--login-bg-color);
		// padding-top: 376rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		height:100vh;
		.mt40 {
			margin-top: 40rpx;
		}

		.index_body_img {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;

			.img_box {
				display: flex;
				justify-content: center;
				flex-direction: column;
				align-items: center;
				overflow: hidden;
				border-radius: 50%;

				// background: rgba(99, 234, 238, 1);
				image {
					width: 120rpx;
					height: 120rpx;

				}
			}




			.index_body_text {
				margin-top: 25rpx;
				font-size: 28rpx;
				font-weight: 700;
				line-height: 37rpx;
				color: rgba(255, 255, 255, 1);
				text-align: center;
				vertical-align: top;

			}

		}

		.index_body_box {
			margin-top: 100rpx;
			display: flex;
			align-items: center;
			flex-direction: column;

			.index_body_content1 {
				display: flex;
				justify-content: center;
			}

			.top {
				width: 540rpx;
				height: 90rpx;
				opacity: 1;
				border-radius: 50rpx;
				background: rgba(70, 69, 79, 1);
				padding:10rpx 40rpx;
				// .right {
				input {
					width: 540rpx;
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 37rpx;
					color: rgba(166, 166, 166, 1);
				}
				::v-deep {
					.uni-input-placeholder {
						font-weight: 400 !important;
						font-size: 28rpx !important;
					}
				}
				// }
			}
		}

		.agreement {
			padding: 0rpx 105rpx;
			margin-top: 80rpx;
			display: flex;

			.u-image {
				margin-right: 10rpx;
				margin-top: 4rpx;
			}

			.text {
				font-size: 24rpx;
				color: #A6A6A6;
				line-height: 32rpx;

				span {
					text-decoration: underline;
					color: var(--active-color1);
				}
			}
		}

		.submit_login {
			width: 540rpx;
			height: 100rpx;
			background: var(--primary-button-color);
			border-radius: 60rpx;
			font-size: 34rpx;
			color: #141414;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 206rpx auto 0rpx auto;

		}

		.blue {
			color: var(--active-color1);
			font-size: 28rpx;
			font-weight: 400;
			letter-spacing: 0px;
			line-height: 90rpx;
			color: rgba(99, 234, 238, 1);
			text-align: center;
			vertical-align: top;
			margin-bottom: 100rpx;
		}

	}

	:v-deep .uni-input-input {
		text-align: center;
	}

	.modal-resale-input::v-deep {
		.u-input__input {
			color: var(--default-color1) !important;
		}

		.uni-input-placeholder {
			font-size: 32rpx !important;
		}
	}
</style>