<template>
	<view class="centent">
		<view class="head_bg_view">
			<!-- <hx-lottie ref="lottieWeb" :loop="false" :autoplay="false" :options="option1" /> -->
		</view>
		<u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end" @start="start"
			ref="uCode" @change="codeChange"></u-verification-code>
		<view class="head_bg">
			<view class="logo">
				<image src="https://cdn.yanjie.art/image/20241023/10ecdf01ffc92470b82649df392f5936_750x400.png"
					mode="heightFix"></image>
			</view>
			<view class="back" @tap="nav_back()">
				<image src="https://cdn.yanjie.art/image/20240724/d5bd69aca947c17db64987e12410daeb_52x100.png"
					mode="heightFix"></image>
			</view>
		</view>
		<view class="login_view">
			<view class="phone_inp def_input_bg">
				<view class="right">
					<u-input v-model="phone" type="number" placeholder="请输入手机号" :maxlength="11" />
				</view>
			</view>
			<view class="pwd_inp def_input_bg">
				<view class="right">
					<u-input v-model="password" type="password" placeholder="请输入密码" :maxlength="11" />
				</view>
			</view>
		</view>
		<view class="agreement">
			<view class="check">
				<image @tap="checkAgreement" v-if="isAgreement"
					src="https://cdn.yanjie.art/image/20241023/98b6f94570306e0b3796dd72613fa8f8_24x26.png"
					mode="widthFix"></image>
				<image @tap="checkAgreement" v-else
					src="https://cdn.yanjie.art/image/20241023/63ed1abbf0cd92dddc8e5d78de022152_24x24.png"
					mode="widthFix"></image>

			</view>
			<view class="text">
				<text @tap="checkAgreement">我已阅读并同意</text><span @tap="nav_link('Pink Wallet平台服务协议', 1)">《用户协议》</span>与
				<span @tap="nav_link('Pink Wallet法律声明及隐私政策', 2)">《隐私协议》</span>
			</view>
		</view>

		<view class="submit_login login_lang_input_bg" @tap="login">
			登录
		</view>

		<!-- <view class="tips">忘记密码?</view> -->

		<view class="otherLogin">
			<view @click="PhoneLogin">
				<image src="https://cdn.yanjie.art/image/20241023/2128ffaa333939ecf6190e8fed9d5e21_60x60.png"
					mode="widthFix"></image>
				<text>手机号登录</text>
			</view>
			<view @click="RegisterLogin">
				<image src="https://cdn.yanjie.art/image/20241023/f5fe38d9487bf56b5a273b748ff0b728_60x60.png"
					mode="widthFix"></image>
				<text>注册</text>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			invitationCode: "",
			region: '86',
			phone: '',
			password: '',
			tips: '',
			// refCode: null,
			seconds: 60,
			status: 0,
			changeText: '重新获取(x)',
			isAgreement: false,
			option1: {
				path: 'https://cdn.yanjie.art/h5/xCase/head/head.json',
				loop: false,
				autoplay: true
			},

		}
	},
	onLoad(options) {
		console.log(uni.$u.config.v);
		this.phone = options.phone
		this.invitationCode = options.code
		this.region = options.region ? options.region : "86"
		if (options.url) {
			this.returnUrl = options.url
			console.log(this.returnUrl)
			if (/^#/.test(options.url)) {
				this.returnUrl = `/h5/${options.url}`;
				console.log(this.returnUrl)
			}
		}
	},
	methods: {
		PhoneLogin() {
			this.$Router.push({
				name: "login",
				params: {
					phone: this.phone,
					code: this.invitationCode,
					url: this.returnUrl || ""
				}
			})
		},
		RegisterLogin() {
			this.$Router.push({
				name: "register",
				params: {
					url: this.returnUrl || "",
					phone: this.phone,
					code: this.invitationCode,
				}
			})
		},
		codeChange(text) {
			this.tips = text;
		},
		getCode() {
			if (this.$refs.uCode.canGetCode) {
				// 模拟向后端请求验证码
				if (this.$u.test.mobile(this.phone)) {
					uni.showLoading({
						title: '正在获取验证码'
					})
					this.sendPhoneVerifyCode()
				} else {
					this.$u.toast('请输入手机号再获取');
				}

			} else {
				// this.$u.toast('倒计时结束后再发送');
			}
		},
		end() {
			this.status = 0
		},
		start() {
			this.status = 1
		},
		checkAgreement() {
			this.isAgreement = !this.isAgreement
			console.log(this.isAgreement)
		},
		async sendPhoneVerifyCode() {

			let res = await this.$api.sendPhoneVerifyCode({
				type: "LOGIN",
				phonePrefix: this.region,
				phone: this.phone
			});

			if (res.status.code == 0) {
				this.$u.toast('验证码已发送');
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
			} else {
				if (res.status.code == 110002) {
					return
					this.$u.toast(res.status.msg);
					setTimeout(() => {
						this.$Router.push({
							name: 'register',
							params: {
								phone: this.phone,
								region: this.region
							}
						})
					}, 2000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async login() {
			if (this.phone == "") {
				this.$u.toast('请输入手机号');
			} else if (this.password == "") {
				this.$u.toast('请输入密码');
			} else if (!this.isAgreement) {
				this.$u.toast('请先勾选协议');
			} else {
				let res = await this.$api.phonePasswordLogin({
					phonePrefix: this.region,
					phone: this.phone,
					passWord: this.password
				});
				if (res.status.code == 0) {
					uni.setStorageSync("token", res.result?.accessToken)
					this.$u.toast('登录成功');
					// #ifdef APP-PLUS
					setTimeout(() => {
						this.$Router.pushTab({
							name: 'personal',
							params: {
								isRegister: false,
								code: this.invitationCode,
							},
						})
					}, 300);
					// #endif
					// #ifdef H5
					if (this.returnUrl) {
						window.location.href = this.returnUrl
					} else {
						setTimeout(() => {
							this.$Router.pushTab({
								name: 'personal',
								params: {
									isRegister: false,
									code: this.invitationCode,
								},
							})
						}, 300);
					}
					// #endif
				} else {
					if (res.status.msg == '未设置登陆密码' && res.status.code == 9999) {
						this.$u.toast('未设置登陆密码');
						setTimeout(() => {
							this.$Router.push({
								name: 'login',
								params: {
									phone: this.phone,
								}
							})
						}, 300);

						return
					}

					if (res.status.code == 1005) {
						// uni.showToast({
						// 	title: '数据迁移中，请稍后再来。',
						// 	icon: 'none',
						// 	duration: 3000
						// });
						// return
						this.$Router.push({
							name: 'register',
							params: {
								phone: this.phone,
								region: this.region,
								url: this.returnUrl || ""
							}
						})
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000
						});
					}

				}
			}

		},
		nav_back() {
			this.$Router.back()
		},
		nav_link(title, index) {
			if (index === 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/yanjiePlatformServicesAgreement"
					}
				})
			} else {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/yanjiePrivacyPolicy"
					}
				})
			}
		},
	}
}
</script>
<style lang="scss">
page {
	width: 100%;
	min-height: 100%;
	background-image: url(https://cdn.yanjie.art/image/20241023/6debfead1c8d13f6d6223c7698cb225c_750x1624.png);
	background-size: 100%;
	background-repeat: no-repeat;
}

.centent {
	min-height: 100vh;
}

.otherLogin {
	display: flex;
	// align-items: flex-end; /* 将子元素对齐到底部 */
	justify-content: center;
	width: 100%;
	margin-top: 250rpx;

	>view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin: 0 110rpx;

		image {
			width: 60rpx;
			height: 60rpx;
		}

		text {
			font-weight: 400;
			font-size: 22rpx;
			color: #D8B662;
			margin-top: 20rpx;
		}
	}
}

::v-deep .u-input__input {
	font-weight: 400;
	font-size: 28rpx;
	color: #FFFFFF !important;

	.uni-input-placeholder {
		color: rgba(255, 255, 255, 0.2) !important;
	}
}

.tips {
	margin-top: 40rpx;
	text-align: center;
	font-weight: 400;
	font-size: 24rpx;
	color: #D8B662;
}

.head_bg {
	height: 600rpx;
	padding-top: 200rpx;
	position: relative;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	z-index: 0;

	.back {
		position: absolute;
		left: 40rpx;
		/* #ifdef APP */
		top: 80rpx;
		/* #endif */
		/* #ifdef H5 */
		top: 40rpx;

		/* #endif */
		image {
			width: 26rpx;
			height: 50rpx;
		}
	}

	.logo {
		display: flex;
		align-items: center;
		justify-content: center;

		image {
			height: 400rpx;
		}
	}
}

.login_view {
	padding: 0rpx 80rpx;
	position: relative;
	margin-top: 105rpx;
	z-index: 1;

	.phone_inp {
		height: 90rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0rpx 40rpx;

		.left {
			width: 100rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		.right {
			width: 100%;

			.verification {
				// width: 100rpx;
				height: 90rpx;
			}

		}
	}

	.pwd_inp {
		height: 90rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0rpx 40rpx;
		margin-top: 50rpx;

		.right {
			width: 100%;

			.verification {
				width: 100rpx;
				height: 90rpx;
			}

		}
	}
}

.agreement {
	padding: 0rpx 105rpx;
	margin-top: 30rpx;
	display: flex;

	.check {
		width: 30rpx;
		margin-right: 20rpx;

		image {
			width: 30rpx;
		}
	}

	.text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.4);

		span {
			text-decoration: underline;
			color: rgba(216, 182, 98, 0.4)
		}
	}
}

.submit_login {
	height: 90rpx;
	width: 540rpx;
	margin: 80rpx auto;
	font-size: 34rpx;
	font-weight: 400;
	font-size: 30rpx;
	color: #130E07;
	display: flex;
	align-items: center;
	justify-content: center;

}

.head_bg_view {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	height: 500rpx;
	z-index: -1;
}
</style>