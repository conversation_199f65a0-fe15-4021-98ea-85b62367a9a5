<template>
  <view class="pink_card">
    <view class="pink_card_banner">
      <view class="pink_card_banner_content">
        <view class="pink_card_banner_title">{{ $t('pink_card_page.title') }}</view>
        <view class="pink_card_banner_subtitle">{{ $t('pink_card_page.subtitle') }}</view>
        <view class="pink_card_banner_btn">
          <u-button type="primary" @click="applyCard">{{ $t('pink_card_page.apply_now') }}</u-button>
          <u-button type="info" @click="checkCard">{{ $t('pink_card_page.check_card') }}</u-button>
        </view>
      </view>
    </view>

    <view class="pink_card_content">
      <view class="pink_card_content_title">{{ $t('pink_card_page.overdraft') }}</view>
      <view class="pink_card_content_desc">{{ $t('pink_card_page.highlights') }}</view>
      
      <view class="pink_card_content_list">
        <view class="pink_card_content_item">
          <view class="pink_card_content_item_title">{{ $t('pink_card_page.feature1_title') }}</view>
          <view class="pink_card_content_item_desc">{{ $t('pink_card_page.feature1_desc') }}</view>
        </view>
        <view class="pink_card_content_item">
          <view class="pink_card_content_item_title">{{ $t('pink_card_page.feature2_title') }}</view>
          <view class="pink_card_content_item_desc">{{ $t('pink_card_page.feature2_desc') }}</view>
        </view>
        <view class="pink_card_content_item">
          <view class="pink_card_content_item_title">{{ $t('pink_card_page.feature3_title') }}</view>
          <view class="pink_card_content_item_desc">{{ $t('pink_card_page.feature3_desc') }}</view>
        </view>
        <view class="pink_card_content_item">
          <view class="pink_card_content_item_title">{{ $t('pink_card_page.feature4_title') }}</view>
          <view class="pink_card_content_item_desc">{{ $t('pink_card_page.feature4_desc') }}</view>
        </view>
        <view class="pink_card_content_item">
          <view class="pink_card_content_item_title">{{ $t('pink_card_page.feature5_title') }}</view>
          <view class="pink_card_content_item_desc">{{ $t('pink_card_page.feature5_desc') }}</view>
        </view>
      </view>
    </view>

    <view class="pink_card_apply">
      <view class="pink_card_apply_content">
        <view class="pink_card_apply_title">{{ $t('pink_card_page.apply_title') }}</view>
        <view class="pink_card_apply_desc">{{ $t('pink_card_page.apply_desc') }}</view>
        <view class="pink_card_apply_highlight">{{ $t('pink_card_page.apply_highlight', { brand: 'PinkWallet' }) }}</view>
        <u-button type="primary" @click="applyCard">{{ $t('pink_card_page.apply_btn') }}</u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // ... existing data ...
    }
  },
  methods: {
    applyCard() {
      // ... existing code ...
    },
    checkCard() {
      // ... existing code ...
    }
  }
}
</script>

<style lang="scss" scoped>
// ... existing styles ...
</style> 