import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';


// 🔹 替换成你的 Firebase 项目配置
const firebaseConfig = {
    apiKey: "AIzaSyCn_qZ3utKfJS8P6xPCM7_Pnf8bEbi4asE",
    authDomain: "cwallet-7e1db.firebaseapp.com",
    projectId: "cwallet-7e1db",
    storageBucket: "cwallet-7e1db.firebasestorage.app",
    messagingSenderId: "343545589417",
    appId: "1:343545589417:web:84cf44a4c46f93ae7503b8"
  };
  firebase.initializeApp(firebaseConfig);

export default {
    auth: firebase.auth(),
    login() {
      const provider = new firebase.auth.GoogleAuthProvider();
      console.log(provider);

      firebase.auth().signInWithPopup(provider)      
      .then(function(result) {
        console.log(result);
      })
      .catch(function(error){
        console.log(error);
        
        const errorCode = error.code;
        const errorMessage = error.message;
        const email = error.email;
        const credential = error.credential;
        console.log(errorCode, errorMessage, email, credential);
        })
    },
    logout() {
      firebase.auth().signOut()
      .then(function() {})
      .catch(function(error) {
        console.log(error)});
    }
}
