import axios from 'axios'
import { Message } from 'element-ui'
import router from '@/router'

import md5 from 'blueimp-md5'
import jsrsasign from 'jsrsasign'

/**
 * 请求相应日志记录
 */

function changeDataType (obj) {
  let str = ''
  if (typeof obj === 'object') {
    for (const i in obj) {
      if (typeof obj[i] !== 'function' && typeof obj[i] !== 'object') {
        str += i + '=' + obj[i] + '&'
      } else if (typeof obj[i] === 'object') {
        str += i + '=' + JSON.stringify(obj[i]) + '&'
      }
    }
  }
  return str.replace(/&$/g, '')
}

function deleteEmptyProperty (object) {
  for (const i in object) {
    const value = object[i]
    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        if (value.length === 0) {
          delete object[i]
          continue
        }
      }
      deleteEmptyProperty(value)
      if (isEmpty(value)) {
        delete object[i]
      }
    } else {
      if (value === '' || value === null || value === undefined) {
        delete object[i]
      }
    }
  }
}

function isEmpty (object) {
  for (const name in object) {
    return false
  }
  return true
}

function isRandom () {
  const str = '0123456789'
  let num = ''
  for (let i = 0; i < 21; i++) {
    num += str.charAt(Math.floor(Math.random() * str.length))
  }
  return num
}

function getNftcnApiSignature (str) {
  // 此处操作与后端约定参数
  // 因为后端提供的是pck#8的密钥对，所以这里使用 KEYUTIL.getKey来解析密钥
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  // 将密钥转码
  const rsa = jsrsasign.KEYUTIL.getKey(key)
  // 创建Signature对象，设置签名编码算法
  const sig = new jsrsasign.KJUR.crypto.Signature({
    alg: 'SHA256withRSA'
  })
  // 初始化
  sig.init(rsa)
  // 传入待加密字符串
  sig.updateString(str)
  // 生成密文
  return jsrsasign.hextob64(sig.sign())
}
// 排序的函数;
function objKeySort (arys) {
  // 先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
  const newkey = Object.keys(arys).sort()
  const newObj = {} // 创建一个新的对象，用于存放排好序的键值对
  for (let i = 0; i < newkey.length; i++) {
    // 遍历newkey数组
    newObj[newkey[i]] = arys[newkey[i]]
    // 向新创建的对象中按照排好的顺序依次增加键值对
  }
  return newObj // 返回排好序的新对象
}

// 创建axios的实例
const service = axios.create()
// 请求拦截器
service.interceptors.request.use(
  function (config) {
    config.baseURL = process.env.VUE_APP_BASE_URL
    config.headers.AgentAuthorization = localStorage.getItem('usertoken')
    if (config.headers['Content-Type'] === 'multipart/form-data') return config
    // 接口验签规则文档: https://thoughts.aliyun.com/workspaces/61dbb1ead1ea2b001af22576/docs/623a7bf2dcb1eb0001dc0611
    const nftcnApiAppId = 'nftcn-web-admin' // 写死的参
    const nftcnApiTimestamp = new Date().getTime() // 时间戳
    const nftcnApiNonce = isRandom() // 获取随机数
    const nftcnApiAppSecret = md5(nftcnApiAppId + nftcnApiNonce).toUpperCase() // md5拼接转换
    // 定义;
    const map = {
      nftcnApiAppId,
      nftcnApiAppSecret,
      nftcnApiNonce,
      nftcnApiTimestamp,
      ...config.data
    }
    deleteEmptyProperty(map)
    const nftcnApiSignature = getNftcnApiSignature(
      changeDataType(objKeySort(map))
    )
    Object.assign(config.headers, {
      nftcnApiTimestamp,
      nftcnApiNonce,
      nftcnApiSignature,
      nftcnApiAppId,
      nftcnApiAppSecret
    })
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)
// 响应拦截
service.interceptors.response.use(
  ({ data }) => {
    if (data instanceof Blob) {
      return data
    }
    const { code, msg } = data
    
    if (code == 200) {
      return data
    } else {
      Message.error(msg)
      if (code === 401) {
        router.push('/login')
      }
	 // return Promise.reject(error)
     return data
    }
  },
  (err) => {
    console.log(err);
    
    // debugger
    let errMsg = ''
    const status = err?.response?.status
    if (status) {
      switch (status) {
        case 401:
          errMsg = '登录状态失效，请重新登录'
          break
        case 403:
          errMsg = '拒绝访问'
          break
        case 408:
          errMsg = '请求超时'
          break
        case 500:
          errMsg = '服务器内部错误'
          break
        case 501:
          errMsg = '服务未实现'
          break
        case 502:
          errMsg = '网关错误'
          break
        case 503:
          errMsg = '服务不可用'
          break
        case 504:
          errMsg = '网关超时'
          break
        case 505:
          errMsg = 'HTTP版本不受支持'
          break
        default:
          errMsg = '网络请求错误'
          break
      }
    } else {
      errMsg = err
    }
    // ElMessage({
    //   message: errMsg,
    //   type: 'warning',
    // });
    // Toast(errMsg)
    return Promise.reject(errMsg)
  }
)

export default service
