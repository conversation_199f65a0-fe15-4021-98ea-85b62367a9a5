import { otherPeople, boxSeries, mallDetails, back, authentication, addBankCard, ordinarySeries, toPay } from '@/utils/native';

const { origin, hash } = window.location;
// 跳主站登录
export const goLogin = () => {
  const url = encodeURIComponent(`/active/${hash}`);
  window.location.href = `${origin}/h5/#/pages/project/login/login?url=${url}`;
};
/**
 * 跳转主站页面
 * @param { string } url 页面路径
 */
export const goPage = (url) => {
  window.location.href = `${origin}/h5/#/${url}`;
};
/**
 * 页面返回
 * @param { string } platform 当前页面的环境（h5、ios、android）
 */
// 页面返回
export const goBack = (platform) => {
  if (platform !== undefined && platform !== '') {
    back();
  } else {
    window.location.href = `${origin}/h5/#/`;
    // if (history.length < 2) {
    //   window.location.href = `${origin}/h5/#/`;
    // } else {
    //   history.back();
    // }
  }
};
/**
 * 查看主页
 * @param { string } platform 当前页面的环境（h5、ios、android）
 * @param {String} contractAddress 用户contract_address(h5)
 * @param {String} userId 用户ID(ios/android)
 */
export const goPerson = (platform, contractAddress, userId) => {
  if (platform !== undefined && platform !== '') {
    otherPeople(userId);
  } else {
    goPage(`pagesA/project/personal/social/otherPeople?contract_address=${contractAddress}&origin=active`);
  }
};
/**
 * 查看盲盒详情
 * @param { string } platform 当前页面的环境（h5、ios、android）
 * @param {String} ctId 作品id
 * @param {String} userId 用户ID(ios/android)
 */
export const goBlind = (platform, ctId, userId) => {
  if (platform !== undefined && platform !== '') {
    boxSeries({
      itemTokenId: ctId,
      userId,
      isSelf: 0,
      isCreate: '',
    });
  } else {
    goPage(`pagesA/project/mall/boxSeries?ctId=${ctId}&origin=active`);
  }
};
/**
 * 查看作品详情
 * @param { string } platform 当前页面的环境（h5、ios、android）
 * @param {String} itemTokenId 作品id(ios/android)
 * @param {String} ctId 作品id(h5)
 */
export const goGoods = (platform, ctId, itemTokenId) => {
  if (platform !== undefined && platform !== '') {
    mallDetails(itemTokenId);
  } else {
    goPage(`pagesA/project/mall/mallDetails?tid=${ctId}&origin=active`);
  }
};
/**
 * 查看普通系列
 * @param { string } platform 当前页面的环境（h5、ios、android）
 * @param {String} itemTokenId 系列tid
 * @param {String} ctId 系列tid
 * @param {Number} userId 创作者用户ID
 */
export const goSeries = (platform, ctId, userId) => {
  if (platform !== undefined && platform !== '') {
    ordinarySeries({
      itemTokenId: ctId,
      userId,
      isCreate: 1,
      isSelf: 0,
    });
  } else {
    goPage(`pagesA/project/mall/ordinarySeries?ctId=${ctId}&isCreate=1&uid=${userId}&isSelf=0&origin=active`);
  }
};
/**
 * 实名认证
 * @param { string } platform 当前页面的环境（h5、ios、android）
 */
export const goAuthentication = (platform) => {
  if (platform !== undefined && platform !== '') {
    authentication();
  } else {
    goPage(`pagesA/project/personal/realName?origin=active`);
  }
};
/**
 * 绑定银行卡
 * @param { string } platform 当前页面的环境（h5、ios、android）
 */
export const goAddBankCard = (platform) => {
  if (platform !== undefined && platform !== '') {
    addBankCard();
  } else {
    goPage(`pagesA/project/bank/addbank?origin=active`);
  }
};
/**
 * 前往支付页
 * @param { string } platform 当前页面的环境（h5、ios、android）
 * @param {String} query.orderNo 订单号
 * @param {Number} query.price 订单金额
 * @param {Number} query.countdown 倒计时,单位s
 * @param {Number} query.from 支付类型, 0:作品  1:保证金  2: 燃料
 */
export const goPayOrder = (platform, query) => {
  if (platform === 'android') {
    toPay(query);
  } else if (platform === 'ios') {
    goPage(`pagesA/project/order/payOrder?${window.location.href.split('?')[1]}&orderId=${query.orderNo}&type=buyFuel`);
  } else {
    goPage(`pagesA/project/order/payOrder?orderId=${query.orderNo}&type=buyFuel`);
  }
};