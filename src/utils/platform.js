// import { userInfo } from '@/api/appusercenter.js';
/**
 * 判断进入的环境
 * @param { String } platform 平台类型, ios/android
 * @param { String } token 登录用户的token;
 */
export const judgePlatform = (platform, token) => {
  let isHeader = true;
  console.log(123);

  // 存储当前平台环境
  localStorage.setItem("platform", platform);
  return new Promise((resolve) => {
    if (platform !== undefined && platform !== "") {
      // 不需要专属配置就直接在下方一起配置app是否又header即可 ↓↓↓↓
      console.log(token, 123);
      localStorage.setItem("token", token);
      isHeader = false;
    } else {
      if (token) {
        localStorage.setItem("token", token);
      }
      isHeader = true;
    }
    resolve(isHeader);
  });
};

// 获取用户信息
export const getUser = async () => {
  await userInfo({
    userId: "",
  }).then((res) => {
    localStorage.setItem("uid", res.userId);
    localStorage.setItem("contract_address", res.contractAddress);
  });
};
