
/**
 * 获取一个uuid
 * @returns {String}
 */
export function getUuid () {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
/**
 * 保存数据
 * @param {*} key
 * @param {*} value
 * @param {*} isLocal
 */
export function setStorage (key, value, isLocal) {
  const str = JSON.stringify(value);
  if (isLocal) localStorage.setItem(key, str);
  else sessionStorage.setItem(key, str);
}
/**
 * 获取数据
 * @param {*} key
 * @param {*} isLocal
 */
export function getStorage (key, isLocal) {
  const str = isLocal ? localStorage.getItem(key) : sessionStorage.getItem(key);
  return str ? JSON.parse(str) : str;
}
/**
 * 获取类型
 * @param me 需要判断的对象
 * @returns 类型字符串,例: String
 */
export function getMyType (me) {
  const str = Object.prototype.toString.call(me);
  return str.match(/\s(\S*)\]/)?.[1] || '';
}


export function _debounce(fn, delay) {
    //防抖  只执行最后一次
    var delay = delay || 500;
    var timer = "";
    return function () {
      var th = this;
      var args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(function () {
        timer = "";
        fn.apply(th, args);
      }, delay);
    };
}