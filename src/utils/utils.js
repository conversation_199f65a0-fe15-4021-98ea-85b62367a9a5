export function desensitizeMobile(phoneNumber) {
  // 确保传入的是字符串类型，并且长度至少为7（例如：10086）
  if (typeof phoneNumber === "string" && phoneNumber.length >= 7) {
    const prefix = phoneNumber.slice(0, 4); // 获取前三位
    const suffix = phoneNumber.slice(-3); // 获取后四位

    // 根据手机号码长度生成不同数量的星号
    let asterisks = "***";
    return `${prefix}${asterisks}${suffix}`; // 组合成脱敏后的手机号码
  } else {
    console.error("Invalid phone number format");
    return phoneNumber; // 或者返回空字符串，取决于错误处理策略
  }
}

/**
 * 根据货币代码返回货币符号
 * @param {string} fiat - 币种代码，如 "CNY"、"HK"、"USD"
 * @returns {string} 对应的货币符号
 */
export function getCurrencySymbol(fiat) {
  switch (fiat) {
    case "CNY":
      return "￥";
    case "HK":
    case "HKD":
      return "HK$";
    case "USD":
      return "$";
    default:
      return "";
  }
}

export function formatTimestamp(seconds) {
  if (!seconds) {
    return "--";
  }
  const date = new Date(seconds); // 转成毫秒时间戳
  const Y = date.getFullYear();
  const M = String(date.getMonth() + 1).padStart(2, "0");
  const D = String(date.getDate()).padStart(2, "0");
  const h = String(date.getHours()).padStart(2, "0");
  const m = String(date.getMinutes()).padStart(2, "0");
  const s = String(date.getSeconds()).padStart(2, "0");
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
}

export function formatTimestampms(seconds) {
  if (!seconds) {
    return "--";
  }

  const date = new Date(seconds * 1000); // 转成毫秒时间戳
  const Y = date.getFullYear();
  const M = String(date.getMonth() + 1).padStart(2, "0");
  const D = String(date.getDate()).padStart(2, "0");
  const h = String(date.getHours()).padStart(2, "0");
  const m = String(date.getMinutes()).padStart(2, "0");
  const s = String(date.getSeconds()).padStart(2, "0");
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
}
