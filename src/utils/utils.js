export function desensitizeMobile(phoneNumber) {
  // 确保传入的是字符串类型，并且长度至少为7（例如：10086）
  if (typeof phoneNumber === 'string' && phoneNumber.length >= 7) {
    const prefix = phoneNumber.slice(0, 4); // 获取前三位
    const suffix = phoneNumber.slice(-3); // 获取后四位

    // 根据手机号码长度生成不同数量的星号
    let asterisks = '***';
    return `${prefix}${asterisks}${suffix}`; // 组合成脱敏后的手机号码
  } else {
    console.error('Invalid phone number format');
    return phoneNumber; // 或者返回空字符串，取决于错误处理策略
  }
}

// #ifdef H5
	/**
	 * 初始化聊天
	 */
	(function(w, d, n, a, j) {
		w[n] = w[n] || function() {
			(w[n].a = w[n].a || []).push(arguments);
		};
		j = d.createElement('script');
		j.async = true;
		j.src = 'https://qiyukf.com/script/3d1f1a45bc64cc1aab8aca825ff89c13.js?hidden=1';
		d.body.appendChild(j);
	})(window, document, 'ysf');
	/**
	 * 唤起七鱼客服系统
	 */
	export function nav_contactService() {
		// this.$emit('contactService', true)
		const {
			contractAddress,
			leapPlanTag,
			userId,
			name,
			email,
			phone,
		} = JSON.parse(uni.getStorageSync('userInfo'))
		const data = [{
			label: 'contractAddress',
			key: 'contractAddress',
			value: contractAddress
		}, {
			key: "tags",
			value: leapPlanTag
		}]
		ysf('config', {
			uid: userId,
			name,
			email,
			mobile: phone,
			data: JSON.stringify(data),
			groupid:'482416932',
			robotShuntSwitch:1,
			success: function() { // 成功回调
				// window.open(window.ysf('url'), '_blank')
				window.location.href = window.ysf('url')
			},
			error: function() { // 错误回调
				// handle error
			}
		})
	}
	
// #endif
