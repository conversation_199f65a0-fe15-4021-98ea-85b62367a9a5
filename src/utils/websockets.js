import Pako from "pako";
import store from "@/store";
export function startSocket() {
  // data, topList, bottomList

  let Indictor = uni.getStorageSync("currentIndictor");

  uni.connectSocket({
    // url: 'wss://ws.okx.com:8443/ws/v5/public',
    // #ifdef H5
    url:
      // store.state.optionSymbol != "aaveusdt"
      Indictor
        ? process.env.VUE_APP_WS_NEW_API_URL
        : process.env.VUE_APP_WS_API_URL,
    // VUE_APP_WS_NEW_API_URL
    // #endif

    // #ifdef APP
    url:
      // store.state.optionSymbol != "aaveusdt"
      Indictor
        ? getApp().globalData.newsocketApi
        : getApp().globalData.socketApi,
    // newsocketApi
    // #endif

    success: (res) => {
      console.log(
        "请求变化了",
        store.state.optionSymbol,
        res,
        store.state.optionSymbol != "aaveusdt"
          ? process.env.VUE_APP_WS_NEW_API_URL
          : process.env.VUE_APP_WS_API_URL
      );
    },
  });

  // uni.onSocketOpen(function (res) {
  //     let subData = {
  //         event: "sub",
  //         params: { channel: "market_e_aaveusdt_depth_step0", cb_id: "e_aaveusdt" }
  //     }
  //     let message = JSON.stringify(subData)
  //     uni.sendSocketMessage({
  //         data: message,
  //         // complete(res2) {
  //         //     console.log("发送的消息", res2)

  //         // },
  //     })

  // })
  // let symbol = store.state.optionSymbol;
  let symbols = uni.getStorageSync("currentIndictor");
  let symbol;
  if (symbols) {
    symbol = symbols.split("-")[1].toLowerCase() + 'usdt';
  } else {
    symbol = "aaveusdt";
  }

  uni.onSocketOpen(function (res) {
    console.log(res, "打开的消息");
    let subDatadepth = {
      event: "sub",
      params: {
        channel: `market_e_${symbol}_depth_step0`,
        cb_id: `e_${symbol}`,
      },
    };
    let messagedep = JSON.stringify(subDatadepth);
    uni.sendSocketMessage({
      data: messagedep,
      complete(res) {
        console.log("深度图发送的消息", res);
      },
    });

    let fundingRateData = {
      event: "sub",
      params: { channel: `market_${symbol}_info`, cb_id: `e_${symbol}` },
    };
    let messagefundingRate = JSON.stringify(fundingRateData);
    uni.sendSocketMessage({
      data: messagefundingRate,
      complete(res) {
        console.log("资金费率发送的消息", res);
      },
    });

    let subData = {
      event: "sub",
      params: {
        channel: `market_e_${symbol}_trade_ticker`,
        cb_id: `e_${symbol}`,
      },
    };
    let message = JSON.stringify(subData);
    uni.sendSocketMessage({
      data: message,
      complete(res2) {
        console.log("发送的消息exchange", res2);
      },
      fail(err) {
        // this.FetchLatestPrice()
        console.log("exchange连接失败 connectSocket=", err);
      },
    });
  });

  // uni.onSocketMessage((event) => {
  //     calculationTime()
  //     console.log(event,'封装');

  // })

  // uni.onSocketMessage((event) => {

  //     var uint8array = new Uint8Array(event.data);
  //     const output = Pako.inflate(uint8array, {
  //         to: "string"
  //     });
  //     let res = JSON.parse(output)
  //     console.log(res, 'channelsubscribe')
  //     if (res.channel == 'market_e_aaveusdt_depth_step0') {
  //         if (res.data) {
  //             this.orderBook = this.transformData(res.data)
  //         }
  //     } else if (res.channel == 'market_e_aaveusdt_trade_ticker') {
  //         if (res.tick) {
  //             const tickId = res.tick.id;
  //             const matchingDataObject = res.tick.data.find(obj => obj.id === tickId);
  //             if (matchingDataObject) {
  //                 const price = matchingDataObject.price;
  //                 uni.setStorageSync('realPrice', price);
  //                 uni.setStorageSync('side', matchingDataObject.side)

  //                 this.latestPrice = uni.getStorageSync('realPrice') || 0
  //                 this.midside = uni.getStorageSync('side')
  //             } else {
  //                 // console.log("No matching data object found.");
  //             }
  //         }
  //     }
  // })
  // },

  uni.onSocketError(function (res) {
    uni.connectSocket({
      // url: 'wss://ws.okx.com:8443/ws/v5/public',
      // #ifdef H5
      url: process.env.VUE_APP_WS_API_URL,
      // #endif
      // #ifdef APP
      url: getApp().globalData.socketApi,
      // #endif
      success: (res) => {},
    });
  });
}

let socketTime = []; //这个时间用来记录第一次触发的时间
let timeIndex = -1; //记录时间索引 第一次收到消息是0，第二次收到小时就+1
function calculationTime() {
  timeIndex += 1;

  socketTime[timeIndex] = new Date().getTime();
  if (socketTime.length >= 2) {
    if (socketTime[timeIndex] / 1000 - socketTime[timeIndex - 1] / 1000 > 5) {
      uni.closeSocket();
      uni.onSocketClose(function (res) {
        console.log("WebSocket 已关闭！");
        uni.connectSocket({
          // url: 'wss://ws.okx.com:8443/ws/v5/public',
          // #ifdef H5
          url: process.env.VUE_APP_WS_API_URL,
          // #endif
          // #ifdef APP
          url: getApp().globalData.socketApi,
          // #endif
          success: (res) => {
            console.log("连接", res);
          },
        });
      });
    }
  }

  if (timeIndex == 200) {
    socketTime.length = 0;
    timeIndex = -1;
  }
}

export function onSocketOpen() {
  uni.closeSocket();
}
