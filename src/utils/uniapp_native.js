
import qs from 'qs';
import uni from '@/utils/uni.webview.1.5.5';


/**
 * 跳转方法
 * @param { String } type 页面类型, 0 跳转下一个页面 1 跳转到个人中心的二级三级页面 2个人主页 3返回上一页
 * @param { String } path 页面路径, 由app提供;
 * @param { Object } query 所需传递的字段;
 */
const navigateTo = ({ type, path = '', query }) => {
	const search =
	  query && Object.keys(query).length > 0 ? `?${qs.stringify(query)}` : '';
	const req = {
	  type,
	  link: `${path}${search}`,
	};
	console.log(req)
	if(type==0){
		console.log(`${path}${search}`)
		  uni.navigateTo({
		  	 url:`${path}${search}`
		  })
	}else if(type == 2){
		  uni.switchTab({
		  	 url:`${path}${search}`
		  })
	}
};

// 以下是uniapp新写的方法
export const loginTo = () => {
  navigateTo({
    type: '0',
    path: '/pages/project/login/loginMain',
  });
};
export const seriesList = (ctid) => {
  navigateTo({
    type: '0',
    path: '/pagesA/project/putaway/seriesList',
	query:{
		ctid
	}
  });
};
export const personalTo = () => {
  navigateTo({
    type: '2',
    path: '/pages/project/personal/index',
  });
};
/**
 * 跳转我的钱包
 */
export const balance = () => {
  navigateTo({
    type: '0',
    path: 'nftcn://account_cash_info',
  });
};
/**
 * 绑定银行卡
 */
export const addBankCard = () => {
  navigateTo({
    type: '0',
    path: 'nftcn://add_bank',
  });
};
/**
 * 燃料购买成功
 * @param { String } orderNo 订单号
 */
export const gasPaySyccess = (orderNo) => {
  navigateTo({
    type: '0',
    path: 'nftcn://order_pay_result',
    query: {
      orderNo,
    },
  });
};
/**
 * 跳转打开一个协议
 * @param {String} path 协议链接
 */
export const openLink = (path) => {
  navigateTo({
    type: '0',
    path,
  });
};
/**
 * 实名认证
 */
export const authentication = () => {
  navigateTo({
    type: '0',
    path: 'nftcn://user_auth',
  });
};
/**
 * 订单列表
 */
export const orderList = () => {
  navigateTo({
    type: '1',
    path: 'nftcn://my_order',
  });
};
/**
 * 我的个人主页
 */
export const personal = () => {
  navigateTo({
    type: '2',
  });
};
/**
 * 他人的个人主页
 * @param {String} userId 用户ID
 */
export const otherPeople = (userId) => {
  navigateTo({
    type: '0',
    path: 'nftcn://user',
    query: {
      userId,
    },
  });
};
/**
 * 盲盒系列详情
 * @param {Object} query 参数
 * @param {String} query.itemTokenId 盲盒tid
 * @param {Number} query.isCreate 创作还是藏品 1-创作 0-藏品
 * @param {Number} query.userId 创作者用户ID
 * @param {Number} query.isSelf 是否从"我的"页面进入
 */
export const boxSeries = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://box',
    query,
  });
};
/**
 * 普通系列详情
 * @param {Object} query 参数
 * @param {String} query.itemTokenId 系列tid
 * @param {Number} query.isCreate 创作还是藏品
 * @param {Number} query.userId 创作者用户ID
 * @param {Number} query.isSelf 是否从"我的"页面进入
 */
export const ordinarySeries = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://series',
    query,
  });
};
/**
 * 作品详情
 * @param {String} itemTokenId
 */
export const mallDetails = (itemTokenId) => {
  navigateTo({
    type: '0',
    path: 'nftcn://item',
    query: {
      itemTokenId,
    },
  });
};
/**
 * 后退一页
 */
export const back = () => {
  navigateTo({
    type: '3',
  });
};
/**
 * 前往支付页
 * @param {String} query.orderNo 订单号
 * @param {Number} query.price 订单金额
 * @param {Number} query.countdown 倒计时,单位s
 * @param {Number} query.from 支付类型, 0:作品  1:保证金  2: 燃料
 */
export const toPay = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://order_payment',
    query,
  });
};
/**
 * 飞跃计划支付成功
 */
export const toFYPay = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://flyPlan_success',
    query,
  });
};
/**
 * 飞跃计划购买页
 */
export const toFYBuy = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://flyPlan',
    query,
  });
};
/**
 * 跳转搜索页面
 */
export const searchIndex = () => {
  navigateTo({
    type: '0',
    path: 'nftcn://search_index'
  });
};
/**
 * 分享
 * @param {Object} query 参数
 * @param {String} query.pic 图片
 * @param {String} query.title 标题
 * @param {String} query.subtitle 副标题
 * @param {String} query.link 链接
 */
export const share = (query) => {
  shareTo({
    query,
  });
};

/**
 * 跳转到专题详情
 */
export const NewRecommended = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://NewRecommended',
    query,
  });
};

/**
 * 跳转到专题详情列表也
 */
export const newRecommendedList = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://newrecommended_list',
    query,
  });
};

/**
 * 跳转到新版吉物仓
 */
export const mall_jxip_detail = (query) => {
  navigateTo({
    type: '0',
    path: 'nftcn://mall_jxip_detail',
    query,
  });
};
// 跳转登录
export const nav_app_login = () => {
	loginTo();
};




