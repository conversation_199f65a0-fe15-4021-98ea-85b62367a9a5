import tracker from "./track"

/**
 * @description: 埋点方法
 * @author: jay
 * @date: 2022-07-27
 * @update: 2022-07-27
 * @param {object} options 埋点选项，参考 interface Options，老版本为 pageId
 * @param {object} content 埋点参数，老版本为 scenes
 * @param {string} oldQuery 兼容老版本，搜索词
 * @param {string} oldEventType 兼容老版本，事件类型
 * @param {string} oldBehaviorType 兼容老版本，点击事件的行为类型
 * @param {object} oldBehaviorContent 兼容老版本，埋点参数
 * @param {number} oldStatus 兼容老版本，操作的状态: 1成功，0失败
 */
// interface Options = {
//     pageId: string, // 页面id
//     scenes: string; // 场景（有作品展示的）
//     query: string; // 搜索词
//     eventType: 'exposure' | 'click'; // 事件类型
//     behaviorType: string; // 点击事件的行为类型 https://dnik2kt2qo.feishu.cn/wiki/wikcnnZ0a3CvpuZzf1fcQYHw9vf
//     status: 1 | 0; // 操作的状态: 1成功，0失败
// }
const slsTracker = (options = {
  pageId: '',
  scenes: '',
  query: '',
  eventType: '',
  behaviorType: '',
  status: ''
}, content = {}, oldQuery, oldEventType, oldBehaviorType, oldBehaviorContent, oldStatus) => {
  // 兼容旧版本
  let params = null
  console.log(Object.prototype.toString.call(options))
  if (typeof options === 'string') {
    params = {
      pageId: options,
      scenes: content,
      query: oldQuery,
      eventType: oldEventType,
      behaviorType: oldBehaviorType,
      status: oldStatus
    }
  } else if (Object.prototype.toString.call(options) === '[object Object]'
      && [oldQuery, oldEventType, oldBehaviorType, oldBehaviorContent, oldStatus]
          .every(item => item === undefined)) {
    // options 是一个对象, 并且没有旧版本的参数
    // 设置默认值
    const { pageId = '', scenes = '', query = '', eventType = '', behaviorType = '', status = '' } = options
    params = {
      pageId,
      scenes,
      query,
      eventType,
      behaviorType,
      status
    }
  } else {
    console.warn('埋点参数不正确')
  }
  const behaviorContent = Object.prototype.toString.call(content) === '[object Object]' ? content : oldBehaviorContent
  console.log(params)
  const { pageId, scenes, query, eventType, behaviorType, status } = params
  tracker.send({
    user: {
      cookie_id: document.cookie,
      session_id: '',
      uid: uni.getStorageSync('uid'),
      // uid: JSON.parse(localStorage.uid)?.data,
    },
    platform: {
      os_type: 'h5',
      terminal: 'h5',
      log_version: 'v2_2'
      // ip: uni.getStorageSync('contract_address')
    },
    page_view: {
      page_id: pageId,
      scenes,
      url: window.location.href,
      url_path: window.location.hash,
      source_path: localStorage.source_path,
      source_title: localStorage.source_title,
      query,
      event_time: Date.now()
    },
    event_type: eventType,
    behavior_type: behaviorType,
    behavior_content: behaviorContent,
    status
  });
}
tracker.opt.installUnloadHook(() => {
  slsTracker('', '', '', 'pageview', 'h5jump1', {}, 1);
  tracker.platformSend(tracker.assemblePayload());
});

export { slsTracker }
