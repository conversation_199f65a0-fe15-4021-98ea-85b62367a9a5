import tracker from './track';
import { getUuid, setStorage, getStorage } from '@/utils/publicTools';

const getCookieId = () => {
  let uuid = getStorage('uuid', true);
  if (uuid) return uuid;
  else {
    uuid = getUuid();
    setStorage('uuid', uuid, true);
    return uuid;
  }
};
// page_id  页面id
// scenes  场景（有作品展示的）
// query  搜索词
// event_type  事件类型(曝光 exposure，点击 click)
// behavior_type  点击事件的具体事件
// behavior_content{}
// status  操作的状态1/0,成功为1，失败为0
const slsTracker = (page_id, scenes, query, event_type, behavior_type, behavior_content, status) => {
  tracker.send({
    user: {
      cookie_id: getCookieId(),
      session_id: '',
      uid: JSON.parse(localStorage.getItem('uid'))?.data,
    },
    platform: {
      os_type: localStorage.getItem('platform'),
      terminal: 'h5',
      // ip: uni.getStorageSync('contract_address')
    },
    page_view: {
      page_id,
      scenes,
      url: window.location.href,
      url_path: window.location.hash,
      source_path: '',
      source_title: '',
      query,
      event_time: Date.now(),
    },
    event_type,
    behavior_type,
    behavior_content,
    status,
  });
};

tracker.opt.installUnloadHook(() => {
  slsTracker('', '', '', 'pageview', 'h5jump1', {}, 1);
  tracker.platformSend(tracker.assemblePayload());
});

export { slsTracker };