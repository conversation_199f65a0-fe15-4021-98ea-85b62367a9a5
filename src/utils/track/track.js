import SlsTracker from './web-track-browser.cjs';

const opts = {
  host: 'cn-shanghai.log.aliyuncs.com', // 所在地域的服务入口。例如cn-hangzhou.log.aliyuncs.com
  project: process.env.VUE_APP_TRACK, // Project名称。
  logstore: 'bury-dots', // Logstore名称。
  time: 10, // 发送日志的时间间隔，默认是10秒。
  count: 10, // 发送日志的数量大小，默认是10条。
  topic: 'topic', // 自定义日志主题。
  source: 'source',
  tags: {
    tags: 'tags',
  },
};

const tracker = new SlsTracker(opts);

export default tracker;
