@function px($px) {
  @return $px * 0.75 + px;
}
:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgb(199, 195, 195);
  background-color: #0A0A0A;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: 'MiSans';
	src: url(./assets/font/MiSans-Semibold.ttf); 
}

@font-face {
	font-family: 'MiSans-bold';
	src: url(./assets/font/MiSans-Bold.ttf); 
}

@font-face {
	font-family: 'MiSans-normal';
	src: url(./assets/font/MiSans-Normal.ttf); 
}

@font-face {
	font-family: 'MiSans-thin';
	src: url(./assets/font/MiSans-Thin.ttf); 
}

@font-face {
	font-family: 'MiSans-regular';
	src: url(./assets/font/MiSans-Regular.ttf); 
}

@font-face {
	font-family: 'MiSans-medium';
	src: url(./assets/font/MiSans-Medium.ttf); 
}

@font-face {
	font-family: 'MiSans-dbold';
	src: url(./assets/font/MiSans-Demibold.ttf); 
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  /* min-width: 320px; */
  min-width: 1440px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1; 
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
} 
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  margin: 0 auto;
  width: 100vw;
  /* max-width: 1317px; */
  /* padding: 2rem; */
  text-align: center;
}

/* @media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
} */

/* 通用工具类 */
.flex {
  display: flex;
}

.flex_column {
  flex-direction: column;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex_wrap {
  flex-wrap: wrap;
}

.flex_1 {
  flex: 1;
}

/* 间距类 */
@for $i from 2 through 40 {
  @if $i % 2 == 0 {
    .m_#{$i} { margin: px($i); }
    .p_#{$i} { padding: px($i); }
  }
}

@for $i from 2 through 300 {
  @if $i % 2 == 0 {
    .mt_#{$i} { margin-top: px($i); }
    .mb_#{$i} { margin-bottom: px($i); }
    .ml_#{$i} { margin-left: px($i); }
    .mr_#{$i} { margin-right: px($i); }
    .pt_#{$i} { padding-top: px($i); }
    .pb_#{$i} { padding-bottom: px($i); }
    .pl_#{$i} { padding-left: px($i); }
    .pr_#{$i} { padding-right: px($i); }
  }
}
/* 字体大小类 */
@for $i from 10 through 60 {
  @if $i % 2 == 0 {
    .fz_#{$i} { font-size: px($i); }
  }
}
/* 圆角 */
@for $i from 10 through 60 {
  @if $i % 2 == 0 {
    .br_#{$i} { border-radius: px($i); }
  }
}
/* 文本类 */
.text_center { text-align: center; }
.text_left { text-align: left; }
.text_right { text-align: right; }

.text_bold { font-weight: bold; }
.text_normal { font-weight: normal; }

.text_ellipsis_1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text_ellipsis_2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

.text_ellipsis_3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

/* 定位类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* 显示类 */
.hidden { display: none; }
.block { display: block; }
.inline_block { display: inline-block; }

/* 溢出处理 */
.overflow_hidden { overflow: hidden; }
.overflow_auto { overflow: auto; }
.overflow_scroll { overflow: scroll; }

/* 边框类 */
.border_none { border: none; }
.border_1 { border: px(1) solid #4B4B4B; }
.border_radius_4 { border-radius: px(4); }
.border_radius_8 { border-radius: px(8); }

/* 阴影类 */
.box_shadow {
  box-shadow: 0 px(2) px(12) 0 rgba(0, 0, 0, 0.1);
}

/* 过渡动画 */
.transition {
  transition: all 0.3s ease;
}

/* 鼠标样式 */
.cursor_pointer {
  cursor: pointer;
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 背景色类 */
.bg_theme  { background-color: #EF88A3 ; }
.bg_cart { background-color: #222222; }
.bg_1 { background-color: #f5f5f5; }
.bg_1 { background-color: #409EFF; }
.bg_1 { background-color: #67C23A; }
