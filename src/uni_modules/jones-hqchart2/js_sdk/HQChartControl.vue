<template>
	<view>
		<!--  #ifdef  H5 -->
		<div class='kline' v-bind:id='KLineID' v-bind:style="{ width: ChartWidth + 'px', height: ChartHeight + 'px' }"
			ref='kline'></div>
		<!--  #endif -->

		<!--  #ifndef  H5 -->
		<div>
			<canvas v-bind:id='KLineID' v-bind:canvas-id='KLineID' class='kline2'
				v-bind:style="{ width: ChartWidth + 'px', height: ChartHeight + 'px' }" @touchstart="KLineTouchStart"
				@touchmove='KLineTouchMove' @touchend='KLineTouchEnd'></canvas>
		</div>
		<!--  #endif -->

		<!-- <view id='customtooltip' class='uni-flex uni-column customtooltip' :style="{ left: isleft ? '2px' : '200px' }"
			ref="customtooltip" v-show="TooltipISshow">
			<view class="time" ref="tooltiptime">{{ times }}</view>
			<view style="height: 26rpx;"></view>
			<view class="item">
				<view class="left">
					<view class="red"></view>
					<text class="open">开</text>
				</view>
				<view class="right" ref="tooltipprice">{{ openitem }}</view>
			</view>
			<view class="item">
				<view class="left">
					<view class="red"></view>
					<text class="open">高</text>
				</view>
				<view class="right" ref="tooltiphigh">{{ highitem }}</view>
			</view>
			<view class="item">
				<view class="left">
					<view class="green"></view>
					<text class="open">低</text>
				</view>
				<view class="right" ref="tooltiplow">{{ lowitem }}</view>
			</view>
			<view class="item">
				<view class="left">
					<view class="green"></view>
					<text class="open">收</text>
				</view>
				<text class="right" ref="tooltipclose">{{ closeitem }}</text>
			</view>

		</view> -->
	</view>
</template>

<script>

/*
	copyright (c) 2018 jones

	http://www.apache.org/licenses/LICENSE-2.0

	开源项目 https://github.com/jones2000/HQChart

	<EMAIL>

	HQChart简单的模板类
*/

// #ifdef H5
import HQChart from './umychart.uniapp.h5.js'
import './umychart.resource/font/iconfont.css'
// #endif

// #ifndef H5
import { JSCommon } from './umychart.wechat.3.0.js'
import { JSCommonHQStyle } from './umychart.style.wechat.js'
import { JSConsole } from './umychart.console.wechat.js'

//禁用日志
JSConsole.Complier.Log = () => { };
JSConsole.Chart.Log = () => { };
// #endif

function DefaultData() { }

DefaultData.GetKLineOption = function () {
	//K线配置信息
	let data =
	{
		Type: "历史K线图",   //创建图形类型
		Language: "EN",

		Windows: //窗口指标
			[
				// { Index: 'EMPTY', TitleHeight: 0 },
				{ Index: "MA", Modify: false, Change: false, TitleHeight: 0 },

				{ Index: "VOL", Modify: false, Change: false, Close: false, TitleHeight: 0 },
			],
		ExtendChart:    //扩展图形
			[
				// { Name: 'KLineTooltip', Create: function () { return CustomTooltip(); } }  //手机端tooltip
			],
		// EventCallback:
		//   [
		//     {
		//       event: HQChart.JSCHART_EVENT_ID.ON_CLICK_CHART_PAINT,
		//       callback: (event, data, obj) => {
		//         (event, data, obj) => {
		//           console.log(data, 'callbackdata');
		//         }
		//       }
		//     }
		//   ],
		IsAutoUpdate: true,              //是自动更新数据
		AutoUpdateFrequency: 1000,       //数据更新频率
		IsShowRightMenu: false,          //右键菜单
		IsApiPeriod: true,             //复权,周期都使用后台数据
		CorssCursorTouchEnd: true,
		StepPixel: 5,        //移动一个K线需要的手势移动的像素(默认4)
		ZoomStepPixel: 8,    //缩放一次,2个手指需要移动的间距像素(默认5)

		KLine:  //K线设置
		{
			DragMode: 1,                 //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择
			Right: 0,                    //复权 0 不复权 1 前复权 2 后复权
			Period: 0,                   //周期 0 日线 1 周线 2 月线 3 年线
			MaxReqeustDataCount: 1000,    //数据个数
			MaxRequestMinuteDayCount: 10, //分钟数据取5天
			// PageSize: 40,                 //一屏显示多少数据
			DataWidth: 12,
			IsShowMaxMinPrice: false,      // 最大最小值
			IsShowTooltip: false,          //是否显示 view K线提示信息 (手机端要填false)
			DrawType: 0,                   //K线类型 0=实心K线柱子 1=收盘价线 2=美国线 3=空心K线柱子 4=收盘价面积图
			RightSpaceCount: 1,
		},

		KLineTitle: //标题设置
		{
			IsShowName: false,       //显示股票名称
			IsShowSettingInfo: false, //显示周期/复权
		},

		Border: //边框
		{
			Left: 1,         //左边间距
			Right: 42,       //右边间距
			Bottom: 15,      //底部间距
			Top: 4,          //顶部间距
		},
		BorderLine: 2 | 8,
		Frame:  //子框架设置
			[
				{
					SplitCount: 5, IsShowLeftText: false, Height: 6, SplitType: 1,
					IsShowXLine: false, IsShowYLine: false,
					Custom: [{ Type: 0, Position: "right" }],
				},

				{
					SplitCount: 2, IsShowLeftText: false, Height: 2, IsShowIndexTitle: false,
					IsShowXLine: false, IsShowYLine: false, IsShowRightText: false,
				},
			],

		ExtendChart:    //扩展图形
			[
				// { Name: "KLineTooltip" },  //手机端tooltip
				// [
				// { Name: 'KLineTooltip', Create: function () { return new CustomTooltip(); } }  //手机端tooltip
				// ],
			],
	}

	return data
}

DefaultData.GetMinuteOption = function () {
	//K线配置信息
	var option =
	{
		Type: "历史K线图",   //创建图形类型
		Language: "EN",
		Windows: //窗口指标
			[
				{ Index: "EMPTY", TitleHeight: 0 },
				{
					Index: "VOL", Modify: false, Change: false, Close: false, TitleHeight: 0, YAxis: false
				},
				// {  Name: 'MA_NEW', Script:'MA5:VOL(AMOUNT,5);'},
			],
		IsCorssOnlyDrawKLine: true,
		IsAutoUpdate: false,              //是自动更新数据
		AutoUpdateFrequency: 1000,       //数据更新频率
		IsShowRightMenu: false,          //右键菜单

		IsApiPeriod: true,             //复权,周期都使用后台数据

		CorssCursorTouchEnd: true,
		//StepPixel:5,        //移动一个K线需要的手势移动的像素(默认4)
		//ZoomStepPixel:8,    //缩放一次,2个手指需要移动的间距像素(默认5)

		KLine:  //K线设置
		{
			DragMode: 1,                 //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择
			Right: 0,                    //复权 0 不复权 1 前复权 2 后复权
			Period: 7,                   //周期 0 日线 1 周线 2 月线 3 年线
			MaxReqeustDataCount: 1000,    //数据个数
			MaxRequestMinuteDayCount: 10, //分钟数据取5天
			PageSize: 60,                 //一屏显示多少数据
			IsShowTooltip: false,          //是否显示 view K线提示信息 (手机端要填false)
			DrawType: 4,                   //K线类型 0=实心K线柱子 1=收盘价线 2=美国线 3=空心K线柱子 4=收盘价面积图
			RightSpaceCount: 1,
		},

		KLineTitle: //标题设置
		{
			//IsShowName:true,       //显示股票名称
			//IsShowSettingInfo:true //显示周期/复权
		},

		Border: //边框
		{
			Left: 1,         //左边间距
			Right: 42,       //右边间距
			Bottom: 25,      //底部间距
			Top: 4,          //顶部间距
		},

		Frame:  //子框架设置
			[
				{
					SplitCount: 5, IsShowLeftText: false, Height: 12, SplitType: 1,
					IsShowXLine: false, IsShowYLine: false,
					Custom: [{ Type: 0, Position: "right" },],
				},

				{ SplitCount: 2, IsShowLeftText: false, Height: 4, IsShowRightText: false, IsShortTitle: false },
			],

		ExtendChart:    //扩展图形
			[
				// { Name: "KLineTooltip" },  //手机端tooltip
			],
	}

	return option
}

DefaultData.CreateGuid = function () {
	function S4() {
		return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
	}

	return "guid" + (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

var g_JSChart = new Map();
// #ifndef H5
var g_HQChart = { JSChart: null };	//图形实例
// #endif



export default
	{
		name: "HQChartControl",

		props:
			[
				'DefaultSymbol',
				'DefaultChart'
			],


		data() {
			let data =
			{
				tooltipTimer: null,
				isleft: true,
				boo: false,
				Chart: null,
				lowitem: 0,
				highitem: 0,
				closeitem: 0,
				openitem: 0,
				times: '',
				TooltipISshow: false,
				Symbol: '600000.sh',
				ChartWidth: 350,
				ChartHeight: 100,
				KLineID: "HQChart_" + DefaultData.CreateGuid(),

				KLine:
				{
					Option: DefaultData.GetKLineOption(),
				},

				Minute:
				{
					Option: DefaultData.GetMinuteOption(),
				},
				Tooltip:
				{
					High: '',
					Close: '',
					Low: '',
					Open: '',
					Name: "",
					Time: "",
					Date: "",
					Price: "",
					Vol: "",
					IsShow: true,
					IsShowTime: false,
					ColorPrice: 'rgb(0,255,0)',
				},
				ChartType: "Minute",
			};

			return data;
		},

		created() {
			if (this.DefaultSymbol) this.Symbol = this.DefaultSymbol; //默认股票
			if (this.DefaultChart) {
				//TODO:默认天数，周期在这里加
				if (this.DefaultChart.Type == 'Minute') {
					this.ChartType = "Minute";
				}
				else {
					this.ChartType = "KLine";
				}
			}
		},
		// beforeDestroy() {
		// 	// #ifndef H5
		// 	if (g_HQChart.JSChart) {
		// 		g_HQChart.JSChart.StopAutoUpdate();
		// 		g_HQChart.JSChart = null;
		// 	}
		// 	// #endif

		// },
		methods:
		{
			DrawTooltip(event, data, obj) {

				// console.log("[HQChartDemo::DrawTooltip] ", data, event, obj);
				var tooltip = this.$refs.customtooltip;

				if (data.Draw) {
					var item = data.Draw;
					//日期
					// console.log(item,'tooltip');

					// #ifndef H5
					this.times = `时间: ${JSCommon.IFrameSplitOperator.FormatDateString(item.Date)} ${JSCommon.IFrameSplitOperator.FormatTimeString(item.Time)}`; //格式化时间
					// #endif

					var tpTime = this.$refs.tooltiptime;
					// #ifdef H5

					if (HQChart.ChartData.IsMinutePeriod(this.Chart.JSChartContainer.Period, true))    //是否是分钟周期
					{
						this.times = '时间：' + `${HQChart.IFrameSplitOperator.FormatDateString(item.Date)} ${HQChart.IFrameSplitOperator.FormatTimeString(item.Time)}`; //格式化时间
					}
					else {
						this.times = '时间：' + HQChart.IFrameSplitOperator.FormatDateString(item.Date); //格式化时间
					}
					// #endif

					// #ifndef H5
					// this.times = JSCommon.IFrameSplitOperator.FormatDateString(item.Date); //格式化时间
					// #endif

					this.closeitem = item.Close.toFixed(3);
					this.highitem = item.High.toFixed(3);
					this.lowitem = item.Low.toFixed(3);
					this.openitem = item.Open.toFixed(3);
					//如果隐藏 数据还是要更新的
					// if (this.IsShowTooltip == false) { tooltip.style.display = 'none'; }
					// else { tooltip.style.display = 'inline' };
				}
				else {
					// tooltip.style.display = 'none';
				}

			},
			SetSize(width, height) {
				this.ChartWidth = width;
				this.ChartHeight = height;
			},

			GetJSChart() {
				if (g_JSChart.has(this.KLineID)) return g_JSChart.get(this.KLineID);
				return null;
			},

			SetJSChart(jsChart) {
				setTimeout(() => {
					g_JSChart.set(this.KLineID, jsChart)
				}, 0);
			},

			ClearChart() {
				console.log("[ClearChart] clear");
				var jsChart = this.GetJSChart();
				console.log(jsChart);

				if (jsChart) {
					jsChart.ChartDestory();
					this.SetJSChart(null);
				}

				// #ifdef H5
				var divKLine = document.getElementById(this.KLineID);
				while (divKLine.hasChildNodes()) {
					divKLine.removeChild(divKLine.lastChild);
				}
				// #endif
			},

			OnSize(option) {
				// #ifdef H5
				this.OnSize_h5(option);
				// #endif

				// #ifndef H5
				this.OnSize_app(option);
				// #endif
			},

			OnSize_h5(option) {
				var chartHeight = this.ChartHeight;
				var chartWidth = this.ChartWidth;

				var kline = this.$refs.kline;
				kline.style.width = chartWidth + 'px';
				kline.style.height = chartHeight + 'px';

				var jsChart = this.GetJSChart();
				if (jsChart) jsChart.OnSize(option);
			},
			OnSize_app(option) {
				var jsChart = this.GetJSChart();
				if (jsChart) {
					jsChart.CanvasElement.Width = this.ChartWidth;
					jsChart.CanvasElement.Height = this.ChartHeight;
					jsChart.OnSize(option);
				}
			},
			SetHQChartStyle() {
				// #ifdef H5
				this.SetHQChartStyle_h5();
				// #endif

				// #ifndef H5
				this.SetHQChartStyle_app();
				// #endif

			},

			SetHQChartStyle_h5() {
				var blackStyle = HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
				HQChart.JSChart.SetStyle(blackStyle);
				//this.$refs.kline.style.backgroundColor=blackStyle.BGColor;	//div背景色设置黑色
			},

			SetHQChartStyle_app() {
				var blackStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
				console.log(blackStyle, 'blackStyle');
				blackStyle.Index.LineColor[0] = "rgb(255,255,255,0)"
				blackStyle.Index.LineColor[1] = "rgb(255,255,255,0)"
				blackStyle.Index.LineColor[2] = "rgb(255,255,255,0)"
				JSCommon.JSChart.SetStyle(blackStyle);
			},

			CreateHQChart() {
				this.SetHQChartStyle();

				if (this.ChartType == "Minute")
					return this.CreateMinuteChart();
				else
					return this.CreateKLineChart();
			},

			CreateKLineChart_h5()  //创建K线图
			{
				this.ClearChart();

				var chart = HQChart.JSChart.Init(this.$refs.kline);

				this.KLine.Option.Symbol = this.Symbol;
				this.KLine.Option.NetworkFilter = this.NetworkFilter;

				chart.SetOption(this.KLine.Option);

				this.SetJSChart(chart);
				this.Chart = chart;
				//!!!注册事件必须在setoption以后
				console.log(this.Chart, '123321123');

				// #ifdef H5
				this.Chart.AddEventCallback({ event: HQChart.JSCHART_EVENT_ID.ON_TITLE_DRAW, callback: (event, data, obj) => { this.DrawTooltip(event, data, obj); } });
				this.Chart.AddEventCallback(
					{
						event: HQChart.JSCHART_EVENT_ID.ON_PHONE_TOUCH,
						callback: (event, data, chart) => { this.OnClickHQChart(event, data, chart); }
					}
				);
				// #endif


				// //外部处理键盘消息
				// this.Chart.JSChartContainer.SuperOnKeyDown = this.Chart.JSChartContainer.OnKeyDown;   //备份下原来的键盘处理函数
				// this.Chart.JSChartContainer.OnKeyDown = (e) => { this.HQChartOnKeyDown(e); }
				return chart;
			},

			showTooltip() {
				// 首先清除之前的定时器
				if (this.tooltipTimer) {
					clearTimeout(this.tooltipTimer);
					this.TooltipISshow = false; // 还没到1秒，设为false
				}

				// 设置为true，假设之后超过1秒
				// this.TooltipISshow = true;

				// 1秒后检查是否保持为true
				this.tooltipTimer = setTimeout(() => {
					// 如果超时后，TooltipISshow 还是 true，则保持 true
					this.TooltipISshow = true;
					this.tooltipTimer = null; // 清除定时器
				}, 500);
			},
			OnClickHQChart(event, data, chart) {
				console.log('OnClickHQChart', data, event, chart, chart.Period);
				if (data.FunctionName == "OnTouchStart") {
					if (data.Drag.Start.X < 140) {
						this.isleft = false
					} else {
						this.isleft = true
					}
					console.log(this.isleft, 'isflef');

					// setTimeout(() => {
					// 	this.TooltipISshow = true
					// }, 1000);
					this.showTooltip()
				} else if (data.FunctionName == "OnTouchEnd") {
					// 清除定时器
					if (this.tooltipTimer) {
						clearTimeout(this.tooltipTimer);
						this.tooltipTimer = null;
					}
					this.TooltipISshow = false; // 隐藏 Tooltip
					// this.$emit('changeArea', chart.Period)
				}
			},
			CreateKLineChart_app() {
				// if (g_HQChart.JSChart) return;

				this.ClearChart();

				var element = new JSCommon.JSCanvasElement();
				// #ifdef APP-PLUS
				element.IsUniApp = true;	//canvas需要指定下 是uniapp的app
				// #endif

				element.ID = this.KLineID;
				element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
				element.Width = this.ChartWidth;

				var chart = JSCommon.JSChart.Init(element);
				this.KLine.Option.NetworkFilter = this.NetworkFilter;
				this.KLine.Option.Symbol = this.Symbol;
				this.KLine.Option.IsClickShowCorssCursor = true;
				this.KLine.Option.IsFullDraw = true; 	//每次手势移动全屏重绘
				chart.SetOption(this.KLine.Option);

				this.SetJSChart(chart);

				// g_HQChart.JSChart = JSCommon.JSChart.Init(element);

				chart.AddEventCallback({
					event: JSCommon.JSCHART_EVENT_ID.ON_TITLE_DRAW,
					callback: (event, data, obj) => { this.DrawTooltip(event, data, obj); }
				});

				chart.AddEventCallback(
					{
						event: JSCommon.JSCHART_EVENT_ID.ON_PHONE_TOUCH,
						callback: (event, data, chart) => { this.OnClickHQChart(event, data, chart); }
					}
				);



				return chart;

			},

			CreateKLineChart() {
				this.ChartType = "KLine";

				// #ifdef H5
				return this.CreateKLineChart_h5();
				// #endif

				// #ifndef H5
				return this.CreateKLineChart_app();
				// #endif
			},

			CreateMinuteChart_h5() //创建日线图
			{
				this.ClearChart();

				var chart = HQChart.JSChart.Init(this.$refs.kline);
				this.Minute.Option.Symbol = this.Symbol;
				this.Minute.Option.NetworkFilter = this.NetworkFilter;
				chart.SetOption(this.Minute.Option);

				this.SetJSChart(chart);

				return chart;
			},

			CreateMinuteChart_app() {
				this.ClearChart();

				var element = new JSCommon.JSCanvasElement();
				// #ifdef APP-PLUS
				element.IsUniApp = true;	//canvas需要指定下 是uniapp的app
				// #endif
				element.ID = this.KLineID;
				element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
				element.Width = this.ChartWidth;

				var chart = JSCommon.JSChart.Init(element);

				this.Minute.Option.NetworkFilter = this.NetworkFilter;
				this.Minute.Option.Symbol = this.Symbol;
				this.Minute.Option.IsFullDraw = true; 	//每次手势移动全屏重绘
				chart.SetOption(this.Minute.Option);

				this.SetJSChart(chart);

				return chart;
			},

			CreateMinuteChart() {
				this.ChartType = "Minute";

				// #ifdef H5
				return this.CreateMinuteChart_h5();
				// #endif

				// #ifndef H5
				return this.CreateMinuteChart_app();
				// #endif
			},

			IsKLineChart() {
				var jsChart = this.GetJSChart();
				if (!jsChart) return false;
				var className = jsChart.JSChartContainer.ClassName;
				if (className == "KLineChartContainer" || className == "KLineChartHScreenContainer") return true;

				return false;
			},

			IsMinuteChart() {
				var jsChart = this.GetJSChart();
				if (!jsChart) return false;
				var className = jsChart.JSChartContainer.ClassName;
				if (className == "MinuteChartContainer" || className == "MinuteChartHScreenContainer") return true;

				return false;
			},

			//K线周期切换
			ChangeKLinePeriod(period) {
				var jsChart = this.GetJSChart();
				this.KLine.Option.KLine.Period = period;
				console.log(this.IsKLineChart(), jsChart)
				if (this.IsKLineChart() && jsChart) {
					jsChart.ChangePeriod(period);
				} else {
					// jsChart.ChangePeriod(period);
					this.CreateKLineChart();
				}
			},

			//走势图多日切换
			ChangeMinutePeriod(dayCount) {
				var jsChart = this.GetJSChart();
				this.Minute.Option.DayCount = dayCount;
				if (this.IsMinuteChart() && jsChart) {
					jsChart.ChangeDayCount(dayCount);
				}
				else {
					this.CreateMinuteChart();	//类型不对 删了重建
				}
			},

			//切换指标 windowId=窗口索引 name=指标名字
			ChangeKLineIndex(windowId, name) {
				var jsChart = this.GetJSChart();
				if (this.IsKLineChart() && jsChart) jsChart.ChangeIndex(windowId, name);
			},

			//切换股票
			ChangeSymbol(symbol) {
				this.Symbol = symbol;
				var jsChart = this.GetJSChart();
				if (jsChart) jsChart.ChangeSymbol(symbol);
			},

			ChangeRight(right) {
				var jsChart = this.GetJSChart();

				if (this.IsKLineChart() && jsChart) jsChart.ChangeRight(right);
			},

			ChangeKLineDrawType(id) {
				var jsChart = this.GetJSChart();

				if (this.IsKLineChart() && jsChart) jsChart.ChangeKLineDrawType(id);
			},

			///////////////////////////////////////////////
			//手势事件 app/小程序才有
			//KLine事件
			KLineTouchStart(event) {
				// console.log(event);
				// this.TooltipISshow = true
				var jsChart = this.GetJSChart();
				if (jsChart) jsChart.OnTouchStart(event);
			},

			KLineTouchMove(event) {
				var jsChart = this.GetJSChart();
				if (jsChart) jsChart.OnTouchMove(event);
			},

			KLineTouchEnd(event) {
				// this.TooltipISshow = false
				var jsChart = this.GetJSChart();
				if (jsChart) jsChart.OnTouchEnd(event);
			},

			/////////////////////////////////////////////////////////////////////////////////////////
			NetworkFilter(data, callback) {
				console.log(`[HQChartTemplate:NetworkFilter] Name=${data.Name} Explain=${data.Explain}`);
			},

		},

	}


</script>

<style>
.left {
	display: flex;
}

.right {
	font-weight: 400;
	font-size: 22rpx;
	color: #FFFFFF;
}

.green {
	width: 6rpx;
	height: 20rpx;
	background: #6CFF8A;
}

.red {
	width: 6rpx;
	height: 20rpx;
	background: #EC4068;
}

.open {
	margin-left: 6rpx;
	font-weight: 400;
	font-size: 22rpx;
	color: #FFFFFF;
}

.item {
	padding: 0 10rpx;
	width: 100%;
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.customtooltip {
	display: flex;
	flex-direction: column;
	/* justify-content: center; */

	width: 278rpx;
	height: 270rpx;
	/* background: #141816; */
	background: rgba(20, 24, 22, 0.8);
	border-radius: 30rpx;
	/* opacity: 0.8; */
	align-items: center;
	position: absolute;
	/* border: 1px solid #000000; */
	/* background-color: #FFFAFA; */
	/* padding: 7px 7px 7px 7px; */
	padding: 24rpx 19rpx;
	top: 140px;
	/* //height: 200px; */
	Z-index: 99;
	font-size: 20rpx;
}

.time {
	all: initial;
	font-weight: bold;
	font-size: 20rpx;
	color: rgba(255, 255, 255, 1);
	opacity: 1 !important;
}

.tooltip_label {
	text-align: left;
	display: block;
	line-height: 15px;
}

.tooltip_name {
	text-align: center;
	display: block;
	line-height: 15px;
}

.tooltip_time {
	text-align: right;
	display: block;
	line-height: 15px;
}

.tooltip_price {
	text-align: right;
	display: block;
	line-height: 15px;
}

.tooltip_vol {
	text-align: right;
	display: block;
	line-height: 15px;
}
</style>
