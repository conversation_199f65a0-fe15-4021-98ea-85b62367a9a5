import {
  uniqueId
} from 'lodash'

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
function supplementPath(menu) {
  return menu.map(e => ({
    ...e,
    path: e.path || uniqueId('d2-menu-empty-'),
    ...e.children ? {
      children: supplementPath(e.children)
    } : {}
  }))
}
// 头部
export const menuHeader = supplementPath([

])
// 侧边栏
export const menuAside2 = supplementPath([{
    path: '/index',
    title: '首页',
    icon: 'home'
  },
    {
      title: '群聊管理',
      icon: 'folder-o',
      children: [{
          path: '/weChatCode',
          title: '微信二维码'
        },{
          path: '/ChatList',
          title: '群聊列表'
        },
        {
          path: '/ChatRecord',
          title: '群聊记录'
        },
        {
          path: '/ReportList',
          title: '举报列表'
        },
        {
          path: '/ReportListNew',
          title: '举报列表(新)'
        },
      ]
    },
    {
      path: '/user_details?type=user&id=',
      title: '修改密码',
      icon: 'home'
    },
])
