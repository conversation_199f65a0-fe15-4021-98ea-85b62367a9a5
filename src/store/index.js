import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);
const store = new Vuex.Store({
  state: {
    userInfo: { name: "xxx" },
    hasLogin: false,
    token: "",
    address: "",
    Depths: {},
    realprice: "",
    side: "BUY",
    klinedata: [],
    klineid: "",
    indexprice: "",
    fundrate: "",
    optionSymbol: "",  //所有的表的symbol
  },
  mutations: {
    changeOPtionSymbol(state, val) {
      state.optionSymbol = val
    },
    changeIndexPrice(state, val) {
      state.indexprice = val;
    },
    changeFundRate(state, val) {
      state.fundrate = val;
    },
    changeKlineid(state, val) {
      state.klineid = val;
    },
    changeKline(state, val) {
      state.klinedata = val;
    },
    changeSide(state, val) {
      state.side = val;
    },
    changePrice(state, val) {
      state.realprice = val;
    },
    changeDepths(state, val) {
      state.Depths = val;
    },
    login(state, provider) {
      state.hasLogin = true;
      state.userInfo.userName = provider.userName;
      state.userInfo.userPass = provider.userPass;
      uni.setStorage({
        key: "uerInfo",
        data: provider,
      });
    },
    tokenStorage(state, provider) {
      state.token = provider;
      uni.setStorage({
        key: "token",
        data: provider,
      });
    },
    GetAddress(state, provider) {
      state.address = provider.address;
    },
    // setUser(state, user) {
    // 	state.user = user
    // 	console.log('存储用户成功', state.user)
    // },
  },
});
export default store;
