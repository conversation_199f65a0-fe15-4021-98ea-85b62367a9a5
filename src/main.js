// Vue
import Vue from 'vue'
import i18n from './i18n'
import App from './App'
// 核心插件
import d2Admin from '@/plugin/d2admin'
// store
import store from '@/store/index'
// video
// import Video from 'video.js'
import 'video.js/dist/video-js.css'
// 菜单和路由设置
import router from './router'
import { menuHeader, menuAside } from '@/menu'
import { menuAside2 } from '@/menu2'
import { menuAside3 } from '@/menu3'
import { frameInRoutes } from '@/router/routes'
import * as FarmerApi from '@/api'
import { mapState, mapActions } from 'vuex'
import debounce from '../src/mixins/debounce'
Vue.mixin(debounce)
// 核心插件
Vue.use(d2Admin)
Vue.prototype.$api = FarmerApi

new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
  created () {
    let username =   localStorage.getItem('username')
    // 处理路由 得到每一级的路由设置
    this.$store.commit('d2admin/page/init', frameInRoutes)
    // 设置顶栏菜单
    this.$store.commit('d2admin/menu/headerSet', menuHeader)
    // 设置侧边栏菜单
    if(username == '鸡哥'){
      this.$store.commit('d2admin/menu/asideSet', menuAside2)
    }else if(username == '我执'||username == 'bk'){
       this.$store.commit('d2admin/menu/asideSet', menuAside3)
    }else{
       this.$store.commit('d2admin/menu/asideSet', menuAside)
    }
    // 初始化菜单搜索功能
    this.$store.commit('d2admin/search/init', menuAside)
    console.log(menuAside2)
  },
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ]),
  },
  mounted () {
    //初始化设置尺寸
    // this.$store.dispatch('d2admin/size/set', 'mini')
    // 展示系统信息
    this.$store.commit('d2admin/releases/versionShow')
    // 用户登录后从数据库加载一系列的设置
    this.$store.dispatch('d2admin/account/load')
    // 获取并记录用户 UA
    this.$store.commit('d2admin/ua/get')
    // 初始化全屏监听
    this.$store.dispatch('d2admin/fullscreen/listen')


  }
}).$mount('#app')
