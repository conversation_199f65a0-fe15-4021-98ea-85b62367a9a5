import { createApp } from "vue";
import "./style.scss";
import App from "./App.vue";
import router from "./router";
import "lib-flexible-computer";
import "amfe-flexible/index";
import Vue3Marquee from "vue3-marquee";
import i18n from "./locales"; // 引入 i18n 配置
// import vuetify from "./plugins/vuetify";

// import "vuetify/styles";
// import { createVuetify } from "vuetify";
// import * as components from "vuetify/components";
// import * as directives from "vuetify/directives";

// const vuetify = createVuetify({
//   components,
//   directives,
// });
const token = localStorage.getItem("token");
if (token) {
  const payloadBase64 = token.split(".")[1];
  const payload = JSON.parse(
    decodeURIComponent(
      atob(payloadBase64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    )
  );
  console.log(payload);
  localStorage.setItem("uid", payload.uid);
  const nowAt = Date.now() / 1000; // 当前时间（秒）
  if (payload.exp && nowAt > payload.exp) {
    localStorage.removeItem("token");
  }
}

// console.log(payload.exp, nowAt, "时间");

const app = createApp(App);
// app.use(vuetify);
app.use(i18n);
app.use(Vue3Marquee);
app.use(router);
app.mount("#app");
