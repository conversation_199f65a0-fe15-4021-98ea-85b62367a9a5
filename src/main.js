import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import Vant from "vant";
import "vant/lib/index.css";
import "@/styles/reset.css";
import { slsTracker } from "./utils/track/tracker";

// import TIM from 'tim-js-sdk';//注释tim
// import TIMUploadPlugin from 'tim-upload-plugin';//注释tim

// import TIMProfanityFilterPlugin from 'tim-profanity-filter-plugin';

// let options = {
//   SDKAppID: 1400709264 // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
// };
// // // 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
// let tim = TIM.create(options); // SDK 实例通常用 tim 表示

// // // 设置 SDK 日志输出级别，详细分级请参见 setLogLevel https://web.sdk.qcloud.com/im/doc/zh-cn/SDK. #setLogLevel 接口的说明</a>
// tim.setLogLevel(1); // 普通级别，日志量较多，接入时建议使用
// // tim.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用

// // 注册腾讯云即时通信 IM 上传插件
// tim.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});

if (process.env.VUE_APP_SHOW_CONSOLE === "true") {
  const VConsole = require("vconsole");
  new VConsole();
}

const token = localStorage.getItem("token");
if (token) {
  const payloadBase64 = token.split(".")[1];
  const payload = JSON.parse(
    decodeURIComponent(
      atob(payloadBase64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    )
  );
  console.log(payload);
  localStorage.setItem("uid", payload.uid);
  const nowAt = Date.now() / 1000; // 当前时间（秒）
  if (payload.exp && nowAt > payload.exp) {
    localStorage.removeItem("token");
  }
}

const app = createApp(App);
app.use(store).use(router).use(Vant);
// 全局挂载
app.config.globalProperties.$slsTracker = slsTracker;
// app.config.globalProperties.tim = tim;//注释tim
// app.config.globalProperties.TIM = TIM;//注释tim
app.mount("#app");
