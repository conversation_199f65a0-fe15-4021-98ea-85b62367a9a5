import Vue from 'vue'
import App from './App'
import store from '@/store'
import api from '@/common/api/' //数据请求
import router from './router/index.js'
import jsrsasign from 'jsrsasign'
import * as native from '@/utils/native'
import uView from '@/uni_modules/uview-ui'
import CryptoJS from 'crypto-js'

// #ifdef H5
import * as uni from './utils/uni.webview.1.5.4.js'
// #endif

// import { Slider } from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';

// Vue.component("popupBar", popupBar)
// 引入全局uView
//挂在原型
Vue.prototype.$native = native
Vue.prototype.$store = store
Vue.prototype.$api = api
Vue.use(uView)
// Vue.use(Slider)

//判断是否是微信环境
Vue.prototype.$toast = function(title, state) {
	uni.showToast({
		title: title,
		icon: "none",
		duration: 2000,
		mask: state || false //是否显示透明蒙层，防止触摸穿透
	});
}
Vue.prototype.$setUser = function(user) {
	let that = this
	that.$store.state.user = user
	that.$setStorageSync('user', user)
}
Vue.prototype.$getUser = async function() {
	let that = this
	let user = that.$store.state.user
	if (!user) {
		let res = await uni.getStorageSync('user')
		if (res) { //已注册过
			user = res
		}
	}
	that.$setUser(user)
	return user
}
// 设置缓存+store
Vue.prototype.$setStorageSync = function(key, value) {
	try {
		uni.setStorageSync(key, value);

	} catch (e) {
		console.log(`设置${key}缓存失败`)
	}
}
// 获取缓存
Vue.prototype.$getStorageSync = function(key, fn) {
	try {
		const value = uni.getStorageSync(key);
		if (value) {
			fn(value)
		}
	} catch (e) {
		// error
	}
}
//页面右上角文字点击，放在这里统一管理
Vue.prototype.$onTap = function() {
	console.log('点击le')

}
//获取系统信息
Vue.prototype.$getSystemInfo = function() {
	uni.getSystemInfo({
		success: function(res) {
			console.log('当前平台？')
			console.log(res)
			// localStorage.setItem('platform', res.platform);
			let model = res.model
			Vue.prototype.$isIphoneX = model.search('iPhoneX') != -1 || model.search('iPhone X') != -1;

			Vue.prototype.$isIphone = model.platform != "android";
			Vue.prototype.$screenH = res.screenHeight
		}
	});
}
Vue.prototype.$getSystemInfo()
Vue.prototype.$CryptoJS = CryptoJS
console.log('$isIphoneX', Vue.prototype.$isIphoneX)

// #ifdef H5
	document.addEventListener("UniAppJSBridgeReady", function() {
	    Vue.prototype.myUni = uni  
	});
// #endif
App.mpType = 'app'
const app = new Vue({
	store,
	router,
	...App
})
app.$mount()