<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import util from '@/libs/util'

export default {
  name: 'app',
  watch: {
    '$i18n.locale': 'i18nHandle'
  },

  created() {
    this.i18nHandle(this.$i18n.locale)
  },
  methods: {
    i18nHandle(val, oldVal) {
      util.cookies.set('lang', val)
      document.querySelector('html').setAttribute('lang', val)
    }
  }
}
</script>

<style lang="scss">
@import '~@/assets/style/public-class.scss';

.el-table th.el-table__cell {
  user-select: inherit !important;
}

.el-message-box {
  & .el-message-box__message p {
    word-break: break-all;
  }
}
#app{
    // transform: scale(0.8);
    // transform-origin: top left;
    // width: 125%; /* 由于缩放，需要增加宽度来适配 */
    // height: 125%; /* 由于缩放，需要增加高度来适配 */
    // overflow: hidden; /* 可选，隐藏超出容器的内容 */
}

</style>
