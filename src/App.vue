<script>
const {
	VUE_APP_PAY_BACK,
	VUE_APP_COLLECTION_PATH,
	VUE_APP_UPLOAD_URL,
	VUE_APP_JAVA_UPLOADIMAGE,
	VUE_APP_JAVA_UPLOADVIDEO
} = process.env;
import { startSocket, onSocketOpen } from "@/utils/websockets"

export default {
	globalData: {
		userInfo: [], //建议存放store
		isIPX: false, //是否为iphone X
		isIphone: false,
		urlcollectionUrl: VUE_APP_COLLECTION_PATH, //测试 藏品室地址
		uploadUrl: VUE_APP_UPLOAD_URL, // 图片上传
		java_uploadImage: VUE_APP_JAVA_UPLOADIMAGE, //java  图片上传
		java_uploadVideo: VUE_APP_JAVA_UPLOADVIDEO, //java  视频上传

		Imurl: 'http://web-test.nftcn.com/',
		url: 'http://web-test.nftcn.com.cn/h5/#/', //测试
		urlZf: 'http://web-test.nftcn.com.cn/orderView/#/', //zf测试
		activeUrl: 'http://web-test.nftcn.com.cn/active/',//活动测试
		exhibitionUrl: 'http://web-test.nftcn.com.cn/3dlink/#/exhibition',//测试3d
		apiUrl: 'http://api-test.nftcn.com.cn/nms/dubbo/',//测试 api
		socketApi: "ws://api-test.nftcn.com.cn:81/kline-api/ws",
		newsocketApi: "ws://api-test.nftcn.com.cn:82/kline-api"

		//发版APP必改
		// Imurl:'https://www.nftcn.com/',
		// url: 'https://www.nftcn.com/h5/#/', //APP端线上 url
		// urlZf: 'https://www.nftcn.com/orderView/#/', //APP端 支付线上url
		// activeUrl: 'https://www.nftcn.com/active/',//APP端活动线上url
		// exhibitionUrl: 'https://www.nftcn.com/3dlink/#/exhibition',//APP端线上3d
		// apiUrl: 'https://api-pro.nftcn.com.cn/nms/dubbo/',//APP端线上api
		// socketApi: "wss://api-pro.nftcn.com.cn:81/kline-api/ws",//APP端线上
		// newsocketApi: "wss://api-pro.nftcn.com.cn:82/kline-api/ws"

	},
	onLaunch: function () {
		// console.log("首次打开app触发，其他时候不会触发")
		// startSocket()
		uni.setStorageSync('isbitpage', 1)
		uni.setStorageSync('isB5', 'h5')

		// #ifdef APP
		let appType = uni.getSystemInfoSync().platform
		const UmPush = uni.requireNativePlugin("spx-umpush");
		UmPush.initUmPush((res) => {
			console.log(res);
			uni.setStorageSync('deviceToken', res.deviceToken)
		})
		console.log(UmPush)
		if (appType == 'android') {
			UmPush.setCustomPushChannel({
				soundName: "nftcnsound",//铃声名称，无需文件后缀
				channelId: "1",
				channelName: '测试',
				channelDesc: "渠道描述",
				enableLights: true,
				enableVibration: true,
				importance: 3,
				lockscreenVisibility: 0
			}, (errorCB) => {
				console.log(errorCB)
			});
			UmPush.onPushMessage((res) => {
				console.log(res);
			});
		}
		uni.hideTabBar();
		// #endif

		// // #ifdef APP
		// // 推送SDK初始化，注意只有用户同意隐私协议后，才能传agreement=1，否则可能APP会被下架
		// const upush = uni.requireNativePlugin('nrb-upush-plugin')

		// // 监听消息透传，Android点击离线通知后才能收到回调
		// upush && upush.addCustomMessageListener( res => {
		//     console.log("upush callback:  res="+JSON.stringify(res))
		// })

		// // 再初始化
		// upush && upush.init({"agreement":"0"}, res => {
		// 	console.log(res)
		//     console.log("upush init result for Android:  res="+JSON.stringify(res))
		// })

		// //用户同意隐私协议后，调用延迟初始化实际初始化，device_token在init方法传入的代码块中回调
		// upush.delayInit({

		// })
		// // #endif
	},
	onShow: function () {
		// console.log('App Show');
		// #ifdef APP
		setTimeout(() => {
			uni.hideTabBar({
				animation: false // 是否需要动画效果，默认为false
			});
		}, 400)
		// #endif
		// #ifdef H5
		uni.hideTabBar({
			animation: false // 是否需要动画效果，默认为false
		});
		// #endif

	},
	onHide: function () {
		// uni.closeSocket()
		// console.log('App Hide');
	},
	mounted() {

	},
	methods: {
	}
};
</script>
<style lang="scss">
@import "common/css/pages/reset.css";
@import "./styles/public.css";
@import "uni_modules/uview-ui/index.scss";

uni-app.inapp uni-page-head {
	display: none;
}

.flex_view {
	width: 123rpx;
	height: 123rpx;
	position: fixed;
	right: 0rpx;
	bottom: 120rpx;

	img {
		width: 100%;
		height: auto;
	}
}

// 公共样式
/*每个页面公共css */
/* #ifdef H5 */
@font-face {
	font-family: 'HarmonyOS';
	src: url(https://cdn-lingjing.nftcn.com.cn/h5/ttf/HarmonyOS.ttf);
}

html,
body {
	font-family: 'HarmonyOS';
}

/* #endif */


page {
	background-color: var(--main-bg-color);
}

html,
body {
	// filter: grayscale(1);
}

.iconfont {
	font-family: 'iconfont' !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-webkit-text-stroke-width: 0.2px;
	-moz-osx-font-smoothing: grayscale;

}

uni-page-body,
page,
uni-app {
	box-sizing: border-box;
	color: $uni-text-color;
	font-size: 32rpx;
}

//btn激活色
.status_bar_height {
	width: 100%;
	height: var(--status-bar-height);
}

.btn_active {
	width: 670rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
	color: #FFFFFF;
	background-color: $uni-text-color-inverse;
	margin: 40rpx auto 0;
	box-shadow: 0px 2px 8px 0px rgba(0, 208, 90, 0.5);
	border-radius: 52rpx;
}

.placeholder {
	color: $uni-text-color-grey;
	font-weight: 500;
}

// 单个页面设置背景色
.bg_grey_Color {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: $uni-bg-color-grey;
	z-index: -2;
}

// title下方10px灰色背景
.rule10px {
	height: 20rpx;
	background-color: $uni-bg-color-grey;
}

::-webkit-scrollbar {
	display: none;
}

image {
	will-change: transform; //优化图片加载缓慢，不知是否有用
	display: block;
	height: auto; //优化图片加载完成前变形
	box-sizing: border-box;
}

view {
	box-sizing: border-box;
}

::v-deep .u-model {
	background-color: #35333E !important;

	.text_msg {
		color: #FFFFFF !important;
	}
}

::v-deep .u-toast.u-show {
	background-color: rgba(0, 0, 0, 0.7) !important;
	color: #F9F9F9;
	border-radius: 40rpx !important;
	opacity: 0.85;
	padding: 40rpx 40rpx;
}

// 等分布局
.flex_divide {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.item_divide {
	flex: 1
}

.flex_start {
	display: flex;
	display: -webkit-flex;
	justify-content: flex-start;
	-webkit-justify-content: flex-start;
	align-items: center;
	-webkit-align-items: center;
}

/*弹性布局：水平垂直居中*/
.flex_all {
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
}

//弹性布局：从末尾开始
.flex_end {
	display: flex;
	display: -webkit-flex;
	justify-content: flex-end;
	-webkit-justify-content: flex-end;
	align-items: center;
	-webkit-align-items: center;
}

/*水平居中弹性布局*/
.flex_x {
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
}

/* 垂直居中 */
.flex_y {
	display: flex;
	display: -webkit-flex;
	align-items: center;
	-webkit-align-items: center;
}

/* 水平分散对齐 */
.flex_between_x {
	display: flex;
	display: -webkit-flex;
	justify-content: space-between;
	-webkit-justify-content: space-between;
}

/* 上下分散对齐 */
.flex_between_y {
	display: flex;
	display: -webkit-flex;
	align-content: space-between;
	-webkit-align-content: space-between;
}

.iphoneX_bottom {
	height: 68rpx;
	width: 100%;
}

/* 清除浮动 */
.clear {
	zoom: 1;
}

.clear:after {
	content: '';
	display: block;
	clear: both;
}

/* 超出一行省略 */
.oneOver {
	display: inline-block;
	width: 86rpx;
	/*超出部分隐藏*/
	white-space: nowrap;
	overflow: hidden;
	/*不换行*/
	text-overflow: ellipsis;
	/*超出部分文字以...显示*/
}

/* 超出2行省略 */
.twoOver {
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
}

/* 超出3行省略 */
.threeOver {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

.sk-wave {
	position: relative;
	width: 50rpx;
	height: 50rpx;
	border: 3px solid #3cefff;
	overflow: hidden;
	animation: spin 3s ease infinite;
	margin: 40rpx auto;
	text-align: center;
	font-size: 20px;
}

.sk-wave {
	display: flex;
	width: 60rpx;
	height: 60rpx;
	margin: 40rpx auto;
	border: 3px solid transparent;
	border-top-color: #3cefff;
	border-bottom-color: #3cefff;
	border-radius: 50%;
	animation: spin 1.5s linear infinite;
}

.sk-wave:before {
	content: '';
	display: block;
	margin: auto;
	width: 0.75em;
	height: 0.75em;
	border: 3px solid #3cefff;
	border-radius: 50%;
	animation: pulse 1s alternate ease-in-out infinite;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

@keyframes pulse {
	from {
		transform: scale(0.5);
	}

	to {
		transform: scale(1);
	}
}

.loading_list {
	width: 100%;
	height: 45vh;
	display: flex;
	justify-content: center;
	align-items: center;

	.flex {
		display: flex;
		justify-content: center;
		align-items: center;

		.balls {
			width: 2.5em;
			height: 3em;
			border: 6rpx solid transparent;
			border-top-color: #3cefff;
			border-bottom-color: #3cefff;
			border-radius: 50%;
			animation: spin-stretch 2s ease infinite;
		}
	}

	.text {
		color: #fff;
		font-size: 22rpx;
		text-align: center;
		margin-top: 26rpx;
	}
}


@keyframes spin-stretch {
	50% {
		transform: rotate(360deg) scale(0.4, 0.33);
		border-width: 16rpx;
	}

	100% {
		transform: rotate(720deg) scale(1, 1);
		border-width: 6rpx;
	}
}

.grayscale {
	// filter: grayscale(100%);
	// -webkit-filter: grayscale(100%);
	// -moz-filter: grayscale(100%);
	// -ms-filter: grayscale(100%);
	// -o-filter: grayscale(100%);
	// -webkit-filter: grayscale(1);
}

.barHeight {
	height: var(--status-bar-height);
}
.blur {
		width: 593rpx;
		height: 593rpx;
		background: #A4A8FF;
		border-radius: 0px 0px 0px 0px;
		filter: blur(354.9288024902344rpx);
		position: fixed;
		top: -200rpx; 
		right: -200rpx;
		z-index: 0;
	}
	.bottom_blur {
		width: 593rpx;
		height: 593rpx;
		background: #A4A8FF;
		border-radius: 0px 0px 0px 0px;
		filter: blur(354.9288024902344rpx); 
		position: fixed;
		bottom: -200rpx;
		left: -200rpx;
		z-index: 0;
	}
	$bg: #0FA3B1;
	$text: #B5E2FA;
	$light: rgba(164, 168, 255, 1);
	$accent: lighten(#F7A072, 10%);
	#loading1 {
	  &, &::before, &::after {
	    position: absolute;
	    background: $light;
	    display: block;
	    content: '';
	    transform-origin: 50% 50%;
	  }
	  
	  & {
	    height: 1.8em;
	    width: 1.8em;
	     border-radius:(2em);
	     border-top-right-radius:(1.7em);
	     border-top-left-radius:(1.7em);
	    background: rgba($light, 0.5);
	    border: 0.2em solid $light;
	    border-top-width: 0.7em;
		transform-origin: top left right bottom;
	  }
	 
	  &::before, &::after {
	    opacity: 0;
	    width: 1em;  
	    bottom: 2.5em;
	    left: -14rpx;
	    background: transparent;
	    border-left: 1em solid $light;
	    border-top: .5em solid transparent;
	    border-bottom: .5em solid transparent;
	  }
	  
	  &::before {
	    animation: loading1 2s infinite;
	  }
	  
	  &::after {
	    animation: loading1 2s 1s infinite;    
	  }
	  transform: scale(0.7, 0.7);
	}
	@keyframes loading1 {
	  0% {
	    opacity: 1;
	    background: transparent;
	    border-left: 1em solid $light;
	    border-top: 0.5em solid transparent;
	    border-bottom: 0.5em solid transparent;
	    transform: translateX(-1em) rotateY(-90deg); //rotate(45deg);
	  }
	  16%, 34% {
	    // opacity: 1;
	    background: transparent;
	    border-left: 1em solid $light;
	    border-top: .5em solid transparent;
	    border-bottom: .5em solid transparent;
	  }
	  8%, 25%, 42% {
	    background: transparent;
	    border-left: 1em solid $light;
	    border-top: .2em solid transparent;
	    border-bottom: .2em solid transparent;
	  }
	  50% {
	    // opacity: 0;
	    background: transparent;
	    border-left: 1em solid $light;
	    border-top: .5em solid transparent;
	    border-bottom: .5em solid transparent;
	    transform: translateX(1.5em) rotateY(90deg);// rotate(-45deg);
	  }
	  64%, 86% {
	    // opacity: 1;
	    background: transparent;
	    border-left: 1em solid rgba($light, 0.5);
	    border-top: .3em solid transparent;
	    border-bottom: .3em solid transparent;
	  }
	  58%, 75%, 92% {
	    background: transparent;
	    border-left: 1em solid rgba($light, 0.5);
	    border-top: .2em solid transparent;
	    border-bottom: .2em solid transparent;
	  }
	  100% {
	    opacity: 1;
	    border-top: 0.5em solid transparent;
	    border-bottom: .5em solid transparent;
	    transform: translateX(-1em) rotateY(270deg); 
	  }
	}
/* 解决头条小程序组件内引入字体不生效的问题 */
/* #ifdef MP-TOUTIAO */
@font-face {
	font-family: uniicons;
	src: url('/static/uni.ttf');
}

/* #endif */
.loadding {
	display: flex;
	justify-content: center;
	align-items: center;

	.gif {
		image {
			width: 100rpx;
		}
	}
}
</style>
