<template>
	<router-view />
</template>

<script setup>
import { getStorage, setStorage } from '@/utils/publicTools';
import { useStore } from 'vuex';

const { state, commit } = useStore();
// 在页面加载时读取sessionStorage里的状态信息
const store = getStorage('vuex');
const { origin, hash } = window.location;
if (origin === 'https://pinkwallet.com/h5') {
	window.location.href = `https://pinkwallet.com/h5/active/${hash}`;
} else if (origin === 'http://test-www.pinkwallet.xyz/h5') {
	window.location.href = `http://test-www.pinkwallet.xyz/h5/active/${hash}`;
}
if (store) commit('setData', store);
// 在页面刷新时将vuex里的信息保存到sessionStorage里
window.addEventListener('beforeunload', () => {
	setStorage('vuex', state);
});
</script>

<style lang="scss">
@font-face {
	font-family: 'regular';
	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/Alibaba-PuHuiTi-Regular.ttf');
}

@font-face {
	font-family: "MiSans";
	src: url(./asstes/font/MiSans-Semibold.ttf);
}

@font-face {
	font-family: "MiSans-bold";
	src: url(./asstes/font/MiSans-Bold.ttf);
}

@font-face {
	font-family: "MiSans-normal";
	src: url(./asstes/font/MiSans-Normal.ttf);
}

@font-face {
	font-family: "MiSans-thin";
	src: url(./asstes/font/MiSans-Thin.ttf);
}

@font-face {
	font-family: "MiSans-regular";
	src: url(./asstes/font/MiSans-Regular.ttf);
}

@font-face {
	font-family: "MiSans-medium";
	src: url(./asstes/font/MiSans-Medium.ttf);
}

@font-face {
	font-family: "MiSans-dbold";
	src: url(./asstes/font/MiSans-Demibold.ttf);
}
</style>
<style>
.oneOver {
	display: inline-block;
	width: 86rpx;
	/*超出部分隐藏*/
	white-space: nowrap;
	overflow: hidden;
	/*不换行*/
	text-overflow: ellipsis;
	/*超出部分文字以...显示*/
}
</style>