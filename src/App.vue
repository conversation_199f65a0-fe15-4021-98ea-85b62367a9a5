<template>

  <CustomHead></CustomHead>
  <router-view />
  <CustomFoot></CustomFoot>
</template>


<script setup>
import CustomHead from './components/Head.vue'
import CustomFoot from './components/Foot.vue'

// import Head from "./components/Head"
components: {
  CustomHead,
    CustomFoot
}

let lang = localStorage.getItem('lang')
if (!lang) {
  localStorage.setItem('lang', 'en')
}
</script>


<style scoped>
  
</style>
