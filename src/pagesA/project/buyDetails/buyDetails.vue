<template>
	<view class="buyDetails">
		<view class="back" >
			<view @click="back">
				<image src="@/static/imgs/public/left.png" mode="widthFix"></image>
			</view>
			<view>{{name}}的买入明细</view>
			<view></view>
		</view>
		<view class="list" v-for="(item,index) in list" :key="index">
			<view>
				<image :src="item.cover" mode="aspectFill"></image>
				<view></view>
			</view>
			<view>
				<view class="lef">
					<view>{{item.title}}</view>
					<view @click="nav_series(item)">跟投</view>
				</view>
				<view class="rig">
					<view>
						<view>购买数量:{{item.buyNum}}</view>
						<view>购入均价:￥{{item.avgPrice}}</view>
					</view>
					<view>
						<view>{{item.lastBuyTime}}</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				height: 40,
				name: '',
				list: [],
				totalCount: 0,
				pageNum: 1,
				index: '',
				isLoadding:false,
				type:""
			}
		},
		onLoad(op) {
			this.name = op.name
			this.index = op.index
			this.type = op.type
			if (op.index) {
				this.getList()
			} else {
				uni.showToast({
					title: '该用户已设置隐私 不可查看TA的买入',
					icon: 'none'
				})
			}
		},
		methods: {
			async getList() { //买入清单
				let {
					status,
					result
				} = await this.$api.java_rankingBuyList({
					index: this.index,
					pageNum: this.pageNum,
					pageSize: 6,
					type:this.type
				})
				if (status.code == 0) {
					this.list = [...this.list, ...result.list]
					this.totalCount = result.totalCount
				}else if(status.code == 1002){
					this.isLoadding = true
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin"
						})
					}, 1500);
				}
			},
			back() {
				this.$Router.back()
			},
			nav_series(item) {
				if (uni.getStorageSync('is_platform')) {
					console.error(item.ctid)
					this.myUni.webView.navigateTo({
						url:`/pagesA/project/mall/seriesList?ctid=${item.ctid}`
					});
				} else {
					this.$Router.push({
						name: "seriesList",
						params: {
							ctid: item.ctid,
							title: item.title
						},
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.buyDetails {
		padding-bottom: 30rpx;
	}

	.back {
		width: 100%;
		color: #fff;
		font-weight: 900;
		font-size: 34rpx;
		color: #FFFFFF;
		text-align: center;
		margin-bottom: 50rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding:32rpx;
		box-sizing: border-box;

		>view:nth-child(1) {
			width: 26rpx;
			height: 50rpx;

			>image {
				width: 100%;
				left: 100%;
			}
		}
	}

	.list {
		width: 678rpx;
		height: 208rpx;
		background: #25232D;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		box-sizing: border-box;
		margin: 0 auto 40rpx auto;

		>view:nth-child(1) {
			width: 148rpx;
			height: 148rpx;
			background: #F5F5F5;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 24rpx;

			>image {
				width: 140rpx;
				height: 140rpx;
				background: #F5F5F5;
				border-radius: 8rpx;

			}
		}

		>view:nth-child(2) {
			flex: 1;
			height: 148rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.lef {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF;

				>view:nth-child(2) {
					width: 140rpx;
					height: 50rpx;
					line-height: 50rpx;
					background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
					border-radius: 30rpx;
					text-align: center;
					font-weight: bold;
					font-size: 24rpx;
					color: #25232D;
				}
			}

			.rig {
				display: flex;
				align-items: flex-end;
				justify-content: space-between;

				>view:nth-child(1) {
					font-weight: 400;
					font-size: 24rpx;
					color: #ccc;

					>view:nth-child(2) {
						margin-top: 20rpx;
					}
				}

				>view:nth-child(2) {
					width: 99rpx;
					height: 40rpx;
					text-align: center;
					border-radius: 20rpx;
					font-weight: 400;
					font-size: 18rpx;
					color: #63EAEE;
					border: 1rpx solid;
					border-radius: 8rpx;
					overflow: hidden;

					>view {
						width: 99rpx;
						height: 40rpx;
						line-height: 40rpx;
						color: #63EAEE;
						background: #25232D;
						border-image: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%) 1;
					}
				}
			}
		}
	}
</style>