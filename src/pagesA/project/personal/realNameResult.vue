<template>
	<view class="authentication">
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="实名结果" title-color="var(--main-front-color)" :custom-back="back">
		</u-navbar>
		<u-toast ref="uToast" />
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				response:{
					extInfo:{}
				},
				code:""
			}
		},
		onLoad() {
			 var url = new URL(window.location.href);
			            // 解析response
			this.response = JSON.parse(
				decodeURIComponent(url.searchParams.get('response'))
			);
			console.log(JSON.parse(decodeURIComponent(url.searchParams.get('response'))))
			console.log(this.response)
			if(this.response.subCode==='Z5055'){
				this.$Router.back();
			}else{
				this.completeFaceAuth()
			}
		},
		watch: {},
		methods: {
			async completeFaceAuth() {
				let isRealNameNavMall = uni.getStorageSync("isRealNameNavMall")
				uni.removeStorageSync("isRealNameNavMall")
				let res = await this.$api.java_completeFaceAuth({
					clientScene:1,
					certifyId:this.response.extInfo.certifyId
				});
				if (res.status.code == 0) {
					this.$refs.uTips.show({
						title: '实名认证成功',
						type: 'success',
						duration: '2300'
					})
					uni.showToast({
						title: '实名认证成功',
						duration: 2000
					});
					if(isRealNameNavMall==1){
						this.$Router.pushTab({
							name: "mall",
						})
					}else{
						this.$Router.pushTab({
							name: "personal",
						})
					}
					
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
					console.log(res.status.msg)
					setTimeout(()=> {
						this.$Router.back();
					}, 1500);
					
				}
			},
			back(){
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "personal"
					});
				} else {
					this.$Router.back();
				}
			}
		},
		mounted() {},
	}
</script>

<style lang="scss" scoped>
	
</style>
