<template>
    <view class="profile-container">
        <u-navbar :border-bottom="false" :title="$t('profile.title')">
        </u-navbar>
        <view class="info-section">
            <view style="height: 80rpx;"></view>

            <view class="info-item qrcode">
                <text class="left-info">{{ $t("profile.QRcode") }}</text>
                <view class="qr_div" @click="qrcodeShow = true">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="70rpx"
                        :options="options"></uv-qrcode>
                </view>
            </view>

            <view class="divide"></view>


            <view class="content">
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.KYCstatus") }}</text>
                    <!-- startVerification @click="openVerificationPopup"-->
                    <text v-if="kycstatus" class="pass">{{ $t("profile.Passed") }}</text>
                    <view v-else class="Verify" @click="startVerification('id-and-liveness')">{{ $t("profile.Verify") }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                    </view>
                </view>
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.KYBstatus") }}</text>
                    <text v-if="kybstatus" class="pass">{{ $t("profile.Passed") }}</text>
                    <view v-else class="Verify" @click="startVerification('basic-kyb-level')">{{ $t("profile.Verify") }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                    </view>
                </view>
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.MyName") }}</text>
                    <text v-if="userInfo.userName" class="pass">{{ userInfo.userName }}</text>
                    <view v-else class="Verify">{{ $t("profile.Verify") }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                    </view>
                </view>
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.IDtype") }}</text>
                    <text v-if="idType" class="pass">{{ idType }}</text>
                    <view v-else class="Verify" @click="startVerification('id-and-liveness')">{{ $t("profile.Verify") }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                    </view>
                </view>
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.IDnumber") }}</text>
                    <text v-if="idNumber" class="pass">{{ maskId(idNumber) }}</text>
                    <view v-else class="Verify" @click="startVerification('id-and-liveness')">{{ $t("profile.Verify") }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                    </view>

                </view>
                <view class="info-items">
                    <text class="left-info">{{ $t("profile.Email") }}</text>
                    <view class="flex_x">
                        <view v-if="userInfo.email" class="pass flex_y">{{ userInfo.email }}
                            <view class="change-link" @click="emailPopup = true">{{ $t("profile.Edit") }}</view>
                        </view>
                        <view v-else class="Verify">{{ $t("profile.Verify") }}
                            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                        </view>

                    </view>
                </view>
                <!-- <view class="info-item">
                    <text class="left-info">Phone:</text>
                    <view class="flex_x">
                        <text v-if="veify.name" class="pass">+86 123***3434
                            <view class="change-link">更换</view>


                        </text>
                        <view v-else class="Verify">{{ $t("profile.Verify") }}
                            <image
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370062006622183424.png" />
                        </view>
                    </view>
                </view> -->
            </view>

        </view>

        <!-- <u-button hover-class="none" class="logout-button" @click="logout">退出登录</u-button> -->

        <!-- <view id="sumsub-widget"></view> -->

        <!-- <web-view :src="kyclink"></web-view> -->
        <!-- <iframe :src="kyclink" style="height: 100%; width: 100%" class="iframe"></iframe> -->

        <!-- <view class="bottom-text flex_all">
            <a href="#" style="color: #000;" @click="logout">{{ $t("profile.Logout") }}</a>
        </view> -->
        <view class="btn">
            <u-button hover-class="none" class="exchange-btn " @click="logout">{{ $t("profile.Logout") }}</u-button>
        </view>
        <!-- 放大二维码 -->
        <u-popup v-model="qrcodeShow" mode="center" :mask="true" :close-on-click-mask="true">
            <div class="qr-modal">
                <div class="qr-container">
                    <div class="header">
                        <span>{{ $t("profile.QRcode") }}</span>
                        <image class="close-btn" @click="qrcodeShow = false"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250407/cabd712a810a0ad354e9fcf61bd063a6_104x104.png" />
                    </div>
                    <div class="qr-box">
                        <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="350rpx"
                            :options="options"></uv-qrcode>
                    </div>
                </div>
            </div>
        </u-popup>

        <!-- 换绑邮箱 -->
        <u-popup v-model="emailPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="email-container">
                <view class="titles">{{ $t("profile.Editemail") }}</view>

                <view class="input-group">
                    <view class="input-title">{{ $t("profile.oldemail") }}</view>
                    <view class="phone-input">
                        <u-input type="text" v-model="emailobj.oldemail" placeholder=" " height="102"
                            class="phone-number-input" />
                    </view>
                </view>
                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.SignUp.VerificationCode") }}</view>
                    <view class="verification-input">
                        <u-input type="text" v-model="emailobj.oldverificationCode" placeholder=" " height="102"
                            class="verification-code-input" />
                        <view class="get-code-btn flex_all" :disabled="oldisGettingCode"
                            @click="oldgetVerificationCode">
                            {{ oldisGettingCode ? `${oldcountdown}s` : $t("Auth.ForgotPassword.GetOTP") }}
                        </view>
                    </view>
                </view>
                <view class="input-group">
                    <view class="input-title">{{ $t("profile.newemail") }}</view>
                    <view class="phone-input">
                        <u-input type="text" v-model="emailobj.email" placeholder=" " height="102"
                            class="phone-number-input" />
                    </view>
                </view>
                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.SignUp.VerificationCode") }}</view>
                    <view class="verification-input">
                        <u-input type="text" v-model="emailobj.verificationCode" placeholder=" " height="102"
                            class="verification-code-input" />
                        <view class="get-code-btn flex_all" :disabled="isGettingCode" @click="getVerificationCode">
                            {{ isGettingCode ? `${countdown}s` : $t("Auth.ForgotPassword.GetOTP") }}
                        </view>
                    </view>
                </view>
                <!-- :disabled="!isFormValid" -->
                <u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSubmit">
                    {{ $t("title.submit") }}
                </u-button>
                <u-button hover-class="none" class="cancel-btn flex_all !rounded-button" @click="handleCancel">
                    {{ $t("title.cancel") }}
                </u-button>
            </view>
        </u-popup>

        <u-popup v-model="verificationPopup" mode="bottom" :mask="true" :close-on-click-mask="true">
            <view class="popup-content">
                <text class="popup-title">请选择认证类型</text>
                <u-button hover-class="none" border @click="startVerification('id-and-liveness')" class="popup-button"
                    plain>
                    KYC认证<text v-if="kycstatus">（{{ kycstatus }}）</text></u-button>
                <u-button hover-class="none" @click="startVerification('basic-kyb-level')" class="popup-button"
                    plain>KYB认证<text v-if="kybstatus">（{{ kybstatus }}）</text></u-button>
                <u-button hover-class="none" @click="verificationPopup = false" class="popup-button" plain>取消</u-button>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            qrcodeShow: false,
            timer: null,
            oldtimer: null,
            oldcountdown: 59,
            countdown: 59,
            oldisGettingCode: false,
            isGettingCode: false,
            emailobj: {
                oldemail: "",
                email: "",
                oldverificationCode: "",
                verificationCode: ""
            },
            emailPopup: false,
            idNumber: "",
            idType: "",
            veify: {
                name: false,
            },
            kybstatus: "",
            kycstatus: "",
            fetching: false,
            // qrcodeUrl: '0x68a6ac174E3846035F2aEF4D1F91CB3b682ff331',
            qrcodeUrl: "",
            kyclink: "",
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            NEW_ACCESS_TOKEN: "",
            verificationPopup: false,
            userInfo: {}
        }
    },
    onLoad() {
        uni.setNavigationBarTitle({
            title: this.$t("profile.title") // 切换语言后重新设置标题
        })
    },
    onShow() {
        this.getResult()
        this.getResultKYC()
        this.getUserInfos()
    },
    methods: {
        async signOutHandle() {
            uni.showLoading();
            let res = await this.$api.signOut();
            if (res.code == 200) {
            }
        },
        oldgetVerificationCode() {
            if (!this.emailobj.oldemail) {
                uni.showToast({
                    title: this.$t("Please.email"),
                    icon: 'none',
                    duration: 2000
                });
                return
            } else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.emailobj.oldemail)) {
                uni.showToast({
                    title: this.$t("Please.erroremail"),
                    icon: 'none',
                    duration: 2000
                });
                return
            }
            if (this.oldisGettingCode) return;
            this.oldisGettingCode = true;
            this.oldcountdown = 59;
            this.oldtimer = setInterval(() => {
                if (this.oldcountdown > 0) {
                    this.oldcountdown--;
                } else {
                    this.oldisGettingCode = false;
                    clearInterval(this.oldtimer);
                }
            }, 1000);
            this.oldsendEmailVerifyCode()
        },
        async oldsendEmailVerifyCode() {
            // let res = await this.$api.sendPhoneVerifyCode({
            let res = await this.$api.sendMailCaptcha({
                email: this.emailobj.oldemail
            });
            if (res.code == 200) {
                this.$u.toast(this.$t("register.Send"));
                // 通知验证码组件内部开始倒计时
                this.$refs.uCode.start();
            } else {
                // if (res.code == 110001) {
                //     this.$u.toast(res.msg);
                //     setTimeout(() => {
                //         this.$Router.push({
                //             name: 'login',
                //         })
                //     }, 2000)
                // } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
                // }
            }
        },
        async sendEmailVerifyCode() {
            // let res = await this.$api.sendPhoneVerifyCode({
            let res = await this.$api.sendMailCaptcha({
                email: this.emailobj.email
            });
            if (res.code == 200) {
                this.$u.toast(this.$t("register.Send"));
                // 通知验证码组件内部开始倒计时
                this.$refs.uCode.start();
            } else {
                // if (res.code == 110001) {
                //     this.$u.toast(res.msg);
                //     setTimeout(() => {
                //         this.$Router.push({
                //             name: 'login',
                //         })
                //     }, 2000)
                // } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
                // }
            }
        },
        getVerificationCode() {
            if (!this.emailobj.email) {
                uni.showToast({
                    title: this.$t("Please.email"),

                    icon: 'none',
                    duration: 2000
                });
                return
            } else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.emailobj.email)) {
                uni.showToast({
                    title: this.$t("Please.erroremail"),
                    icon: 'none',
                    duration: 2000
                });
                return
            }
            if (this.isGettingCode) return;
            this.isGettingCode = true;
            this.countdown = 59;
            this.timer = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.isGettingCode = false;
                    clearInterval(this.timer);
                }
            }, 1000);
            this.sendEmailVerifyCode()
        },
        async handleSubmit() {
            // 此处可添加校验或提交逻辑
            if (!this.emailobj.email || !this.emailobj.verificationCode || !this.emailobj.oldemail || !this.emailobj.oldverificationCode) {
                uni.showToast({
                    title: this.$t("Please.full"),
                    icon: 'none'
                });
                return
            }
            let res = await this.$api.changeEmail({
                oldEmail: this.emailobj.oldemail,
                newEmail: this.emailobj.email,
                newEmailCaptcha: this.emailobj.verificationCode,
                oldEmailCaptcha: this.emailobj.oldverificationCode,
            })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
                this.emailPopup = false
                this.getUserInfos()
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        handleCancel() {
            this.emailPopup = false
        },
        maskId(idNumber) {
            return idNumber.replace(/(?<=\w{4})\w(?=\w{4})/g, '*')
            // return idNumber.slice(0, 3) + "****" + idNumber.slice(-4);
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            console.log(res, 123);

            if (res.code == 200) {
                this.userInfo = res.result
                this.qrcodeUrl = res.result.email
                this.emailobj.oldemail = this.userInfo.email
            }

        },
        openVerificationPopup() {
            this.verificationPopup = true
        },
        async startVerification(e) {
            // if(this.kycstatus)
            await this.fetchNEW_ACCESS_TOKEN(e)
            // this.fetching = true
            if (this.fetching) {
                this.kyclink = '/static/kyc.html'
                this.$Router.push({
                    name: 'webView',
                    params: {
                        url: this.kyclink,
                        NEW_ACCESS_TOKEN: this.NEW_ACCESS_TOKEN
                        // NEW_ACCESS_TOKEN: '_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC05YTU1ZGQxZi1hMzU0LTRkODItOWJjNy05ZTMxYzUwYjY3NzItdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2'
                    }
                })
            }

        },
        async fetchNEW_ACCESS_TOKEN(e) {
            this.fetching = false
            uni.showLoading();
            this.verificationPopup = false
            let res = await this.$api.getAccToken({
                levelName: e,
                uid: uni.getStorageSync('uid'),
            });
            console.log(res);

            if (res.code != 200) {
                this.$u.toast(res.msg);
                return
            }

            if (res.code == 200) {
                this.fetching = true
                uni.hideLoading();
                // 解析 JSON 字符串
                const parsedData = JSON.parse(res.result);
                // 获取 token
                const token = parsedData.token;
                this.NEW_ACCESS_TOKEN = token
            } else {

            }
        },
        async getResult() {
            let res = await this.$api.getWebHookResult({
                // applicantId: "67af126bdc26fd2786eaa606",
                uid: uni.getStorageSync('uid'),
                language: 'english',
                level: "kyb" // kyb or kyc
            });
            if (res.code == 200 && res.result) {
                this.getApplicantInfoSync(res.result, 'kyb')
            }
        },
        async getResultKYC() {
            let res = await this.$api.getWebHookResult({
                // applicantId: "67af126bdc26fd2786eaa606",
                uid: uni.getStorageSync('uid'),
                language: 'english',
                level: "kyc" // kyb or kyc
            });
            if (res.code == 200 && res.result) {
                this.getApplicantInfoSync(res.result, 'kyc')
            }
        },
        async getApplicantInfoSync(e, name) {
            let res = await this.$api.getApplicantInfo({
                applicationId: e,
                level: name
            })
            if (res.code == 200) {
                const parsedData = JSON.parse(res.result);
                // inspectionId
                console.log(parsedData);
                if (parsedData?.type == "company" && parsedData?.review.reviewResult.reviewAnswer == "GREEN") {
                    this.kybstatus = "已通过" + parsedData?.inspectionId
                }
                if (parsedData?.type == "individual" && parsedData?.review.reviewResult.reviewAnswer == "GREEN") {
                    this.kycstatus = "已通过" + parsedData?.inspectionId
                    this.idNumber = parsedData?.info.tin
                    this.idType = parsedData?.info.idDocs[0].idDocType
                }
            }
            console.log(this.idType, this.idNumber);
        },
        logout() {
            this.signOutHandle()
            setTimeout(() => {
                uni.hideLoading();
                uni.removeStorageSync('token');
                this.$Router.push({
                    name: "login"
                })
            }, 500);
            return
            // uni.showModal({
            //     title: '提示',
            //     content: '确定要退出登录吗？',
            //     success: (res) => {
            //         if (res.confirm) {
            //             uni.removeStorageSync('token');
            //             // uni.reLaunch({ url: '/pages/login/login' });
            //             this.$Router.push({
            //                 name: "register"
            //             })
            //         }
            //     }
            // });
        }
    }
};
</script>

<style scoped lang="scss">
.profile-container {
    min-height: 100vh;

    .btn {
        display: flex;
        justify-content: center;
        width: 100%;
        padding-bottom: 20rpx;
        margin-top: 408rpx;
        text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .exchange-btn {
            width: 340*2rpx;
            // margin: 0 32rpx;
            height: 100rpx;
            // background: #FF82A3;
            border: 1.5px solid rgba(235, 54, 54, 1);
            border-radius: 64*2rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #EB3636;
        }

        .exchange-btn[disabled] {
            // background: #FF82A380; // 加透明度效果
            background: #D9D6D6;
            color: #666666;
        }
    }

    .bottom-text {
        position: fixed;
        bottom: 0;
        height: 84*2rpx;
        width: 100vw;
        // opacity: 0.3;
        border-top-left-radius: 30*2rpx;
        border-top-right-radius: 30*2rpx;
        background: rgba(217, 214, 214, .3);

        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 16*2rpx;
        line-height: 19.2*2rpx;
        color: #666666;
    }

    // .logout-button {
    //     width: 100%;
    //     margin-top: 30rpx;
    //     /* background-color: #fff; */
    //     /* border: 1px solid #000; */
    // }


    .info-section {
        // height: calc(100vh - 240rpx);
        // height: 100vh;
        border-top-left-radius: 60rpx;
        border-top-right-radius: 60rpx;
        background: #fff;

        .qrcode {
            // mt87*2rpx
            // margin-top: 87*2rpx;
            padding: 30rpx 36rpx;
            // width: 398;
            // height: 80;
            border-radius: 34rpx;
            border-width: 2rpx;
            background: #FFFFFF;
            // border: 2rpx solid #D9D6D6;
            border: 1.5px solid rgba(0, 0, 0, .1);
        }

        .content {
            margin: 40rpx 32rpx 0 32rpx;

            // margin: 40rpx 32rpx 0 32rpx;
            // padding: 60rpx 42rpx;
            .info-items {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 80rpx;

                .pass {
                    font-family: Gilroy-SemiBold;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, 1);
                    margin-right: 17rpx;
                }

                .Verify {
                    margin-left: 20rpx;
                    display: flex;
                    align-items: center;
                    font-family: Gilroy-SemiBold;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .4);

                    image {
                        width: 34rpx;
                        height: 30rpx;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }

                .left-info {
                    font-family: Gilroy-SemiBold;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 34rpx;
                    color: #000;
                }

                .change-link {
                    color: rgba(0, 0, 0, .4);
                    text-decoration: underline;
                    margin-left: 14rpx;
                }

                .qr_div {
                    padding: 10rpx;
                    background: #fff;
                    border-radius: 10rpx;
                }
            }
        }

        .divide {
            height: 16rpx;
            background: #F5F5F5;
            margin-top: 36rpx;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // padding: 26rpx 0;
            margin-bottom: 40rpx;
            margin: 0 32rpx;

            .pass {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: right;
                vertical-align: middle;
                color: #000;
                margin-right: 17rpx;
            }

            .Verify {
                margin-left: 20rpx;
                display: flex;
                align-items: center;
                font-family: Gilroy-SemiBold;
                font-family: Gilroy;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 32rpx;


                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }

            .left-info {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 120%;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #000;
            }

            .change-link {
                color: #FF82A3;
                text-decoration: underline;
                margin-left: 14rpx;
            }

            .qr_div {
                padding: 10rpx;
                background: #fff;
                border-radius: 10rpx;
            }
        }
    }

    .popup-content {
        padding: 20px;
        text-align: center;

        .popup-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .popup-button {
            margin: 10px 0;
            width: 100%;
            font-size: 14px;

            &:hover {
                background-color: transparent;
                /* 取消 hover 背景颜色 */
            }

            &:active {
                background-color: transparent;
                /* 取消 active 背景颜色 */
            }
        }
    }

    .email-container {
        box-shadow: 7px 10px 100.3px 0px #0000001A;
        background: #FFFFFF;
        width: 90vw;
        border-radius: 40rpx;
        padding: 42rpx 44rpx;
        // gap: 16px;

        .titles {
            text-align: center;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 40rpx;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;
            margin-bottom: 32rpx;
        }

        .cancel-btn {
            margin-top: 24rpx;
            width: 100%;
            background-color: #fff;
            color: 000;
            font-size: 16*2rpx;
            border-radius: 64*2rpx;
            height: 100rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            border: 2rpx solid #999999
        }

        .signup-btn {
            width: 100%;
            background-color: #FF82A3;
            color: white;
            border: none;
            font-size: 16*2rpx;
            border-radius: 64*2rpx;
            height: 100rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;

            &:disabled {
                // background-color: #e5e7eb;
            }
        }

        .input-group {
            margin-bottom: 16px;

            .input-title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                color: #000;
                margin-bottom: 20rpx;
            }

            .phone-input {
                display: flex;
                border-radius: 8px;
                // overflow: hidden;
                position: relative;

                .helpoption {
                    width: 85*2rpx;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 122rpx;
                    left: 0;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;
                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                .country-code {
                    // overflow: hidden;
                    width: 85*2rpx;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    border-width: 2rpx;
                    // padding: 16px;
                    border: 2rpx solid #999999;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;


                    display: flex;
                    align-items: center;



                    // padding: 12px 16px;
                    // border-right: 1px solid #e5e7eb;
                    // font-size: 14px;
                    // color: #374151;
                    // gap: 8px;
                    .arrow {
                        /* 图片宽度 */
                        /* 图片高度 */
                        transition: transform 0.3s ease;
                        /* 动画效果：0.3秒平滑旋转 */
                    }

                    .rotated {
                        transform: rotate(180deg);
                        /* 旋转180度 */
                    }

                    image {
                        margin-left: 22rpx;
                        width: 28rpx;
                        height: 14rpx;
                    }
                }

                .phone-number-input {
                    border: 2rpx solid #999999 !important;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    // margin-left: 20rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                    padding: 0 32rpx !important;
                }
            }

            .verification-input {
                height: 51*2rpx;
                display: flex;
                border: 2rpx solid #999999 !important;
                border-radius: 10*2rpx;
                padding: 0 32rpx !important;
                overflow: hidden;
                position: relative;

                .verification-code-input {
                    flex: 1;
                    padding: 12px 16px;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                }

                .get-code-btn {
                    font-size: 14*2rpx;
                    color: #000;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    vertical-align: middle;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-decoration-offset: 15%;
                    text-decoration-thickness: 10%;


                    &:disabled {
                        background: #FF82A3;
                    }
                }
            }
        }
    }

    .qr-modal {
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999;
        width: 90vw;

        .qr-container {
            background: #fff;
            width: 100%;
            border-radius: 40rpx;
            padding: 42rpx 44rpx;
            box-shadow: 7px 10px 100.3px 0px #0000001A;
            position: relative;
            text-align: center;

            .header {
                margin-bottom: 32rpx;
                position: relative;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 40rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                color: #000;

                .close-btn {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 52rpx;
                    height: 52rpx;
                }
            }

            .qr-box {
                border: 2rpx solid #FFCDDA;
                border-radius: 32rpx;
                padding: 42rpx 0;
                display: flex;
                justify-content: center;
            }
        }
    }

}
</style>