<template>
	<view>
		<view class="myBalance_header">
			<view class="title">
				<view class="left" @click="nav_back()">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240305/f605d9efcf61d5838e64a76a631bbebd_26x50.png"
						mode="widthFix"></image>
				</view>
				我的钱包
			</view>
			<view class="cart">
				<view class="balance">
					账户余额（元）
					<!-- <view class="icon">
						<image src="../../../static/imgs/public/open.png" v-if="isShowBalance" mode="widthFix"
							@click="check()"></image>
						<image src="../../../static/imgs/public/close.png" v-else mode="widthFix" @click="check()">
						</image>
					</view> -->
				</view>
				<view class="num">
					<view class="money">
						<text>￥</text>
						{{ balance }}
					</view>
					<!-- <view class="money" v-if="isShowBalance">
						<text>￥</text>
						{{balance}}
					</view>
					<view class="money" v-else>
						<text>￥</text>
						****.**
					</view> -->
					<view class="tx">提现中：{{ withdrawing }}</view>
					<view class="msg" v-show="isShow">无需钱包管理费。非系统钱包</br>
						资金存管于平安银行金融级托管账户</view>
				</view>
				<view class="but">
					<view @click="nav_withdraw()">提现</view>
					<view @click="nav_pay()" class="withdraw">充值</view>
				</view>
			</view>
		</view>

		<view class="bank item" @click="nav_bank()">
			<view class="border">
				<view>
					我的银行卡（{{ bankCardCount }}）
				</view>
				<view class="icon">
					<image src="../../../static/imgs/public/nav_right.png" mode="widthFix"></image>
				</view>
			</view>
		</view>
		<!-- <view class="bank item" @click="nav_PayGuan()">
			<view>
				支付管理
			</view>
			<view class="icon">
				<image src="../../../static/imgs/public/nav_right.png" mode="widthFix"></image>
			</view>
		</view> -->
		<view class="bill">
			<view class="head">
				<text>账单</text>
				<view class="right" @click="isSelect = true">
					<text>{{ yearMonth }}</text>
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/********/4959bb1722ec0157b8edcd006b31863c_20x17.png"
							mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="filtrate">
				<!-- <view class="li" :class="{'active':tagNum==index}" v-for="(item,index) in tag" :key="index"
					@click="checkTag(item,index)">
					{{item.name}}
				</view> -->
				<u-tabs class="u-tabs" name="cate_name" bg-color="var(--main-bg-color)" :bar-style="barStyle"
					:list="tabList" bold :is-scroll="true" inactive-color="var(--default-color3)"
					:active-item-style="itemStyle" active-color="var(--default-color1)" :current="current"
					@change="checkTag"></u-tabs>
			</view>
			<view class="myBalance_list" v-if="details != ''">
				<view class="li" v-for="(item, index) in details" :key="index">
					<view class="left">
						{{ item.name }}
						<!-- {{billType[item.type]}} -->
					</view>
					<view class="right">
						<view class="text">
							<text>
								{{ item.desc }}
							</text>
							<view class="msg">{{ item.time }}</view>
						</view>
						<view class="price">
							<text v-if="item.isNegative">-</text>
							<text v-else>+</text>
							{{ item.amount }}
						</view>
					</view>
				</view>
				<u-loadmore :status="loadStatus" style="padding:30rpx;"></u-loadmore>
			</view>
			<view class="myBalance_list_data" v-else>
				<view class="img">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						mode="widthFix"></image>
				</view>
				<view>
					哎呦，你还没有账单记录哦！
				</view>
			</view>
			<u-picker mode="time" v-model="isSelect" start-year='2021' end-year='2024' :default-time="yearMonth"
				confirm-color="#1e1e1e" cancel-color="#b3b1b1" @confirm="confirm" :params="params"></u-picker>
		</view>
		<!-- 实名认证 -->
		<!-- 确认充值 -->
		<u-modal v-model="isRecharge" font-size="40" :show-title="false" width="80%" border-radius="0"
			:mask-close-able="true" :show-confirm-button="false">
			<view class="autonym_model">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20220614/18d84e4a0f33d074f5acc6beed9f8a43_32x32.png"
					mode="scaleToFill" @click="isRecharge = false" />
				<view class="title">
					<view>确认充值</view>
				</view>
				<view class="msg">
					<view>实际支付金额：148（包含手续费44.4元）</view>
					<view style="margin-top:20rpx;color:#616161;">充值金额/70%=实际支付金额</view>
				</view>
				<view class="button_db">
					<view class="button active" style="margin:0;" @click="nav_pay()">确认</view>
				</view>
			</view>
		</u-modal>

		<u-popup v-model="exemptionPop" mode="center">
			<view class="exemption_pop_content">
				<view class="pop_title">{{ exemptionInfo.title }}</view>
				<view class="pop_detail">{{ exemptionInfo.usageCount }}</view>
				<view class="pop_detail">{{ exemptionInfo.totalSavings }}</view>
			</view>
		</u-popup>

		<u-modal class="" v-model="isLoaddingLogin" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>

		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				加载中...
			</view>
		</u-modal>
		<popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false" @confirm="nav_realName()">
		</popup-bar>
	</view>
</template>

<script>
import popupBar from "@/components/public/PopupBar";
export default {
	data() {
		return {
			isLoaddingLogin:false,
			token:uni.getStorageSync('token'),
			balance: '',
			withdrawing: '',
			details: [],
			isShowBalance: false,
			certification: "",
			isRegistration: false,
			isRecharge: false,
			show: false,
			link: "",
			tabList: [{
				name: "全部",
				value: ""
			},
			{
				name: "购买",
				value: "BUY"
			},
			{
				name: "出售",
				value: "SELL"
			},
			{
				name: "提现",
				value: "WITHDRAW"
			},
			{
				name: "充值",
				value: "DEPOSIT"
			},
			{
				name: "奖励",
				value: "REWARD"
			}
			],
			params: {
				year: true,
				month: true,
				day: false,
				hour: false,
				minute: false,
				second: false,

			},
			isSelect: false,
			defaultNum: 0,
			month: 0,
			year: "",
			type: "",
			bankCardCount: 0,
			exemption: false, // 显示免佣资格
			freeSystemFeeNumText: "",
			freeSystemFeeNum: "",
			pageNum: 1,
			loadStatus: 'loadmore',
			isFooter: true, //没有更多了
			isRequest: false, //多次请求频繁拦截
			exemptionPop: false, // 免佣资格弹窗
			billType: {
				"DEPOSIT": "充值",
				"BUY_GAS": "燃料",
				"BUY_ITEM": "购买",
				"WITHDRAW": "提现",
				"SELL_ITEM": "出售",
				"REWARD": "获奖励",
				"INVITE_REWARD": "佣金",
				"COPYRIGHT_FEE": "版税",
				"PAY_SD": "保证金",
				"REFUND_SD": "退保",
				"TAX": "劳务",
				"GIFT_SYSTEM_FEE": "转赠佣金",
			},
			exemptionInfo: {
				title: "",
				totalSavings: "",
				usageCount: "",
			}, // 保证金弹窗信息
			appUrl: "",
			barStyle: {
				'background': 'var(--primary-button-color)',
				'width': '44rpx',
				'height': '8rpx',
			},
			itemStyle: {
				'font-size': '28rpx',
				'min-width': '100rpx'
			},
			current: 0,
			iversion: "",
			isLoadding: false,
			deo: false
		};
	},
	onBackPress() {

		return true
	},
	onLoad(options) {
		// if(!this.token){
		// 	this.$Router.push({
		// 		name:'mainLogin'
		// 	})
		// }
		this.deo = options.deo
		this.appUrl = getApp().globalData.urlZf
		const {
			platform
		} = options;
		this.platform = platform;
		switch (platform) {
			case "ios":
				this.myUni.webView.navigateTo({
					url: `/pagesA/project/personal/myBalance`
				});
				break;
			case "android":
				this.myUni.webView.navigateTo({
					url: `/pagesA/project/personal/myBalance`
				});
				break;
			default:
				this.paymentScene = 1;
		}
	},
	computed: {
		isShow() {
			// #ifdef APP
			if (uni.getSystemInfoSync().platform == 'ios') {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = this.iversion
				if (curV == reqV) {
					return false
				} else {
					return true
				}
			} else {
				return true
			}
			// #endif
			// #ifdef H5
			return true
			// #endif
		}

	},
	onShow(options) {
		let month = 0
		var date = new Date;
		this.month = date.getMonth()
		this.year = date.getFullYear()
		this.defaultNum = date.getMonth()
		// console.log(date.getMonth()+1)
		if (date.getMonth() < 9) {
			month = "0" + (Number(date.getMonth()) + 1)
		} else {
			month = (Number(date.getMonth()) + 1)
		}
		this.yearMonth = date.getFullYear() + "-" + month
		console.error(this.yearMonth)
		this.certification = uni.getStorageSync("certification")
		this.getBalance()
		this.getBalanceLog()
	},
	onReachBottom() {
		this.loadStatus = 'loading';
		setTimeout(() => {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getBalanceLog()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				this.loadStatus = 'nomore';
				console.log("已经到底了")
			}
		}, 1000)
	},
	methods: {
		async getBalance() {
			if(this.token){
				this.isLoadding = true
			}
			let res = await this.$api.java_balance_info();
			console.log(res)
			if (res.status.code == 0) {
				this.isLoadding = false
				this.balance = res.result.available
				this.withdrawing = res.result.withdrawing
				this.bankCardCount = res.result.bankCardCount
				this.freeSystemFeeNumText = res.result.freeSystemFeeNumText
				this.freeSystemFeeNum = res.result.freeSystemFeeNum || 0
				this.iversion = res.result.iversion
				console.log(res)
			} else if (res.status.code == 1002) {
				this.isLoaddingLogin = true
				setTimeout(() => {
					this.isLoaddingLogin = false
					this.$Router.push({
						name: "mainLogin"
					})
				}, 1500);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none ',
					duration: 3000
				});
			}
		},
		async getBalanceLog() {
			this.isRequest = true
			let res = await this.$api.java_balance_log({
				yearMonth: this.yearMonth,
				type: this.type,
				pageNum: this.pageNum
			});
			if (res.status.code == 0) {
				this.isRequest = false
				console.log(res.result)
				if (res.result.list == null || res.result.list == "") {
					console.log("没数据咯")
					this.isFooter = false
					this.loadStatus = 'nomore';
					if (this.pageNum == 1) {
						this.details = []
					}
				} else {
					this.pageNum++
					res.result.list.forEach((item) => {
						this.details.push(item)
					})
					console.log(this.details)
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getNotice() {
			let res = await this.$api.extra_notice({
				position: "1"
			});
			if (res.status == 200) {
				// this.list.push(res.result.content)
				this.link = res.result.link
				this.show = true
			}
		},
		nav_withdraw() {
			// uni.showToast({
			// 	title: '敬请期待',
			// 	icon: 'none ',
			// 	duration: 3000
			// });
			console.log(this.certification)
			if (this.certification == 1) {
				this.$Router.push({
					name: "withdraw"
				})
			} else {
				this.isRegistration = true
			}
		},
		nav_pay() {
			if (this.certification == 0) {
				this.isRegistration = true
				return false
			}
			let url = `${this.appUrl}pagesA/project/security/pay`
			console.log(url)
			// #ifdef APP
			if (uni.getSystemInfoSync().platform == 'ios') {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = this.iversion
				if (curV == reqV) {
					this.$Router.push({
						name: "iosPay",
					})
				} else {
					this.$Router.push({
						name: "webView",
						params: {
							url,
						}
					})
				}
			} else {
				this.$Router.push({
					name: "webView",
					params: {
						url,
					}
				})
			}
			// #endif
			// #ifdef H5
			let {
				origin
			} = window.location
			window.location.href = `${origin}/orderView/#/pagesA/project/security/pay`
			// #endif
		},
		nav_bank() {
			this.$Router.push({
				name: "bank"
			})
		},
		nav_PayGuan() {
			if (this.certification == 1 || this.certification == 2) {
				this.$Router.push({
					name: "payManage"
				})
			} else {
				this.isRegistration = true
			}
		},
		nav_realName() {
			this.isRegistration = false
			console.log(uni.getStorageSync('authStatus'))
			if (uni.getStorageSync('authStatus') == 30) {
				this.$Router.push({
					name: "authentication"
				})
			} else {
				this.$Router.push({
					name: "realName"
				})
			}
		},
		nav_link() {
			if (this.link != '') {
				window.location.href = this.link
			}
		},
		check() {
			this.isShowBalance = !this.isShowBalance
		},
		checkTag(index) {
			this.current = index
			this.type = this.tabList[index].value
			this.pageNum = 1
			this.details = []
			this.getBalanceLog()
		},
		confirm(e) {
			console.log(e)
			this.yearMonth = e.year + "-" + e.month
			this.month = parseInt(e.month) - 1
			this.year = e.year
			this.pageNum = 1
			this.details = []
			this.getBalanceLog()
		},
		nav_back() {
			console.log(this.deo);
			if (this.deo) {
				this.$Router.pushTab({
					name: "contract-BITindex"
				})
			} else {
				this.$Router.pushTab({
					name: "personal"
				})
			}

		},
	},
	components: {
		popupBar
	}

}
</script>

<style lang="scss" scoped>
.myBalance_header {
	width: 100%;
	height: 670rpx;
	background-size: 100% 100%;
	background-color: var(--main-bg-color);
	/* #ifdef APP */
	padding-top: 180rpx;
	/* #endif */
	/* #ifdef H5 */
	padding-top: 120rpx;
	/* #endif */
	color: #FFF;

	.title {
		position: absolute;
		width: 100%;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 40rpx;
		/* #endif */
		left: 0rpx;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		color: #FFF;

		.left {
			position: absolute;
			top: 12rpx;
			left: 34rpx;

			image {
				width: 26rpx;
			}
		}
	}

	.cart {
		width: 750rpx;
		// height: 336rpx;
		background-size: 100% 100%;
		background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240305/e980d92227c08ae1a3bdaa5d5eb5fdbb_750x522.png");
		padding: 90rpx 40rpx;
		margin: 0rpx auto;

		.balance {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #616161;
			line-height: 24rpx;

			// .icon {
			// 	image {
			// 		width: 38rpx;
			// 		height: 30rpx;
			// 	}
			// }
		}

		.num {
			margin: 28rpx 0 46rpx;
			font-weight: 500;
			color: #63EAEE;
			text-align: center;

			.money {
				font-size: 54rpx;
				font-weight: 600;

				text {
					font-size: 26rpx;
				}
			}

			.tx {
				font-weight: 400;
				color: #fff;
				line-height: 14px;
				font-size: 24rpx;
				margin: 20rpx 0rpx 0rpx;
				transform: scale(0.94);
			}

			.msg {
				font-weight: 400;
				color: rgba(255, 255, 255, 0.5);
				line-height: 32rpx;
				font-size: 24rpx;
				margin: 20rpx 0rpx 46rpx;
				transform: scale(0.94);

			}
		}

		.but {
			display: flex;
			justify-content: center;
			align-items: center;

			view {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 240rpx;
				height: 80rpx;
				background: #25232D;
				color: #FFF;
				border-radius: 14rpx;
				font-size: 28rpx;
				margin: 0rpx;
				border: 1px solid #FFF;
			}

			.withdraw {
				margin-left: 40rpx;
				background: var(--primary-button-color);
				color: #000000;
				border: none;
				font-weight: 600;
			}
		}
	}
}

.item {
	width: 100%;
	// letter-spacing: 2rpx;
	background-color: var(--main-bg-color);
	padding: 0rpx 36rpx;
	color: #FFF;
	font-size: 30rpx;

	>.border {
		display: flex;
		padding: 40rpx 0rpx;
		justify-content: space-between;
		align-items: center;
		line-height: 50rpx;
		border-bottom: 1px solid #53505D;
		border-top: 1px solid #53505D;
	}

	.icon {
		display: flex;
		align-items: center;

		image {
			width: 16rpx;
		}
	}
}

.exemption {
	position: relative;

	.free_system_title {
		display: flex;
		align-items: center;
	}

	.exemption_tip {
		z-index: 500;
		position: absolute;
		width: 400rpx;
		background-color: #2C2C2C;
		border-radius: 4rpx;
		font-size: 24rpx;
		padding: 20rpx;
		top: 100rpx;
		color: var(--message-box-point-color);
		font-weight: 400;

		.triangle {
			border-bottom: 20rpx solid #2C2C2C;
			border-left: 10rpx solid transparent;
			border-right: 10rpx solid transparent;
			position: absolute;
			height: 0;
			width: 0;
			top: -20rpx;
			right: 180rpx;
		}

		.exemption_title {
			font-size: 12px;
			color: #616161;
		}
	}
}

.bill {
	background-color: var(--main-bg-color);

	.head {
		width: 100%;
		padding: 36rpx 42rpx 34rpx 42rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #FFF;

		text {
			font-size: 32rpx;
			font-weight: 600;
		}

		.right {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-radius: 40rpx;
			border: 1rpx solid #63EAEE;
			padding: 5rpx 20rpx;
			color: #63EAEE;

			text {
				font-size: 28rpx;
				font-weight: 400;
			}

			.icon {
				width: 20rpx;
				margin-left: 10rpx;

				image {
					width: 100%;
				}
			}
		}
	}

	.filtrate {
		width: 100%;
		padding: 0rpx 42rpx;
		background-color: var(--main-bg-color);
		display: inline-block;
		color: #A6A6A6;
		display: flex;
		justify-content: space-between;

		.u-tabs {
			width: 100%;
		}

		.li {
			width: 86rpx;
			height: 50rpx;
			font-size: 26rpx;
			border-radius: 8rpx;
			border-radius: 10rpx;
			border: 2rpx solid #A6A6A6;
			text-align: center;
			line-height: 47rpx;

			&.active {
				background: #63EAEE;
				color: #FFF;
				font-weight: 400;
			}
		}
	}

	.myBalance_list {
		padding: 32rpx 40rpx;

		.li {
			padding: 20rpx 30rpx;
			height: 198rpx;
			background: #25232D;
			margin-bottom: 32rpx;
			border-radius: 30rpx;

			.left {
				padding: 0 24rpx 20rpx;
				font-size: 28rpx;
				font-weight: 600;
				color: #fff;
				border-bottom: 1rpx solid #53505D;
			}

			.right {
				width: 100%;
				padding: 22rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.text {
					font-size: 28rpx;
					line-height: 45rpx;
					font-weight: 400;
					color: #FFF;

					.msg {
						font-weight: 400;
						font-size: 22rpx;
						color: #A6A6A6;
					}
				}

				.price {
					font-size: 32rpx;
					font-weight: 600;
					color: #63EAEE;

					&.lvse {
						color: #63EAEE;
					}
				}
			}
		}
	}

	.myBalance_list_data {
		width: 80%;
		height: 400rpx;
		text-align: center;
		margin: 100rpx auto;
		background-color: var(--main-bg-color);

		.img {
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;

			image {
				width: 242rpx;
			}
		}

		font-size:28rpx;
		color: #AAAAAA;
	}
}

::v-deep .u-mode-center-box {
	background-color: var(--main-bg-color);
}

.exemption_pop_content {
	width: 470rpx;
	padding: 30rpx 40rpx;
	font-size: 30rpx;
	font-weight: 500;
	color: #FFF;
	line-height: 44rpx;

	.pop_title {
		text-align: center;
		margin-bottom: 20rpx;
	}

	.pop_detail {
		font-size: 26rpx;
	}
}
</style>