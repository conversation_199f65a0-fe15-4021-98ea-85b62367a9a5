<template>
	<view>
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{ backgroundColor: 'var(--main-bg-color)' }" title="实名认证" title-color="var(--main-front-color)"
			:custom-back="back">
		</u-navbar>

		<view class="content">

			<view class="main">
				<u-toast ref="uToast" />
				<u-top-tips ref="uTips"></u-top-tips>
				<view class="title">
					{{ type == 'COMPLETE_AUTH_INFO' ? '请继续完善实名认证信息' : '请填写实名认证信息' }}
				</view>
				<view class="tips">
					请您确保实名信息真实一致，Bigverse安全保障中，身份信息仅用于身份认证。
				</view>

				<view v-if="form.type === '居民身份证'">
					<input-bar v-model="form[item.key]" :item="item" v-for="item in inputList" :key="item.key"
						@click.native.stop="clickInput(item.type)"></input-bar>
				</view>

				<view v-else>
					<input-bar v-model="form[item.key]" :item="item" v-for="item in inputList2" :key="item.key"
						@click.native.stop="clickInput(item.type)">
					</input-bar>
				</view>


				<u-select v-model="isShowType" mode="single-column" :list="typeList" @confirm="confirm"></u-select>
				<view class="upload">
					<view class="title"
						v-if="(form.type === '居民身份证' && type != 'TWO_ELEMENT_AUTH') || form.type === '护照'">
						请上传{{ form.type === '居民身份证' ? '身份证' : '护照' }}照片
					</view>
					<view class="tips"
						v-if="(form.type === '居民身份证' && type != 'TWO_ELEMENT_AUTH') || form.type === '护照'">
						请确保照片中{{ form.type === '居民身份证' ? '身份证' : '护照' }}信息清晰，边框无缺失
					</view>
					<view class="upload-box" v-if="form.type === '居民身份证' && type != 'TWO_ELEMENT_AUTH'">
						<u-upload :width="320" :max-size="10 * 1024 * 1024" :height="214" name="image"
							:show-progress='false' :header="header" max-count="1" custom-btn ref="idCartZ"
							:action="action" :auto-upload="true" @on-choose-complete='change' @on-uploaded="successimg1"
							@on-oversize="oversize">
							<view slot="addBtn" class="slot-btn" hover-class="slot-btn__hover" hover-stay-time="150">
								<u-icon name="plus" color="var(--main-front-color)" size="62"></u-icon>
								<view class="tips">请上传人像面，限制10M</view>
							</view>
						</u-upload>
						<u-upload :width="320" :max-size="10 * 1024 * 1024" :height="214" name="image"
							:show-progress='false' :header="header" max-count="1" custom-btn ref="idCartF"
							:action="action" :auto-upload="true" @on-choose-complete='change'
							@on-uploaded="successimg2">
							<view slot="addBtn" class="slot-btn" hover-class="slot-btn__hover" hover-stay-time="150">
								<u-icon name="plus" color="var(--main-front-color)" size="62"></u-icon>
								<view class="tips">请上传国徽面，限制10M</view>
							</view>
						</u-upload>
					</view>
					<view class="upload-box" v-if="form.type === '护照'">
						<u-upload :width="320" :max-size="10 * 1024 * 1024" :height="214" name="image"
							:show-progress='false' :header="header" max-count="1" ref="passport" :action="action"
							custom-btn :auto-upload="true" @on-choose-complete='change' @on-uploaded="successimg1">
							<view slot="addBtn" class="slot-btn" hover-class="slot-btn__hover" hover-stay-time="150">
								<u-icon name="plus" color="var(--main-front-color)" size="62"></u-icon>
								<view class="tips">请上次护照首页，限制10M</view>
							</view>
						</u-upload>
					</view>
				</view>
				<view class="footer" @tap="submit" v-show="isFooterButShow">
					提交信息
				</view>
			</view>
			<u-modal class="" v-model="isLoading" width="40%" :show-title="false" :show-confirm-button="false">
				<div class='sk-wave'></div>
				<view class="text_msg" v-if="img"
					style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
					实名认证中...
				</view>
				<view class="text_msg" v-else
					style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
					图片上传中...
				</view>
			</u-modal>
		</view>
	</view>
</template>

<script>
import InputBar from "@/components/public/InputBar";
import ButtonBar from "@/components/public/ButtonBar";
// #ifdef APP
const n = uni.requireNativePlugin('AP-FaceDetectModule');
// #endif

export default {
	components: {
		InputBar,
		ButtonBar,
	},
	data() {
		return {
			inputList: [{
				label: '证件类型',
				type: 'select',
				key: 'type',
				placeholder: '请选择证件类型',
				disabled: true,
			},
			{
				label: '真实姓名',
				type: 'input',
				key: 'realName',
				placeholder: '请输入真实姓名',
			},
			{
				label: '身份证号',
				type: 'input',
				key: 'IDCardNo',
				placeholder: '请输入身份证号',
				maxlength: 18
			},
			],
			inputList2: [{
				label: '证件类型',
				type: 'select',
				key: 'type',
				placeholder: '请选择证件类型',
				disabled: true,
			},
			{
				label: '护照姓名',
				type: 'input',
				key: 'realName',
				placeholder: '请输入护照姓名',
			},
			{
				label: '护照号码',
				type: 'input',
				key: 'IDCardNo',
				placeholder: '请输入护照号码',
			},
			],
			action: "",
			form: {
				type: '居民身份证',
				realName: "",
				IDCardNo: "",
				realPhone: ""
			},
			isShowType: false,
			isShow: true,
			typeList: [{
				value: '1',
				label: '居民身份证'
			},
			{
				value: '2',
				label: '护照'
			}
			],
			isLoading: false,
			heightChange: true,
			header: {
				'Authorization': uni.getStorageSync("token"),
			},
			Uploadone: 0,
			Uploadtwo: 0,
			img: false, //控制图画文本
			type: "TWO_ELEMENT_AUTH",
			metaInfo: "",
			certifyId: "",
			platform: "",
			isFooterButShow: true
		}

	},
	onLoad(options) {
		let appHeight;
		if (options.type) {
			this.type = options.type
			if (options.type == 'COMPLETE_AUTH_INFO') {
				this.inputList.forEach((item) => {
					item.disabled = true
				})
				this.typeList.splice(1, 1)
				this.inputList[0].type = 'input'
				this.get_authentication()
			}
			console.log(this.type)
		}
		this.action = getApp().globalData.java_uploadImage

		uni.getSystemInfo({
			success: ((res) => {
				console.log(res)
				console.log(res.windowHeight)
				appHeight = res.windowHeight
				// localStorage.setItem('platform', res.platform);
				this.platform = res.platform
			})
		});
		// #ifdef H5
		window.addEventListener('resize', () => {
			const windowHeight = window.innerHeight;
			if (windowHeight < appHeight) {
				this.isFooterButShow = false
			} else {
				this.isFooterButShow = true
			}
		});
		// #endif

		// #ifdef APP
		this.getMetaInfo()
		// #endif
	},
	methods: {
		clickInput(key) {
			// if (key === 'select') {
			// 	this.isShowType = true;
			// }
		},
		//身份正面
		successimg1(a) {
			console.log(a[0])
			if (a[0] == undefined) {
				this.isLoading = false
			} else {
				if (a[0].response.status.code == 0) {
					this.isLoading = false
					this.$refs.uToast.show({
						title: "上传成功",
						type: 'default',
					})
					this.Uploadone = 1
				} else {
					this.$refs.uToast.show({
						title: a[0].response.status.msg,
						type: 'default',
					})
					this.isLoading = false
					if (this.$refs.idCartZ != undefined) {
						this.$refs.idCartZ.remove(0)
					} else {
						this.$refs.passport.remove(0)
					}
				}
			}
		},
		//身份反面
		successimg2(a) {
			if (a[0] === undefined) {
				this.isLoading = false
			} else {
				if (a[0].response.status.code === 0) {
					this.isLoading = false
					this.$refs.uToast.show({
						title: "上传成功",
						type: 'default',
					})
					this.Uploadtwo = 1
				} else {
					this.$refs.uToast.show({
						title: a[0].response.status.msg,
						type: 'default',
					})
					this.isLoading = false
					if (this.$refs.idCartF != undefined) {
						this.$refs.idCartF.remove(0)
					} else {
						this.$refs.passport.remove(0)
					}
				}
			}
		},
		//上传图片
		change(e) {
			console.log(e)
			this.isLoading = true
			this.img = false
		},
		oversize(e) {
			this.isLoading = false
			console.log(111)
		},
		// 注意返回值为一个数组，单列时取数组的第一个元素即可(只有一个元素)
		confirm(e) {
			if (e[0].label !== this.form.type) {
				this.form.realName = ""
				this.form.IDCardNo = ""
				this.form.realPhone = ""
				this.form.type = e[0].label;
			}
			if (e[0].value === '1') {
				this.isShow = true
			} else if (e[0].value === '2') {
				this.isShow = false
			}
		},
		showTooltips(key, tooltip) {
			this.inputList = this.inputList.map(item => {
				if (item.key === key) {
					return {
						...item,
						tooltip
					}
				}
				return item
			})
		},
		async submit() {
			console.log(this.platform)
			let data = {}
			// #ifdef APP
			data = {
				name: this.form.realName,
				idNumber: this.form.IDCardNo,
				clientScene: this.platform == 'ios' ? 3 : 4,
				metaInfo: this.platform == 'ios' ? JSON.stringify(this.metaInfo) : this.metaInfo,
				type: 1,
				authScene: "FACE_AUTH",
			}
			// #endif
			// #ifdef H5
			let {
				origin
			} = window.location
			data = {
				name: this.form.realName,
				idNumber: this.form.IDCardNo,
				type: 1,
				authScene: "FACE_AUTH",
				returnUrl: `${origin}/h5/#/pagesA/project/personal/realNameResult`,
				clientScene: 1,
				metaInfo: JSON.stringify(window.getMetaInfo())
			}
			// #endif
			console.log(data)
			let res = await this.$api.certification(data)
			if (res.status.code === 0) {
				// #ifdef APP
				this.certifyId = res.result.certifyId
				this.verifyC()
				// #endif
				// #ifdef H5
				window.location.href = res.result.certifyUrl;
				// #endif

			} else {
				this.isLoading = false
				this.$refs.uToast.show({
					title: res.status.msg,
					type: 'default',
				})
			}
		},
		back() {
			const pages = getCurrentPages();
			if (pages.length === 1) {
				this.$Router.pushTab({
					name: "personal",
				});
			} else {
				this.$Router.back();
			}
		},
		verifyC: function () {
			let _this = this
			n.verify({
				certifyId: this.certifyId
			},
				function (t) {
					console.log(t)
					_this.completeFaceAuth()
				}
			);
		},
		//获取环境参数接口。
		getMetaInfo: function () {
			this.metaInfo = n.getMetaInfo();
			console.log(this.metaInfo)
		},
		//实名认证查询
		async completeFaceAuth() {
			let res = await this.$api.java_completeFaceAuth({
				clientScene: this.platform == 'ios' ? 3 : 4,
				certifyId: this.certifyId,
			});
			if (res.status.code == 0) {
				this.$refs.uTips.show({
					title: '实名认证成功',
					type: 'success',
					duration: '2300'
				})
				uni.showToast({
					title: '实名认证成功',
					duration: 2000
				});
				setTimeout(() => {
					this.$Router.back(2)
				}, 1500);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
				console.log(res.status.msg)
				setTimeout(() => {
					this.$Router.back();
				}, 1500);
			}
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .u-select__header {
	background-color: #121212;
}

::v-deep .u-select__body__picker-view__item {
	color: var(--message-box-point-color);
}

::v-deep .uni-picker-view-mask {
	height: 0;
}

::v-deep .u-select__body {
	background-color: #121212;
}

.content {
	padding: 35rpx 40rpx 0 40rpx;
	letter-spacing: 2rpx;

	.main {
		.title {
			// margin-bottom: 24rpx;
			// margin-top: 40rpx;

			font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: 38px;
		}

		.tips {
			font-size: 28rpx;
			font-weight: 400;
			color: var(--default-color3);
			line-height: 44rpx;

			font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		}

		.upload {
			.title {
				font-size: 28rpx;
				margin-bottom: 24rpx;
			}

			.tips {
				font-size: 24rpx;
				margin-bottom: 38rpx;
			}

			.upload-box {
				width: 100%;
				display: flex;
				justify-content: space-between;

				.slot-btn {
					width: 326rpx;
					height: 206rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					background: var(--main-bg-color);
					border: 2rpx dashed var(--secondary-front-color);

					.tips {
						margin: 10rpx 0 0;
					}

					image {
						width: 100%;
						height: 100%;
					}
				}

			}
		}

		.footer {
			width: 670rpx;
			position: absolute;
			bottom: 40rpx;
			height: 120rpx;
			left: 0rpx;
			right: 0rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 60rpx;
			background: var(--primary-button-color);
			margin: 0 auto;
			font-size: 34rpx;
			font-weight: 600;
		}
	}
}

//.type_title{
//    font-size: 24rpx;
//    font-family: PingFang SC-Regular, PingFang SC;
//    font-weight: 400;
//    color: #666666;
//    line-height: 28rpx;
//    padding: 48rpx 42rpx 10rpx;
//    background-color: var(--message-box-point-color);
//}
//.type_titles{
//    font-size: 24rpx;
//    font-family: PingFang SC-Regular, PingFang SC;
//    font-weight: 400;
//    color: #666666;
//    line-height: 28rpx;
//    padding: 10rpx 0rpx;
//    padding-top: 48rpx;
//    background-color: var(--message-box-point-color);
//    span{
//        font-size: 24rpx;
//        font-family: PingFang SC-Regular, PingFang SC;
//        font-weight: 400;
//        color: #D84D51;
//        line-height: 28rpx;
//        margin-right: 8rpx;
//    }
//}
//
//.u-form-item {
//    background-color: var(--message-box-point-color);
//
//    &.type {
//        padding: 10rpx 42rpx;
//        // margin-bottom: 16rpx;
//    }
//
//    &.upload-label {
//        padding: 10rpx 42rpx 0;
//        // margin-top: 16rpx;
//    }
//}
//


//.upload-tip {
//    height: 80rpx;
//    line-height: 80rpx;
//    padding: 0 42rpx;
//    font-size: 28rpx;
//    color: #999;
//    background-color: var(--message-box-point-color);
//}
//
//.submit {
//    // position: absolute;
//    // bottom: 67rpx;
//    // left: 137rpx;
//    // right: 137rpx;
//    width: 80%;
//    margin-top: 50rpx;
//    // margin-bottom: 30rpx;
//    color: var(--message-box-point-color);
//    background-color: #333;
//    &:active {
//        color: #333;
//        background-color: #eee;
//    }
//}
//.blank{
//    height: 100rpx;
//}</style>