<template>
	<view class="main">
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="账户安全" title-color="var(--main-front-color)">
		</u-navbar>
		<view class="content">
			<view class="item" v-for="(item,index) in list" :key="index" @click="clickItem(item)">
				<span class="title">{{ item.title }}</span>
				<view class="right">
					<span v-if="item.title === '实名认证'">{{ authStatus(userInfo['authStatus']) }}</span>
					<span v-if="item.title === '邮箱账号'">{{ substr(userInfo['email']) }}</span>
					<span v-if="item.title === '手机号码'">{{ substr(userInfo['phone'])}}</span>
					<u-image mode="widthFix" width="48rpx" :src="`../../../static/imgs/public/arrow-right.png`">
					</u-image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "security",
		data() {
			return {
				list: [{
						title: '实名认证',
						path: 'authentication',
					},
					{
						title: '手机号码',
						path: 'phoneVerify'
					},
					{
						title: '邮箱账号',
						path: 'emailVerify'
					},
					{
						title: '登录密码',
						path: 'selectPay'
					},
					{
						title: '支付密码',
						path: 'payManage'
					},
					{
						title: '账号注销',
						path: 'accountLogout'
					},
				],
				userInfo: {}
			}
		},
		onLoad() {
			this.getUserInfo()
		},
		methods: {
			clickItem(item) {
				const {
					title,
					path
				} = item
				let params = {}
				if (path === 'selectPay') {
					params = {
						type: 'change',
						path: "modifyPwd"
					}
				}
				if (title === '实名认证' && this.userInfo['authStatus'] === 0) {
					this.$Router.push({
						name: 'realName',
						params: {
							type: 'TWO_ELEMENT_AUTH'
						}
					})
				} else {
					if (path === 'emailVerify') {
						let vtype;
						if (this.userInfo.email == "") {
							vtype = "EMAIL_BIND" //绑定邮箱
						} else {
							vtype = "EMAIL_BIND_VERIFY_ME" //更换邮箱
						}
						params = {
							path: "emailVerify",
							vtype
						}
						this.$Router.push({
							name: 'selectPay',
							params
						})
					} else if (path === 'phoneVerify') {
						let vtype;
						if (this.userInfo.phone == "") {
							vtype = "PHONE_BIND" //绑定手机号
						} else {
							vtype = "PHONE_BIND_VERIFY_ME" //更换手机号
						}
						params = {
							phone: this.userInfo['phone'],
							path: "phoneVerify",
							vtype
						}
						this.$Router.push({
							name: 'selectPay',
							params
						})
					} else {
						this.$Router.push({
							name: path,
							params
						})
					}
				}
			},
			async getUserInfo() {
				let {
					status: {
						code
					},
					result
				} = await this.$api.java_userInfoV2({
					userId: ""
				});
				if (code === 0) {
					this.userInfo = result
					console.log(this.userInfo)
				} else {
					this.$Router.pushTab({
						name: "mainLogin"
					})
				}
			},
			authStatus(status) {
				if (status == 0) {
					return '未认证'
				} else if (status == 30) {
					return '审核中'
				} else if (status == 31 && this.userInfo.photo1 != null) {
					return '已实名'
				} else if (status == 31 && this.userInfo.photo1 == null) {
					return '继续完善实名信息'
				}
			},
			substr(str) {
				if (str) {
					return str.substr(0, 3) + '****' + str.substr(7)
				}
				return '去绑定'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		padding: 60rpx 40rpx 0;

		.item {
			display: flex;
			justify-content: space-between;
			align-content: center;
			color: var(--main-front-color);
			font-weight: bold;
			font-size: 28rpx;
			margin-bottom: 80rpx;
			line-height: 48rpx;

			.right {
				display: flex;
				justify-content: space-between;
				align-content: center;
				line-height: 48rpx;
				color: var(--secondary-front-color);
				font-weight: normal;
			}
		}

		.button-bar {
			margin-top: 64vh;
			background-image: none;
			background-color: var(--main-bg-color);
			color: var(--main-front-color);
			font-weight: normal;
			border: var(--secondary-front-color) solid 2rpx;
		}
	}
</style>
