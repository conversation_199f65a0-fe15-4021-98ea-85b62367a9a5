<template>
    <view class="face">

        <view class="center" v-if="initStatus" @click="startFaceIDAuthentication">
            <image
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250414/9f5ad1078f2113b6ff5d1caa972ebfe6_424x424.png" />
            <view class="title">Face ID</view>
            <view class="content">{{ $t("face.AccessDashboard") }}</view>
        </view>

        <view class="center" v-else>
            <image
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250414/cb8000ae52739edfabc9baa9780fef36_424x424.png" />
            <view class="title">{{ $t("face.not") }}</view>
            <u-button hover-class="none" class="btns flex_all" @click="startFaceIDAuthentication">{{ $t("face.again")
            }}</u-button>
        </view>

        <!-- <view class="bottom-text flex_all">
            Sign in with
            <a href="#" style="color: #000;margin-left: 12rpx;" @click="nav_to('login')">Email</a>
        </view> -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            initStatus: true,
            isIOS: false
        }
    },
    onLoad() {
        const systemInfo = uni.getSystemInfoSync();
        this.isIOS = systemInfo.platform === 'ios';
        console.log(this.isIOS);

        if (this.isIOS) {
            this.startFaceIDAuthentication()
        }
    },
    methods: {
        startFaceIDAuthentication() {
            if (!this.isIOS) {
                return
            }
            // 不管多少次，只要没成功，就一直验证
            uni.startSoterAuthentication({
                requestAuthModes: ['facial'],
                challenge: '123456',
                authContent: '请用Face ID验证身份',
                success: (res) => {
                    console.log('Face ID 验证成功:', res);
                    this.initStatus = true;
                    // this.$Router.back();
                    let token = uni.getStorageSync('token')
                    if (token) {
                        this.$Router.push({
                            name: 'index'
                        })
                    } else {
                        this.$Router.push({
                            name: 'login'
                        })
                    }

                    // this.isFaceIDModalVisible = false; // 通过后进入 App
                },
                fail: (err) => {
                    console.log('Face ID 验证失败:', err);
                    // 常见取消的 code：90009 或其他非认证失败的 code
                    // 只要失败了，就重新开始验证
                    // setTimeout(() => {
                    //     this.startFaceIDAuthentication();
                    // }, 100); // 稍微延迟再调起
                    this.initStatus = false;
                },
                complete: (res) => {
                    console.log('Face ID 完成:', res);
                }
            });
        },
        nav_to(e, email) {
            this.$Router.push({
                name: e,
                params: {
                    email
                }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.face {}

.center {
    margin-top: 470rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // height: 100vh;

    .btns {
        margin-top: 44rpx;
        width: 182*2rpx;
        height: 100rpx;
        border-radius: 128rpx;
        background: #FF82A3;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        color: #fff;

    }

    image {
        width: 212rpx;
        height: 212rpx;
    }

    .title {
        margin-top: 44rpx;
        font-family: Gilroy-ExtraBold;
        font-weight: 400;
        font-size: 60rpx;
        line-height: 120%;
        text-align: center;
        color: #333;
    }

    .content {
        margin-top: 12rpx;
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 32rpx;
        line-height: 120%;
        color: #333;

    }
}

.bottom-text {
    position: fixed;
    bottom: 0;
    height: 84*2rpx;
    width: 100vw;
    // opacity: 0.3;
    border-top-left-radius: 30*2rpx;
    border-top-right-radius: 30*2rpx;
    background: rgba(217, 214, 214, .3);

    font-family: Gilroy-SemiBold;
    font-weight: 400;
    font-size: 16*2rpx;
    line-height: 19.2*2rpx;
    color: #666666;
}
</style>