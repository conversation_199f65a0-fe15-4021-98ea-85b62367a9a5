<template>
    <div>
        <u-navbar :border-bottom="false" :title='title'>
        </u-navbar>

        <view class="comming">
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382799530201276416.png" />
            <text>功能暂未开放，敬请期待</text>
        </view>
    </div>
</template>

<script>
export default {
    data() {
        return {
            title: ''
        }
    },
    onLoad(e) {
        if (e.title) {
            this.title = e.title
        }
    }
}
</script>

<style lang="scss" scoped>
.comming {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 180rpx;
    image {
        width: 304rpx;
        height: 230rpx;
    }

    text {
        margin-top: 72rpx;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 32rpx;
        color: #000;
        opacity: .4;
    }
}
</style>