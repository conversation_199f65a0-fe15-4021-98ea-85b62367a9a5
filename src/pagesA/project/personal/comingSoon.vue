<template>
    <view class="main">
        <view class="back" @tap="nav_back()">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20240304/bb92e20e90212f1c693f90df3df739e7_80x80.png" mode="widthFix"></image>
		</view>
		<view class="body">
			<view class="null">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240310/9e42528d68bcfe82dcad646d39722d6a_480x480.png" mode="widthFix"></image>		
				<view class="text">
					敬请期待
				</view>
			</view>
			
		</view>
    </view>
</template>

<script>
export default {
    name: "setting",
    data() {
        return {
        }
    },
    methods: {
        nav_back() {
            this.$Router.back()
        },
		nav_logout() {
			uni.removeStorageSync("token")
			uni.removeStorageSync("uid")
			uni.removeStorageSync("contract_address")
			uni.removeStorageSync("isNewUser")
			this.$Router.push({name: "mainLogin"})
		},
    }
}
</script>

<style lang="scss" scoped>
.main{
	position: relative;
	.back{
		position: absolute;
		left:40rpx;
		top:40rpx;
		image{
			width:70rpx;
		}
	}
	.body{
		.null{
			image{
				width:182rpx;
				height:220rpx;
			}
		}
		.text{
			color:#A6A6A6;
			font-size:28rpx;
			text-align: center;
			margin-top:30rpx;
		}
		width:100%;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}

</style>
