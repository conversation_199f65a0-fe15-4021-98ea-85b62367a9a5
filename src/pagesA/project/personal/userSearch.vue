<template>
	<view class="main">
		<view class="top" :style="{'height':height+'rpx'}"></view>
		<view class="content ">
			<view class="search_view padding_lr">
				<view class="back" @click="nav_back">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240329/442de86adcfcf12f3454c0b1e126b70e_52x100.png"
						mode="widthFix"></image>
				</view>
				<u-search placeholder="" search-icon="https://cdn-lingjing.nftcn.com.cn/image/20240118/9b03ce9292767ae8eec9722d8eac0ebf_44x44.png" shape="square" v-model="keyword" @search="search" :clearabled="false" :show-action="false"></u-search>
				<view class="right" @tap="search">搜索</view>
			</view>
			<!-- <view class="search_lishi" v-if="searchHistory!=''">
				<view class="title">
					搜索历史
				</view>
				<view class="lishi" >
					<view class="li" v-for="(item,index) in searchHistory" @tap="clickItem(item)">
						{{item}}
					</view>
				</view>
			</view> -->
			<view class="tabbar_view">
				<view class="tabber">
					<u-tabs name="cate_name" :bar-style="barStyle" :list="list" bold :is-scroll="false" :item-width="200"
						:active-item-style="itemStyle" inactive-color="#fff" bg-color="#35333E" active-color="#fff"
						:current="current" @change="change"></u-tabs>
				</view>
			</view>
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view class="collection" v-show="seriesList!=''&&isLoadingStatus == 1">
				<view class="li" v-for="(item,index) in seriesList">
					<view class="cover">
						<image :src="item.smallCover" v-show="current==0||current==1" mode="aspectFill"
							@tap="open_sale(item)">
						</image>
						<image :src="item.cover" v-show="current==2" mode="aspectFill" @tap="nav_details(item)"></image>
					</view>
					<view class="title oneOver">
						{{item.title}}
					</view>
					<view class="total_text">
						<view class="" v-show="current==2">售价:￥<text>{{item.price}}</text></view>
						<view class="" v-show="current==1">共<text>{{item.allCount}}</text>个 寄售<text>{{item.saleSum}}</text>个
						</view>
						<view class="" v-show="current==0">共<text>{{item.allCount}}</text>个</view>
					</view>
				</view>
			</view>
			<view class="null_body" v-show="seriesList==''&&isLoadingStatus == 2">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						暂无数据
					</view>
				</view>
			</view>
			<u-popup ref="popup" class="solePopup" v-model="solePopupBox" :border-radius="40" height="1000rpx"
				mode="bottom">
				<view class="popup-content">
					<view class="tx_view">
						<image :src="popupHead.smallCover" mode="aspectFill"></image>
					</view>
					<view class="content_view">
						<view class="right_font_view">
							<view class="title twoOver">
								{{popupHead.title}}
							</view>
							<view class="num">
								<view class="gs">共{{popupHead.allCount}}个</view>
								<view class="jj">均价<text>￥{{popupHead.avgBuyPrice}}</text></view>
							</view>
						</view>
						<scroll-view ref="scrollView" scroll-top="scrollTop" class="scroll-Y" scroll-y
							:refresher-triggered="triggered"  :refresher-enabled="true"
							@refresherrefresh="refresher" @scrolltolower="lower">
							<view class="sole_ul_view">
								<view class="sole_ul_li" :class="{'active':item.sale == 1}"
									v-for="(item,index) in popupList">
									<view class="type" :class="{'active':item.sale == 1}" @tap="openMore(item,index)">
										<text v-show="item.sale==1">寄售中</text>
										<text v-show="item.sale==0">未寄售</text>
									</view>
									<view class="price_tid" @tap="nav_details(item)">
										<view class="price">
											<text v-show="item.sale==1">
												￥{{item.price}}
											</text>
										</view>
										<view class="tid_view">
											<text>TID:</text>
											<text>{{item.tidd}}</text>
										</view>
									</view>
								</view>
							</view>
							<!-- <view slot="refresher">
								<view class="loadding">
									<view class="gif">
										<image
											src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
											mode="widthFix"></image>
									</view>
								</view>
							</view> -->
						</scroll-view>
						<view class="sale_view" v-if="current == 1" @tap="batchUnSaleCollectionList">
							批量取消寄售
						</view>
					</view>
				</view>
			</u-popup>
			<u-popup v-model="isMore" mode="bottom" @close="closeMore" border-radius="36">
				<view class="mall_more_ul">
					<view class="close" @click="isMore = false">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240411/d989685a9e1809a9396823f629ec2236_160x160.png" mode="widthFix"></image>
					</view>
					<view >
						<view class="li">
							<text>更多</text>
						</view>
						<view class="li"
							@click="openUp()" v-show="moreInfo.sale == 0">
							<view class="icon">
								<image src="@/static/imgs/mall/mall_more_up.png" mode="widthFix"></image>
							</view>
							<text>寄售作品</text>
						</view>
						<view class="li"
							@click="openDown()" v-show="moreInfo.sale == 1">
							<view class="icon">
								<image src="@/static/imgs/mall/mall_more_dow.png" mode="widthFix"></image>
							</view>
							<text>停售作品</text>
						</view>
						<view class="li" @click="openSet()">
							<view class="icon">
								<image src="@/static/imgs/mall/mall_more_set.png" mode="widthFix"></image>
							</view>
							<text>设为头像</text>
						</view>
						<view class="li"  @click="openDestroy()">
							<view class="icon">
								<image src="@/static/imgs/mall/mall_more_del.png" mode="widthFix"></image>
							</view>
							<text>销毁作品</text>
						</view>
					</view>
				</view>
			</u-popup>
			<modalPop ref="modalPop" :tid="moreInfo.tid"
				:isCreation="0" :isMoreShow="true"  @downSucceed="downSucceed"
				@upSucceed="upSucceed" @destroySucceed="destroySucceed"></modalPop>
		</view>
	</view>
	</view>
</template>
<script>
	import {
		desensitizeMobile
	} from '@/utils/utils'; // 假设这是你存放公共方法的路径
	import api from '@/common/api/index.js';
	import modalPop from "@/components/public/modalPop";
	export default {
		data() {
			return {
				height: "",
				keyword: "",
				tabList: [],
				seriesList: [],
				show: true,
				pageNum: 1,
				marketTabId: "",
				searchHistory: [],
				list: [{
					cate_name: '我的藏品'
				}, {
					cate_name: '寄售中'
				}, {
					cate_name: '已出售',
					cate_count: 5
				}],
				current: 0,
				barStyle: {
					'background': 'linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%)',
					'width': '54rpx',
					'height': '10rpx',
					'border-radius': '0rpx',
				},
				itemStyle: {
					'font-size': '32rpx',
					'height': '90rpx',
				},
				solePopupBox: false,
				triggered: false,
				ctid: "",
				popupHead: "",
				popupList: [],
				sale:"",
				isMore:false,
				moreInfo:"",
				isLoadingStatus:0
				
			}
		},
		onLoad(options) {
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			});
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					console.log('当前平台？？？？？', res.statusBarHeight)
					_this.height = res.statusBarHeight * 2
					console.log(_this.height)
				}
			});
			if(options.keyword){
				this.keyword = options.keyword
				this.popupList = []
				this.pageNum = 1
				this.solePopupBox = true
				this.ctid = options.ctid
				this.getSaleHead()
			}
			this.searchHistory=this.getSearchHistory()
			this.holdSeriesList()
		},
		onShow() {
			console.log(22222)
		},
		components:{
			modalPop
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					if(this.current == 2){
						this.saleSeriesList()
					}else{
						this.holdSeriesList()
					}
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.pageNum = 1
				this.getList()
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 1000);

		},
		methods: {
			search() {
				if(this.keyword){
					this.isLoadingStatus = 0
					if(this.current == 2){
						this.seriesList = []
						this.pageNum=1
						this.saleSeriesList()
					}else{
						this.seriesList = []
						this.pageNum = 1
						this.holdSeriesList()
					}
					this.addSearchHistory(this.keyword)
				}else{
					uni.showToast({
						title: "请输入关键字",
						icon: 'none',
						duration: 3000
					});
				}
				
			},
			// 添加搜索记录
			addSearchHistory(keyword) {
				let history = this.getSearchHistory();

				// 删除已存在的关键词
				history = history.filter(item => item !== keyword);

				// 将新的关键词添加到历史记录的最前面
				history.unshift(keyword);

				// 限制历史记录的数量（例如只保留10条）
				if (history.length > 10) {
					history.pop();
				}

				// 存储到本地
				uni.setStorageSync('searchHistory', history);

				// 更新当前组件的数据
				this.searchHistory = history;
			},
			// 获取搜索历史
			getSearchHistory() {
				const history = uni.getStorageSync('searchHistory') || [];
				return history;
			},
			nav_back() {
				this.$Router.back()
			},
			clickItem(item){
				this.keyword=item
				this.search()
			},
			change(index) {
				this.isLoadingStatus = 0
				this.isFooter = true
				if(index == 2 ){
					this.current = index
					this.seriesList = []
					this.pageNum=1
					this.saleSeriesList()
				}else{
					this.current = index
					this.pageNum = 1
					if(index == 1){
						this.sale = 1
					}else{
						this.sale = ""
					}
					this.seriesList = []
					this.holdSeriesList()
				}
			},
			nav_login() {
				this.$Router.push({
					name: "loginMain"
				})
			},
			nav_to(name) {
				this.$Router.push({
					name
				})
			},
			open_sale(item) {
				this.popupList = []
				this.pageNum = 1
				this.solePopupBox = true
				this.ctid = item.ctid
				this.getSaleHead()
			},
			async holdSeriesList() {
				const {
					status,
					result
				} = await this.$api.getUserHoldSeriesList({
					pageNum: this.pageNum,
					pageSize: 10,
					sale: this.sale,
					keyword:this.keyword
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if(this.seriesList==''){
							this.isLoadingStatus = 2
						}
					} else {
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getSaleHead() {
				let res = await this.$api.getAppUserHoldSeriesCountAndAvgPriceVO({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					this.popupHead = res.result
					this.getUserSeries()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getUserSeries() {
				let res = await this.$api.userSeriesCollectionList({
					ctid: this.ctid,
					pageNum: this.popuePageNum
				});
				if (res.status.code == 0) {
					if(res.result.list!=''){
						this.popuePageNum++
						res.result.list.forEach((item) => {
							item.tidd = desensitizeMobile(item.tid)
							this.popupList.push(item)
						})
					}else{
						
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			strap() {
				this.$refs.lottieWeb.call('play')
			},
			lower() {
				console.log("触底了")
				this.getUserSeries()
			},
			scroll(e) {
			
			},
			refresher() {
				this.triggered = true
				this.popuePageNum =1
				this.popupList = []
				this.getUserSeries()
				setTimeout(() => {
					this.triggered = false
				}, 1000)
			},
			copy() {
				uniCopy({
					content: this.info.contractAddress,
					success: (res) => {
						uni.showToast({
							title: res,
							icon: 'none'
						})
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: 'none',
							duration: 3000,
						})
					}
				})
			},
			async batchUnSaleCollectionList() {
				let res = await this.$api.batchUnSaleCollectionList({
					ctid:this.ctid
				});
				if (res.status.code == 0) {
					this.solePopupBox = false
					this.seriesList = []
					this.holdSeriesList()
					uni.showToast({
						title: '批量取消寄售成功',
						icon: 'none',
						duration: 3000
					});
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_details(item){
				this.$Router.push({
					name:"detailsShop",
					params:{
						tid:item.tid,
						sale:item.sale
					}
				})
			},
			async saleSeriesList() {
				const {
					status,
					result
				} = await this.$api.soldList({
					pageNum: this.pageNum,
					pageSize: 10,
					keyword:this.keyword
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if(this.seriesList==''){
							this.isLoadingStatus = 2
						}
					} else {
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			openMore(item,index){
				this.sunIndex = index
				this.moreInfo = item
				this.isMore = true
			},
			closeMore() {
				this.isMore = false
			},
			
			// 调用设置头像
			openSet() {
				this.isMore = false
				this.$refs.modalPop.openPop('set');
			},
			// 调用停售作品
			openDown() {
				this.isMore = false
				this.$refs.modalPop.openPop('down');
			},
			// 调用寄售售作品
			openUp() {
				this.isMore = false
				this.$refs.modalPop.openPop('up');
			},
			// 调用销毁作品
			openDestroy() {
				this.isMore = false
				this.$refs.modalPop.openPop('destroy');
			},
			downSucceed(){
				this.popupList[this.sunIndex].sale = 0
			},
			upSucceed(){
				this.popupList[this.sunIndex].sale = 1
			},
			destroySucceed(){
				this.popupList.splice(this.sunIndex, 1);
			},
			showIp(){
				if(this.ipShow&&!this.suo){
					console.log(1111)
					this.suo = true
					this.ipShow = false
					let timeout = setTimeout(()=>{
						this.ipShow = true
						this.suo = false
						clearInterval(timeout)
					},1100)
				}
				console.log(this.ipShow)
			}
		}
	}
</script>
<style lang="scss">
	::v-deep .solePopup{
		.u-drawer-bottom {
			background-color: transparent !important;
		}
	} 
	::v-deep .uni-input-input{
		width:85% !important;
	}
	.padding_lr {
		padding: 0rpx 34rpx;
	}

	.head_bg {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 1000rpx;
		height: 500rpx;
		z-index: -1;
	}

	.main {
		flex: 1;
		min-height: 100vh;
	}

	.head_title {
		height: 170rpx;
		// padding:86rpx 0 0 0;
	}

	.title_1 {
		color: #141414;
		font-weight: 600;
		font-size: 44rpx;
	}

	.content {}

	.search_view {
		padding-top: 30rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.right{
			font-size:28rpx;
			position: absolute;
			right:50rpx;
			color:#999999;
		}
		.back {
			width: 26rpx;
			margin-right: 20rpx;

			image {
				width: 26rpx;
			}
		}
	}

	.search_lishi {
		padding: 0rpx 32rpx;
		margin-top: 37rpx;
		
		.title {
			font-size: 28rpx;
			font-weight: 600
		}

		.lishi {
			height: 60rpx;
			overflow: auto;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-top: 30rpx;
			
			.li {
				border-radius: 10rpx;
				border: 1px solid #A6A6A6;
				color: #A6A6A6;
				font-size: 20rpx;
				padding: 0rpx 18rpx;
				height: 44rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 20rpx;
				flex: 1 0 auto;
				max-width: max-content; /* 或者指定一个最大宽度 */
			}
		}
	}

	.tabbar_view {
		margin-top: 40rpx;
		.tabber{
			width: 75%;
		}
	}

	.list_view {
		width: 638rpx;
		margin-top: 40rpx;
	}

	.list_li {
		display: flex;
		flex-direction: row;
		align-items: center;
		background-color: #F5F5F5;
		padding: 32rpx;
		border-radius: 24rpx;
		margin-bottom: 40rpx;
	}

	.left_img {
		margin: 0 40rpx 0 0;
	}

	.right_font {
		&_title {
			color: #141414;
			font-size: 38rpx;
			font-weight: 600;
		}
	}

	.sub_title {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.time {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.collection {
		padding: 0rpx 36rpx;
		margin-top: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		padding-bottom: 80rpx;
	
		.li {
			width: 320rpx;
			margin-bottom: 30rpx;
			padding: 20rpx;
			background-color: #25232D;
			border-radius: 30rpx;
	
			.cover {
				position: relative;
				width: 280rpx;
				height: 280rpx;
				border-radius: 20rpx;
	
				image {
					width: 280rpx;
					height: 280rpx;
					border-radius: 20rpx;
				}
	
				.left_bottom_icon {
					position: absolute;
					bottom: 0;
					left: 0;
					min-width: 140rpx;
					height: 44rpx;
					border-radius: 0px 20rpx 0px 30rpx;
					text-align: center;
					font-size: 20rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					background: rgba(20, 20, 20, 0.7);
					padding: 0rpx 30rpx;
					border-radius: 0px 20px 0px 30px;
				}
			}
	
			.title {
				width: 100%;
				font-size: 24rpx;
				color: #fff;
				margin-top: 20rpx;
			}
	
			.total_text {
				color: #fff;
				font-size: 24rpx;
				margin-top: 10rpx;
	
				text {
					color: #63EAEE;
				}
			}
		}
	}

	.null_body {
		.null {

			.img {
				display: flex;
				justify-content: center;

				image {
					width: 142rpx;
				}
			}

		}

		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}

		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	::v-deep .u-drawer-bottom {
		background-color: transparent !important;
	}
	
	.popup-content {
		height: 100%;
		display: flex;
		align-items: flex-end;
		position: relative;
	
		.tx_view {
			width: 260rpx;
			height: 260rpx;
			position: absolute;
			top: 0;
			left: 40rpx;
	
			image {
				width: 260rpx;
				border-radius: 30rpx;
				height: 260rpx;
			}
		}
	
		.content_view {
			width: 100%;
			height: 920rpx;
			background: linear-gradient(180deg, #25232D 0%, #111116 100%);
			border-radius: 40rpx 40rpx 0rpx 0rpx;
	
			.right_font_view {
				padding: 20rpx 20rpx 60rpx 340rpx;
	
				.title {
					color: #fff;
					font-size: 28rpx;
					width: 100%;
					height: 70rpx;
				}
	
				.num {
					color: #A6A6A6;
					font-size: 28rpx;
	
					.gs {
						margin-bottom: 10rpx;
					}
	
					.jj {
						text {
							color: #63EAEE;
						}
					}
				}
			}
	
			.sole_ul_view {
				padding: 0rpx 40rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;
	
				.sole_ul_li {
					width: 200rpx;
					height: 150rpx;
					margin-bottom: 35rpx;
					margin-right: 35rpx;
					border-radius: 24rpx;
					overflow: hidden;
	
					&.active {
						border: 1px solid #63EAEE;
					}
	
					.type {
						height: 60rpx;
						background-color: #35333E;
						display: flex;
						justify-content: center;
						align-items: center;
						color: #fff;
						font-size: 22rpx;
	
						&.active {
							color: #25232D;
							background-color: #63EAEE;
						}
					}
	
					.price_tid {
						height: 90rpx;
						background: #46454F;
	
						.price {
							width: 100%;
							font-size: 28rpx;
							line-height: 34rpx;
							font-weight: 600;
							color: #63EAEE;
							text-align: center;
							font-weight: 600;
							padding-top: 10rpx;
						}
	
						.tid_view {
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 20rpx;
							color: var(--default-color3);
							margin-top: 10rpx;
						}
					}
				}
	
				.sole_ul_li:nth-child(3n) {
					margin-right: 0rpx;
				}
	
			}
	
			.sale_view {
				width: 670rpx;
				height: 120rpx;
				border: 1px solid #63EAEE;
				color: #63EAEE;
				font-size: 34rpx;
				font-weight: 600;
				position: fixed;
				bottom: 20rpx;
				left: 40rpx;
				right: 40rpx;
				border-radius: 24rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #111116;
			}
		}
	}
	.scroll-Y{
		max-height:670rpx;
	}
	::v-deep .u-drawer-bottom {
		background-color:#35333E !important;
	}
	.mall_more_ul {
		position: relative;
		.close{
			position: absolute;
			right:0rpx;
			top:0rpx;
			image{
				width:80rpx;
			}
		}
		.li {
			display: flex;
			justify-content: center;
			align-items: center;
			color: #FFFFFF;
			border-bottom: 1px solid #53505D;
			font-size: 28rpx;
			padding:38rpx 0rpx;
			
			.icon {
				margin-right: 20rpx;
	
				image {
					width: 44rpx;
				}
			}
	
			text {
				margin-right: 20rpx;
			}
		}
	
		.li:last-child {
			border-bottom: none;
		}
	}
</style>