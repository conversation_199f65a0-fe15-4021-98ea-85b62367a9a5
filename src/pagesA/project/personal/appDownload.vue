<template>
    <view class="main">
        <u-image src="https://cdn-lingjing.nftcn.com.cn/image/20241118/aeaeda1549bca9124db8d28681b9b818_1125x2503.png" mode="widthFix"></u-image>
		<view class="div1" @click="nav_Android()">
			
		</view>
		<view class="div2" @click="nav_mzhan()">
			
		</view>
		<view class="div3" @click="nav_ios()">
			
		</view>
     <!-- <div class="action">
          <div class="button" v-if="!unknownDevice">
              <u-button hover-class="none"  :hair-line="false" @click="download()"
                        >立即安装</u-button>
          </div>
          <template v-else >
              <u-button  hover-class="none"  :hair-line="false" @click="nav_ios()">
                  <img :src="require('/src/static/imgs/personal/ios.png')" mode="widthFix" >
                  iOS 下载
              </u-button>
              <u-button hover-class="none"  :hair-line="false" @click="nav_Android()">
                  <img :src="require('/src/static/imgs/personal/android.png')" mode="widthFix" >
                  安卓下载
              </u-button>
          </template>
      </div> -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            unknownDevice:false,
            url:''
        };
    },
    onLoad() {
    },
    methods: {
        nav_Android() {
            window.location.href = "https://cdn-lingjing.nftcn.com.cn/app/android_cp/BigverseCp.apk"
        },
        nav_ios() {
            window.location.href = "https://apps.apple.com/us/app/nftcn/id1605702361"
        },
        nav_mzhan() {
			this.$Router.push({
				name:'download'
			})
        }
    }
}
</script>

<style lang="scss" scoped>
.main {
    position: relative;
    display: flex;
    justify-content: center;
	.div1{
		width:420rpx;
		height:70rpx;
		position: absolute;
		top:320rpx;
		left:0rpx;
		right:0rpx;
		margin:0 auto;
	}
	.div2{
		width:420rpx;
		height:70rpx;
		position: absolute;
		top:434rpx;
		left:0rpx;
		right:0rpx;
		margin:0 auto;
	}
	.div3{
		width:420rpx;
		height:70rpx;
		position: absolute;
		top:544rpx;
		left:0rpx;
		right:0rpx;
		margin:0 auto;
	}
}

//.img {
//	width: 100%s;
//
//	image {
//		width: 100%;
//	}
//
//	.Android {
//		width: 280rpx;
//		height: 80rpx;
//		position: absolute;
//		top: 1132rpx;
//		left: 90rpx;
//	}
//
//	.ios {
//		width: 280rpx;
//		height: 80rpx;
//		position: absolute;
//		top: 1132rpx;
//		right: 90rpx;
//	}
//}
</style>
