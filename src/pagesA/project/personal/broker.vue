<template>
	<view class="broke-body">
		<view class="head-cart">
			<view class="title">
				<view class="left" @click="nav_back()">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240305/f605d9efcf61d5838e64a76a631bbebd_26x50.png"
						mode="widthFix"></image>
				</view>
				经纪商奖励(LV{{info.level}})
			</view>
			<view class="cart_view">
				<view class="left">
					<view class="msg_text">
						本期可提现金额
					</view>
					<view class="msg_price">
						<text>￥</text>{{info.canWithdrawalRewordAmount}}
					</view>
					<view class="jiangli_price">
						经纪商奖励:￥{{info.allRewordAmount}}
					</view>
					<view class="msg_button" @click="open()" :class="{'active':info.canWithdrawalRewordAmount>0}">
						提现
					</view>
				</view>
				<!-- https://cdn-lingjing.nftcn.com.cn/image/20240402/34a636d79254d48319b0905bde3fb834_170x151.png -->
				<view class="right">
					<text>LV{{info.level}}</text>
				</view>
			</view>
		</view>
		<view class="list-body">
			<view class="label_title">
				<text>本周期</text>
				<view class="right_text" v-if="info.level>0">
					消费满￥{{info.keepLevelLimit}}即可提现
				</view>
				<view class="right_text" v-else>
					当前等级为0无法提现，请提高您的等级
				</view>
			</view>
			<view class="item_ul">
				<view class="li">
					<view class="label">时间</view>
					<view class="value">{{info.currentLevelStartTime}}-{{info.currentLevelEndTime}}</view>
				</view>
				<view class="li">
					<view class="label">本周期消费总计</view>
					<view class="value">￥{{info.currentCycleConsumptionTotalAmount}}</view>
				</view>
				<view class="li">
					<view class="label">经纪商奖励累计提现</view>
					<view class="value">￥{{info.accumulatedWithdrawal}}</view>
				</view>
			</view>
			<view class="label_title border_no">
				<text>邀新奖励明细</text>
			</view>
			<view class="table-head">
				<view class="li name">
					用户名
				</view>
				<view class="li">
					消费
				</view>
				<view class="li">
					奖励
				</view>
			</view>
			<view class="table-body"  v-if="list!=''">
				<view class="li_view" v-for="(item,index) in list">
					<view class="img_name ">
						<view class="top">
							<view class="img">
								<image :src="item.avatar" mode="widthFix"></image>
							</view>
							<text class="oneOver">{{item.name}}</text>
						</view>
						<view class="time">{{item.lastPayTime}}</view>
					</view>
					<view class="price">
						￥{{item.consumptionTotalAmount}}
					</view>
					<view class="jiangli">
						￥{{item.reword}}
					</view>
				</view>
			</view>
			<view class="null_body" v-else>
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						暂无数据
					</view>
				</view>
			</view>
			<u-modal v-model="isWithdrawal"  :content-style="bgObject"
				:title-style="titleObject" border-radius="30" :show-title="false" :show-confirm-button="false">
				<view class="new-modal-content">
					<view class="title_bg">
						<view class="icon"></view>
						提现
					</view>
					<view class="modal-content">
						<view class="flex_input">
							<view class="input">
							<u-input class="modal-resale-input" v-model="withdrawalPrice" placeholder="请输入提现金额" type="number"
									border border-color="transparent" :trim="true" :adjust-position="true"
									:show-confirmbar="true" :custom-style="{'padding-left': '25rpx'}" 
									:clearable="false" />
							</view>
						</view>
					</view>
					<view class="showModal-btn">
						<view class="img_reasale" @click="withdrawal()">确认提现</view>
						<view class="img_cancel" @click="cancel()">取消</view>
					</view>
				</view>
			</u-modal>
			<u-modal v-model="isWithdrawSuccess"
				 border-radius="30" :mask-close-able="true" :show-title="false" :show-confirm-button="false" width="480">
				<view class="new-modal-content">
					<view class="success_img">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240322/942264f29d34245fa78a23becfe96b87_480x480.png" mode="widthFix"></image>
					</view>
					<view class="modal-content" style="border-bottom:none;">
						<p style="margin-bottom:10rpx;">提现申请成功</p>
						请前往钱包查看余额
					</view>
				</view>
			</u-modal>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:"",
				list:[],
				pageNum: 1,
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				titleObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				bgObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				isWithdrawal:false,
				withdrawalPrice:"",
				isWithdrawSuccess:false
				
			}
		},
		onLoad(options){
			this.getInfo()
			this.getList()
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.list = []
				this.pageNum = 1
				this.getList()
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 1000);
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			async getInfo() {
				let res = await this.$api.settlementedRewordInFo({
					
				});
				if (res.status.code == 0) {
					this.info = res.result
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getList() {
				this.isRequest = true
				const {
					status,
					result
				} = await this.$api.friendRewordInFo({
					pageNum:this.pageNum,
					pageSize:10
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							this.list.push(item)
						})
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async withdrawal() {
				if(this.withdrawalPrice == ""){
					uni.showToast({
						title: "请输入提现金额",
						icon: 'none',
						duration: 3000
					});
					return false
				}
				let res = await this.$api.settlementedWithdrawal({
					withdrawalAmount:this.withdrawalPrice
				});
				if (res.status.code == 0) {
					this.cancel()
					this.isWithdrawSuccess = true
					setTimeout(()=>{
						this.isWithdrawSuccess = false
						this.$Router.push({
							name:"myBalance"
						})
					},2000)
				} else {
					this.cancel()
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			open(){
				if(this.info.canWithdrawalRewordAmount>0){
					this.isWithdrawal = true
				}
			},
			cancel(){
				this.isWithdrawal = false
			},
			nav_back() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss">
	.broke-body {
		.head-cart {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240402/8a19a3bc59e505531dca61b10459fb1e_1125x975.png);
			background-size: 100% 100%;
			height: 650rpx;

			.title {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				color: #FFF;

				>.left {
					position: absolute;
					top: 20rpx;
					left: 34rpx;

					image {
						width: 26rpx;
					}
				}
			}

			.cart_view {
				padding: 0rpx 0rpx 0rpx 32rpx;
				margin-top: 84rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				>.left {
					.msg_text {
						font-size: 30rpx;
						color: #fff;
					}

					.msg_price {
						font-size: 56rpx;
						color: #63EAEE;
						font-weight: 600;
						margin-top: 24rpx;

						text {
							font-size: 40rpx;
						}
					}
					.jiangli_price{
						min-width:242rpx;
						height:50rpx;
						border-radius:30rpx;
						border:1px solid #4E4C59;
						margin-top:30rpx;
						font-size:24rpx;
						color:#fff;
						line-height: 50rpx;
						text-align: center;
					}
					.msg_button {
						margin-top: 40rpx;
						width: 180rpx;
						height: 64rpx;
						border-radius: 12rpx;
						background-color: #63EAEE;
						color: #101514;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 28rpx;
						font-weight: 600;
						opacity:0.5;
						&.active{
							opacity:1;
						}
					}
				}

				>.right {
					background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240402/34a636d79254d48319b0905bde3fb834_170x151.png);
					background-size: 100% 100%;
					width: 300rpx;
					height: 262rpx;
					text-align: center;
					padding-top: 90rpx;
					font-size: 44rpx;
					color: #101514;
					font-weight: 600;
				}
			}
		}

		.list-body {
			background-color: #35333E;
			border-top-left-radius: 40rpx;
			border-top-right-radius: 40rpx;
			width: 100%;
			height: 50vh;
			margin-top: -150rpx;
			padding: 30rpx;
			color: #fff;

			.label_title {
				height: 80rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 28rpx;
				padding-bottom: 20rpx;
				&.border_no{
					border:none;
				}
				.right_text {
					font-size: 24rpx;
					display: inline-block;
					background-image: linear-gradient(270deg, #E199F4 0%, #53EFEB 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					border: 1px solid #4E4C59;
					height: 50rpx;
					border-radius: 20rpx;
					padding: 0rpx 20rpx;
					line-height: 50rpx;
				}

				border-bottom: 1px solid #4E4C59;
			}

			.item_ul {
				padding-top: 40rpx;

				.li {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 24rpx;
					margin-bottom: 40rpx;

					.label {
						color: #8B8A91;
					}

					.value {
						color: #fff;
					}
				}
			}
			.table-head{
				background-color:#25232E;
				height:54rpx;
				padding:10rpx 20rpx;
				border-radius:20rpx;
				display: flex;
				justify-content:space-between;
				align-items: center;
				.li{
					color:#999999;
					font-size:22rpx;
				}
			}
			.table-body{
				.li_view{
					padding:10rpx 20rpx;
					display: flex;
					justify-content:space-between;
					align-items: center;
					font-size:24rpx;
					border-bottom:1px solid #45424D;
					padding:30rpx 0rpx;
					.img_name{
						width:200rpx;
						.top{
							.img{
								margin-right: 10rpx;
								width:50rpx;
								image{
									width:50rpx;
									margin-right: 10rpx;
									border-radius:50%;
								}
							}
							text{
								color:#fff;
								width:180rpx;
							}
							display: flex;
							justify-content: flex-start;
							align-items: center;
						}
						.time{
							font-size:22rpx;
							color:#999999;
						}
						
					}
					.price{
						
					}
					.jiangli{
						width:180rpx;
						text-align: right;
					}
				}
			}
		}
	}
	.null_body {
		.null {
	
			.img {
				display: flex;
				justify-content: center;
	
				image {
					width: 242rpx;
				}
			}
	
		}
	
		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
	
		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;
	
		.icon {
			position: absolute;
			left: 0rpx;
			top: 16rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}
	
	.new-modal-content {
		padding:35rpx 40rpx;
		background:var(--main-bg-color);
		.success_img{
			display: flex;
			justify-content: center;
			align-items: center;
			image{
				width:160rpx;
				height:160rpx;
			}
		}
		
		.modal-content{
			padding:35rpx 0rpx;
			border-bottom:1rpx solid #53505D;
			font-size:28rpx;
			color:#fff;
			text-align: center;
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color:#fff;
			}
			.img_cancel {
				border: 1px solid #fff;
			}
			.img_reasale {
				color:var(--default-color2);
				background: var(--primary-button-color);
			}
		}
	}
	.modal-resale-input::v-deep {
		margin: 0 auto;
		color: #FFF;
		border-radius: 0;
		
		.u-input__input {
			color: #63EAEE !important;
			font-weight: 600;
			font-size: 34rpx;
			padding: 0rpx 20rpx;
			background-color: #25232D;
			border-radius: 14rpx;
			width: 240rpx;
			text-align: center;
		}
	
		.uni-input-placeholder {
			font-weight: 400 !important;
			font-size: 28rpx !important;
		}
	}
</style>