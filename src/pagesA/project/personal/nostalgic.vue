<template>
	<view class="container">
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false" :custom-back="customBack"
			:immersive="true" :background="{ backgroundColor: 'var(--main-bg-color)' }" :title="titleTop"
			title-color="var(--main-front-color)" :is-back="isback">
			<view slot="right" class="register" @click="isMsg = true">一键全部销毁</view>
		</u-navbar>
		<view class="body_div">
			<view class="mall_catr">
				<!-- <view class="tabs">
					<u-tabs name="cate_name" count="cate_count" :list="list" :is-scroll="true" :show-bar="false"
						:current="current" @change="change1" bar-height="4"
						inactive-color="var(--secondary-front-color)" gutter="0" font-size="28"
						active-color="var(--active-color1)" bg-color="var(--main-bg-color)" :bold="true"></u-tabs>
				</view> -->


				<!-- 搜索区域 -->
				<view class="screen_search">
					<view class="screen">
						<!-- <view class="active" @click="worksSelected=1">综合</view> -->
						<!-- <commonSelect :options="priceOptions" :showMask="true" :clear="true" placeholder="价格"
							:selected.sync="priceSelected"></commonSelect>
						<commonSelect :options="timeOptions" :showMask="true" :clear="true" placeholder="时间"
							:selected.sync="timeSelected"></commonSelect> -->
						<!-- <view class="search_icon" @click="checkSearch()">
							<image src="@/static/imgs/mall/mall_sousuo.png" mode="widthFix"></image>
						</view>  v-if="isSearch"-->
					</view>
					<view class="search">
						<u-search v-model="keyword" placeholder="输入关键字搜索" search-icon-color="#999" bg-color="#1E1E1E"
							shape="square" :show-action="false" @search="search" @custom="custom" :focus="false"
							color="#FFF"></u-search>
						<view class="colse" @click="search()">
							搜索
						</view>
					</view>
					<!-- <view class="screen_mode" v-show="is_screen_mode">
						<view class="screen_mode_li">
							价格升序
						</view>
						<view class="screen_mode_li">
							价格降序
						</view>
					</view> -->
				</view>

				<view class="collection" v-if="goodsList.length">
					<view class="li" v-for="(item, index) in goodsList">
						<view class="cover">
							<image :src="item.smallCover" v-show="current == 0 || current == 1" mode="aspectFill"
								@tap="open_sale(item)">
							</image>
							<image :src="item.cover" v-show="current == 2" mode="aspectFill" @tap="nav_details(item)">
							</image>
						</view>
						<view class="title oneOver">
							{{ item.title }}
						</view>
						<view class="total_text">
							<view class="" v-show="current == 2">售价:￥<text>{{ item.price }}</text></view>
							<view class="" v-show="current == 1">共<text>{{ item.allCount }}</text>个 寄售<text>{{
								item.saleSum
							}}</text>个
							</view>
							<view class="" v-show="current == 0">共<text>{{ item.allCount }}</text>个</view>
							<view class="jishou" v-show="current == 0 && item.notSaleSign == 1">
								未开放寄售
							</view>
							<view class="" v-show="current == 0 && item.notSaleSign == 0">
								{{ item.isExitMarket == 1 ? '退市:' : '地板价:' }}<text>￥{{ item.floorPrice ? item.floorPrice
									: '-'
									}}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-else class="null">
					<u-empty
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						:text="msg_text" mode="list" margin-top="140"></u-empty>
				</view>
			</view>
		</view>
		<u-toast ref="uToast" />


		<u-modal v-model="isShows" :content="isShowsContent" title="审核未通过理由" :show-confirm-button="true"
			border-radius="0" :content-style="bgObject" :title-style="titleObject" :show-cancel-button="false"
			confirm-text="我知道了" confirm-color="#333333"></u-modal>
		<!-- 一键上停售 -->
		<u-modal v-model="isShowModalOffAndOnShelf" :title="modalOffAndOnShelfTitle" border-radius="0"
			:content-style="bgObject" :title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="offAndOnShelfCancel">取消</view>
				<view class="mb-confirm" @click="offAndOnShelfConfirm">确定</view>
			</view>
		</u-modal>
		<!-- 一键销毁 -->
		<u-modal v-model="isShowModalDestroy" :title="destroyTitle" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-content">
				{{ destroyMsg }}
			</view>
			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="destroyCancel">取消</view>
				<view class="mb-confirm" @click="destroyConfirm">确定</view>
			</view>
		</u-modal>

		<!-- 确认设置该作品为头像 -->
		<u-modal v-model="isShowSetImage" title="确认设置该作品为头像？" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="isShowSetImage = false">取消</view>
				<view class="mb-confirm" @click="setImage()">确定</view>
			</view>
		</u-modal>
		<!-- 一键寄售 -->
		<resale-pop :isShowModalResale.sync="isShowModalResale" :itemId="tid" @closeResale="closeResale"></resale-pop>
		<!-- 	<u-modal v-model="isShowModalResale" :show-title="false" border-radius="0" :show-confirm-button="false"
			:content-style="bgObject" :title-style="titleObject">
			<view style="padding: 50rpx 42rpx 0;">
				<u-input class="modal-resale-input" v-model="resalePrice" border-color="" placeholder="请输入寄售价格"
					type="number" :custom-style="{'padding-left': '25rpx','color':'var(--message-box-point-color)'}" border
					@input="changePrice" />
			</view>
			<view class="msg" style="padding: 50rpx 42rpx 0;" v-if="contentList!=''">
				<view v-for="(item,index) in contentList" :key="index"
					style="margin-bottom:16rpx;font-size:26rpx;font-weight:500;line-height:30rpx;">
					{{item}}
				</view>
			</view>
			<view class="xieyi">
				<view class="p">
					<image @click="j_isAgree" v-if="!isAgree" src="@/static/imgs/public/check.png" mode="">
					</image>
					<image @click="j_isAgree" v-else src="@/static/imgs/public/checked.png" mode=""></image>
					<view class="xieyi_msg" @click="nav_link('平台用户售卖服务协议',1)">
						您已同意<text>《平台用户售卖服务协议》</text>
					</view>
				</view>
			</view>
			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="resaleCancel()">取消
				</view>
				<view class="mb-confirm" @click="reasaleConfirm()">确认</view>
			</view>
		</u-modal> -->
		<!-- 藏品一键停售 -->
		<u-modal v-model="isShowModalCollectionOffShelf" title="是否停售藏品" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">

			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="collectionOffShelfCancel()">取消</view>
				<view class="mb-confirm" @click="collectionOffShelfConfirm()">确定</view>
			</view>
		</u-modal>
		<!-- 查看证书-->
		<u-mask :show="isShowMaskCert" z-index="999" @click="closeMaskCert"></u-mask>
		<view class="modal-cert" v-if="isShowMaskCert" @click="closeMask">
			<view class="modal_head">
				<image :src="existingevidenceList.u_avatar" mode="acseptFit" />
				<view>BIGVERSE</view>
				<view class="time">存证时间:{{ existingevidenceList.created_at }}</view>
			</view>
			<view class="poster_content">
				<view class="photoShow">
					<image :src="existingevidenceList.cover" mode="aspectFill" class="img"></image>
					<image src="@/static/imgs/public/badge.png" class="icon" mode="widthFix"></image>
				</view>
				<!-- <view class="content_title">
		      <view class="type_tip" v-if="isBind">盲盒</view>
		      {{existingevidenceList.title}}
		    </view> -->
				<view class="content_line">
					<view class="line_label">作者：</view>
					<view class="line_detail">{{ existingevidenceList.o_nickname }}</view>
				</view>
				<view class="content_line">
					<view>拥有者：</view>
					<view class="line_detail">{{ existingevidenceList.o_nickname }}</view>
				</view>
				<view class="content_line">
					<view class="line_label">价格：</view>
					<view class="line_detail">￥{{ existingevidenceList.price }}</view>
				</view>
				<view class="content_line line_end">
					<view class="line_id">TokenID：</view>
					<view class="line_detail tokenID">{{ existingevidenceList.tid }}</view>
				</view>
			</view>
			<view class="mc-share">
				<view class="qrimg-i">
					<tki-qrcode v-if="ifShow" cid="qrcode1" ref="qrcode" :val="val" :size="size" :unit="unit"
						:background="background" :foreground="foreground" :pdground="pdground" :icon="icon"
						:iconSize="iconsize" :lv="lv" :onval="onval" :loadMake="loadMake" :usingComponents="true"
						@result="qrR" />
				</view>
				<view class="mcs-right">
					<view>NFTCN</view>
					<view>截屏保存至本地</view>
					<view class="tip_code">扫描二维码查看系列</view>
				</view>
			</view>
			<view class="mcb-foot">NFT CHINNA VERIFIED ART WROK</view>
		</view>

		<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false"
			border-radius="0" :content-style="bgObject" :title-style="titleObject">
			<view class="BankVerifyBody">
				<view class="head_title_y">
					<view class="right" @click="isPassword = false">
						<image src="../../../static/imgs/mall/mall_colse.png" mode="widthFix"></image>
					</view>
					请先设置支付密码
				</view>
				<view class="msg_y">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="modal-btn space-between" style="padding:60rpx 0rpx 0rpx 0rpx;">
					<view class="mb-cancel" @click="isPassword = false">取消
					</view>
					<view class="mb-confirm" @click="SetPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isMailboxVerify" width="80%" border-radius="0" :show-title="false"
			:show-confirm-button="false">
			<view class="BankVerifyBody">
				<view class="head_title">
					<view class="right" @click="isBankVerifyColse(1)">
						<image src="../../../static/imgs/mall/mall_colse.png" mode="widthFix"></image>
					</view>
					支付验证
				</view>
				<view class="item">
					<view class="labal">
						邮箱验证码
					</view>
					<view class="flex_between_x input">
						<view class="left">
							{{ this.email }}
						</view>
						<!-- <view class="right" @click="isShow=true">
							<image src="../../../static/imgs/public/why.png" mode="widthFix"></image>
						</view> -->
					</view>
				</view>
				<view class="item">
					<view class="labal">
						验证码
					</view>
					<view class="flex_between_x input">
						<view class="left">
							<input type="number" v-model="MailboxVerifyCode" maxlength="6" placeholder="请输入验证码" />
						</view>
						<view class="right">
							<text class="yzm" v-show="showVerify == true" @click="sendMailboxVerify()">获取验证码</text>
							<text class="yzm" v-show="showVerify == false">{{ count }}秒后可重试</text>
						</view>
					</view>
				</view>
				<view class="footer">
					<u-button :throttle-time="1000" :hair-line="false" hover-class="none" shape="square"
						@click="mailboxVerifySubmit()">
						支付
					</u-button>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
		<u-modal v-model="isWorkSsucceed" border-radius="0" :show-title="false" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="work_ssucceed">
				<view class="icon">
					<image src="@/static/imgs/public/checked.png" mode="widthFix"></image>
				</view>
				<view class="msg">
					{{ workSsucceedText }}
				</view>
			</view>
		</u-modal>
		<pay-popup ref="payPopup" :popup-show.sync="isPasswordImport" :title="passwordTitle" :message="passwordMsg"
			order-type="" :email="email" :mode="mode" @pay="finishPay" @createSuccess="createSuccess" />
		<!-- <modalPop ref="modalPop" :tid="moreInfo.tid"></modalPop> -->
		<modalPop ref="modalPop" :tid="moreInfo.tid" :isCreation="0" :isMoreShow="true" @downSucceed="downSucceed"
			@upSucceed="upSucceed" @destroySucceed="destroySucceed"></modalPop>
		<setPresellPop :tid="tid" :show.sync="isShow" :price.sync="expectedPrice" type="1" @succeed="expectedSucceed">
		</setPresellPop>
		<introducePop :show.sync="isShowIntroduce" :title="title" :introduce="introduce">
		</introducePop>
		<globalWindow></globalWindow>
		<u-modal v-model="isMsg" title="一键全部销毁" border-radius="0" :content-style="bgObject" :title-style="titleObject"
			:show-confirm-button="false">
			<view class="modal-content">
				一键全部销毁藏品将在区块链发送至黑洞地址，永远不可复原。确认一键销毁所有怀旧藏品？
			</view>
			<view class="modal-btn space-between">
				<view class="mb-cancel" @click="isMsg = false">取消</view>
				<view class="mb-confirm" @click="openMdel">确定</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg" style="
		  padding: 10rpx 20rpx 40rpx 20rpx;
		  text-align: center;
		  font-size: 26rpx;
		  line-height: 40rpx;
		">
				销毁中...
			</view>
		</u-modal>

		<!-- 更多操作popup -->
		<u-popup v-model="isMore" mode="bottom" @close="closeMore" border-radius="36">
			<view class="mall_more_ul">
				<view class="close" @click="isMore = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240411/d989685a9e1809a9396823f629ec2236_160x160.png"
						mode="widthFix"></image>
				</view>
				<view>
					<view class="li">
						<text>更多</text>
					</view>
					<view class="li" @click="openSell()" v-show="popupHead.notSaleSign == 3">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_up.png" mode="widthFix"></image>
						</view>
						<text>转售作品</text>
					</view>
					<view class="li" @click="openUp()" v-show="moreInfo.sale == 0 && popupHead.notSaleSign !== 3">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_up.png" mode="widthFix"></image>
						</view>
						<text>寄售作品</text>
					</view>
					<view class="li" @click="openDown()" v-show="moreInfo.sale == 1">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_dow.png" mode="widthFix"></image>
						</view>
						<text>停售作品</text>
					</view>
					<view class="li" @click="openSet()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_set.png" mode="widthFix"></image>
						</view>
						<text>设为头像</text>
					</view>
					<view class="li" @click="openDestroy()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_del.png" mode="widthFix"></image>
						</view>
						<text>销毁作品</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- </scroll-view>  打开藏品popup -->
		<u-popup ref="popup" class="solePopup" v-model="solePopupBox" :border-radius="40" height="1000rpx"
			mode="bottom">
			<view class="popup-content">
				<view class="tx_view">
					<image :src="popupHead.smallCover" mode="aspectFill"></image>
				</view>
				<view class="content_view">
					<view class="right_font_view">
						<view class="right_icon"></view>
						<view class="title twoOver">
							{{ popupHead.title }}
						</view>
						<view class="num">
							<view class="gs">共{{ popupHead.allCount }}个</view>
							<view class="jj">均价<text>￥{{ popupHead.avgBuyPrice }}</text></view>
						</view>
						<view class="mall_right" @click="nav_series()">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240424/c8c4d01ae296db728fe9039da002bb3a_48x45.png"
								mode="widthFix"></image>
							<text>
								市场
							</text>
						</view>
					</view>

					<scroll-view ref="scrollView" scroll-top="scrollTop" class="scroll-Y" scroll-y
						:refresher-triggered="triggered" :refresher-enabled="true" @refresherrefresh="refresher"
						@scrolltolower="lower">
						<view class="sole_ul_view">
							<view class="sole_ul_li" :class="{ 'active': item.sale == 1 }"
								v-for="(item, index) in popupList">
								<view class="type" :class="{ 'active': item.sale == 1 }" @tap="openMore(item, index)">
									<text v-show="item.sale == 1">寄售中</text>
									<text v-show="item.sale == 0">未寄售</text>
								</view>
								<view class="price_tid" @tap="nav_details(item)">
									<view class="price">
										<text v-show="item.sale == 1">
											￥{{ item.price }}
										</text>
									</view>
									<view class="tid_view">
										<text>TID:</text>
										<text>{{ item.tidd }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- <view slot="refresher">
							<view class="loadding">
								<view class="gif">
									<image
										src="https://cdn-lingjing.nftcn.com.cn/image/20240409/7fa0e98fabb7e89d11e9e5cc0b9b2c84_250x250.gif"
										mode="widthFix"></image>
								</view>
							</view>
						</view> -->
					</scroll-view>
					<view class="sale_view" v-if="current == 1" @tap="batchUnSaleCollectionList">
						批量取消寄售
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import {
	desensitizeMobile
} from '@/utils/utils'; // 假设这是你存放公共方法的路径
import antiShake from "@/common/public.js"
import ModelShare from '../../../components/index/model_share';
import payPopup from "@/components/payPopup/index.vue";
import resalePop from '@/components/public/resalePop.vue';
import modalPop from "@/components/public/modalPop";
import setPresellPop from '@/components/public/setPresellPop.vue';
import introducePop from '@/components/public/introducePop.vue';
import commonSelect from "@/components/select/commonSelect";
export default {
	data() {
		return {
			isback:true,
			titleTop: '',
			isMore: false,
			moreInfo: "",
			sunIndex: -1,
			popupHead: "",
			popuePageNum: 1,
			popupList: [],
			triggered: false,

			solePopupBox: false,
			list: [],
			current: 0,
			exploreList: [],
			pageNum: 1,
			isOriginator: true,
			keyword: "",
			search_icon: "../../../static/imgs/public/search_icon.png",
			isOptions: false,
			isReject: false,
			info: {
				seriesUser: "",
			},
			seriesId: "",
			goodsList: [],
			tags: "",
			saleStatus: 0,
			pageNum: 1,
			loadStatus: 'loadmore',
			isSeriesFooter: true, //系列没有更多了
			isSeriesRequest: false, //系列请求频繁拦截
			isShows: false,
			isShowsContent: "",
			ifShow: true,
			val: "", // 要生成的二维码值
			size: 66, // 二维码大小
			unit: 'px', // 单位
			background: 'var(--message-box-point-color)', // 背景色
			foreground: '#000000', // 前景色
			pdground: '#000000', // 角标色
			icon: '', // 二维码图标
			iconsize: 20, // 二维码图标大小
			lv: 3, // 二维码容错级别 ， 一般不用设置，默认就行
			onval: true, // val值变化时自动重新生成二维码
			loadMake: true, // 组件加载完成后自动生成二维码
			src: '', // 二维码生成后的图片地址或base64

			isError: false,
			content: "",
			contentList: [],

			tid: '',
			isShowModalOffAndOnShelf: false,
			modalOffAndOnShelfTitle: '',
			isShowMaskCert: false,
			isShowModalDestroy: false,
			isShowModalResale: false,
			resalePrice: '',
			isShowModalCollectionOffShelf: false,

			isPassword: false,
			isSetTradePassword: 0, //是否设置过支付密码
			isSetPayPassword: false,
			isPasswordImport: false,
			title: "请设置余额支付密码，用于支付验证。",
			password: '',
			psw: '',
			error: '',
			passwordImportPay: "",
			isMailboxVerify: false,
			count: 0,
			email: "", //邮箱
			showVerify: false,
			MailboxVerifyCode: "",
			isShows: false,
			isShowsContent: "",
			nftcnOptionsVOList: {
				creationImg: "",
				collectionImg: ""
			},
			isRegistration: false,
			keyword: "",
			url: "",
			existingevidenceList: "",
			statusText: "",
			isCreate: 0,
			uid: "",
			isSelf: 0,
			itemId: "",
			isLoadding: false,
			listNum: 0,
			isAgree: false,
			isShowSetImage: false,
			is_screen_mode: false,
			index_sun: -1,
			titleObject: {
				'background-color': '#35333E',
				'color': '#fff'
			},
			bgObject: {
				'background-color': '#35333E',
				'color': '#fff',
			},
			isWorkSsucceed: false,
			isSearch: false,
			sortType: 1,
			flagDesc: 0,
			priceChecType: 0,
			timeChecType: 0,
			msg_text: "哎哟，TA还没有藏品哦！",
			isLoseText: false,
			workSsucceedText: "",
			isShowNavIcon: false,
			slide_num: "",
			reject_sun: -1,
			mode: "pay",
			behavior_content: {
				collection_id: "",
				collection_name: "",
				creator_id: "",
				creator_name: "",
				price: "",
			},
			shared_contract_address: '',
			accept_id: '',
			isShow: false,
			expectedPrice: 0,
			isShowIntroduce: false,
			introduce: "当您针对该藏品设置预期售价后，若该藏品其他版号真实成交价格达到您设定的价格，您将会收到系统推送，您可及时地进行寄售或改价。",
			title: "什么是预期售价",
			worksOptions: [{
				label: "综合",
				value: 1,
				disabled: false
			}],
			priceOptions: [{
				label: "价格升序",
				value: 1,
				disabled: false
			},
			{
				label: "价格降序",
				value: 2,
				disabled: false
			},
			],
			timeOptions: [{
				label: "时间排序",
				value: 1,
				disabled: false
			},],
			worksSelected: 1,
			priceSelected: '',
			timeSelected: '',
			passwordTitle: "确认寄售",
			passwordMsg: "请输入余额支付密码，用于寄售",
			destroyTitle: "确认销毁该作品",
			destroyMsg: "销毁作品后将无法恢复，是否确定删除该作品？",
			destroyFlag: 0,
			exhibitionUrl: "",
			titleObject: {
				'background-color': '#35333E',
				'color': '#fff',
				'padding': '80rpx 40rpx 0rpx 40rpx'
			},
			bgObject: {
				'background-color': '#35333E',
				'color': '#fff'
			},
			isMsg: false,
			isLoadding: false,
			platform: '',
			ctid: "",
		};
	},
	onShow() {

	},
	// 加载更多
	onReachBottom() {
		this.addRandomData();
	},
	onLoad(options) {
		this.url = getApp().globalData.url
		this.isSetTradePassword = uni.getStorageSync("isSetTradePassword")
		this.email = uni.getStorageSync("email")
		const {
			token,
			platform,
			title
		} = options;
		if (platform) {
			this.titleTop = ""
			this.isback = false
		} else {
			this.isback = true
			this.titleTop = "怀旧区"
		}
		this.platform = platform
		if (token) {
			uni.setStorageSync('token', token);
			this.getUserInfo()
		} else {
			this.getGoods(0)
		}
		this.list = [{
			cate_name: '全部'
		}, {
			cate_name: '寄售中',
		}, {
			cate_name: '未寄售',
		}]

	},
	watch: {
		worksSelected() {
			if (this.worksSelected == 1) {
				this.timeSelected = ''
				this.priceSelected = ''
				this.sortType = 1
				this.flagDesc = 0
				this.goodsList = []
				this.pageNum = 1
				this.getGoods(this.current)
				console.log("调用")
			}
		},
		priceSelected(newVal, oldVal) {
			console.error(this.current)
			let index;
			if (this.isCreate == 0) {
				index = this.current + 1
			} else {
				index = this.current
			}
			// if (this.current == 0 || this.current == 3 ||) {
			// 	index = this.current
			// } else {
			// 	index = this.current + 1
			// }
			this.goodsList = []
			this.pageNum = 1
			if (this.priceSelected === '') { } else {
				this.timeSelected = ''
				this.sortType = 2
				if (this.priceSelected == 1) {
					this.flagDesc = 0
					this.worksSelected = 0
					this.getGoods(index)
				} else if (this.priceSelected == 2) {
					this.flagDesc = 1
					this.worksSelected = 0
					this.getGoods(index)
				} else {
					this.sortType = 1
					this.flagDesc = 0
					this.getGoods(index)
				}
			}
		},
		timeSelected(newVal, oldVal) {
			console.log(this.current)
			let index;
			if (this.isCreate == 0) {
				index = this.current + 1
			} else {
				index = this.current
			}
			this.goodsList = []
			this.pageNum = 1
			if (this.timeSelected === '') { } else {
				this.priceSelected = ''
				this.sortType = 3
				if (this.timeSelected == 1) {
					this.flagDesc = 1
					this.getGoods(index)
				} else {
					this.sortType = 1
					this.flagDesc = 0
					this.getGoods(index)
				}

			}
		}
	},
	onPageScroll(res) {
		// console.log(res.scrollTop)
		if (res.scrollTop >= (this.tabScrollTop - 34)) {
			this.isShowNavIcon = true;
		} else {
			this.isShowNavIcon = false;
		}
	},
	onPullDownRefresh() {
		setTimeout(() => {
			if (this.$refs.uWaterfall != undefined) {
				this.$refs.uWaterfall.clear()
			}
			this.goodsList = []
			this.pageNum = 1
			if (this.current == 0) {
				this.getGoods(this.current)
			} else {
				this.getGoods(this.current + 1)
			}


			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 1000);
	},
	mounted() {
		const query = uni.createSelectorQuery().in(this);
		query.select('.mall_catr').boundingClientRect();
		query.exec(data => {
			this.tabScrollTop = data[0].top;
		});
	},
	methods: {
		nav_series() {
			this.$Router.push({
				name: "seriesList",
				params: {
					ctid: this.ctid
				}
			})
		},
		// 调用设置头像
		openSet() {
			this.isMore = false
			this.$refs.modalPop.openPop('set');
		},
		// 调用停售作品
		openDown() {
			this.isMore = false
			this.$refs.modalPop.openPop('down');
		},
		// 调用寄售售作品
		openUp() {
			this.isMore = false
			this.$refs.modalPop.openPop('up');
		},
		// 调用销毁作品
		openDestroy() {
			this.isMore = false
			this.$refs.modalPop.openPop('destroy');
		},
		downSucceed() {
			this.popupList[this.sunIndex].sale = 0
		},
		upSucceed() {
			this.popupList[this.sunIndex].sale = 1
		},
		destroySucceed() {
			this.popupList.splice(this.sunIndex, 1);
		},
		closeMore() {
			this.isMore = false
		},
		openMore(item, index) {
			console.log(item, '12222');

			this.sunIndex = index
			this.moreInfo = item
			this.isMore = true
			this.solePopupBox = false

		},
		refresher() {
			this.triggered = true
			this.popuePageNum = 1
			this.popupList = []
			this.getUserSeries()
			setTimeout(() => {
				this.triggered = false
			}, 1000)
		},
		async getUserSeries() {
			let res = await this.$api.userSeriesCollectionList({
				ctid: this.ctid,
				pageNum: this.popuePageNum
			});
			if (res.status.code == 0) {
				if (res.result.list != '') {
					this.popuePageNum++
					res.result.list.forEach((item) => {
						item.tidd = desensitizeMobile(item.tid)
						this.popupList.push(item)
					})
					console.log(this.popupList, 'popuipliast');
				} else {

				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async getSaleHead() {
			let res = await this.$api.getAppUserHoldSeriesCountAndAvgPriceVO({
				ctid: this.ctid
			});
			if (res.status.code == 0) {
				this.popupHead = res.result
				this.getUserSeries()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		open_sale(item) {
			console.log(item)
			this.appUrl = getApp().globalData.url
			let link = `${this.appUrl}pagesA/project/personal/nostalgic`
			if (item.ctid == "-1") {
				// #ifdef APP
				this.$Router.push({
					name: "webView",
					params: {
						url: link,
					}
				})
				// #endif
				// #ifdef H5
				this.$Router.push({
					name: "nostalgic"
				})
				// #endif
			} else {
				this.popupList = []
				this.popuePageNum = 1
				this.solePopupBox = true
				this.ctid = item.ctid
				this.getSaleHead()
			}

		},
		//下拉刷新时，页数自增 作品
		addRandomData() {
			if (this.isSeriesFooter) {
				if (this.isSeriesRequest == false) {
					if (this.isCreate == 1) {
						this.getGoods(this.current)
					} else {
						if (this.current == 0) {
							this.getGoods(this.current)
						} else {
							this.getGoods(this.current + 1)
						}

					}
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				this.loadStatus = 'nomore';
				console.log("已经到底了")
			}

		},
		change1(index) {
			if (this.$refs.uWaterfall != undefined) {
				this.$refs.uWaterfall.clear()
			}
			this.goodsList = []
			this.current = index;
			this.pageNum = 1
			this.isSeriesFooter = true
			this.index_sun = -1
			if (this.isCreate == 1) {
				this.getGoods(index)
			} else {
				if (index == 0) {
					this.getGoods(index)
				} else {
					this.getGoods(index + 1)
				}

			}
		},
		async getGoods(status) {
			console.log("作品当前分页:" + this.pageNum)
			this.isSeriesRequest = true
			let res = await this.$api.oldZone({ //请求出来的数据
				// seriesId: this.seriesId,
				pageNum: this.pageNum,
				pageSize: 15,
				sale: "",
				sortField: 1,
				sortOrder: "desc",
				// saleStatus: status,
				// isCreate: this.isCreate,
				// userId: this.uid,
				keyword: this.keyword,
				// isSelf: this.isSelf,
				// sortType: this.sortType,
				// flagDesc: this.flagDesc
			});
			// pageNum: 1
			// pageSize: 10
			// sale: ""
			// sortField: 1
			// sortOrder: "desc"

			if (res.status.code == 0) {
				this.isSeriesRequest = false
				if (res.result.list == '') {
					this.loadStatus = 'nomore';
					this.isSeriesFooter = false
				} else {
					if (res.result.list != '') {
						this.pageNum++
					} else {
						this.isSeriesFooter = false
					}
					res.result.list.forEach(item => {
						this.goodsList.push(item)
						return
						item.likeCheck = false
						item.isLoseText = false
						item.isOptions = false
						item.isNav = true
						// item.isOptions  选项卡是否展开
						// item.rightStatus 选项卡是否展开 1审核中  2未过审核  3新寄售  4已寄售   5未寄售
						// isNav  是否允许往里面点击
						if (item.showStatus == 0) { //审核中
							item.rightStatus = 1
							item.isNav = false
						} else if (item.showStatus == 1) { //新寄售
							item.rightStatus = 3
						} else if (item.showStatus == 2) { //已寄售
							item.rightStatus = 4
						} else if (item.showStatus == 3) { //未寄售
							item.rightStatus = 5
						} else if (item.showStatus == 4) { //未过审核
							item.rightStatus = 2
							item.isNav = false
						}
						item.expectPriceNum = 24
						if (item.expectPrice.length > 8) {
							item.expectPriceNum = 16
						} else if (item.expectPrice.length > 7) {
							item.expectPriceNum = 18
						} else if (item.expectPrice.length > 6) {
							item.expectPriceNum = 20
						} else if (item.expectPrice.length > 5) {
							item.expectPriceNum = 22
						}
						this.goodsList.push(item)
					})
					console.log(this.goodsList)
				}
			} else if (res.status.code == 1002) {
				this.isLoadding = true
				setTimeout(() => {
					this.$Router.pushTab({
						name: "mainLogin",
						params: {
							url: window.location.hash
						}
					})
				}, 1500);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 3000
				});
			}
		},
		back() {
			const pages = getCurrentPages();
			if (pages.length === 1) {
				this.$Router.pushTab({
					name: "mall"
				})
			} else {
				this.$Router.back();
			}
		},
		nav_details(item) {
			console.log(item.isNav)
			if (this.isCreate == 1) {
				if (item.isNav) {
					this.$Router.push({
						name: "mallDetails",
						params: {
							tid: item.tid
						}
					})
				}
			} else {
				if (this.platform == 'ios' || this.platform == 'android') {
					this.$native.mallDetails(item.tid);
				} else {
					this.$Router.push({
						name: "mallDetails",
						params: {
							tid: item.tid
						}
					})
				}
			}
		},
		nav_userCart(item) {
			console.log(item.contractAddress)
			if (item.banStatus == 0) {
				uni.showToast({
					title: "此账号已被官方禁用或删除,请联系客服!",
					icon: 'none',
					duration: 3000,
				});
			} else {
				if (uni.getStorageSync("contract_address") == item.contractAddress) {
					this.$Router.pushTab({
						name: "personal"
					})
				} else {
					this.$Router.push({
						name: "otherPeople",
						params: {
							contract_address: item.contractAddress
						}
					})
				}
			}
		},
		openOptions(item) {
			console.log(1111)
			this.tid = item.tid
			this.itemId = item.id
			item.isOptions = !item.isOptions
		},
		isShowsOpen(item) {
			console.log(item)
			this.isShows = true
			this.isShowsContent = item.showsDesc
		},

		openGrounding(item, index) {
			console.log(item.tid)
			this.destroyFlag = 0
			let authStatus = uni.getStorageSync('authStatus')
			if (this.isCreate == 1) {
				console.log("创作")
				if (!authStatus || authStatus === 30) {
					this.isRegistration = true
				} else {
					this.openModalOffAndOnShelf(item, index)
				}

			} else if (this.isSetTradePassword == 0) {
				this.isPassword = true
			} else {
				console.log("藏品")
				if (item.rickStatus == 0) {
					this.openModalResale()
				} else {
					this.openModalCollectionOffShelf(item)
				}

			}
		},
		//个人中心
		// 打开一键上停售
		openModalOffAndOnShelf(item, index) {
			item.isOptions = !item.isOptions
			this.currentIndex = index;
			this.visibility = item.rickStatus
			let visibility = item.rickStatus
			console.log(this.currentIndex)
			this.upPrice = item.price
			// let visibility = this.creation[this.currentIndex].visibility;
			if (visibility == '0') {
				// this.modalOffAndOnShelfTitle = '是否寄售作品';
				this.offAndOnShelfConfirm()
			}
			if (visibility == '1') {
				this.modalOffAndOnShelfTitle = '是否停售作品';
				this.isShowModalOffAndOnShelf = true;
			}

		},
		// 一键上停售取消按钮
		offAndOnShelfCancel() {
			this.isShowModalOffAndOnShelf = false;
			this.isShowModalBubble = false
		},
		// 一键上停售确定按钮
		offAndOnShelfConfirm() {
			if (this.visibility == '0') {
				if (this.isSetTradePassword == 0) {
					//设置交易密码
					this.mode = "set"
					this.isPassword = true
				} else {
					this.mode = "pay"
					this.isPasswordImport = true
					//输入交易密码
					console.log("输入密码支付")
				}
				// 查询是否有设置过支付密码
				// if (this.upPriceSub == "") {
				// 	this.offAndOnShelf(tid, '1', this.upPrice);
				// } else {
				// 	this.offAndOnShelf(tid, '1', this.upPriceSub);
				// }
			}
			if (this.visibility == '1') {
				this.offAndOnShelf(this.tid, '0', this.upPrice, "");
			}
		},
		async offAndOnShelf(tid, visibility, price, password) {
			let res;
			if (visibility == 0) {
				res = await this.$api.unSale({
					tid,
					price,
					tradePassword: password
				})
			} else {
				res = await this.$api.visibility({
					tid,
					price,
					tradePassword: password
				})
			}
			if (res.status.code == 0) {
				let title;
				if (visibility == '0') {
					this.workSsucceedText = "作品停售成功！"
					this.goodsList[this.index_sun].rightStatus = 5
					this.goodsList[this.index_sun].rickStatus = 0
					this.isWorkSsucceed = true
					this.index_sun = -1
					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
				}
				if (visibility == '1') {
					this.workSsucceedText = "作品寄售成功！"
					if (this.goodsList[this.index_sun].saleStatus == 3) {
						console.log("已经产生交易" + this.goodsList[this.index_sun].saleStatus)
						this.goodsList[this.index_sun].rightStatus = 4
					} else {
						console.log("新寄售啊" + this.goodsList[this.index_sun].saleStatus)
						this.goodsList[this.index_sun].rightStatus = 3
					}
					this.goodsList[this.index_sun].rickStatus = 1
					this.isWorkSsucceed = true
					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
					this.index_sun = -1
					this.isPasswordImport = false
					this.passwordImportPay = ""
				}
				this.offAndOnShelfCancel();
			} else {
				this.passwordImportPay = ""
				this.isPasswordImport = false
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 2000
				});
			}
		},
		// 打开一键寄售
		openModalResale() {
			this.isShowModalResale = true;
			this.resalePrice = ""
		},
		// 关闭快捷寄售  回调
		closeResale(data) {
			this.isShowModalResale = false
			if (!data) {
				this.resaleCancel()
			} else {
				this.mode = "pay"
				this.resalePrice = data
				this.isPasswordImport = true
			}
			// console.log('取消关闭');
		},
		async collectionPutaway(password) { //提交藏品寄售
			let res = await this.$api.visibility({
				tid: this.tid,
				price: this.resalePrice,
				tradePassword: password
			});
			if (res.status.code == 0) {
				this.isWorkSsucceed = true
				this.workSsucceedText = "作品寄售成功！"
				this.isShowModalResale = false
				setTimeout(() => {
					this.isWorkSsucceed = false
				}, 2000)
				this.isPasswordImport = false
				this.passwordImportPay = ""
				this.contentList = ""
				if (this.goodsList[this.index_sun].saleStatus == 2) {
					this.goodsList[this.index_sun].rightStatus = 3
				} else {
					this.goodsList[this.index_sun].rightStatus = 4
				}
				this.goodsList[this.index_sun].rickStatus = 1
				this.goodsList[this.index_sun].price = parseInt(this.resalePrice)
				this.index_sun = -1
				this.resaleCancel();
			} else {
				this.passwordImportPay = ""
				this.isPasswordImport = false
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 2000
				});
			}
		},
		// 打开藏品一键停售
		openModalCollectionOffShelf(index) {
			this.currentIndex = index;
			this.isShowModalCollectionOffShelf = true;
		},
		// 藏品一键停售取消按钮
		collectionOffShelfCancel() {
			this.isShowModalCollectionOffShelf = false;
			if (this.collection[this.currentIndex] != undefined) {
				this.collection[this.currentIndex].isShowBubble = false;
			}
			this.isShowModalBubble = false
		},
		// 打开查看证书
		openMaskCert(item) {
			item.isOptions = !item.isOptions
			this.$refs.modalPop.openPop('cert');
		},
		// 关闭查看证书
		closeMaskCert() {
			this.isShowMaskCert = false;
			this.isShowModalBubble = false
		},
		qrR(res) {
			this.src = res
		},
		// 跳转【一键转赠】
		nav_donation(item) {
			this.$Router.push({
				name: "donation",
				params: {
					tid: item.tid
				}
			});
		},
		// 打开一键销毁
		openModalDestroy(index, type) {
			if (type == 1) {
				this.destroyTitle = "销毁该藏品",
					this.destroyMsg = "销毁的藏品将在区块链发送至黑洞地址，永远不可复原。确认销毁该藏品？"
			}
			this.currentIndex = index;
			this.isShowModalDestroy = true;
		},
		// 一键销毁确定按钮
		destroyConfirm() {
			this.isShowModalDestroy = false
			this.destroyFlag = 1
			if (this.isSetTradePassword == 0) {
				//设置交易密码
				this.isPassword = true
			} else {
				this.mode = "pay"
				this.isPasswordImport = true
				//输入交易密码
				console.log("输入密码支付")

			}
		},
		async destroy(tid, tradePassword) {
			let res = await this.$api.goodsDestroy({
				tid,
				tradePassword
			})
			if (res.status.code == 0) {
				uni.showToast({
					title: '作品销毁成功',
					icon: 'none',
					duration: 2000
				});
				if (this.$refs.uWaterfall != undefined) {
					this.$refs.uWaterfall.clear()
				}
				this.index_sun = -1
				this.goodsList = []
				this.pageNum = 1
				if (this.isCreate == 1) {
					this.getGoods(this.current)
				} else {
					if (this.current == 0) {
						this.getGoods(this.current)
					} else {
						this.getGoods(this.current + 1)
					}
				}
				this.destroyCancel();
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 2000
				});
			}
		},
		destroyCancel() {
			this.isShowModalDestroy = false
		},
		// 打开一键寄售
		openModalResale(index) {
			this.currentIndex = index;
			this.isShowModalResale = true;
			this.resalePrice = ""
		},
		// 一键寄售取消按钮
		resaleCancel() {
			this.contentList = ""
			this.isShowModalResale = false;
			// if (this.collection[this.currentIndex] != undefined) {
			// 	this.collection[this.currentIndex].isShowBubble = false;
			// }
			this.isShowModalBubble = false
		},
		// // 一键寄售确定按钮
		// async reasaleConfirm() {
		// 	if (this.resalePrice < 37) {
		// 		uni.showToast({
		// 			title: "寄售价格不得低于37元哦",
		// 			icon: "none",
		// 			duration: 2000
		// 		});
		// 	} else {
		// 		if (this.isSetTradePassword == 0) {
		// 			//设置交易密码
		// 			this.isPassword = true
		// 		} else {
		// 			this.isPasswordImport = true
		// 			//输入交易密码
		// 			console.log("输入密码支付")
		// 		}
		// 	}
		// },
		// async collectionPutaway(password) { //提交藏品寄售
		// 	let res = await this.$api.resale({
		// 		tid: this.tid,
		// 		price: this.resalePrice,
		// 		tradePassword: password
		// 	});
		// 	if (res.status == 200) {
		// 		uni.showToast({
		// 			title: "寄售成功",
		// 			icon: "none",
		// 			duration: 2000
		// 		});
		// 		this.isPasswordImport = false
		// 		this.passwordImportPay = ""
		// 		this.contentList = ""
		// 		// this.collection[this.currentIndex].sale = 1
		// 		// this.collection[this.currentIndex].price = parseInt(this.resalePrice)

		// 		this.collectionPageNum = 1;
		// 		this.getMybuys();
		// 		this.$forceUpdate()
		// 		this.resaleCancel();
		// 	} else {
		// 		this.passwordImportPay = ""
		// 		this.isPasswordImport = false
		// 		uni.showToast({
		// 			title: res.msg,
		// 			icon: "none",
		// 			duration: 2000
		// 		});
		// 	}
		// },
		// 打开藏品一键停售
		openModalCollectionOffShelf(index) {
			this.isShowModalCollectionOffShelf = true;
		},
		// 藏品一键停售取消按钮
		collectionOffShelfCancel() {
			this.isShowModalCollectionOffShelf = false;
			this.isShowModalBubble = false
		},
		// 藏品一键停售确定按钮
		async collectionOffShelfConfirm() {
			let res = await this.$api.unSale({
				tid: this.tid,
				price: this.resalePrice,
				tradePassword: ""
			})
			if (res.status.code == 0) {
				console.log(1111)
				this.workSsucceedText = "作品停售成功！"
				this.isShowModalResale = false
				this.isShowModalCollectionOffShelf = false
				this.isWorkSsucceed = true
				this.goodsList[this.index_sun].rickStatus = 0
				this.goodsList[this.index_sun].rightStatus = 5
				this.index_sun = -1
				setTimeout(() => {
					this.isWorkSsucceed = false
				}, 2000)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 2000
				});
			}
		},
		changePrice() {
			console.log(this.resalePrice)
			this.antiShakeclick()
			if (this.resalePrice == "") {
				this.contentList = []
			}
		},
		antiShakeclick: antiShake._debounce(function () {
			this.calculatePrice(this.resalePrice)
		}, 200),
		async calculatePrice(price) {
			let res = await this.$api.calculatePrice({
				price: price,
				itemId: this.itemId
			})
			console.log(res)
			if (res.result != "") {
				this.contentList = res.result.content.split("\n")
			}
			console.log(this.contentList)
		},
		change(e) {
			console.log(e)
		},
		finish(e) {
			this.password = e
			this.openSetPassword()
		},
		openSetPassword() {
			let _this = this
			uni.showLoading({
				title: '设置中'
			})
			if (this.psw == "") {
				setTimeout(() => {
					this.psw = this.password
					this.title = "请再次输入余额支付密码，用于二次确认密码"
				}, 1000)
				setTimeout(() => {
					this.title = "请再次输入余额支付密码，用于二次确认密码"
				}, 2000)
			} else {
				this.error = ""
				if (this.psw == this.password) {
					setTimeout(() => {
						_this.isSetPayPassword = false
						this.setPassword(this.psw)
					}, 2000);
				} else {
					setTimeout(() => {
						this.error = "二次密码不一致，请重新设置"
						setTimeout(() => {
							this.error = ""
						}, 2000);
						this.psw = ""
						this.title = "请设置余额支付密码，用于支付验证。",
							this.password = ""
					}, 2000);
				}
			}
			setTimeout(() => {
				uni.hideLoading();
				this.password = ""
			}, 2000);
		},
		async setPassword(password) { //设置支付密码
			let res = await this.$api.tradePassCreate({
				captcha: this.MailboxVerifyCode,
				password: password,
			});
			if (res.status == 200) {
				console.log(res)
				uni.setStorageSync("isSetTradePassword", 1)
				this.isSetTradePassword = 1
				// uni.setStorageSync("isSetTradePassword","1")
				console.log("刚设置的密码为：" + password)
				if (this.isCreate == 1) {
					//创作寄售
					this.offAndOnShelf(this.tid, '1', this.upPrice, password);
				} else {
					//藏品寄售
					this.collectionPutaway(password)
				}
			} else {
				this.psw = ""
				this.title = "请设置余额支付密码，用于支付验证。",
					this.password = ""
				uni.showToast({
					title: res.msg,
					icon: "none",
					duration: 3000
				});
			}
		},
		createSuccess(psw) {
			uni.setStorageSync("isSetTradePassword", 1)
			this.isSetTradePassword = 1
			this.isPasswordImport = false
			// this.finishPay(psw)
		},
		finishPay(e) {
			this.passwordImportPay = e
			console.log(this.passwordImportPay)
			this.$refs.payPopup.password = '';
			if (this.destroyFlag == 0) {
				if (this.isCreate == 1) {
					//创作寄售
					this.offAndOnShelf(this.tid, '1', this.upPrice, e);
				} else {
					//藏品寄售
					this.isShowModalResale = true
					this.collectionPutaway(e)
				}
			} else if (this.destroyFlag == 2) {
				this.destroyAll(e);
			} else {
				this.isPasswordImport = false
				this.destroy(this.tid, e);
			}

		},
		SetPayPassword() {
			this.mode = "set"
			this.isPasswordImport = true
			this.isPassword = false
			// this.isMailboxVerify = true
			// this.MailboxVerifyCode = ""
			// this.count = 0
			// this.getCode()
			// this.sendMailboxVerify()
		},
		getCode() {
			const TIME_COUNT = 60;
			if (!this.timer) {
				this.count = TIME_COUNT;
				this.show = false;
				this.timer = setInterval(() => {
					if (this.count > 0 && this.count <= TIME_COUNT) {
						this.count--;
					} else {
						this.show = true;
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000)
			}
		},
		mailboxVerifySubmit() { //邮箱验证码提交
			if (this.MailboxVerifyCode == "") {
				uni.showToast({
					title: "请输入邮箱验证码哦",
					icon: "none",
					duration: 3000
				});
			} else {
				this.isMailboxVerify = false
				this.isSetPayPassword = true
			}
		},
		async sendMailboxVerify() {
			let res = await this.$api.sendEmailCaptcha({
				type: 1
			});
			if (res.status == 200) {
				const TIME_COUNT = 60;
				if (!this.timer) {
					this.count = TIME_COUNT;
					this.show = false;
					this.timer = setInterval(() => {
						if (this.count > 0 && this.count <= TIME_COUNT) {
							this.count--;
						} else {
							this.show = true;
							clearInterval(this.timer);
							this.timer = null;
						}
					}, 1000)
				}
			} else {
				uni.showToast({
					title: res.msg,
					icon: "none",
					duration: 3000
				});

			}
		},
		isBankVerifyColse(index) {
			if (index == 0) {
				this.isBankVerify = false
				this.count = 0
				this.getCode()
			} else {
				this.isMailboxVerify = false
				this.count = 0
				this.getCode()
			}
		},
		nav_forgetPayPassword() {
			this.$Router.push({
				name: 'forgetPayPassword'
			});
		},
		search() {
			if (this.$refs.uWaterfall != undefined) {
				this.$refs.uWaterfall.clear()
			}
			this.goodsList = []
			this.pageNum = 1
			this.msg_text = "未找到相关内容 换个关键词试试"
			if (this.isCreate == 1) {
				this.getGoods(this.current)
			} else {
				if (this.current == 0) {
					this.getGoods(this.current)
				} else {
					this.getGoods(this.current + 1)
				}

			}
		},
		addCheckLikeExplore(item) {

			this.listNum++
			console.log(this.listNum)
			this.tid = item.tid
			item.fondCount = Number(item.fondCount) + 1
			item.likeCheck = true
			item.isShowLikeAnimation = true
			this.fclick()
		},
		fclick: antiShake._debounce(function () {
			this.clickLike(this.tid)
		}, 1000),
		async clickLike(tid) {
			let res = await this.$api.java_communityLike({
				tid: tid,
				likeNum: this.listNum,
			});
			if (res.status.code == 0) {
				this.listNum = 0
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 3000
				})
			}
		},
		j_isAgree() {
			this.isAgree = !this.isAgree
		},
		nav_link(title, index) {
			if (index == 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com.cn/link/#/pages/index/saleAgreement"
					}
				})
			}
		},
		async setImage() {
			let res = await this.$api.java_userEditAvatar({
				tid: this.tid
			});
			if (res.status.code == 0) {
				this.$refs.uToast.show({
					title: "头像设置成功",
					type: 'success',
				})
				this.isShowSetImage = false
				if (this.$refs.uWaterfall != undefined) {
					this.$refs.uWaterfall.clear()
				}
				this.goodsList = []
				this.pageNum = 1
				if (this.isCreate == 1) {
					this.getGoods(this.current)
				} else {
					if (this.current == 0) {
						this.getGoods(this.current)
					} else {
						this.getGoods(this.current + 1)
					}

				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 3000
				})
			}
		},
		closeDropdown() {
			this.$refs.uDropdown.close();
		},
		check_isOptons(item, index) {
			this.tid = item.tid
			this.itemId = item.id
			this.index_sun = index
		},
		checkSearch() {
			this.isSearch = true
		},
		closeSearch() {
			this.msg_text = "哎哟，TA还没有作品哦！"
			this.isSearch = false
		},
		closeLoseText(item) {
			this.reject_sun = -1
		},
		openShare() {
			console.log(this.seriesId)
			this.$refs.share.open(7, this.seriesId);
			this.$slsTracker(
				"seriesdetails",
				"series_details",
				"",
				"click",
				"share2", {
				collection_id: this.behavior_content.collection_id,
				collection_name: this.behavior_content.collection_name,
				creator_id: this.behavior_content.creator_id,
				creator_name: this.behavior_content.creator_name,
				price: this.behavior_content.price,
				accept_id: '',
			},
				1
			);
		},
		expectedSucceed(e) {
			console.log(e)
			this.isShow = false
			if (e.length > 8) {
				this.goodsList[this.index_sun].expectPriceNum = 16
			} else if (e.length > 7) {
				this.goodsList[this.index_sun].expectPriceNum = 18
			} else if (e.length > 6) {
				this.goodsList[this.index_sun].expectPriceNum = 20
			} else if (e.length > 5) {
				this.goodsList[this.index_sun].expectPriceNum = 22
			} else {
				this.goodsList[this.index_sun].expectPriceNum = 24
			}
			if (e == 0) {
				this.goodsList[this.index_sun].hasExpectPrice = 0
				this.goodsList[this.index_sun].expectPrice = 0
			} else {
				this.goodsList[this.index_sun].expectPrice = e
				this.goodsList[this.index_sun].hasExpectPrice = 1
			}
		},
		//跳转至实名
		nav_realName() {
			this.isRegistration = false
			if (uni.getStorageSync('authStatus') == 30) {
				this.$Router.push({
					name: "authentication"
				})
			} else {
				this.$Router.push({
					name: "realName"
				})
			}
		},
		nav_choicenessDetails() {
			this.$Router.push({
				name: "seriesList",
				params: {
					ctid: this.info.ctid,
					title: this.info.name
				}
			});
		},
		nav_download(type) {
			this.$Router.push({
				name: 'appDownload'
			})
		},
		nav_notice() {
			this.$Router.push({
				name: "official",
				params: {
					title: this.info.name
				}
			});
		},
		nav_history() {
			this.$Router.push({
				name: "choicenessHistory",
				params: {
					ctid: this.info.ctid,
				}
			});
		},
		openMdel() {
			this.isMsg = false
			this.isPasswordImport = true
			this.passwordTitle = "一键销毁"
			this.passwordMsg = "请输入余额支付密码，用于一键销毁"
			this.destroyFlag = 2
		},
		async destroyAll(tradePassword) {
			this.isPasswordImport = false
			this.isLoadding = true
			let res = await this.$api.java_oldSeriesGoodsDestroy({
				tradePassword
			});
			if (res.status.code == 0) {

				this.isLoadding = false
				this.isWorkSsucceed = true
				this.workSsucceedText = "销毁成功，可能会出现几秒的延迟，请稍后查看"
				setTimeout(() => {
					this.isWorkSsucceed = false
					this.pageNum = 1
					this.goodsList = []
					this.getGoods(0)
				}, 2000)
			} else {
				this.isLoadding = false
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		customBack() {
			if (this.platform == 'ios' || this.platform == 'android') {
				this.$native.back();
			} else {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "personal",
					});
				} else {
					this.$Router.back();
				}
			}
		},
		async getUserInfo() {
			let res = await this.$api.userInfo({
				userId: ""
			});
			if (res.status.code == 0) {
				uni.setStorageSync("authStatus", res.result.authStatus)
				uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword)
				uni.setStorageSync("email", res.result.email)
				this.isSetTradePassword = uni.getStorageSync("isSetTradePassword")
				this.email = uni.getStorageSync("email")
				this.getGoods(0)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
	},
	components: {
		ModelShare,
		payPopup,
		resalePop,
		modalPop,
		setPresellPop,
		introducePop,
		commonSelect
	}

}
</script>

<style lang="scss" scoped>
::v-deep .solePopup {
	.u-drawer-bottom {
		background-color: transparent !important;
	}
}

.mall_more_ul {
	position: relative;

	.close {
		position: absolute;
		right: 0rpx;
		top: 0rpx;

		image {
			width: 80rpx;
		}
	}

	.li {
		display: flex;
		justify-content: center;
		align-items: center;
		color: #FFFFFF;
		border-bottom: 1px solid #53505D;
		font-size: 28rpx;
		padding: 38rpx 0rpx;

		.icon {
			margin-right: 20rpx;

			image {
				width: 44rpx;
			}
		}

		text {
			margin-right: 20rpx;
		}
	}

	.li:last-child {
		border-bottom: none;
	}
}

.popup-content {
	height: 100%;
	display: flex;
	align-items: flex-end;
	position: relative;

	.tx_view {
		width: 260rpx;
		height: 260rpx;
		position: absolute;
		top: 0;
		left: 40rpx;
		z-index: 99;

		image {
			width: 260rpx;
			border-radius: 30rpx;
			height: 260rpx;
		}
	}

	.content_view {
		width: 100%;
		height: 920rpx;
		background: linear-gradient(180deg, #25232D 0%, #111116 100%);
		border-radius: 40rpx 40rpx 0rpx 0rpx;
		position: relative;

		.right_icon {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240424/e03a47db708213812446d72957dc867e_702x676.png);
			width: 351rpx;
			height: 338rpx;
			background-size: 100% 100%;
			position: absolute;
			right: 0;
			top: 0;
			z-index: 0;
		}

		.right_font_view {
			padding: 20rpx 50rpx 60rpx 340rpx;
			z-index: 2;

			.title {
				color: #fff;
				font-size: 28rpx;
				width: 100%;
				height: 70rpx;
			}

			.num {
				color: #A6A6A6;
				font-size: 28rpx;

				.gs {
					margin-bottom: 10rpx;
				}

				.jj {
					text {
						color: #63EAEE;
					}
				}
			}

			.mall_right {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 22rpx;
				background-color: #35333E;
				border: 1px solid #fff;
				border-radius: 6rpx;
				padding: 8rpx;
				width: 100rpx;
				position: absolute;
				top: 66rpx;
				right: 50rpx;
				z-index: 2;

				text {
					margin-left: 6rpx;
					color: #fff;
					font-size: 22rpx;

				}

				image {
					width: 30rpx;
				}
			}
		}

		.sole_ul_view {
			padding: 0rpx 40rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;

			.sole_ul_li {
				width: 200rpx;
				height: 150rpx;
				margin-bottom: 35rpx;
				margin-right: 35rpx;
				border-radius: 24rpx;
				overflow: hidden;

				&.active {
					border: 1px solid #63EAEE;
				}

				.type {
					height: 60rpx;
					background-color: #35333E;
					display: flex;
					justify-content: center;
					align-items: center;
					color: #fff;
					font-size: 22rpx;

					&.active {
						color: #25232D;
						background-color: #63EAEE;
					}
				}

				.price_tid {
					height: 90rpx;
					background: #46454F;

					.price {
						width: 100%;
						font-size: 28rpx;
						line-height: 34rpx;
						font-weight: 600;
						color: #63EAEE;
						text-align: center;
						font-weight: 600;
						padding-top: 10rpx;
					}

					.tid_view {
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 20rpx;
						color: var(--default-color3);
						margin-top: 10rpx;
					}
				}
			}

			.sole_ul_li:nth-child(3n) {
				margin-right: 0rpx;
			}

		}

		.sale_view {
			width: 670rpx;
			height: 120rpx;
			border: 1px solid #63EAEE;
			color: #63EAEE;
			font-size: 34rpx;
			font-weight: 600;
			position: fixed;
			bottom: 20rpx;
			left: 40rpx;
			right: 40rpx;
			border-radius: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #111116;
		}
	}
}

.collection {
	padding: 0rpx 36rpx;
	// margin-top: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	padding-bottom: 80rpx;

	.li {
		width: 320rpx;
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #25232D;
		border-radius: 30rpx;

		.cover {
			position: relative;
			width: 280rpx;
			height: 280rpx;
			border-radius: 20rpx;

			image {
				width: 280rpx;
				height: 280rpx;
				border-radius: 20rpx;
			}

			.left_bottom_icon {
				position: absolute;
				bottom: 0;
				left: 0;
				min-width: 140rpx;
				height: 44rpx;
				border-radius: 0px 20rpx 0px 30rpx;
				text-align: center;
				font-size: 20rpx;
				color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
				background: rgba(20, 20, 20, 0.7);
				padding: 0rpx 30rpx;
				border-radius: 0px 20px 0px 30px;
			}
		}

		.title {
			width: 100%;
			font-size: 24rpx;
			color: #fff;
			margin-top: 20rpx;
		}

		.total_text {
			color: #fff;
			font-size: 24rpx;
			margin-top: 10rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.jishou {
				width: 118rpx;
				height: 36rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240424/6ea361dba85c051ba1bbbbe9b092ef5f_236x72.png);
				background-size: 100% 100%;
				color: #141816;
				font-size: 18rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 600;
			}

			text {
				color: #63EAEE;
			}
		}
	}
}

::v-deep .u-drawer-bottom {
	background-color: #1E1E1E !important;
}

::v-deep .u-icon__label {
	margin-top: 40rpx !important;
}

::v-deep .u-tab-item {
	margin-right: 125rpx;
}

::v-deep .u-tab-item:last-child {
	margin-right: 0rpx;
}

.modal-content {
	padding: 40rpx 40rpx 0rpx 40rpx;
	font-size: 28rpx;
	line-height: 40rpx;
	text-align: center;
}

.modal-btn {
	padding: 60rpx 40rpx;

	.mb-cancel,
	.mb-cancel1,
	.mb-confirm {
		width: 240rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
	}

	.mb-cancel {
		color: #666;
		background-color: var(--main-bg-color);
		border: 2rpx solid #616161;
		color: #fff;
		border-radius: 4rpx;
	}

	.mb-cancel1 {
		width: 100%;
		color: #fff;
		background-color: #eee;
		border-radius: 4rpx;
	}

	.mb-confirm {
		color: #25232E;
		background: var(--primary-button-color)
	}
}

.modal-resale-input {
	margin: 0 auto;
	border: 2rpx solid var(--active-color1);

	&::before {
		content: '￥';
		display: block;
		position: absolute;
		top: 20rpx;
		left: 5rpx;
	}
}

.container {
	.register {
		color: #fff;
		font-size: 26rpx;
		margin-right: 40rpx;
	}

	.body_div {
		position: relative;

		.bg_cover {
			width: 100%;
			height: 632rpx;
			overflow: hidden;
			position: relative;

			.header {
				width: 100%;
				position: fixed;
				top: 0rpx;
				left: 0;
				align-items: flex-start;
				padding: 40rpx 30rpx 10rpx 30rpx;
				z-index: 100;
				padding-top: 40rpx;
				background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%);

				&.bg {
					background-color: var(--new-consignment-color);
					transition: background .5s;
				}

				.back {
					image {
						width: 70rpx;
						height: 70rpx;
					}
				}

				.share {
					image {
						width: 70rpx;
						height: 70rpx;
					}
				}

				.title {
					color: #fff;
					font-size: 30rpx;
					height: 70rpx;
					line-height: 70rpx;
					transition: all .5s;
				}

				.center_icon {
					image {
						width: 204rpx;
						height: 48rpx;
					}
				}
			}

			.cover {
				image {
					width: 100%;
					height: 680rpx;
				}

				.masking {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 680rpx;
					// backdrop-filter: blur(14rpx);
				}
			}
		}

		.mall_catr {
			width: 100%;
			position: absolute;
			top: 632srpx;
			left: 0;

			.new_data_view {
				.title {
					text-align: center;
					font-size: 38rpx;
					color: #fff;
					margin: 40rpx 0rpx;
					font-family: "黑体";
				}

				.data_view {
					padding: 0rpx 48rpx;
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;
					color: #fff;

					>view {
						width: 285rpx;
						margin-bottom: 28rpx;
						height: 57rpx;
						padding-left: 147rpx;
						text-align: center;
						background-size: 100% 100%;
						line-height: 57rpx;
						font-size: 28rpx;
					}

					.good_num {
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230914/b333f10d6892de3dbc1d071f828c69e9_857x173.png);
					}

					.circulate_num {
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230914/3eb8b0e408130372cc65817ffe2ae98c_857x173.png);
					}

					.hold_num {
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230914/915802d7394821ebd66945bdd123f312_857x173.png);
					}

					.average_num {
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230914/76a25cefc07c4424541dab6d25bf6a26_857x173.png);
					}
				}
			}

			.func_view {
				padding: 0rpx 48rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;

				.li {
					width: 100rpx;

					image {
						width: 100rpx;
					}
				}
			}

			.collect_ul {
				justify-content: flex-end;
				padding: 0rpx 20rpx;

				>.li {
					height: 56rpx;
					background: var(--ordinarySeries-bg-color);
					border-radius: 4rpx;
					margin-left: 14rpx;
					color: var(--message-box-point-color);
					display: flex;
					align-items: center;
					justify-content: center;
					backdrop-filter: blur(2rpx);
					font-size: 24rpx;
					padding: 0rpx 20rpx;

					text {
						margin-left: 10rpx;
					}
				}
			}

			.cart_view {
				padding: 20rpx;

				.cart {
					background: var(--ordinarySeries-bg-color);
					background-size: 100% 100%;
					width: 100%;
					padding: 35rpx;
					border-radius: 8rpx;
					position: relative;

					.title {
						color: var(--main-text-color);
						font-size: 40rpx;
						line-height: 64rpx;
						display: flex;
						align-items: center;
						font-weight: 600;
						width: 100%;
					}

					.intro {
						color: var(--secondary-front-color);
						font-size: 28rpx;
						display: flex;
						align-items: center;
						width: 100%;
						margin-top: 30rpx;
						line-height: 34rpx;
					}

					.shuju {
						width: 200rpx;
						position: absolute;
						top: 40rpx;
						right: 40rpx;

						.tag {
							display: flex;
							justify-content: flex-start;
							align-items: center;
							border-radius: 4rpx;

							.red {
								background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230208/90915bf3baae9984d323c468a93f8ac1_282x106.png');
								color: #111111;
								width: 96rpx;
								height: 36rpx;
								background-size: 100% 100%;
								line-height: 36rpx;
								font-size: 22rpx;
								border-radius: 0rpx;
								text-align: center;
							}

							.text {
								height: 36rpx;
								min-width: 96rpx;
								color: #FFFFFF;
								font-size: 22rpx;
								background-color: #383838;
								text-align: center;
								line-height: 36rpx;
								color: #04F0FF;
							}
						}
					}

					.tag_div {
						overflow-x: scroll;
						width: 100%;
						margin-top: 30rpx;

						.slide {
							width: 600rpx;
							display: inline-block;

							.li {
								float: left;
								height: 46rpx;
								color: var(--secondary-front-color);
								border: 1px solid var(--secondary-front-color);
								line-height: 46rpx;
								text-align: center;
								border-radius: 8rpx;
								font-size: 24rpx;
								margin-right: 30rpx;
								padding: 0rpx 20rpx;
							}
						}
					}

					.price_view {
						margin-bottom: 40rpx;
						font-size: 52rpx;
						align-items: flex-end;

						.price {
							color: var(--message-box-point-color);

							text {
								font-size: 32rpx;

							}
						}

						.likeNum {
							color: var(--active-color1);
							font-size: 38rpx;

							text {
								color: var(--secondary-front-color);
								font-size: 24rpx;
								margin-right: 20rpx;
							}
						}
					}

				}
			}

			.userInfo {
				margin-top: 32rpx;
				padding: 0rpx 20rpx 48rpx 20rpx;

				.tx {
					width: 90rpx;
					height: 90rpx;
					margin-right: 24rpx;

					>view {
						width: 100%;

						image {
							width: 90rpx;
							height: 90rpx;
							border-radius: 4rpx;
							background-color: #eee;
						}
					}
				}

				.user_cart {
					width: 85%;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.username {
						font-size: 28rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.cash_deposit {
							margin-left: 10rpx;

							image {
								width: 40rpx;
							}
						}
					}

					.icon_right {
						width: 32rpx;

						image {
							width: 32rpx;
						}
					}
				}

			}

			.screen_search {
				margin-top: 70rpx;
				padding: 30rpx;
				position: relative;

				.screen {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					font-size: 28rpx;
					color: #FFF;
					font-size: 24rpx;
					font-weight: 600;

					.active {
						font-weight: 700;
						color: var(--active-color1);
					}

					.search_icon {
						width: 48rpx;
						height: 48rpx;
						margin-left: 110rpx;

						image {
							width: 48rpx;
							height: 48rpx;
						}
					}

					.price,
					.time {
						width: 200rpx;
						margin-left: 40rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						height: 52rpx;

						image {
							width: 36rpx;
						}
					}

					.all {
						width: 108rpx;
						height: 52rpx;
						line-height: 52rpx;

						&.active {
							border-radius: 28rpx;
							border: 2rpx solid var(--active-color1);
							font-weight: 700;
							color: var(--active-color1);
						}
					}
				}

				.search {
					margin-top: 24rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 28rpx;

					.colse {
						margin-left: 30rpx;
						color: #FFF;
					}
				}
			}

			.screen_mode {
				width: 100%;
				padding: 30rpx;
				position: absolute;
				left: 0;
				z-index: 99;
				background-color: var(--new-consignment-color);
				font-size: 28rpx;

				.screen_mode_li {
					width: 60rpx;
					line-height: 60rpx;
					color: #616161;
					width: 100% !important;
				}
			}

			.works_container {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				padding-bottom: 200rpx;

				.item {
					width: 336rpx;
					position: relative;
					margin-bottom: 20rpx;
					border-radius: 8rpx;
					color: var(--message-box-point-color);
					margin-bottom: 40rpx;
					position: relative;
					background-color: #202022;
					border-bottom-left-radius: 47rpx;
					border-bottom-right-radius: 47rpx;

					.view_set {
						width: 100%;
						height: 100%;
						position: absolute;
						left: 0;
						top: 0;
						z-index: 14;
						background-color: rgba(0, 0, 0, 0.82);
						border-radius: 8rpx;

						.colse {
							position: absolute;
							right: 10rpx;
							top: 10rpx;

							image {
								width: 36rpx;
							}
						}

						.jujue {
							padding: 30rpx;

							.title {
								color: var(--secondary-front-color);
								font-size: 28rpx;
								margin-bottom: 20rpx;
							}

							.showsDesc {
								font-size: 24rpx;
								color: var(--message-box-point-color);
							}
						}

						.view_set_ul {
							width: 100%;
							padding: 16rpx;
							color: #fff;

							.li {
								height: 88rpx;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								font-size: 24rpx;
								border-bottom: 1rpx solid #888888;

								.icon {
									margin-right: 10rpx;

									image {
										width: 52rpx;
									}
								}

								text {
									margin-right: 10rpx;
								}
							}

							.li:last-child {
								border-bottom: none;
							}
						}
					}

					.cover_view {
						position: relative;

						.cover {
							image {
								width: 336rpx;
								height: 336rpx;
								border-top-left-radius: 8rpx;
								border-top-right-radius: 8rpx;
								background-color: #eee;
							}
						}

						.right_status {
							position: absolute;
							right: 0rpx;
							top: 20rpx;

							image {
								width: 100%;
								height: 36rpx;
							}
						}

						.masking {
							width: 100%;
							height: 100rpx;
							background: linear-gradient(180deg, rgba(238, 238, 238, 0) 0%, rgba(0, 0, 0, 0.2) 100%);
							position: absolute;
							left: 0;
							bottom: 0;
							z-index: 7;
						}

						.operate {
							position: absolute;
							right: 20rpx;
							bottom: 10rpx;
							z-index: 11;

							image {
								width: 50rpx;
								height: 50rpx;
							}
						}
					}

					.introduce {
						margin-top: 24rpx;
						padding: 17rpx 28rpx;
						color: #fff;

						.title {
							font-size: 28rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							margin-left: 5px;
							max-width: 125px;
						}

						.user_xx {
							display: flex;
							justify-content: flex-start;
							align-items: center;
							margin: 24rpx 0rpx;

							text {
								color: var(--secondary-front-color);
								font-size: 24rpx;
							}

							.portrait {
								image {
									width: 42rpx;
									height: 42rpx;
									border-radius: 50%;
									margin-right: 20rpx;
									background-color: #eee;
								}
							}
						}

						.price_like {
							display: flex;
							justify-content: flex-end;
							align-items: center;
							margin-top: 30rpx;

							.like {
								display: flex;
								justify-content: flex-start;
								align-items: center;
								color: var(--secondary-front-color);
								font-size: 24rpx;

								.like_icon {
									image {
										width: 36rpx;
										height: 36rpx;
										margin-right: 10rpx;
									}
								}
							}

							.price {
								font-size: 40rpx;
								font-weight: 600;
							}
						}
					}
				}
			}
		}
	}

	.tabs {
		width: 100%;
		padding: 0 30rpx;
		margin-top: 30rpx;
	}

	.demo-warter {
		border-radius: 16rpx !important;
		background-color: var(--message-box-point-color);
		position: relative;
		margin: 10rpx 7rpx;

		.image_view {
			border-radius: 28rpx;
			position: relative;

			.bubble {
				position: absolute;
				top: 70rpx;
				right: 0rpx;
				z-index: 99;

				.bubble-triangle {
					// width: 0;
					// height: 0;
					// border-top: 20rpx solid transparent;
					// border-right: 15rpx solid transparent;
					// border-bottom: 20rpx solid var(--message-box-point-color);
					// border-left: 15rpx solid transparent;
					position: absolute;
					top: -29rpx;
					right: 15rpx;
				}

				.bubble-square {
					width: 244rpx;
					padding: 0 10rpx;
					border-radius: 10rpx;
					box-shadow: 0 0 10rpx #ccc;
					background-color: var(--message-box-point-color);

					.bubble-item {
						height: 80rpx;
						line-height: 80rpx;
						text-align: center;
						border-bottom: 1rpx solid #ddd;
						color: #000000;

						image {
							width: 29rpx;
							height: 29rpx;
							display: inline-block;
							vertical-align: middle;
							margin-right: 17rpx;
						}

						&:last-child {
							border-bottom: none;
						}
					}
				}
			}

			.box_series {
				position: absolute;
				right: 0rpx;
				top: 0rpx;
				width: 142rpx;
				height: 42rpx;

				image {
					width: 100%;
					border-bottom-left-radius: 25rpx;
				}
			}

			.bg {
				width: 100%;
				position: absolute;
				bottom: 0rpx;
				left: 0rpx;

				image {
					width: 100%;
				}
			}

			.userInfo {
				width: 100%;
				position: absolute;
				bottom: 0rpx;
				left: 0rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				color: var(--message-box-point-color);
				padding: 10rpx;
				font-size: 26rpx;

				.left_user {
					width: 80%;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					text {
						width: 70%;
					}

					image {
						width: 50rpx;
						height: 50rpx;
						border-radius: 50%;
						margin-right: 14rpx;

					}
				}

				.right_series {
					width: 20%;
					text-align: center;
					font-size: 32rpx;
					font-weight: 600;
					z-index: 99;
				}
			}

			.options_view {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0rpx;
				left: 0rpx;
				background-color: rgba(0, 0, 0, 0.72);
				z-index: 10;

				.options_ul {
					padding: 20rpx 60rpx;

					.li {
						width: 100%;
						border-bottom: 1rpx solid #7c7c7c;
						height: 90rpx;
						line-height: 90rpx;

						image {
							width: 50rpx;
							margin-right: 10rpx;
						}

						display: flex;
						justify-content: flex-start;
						align-items: center;
						color:var(--message-box-point-color);
						font-size:26rpx;
					}

					.li:last-child {
						border-bottom: none;
					}
				}
			}

			.reject_view {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0rpx;
				left: 0rpx;
				background-color: rgba(0, 0, 0, 0.72);
				padding: 30rpx;

				.msg {
					color: var(--message-box-point-color);
					font-size: 24rpx;
					line-height: 30rpx;
					height: 300rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}

				.colse {
					display: flex;
					justify-content: center;
					align-items: center;
					position: absolute;
					bottom: 50rpx;
					left: 0;
					display: flex;
					justify-content: center;
					width: 100%;
				}
			}

			.label_status {
				z-index: 8;

				>view {
					padding: 10rpx 30rpx;
					position: absolute;
					right: 0rpx;
					top: 0rpx;
					background-color: #2F3449;
					color: var(--message-box-point-color);
					border-bottom-left-radius: 25rpx;
					border-top-right-radius: 20rpx;
					font-size: 24rpx;

					&.active {
						background-color: $uni-wgm-color
					}

					&.lvse {
						background-color: #2CBAB2;
					}

					&.huise {
						background-color: var(--secondary-front-color);
					}
				}
			}

			.coverImage {
				width: 100%;
				height: 430rpx;
				border-radius: 24rpx;

				// border-top-left-radius: 16rpx;
				// border-top-right-radius: 16rpx;

				// min-height:375rpx;
			}
		}

	}

	.body {
		padding: 20rpx;
	}

	.u-close {
		position: absolute;
		top: 32rpx;
		right: 32rpx;
	}

	.demo-image {
		width: 100%;
		border-radius: 4px;
	}

	.demo-title {
		font-size: 26rpx;
		color: #000000;
		width: 100%;
		overflow: hidden;
	}

	.dome_price {
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.left {
			color: #2CBAB2;
			font-size: 32rpx;
			font-weight: 600;

			text {
				font-size: 32rpx;
			}
		}

		.right {
			display: flex;
			justify-content: space-between;
			align-items: center;

			image {
				width: 30rpx;
				margin-right: 10rpx;
			}

			text {
				font-size: 26rpx;
			}
		}
	}
}

.sousuo {
	padding: 20rpx;
}

.bubble-modal {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9;
	// background-color: rgba(0, 0, 0, 0.5);
}



.ver-center {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.ver-space-around {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	align-items: center;
}

.space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.modal-cert {
	width: 600rpx;
	height: auto;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1000;
	background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20220611/1bcfec8f15d981de255e7366ef0a7a1e_582x1010.png);
	background-size: 100%;
	background-repeat: no-repeat;

	.modal_head {
		border-radius: 6rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: var(--main-text-color);
		font-size: 24rpx;
		padding: 18rpx 48rpx 10rpx;

		image {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			margin-right: 16rpx;
		}

		.time {
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: var(--secondary-front-color);
			transform: scale(0.92);
			width: 380rpx;
		}
	}

	.poster_content {
		margin: 22rpx 48rpx 16rpx;
		padding-bottom: 8rpx;
		border-bottom: 2rpx solid #282828;

		.content_title {
			font-size: 28rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: var(--main-text-color);
			margin: 24rpx 0;
			letter-spacing: 4rpx;
			display: flex;
			align-items: center;

			.type_tip {
				background: var(--active-color1);
				border-radius: 2rpx;
				margin-right: 16rpx;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--new-consignment-color);
				height: 32rpx;
				width: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transform: scale(0.9);
			}
		}

		.content_line {
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: var(--secondary-front-color);
			display: flex;
			// align-items: center;
			margin-bottom: 16rpx;

			.line_label {
				letter-spacing: 8rpx;
			}

			.tokenID {
				width: 440rpx;
				line-height: 30rpx;
				word-wrap: break-word;
			}
		}

		.line_end {
			font-size: 24rpx;
			transform: scale(0.94);
			margin-left: -14rpx;
		}
	}

	.mc-share {
		height: auto;
		min-height: 150rpx;
		display: flex;
		align-items: flex-end;
		padding: 18rpx 48rpx 32rpx;

		.qrimg-i {
			width: 140rpx;
			height: 140rpx;
			background-color: var(--main-text-color);
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.mcs-right {
			height: 136rpx;
			margin-left: 48rpx;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: var(--main-text-color);
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			view:first-child {
				font-weight: 600;
				color: var(--main-text-color);
			}

			.tip_code {
				font-size: 24rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: var(--active-color1);
			}
		}
	}

	.mcb-foot {
		padding: 32rpx 0 40rpx;
		text-align: center;
		color: #333;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: var(--secondary-front-color);
		background: var(--main-bg-color);
		margin-top: -10rpx;
		border-radius: 10rpx;
	}
}

.modal_Body {
	padding: 24rpx;

	.bank_ul {
		display: inline-block;
		background-color: var(--message-box-point-color);
		border-radius: 12rpx;
		overflow: hidden;
		width: 100%;

		.li {
			width: 100%;
			padding: 0rpx 24rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.left_icon {
				margin-right: 10rpx;
				width: 42rpx;

				image {
					width: 42rpx;
					border-radius: 50%;
				}
			}

			.right_text {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 0rpx;
				border-bottom: 1rpx solid #F2F2F7;
				font-size: 30rpx;

				.img {
					image {
						width: 40rpx;
						border-radius: 50%;
					}
				}
			}
		}
	}
}

.BankVerifyBody {
	padding: 42rpx;

	.head_title {
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		color: var(--message-box-point-color);

		.right {
			position: absolute;
			right: 40rpx;
			top: 66rpx;

			image {
				width: 30rpx;
			}
		}
	}

	.item {
		margin-bottom: 46rpx;

		.labal {
			color: var(--secondary-front-color);
			font-size: 24rpx;
		}

		.input {
			align-items: center;
			height: 88rpx;

			.left {
				width: 50%;
				color: var(--secondary-front-color);
				padding: 6rpx;
				border-radius: 4rpx;

				.input {
					font-size: 24rpx;
				}
			}

			.right {
				image {
					width: 40rpx;
				}

				text {
					color: var(--active-color1);
					font-size: 28rpx;
				}
			}
		}
	}

	.footer {
		button {
			width: 100%;
			height: 88rpx;
			color: var(--message-box-point-color);
			line-height: 88rpx;
			font-size: 32rpx;
			border-radius: 0rpx;
			background: var(--primary-button-color);
			border: none;
			color: var(--new-consignment-color);
		}
	}
}

.head_title_y {
	text-align: left;
	font-size: 40rpx;
	font-weight: 600;
	height: 80rpx;
	line-height: 80rpx;

	.right {
		position: absolute;
		right: 40rpx;
		top: 46rpx;

		image {
			width: 30rpx;
		}
	}
}

.footer_y {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 30rpx;

	button {
		margin: 0rpx;
		padding: 0rpx;
		width: 238rpx;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		background-color: var(--secondary-front-color);
		color: var(--message-box-point-color);

		&.active {
			background-color: #333333;
			color: var(--message-box-point-color);
		}
	}
}

.msg_y {
	font-size: 28rpx;
	color: var(--secondary-front-color);
	line-height: 40rpx;
}

.pwd-box {
	margin-top: 50rpx;

	.set-pwd {
		position: relative;

		.set-pwd-tip {
			font-size: 32rpx;
			color: #333;
			padding-bottom: 64rpx;
			text-align: center;
		}

		.set-error {
			color: #BB3835;
			font-size: 24rpx;
			text-align: center;
			position: absolute;
			// left: 290rpx;
			width: 100%;
			bottom: 20rpx;
		}
	}
}

.modal_head_password {
	font-size: 32rpx;
	font-weight: 600;
	text-align: center;
	height: 80rpx;
	line-height: 80rpx;
	margin-top: 42rpx;
	position: relative;
	color: var(--message-box-point-color);

	.left {
		position: absolute;
		left: 40rpx;
		top: 20rpx;

		image {
			width: 30rpx;
		}
	}

	.right {
		position: absolute;
		right: 40rpx;
		top: 0rpx;
		font-weight: 500;
		font-size: 24rpx;
	}
}

.modal_Body_password {
	padding: 24rpx;

}

.xieyi {
	width: 100%;
	margin: 40rpx 0rpx;

	.p {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
			border-radius: 50%;
		}

		.xieyi_msg {
			font-size: 24rpx;
			color: var(--secondary-front-color);

			text {
				text-decoration: underline;
				color: var(--active-color1);
			}
		}
	}
}

.work_ssucceed {
	padding: 60rpx;

	.icon {
		display: flex;
		justify-content: center;

		image {
			width: 60rpx;
		}
	}

	.msg {
		margin-top: 30rpx;
		text-align: center;
		line-height: 38rpx;
	}
}

.leftLabel {
	width: 92rpx;
	height: 32rpx;
	position: absolute;
	top: 10rpx;
	left: 0rpx;
	z-index: 10;

	.img {
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100% !important;
			border-radius: 0;
		}
	}
}

.null {
	width: 100%;

	::v-deep .u-icon__img {
		width: 170rpx !important;
	}

	::v-deep .u-icon__label {
		margin-top: 46rpx !important;
		font-size: 28rpx;
		color: #616161 !important;
	}
}

.exhibition {
	width: 100%;
	height: 632rpx;
}

.modal-btn_download {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 84rpx;
	border-top: 1rpx solid #282828;

	.but {
		width: 100%;
		text-align: center;
		line-height: 110rpx;
		height: 110rpx;
	}

	.mb-cancel {
		color: #f9f9f9;
	}

	.mb-confirm {
		color: #1FEDF0;
		border-left: 1rpx solid #282828;
	}
}
</style>
