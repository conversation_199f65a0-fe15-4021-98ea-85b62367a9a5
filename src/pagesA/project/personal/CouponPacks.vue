<template>
    <view>
        <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
            :background="{ backgroundColor: 'var(--main-bg-color)' }" title="我的券包" title-color="var(--main-front-color)"
            :custom-back="back">
        </u-navbar>
        <view style="height: 70rpx;"></view>
        <view class="using" v-if="usingList.length > 0">
            <text class="title">可使用（{{ usingListTotal }}）</text>
            <view class="box">
                <view class="box-item" v-for="(item, index) in filterUsList" :key="index">
                    <view class="left">
                        <view>
                            <text class="symbol">¥</text>
                            <text class="num">{{ item.amount }}</text>
                        </view>

                        <view class="ball"></view>
                    </view>
                    <view class="right">
                        <view class="ball"></view>

                        <view class="item-left">
                            <view class="top">
                                <text>万能金</text>
                                <view @click="showDetails(item)">详情</view>
                            </view>
                            <view class="obj">
                                <!-- <view class="obj" v-for="(item, index) in item.supportContract" :key="index"> -->
                                {{ formatSupportContract(item.supportContract) }}
                                <!-- </view> -->
                            </view>

                            <view class="time" v-if="item.expireTime">有效:{{ item.expireTime.split('.')[0] || '--' }}前
                            </view>
                        </view>
                        <view class="item-right" @click="gobit">
                            GO
                        </view>

                    </view>
                </view>
            </view>

        </view>

        <view class="down" @tap="checkShowDow(0)" v-show="filterUsList.length > 2">
            <image v-show="isDownShow"
                src="https://cdn-lingjing.nftcn.com.cn/image/20240307/1af04d46580dfcd5fa0fd33787663f96_88x72.png"
                mode="widthFix"></image>
            <image v-show="!isDownShow"
                src="https://cdn-lingjing.nftcn.com.cn/image/20240301/97d0d1f2163359a196526ed555ababc9_44x36.png"
                mode="widthFix"></image>
        </view>
        <view style="height: 72rpx;"></view>
        <view class="using" v-if="unusingList.length > 0">
            <text class="title">已失效（{{ unusingListTotal }}）</text>
            <view class="box ">
                <view class="box-item gray" v-for="(item, index) in unusingList" :key="index">
                    <view class="left">
                        <view class="">
                            <text class="symbol fff">¥</text>
                            <text class="num fff">{{ item.amount }}</text>
                        </view>

                        <view class="ball"></view>
                    </view>
                    <view class="right">
                        <view class="ball"></view>

                        <view class="item-left">
                            <view class="top">
                                <text>万能金</text>
                                <!-- <view @click="showDetails(item)">详情</view> -->
                            </view>
                            <view class="obj">
                                <!-- <view class="obj" v-for="(item, index) in item.supportContract" :key="index"> -->
                                {{ formatSupportContract(item.supportContract) }}
                                <!-- </view> -->
                            </view>

                            <view class="time" v-if="item.expireTime">有效:{{ item.expireTime.split('.')[0] || '--' }}前
                            </view>
                        </view>
                        <view class="item-right expire">
                            过期
                        </view>

                    </view>
                </view>
            </view>
        </view>

        <view class="nodata" v-if="!usingList.length && !unusingList.length">
            <image mode="widthFix"
                src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
            <text>暂时无数据</text>
        </view>

        <u-modal v-model="popup" width="710" border-radius="30" :show-title="false" :show-confirm-button="false">
            <view class="fast-modal-content">
                <view class="right_close" @click="popup = false">
                    <image
                        src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240920/01a0a376de633fba5c470a604f187804_80x80.png"
                        mode="widthFix"></image>
                </view>

                <view class="body">
                    <view class="coin">
                        支持对标物
                    </view>
                    <view style="display: flex;align-items: center;margin: 37rpx 0 68rpx 0;">
                        <view class="coin-item" v-for="(item, index) in nowDetails.supportContract" v-if="nowDetails"
                            :key="item.id">
                            {{ item == 0 ? '无限制' : item }}{{ index !== nowDetails.supportContract.length - 1 && item
                                !== 0 ? '/' : '' }}
                        </view>
                    </view>

                    <view class="hold">最大持仓时长</view>
                    <view style="display: flex;align-items: center;margin: 37rpx 0 68rpx 0;" class="coin-item">
                        {{ nowDetails.maxPositionTime + 'h' }}
                    </view>
                    <view class="level">支持杠杆倍数</view>
                    <view style="display: flex;align-items: center;margin: 37rpx 0 68rpx 0;">
                        <view class="coin-item" v-for="(item, index) in nowDetails.supportLever" v-if="nowDetails"
                            :key="item.id">
                            {{ item === 0 ? '无限制' : 'X' + item }}{{ index !== nowDetails.supportLever.length - 1 ? '、' :
                                '' }}</view>
                    </view>
                </view>

            </view>
        </u-modal>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentlyDisplayed: 3, // 当前显示的数据条数
            isDownShow: false,
            usingList: [],
            usingListTotal: 0,
            unusingList: [],
            unusingListTotal: 0,
            popup: false,
            nowDetails: {}
        }
    },
    onLoad() {
        this.getList()
    },
    computed: {
        filterUsList() { //涨幅
            if (this.usingList) {
                return this.usingList.slice(0, this.currentlyDisplayed);
            }
        },
    },
    methods: {
        // 格式化 supportContract 数组
        formatSupportContract(arr) {
            console.log(arr);

            return arr
                .map((item, index) => {
                    return item == 0 ? '无限制' : item;  // 处理 0 为 '无限制'
                })
                .join('/') // 将数组用 '/' 拼接
                .replace(/\/$/, ''); // 去掉结尾的 '/'
        },
        showDetails(item) {
            this.nowDetails = item
            this.popup = true
        },
        checkShowDow(index) {
            this.isDownShow = !this.isDownShow
            if (this.isDownShow) {
                this.getList()
                this.currentlyDisplayed = this.usingList.length;
            } else {
                this.currentlyDisplayed = 3
            }
        },
        async getList() {
            let res = await this.$api.couponList({
                pageNum: 1,
                pageSize: 100,
                status: 1
            })
            if (res.status.code == 0) {
                this.usingList = res.result.list
                this.usingListTotal = res.result.totalCount
            }
            let res2 = await this.$api.couponList({
                pageNum: 1,
                pageSize: 100,
                status: 3
            })
            if (res2.status.code == 0) {
                this.unusingList = res2.result.list
                this.unusingListTotal = res2.result.totalCount

            }
        },
        back() {
            this.$Router.back();
        },
        gobit() {
            this.$Router.pushTab({
                name: "contract-BITindex"
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.nodata {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    image {
        width: 240rpx;
        height: 240rpx;
    }

    text {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
    }
}

page {
    padding-bottom: 150rpx;
}

.fast-modal-content {
    background: #35333E;
    border-radius: 35rpx;
    margin: 0 35rpx;

    .body {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 100rpx 0 32rpx 0;

        .coin,
        .hold,
        .level {
            font-family: HarmonyOS Sans SC;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 34rpx;
            color: #FFFFFF;
            background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241223/97777f531f81045c939ab10f696dcdb2_580x16.png");
            background-size: 100% 100%;
        }

        .coin {
            height: 8rpx;
            width: 270rpx;
        }

        .hold,
        .level {
            height: 8rpx;
            width: 290rpx;
        }

        .coin-item {
            font-family: HarmonyOS Sans SC;
            font-weight: 300;
            font-size: 26rpx;
            color: #FFFFFF;
        }
    }

    .right_close {
        position: absolute;
        right: 0rpx;
        top: 0rpx;

        image {
            width: 80rpx;
        }
    }
}

.using {
    margin: 0 36rpx;

    .box {
        .box-item {
            display: flex;
            background: #4b6c76;
            border-radius: 15rpx;
            height: 153rpx;
            margin-bottom: 14rpx;

            .right {
                padding: 14rpx 25rpx 14rpx 51rpx;
                display: flex;
                width: calc(100% - 173rpx);
                align-items: center;
                justify-content: space-between;
                position: relative;

                .ball {
                    position: absolute;
                    width: 30rpx;
                    height: 30rpx;
                    border-radius: 50%;
                    top: 59rpx;
                    right: -15rpx;
                    background: transparent;
                    z-index: 1;
                    background: var(--main-bg-color);
                }

                .item-left {
                    display: flex;
                    flex-direction: column;

                    .top {
                        display: flex;
                        align-items: center;

                        >text {
                            font-family: HarmonyOS Sans SC;
                            font-weight: 500;
                            font-size: 32rpx;
                            color: #FFFFFF;
                        }

                        >view {
                            margin-left: 20rpx;
                            width: 71rpx;
                            height: 25rpx;
                            border-radius: 13rpx;
                            border: 1rpx solid #FFFFFF;
                            font-family: HarmonyOS Sans SC;
                            font-weight: 400;
                            font-size: 18rpx;
                            color: #FFFFFF;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    }

                    .obj {
                        margin: 18rpx 0;

                    }

                    .obj,
                    .time {
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #FFFFFF;
                        opacity: 0.5;
                    }
                }

                .item-right {
                    width: 125rpx;
                    height: 125rpx;
                    background: #43F6EC;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-family: HarmonyOS Sans SC;
                    font-weight: bold;
                    font-size: 32rpx;
                    color: #000000;
                }
            }

            .left {
                width: 173rpx;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                border-right: 1rpx dashed #fff;
                position: relative;

                .ball {
                    position: absolute;
                    width: 30rpx;
                    height: 30rpx;
                    border-radius: 50%;
                    top: 59rpx;
                    left: -15rpx;
                    background: transparent;
                    z-index: 1;
                    background: var(--main-bg-color);
                }

                .symbol {
                    font-family: HarmonyOS Sans SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #43F6EC;
                }

                .num {
                    margin-left: 8rpx;
                    font-family: HarmonyOS Sans SC;
                    font-weight: 900;
                    font-size: 46rpx;
                    color: #43F6EC;
                }
            }
        }
    }

    .title {
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        display: block;
        margin-bottom: 43rpx;
    }
}

.gray {
    background: #4D4A59 !important;
}

.fff {
    color: #fff !important;
}

.down {
    transition: .3s all;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 48rpx;

    image {
        width: 44rpx;
    }
}

.expire {
    background: #5B5867 !important;
    border: 1rpx solid #FFFFFF;
    color: rgba(255, 255, 255, .5) !important;
}
</style>