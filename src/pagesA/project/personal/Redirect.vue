<template>
    <view class="body">
    </view>
</template>

<script>
export default {
    data() {
        return {
            isLoading: true
        }
    },
    onLoad(options) {
        setTimeout(() => {
            this.myUni.webView.navigateTo({
                url: `/pagesA/project/personal/profile`
            });
        }, 500)
    },
    methods: {

    }
}
</script>

<style lang="scss" scoped>
.body {
    background: #fff !important;
    height: 100vh;
}
</style>
