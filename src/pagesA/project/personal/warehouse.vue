<template>
	<view class="main">
		<view class="back" v-if="!isApp" @click="back()">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20230620/73f3f6f0822d6563092fba52dd5954c0_112x45.png"
				mode="widthFix"></image>
		</view>
		<view class="head">
			<view class="cart">
				<view class="text">
					<text>￥</text>{{totalGoodsAmount}}
				</view>
			</view>
			<view class="icon" @click="isModel=true">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20230620/36d573d5888f75372e07a98e2770438c_189x34.png"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="tab_view" :class="{'active':sun}">
			<view class="left" @click="check(1)"></view>
			<view class="right" @click="check(2)"></view>
		</view>
		<view class="list">
			<view class="li" v-for="(item,index) in list">
				<view class="left_img">
					<view class="cover">
						<image
							:src="item.cover"
							mode="widthFix"></image>
						<view class="xuanfu">
							￥{{item.floorPrice}}
						</view>
						<view class="bg"></view>
					</view>
					<view class="title oneOver">
						{{item.title}}
					</view>
				</view>
				<view class="num">
					x{{item.goodsNum}}
				</view>
				<view class="price">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20230620/e93a957edd1d52e448ed6dbae5ab42c2_66x66.png" mode="widthFix"></image>
					<text>{{item.totalGoodsAmount}}</text>
				</view>
			</view>
		</view>
		<u-modal class="model" width="600" v-model="isModel" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="colse" @click="colse()">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="introduce">
				<view>藏品单价的估价统计均采取小口径：</view>
				<br>
				<view>1.单品价格采取同系列下地板价的价格。</view>
				<view>2.退市藏品以最高限价视为地板价。</view>
				<view>3.未开寄售且没有最高限价的藏品价值视为0。</view>
				<view>4.该数值仅统计PGC藏品</view>
				<br>
				<view>注：数据每1小时更新一次</view>
			</view>
		</u-modal>
		<!-- <introducePop :show.sync="isShowIntroduce" :title="title" :introduce="introduce"></introducePop> -->
	</view>
</template>

<script>
	import introducePop from '@/components/public/introducePop.vue';
	export default {
		data() {
			return {
				sun: false,
				isModel:false,
				title:"统计口径",
				introduce:"",
				isApp:false,
				platform:'',
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				list:[],
				pageNum:1,
				sortType:1,
				totalGoodsAmount:0,
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600'
				},
			};
		},
		onLoad(options) {
			const {
				token,
				platform,
				title
			} = options;
			this.platform = platform
			if(platform){
				this.isApp=true
			}
			if (token) {
				uni.setStorageSync('token', token);
			}
			this.getList()
			this.myGoodsAmountTotal()
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				this.loadStatus = 'nomore';
				console.log("已经到底了")
			}
		},
		methods: {
			check(type) {
				if (type == 2) {
					this.sun = true
					this.pageNum=1
					this.sortType=2
					this.list=[]
					this.isFooter=true
					this.getList()
				} else {
					this.sun = false
					this.pageNum=1
					this.sortType=1
					this.isFooter=true
					this.list=[]
					this.getList()
				}
			},
			async getList() {
				this.isLoadding = true
				this.isRequest = true
				let res = await this.$api.java_myGoodsAmount({
					pageNum:this.pageNum,
					pageSize:20,
					sortType:this.sortType
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item)=>{
							if(item.totalGoodsAmount){
								item.totalGoodsAmount =item.totalGoodsAmount .toLocaleString('en-US')
							}
							this.list.push(item)
						})
						console.log(this.list)
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async myGoodsAmountTotal() {
				let res = await this.$api.java_myGoodsAmountTotal({
			   
				});
				if (res.status.code == 0) {
					this.totalGoodsAmount=res.result.totalGoodsAmount.toLocaleString('en-US')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			back(){
				this.$Router.back()
			},
			colse() {
				console.log("关闭")
				this.isModel=false
			}, 
		},
		components:{
			introducePop
		}
	}
</script>

<style lang="scss">
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/yahei.ttf');
	}
	@font-face {
		font-family: 'fonts';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	}
	.main {
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/0f44e6ae28eb577e4e414e05ea7d8fca_400x711.png);
		background-size: 100%;
		min-height: 100vh;

		.back {
			position: absolute;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:27rpx;
			/* #endif */
			left: 30rpx;

			image {
				width: 78rpx;
			}
		}

		.head {
			padding: 76rpx 0rpx 30rpx 0rpx;

			.cart {
				width: 605rpx;
				height: 306rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/cd5a07639f3189d0eb9edc9c27da178c_876x442.png);
				background-size: 100%;
				margin: 0 auto;
				color: #Fff;
				font-size: 70rpx;
				padding: 160rpx 0rpx 0rpx 0rpx;
				text-align: center;

				.text {
					font-family: 'fonts_title';
					background: linear-gradient(360deg, #ed9003 0%, #fedf0e 100%);
					font-weight: 600;
					background-clip: text;
					color: transparent;

					// -webkit-box-reflect: below 0 -webkit-linear-gradient(transparent, transparent 20%, rgba(255, 255, 255, .3));
					text {
						font-size: 88rpx;
					}
				}
			}

			.icon {
				width: 131rpx;

				image {
					width: 100%;
				}

				position: absolute;
				left:80rpx;
			}

		}

		.tab_view {
			width: 100%;
			height: 100rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/b6dd86ca799a11ac755fce618c36e41c_400x61.png);
			background-size: 100%;
			background-repeat:no-repeat;
			display: flex;
			justify-content: flex-start;

			&.active {
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/2ecce5fd6ad38f712fdb05a6d68b1b58_400x61.png);
			}

			.left,
			.right {
				width: 50%;
			}
		}

		.list {
			padding: 10rpx 20rpx;
			min-height:100vh;
			.li {
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/9d64648573610ac12dc2ea9bbfe40b82_400x130.png);
				background-size: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 40rpx;
				height: 240rpx;

				.left_img {
					width: 151rpx;

					.cover {
						width: 151rpx;
						height: 151rpx;
						position: relative;
						border-radius: 30rpx;
						overflow: hidden;
						display: flex;
						justify-content: center;
						align-items: center;
						isolation: isolate;
						image {
							width: 145rpx;
						}

						.xuanfu {
							background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230620/b73b050cdf82d298d068fc3a57305e2d_246x249.png);
							background-size: 100%;
							position: absolute;
							left: 0rpx;
							top: 0rpx;
							width: 151rpx;
							height: 151rpx;
							z-index: 99;
							padding-top: 110rpx;
							text-align: center;
							font-size: 28rpx;
							font-family: 'fonts_title';
							color: #fff;
						}
						.bg{
							background: rgba(0, 0, 0, 0.4);
							position: absolute;
							left: 0rpx;
							bottom:0rpx;
							width: 151rpx;
							height: 55rpx;
							z-index: 80;
							border-bottom-left-radius:30rpx;
							border-bottom-right-radius:30rpx;
							transform: translate3d(0, 0, 0);
						}
					}

					.title {
						color: #fff;
						font-family: 'fonts_title';
						margin-top: 10rpx;
						text-align: center;
						width:100%;
					}

				}

				.num {
					font-size: 44rpx;
					color: #fff;
					font-weight: 600;
					font-family: 'fonts_title';
				}

				.price {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					color:#fff;
					font-size:40rpx;
					letter-spacing:2rpx;
					font-weight:600;
					image {
						width: 40rpx;
						margin-right:10rpx;
					}
				}
			}
		}
	}
	.model::v-deep {}
	
	::v-deep .u-toast.u-show {
		background-color: rgba(0, 0, 0, 0.9) !important;
		color: #F9F9F9;
		border-radius: 10rpx !important;
		padding: 40rpx 40rpx;
	}
	
	// .u-modal {
	//   border: 1px solid cyan;
	//   background: var(--dialog-bg-color);
	//   border-radius: 4rpx;
	//   width: 100%;
	//   height: 100%;
	// }
	.model {
		font-family: 'fonts';
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
	
		.msg {
			color: #F9F9F9;
			font-size: 26rpx;
			padding: 0rpx 40rpx 28rpx 40rpx;
		}
	}
	
	.input-box {
		border-bottom: 1rpx solid #282828;
		padding-bottom: 20rpx;
	}
	
	.introduce {
		
		padding: 40rpx 40rpx 48rpx 40rpx;
		font-size: 28rpx;
		line-height: 38rpx;
		color: #9F9F9F;
		>view{
			margin-bottom:10rpx;
		}
	}
</style>
