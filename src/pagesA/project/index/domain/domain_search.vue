<template>
	<view class="main">

		<view class="slot-wrap" :style="{'paddingLeft':!isBack?'39rpx':'0rpx'}" @click="nav_search_result">
			<view class="search">
				<view>
					<view>
						<image src="@/static/imgs/notic/search.png" mode="widthFix"></image>
					</view>
					<view> 搜索你想要的数字身份</view>
				</view>
				<view>搜索</view>
			</view>
		</view>
		<!-- </u-navbar> -->
		<view class="content">
			<view class="shaixuan">
				<view class="li">
					<commonSelect :options="priceOptions" :showMask="true" :clear="true" placeholder="价格"
						:selected.sync="priceSelected"></commonSelect>
				</view>
				<view class="li">
					<commonFilter ref="filter" :options="DomainfilterOptions" :showMask="true" :selected="filterResult"
						:reset="reset" :domain='true' :confirm="filter_submit" @homingResult="homingResult">
					</commonFilter>
				</view>
			</view>
			<view class="list">
				<view class="li" v-for="(item,index) in list" :key="index">
					<view>
						<view class="id">
							身份id
						</view>
						<view class="left_text">
							{{item.title}}
						</view>
					</view>
					<view class="right_but">
						<view :class="[item.saleStatus==0?'text':'gray']">
							{{item.saleStatus==0?'未寄售':`￥${item.price}`}}
						</view>
						<view @click="submit(item)" :class="[ item.saleStatus==1? 'active':'but']">
							{{item.saleStatus==0?'去求购':'购买'}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import commonSelect from "@/components/select/commonSelect";
	import commonFilter from "@/components/filter/commonFilter";
	export default {
		data() {
			return {
				search_icon: "../../../static/imgs/public/search_icon2.png",
				searchValue: '',
				keyword: '',
				isHistoryList: true,
				inputStyle: {
					'width': '70%'
				},
				priceOptions: [{
						label: "价格升序",
						value: 1,
						disabled: false
					},
					{
						label: "价格降序",
						value: 2,
						disabled: false
					},
				],
				priceSelected: '',
				DomainfilterOptions: [{
					title: '属性', //  标题
					field: "tag", // 绑定 selected 字段名
					clear: true, // 是否可清除
					options: [{
						label: '数字', // 选项标签
						value: 'number' // 选项值
					}, {
						label: '字母', // 选项标签
						value: 'alphabet' // 选项值
					}, {
						label: '中文', // 选项标签
						value: 'chinese' // 选项值
					}, {
						label: '重叠', // 选项标签
						value: 'repeat' // 选项值
					}, {
						label: '混杂', // 选项标签
						value: 'mixed' // 选项值
					}]
				}, ],
				filterResult: {},
				list: [],
				sortBy: "",
				sort: "",
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				pageNum: 1,
				isBack: true
			};
		},
		components: {
			commonSelect,
			commonFilter
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		onLoad(option) {
			const {
				token,
				platform,
				orderId,
			} = option;
			this.platform = platform;
			if (platform) {
				uni.setStorageSync('is_platform', platform);
				this.isBack = false
			}
			if (token) {
				uni.setStorageSync('token', token);
				this.getUser()
			}
			if (orderId) {
				uni.setStorageSync('uid', orderId);
			}

			this.getList()
		},
		watch: {
			priceSelected() {
				if (this.priceSelected === '') {} else {
					this.sortBy = 'price'
					if (this.priceSelected == 1) {
						this.sort = 'asc'
					} else if (this.priceSelected == 2) {
						this.sort = 'desc'
					} else {
						this.sortBy = ''
						this.sort = ''
					}
					this.filter_submit()
				}
			}
		},
		// 加载更多
		methods: {
			//搜索热词
			async getList() {
				this.isRequest = true
				const {
					result,
					status
				} = await this.$api.java_domainNamelist({
					pageNum: this.pageNum,
					pageSize: 15,
					sort: this.sort,
					sortBy: this.sortBy,
					tag: this.filterResult.tag,
					subTag: this.filterResult.subTag
				})
				if (status.code === 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							this.list.push(item)
						})
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			T_search(item) {
				this.searchValue = item
				this.d_value = item
				this.search()
			},
			itemClick(item) {
				this.searchValue = item
				this.d_value = item
				this.search()
			},
			search() {
				this.searchValue = this.$u.trim(this.searchValue, 'both')
				let num = 0
				if (this.searchValue === "") {
					uni.showToast({
						title: "请输入关键字搜索哦",
						icon: "none",
						duration: 3000
					});
				} else {
					if (this.historyList !== "") {
						this.historyList.forEach((item) => {
							if (item.name === this.searchValue) {
								num = 1
							}
						})
						if (num === 0) {
							this.historyList.push({
								"name": this.searchValue
							})
							uni.setStorageSync("historyList", this.historyList)
							this.$Router.push({
								name: "synthesizeSearch",
								params: {
									keyword: this.searchValue
								}
							})
						} else {
							this.$Router.push({
								name: "synthesizeSearch",
								params: {
									keyword: this.searchValue
								}
							})
						}
					} else {
						this.historyList.push({
							"name": this.searchValue
						})
						uni.setStorageSync("historyList", this.historyList)
						this.$Router.push({
							name: "synthesizeSearch",
							params: {
								keyword: this.searchValue
							}
						})
					}
				}
			},
			custom() {
				this.$Router.back()
			},
			del() {
				this.historyList = []
				uni.removeStorageSync("historyList")
				// this.isHistoryList=false
			},
			reset(e) {
				console.log(e)
				this.filterResult = [],
					this.filter_submit()
			},
			filter_submit() {
				console.log(this.filterResult)
				// if(this.filterResult.tag==''||this.filterResult.tag==undefined){
				// 	uni.showToast({
				// 		title: "请选择属性再筛选",
				// 		icon: "none",
				// 		duration: 3000
				// 	});
				// 	return false
				// }
				this.list = []
				this.pageNum = 1
				this.getList()
			},
			nav_search_result() {
				this.$Router.push({
					name: "domain_search_result",
					params: {
						platform: this.platform
					}
				})
			},
			submit(item) {
				if (item.saleStatus == 1) {
					let orderList = {
						"needConsignee": false,
						"alipay": true,
						"wechat": false,
						"isFirst": 0,
						"item": {
							"id": null,
							"type": 0,
							"tokenId": "",
							"name": "",
							"desc": "",
							"content": "",
							"showVersion": "1/1",
							"version": 1,
							"photoShow": "",
							"photo": "",
							"price": "",
							"fondCount": 0,
							"scanCount": 0,
							"commentCount": 0,
							"collectCount": 0,
							"time": "",
							"qrLink": "http://web-test.nftcn.com.cn/h5/#/pagesA/project/mall/mallDetails?tid=43465200249102937606166236479231",
							"link": "nftcn://item?itemTokenId=43465200249102937606166236479231",
							"coverImage": {
								"src": "",
								"w": 240,
								"h": 240
							},
							"isFromMysteryBox": false,
							"isBlind": false,
							"endTime": "",
							"notSaleSign": 0
						},
						"kingVersion": null,
						"copyrightFeeShow": null,
						"tip": null,
						"iversion": null
					}
					orderList.item.price = item.price
					orderList.item.name = item.title
					orderList.item.tid = item.tid
					orderList.item.coverImage.src = item.cover.src
					orderList.batchBuyNum = 1
					orderList.domain = true
					uni.setStorageSync("detailsList", orderList);
					this.$Router.push({
						name: "checkOrder",
						params: {
							isDomain: 3
						}
					});
				} else {
					var list = {
						"title": "",
						"cover": {
							"src": "",
						},
						"createNum": 1,
						"activeNum": 1,
						"onSaleMaxPrice": null,
						"minPrice": null,
						"tid": "",
						"releasePrice": 1,
					}
					list.tid = item.tid
					list.cover = item.cover
					list.title = item.title
					uni.setStorageSync("askBuyList", list);
					this.$Router.push({
						name: "askBuy",
						params: {
							isDomain: true
						}
					})
				}

			},
			async getUser() {
				let res = await this.$api.java_userInfoV2({
					userId: ""
				});
				if (res.status.code == 0) {
					uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			homingResult(e) {
				console.log(this.filterResult.subTag)
				if (e == 'number' || e == 'alphabet' || e == 'chinese' || e == 'repeat' || e == 'mixed') {
					this.filterResult = {
						tag: e
					}
				}
			},
			back() {
				this.$Router.pushTab({
					name: 'mall_new',
				})
			}
		},
	}
</script>
<style lang="scss" scoped>
	.main {
		.slot-wrap {

			width: 678rpx;
			height: 80rpx;
			border-radius: 28rpx;
			border: 1rpx solid #ccc;
			overflow: hidden;
			margin: 0 auto;

			.search {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 30rpx 0 20rpx;
				box-sizing: border-box;

				>view:nth-child(1) {
					display: flex;
					align-items: center;
					color: #afafaf;

					>view:nth-child(1) {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
						font-weight: 400;
						font-size: 28rpx;

						>image {
							width: 100%;
							height: 100%;
						}
					}
				}

				>view:nth-child(2) {
					font-weight: 400;
					font-size: 28rpx;
					color: #FFFFFF;
				}
			}


		}

		.search {
			color: #E4E4E4;
			padding-right: 40rpx;
			font-size: 28rpx;
		}

		.card {
			padding: 40rpx 40rpx 0;

			.top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-bottom: 32rpx;

				.title {
					color: var(--main-front-color);
					font-size: 28rpx;
				}
			}

			.list {
				display: flex;
				flex-wrap: wrap;

				.item {
					padding: 16rpx 20rpx;
					background: var(--tag-bg-color);
					border-radius: 4rpx;
					margin-right: 20rpx;
					font-size: 24rpx;
					color: var(--secondary-front-color);
					margin-bottom: 20rpx;
				}
			}
		}
	}

	.search_view {}

	::v-deep .uni-input-form {
		width: 80% !important;
	}



	.content {
		padding: 40rpx;

		.shaixuan {
			width: 100%;
			height: 70rpx;
			margin-bottom: 30rpx;

			.li {
				background-color: #fff;
				border-radius: 40rpx;
				display: inline-block;
				color: #35333E;
				margin-right: 20rpx;

			}
		}

		.list {
			.li {
				width: 100%;
				height: 220rpx;

				margin-bottom: 40rpx;
				border-radius: 16rpx;
				padding: 30rpx 30rpx 24rpx 30rpx;
				background: url('@/static/imgs/public/listBg.png') no-repeat;
				background-size: cover;
				box-sizing: border-box;

				.id {
					font-weight: 400;
					font-size: 24rpx;
					color: #ccc;
					margin-bottom: 24rpx;
				}

				.left_text {
					color: #fff;
					font-size: 24rpx;
				}

				.right_but {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 34rpx;

					.text {
						//未寄售
						width: 96rpx;
						height: 36rpx;
						line-height: 36rpx;
						background: linear-gradient(90deg, #80799F 0%, #524D68 100%);
						border-radius: 8rpx;
						text-align: center;
						font-weight: bold;
						font-size: 22rpx;
						color: #25232D;
					}

					.gray {
						//价格
						font-weight: bold;
						font-size: 34rpx;
						color: #63EAEE;
					}

					.but {
						//求购
						width: 150rpx;
						height: 48rpx;
						line-height: 48rpx;
						background: #25232D;
						border-radius: 35rpx;
						border: 1rpx solid #FFFFFF;
						font-weight: bold;
						font-size: 24rpx;
						color: #FFFFFF;
						text-align: center;
						box-sizing: border-box;
					}

					.active {
						//购买
						width: 150rpx;
						height: 48rpx;
						line-height: 48rpx;
						text-align: center;
						font-weight: bold;
						font-size: 24rpx;
						color: #25232D;
						background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
						border-radius: 35rpx;
					}
				}
			}
		}
	}

	::v-deep.common-filter .common-filter-label .common-filter-icon {
		width: 22rpx !important;
	}
</style>