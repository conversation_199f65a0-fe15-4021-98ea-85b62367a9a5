<template>
	<view class="main">

		<view class="slot-wrap" :style="{'paddingLeft':!isBack?'39rpx':'0rpx'}">

			<view class="search">
				<view>
					<view>
						<image src="@/static/imgs/notic/search.png" mode="widthFix"></image>
					</view>
					<view> <input type="text" placeholder="搜索你想要的数字身份" v-model="keyword" :focus="true" /></view>
				</view>
				<view @click="search()">搜索</view>
			</view>
		</view>
		<view class="content">
			<view class="title_guide" v-if="searchList!=''">
				搜索结果
			</view>
			<view class="list" v-if="searchList.length!==0">
				<view class="li" v-for="(item,index) in searchList" :key="index">
					<view>
						<view class="id">
							身份id
						</view>
						<view class="left_text">
							{{item.title}}
						</view>
					</view>
					<view class="right_but">
						<view :class="[item.saleStatus==0?'text':'gray']">
							{{item.saleStatus==0?'未寄售':`￥${item.price}`}}
						</view>
						<view @click="submit(item)" :class="[ item.saleStatus==1? 'active':'but']">
							{{item.saleStatus==0?'去求购':'购买'}}
						</view>
					</view>
				</view>
			</view>
			<view class="title_guide" v-if="recommendList!=''">
				为您推荐
			</view>
			<view class="list" v-if="recommendList!=''">
				<view class="li" v-for="(item,index) in recommendList" :key="index">
					<view>
						<view class="id">
							身份id
						</view>
						<view class="left_text">
							{{item.title}}
						</view>
					</view>
					<view class="right_but">
						<view :class="[item.saleStatus==0?'text':'gray']">
							{{`￥${item.price}`}}
						</view>
						<view @click="domainGenerateInfo(item)" class="active">
							去注册
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal v-model="isMsg" font-size="40" :show-title="false" width="619" :mask-close-able="true"
			:show-confirm-button="false">
			<view class="autonym_model">
				<view class="msg">
					{{message}}
				</view>
				<view class="button_db">
					<buttonBar class="but" @click="nav_order()"></buttonBar>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoading" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg" style="
		  padding: 10rpx 20rpx 40rpx 20rpx;
		  text-align: center;
		  font-size: 26rpx;
		  line-height: 40rpx;
		">
				数字身份生成中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	import buttonBar from "@/components/public/ButtonBar.vue"
	export default {
		data() {
			return {
				search_icon: "../../../static/imgs/public/search_icon2.png",
				searchValue: '',
				searchList: [],
				recommendList: [],
				keyword: '',
				isHistoryList: true,
				inputStyle: {
					'width': '70%'
				},
				isMsg: false,
				details: [],
				message: "",
				isLoading: false,
				isNav: true,
				isBack: true,
			};
		},
		components: {
			buttonBar
		},
		onLoad(option) {
			const {
				token,
				platform,
				orderId,
			} = option;
			this.platform = platform;
			if (platform) {
				this.isBack = false
			}
		},
		// 加载更多
		methods: {
			//搜索热词
			async search() {
				const {
					result,
					status
				} = await this.$api.java_search_domainNamelist({
					keyword: this.keyword,
				})
				if (status.code === 0) {
					this.searchList = result.searchList
					this.recommendList = result.recommendList
				} else {
					uni.showToast({
						title: status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			T_search(item) {
				this.searchValue = item
				this.d_value = item
				this.search()
			},
			itemClick(item) {
				this.searchValue = item
				this.d_value = item
				this.search()
			},
			custom() {
				this.$Router.back()
			},
			del() {
				this.historyList = []
				uni.removeStorageSync("historyList")
				// this.isHistoryList=false
			},
			async domainGenerateInfo(item) {
				let res = await this.$api.java_domainGenerateInfo({
					title: item.title
				});
				if (res.status.code == 0) {
					console.log(res)
					this.details = item
					if (res.result.warnMsg != '') {
						this.message = res.result.warnMsg
						this.isMsg = true
					} else {
						this.nav_order()
					}
				} else if (res.status.code == 640007) {
					this.message = res.status.msg
					this.isMsg = true
					this.isNav = false
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async nav_order() {
				this.isMsg = false
				if (this.isNav) {
					let orderList = {
						"needConsignee": false,
						"alipay": true,
						"wechat": false,
						"isFirst": 1,
						"item": {
							"id": 0,
							"type": 0,
							"tokenId": "",
							"name": "",
							"desc": "",
							"content": "",
							"showVersion": "1/1",
							"version": 1,
							"photoShow": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
							"photo": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
							"price": "",
							"fondCount": 0,
							"scanCount": 0,
							"commentCount": 0,
							"collectCount": 0,
							"time": "",
							"qrLink": "http://web-test.nftcn.com.cn/h5/#/pagesA/project/mall/mallDetails?tid=43465200249102937606166236479231",
							"link": "nftcn://item?itemTokenId=43465200249102937606166236479231",
							"coverImage": {
								"src": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
								"w": 240,
								"h": 240
							},
							"isFromMysteryBox": false,
							"isBlind": false,
							"endTime": "",
							"notSaleSign": 0
						},
						"kingVersion": null,
						"copyrightFeeShow": null,
						"tip": null,
						"iversion": null
					}
					this.isLoading = true
					let res = await this.$api.java_generator({
						domainStr: this.details.title
					});
					if (res.status.code == 0) {
						orderList.item.coverImage.src = res.result.domainImageBase64
						orderList.item.price = this.details.price
						orderList.item.name = this.details.title
						orderList.batchBuyNum = 1
						orderList.domain = true
						this.isLoading = false
						uni.setStorageSync("detailsList", orderList);
						this.$Router.push({
							name: "checkOrder",
							params: {
								isDomain: 1
							}
						});
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}
				// orderList.item.showVersion = this.$u.timeFormat(parseInt(new Date().valueOf() / 1000 + 31536000),
				// 	'yy.mm.dd 到期')

			},
			submit(item) {
				console.log(item)
				if (item.saleStatus == 1) {
					let orderList = {
						"needConsignee": false,
						"alipay": true,
						"wechat": false,
						"isFirst": 0,
						"item": {
							"id": null,
							"type": 0,
							"tokenId": "",
							"name": "",
							"desc": "",
							"content": "",
							"showVersion": "1/1",
							"version": 1,
							"photoShow": "",
							"photo": "",
							"price": "",
							"fondCount": 0,
							"scanCount": 0,
							"commentCount": 0,
							"collectCount": 0,
							"time": "",
							"qrLink": "http://web-test.nftcn.com.cn/h5/#/pagesA/project/mall/mallDetails?tid=43465200249102937606166236479231",
							"link": "nftcn://item?itemTokenId=43465200249102937606166236479231",
							"coverImage": {
								"src": "",
								"w": 240,
								"h": 240
							},
							"isFromMysteryBox": false,
							"isBlind": false,
							"endTime": "",
							"notSaleSign": 0
						},
						"kingVersion": null,
						"copyrightFeeShow": null,
						"tip": null,
						"iversion": null
					}
					orderList.item.price = item.price
					orderList.item.name = item.title
					orderList.item.tid = item.tid
					orderList.item.coverImage.src = item.cover.src
					orderList.batchBuyNum = 1
					orderList.domain = true
					uni.setStorageSync("detailsList", orderList);
					this.$Router.push({
						name: "checkOrder",
						params: {
							isDomain: 3,
							tid:item.tid
						}
					});
				} else {
					var list = {
						"title": "",
						"cover": {
							"src": "",
						},
						"createNum": 1,
						"activeNum": 1,
						"onSaleMaxPrice": null,
						"minPrice": null,
						"tid": "",
						"releasePrice": 1,
					}
					list.tid = item.tid
					list.cover = item.cover
					list.title = item.title
					uni.setStorageSync("askBuyList", list);
					this.$Router.push({
						name: "askBuy",
						params: {
							isDomain: true
						}
					})
				}

			}
		},
	}
</script>
<style lang="scss" scoped>
	.main {
		.slot-wrap {
			// flex: 1;
			// padding-left: 0rpx;
			// padding-right: 39rpx;
			// position: relative;

			// .u-search {
			// 	width: 100%;
			// }
			width: 678rpx;
			height: 80rpx;
			border-radius: 28rpx;
			border: 1rpx solid #ccc;
			overflow: hidden;
			margin: 0 auto;

			.search {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 30rpx 0 20rpx;
				box-sizing: border-box;

				>view:nth-child(1) {
					display: flex;
					align-items: center;
					color: #afafaf;

					>view:nth-child(1) {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
						font-weight: 400;
						font-size: 28rpx;

						>image {
							width: 100%;
							height: 100%;
						}
					}
				}

				>view:nth-child(2) {
					font-weight: 400;
					font-size: 28rpx;
					color: #FFFFFF;
				}
			}
		}

		.search {
			color: #E4E4E4;
			padding-right: 40rpx;
			font-size: 28rpx;
		}

		.card {
			padding: 40rpx 40rpx 0;

			.top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-bottom: 32rpx;

				.title {
					color: var(--main-front-color);
					font-size: 28rpx;
				}
			}

			.list {
				display: flex;
				flex-wrap: wrap;

				.item {
					padding: 16rpx 20rpx;
					background: var(--tag-bg-color);
					border-radius: 4rpx;
					margin-right: 20rpx;
					font-size: 24rpx;
					color: var(--secondary-front-color);
					margin-bottom: 20rpx;
				}
			}
		}
	}

	.search_view {}

	::v-deep .uni-input-form {
		width: 80% !important;
	}

	.search_right {
		position: absolute;
		right: 60rpx;
		top: 14rpx;
		color: #989898;
	}

	.content {
		padding: 0rpx 40rpx;

		.shaixuan {
			width: 100%;
			height: 70rpx;
		}

		.title_guide {
			font-size: 32rpx;
			color: #F9F9F9;
			padding: 44rpx 0rpx;
			font-weight: 600;
		}

		.list {

			.li {
				width: 100%;
				height: 220rpx;

				margin-bottom: 40rpx;
				border-radius: 16rpx;
				padding: 30rpx 30rpx 24rpx 30rpx;
				background: url('@/static/imgs/public/listBg.png') no-repeat;
				background-size: cover;
				box-sizing: border-box;

				.id {
					font-weight: 400;
					font-size: 24rpx;
					color: #ccc;
					margin-bottom: 24rpx;
				}

				.left_text {
					color: #fff;
					font-size: 24rpx;
				}

				.right_but {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 34rpx;

					.text {
						//未寄售
						width: 96rpx;
						height: 36rpx;
						line-height: 36rpx;
						background: linear-gradient(90deg, #80799F 0%, #524D68 100%);
						border-radius: 8rpx;
						text-align: center;
						font-weight: bold;
						font-size: 22rpx;
						color: #25232D;
					}

					.gray {
						//价格
						font-weight: bold;
						font-size: 34rpx;
						color: #63EAEE;
					}

					.but {
						//求购
						width: 150rpx;
						height: 48rpx;
						line-height: 48rpx;
						background: #25232D;
						border-radius: 35rpx;
						border: 1rpx solid #FFFFFF;
						font-weight: bold;
						font-size: 24rpx;
						color: #FFFFFF;
						text-align: center;
						box-sizing: border-box;
					}

					.active {
						//购买
						width: 150rpx;
						height: 48rpx;
						line-height: 48rpx;
						text-align: center;
						font-weight: bold;
						font-size: 24rpx;
						color: #25232D;
						background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
						border-radius: 35rpx;
					}
				}
			}

			.li:last-child {
				border: none;
			}
		}
	}

	.autonym_model {
		padding: 66rpx;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230310/7f5dc664f22f252d198f05187c98adc2_928x575.png);
		background-size: 100% 100%;

		.msg {
			color: #C3C1C2;
			font-size: 30rpx;
			font-weight: 600;
			line-height: 54rpx;

			.text {
				color: #2CEBF1;
			}
		}

		.button_db {
			.but {
				border-radius: 40rpx;
				width: 277rpx;
				height: 55rpx;
				font-size: 30rpx;
			}
		}
	}
</style>