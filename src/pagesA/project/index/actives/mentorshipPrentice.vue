<template>
	<view class="content">
		<view class="model1"></view>
		<view class="back" @click="custom">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/f78780886762e4b1a251830950da7d6a_76x58.png"
				mode="widthFix"></image>
		</view>
		<view class="right_back" @click="nav_Explain">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/7d133138b1c2d4a0bd3a85b055ff97a8_210x43.png"
				mode="widthFix"></image>
		</view>
		<view class="data_view">
			<view class="data_view_bg">
				<view>
					<view class="view_data_li">
						当前累计收徒<text>{{info.studentCount}}</text>人
					</view>
					<view class="view_data_li">
						当前累计收益<text>{{info.teacherIncome}}</text>元
					</view>
					<view class="view_data_li">
						师门排行榜第<text>{{info.teacherRanking}}</text>名
					</view>
				</view>
			</view>
		</view>
		<view class="theme">
			<view class="but" @click="copy()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/********/ea5fdb630ed0da7a7af6bce28c3a3a71_590x179.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="all_list">
			<view class="top_view">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/********/880866f5f985a3b3c86251b410575927_642x73.png" mode="widthFix"></image>
				<view class="iconn">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/dac6f65c7d5a6650f1141a6488659e66_200x78.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="prenticeList" v-if="isMaster==1">
				<view class="list" @scroll="scrollEvent($event)">
					<view class="li" v-for="(item,index) in prenticeList">
						<view class="flex_start align_items">
							<view class="left_view">
								<view class="img">
									<image :src="item.avatar" mode="aspectFill"></image>
								</view>
							</view>
							<view class="right_view">
								<view class="title_view">
									<view class="title oneOver">{{item.nickname}}</view>
									<view class="icon" @click="nav_details(item)">
										<image src="https://cdn-lingjing.nftcn.com.cn//image/********/8cdf415cc7124ae13709a701889521c4_140x46.png" mode="widthFix"></image>
									</view>
								</view>
								<view class="introduce">
									<span>拜师时间:</span>{{item.startTime}}
								</view>
								<view class="introduce">
									<span>近7天得收益:</span>￥{{item.teacherIncome7Day}}
								</view>
								<view class="introduce">
									<span>累计分得收益:</span>￥{{item.teacherIncome}}
								</view>
							</view>
						</view>
						<!--  -->
						<view class="error" v-show="item.isBind==1" @click="open(item,1,index)">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/********/c5641d5c6777fa6d991de93f4af4580a_120x38.png"
								mode="widthFix"></image>
						</view>
						<view class="error" v-show="item.isBind==0">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/********/236c85e11d91c80c60906ff1c1281e65_120x38.jpg"
								mode="widthFix"></image>
						</view>
					</view>
					<view v-if="prenticeList==''&&isMaster==1">
						<view class="null_view">
							<u-empty class="null"
								src="https://cdn-lingjing.nftcn.com.cn/image/********/aaf8a0b5f985c1a5e54d50d2a2ce8b62_159x159.png"
								text=" " mode="list" margin-top="340"></u-empty>
							<view class="font-null">您还没有收徒</br>快点击上面的链接开始收徒吧</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="fixed_right"  @click="nav_router()" v-if="teacherGroupAccount">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/d473c885dc9b0b099e2e7b34a0c4a504_170x145.png" mode="widthFix"></image>
		</view>
		<u-modal v-model="isRelieve" border-radius="0" :show-title="false" :content-style="bgObject"
			:show-confirm-button="false">
			<view class="body_div">
				<view class="modal_msg">确认解除师徒关系？</view>
				<view class="modal_button">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/0e62b0194b6e1dfe17417304bcef00d7_172x66.png"
						@click="isRelieve=false" mode="widthFix"></image>
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/149f74bea7f0c7de40f9d90afe1b5be5_172x66.png" @click="checkSubmit"
						mode="widthFix"></image>
				</view>
			</view>
		</u-modal>
		
		<u-modal class="model" width="600" v-model="is_msg" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="model_body">
				<view class="colse" @click="is_msg=false">
					<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
				</view>
				<view class="introduce">
					<view>{{time_msg}}</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="model" width="600" v-model="isShowIntroduce" :show-title="true" :show-confirm-button="false" :content-style="bgObject"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="colse" @click="isShowIntroduce=false">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/********/4f8cd15ae119d3d6c2fc6c72085ba3e6_44x44.png" mode="widthFix"></image>
			</view>
			<view class="model_body">
				<view class="introduce">
					<view>请使用APP进入师门群聊界面</view>
					<view class="but" @click="goDownload">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/********/470740bb383c5253f620785808e77a08_160x62.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import uniCopy from "@/js_sdk/uni-copy.js";
	import antiShake from "@/common/public.js"
	import introducePop from '@/components/public/introducePop.vue';
	export default {
		data() {
			return {
				isPrentice: true,
				isMaster: 1,
				titleObject: {
					'background-color': '#35333E',
					'color': '#FFFFFF'
				},
				bgObject: {
					'background': 'transparent',
					'color': '#FFFFFF',
					'background-image': 'url(https://cdn-lingjing.nftcn.com.cn//image/********/7357bcef6c524779b5af1c8fd4989a22_420x200.png)',
					'background-size': '100% 100%',
					'height': '250rpx'
				},
				isRelieve: false,
				masterList: [],
				prenticeList: [],
				isRequest: false,
				prenticePageNum: 1,
				masterPageNum: 1,
				isFooter: true,
				platform: '',
				sun: 0,
				isTeacher: '',
				isShowIntroduce:false,
				title:'',
				introduce:'请使用APP进入师门群聊界面',
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600',
				},
				is_msg:false,
				time_msg:'',
				info:[],
				idx:'',
				teacherGroupAccount:"",
				isRequest2: false,
				isFooter2: true,
				startTime:"",
				options_info:"",
				status: 'loadmore',
			}
		},
		components:{
			introducePop
		},
		onLoad(options) {
			this.options_info=options
			this.contract_address=options.contract_address
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if (token) {
				uni.setStorageSync('token', token);
			}
			this.getList()
			this.my_teacher()
			this.teacher_info()
		
		},
		onShow() {

		},
		onReachBottom() {
			if(this.isMaster==1){
				if (this.isFooter) {
					if (this.isRequest == false) {
						this.getList()
					} else {
						console.log("请求超时，已经拦截")
					}
				} else {
					console.log("已经到底了")
				}
			}else if(this.isMaster==2){
				if (this.isFooter2) {
					if (this.isRequest2 == false) {
						this.my_teacher()
					} else {
						console.log("请求超时，已经拦截")
					}
				} else {
					console.log("已经到底了")
				}
			}
			
		},
		methods: {
			async getList() {
				this.isRequest = true
				let res = await this.$api.java_myStudentList({
					pageNum: this.prenticePageNum,
					pageSize: 10
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.prenticePageNum++
						res.result.list.forEach((item) => {
							this.prenticeList.push(item)
						})
						console.log(this.prenticeList)
					}
				} else if (res.status.code == 1002) {
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async my_teacher() {
				this.isRequest2 = true
				console.log('我的师傅分页：'+this.masterPageNum)
				let res = await this.$api.java_myTeacher({
					pageNum: this.masterPageNum,
					pageSize: 10
				});
				if (res.status.code == 0) {
					this.isRequest2 = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter2 = false
					} else {
						this.masterPageNum++
						res.result.list.forEach((item) => {
							this.masterList.push(item)
						})
						console.log(this.masterList)
					}
				} else if (res.status.code == 1002) {
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(item) {
				window.location.href = item.link
			},
			nav_guize(item) {
				this.$Router.push({
					name: 'mentorshipExplain',
				})
			},
			nav_details(item) {
				this.$Router.push({
					name: 'mentorshipDetails',
					params: {
						teacherStudentId: item.teacherStudentId
					}
				})
			},
			custom() {
				const pages = getCurrentPages();
				if (this.isActive) {
					history.back();
				} else if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall",
					});
				} else {
					this.$Router.back();
				}
			},
			check(type) {
				this.isFooter = true
				this.isRequest = false
				this.isFooter2 = true
				this.isRequest2 = false
				this.isMaster=type
				console.log(type,this.isMaster)
			},
			open(item,type,index) {
				this.teacherStudentId = item.teacherStudentId
				this.isTeacher = type
				this.isRelieve = true
				this.startTime = Date.parse(item.startTime.replace(/\-/g, "/"))/1000
				this.idx=index
			},
			copy(val) {
				// #ifdef H5
					let {
						origin
					} = window.location
					let contract_address = uni.getStorageSync('contract_address')?uni.getStorageSync('contract_address'):this.contract_address
					let url = `${origin}/h5/#/pagesA/project/index/actives/prenticeAccept?contractAddress=${contract_address}`
				// #endif
				// #ifdef APP
					let contract_address = uni.getStorageSync('contract_address')?uni.getStorageSync('contract_address'):this.contract_address
					let url = `${getApp().globalData.url}pagesA/project/index/actives/prenticeAccept?contractAddress=${contract_address}`
				// #endif
				uniCopy({
					content: url,
					success: (res) => {
						uni.showToast({
							title: '已复制您的邀请链接，快去粘贴给您的好友吧～',
							icon: "none",
						});
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: "none",
							duration: 3000,
						});
					},
				});
			},
			checkSubmit: antiShake._debounce(function() {
				this.submitRelieve()
			}, 300),
			async submitRelieve() {
				let time = Date.parse(new Date())/1000;
				let startTime = this.startTime
				if((time-startTime)>604800){
					this.isRelieve = false
					let res = await this.$api.java_unbindTeacher({
						teacherStudentId: this.teacherStudentId,
						isTeacher: this.isTeacher
					});
					if (res.status.code == 0) { 
						if(this.isMaster==1){
							uni.showToast({
								title: "已解除师徒关系",
								icon: 'none',
								duration: 3000
							});
							this.prenticeList[this.idx].isBind=0
						}else{
							uni.showToast({
								title: "已与原师父解除师徒关系",
								icon: 'none',
								duration: 3000
							});
							this.masterList[this.idx].isBind=0
						}
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}else{
					let time_msg = this.$u.timeFormat(startTime+604800, 'mm月dd日 hh:MM')
					this.is_msg=true
					this.time_msg=`师徒关系建立少于7天，暂不可解除师徒关系。您可以在${time_msg}后再来。`
					this.isRelieve = false
				}
				
			},
			nav_router() {
				if(this.platform){
					this.$native.groupChatDetail({
						groupID:this.teacherGroupAccount,
						judge:1
					})
					if(this.platform == 'ios'){
						
					}else{
						this.$native.groupChatDetail({
							groupId:this.teacherGroupAccount,
						})
					}
				}else{
					this.isShowIntroduce=true
				}
			},
			goDownload() {
				this.isShowIntroduce=false
				this.$Router.push({
					name: "appDownload",
				});
			},
			async teacher_info() {
				let res = await this.$api.java_teacherInfo({});
				if (res.status.code == 0) {
					console.log(res)
					this.info=res.result
					this.teacherGroupAccount=res.result.teacherGroupAccount
					this.info.teacherIncome=this.info.teacherIncome.toLocaleString('en-US')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_Explain(){
				this.$Router.push({
					name: "mentorshipExplain",
				});
			}
		},
		
	}
</script>

<style lang="scss" scoped>
	// @font-face {
	// 	font-family: 'fonts_title';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newActive.OTF');
	// }

	// @font-face {
	// 	font-family: 'fonts';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	// }

	// page {
	// 	font-family: 'fonts';
	// }
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/sanjicuti.ttf');
	}
	page{
		background-color: #222F31;
	}
	::v-deep .u-model{
		background-color: transparent !important;
	}

	.content {
		padding-top:430rpx;
		.model1 {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/********/71f2ed45295262bbf3ebcceb03a61c4d_400x309.png);
			background-size: 100% 100%;
			height: 580rpx;
			width: 100%;
			position: absolute;
			z-index: 0;
			top: 0;
		}
		
		.back {
			position: absolute;
			left: 20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:30rpx;
			/* #endif */
			z-index: 99;
		
			image {
				width: 54rpx;
			}
		}
		.right_back{
			position: absolute;
			right: 20rpx;
			top: 30rpx;
			z-index: 99;
					
			image {
				width: 200rpx;
			}
		}
		.float_icon {
			position: fixed;
			z-index: 99;
			width: 172rpx;
			height: 42rpx;
			top: 258rpx;

			image {
				width: 100%;
			}
		}

		.theme {
			width: 100%;
			position: relative;
			height: 136rpx;
			margin-bottom: 75rpx;
			.but {
				position: absolute;
				width: 558rpx;
				height: 136rpx;
				margin: 0 auto;
				left: 0;
				right: 0;
				image {
					width: 100%;
				}
			}
		}

		.tag_view {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 74rpx 55rpx 34rpx 55rpx;

			.bg_view {
				width: 278rpx;
				height: 369rpx;
				background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230626/ab2b573c6585f0a1c3fd5234bf068859_400x518.png');
				background-size: 100% 100%;
				padding: 2rpx;
				position: relative;
				transition: all 0.3s;

				&.active {
					padding: 18rpx 24rpx;
					transition: all 0.3s;

					.img {
						border-radius: 10rpx;
						margin-bottom: 20rpx;
						box-shadow: 0px 0px 14rpx 4rpx rgb(0 0 0 / 40%);
					}
				}

				.img {
					border-right: 8rpx;
					overflow: hidden;

					image {
						width: 100%;
						border-right: 8rpx;
					}
				}

				.footer {
					text-align: center;
					position: absolute;
					bottom: 20rpx;
					left: 0;
					width: 100%;

					.but {
						width: 247rpx;
						margin: 0rpx auto;

						image {
							width: 247rpx;
							height: 54rpx;
						}
					}

					text {
						font-size: 20rpx;
						color: #8F91AA;
					}
				}
			}
		}

		.data_view {
			padding: 0rpx 0rpx 14rpx 0rpx;
			display: flex;
			position: absolute;
			width:100%;
			z-index: 1;
			top: 100rpx;
			.data_view_bg {
				padding: 40rpx 90rpx;
				background-image: url('https://cdn-lingjing.nftcn.com.cn/image/********/3a3d241308adb654bd1cd93e95733c97_700x343.png');
				background-size: 100% 100%;
				height: 342rpx;
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				.view_data_li {
					width: 100%;
					height: 44rpx;
					line-height: 44rpx;
					background-size: 100% 100%;
					font-size: 28rpx;
					color: #B54D37;
					text-align: center;
					margin-bottom: 20rpx;
					font-family: "fonts_title";
					text {
						color: #B54D37;
						font-size:40rpx;
						padding: 0rpx 10rpx;
						font-family: "";
						font-weight: 600;
					}
				}
			}
		} 

		.all_list {
			padding: 47rpx 42rpx;
			
			.top_view{
				width:100%;
				position: relative;
				image{
					width:100%;
				}
				.iconn{
					position: absolute;
					top:-39rpx;
					left:0;
					right:0;
					margin: 0 auto;
					width: 200rpx;
					height:78rpx;
					image{
						width:100%;
					}
				}
			}
			
		}

		.prenticeList,
		.masterList {
			width: 100%;
			background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/c39a77c800274fe5232d936db9fa49aa_642x990.png);
			background-size: 100% 100%;
			padding:0rpx 32rpx 40rpx 32rpx;
			.list{
				.li {
					padding: 46rpx 22rpx 50rpx 36rpx;
					width: 100%;
					margin-bottom: 20rpx;
					position: relative;
					background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/ffa3a93feb561d0f09e357f832df6249_596x276.png);
					background-size: 100% 100%;
					height:260rpx;
					.align_items { 
						align-items: flex-start !important;
					}
				
					.left_view {
						width: 122rpx;
						height: 122rpx;
						border-radius: 50%;
						display: flex;
						justify-content: center;
						align-items: center;
						.img{
							width: 122rpx;
							height: 122rpx;
							border:2rpx solid #AEB4B9;
							background-color:#50565B;
							border-radius:50%;
							display: flex;
							justify-content: center;
							align-items: center;
							image {
								width: 112rpx;
								height: 112rpx;
								border-radius: 50%;
							}
						}
					}
				
					.right_view {
						margin-left: 30rpx;
				
						.title_view {
							color: #FEF3C4;
							font-size: 36rpx;
							position: relative;
							margin-bottom:15rpx;
							font-weight:600;
							.title {
								width: 240rpx;
								font-size:28rpx;
								&.master {
									width: 420rpx;
								}
							}
				
							.icon {
								width: 136rpx;
								height: 33rpx;
								line-height: 33rpx;
								text-align: center;
								position: absolute;
								right: 0rpx;
								top: -2rpx;
								image{
									width:100%;
								}
							}
						}
				
						.introduce {
							border-radius: 4rpx;
							width: 400rpx;
							height: 35rpx;
							font-size: 24rpx;
							line-height: 36rpx;
							color: #FEF3C4;
							margin-bottom:15rpx;
							padding: 0rpx 10rpx;
							span{
								color:#121C1E;
								font-family: "fonts_title";
							}
						}
					}
				}
				.error {
					// color: #0AF7EF;
					// font-size: 22rpx;
					position: absolute;
					top: 180rpx;
					// width: 152rpx;
					// text-align: center;
					width: 120rpx;
					image {
						width: 100%;
					}
				
				}
				
				.button_footer {
					align-items: center;
					margin-top: 30rpx;
				
					image {
						width: 269rpx;
						height: 56rpx;
					}
				}
			}
		}
	}

	.body_div {
		padding: 72rpx 85rpx;
		
		.modal_msg {
			font-size: 35rpx;
			color: #FEF3C4;
			text-align: center;
			font-family: 'fonts_title';
		}
		
		.modal_button {
			margin-top: 47rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
		
			image {
				width: 200rpx;
			}
		}
	}

	.font-null {
		font-size: 28rpx;
		color: #425357;
		text-align: center;
		line-height: 40rpx;
		padding: 0rpx 60rpx;
		font-family: 'fonts_title';
	}
	.null_view{ 
		padding-bottom:340rpx;
		.null{
			margin-top:0rpx !important;
			padding-top:250rpx;
		}
	}
	.font-but {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 86rpx;

		image {
			width: 346rpx;
		}
	}
	.model_body{
		// background-color:#1E1E1E;
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
		.introduce {
			padding: 50rpx 40rpx 48rpx 40rpx;
			font-size: 32rpx;
			line-height: 38rpx;
			color: #FBE8AF;
			text-align:center;
			>view{
				margin-bottom:20rpx;
				font-family: 'fonts_title';
			}
			.but{
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top:40rpx;
				image{
					width:210rpx;
				}
			}
		}
		.msg {
			color: #F9F9F9;
			font-size: 26rpx;
			padding: 0rpx 40rpx 28rpx 40rpx;
		}
		.model {
			font-family: 'fonts';
		}
	}
	.colse{
		image {
			position: absolute;
			right: -10rpx;
			top: -10rpx;
			width: 48rpx;
			height: 48rpx;
		}
	}
	.fixed_right{
		position:fixed;
		right:20rpx;
		top:1200rpx;
		image{
			width:180rpx;
		}
	}
</style>
