<template>
	<view class="">
		<view class="head">
			<view class="back" @click="custom">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/f78780886762e4b1a251830950da7d6a_76x58.png" mode="widthFix"></image>
			</view>
			<view class="title">
				收益分成明细
			</view>
		<!-- 	<u-tabs name="cate_name" count="cate_count" font-size="35" active-color="#1FEDF0"
				inactive-color="var(--secondary-front-color)" :show-bar="false" :list="tabList"
				:is-scroll="false" bar-height="4" height="88" :current="current" style="background:none"
				@change="change"  bar-width="40"></u-tabs> -->
		</view>
		<view class="content">
			<view class="model">
				
			</view>
			<view class="ask_buy_view_succeed" v-show="current===0">
				<view class="li" v-for="(item,index) in askBuySucceedList" >
					<view class="shop_cart">
						<view class="img">
							<image :src="item.cover" mode="aspectFill"></image>
						</view>
						<view class="info">
							<view class="title oneOver">
								{{item.title}}
							</view>
							<view class="price_num"> 
								<view class="item">
									<view class="active">
										买入金额：￥{{item.buyPrice}}
									</view>
								</view>
								<view class="item"> 
									<view class="active">
										买入时间：{{item.buyTime}}
									</view>
								</view>
							</view>
							<view class="price_num">
								<view class="item">
									<view class="active">
										卖出金额：￥{{item.sellPrice}}
									</view>
								</view>
								<view class="item"> 
									<view class="active">
										卖出时间：	{{item.sellTime}}
									</view>
								</view>
							</view>
							<view class="msg">
								<view class="">师父收益: ￥{{item.teacherIncome}}</view>
								<!-- <view class="">求购发起时间: {{item.dutyTime.split(".000")[0]}}</view> -->
							</view>
						</view>
					</view>
				</view>
				<view v-if="askBuySucceedList==''">
					<u-empty class="null"
						src="https://cdn-lingjing.nftcn.com.cn/image/20230531/d07d99e678612ef41ec4d1f109ddda52_400x400.png"
						text="暂无收益分成明细" mode="list" margin-top="340"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		data() {
			return {
				tabList: [{
					cate_name: '收益分成明细'
				}],
				current: 0,
				askBuyList:[],
				askBuySucceedList:[],
				pageNum:1,
				show:false,
				titleObject: {
					"background-color": "#1E1E1E",
					color: "#FFFFFF",
					"padding-top": "84rpx",
				},
				bgObject: {
					"background-color": "#1E1E1E",
					color: "#FFFFFF",
				},
			};
		},
		onLoad(options) {
			this.teacherStudentId=options.teacherStudentId
			this.getListSucceed()
		},
		onReachBottom() {
			
		},
		computed: {
			
		},
		methods: {
			//点击icon返回上一页
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			async getListSucceed() {
				let res = await this.$api.java_teacherIncomeList({
					teacherStudentId:this.teacherStudentId,
					pageNum:this.pageNum,
					pageSize:15
				});
				if (res.status.code == 0) {
					res.result.list.forEach((item)=>{
						console.log(item)
						this.askBuySucceedList.push(item)
					})
					this.pageNum++
					console.log(res)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			revocation(item){
				this.show=true
				this.dutyId=item.dutyId
			},
			async targetCancel() {
				let res = await this.$api.java_targetCancel({
					dutyId:this.dutyId
				});
				if (res.status.code == 0) {
					this.show=false
					this.pageNum=1
					this.askBuyList=[]
					this.getList()
				} else {
					this.show=false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			change(e){
				this.pageNum=1
				if(e==0){
					this.askBuyList=[]
					this.getList()
				}else{
					this.askBuySucceedList=[]
					this.getListSucceed()
				}
				this.current=e
			}
		},
		components: {
			ButtonBar
		}

	}
</script>

<style lang="scss" scoped>
	// @font-face {
	// 	font-family: 'fonts';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	// }
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/sanjicuti.ttf');
	}
	
	// page {
	// 	font-family: 'fonts';
	// }
	.head{
		display: flex;
		justify-content:center;
		align-items: center;
		position:fixed;
		width:100%;
		background-color:#222F31;
		z-index:99;
		// padding-top:60rpx;
		.back{
			position: absolute;
			left:20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:20rpx;
			/* #endif */
			image{
				width:48rpx;
			}
		}
		.title{
			height:80rpx;
			line-height: 80rpx;
			font-size:34rpx;
			color:#FEF3C4;
			font-family: 'fonts_title';
		}
	}
	.u-tabs {
		width: 50%;
	
		::v-deep {
			.u-tabs {
				margin-top: -20rpx;
			}
	
			.u-tab-item {
				// text-align: start;
				font-size: 36rpx;
				padding-left: 0rpx !important;
			}
		}
	
	}
	.content{
		padding-top:45rpx;
		background-color:#222F31;
		position: relative;
		.model{
			background-image:url(https://cdn-lingjing.nftcn.com.cn/image/20231120/705cb3949326a0a33c6c56fe2f445414_400x309.png);
			background-size: 100% 100%;
			height:580rpx;
			width:100%;
			position: absolute;
			z-index:0;
		}
	}
	.ask_buy_view_succeed{
		padding:50rpx 18rpx;
		z-index:9;
		min-height: 100vh;
		.li{
			background-image:url(https://cdn-lingjing.nftcn.com.cn/image/20231120/b3301d557cc33a40469214f110ef661f_400x154.png);
			background-size: 100% 100%;
			border-radius:16rpx;
			margin-bottom:20rpx;
			height:260rpx;
			.shop_cart{
				display: flex;
				justify-content: flex-start;
				padding:40rpx 20rpx 40rpx 40rpx;
				.img{
					width:163rpx;
					height:163rpx;
					margin-right:30rpx;
					border-radius:8rpx; 
					background-color:#6F7F88;
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					image{
						width:153rpx;
						height:153rpx;
					}
					.left_bottom_icon{
						position: absolute;
						left:-10rpx;
						bottom:-10rpx;
						background-image:url('https://cdn-lingjing.nftcn.com.cn/image/20230605/209d5e3ff15016eb889045c94d5f2b4b_198x210.png');
						background-size:100% 100%;
						background-repeat: no-repeat;
						width:90rpx;
						height:80rpx;
						color:#000;
						padding:38rpx 35rpx 16rpx 12rpx;
						font-size:22rpx;
						text-align: center;
					}
				}
				.info{
					width:450rpx;
					position:relative;
					.title{
						color:#fff;
						font-size:26rpx;
						overflow:hidden;
						width:60%;
					}
					.msg{
						color:#FEF3C4;
						font-size:20rpx;
						>view{
							margin-top:14rpx;
							line-height:30rpx;
						}
					}
					.price_num{
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top:10rpx;
						background-color:#344149;
						.item{
							height:40rpx;
							line-height:40rpx;
							color:#fff;
							font-size:20rpx;
							display: flex;
							justify-content: flex-start;
							.active{
								border-radius:8rpx;
								padding:0rpx 6rpx 0rpx 6rpx;
								text-align: center;
								margin-right:10rpx;
							}
							// margin-right:14rpx;
						}
					}
					.right_repeal{
						position:absolute;
						right:0;
						top:0;
						font-size:20rpx;
						text-align: center;
						border-radius:16rpx;
						padding:6rpx 10rpx;
						background: linear-gradient(90deg, #F4ABF2, #06F9EF);
						min-width:119rpx;
						color:#000;
					}
					.error{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						font-size:20rpx;
						margin-top:10rpx;
						color:#fff;
						image{
							width:40rpx;
							margin-right:10rpx;
						}
					}
				}
				
			}
		}
		
	}
	.modal-btn {
		padding: 60rpx 40rpx;
	
		.mb-cancel,
		.mb-cancel1,
		.mb-confirm {
			width: 240rpx;
			height: 64rpx;
			line-height: 64rpx;
			text-align: center;
		}
	
		.mb-cancel {
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: var(--active-color);
			border-radius: 4rpx;
		}
	
		.mb-cancel1 {
			width: 100%;
			color: #666;
			background-color: #eee;
			border-radius: 4rpx;
		}
	
		.mb-confirm {
			color: var(--main-bg-color);
			box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
			background: var(--primary-button-color);
		}
	}
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>
