<template>
 	<view class="content">
		<view class="model"></view>
		<view class="back" @click="custom">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/f78780886762e4b1a251830950da7d6a_76x58.png"
				mode="widthFix"></image>
		</view>
		<view class="theme">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/9a9b64121092670df73c9f284b770307_302x396.png"
				mode="widthFix"></image>
		</view>
		<view class="tab-bar" @click="check">
			<image class=""  src="https://cdn-lingjing.nftcn.com.cn/image/20231129/3042ea39c93a7e64fa8ec00b67329763_324x68.png" mode="widthFix" v-if="sun==1">
				
			</image>
			<image class="" src="https://cdn-lingjing.nftcn.com.cn/image/20231129/39152d0d398d830c65ba96698e52b7db_324x68.png" mode="widthFix" v-if="sun==2">
				
			</image>
		</view>
		<view class="explain1" v-show="sun==1">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/2d5c7dcff29280c2385777204cb11f04_750x1674.png" mode="widthFix"></image>
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/8adba47971df419531e102fe3ad1e363_750x1286.png" mode="widthFix"></image>
		</view>
		<view class="explain2" v-show="sun==2">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/7306bbd5f9e070edef2a12da817a027e_750x820.png" mode="widthFix"></image>
			<view class="image">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/0a67d3cffd6b7a7bca809b75fdd67529_750x1018.png" mode="widthFix"></image>
				<view class="but1" @click="nav_link"></view>
				<view class="but2" @click="nav_Prenticek"></view>
			</view>
		</view>
 	</view>
 </template>

 <script>
 	export default {
 		data() {
 			return {
				show:false,
				info:[],
				sun:1,
				contract_address:""
 			}  
 		},
 		onLoad(options) {
this.contract_address=options.contract_address
 		},
 		onShow() {

 		},
 		onReachBottom() {
 		
 		},
 		methods: {
 			check(){
				if(this.sun==1){
					this.sun = 2
				}else {
					this.sun = 1
				}
			},
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			nav_link() {
				window.location.href = 'https://www.nftcn.com/link/#/pages/index/mentorshipAgreement'
			},
			nav_Prenticek(){
				this.$Router.push({
					name:"mentorshipPrentice",
					params:{
						contract_address:this.contract_address,
						platform:uni.getStorageSync('is_platform')?uni.getStorageSync('is_platform'):null
					}
				})
			}
 		}
 	}
 </script>

 <style lang="scss" scoped>
 	// @font-face {
 	// 	font-family: 'fonts_title';
 	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newActive.OTF');
 	// }

 	// @font-face {
 	// 	font-family: 'fonts';
 	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
 	// }

 	// page {
 	// 	font-family: 'fonts';
 	// }
	::v-deep .u-model{
		background-color: transparent !important;
	}
	page{
		background-color: #222F31;
	}
 	.content {
		position: relative;
		
		.model {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20231120/71f2ed45295262bbf3ebcceb03a61c4d_400x309.png);
			background-size: 100% 100%;
			height: 580rpx;
			width: 100%;
			position: absolute;
			z-index: 0;
			top: 0;
		}
		
		.back {
			position: absolute;
			left: 20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:30rpx;
			/* #endif */
			z-index: 99;
		
			image {
				width: 54rpx;
			}
		}
		.theme {
			width: 100%;
			height: 580rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		
			image {
				width: 302rpx;
			}
		}
		.tab-bar{
			width:100%;
			height:50rpx;
			margin-top:-30rpx;
			image{
				width:405rpx;
				margin:0 auto;
			}
			
		}
		.explain1,.explain2{
			margin-top:100rpx;
			image{
				width:100%;
			}
		}
		.explain2{
			.image{
				position: relative;
				.but1{
					position: absolute;
					bottom:184rpx;
					left:50rpx;
					width:440rpx;
					height:60rpx;
				}
				.but2{
					position: absolute;
					bottom:90rpx;
					left:50rpx;
					width:440rpx;
					height:60rpx;
				}
				image{
					width:100%;
				}
			}
		}
		.body{ 
			padding:53rpx 42rpx;
			.li{ 
				margin-bottom:30rpx;
				&.margin_bottom{
					margin-top:70rpx;
				}
				.header{
					display: flex;
					justify-content: flex-start;
					align-items: center;
					>view{
						width:100%;
						height:63rpx;
						line-height:63rpx;
						text-align: center;
						font-size:24rpx; 
					}
					.title{
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image-sts/20230626/c7bk37mpefzz73bdjpdjmhnrwdzrjixk_400x37.png);
						background-size: 100% ;
						color:#121212;
						font-weight:600;
						&.max_font{
							font-size:32rpx;
						}
					}
					.right_msg{
						background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/activity/master/right_msg_dow.png);
						background-size: 100% ;
						color:#1EF1EF;
						&.show{
							background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/activity/master/right_msg_up.png);
						}
					}
				}
				.vessel{
					background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/activity/master/msg_bg.png);
					background-size: 100% 100%;
					height:390rpx;
					padding:28rpx 20rpx; 
					overflow: hidden;
					position: relative;
					.open_icon{
						background-color:#000000;
						width:100%;
						margin:0 auto;
						border-bottom-right-radius: 14rpx;
						border-bottom-left-radius:14rpx;
						display: flex;
						justify-content: center;
						padding:15rpx 0rpx 10rpx 0rpx;
						image{
							width:422rpx;
						}
					}
					.main{
						background-color:#000000;
						border-top-right-radius: 14rpx;
						border-top-left-radius:14rpx;
						padding:36rpx;
						height:265rpx;
						overflow: hidden;
						border-bottom:0rpx;
						// margin-bottom:10rpx;
						.text{
							color: #fff;
							line-height:40rpx;
							margin-bottom:33rpx;
							font-size:24rpx;
							&.min_margin{
								margin-bottom:0rpx;
							}
						}
						&.show { 
							height: auto;
						}
					}
					&.show {
						height: auto;
						background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/activity/master/msg_bg_1.png);
					}
					&.show2{
						height: auto;
						background-image: url(https://cdn-lingjing.nftcn.com.cn/h5/activity/master/msg_bg.png);
					}
				}
			}
		}
 	}
 </style>
