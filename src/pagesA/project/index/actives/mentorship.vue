<template>
 	<view class="content">
		<view class="back" @click="custom" v-if="!platform">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/f78780886762e4b1a251830950da7d6a_76x58.png" mode="widthFix"></image>
		</view>
 		<view class="theme">
			<view class="title">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/b26c7b592664da2d7aa2668ccab26cb4_570x126.png" mode="widthFix"></image>
			</view>
			<view class="massge" @click="nav_Prentice">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20231129/d1f639acd69a4dcd9619cddbef83f69c_220x82.png" mode="widthFix"></image>
			</view>
			<view class="tab-bar">
				<view class="li" :class="{'active':index==sun}" @click="check(index)" v-for="(item,index) in ['累计','近三天','近一月']">
					<view v-if="index==0">
						<image v-if="index!=sun" src="https://cdn-lingjing.nftcn.com.cn/image/20231120/c1fb29cfbbc22fb01703eff1c885c0be_230x80.png" mode="widthFix"></image>					
						<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/20231120/247546fc404c15583db7a41d35492c9b_230x80.jpg" mode="widthFix"></image>
					</view>
					<view v-if="index==2">
						<image  v-if="index!=sun" src="https://cdn-lingjing.nftcn.com.cn/image/20231120/6de5444e9e9ef5c49d8ec925c45cdc0d_230x80.png" mode="widthFix"></image>					
						<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/20231120/41592db00be6a296ae700e7161615218_230x80.png" mode="widthFix"></image>
					</view>
					<view v-if="index==1">
						<image v-if="index!=sun" src="https://cdn-lingjing.nftcn.com.cn/image/20231120/02b116581279ea1504f67614415a581e_230x80.png" mode="widthFix"></image>					
						<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/20231120/a7cdbe41c9de6986cc82f2a445b3acb0_230x80.jpg" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
		<view class="body">
			<view class="date">
				最近更新:<text>{{refreshTime}}</text>
			</view>
			<view class="list_view">
				<view class="header">
					<view class="rank">
						排名
					</view>
					<view class="center">
						师父
					</view>
					<view class="num" @click="isModel=true">
						徒弟盈利
						<image src="https://cdn-lingjing.nftcn.com.cn//image/20231129/1e5c8d94cdd5412faae0a5c2f57bf702_30x30.png" mode="widthFix"></image>
					</view>
					<view class="caozuo">
						加入
					</view>
				</view>
				<view class="table">
					<view class="li" v-for="(item,index) in list" >
						<view class="rank">
							<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/11903414d04ef4220b615ecf2aa2d85a_80x80.png" mode="widthFix" v-if="index==0"></image>
							<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/09ecbddb2ab3089afefe91fd45dd80fb_80x80.png" mode="widthFix" v-else-if="index==1"></image>
							<image src="https://cdn-lingjing.nftcn.com.cn/image/20231120/96ba096f2ddfa709a8855c78a7ef7b6b_80x80.png" mode="widthFix" v-else-if="index==2"></image>
							<view v-else>
								{{index+1}}
							</view> 
						</view>
						<view class="center oneOver">
							{{item.teacher}}
						</view>
						<view class="num">
							￥{{item.studentIncome.toLocaleString('en-US')}}
						</view> 
						<view class="caozuo" >
							<image @click="nav_prenticeAccept(item)" v-if="item.isShowBindButton == 1"  src="https://cdn-lingjing.nftcn.com.cn/image/20231120/32467a0cdcaed4be2a01ebe7a93631d3_130x40.png" mode="widthFix"></image> 
						</view>
					</view> 
				</view> 
			</view>
			<view class="footer" @click="nav_master()" v-if="teacher">
				我的师父：{{teacher}}
			</view>
		</view> 
		<u-modal class="model" width="600" v-model="isModel" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="colse" @click="isModel=false">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="introduce">
				<view>根据所有徒弟在拜师期间的实盈+浮盈计算而得。</view>
				<br>
				<view>实盈 = 卖出金额 - 买入金额</view>
				<view>浮盈 = 仓位价值 - 仓位成本</view>
			</view>
		</u-modal>
 	</view>
 </template>

 <script>
 	export default {
 		data() {
 			return {
				sun:1,
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600'
				},
				isModel:false,
				title:"",
				list:[],
				refreshTime:'',
				timeType:2,
				teacher:""
 			}  
 		},
 		onLoad(options) {
			this.getList()
 		},
		onLoad(options) {
			this.options_info=options
			this.contract_address=options.contract_address
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if (platform) {
				uni.setStorageSync('is_platform', platform);
			}
			if (token) {
				uni.setStorageSync('token', token);
			}
			this.getList()
		},
 		onShow() {

 		},
 		methods: {
 			check(index){
				if(index==0){
					this.timeType=index
				}else if(index==1){
					this.timeType=2
				}else if(index==2){
					this.timeType=1
				}
				this.sun=index
				this.list=[]
				this.getList()
			},
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			async getList() {
				let res = await this.$api.java_teacherRanking({
					timeType:this.timeType
				});
				if (res.status.code == 0) {
					this.list=res.result.list
					this.refreshTime=res.result.refreshTime
					this.teacher= res.result.teacher
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_prenticeAccept(item){
				if(item.teacherContractAddress == uni.getStorageInfoSync('contract_address')){
					uni.showToast({
						title: "不能拜自己为师哦",
						icon: 'none',
						duration: 3000
					});
				}else{
					this.$Router.push({
						name:"prenticeAccept",
						params:{
							contractAddress:item.teacherContractAddress,
							type:1
						}
					})
				}
			},
			nav_master(){
				this.$Router.push({
					name:"mentorshipMaster",
					params:{
						platform:this.platform
					}
				})
			},
			nav_Prentice(){
				this.$Router.push({
					name:"mentorshipPrentice",
					params:{
						platform:this.platform
					}
				})
			}
			// async test() {
			// 	let res = await this.$api.({
			   
			// 	});
			// 	if (res.status.code == 0) {
			// 		this.time=res.result.time
			// 	} else {
			// 		uni.showToast({
			// 			title: res.status.msg,
			// 			icon: 'none',
			// 			duration: 3000
			// 		});
			// 	}
			// },
 		}
 	}
 </script>

 <style lang="scss" scoped>
 	@font-face {
 		font-family: 'fonts_title';
 		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/sanjicuti.ttf');
 	}

 	// @font-face {
 	// 	font-family: 'fonts';
 	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
 	// }

 	// page {
 	// 	font-family: 'fonts';
 	// }
 	.content {
		.back{
			position: absolute;
			left:20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:30rpx;
			/* #endif */
			z-index:99;
			image{
				width:48rpx;
			}
		}
 		.theme {
 			width: 100%;
 			height: 580rpx;
 			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20231120/d06ee4ac2b929ad327cdac0c7560c1a2_750x580.png);
 			background-size: 100% ;
 			position: relative;
			background-color: #222F31;
			.tab-bar{
				width:100%;
				height:68rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-radius:30rpx;
				position: absolute;
				bottom:10rpx;
				left:0;
				padding:0rpx 35rpx;
				.li{
					width:33.33%;
					height:68rpx;
					text-align: center;
					color:#11F5EF;
					font-size:32rpx;
					line-height: 68rpx;
					image{
						width:100%;
					}
					&.active{ 
						margin-bottom:30rpx;
						width:34.33%;
						// overflow: hidden; 
					} 
				}
			}
			.title{
				width:100%;
				image{
					width:570rpx;
				}
				display:flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top:213rpx;
			}
			.massge{
				width:100%;
				image{
					width:220rpx;
				}
				display:flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top:371rpx;
			}
 		}
		.body{
			padding:20rpx 55rpx 40rpx 55rpx;
			background-color: #222F31;
			.footer{
				position: fixed;
				z-index:99;
				bottom:0rpx;
				left:0;
				right:0;
				margin:0 auto;
				width:720rpx;
				height:100rpx;
				background-image:url(https://cdn-lingjing.nftcn.com.cn/image/20231120/c86f00faaa07771f9849f9db7828a5fc_702x136.png);
				background-size: 100% 100%;
				text-align: center;
				line-height:90rpx;
				font-size:30rpx;
				color:#FEF3C4;
				font-family:"fonts_title";
				background-color:#222F31;
			}
			.date{
				width:250rpx;
				height:54rpx;
				font-size:22rpx;
				color:#4F5C5D;
				margin:0rpx auto 20rpx auto;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20231120/436891e6500421778097eb73b84ea4d3_250x53.png);
				background-size:100% 100%;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.list_view{
				background-image:url(https://cdn-lingjing.nftcn.com.cn/image/20231120/9cb700111161c4c1ef6f4ccea08e3d9e_642x1063.png);
				background-size: 100% 100%;
				min-height:900rpx;
				.header{
					color:#E3CA89;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					text-align: center;
					padding:40rpx 0rpx;
					font-size:30rpx; 
					font-family:'fonts_title';
					.rank{
						height:42rpx;
						line-height: 42rpx;
						width:188rpx;
					}
					.center{
						width:188rpx;
						height:42rpx;
						line-height: 42rpx;
					}
					.caozuo{
						width:188rpx;
						height:42rpx;
						line-height: 42rpx;
					}
					.num{
						width:261rpx;
						height:42rpx;
						line-height: 42rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						image{
							width:24rpx;
							margin-left:6rpx;
						}
					}
				}
				.table{
					padding-bottom:100rpx;
					overflow:auto;
					height:800rpx;
					.li{
						color:#FFF5D2; 
						display: flex;
						justify-content: flex-start;
						align-items: center;
						text-align: center;
						height:80rpx;
						font-size:24rpx;
						
						&.bg{
							background-image: url('https://cdn-lingjing.nftcn.com.cn/image-sts/20230626/i7rjknshj6258em53iryn7fzmfif53fn_400x37.png');
							background-size:100% 100%;
						}
						.rank{
							height:42rpx;
							line-height: 42rpx;
							width:188rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							image{
								width:58rpx;
							}
						}
						.center{
							width:188rpx;
							height:42rpx;
							line-height: 42rpx;
							padding-left:10rpx;
						}
						.caozuo{
							width:188rpx;
							height:42rpx;
							line-height: 42rpx;
							padding-left:10rpx;
							image{
								width:130rpx;
							}
						}
						.num{
							width:261rpx;
							height:42rpx;
							line-height: 42rpx;
						}
					}
				}
			}
		}
 	}
	.model {
		// font-family: 'fonts';
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
	
		.msg {
			color: #F9F9F9;
			font-size: 26rpx;
			padding: 0rpx 40rpx 28rpx 40rpx;
		}
	}
	
	.input-box {
		border-bottom: 1rpx solid #282828;
		padding-bottom: 20rpx;
	}
	
	.introduce {
		
		padding: 40rpx 40rpx 48rpx 40rpx;
		font-size: 28rpx;
		line-height: 38rpx;
		color: #9F9F9F;
		>view{
			margin-bottom:10rpx;
		}
	}
 </style>
