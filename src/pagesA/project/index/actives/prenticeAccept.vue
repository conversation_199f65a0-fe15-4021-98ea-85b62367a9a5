<template>
	<view class="content">
		<view class="model">
			
		</view>
		<view class="back" @click="custom">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/f78780886762e4b1a251830950da7d6a_76x58.png" mode="widthFix"></image>
		</view>
		<view class="theme">
			<view class="name" v-if="type==1">
				加入<span>{{info.nickname}}</span> 的师门
			</view>
			<view class="name" v-else>
				来自<span>{{info.nickname}}</span>的邀请
			</view>
			<view class="icon">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/f0695cee0c2bcc526917335dd95d4c5d_209x29.png" mode="widthFix"></image>
			</view>
		</view>
		<view class="button" @click="checkSubmit()">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/5828042a33a5d21215ef8b24335d2ae7_371x123.png" mode="widthFix"></image>
		</view>
		<view class="xieyi">
			<u-image mode="widthFix" width="32rpx" @click="isAgree = !isAgree" 
				:src="`../../../../static/imgs/activity/${isAgree?'checked':'check'}.png`">
			</u-image>
			<view class="msg">
				我已同意
				<text @click="nav_link('绑定师徒关系服务协议',1)">《绑定师徒关系服务协议》</text>
				</text>
			</view>
		</view>
		<view class="guize">
			<view class="li">
				1. 绑定邀请关系，意味着您同意，在与该师父绑定关系时期内买到/合成的藏品，在获得收益时，将会从您的收益中自动分配2%的收益给您的师父。
			</view>
			<view class="li">
				2.如果您对您的师傅不认可，您可在<text>锁定期7天结束后</text>单方面随时解除师徒关系。解除师徒关系后，您买到/合成的藏品带来的收益便于前任师傅无关。解除师徒关系后，仍可与其他师傅建立师徒关系。 一位徒弟，不可同时拥有两位师傅。
			</view>
			<view class="li">
				3. Bigverse不会给您推荐师父，您可以通过您自己的社交关系，如好友、社群、抖音等，寻找师父。Bigverse提醒您，找一个有丰富经验和成功战绩的师父，更容易快速入门。
			</view>
			<view class="li">
				4. 建立师徒关系的操作方式：点击您师父的专属链接， 点击“拜他为师”。您可在“我的师徒关系”中看到绑定关系及进行解绑。
			</view>
		</view>
		<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
			email="333" :mode=mode @pay="password" @createSuccess="createSuccess" />
		<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false"
			border-radius="0">
			<view class="BankVerifyBody">
				<view class="head_title_y">
					<view class="right" @click="isPassword=false">
						<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
					</view>
					请先设置支付密码
				</view>
				<view class="msg_y">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="footer_y" @click="isPassword=false">
					<button>
						取消
					</button>
					<button class="active" @click="SetPayPassword()">
						去设置
					</button>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import antiShake from "@/common/public.js"
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		data() {
			return {
				isAgree: false,
				contractAddress:'',
				info:"",
				isPasswordImport: false,
				passwordTitle: "确认身份",
				passwordMsg: "请输入支付密码以确认是本人",
				mode: "pay",
				balance: 0,
				isPassword:false,
			}
		},
		onLoad(options) {
			this.contractAddress=options.contractAddress
			this.type = options.type
			this.getInfo()
		},
		onShow() {
			
		},
		onReachBottom() {
			// if (this.isFooter) {
			// 	if (this.isRequest == false) {
			// 		this.getList()
			// 	} else {
			// 		console.log("请求超时，已经拦截")
			// 	}
			// } else {
			// 	this.loadStatus = 'nomore';
			// 	console.log("已经到底了")
			// }
		},
		methods: {
			async getInfo() {
				let res = await this.$api.java_bindTeacherShow({
					teacherContractAddress:this.contractAddress
				});
				if (res.status.code == 0) {
					this.info = res.result
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(item) {
				window.location.href = 'https://www.nftcn.com/link/#/pages/index/mentorshipAgreement'
			},
			end(item) {
				item.onlineStatus = 1
			},
			custom() {
				const pages = getCurrentPages();
				if (this.isActive) {
					history.back();
				} else if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall",
					});
				} else {
					this.$Router.back();
				}
			},
			check(type) {
				if (type == 1) {
					this.isMaster = false
					this.isPrentice = true
				} else {
					this.isMaster = true
					this.isPrentice = false
				}
			},
			open(item) {
				this.isRelieve = true
			},
			checkSubmit: antiShake._debounce(function(){
				console.log(this.isAgree)
				if(!this.isAgree){
					uni.showToast({
						title: '请先同意《绑定师徒关系服务协议》',
						icon: 'none',
						duration: 3000
					});
				}else if(this.info.isLogin==0){
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(()=> {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				}else if(this.info.isSetTradePassword==0){
					this.isPassword=true
				}else{
					this.isPasswordImport = true
				}
			}, 300),
			SetPayPassword() {
				this.mode = "set"
				this.isPasswordImport = true
			},
			password(e) {
				console.log(e)
				this.isPasswordImport = false
				this.bind_teacher(e)
			},
			createSuccess(psw) {
				console.log(psw)
				this.isPasswordImport = false
				this.bind_teacher(psw)
			},
			async bind_teacher(password) {
				let res = await this.$api.java_bindTeacher({
					teacherContractAddress:this.contractAddress,
					tradePassword:password
				});
				if (res.status.code == 0) {
					uni.showToast({
						title: "师徒关系绑定成功",
						icon: 'none',
						duration: 3000
					});
					setTimeout(()=> {
						this.$Router.push({
							name: "mentorship",
							params:{
								platform:uni.getStorageSync('is_platform')?uni.getStorageSync('is_platform'):null
							}
						});
					}, 2100);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		},
		components: {
			payPopup
		}
	}
</script>

<style lang="scss" scoped>
	page{
		background-color:#222F31;
	}
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/sanjicuti.ttf');
	}
	.content {
		position: relative;
		padding-top:100rpx;
		.model{ 
			background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/71f2ed45295262bbf3ebcceb03a61c4d_400x309.png);
			background-size: 100% 100%;
			height:580rpx;
			width:100%;
			position: absolute;
			z-index:0;
			top:0;
		}
		.back{
			position: absolute;
			left:20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:30rpx;
			/* #endif */
			z-index:99;
			image{
				width:54rpx;
			} 
		}
		.theme {
			height: 258rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/********/320deaa6008d22814770445db3fbb3a4_691x258.png);
			background-size: 100%;
			position: relative;
			width:690rpx;
			margin:0 auto;
			.name {
				font-size: 42rpx;
				font-family: 'fonts_title';
				font-weight: 600;
				color: #FEF3C4;
				position: absolute;
				top: 105rpx;
				left: 0;
				right: 0;
				margin: 0 auto;
				text-align: center;
				padding: 0rpx 30rpx;
				span{
					font-family:"";
					margin:0rpx 10rpx;
				}
			}
			.icon{
				width:200rpx;
				position: absolute;
				top: 200rpx;
				left: 0;
				right: 0;
				margin: 0 auto;
				image{
					width:100%;
				}
			}
		}

		.button {
			margin-top: 60rpx;
			display: flex;
			justify-content: center;

			image {
				width:400rpx;
			}
		}

		.xieyi {
			display: flex;
			justify-content: center;
			padding: 0rpx 50rpx;
			margin-top:20rpx;
			.u-image {
				margin-right: 10rpx;
				margin-top: 8rpx;
			}

			.msg {
				font-size: 24rpx;
				color: #888888;
				text-align: left;
				line-height: 40rpx;
				font-family: 'fonts_title';
				text {
					color:#FEF3C4;
				}
			}
		}
		.guize{
			width:641rpx;
			background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/28e987e53109d612b53f2677f02971fb_400x767.png);
			background-size: 100% 100%;
			margin: 0rpx auto;
			padding:48rpx 35rpx;
			margin-top:40rpx;
			.li{
				line-height:52rpx;
				
				font-size:30rpx;
				color:#FFF2C4;
				line-height:40rpx;
				margin-bottom:40rpx;
				font-family: 'fonts_title';
				text{
					color: #C43E22;
					font-weight:600;
				}
			}
		}
	}
	.BankVerifyBody {
		padding: 42rpx;
	
		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
			color: var(--message-box-point-color);
	
			.right {
				position: absolute;
				right: 40rpx;
				top: 66rpx;
	
				image {
					width: 30rpx;
				}
			}
		}
	
	
		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: var(--message-box-point-color);
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}
	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;
		color: var(--message-box-point-color);
	
		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;
	
			image {
				width: 30rpx;
			}
		}
	}
	
	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;
	
		button {
			margin: 0rpx;
			width: 238rpx;
			height: 64rpx;
			width: 240rpx;
			line-height: 64rpx;
			text-align: center;
			background-color: #999999;
			border-radius: 0rpx;
			font-size: 30rpx;
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: var(--active-color);
			border-radius: 0rpx;
	
			&.active {
				color: var(--main-bg-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				background: var(--primary-button-color);
				color: #121212;
				border: 0rpx;
			}
		}
	}
	
	.msg_y {
		font-size: 28rpx;
		color: #999999;
		line-height: 40rpx;
	}
</style>
