<template>
	<view>
		<u-navbar v-if="!isApp" back-icon-color="#fff" title-color="#fff"
			title-size="32" :title-bold="true" :immersive="true" title="活动" :border-bottom="false"
			:background="{backgroundColor: '#35333E'}" :custom-back="custom">
		</u-navbar>
		<view style="height:180rpx;" v-if="!isApp"></view>
		<view style="text-align: center;color:#fff;padding:20rpx 0rpx;font-weight:600;" v-show="isApp">
			活动
		</view>
		<view class="content" :class="{'padding':!isApp}">
			<view class="list" v-for="(item, index) in list" :key="index"
				:class="[item.onlineStatus==1?'nowImg':'',item.onlineStatus==2?'overImg':'',item.onlineStatus==0?'beginImg':'']">
				<view class="img" @click="nav_link(item)">
					<image :src="item.activityNewBannerImage" mode="aspectFill"></image>
				</view>
				<view class="tit">{{item.title}}</view>
				<view class="btn">
					<view>
						<view style="margin-bottom: 7rpx;">开始时间:{{item.startTime}}</view>
						<view>结束时间:{{item.endTime}}</view>
					</view>
					<view @click="nav_link(item)">
						<image src="@/static/imgs/public/noOut.png" mode="widthFix" v-if="item.onlineStatus==0"></image>
						<image src="@/static/imgs/public/goIn.png" mode="widthFix" v-else></image>
					</view>
				</view>
				<view class="gif_view" v-show="item.onlineStatus==1">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20240508/2257bec4fd2d75f4a9aff7b0d5328c5e_94x40.gif" mode="widthFix" ></image>
				</view>
			</view>
		</view>
	</view>
	
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				timestamp: 386400,
				currentTime: '',
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				pageNum: 1,
				platform: '',
				isApp: false,
				token: '',
				contract_address: ''
			}
		},
		onLoad(options) {
			this.getList()
			this.currentTime = Date.parse(new Date())
			console.log(this.currentTime)
			const {
				platform,
				token,
				contract_address
			} = options;
			this.platform = platform
			this.token = token
			this.contract_address = contract_address
			if (platform) {
				this.isApp = true
			}
		},
		onShow() {

		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				this.loadStatus = 'nomore';
				console.log("已经到底了")
			}
		},
		methods: {
			async getList() {
				this.isLoadding = true
				this.isRequest = true
				let res = await this.$api.java_activityNewActivityPageList({
					pageNum: this.pageNum,
					pageSize: 20
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							if (item.onlineStatus == 0) { //即将开始 增加倒计时字段
								item.timestamp = ((new Date(item.startTime).getTime() - this.currentTime) /
									1000 + 1000)
							}
							this.list.push(item)
						})
						console.log(this.list)
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(item) {
				console.log(item.link.indexOf('?') != -1)
				if (this.token) {
					if (item.link.indexOf('?') != -1) {
						window.location.href =
							`${item.link}&platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					} else {
						window.location.href =
							`${item.link}?platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					}
				} else {
					// #ifdef APP
					this.$Router.push({
						name: "webView",
						params: {
							url: item.link,
						}
					})
					// #endif
					// #ifdef H5
					window.location.href = item.link
					// #endif
					
				}
			},
			end(item) {
				item.onlineStatus = 1
			},
			custom() {
				const pages = getCurrentPages();
				if (this.isActive) {
					history.back();
				} else if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall",
					});
				} else {
					this.$Router.back();
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		width: 100%;
		padding-top:20rpx;
	}

	.nowImg {
		background: url('@/static/imgs/public/nowImg.png') no-repeat;
	}

	.overImg {
		background: url('@/static/imgs/public/overImg.png') no-repeat;
	}

	.beginImg {
		background: url('@/static/imgs/public/beginImg.png') no-repeat;
	}

	.list {
		width: 678rpx;
		height: 520rpx;
		/* #ifdef APP */
		padding-top: 88rpx;
		/* #endif */
		/* #ifdef H5 */
		padding-top: 68rpx;
		/* #endif */
		box-sizing: border-box;
		background-size: cover;
		margin: 0 auto 40rpx auto;
		position: relative;
		.gif_view{
			position:absolute;
			top:15rpx;
			left:180rpx;
			image{
				width:70rpx;
			}
		}
		.img {
			width: 100%;
			height: 302rpx;
			margin-bottom: 12rpx;
			background: #fff;

			>image {
				width: 100%;
				height: 100%;
			}
		}

		.tit {
			font-weight: 400;
			font-size: 28rpx;
			color: #FFFFFF;
			padding-left: 29rpx;
			margin-bottom: 10rpx;
			box-sizing: border-box;
		}

		.btn {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 29rpx;
			box-sizing: border-box;
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;

			>view:nth-child(2) {
				width: 180rpx;
				height: 60rpx;

				>image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
</style>