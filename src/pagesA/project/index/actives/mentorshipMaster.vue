<template>
	<view class="content">
		<view class="model"></view>
		<view class="back" @click="custom">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/f78780886762e4b1a251830950da7d6a_76x58.png"
				mode="widthFix"></image>
		</view>
		<view class="theme">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/57f6f586bdcdc7f13b086b730d053bab_302x399.png"
				mode="widthFix"></image>
		</view>
		<view class="all_list">
			<view class="masterList" v-if="isMaster==2&&masterList!=''">
				<view class="li" v-for="(item,index) in masterList">
					<view class="flex_start" style="align-items: flex-start;">
						<view class="left_view">
							<view class="img">
								<image :src="item.avatar" mode="aspectFill"></image>
							</view>
						</view>
						<view class="right_view">
							<view class="title_view">
								<view class="title oneOver master">{{item.nickname}}</view>
							</view>
							<view class="introduce">
								<span>拜师时间:</span>{{item.startTime}}
							</view>
							<view class="introduce">
								<span>近7天得收益:</span>￥{{item.teacherIncome7Day}}
							</view>
							<view class="introduce">
								<span>累计分得收益:</span>￥{{item.teacherIncome}}
							</view>
						</view>
					</view>
					<view class="button_footer flex_between_x">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/********/db2b334a5dec88a4135c02d27f8ae143_200x62.png" v-show="item.isBind==1" @click="open(item,0,index)"
							mode="widthFix"></image>
						<!-- //已解除 -->
						<image src="https://cdn-lingjing.nftcn.com.cn/image/********/63b5ce5cc81fd44385fc5304261dc708_200x62.jpg"  v-show="item.isBind==0" 
							mode="widthFix"></image>
							
						<image src="https://cdn-lingjing.nftcn.com.cn/image/********/167ae04cb923f83db213058386b04ff7_200x62.png"
							@click="nav_details({teacherStudentId:item.teacherStudentId})" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view v-if="masterList==''&&isMaster==2">
				<view class="null_view">
				<u-empty class="null"
					src="https://cdn-lingjing.nftcn.com.cn/h5/activity/%E5%9C%86%E8%A7%92%E7%9F%A9%E5%BD%A2%20732%20%E6%8B%B7%E8%B4%9D.png"
					text=" " mode="list" margin-top="340"></u-empty>
				<view class="font-null">您还没有师父，去微信群、APP萌新群、抖音等等找个师父带你起飞吧～</view>
				</view>
			</view>
		</view>
		<view class="fixed_right"  @click="nav_router()" v-if="studentGroupAccount">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/d473c885dc9b0b099e2e7b34a0c4a504_170x145.png" mode="widthFix"></image>
		</view>
		<u-modal v-model="isRelieve" border-radius="0" :show-title="false" :content-style="bgObject"
			:show-confirm-button="false">
			<view class="body_div">
				<view class="modal_msg">确认解除师徒关系？</view>
				<view class="modal_button">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/0e62b0194b6e1dfe17417304bcef00d7_172x66.png"
						@click="isRelieve=false" mode="widthFix"></image>
					<image src="https://cdn-lingjing.nftcn.com.cn/image/********/149f74bea7f0c7de40f9d90afe1b5be5_172x66.png" @click="checkSubmit"
						mode="widthFix"></image>
				</view>
			</view>
		</u-modal>
		<u-modal class="model" width="600" v-model="isShowIntroduce" :show-title="true" :show-confirm-button="false" :content-style="bgObject"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="colse" @click="isShowIntroduce=false">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/********/4f8cd15ae119d3d6c2fc6c72085ba3e6_44x44.png" mode="widthFix"></image>
			</view>
			<view class="model_body">
				<view class="introduce">
					<view>请使用APP进入师门群聊界面</view>
					<view class="but" @click="goDownload">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/********/470740bb383c5253f620785808e77a08_160x62.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="model" width="600" v-model="is_msg" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="model_body">
				<view class="colse" @click="is_msg=false">
					<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
				</view>
				<view class="introduce">
					<view>{{time_msg}}</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import uniCopy from "@/js_sdk/uni-copy.js";
	import antiShake from "@/common/public.js"
	import introducePop from '@/components/public/introducePop.vue';
	export default {
		data() {
			return {
				isPrentice: true,
				isMaster: 2,
				titleObject: {
					'background-color': '#35333E',
					'color': '#FFFFFF'
				},
			bgObject: {
				'background': 'transparent',
				'color': '#FFFFFF',
				'background-image': 'url(https://cdn-lingjing.nftcn.com.cn//image/********/7357bcef6c524779b5af1c8fd4989a22_420x200.png)',
				'background-size': '100% 100%',
				'height': '250rpx'
			},
				isRelieve: false,
				masterList: [],
				prenticeList: [],
				isRequest: false,
				prenticePageNum: 1,
				masterPageNum: 1,
				isFooter: true,
				platform: '',
				sun: 0,
				isTeacher: '',
				isShowIntroduce:false,
				title:'',
				introduce:'请使用APP进入师门群聊界面',
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600',
				},
				is_msg:false,
				time_msg:'',
				info:[],
				idx:'',
				studentGroupAccount:"",
				isRequest2: false,
				isFooter2: true,
				startTime:"",
				options_info:""
			}
		},
		components:{
			introducePop
		},
		onLoad(options) {
			this.options_info=options
			this.contract_address=options.contract_address
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if (token) {
				uni.setStorageSync('token', token);
			}
			this.my_teacher()
			this.teacher_info()
		},
		onShow() {

		},
		onReachBottom() {
			if(this.isMaster==1){
				if (this.isFooter) {
					if (this.isRequest == false) {
						this.getList()
					} else {
						console.log("请求超时，已经拦截")
					}
				} else {
					console.log("已经到底了")
				}
			}else if(this.isMaster==2){
				if (this.isFooter2) {
					if (this.isRequest2 == false) {
						this.my_teacher()
					} else {
						console.log("请求超时，已经拦截")
					}
				} else {
					console.log("已经到底了")
				}
			}
			
		},
		methods: {
			async getList() {
				this.isRequest = true
				let res = await this.$api.java_myStudentList({
					pageNum: this.prenticePageNum,
					pageSize: 10
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.prenticePageNum++
						res.result.list.forEach((item) => {
							this.prenticeList.push(item)
						})
						console.log(this.prenticeList)
					}
				} else if (res.status.code == 1002) {
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async my_teacher() {
				this.isRequest2 = true
				console.log('我的师傅分页：'+this.masterPageNum)
				let res = await this.$api.java_myTeacher({
					pageNum: this.masterPageNum,
					pageSize: 10
				});
				if (res.status.code == 0) {
					this.isRequest2 = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter2 = false
					} else {
						this.masterPageNum++
						res.result.list.forEach((item) => {
							this.masterList.push(item)
						})
						console.log(this.masterList)
					}
				} else if (res.status.code == 1002) {
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(item) {
				window.location.href = item.link
			},
			nav_guize(item) {
				this.$Router.push({
					name: 'mentorshipExplain',
				})
			},
			nav_details(item) {
				this.$Router.push({
					name: 'mentorshipDetails',
					params: {
						teacherStudentId: item.teacherStudentId
					}
				})
			},
			custom() {
				const pages = getCurrentPages();
				if (this.isActive) {
					history.back();
				} else if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall",
					});
				} else {
					this.$Router.back();
				}
			},
			check(type) {
				this.isFooter = true
				this.isRequest = false
				this.isFooter2 = true
				this.isRequest2 = false
				this.isMaster=type
				console.log(type,this.isMaster)
			},
			open(item,type,index) {
				this.teacherStudentId = item.teacherStudentId
				this.isTeacher = type
				this.isRelieve = true
				this.startTime = Date.parse(item.startTime.replace(/\-/g, "/"))/1000
				this.idx=index
			},
			copy(val) {
				let {
					origin
				} = window.location
				let contract_address = uni.getStorageSync('contract_address')?uni.getStorageSync('contract_address'):this.contract_address
				uniCopy({
					content: `${origin}/h5/#/pagesA/project/activity/prenticeAccept?contractAddress=${contract_address}`,
					success: (res) => {
						uni.showToast({
							title: '已复制您的邀请链接，快去粘贴给您的好友吧～',
							icon: "none",
						});
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: "none",
							duration: 3000,
						});
					},
				});
			},
			checkSubmit: antiShake._debounce(function() {
				this.submitRelieve()
			}, 300),
			async submitRelieve() {
				let time = Date.parse(new Date())/1000;
				let startTime = this.startTime
				if((time-startTime)>604800){
					this.isRelieve = false
					let res = await this.$api.java_unbindTeacher({
						teacherStudentId: this.teacherStudentId,
						isTeacher: this.isTeacher
					});
					if (res.status.code == 0) { 
						if(this.isMaster==1){
							uni.showToast({
								title: "已解除师徒关系",
								icon: 'none',
								duration: 3000
							});
							this.prenticeList[this.idx].isBind=0
						}else{
							uni.showToast({
								title: "已与原师父解除师徒关系",
								icon: 'none',
								duration: 3000
							});
							this.masterList[this.idx].isBind=0
						}
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000
						});
					}
				}else{
					let time_msg = this.$u.timeFormat(startTime+604800, 'mm月dd日 hh:MM')
					this.is_msg=true
					this.time_msg=`师徒关系建立少于7天，暂不可解除师徒关系。您可以在${time_msg}后再来。`
					this.isRelieve = false
				}
				
			},
			nav_router() {
				if(this.platform){
					this.$native.groupChatDetail({
						groupID:this.studentGroupAccount,
						judge:1
					})
					if(this.platform == 'ios'){
						
					}else{
						this.$native.groupChatDetail({
							groupId:this.studentGroupAccount,
						})
					}
				}else{
					this.isShowIntroduce=true
				}
			},
			goDownload() {
				this.$Router.push({
					name: "appDownload",
				});
			},
			async teacher_info() {
				let res = await this.$api.java_teacherInfo({});
				if (res.status.code == 0) {
					console.log(res)
					this.info=res.result
					this.studentGroupAccount=res.result.studentGroupAccount
					this.info.teacherIncome=this.info.teacherIncome.toLocaleString('en-US')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	// @font-face {
	// 	font-family: 'fonts_title';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newActive.OTF');
	// }
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/sanjicuti.ttf');
	}
	// @font-face {
	// 	font-family: 'fonts';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	// }

	// page {
	// 	font-family: 'fonts';
	// }

	::v-deep .u-model{
		background-color: transparent !important;
	}
	page{
		background-color: #222F31;
	}
	.content {
		position: relative;
		
		.model {
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/********/71f2ed45295262bbf3ebcceb03a61c4d_400x309.png);
			background-size: 100% 100%;
			height: 580rpx;
			width: 100%;
			position: absolute;
			z-index: 0;
			top: 0;
		}
		
		.back {
			position: absolute;
			left: 20rpx;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:30rpx;
			/* #endif */
			z-index: 99;
		
			image {
				width: 54rpx;
			}
		}
		
		.theme {
			width: 100%;
			height: 580rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		
			image {
				width: 302rpx;
			}
		}

		.all_list {
			
		}

		.prenticeList,
		.masterList {
			width: 100%;
			min-height: 50vh;
			.li {
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/********/4ae692d8a13163335ca06f5c329b857a_750x520.png);
				background-size: 100% 100%;
				width: 100%;
				margin-bottom: 20rpx;
				position: relative;
				height:520rpx;
				padding:120rpx 80rpx;
				.align_items {
					align-items: flex-start !important;
				}
				
				.left_view {
					width: 163rpx;
					height: 163rpx;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					.img{
						width: 163rpx;
						height: 163rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						background-size: 100% 100%;
						background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/d2edec72f39025ccb2bdfe614c8cca7d_172x172.png);
						image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 50%;
						}
					}
					margin-right:30rpx;
				}

				.right_view {
					.title_view {
						color: #FEF3C4;
						font-size: 36rpx;
						position: relative;
						margin-bottom:17rpx;
						font-weight:600;
						.title {
							width: 240rpx;
							font-size:28rpx;
							&.master {
								width: 420rpx;
							}
						}

						.icon {
							width: 185rpx;
							height: 33rpx;
							line-height: 33rpx;
							text-align: center;
							background: linear-gradient(90deg, #F4ABF2, #06F9EF);
							border-radius: 12rpx;
							color: #121212;
							font-size: 22rpx;
							position: absolute;
							right: 0rpx;
							top: 2rpx;
						}
					}

					.introduce {
						border-radius: 4rpx;
						width: 417rpx;
						height: 35rpx;
						font-size: 24rpx;
						line-height: 36rpx;
						color: #FEF3C4;
						margin-bottom:17rpx;
						span{
							color:#000;
							font-family: 'fonts_title';
						}
					}
				}
			}

			.error {
				// color: #0AF7EF;
				// font-size: 22rpx;
				position: absolute;
				top: 230rpx;
				// width: 152rpx;
				// text-align: center;
				width: 150rpx;

				image {
					width: 100%;
				}

			}

			.button_footer {
				align-items: center;
				margin-top: 30rpx;
				padding:0rpx 80rpx;
				image {
					width: 200rpx;
					height: 62rpx;
				}
			}
		}
	}

	.body_div {
		padding: 72rpx 85rpx;

		.modal_msg {
			font-size: 35rpx;
			color: #FEF3C4;
			text-align: center;
			font-family: 'fonts_title';
		}

		.modal_button {
			margin-top: 47rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			image {
				width: 200rpx;
			}
		}
	}

	.font-null {
		font-size: 24rpx;
		color: #909090;
		text-align: center;
		line-height: 32rpx;
		padding: 0rpx 60rpx;
	}
	.null_view{
		padding-bottom:340rpx;
	}
	.font-but {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 86rpx;

		image {
			width: 346rpx;
		}
	}
	.model_body{
		// background-color:#1E1E1E;
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
		.introduce {
			padding: 50rpx 40rpx 48rpx 40rpx;
			font-size: 32rpx;
			line-height: 38rpx;
			color: #FBE8AF;
			text-align:center;
			>view{
				margin-bottom:20rpx;
				font-family: 'fonts_title';
			}
			.but{
				display: flex;
				justify-content: center;
				align-items: center;
				margin-top:40rpx;
				image{
					width:210rpx;
				}
			}
		}
		.msg {
			color: #F9F9F9;
			font-size: 26rpx;
			padding: 0rpx 40rpx 28rpx 40rpx;
		}
		.model {
			font-family: 'fonts';
		}
	}
	.colse{
		image {
			position: absolute;
			right: -10rpx;
			top: -10rpx;
			width: 48rpx;
			height: 48rpx;
		}
	}
	.fixed_right{
		position:fixed;
		right:20rpx;
		top:1200rpx;
		image{
			width:180rpx;
		}
	}
</style>
