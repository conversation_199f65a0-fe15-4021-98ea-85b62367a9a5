<template>
    <view class="main">
        <view class="top" :style="{ 'height': height + 'rpx' }"></view>
        <view class="content padding_lr">
            <view style="display: flex;align-items: center;width: 100%;margin-top: 40rpx;">
                <view class="back" @click="nav_back">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240329/442de86adcfcf12f3454c0b1e126b70e_52x100.png"
                        mode="widthFix"></image>
                </view>
                <view class="search-box">
                    <!-- 左侧放大镜图标 -->
                    <view class="icon-search">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240118/9b03ce9292767ae8eec9722d8eac0ebf_44x44.png"
                            class="icon"></image>
                    </view>
                    <!-- 输入框 -->
                    <input v-model="searchText" @confirm="searchNew" placeholder=""
                        placeholder-class="input-placeholder" class="input-field" />
                    <!-- 右侧关闭图标 -->
                    <view class="icon-close" v-if="searchText" @click="clearSearch">
                        <image
                            src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241218/315d9761c6eae8471d1623876a5d934a_200x200.png"
                            class="icon"></image>

                    </view>
                </view>
            </view>

            <view class="search_lishi" v-if="searchHistory != ''">
                <view class="title">
                    搜索历史
                </view>
                <view class="lishi">
                    <view class="li" v-for="(item, index) in searchHistory">
                        <text @tap="clickItem(item)">{{ item }}</text>
                        <image @tap="delHistory(index)"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240515/a25d76043c6295594b105b546b04850e_200x200.png"
                            mode="widthFix"></image>
                    </view>
                </view>
            </view>

            <view class="tabs_div">
                <view class="left">
                    <!-- <u-tabs name="cate_name" bg-color="" :bar-style="barStyle" :list="tabList" bold
                    inactive-color="rgb(255,255,255,0.3)" :active-item-style="itemStyle" active-color="#fff"
                    :scrollable="false" :current="current" @change="changeTab" font-size="28rpx"></u-tabs> -->
                    <view class="utab">
                        <view @click="changeTab(0)" :class="current == 0 ? 'utabact' : ''">对标物
                        </view>
                        <view @click="changeTab(1)" :class="current == 1 ? 'utabact' : ''">常见问题
                        </view>
                        <view class="utabbar"
                            :style="{ left: current == 0 ? '35rpx' : current == 1 ? '180rpx' : '-2000rpx' }"></view>
                    </view>
                </view>

            </view>

            <view class="list_view_ul" v-if="current == 0">
                <view style="height: 33rpx;"></view>
                <view class="list_viewss" v-for="(item, index) in filteredList" @click="nav_mgus(item)" :key="index">
                    <view class="left_title">
                        <image :src="item.icon" class="icon" />
                        <view class="title">{{ item.title || 'BIT指数' }}</view>
                    </view>
                    <view class="echarts_view">
                        <l-echart class="klink" ref="chartRef"></l-echart>
                    </view>
                    <view class="rose_view">
                        <view class="num">
                            ¥{{ Number(item.lastPrice).toFixed(2) || 0.000 }}
                            <text style="font-size: 16rpx;">/份</text>
                        </view>
                        <view class="percentage">
                            {{ formatCurrency(item.markPrice, item.title) }}
                        </view>
                    </view>
                </view>


                <view class="null_body">
                    <view class="null" v-if="!isLogin">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            您还未登录
                        </view>
                        <view class="nav_login" @tap="nav_login">
                            登录/注册
                        </view>
                    </view>

                    <view class="null" v-if="isLogin && filteredList.length <= 0">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            请搜索关键词查询
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="current == 1" class="question">
                <view v-for="(item, index) in KwdfilteredList" @click="handleAnswerClick(item)" :key="index"
                    class="answer-item" v-if="searchText" v-html="index + 1 + '、' + item">
                </view>
                <view class="null_body">
                    <view class="null" v-if="!isLogin">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            您还未登录
                        </view>
                        <view class="nav_login" @tap="nav_login">
                            登录/注册
                        </view>
                    </view>

                    <view class="null" v-if="isLogin && KwdfilteredList.length <= 0">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            请搜索关键词查询
                        </view>
                    </view>
                </view>
            </view>

            <view v-if="current == 2">
                <text class="title" v-if="filteredList.length > 0">对标物</text>
                <view class="list_view_ul" v-if="filteredList.length > 0">
                    <view style="height: 13rpx;"></view>
                    <view class="list_viewss" v-for="(item, index) in filteredList" @click="nav_mgus(item)"
                        :key="index">
                        <view class="left_title">
                            <image :src="item.icon" class="icon" />
                            <view class="title">{{ item.title || 'BIT指数' }}</view>
                        </view>
                        <view class="echarts_view">
                            <l-echart class="klink" ref="chartRef"></l-echart>
                        </view>
                        <view class="rose_view">
                            <view class="num">
                                ¥{{ Number(item.lastPrice).toFixed(2) || 0.000 }}
                                <text style="font-size: 16rpx;">/份</text>
                            </view>
                            <view class="percentage">
                                {{ formatCurrency(item.markPrice, item.title) }}
                            </view>
                        </view>
                    </view>
                </view>

                <text class="title" v-if="KwdfilteredList.length > 0">常见问题</text>
                <view v-for="(item, index) in KwdfilteredList" @click="handleAnswerClick(item)" :key="index"
                    class="answer-item" v-if="searchText" v-html="index + 1 + '、' + item">
                </view>

                <view class="null_body">
                    <view class="null" v-if="!isLogin">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            您还未登录
                        </view>
                        <view class="nav_login" @tap="nav_login">
                            登录/注册
                        </view>
                    </view>

                    <view class="null" v-if="isLogin && KwdfilteredList.length <= 0 && filteredList.length <= 0">
                        <view class="img">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                                mode="widthFix"></image>
                        </view>
                        <view class="text">
                            请搜索关键词查询
                        </view>
                    </view>
                </view>

            </view>

            <u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
                <div class='sk-wave'></div>
                <view class="text_msg"
                    style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                    跳转登录中...
                </view>
            </u-modal>
        </view>
    </view>
</template>
<script>
import api from '@/common/api/index.js';
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'

name: "Desearch"
export default {
    data() {
        return {
            allList: [],
            questionsList: [
                "如何实名",
                "如何开仓",
                "如何平仓",
                "如何设置止盈止损",
                "BIT指数是如何计算的？",
                "盘口位置下面的“标记价格”代表什么？",
                "盘口中间的BIT指数价格代表什么？",
                "BTC等对标物的价格是否有缩放？",
                "现在bit指数不同杠杆的强平系数是怎么计算？",
                "如何阅读和理解K线图？",
                "平台的行情数据来源是什么？",
                "什么是行情深度？",
                "手续费是如何计算的？",
                "资金费率是什么？如何计算？",
                "强平价是如何计算的？",
                "提现多久能到账？",
                "为什么实际开仓金额比输入金额少？",
                "体验金使用规则",
                "万能金使用规则"
            ],
            globalOption: {
                grid: {
                    borderWidth: 0, // 隐藏网格边界线
                    bottom: '15%', // 增加底部空间，防止标签遮挡
                    top: '25%', // 增加顶部空间，防止标题遮挡
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: [],
                    axisLine: { // 隐藏X轴线
                        show: false
                    },
                    axisTick: { // 隐藏X轴刻度
                        show: false
                    },
                    axisLabel: { // 隐藏X轴标签
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    splitLine: {
                        show: false
                    },
                    axisLine: { // 隐藏Y轴线
                        show: false
                    },
                    axisTick: { // 隐藏Y轴刻度
                        show: false
                    },
                    axisLabel: { // 隐藏Y轴标签
                        show: false
                    }
                },
                series: [{
                    data: [100, 222, 9999, 444, 888, 666],
                    type: 'line',
                    smooth: true,
                    symbol: 'none', // 隐藏折线上的点
                    lineStyle: {
                        color: 'rgba(124, 228, 81, 1)',// 折线颜色
                        width: 1
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [{
                                offset: 0,
                                color: 'rgba(124, 228, 81, 1)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(34, 161, 235, 0)'
                            }
                            ]
                        )
                    },
                }],
            },
            current: 2,
            height: "",
            keyword: "",
            tabList: [],
            seriesList: [],
            show: true,
            pageNum: 1,
            marketTabId: "",
            searchHistory: [],
            isFooter: true, //没有更多了
            isRequest: false, //多次请求频繁拦截
            isLogin: false,
            isLoadding: false,
            searchText: "", // 输入框文本
            bitInof: {
                title: 'BIT指数',
                kline: [],
                aimPrice: "",
                aimRatio: "",
                lastPrice: "",
                icon: "https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png"
            },
        }
    },
    onLoad(options) {
        uni.pageScrollTo({
            scrollTop: 0,
            duration: 0,
        });
        let _this = this
        uni.getSystemInfo({
            success: function (res) {
                console.log('当前平台？？？？？', res.statusBarHeight)
                _this.height = res.statusBarHeight * 2
                console.log(_this.height)
            }
        });
        this.searchHistory = this.getSearchHistory()
        this.userInfo()
        this.makeUpTogther()
        console.log(this.allList, '12312312312');

    },
    onShow() {
        console.log(22222)
    },
    onPullDownRefresh() {
        setTimeout(() => {
            this.pageNum = 1
            this.getList()
            uni.stopPullDownRefresh(); //停止下拉刷新动画
        }, 1000);
    },
    onReachBottom() {
        if (this.isFooter) {
            if (this.isRequest == false) {
                this.getList()
            } else {
                console.log("请求超时，已经拦截")
            }
        } else {
            console.log("已经到底了")
        }
    },
    computed: {
        // 计算属性实现模糊匹配
        filteredList() {
            if (!this.searchText || this.current == 1) return []; // 如果输入为空，不回显
            setTimeout(() => {
                this.allList.forEach((item, index) => {
                    if (item.aimRatio) {
                        item.lastPrice = item.aimPrice
                        item.increaseRatio = item.aimRatio
                    }
                    if (item.lastTradePrice) {
                        item.lastPrice = item.lastTradePrice
                    }
                    if (!item.increaseRatio && item.cover) {
                        item.increaseRatio = 0.00
                    }
                    if (!item.lastPrice && item.cover) {
                        item.lastPrice = 0.00
                    }
                    let isRed = (item.aimRatio !== undefined && item.aimRatio !== null ? item.aimRatio : item.increaseRatio) >= 0;
                    this.init(index, item.kline || item.prices, isRed)
                })
                console.log(this.allList, '所有的12312312');
            }, 100);
            return this.allList.filter(
                (item) =>
                    item.title && item.title.toLowerCase().includes(this.searchText.toLowerCase())
            );
        },
        // KwdfilteredList() {
        //     if (!this.searchText || this.current == 0) return []; // 如果输入为空，不回显
        //     const reg = new RegExp(this.searchText, 'i')
        //     // return this.questionsList.filter(item => reg.test(item))
        //     return this.questionsList.map(item => {
        //         // 对每个问题进行替换，匹配到的部分包裹成 <span class="highlight">...</span>
        //         const highlightedItem = item.replace(reg, match => `<text class="highlight">${match}</text>`);
        //         return highlightedItem;
        //     });
        // }
        KwdfilteredList() {
            if (!this.searchText || this.current === 0) return []; // 如果输入为空，不回显
            const reg = new RegExp(this.searchText, 'i'); // 使用正则表达式，'i' 使其大小写不敏感
            const arr = this.questionsList.filter(item => reg.test(item))

            return arr.map(item => {

                // 对每个问题进行替换，匹配到的部分包裹成 <span class="highlight">...</span>
                const highlightedItem = item.replace(reg, match => `<span class="highlight" style="color:#63EAEE">${match}</span>`);
                console.log(highlightedItem, '过滤的');
                return highlightedItem;
            });
        }
    },
    methods: {
        nav_mgus(e) {
            // if (e.titleCn) {
            // this.$Router.pushTab({
            // name: "mgUs"
            // })
            // } else {
            this.$Router.pushTab({
                name: "contract-BITindex"
            })
            // }

        },
        formatCurrency(amount, title) {
            let initstr = Number(amount)
            // 保证只有2位小数
            let fixedAmount = initstr.toFixed(2);

            // 将整数部分和小数部分分开
            let [integerPart, decimalPart] = fixedAmount.split('.');

            // 使用正则添加千位分隔符
            let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            // 返回格式化后的金额
            if (title == 'BIT指数') {
                return '¥' + formattedIntegerPart + '.' + decimalPart;
            } else {
                return '＄' + formattedIntegerPart + '.' + decimalPart;
            }
        },
        handleAnswerClick(answers) {

            const answer = answers.replace(/<span[^>]*>(.*?)<\/span>/g, '$1');

            // #ifdef H5
            this.$Router.push({
                name: 'answer',
                params: {
                    title: answer
                }
            })
            // #endif


            // #ifdef APP-PLUS
            let link = `${getApp().globalData.url}pagesA/project/helpCenter/answer`
            this.$Router.push({
                name: 'webView',
                params: {
                    url: link,
                    title: answer
                }
            })
            // #endif

        }
        ,
        searchNew() {
            if (this.current == 0) {

            }
        },
        extendArrayWithNull(arr, targetLength) {
            // 确保目标长度大于等于当前数组长度
            const fillLength = Math.max(0, targetLength - arr.length);
            const extendedArray = arr.concat(Array(fillLength).fill(null));
            return extendedArray;
        },
        //初始化k线图，循环加载折线图，通过下标传递过来
        async init(index, globalOption, isRed) {
            let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
            // chart 图表实例不能存在data里
            // 计算最大值和最小值
            const minValue = Math.min(...globalOption);
            const maxValue = Math.max(...globalOption);

            // 设置 y 轴的最小值和最大值
            data.yAxis.min = minValue;
            data.yAxis.max = maxValue;
            if (isRed) {
                data.series[0].lineStyle.color = 'rgba(236, 63, 103, 1)'
                data.series[0].areaStyle.color.colorStops[0].color = 'rgba(215, 57, 94, 1)'
                data.series[0].areaStyle.color.colorStops[1].color = 'rgba(255, 90, 117, 0)'
            }
            data.series[0].data = []
            // 扩展数组并填充 null
            const extendedData = this.extendArrayWithNull(globalOption, 11);
            data.series[0].data = extendedData
            if (index == 99) {
                const lEchart = await this.$refs.chartRefBit.init(echarts);
                lEchart.setOption(data)
            } else {
                const lEchart = await this.$refs.chartRef[index].init(echarts);
                lEchart.setOption(data)
            }
        },
        async getBitKline() {
            // getMutilCoin
            let resIndex = await this.$api.indexs()
            let resAll = await this.$api.getMutilCoin()   // 拿价格
            console.log(resAll.result);
            let coinData2 = resAll.result
            let coinDataIcon = resIndex.result.list
            console.log(coinDataIcon, '1231231231212');

            if (resIndex.status.code === 0) {
                try {
                    const responses = await this.$api.exchangeGetKlineCoin() // 拿k线
                    let kData = responses.result
                    // 整合数据
                    const combinedData = Object.keys(kData).map(coinKey => {
                        const coinInfo = coinDataIcon.find(item => item.contractName === coinKey);
                        if (!coinInfo) return {}; // 如果没有匹配到，返回空对象
                        return {
                            markPrice: coinInfo.markPrice,  // 从 coinDataIcon 获取 markPrice
                            icon: coinInfo.icon,            // 从 coinDataIcon 获取 icon
                            title: coinKey.split('-')[1],
                            aimPrice: coinData2[coinKey].aimPrice,
                            lastPrice: coinData2[coinKey].lastPrice,
                            prices: kData[coinKey],
                            increaseRatio: (Number(coinData2[coinKey].aimPrice) - Number(coinData2[coinKey].lastPrice)) / Number(coinData2[coinKey].lastPrice)
                        };
                    });
                    this.IndexList = combinedData;
                } catch (error) {
                    console.error("请求出错", error);
                }
            }

            let res = await this.$api.exchangeGetKline({});
            if (res.status.code == 0) {
                this.bitInof.kline = res.result
                console.log(this.bitInof.kline, 'bit');

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async getParam() {
            await this.getBitKline()//查询bit指数
            let res = await this.$api.GetExchangeParam({});
            if (res.status.code == 0) {
                let { aimPrice, lastPrice } = res.result
                let aimRatio = ((Number(aimPrice) - Number(lastPrice)) / lastPrice).toFixed(2)
                console.log(aimRatio * 100)
                this.bitInof.aimPrice = res.result.aimPrice
                this.bitInof.aimRatio = aimRatio
                this.bitInof.markPrice = res.result.aimPrice

                let isRed = aimRatio >= 0 && aimRatio != null ? true : false
                console.log(this.bitInof.kline, 'kkkkk');

                // setTimeout(() => {
                //   this.init(99, this.bitInof.kline, isRed)
                // }, 100)
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async makeUpTogther() {
            // await this.getBitKline()
            await this.getParam()
            setTimeout(() => {
                // ...this.usList
                const result = [this.bitInof, ...this.IndexList];
                console.log(result);

                this.allList = result;
                this.allList = this.sortByChange([...this.allList]).reverse();
                setTimeout(() => {
                    this.allList.forEach((item, index) => {
                        if (item.aimRatio) {
                            item.lastPrice = item.aimPrice
                            item.increaseRatio = item.aimRatio
                        }
                        if (item.lastTradePrice) {
                            item.lastPrice = item.lastTradePrice
                        }
                        if (!item.increaseRatio && item.cover) {
                            item.increaseRatio = 0.00
                        }
                        if (!item.lastPrice && item.cover) {
                            item.lastPrice = 0.00
                        }
                        let isRed = (item.aimRatio !== undefined && item.aimRatio !== null ? item.aimRatio : item.increaseRatio) >= 0;
                        this.init(index, item.kline || item.prices, isRed)
                    })
                    console.log(this.allList, '所有的12312312');
                }, 100);
            }, 0);
        },
        // 排序函数：按涨跌幅（aimRatio或increaseRatio）
        sortByChange(arr) {
            return arr.sort((a, b) => {
                const aRatio = a.increaseRatio || 0;
                const bRatio = b.increaseRatio || 0;
                return aRatio - bRatio;
            });
        },

        changeTab(e) {
            if (this.current == e) return
            this.current = e
        },
        clearSearch() {
            this.searchText = ""; // 清空搜索框内容
        },
        change(index) {
            this.current = index
            this.marketTabId = this.tabList[index].value
            this.pageNum = 1
            this.getList()
        },
        search() {
            if (this.keyword) {
                this.seriesList = []
                this.pageNum = 1
                this.getList()
                this.addSearchHistory(this.keyword)
            } else {
                uni.showToast({
                    title: "请输入关键字",
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        // 添加搜索记录
        addSearchHistory(keyword) {
            let history = this.getSearchHistory();

            // 删除已存在的关键词
            history = history.filter(item => item !== keyword);

            // 将新的关键词添加到历史记录的最前面
            history.unshift(keyword);

            // 限制历史记录的数量（例如只保留10条）
            if (history.length > 10) {
                history.pop();
            }

            // 存储到本地
            uni.setStorageSync('searchHistory', history);

            // 更新当前组件的数据
            this.searchHistory = history;
        },
        // 获取搜索历史
        getSearchHistory() {
            const history = uni.getStorageSync('searchHistory') || [];
            return history;
        },
        clear() {
            this.keyword = ""
            this.pageNum = 1
            this.getList()
        },
        nav_details(item) {
            console.log(item.id)
            this.$Router.push({
                name: "webView",
                params: {
                    url: 'http://web-test.nftcn.com/h5/#/pagesA/project/official/detail_art?id=' + item.id
                }
            })
        },
        nav_to(name) {
            this.$Router.push({
                name
            })
        },
        async getList() {
            const {
                status,
                result
            } = await this.$api.pgcSearch({
                keyword: this.keyword,
                pageNum: this.pageNum
            });
            if (status.code == 0) {
                this.isRequest = false
                if (result.list == null || result.list == "") {
                    this.isFooter = false
                } else {
                    this.pageNum++
                    result.list.forEach((item) => {
                        this.seriesList.push(item)
                    })
                }
            } else if (status.code == 1002) {
                this.isLoadding = true
                setTimeout(() => {
                    this.nav_login()
                }, 2000)
            } else {
                uni.showToast({
                    title: status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        nav_seriesList(item) {
            if (item.ctid) {
                this.$Router.push({
                    name: 'seriesList',
                    params: {
                        ctid: item.ctid
                    }
                })
            }
        },
        nav_back() {
            this.$Router.back()
        },
        clickItem(item) {
            this.keyword = item
            this.search()
        },
        //删除指定位置
        removeElementFromArrayWithSplice(array, element) {
            let index = array.indexOf(element);
            if (index > -1) {

            }
            return array;
        },
        // 删除历史记录
        delHistory(index) {
            this.searchHistory.splice(index, 1);
            console.log(this.searchHistory)
            uni.setStorageSync('searchHistory', this.searchHistory);
        },
        async userInfo() {
            let res = await this.$api.userInfo({});
            if (res.status.code == 0) {
                this.isLogin = true
            } else {
                // uni.showToast({
                // 	title: res.status.msg,
                // 	icon: 'none',
                // 	duration: 3000
                // });
            }
        },
        nav_login() {
            this.$Router.push({
                name: 'mainLogin',
            })
        },
    }
}
</script>
<style lang="scss">
$bg-color: #1c1c24; // 页面背景色
$search-box-bg: #2d2d3b; // 搜索框背景色
$placeholder-color: #b5b5be; // placeholder 字体颜色
$text-color: #ffffff; // 输入字体颜色
$icon-size: 20px; // 图标容器大小
$icon-img-size: 25rpx; // 图标图片大小

.title {
    font-family: HarmonyOS Sans SC;
    font-weight: 500;
    font-size: 24rpx;
    line-height: 50rpx;
    color: #FFFFFF;
}

.answer-item {
    font-weight: 400;
    font-size: 24rpx;
    color: #FFFFFF;
    line-height: 50rpx;
}

.highlight {
    color: #63EAEE;
    /* 设置高亮颜色 */
    font-weight: bold;
    /* 设置字体加粗 */
}

.question {
    .title {
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 24rpx;
        line-height: 50rpx;
        color: #FFFFFF;
    }

    .answer-item {
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 50rpx;
    }
}

.list_view_ul {
    min-height: 150rpx;

    .list_viewss {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        background: #2D2B36;
        border-radius: 25rpx;
        margin-bottom: 14rpx;
        padding: 18rpx 25rpx 18rpx 18rpx;

        .left_title {
            width: 190rpx;
            display: flex;
            align-items: center;

            .icon {
                width: 67rpx;
                height: 67rpx;
                border-radius: 50%;
                margin-right: 17rpx;
            }

            .title {
                font-size: 26rpx;
                // margin-bottom: 14rpx;
                font-weight: 400;
            }

            .icon {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: 24rpx;

                .but {
                    color: #fff;
                    background-color: #8D76DB;
                    border-radius: 4rpx;
                    font-size: 22rpx;
                    width: 44rpx;
                    height: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10rpx;
                }

                .msg {
                    color: rgba(255, 255, 255, 0.5);
                }

            }
        }

        .echarts_view {
            width: 175rpx;
            height: 56rpx;
        }

        .trade {
            width: 140rpx;
            height: 50rpx;
            border-radius: 25rpx;
            background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);

            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;

            .body {
                width: 136rpx;
                height: 48rpx;
                background: #2D2B36;
                border-radius: 25rpx;
                display: flex;
                align-items: center;
                overflow: hidden;
                justify-content: center;
                color: #45F5EC;
                overflow: hidden;
                font-family: HarmonyOS Sans SC;
                font-weight: 500;
                font-size: 24rpx;
            }
        }

        .rose_view {
            width: 170rpx;
            display: flex;
            align-items: center;
            flex-direction: column;

            .num {
                font-size: 30rpx;
                font-weight: 600;
                text-align: right;
            }

            .percentage {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                font-size: 24rpx;
                margin-top: 12rpx;

                image {
                    width: 28rpx;
                }

                text {
                    margin-left: 15rpx;
                    color: #7CE451;
                    font-size: 22rpx;

                    &.red {
                        color: #EC3F67;
                    }
                }
            }
        }
    }

    .reachbottom {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 47rpx;

        .line {
            width: 152rpx;
            height: 2rpx;
            background: #46454F;
        }

        .tips {
            margin: 0 24rpx;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #FFFFFF;
            opacity: 0.5;
        }
    }

    .down {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20rpx;

        image {
            width: 44rpx;
        }
    }
}


.utab {
    display: flex;
    font-weight: 400;
    font-size: 28rpx;
    color: #888888;
    position: relative;
    margin: 30rpx 0;

    view {
        &:nth-of-type(1) {
            margin: 0 51rpx 0 28rpx;
        }
    }
}

.utabact {
    font-weight: bold;
    color: #FFFFFF;
}

.utabbar {
    width: 61rpx;
    border-radius: 3rpx;
    height: 6rpx;
    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
    position: absolute;
    bottom: -18rpx;
    // left: 67rpx;
    transition: all 0.5s ease-in-out;
}

.tabs_div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 50rpx 0 34rpx -30rpx;
    // padding: 0rpx 55rpx 0 -40rpx;
    // margin-top: 10rpx;

    .left {
        // margin-left: -50rpx;
        // width: 460rpx;
    }

    .right {
        image {
            width: 36rpx;
            margin-top: 14rpx;
        }
    }
}

.back {
    width: 26rpx;
    margin-right: 20rpx;

    image {
        width: 26rpx;
    }
}

.search-box {
    display: flex;
    align-items: center;
    height: 67rpx;
    width: 643rpx;
    background-color: transparent;
    border-radius: 22px;
    padding: 0 26rpx;
    border: 2rpx solid #A3A2A9;
    box-sizing: border-box;

    .icon-search {
        width: $icon-size;
        height: $icon-size;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
            width: 41rpx;
            height: 41rpx;
        }
    }

    .icon-close {
        width: $icon-size;
        height: $icon-size;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
            width: $icon-img-size;
            height: $icon-img-size;
        }
    }

    .input-field {
        flex: 1;
        height: 100%;
        color: $text-color;
        font-size: 14px;
        padding: 0 10px;

        &::placeholder {
            color: $placeholder-color;
            font-size: 14px;
        }
    }
}


::v-deep .uni-input-input {
    // width: 85% !important;
}

.padding_lr {
    padding: 0rpx 34rpx;
}

.head_bg {
    position: absolute;
    top: 0rpx;
    left: 0rpx;
    width: 1000rpx;
    height: 500rpx;
    z-index: -1;
}

.main {
    flex: 1;
    min-height: 100vh;
}

.head_title {
    height: 170rpx;
    // padding:86rpx 0 0 0;
}

.title_1 {
    color: #141414;
    font-weight: 600;
    font-size: 44rpx;
}

.content {}

.search_view {
    padding-top: 30rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100rpx;
    position: relative;

    .right {
        font-size: 28rpx;
        position: absolute;
        right: 30rpx;
        color: #999999;
    }

    .back {
        width: 26rpx;
        margin-right: 20rpx;

        image {
            width: 26rpx;
        }
    }
}

.search_lishi {
    padding: 0rpx 32rpx;
    margin-top: 37rpx;

    .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #fff;
    }

    .lishi {
        height: 60rpx;
        overflow: auto;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 30rpx;

        .li {
            border-radius: 10rpx;
            border: 1px solid #A6A6A6;
            color: #A6A6A6;
            font-size: 20rpx;
            padding: 0rpx 18rpx;
            height: 44rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20rpx;
            flex: 1 0 auto;
            max-width: max-content;
            /* 或者指定一个最大宽度 */
            position: relative;

            image {
                width: 30rpx;
                position: absolute;
                right: -14rpx;
                top: -14rpx;
            }
        }
    }
}

.tabbar_view {
    margin-top: 0rpx;
}

.list_view {
    width: 638rpx;
    margin-top: 40rpx;
}

.list_li {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #F5F5F5;
    padding: 32rpx;
    border-radius: 24rpx;
    margin-bottom: 40rpx;
}

.left_img {
    margin: 0 40rpx 0 0;
}

.right_font {
    &_title {
        color: #141414;
        font-size: 38rpx;
        font-weight: 600;
    }
}

.sub_title {
    &_text {
        color: $uni-color-gray;
        font-size: $uni-font-size-h4;
        line-height: 44rpx;
    }
}

.time {
    &_text {
        color: $uni-color-gray;
        font-size: $uni-font-size-h4;
        line-height: 44rpx;
    }
}

.collection {
    margin-top: 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: 100rpx;

    .li {
        width: 320rpx;
        height: 450rpx;
        background-color: #25232D;
        margin-bottom: 30rpx;
        border-radius: 30rpx;
        padding: 20rpx;

        .bg {
            position: relative;
            width: 288rpx;
            height: 288rpx;

            .cover {
                position: absolute;
                top: 0rpx;
                left: 0rpx;
                width: 280rpx;
                height: 280rpx;
                border-radius: 30rpx;

                >image {
                    width: 280rpx;
                    height: 280rpx;
                    border-radius: 30rpx;
                }

                .left_bottom_icon {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240104/79eb4cb92625dd836b2749f657f1437d_140x44.png);
                    background-size: 100% 100%;
                    width: 140rpx;
                    height: 44rpx;
                    border-radius: 0px 20rpx 0px 30rpx;
                    text-align: center;
                    font-size: 20rpx;
                    color: #fff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .right_top_icon {
                    position: absolute;
                    right: 0rpx;
                    top: 0rpx;
                    width: 110rpx;
                    height: 44rpx;

                    image {
                        width: 110rpx;
                        height: 44rpx;
                    }
                }

                .tuishi {
                    position: absolute;
                    right: 0rpx;
                    bottom: 0rpx;
                    width: 137rpx;
                    height: 137rpx;

                    image {
                        width: 137rpx;
                        height: 137rpx;
                    }
                }
            }
        }

        .font_view {
            padding: 12rpx 0rpx 24rpx 0rpx;

            .title {
                width: 100%;
                font-size: 24rpx;
                font-weight: 600;
                color: #fff;
            }

            .sub_title {
                font-size: 22rpx;
                color: #FFFFFF;
                margin-top: 10rpx;
            }

            .icon_price {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 10rpx;

                .left_huo {
                    image {
                        width: 22rpx;
                        height: 22rpx;
                    }
                }

                .right_price {
                    color: var(--active-color1);
                    font-weight: 600;
                    font-size: 34rpx;
                    min-width: 50%;
                    text-align: right;

                    span {
                        font-size: 24rpx;
                    }

                    .qi {
                        color: var(--active-color1);
                    }
                }
            }
        }
    }
}

.null_body {
    .null {

        .img {
            display: flex;
            justify-content: center;

            image {
                width: 142rpx;
            }
        }

    }

    .text {
        color: #A6A6A6;
        font-size: 28rpx;
        text-align: center;
        margin-top: 30rpx;
    }

    .nav_login {
        width: 200rpx;
        height: 60rpx;
        background: var(--main-bg-color);
        border-radius: 12rpx;
        border: 2rpx solid #63EAEE;
        color: #63EAEE;
        font-size: 26rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40rpx;
    }

    width:100%;
    height: 40vh;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>