 <template>
 	<view class="body">
		<view class="bg">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20241115/c02b9a5fc578fb5374994d820af276e4_1125x3944.png" mode="widthFix"></image>
		</view>
		<view class="button" @click="download()" v-show="show">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20241115/03962c2196a9ca121a5740e6bb75cdb1_605x104.png" mode="widthFix"></image>
		</view>
		<view class="loadding" v-show="!show">
			<view class="flex" >
				<svg viewBox="25 25 50 50" v-show="showLoading">
				  <circle cx="50" cy="50" r="20"></circle>
				</svg>
			</view>
			<view class="text">
				下载时请勿离开页面(如未自动安装,请手动安装)
			</view>
		</view>
 	</view>
 </template>
 <script>
 	export default {
 		data() {
 			return {
 				show:true,
				showLoading:true
 			}
 		},
 		onLoad(options) {
 			 
 		},
 		methods: {
 			download(){
				this.show= false
				setTimeout(()=>{
					this.showLoading= false
				},5000)
				window.location.href = 'https://cdn-lingjing.nftcn.com.cn/app/ios/Bigverse.mobileconfig'
			}
 		}
 	}
 </script>
 <style lang="scss">
	 .body{
		 position: relative;
		 .bg{
			 width:100%;
			 image{
				 width:100%;
			 }
		 }
		 .button{
			 width:403rpx;
			 position: absolute;
			 top:426rpx;
			 left:0;
			 right:0;
			 margin:0 auto;
			 image{
				 width:100%;
			 }
		 }
		 .loadding{
			 width:600rpx;
			 position: absolute;
			 top:386rpx;
			 left:0;
			 right:0;
			 margin:0 auto;
			 display: block;
			 .flex{
				 display: flex;
				 justify-content: center;
				 height:130rpx;
			 }
			 .text{
				 text-align: center;
				 color:#63EAEE;
				 font-size:26rpx;
				 margin-top: 20rpx;
			 }
		 }
	 }
	 svg {
	   width: 3.75em;
	   transform-origin: center;
	   animation: rotate 2s linear infinite;
	 }
	 
	 circle {
	   fill: none;
	   stroke: #63EAEE;
	   stroke-width: 2;
	   stroke-dasharray: 1, 200;
	   stroke-dashoffset: 0;
	   stroke-linecap: round;
	   animation: dash 1.5s ease-in-out infinite;
	 }
	 
	 @keyframes rotate {
	   100% {
	     transform: rotate(360deg);
	   }
	 }
	 
	 @keyframes dash {
	   0% {
	     stroke-dasharray: 1, 200;
	     stroke-dashoffset: 0;
	   }
	   50% {
	     stroke-dasharray: 90, 200;
	     stroke-dashoffset: -35px;
	   }
	   100% {
	     stroke-dashoffset: -125px;
	   }
	 }
 </style>