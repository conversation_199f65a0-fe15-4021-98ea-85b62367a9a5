<template>
	<view class="player">
		<!-- <view class="msg" >
			我获取的:{{url}}
			<div style="color:red;"> 跳转前的url：{{this.linkStr}}</div>
		</view> -->
		<!-- <u-navbar class="list-nav" title="边玩边赚" style="color: var(--default-color1)"
			back-icon-color="var(--message-box-point-color)" :border-bottom="false" :custom-back="custom"
			:background="{ backgroundColor: 'var(--main-bg-color)' }">
		</u-navbar> -->

		<view class="nav_bar">
			<u-image style="position: relative;"
				src="https://cdn-lingjing.nftcn.com.cn/image/20240514/b0f09172d1ca5fc140429dd9ed4aabb3_750x471.png"
				mode="widthFix"></u-image>
			<view class="nav_title">
				<view class="">
					<u-image @click="back()"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240513/d1ca1e0b91da2db23557ee5b87d985eb_52x100.png"
						mode="widthFix" width="22"></u-image>
				</view>
				<view class="" style="margin-right: 30rpx;">
					玩转B宝
				</view>
				<view class="nar_rankTitle">

				</view>
			</view>
			<view class="nav_userInfo">
				<view class="menu_bar">
					<view class="my_coin">我的B宝</view>
					<view class="play_coin" @click="goGameHistory()">B宝记录</view>
				</view>
			</view>
			<view class="balance_bar">
				<u-image
					src="https://cdn-lingjing.nftcn.com.cn/image/20240513/ce658aac598e8e5393562317864956a8_638x148.png"
					mode="widthFix"></u-image>
				<view class="balance_text">{{balanceNum||0}}</view>
			</view>
			<view class="balance_bar" style="margin-top: 50rpx;">
				<view class="btn_exchange2" @click="show=true">立即兑换</view>
				<view class="btn_exchange3" @click="goPlayGame()">去玩游戏</view>
			</view>
		</view>




		<view class="coin_rule">
			<u-image
				src="https://cdn-lingjing.nftcn.com.cn/image/20240513/7d5abc0676a677de224dc0563180de4f_1500x2475.png"
				mode="widthFix"></u-image>
			<view class="rule_1">
				<view class="p1">什么是B宝</view>
				<view class="p2">B宝是边玩边赚中，所有游戏内可以流通的货币。无论您参与B宝游戏，或者是起飞卡游戏，您都需要消耗一定的B宝，才能与其他玩家一同竞技，赢取更多的B宝或者起飞卡。</view>
			</view>
			<view class="rule_2">
				<view class="p1">我要怎么获得B宝呢</view>
				<view class="p2">您可以在任何B宝游戏中进行游戏来获得更多的B宝，或者在兑换B宝中，使用您的起飞卡进行兑换，其兑换关系为：</view>
				<view class="p3">
					<view class="nav_tabbar_icon1">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/7d4050219b8a3498f458d3cad7f87b9a_100x100.png"
							mode="widthFix"></u-image>
					</view>
					<view class="nav_tabbar_icon2">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/3058a8bdde37d0e3de8b418747f333c9_124x124.png"
							mode="widthFix"></u-image>
					</view>
					&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbspX 1 = &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp X 1
				</view>
			</view>
			<view class="btn_exchange4" @click="show=true">
				立即兑换
			</view>
		</view>
		<u-popup v-model:show="show" class="popup_body_bottom" mode="bottom" border-radius="36">
			<view class="bodyPop_exchange">
				<view class="title">
					<view class="colse" @click="clickShowChange(false)">
						<img src="https://cdn-lingjing.nftcn.com.cn//image/20240219/dc022946fdd0d661192b699cce40ee76_200x200.png"
							alt="" srcset="" />
					</view>
					<view class="popup_icon">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/7d4050219b8a3498f458d3cad7f87b9a_100x100.png"
							mode="widthFix"></u-image>
					</view>
					兑换B宝
				</view>
				<view class="input">
					<input type="number" v-model="batchNum" />
					<view class="add">
						<u-image @click="btn_add()"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/06d79ced878c74f6f8dd2f679a390e07_120x120.png"
							mode="widthFix"></u-image>
					</view>
					<view class="sub">
						<u-image @click="btn_sub()"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/519d839229a861ad0a7842d4174c7116_120x120.png"
							mode="widthFix"></u-image>
					</view>
					<view class="max" @click="btn_max()">全部</view>
					<view class="min" @click="btn_min()">最少</view>
				</view>
				<view class="tips">
					<p class="t1">1张</p>
					<view class="card">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/3058a8bdde37d0e3de8b418747f333c9_124x124.png"
							mode="widthFix"></u-image>
					</view>
					<p class="t2">可以兑换1个</p>
					<view class="coin">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/7d4050219b8a3498f458d3cad7f87b9a_100x100.png"
							mode="widthFix"></u-image>
					</view>
				</view>
				<view class="myCard">
					<p class="t1">我的</p>
					<view class="card">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/3058a8bdde37d0e3de8b418747f333c9_124x124.png"
							mode="widthFix"></u-image>
					</view>
					<p class="t2">{{ myCoinNum }}</p>
				</view>
				<view class="btn">
					<view class="btn_confirm" @click="submitAll()">{{myCoinNum>=batchNum?'确认兑换':'购买并兑换'}}</view>
				</view>
			</view>
		</u-popup>

		<pay-popup :popup-show.sync="isPasswordImport" title="支付密码" order-type="O" @pay="finishPay" ref="payPopup"
			:mode="mode" @createSuccess="createSuccess" />
		<u-popup v-model="isOrderCheck" class="popup_body_bottom" mode="bottom">
			<view class="bodyPop">
				<view class="right_colse" @click="isOrderCheck = false">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240218/82101063938685dbb1b68833f31c70a7_80x80.png"
						alt="" srcset="" mode="widthFix"></image>
				</view>
				<view class="head2">
					<view class="title_bg">
						<view class="icon"></view>
						确认支付
					</view>
					<view class="sub_title">
						<text>￥{{modeInfo.price}}</text>
					</view>
					<view class="countdown">
						支付剩余时间：
						<text>
							<u-count-down :timestamp="modeInfo.countdown" :show-hours="false"
								bg-color="var(--dialog-bg-color)" color="var(--main-front-color)"
								separator-color="var(--main-front-color)" font-size="24">
							</u-count-down>
						</text>
					</view>
				</view>
				<view class="body_view">
					<view class="padding40">
						<view class="item">
							<view class="name">买入份数</view>
							<view class="valUue">{{modeInfo.num}}份</view>
						</view>
						<!-- <view class="item">
							<view class="name">数量</view>
							<view class="valUue">444份</view>
						</view> -->
					</view>
					<view class="but" @click="submitPay()">
						支付
					</view>
				</view>
			</view>
		</u-popup>
		<u-modal v-model="isBalance" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg2">
					<view class="icon"></view>
					余额不足
				</view>
				<view class="modal-content">
					非常抱歉，您的当前余额不足以支付本次订单，
					请您充值余额或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isBalance = false">取消</view>
					<view class="img_reasale" @click="nav_pay()">去充值</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isPassword" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg2">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content">
					<p>请先设置支付密码</p>
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isPassword = false">取消</view>
					<view class="img_reasale" @click="SetPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<view class="loading_list">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						{{loaddingMsg}}
					</view>
				</view>
			</view>
		</u-modal>

	</view>
</template>

<script>
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		data() {
			return {
				isPasswordText: false,
				tabs: [{
					name: '赢B宝',
					value: 2,
				}, {
					name: '赚起飞卡',
					value: 1,
				}, ],
				current: 0,
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				},
				balanceNum: 0,
				gameList: [
					// {
					// 	name:"幸运刮刮乐",
					// 	subName:"幸运刮刮乐",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240407/f12b632d57f7aedd8d49a154e0354e31_1920x859.jpg",
					// 	path:"/bvgame/scartchcard/",
					// },{
					// 	name:"幸运翻翻乐",
					// 	subName:"幸运翻翻乐",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240328/a11114063fdffc898e9a5126e200a924_1005x450.jpg",
					// 	path:"/bvgame/luckyflip/",
					// },{
					// 	name:"第二十楼",
					// 	subName:"第二十楼",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240320/e3dd553810730fd387fcff7c620b7c0e_1005x450.jpg",
					// 	path:"/bvgame/twentyfloor/",
					// },{
					// 	name:"向云端",
					// 	subName:"向云端",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240222/96b2709a728d1fc2771700f3cd224058_1005x450.jpg",
					// 	path:"/bvgame/rqqts/",
					// },{
					// 	name:"奔跑吧小兔子",
					// 	subName:"奔跑吧小兔子",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240315/c7fce769c7221c998a3a6c21c1ebaef6_1005x450.jpg",
					// 	path:"/bvgame/rabbit/",
					// },{
					// 	name:"欢乐钓鱼",
					// 	subName:"欢乐钓鱼",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240315/b8fe0232cad8ea7c310bbd18df4d00a9_1005x450.jpg",
					// 	path:"/bvgame/fish/",
					// },{
					// 	name:"口红插西瓜",
					// 	subName:"口红插西瓜",
					// 	cover:"https://cdn-lingjing.nftcn.com.cn/image/20240315/394430b36104154abd4cada4f3f54b3e_1005x450.jpg",
					// 	path:"/bvgame/lipstick/",
					// },
				],
				platform: '',
				isApp: false,
				token: '',
				contract_address: '',
				url: "",
				linkStr: "",
				pageNum: 1,
				isFooter: true,
				isRequest: false,
				orderField: 2,
				show: false,
				batchNum: 1,
				myCoinNum: 0,
				myBalance: 0,
				isPayPassword: false,
				isShowMsg: false,
				isPasswordImport: false,
				isOrderCheck: false,
				ctid: "cs14729210152792764673170846406530",
				orderNoList: "",
				modeInfo: {
					countdown: "",
					price: ""
				},
				firstMyCoinNum: "",
				mode: '',
				orderNo: "",
				isBalance: false,
				isPassword: false,
				isLoadding: false,
				loaddingMsg: "支付中...",
				appUrl: "",
				iversion: ""
			}
		},
		components: {
			payPopup,
		},
		onLoad(options) {
			const {
				platform,
				token,
				contract_address
			} = options;
			this.platform = platform
			this.token = token
			this.contract_address = contract_address
			this.appUrl = getApp().globalData.urlZf
			if (token) {
				uni.setStorageSync('token', token)
			}
			this.getPlay()
			this.get_version()
			if (this.platform) {
				this.myUni.webView.navigateTo({
					url: `/pagesA/project/index/player/player`
				});
			}
		},
		onShow() {
			this.getBalance()
			this.flyCardCount(0)
			this.getBuyUnPaidQuery()
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getPlay()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			goGameHistory() {
				this.$Router.push({
					name: "gameHistory",
				})
			},
			goPlayGame() {
				this.$Router.push({
					name: "player",
				})
			},
			SetPayPassword() {
				this.isPassword = false
				this.mode = 'set'
				this.isPasswordImport = true
			},
			async finishPay(e) {
				console.log(this.batchNum, this.myCoinNum, this.myBalance)
				this.isLoadding = true
				if (this.myCoinNum >= this.batchNum) {
					//持有数量足够，直接兑换
					this.loaddingMsg = "兑换中..."
					
					this.clickExchange(e,this.batchNum)
					
				} else {
					//支付
					this.balancePay(e)
				}
			
				console.log("密码为", e)
			},
			//兑换
			async clickExchange(password, batchNum) {
				this.loaddingMsg = "兑换中..."
				let res = await this.$api.flyCardExchange({
					exchangeBBaoAmount: batchNum,
					exchangeFlyCardNum: batchNum,
					tradePassword: password
				});
				if (res.status.code == 0) {
					uni.hideLoading();
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
					this.getBalance()

				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
				this.isPasswordImport = false
				this.show = false
				this.isLoadding = false
				this.isOrderCheck = false
			},
			closeResale(data) {
				this.isPasswordImport = false
			},

			async getBalance() { //我的B宝数量
				let result = await this.$api.getCoinNum({})
				if (result.status.code == 1002) {
					uni.showToast({
						title: "未登录，请先登录，2秒为您自动跳转为登录页面",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.push({
							name: "mainLogin",
							params: {
								url: window.location.hash,
							},
						});
					}, 2100);
				}
				this.balanceNum = result.result
				// console.log(result, '我的');
			},
			change(e) {
				this.gameList = []
				this.pageNum = 1
				this.current = e
				this.orderField = this.tabs[e].value
				this.getPlay()
			},
			custom() {
				const pages = getCurrentPages();
				if (this.isActive) {
					history.back();
				} else if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall",
					});
				} else {
					this.$Router.back();
				}
			},
			async getPlay() {
				let res = await this.$api.playAndEarnList({
					pageNum: this.pageNum,
					pageSize: 10,
					orderField: this.orderField
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.gameList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}

				if (res.status.code == 1002) {
					if (result.status.code == 1002) {
						uni.showToast({
							title: "未登录，请先登录，2秒为您自动跳转为登录页面",
							icon: 'none',
							duration: 3000
						});
						setTimeout(() => {
							this.$Router.push({
								name: "mainLogin",
								params: {
									url: window.location.hash,
								},
							});
						}, 2100);
					}
				}
			},
			nav_link(item) {
				// #ifdef H5
				let {
					origin
				} = window.location
				// let origin = 'http://web-test.nftcn.com'
				if (this.token) {
					if (item.linkUrl.indexOf('?') != -1) {
						window.location.href =
							`${item.linkUrl}&platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					} else {
						window.location.href =
							`${item.linkUrl}?platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					}
				} else {
					window.location.href = item.linkUrl
				}
				// #endif
				// #ifdef APP
				let url = item.linkUrl
				this.$Router.push({
					name: "webView",
					params: {
						url,
					}
				})
				// #endif
			},
			goGameRank() {
				console.log(11111)
				let {
					origin
				} = window.location
				window.location.href = `${origin}/active/#/gameRank`
				let url = origin.linkUrl
				this.$Router.push({
					name: "webView",
					params: {
						url,
					}
				})
			},
			clickShowChange(value) {
				this.show = value
				// isPayPassword.value = value
				// show.value  = value;
			},
			back() {
				console.log(11111)
				this.$Router.back();
			},
			btn_add() {
				this.batchNum++
			},
			btn_sub() {
				if (this.batchNum > 1) {
					this.batchNum--
				}
			},
			btn_max() {
				this.batchNum = this.myCoinNum;
			},
			btn_min() {
				this.batchNum = 1;
			},
			handleInput(e) {
				// console.log(e.target.value)
				if (e.target.value > this.myCoinNum) {
					this.batchNum = myCoinNum.value;
				} else {
					this.batchNum = e.target.value.replace(/\D/g, '');
				}
			},
			async flyCardCount(type, pwd) {
				console.log("this.myCoinNum---------", this.myCoinNum)
				let res = await this.$api.getFlyCardCount({})
				this.myCoinNum = res.result.count

				if (type == 0) {
					this.firstMyCoinNum = res.result.count
					console.log("首次查询的起飞卡数量", this.firstMyCoinNum)
				} else {
					console.log("到账的数量", res.result.count)
					console.log("预期数量", (this.firstMyCoinNum + this.modeInfo.num))
					if (this.myCoinNum >= (this.firstMyCoinNum + this.modeInfo.num)) {
						//到账了继续下一步操作  跳出循环  去兑换
						this.clickExchange(pwd, this.myCoinNum)
					} else {
						this.getReceived(pwd)
						//递归 继续查询
					}
				}
				console.log(res.result.count)
			},
			submitAll() {
				console.log(this.batchNum, this.myCoinNum, this.myBalance)
				if (this.myCoinNum >= this.batchNum) {
					if (this.batchNum > 0) {
						this.mode = 'pay'
						this.isPasswordImport = true
					} else {
						uni.showToast({
							title: "购买数量必须大于0",
							icon: 'none',
							duration: 3000
						});
					}
				} else {
					//购买并且兑换
					if (this.batchNum > 100) {
						uni.showToast({
							title: "单次最多购买100份",
							icon: 'none',
							duration: 3000
						});
						return
					}
					if (uni.getStorageSync('isSetTradePassword') == 0) {
						this.isPassword = true
						return
					}
					this.isLoadding = true
					this.loaddingMsg = "购买中..."
					this.createItem()
				}
			},
			// 打开立即兑换弹窗
			openShow() {
				this.show = true
				this.flyCardCount(0)
			},
			//下单接口
			async createItem() {
				let batchBuyNum = this.batchNum - this.firstMyCoinNum
				let res = await this.$api.java_create_item({
					paymentScene: 1,
					ctid: this.ctid,
					batchBuyNum
				});
				if (res.status.code == 0) {
					console.log('下单成功')
					this.show = false
					this.isOrderCheck = true
					this.isLoadding = false
					this.orderNoList = JSON.stringify(res.result.orderNoList)
					this.modeInfo = res.result
					this.modeInfo.num = res.result.orderNoList.length
					this.orderNo = res.result.orderNoList[0]
				} else if (res.status.code == 1090) {
					this.isBalance = true
					this.isLoadding = false
				} else {
					this.isLoadding = false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//吊起支付 输入密码  
			async submitPay() {
				this.mode = 'pay'
				this.isPasswordImport = true
			},
			async finishPay(e) {
				console.log(this.batchNum, this.myCoinNum, this.myBalance)
				this.isLoadding = true
				if (this.myCoinNum >= this.batchNum) {
					//持有数量足够，直接兑换
					this.loaddingMsg = "兑换中..."

					this.clickExchange(e, this.batchNum)

				} else {
					//支付
					this.balancePay(e)
				}

				console.log("密码为", e)
			},
			//重复查询  递归
			getReceived(e) {
				this.loaddingMsg = "兑换中..."
				setTimeout(() => {
					this.flyCardCount(1, e)
				}, 1000)
			},
			/**
			 * 余额支付
			 * @param {String} psw 支付密码
			 */
			async balancePay(psw) {
				this.loaddingMsg = "支付中..."
				const res = await this.$api.java_order_pay_call({
					payMethod: 10,
					tradePassword: psw,
					paymentScene: 1,
					orderType: "O",
					orderId: this.orderNo,
					isH5: 1,
					orderNoListStr: this.orderNoList,
					isBatchBuy: this.batchNum > 1 ? 1 : 0
				});
				if (res.status.code == 0) {
					this.show = false;
					this.getReceived(psw)
				} else if (res.status.code == 1090) {
					this.isBalance = true
					this.isLoadding = false
				} else {
					this.isLoadding = false
					this.$refs.payPopup.password = '';
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000,
					});
				}
			},
			nav_pay() {
				let url = `${this.appUrl}pagesA/project/security/pay?returnUrl=/pagesA/project/index/player/player`
				console.log(url)
				// #ifdef APP
				if (uni.getSystemInfoSync().platform == 'ios') {
					let curV = uni.getSystemInfoSync().appVersion
					let reqV = this.iversion
					if (curV == reqV) {
						this.$Router.push({
							name: "iosPay",
						})
					} else {
						this.$Router.push({
							name: "webView",
							params: {
								url,
							}
						})
					}
				} else {
					this.$Router.push({
						name: "webView",
						params: {
							url,
						}
					})
				}
				// #endif
				// #ifdef H5
				let {
					origin
				} = window.location
				window.location.href =
					`${origin}/orderView/#/pagesA/project/security/pay?returnUrl=/pagesA/project/index/player/player`
				// #endif
			},
			async getBuyUnPaidQuery() {
				let res = await this.$api.batchBuyUnPaidQuery({

				});
				if (res.status.code == 0) {
					if (res.result) {
						console.log("存在订单，直接弹出支付")
						this.show = false
						this.isOrderCheck = true
						this.orderNoList = JSON.stringify(res.result.orderNoList)
						this.modeInfo = res.result
						this.modeInfo.num = res.result.orderNoList.length
						this.orderNo = res.result.orderNoList[0]
						this.batchNum = this.modeInfo.num + this.myCoinNum
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async get_version() {
				let res = await this.$api.java_commonconfigInfo({
					name: 'ios_apple_pay_version',
				});
				if (res.status.code == 0) {
					this.iversion = res.result.value
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			createSuccess() {
				this.isPasswordImport = false
				uni.setStorageSync("isSetTradePassword", 1)
			}

		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-drawer-bottom {
		background: transparent;
	}

	.nav_bar {
		width: 100%;
		position: relative;
		text-align: center;
		font-size: 32rpx;
		line-height: 40rpx;
		color: #F9F9F9;

		letter-spacing: 2rpx;
		font-weight: bold;

	}

	.btn_exchange2 {
		position: absolute;
		top: 20rpx;
		left: 30rpx;
		text-align: center;
		width: 260rpx;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 28rpx;
		background: linear-gradient(135deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: bold;
		color: #141414;
		font-style: normal;
		text-transform: none;
	}


	.btn_exchange3 {
		position: absolute;
		top: 25rpx;
		left: 330rpx;
		text-align: center;
		width: 200rpx;
		background: #35333E;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		border: 1rpx solid #FFFFFF;
		line-height: 60rpx;
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: bold;
		font-size: 28rpx;
		background: #35333E;
		border: 1px solid #FFFFFF;
	}

	.nav_title {
		width: 95%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 50rpx;
		position: absolute;
		top: 50rpx;
		/* #ifdef APP */
		top: var(--status-bar-height)
			/* #endif */
			/* #ifdef H5 */
			top: 50rpx;
		/* #endif */
	}

	.nar_rankTitle {
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		text-align: center;
		font-style: normal;
		text-transform: none;
		position: relative;
		padding-right: 40rpx;
	}

	.nar_price {
		position: absolute;
		right: 95rpx;
		top: 7rpx;
	}



	.nav_userInfo {
		position: absolute;
		top: 200rpx;

		.menu_bar {
			margin-left: 40rpx;
			width: 300rpx;
			text-align: center;
			display: flex;

			letter-spacing: 2rpx;

			.my_coin {
				width: 100%;
				font-size: 35rpx;
				line-height: 40rpx;
				color: #F9F9F9;
				font-weight: bold;
			}

			.play_coin {
				width: 100%;
				font-size: 28rpx;
				line-height: 40rpx;
				color: #63EAEE;
				font-weight: bold;
				text-decoration: underline;
			}
		}
	}

	.balance_bar {
		width: 50%;
		top: -200rpx;
		left: 30rpx;
		position: relative;
		display: flex;
		text-align: center;

		letter-spacing: 2rpx;
		font-size: 35rpx;
		line-height: 40rpx;
		color: #F9F9F9;

		.balance_text {
			width: 85%;
			top: 27rpx;
			left: 110rpx;
			text-align: left;
			position: absolute;
			font-weight: bold;
		}
	}

	.nav_tabbar_view {
		margin-left: 20rpx;
	}

	.nav_tabbar_icon1 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		left: 80rpx;

	}

	.nav_tabbar_icon2 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		left: 440rpx;

	}

	.player {
		position: relative;

		&.padding {
			padding: 114rpx 0rpx;
		}
	}

	.msg {
		color: #fff;
		font-size: 22rpx;
		padding: 20rpx;
	}

	.nav_tabbar {
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 36rpx;
		color: #fff;

		position: relative;
		justify-content: space-between;
	}

	.body {
		position: relative;
		background-color: #35333E;

		&::before {
			content: ' ';
			position: fixed;
			z-index: -1;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background: #35333E;
			background-size: 100% auto;
		}
	}

	.coin_rule {
		width: 100%;

		display: flex;
		justify-content: center;
		align-items: center;
		/* 垂直居中 */

		img {
			position: absolute;
			top: 530rpx;
			width: 100%;
		}

		.rule_1 {
			position: absolute;
			width: 85%;
			top: 650rpx;
			text-align: center;

			.p1 {
				color: #fff;
				font-weight: bold;
				font-size: 40rpx;
				line-height: 150rpx;
				text-decoration-line: underline;
				/* 下划线 */
				text-decoration-color: #63EAEE;
				/* 下划线颜色 */
				text-decoration-thickness: 5rpx;
				/* 下划线粗细 */
			}

			.p2 {
				color: #fff;
				// font-weight: bold;
				font-size: 30rpx;
				line-height: 50rpx;
				text-align: left;
			}
		}

		.rule_2 {
			position: absolute;
			width: 85%;
			top: 1130rpx;
			text-align: center;

			.p1 {
				color: #fff;
				font-weight: bold;
				font-size: 40rpx;
				line-height: 150rpx;

				text-decoration-line: underline;
				/* 下划线 */
				text-decoration-color: #63EAEE;
				/* 下划线颜色 */
				// text-decoration-style: dotted; /* 下划线样式 */
				text-decoration-thickness: 5rpx;
				/* 下划线粗细 */
			}

			.p2 {
				color: #fff;
				// font-weight: bold;
				font-size: 30rpx;
				line-height: 50rpx;
				text-align: left;
			}

			.card {
				position: absolute;
				top: 350rpx;
				left: 150rpx;
				width: 70rpx;
			}

			.coin {
				position: absolute;
				top: 350rpx;
				left: 350rpx;
				width: 60rpx;
			}

			.p3 {
				width: 100%;

				position: absolute;
				top: 355rpx;
				color: #fff;
				// font-weight: bold;
				font-size: 40rpx;
				line-height: 50rpx;
				font-weight: bold;
			}
		}
	}




	.popup_head {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: bold;
		font-size: 28px;
		color: #FFFFFF;
		text-align: center;
		font-style: normal;
		text-transform: none;
		background: #35333E;
		border-bottom: 1px solid #53505D;
		position: relative;

		.popup_icon {
			width: 50rpx;
			height: 50rpx;
			position: absolute;
			left: 270rpx;
			bottom: 18rpx;
		}
	}

	::v-deep .popup_body_bottom {
		height: px2vw(700rpx);
		width: 100%;
	}

	.popup_content {
		height: 430rpx;
	}

	.nav_tabbar_icon1 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		left: 140rpx;

	}

	.nav_tabbar_icon2 {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		left: 320rpx;
	}


	.btn_exchange4 {
		position: absolute;
		width: 100%;
		top: 1530rpx;
		text-align: center;
		color: #63EAEE;
		font-size: 30rpx;
		line-height: 180rpx;
		text-decoration-line: underline;
		/* 下划线 */

	}





	.bodyPop_exchange {
		height: 800rpx;
		width: 100%;
		background-color: #35333E;

		.title {
			text-align: center;
			position: relative;
			height: 100rpx;
			line-height: 100rpx;
			color: #fff;
			font-weight: bold;

			.colse {
				position: absolute;
				right: 40rpx;
				top: 10rpx;

				img {
					width: 40rpx;
				}
			}

			.popup_icon {
				width: 50rpx;
				height: 50rpx;
				position: absolute;
				left: 260rpx;
				bottom: 25rpx;
			}

		}
	}

	.input {
		padding-top: 80rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;

		/* 垂直居中 */
		input {
			margin: 0 auto;
			width: 25%;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			background-color: #46454F;
			border: 1px solid #46454F;
			border-radius: 50rpx;
			color: #63EAEE;
			font-size: 40rpx;
			font-weight: bold;
		}

		.add {
			position: absolute;
			right: 200rpx;
			width: 55rpx;
		}

		.sub {
			position: absolute;
			left: 200rpx;
			width: 55rpx;
		}

		.max {
			position: absolute;
			right: 30rpx;
			width: 150rpx;
			height: 70rpx;
			border-radius: 60rpx;
			mix-blend-mode: normal;
			background: linear-gradient(101.31deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
			text-align: center;

			letter-spacing: 2rpx;
			font-size: 25rpx;
			line-height: 70rpx;
			color: #0a0a0a;
			font-weight: bold;
		}

		.min {
			position: absolute;
			left: 30rpx;
			width: 150rpx;
			height: 70rpx;
			border-radius: 60rpx;
			border: #ffffff 1rpx solid;
			mix-blend-mode: normal;
			text-align: center;

			letter-spacing: 2rpx;
			font-size: 25rpx;
			line-height: 70rpx;
			color: #ffffff;
			font-weight: bold;
		}
	}

	.tips {
		width: 100%;
		margin-top: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		/* 垂直居中 */
		.coin {
			width: 55rpx;
		}

		.card {
			width: 60rpx;
		}

		p {
			color: white;
			opacity: 0.5;

		}
	}

	.myCard {
		width: 100%;
		margin-top: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		/* 垂直居中 */
		.card {
			width: 60rpx;
		}

		.t1 {
			color: white;
			font-weight: bold;
		}

		.t2 {
			color: #63EAEE;
			font-weight: bold;
		}
	}

	.btn {
		width: 100%;
		margin-top: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		/* 垂直居中 */
		.btn_confirm {
			position: absolute;
			width: 500rpx;
			height: 100rpx;
			border-radius: 60rpx;
			mix-blend-mode: normal;
			background: linear-gradient(101.31deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
			text-align: center;

			letter-spacing: 2rpx;
			font-size: 30rpx;
			line-height: 100rpx;
			color: #0a0a0a;
			font-weight: bold;
		}
	}

	::v-deep .popup_body_bottom {
		z-index: 2030 !important;

		.bodyPop {
			padding: 0 55rpx;
			background-color: #35333E;
			z-index: 2030 !important;
			border-radius: 36rpx 36rpx 0 0;
			padding-bottom: 30rpx;

			.head2 {
				padding: 55rpx 0;
				text-align: center;
				border-bottom: 1px solid #616161;
				color: #fff;

				.title {
					color: #fff;
					font-weight: 600;
					font-size: 34rpx;
				}

				.sub_title {
					font-size: 50rpx;
					margin-top: 30rpx;
					font-weight: 600;
					;

					text {
						color: #63EAEE;
						margin-right: 10rpx;
					}
				}

				.countdown {
					font-size: 24rpx;
					margin-top: 20rpx;

					text {
						font-size: 24rpx;
					}
				}
			}

			>.body_view {
				.padding40 {
					padding: 40rpx 0rpx;
				}

				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 30rpx;

					.name {
						color: #fff;
						margin-bottom: 20rpx;
					}

					.valUue {
						color: #63EAEE;
					}
				}

				.msg {
					display: flex;
					align-items: center;

					image {
						width: 30rpx;
						margin-right: 15rpx;
					}

					color: #fff;
					font-size: 24rpx;
				}

				.but {
					width: 640rpx;
					height: 100rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
					border-radius: 50rpx;
					margin: 40rpx auto 31rpx auto;
					font-weight: 600;
					color: #141816;
				}
			}
		}

		.right_colse {
			position: absolute;
			right: 0rpx;
			top: 0rpx;

			image {
				width: 80rpx;
			}
		}


		.button {
			margin-top: 50rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 28rpx;
			font-weight: 600;

		}
	}

	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 288rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;

		.icon {
			position: absolute;
			left: 0rpx;
			top: 12rpx;
			width: 288rpx;
			height: 12rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240819/0acd0a67a4637042db6deb2251d1db47_289x12.png);
			background-size: 100%;
		}
	}

	.new-modal-content {
		padding: 35rpx 40rpx;

		.success_img {
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 160rpx;
				height: 160rpx;
			}
		}

		.modal-content {
			padding: 35rpx 0rpx;
			border-bottom: 1rpx solid #EDEDED;
			font-size: 28rpx;
			color: #fff;
			text-align: center;
		}

		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color: #fff;
			}

			.img_cancel {
				border: 1px solid #FFF;
				color: var(--default-color3);
			}

			.img_reasale {
				color: #141414;
				background: var(--primary-button-color);
			}
		}
	}

	.title_bg2 {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;

		.icon {
			position: absolute;
			left: 0rpx;
			top: 20rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}

	.loading_list {
		height: 250rpx;
	}
</style>