<template>
	<view class="body">
		<view class="nav_bar">
			<view class="nav_title">
				<view class="">
					<u-image @click="back()"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240513/d1ca1e0b91da2db23557ee5b87d985eb_52x100.png"
						mode="widthFix" width="22"></u-image>
				</view>
				<view class="" style="margin-right: 50rpx;">
					B宝记录
				</view>
				<view class="nar_rankTitle">

				</view>
			</view>
		</view>


		<view class="game_list">
			<view v-for="(game, index) in games" :key="index" class="game-item">
				<u-image
					src="https://cdn-lingjing.nftcn.com.cn/image/20240514/4bdb96c77462d67318a75d11b6c6db9c_678x160.png"
					mode="widthFix"></u-image>
				<view class="game-info">
					<h3 class="remark">{{ game.remark }}</h3>
					<h4 class="createTime">{{ game.createTime.split(".")[0] }}</h4>
					<view class="game-coin">
						<u-image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240513/7d4050219b8a3498f458d3cad7f87b9a_100x100.png"
							mode="widthFix"></u-image>
					</view>
					<h3 :class="{ 'balance-red': game.balanceOp == 2, 'balance-green': game.balanceOp == 1 }">
						{{ `${game.balanceOp==1?"+":"-"}`+game.balance }}
					</h3>
					<h3>{{ game.balance }}</h3>
				</view>
			</view>
			<view class="tips">
				仅显示最近100条B宝数据
			</view>
		</view>






	</view>
</template>

<script>
	export default {

		data() {
			return {
				height: "",
				games: [],
				pageSize:5,
				status: 'loadmore',
				list: 15,
				page: 0,
				marketTabId: "",
				refresherState: true,
				showAnimation: false,
				animationInstance: null,
				isLoadingStatus: 0, //0 加载中  1 正常载入  2无数据
				pageNum:1,
			}
		},
		// 下拉刷新的事件
		// onPullDownRefresh() {
		// 	console.log('refresh');
		// 	setTimeout(function () {
		// 		uni.stopPullDownRefresh();
		// 	}, 1000);
		// 	this.getHistory()
		// },


		onLoad(e) {
			this.getHistory()

		},
		onShow() {

		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					uni.showLoading({
					title: '加载中'
					});		
					setTimeout(()=>{
						this.getHistory()
					},2000)
					this.getHistory()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
				
				
			}
		},
		methods: {
			refresher() {
			},
			lower() {
				console.log("触底了")
			},
			back() {
				console.log(11111)
				this.$Router.back()
			},
			async getHistory() {
				
				
				console.log(1111111)
				let res = await this.$api.getCoinHistory({
					pageSize: 100,
					pageNum:1
				})
				console.log(res)
				if (res.status.code == 0) {
					console.log(222222222222222)
					this.games=res.result.list
				
				}
				if (res.status.code === 1002) {
						uni.showToast({
							title: "未登录，请先登录，2秒为您自动跳转为登录页面",
							icon: 'none',
							duration: 3000
						});
						setTimeout(() => {
							this.$Router.push({
								name: "mainLogin",
								params: {
									url: window.location.hash,
								},
							});
						}, 2100);
					
				}

			},
			goLogin() {
				this.$Router.push({
					name: "login",
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
	

	.nav_bar {
		width: 100%;
		position: relative;
		text-align: center;
		font-size: 32rpx;
		line-height: 40rpx;
		color: #F9F9F9;
		font-family: PingFangSC-Medium, PingFang SC;
		letter-spacing: 2rpx;
		font-weight: bold;

	}

	.nav_title {
		width: 95%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		position: absolute;

	}

	.body {}

	.nav_bar {
		width: 100%;
		padding: 100rpx 40rpx;
		
		// position: absolute;
		text-align: center;
		font-size: 32rpx;
		line-height: 40px;
		color: #F9F9F9;
		font-family: PingFangSC-Medium, PingFang SC;
		letter-spacing: 2rpx;
		font-weight: bold;
		display: flex;
		text-align: center;
		justify-self: center;

		img {
			width: 16rpx;
			height: 28rpx;
			position: absolute;
			left: 40rpx;
			top: 40rpx;
		}
	}

	.game_list {
		width: 100%;
		// top: 200rpx;
		// position: absolute;
		padding-top: 20rpx;

	}

	.game-item {
		position: relative;
		margin: 20rpx auto;
		/* 水平居中 */
		width: 90%;
		height: 160rpx;

		img {
			width: 100%;
			height: 112rpx;
		}
	}

	.game-info {
		flex: 1;

		.remark {
			position: absolute;
			left: 40rpx;
			bottom: 10rpx;
			font-size: 28rpx;
			line-height: 80rpx;
			font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
			color: #F9F9F9;
			font-weight: bold;
		}

		.createTime {
			position: absolute;
			left: 40rpx;
			bottom: 90rpx;
			font-size: 25rpx;
			line-height: 40rpx;
			color: #F9F9F9;
			opacity: 0.7;
		}

		.game-coin {
			position: absolute;
			right: 210rpx;
			bottom: 33rpx;
			width: 50rpx;
			height: 50rpx;
		}

		.balance-red {
			width: 120rpx;
			position: absolute;
			right: 80rpx;
			bottom: 20rpx;
			line-height: 80rpx;
			font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
			font-weight: bold;
			font-size: 34rpx;
			color: #63EAEE;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

		.balance-green {
			width: 120rpx;
			position: absolute;
			right: 80rpx;
			bottom: 20rpx;
			line-height: 80rpx;
			font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
			font-weight: bold;
			font-size: 34rpx;
			color: #63EAEE;
			text-align: left;
			font-style: normal;
			text-transform: none;
		}

	}

	.tips {
		// position: absolute;
		text-align: center;
		font-size: 30rpx;
		line-height: 80rpx;
		color: white;
		font-weight: bold;
		margin-top: 50rpx;
	}

	.scroll-Y {
		max-height: 80vh;
	}
</style>