<template>
	<view class="mall_copyright">
		<u-navbar class="list-nav" title="吉物仓" style="color: var(--default-color1)"
			back-icon-color="var(--message-box-point-color)" :border-bottom="false" :custom-back="custom"
			:background="{ backgroundColor: 'var(--main-bg-color)' }">
		</u-navbar>
		<view class="index_explore">
			<view class="sousuo">
				<u-search placeholder="" v-model="keyword" shape="square" height="64"
					color="var(--default-color1)" :show-action="false" bg-color="var(--main-bg-color)" search-icon-color="var(--main-bg-color)"
					border-color="var(--default-color1)" @search="search" @clear="clear"
					search-icon="https://cdn-lingjing.nftcn.com.cn/image/20220804/5d5b3be343113b18bf040962f1008e50_48x48.png"
					:focus="false"></u-search>
				<view  class="search" @click="search()">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20230510/d3aeca4181e8f25049d985875c0b4506_95x95.png" mode="widthFix"></image>
				</view>
			</view>
			<u-waterfall v-model="List" ref="uWaterfall" v-if="List!=''">
				<template v-slot:left="{leftList}">
					<view class="item" v-for="(item, index) in leftList" :key="index" @click="nav_details(item,index)">
						<template>
							<view class="image_bg">
								<u-image radius :src="item.cover.src" alt="" mode="aspectFill" />
								<view class="new_icon" v-if="item.newFlag==1">
									<image src="https://cdn-lingjing.nftcn.com.cn/image/20230315/cd6b3491a3b42b746a9c17cbda59a767_140x82.png" mode="widthFix"></image>
								</view>
								<view class="ji_icon" v-if="item.tag>6">
									<image :src="`../../../static/imgs/public/kun/list_ji_${item.tag}.png`" mode="heightFix"></image>
								</view>
								<view class="dan_icon" v-else>
									<image :src="`../../../static/imgs/public/kun/list_dan_${item.tag}.png`" mode="heightFix"></image>
								</view>
								<view class="bg">
									<image class="bg_icon"
										src="https://cdn-lingjing.nftcn.com.cn/image/20221107/6464b7f3a98f8d4dbbffae5f0b0c08e6_284x54.png"
										mode="widthFix"></image>
									<view class="image_text">
										<text>流通：</text>
										<view>{{item.activeNum}}</view>
										<text>份</text>
									</view>
									<view class="tuishi" v-if="item.isExitMarket==1">
										<image
											src="https://cdn-lingjing.nftcn.com.cn/image/20240310/89a70b6d15a2a87fcc8fd4628db673c2_274x274.png"
											mode="widthFix"></image>
									</view>
								</view>
							</view>
							<view class="info">
								<view class="title oneOver">{{ item.title }}</view>
								<view class="tag">
									<view class="red">总量</view>
									<view class="text">{{item.createNum}}份</view>
								</view>
								<view class="price" v-if="item.isExitMarket===1">
									<text>{{item.isLimitPrice===1?"限价:￥":"￥"}}</text>
									<view>{{item.isLimitPrice===1?item.limitPrice:"——"}}</view>
								</view>
								<view class="price" v-else>
									<text>￥</text>
									<view>{{item.minPrice}}</view>
									<text>起</text>
								</view>
							</view>
						</template>
					</view>
				</template>
				<template v-slot:right="{rightList}">
					<view class="item" v-for="(item, index) in rightList" :key="index" @click="nav_details(item,index)">
						<template>
							<view class="image_bg">
								<u-image radius :src="item.cover.src" alt="" mode="aspectFill" />
								<view class="new_icon" v-if="item.newFlag==1">
									<image src="https://cdn-lingjing.nftcn.com.cn/image/20230315/cd6b3491a3b42b746a9c17cbda59a767_140x82.png" mode="widthFix"></image>
								</view>
								<view class="ji_icon" v-if="item.tag>6">
									<image :src="`../../../static/imgs/public/kun/list_ji_${item.tag}.png`" mode="heightFix"></image>
								</view>
								<view class="dan_icon" v-else>
									<image :src="`../../../static/imgs/public/kun/list_dan_${item.tag}.png`" mode="heightFix"></image>
								</view>
								<view class="bg">
									<image class="bg_icon"
										src="https://cdn-lingjing.nftcn.com.cn/image/20221107/6464b7f3a98f8d4dbbffae5f0b0c08e6_284x54.png"
										mode="widthFix"></image>
									<view class="image_text">
										<text>流通：</text>
										<view>{{item.activeNum}}</view>
										<text>份</text>
									</view>
									<view class="tuishi" v-if="item.isExitMarket==1">
										<image
											src="https://cdn-lingjing.nftcn.com.cn/image/20240310/89a70b6d15a2a87fcc8fd4628db673c2_274x274.png"
											mode="widthFix"></image>
									</view>
								</view>
							</view>
							<view class="info">
								<view class="title oneOver">{{ item.title }}</view>
								<view class="tag">
									<view class="red">总量</view>
									<view class="text">{{item.createNum}}份</view>
								</view>
								<view class="price" v-if="item.isExitMarket===1">
									<text>{{item.isLimitPrice===1?"限价:￥":"￥"}}</text>
									<view>{{item.isLimitPrice===1?item.limitPrice:"——"}}</view>
								</view>
								<view class="price" v-else>
									<text>￥</text>
									<view>{{item.minPrice}}</view>
									<text>起</text>
								</view>
							</view>
						</template>
					</view>
				</template>
 
			</u-waterfall>
			<view v-else>
				<u-empty class="null"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
					:text="msg_text" mode="list" margin-top="340"></u-empty>
			</view>
		</view>
		<u-toast ref="uToast" />
		<u-modal class="modal" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<view class='sk-wave'>
				<view class='sk-rect sk-rect-1'></view>
				<view class='sk-rect sk-rect-2'></view>
				<view class='sk-rect sk-rect-3'></view>
				<view class='sk-rect sk-rect-4'></view>
				<view class='sk-rect sk-rect-5'></view>
			</view>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;color:var(--default-color1);">
				玩命加载中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	import fa from "@/common/public.js"
	export default {
		data() {
			return {
				share_png: '../../../static/imgs/hot/share.png',
				List: [],
				show: false, // 筛选弹框
				shoplist: [], //商品
				active: true, // 综合排序
				flag: 0,
				loading: "",
				listNum: 0, //点赞
				tid: "",
				search_icon: "../../../static/imgs/public/search_icon2.png",
				keyword: '', // 搜索关键字
				moneynum: 0, //价格
				money: '',
				timenum: 0, //时间
				time: '',
				priceList: [{
					name: "0~500",
					value: "1"
				}, {
					name: "500~2000",
					value: "2"
				}, {
					name: "2000+",
					value: "3"
				}, ],
				numList: [{
					name: "独版",
					value: "1"
				}, {
					name: "多版",
					value: "20"
				}],
				stateList: [{
					name: "在售",
					value: 1
				}, {
					name: '仅展示',
					value: 0
				}],
				filter: null, // 弹窗选中的数据
				filterData: {
					sortBy: '',
					sort: '',
				}, // 筛选数据
				isFilterLoading: false,
				shared_contract_address: '',
				accept_id: '',
				isLoadding: false,
				loadStatus: 'loadmore',
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				pageNum: 1,
				msg_text: "无搜索结果",
				marketTabLeftId: "",
			};
		},
		onLoad(options) {
			this.marketTabLeftId = options.marketTabLeftId
			this.marketTabId = options.marketTabId
			this.get()
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.get()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				this.loadStatus = 'nomore';
				console.log("已经到底了")
			}
		},
		computed: {
			title() {
				return this.$route.query.title;
			}
		},
		methods: {
			//点击icon返回上一页
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			// 页面分享
			openShare() {
				this.$refs.share.open(8);
			},
			async get() {
				this.isLoadding = true
				this.isRequest = false
				let res = await this.$api.java_jwcList({
					pageNum: this.pageNum,
					pageSize: "10",
					keyword:this.keyword 
					// marketTabLeftId: this.marketTabLeftId,
					// marketTabId:this.marketTabId
				})
				if (res.status.code === 0) {
					console.log(res.result.list)
					this.isLoadding = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
						this.loadStatus = 'nomore';
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.List.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//跳转
			nav_details(item, index) {
				console.log(item)
				this.$Router.push({
					name: "seriesList",
					params: {
						ctid: item.ctid,
						title:item.title
						// isExitMarket:item.isExitMarket,
						// activeNum:item.activeNum
					}
				})
			},
			imgSrc(item) {
				const {
					specialCover,
					coverImage,
					showImage
				} = item
				return specialCover || showImage || coverImage?.src
			},
			// 搜索跳转
			nav_search() {
				this.pageNum = 1
				this.List = []
				if (this.$refs.uWaterfall != undefined) {
					this.$refs.uWaterfall.clear()
				}
				this.get()
			},
			clear() {
				this.pageNum = 1
				this.List = []
				this.keyword = ""
				this.isFooter = true
				this.isRequest = false
				if (this.$refs.uWaterfall != undefined) {
					this.$refs.uWaterfall.clear()
				}
				this.get()
			},
			search(){
				this.pageNum = 1
				this.List = []
				this.isFooter = true
				this.isRequest = false
				if (this.$refs.uWaterfall != undefined) {
					this.$refs.uWaterfall.clear()
				}
				this.get()
			}
		},
	}
</script>

<style lang="scss" scoped>
	.u-drawer::v-deep {
		.u-drawer-right {
			background: var(--main-bg-color);
		}
	}

	::v-deep .u-icon__label {
		margin-top: 46rpx !important;
		font-size: 28rpx;
		color: #616161 !important;
	}
	
	.modal::v-deep {
		.u-model {
			background-color: var(--main-bg-color);
		}

		.sk-rect {
			background-color: var(--default-color1);
		}
	}

	::v-deep .u-content {
		height: 64rpx;
		color: #F9F9F9;
		border-radius: 30rpx !important;
	}
	::v-deep .u-content .u-icon-wrap {
		display:none !important; 
	}
	
	.list-nav::v-deep {
		.u-navbar-content-title {
			.u-title.u-line-1 {
				color: var(--default-color1) !important;
			}
		}
	}

	.nav-right {
		padding-right: 34rpx;

		.share-btn {
			margin-left: 34rpx;
		}
	}

	.header {
		background: var(--main-bg-color);

		.mall_recommend {
			.title {
				height: 88rpx;
				line-height: 88rpx;
				padding: 18rpx 40rpx;
				font-size: 38rpx;
				background-color: var(--main-bg-color);
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
				justify-content: space-between;
				align-items: center;
				color: var(--default-color1);

				.choice {

					font-size: 28rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 600;
					color: var(--info-front-color);
					line-height: 40rpx;
					// line-height: 40rpx;
					// display: flex;
					// flex-direction: row;
					// flex-wrap: nowrap;
					// justify-content: space-between;
					// align-items: center;
				}

				.total {
					padding: 0 20rpx;
				}


				.left,
				.right {
					// background: #1E1E1E;
					// border-radius: 30px;
					line-height: 42rpx;
					padding: 0 20rpx;
					display: flex;
					align-items: center;

				}

				.left {

					.img {
						margin-left: 16rpx;
						width: 15rpx;
						height: 25rpx;
						line-height: 30rpx;

						image {
							width: 100%;
							height: 100%;
							vertical-align: middle;
						}
					}
				}

				.right {
					margin-left: 10rpx;

					.img {
						margin-left: 16rpx;
						width: 15rpx;
						height: 25rpx;

						image {
							width: 100%;
							height: 100%;
						}
					}
				}

				.screen {
					font-size: 26rpx;
					font-family: PingFang-SC-Medium, PingFang-SC;
					font-weight: 600;
					color: var(--info-front-color);
					line-height: 37rpx;
					display: flex;
					align-items: center;

					.u-image {
						margin-left: 4rpx;
					}
				}
			}
		}
	}

	.index_explore {
		padding: 10rpx 16rpx;
		padding-left: 32rpx;

		.item {
			overflow: hidden;
			background-color: #2B2B2B;
			border-radius: 12rpx;
			margin-bottom: 16rpx;
			margin-right: 16rpx;
			padding:20rpx;
			.image_bg {
				overflow: hidden;
				position: relative;
				width: 100% !important;
				height: 294rpx;
				border-radius: 12rpx;
				background-color:#fff;
				.u-image {
					width: 100% !important;
					height: 294rpx !important;
				}

				.bg {
					position: absolute;
					left: 0rpx;
					bottom: 0rpx;
					height: 68rpx;
					width: 100%;
					display: flex;
					align-items: flex-end;
					
					.bg_icon {
						width: 187rpx;
						position: absolute;
						bottom: 0;
						left: 0;
						right: 0;
						margin: 0 auto;
					}

					.image_text {
						color: #DEDEDE;
						display: flex;
						justify-content: center;
						align-items: center;
						height: 38rpx;
						width: 100%;
						font-size: 22rpx;
						z-index: 99;
					}

					.tuishi {
						position: absolute;
						bottom: 34rpx;
						right: 10rpx;
						width: 130rpx;
						background-color: rgba(0, 0, 0, 0.3);
						border-radius: 50%;

						image {
							width: 100%;
						}
					}
				}

			}

			.name {
				color: var(--default-color1);
				font-size: 28rpx;
				line-height: 32rpx;
				margin-top: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
			}

			.info {
				// background: #1E1E1E;
				position: relative;
				font-family: PingFangSC-Semibold, PingFang SC;
				border-bottom-left-radius: 8rpx;
				border-bottom-right-radius: 8rpx;
				padding: 20rpx 20rpx 0rpx 20rpx;
				.title {
					font-size: 28rpx;
					font-weight: bold;
					color: var(--default-color1);
					line-height: 28rpx;
					width: 300rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
				}

				.tag {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					margin-top: 10rpx;
					background-color: #383838;
					border-radius: 4rpx;
					width: 170rpx;
					.red {
						background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20221102/a009e0535dd8593616346de21b2416aa_108x56.png');
						color: #111111;
						width: 54rpx;
						height: 34rpx;
						background-size: 100% 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 22rpx;
					}

					.text {
						color: #FFFFFF;
						font-size: 22rpx;
						padding-left: 10rpx;
					}
				}

				.price {
					display: flex;
					justify-content: flex-end;
					align-items: flex-end;
					color: #fff;
					text {
						font-size: 24rpx;
						padding: 0rpx 4rpx;
					}

					>view {
						font-size: 34rpx;
						font-weight: 600;
					}
				}
			}
		}

	}

	.like-btn {
		width: 44rpx;
		height: 44rpx;
		position: relative;
		margin: 0 auto;

		.like,
		.liked {
			width: 44rpx;
			height: 44rpx;
			position: relative;
			background-size: contain;
			background-repeat: no-repeat;
		}

	}

	.like1-btn {
		width: 36rpx;
		height: 36rpx;
		margin-right: 10rpx;
		position: relative;

		.like1,
		.liked1 {
			width: 36rpx;
			height: 36rpx;
			position: relative;
			background-size: contain;
			background-repeat: no-repeat;
		}


	}
	//仅供收藏
	.label {
		// width: 92rpx;
		height: 54rpx;
		color: var(--message-box-point-color);
		text-align: center;
		line-height: 54rpx;
		background-color: rgba(102, 102, 102, 0.7);
		// background: linear-gradient(315deg, #1C1C1E 0%, #48484A 100%);
		// border-radius: 0rpx 16rpx 0rpx 16rpx;
		border-top-right-radius: 14rpx;
		border-bottom-left-radius: 14rpx;
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		padding: 0rpx 20rpx;
		z-index: 10;

		.text {
			font-size: 20rpx;
		}
	}

	.text-color {
		color: var(--active-color) !important;
		border-radius: 28rpx;
		border: 2rpx solid var(--active-color);
	}

	.leftLabel {
		width: 92rpx;
		height: 32rpx;
		position: absolute;
		top: 10rpx;
		left: 0rpx;
		z-index: 10;

		.img {
			width: 100%;
			height: 100%;

			image {
				width: 100%;
				height: 100%;
				border-radius: 0;
			}
		}
	}

	.null {
		::v-deep .u-icon__img {
			width: 170rpx !important;
		}
	}
	.new_icon{
		position:absolute;
		top:2rpx;
		left:4rpx;
		image{
			width:46rpx;
		}
	}
	.ji_icon{
		position:absolute;
		width:120rpx;
		height:36rpx;
		top:0;
		right:0;
		left:0;
		margin:0 auto;
		text-align: center;
		font-size:24rpx;
		color:#DEDEDE;
		line-height: 38rpx;
		image{
			height:36rpx;
		}
	}
	.dan_icon{
		position:absolute;
		width:120rpx;
		height:36rpx;
		top:0;
		right:0;
		left:0;
		margin:0 auto;
		text-align: center;
		font-size:24rpx;
		color:#DEDEDE;
		line-height: 38rpx;
		image{
			height:36rpx;
		}
	}
	.sousuo{
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding:20rpx 20rpx 20rpx 0rpx;
		.search{
			color:#fff;
			font-size:26rpx;
			margin-left:30rpx;
			image{
				width:43rpx;
			}
		}
	}
	
</style>
