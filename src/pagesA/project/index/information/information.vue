<template>
	<view class="information">

		<view class="list" v-for="(item,index) in list" :key="item.id" @click="nav_link(item)">
			<view>
				<image :src="item.iconUrl" mode="widthFix"></image>
			</view>
			<view>
				<view>{{item.title}}</view>
				<view>{{item.showTime}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				pageNum:1,
				isFooter:true,
				isRequest:false,
			}
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		onLoad() {
			this.getList()
		},
		methods: {
			async getList() {
				let {
					status,
					result
				} = await this.$api.java_moreInquiriesList({
					pageNum: this.pageNum,
					pageSize: 10
				})
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							this.list.push(item)
						})
					}
				}
			},
			nav_link(item){
				if(item.linkUrl){
					// #ifdef APP
						// #ifdef APP
						this.$Router.push({
							name: "webView",
							params: {
								url:item.linkUrl
							}
						})
						// #endif
					// #endif
					// #ifdef H5
					window.location.href = item.linkUrl
					// #endif
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.information {
		height: auto;
		padding-bottom: 20rpx;
	}

	.list {
		width: 678rpx;
		background: #25232E;
		border-radius: 36rpx;
		overflow: hidden;
		padding: 30rpx 20rpx;
		box-sizing: border-box;
		margin: 40rpx auto;

		>view:nth-child(1) {
			width: 638rpx;
			max-height: 300rpx;
			border-radius: 24rpx;
			overflow: hidden;
			margin-bottom: 20rpx;

			>image {
				width: 100%;
				height: 100%;
			}
		}

		>view:nth-child(2) {
			width: 638rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 0 auto;

			>view:nth-child(1) {
				color: #fff;
				font-weight: 400;
				font-size: 28rpx;
			}

			>view:nth-child(2) {
				font-weight: 400;
				font-size: 24rpx;
				color: #ccc;
			}
		}
	}
</style>