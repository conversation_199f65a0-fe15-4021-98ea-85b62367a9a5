<template>
	<view class="person">
		<!-- 头像/昵称 -->
		<view class="headImg">
			<view>
				<image :src="avatar" mode="aspectFill" @click="showAvatar = true"></image>
				<image src="@/static/imgs/person/update.png" mode="aspectFill" @click="uploadImg"></image>
			</view>
			<view>
				<view>区块链地址：{{ contractAddress }}</view>
				<view @click="copy">
					<image src="@/static/imgs/public/copy.png" mode="aspectFill"></image>
				</view>
			</view>
		</view>

		<!-- 基本信息 -->
		<view class="basic">
			<view class="tit">
				基本信息
			</view>
			<view class="box">

				<view v-for="item in basicList" :key="item.id" @click="nav_to(item)">
					<view>{{ item.name }}</view>
					<view>
						<view class="flags" v-if="item.id == 1 && item.text == 31">
							<view>已实名</view>
						</view>
						<view class="flags" v-else-if="item.id == 1 && item.text == 30">
							<view>待审核</view>
						</view>
						<view class="flags" v-else-if="item.id == 1 && !item.text">
							<view>去实名</view>
						</view>
						<view v-else-if="item.id == 0" class="inp">
							<input type="text" v-model="item.text" :height="30" @blur="blur" @focus="focus" />
						</view>
						<view v-else-if="item.id == 3 && showWxLogin" class="weixin">
							<view class="img" v-if="wechatAvatar">
								<image :src="wechatAvatar" mode="widthFix"></image>
								<text>已绑定</text>
							</view>
							<view class="wbd" v-else>未绑定</view>
						</view>

						<view class="text" v-else>
							{{ item.text }}
						</view>
						<view v-if="item.flag">
							<image src="@/static/imgs/public/right.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 主动Push区分 -->
		<!-- #ifdef APP -->
		<view class="secure" v-show="showWxLogin">
			<view class="tit">
				主动Push区分
			</view>
			<view class="box">
				<view v-for="(item, index) in pushList" :key="item.id" @click="nav_to(item)">
					<view>{{ item.name }}</view>
					<view>
						<view>
							<view class="witch" v-if="index == 0">
								<u-switch v-model="item.checked" inactive-color="#eee"
									active-color="rgba(99, 234, 238, 1)" @change="switchChangePushBv"></u-switch>
							</view>
							<view class="witch" v-if="index == 1">
								<u-switch v-model="item.checked" inactive-color="#eee"
									active-color="rgba(99, 234, 238, 1)" @change="switchChangePushYs"></u-switch>
							</view>
							<view class="witch" v-if="index == 2">
								<u-switch v-model="item.checked" inactive-color="#eee"
									active-color="rgba(99, 234, 238, 1)" @change="switchChangePushBIT"></u-switch>
							</view>
						</view>
						<view v-if="!item.flag">
							<image src="@/static/imgs/public/right.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- 安全与隐私 -->
		<view class="secure">
			<view class="tit">
				安全与隐私 
			</view>
			<view class="box">
				<view v-for="item in secureList" :key="item.id" @click="nav_to(item)">
					<view>{{ item.name }}</view>
					<view>
						<view>
							{{ item.text }}
							<view v-if="item.flag" class="witch">
								<u-switch v-model="checked" inactive-color="#eee" active-color="rgba(99, 234, 238, 1)"
									@change="switchChange"></u-switch>
							</view>
						</view>
						<view v-if="!item.flag">
							<image src="@/static/imgs/public/right.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 其他 -->
		<view class="secure">
			<view class="tit">
				其他
			</view>
			<view class="box">
				<view v-for="item in otherList" :key="item.id" @click="nav_to(item)">
					<view>{{ item.name }}</view>
					<view>
						<view class="renew" v-if="item.flag && isUpdate">
							{{ item.text }}
							<view>更新</view>
						</view>
						<view class="renewFlag" v-else>
							{{ item.text }}
						</view>
						<view v-if="!item.flag">
							<image src="@/static/imgs/public/right.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 用户协议 -->
		<view class="agreement">
			<view class="tit">
				用户协议
			</view>
			<view class="box">
				<view v-for="item in agreementList" :key="item.id" @click="nav_link(index + 1)">
					<view>{{ item.name }}</view>
					<view>
						<view>
							{{ item.text }}
						</view>
						<view>
							<image src="@/static/imgs/public/right.png" mode="aspectFill"></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 退出登录 -->

		<view class="back" @click="nav_logout">
			退出登录
		</view>
		<u-popup v-model="showAvatar" mode='center' width="100%" duration='200'>
			<view class="showAvatar">
				<image :src="avatar" mode="widthFix"></image>
			</view>
		</u-popup>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>

	</view>
</template>

<script>
import {
	number
} from 'echarts';
export default {
	data() {
		return {
			showAvatar: false, //打開頭像預覽
			checked: true, //是否保密  2保密 0所有人可见
			checkedText: false,
			timer: null,
			avatar: '', //头像
			contractAddress: '', //区块链地址
			basicList: [{
				id: 0,
				name: '昵称',
				text: '',
				flag: true
			}, {
				id: 1,
				name: '实名验证',
				text: '',
				flag: false,
				path: 'realName'
			}, {
				id: 2,
				name: '数字身份',
				text: '去设置',
				flag: true,
				path: 'digitalIdentity'
			}, {
				id: 3,
				name: '微信',
				text: '微信号',
				flag: false
			}, {
				id: 4,
				name: '手机号',
				text: '',
				flag: false,
				path: 'selectPay'
			}, {
				id: 5,
				name: '邮箱',
				text: '',
				flag: false,
				path: 'selectPay'
			}, {
				id: 6,
				name: '地址',
				text: '',
				flag: true,
				path: 'addressManagement'
			}],
			secureList: [{
				id: 0,
				name: '登录密码',
				flag: false,
				path: 'selectPay'
			}, {
				id: 1,
				name: '支付密码',
				flag: false,
				path: 'payManage'
			}, {
				id: 2,
				name: '对他人展示我的藏品',
				flag: true,
			}, {
				id: 3,
				name: '吉物仓市场',
				flag: false,
				path: 'hotList'
			}, {
				id: 4,
				name: '我的师徒关系',
				flag: false,
				path: 'mentorship'
			}],
			pushList: [{
				id: 1,
				name: '数字藏品通知',
				flag: true,
				checked: false
			}, {
				id: 2,
				name: '合约交易通知',
				flag: true,
				checked: false
			}, {
				id: 3,
				name: '衍生品突破关键价格提醒',
				flag: true,
				checked: false
			}],
			otherList: [
				// #ifdef APP
				{
					id: 99,
					name: '清除缓存',
					flag: false,
					text: "0MB"
				}, {
					id: 98,
					name: '版本更新',
					flag: true,
					text: '10.03.12'
				},
				// #endif
				{
					id: 2,
					name: '账号注销',
					flag: false,
					text: '',
					path: 'accountLogout'
				}
			],
			agreementList: [{
				id: 0,
				name: '《Bigverse用户协议》',
			}, {
				id: 1,
				name: '《Bigverse隐私协议》',
			}],
			userName: '',
			filePath: '', //裁剪返回的圖片
			imgType: 0, //裁剪的圖片類型
			isLoadding: false,
			cache: "", //app缓存
			appVersion: "",
			isUpdate: false,
			wechatAvatar: '',
			showWxLogin: false
		}
	},
	beforeDestroy() {
		clearTimeout(this.timer)
	},
	async onLoad() {
		await this.user()

		this.secretInfo()
		this.get_version()
		uni.$on('uAvatarCropper', val => {
			this.filePath = val.path;
			this.imgType = val.actionType
			// console.log(val, 'val')
			this.uploadPic()
		})

		// #ifdef APP
		this.get_tag_list()
		// let isPushBv = uni.getStorageSync('isPushBv')
		// let isPushYs = uni.getStorageSync('isPushYs')
		// if (isPushBv && isPushYs) {
		this.pushList[0].checked = !uni.getStorageSync('isPushBv')
		this.pushList[1].checked = !uni.getStorageSync('isPushYs')
		this.pushList[2].checked = !uni.getStorageSync('isPushBit')
		console.log(this.pushList[2].checked, '12312312');

		// } else {
		// 	this.get_tag_list()
		// }
		// #endif

		// #ifdef APP
		plus.cache.calculate((size) => {
			this.cache = (size / 1024 / 1024).toFixed(2)
			this.otherList[0].text = `${this.cache}MB`
		});
		uni.getSystemInfo({
			success: ((res) => {
				console.log('当前版本', res)
				this.otherList[1].text = res.appVersion
				this.appVersion = res.appVersion
				let type = res.platform === 'ios' ? '1' : '3'
				this.getVersion(res.appVersion, type)
			})
		});
		// #endif
		// this.user()
	},
	async onShow() {
		await this.user()
	},
	methods: {
		async user() { //个人信息
			let {
				status,
				result
			} = await this.$api.userInfo({})
			if (status.code == 0) {
				console.log(result)
				uni.setStorageSync('userInfo', JSON.stringify(result))
				if (result.email) {
					uni.setStorageSync('email', result.email)
				}
				// 头像
				this.avatar = result.avatar
				// 名称
				this.basicList[0].text = result.name
				// 实名信息
				this.basicList[1].text = result.authStatus
				// 手机号
				this.basicList[4].text = result.phone
				// 邮箱
				if (result.email.length > 0) {
					this.basicList[5].text = result.email
				} else {
					this.basicList[5].text = '去绑定'
				}
				// 区块链地址
				this.contractAddress = result.contractAddress
				//  2保密 0所有人可见
				if (result.collectionPurview == 0) {
					this.checked = true
					this.checkedText = true
				} else if (result.collectionPurview == 2) {
					console.log('到这里');

					this.checked = false
					this.checkedText = false
				}
			} else {
				this.isLoadding = true
				setTimeout(() => {
					this.isLoadding = false
					this.$Router.push({
						name: "mainLogin",
						// #ifdef H5
						params: {
							url: window.location.hash,
						},
						// #endif
					})
				}, 1500);
			}
		},
		switchChange(e) { //开关切换 是否保密  2= false  0=true
			console.log(this.checked);
			if (!this.checked) {
				this.timer = setTimeout(() => {
					this.checkedText = false
				}, 100)
				this.switchApi(2)
			} else {
				clearTimeout(this.timer)
				this.checkedText = true
				this.switchApi(0)
			}
		},
		async switchApi(index) { //  2保密 0所有人可见
			let {
				result
			} = await this.$api.java_userCollectionsPreview({
				preview: index
			})
			if (result.isSet) {
				uni.showToast({
					title: '切换成功',
					icon: 'none'
				})
			}
		},
		nav_to(item) {
			if (item.name == '昵称') {
				return
			}
			if (item.id == 1 && item.path == "realName") {
				if (item.text == 31) {
					return false
				} else {
					this.$Router.push({
						name: item.path
					})
				}
			}
			if (item.path == "selectPay" && item.id == 0) {
				// 登录密码
				return this.$Router.push({
					name: item.path,
					params: {
						type: 'change',
						path: 'modifyPwd'
					}
				})
			}
			if (item.id == 3 && item.name == '微信') {
				return this.wxLogin()
			}
			if (item.path == "selectPay" && item.id == 4) {
				// 手机号
				return this.$Router.push({
					name: item.path,
					params: {
						phone: JSON.parse(uni.getStorageSync('userInfo')).phone,
						path: 'phoneVerify',
						vtype: 'PHONE_BIND_VERIFY_ME'
					}
				})
			} else if (item.path == 'selectPay' && item.id == 5) {
				// 邮箱
				return this.$Router.push({
					name: item.path,
					params: {
						path: 'emailVerify',
						vtype: 'EMAIL_BIND'
					}
				})
			}
			if (item.id == 99) {
				console.log("清除缓存")
				this.cacheClear()
				return false
			}
			this.$Router.push({
				name: item.path
			})
		},
		async wxLogin() { //微信登录
			uni.removeStorageSync('wx_access_token');
			uni.removeStorageSync('wx_openid');
			// #ifdef H5
			return false
			// #endif
			try {
				const loginRes = await uni.login({
					provider: 'weixin',
					onlyAuthorize: true,
					success: async (res) => {
						if (res.code) {
							this.getWxAccessToken(res.code);
						} else {
							console.error('微信登录失败，未获取到 code');
						}
					},
					fail: (err) => {
						console.error('微信登录失败:', err);
					},
				});
			} catch (error) {
				console.error('微信登录异常:', error);
			}
		},
		async getWxAccessToken(code) {
			const wxApiUrl = 'https://api.weixin.qq.com/sns/oauth2/access_token';
			const params = {
				appid: 'wxad69c7f16fdcf595', // 替换为您的微信 AppID
				secret: '3fcaeac966a12a7ec20c2af681e8e866', // 替换为您的微信 AppSecret
				code,
				grant_type: 'authorization_code',
			};

			const response = await uni.request({
				url: wxApiUrl,
				method: 'GET',
				data: params,
			});
			console.log(response)
			console.log(response[1].statusCode)
			if (response[1].statusCode === 200) {
				console.log(response[1].data.access_token)
				this.getWxUserInfo(response[1].data.access_token, response[1].data.openid);
				return response[1].data;
			} else {
				throw new Error(`获取微信 access_token 失败: ${JSON.stringify(response.data)}`);
			}
		},

		async getWxUserInfo(accessToken, openid) {
			const wxUserInfoUrl = 'https://api.weixin.qq.com/sns/userinfo';
			const params = {
				access_token: accessToken,
				openid,
				lang: 'zh_CN',
			};
			const response = await uni.request({
				url: wxUserInfoUrl,
				method: 'GET',
				data: params,
			});
			console.error(response[1])
			if (response[1].statusCode === 200) {
				let data = response[1].data
				this.obj = {
					openid: data.openid,
					unionId: data.unionid,
					nickname: data.nickname,
					avatar: data.headimgurl
				}
				this.binding()
			} else {
				throw new Error(`获取微信用户信息失败: ${JSON.stringify(response.data)}`);
			}
		},
		//绑定微信
		async binding() {
			let res = await this.$api.java_wechatbind({
				...this.obj
			})
			if (res.status.code == 0) {
				return uni.showToast({
					title: '绑定成功',
					icon: 'none',
					duration: 3000
				})
				setTimeout(() => {
					this.secretInfo()
				}, 500)
			} else {
				return uni.showToast({
					title: res.status.msg,
					icon: 'none'
				})
			}
		},

		focus() { //昵称 获取焦点
			this.userName = this.basicList[0].text
		},
		async blur() { //昵称 失去焦点后
			if (this.userName == this.basicList[0].text) {
				return
			}
			let {
				status,
				result
			} = await this.$api.java_appUserEditNickName({
				name: this.basicList[0].text
			})
			uni.showToast({
				title: status.msg,
				icon: 'none'
			})
		},
		copy() { //复制区块链地址
			uni.setClipboardData({
				data: this.contractAddress,
				success() {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				}
			})
		},
		uploadImg() { //裁剪头像
			console.log(1)
			this.$Router.push({
				name: 'u-avatar-cropper',
				params: {
					destWidth: 300,
					rectWidth: 300,
					fileType: 'jpg',
					actionType: 0
				}
			})
		},
		uploadPic() { //上传
			// #ifdef APP
			let url = `${getApp().globalData.apiUrl}osscenter/appApi/uploadImage`
			// #endif
			// #ifdef H5
			let url = process.env.VUE_APP_JAVA_UPLOADIMAGE
			// #endif
			console.log("url", url)
			uni.uploadFile({
				url,
				filePath: this.filePath,
				header: {
					'Authorization': uni.getStorageSync("token"),
				},
				name: 'image',
				complete: (res) => {
					if (this.imgType === '1') {
						this.setBackPic(JSON.parse(res.data).result.url)
					} else {
						this.setHeadPic(JSON.parse(res.data).result.url)
					}
				}
			});
		},
		async setHeadPic(url) {
			let res = await this.$api.java_userEditAvatar({
				avatar: url
			})
			if (res.status.code === 0) {
				uni.showToast({
					title: '头像设置成功',
					icon: 'none',
				})
				await this.user()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none ',
					duration: 3000
				});
			}
		},
		//登出
		nav_logout() {
			uni.removeStorageSync('token')
			this.$Router.push({
				name: 'mainLogin'
			})
		},
		nav_link(index) {
			if (index === 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: "Bigverse平台服务协议",
						link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
					}
				})
			} else {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: "Bigverse法律声明及隐私政策",
						link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
					}
				})
			}
		},
		cacheClear() {
			plus.cache.clear(() => {
				uni.showToast({
					title: '缓存清理成功',
					icon: 'none ',
					duration: 3000
				});
				this.otherList[0].text = "0MB"
			});
		},
		async getVersion(v, type) {
			console.log(333)
			let res = await this.$api.getAllDTOList({
				type: type,
				userVersion: v
			});
			console.log("版本号", v, res.result.currentVersion)
			// if (Number(v)<Number(res.result.currentVersion)) {
			// 	this.isUpdate = true
			// }
			console.log("版本", res.result)
		},
		async secretInfo() {
			let res = await this.$api.secretInfo({

			});
			if (res.status.code == 0) {
				this.wechatAvatar = res.result.wechatAvatar
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_version() {
			let res = await this.$api.java_commonconfigInfo({
				name: 'ios_apple_pay_version',
			});
			if (res.status.code == 0) {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = res.result.value
				console.log(curV)
				console.log(reqV)
				if (curV == reqV) {
					this.showWxLogin = false
					// #ifdef APP
					if (uni.getSystemInfoSync().platform == 'ios') {
						this.basicList.splice(3, 1);
						this.secureList.splice(4, 1);
					}
					// #endif
				} else {
					this.showWxLogin = true
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		switchChangePushBv(e) {
			console.log('藏品', e)
			this.subscribe(e, 'NO_COLLECTIBLES_TAG')
		},
		switchChangePushYs(e) {
			console.log('衍生', e)
			this.subscribe(e, 'NO_TRADE_TAG')
		},
		switchChangePushBIT(e) {
			console.log('衍生bit', e)
			this.subscribeBIT(e, 'TRADE_PRICE_CHANGE_TAG')
		},
		async subscribeBIT(operationType, tag) {
			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform
			let params = {
				deviceToken,
				operationType: operationType ? 1 : 0,
				phoneType,
				tag
			}
			console.log(params)
			let res = await this.$api.subscribe(params);
			if (res.status.code == 0) {
				uni.showToast({
					title: '成功',
					icon: 'none',
					duration: 3000
				});
				this.get_tag_list()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async subscribe(operationType, tag) {
			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform
			let params = {
				deviceToken,
				operationType: !operationType ? 1 : 0,
				phoneType,
				tag
			}
			console.log(params)
			let res = await this.$api.subscribe(params);
			if (res.status.code == 0) {
				uni.showToast({
					title: '成功',
					icon: 'none',
					duration: 3000
				});
				this.get_tag_list()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_tag_list() {
			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform
			let res = await this.$api.tag_list({
				deviceToken,
				phoneType
			});
			console.log(res)
			if (res.status.code == 0) {
				let tags = res.result.tags
				if (tags) {
					let isPushBv = this.containsCharacter(tags, 'NO_COLLECTIBLES_TAG')
					let isPushYs = this.containsCharacter(tags, 'NO_TRADE_TAG')
					let isPushBit = this.containsCharacter(tags, 'TRADE_PRICE_CHANGE_TAG')
					console.log('isPushBv', isPushBv)
					console.log('isPushYs', isPushYs)
					console.log('isPushBit', isPushBit)

					uni.setStorageSync('isPushBv', isPushBv)
					uni.setStorageSync('isPushYs', isPushYs)
					uni.setStorageSync('isPushBit', isPushBit)

					this.pushList[0].checked = !uni.getStorageSync('isPushBv')
					this.pushList[1].checked = !uni.getStorageSync('isPushYs')
					this.pushList[2].checked = uni.getStorageSync('isPushBit')
					this.$forceUpdate()
				} else {
					uni.setStorageSync('isPushBv', true)
					uni.setStorageSync('isPushYs', true)
					uni.setStorageSync('isPushBit', false)
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		containsCharacter(str, char) {
			// 使用 String.prototype.includes 方法检查字符串 str 是否包含字符 char
			return str.includes(char);
		},

	}
}
</script>

<style lang="scss" scoped>
.person {
	color: #fff;
	padding-bottom: 34rpx;
}

.tit {
	font-size: 24rpx;
	font-weight: 700;
	margin-bottom: 30rpx;
}


.headImg {
	text-align: center;
	margin: 79rpx 0 60rpx 0;

	>view:nth-child(1) {
		width: 180rpx;
		height: 180rpx;
		margin: 0 auto 39rpx auto;
		position: relative;

		>image {
			width: 100%;
			height: 100%;
			border-radius: 50%;
		}

		>image:nth-child(2) {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 48rpx;
			height: 48rpx;
			z-index: 3;
		}
	}

	>view:nth-child(2) {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		font-weight: 400;

		>view:nth-child(2) {
			width: 24rpx;
			height: 26rpx;
			margin-left: 15rpx;

			>image {
				width: 100%;
				height: 100%;
			}
		}
	}
}



// 列表
.basic,
.secure,
.agreement {
	width: 678rpx;
	margin: 0 auto 49rpx auto;

	.box {
		width: 100%;
		border-radius: 20rpx;
		overflow: hidden;
		background: rgb(70, 69, 79);

		>view {
			width: 100%;
			height: 120rpx;
			line-height: 120rpx;
			padding: 0 36rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-sizing: border-box;

			>view:nth-child(1) {
				width: fit-content;
				font-size: 28rpx;
				font-weight: 400;
				color: rgba(255, 255, 255, 1);
				white-space: nowrap;
			}

			>view:nth-child(2) {
				display: flex;
				align-items: center;

				.text {
					width: 336rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: var(--default-color3);
					text-align: right;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				// 已实名
				.flags {
					display: flex;
					align-items: center;
					color: var(--default-color3);

					>view {
						width: 90rpx;
						height: 34rpx;
						line-height: 34rpx;
						border-radius: 17rpx;
						background: linear-gradient(180deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
						margin-left: 15rpx;
						color: rgba(53, 51, 62, 1);
						font-size: 20rpx;
						font-weight: 700;
						text-align: center;
					}
				}

				.inp {
					text-align: right;
					color: var(--default-color3);
				}

				// 更新
				.renew {
					color: rgba(99, 234, 238, 1);
					font-size: 28rpx;
					font-weight: 400;
					display: flex;
					align-items: center;

					>view {
						width: 90rpx;
						height: 34rpx;
						line-height: 34rpx;
						border-radius: 17rpx;
						background: linear-gradient(90deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
						text-align: center;
						font-size: 20rpx;
						font-weight: 700;
						color: rgba(53, 51, 62, 1);
						margin-left: 15rpx;
					}
				}

				.renewFlag {

					color: rgba(99, 234, 238, 1);
					font-size: 28rpx;
					font-weight: 400;

				}

				// 保密
				.witch {
					display: flex;
					align-items: center;
				}

				>view:nth-child(2) {
					width: 26rpx;
					height: 25rpx;
					margin-left: 20rpx;

					>image {
						width: 100%;
						height: 100%;
					}
				}

				.weixin {
					width: 200rpx;

					.img {
						font-size: 28rpx;
						font-weight: 400;
						color: var(--default-color3);
						display: flex;
						justify-content: flex-end;
						align-items: center;

						image {
							width: 60rpx;
							border-radius: 50%;
							margin-right: 10rpx;
						}
					}

					.wbd {
						font-size: 28rpx;
						font-weight: 400;
						color: var(--default-color3);
						text-align: right;
					}
				}
			}
		}
	}

}

// 退出登录
.back {
	width: 678rpx;
	height: 120rpx;
	border-radius: 2460rpx;
	background: linear-gradient(90deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
	text-align: center;
	line-height: 120rpx;
	margin: 0 auto;
	font-size: 34rpx;
	font-weight: 700;
	color: rgba(20, 20, 20, 1);
}

.showAvatar {
	width: 750rpx;
	// height: 500rpx;

	>image {
		width: 100%;
		height: 100%;
	}
}

// ::v-deep .u-switch__node[data-v-3a8aa7a9] {
// 	position: relative;
// }

// ::v-deep .u-switch__node[data-v-3a8aa7a9]::before {
// 	width: fit-content;
// 	height: 10rpx;
// 	content: '保密';
// 	position: absolute;
// 	top: -32rpx;
// 	left: 5rpx;
// 	font-size: 20rpx;
// 	font-weight: 700;
// 	color: rgba(53, 51, 62, 1);
// }</style>
