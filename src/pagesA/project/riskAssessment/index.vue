<template>
  <view class="risk-assessment">
    <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
      :background="{ backgroundColor: 'var(--main-bg-color)' }" title="新手引导" title-color="var(--default-color1)"
      title-bold>
    </u-navbar>
    <!-- 第一步：观看视频 -->
    <view v-if="step === 1" class="step-one">
      <view class="video-title">  </view>
      <view class="video-container">
        <!-- 视频组件 -->
        <video id="videoPlayer" src=https://cdn-lingjing.nftcn.com.cn/h5/video/20241219-113102.mov
          style="width: 100%; height: 100%;border-radius: 38rpx"></video>

        <!-- 占位图片，在视频未播放或播放结束时显示 -->
        <!--        <image-->
        <!--            v-if="!isPlaying"-->
        <!--            src="https://cdn-lingjing.nftcn.com.cn/image/20241106/f4a8d3c52fa3fafc21ecd4e96123794e_83x83.png"-->
        <!--            mode="widthFix"-->
        <!--        ></image>-->
      </view>
      <u-button @click="nextStep" :border="false" class="next-btn">我学会了</u-button>
    </view>

    <!-- 第二步：风险评估问卷 -->
    <!-- <view v-else-if="step === 2" class="step-two">
      <view class="question-titleTop">第二步:完成风险评估问卷</view>
      <view class="error" v-if="iserror">您未通过风险评估测试,请重新作答。</view>

      <view class="question" v-for="(question, index) in questions" :key="index">
        <text class="question-title">{{ index + 1 }}. {{ question.question }}</text>
        <u-radio-group v-model="answers[index]" :wrap="true" @change="checkboxGroupChange">
          <u-radio shape="circle" @change="radioChange" v-for="(option, oIndex) in question.answerList" :key="oIndex"
            :name="option.options">
            <text v-if="question.correctOption !== question.selectOption"
              :style="{ color: ((option.options === question.correctOption)) ? '#EC4068' : '#fff' }">
              {{ option.options + '. ' + option.answer }}
            </text>
            <text v-else> {{ option.options + '. ' + option.answer }}</text>
          </u-radio>
        </u-radio-group>
      </view>
      <u-button @click="submitAnswers" :border="false" borderRadius="36" class="submit-btn">提交</u-button>
      <view style="height: 56rpx"></view>
    </view> -->

    <!-- 第三步：评估结果 -->
    <view v-else-if="step === 2" class="step-three">
      <view class="result-container">
        <!--        <u-icon name="success" size="120" color="#00CC00"/>-->
        <image src="https://cdn-lingjing.nftcn.com.cn/image/20241106/8fbdddde22522917342aad69a6eda8a4_126x127.png"
          mode="widthFix"></image>
        <text class="result-text">恭喜您，成功通过风险评估测试！</text>
        <view @click="returnHome" class="return-btn">
          <view class="inside">返回({{ countdown }}S)</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "riskAssessment",
  data() {
    return {
      isPlaying: false,// 控制占位图片显示
      timer: null,
      countdown: 3, // 倒计时初始值为 3 秒
      matchedItems: [],
      iserror: false,
      list: [
        {
          name: 'apple',
          checked: false,
          disabled: false
        },
        {
          name: 'banner',
          checked: false,
          disabled: false
        },
        {
          name: 'orange',
          checked: false,
          disabled: false
        }
      ],
      // u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
      value: 'orange',
      step: 1, // 当前步骤，默认从第 1 步开始
      questionsList:
        ["A. 开杠是一种冒险的行为", "B. 借贷影响市价", "C. 开杠属于杠杆交易"],
      questions: [],
      answers: ["", "", "", ""] // 存储用户的答案
    };
  },
  onLoad() {
    this.fetchAnswers()
  },
  watch: {
    step: {
      immediate: true,
      handler(newVal) {
        if (newVal === 2) {
          this.startCountdown();
        } else {
          // 如果 step 不等于 3，清除倒计时
          this.clearCountdown();
        }
      }
    }
  },
  onUnload() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 视频播放时隐藏占位图片
    onPlay() {
      console.log('开始')

      this.isPlaying = true;
    },
    // 视频播放结束后显示占位图片
    onEnded() {
      console.log('结束')
      this.isPlaying = false;
    },
    clearCountdown() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.countdown = 3; // 重置倒计时显示
      }
    },
    startCountdown() {
      // 每秒更新倒计时
      this.countdown = 3;
      this.timer = setInterval(() => {
        if (this.countdown > 1) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
          uni.switchTab({
            url: '/pages/project/index/indexYs'
          });
        }

      }, 1000);
    },
    // 选中某个复选框时，由checkbox时触发
    checkboxChange(e) {
      //console.log(e);
    },
    // 选中任一checkbox时，由checkbox-group触发
    checkboxGroupChange(e) {
      // console.log(e);
    },
    async fetchAnswers() {
      let res = await this.$api.question();
      if (res.status.code == 0) {
        // this.questions = res.result.questionList;
        this.questions = res.result.questionList.map(question => ({
          ...question,
          answerList: question.answerList.map(answer => ({
            ...answer,
            checked: false
          }))
        }));
        console.dir(this.questions, 'dati')
      }

    },
    // 选中某个单选框时，由radio时触发
    radioChange(e) {

      // this.fetchAnswers()

      console.log(e);

    },
    // 选中任一radio时，由radio-group触发
    radioGroupChange(e) {
      console.log(e);
    },
    nextStep() {
      // "[{\"id\":\"1\",\"question\":\"以下关于“开杠吧”平台的描述，哪项是准确的？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"“开杠吧”是一个百分百赚钱的平台。\",\"checked\":false},{\"options\":\"B\",\"answer\":\"我没有任何空余资金，所以可以先借钱或者挪用其他资金来“开杠”，这样可以更快地赚到收益。\",\"checked\":false},{\"options\":\"C\",\"answer\":\"“开杠吧”是专业的数字藏品交易平台，旨在为用户提供多元化的收藏和交易体验，用户需根据自身情况审慎评估交易行为。\",\"checked\":false},{\"options\":\"D\",\"answer\":\"“开杠吧”上的交易完全没有任何风险，用户无需担心资金损失。\",\"checked\":false}],\"selectOption\":\"C\",\"correctOption\":null,\"analysis\":null},{\"id\":\"2\",\"question\":\"BIT指数受哪些因素影响？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"BTC和ETH\",\"checked\":false},{\"options\":\"B\",\"answer\":\"SOL和DOGE\",\"checked\":false},{\"options\":\"C\",\"answer\":\"BNB和XRP\",\"checked\":false},{\"options\":\"D\",\"answer\":\"以上均会影响\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null},{\"id\":\"3\",\"question\":\"在平台使用“开杠”¥1.0的价格唱多100元，10倍杠杆，当价格到¥2.0时。盈利多少？（盈利=开仓本金*杠杆*当前价格/开仓价格）\",\"answerList\":[{\"options\":\"A\",\"answer\":\"1元\",\"checked\":false},{\"options\":\"B\",\"answer\":\"10元\",\"checked\":false},{\"options\":\"C\",\"answer\":\"100元\",\"checked\":false},{\"options\":\"D\",\"answer\":\"1000元\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null},{\"id\":\"4\",\"question\":\"以下关于”开杠吧“的说法不正确的是？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"行情波动较大时，使用市价交易可以更快速成交，避免错过行情。\",\"checked\":false},{\"options\":\"B\",\"answer\":\"设置止盈止损，可以有效的控制风险。\",\"checked\":false},{\"options\":\"C\",\"answer\":\"当价格波动巨大时，收益和风险都会较大，需要合理判断交易方式。\",\"checked\":false},{\"options\":\"D\",\"answer\":\"杠杆设置越高越好。只放大收益，不放大风险。\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null}]"
      // this.step++;
      this.submitAnswers()
    },
    async submitAnswers() {
      console.log(this.answers, 12312312312)

      // 检查所有问题是否已回答
      // for (let index = 0; index < this.answers.length; index++) {
      //   if (this.answers[index] === "") {
      //     uni.showToast({
      //       title: `请完成第${index + 1}题`,
      //       icon: "none"
      //     });
      //     return
      //     break; // 找到第一个空题目后停止循环
      //   } else {
      //     this.questions.forEach((question, index) => {
      //       question.selectOption = this.answers[index];
      //     });
      //   }
      // }


      // this.questions.forEach((question, qIndex) => {
      //   console.log(`题目 ${qIndex + 1} 的答案选项状态：`);
      //   // 筛选出 checked 为 true 的选项的 options，并用逗号连接
      //   const selectedOptions = question.answerList
      //       .filter(answer => answer.checked)
      //       .map(answer => answer.options)
      //       .join(","); // 多选的选项用逗号分隔
      //
      //   // 将结果赋值给 selectOption
      //   question.selectOption = selectedOptions || null;
      //   question.answerList.forEach((answer, aIndex) => {
      //     if (answer.checked) {
      //       console.log(`选项 ${answer.options} 被选择 (checked: true)`);
      //     } else {
      //       console.log(`选项 ${answer.options} 未选择 (checked: false) 都没选`);
      //     }
      //   });
      // });
      console.log(this.questions)
      let arr = [
        {
          "id": "1",
          "question": "以下关于“开杠吧”平台的描述，哪项是准确的？",
          "answerList": [
            {
              "options": "A",
              "answer": "“开杠吧”是一个百分百赚钱的平台。",
              "checked": false
            },
            {
              "options": "B",
              "answer": "我没有任何空余资金，所以可以先借钱或者挪用其他资金来“开杠”，这样可以更快地赚到收益。",
              "checked": false
            },
            {
              "options": "C",
              "answer": "“开杠吧”是专业的数字藏品交易平台，旨在为用户提供多元化的收藏和交易体验，用户需根据自身情况审慎评估交易行为。",
              "checked": false
            },
            {
              "options": "D",
              "answer": "“开杠吧”上的交易完全没有任何风险，用户无需担心资金损失。",
              "checked": false
            }
          ],
          "selectOption": 'C',
          "correctOption": null,
          "analysis": null
        },
        {
          "id": "2",
          "question": "BIT指数受哪些因素影响？",
          "answerList": [
            {
              "options": "A",
              "answer": "BTC和ETH",
              "checked": false
            },
            {
              "options": "B",
              "answer": "SOL和DOGE",
              "checked": false
            },
            {
              "options": "C",
              "answer": "BNB和XRP",
              "checked": false
            },
            {
              "options": "D",
              "answer": "以上均会影响",
              "checked": false
            }
          ],
          "selectOption": 'D',
          "correctOption": null,
          "analysis": null
        },
        {
          "id": "3",
          "question": "在平台使用“开杠”¥1.0的价格唱多100元，10倍杠杆，当价格到¥2.0时。盈利多少？（盈利=开仓本金*杠杆*当前价格/开仓价格）",
          "answerList": [
            {
              "options": "A",
              "answer": "1元",
              "checked": false
            },
            {
              "options": "B",
              "answer": "10元",
              "checked": false
            },
            {
              "options": "C",
              "answer": "100元",
              "checked": false
            },
            {
              "options": "D",
              "answer": "1000元",
              "checked": false
            }
          ],
          "selectOption": 'D',
          "correctOption": null,
          "analysis": null
        },
        {
          "id": "4",
          "question": "以下关于”开杠吧“的说法不正确的是？",
          "answerList": [
            {
              "options": "A",
              "answer": "行情波动较大时，使用市价交易可以更快速成交，避免错过行情。",
              "checked": false
            },
            {
              "options": "B",
              "answer": "设置止盈止损，可以有效的控制风险。",
              "checked": false
            },
            {
              "options": "C",
              "answer": "当价格波动巨大时，收益和风险都会较大，需要合理判断交易方式。",
              "checked": false
            },
            {
              "options": "D",
              "answer": "杠杆设置越高越好。只放大收益，不放大风险。",
              "checked": false
            }
          ],
          "selectOption": 'D',
          "correctOption": null,
          "analysis": null
        }
      ]

      // [{\"id\":\"1\",\"question\":\"以下关于“开杠吧”平台的描述，哪项是准确的？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"“开杠吧”是一个百分百赚钱的平台。\",\"checked\":false},{\"options\":\"B\",\"answer\":\"我没有任何空余资金，所以可以先借钱或者挪用其他资金来“开杠”，这样可以更快地赚到收益。\",\"checked\":false},{\"options\":\"C\",\"answer\":\"“开杠吧”是专业的数字藏品交易平台，旨在为用户提供多元化的收藏和交易体验，用户需根据自身情况审慎评估交易行为。\",\"checked\":false},{\"options\":\"D\",\"answer\":\"“开杠吧”上的交易完全没有任何风险，用户无需担心资金损失。\",\"checked\":false}],\"selectOption\":\"C\",\"correctOption\":null,\"analysis\":null},{\"id\":\"2\",\"question\":\"BIT指数受哪些因素影响？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"BTC和ETH\",\"checked\":false},{\"options\":\"B\",\"answer\":\"SOL和DOGE\",\"checked\":false},{\"options\":\"C\",\"answer\":\"BNB和XRP\",\"checked\":false},{\"options\":\"D\",\"answer\":\"以上均会影响\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null},{\"id\":\"3\",\"question\":\"在平台使用“开杠”¥1.0的价格唱多100元，10倍杠杆，当价格到¥2.0时。盈利多少？（盈利=开仓本金*杠杆*当前价格/开仓价格）\",\"answerList\":[{\"options\":\"A\",\"answer\":\"1元\",\"checked\":false},{\"options\":\"B\",\"answer\":\"10元\",\"checked\":false},{\"options\":\"C\",\"answer\":\"100元\",\"checked\":false},{\"options\":\"D\",\"answer\":\"1000元\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null},{\"id\":\"4\",\"question\":\"以下关于”开杠吧“的说法不正确的是？\",\"answerList\":[{\"options\":\"A\",\"answer\":\"行情波动较大时，使用市价交易可以更快速成交，避免错过行情。\",\"checked\":false},{\"options\":\"B\",\"answer\":\"设置止盈止损，可以有效的控制风险。\",\"checked\":false},{\"options\":\"C\",\"answer\":\"当价格波动巨大时，收益和风险都会较大，需要合理判断交易方式。\",\"checked\":false},{\"options\":\"D\",\"answer\":\"杠杆设置越高越好。只放大收益，不放大风险。\",\"checked\":false}],\"selectOption\":\"D\",\"correctOption\":null,\"analysis\":null}]
      // let res = await this.$api.answer({ questionListStr: JSON.stringify(this.questions) });
      let res = await this.$api.answer({ questionListStr: JSON.stringify(arr) });

      if (res.status.code == 0) {
        if (res.result.isCorrect) {
          setTimeout(() => {
            this.$Router.pushTab({
              name: 'contract-BITindex'
            })
          }, 500);

          // uni.showToast({
          //   title: "恭喜你，回答正确！",
          //   icon: "none"
          // });
          // setTimeout(() => {
          //   this.nextStep();
          // }, 1000)
        } else {
          // this.iserror = true;
          // this.matchedItems = res.result.questionList.filter(item => item.selectOption !== (item.correctOption).replace(/\"/g, ""))
          //   .map(item => ({ id: item.id, correctOption: (item.correctOption).replace(/\"/g, "") }));
          // console.log(this.matchedItems, 'matchedItems')
          // uni.showToast({
          //   title: "回答错误，请重新作答",
          //   icon: "none"
          // })
          // this.questions = res.result.questionList.map(question => {
          //   return {
          //     ...question,
          //     correctOption: question.correctOption.replace(/\"/g, "")  // 去除引号
          //   };
          // });
          // console.log(this.questions, '后')
          // uni.pageScrollTo({
          //   scrollTop: 0,
          //   duration: 300 // 滚动时长
          // });
        }
      }
    },
    returnHome() {
      // 模拟返回主页面或重置评估流程
      this.step = 1;
      this.answers = [];
      // uni.switchTab({
      //   url: '/pages/project/index/indexYs'
      // });
      this.$Router.pushTab({
        name: 'contract-BITindex'
      })
    }
  }
}
  ;
</script>

<style scoped lang="scss">
::v-deep .uicon-checkbox-mark:before {
  content: "" !important;
}

::v-deep .u-radio__icon-wrap--checked {
  background-color: transparent !important;
  border: none !important;
  background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20241106/98c0368f3efeb5cddd9e0f6988b08df8_41x41.png") !important;
  background-size: cover;
}

::v-deep .u-radio__icon-wrap--circle {
  background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20241106/601d17e66d0caea39d75a33401ca871f_41x41.png");
  background-size: 100%;
}

::v-deep .u-hairline-border::after {
  border: none;
}

::v-deep .u-radio__label {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 34rpx;
  margin-left: 23rpx;
}

::v-deep .u-radio {
  margin-top: 20rpx;

}

.risk-assessment {
  color: #ffffff;
}

.step-two {
  .error {
    display: flex;
    align-items: center;
    height: 61rpx;
    padding-left: 37rpx;
    background: rgba(99, 234, 238, 0.3);
    margin-bottom: 30rpx;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 26rpx;
    color: #FFFFFF;
    line-height: 34rpx;
  }

  .question {
    padding: 0 60rpx 73rpx 39rpx;
    display: flex;
    flex-direction: column;
  }

  .question-titleTop {
    padding: 100rpx 0 44rpx 36rpx;
    display: block;
  }
}

.video-title {
  padding: 100rpx 0 44rpx 36rpx;
  display: block;
  font-weight: 800;
  font-size: 34rpx;
  color: #FFFFFF;
  line-height: 45rpx;
}

.video-container {
  height: 420rpx;
  margin: 0 36rpx 120rpx 38rpx;
  background: #34323E;
  border-radius: 25rpx;
  border: 1px solid #50EFED;
  display: flex;
  justify-content: center;
  align-items: center;

  image {
    width: 83rpx;
    height: 83rpx;
  }
}

.next-btn,
.submit-btn {
  border: none !important;
  height: 88rpx;
  background: linear-gradient(0deg, #EF91FB, #40F8EC);
  border-radius: 38rpx;
  font-family: PingFang SC;
  font-weight: 800;
  font-size: 34rpx;
  color: #000000;
  line-height: 45rpx;
  margin: 0 34rpx 0 36rpx;
}

.question-title {
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 26rpx;
  color: #63EAEE;
  line-height: 34rpx;
}

.return-btn {
  width: 403rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
  background: linear-gradient(0deg, #EF91FB, #40F8EC);
  border-radius: 38rpx;

  .inside {
    width: 399rpx;
    border-radius: 38rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #FFFFFF;
    line-height: 45rpx;
    height: 84rpx;
    background: #36343F;
  }
}

.result-container {
  margin-top: 180rpx;

  image {
    width: 126rpx;
    height: 126rpx;
  }


  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-text {
  font-family: PingFang SC;
  font-weight: 800;
  font-size: 34rpx;
  color: #FFFFFF;
  line-height: 45rpx;
  margin: 130rpx 0 85rpx 0;
}
</style>
