<template>
    <view class="container">
        <u-navbar :border-bottom="false" title="Transaction History">
        </u-navbar>
        <view style="height: 40rpx;"></view>
        <view class="tab-filter">
            <view v-for="(tab, index) in tabs" :key="index" :class="['tab-item', { active: activeTab === tab.value }]"
                @click="confirmType(tab)">
                <text> {{ tab.label }}</text>
            </view>
        </view>
        <view class="Transaction_History">

            <view class="Transaction_list">
                <view class="Transaction_item flex_divide" v-for="(item, index) in list" :key="index">
                    <view class="tran_left flex-column ">
                        <view class="times">{{ formatDate(item.ctime) }}</view>
                        <view class="add">{{ item.hash || '--' }}</view>
                    </view>
                    <view class="tran_right flex_y">
                        <view class="info flex-column-end">
                            <view class="plusoradd" :style="{ color: item.amount >= 0 ? '#02B632' : '#D72D4A' }">{{
                                item.amount
                            }} {{
                                    item.symbol }}</view>
                            <view class="num">-- {{ item.symbol }}</view>
                        </view>
                        <view class="btn flex_all deposit">{{ getTabLabel(item.type) }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>


<script>
export default {
    data() {
        return {
            page: {
                pageNum: 1,
                pageSize: 10,
            },
            activeTab: 'all', // 默认选中 'All'
            tabs: [
                { label: 'All', value: 'all' },
                { label: 'Deposit', value: 'deposit' },
                { label: 'Send', value: 'send' },
                { label: 'Received', value: 'received' },
                { label: 'Swap', value: 'swap' },
                { label: 'Withdraw', value: 'withdraw' },
            ],
            list: []
        }
    },
    computed: {
        activeTab() {
            return this.value;
        },
        getTabLabel() {
            return (type) => {
                const tab = this.tabs.find(tab => tab.value === type);
                return tab ? tab.label : 'Unknown';
            };
        }
    },
    onLoad() {
        this.getList()
    },
    methods: {
        confirmType(tab) {
            this.activeTab = tab.value
            this.getList()
        },
        async getList() {
            let res = await this.$api.getTransactionPaged({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                symbol: "",
                type: this.activeTab,
                // beginTime: "",
                // endTime: ""
            })
            if (res.code == 200) {
                if (this.page.pageNum) {
                    this.list = res.result.data
                } else {
                    this.list = this.list.concat(res.result.data)
                }
            } else {
                this.$u.toast(res.msg)
            }
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        formatDate(isoString) {
            if (!isoString) return
            const [date, time] = isoString.split('T'); // 分割日期和时间
            const formattedTime = time.split('.')[0];  // 去掉毫秒部分
            return `${date} ${formattedTime}`;
        },
    }
}
</script>

<style lang="scss" scoped>
.tab-filter {
    display: flex;
    // padding: 10px;
    // justify-content: center;
    gap: 12rpx;
    margin: 40rpx 32rpx;
    overflow-x: auto;

    .tab-item {
        padding: 8rpx 24rpx;
        border-radius: 40rpx;
        background: #FFF3F6;
        transition: all 0.3s;
        // min-width:120rpx;
        width: fit-content;
        display: flex;
        color: #000;
        align-items: center;
        justify-content: center;

        &:first-child {
            padding: 8rpx 40rpx;

        }

        text {
            display: block;
            // width: 90rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 160%;
            letter-spacing: 0%;
            text-align: center;

        }

        &.active {
            background: #ff6688;
            color: #fff;
        }
    }
}

.Transaction_History {
    margin: 40rpx 32rpx;

    .Transaction_title {
        .left {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 18*2rpx;
            line-height: 100%;
            letter-spacing: 0%;
            color: #000;
        }

        .right {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            letter-spacing: 0%;
            text-align: center;
            text-decoration: underline;
            text-decoration-style: solid;
            text-decoration-offset: 15%;
            text-decoration-thickness: 1.5px;
            color: #FF82A3;
        }
    }

    .Transaction_list {
        margin-top: 30rpx;

        .Transaction_item {
            // height: 108*2rpx;
            // background: #FF82A326;
            // border: 2rpx solid #D9D6D6;
            // border-radius: 40rpx;
            padding-bottom: 40rpx;
            border-bottom: 2rpx solid #E0E0E0;
            margin-top: 20rpx;

            &:first-child {
                margin-top: 0;
            }

            .tran_right {
                .deposit {
                    color: #FF82A3;
                    background: #FFF3F6;
                }

                .send {
                    background: #E6F4EA;
                    color: #008E28;
                }

                .btn {

                    margin-left: 48rpx;
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    text-align: right;
                    vertical-align: middle;
                    padding: 14rpx 20rpx;
                    border-radius: 41*2rpx;
                }

                .info {
                    .plusoradd {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 120%;
                        letter-spacing: 0%;
                        text-align: center;
                        vertical-align: middle;
                    }

                    .num {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 160%;
                        letter-spacing: 0%;
                        text-align: center;
                        vertical-align: middle;
                        color: #333;
                    }
                }
            }

            .tran_left {
                .times {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 160%;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    color: #000;
                }

                .add {
                    margin-top: 12rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 160%;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    color: #999999;
                }
            }

        }
    }
}
</style>