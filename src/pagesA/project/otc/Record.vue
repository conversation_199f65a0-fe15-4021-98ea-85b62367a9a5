<template>
    <view class="container">
        <!-- 标题 -->
        <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false" :title="$t('title.history')"
            backIconColor="#121212" :custom-back="back">
        </u-navbar>
        <!-- :title="title" -->
        <view class="wrapper">
            <!-- 币种： -->
            <view class="item flex-column " @click="toggleRotate">
                <text class="label">{{ $t("Transfer.Currency") }}</text>
                <view class="dropdown" @click="Coinshow = !Coinshow">
                    <text class="picker-text">{{ nowsymbol || 'All' }}</text>
                    <view :class="['arrow', { rotate: isRotated }]">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png"
                            class="icon" />
                    </view>
                    <transition name="expand-slide">
                        <view class="helpoption" v-show="Coinshow">
                            <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                @click="SetCoin(item)">
                                <text>{{ item.symbol }}</text>
                            </view>
                        </view>
                    </transition>
                </view>
            </view>
            <!-- 类型 -->
            <view class="item flex-column " @click="toggleRotatetype">
                <text class="label">{{ $t("Record.type") }}</text>
                <view class="dropdown" @click="Tabshow = !Tabshow">
                    <text class="picker-text">{{ nowtab }}</text>
                    <view :class="['arrow', { rotate: isRotatedType }]">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png"
                            class="icon" />
                    </view>

                    <transition name="expand-slide">
                        <view class="helpoption" v-show="Tabshow">
                            <view v-for="(item, index) in tabs" :key="index" class="Roptions" @click="SetType(item)">
                                <text>{{ item.label }}</text>
                            </view>
                        </view>
                    </transition>

                </view>
            </view>
            <!-- 日期 -->
            <view class="item flex-column " @click="show = true">

                <text class="picker-text">{{ $t("Record.Date") }}</text>

                <view class="calendar">
                    <input type="text" :disabled="true" :value="startDate + ' ~ ' + endDate" class="date-input" />
                    <view class="calendar-icon">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/a5a63c28afef33fd90ede3adc728fee5_57x61.png"
                            class="icon" />
                    </view>
                </view>

            </view>
        </view>

        <!-- 记录列表 -->
        <view class="record-list">
            <view v-for="(item, index) in transactions" :key="index" class="record-item">
                <view class="record-info">
                    <text class="type">{{ item.type }}</text>
                    <text class="date">{{ formatTimestamp(item.createAt) }}</text>
                </view>
                <text class="record-amount">
                    <text :style="{ color: item.amount > 0 ? '#02B632' : '#FF82A3', marginRight: '10rpx' }">
                        {{ item.amount > 0 ? '+' + Number(item.amount) : Number(item.amount) }}
                    </text>
                    {{ item.symbol }}</text>
                <view class="record-status flex_all" :style="getStatusStyle(item.status)">
                    <text class="status">{{ getStatusLabel(item.status) }}</text>
                    <!-- <text class="confirm">11/45 确认数</text>-->
                </view>
            </view>
            <nodata v-if="!transactions.length" />
        </view>

        <u-calendar @change="dateChange" v-model="show" mode="range"></u-calendar>
        <zero-loading type="sword" v-if="loading"></zero-loading>

    </view>
</template>

<script>
import nodata from "../../../components/public/nodata.vue"
export default {
    components: {
        nodata
    },
    data() {
        return {
            loading: false,
            Coinshow: false,
            transactions: [],
            nowtab: 'All',
            Tabshow: false,
            tabs: [
                { label: 'All', value: 'all' },
                { label: 'Deposit', value: 'deposit' },
                // { label: 'Send', value: 'send' },
                // { label: 'Received', value: 'received' },
                { label: 'Swap', value: 'swap' },
                { label: 'Withdraw', value: 'withdraw' },
            ],

            isRotatedType: false,
            show: false, isRotated: false,
            isRotatedCurrency: false,
            startDate: '',
            endDate: '',
            currency: 'USD',
            network: '充值',
            currencyOptions: ['USD', 'USDT'],
            networkOptions: ['充值', '提币'],
            dateRange: ['2023-01-01', '2023-12-31'],
            records: [
                { date: '2023-01-01 15:44:00', type: 'Withdrawal', amount: '+0.124', status: 'Process' },
                { date: '2023-01-02 16:22:00', type: 'Withdrawal', amount: '+0.124', status: 'Completed' },
                { date: '2023-01-03 17:10:00', type: 'Withdrawal', amount: '+0.124', status: 'Completed' },
            ],
            title: "",
            nowsymbol: "",
            pageNum: 1,
            pageSize: 10,
            CoinList: [],
            page: {
                pageNum: 1,
                pageSize: 10,
            }
        };
    },
    watch: {
        Coinshow(val) {
            if (val) {
                this.Tabshow = false
            }
        },
        Tabshow(val) {
            if (val) {
                this.Coinshow = false
            }
        }
    },
    onLoad(options) {

        // this.title = options.type == 'withdraw' ? '提现记录' : '充值记录'
        uni.setNavigationBarTitle({
            title: this.$t("page.Record") // 切换语言后重新设置标题
        })
        this.nowtab = options.type ? options.type : 'All'

        const date = new Date()
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const day = date.getDate()

        let startYear = year
        let startMonth = null

        if (month - 6 <= 0) {
            startMonth = 12 + month - 6
            startYear = year - 1
        } else {
            startMonth = month - 6
        }

        const format = (n) => (n < 10 ? '0' + n : n)

        this.endDate = `${year}-${format(month)}-${format(day)}`
        this.startDate = `${startYear}-${format(startMonth)}-${format(day)}`

        this.getList(this.nowtab)
        this.getAvailableBalance()
    },
    computed: {
        statusOptions() {
            return [
                { value: 1, label: this.$t("Record.Status.Init") },
                { value: 2, label: this.$t("Record.Status.InProgress") },
                { value: 3, label: this.$t("Record.Status.Completed") },
                { value: 4, label: this.$t("Record.Status.Failed") }
            ]
        }
    },
    onReachBottom() {
        console.log(123);

        this.pageNum++
        this.getList()
    },
    methods: {
        // app-查询自己币对和可用余额信息
        async getAvailableBalance() {
            let res = await this.$api.symbolListPaged({
                // pageNum: this.page.pageNum,
                // pageSize: this.page.pageSize
                pageSize: 100
            })
            if (res.code == 200) {
                if (this.page.pageNum == 1) {
                    this.CoinList = res.result.data
                    this.CoinList.unshift({
                        symbol: 'All'
                    })
                } else {
                    this.CoinList = this.CoinList.concat(res.result.data)
                    this.CoinList.unshift({
                        symbol: 'All'
                    })
                }
                // this.fromCurrency = this.CoinList[0].name
                // this.toCurrency = this.CoinList[1].name



                // this.balanceFrom = this.CoinList[0].balance
                // this.balanceTo = this.CoinList[1].balance

            }
        },
        async getList(e) {
            this.loading = true
            let res = await this.$api.getTransactionPaged({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                symbol: this.nowsymbol == 'All' ? '' : this.nowsymbol,
                type: this.nowtab == 'All' ? 'all' : e,
                beginTime: this.toTimestamp(this.startDate),
                endTime: this.toTimestamp(this.endDate)
            })
            if (res.code == 200) {
                this.loading = false
                if (this.pageNum == 1) {
                    this.transactions = res.result.data
                } else {
                    this.transactions = this.transactions.concat(res.result.data)
                }
            } else {
                this.loading = false
                this.$u.toast(res.msg)
            }
        },
        toTimestamp(dateStr) {
            return Math.floor(new Date(dateStr).getTime() / 1000);
        },
        SetType(item) {
            this.nowtab = item.label
            this.pageNum = 1
            this.transactions = []
            this.getList(item.value)
        },
        SetCoin(item) {
            this.nowsymbol = item.symbol
            this.pageNum = 1
            this.transactions = []
            this.getList()
        },
        getStatusStyle(status) {
            if (status == 1 || 3 || 4) {
                return { backgroundColor: '#FFF3F6', color: '#FF82A3' };
            } else if (status == 2) {
                return { backgroundColor: '#E6F4EA', color: "#008E28" };
            }
            return { backgroundColor: '#FFFFFF' }; // 默认颜色（可选）
        },
        getStatusLabel(status) {
            const match = this.statusOptions.find(opt => opt.value === status)
            return match ? match.label : '--'
        },
        toggleRotatetype() {
            this.isRotatedType = !this.isRotatedType;
        },
        toggleRotate() {
            this.isRotated = !this.isRotated;
        },
        dateChange(value) {
            console.log(value);

            this.endDate = value.endDate
            this.startDate = value.startDate
            this.transactions = []
            this.pageNum = 1
            // this.isLoadingStatus = 0
            // this.CountV2()
            this.getList()
        },
        back() {
            this.$Router.back();
        },
        formatDate(isoString) {
            if (!isoString) return
            const [date, time] = isoString.split('T'); // 分割日期和时间
            const formattedTime = time.split('.')[0];  // 去掉毫秒部分
            return `${date} ${formattedTime}`;
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        }
    }
};
</script>

<style scoped lang="scss">
.wrapper {
    padding: 20px;

    display: flex;
    justify-content: space-between;
    margin-top: 44rpx;

    .item {

        margin-bottom: 20rpx;



        .label {
            font-size: 24rpx;
            width: 82rpx;

            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 14*2rpx;
            margin-bottom: 12rpx;
        }

        .calendar {
            display: flex;
            align-items: center;
            background: #FFF3F6;
            height: 30*2rpx;

            border-radius: 40rpx;
            padding: 30rpx 16rpx;
            line-height: 22.4*2rpx;

        }

        .picker-text {
            margin-right: 36rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
            flex: 1;
        }

        .dropdown {
            display: flex;
            align-items: center;
            flex: 1;
            background: #FFF3F6;
            height: 30*2rpx;
            border-radius: 40rpx;
            padding: 0 16rpx;
            position: relative;

            .helpoption {
                overflow-y: auto;
                max-height: 400rpx;
                // width: 85*2rpx;
                transition: transform 0.3s ease, opacity 0.3s ease;
                transform-origin: top;
                /* 设置变换的起点为顶部 */
                z-index: 9999;
                position: absolute;
                top: 80rpx;
                left: 0;
                box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                // background-color: rgba(0, 0, 0, .5);
                background: #fff;
                border-radius: 16*2rpx;
                padding: 16*2rpx;
                opacity: 1;
                //padding: 100rpx;
                // height: 446rpx;
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                &.collapse {
                    transform: scaleY(0) translateY(-100%);
                    /* 缩小至0，并向上移动 */
                    opacity: 0;
                }

                &.expand {
                    transform: scaleY(1) translateY(0%);
                    /* 恢复到正常大小，并位置恢复 */
                    opacity: 1;

                }

                >view {

                    padding: 15rpx 0;
                    display: flex;
                    align-items: center;

                    image {
                        width: 40rpx;
                        height: 30rpx;
                    }

                    text {
                        margin-left: 20rpx;
                        display: block;
                        font-family: Gilroy-Bold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        color: #000;
                    }
                }
            }


            .picker-text {
                margin-right: 36rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #000;
                flex: 1;
            }

            .arrow {
                width: 28rpx;
                height: 28rpx;
                transition: transform 0.3s ease;
                font-size: 22rpx;

                &.rotate {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .date-input {
        flex: 1;
        // padding: 10rpx;
        border-radius: 5rpx;
        line-height: 22.4*2rpx;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 28rpx;
        color: #000;

    }

    .calendar-icon {
        width: 26rpx;
        height: 28rpx;
        margin-left: 18rpx;
        margin-top: -10rpx;
    }

    .icon {
        width: 100%;
        height: 100%;
    }
}

.container {
    color: #121212;
}

.header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
}

.form-item {
    margin-bottom: 20px;
}

.record-list {

    .record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 40rpx 32rpx;
        border-top: 2rpx solid #E0E0E0;

        &:last-child {
            border-bottom: 2rpx solid #E0E0E0;
        }

        .record-info {
            display: flex;
            flex-direction: column;

            .type {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #000;
            }

            .date {
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;

                margin-top: 12rpx;
                color: rgba(0, 0, 0, .5);
            }
        }


        .record-status {
            height: 31*2rpx;
            border-radius: 82rpx;
            padding: 14rpx 20rpx;



            .status {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                text-align: right;
                // color: #fff;
            }

            .confirm {
                color: rgba(0, 0, 0, .5);
                font-size: 20rpx;
                margin-top: 10rpx;
            }
        }

        .record-amount {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            text-align: center;
            color: #000;
        }



        .record-status {
            font-weight: bold;
        }
    }
}



// .record-type,
// .record-amount,
// .record-status {
//     color: #121212;
// }</style>