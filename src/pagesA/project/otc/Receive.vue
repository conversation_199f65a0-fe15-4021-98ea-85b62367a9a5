<template>
    <view class="container">
        <!-- 标题 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="Receive" :custom-back="back">
            <view slot="right" class="search-box" @click="nav_to('Record', 'swap')">
                <image
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>

        <view class="form-container">
            <view class="form-item">
                <text>Receive Currency</text>
                <view class="inputs flex_divide" @click="showcoin = true">
                    <view class="input_view flex_y">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/098bd983e0cb24382da5927a853274b9_80x80.png" />
                        <view class="coins">
                            <!-- <image
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/72e5d75d2638d2439b2d1037ca19c985_80x80.png" /> -->
                            <text v-if="currency">{{ currency }}</text>
                            <text class="pleacholder" style="color: #666 ">{{ 'Search' }}</text>
                        </view>
                    </view>
                    <view class="right" v-if="currency">
                        <image class="delete"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/a318d26720a622f4da0651ecd3ebb12d_112x113.png" />
                    </view>
                </view>
            </view>


            <view class="form-item">
                <text>Network</text>
                <view class="inputs flex_divide" @click="showcoin = true">
                    <view class="input_view">AVAX C-Chain</view>
                    <view class="right">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/2ac88d2d91400e2686de9a908f6a8e41_96x96.png" />
                    </view>
                </view>
                <!-- <u-input height="96" v-model="formData.accountName" placeholder="请输入账户名称" /> -->
            </view>


            <view class="content_code ">
                <view class="qr_div flex_x">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="444rpx"
                        :options="options"></uv-qrcode>
                </view>
                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">Address</view>
                        <view class="name">******************************************</view>
                    </view>
                    <view class="copy flex_all">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">Memo</view>
                        <view class="name">old Fe 666</view>
                    </view>
                    <view class="copy flex_all">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">Minimum Deposit</view>
                        <!-- <view class="name">**********************************</view> -->
                    </view>
                    <view class="rights flex_all">
                        0.0005 BTC
                    </view>
                </view>

                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">Estimated Arrival Time</view>
                    </view>
                    <view class="rights flex_all">
                        10 min
                    </view>
                </view>

            </view>

            <view class="warning">
                This address is exclusively for BTC deposits. Do not use it for sending inscriptions, NFTs, or any other
                assets. Any non-BTC assets sent to this address will be permanently lost and cannot be recovered or
                refunded.
            </view>
        </view>

    </view>
</template>

<script>
export default {
    data() {
        return {
            currency: "",
            isShowType: false,
            isShownetwork: false,
            qrcodeUrl: "******************************************",
            network: '',
            address: '******************************************',
            memo: 'memo demo',
            currencyOptions: [{
                value: 'USDT',
                label: 'USDT'
            }
                // , {
                //     value: 'BTC',
                //     label: 'BTC'
                // }, {
                //     value: 'ETH',
                //     label: 'ETH'
                // }
            ],
            networkOptions: [{
                value: 'ERC-20',
                label: 'ERC-20'
            }, {
                value: 'TRC-20',
                label: 'TRC-20'
            },
            {
                value: 'ETH',
                label: 'ETH'
            }
            ],
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            codeSize: 240,

        };
    },
    onLoad() {
        this.codeSize = 170 * (uni.upx2px(200) / 100)
    },
    methods: {
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) {
            this.network = e[0].value
        },
        nav_charge_record() {
            this.$Router.push({
                name: 'Record',
                params: {
                    type: 'deposit'
                }
            })
        },
        copyAddress() {
            uni.setClipboardData({
                data: this.address,
                success() {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                    });
                },
            });
        },
        copyMemo() {
            uni.setClipboardData({
                data: this.memo,
                success() {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                    });
                },
            });
        },
        back() {
            this.$Router.back();
        }
    },
};
</script>

<style scoped lang="scss">
.container {
    padding: 32rpx;
    padding-bottom: 300rpx;

    .form-container {
        margin-top: 64rpx;
        margin-bottom: 20px;

        .warning {
            margin-top: 52rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            padding: 16*2rpx;
            background: #FFF3F6;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #666;
        }

        .content_code {
            margin-top: 40rpx;
            border-radius: 20*2rpx;
            border-width: 2rpx;
            padding: 84rpx 42rpx 52rpx 42rpx;
            border: 2rpx solid #D9D6D6;
            width: 100%;

            .qr_div {
                margin-bottom: 84rpx;
            }

            .content-item {
                margin-bottom: 52rpx;

                &:last-child {
                    margin: 0rpx;
                }

                .rights {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;
                    color: #000;
                }

                .names {
                    .cointitile {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #666;
                    }

                    .name-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 22.4*2rpx;
                        color: #666;
                    }

                    .name {
                        width: 240*2rpx;
                        display: block;
                        word-break: break-all;
                        /* 超出宽度时强制换行 */
                        margin-top: 14rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #000;
                    }
                }

                .copy {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 20rpx;
                    background: #FFE6ED;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }
        }

        .form-item {
            margin-bottom: 28rpx;
            position: relative;

            .amount {
                text-indent: 16rpx;
            }

            .dao {
                position: absolute;
                bottom: 30rpx;
                left: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            .all {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .label {
                margin-left: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                // line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .icon_serve {
                width: 40rpx;
                height: 40rpx;
            }

            .right_fix {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                width: 48rpx;
                height: 48rpx;
            }

            .inputs_bom {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #666;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;

                .rights {
                    // width: 60rpx;
                    // height: 60rpx;

                    image {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        .pleacholder {}

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .inputs {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #333;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;

                .right {
                    width: 48rpx;
                    height: 48rpx;

                    .delete {
                        width: 56rpx;
                        height: 56rpx;
                    }

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .btn {
            margin-top: 48rpx;
            width: 100%;

            .exchange-btn {
                width: 100%;
                height: 100rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }
        }


    }
}
</style>