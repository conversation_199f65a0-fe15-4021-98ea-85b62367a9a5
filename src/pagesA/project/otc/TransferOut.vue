<template>
    <view v-if="!isSelectCoin" class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('Send.Send')">
            <view slot="right">
                <!-- <image
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" /> -->
                <view class="flex_x">
                    <view class="search-box2" @click="nav_to('Record', 'withdraw')">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
                    </view>
                    <view class="search-box" @click="goChat">
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370421485045899264.png" />
                    </view>
                </view>
            </view>
        </u-navbar>

        <view class="toggle-container">
            <!-- <view class="triangle top" :style="{ left: '50%' }"></view> -->
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab === 'currencies' }" @tap="switchTab('currencies')">
                Currencies
            </view>
            <view class="toggle-item" :class="{ active: activeTab === 'crypto' }" @tap="switchTab('crypto')">
                Crypto
            </view>
            <!-- <view class="triangle bottom" :style="{ left: '50%' }"></view> -->
        </view>

        <!-- 兑换模块 -->
        <view class="form-wrapper">
            <!-- :style="{ backgroundColor: isSwapped ? '#e6f0ff' : '' }" -->
            <view class="form-row from">
                <view class="float_box">
                    <view class="from-currency">{{ $t("Send.CurrencyForPayment") }}</view>
                    <view class="flex_divide padding">
                        <u-input v-model="fromAmount" type="digit" @focus="handleFocus" @blur="handleBlur"
                            class="from-input" placeholder="$0"></u-input>
                        <view class="qu" v-if="inputTip && sendObj.balance && !fromAmount">{{
                            withdrawLimit.minWithdrawAmount }} - {{ withdrawLimit.maxWithdrawAmount }}
                            {{
                                sendObj.symbol }}</view>
                        <view class="frominfo" @click="Choose()">
                            <!-- <image :src="fromCurrency.icon" /> -->
                            <text>{{ sendObj.symbol || '--' }}</text>
                            <image :class="{ rotated: isChoose }"
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                        </view>
                    </view>
                </view>


                <view class="available">{{ $t("Send.Available") }} : {{ sendObj.balance || '0.00' }}
                    <!-- <text v-if="fromAmount" style="margin-left: 14rpx;">最大</text> -->
                </view>
            </view>

            <text class="Estimated" v-if="sendObj.symbol && fromAmount">{{ $t("Send.EstimatedAmountToBeReceived") }}: {{
                Number(fromAmount) - Number(fee) }} {{ sendObj.symbol }}</text>

            <view class="recipient-container" v-if="sendObj.symbol && fromAmount">
                <view class="recipient-header">
                    <text class="recipient-title">{{ $t("Send.RecipientAccount") }}</text>
                    <view class="add-button flex_all" hover-class="none" @click="addAccount">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/01c745d782ebe247d165f2176b2b25b4_80x80.png" />
                        {{ $t("Send.AddAccount") }}
                    </view>
                </view>

                <view class="inputs">
                    <image class="query"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/098bd983e0cb24382da5927a853274b9_80x80.png" />
                    <u-input type="text" :clearable="false" class="recipient-input"
                        :placeholder="$t('Send.PleaseSelectTheRecipientAccount')" v-model="searchQuery" />
                    <image class="delete" @click="deletes" v-if="nowAccount"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/655fad2fb478caa37a97a641b8be2241_120x120.png" />
                </view>


                <view class="dropdown" v-if="!nowAccount">
                    <view class="tabbar_view">
                        <u-tabs style="width: 100%;" name="cate_name" bg-color="var(--main-bg-color)"
                            :bar-style="barStyle" :list="tabList" bold inactive-color="#999999"
                            :active-item-style="itemStyle" active-color="#FF82A3" :current="current"
                            @change="change"></u-tabs>
                    </view>

                    <scroll-view :scroll-y="true" @scrolltolower="bottomOutAccount()"
                        class="dropdown-section flex-column">
                        <view class="account-item" v-for="(account, index) in filteredRecentAccounts" :key="index"
                            @click="selectAccount(account)">
                            <!-- <image :src="account.avatar" class="avatar" mode="aspectFill"></image> -->
                            <!-- {{ account }} -->
                            <view class="account-info flex_divide">
                                <text class="name" v-if="account.tag">{{
                                    account.tag || '' }}</text>
                                <text class="bank">{{ account.network || '' }}</text>
                                <text class="account-number">{{
                                    account.address || account.token }}</text>
                            </view>
                            <view @click.stop="checkAccount(account)">
                                <u-checkbox shape="circle" v-model="account.check" active-color="#008E28"></u-checkbox>
                            </view>
                        </view>
                    </scroll-view>
                </view>
                <view class="Add flex_all" v-if="filteredRecentAccounts.length == 0 && !nowAccount"
                    @click="showAddPopup = true">
                </view>
            </view>


            <view class="btn flex-column-all">
                <!-- <text>Please complate your swap within 69 sectionds.</text> -->
                <u-button hover-class="none" :disabled="!sendObj.balance" class="exchange-btn flex_all"
                    @click="Transfer">{{ $t("index.Transfer") }}</u-button>
            </view>
            <!-- <view class="bom">
                <text>Fee: 0.5 GBP</text>
                <text>Estimated Arrival Time : 1 Day</text>
            </view> -->
        </view>

        <!-- 添加账户选择 -->
        <u-popup v-model="showAddPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="popup-content">
                <view class="popup-header flex_divide">
                    <view class="popup-title">{{ $t("Send.PleaseSelectAccountType") }}</view>
                    <image @click="showAddPopup = false"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/5b6a43cb2e49dd69bcc90c50372295b0_92x92.png" />
                </view>
                <u-button hover-class="none" @click="nav_to('AddCount', 'pinkwallet')" class="popup-button" plain>{{
                    $t("Send.pwcount") }}</u-button>
                <!-- <u-button hover-class="none" @click="nav_to('AddCount', 'legal')" class="popup-button" plain>Bank
                    Account</u-button> -->
                <u-button hover-class="none" @click="nav_to('AddCount', 'coin')" class="popup-button" plain>{{
                    $t("Send.TokenAddress") }}</u-button>
            </view>
        </u-popup>

        <!-- 去设置密码 -->
        <u-popup v-model="goSetPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="set-password-modal">
                <view class="set-password-title">{{ $t("Send.SetPassword") }}</view>
                <view class="set-password-description">
                    {{ $t("Send.YouHaventSetALoginPasswordAndAPaymentPasswordYet") }}
                </view>

                <view class="set-password-actions">
                    <view class="set-password-btn set-now-btn" @click="onSetNow">{{ $t("Send.SetNow") }}</view>
                    <view class="set-password-btn later-btn" @click="goSetPopup = false">{{ $t("Send.Later") }}</view>
                </view>
            </view>
        </u-popup>

        <!-- 输入密码 -->
        <u-popup v-model="ConfirmTransfer" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="confirm-transfer-modal">
                <view class="confirm-title">{{ $t("Send.ConfirmTransfer") }}</view>
                <view class="confirm-subtitle">
                    {{ $t("Send.YouAreCurrentlyMakingAPaymentOf") }} {{ fromAmount + sendObj.symbol }}
                </view>

                <view class="confirm-password-label">
                    <text class="plz">{{ $t("Send.PleaseEnterYourPaymentPassword") }}</text>
                    <view class="show-toggle" @click="togglePassword">
                        <image v-if="!showPassword"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250422/dccc9beeb1f2ed5a48725358e8d46b2c_80x80.png" />
                        <image v-else
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250422/1a09eb5fb8ef558c56b3d70396bdfea0_200x200.png" />
                        <!-- <text class="toggle-text">{{ $t("Send.Show") }}</text> -->
                    </view>
                </view>

                <view class="password-box">
                    <view v-for="(digit, index) in 6" :key="index" class="password-digit">
                        {{ showPassword ? password[index] || '' : password[index] ? '•' : '' }}
                    </view>
                    <!-- 输入框透明覆盖 -->
                    <input type="number" maxlength="6" class="hidden-input" v-model="password" @input="handleInput" />
                </view>


                <view class="set-password-actions">
                    <view class="set-password-btn set-now-btn" @click="onSubmit">{{ $t("title.submit") }}</view>
                    <view class="set-password-btn later-btn" @click="ConfirmTransfer = false">{{ $t("title.cancel") }}
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
    <view v-else>
        <transelect @checkAssets="checkAssets" />
    </view>
</template>

<script>
import transelect from "./transcomponents/select"
import store from "@/store/index.js"
export default {
    components: {
        transelect
    },
    data() {
        return {
            activeTab: 'currencies',
            link: "../../../static/serve.html",
            showPassword: false,
            password: '',
            goSetPopup: false,
            ConfirmTransfer: false,
            userInfo: {},
            withdrawLimit: {},
            fee: "",
            sendObj: {},
            isSelectCoin: false,
            inputTip: true,

            current: 0,
            barStyle: {
                'background': '#FF82A3',
                'width': '196rpx',
                'height': '2rpx',
                'border-radius': '0rpx',
                'bottom': '6rpx',
                'z-index': '1'
            },
            itemStyle: {
                'font-size': '28rpx',
                'min-width': '120rpx',
                'z-index': '2'
            },
            accounts: [
                // {
                //     name: 'Li Si', type: 'legal', bank: 'HSBC', number: '6534343', check: false, avatar: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/21b3b75b4c5e9a5b5a77f379a890afbf_120x120.png"
                // },
                // {
                //     name: 'Binance', type: 'coin', bank: ' BTC-Lightning', check: false, number: 'SFRFF', avatar: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/21b3b75b4c5e9a5b5a77f379a890afbf_120x120.png"
                // },
                // { name: 'General Address', type: 'pinkwallet', bank: 'Citibank', number: '8534343', check: false, avatar: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/6740b04b87fd47cf5c48bca177f34c4f_120x120.png" },
                // { name: 'Zhang San', bank: 'HSBC', number: '6534343', check: false, avatar: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/6740b04b87fd47cf5c48bca177f34c4f_120x120.png" },

            ],
            isChoose: false,
            showAddPopup: false,
            searchQuery: '',
            selectedAccount: null,
            isSwapped: false,
            fromAmount: '',
            toAmount: '',
            // 货币数据
            fromCurrency: "",
            toCurrency: { symbol: 'BTC', name: 'Bitcoin', icon: '../../../static/imgs/public/BTC.png' },
            coins: [
                { name: 'Bitcoin', symbol: 'BTC', price: '$42,000', approx: '≈ ¥300,000', icon: 'path/to/bitcoin-icon.png' },
                { name: 'Ethereum', symbol: 'ETH', price: '$3,000', approx: '≈ ¥21,000', icon: 'path/to/ethereum-icon.png' },
                // 添加其他币种数据
            ],
            // fromCurrency: 'BTC', // 默认选择的币种
            // toCurrency: 'ETH',   // 默认选择的币种
            amount: 1000,        // 默认金额

            nowbalance: "",
            Account: {
                pageNum: 1,
                pageSize: 10
            },
            addressList: [],
            nowAccount: "",
            nownetwork: "",
            nowrow: {}
        };
    },
    onLoad(e) {
        uni.setNavigationBarTitle({
            title: this.$t("page.TransferOut") // 切换语言后重新设置标题
        })
        // if (e.fromCurrency) {
        //     this.sendObj.symbol = e.fromCurrency
        // }
        // this.getAvailableBalance()
        // this.getCurrencyFiat()
        // this.getCurrencyCrypto()
    },
    onShow() {
        this.sendObj = {}
        this.getUserInfos()
    },
    computed: {
        tabList() {
            return [
                {
                    name: this.$t("Send.Recent")
                },
                {
                    name: this.$t("Send.Frequent")
                }
            ]
        },
        filteredRecentAccounts() {
            const query = this.searchQuery?.toLowerCase() || ''

            if (!query) {
                return this.accounts
            }

            return this.accounts.filter(account => {
                const token = account.token?.toLowerCase() || ''
                const address = account.address?.toLowerCase() || ''
                const tag = account.tag?.toLowerCase() || ''
                const network = account.network?.toLowerCase() || ''


                return token.includes(query) || address.includes(query) || tag.includes(query) || network.includes(query)
            })
        },
        filteredFrequentAccounts() {
            if (!this.searchQuery) {
                return this.accounts;
            } else {
                return this.accounts.filter(account => { }
                    // account?.token.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    // account?.address.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    // account?.tag.toLowerCase().includes(this.searchQuery.toLowerCase())
                )
            }
        }
    },
    methods: {
        switchTab(tab) {
            this.activeTab = tab
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        togglePassword() {
            this.showPassword = !this.showPassword;
        },
        handleInput(e) {
            this.password = e.target.value.slice(0, 6);
        },
        onSubmit() {
            this.ConfirmTransfer = false
            this.ConfirmTransferActions()
        },
        onCancel() {
            this.showPopup = false;
        },
        onSetNow() {
            // uni.showToast({ title: 'Set Now Clicked', icon: 'none' });
            this.$Router.push({
                name: 'SettingPwd',
                params: {
                    type: 'Pay',
                    email: this.userInfo.email
                }
            })
            this.goSetPopup = false;
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            if (res.code == 200) {
                this.userInfo = res.result
            }
        },
        deletes() {
            this.searchQuery = ''
            this.nowAccount = ""
            this.getsearchUserWithdrawAccountPaged(this.sendObj.symbol) // 选中币种查询地址

        },
        checkAccount(e) {
            console.log(e, 'nowrow');
            this.nowrow = e
            // this.nownetwork = e?.network
            this.nowAccount = e.token || e.address
            this.searchQuery = e.token || e.address
            this.FetchgetWithdrawFee(e)
        },
        bottomOutAccount() {
            this.Account.pageNum++
            this.getsearchUserWithdrawAccountPaged()
        },
        async getsearchUserWithdrawAccountPaged(arg) {
            let res = await this.$api.searchUserWithdrawAccountPaged({
                pageNum: this.Account.pageNum,
                pageSize: this.Account.pageSize,
                symbol: arg,
                searchType: this.current + 1,
            })
            if (res.code == 200) {
                if (this.Account.pageNum == 1) {
                    this.accounts = res.result.data;

                    this.accounts = (res.result.data || []).map(item => ({
                        type: item.type,
                        id: item.id,
                        ...(item.withdrawCryptoDTO || {}), // 避免 null
                        ...(item.withdrawInsideAccountDTO || {}), // 避免 null
                        check: false
                    }));
                    console.log(this.accounts);

                    // this.addressList = [...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data]
                } else {
                    this.accounts = this.accounts.concat(res.result.data)
                    this.accounts = (res.result.data || []).map(item => ({
                        type: item.type,
                        id: item.id,
                        ...(item.withdrawCryptoDTO || {}), // 避免 null
                        ...(item.withdrawInsideAccountDTO || {}), // 避免 null
                        check: false
                    }));
                }
            }
        },
        checkAssets(e) {
            console.log(e, 123);
            this.isSelectCoin = !this.isSelectCoin // 切换send页面
            this.isChoose = !this.isChoose; // 箭头旋转
            this.sendObj = e
            this.getsearchUserWithdrawAccountPaged(e.symbol) // 选中币种查询地址
            this.getWithdrawLimitNow(e)
        },
        async getWithdrawLimitNow(e) {
            let res = await this.$api.getWithdrawLimit({
                symbol: e.symbol
            })
            if (res.code == 200) {
                this.withdrawLimit = res.result
            }
        },
        async FetchgetWithdrawFee(e) {
            let res = await this.$api.getWithdrawFee({
                withdrawType: e.type,
                symbol: this.sendObj.symbol,
                token: e?.token,
                network: e?.network,
                address: e?.address,
                amount: this.fromAmount,

            })
            if (res.code == 200) {
                this.fee = res.result
            } else {
                this.$u.toast(res.msg)
            }
        },
        // async getCurrencyFiat() {
        //     let res = await this.$api.symbolListPaged({
        //         fiat: true,
        //         symbol: "",
        //         pageNum: this.Fiat.pageNum,
        //         pageSize: this.Fiat.pageSize
        //     });
        //     this.FiatList = res.result.data
        // },
        // async getCurrencyCrypto() {
        //     let res = await this.$api.symbolListPaged({
        //         fiat: false,
        //         symbol: "",
        //         pageNum: this.Crypto.pageNum,
        //         pageSize: this.Crypto.pageSize
        //     });
        //     this.cryptoList = res.result.data
        // },
        handleFocus(event) {
            this.inputTip = false
        },
        handleBlur(event) {
            setTimeout(() => {
                this.inputTip = true
            }, 200);
        },
        change(index) {
            this.current = index
            this.accounts = []
            this.getsearchUserWithdrawAccountPaged(this.sendObj.symbol) // 选中币种查询地址

        },
        addAccount() {
            this.showAddPopup = true
            // 实现添加账户的逻辑，可以跳转到添加页面或弹窗
            // console.log('Add Account clicked');
            // uni.navigateTo({
            //     url: '/pages/add-account/add-account' // 假设有一个添加账户页面
            // });
        },
        selectAccount(account) {
            this.$Router.push({
                name: 'AccountDetail',
                params: {
                    account: encodeURIComponent(JSON.stringify(account))
                }
            })
            this.selectedAccount = account;
        },
        Choose() {
            this.isChoose = !this.isChoose;
            this.isSelectCoin = !this.isSelectCoin
        },
        nav_to(name, type) {
            this.$Router.push({
                name: name,
                params: {
                    type: type
                }
            });
        },
        // selectAccount(account) {
        //     this.selectedAccount = account;
        // },
        deselectAccount() {
            this.selectedAccount = null;
        },
        swap() {
            this.isSwapped = !this.isSwapped;
            // 交换 from 和 to 的货币数据
            const tempCurrency = this.fromCurrency;
            this.fromCurrency = this.toCurrency;
            this.toCurrency = tempCurrency;
            // 交换金额
            const tempAmount = this.fromAmount;
            this.fromAmount = this.toAmount;
            this.toAmount = tempAmount;
        },
        async Transfer() {
            if (store.state.shouldVibrate) {
                uni.vibrateShort()
            }
            if (!this.userInfo.tradePassSet) {
                this.goSetPopup = true
                return
            } else {
                this.password = ''
                this.ConfirmTransfer = true
                return
            }
        },
        async ConfirmTransferActions() {
            if (!this.sendObj.symbol || !this.fromAmount || !this.nowrow.type) {
                this.$toast(this.$t("Please.full"));
                // return;
            } else {
                try {
                    // let res = await 
                    this.$api.withdraw({
                        symbol: this.sendObj.symbol,
                        network: this.nowrow.network,
                        amount: this.fromAmount,
                        fee: this.fee || 0,
                        token: this.nowrow.token,
                        address: this.nowrow.address,
                        memo: this.nowrow.memo,
                        withdrawType: this.nowrow.type,
                        tradePass: this.password
                    }).then(res => {
                        if (res.code === 200) {
                            this.sendObj = {}
                            this.fromAmount = ''
                            this.$toast(res.msg);
                        } else {
                            this.$toast(res.msg);
                        }
                    });
                } catch (e) {
                }
            }
        },
    },
    mounted() {
    },
};
</script>

<style scoped lang="scss">
::v-deep .u-scroll-box {
    // display: flex !important;
    // margin-left: -30rpx !important;
}

::v-deep .u-tab-item {
    // width: 98*2rrpx !important;
    padding: 0 46rpx !important;
}

::v-deep .u-tab-bar {
    margin-left: -70rpx !important;
}

.toggle-container {
    position: relative;
    // width: 500rpx;
    margin-top: 27rpx;
    width: 100%;
    height: 96rpx;
    // background-color: #F6F6F6;
    border-radius: 80rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6rpx;
    // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
    background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370432022240649216.png");
    background-size: 100% 100%;
    // overflow: hidden;
    // filter: blur(13.800000190734863px);
    .rounded-triangle-svg {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .toggle-item {
        width: 50%;
        text-align: center;
        font-size: 28rpx;
        z-index: 1;
        font-family: Gilroy;
        font-weight: 500;
        line-height: 32rpx;
        transition: color 0.3s;
        color: rgba(0, 0, 0, .4);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .toggle-item.active {
        color: #fff;
    }

    .toggle-bg {
        height: 80rpx;
        position: absolute;
        // top: -10rpx;
        // border: 1.07px solid #FFFFFF61;
        // bottom: 6rpx;
        width: 328rpx;
        border-radius: 80rpx;
        background-color: #ff6f96;
        // z-index: 0;
        transition: left 0.3s;
        border: 1.07px solid #FFFFFF61;
        display: flex;
        justify-content: center;
        align-items: center;
        // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
    }

    .toggle-bg.currencies {
        left: 6rpx;
        // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
    }

    .toggle-bg.crypto {
        left: 346rpx; // 500rpx - 50% + padding
        // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
    }
}



.payment-container {
    width: 100%;
    //   width: 400px;
    height: 500rpx;
    margin: 40rpx auto;
    border-radius: 8px;
    background-color: #fff;
}


.frominfo {
    display: flex;
    align-items: center;
    padding: 5rpx 8rpx;
    border: 1rpx solid rgb(250, 249, 249);
    border-radius: 26rpx;

    image {
        width: 42rpx;
        height: 42rpx;
    }

    text {
        margin-left: 10rpx;
        font-size: 26rpx;
        color: #333333;
    }
}

// ::v-deep .u-input {
//     height: 60rpx !important;
//     width: 400rpx !important;
// }

.padding {
    align-items: center;
    margin: 10rpx 30rpx 0 30rpx;
    position: relative;

    .qu {
        top: 32rpx;
        position: absolute;
        left: 108rpx;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 32rpx;
        line-height: 120%;
        letter-spacing: 0%;
        color: #000;
    }
}

.container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;

    .popup-content {
        // padding: 20px;
        width: 694rpx;
        margin: 0 32rpx;
        // height: 190*2rpx;
        border-radius: 20*2rpx;
        background: #FFFFFF;
        box-shadow: 14rpx 20rpx 100.3*2rpx 0px #0000001A;

        .popup-header {
            padding: 44rpx 44rpx 32rpx 44rpx;

            image {
                width: 46rpx;
                height: 46rpx;
            }

            .popup-title {
                // margin-bottom: 20px;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 20*2rpx;
                line-height: 24*2rpx;
                letter-spacing: 0%;
                color: #000;
            }
        }



        .popup-button {
            width: 100%;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            color: #333;
            display: flex;
            justify-content: flex-start !important;
            background: transparent !important;
            padding-bottom: 44rpx;

            &:hover {
                background-color: transparent;
                /* 取消 hover 背景颜色 */
            }

            &:active {
                background-color: transparent;
                /* 取消 active 背景颜色 */
            }
        }
    }

    .chart-wrapper {
        z-index: 999;
        width: 100%;
        height: 140px;
        // height: 100%; /* 根据父容器大小自适应 */
        margin: 30rpx 0;
    }

    .form-wrapper {
        margin-top: 64rpx;
        width: 100%;
        border-radius: 6px;
        position: relative;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .swap_btn {
        // position: absolute;
        // left: 50%;
        cursor: pointer;
        transition: transform 0.3s ease-in-out;
        height: 40rpx;
        margin: -20rpx 0;
        // top: 220rpx;
        z-index: 999;
        // margin: 0 auto;
        border-radius: 50%;
        background-color: #FF82A3;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 68rpx;
        height: 68rpx;


        image {
            width: 34rpx;
            height: 30rpx;
        }
    }

    .Estimated {
        text-align: left;
        width: 100%;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 160%;
        letter-spacing: 0%;
        color: #FF82A3;
        margin-top: 32rpx;
    }

    .recipient-container {
        width: 100%;
        margin-top: 48rpx;

        .recipient-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;

            .recipient-title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            .add-button {
                width: 134*2rpx;
                height: 34*2rpx;
                border-radius: 10*2rpx;
                background-color: #FF82A3;
                color: #fff;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;

                image {
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 20rpx;
                }
            }
        }



        .inputs {
            width: 100%;
            position: relative;

            .query {
                position: absolute;
                top: 28rpx;
                left: 38rpx;
                width: 40rpx;
                height: 40rpx;
            }


            .delete {
                position: absolute;
                top: 18rpx;
                right: 18rpx;
                width: 60rpx;
                height: 60rpx;
            }

            .recipient-input {
                // padding: 0 64rpx 0 24rpx;
                box-sizing: border-box;
                border: 2rpx solid #999999;
                border-width: 2rpx;
                border-radius: 10*2rpx;


                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 120%;
                letter-spacing: 0%;

                ::v-deep .u-input__input {
                    height: 48*2rpx !important;
                    width: 398*2rpx !important;
                    padding: 0 98rpx 0 98rpx !important;

                }

                &:focus {
                    border-color: #FF69B4;
                }
            }
        }


    }


    .Add {
        margin: 100rpx auto;
        width: 194*2rpx;
        height: 100rpx;
        border-radius: 128rpx;
        background: #FFE6ED;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 32rpx;
        color: #000;
    }

    .dropdown {
        margin-top: 32rpx;
        border-radius: 12rpx;


        .tabbar_view {
            width: 100%;
        }

        .dropdown-section {
            padding: 16rpx;
            max-height: 500rpx;
            overflow-y: auto;

            .account-item {
                width: 100%;
                display: flex;
                align-items: center;
                padding: 20rpx 0 20rpx 20rpx;
                border-bottom: 2rpx solid #999999;
                cursor: pointer;

                // &:hover {
                //     background-color: #f5f5f5;
                // }

                &:last-child {
                    border-bottom: none;
                }

                .avatar {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    margin-right: 20rpx;
                }

                .account-info {
                    flex: 1;
                    color: #000;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;

                    .name {}

                    .bank {}

                    .account-number {
                        width: 400rpx;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                .radio {
                    width: 32rpx;
                    height: 32rpx;
                    margin-left: 16rpx;
                }
            }
        }
    }

    .form-row {
        width: 100%;
        // display: flex;
        height: 274rpx;
        // align-items: center;
        // margin: -20rpx 0;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        background: #FF82A326;
        border: 2rpx solid #D9D6D6;
        border-radius: 17*2rpx;
        position: relative;

        .float_box {
            position: absolute;
            top: -2rpx;
            left: -2rpx;
            border-radius: 17*2rpx;
            background: #FFFFFF;
            height: 94*2rpx;
            width: 101%;
            border: 2rpx solid #D9D6D6;

            ::v-deep .u-input__input {
                font-family: Gilroy-ExtraBold;
                font-weight: 400;
                font-size: 30*2rpx;
                line-height: 36*2r12px;
                letter-spacing: 0%;
                color: #000;
            }


            .frominfo {
                display: flex;
                transition: transform 0.3s ease-in-out;
                align-items: center;
                justify-content: space-between;
                // padding: 5rpx 8rpx;
                padding: 0 22rpx;
                width: 88*2rpx;
                height: 32*2rpx;
                border-radius: 20*2rpx;
                background: #FF82A333;

                image {
                    width: 48rpx;
                    height: 48rpx;
                }

                text {
                    margin-left: 10rpx;
                    color: #333333;
                    line-height: 38rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    letter-spacing: 0%;

                }
            }
        }

        .available {
            font-size: 24rpx;
            color: #121212;
            display: block;
            // padding: 40rpx 0 0 40rpx;
            position: absolute;
            bottom: 24rpx;
            left: 30rpx;

            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .from-currency,
        .to-currency {
            display: block;
            padding: 30rpx 0 0 30rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #666;
        }

        label {
            flex: 1;
            font-size: 16px;
            color: #333;
        }

        select,
        input {
            flex: 2;
            padding: 10px;
            font-size: 16px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin-left: 20px;
        }
    }

    .bom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        color: #121212;
        margin-top: 16rpx;
        font-weight: 400;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;



        text {
            display: block;
        }

        width: 100%;
    }

    .btn {
        margin-top: 48rpx;
        width: 100%;

        .exchange-btn {
            width: 100%;
            height: 100rpx;
            background: #FF82A3;
            border-radius: 64*2rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;

        }

        // 禁用状态样式
        .exchange-btn[disabled] {
            // background: #FF82A380; // 加透明度效果
            color: #ffffff80;
            opacity: 0.6;
        }
    }


}

.search-box {
    margin-top: 10rpx;
    margin-right: 32rpx;
    background: #F1F1F1;
    border-radius: 50%;
    width: 100rpx;
    height: 100rpx;
    // height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        // padding: 25rpx;
        width: 52rpx;
        height: 52rpx;
    }
}



.rotated {
    transform: rotate(180deg);
}

.set-password-modal {
    background: #fff;
    padding: 42rpx 44rpx;
    width: 90vw;
    border-radius: 40rpx;
    text-align: center;
    box-shadow: 7px 10px 100.3px 0px #0000001A;

    .set-password-title {
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 40rpx;
        line-height: 120%;
        letter-spacing: 0%;
        color: #000;
        margin-bottom: 16rpx;
    }

    .set-password-description {
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 120%;
        color: #666666;
        margin-bottom: 48rpx;
    }

    .set-password-actions {
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .set-password-btn {
            height: 100rpx;
            border-radius: 128rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 120%;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;

        }

        .set-now-btn {
            background: #FF82A3;
            color: #fff;
            line-height: 100rpx;

        }

        .later-btn {
            border: 2rpx solid #999;
            color: #000;
            line-height: 100rpx;

            // background: #fff;
        }

    }

}

.confirm-transfer-modal {
    background: #fff;
    padding: 42rpx 44rpx;
    width: 90vw;
    border-radius: 40rpx;
    text-align: center;
    box-shadow: 7px 10px 100.3px 0px #0000001A;

    .confirm-title {
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 40rpx;
        line-height: 120%;
        letter-spacing: 0%;
        color: #000;
        margin-bottom: 16rpx;
    }

    .confirm-subtitle {
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 120%;
        color: #666666;
        margin-bottom: 32rpx;
    }

    .confirm-password-label {

        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .plz {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            color: #666666;
            white-space: wrap;
            white-space: nowrap;
        }

        .show-toggle {
            display: flex;
            // border: 2rpx solid #D9D7D7;
            align-items: center;
            // gap: 8rpx;
            // border-radius: 134rpx;
            // padding: 12rpx 16rpx;

            image {
                margin-right: 16rpx;
                width: 36rpx;
                height: 36rpx;
            }

            .toggle-text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 100%;
                text-align: center;
                color: #000;
            }
        }
    }

    .password-box {
        display: flex;
        justify-content: space-between;
        gap: 12rpx;
        margin-bottom: 40rpx;
        position: relative;

        .password-digit {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 108rpx;
            height: 90rpx;
            border-radius: 20rpx;
            gap: 10px;
            // padding: 14px;
            border: 2rpx solid #999999;

            text-align: center;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 36rpx;

        }

        .hidden-input {
            position: absolute;
            opacity: 0;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10;
            caret-color: transparent;
        }
    }

    .set-password-actions {
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .set-password-btn {
            height: 100rpx;
            border-radius: 128rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 120%;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;

        }

        .set-now-btn {
            background: #FF82A3;
            color: #fff;
            line-height: 100rpx;

        }

        .later-btn {
            border: 2rpx solid #999;
            color: #000;
            line-height: 100rpx;

            // background: #fff;
        }

    }
}




::v-deep .u-checkbox {
    margin-left: 20rpx;
}
</style>