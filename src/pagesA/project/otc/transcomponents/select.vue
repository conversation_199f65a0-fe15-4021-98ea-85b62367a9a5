<template>
    <view class="select">
        <u-navbar :border-bottom="false" :title="$t('Send.Send')">
            <view slot="right" class="search-box" @click="nav_to('Record', 'swap')">
                <image
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>
        <view class="search">
            <u-input height="102" class="select-input" v-model="searchQuery" :placeholder="$t('Send.SearchAsset')" />
            <image class="img"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250331/f55439ec94ae6499dc7e196641d6b267_80x81.png" />
        </view>

        <view class="allcoin">

            <view class="coin" v-if="!nocoin">
                <!-- 法币 -->
                <!-- <view class="coinitem" v-if="filteredAssetsCash.length">
                    <view class="title">Cash</view>
                    <scroll-view :scroll-y="true" @scrolltolower="bottomOutCash()" class="box">
                        <div class="asset-item" v-for="asset in filteredAssetsCash" :key="asset.id"
                            @click="goAsset(asset)">
                            <div class="asset-info">
                                <img :src="asset.icon" :alt="asset.name" class="asset-icon" />
                                <div class="asset-text">
                                    <div class="asset-name">{{ asset.name }}</div>
                                    <div class="asset-symbol">{{ asset.symbol }}</div>
                                </div>
                            </div>
                            <div class="asset-value">
                                <div class="amount">${{ asset.balance }}</div>
                                <div class="quantity">{{ asset.quantity }} {{ asset.symbol }}</div>
                            </div>
                        </div>
                    </scroll-view>
                </view>
                <view style="height: 40rpx;" v-if="filteredAssetsCash.length"></view> -->
                <!-- 加密货币 -->
                <view class="coinitem" v-if="filteredAssetsCrypto.length">
                    <view class="title">{{ $t("Send.Crypto") }}</view>
                    <scroll-view :scroll-y="true" @scrolltolower="bottomOutCrypto()" class="box">
                        <div @click="goAsset(asset)" class="asset-item" v-for="asset in filteredAssetsCrypto"
                            :key="asset.id">
                            <div class="asset-info">
                                <img :src="asset.icon" :alt="asset.name" class="asset-icon" />
                                <div class="asset-text">
                                    <div class="asset-name">{{ asset.name }}</div>
                                    <div class="asset-symbol">{{ asset.symbol }}</div>
                                </div>
                            </div>
                            <div class="asset-value">
                                <div class="amount">{{ formatAmount(asset.balance, asset.price) }}</div>
                                <div class="quantity">{{ asset.balance }} {{ asset.symbol }}</div>
                            </div>
                        </div>
                        <!-- "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250331/0205e244e7bfbfac5ad0eea338b8fa92_120x121.png" -->
                    </scroll-view>
                </view>
                <view style="height: 40rpx;" v-if="filteredAssetsCrypto.length"></view>
                <!-- 没钱 -->
                <view class="coinitem" v-if="filteredAssetsnodata.length">
                    <view class="title">{{ $t("Send.nobalance") }}</view>
                    <!-- @click="goAsset(asset)" -->
                    <scroll-view :scroll-y="true" @scrolltolower="bottomOutNodata()" class="box">
                        <div class="asset-item" v-for="asset in filteredAssetsnodata" :key="asset.id"
                            @click="goAsset(asset)">
                            <div class="asset-info">
                                <img :src="asset.icon" :alt="asset.name" class="asset-icon" />
                                <div class="asset-text">
                                    <div class="asset-name">{{ asset.name }}</div>
                                    <div class="asset-symbol">{{ asset.symbol }}</div>
                                </div>
                            </div>
                            <div class="asset-value">
                                <!-- <div class="amount">${{ asset.usdValue }}</div> -->
                                <!-- <div class="quantity">{{ asset.quantity }} {{ asset.symbol }}</div> -->
                            </div>
                        </div>
                        <!-- "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250331/0205e244e7bfbfac5ad0eea338b8fa92_120x121.png" -->
                    </scroll-view>
                </view>
            </view>

            <nodata v-if="!filteredAssetsnodata.length && !filteredAssetsCrypto.length && !filteredAssetsCash.length" />
        </view>
    </view>
</template>

<script>
import nodata from '@/components/public/nodata.vue'

export default {
    components: {
        nodata,
    },
    data() {
        return {
            nocoin: false,
            searchQuery: "",
            assets1: [
                {
                    name: 'ETH',
                    symbol: 'ETH',
                    icon: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
                    usdValue: '11,256.26',
                    quantity: '11.*********'
                },
                {
                    name: 'USDC',
                    symbol: 'USDC',
                    icon: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
                    usdValue: '11,256.26',
                    quantity: '11.*********'
                },
                {
                    name: 'Bitcoin',
                    symbol: 'BTC',
                    icon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
                    usdValue: '176.12',
                    quantity: '0.0020352'
                },
                {
                    name: 'USDC',
                    symbol: 'USDC',
                    icon: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
                    usdValue: '11,256.26',
                    quantity: '11.*********'
                }
            ],
            assets2: [
                {
                    name: 'SOL',
                    symbol: 'solana',
                    icon: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
                    usdValue: '11,256.26',
                    quantity: '11.*********'
                },
                {
                    name: 'meme',
                    symbol: 'meme',
                    icon: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
                    usdValue: '176.12',
                    quantity: '0.0020352'
                }
            ],
            assets3: [
                {
                    name: 'TRON',
                    symbol: 'tron',
                    icon: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
                    usdValue: '11,256.26',
                    quantity: '11.*********'
                }
            ],
            fiatCoin: [],
            ZeroAssets: {
                pageNum: 1,
                pageSize: 10,
            },
            Fiat: {
                pageNum: 1,
                pageSize: 10,
            },
            Crypto: {
                pageNum: 1,
                pageSize: 10,
            },
            Account: {
                pageNum: 1,
                pageSize: 10
            },
            cryptoList: [],
            FiatList: [],
            page: {
                pageNum: 1,
                pageSize: 20
            },
            CoinList: [],
            zeroBalanceList: [],
            nozerohasNext: false,
        }
    },
    mounted() {
        this.fetchData()
    },
    watch: {
        "$store.state.indexBottom"(val) {
            console.log(val);

            if (this.CoinList.length) {
                this.CoinList = this.CoinList.map(item2 => {
                    const matchedItem = val.find(item1 => item1.baseCoin === item2.symbol);
                    if (matchedItem) {
                        return {
                            ...item2,
                            price: matchedItem.price,
                            percentageChange: matchedItem.percentageChange,
                            targetCoin: matchedItem.targetCoin
                        };
                    }
                    return { ...item2 };
                });
                // this.$emit('userCoin', this.filteredAssetsCrypto)
                // this.cryptobalance = this.filteredAssetsCrypto.reduce((acc, cur) => {
                //     return acc + cur.totalBalance
                // }, 0)
                // console.log(123, this.filteredAssetsCrypto);
            }
        }
    },
    computed: {
        filteredAssetsCash() {
            const query = this.searchQuery.toLowerCase();
            return this.fiatCoin.filter(asset =>
                asset.name.toLowerCase().includes(query) || asset.symbol.toLowerCase().includes(query)
            );
        },
        filteredAssetsCrypto() {
            const query = this.searchQuery.toLowerCase();
            return this.CoinList.filter(asset =>
                asset.name.toLowerCase().includes(query) || asset.symbol.toLowerCase().includes(query)
            );
        },
        filteredAssetsnodata() {
            const query = this.searchQuery.toLowerCase();
            return this.zeroBalanceList.filter(asset =>
                asset.name.toLowerCase().includes(query) || asset.symbol.toLowerCase().includes(query)
            );
        }
    },
    methods: {
        formatAmount(balance, price) {
            if (price === undefined || price === null) {
                return '--';
            }
            const result = Number(balance) * Number(price);
            return `$${result.toFixed(2)}`; // 保留2位小数
        },
        async fetchData() {
            try {
                // 等待两个接口请求完成
                await Promise.all([
                    this.getAvailableBalance(),
                    this.getAvailableBalanceFiat(),
                    this.getZeroAssets()
                ])

                // // 筛选 balance 为 0 的对象
                // this.zeroBalanceList = [
                //     ...this.CoinList.filter(item => item.balance == 0),
                //     ...this.fiatCoin.filter(item => item.balance == 0)
                // ]
            } catch (error) {
                console.error('Failed to fetch data:', error)
                this.zeroBalanceList = []
            }
        },
        async getAvailableBalance() {
            let res = await this.$api.userAvailableCoinList({
                pageNum: this.Crypto.pageNum,
                pageSize: this.Crypto.pageSize,
                zeroAsset: false,
                fiat: false,
            })
            if (res.code == 200) {
                if (this.Crypto.pageNum == 1) {
                    this.CoinList = res.result.data
                } else {
                    this.CoinList = this.CoinList.concat(res.result.data)
                }
            }
        },
        // fiatCoin
        async getAvailableBalanceFiat() {
            let res = await this.$api.userAvailableCoinList({
                pageNum: this.Fiat.pageNum,
                pageSize: this.Fiat.pageSize,
                zeroAsset: false,
                fiat: true,
            })
            if (res.code == 200) {
                if (this.Fiat.pageNum == 1) {
                    this.fiatCoin = res.result.data
                } else {
                    this.fiatCoin = this.fiatCoin.concat(res.result.data)
                }
            }
        },
        // 0 assets
        async getZeroAssets() {
            let res = await this.$api.userAvailableCoinList({
                pageNum: this.ZeroAssets.pageNum,
                pageSize: this.ZeroAssets.pageSize,
                zeroAsset: true,
            })
            if (res.code == 200) {
                this.nozerohasNext = res.result.hasNext
                if (this.ZeroAssets.pageNum == 1) {
                    this.zeroBalanceList = res.result.data
                } else {
                    this.zeroBalanceList = this.zeroBalanceList.concat(res.result.data)
                }
            }
        },
        goAsset(asset) {
            console.log(asset);
            this.$emit("checkAssets", asset);
        },
        bottomOutCash() {
            console.log(123);
            this.Fiat.pageNum++
            this.getAvailableBalanceFiat()
        },
        bottomOutCrypto() {
            this.Crypto.pageNum++
            this.getAvailableBalance()
        },
        bottomOutNodata() {
            if (this.nozerohasNext) {
                this.ZeroAssets.pageNum++
                this.getZeroAssets()
            }
        },
        nav_to(name, type) {
            this.$Router.push({
                name: name,
                params: {
                    type: type
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.select {
    margin: 64rpx 32rpx 0 32rpx;

    .allcoin {
        margin-top: 32rpx;
        gap: 40rpx;
        border-radius: 32rpx;
        padding: 32rpx;
        background: #FFFFFF;
        box-shadow: 14rpx 20rpx 200.6rpx 0rpx #00000026;

        .coinitem {
            .title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 160%;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #FF82A3;
            }

            .box {
                margin-top: 40rpx;
                max-height: 500rpx;

                .asset-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 0 43rpx 0;

                    &:last-child {
                        padding-bottom: 0;
                    }

                    .asset-info {
                        display: flex;
                        align-items: center;

                        .asset-icon {
                            width: 60rpx;
                            height: 60rpx;
                            margin-right: 20rpx;
                        }

                        .asset-text {
                            display: flex;
                            flex-direction: column;

                            .asset-name {
                                font-family: Gilroy-Bold;
                                font-weight: 400;
                                font-size: 28rpx;
                                line-height: 34rpx;
                                color: #000;
                            }

                            .asset-symbol {
                                margin-top: 4rpx;
                                font-family: Gilroy-Medium;
                                font-weight: 400;
                                font-size: 24rpx;
                                line-height: 28rpx;
                                color: #333;
                            }
                        }
                    }

                    .asset-value {
                        text-align: right;

                        .amount {
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 34rpx;
                            text-align: right;
                            color: #000;
                        }

                        .quantity {
                            margin-top: 4rpx;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 24rpx;
                            line-height: 28rpx;
                            letter-spacing: 0%;
                            text-align: right;
                            color: #999999;
                        }
                    }
                }
            }
        }
    }


    .search {

        position: relative;

        .img {
            width: 40rpx;
            height: 40rpx;
            position: absolute;
            top: 31rpx;
            left: 38rpx;
        }

        .select-input {
            height: 102rpx;
            border-radius: 20rpx;
            border: 2rpx solid #999999;
            overflow: hidden;
            text-indent: 98rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 120%;
            color: #000;
            padding-right: 32rpx;

            &::placeholder {
                color: #666666;
            }
        }
    }


}

.search-box {
    margin-top: 10rpx;
    margin-right: 32rpx;
    background: #F1F1F1;
    border-radius: 50%;
    width: 100rpx;
    height: 100rpx;
    // height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        // padding: 25rpx;
        width: 40rpx;
        height: 40rpx;
    }
}
</style>