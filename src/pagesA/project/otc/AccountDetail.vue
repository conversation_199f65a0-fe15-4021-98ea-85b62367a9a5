<template>
    <view class="container">
        <!-- 判断 type 来选择显示不同的表单 -->

        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('Send.AccountDetail')">
            <view slot="right" class="search-box" @click="nav_to('Record')">
                <image
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>

        <!-- USD 页面 -->
        <view v-if="pageType == '1'" class="form-container">
            <view class="title">The current account is a multi-currency account</view>
            <view class="content">
                <view class="content-item flex_divide" v-for="item in BankInfo" :key="item.id">
                    <view class="names flex-column">
                        <view class="name-title">{{ item.titlename }}</view>
                        <view class="name">{{ item.name }}</view>
                    </view>
                    <view class="copy flex_all">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>
            </view>
            <view class="btn flex-column-all">
                <view class="exchange-btn flex_all" @click="convert">Edit Account</view>
                <view class="cancel-btn flex_all" @click="convert">Delete Account</view>
            </view>
        </view>

        <!-- BTC 页面 -->
        <view v-if="pageType == '2'" class="form-container">

            <view class="content">
                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <!-- <view class="name-title">Currency:</view> -->
                        <view class="cointitile">{{ $t("Transfer.Currency") }}:</view>
                    </view>
                    <view class="rights flex_all">
                        {{ nowAccount.symbol }}
                    </view>
                </view>
                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <!-- <view class="name-title">Currency:</view> -->
                        <view class="cointitile">{{ $t("Transfer.Network") }}:</view>
                    </view>
                    <view class="rights flex_all">
                        {{ nowAccount.network }}
                    </view>
                </view>
            </view>
            <view class="content_code ">
                <view class="qr_div flex_x">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="444rpx"
                        :options="options"></uv-qrcode>
                </view>
                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">{{ $t("Transfer.Address") }}</view>
                        <view class="name">{{ nowAccount.address }}</view>
                    </view>
                    <view class="copy flex_all" @click="copy(nowAccount.address)">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">{{ $t("Send.Memo") }}</view>
                        <view class="name">{{ nowAccount.memo || '--' }}</view>
                    </view>
                    <view class="copy flex_all" @click="copy(nowAccount.memo)">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">{{ $t("Transfer.Tag") }}:</view>
                        <!-- <view class="name">**********************************</view> -->
                    </view>
                    <view class="rights flex_all">
                        {{ nowAccount.tag }}
                    </view>
                </view>

            </view>
            <view class="btn flex-column-all">
                <view class="exchange-btn flex_all" @click="Edit">{{ $t("Transfer.EditAccount") }}</view>
                <view class="cancel-btn flex_all" @click="deleteAccount">{{ $t("Transfer.DeleteAccount") }}</view>
            </view>
        </view>

        <!-- pw 页面 -->
        <view v-if="pageType == '3'" class="form-container">
            <view class="content">
                <view class="pink-title flex_divide">
                    <view class="left-title">{{ $t("Send.PinkWalletAccount") }}:</view>
                    <view class="right-title">
                        <!-- <text>+86</text> -->
                        <text style="margin-left: 10rpx;">{{ userInfo.email }}</text>
                    </view>
                </view>
                <view class="qr_div flex_x" style="margin-top: 64rpx;">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="468rpx"
                        :options="options"></uv-qrcode>
                </view>
            </view>

            <view class="btn flex-column-all">
                <view class="exchange-btn flex_all" @click="Edit">{{ $t("Transfer.EditAccount") }}</view>
                <view class="cancel-btn flex_all" @click="deleteAccount">{{ $t("Transfer.DeleteAccount") }}</view>
            </view>

        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // qrcodeUrl: '******************************************',
            qrcodeUrl: "",
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },

            BankInfo: [
                {
                    titlename: "Account Holder Name:",
                    name: "Jane Doe"
                },
                {
                    titlename: "Bank Name:",
                    name: "JPMorgan Chase Bank, N.A"
                },
                {
                    titlename: "Sort Code:",
                    name: "2548725"
                },
                {
                    titlename: "BIC/SWIFT:",
                    name: "BUKBGB22XXX"
                },
                {
                    titlename: "IBAN Number:",
                    name: "GB 12 ABCD 035790 ********"
                },
                {
                    titlename: "Account Number:",
                    name: "************"
                },
                {
                    titlename: "Bank Address:",
                    name: "100 North Tryon Street, Charlotte"
                },
                {
                    titlename: "Reference:",
                    name: "12345"
                }
            ],
            showbank: false,
            banks: [
                {
                    value: "招商银行",
                    label: "招商银行"
                },
                {
                    value: "工商银行",
                    label: "工商银行"
                },
                {
                    value: "建设银行",
                    label: "建设银行"
                },
                {
                    value: "交通银行",
                    label: "交通银行"
                },
                {
                    value: "中国银行",
                    label: "中国银行"
                },


            ],
            showcoin: false,
            shownetwork: false,
            pageType: '', // 页面类型
            formData: {
                SameCount: false,
                isAll: false,
                accountName: '',
                bankName: '',
                sortCode: '',
                bic: '',
                iban: '',
                accountNumber: '',
                bankAddress: '',
                reference: '',
                address: '',
                memo: '',
                payUser: ''
            },
            currencies: [
                {
                    value: 'USD',
                    label: 'USD'
                },
                {
                    value: 'BTC',
                    label: 'BTC'
                }
            ],
            references: ['选填', '必填'],
            networks: [
                {
                    value: 'ERC-20',
                    label: 'ERC-20'
                }, {
                    value: 'TRC-20',
                    label: 'TRC-20'
                },
                {
                    value: 'ETH',
                    label: 'ETH'
                }
            ],
            labels: ['选填', '必填'],
            currencyIndex: 0,
            referenceIndex: 0,
            networkIndex: 0,
            labelIndex: 0,
            currency: "",
            network: "",
            nowAccount: {},
            userInfo: {}
        };
    },
    onLoad(options) {
        uni.setNavigationBarTitle({
            title: this.$t("page.AccountDetail") // 切换语言后重新设置标题
        })
        let account = JSON.parse(decodeURIComponent(options.account));
        console.log(account);

        if (account) {
            this.pageType = account.type || 'USD'; // 默认页面类型为 'USD'
            if (account.type == 2) {
                this.qrcodeUrl = account.address
            }
            this.nowAccount = account
            // this.qrcodeUrl = options.token;
        }
        this.getUserInfos()
    },
    methods: {
        copy(text) { //复制
            let that = this
            if (!text) {
                uni.showToast({
                    title: fail,
                    icon: 'none'
                })
                return
            }
            uni.setClipboardData({
                data: text,
                success() {
                    uni.showToast({
                        title: that.$t("title.copy"),

                        icon: 'none'
                    })
                }
            })
        },
        Edit() {
            this.$Router.push({
                name: 'AddCount',
                params: {
                    // account: this.nowAccount
                    account: encodeURIComponent(JSON.stringify(this.nowAccount))
                }
            })
        },
        async deleteAccount() {
            let res = await this.$api.deleteUserWithdrawAccount({
                id: this.nowAccount.id
            })
            if (res.code == 200) {
                setTimeout(() => {
                    this.$Router.back()
                }, 1000);
            }
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            console.log(res, 123);

            if (res.code == 200) {
                this.userInfo = res.result
                this.qrcodeUrl = res.result.email
            }

        },
        confirmbank(e) {
            this.formData.bankName = e[0].value
        },
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) {
            this.network = e[0].value
        },
        // 表单提交
        submitForm() {
            // 处理提交逻辑
        },
        // 表单取消
        cancelForm() {
            // 处理取消逻辑
        },
        handleCurrencyChange(event) {
            this.currencyIndex = event.detail.value;
        },
        handleReferenceChange(event) {
            this.referenceIndex = event.detail.value;
        },
        handleNetworkChange(event) {
            this.networkIndex = event.detail.value;
        },
        handleLabelChange(event) {
            this.labelIndex = event.detail.value;
        },
        nav_to(name, type) {
            this.$Router.push({
                name: name,
                params: {
                    type: type
                }
            });
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-select__body__picker-view__item {
    color: #e6f0ff !important;
}

::v-deep .u-hairline-border:after {
    border: none !important;
}

::v-deep .u-input {
    margin-top: 20rpx !important;
    height: 48*2rpx !important;
    border-radius: 10*2rpx;
    border-width: 2rpx;
    border: 2rpx solid #999999 !important;
    padding: 0 38rpx !important;

    font-family: Gilroy-Medium;
    font-weight: 400;
    font-size: 14*2rpx;
    line-height: 16.8*2rpx;
    letter-spacing: 0%;

}

.container {

    padding: 20px;
    min-height: 100vh;

    .form-container {
        margin-top: 64rpx;
        margin-bottom: 20px;

        .title {
            color: #000;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
        }

        .content_code {
            margin-top: 40rpx;
            border-radius: 20*2rpx;
            border-width: 2rpx;
            padding: 84rpx 42rpx 52rpx 42rpx;
            border: 2rpx solid #D9D6D6;
            width: 100%;

            .qr_div {
                margin-bottom: 84rpx;
            }

            .content-item {
                margin-bottom: 52rpx;

                &:last-child {
                    margin: 0rpx;
                }

                .rights {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;
                    color: #000;
                }

                .names {
                    .cointitile {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #666;
                    }

                    .name-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 22.4*2rpx;
                        color: #666;
                    }

                    .name {
                        width: 240*2rpx;
                        display: block;
                        word-break: break-all;
                        /* 超出宽度时强制换行 */
                        margin-top: 14rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #000;
                    }
                }

                .copy {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 20rpx;
                    background: #FFE6ED;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }
        }

        .content {
            margin-top: 24rpx;
            border-radius: 20*2rpx;
            border-width: 2rpx;
            padding: 26*2rpx 21*2rpx;
            border: 2rpx solid #D9D6D6;

            .pink-title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #000;
            }

            .content-item {
                margin-bottom: 52rpx;

                &:last-child {
                    margin: 0rpx;
                }

                .rights {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;
                    color: #000;
                }

                .names {
                    .cointitile {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #666;
                    }

                    .name-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 22.4*2rpx;
                        color: #666;
                    }

                    .name {
                        margin-top: 14rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #000;
                    }
                }

                .copy {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 20rpx;
                    background: #FFE6ED;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }
        }

        .form-item {
            margin-bottom: 28rpx;
            position: relative;

            .scan {
                position: absolute;
                right: 38rpx;
                bottom: 27rpx;
                width: 48rpx;
                height: 48rpx;
                // margin-left: 10rpx;
                // margin-top: -4rpx;
            }

            .check_label {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                color: #666;
            }

            .label {
                margin-left: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                // line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .icon_serve {
                width: 40rpx;
                height: 40rpx;
            }

            .right_fix {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                width: 48rpx;
                height: 48rpx;
            }

            .inputs {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #333;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;

                .right {
                    width: 60rpx;
                    height: 60rpx;

                    image {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #000;
            }
        }

        .picker {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tips {
            width: 100%;
            // height: 76*2rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            padding: 16*2rpx;
            background: #F2F2F2;

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .btn {
            margin-top: 48rpx;
            width: 100%;
            gap: 20rpx;

            .cancel-btn {
                width: 100%;
                height: 100rpx;
                background: #fff;
                border: 2rpx solid #999999;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #000;
            }

            .exchange-btn {
                width: 100%;
                height: 100rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .buttons {
            display: flex;
            flex-direction: column;
            margin-top: 70px;
            gap: 26rpx;

            button {
                border: none;
                width: 100%;
                // padding: 10px 20px;
                // color: white;
                border-radius: 5px;
            }
        }
    }
}
</style>