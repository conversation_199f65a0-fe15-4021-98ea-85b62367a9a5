<template>
  <view class="container">
    <u-navbar back-icon-color="#121212" :border-bottom="false" :title="titles" :custom-back="back">
      <view slot="right" class="search-box" @click="nav_to('Record', 'swap')">
        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
      </view>
    </u-navbar>

    <view class="form-container">
      <view class="form-item">
        <text>{{ this.titles = options.type == 'Deposit' ? 'Deposit Currency' : 'Receive Currency' }}</text>
        <view class="inputs flex_divide" @click="showcoin = true">
          <view class="input_view flex_y">
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250305/098bd983e0cb24382da5927a853274b9_80x80.png" />
            <view class="coins">
              <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/72e5d75d2638d2439b2d1037ca19c985_80x80.png" />
              <text>USDT</text>
            </view>
          </view>
          <view class="right">
            <image class="delete"
              src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/a318d26720a622f4da0651ecd3ebb12d_112x113.png" />
          </view>
        </view>
      </view>
      <!-- <view class="Deposit-tips">Please transfer GBP to the following recipient account</view> -->

      <view class="content">
        <view class="content-item flex_divide" v-for="item in BankInfo" :key="item.id">
          <view class="names flex-column">
            <view class="name-title">{{ item.titlename }}</view>
            <view class="name">{{ item.name }}</view>
          </view>
          <!-- <view class="copy flex_all">
            <image
              src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
          </view> -->
        </view>
        <view class="copys flex_all" :class="{ active: isCopied, scaling: isScaling }" @click="handleCopy">
          <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1380607164828704768.png" v-if="!isCopied" />
          <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1380607456194420736.png" v-else />
          {{ isCopied ? ' Copied' : ' Copy All' }}
        </view>
      </view>


    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isScaling: false,
      isCopied: false,
      titles: "",
      BankInfo: [
        {
          titlename: "Account Holder Name:",
          name: "Jane Doe"
        },
        {
          titlename: "Bank Name:",
          name: "JPMorgan Chase Bank, N.A"
        },
        {
          titlename: "Sort Code:",
          name: "2548725"
        },
        {
          titlename: "BIC/SWIFT:",
          name: "BUKBGB22XXX"
        },
        {
          titlename: "IBAN Number:",
          name: "GB 12 ABCD 035790 ********"
        },
        {
          titlename: "Account Number:",
          name: "************"
        },
        {
          titlename: "Bank Address:",
          name: "100 North Tryon Street, Charlotte"
        },
        {
          titlename: "Reference:",
          name: "12345"
        }
      ],
      currencyList: ['GBP'],  // 可扩展的币种列表
      selectedCurrency: 0,    // 默认选择第一个币种（GBP）
      infoList: [
        { label: '账户名称', value: 'AA BB' },
        { label: '银行名称', value: 'AA BB' },
        { label: 'Sort Code', value: 'AA BB' },
        { label: 'BIC/SWIFT', value: 'AA BB' },
        { label: 'IBAN号', value: 'AA BB' },
        { label: '银行账号', value: '124434' },
        { label: '银行地址', value: 'Global Bank Limited, SQB,77' },
        { label: 'Reference', value: '123232' },
      ],
    };
  },
  onLoad(options) {
    uni.setNavigationBarTitle({
      title: this.$t("page.CurrencyCharge") // 切换语言后重新设置标题
    })
    this.titles = options.type == 'Deposit' ? 'Deposit' : 'Receive';
  },
  methods: {
    handleCopy() {
      if (this.isCopied) return;

      // 设置放大动画
      this.isScaling = true;
      setTimeout(() => {
        this.isScaling = false;
      }, 300); // 恢复缩放

      // 设置背景变绿
      uni.setClipboardData({
        data: '你要复制的内容',
        success: () => {
          this.isCopied = true;
        }
      });
    },
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    // 复制文本
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
            duration: 2000
          });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.container {
  padding: 32rpx;

  .form-container {
    margin-top: 64rpx;
    margin-bottom: 20px;

    .copys {
      margin-top: 52rpx;
      height: 100rpx;
      border-radius: 128rpx;
      background: #FF82A3;
      font-family: Gilroy-Bold;
      font-weight: 400;
      font-size: 32rpx;
      color: #fff;
      text-align: center;
      line-height: 100rpx;
      transition: background-color 0.3s ease;
      gap: 12rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }

      &.scaling {
        animation: scaleBack 0.3s ease forwards;
      }

      &.active {
        background: #08B819;
      }
    }

    @keyframes scaleBack {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.05);
      }

      100% {
        transform: scale(1);
      }
    }


    .Deposit-tips {
      font-family: Gilroy-SemiBold;
      font-weight: 400;
      font-size: 14*2rpx;
      line-height: 22.4*2rpx;
      color: #000;
    }

    .warning {
      margin-top: 52rpx;
      border-bottom-right-radius: 10*2rpx;
      border-bottom-left-radius: 10*2rpx;
      padding: 16*2rpx;
      background: #FFF3F6;
      font-family: Gilroy-SemiBold;
      font-weight: 400;
      font-size: 14*2rpx;
      line-height: 22.4*2rpx;
      letter-spacing: 0%;
      color: #666;
    }

    .content_code {
      margin-top: 40rpx;
      border-radius: 20*2rpx;
      border-width: 2rpx;
      padding: 84rpx 42rpx 52rpx 42rpx;
      border: 2rpx solid #D9D6D6;
      width: 100%;

      .qr_div {
        margin-bottom: 84rpx;
      }

      .content-item {
        margin-bottom: 52rpx;

        &:last-child {
          margin: 0rpx;
        }

        .rights {
          font-family: Gilroy-SemiBold;
          font-weight: 400;
          font-size: 16*2rpx;
          line-height: 19.2*2rpx;
          color: #000;
        }

        .names {
          .cointitile {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #666;
          }

          .name-title {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            color: #666;
          }

          .name {
            width: 240*2rpx;
            display: block;
            word-break: break-all;
            /* 超出宽度时强制换行 */
            margin-top: 14rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #000;
          }
        }

        .copy {
          width: 100rpx;
          height: 100rpx;
          border-radius: 20rpx;
          background: #FFE6ED;

          image {
            width: 48rpx;
            height: 48rpx;
          }
        }
      }
    }

    .content {
      margin-top: 24rpx;
      border-radius: 20*2rpx;
      border-width: 2rpx;
      padding: 26*2rpx 21*2rpx;
      border: 2rpx solid #D9D6D6;

      .pink-title {
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 16*2rpx;
        line-height: 19.2*2rpx;
        letter-spacing: 0%;
        color: #000;
      }

      .content-item {
        margin-bottom: 52rpx;

        &:last-child {
          margin: 0rpx;
        }

        .rights {
          font-family: Gilroy-SemiBold;
          font-weight: 400;
          font-size: 16*2rpx;
          line-height: 19.2*2rpx;
          color: #000;
        }

        .names {
          .cointitile {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #666;
          }

          .name-title {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            color: #666;
          }

          .name {
            margin-top: 14rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #000;
          }
        }

        .copy {
          width: 100rpx;
          height: 100rpx;
          border-radius: 20rpx;
          background: #FFE6ED;

          image {
            width: 48rpx;
            height: 48rpx;
          }
        }
      }
    }

    .form-item {
      margin-bottom: 28rpx;
      position: relative;

      .amount {
        text-indent: 16rpx;
      }

      .dao {
        position: absolute;
        bottom: 30rpx;
        left: 38rpx;
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 16.8*2rpx;
        letter-spacing: 0%;
        color: #666;
      }

      .all {
        position: absolute;
        bottom: 28rpx;
        right: 38rpx;
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 16.8*2rpx;
        letter-spacing: 0%;
        color: #FF82A3;
      }

      .label {
        margin-left: 20rpx;
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 16*2rpx;
        // line-height: 19.2*2rpx;
        letter-spacing: 0%;
        color: #FF82A3;
      }

      .icon_serve {
        width: 40rpx;
        height: 40rpx;
      }

      .right_fix {
        position: absolute;
        bottom: 28rpx;
        right: 38rpx;
        width: 48rpx;
        height: 48rpx;
      }

      .inputs_bom {
        margin-top: 20rpx;
        height: 48*2rpx !important;
        border-radius: 10*2rpx;
        border-width: 2rpx;
        border: 2rpx solid #999999;
        font-family: Gilroy-Medium;
        color: #666;
        font-weight: 400;
        font-size: 14*2rpx;
        letter-spacing: 0%;
        padding: 0 38rpx !important;

        .rights {
          // width: 60rpx;
          // height: 60rpx;

          image {
            width: 60rpx;
            height: 60rpx;
          }
        }

        .input_view {

          .coins {
            display: flex;
            align-items: center;
            margin-left: 20rpx;

            image {
              width: 40rpx;
              height: 40rpx;
            }

            text {
              line-height: 0;
              margin-left: 10rpx;
            }
          }

          >image {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      .inputs {
        margin-top: 20rpx;
        height: 48*2rpx !important;
        border-radius: 10*2rpx;
        border-width: 2rpx;
        border: 2rpx solid #999999;
        font-family: Gilroy-Medium;
        color: #333;
        font-weight: 400;
        font-size: 14*2rpx;
        letter-spacing: 0%;
        padding: 0 38rpx !important;

        .right {
          width: 48rpx;
          height: 48rpx;

          .delete {
            width: 56rpx;
            height: 56rpx;
          }

          image {
            width: 48rpx;
            height: 48rpx;
          }
        }

        .input_view {

          .coins {
            display: flex;
            align-items: center;
            margin-left: 20rpx;

            image {
              width: 40rpx;
              height: 40rpx;
            }

            text {
              line-height: 0;
              margin-left: 10rpx;
            }
          }

          >image {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      >text {
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;
        color: #666;
      }
    }

    .btn {
      margin-top: 48rpx;
      width: 100%;

      .exchange-btn {
        width: 100%;
        height: 100rpx;
        background: #FF82A3;
        border-radius: 64*2rpx;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        color: #fff;
      }
    }


  }
}
</style>