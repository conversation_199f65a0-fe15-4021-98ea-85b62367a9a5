<template>
    <view class="container">
        <!-- 标题 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('Deposit.title')" :custom-back="back">
            <view slot="right" class="search-box" @click="nav_to('Record', 'deposit')">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>

        <view class="form-container">
            <view class="form-item">
                <text>{{ $t("Deposit.DepositCurrency") }}</text>
                <!--  -->
                <view class="inputs flex_divide" @click="showCoin = !showCoin">
                    <view class="input_view flex_y">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250305/098bd983e0cb24382da5927a853274b9_80x80.png" />
                        <u-input class="coins" @blur="blurs" @focus="foucs" :clearable="false"
                            :placeholder="$t('Deposit.Search')" v-model="nowCurrency">
                        </u-input>

                        <!-- <image
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png" /> -->
                        <!-- <text>{{ nowCurrency || 'Select' }}</text> -->
                    </view>
                    <view class="right" v-show="nowCurrency">
                        <image class="delete" @click.stop="deleteCurrency"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/a318d26720a622f4da0651ecd3ebb12d_112x113.png" />
                    </view>

                    <transition name="expand-slide">
                        <view class="helpoption" v-show="showCoin">
                            <view class="popup-top-title" v-if="filteredList.length">{{ $t("Send.Crypto") }}</view>
                            <scroll-view v-if="filteredList.length" :scroll-y="true" @scrolltolower="bottomOut()"
                                class="crypto flex-column">
                                <view class="crypto-item " @click.stop="choose_coin(item)" v-for="item in filteredList"
                                    :key="item.id">
                                    <image :src="item.image" />
                                    <text>{{ item.symbol }}</text>
                                </view>
                            </scroll-view>

                            <nodata v-if="!filteredList.length" />
                            <!-- <view style="height: 40rpx;" v-if="filteredList.length"></view>
                            <view class="popup-top-title" v-if="filteredFiatList.length">Fiat</view>
                            <scroll-view :scroll-y="true" @scrolltolower="bottomOut2()" class="crypto flex-column">
                                <view class="crypto-item " @click.stop="choose_coin(item)" v-for="item in filteredFiatList"
                                    :key="item.id">
                                    <image :src="item.image" />
                                    <text>{{ item.symbol }}</text>
                                </view>
                            </scroll-view> -->
                        </view>
                    </transition>
                </view>
            </view>


            <view class="form-item" v-if="nowCurrency">
                <text>{{ $t("Deposit.Network") }}</text>
                <view class="inputs flex_divide" @click="ShowNetwork = !ShowNetwork">
                    <view class="input_view">{{ network }}</view>
                    <view class="right">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250305/2ac88d2d91400e2686de9a908f6a8e41_96x96.png" />
                    </view>

                    <transition name="expand-slide">
                        <view class="NetworkPopup" v-show="ShowNetwork">
                            <text class="address" @click="choose_net(item)" v-for="item in networkOptions">{{
                                item.network
                            }}</text>
                        </view>
                        <nodata v-if="!networkOptions.length" />

                    </transition>
                </view>
                <!-- <u-input height="96" v-model="formData.accountName" placeholder="请输入账户名称" /> -->
            </view>
            <!-- 充币地址创建中，请耐心等待。 -->
            <view v-if="nowCurrency && network && !showAddress" class="waiting"> {{ $t("Deposit.tip") }}
            </view>

            <view class="content_code " v-if="nowCurrency && network && showAddress">
                <view class="qr_div flex_x">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="444rpx"
                        :options="options"></uv-qrcode>
                </view>
                <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title"> {{ $t("Deposit.Address") }}</view>
                        <view class="name">{{ qrcodeUrl }}</view>
                    </view>
                    <view class="copy flex_all" @click="copy(qrcodeUrl)">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide" v-if="memo">
                    <view class="names flex-column">
                        <view class="name-title"> {{ $t("Send.Memo") }}</view>
                        <view class="name">{{ memo }}</view>
                    </view>
                    <view class="copy flex_all" @click="copy(memo)">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/75e4ff0e174b8d4fed807e63f8be2d9d_96x96.png" />
                    </view>
                </view>

                <view class="content-item flex_divide" v-if="minAmount">
                    <view class="names flex-column">
                        <view class="name-title">{{ $t("Deposit.Minimum") }}</view>
                    </view>
                    <view class="rights flex_all">
                        {{ formatNumber(minAmount) }} {{ nowCurrency }}
                    </view>
                </view>

                <!-- <view class="content-item flex_divide">
                    <view class="names flex-column">
                        <view class="name-title">Estimated Arrival Time</view>
                    </view>
                    <view class="rights flex_all">
                        10 min
                    </view>
                </view> -->

            </view>

            <view class="warning" v-if="nowCurrency && network">
                {{ $t("Deposit.tipsLong") }} {{ nowCurrency }} {{ $t("Deposit.tipsShort") }}{{ nowCurrency }} {{
                    $t("Deposit.tipsShort2") }}
            </view>
        </view>
        <zero-loading type="sword" v-if="loading"></zero-loading>

    </view>
</template>

<script>
import nodata from "../../../components/public/nodata"
export default {
    components: {
        nodata
    },
    data() {
        return {
            cdObj: {},
            loading: false,
            minAmount: "",
            showAddress: false,
            ShowNetwork: false,
            AddressList: [
                {
                    add: '0x4fA12aB77A3b35a1C59d4eC2B5C6dBFFeE8E',
                    network: 'Ethrum'
                },
                {
                    add: '******************************************',
                    network: 'Ethrum'
                },
                {
                    add: '******************************************',
                    network: 'Ethrum'
                }
            ],
            Showaddress: false,
            nowCurrency: "",
            cryptoList: [
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'ETH'
                // },
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'BTC'
                // },
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'SOL'
                // }
            ],
            FiatList: [
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'USD'
                // },
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'USDT'
                // },
                // {
                //     icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png",
                //     symbol: 'USDC'
                // },
            ],
            showCoin: true,
            isShowType: false,
            isShownetwork: false,
            // qrcodeUrl: "******************************************",
            qrcodeUrl: "",
            currency: 'USDT',
            network: '',
            address: '******************************************',
            memo: '',
            currencyOptions: [{
                value: 'USDT',
                label: 'USDT'
            }
                // , {
                //     value: 'BTC',
                //     label: 'BTC'
                // }, {
                //     value: 'ETH',
                //     label: 'ETH'
                // }
            ],
            networkOptions: [
            ],
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            codeSize: 240,
            Fiat: {
                pageNum: 1,
                pageSize: 10,
            },
            Crypto: {
                pageNum: 1,
                pageSize: 10,
            },
        };
    },
    watch: {
        nowCurrency: {
            handler(newVal, oldVal) {
                if (newVal) {
                    // this.getNet()
                }
            },
            immediate: true
        },
        showCoin(newVal) {
            if (newVal) {
                this.ShowNetwork = false;
            }
        },
        ShowNetwork(newVal) {
            if (newVal) {
                this.showCoin = false;
            }
        },
        "$store.state.indexRate"(val) {
            console.log(val.address, '地址ws');
            console.log(val, '地址ws');

            if (val.address) {
                this.cdObj = val
                this.showAddress = true
                this.minAmount = val.minAmount
                this.memo = val.memo
                this.qrcodeUrl = val.address
                this.loading = false
            }
        },
    },
    onLoad(e) {
        uni.setNavigationBarTitle({
            title: this.$t("page.deposit") // 切换语言后重新设置标题
        })
        this.codeSize = 170 * (uni.upx2px(200) / 100)
        this.getCurrencyFiat()
        this.getCurrencyCrypto()
        if (e.fromCurrency) {
            this.nowCurrency = e.fromCurrency
        }
    },
    computed: {
        filteredList() {
            if (!this.nowCurrency) return this.cryptoList
            return this.cryptoList.filter(item =>
                item.symbol.toLowerCase().includes(this.nowCurrency.toLowerCase())
            )
        },
        filteredFiatList() {
            if (!this.nowCurrency) return this.FiatList
            return this.FiatList.filter(item =>
                item.symbol.toLowerCase().includes(this.nowCurrency.toLowerCase())
            )
        },
    },
    methods: {
        formatNumber(num) {
            if (Math.abs(num) < 1.0) {
                let e = parseInt(num.toString().split('e-')[1]);
                if (e) {
                    num *= Math.pow(10, e - 1);
                    num = '0.' + (new Array(e)).join('0') + num.toString().substring(2);
                }
            } else {
                let e = parseInt(num.toString().split('e+')[1]);
                if (e > 20) {
                    e -= 20;
                    num /= Math.pow(10, e);
                    num += (new Array(e + 1)).join('0');
                }
            }
            return num.toString();
        },
        blurs() {
            this.showCoin = false
        },
        foucs() {
            this.showCoin = true
        },
        bottomOut() {
            console.log(123);
        },
        bottomOut2() {
            console.log(333);

        },
        deleteCurrency() {
            this.nowCurrency = ''
            this.network = ''
        },
        async getCurrencyFiat() {
            let res = await this.$api.symbolListPaged({
                fiat: true,
                symbol: "",
                pageNum: this.Fiat.pageNum,
                pageSize: this.Fiat.pageSize
            });
            this.FiatList = res.result.data
        },
        async getCurrencyCrypto() {
            let res = await this.$api.symbolListPaged({
                fiat: false,
                symbol: "",
                pageNum: this.Crypto.pageNum,
                pageSize: this.Crypto.pageSize
            });
            this.cryptoList = res.result.data
        },
        copy(text) { //复制
            let that = this
            uni.setClipboardData({
                data: text,
                success() {
                    uni.showToast({
                        title: that.$t("title.copy"),
                        icon: 'none'
                    })
                }
            })
        },
        choose_net(item) {
            this.network = item.network
            this.getAdd()
        },
        async getAdd() {
            this.showAddress = false
            this.loading = true
            let res = await this.$api.getWalletAddress({
                symbol: this.nowCurrency,
                network: this.network
            })
            console.log(res);

            if (res.code == 200) {
                this.qrcodeUrl = res.result.address
                this.memo = res.result.memo
                this.minAmount = res.result.minAmount
                this.showAddress = true
                this.loading = false
            } else {
                this.showAddress = false
                this.loading = false
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        async getNet() {
            let res = await this.$api.getNetwork({
                symbol: this.nowCurrency,
            })
            if (res.code == 200) {
                this.networkOptions = res.result
                console.log(this.networkOptions);
            } else {
                this.$u.toast(res.msg)
            }
        },
        choose_coin(item) {
            this.nowCurrency = item.symbol
            this.showCoin = false
            console.log(this.showCoin);
            this.networkOptions = []
            this.network = ''

            this.getNet()
        },
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) {
            this.network = e[0].value
        },
        nav_charge_record() {
            this.$Router.push({
                name: 'Record',
                params: {
                    type: 'deposit'
                }
            })
        },
        copyAddress() {
            let that = this
            uni.setClipboardData({
                data: this.address,
                success() {
                    uni.showToast({
                        title: that.$t("title.copy"),
                        icon: 'none',
                    });
                },
            });
        },
        copyMemo() {
            let that = this

            uni.setClipboardData({
                data: this.memo,
                success() {
                    uni.showToast({
                        title: that.$t("title.copy"),

                        icon: 'none',
                    });
                },
            });
        },
        back() {
            this.$Router.back();
        },
        nav_to(name, type) {
            this.$Router.push({
                name: name,
                params: {
                    type: type
                }
            });
        },
    },
};
</script>

<style scoped lang="scss">
.container {
    padding: 32rpx;
    padding-bottom: 300rpx;

    .form-container {
        margin-top: 64rpx;
        margin-bottom: 20px;

        .warning {
            margin-top: 52rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            padding: 16*2rpx;
            background: #FFF3F6;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #666;
        }

        .waiting {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14px;
            line-height: 160%;
            letter-spacing: 0%;
            color: #000;
        }

        .content_code {
            margin-top: 40rpx;
            border-radius: 20*2rpx;
            border-width: 2rpx;
            padding: 84rpx 42rpx 52rpx 42rpx;
            border: 2rpx solid #D9D6D6;
            width: 100%;

            .qr_div {
                margin-bottom: 84rpx;
            }

            .content-item {
                margin-bottom: 52rpx;

                &:last-child {
                    margin: 0rpx;
                }

                .rights {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;
                    color: #000;
                }

                .names {
                    .cointitile {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #666;
                    }

                    .name-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 22.4*2rpx;
                        color: #666;
                    }

                    .name {
                        width: 240*2rpx;
                        display: block;
                        word-break: break-all;
                        /* 超出宽度时强制换行 */
                        margin-top: 14rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        letter-spacing: 0%;
                        color: #000;
                    }
                }

                .copy {
                    width: 100rpx;
                    height: 100rpx;
                    border-radius: 20rpx;
                    background: #FFE6ED;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }
        }

        .form-item {
            margin-bottom: 28rpx;
            position: relative;

            .amount {
                text-indent: 16rpx;
            }

            .dao {
                position: absolute;
                bottom: 30rpx;
                left: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            .all {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .label {
                margin-left: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                // line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .icon_serve {
                width: 40rpx;
                height: 40rpx;
            }

            .right_fix {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                width: 48rpx;
                height: 48rpx;
            }

            .inputs_bom {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #666;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;

                .rights {
                    // width: 60rpx;
                    // height: 60rpx;

                    image {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .inputs {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #333;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;
                position: relative;

                .NetworkPopup {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    // display: flex;
                    // align-items: flex-start;
                    // flex-direction: column;
                    max-height: 400rpx;
                    overflow-y: auto;
                    width: 100%;

                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }


                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }
                }

                .helpoption {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;


                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .crypto {
                        margin-top: 40rpx;
                        max-height: 400rpx;
                        overflow-y: auto;
                        width: 100%;

                        .crypto-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 40rpx;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            image {
                                width: 60rpx;
                                height: 60rpx;
                            }

                            text {
                                margin-left: 20rpx;
                                font-family: Gilroy-SemiBold;
                                font-weight: 400;
                                font-size: 16*2rpx;
                                // line-height: 120%;
                                letter-spacing: 0%;
                                color: #000;
                            }
                        }
                    }

                    .popup-top-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 160%;
                        color: #FF82A3;
                    }

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }

                    // >view {

                    //     padding: 15rpx 0;
                    //     display: flex;
                    //     align-items: center;

                    //     image {
                    //         width: 40rpx;
                    //         height: 30rpx;
                    //     }

                    //     text {
                    //         margin-left: 20rpx;
                    //         display: block;
                    //         font-family: Gilroy-Bold;
                    //         font-weight: 400;
                    //         font-size: 16*2rpx;
                    //         line-height: 19.2*2rpx;
                    //         color: #000;
                    //     }
                    // }
                }

                .right {
                    width: 48rpx;
                    height: 48rpx;

                    .delete {
                        width: 56rpx;
                        height: 56rpx;
                    }

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .btn {
            margin-top: 48rpx;
            width: 100%;

            .exchange-btn {
                width: 100%;
                height: 100rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }
        }


    }
}
</style>