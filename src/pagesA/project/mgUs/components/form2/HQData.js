import store from "@/store";
import zlib from "zlib";
import <PERSON><PERSON> from "pako";
import api from "@/common/api/index.js";
function HQData() {}
let socketApi;
// #ifdef H5
(socketApi = process.env.VUE_APP_WS_API_URL),
  // #endif
  // #ifdef APP-PLUS
  (socketApi = getApp().globalData.socketApi),
  // #endif
  (HQData.KLineApiUrl = getApp().globalData.historyApi);
HQData.WSKLineUrl = socketApi;

//okx周期对应 hqchart内部的周期值
let PERIOD_NAME = new Map([
  [20001, { HTTP: "20001min", WS: "market_e_aaveusdt_kline_20001min" }], //5 minute
  [5, { HTTP: "5min", WS: "market_e_aaveusdt_kline_5min" }], //5 minute
  [7, { HTTP: "30min", WS: "market_e_aaveusdt_kline_30min" }], //30 minute
  [8, { HTTP: "60min", WS: "market_e_aaveusdt_kline_60min" }], //60 minute
  [4, { HTTP: "4h", WS: "market_e_aaveusdt_kline_4h" }], //240 minute
  [12, { HTTP: "1day", WS: "market_e_aaveusdt_kline_1day" }], //1day
  [6, { HTTP: "1week", WS: "market_e_aaveusdt_kline_1week" }], //1week
]);

HQData.GetPeriodName = function (period) {
  if (!PERIOD_NAME.has(period)) return null;
  return PERIOD_NAME.get(period);
};

HQData.GetPeriodID = function (periodName) {
  for (let item of PERIOD_NAME) {
    // if (item[1].HTTP == periodName) return item[0]
    if (item[1].WS == periodName) return item[0];
  }

  return null;
};
//拿到全量分钟数据
HQData.RequestHistoryMinuteData = async function (data, callback, option) {
  console.log("拿到全量分钟数据");
  const minuteMap = {
    20001: 1,
    5: 5,
	7: 30,
	8: 60,
	12: 1440,
	6: 10080,
  };
  data.PreventDefault = true;
  console.log(data)
  let minute = data.Request.Data.period
  let title = uni.getStorageSync('klineTitle')
  let res = await api.scaleKline({
	title,
	minute:minuteMap[minute]
  });
  let hisdata = res.result
   HQData.RecvHistoryMinuteData(hisdata, callback, data, option);
};

//处理全量分钟数据
HQData.RecvHistoryMinuteData = function (res, callback, data, option) {
  // let symbol = data.Request.Data.symbol
  console.log(res, callback, data, option, "RecvHistoryMinuteData");
  // console.log(res);

  let hqChartData = {
    symbol: "ETH-USDT.bit",
    name: 321,
    data: [],
  };

  let reversedArray = [];

  for (let i = res.length - 1; i >= 0; i--) {
    reversedArray.push(res[i]);
  }

  let yClose = null; //前收盘
  for (let i = reversedArray.length - 1; i >= 0; --i) {
    let item = reversedArray[i];
    let dateTime = new Date();

    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    dateTime.setTime(item.time);

    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time = dateTime.getHours() * 100 + dateTime.getMinutes();
    let open = item.open;
    let high = item.high;
    // console.log('最高', high)
    let low = item.low;
    // console.log('最低', low)
    let close = item.close;
    let vol = item.vol;
    let newItem = [date, yClose, open, high, low, close, vol, null, time];
    //最高最低数据
    yClose = close;
    hqChartData.data.push(newItem);
    // console.log("最终数据", hqChartData)
  }
  console.log(hqChartData, data, option, "6666"); // 在这里订阅
  if (data.Self.IsDestroy == false) {
    // #ifdef H5
    callback(hqChartData);
    // #endif

    // #ifndef H5
    callback({ data: hqChartData });
    // #endif
  }
};

// 处理增量分钟数据
HQData.RecvMinuteRealtimeData = function (res, internalChart) {
  // console.log("[HQData::RecvMinuteRealtimeData] recvdata", res)
  // console.log('进来RecvMinuteRealtimeData');

  if (!res.tick) return;
  let hqChartData = {
    // symbol: arg.instId + ".bit",
    // Name: arg.instId,
    // data: [],
    // ver: 2,
    symbol: res.channel,
    Name: res.channel,
    data: [],
    ver: 2,
  };

  let yClose = null; //前收盘
  let arr = [];
  arr.push(res.tick);
  // console.log(arr,'[HQData::RecvMinuteRealtimeData]');

  for (let i = arr.length - 1; i >= 0; --i) {
    let item = arr[i];
    let dateTime = new Date();

    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    // dateTime.setTime(timestampSeconds)
    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    dateTime.setTime(item.idx * 1000);

    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time = dateTime.getHours() * 100 + dateTime.getMinutes();
    let open = item.open;
    let high = item.high;
    // console.log('最高', high)
    let low = item.low;
    // console.log('最低', low)
    let close = item.close;
    let vol = item.vol;
    let newItem = [date, yClose, open, high, low, close, vol, null, time];
    //最高最低数据

    yClose = close;
    hqChartData.data.push(newItem);
  }

  // console.log("最终数据", hqChartData)
  // #ifdef H5
  internalChart.RecvMinuteRealtimeData(hqChartData);
  // #endif

  // #ifndef H5
  internalChart.RecvMinuteRealtimeData({ data: hqChartData });
  // #endif
};

function JSHQWebSocket() {
  this.Url = HQData.WSKLineUrl;
  this.Socket = null;
  this.HQChart = null; //hqchart实例
  this.LastSubData = null; //最后一次订阅的信息
  this.InitalSubData = null;
  this.IsReady = false;

  //取消订阅
  this.UnsubKLine = function () {
    if (this.LastSubData) {
      let unsubData = { event: "unsub", params: this.LastSubData };
      let message = JSON.stringify(unsubData);
      uni.sendSocketMessage({ data: message });

      this.LastSubData = null;
    }
  };
}

export default {
  HQData: HQData,
  HQWebSocket: JSHQWebSocket,
};
