<template>
	<view class='viewchart'>
		<!-- <view class='line2'>
			<view @click='ChangeKLineArea()' :class="selectTab == 0 ? 'clickTab' : ''">分时
			</view>
			<view @click='ChangePeriod(4)' :class="selectTab == 4 ? 'clickTab' : ''">1m</view>
			<view @click='ChangePeriod(6)' :class="selectTab == 6 ? 'clickTab' : ''">15m</view>
			<view @click='ChangePeriod(8)' :class="selectTab == 8 ? 'clickTab' : ''">1h</view>
			<view @click='ChangePeriod(9)' :class="selectTab == 9 ? 'clickTab' : ''">4h</view>
		</view> -->
		<HQChartControl ref='HQChartCtrl' @changeArea="handleChangeArea" DefaultChart="{Type:'KLine'}"
			:DefaultSymbol='Symbol'>
		</HQChartControl>
	</view>
</template>

<script>
// import store from "../../store"
import HQChartControl from "@/uni_modules/jones-hqchart2/js_sdk/HQChartControl.vue"
import DataCtrl from "./HQData.js"
// import { marketInformation } from "../../api/marketInterface"
// import { mapGetters } from "vuex"
// #ifdef H5
import HQChart from "@/uni_modules/jones-hqchart2/js_sdk/umychart.uniapp.h5.js"
import {
	JSCommon
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js"
import {
	JSCommonHQStyle
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.style.wechat.js"

var MARKET_SUFFIX_NAME = HQChart.MARKET_SUFFIX_NAME
// #endif



// #ifndef H5
import {
	JSCommon
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js"
import {
	JSCommonHQStyle
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.style.wechat.js"
import {
	JSConsole
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.console.wechat.js"
import {
	MARKET_SUFFIX_NAME
} from "@/uni_modules/jones-hqchart2/js_sdk/umychart.coordinatedata.wechat.js"
//禁用日志
//JSConsole.Complier.Log=()=>{ };
//JSConsole.Chart.Log=()=>{ };
// #endif

function DefaultData() { }
//自定义tooltip



DefaultData.GetKLineOption = function () {
	//K线配置信息
	var option = {
		Type: "历史K线图", //创建图形类型
		Language: "EN",
		Windows: //窗口指标
			[
				// {
				// 	Index: 'EMPTY',
				// 	TitleHeight: 0
				// },
				{ Index: "MA", Modify: false, Change: false, TitleHeight: 0 },
				{
					Index: "VOL",
					Modify: false,
					Change: false,
					Close: false,
					TitleHeight: 0
				},
			],
		ExtendChart: //扩展图形
			[
				// { Name: 'KLineTooltip', Create: function () { return CustomTooltip(); } }  //手机端tooltip
			],
		// EventCallback:
		//   [
		//     {
		//       event: HQChart.JSCHART_EVENT_ID.ON_CLICK_CHART_PAINT,
		//       callback: (event, data, obj) => {
		//         (event, data, obj) => {
		//           console.log(data, 'callbackdata');
		//         }
		//       }
		//     }
		//   ],
		IsAutoUpdate: true, //是自动更新数据
		AutoUpdateFrequency: 1000, //数据更新频率
		IsShowRightMenu: false, //右键菜单
		IsApiPeriod: true, //复权,周期都使用后台数据
		CorssCursorTouchEnd: true,
		StepPixel: 5, //移动一个K线需要的手势移动的像素(默认4)
		ZoomStepPixel: 8, //缩放一次,2个手指需要移动的间距像素(默认5)

		KLine: //K线设置
		{
			DragMode: 1, //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择
			Right: 0, //复权 0 不复权 1 前复权 2 后复权
			Period: 0, //周期 0 日线 1 周线 2 月线 3 年线
			MaxReqeustDataCount: 1000, //数据个数
			MaxRequestMinuteDayCount: 10, //分钟数据取5天
			// #ifdef H5
			PageSize: 40, //一屏显示多少数据
			// #endif

			// #ifdef APP-PLUS
			DataWidth: 3,
			// #endif
			IsShowMaxMinPrice: false, // 最大最小值
			IsShowTooltip: false, //是否显示 view K线提示信息 (手机端要填false)
			DrawType: 0, //K线类型 0=实心K线柱子 1=收盘价线 2=美国线 3=空心K线柱子 4=收盘价面积图
			RightSpaceCount: 1,
		},

		KLineTitle: //标题设置
		{
			IsShowName: false, //显示股票名称
			IsShowSettingInfo: false, //显示周期/复权
		},

		Border: //边框
		{
			Left: 1, //左边间距
			Right: 52, //右边间距
			Bottom: 15, //底部间距
			Top: 4, //顶部间距
		},
		BorderLine: 2 | 8,
		Frame: //子框架设置
			[{
				SplitCount: 5,
				IsShowLeftText: false,
				Height: 6,
				SplitType: 1,
				IsShowXLine: false,
				IsShowYLine: false,
				Custom: [{
					Type: 0,
					Position: "right"
				}],
			},

			{
				SplitCount: 2,
				IsShowLeftText: false,
				Height: 2,
				IsShowIndexTitle: false,
				IsShowXLine: false,
				IsShowYLine: false,
				IsShowRightText: false,
			},
			],

		ExtendChart: //扩展图形
			[
				// { Name: "KLineTooltip" },  //手机端tooltip
				// [
				// { Name: 'KLineTooltip', Create: function () { return new CustomTooltip(); } }  //手机端tooltip
				// ],
			],
	}

	return option
}

DefaultData.GetKLineAreaOption = function () {
	//K线配置信息
	var option = {
		Type: "历史K线图", //创建图形类型
		Language: "EN",
		Windows: //窗口指标
			[{
				Index: "EMPTY",
				TitleHeight: 0
			},
			// {
			//   Index: "VOL", Modify: false, Change: false, Close: false, TitleHeight: 0, YAxis: false
			// },
			// #ifdef H5
			{
				Index: "VOL",
				Modify: false,
				Change: false,
				Close: false,
				TitleHeight: 0
			},
			// #endif

			// #ifdef APP-PLUS
			{
				Index: "VOL",
				TitleHeight: 0,
			},
				// #endif

				// {  Name: 'VOL_NEW', Script:'MA5:MA(CLOSE,5);'}, //自定义一个MA的指标 计算5日均线
				// {  Name: 'MA_NEW', Script:'MA5:VOL(AMOUNT,5);'}, 
			],
		CorssCursorInfo: { Left: 2, Right: 1, Bottom: 1, IsShowCorss: true },  //十字光标刻度设置
		IsAutoUpdate: true, //是自动更新数据
		AutoUpdateFrequency: 1000, //数据更新频率
		IsShowRightMenu: false, //右键菜单
		CorssCursorTouchEnd: true,

		IsApiPeriod: true, //复权,周期都使用后台数据

		// CorssCursorTouchEnd: true,
		//StepPixel:5,        //移动一个K线需要的手势移动的像素(默认4)
		//ZoomStepPixel:8,    //缩放一次,2个手指需要移动的间距像素(默认5)

		KLine: //K线设置
		{
			// #ifdef H5
			DragMode: 1, //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择

			// #endif

			// #ifdef APP-PLUS
			DragMode: 1, //拖拽模式 0 禁止拖拽 1 数据拖拽 2 区间选择

			// #endif

			Right: 0, //复权 0 不复权 1 前复权 2 后复权
			Period: 20001, //周期 0 日线 1 周线 2 月线 3 年线
			MaxReqeustDataCount: 1000, //数据个数
			MaxRequestMinuteDayCount: 10, //分钟数据取5天
			// PageSize: 40,                 //一屏显示多少数据
			// #ifdef H5
			PageSize: 40, //一屏显示多少数据
			// #endif

			// #ifdef APP-PLUS
			DataWidth: 3,
			// #endif
			IsShowTooltip: false, //是否显示 view K线提示信息 (手机端要填false)
			DrawType: 4, //K线类型 0=实心K线柱子 1=收盘价线 2=美国线 3=空心K线柱子 4=收盘价面积图
			RightSpaceCount: 1,
		},

		KLineTitle: //标题设置
		{
			//IsShowName:true,       //显示股票名称
			//IsShowSettingInfo:true //显示周期/复权
		},

		Border: //边框
		{
			Left: 1, //左边间距
			Right: 52, //右边间距
			Bottom: 25, //底部间距
			Top: 4, //顶部间距
		},

		Frame: //子框架设置
			[{
				SplitCount: 5,
				IsShowLeftText: false,
				Height: 12,
				SplitType: 1,
				IsShowXLine: false,
				IsShowYLine: false,
				Custom: [{
					Type: 0,
					Position: "right"
				},],
			},

			{
				SplitCount: 2,
				IsShowLeftText: false,
				Height: 4,
				IsShowRightText: false,
				IsShortTitle: false
			},
			],

		ExtendChart: //扩展图形
			[
				// { Name: "KLineTooltip" },  //手机端tooltip
			],
	}

	return option
}
export default {
	props: ["selectTopHeight", "selectBottomHeight", "statusBarHeight", "clickData", "period"],
	components: {
		HQChartControl
	},

	data() {
		let data = {
			isarea: false,
			clickevent: 1,
			// Symbol: `${store.state.optionSymbol.toUpperCase()}-USDT.bit`,
			Symbol: 'ETH-USDT.bit',
			// Symbol: `${store.state.optionSymbol == "unic" ? "OKB" : store.state.optionSymbol.toUpperCase()}-USDT.bit`,
			Period: 20001, //周期 fenshi 
			KLineWebSocket: null,
			ChartWidth: 707,
			ChartHeight: 300,
			selectTab: 4,
			domTopHeight: this.selectTopHeight,
			domBottomHeight: this.selectBottomHeight,
			domBrHeight: this.statusBarHeight,
			addHeight: this.statusBarHeight, //当设备是手机时用来给上面的line1增加高度
		}

		return data
	},

	computed: {
		// ...mapGetters(["optionSymbol", "realSocketData"]),
		// options() {
		//   return this.optionSymbol
		// },
		// socketData() {
		//   return this.realSocketData
		// },
	},

	watch: {
		period: {
			handler(newval, oldval) {
				if (newval == 'area') {
					this.isarea = true
					this.InitalHQChart()
					this.ChangeKLineArea()

				} else {
					this.isarea = false
					this.InitalHQChart()
					this.ChangePeriod(newval)
					// this.createForm()
				}
			},
			deep: true
		},
		clickData(val) {
			console.log("用户点击了切换按钮事件", val)
		},
		options(val) {
			console.log("用户点击了切换按钮事件111", val)

			let symbol = `${val.toUpperCase()}-USDT.bit`
			// if (symbol == "UNIC-USDT.bit") {
			//   symbol = "OKB-USDT.bit"
			// }
			this.ChangeSymbol(symbol)
			// DataCtrl.HQData.GetBITDecimal()
		},
		socketData(val) {
			DataCtrl.HQData.ReceiveSocketData(val)
		},
	},
	created() { },
	mounted() {
		console.log("传过来的元素高度", this.domBottomHeight)
		// setTimeout(() => {
		this.createForm()
		// }, 200);
		// this.ChangeKLineArea()
		// this.InitalHQChart()
		// store.commit("selectShowForm", true)
	},

	onHide() {
		if (this.KLineWebSocket) this.KLineWebSocket.Close()
		this.ClearHQChart()
		console.log("关闭")
		clearInterval(DataCtrl.timer)
	},

	onUnload() {
		if (this.KLineWebSocket) this.KLineWebSocket.Close()
		this.ClearHQChart()
	},

	methods: {
		handleChangeArea(a) {
			console.log(a, 3333);
			this.clickevent++;
			// 根据调用次数来决定执行哪个函数
			if (this.clickevent % 2 === 1) {
				this.isarea = false
				this.ChangePeriod(this.Period);
			} else {
				this.isarea = true
				this.ChangeKLineArea();
			}
		},


		//当获取到元素高度之后，在开始创建表格
		createForm() {
			console.log('createform');
			uni.getSystemInfo({
				success: (res) => {
					var width = res.windowWidth
					var height = res.windowHeight
					console.log("表的屏幕高度", height)
					console.log("设备型号", res.deviceBrand)
					this.ChartWidth = width - 16
					// this.ChartWidth = 390
					console.log(height, this.domTopHeight, this.domBottomHeight, '123123123')
					this.ChartHeight = 170

					// #ifdef H5
					// this.ChartHeight = parseInt(height * 0.6)
					// this.ChartHeight = height - this.domTopHeight - this.domBottomHeight - 31 - 10
					// #endif

					// #ifdef APP-PLUS
					// this.ChartHeight = parseInt(height * 0.58)
					//华为手机要多加15个PX才能满屏
					// if (res.deviceBrand == "huawei") {
					//   this.ChartHeight = height - this.domTopHeight - this.domBottomHeight - this.addHeight + 50 - 31 + 15
					// } else {
					//   this.ChartHeight = height - this.domTopHeight - this.domBottomHeight - this.addHeight + 50 - 31
					// }
					// this.ChartHeight = 200
					// #endif

				},
			})

			this.$nextTick(() => {
				this.KLineWebSocket = new DataCtrl.HQWebSocket()
				// this.CreateKLineChart()
				// this.InitalHQChart()
				// this.CreateKLineAreaChart()
				this.CreateKLineChart()
				this.KLineWebSocket.Create()
			})

			this.isarea = false
			this.InitalHQChart()

			// this.CreateKLineChart_app()
		},
		destroyFrom() {
			console.log("销毁组件")
			this.ClearHQChart()
		},
		CreateKLineChart() {
			if (this.KLineWebSocket) this.KLineWebSocket.UnsubKLine()

			this.ClearHQChart()

			var chartHeight = this.ChartHeight
			let hqchartCtrl = this.$refs.HQChartCtrl

			var option = DefaultData.GetKLineOption()
			option.Symbol = this.Symbol
			option.KLine.Period = this.Period
			hqchartCtrl.KLine.Option = option

			hqchartCtrl.NetworkFilter = this.NetworkFilter
			hqchartCtrl.SetSize(this.ChartWidth, chartHeight)
			hqchartCtrl.OnSize()

			var chart = hqchartCtrl.CreateHQChart()
			console.log(chart)
			chart.ClassName = "KLine"

			this.KLineWebSocket.HQChart = chart

		},

		OnTouchMinuteChart(event, data, chart) {
			console.log('[KLineChart::OnClickChartPaint] event, data 3333333', event, data);
		},

		CreateKLineAreaChart() {
			if (this.KLineWebSocket) this.KLineWebSocket.UnsubKLine()
			this.ClearHQChart()
			var chartHeight = this.ChartHeight
			let hqchartCtrl = this.$refs.HQChartCtrl

			var option = DefaultData.GetKLineAreaOption()
			option.Symbol = this.Symbol
			hqchartCtrl.KLine.Option = option

			hqchartCtrl.NetworkFilter = this.NetworkFilter
			hqchartCtrl.SetSize(this.ChartWidth, chartHeight)
			hqchartCtrl.OnSize(option)
			var chart = hqchartCtrl.CreateHQChart()
			chart.ClassName = "KLineArea"

			this.KLineWebSocket.HQChart = chart

		},

		IsKLineChart() {
			// let hqchartCtrl = this.$refs.HQChartCtrl
			// if (hqchartCtrl) return false
			// // #ifdef H5
			// var chart = hqchartCtrl.GetJSChart()
			// if (!chart) return false
			// return chart.ChartName == "KLine"
			// // #endif

			// // #ifdef APP-PLUS
			// return chart.ChartName == "KLine"
			// // #endif
			let hqchartCtrl = this.$refs.HQChartCtrl
			if (hqchartCtrl) return false
			var chart = hqchartCtrl.GetJSChart()
			if (!chart) return false
			return chart.ChartName == "KLine"
		},

		IsKLineAreaChart() {
			let hqchartCtrl = this.$refs.HQChartCtrl
			if (hqchartCtrl) return false
			var chart = hqchartCtrl.GetJSChart()
			if (!chart) return false
			return chart.ChartName == "KLineArea"
		},

		ClearHQChart() {
			let hqchartCtrl = this.$refs.HQChartCtrl
			if (hqchartCtrl) hqchartCtrl.ClearChart()
		},

		//设置图形颜色
		SetHQChartStyle(blackStyle, bool) {
			console.log(blackStyle, 'blackstyle');
			blackStyle.FrameTitleBGColor = "rgb(12,28,45)" //指标标题背景
			blackStyle.FrameSplitTextColor = "#fff" //刻度颜色
			blackStyle.FrameTitleBGColor = "transparent"
			// blackStyle.IndexTitleBorderColor = 'transparent';
			// blackStyle.IndexTitleSelectedColor = "transparent";
			// blackStyle.OverlayIndexTitleBGColor = 'transparent';
			blackStyle.FrameSplitPen = "transparent"
			if (bool) {
				blackStyle.UpBarColor = "#63EAEE" //上涨柱子颜色
				blackStyle.DownBarColor = "#63EAEE" // 下跌柱子颜色
			} else {
				blackStyle.UpBarColor = "#EC4068" //上涨柱子颜色
				blackStyle.DownBarColor = "#6CFF8A" // 下跌柱子颜色
			}
			// blackStyle.TooltipPaint
			//K线颜色
			// console.log(this.IsKLineAreaChart(),'yane');

			// if (!this.IsKLineAreaChart()) {
			//   blackStyle.UpBarColor = "#63EAEE" //上涨柱子颜色
			//   blackStyle.DownBarColor = "#63EAEE" // 下跌柱子颜色
			// } else {
			//   blackStyle.UpBarColor = "#EC4068" //上涨柱子颜色
			//   blackStyle.DownBarColor = "#6CFF8A" // 下跌柱子颜色
			// }
			// blackStyle.UpBarColor = "#EC4068" //上涨柱子颜色
			blackStyle.UpTextColor = blackStyle.UpBarColor
			// blackStyle.DownBarColor = "#6CFF8A" // 下跌柱子颜色
			blackStyle.DownTextColor = blackStyle.DownBarColor
			//平盘
			blackStyle.UnchagneBarColor = blackStyle.UpBarColor
			blackStyle.UnchagneTextColor = blackStyle.UpBarColor
			//面积图颜色
			blackStyle.CloseLineColor = "#63EAEE"
			blackStyle.CloseLineAreaColor = "transparent"

		


			// #ifdef APP-PLUS
			blackStyle.Index.LineColor[0] = "transparent"
			blackStyle.Index.LineColor[1] = "transparent"
			blackStyle.Index.LineColor[2] = "transparent"
			// #endif

			// Frame:{ XBottomOffset:1*GetDevicePixelRatio() }

			// 边框颜色
			blackStyle.FrameBorderPen = 'transparent';

			//最新价格刻度颜色 '#EC4068' : '#6CFF8A'  #833645  "#458051"

			blackStyle.FrameLatestPrice.UpBarColor = "#833645"
			blackStyle.FrameLatestPrice.DownBarColor = "#458051"
			blackStyle.FrameLatestPrice.UnchagneBarColor = blackStyle.FrameLatestPrice.UpBarColor

		},
		// CreateKLineChart_app() {
		//   if (g_KLine.JSChart) return;

		//   let element = new JSCommon.JSCanvasElement();
		//   // #ifdef APP-PLUS
		//   element.IsUniApp = true;	//canvas需要指定下 是uniapp的app
		//   // #endif
		//   element.ID = 'kline2';
		//   element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
		//   element.Width = this.ChartWidth;

		//   var blackStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
		//   //blackStyle.DefaultTextColor='rgb(255,0,255)';

		//   JSCommon.JSChart.SetStyle(blackStyle);
		//   g_KLine.JSChart = JSCommon.JSChart.Init(element);
		//   this.KLine.Option.NetworkFilter = this.NetworkFilter;
		//   this.KLine.Option.Symbol = this.Symbol;
		//   this.KLine.Option.IsClickShowCorssCursor = true;
		//   this.KLine.Option.IsFullDraw = true;
		//   g_KLine.JSChart.SetOption(this.KLine.Option);

		//   g_KLine.JSChart.AddEventCallback({ event: JSCommon.JSCHART_EVENT_ID.ON_TITLE_DRAW, callback: this.OnTitleDraw });
		// },


		InitalHQChart() {
			MARKET_SUFFIX_NAME.GetBITDecimal = (symbol) => {
				return 3;
			}
			// return DataCtrl.HQData.GetBITDecimal(symbol)
			let hqchartCtrl = this.$refs.HQChartCtrl

			hqchartCtrl.SetHQChartStyle = () => {
				// #ifdef H5
				var blackStyle = HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID)
				this.SetHQChartStyle(blackStyle, this.isarea)
				HQChart.JSChart.SetStyle(blackStyle)

				blackStyle.UpBarColor = "#63EAEE" //上涨柱子颜色
				blackStyle.DownBarColor = "#63EAEE" // 下跌柱子颜色
				// #endif


				// #ifndef H5
				var blackStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID)
				console.log(blackStyle, 'blackStyle');

				this.SetHQChartStyle(blackStyle, this.isarea)
				JSCommon.JSChart.SetStyle(blackStyle)
				// #endif
			}
		},

		ChangePeriod(period) {
			console.log('zhouqi', period);
			this.selectTab = period
			this.Period = period
			let hqchartCtrl = this.$refs.HQChartCtrl
			// hqchartCtrl.ChangeKLinePeriod(period)
			//#ifndef H5
			// #endif
			console.log(this.IsKLineChart())
			if (this.IsKLineChart()) hqchartCtrl.ChangeKLinePeriod(period)
			else this.CreateKLineChart()
			// // #ifdef H5
			//  //  #endif

		},

		ChangeSymbol(symbol) {
			console.log("切换symbol")
			this.Symbol = symbol
			let hqchartCtrl = this.$refs.HQChartCtrl
			hqchartCtrl.ChangeSymbol(symbol)
		},

		ChangeKLineArea() {
			console.log(this.IsKLineAreaChart(), '是否')
			if (this.IsKLineAreaChart()) return
			this.selectTab = 0
			this.CreateKLineAreaChart()
		},

		NetworkFilter(data, callback) {
			console.log("[HQChartDemo::NetworkFilter] data66666", data)
			switch (data.Name) {
				case "KLineChartContainer::ReqeustHistoryMinuteData": //分钟全量数据下载
					setTimeout(() => {
						DataCtrl.HQData.RequestHistoryMinuteData(data, callback, {
							KLineWS: this.KLineWebSocket
						}, this.Period)
					}, 100);
					break
				case "KLineChartContainer::RequestHistoryData": //日线全量数据下载
					setTimeout(() => {
						DataCtrl.HQData.RequestHistoryMinuteData(data, callback, {
							KLineWS: this.KLineWebSocket
						}, this.Period)
						// DataCtrl.HQData.RequestHistoryData(data, callback, { KLineWS: this.KLineWebSocket }, this.Period)
					}, 100);
					break

				case "KLineChartContainer::RequestFlowCapitalData": //流通股本
					DataCtrl.HQData.RequestFlowCapitalData(data, callback)
					break
			}
		},
		checkKLineArea(){
			this.isarea = true
			this.InitalHQChart()
			this.ChangeKLineArea()
		}
	},
}
</script>

<style lang='scss'>
.clickTab {
	background: #87AAFF;
	color: #383466;
}

.viewchart {
	/* background-image: url("../../static/img/1.jpg");
  background-size: 100% 100%; */
	// background-color: #383466;
	//height: 1000rpx;

	.line2 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		//background-color: #13013F;
		padding: 5px 2px;
		//box-sizing: border-box;
		//#ifdef APP-PLUS
		//top: 120rpx;
		//#endif
		// #ifdef H5
		//top: var(--top-height);
		//#endif
		z-index: 9999;

		view {
			flex: 1;
			border-radius: 8rpx;
			font-size: 12px;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 42rpx;
			min-width: 64rpx;
			height: 21px;
			text-align: center;
		}
	}


}

::v-deep .KLineTooltipPaint {
	border-radius: 30rpx !important;
}
</style>