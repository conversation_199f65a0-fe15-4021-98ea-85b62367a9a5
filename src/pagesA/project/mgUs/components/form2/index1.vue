<template>
  <view class='formScreen'>
    <div class='divchart'>

      <!--  #ifdef  H5 -->
      <div>
        <div class='kline' id='kline' ref='kline'></div>
      </div>
      <!--  #endif -->

      <!--  #ifndef  H5 -->
      <view>
        <canvas id='kline2' canvas-id='kline2' class='kline2'
                v-bind:style="{width: ChartWidth+'px', height: ChartHeight+'px'}"
                @touchstart='KLineTouchStart' @touchmove='KLineTouchMove' @touchend='KLineTouchEnd'></canvas>
      </view>
      <!--  #endif -->

      <!-- <div class='button-sp-area'>
        <button class='mini-btn' type='default' size='mini' @click='ChangePeriod(0)'>日线</button>
        <button class='mini-btn' type='default' size='mini' @click='ChangePeriod(1)'>周线</button>
        <button class='mini-btn' type='default' size='mini' @click='ChangePeriod(4)'>1分钟</button>
        <button class='mini-btn' type='default' size='mini' @click='ChangePeriod(6)'>15分钟</button>
      </div>

      <div class='button-sp-area'>
        <button class='mini-btn' type='default' size='mini' @click="ChangeSymbol('btcusdt')">btcusdt</button>
        <button class='mini-btn' type='default' size='mini' @click="ChangeSymbol('ethusdt')">ethusdt</button>
      </div> -->

    </div>
  </view>
</template>
<script>
import moment from 'moment'
// #ifdef H5
import HQChart from '@/uni_modules/jones-hqchart2/js_sdk/umychart.uniapp.h5.js'
// #endif
// #ifndef H5
import { JSCommon } from '@/uni_modules/jones-hqchart2/js_sdk/umychart.wechat.3.0.js'

// #endif

var pako = require('pako')

function DefaultData () { }

DefaultData.GetKLineOption = function () {
  let data =
      {
        Type: '历史K线图',

        Windows: //窗口指标
            [
              { Index: 'MA', Modify: false, Change: false },
              { Index: 'VOL', Modify: false, Change: false },
            ],

        IsAutoUpdate: false,           //是自动更新数据(不自动更新由外部更新)
        IsApiPeriod: true,             //使用Api计算周期
        IsCorssOnlyDrawKLine: true,
        CorssCursorTouchEnd: true,

        Border: //边框
            {
              Left: 1,
              Right: 1, //右边间距
              Top: 25,
              Bottom: 25,
            },

        KLine:
            {
              Right: 1,                            //复权 0 不复权 1 前复权 2 后复权
              Period: 0,                           //周期: 0 日线 1 周线 2 月线 3 年线
              PageSize: 30,
              IsShowTooltip: false,
            },

        Frame:  //子框架设置
            [
              {
                SplitCount: 3, IsShowLeftText: false, SplitType: 1,
                Custom: [{ Type: 0, Position: 'right' }],
              },

              { SplitCount: 2, IsShowLeftText: false, SplitType: 1 },
              { SplitCount: 2, IsShowLeftText: false },
            ],

        ExtendChart:
            [
              { Name: 'KLineTooltip' },	//开启手机端tooltip
            ],

      }

  return data
}

var g_KLine = { JSChart: null }

export default {
  data () {
    let data =
        {
          Symbol: 'btcusdt.BIT',
          OriginalSymbol: 'btcusdt',
          ChartWidth: 300,
          ChartHeight: 600,
          KLine:
              {
                Option: DefaultData.GetKLineOption(),
              },

          // WSUrl: 'wss://api.huobi.pro/ws',
          WSUrl: '',
          SocketOpen: false,
          LastSubString: null,     //最后一个订阅的数据
          BtcData: {
            last: '',
            lastAmount: '',
            lastApplies: '',
          },
          EtcData: {
            last: '',
            lastAmount: '',
            lastApplies: '',
          },
          OkbData: {
            last: '',
            lastAmount: '',
            lastApplies: '',
          },

        }

    return data
  },

  name: 'KLineChart',

  onLoad () {
    console.log(56965)
  },

  createed () {
    console.log('[KLineChart::onReady]')
    // #ifdef H5
    this.OnSize()
    this.CreateKLineChart()
    // #endif
  },

  mounted () {
    console.log('[KLineChart::onShow]')
    // #ifndef H5
    this.OnSize()
    this.CreateKLineChart()
    // #endif
  },

  onHide () {
    if (g_KLine.JSChart) {
      g_KLine.JSChart.StopAutoUpdate()
      g_KLine.JSChart = null
    }
  },

  onUnload () {
    if (g_KLine.JSChart) {
      g_KLine.JSChart.StopAutoUpdate()
      g_KLine.JSChart = null
    }
  },

  methods:
      {
        startSocket (data1, hqChartData) {
          uni.connectSocket({
            url: 'wss://ws.okx.com:8443/ws/v5/public',
          })
          uni.onSocketOpen(function (res) {
            console.log('WebSocket连接已打开1111111111111！')
            uni.sendSocketMessage({
              data: '{"op":"subscribe","args":[{"channel":"candle1m","instId":"DOGE-USDT"}]}',

              complete: (res) => {
                console.log('发消息', res)
              },
            })
            setInterval(() => {
              uni.sendSocketMessage({
                data: 'ping',
                complete: (res) => {
                  console.log('发消息', res)
                },
              })
            }, 15000)
          })
          uni.onSocketError(function (res) {
            console.log('WebSocket连接打开失败，请检查！')
          })
          var yClose = null //前收盘
          uni.onSocketMessage((res) => {
            if (JSON.parse(JSON.stringify(res.data)) == 'pong') {
              console.log('哈哈哈哈', JSON.stringify(res))
            } else {
              if (JSON.parse(JSON.parse(JSON.stringify(res.data))).arg.instId == 'BTC-USDT') {
                // 时间戳转换
                let recreveData = JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0]
                console.log('有没有数据', recreveData)
                // 时间戳
                console.log('时间戳', recreveData[0])
                // let timeData = this.timeForment(parseInt(recreveData.ts))
                let timeData = this.timeForment(parseInt(recreveData[0]))
                var dateTime = new Date()
                dateTime.setTime(timeData * 1000)
                var date = timeData.slice(0, 4) * 10000 + ((timeData.slice(5, 6)) * 100) +
                    parseInt(timeData.slice(7, 9))
                var time = timeData.slice(11, 13) * 100 + parseInt(timeData.slice(14, 16))

                // var newItem = [
                //   date,
                //   null,
                //   parseFloat(recreveData.open24h),
                //   parseFloat(recreveData.high24h),
                //   parseFloat(recreveData.low24h),
                //   1,
                //   parseFloat(recreveData.lastSz),
                //   parseFloat(recreveData.volCcy24h),
                //   time,
                // ]
                var newItem = [
                  date,
                  yClose,
                  parseFloat(recreveData[4]),// 开盘价
                  parseFloat(recreveData[2]),// 最高价
                  parseFloat(recreveData[3]), // 最低价
                  parseFloat(recreveData[1]),// 收盘价
                  parseFloat(recreveData[5]), //交易量
                  parseFloat(recreveData[6]), //以币为单位的交易量
                  time, // 时间
                ]
                let newdatas = [
                  20211119,
                  null,
                  58092.77,
                  58479.07,
                  55607.76,
                  57881.38,
                  850047955.6858411,
                  14998.607988400556]
                if (hqChartData.data[hqChartData.data.length - 1][8] == time) {
                  yClose = recreveData.close
                  console.log('覆盖数据', hqChartData.data[hqChartData.data.length - 1], newItem)
                  hqChartData.data[hqChartData.data.length - 1] = ''
                  hqChartData.data.push(newItem)
                  console.log('覆盖之后的数据', hqChartData.data[hqChartData.data.length - 1])

                } else {
                  yClose = recreveData.close
                  hqChartData.data.push(newItem)
                  console.log('表里数据', hqChartData.data)
                }
                // #ifdef H5
                console.log('开始打击')
                data1.Callback(hqChartData)
                // #endif
                //#ifndef H5
                data1.Callback({ data: hqChartData })
                //#endif
                this.SubscribRealtimeData(data1)
                console.log('两组数据对比', hqChartData.data)
              }
              // if (JSON.parse(JSON.parse(JSON.stringify(res.data))).arg.instId == 'ETC-USDT') {
              //   if (JSON.parse(JSON.parse(JSON.stringify(res.data))).data) {
              //     this.EtcData.last = JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last //最新价格
              //     // this.$store.commit('setEtcLast', JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last)
              //     this.EtcData.lastAmount = JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].lastSz //最后的成交数量
              //     // this.$store.commit('setEtcLastAmount',
              //     //     JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].lastSz)
              //     this.EtcData.lastApplies = (JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last -
              //             JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h) /
              //         JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h
              //     // console.log('最新涨跌幅', this.EtcData.lastApplies)
              //     // this.$store.commit('setEtcLastApplies',
              //     //     (JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last -
              //     //         JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h) /
              //     //     JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h)
              //   }
              // }
              // if (JSON.parse(JSON.parse(JSON.stringify(res.data))).arg.instId == 'OKB-USDT') {
              //   if (JSON.parse(JSON.parse(JSON.stringify(res.data))).data) {
              //     this.OkbData.last = JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last //最新价格
              //     this.OkbData.lastAmount = JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].lastSz //最后的成交数量
              //     this.OkbData.lastApplies = (JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].last -
              //             JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h) /
              //         JSON.parse(JSON.parse(JSON.stringify(res.data))).data[0].open24h
              //     // console.log('最新涨跌幅', this.OkbData.lastApplies)
              //   }
              // }
            }

          })

        },
        timeForment (time) {
          moment.locale('zh-cn')  //设置为中文
          return moment(time).format('lll')
        },
        //对外接口
        ChangePeriod (period)  //周期切换
        {
          console.log('格式', period)
          //var symbol=this.Symbol;
          g_KLine.JSChart.ChangePeriod(period)
        },

        ChangeSymbol (symbol)   //切换股票
        {
          if (this.OriginalSymbol == symbol) return

          this.OriginalSymbol = symbol
          this.Symbol = symbol + '.BIT'
          g_KLine.JSChart.ChangeSymbol(this.Symbol)
        },

        OnSize () {
          // #ifdef H5
          this.OnSize_h5()
          // #endif
        },

        OnSize_h5 () {
          var chartHeight = this.ChartHeight
          var chartWidth = this.ChartWidth
          var kline = this.$refs.kline
          kline.style.width = chartWidth + 'px'
          kline.style.height = chartHeight + 'px'
          if (g_KLine.JSChart) g_KLine.JSChart.OnSize()
        },
        CreateKLineChart_h5 ()  //创建K线图
        {
          if (g_KLine.JSChart) return

          //var blackStyle=HQChart.HQChartStyle.GetStyleConfig(HQChart.STYLE_TYPE_ID.BLACK_ID);
          //HQChart.JSChart.SetStyle(blackStyle);
          // this.$refs.kline.style.backgroundColor=blackStyle.BGColor;	//div背景色设置黑色

          this.KLine.Option.Symbol = this.Symbol
          let chart = HQChart.JSChart.Init(this.$refs.kline)
          this.KLine.Option.NetworkFilter = this.NetworkFilter
          chart.SetOption(this.KLine.Option)
          g_KLine.JSChart = chart
        },

        CreateKLineChart_app () {
          if (g_KLine.JSChart) return

          let element = new JSCommon.JSCanvasElement()
          // #ifdef APP-PLUS
          element.IsUniApp = true	//canvas需要指定下 是uniapp的app
          // #endif
          element.ID = 'kline2'
          element.Height = this.ChartHeight  //高度宽度需要手动绑定!!
          element.Width = this.ChartWidth

          //var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
          //JSCommon.JSChart.SetStyle(blackStyle);

          g_KLine.JSChart = JSCommon.JSChart.Init(element)
          this.KLine.Option.NetworkFilter = (data, callback) => { this.NetworkFilter(data, callback) }
          this.KLine.Option.Symbol = this.Symbol
          this.KLine.Option.IsClickShowCorssCursor = true
          this.KLine.Option.IsFullDraw = true 	//每次手势移动全屏重绘
          g_KLine.JSChart.SetOption(this.KLine.Option)
        },

        CreateKLineChart () {
          // #ifdef H5
          this.CreateKLineChart_h5()
          // #endif

          // #ifndef H5
          this.CreateKLineChart_app()
          // #endif
        },

        NetworkFilter (data, callback) {
          console.log('[KLineChart::NetworkFilter] data', data)
          switch (data.Name) {
            case 'KLineChartContainer::ReqeustHistoryMinuteData':   //分钟全量数据下载
              this.RequestHistoryMinuteData(data, callback)
              break
            case 'KLineChartContainer::RequestFlowCapitalData':     //数字货币不会调用
              this.RequestFlowCapitalData(data, callback)
              break
            case 'KLineChartContainer::RequestHistoryData':         //日线全量数据下载
              this.RequestHistoryData(data, callback)
              break
          }
        },

        //
        //WS
        //心跳包
        SendWSHeartMessage () {
          if (this.SocketOpen) {
            var pong = { 'pong': new Date().getTime() }
            var message = JSON.stringify(pong)
            uni.sendSocketMessage({ data: message })
          }
        },

        //取消订阅上一次的信息
        SendUnSubscribeMessage () {
          if (!this.LastSubString || !this.SocketOpen) return

          var message = JSON.stringify({ unsub: this.LastSubString }) //取消上次订阅的信息
          uni.sendSocketMessage({ data: message })
          this.LastSubString = null    //清空最后一个订阅信息
        },

        RequestWSData (data, recvCallback) {
          console.log('进去没')
          if (!this.SocketOpen) {
            uni.connectSocket({ url: this.WSUrl })//创建连接
            uni.onSocketOpen((event) => {
              this.SocketOpen = true
              console.log(event)
              var message = JSON.stringify(data.SendData)
              uni.sendSocketMessage({ data: message })
              if (data.SendData.sub) this.LastSubString = data.SendData.sub
            })
          } else {
            this.SendUnSubscribeMessage()
            var message = JSON.stringify(data.SendData)
            uni.sendSocketMessage({ data: message })
            if (data.SendData.sub) this.LastSubString = data.SendData.sub    //保存最后一个订阅信息
          }

          uni.onSocketMessage((event) => {
            let ploydata = new Uint8Array(event.data)
            let msg = pako.inflate(ploydata, { to: 'string' })
            //console.log("[KLineChart::RequestWSData] recv ", msg);
            var recvData = JSON.parse(msg)
            if (recvData.ping) {
              this.SendWSHeartMessage()  //回复服务器心跳包
            } else if (recvData.unsubbed) //取消订阅成功
            {
            } else if (recvData.subbed)   //订阅成功
            {
            } else {
              recvCallback(recvData, data)
            }
          })
          uni.onSocketError((evnet) => {
            console.log(event)
          })
        },

        //生成请求数据
        GeneratePostData (symbol, period) {
          //1min, 5min, 15min, 30min, 60min,4hour,1day,1week, 1mon
          var MAP_PERIOD = new Map([
            [4, '1min'],
            [5, '5min'],
            [6, '15min'],
            [0, '1day'],
            [1, '1week'],
            [2, '1mon'],
          ])

          var strPeriod = MAP_PERIOD.get(period)

          var reqData =
              {
                req: `market.${symbol}.kline.${strPeriod}`,
                symbol: symbol,
                period: strPeriod,
              }

          var subData =
              {
                sub: `market.${symbol}.kline.${strPeriod}`,
                symbol: symbol,
                period: strPeriod,
              }

          return { Req: reqData, Sub: subData }
        },

        //请求分钟历史数据
        RequestHistoryMinuteData (data, callback) {
          data.PreventDefault = true
          var symbol = data.Request.Data.symbol
          var period = data.Self.Period    //周期

          var postData = this.GeneratePostData(this.OriginalSymbol, period)

          var obj = {
            SendData: postData.Req,
            Symbol: symbol,
            OriginalSymbol: this.OriginalSymbol,
            Period: period,
            Callback: callback,
          }
          // this.RecvOkHistoryData(obj)
          console.log('对象参数', obj)

          // this.RequestWSData(obj, (recvData, data) => { this.RecvHistoryMinuteData(recvData, data) })
        },

        //接收历史分钟数据
        RecvHistoryMinuteData (recvData, data) {
          if (recvData.rep != data.SendData.req) return

          var hqChartData = { code: 0, data: [] }
          hqChartData.symbol = data.Symbol
          hqChartData.name = data.OriginalSymbol

          if (recvData.data) {
            var yClose = null //前收盘
            for (var i in recvData.data) {
              var item = recvData.data[i]

              //时间戳转换
              var dateTime = new Date()
              dateTime.setTime(item.id * 1000)
              var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate()
              var time = dateTime.getHours() * 100 + dateTime.getMinutes()

              var newItem = [date, yClose, item.open, item.high, item.low, item.close, item.vol, item.amount, time]

              yClose = item.close
              hqChartData.data.push(newItem)
            }
          }

          // #ifdef H5
          data.Callback(hqChartData)
          // #endif

          // #ifndef H5
          data.Callback({ data: hqChartData })
          // #endif

          this.SubscribeMinuteRealtimeData(data)
        },

        //订阅最新分钟K线数据
        SubscribeMinuteRealtimeData (data) {
          //订阅最新数据
          var postData = this.GeneratePostData(data.OriginalSymbol, data.Period)
          var obj = {
            SendData: postData.Sub,
            Symbol: data.Symbol,
            OriginalSymbol: data.OriginalSymbol,
            Period: data.Period,
          }
          this.RecvOkHistoryData(obj)
          // this.RequestWSData(obj, (recvData, data) => { this.RecvMinuteRealtimeData(recvData, data) })
        },

        RecvMinuteRealtimeData (recvData, data) {
          if (recvData.ch != data.SendData.sub) return
          var internalChart = g_KLine.JSChart.JSChartContainer
          var period = internalChart.Period
          var symbol = internalChart.Symbol
          if (symbol != data.Symbol || period != data.Period) return

          var hqChartData = { code: 0, data: [], ver: 2.0 } //更新数据使用2.0版本格式
          hqChartData.symbol = data.Symbol
          hqChartData.name = data.OriginalSymbol

          //TODO:把recvData => hqchart内部格式 格式看教程
          //HQChart使用教程30-K线图如何对接第3方数据15-轮询增量更新1分钟K线数据

          var item = recvData.tick

          var dateTime = new Date()
          dateTime.setTime(item.id * 1000)
          var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate()
          var time = dateTime.getHours() * 100 + dateTime.getMinutes()
          var newItem = [date, null, item.open, item.high, item.low, item.close, item.vol, item.amount, time]

          hqChartData.data.push(newItem)

          // #ifdef H5
          internalChart.RecvMinuteRealtimeData(hqChartData)
          // #endif

          // #ifndef H5
          internalChart.RecvMinuteRealtimeData({ data: hqChartData })
          // #endif
        },

        //日K数据下载
        RequestHistoryData (data, callback) {
          data.PreventDefault = true
          var symbol = data.Request.Data.symbol
          var period = data.Self.Period    //周期
          var postData = this.GeneratePostData(this.OriginalSymbol, period)
          var obj = {
            SendData: postData.Req,
            Symbol: symbol,
            OriginalSymbol: this.OriginalSymbol,
            Period: period,
            Callback: callback,
          }
          this.RecvOkHistoryData(obj)

          // this.RequestWSData(obj, (recvData, data) => {
          //   this.RecvHistoryData(recvData, data)
          //   // this.RecvOkHistoryData(recvData, data)
          // })
        },
        RecvOkHistoryData (data) {
          // if (recvData.rep != data.SendData.req) return
          var hqChartData = { code: 0, data: [] }
          hqChartData.symbol = data.Symbol
          hqChartData.name = data.OriginalSymbol
          uni.request({
            url: 'http://192.168.1.4:7001/ok',
            success: (res) => {
              console.log('请求的数据', res.data.data)
              let yClose = null //前收盘
              for (let i in res.data.data.data) {
                var item = res.data.data.data[i]

                //时间戳转换
                let timeData = this.timeForment(parseInt(item[0]))
                console.log('转换了', timeData)
                // console.log('时间管理年', timeData.slice(0, 4) * 10000)
                // console.log('时间管理月', timeData.slice(5, 6))
                // console.log('时间管理日', timeData.slice(7, 9))
                // console.log('时间管理时', timeData.slice(11, 13))
                // console.log('时间管理分', timeData.slice(14, 16))
                var dateTime = new Date()
                // console.log('时间戳', this.timeForment(parseInt(item[0])))
                dateTime.setTime(item[0] * 1000)
                var date = timeData.slice(0, 4) * 10000 + ((timeData.slice(5, 6)) * 100) +
                    parseInt(timeData.slice(7, 9))
                // console.log('时间', date)
                var time = timeData.slice(11, 13) * 100 + parseInt(timeData.slice(14, 16))
                // console.log('分钟', time)

                var newItem = [
                  date,
                  yClose,
                  parseFloat(item[4]),
                  parseFloat(item[2]),
                  parseFloat(item[3]),
                  parseFloat(item[1]),
                  parseFloat(item[5]),
                  parseFloat(item[6]),
                  time]
                // 参数意思
                // 第一个 数据格式
                // 第二个 null
                // 第三个 收盘价
                // 第四个 最高价
                // 第五个 最低价
                // 第六个 开盘价
                // 第七个 交易量
                // 以基础币种计量的交易量
                yClose = item.close
                hqChartData.data.unshift(newItem)
                console.log('表里数据1', hqChartData.data)
              }
              // #ifdef H5
              console.log('打一把')
              data.Callback(hqChartData)
              // #endif

              // #ifndef H5
              data.Callback({ data: hqChartData })
              // #endif

              this.SubscribRealtimeData(data)
              this.startSocket(data, hqChartData)
            },
          })

          // if (recvData.data) {
          //   var yClose = null //前收盘
          //   for (var i in recvData.data) {
          //     var item = recvData.data[i]
          //     console.log('每一组的数据', item)
          //     //时间戳转换
          //     var dateTime = new Date()
          //     dateTime.setTime(item.id * 1000)
          //     var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate()
          //     var time = dateTime.getHours() * 100 + dateTime.getMinutes()
          //
          //     var newItem = [date, yClose, item.open, item.high, item.low, item.close, item.vol, item.amount]
          //     // 参数意思
          //     // 第一个 数据格式
          //     // 第二个 null
          //     // 第三个 开盘价
          //     // 第四个 最高价
          //     // 第五个 最低价
          //     // 第六个 收盘价
          //     // 第七个 交易量
          //     // 以基础币种计量的交易量
          //     console.log('历史数据', newItem)
          //     yClose = item.close
          //     let newdatas = [
          //       20211119,
          //       null,
          //       58092.77,
          //       58479.07,
          //       55607.76,
          //       57881.38,
          //       850047955.6858411,
          //       14998.607988400556]
          //     // hqChartData.data.push(newdatas)
          //     hqChartData.data.unshift(newdatas)
          //   }
          // }

        },

        //接收到日线数据 转化成hqchart格式数据
        RecvHistoryData (recvData, data) {
          console.log('进去没')
          if (recvData.rep != data.SendData.req) return

          var hqChartData = { code: 0, data: [] }
          hqChartData.symbol = data.Symbol
          hqChartData.name = data.OriginalSymbol

          if (recvData.data) {
            var yClose = null //前收盘
            for (var i in recvData.data) {
              var item = recvData.data[i]

              //时间戳转换
              var dateTime = new Date()
              dateTime.setTime(item.id * 1000)
              var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate()
              var time = dateTime.getHours() * 100 + dateTime.getMinutes()

              var newItem = [date, yClose, item.open, item.high, item.low, item.close, item.vol, item.amount, time]
              console.log('历史数据', newItem)
              yClose = item.close
              let newdatas = [
                20211119,
                null,
                58092.77,
                58479.07,
                55607.76,
                57881.38,
                850047955.6858411,
                14998.607988400556]
              hqChartData.data.push(newItem)
            }
          }

          // #ifdef H5
          data.Callback(hqChartData)
          // #endif

          // #ifndef H5
          data.Callback({ data: hqChartData })
          // #endif

          this.SubscribRealtimeData(data)
        },

        //订阅最新日K线数据
        SubscribRealtimeData (data) {
          //订阅最新数据
          var postData = this.GeneratePostData(data.OriginalSymbol, data.Period)

          var obj = {
            SendData: postData.Sub,
            Symbol: data.Symbol,
            OriginalSymbol: data.OriginalSymbol,
            Period: data.Period,
          }
          // this.startSocket(obj)
          // this.RequestWSData(obj, (recvData, data) => { this.RecvRealtimeData(recvData, data) })
        },

        RecvRealtimeData (recvData, data) {
          if (recvData.ch != data.SendData.sub) return

          var internalChart = g_KLine.JSChart.JSChartContainer
          var period = internalChart.Period
          var symbol = internalChart.Symbol
          if (symbol != data.Symbol || period != data.Period) return

          var hqChartData = { code: 0, stock: [] }
          //TODO:把recvData => hqchart内部格式 格式看教程
          //HQChart使用教程30-K线图如何对接第3方数据14-轮询增量更新日K数据

          var stock = { symbol: data.Symbol, name: data.OriginalSymbol }
          var item = recvData.tick
          var dateTime = new Date()
          dateTime.setTime(item.id * 1000)
          var date = dateTime.getFullYear() * 10000 + (dateTime.getMonth() + 1) * 100 + dateTime.getDate()
          var time = dateTime.getHours() * 100 + dateTime.getMinutes()

          stock.date = date
          stock.yclose = null
          stock.open = item.open
          stock.high = item.high
          stock.low = item.low
          stock.price = item.close
          stock.vol = item.vol
          stock.amount = item.amount

          hqChartData.stock.push(stock)

          // #ifdef H5
          internalChart.RecvRealtimeData(hqChartData)
          // #endif

          // #ifndef H5
          internalChart.RecvRealtimeData({ data: hqChartData })
          // #endif
        },

        ///
        //手势事件 app/小程序才有
        //KLine事件
        KLineTouchStart: function (event) {
          if (g_KLine.JSChart) g_KLine.JSChart.OnTouchStart(event)
        },

        KLineTouchMove: function (event) {
          if (g_KLine.JSChart) g_KLine.JSChart.OnTouchMove(event)
        },

        KLineTouchEnd: function (event) {
          if (g_KLine.JSChart) g_KLine.JSChart.OnTouchEnd(event)
        },
      },
}
</script>
<style scoped lang='less'></style>
