<template>
    <view style="padding-bottom: 120rpx;background: #111;">
        <view class="tabs_div">
            <view class="left">
                <!-- <u-tabs name="cate_name" bg-color="" :bar-style="barStyle" :list="tabList" bold
                    inactive-color="rgb(255,255,255,0.3)" :active-item-style="itemStyle" active-color="#fff"
                    :scrollable="false" :current="current" @change="changeTab" font-size="28rpx"></u-tabs> -->
                <view class="utab">
                    <view @click="changeTab(0)" :class="current == 0 ? 'utabact' : ''">等待成交 ({{ entrusttotal }}) </view>
                    <view @click="changeTab(1)" :class="current == 1 ? 'utabact' : ''">我的仓位 ({{ positiontotal }})</view>
                    <view class="utabbar" :style="{ left: current == 0 ? '67rpx' : dongWidth() }"></view>
                </view>
            </view>
            <view class="right" @click="historyTakeOff">
                <image src="https://cdn-lingjing.nftcn.com.cn/image/20240729/84cdc83886a1010659faa1fd0536c722_72x63.png"
                    mode="widthFix"></image>
            </view>
        </view>

        <view class="details">
            <!-- 当前委托 List-->
            <view class="weituo" v-show="current == 0">
                <view class="li" v-for="(item, index) in entrustList" :key="index">
                    <!-- -->
                    <view class="heads">
                        <view class="left">
                            <image v-if="item.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix"></image>
                            <!-- -->
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <text class="level">{{ item.leverageLevel }}X</text>
                            <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                            <text class="sub">{{ item.ctime || '--' }}</text>
                            <!-- {{ item.createAt }} -->
                        </view>
                        <view class="cd">

                            <view class="progress">
                                <text>{{ dealprogress(item) }}%</text>
                                <view class="bg">
                                    <view class="bar" :style="{ width: (item.dealMoney / item.money) * 100 + '%' }">
                                    </view>
                                </view>
                            </view>
                            <!-- @click="openPop(item, 1, index)" v-if="item.canRevoke" -->
                            <view v-if="item.status == 5" class="cancel">
                                <image mode="widthFix" class="rotate-image"
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                            </view>
                            <view class="cancel" @click="backuporder(item)" v-else>撤单</view>
                        </view>
                    </view>
                    <!-- card 底部 -->
                    <view class="data_view">
                        <view class="li_view">
                            <view class="label">成本</view>
                            <view class="num" :style="{ marginTop: item.experienceMoney > 0 ? '10rpx' : '10rpx' }">
                                <text>{{ '￥' + formatNumber(item.payMoney - item.experienceMoney) }}</text>
                                <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}(体验金)</text>
                                <!-- <text>+￥{{ item.experienceMoney }}(体验金)</text> -->
                            </view>
                        </view>
                        <view class="li_view">
                            <view class="label">成交金额/下单金额</view>
                            <view class="num">{{ '￥' + formatNumber(item.dealMoney, 3) + ' / ' + '￥' +
                                formatNumber(item.money, 3) }}</view>
                            <!-- {{ item.filledQuantity }} -->
                        </view>
                        <view class="li_view">
                            <view class="label">
                                <text>下单价格</text>
                                <!-- <text v-show="item.longShort == 2">唱空价</text>
                                <text v-show="item.longShort == 1">唱多价</text> -->
                            </view>
                            <view class="num" v-if="item.price">{{ '￥' + item.price.toFixed(3) || '--' }}</view>
                            <view class="num" v-else>{{ '市价' }}</view>
                            <!-- {{ item.price }} -->
                        </view>
                    </view>
                </view>

                <view class="nodata" v-if="!entrustList.length">
                    <image mode="widthFix"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                    <text>{{ token ? '暂时无数据' : '您还未登录' }}</text>
                    <view class="nav_login" @tap="nav_login" v-if="!token">
                        登录/注册
                    </view>
                </view>
            </view>

            <!-- 持仓 List -->
            <view class="cangwei" v-show="current == 1">
                <!-- <transition name="van-fade"> -->
                <view class="li" v-for="(item, index) in positionsList" :key="index">
                    <!-- :class="{ 'active': item.isActive }" v-for="(item, index) in positionsList"
                    :key="index" -->
                    <view class="heads">
                        <view class="left">
                            <!-- v-if="item.longShort == 2" -->
                            <image v-if="item.side == 'SELL'"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                                alt="" mode="widthFix" />
                            <image v-else
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                                alt="" mode="widthFix"></image>
                            <text class="level">{{ item.leverageLevel + 'x' }}</text>
                            <!-- {{ item.lever == 1 ? '1X' : item.lever + 'X' }} -->
                        </view>
                        <view class="live_data">
                            <view class="sy">
                                <text>收益 </text>
                                <text :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"> {{
                                    calculateEarnings(item) }}
                                </text>
                            </view>

                            <view class="syl red">
                                <text>收益率 </text>
                                <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"> {{ calculateYield(item) }}
                                </text>
                            </view>
                        </view>
                    </view>
                    <view class="right_fenx">
                        <!-- @click="openShare(item)" -->
                        <image @click="openShare(item)"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240729/0d72a446e4fdb9f704719716b76e3622_72x80.png"
                            alt="" srcset="" mode="widthFix"></image>
                    </view>



                    <view class="data_views">
                        <view class="li_view">
                            <view class="label">开仓金额</view>
                            <!-- {{ item.longShort == 2backuporder ? "唱空" : "唱多" }}  :style="{ marginTop: item.experienceMoney > 0 ? '12rpx' : '20rpx' }"-->
                            <view class="num">
                                <text>{{ '￥' + formatNumber(item.money - item.experienceMoney) }}</text>
                                <text v-if="item.experienceMoney">+￥{{ item.experienceMoney }}(体验金)</text>
                                <!-- <text>+￥{{ item.experienceMoney }}(体验金)</text> -->
                            </view>
                            <!-- {{ item.price }} -->
                        </view>
                        <view class="li_view">
                            <view class="label">{{ item.side == 'SELL' ? '唱空均价' : '唱多均价' }}</view>
                            <view class="num">{{ '￥' + formatNumber(item.price, 3) }}</view>
                            <!-- {{ item.quantity }} -->
                        </view>
                        <view class="li_view">
                            <view class="label">强平价</view>
                            <view class="num">{{ item.reducePrice ? '￥' + formatNumber(item.reducePrice, 3) : '--' }}
                            </view>
                            <!-- {{ item.forceClosePrice ? item.forceClosePrice : '-' }} -->
                        </view>
                        <!-- <view class="li_view" v-if="item.takeProfitPrice || item.stopLossPrice">
                            <view class="label">止盈价</view>
                            <view class="num" v-if="item.takeProfitPrice">{{ '￥' + item.takeProfitPrice || '--' }}
                            </view>
                            <view class="num" v-else>--</view>

                        </view>
                        <view class="li_view" v-if="item.stopLossPrice || item.takeProfitPrice">
                            <view class="label">止损价</view>
                            <view class="num" v-if="item.stopLossPrice">{{ '￥' + item.stopLossPrice }}</view>
                            <view class="num" v-else>--</view>
                        </view> -->
                    </view>
                    <view class="stoploss" v-if="item.takeProfitPrice || item.stopLossPrice">
                        <text class="left">止盈止损</text>
                        <view class="right">
                            <text>{{ item.takeProfitPrice || '--' }}</text>
                            <view style="color: #fff;">/</view>
                            <text>{{ item.stopLossPrice || '--' }}</text>
                            <!-- <image mode="widthFix"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240825/12d33c892712b44c2740d7f8064f7b5c_43x43.png" /> -->
                        </view>
                    </view>
                    <view class="close" :style="{ marginTop: '20rpx' }">
                        <!-- @click="openPop(item, 2, index)" v-if="item.canClose" -->
                        <view @tap="setProLoss(item)"
                            v-if="item.stopLossPrice == null && item.takeProfitPrice == null && EnableproLoss">
                            设置止盈止损</view>
                        <view @tap="resetProLoss(item)"
                            v-if="(item.stopLossPrice || item.takeProfitPrice) && EnableproLoss">重置止盈止损</view>
                        <view @tap="nobalance = true" class="unlock"
                            v-if="!EnableproLoss && item.stopLossPrice == null && item.takeProfitPrice == null">解锁止盈止损
                        </view>
                        <view v-if="(item.pendingCloseVolume != item.closeVolume)" @click="toastPC"
                            style="margin-left: 30rpx;opacity: .5;">平仓中
                            <image mode="widthFix" class="rotate-image"
                                style="width: 18rpx;height: 24rpx;margin-left: 10rpx;"
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240830/71ced236fce5a681bedc9186393ea174_36x48.png" />
                        </view>
                        <view @tap="closeonce(item)" style="margin-left: 30rpx;" v-else>一键平仓</view>
                        <!-- class="oneclose"  -->
                    </view>
                </view>
                <view class="nodata" v-if="!positionsList.length">
                    <image mode="widthFix"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                    <text>{{ token ? '暂时无数据' : '您还未登录' }}</text>
                    <view class="nav_login" @tap="nav_login" v-if="!token">
                        登录/注册
                    </view>
                </view>
                <!-- </transition> -->
            </view>
        </view>

        <!-- 止盈止损弹窗 -->
        <u-modal class="" v-model="proLoss" width="468rpx" borderRadius="36" :show-title="false" :mask-close-able="true"
            :show-confirm-button="false">
            <view class="modalpro">
                <view class="m_head">
                    <view class="left">
                        <image v-if="profitLossobj.side == 'SELL'"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                            alt="" mode="widthFix" />
                        <image v-else
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                            alt="" mode="widthFix"></image>
                        <view>
                            <!-- {{ profitLossobj.side == 'SELL' }} -->
                            <text class="price">价值</text>
                            <text class="num">￥{{ profitLossobj.money }} <text class="num2"
                                    :style="{ color: profitLossobj.red ? '#EC4068' : '#6CFF8A' }">({{
                                        (calculateEarnings(profitLossobj)) }}) </text></text>
                        </view>
                    </view>
                    <view class="right">{{ profitLossobj.leverageLevel }}x</view>
                </view>

                <view class="m_line"></view>

                <view class="m_openclose">
                    <view class="top">
                        <text>市场价</text>
                        <text>￥{{ market }}</text>
                    </view>
                    <view>
                        <text>止盈价</text>
                        <text class="rb">￥</text>
                        <!-- profitLossobj stopLossPrice -->
                        <input v-model="profit" type="digit" @input="onprofitInputChange" />
                    </view>
                    <view>
                        <text>止损价</text>
                        <text class="rb">￥</text>
                        <input v-model="loss" type="digit" @input="onlossInputChange" />
                    </view>

                </view>

                <view class="m_btn">
                    <view @tap="proLoss = false">取消</view>
                    <view @tap="confirmProLoss">确认</view>
                </view>
            </view>

        </u-modal>

        <!-- 当前余额不足 拥有指定藏品开启止损止盈-->
        <u-modal class="" v-model="nobalance" width="600rpx" borderRadius="36" :show-title="false"
            :mask-close-able="true" :show-confirm-button="false">
            <view class="nomoney" style="margin: 120rpx 0;">
                <text>{{ code == 1 ? '当前余额不足' : '持有' + aimobj.otoAskTitle + '藏品即可开通止盈止损功能' }}</text>
                <view class="img"
                    style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 40rpx;">
                    <image :src="aimobj.otoAskImg" mode="aspectFill"
                        style="width: 160rpx;height: 160rpx;border-radius: 30rpx;" />
                    <text style="margin-top: 40rpx">当前地板价为：{{ '¥' + aimobj.otoAskFloorPrice }}</text>
                </view>
                <view class="btn" @click="goBuy()">{{ code == 1 ? '去充值' : '去支付' }}</view>
            </view>
        </u-modal>

        <!-- 老用户进来邀新 -->
        <u-popup mode="center" :duration='700' class="" v-model="oldusershow" @cancel="cancelmodal" borderRadius="36"
            :show-title="false" :mask-close-able="true" @close="popupClose" :show-confirm-button="false">
            <view style="height: 100vh;display: flex;align-items: center;" @click="oldusershow = false">
                <view class="newmoney box" @click.prevent="" :class="!oldusershow ? 'scale' : ''">
                    <text>领取体验金任务</text>
                    <text>邀请3位新用户可免费获得￥15体验金</text>
                    <view @click="gomission()" class="anim">去完成</view>
                </view>
            </view>
        </u-popup>

        <!-- 撤单 -->
        <u-modal class="" v-model="cancelOrder" width="600rpx" borderRadius="36" :show-title="false"
            :mask-close-able="true" :show-confirm-button="false">
            <view class="newmoneys">
                <text>当前下单使用了体验金并已有成交，若撤单则不返还体验金</text>
                <view class="btns">
                    <view @click="backorder()">撤单</view>
                    <view @click="cancelOrder = false">取消</view>
                </view>
            </view>
        </u-modal>

        <!-- 新用户首次进入弹出实名 -->
        <u-modal class="" v-model="checkrealname" width="600rpx" borderRadius="36" :show-title="false"
            :mask-close-able="true" :show-confirm-button="false">
            <view class="newmoneys">
                <text>实名认证即可领取</text>
                <br>
                <text style="font-size: 34rpx;">￥15体验金</text>
                <view class="btnreal">
                    <!-- <view @click="backorder()">撤单</view> -->
                    <view class="goreal" @click="goreals">去实名认证</view>
                </view>
            </view>
        </u-modal>

        <!-- 分享 -->
        <u-popup v-model="isShare" mode="bottom">
            <view class="share_body">
                <!-- <view class="back" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png"
                        alt="" srcset="" mode="widthFix"></image>
                </view> -->
                <view class="cart_div" id="test-id">
                    <view class="title_image">
                        开杠吧
                    </view>
                    <view class="toux_image_div">
                        <view class="toux_border">
                            <view class="image_div">
                                <view class="gray">
                                    <image :src="shareUser.avatar" alt="" srcset="" mode="widthFix"></image>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="toux_name">
                        {{ shareUser.addr }}
                        <!-- 123123112321312 -->
                    </view>

                    <view class="buysell">
                        <image v-if="shareUser.red"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240216/00595db165eb2f5d571f6c18acd268a5_50x50.png"
                            alt="" mode="widthFix" />
                        <image v-else
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240216/a867908d9e85e2465c1ca4ca99599b47_50x50.png"
                            alt="" mode="widthFix"></image>
                        <!-- <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240729/2dfb9593a1cd98273aaeccbf45da2168_100x100.png" /> -->
                        <text>BIT指数</text>
                    </view>
                    <!-- color: #6CFF8A;
                    &.red {
                        color: #FF5270; -->
                    <view class="yield" :style="{ color: shareUser.red ? '#EC4068' : '#6CFF8A' }">
                        <text></text>{{ shareUser.zNum }}
                    </view>
                    <view class="shouyi" :style="{ color: shareUser.red ? '#FF5270' : '#6CFF8A' }">
                        <text></text>{{ '(' + shareUser.gNum + ')' }}
                    </view>
                    <view class="info_div">
                        <p>开仓均价：￥{{ shareUser.price }}</p>
                        <!-- <p>卖出价：￥3.45</p> -->
                        <p>开仓金额：￥{{ shareUser.money }}</p>
                    </view>
                    <view class="yqm">
                        邀请码：{{ shareUser.invitationCode }}
                        <image class="copy-img" style="width: 20rpx;height: 22rpx;margin-left: 10rpx;"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240822/86ee3166817515f270891ab2e1973722_40x44.png"
                            mode="widthFix" @click="copy(shareUser.invitationCode)" />
                    </view>
                    <view class="msg_text">
                        扫码注册，及时上车
                    </view>
                    <view class="icon_bg">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240221/9e18b2b773f6a293f47a95431c680c76_292x274.png"
                            alt="" srcset="" mode="widthFix"></image>
                    </view>
                    <view class="qr_code">
                        <view class="right">
                            <view class="qr_div">
                                <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="104rpx"
                                    :options="options"></uv-qrcode>
                            </view>
                        </view>
                        <view class="time">分享于{{ shareUser.currentDateTime }}</view>
                    </view>
                </view>
                <view class="share_to_div">
                    <!-- #ifdef APP -->
                    <!-- <view class="li" @click="fenx_weixin()">
                        <view class="icon_image">
                            <image
                                src="https://cdn-lingjing.nftcn.com.cn/image/20240221/1b267d478d68e7e233aa9c86b6ca5786_80x80.png"
                                alt="" srcset="" mode="widthFix"></image>
                        </view>
                        <p>微信</p>
                    </view> -->
                    <!-- #endif -->

                    <!-- <view class="li" @click="generate">
						<view class="icon_image">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
								alt="" srcset="" mode="widthFix"></image>
						</view>
						<p>保存海报</p>
					</view> -->
                </view>
                <view class="colse_div" @click="isShare = false">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240221/31bd9dc41df03476f60e632444a93c97_80x80.png"
                        mode="widthFix"></image>
                </view>
            </view>
        </u-popup>

        <!-- 新用户被邀请盈利 -->
        <u-popup mode="center" v-model="newuserreward" :duration='700' @close="popupClose">
            <view class="newuser" :class="!newuserreward ? 'rightscale' : ''">
                <view class="card">
                    <image mode="widthFix" class="tops"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240802/0aec0c2b3fa8152c50a7d08d10e0060f_523x437.png" />

                    <view class="info">
                        <view class="titles">恭喜</view>
                        <view class="money">
                            <view class="grey">
                                <image mode="widthFix" :src="avatar" />
                            </view>

                        </view>
                        <view class="tips">{{ nickname }}</view>

                    </view>

                    <view class="msg">
                        <text :style="{ width: old ? '506rpx' : '260rpx' }">
                            {{ old ? '您已经完成任务，免费获得￥15体验金' : '免费获得￥15体验金' }}
                        </text>
                    </view>

                    <view class="btn" @tap="getbonus">领取</view>
                </view>
            </view>
        </u-popup>

        <!-- 加载中loading -->
        <u-modal class="" v-model="isLinkLoadding" width="40%" :show-title="false" :show-confirm-button="false">
            <div class="sk-wave"></div>
            <view class="text_msg" style="
            padding: 10rpx 20rpx 40rpx 20rpx;
            text-align: center;
            font-size: 26rpx;
            line-height: 40rpx;
          ">
                加载中...
            </view>
        </u-modal>

        <u-modal class="" v-model="isSubmit" width="40%" :show-title="false" :show-confirm-button="false">
            <div class='sk-wave'></div>
            <view class="text_msg"
                style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                支付中...
            </view>
        </u-modal>
    </view>
</template>

<script>
import uniCopy from "@/js_sdk/uni-copy.js";
import antiShake from "@/common/public"

export default {
    props: ['timetofetchPosition', 'isreach', 'ispull'],
    data() {
        return {
            token: '',
            appUrl: '',
            isIntervalSet: false,
            isSubmit: false,
            checkrealname: false,
            latestPrice: '',
            interval: null,
            EnableproLoss: false,
            nickname: '',
            avatar: '',
            isLinkLoadding: false, // 加载中

            positionhasnext: false,
            entrusthasnext: false,
            unlockLoss: false,
            profit: '',
            loss: '',
            cancelOrder: false, // 撤单
            popupShow: '', // 弹窗类型
            tradedPageNum: 1, //分页
            pageNum: 1, //分页
            old: false,
            newuserreward: false,
            show: true,
            oldusershow: false,
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            qrcodeUrl: "",
            shareUser: {},
            isShare: false,
            code: 2,
            nobalance: false,  // 当前余额不足 拥有指定藏品开启止损止盈弹窗
            proLoss: false,    // 止盈止损弹窗
            val: 50,
            current: 0,
            entrustList: [],
            positionsList: [],
            barStyle: {
                'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
                'width': '80rpx',
                'height': '8rpx',
                'border-radius': '0rpx',
                'bottom': '6rpx',
                'z-index': '1'
            },
            itemStyle: {
                'font-size': '28rpx',
                // 'min-width': '120rpx',
                'z-index': '2',
            },
            marketPrice: '', //市价

            tabList: [
                {
                    name: "等待成交",
                    value: 0
                }, {
                    name: "我的仓位",
                    value: 1
                }
            ],
            waitorderNowobj: {},
            profitLossobj: {}, // 止盈止损点击obj
            aimobj: {},
            entrusttotal: 0,
            positiontotal: 0,
            hasFetchedPositions: false, // 标志位，确保 fetchPositions 只调用一次
        }
    },

    created() {
        this.token = uni.getStorageSync("token")
        if (uni.getStorageSync('positionTab')) {
            this.current = uni.getStorageSync('positionTab')
        }
        this.getNewPrice()
        let that = this
        // if (this.entrustList.length != 0) {

        // }
        console.log('created');
        this.fetchActivity()
        this.getUserShare()
        // this.getPositionInfo()
        // this.getPositionInfo()

        this.fetchEntrust()
        this.fetchPositions()

        this.checkpopups()
        this.fetchUser()
        this.fetchwhatgobuy()
        this.waitorderNowobj = {}
        // this.$api.ClosePositions() // 一键平仓
        // this.$api.CreateOrderDer() // 创建订单
        // this.$api.GetCurPosition() // 查询我的仓位
        // this.$api.GetDelegatingOrder() // 查询委托单 等待成交
        // OtoPosition 止盈止损
        // ResetOtoPosition 重置止盈止损
        // RevokeOrder 撤销订单
        // inviteActivity 活动信息查询接口
        // inviteFriends 邀请列表分页查询接口
        // 在组件销毁时清除定时器
        // this.$once('hook:destroyed', () => {
        //     clearInterval(this.interval);
        // });
    },
    destroyed() {
        console.log('beforeDestroy12312312312312');
        // js提供的clearInterval方法用来清除定时器
        clearInterval(this.interval);
        this.interval = null
    },
    // onLoad() {
    //     console.log('12312312312312');
    // },
    // // onHide() {
    // // },
    computed: {
        market() {
            let a = Number(this.marketPrice)
            let b = a.toFixed(3)
            return b
        }
    },
    watch: {
        "$store.state.realprice"(val) {
            this.marketPrice = val;
            let price = Number(val);
            console.log(price, '价格变化了', this.entrustList.length);

            if (this.entrustList.length == 0) {
                clearInterval(this.interval);
                this.interval = null;
            }
            let shouldFetchPositions = false; // 每次进入 watch 监听器时，重置标志位

            if (this.positionsList.length > 0) {
                for (let order of this.positionsList) {
                    if (
                        (order.side === 'BUY' && (order.takeProfitPrice || order.stopLossPrice) && price <= Number(order.stopLossPrice)) ||
                        (order.side === 'BUY' && (order.takeProfitPrice || order.stopLossPrice) && price >= Number(order.takeProfitPrice)) ||
                        (order.side === 'SELL' && (order.takeProfitPrice || order.stopLossPrice) && price <= Number(order.takeProfitPrice)) ||
                        (order.side === 'SELL' && (order.takeProfitPrice || order.stopLossPrice) && price >= Number(order.stopLossPrice)) ||
                        (order.pendingCloseVolume && order.closeVolume && order.pendingCloseVolume === order.closeVolume) // 新增判断条件
                    ) {
                        this.fetchPositions(); // 在循环结束后只调用一次
                        // shouldFetchPositions = true; // 设置标志位为 true
                        break; // 找到符合条件的订单后立即退出循环
                    }
                }
            }

            // if (shouldFetchPositions) {
            //     this.fetchPositions(); // 在循环结束后只调用一次
            // } else {
            //     console.log('条件未满足，不调用 fetchPositions');
            // }
        },

        entrustList: {
            handler(newValue, oldValue) {
                // 如果新数组长度小于旧数组长度，调用 fetchPositions()
                if (newValue.length < oldValue.length) {
                    this.fetchPositions();
                }
                console.log(newValue.length, '委托单变化了');

                // 检查标志位并处理定时任务
                if (newValue && newValue.length > 0) {
                    if (!this.isIntervalSet) {  // 如果定时任务尚未设置
                        this.isIntervalSet = true;  // 设置标志位为 true
                        this.interval = setInterval(() => {
                            this.tradedPageNum = 1;
                            this.fetchEntrust();
                        }, 3000);
                    }
                } else {
                    // 如果数组为空，清除定时任务
                    clearInterval(this.interval);
                    this.interval = null;
                    this.isIntervalSet = false;  // 重置标志位
                }

                if (newValue.length === 0) {
                    clearInterval(this.interval);
                    this.interval = null;
                    this.isIntervalSet = false;  // 确保在数组为空时，标志位被重置
                }

                // 处理数组变动后的逻辑
            },
            deep: true, // 监听数组内部对象的变化
            immediate: true // 组件加载时立即执行 handler
        },

        positionsList: {
            handler(newList, oldValue) {

            }
        },
        proLoss(newval, oldval) {
            if (!newval) {
                this.profitLossobj = {}
                this.profit = ''
                this.loss = ''
            }
        },
        timetofetchPosition(val) {
            // this.getPositionInfo()
            this.changeTab(0)
            this.fetchEntrust()
            this.fetchPositions()
        },
        isreach(val) {
            if (this.current == 0) {
                if (this.entrusthasnext) {
                    this.tradedPageNum += 1
                    this.fetchEntrust()
                }
            } else {
                if (this.positionhasnext) {
                    this.pageNum += 1
                    this.fetchPositions()
                }
            }
        },
        ispull(val) {
            console.log('变了');
            clearInterval(this.interval);
            this.interval = null
            this.fetchUser()
            this.tradedPageNum == 1
            this.pageNum == 1

            this.fetchEntrust()
            this.fetchPositions()
        }
    },
    methods: {
        nav_login() {
            this.$Router.push({
                name: 'mainLogin',
            })
        },
        toastPC() {
            uni.showToast({
                title: '正在平仓中...',
                icon: 'none',
                duration: 2000
            })
        },
        getcards_500() {
            let that = this;
            var timerLoss;

            if (that.hasFetchedPositions) {
                that.fetchPositions() //调用需要触发的函数
            }
            that.hasFetchedPositions = false;
            clearTimeout(timerLoss);
            timerLoss = setTimeout(() => { that.hasFetchedPositions = true }, 500);
        },

        onlossInputChange(event) {
            event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,3})/g)[0]) || ""
            this.$nextTick(() => {
                this.loss = event.detail.value
            })
        },
        onprofitInputChange(event) {
            event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,3})/g)[0]) || ""
            this.$nextTick(() => {
                this.profit = event.detail.value
            })
        },
        // 去支付
        async submitOrder() {
            uni.setStorageSync('isfromEx', 1)
            this.isSubmit = true;
            let res = await this.$api.java_create_item({
                paymentScene: 1,
                ctid: this.aimobj.otoAskCtid
            });
            setTimeout(() => {
                this.isSubmit = false;
            }, 10000)
            if (res.status.code == 0) {
                uni.showToast({
                    title: "下单成功~",
                    icon: "none",
                    duration: 3000,
                });
                this.isSubmit = false;
                console.log(res.result.orderNo)
                // #ifdef APP
                this.appUrl = getApp().globalData.urlZf

                let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
                console.log(url)
                this.$Router.push({
                    name: "webView",
                    params: {
                        url,
                    }
                })
                // #endif
                // #ifdef H5
                let { origin } = window.location
                window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
                // #endif
            } else if (res.status.code == 1002) {
                this.nav_login()
            } else {
                this.isSubmit = false;
                uni.showToast({
                    title: res.status.msg,
                    icon: "none",
                    duration: 3000,
                });
            }
        },
        goreals() {
            this.$Router.push({
                name: "realName"
            })
        },
        //复制
        copy(val) {
            uniCopy({
                content: val,
                success: (res) => {
                    uni.showToast({
                        title: res,
                        icon: "none",
                    });
                },
                error: (e) => {
                    uni.showToast({
                        title: e,
                        icon: "none",
                        duration: 3000,
                    });
                },
            });
        },
        formatNumber(value, decimalPlaces = 2) {
            if (value <= 0) {
                return 0.00
            }
            // 判断是否为整数
            if (Number.isInteger(value)) {
                return value.toString();
            }
            value = Number(value)
            // 如果是整数，则直接返回
            // 如果不是整数，则将其限制为指定的小数位数
            return value.toFixed(decimalPlaces);
        },
        /**
 * 最新价格
 */
        async getNewPrice() {
            let res = await this.$api.GettradePrcie()
            if (res.status.code == 0) {
                this.marketPrice = res.result.price
            }
        },
        dongWidth() {
            if (this.positiontotal < 100 && this.positiontotal >= 10) {
                return '270rpx'
            } else if (this.positiontotal > 100 && this.positiontotal < 1000) {
                return '280rpx'
            } else if (this.positiontotal < 10) {
                return '265rpx'
            } else if (this.positiontotal > 1000) {
                return '290rpx'
            }
        },
        dealprogress(item) {
            let chu = parseFloat(item.dealMoney / item.money) * 100
            let yu = chu.toFixed(2)
            return yu
        },
        isred(item) {
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str

            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            // this.$forceUpdate()
            return str >= 0 ? true : false


        },
        /**
         * 解锁止盈止损的条件
         */
        async fetchwhatgobuy() {
            const res = await this.$api.GetExchangeParam()
            if (res.status.code == 0) {
                this.aimobj = res.result
                this.$emit('getaimobj', res.result)
            }
        },
        /**
         * 去购买
         */
        goBuy() {
            if (this.aimobj.otoAskCtid) {
                this.submitOrder()
                // this.$Router.push({
                //     name: 'seriesList',
                //     params: {
                //         ctid: this.aimobj.otoAskCtid
                //     }
                // })
            }
        },
        /**
         * 检测是否有止盈止损功能
         */
        // async checkProLoss() {
        //     let res = this.$api.CreateOtoRecord()
        // },
        /**
         * 查询用户信息
         */
        async fetchUser() {
            let res = await this.$api.GetExchangeUserInfo()
            if (res.status.code == 0) {
                // if (this.entrustList.length > 0) {
                //     this.interval = setInterval(() => {
                //         this.tradedPageNum = 1
                //         this.fetchEntrust()
                //     }, 3000);
                // }

                this.EnableproLoss = res.result.enableOto
                this.$emit('getbalance', res.result.balance)
                // this.EnableproLoss = false
            }
        },
        async fetchActivity() {
            let res = await this.$api.inviteActivity({ activityNo: 'A81001000211587072' })
            console.log(res)
            if (res.status.code == 0) {
                this.nickname = res.result.nickname
                this.avatar = res.result.avatar
            }
            // 响应参数
            // startEndTime 活动时间
            // successNum 邀请成功的人数
            // button 按钮状态 1-去邀请 2-去领取 3-已领取
            // nickname 用户昵称
            // avatar 用户头像
            // inviteCode 邀请码
        },
        popupClose() {
            this.$emit('popupClose')
            console.log('gx');
        },
        cancelmodal() {
            console.log(123);
        },
        // 点击撤单
        backuporder(item) {
            console.log(item);
            this.waitorderNowobj = item

            if (item.experienceMoney > 0 && item.dealMoney > 0) {
                this.cancelOrder = true
            } else {
                this.backorder()
            }

        },
        // 重置止盈止损
        async resetProLoss(item) {
            const res = await this.$api.ResetOtoPosition({ id: item.id })
            if (res.status.code == 0) {
                uni.showToast({
                    title: '重置止盈止损成功',
                    icon: 'none',
                    duration: 3000
                })
                this.fetchPositions()
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
            // this.proLoss = false

        },
        // 点击止盈止损
        setProLoss(item) {
            this.profitLossobj = item
            this.proLoss = true
        },
        // 确认止盈止损
        async confirmProLoss() {
            if (!this.profit && !this.loss) {
                uni.showToast({
                    title: '请输入止盈止损价格',
                    icon: 'none',
                    duration: 3000
                })
                return
            }
            // if (this.profit == 0) {
            //     uni.showToast({
            //         title: '止盈价格不能为0',
            //         icon: 'none',
            //         duration: 3000
            //     })
            //     return
            // }
            // if (this.loss == 0) {
            //     uni.showToast({
            //         title: '止损价格不能为0',
            //         icon: 'none',
            //         duration: 3000
            //     })
            //     return
            // }




            // console.log(this.profitLossobj,'止盈止损');
            // return


            if (this.profitLossobj.side == 'BUY') {
                // 如果设置了止损价，且止损价大于市价
                if (this.loss && this.loss > this.marketPrice) {
                    uni.showToast({
                        title: '止损价需小于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.loss = ''
                    return;
                }

                // 如果设置了止盈价，且止盈价小于市价
                if (this.profit && this.profit < this.marketPrice) {
                    uni.showToast({
                        title: '止盈价需大于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.profit = ''
                    return;
                }
            }



            if (this.profitLossobj.side == 'SELL') {
                // 如果设置了止盈价，且止盈价大于市价
                if (this.profit && this.profit > this.marketPrice) {
                    uni.showToast({
                        title: '止盈价需小于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.profit = ''
                    return;
                }

                // 如果设置了止损价，且止损价小于市价
                if (this.loss && this.loss < this.marketPrice) {
                    uni.showToast({
                        title: '止损价需大于市价',
                        icon: 'none',
                        duration: 3000
                    });
                    this.loss = ''
                    return;
                }
            }


            let data = {
                id: this.profitLossobj.id,
                stopLossPrice: this.loss ? Number(this.loss) : '',
                takeProfitPrice: this.profit ? Number(this.profit) : ''
            }

            let res = await this.$api.OtoPosition(data)
            if (res.status.code == 0) {
                this.profitLossobj = {}
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
                this.proLoss = false
                this.loss = ''
                this.profit = ''
                this.fetchPositions()
                // this.$Router.push({
                //     name: 'position'
                // })
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }

        },
        // 撤单
        async backorder() {
            let res = await this.$api.RevokeOrders({ id: this.waitorderNowobj.id })
            this.cancelOrder = false

            if (res.status.code == 0) {
                uni.showToast({
                    title: '撤单成功',
                    icon: 'none',
                    duration: 3000
                })
                setTimeout(() => {
                    this.fetchUser()
                }, 2000);
                this.cancelOrder = false
                if (this.current == 0) {
                    this.tradedPageNum = 1
                    this.fetchEntrust()
                } else if (this.current == 1) {
                    this.pageNum = 1
                    this.fetchPositions()
                }


            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }

        },
        // 新用户领取体验金
        async getbonus() {
            let res = await this.$api.GetReward({ activityNo: "A81001000211587072", userType: 2 })
            this.$emit('Bogetbonus', false)

            if (res.status.code == 0) {
                this.newuserreward = false
                // this.popupShow = 0
                uni.showToast({
                    title: '领取成功',
                    icon: 'none',
                    duration: 3000
                })
            } else {
                this.newuserreward = false

                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
            //  请求参数 activityNo=A81001000211587072
            // userType 用户类型 1-邀请者 2-被邀请者

        },
        async checkpopups() {
            let res = await this.$api.CheckPopup({ activityNo: "A81001000211587072" })
            if (res.status.code == 0) {
                if (res.result.isActive == 1) {
                    this.popupShow = res.result.status
                    if (this.popupShow == 3) {
                        this.checkrealname = true
                    }

                    if (this.popupShow == 2) {
                        this.newuserreward = true
                    }

                    if (this.popupShow == 1) {
                        this.oldusershow = true
                    }
                } else {
                    // uni.showToast({
                    //     title:'活动已结束',
                    //     icon: 'none',
                    //     duration: 3000
                    // })
                }
            }
            //  响应参数
            // isActive 活动状态 1-活动中 0-不在活动中
            // status 0-不显示弹窗 1-邀请者可领取任务 2-被邀请者任务完成领取奖励
        },

        closeonce: antiShake._debounce(function (item) {
            this.cheorder(item)
        }, 1000),
        //平仓动作
        async cheorder(item) {
            let data = {
                id: item.id
            }
            let res = await this.$api.ClosePositions(data)
            if (res.status.code == 0) {
                // let result = await this.$api.GetPositonStatus({ positionId: item.positionId })
                // if(result.result.status == 0){

                // }
                // uni.showToast({
                //     title: res.status.msg,
                //     icon: 'none',
                //     duration: 3000
                // })
                // this.pageNum = 1

                uni.showToast({
                    title: '平仓委托提交成功',
                    icon: 'none',
                    duration: 1000
                })

                setTimeout(async () => {
                    let result = await this.$api.GetorderStatus({ id: res.result })
                    if (result.status.code != 0) {
                        uni.showToast({
                            title: '对手盘不足，平仓失败',
                            icon: 'none',
                            duration: 2000
                        })
                        return
                    } else {

                    }
                }, 5000);

                this.getOrderInfo(item.id)

                this.fetchPositions()

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        //我的仓位 订单查询
        async getOrderInfo(orderId) {
            let res = await this.$api.GetPositonStatus({
                positionId: orderId,
            });


            if (res.status.code == 0) {
                if (res.result.closeVolume == res.result.pendingCloseVolume) {
                    // 已撤单 1  成交完 0  下面处理
                    // if (setInterval) {
                    //     clearInterval(setInterval);
                    // }

                    this.fetchPositions()

                    if (res.result.status == 0) {
                        clearInterval(setInterval);
                        let index = this.positionsList.findIndex(item => item.id === orderId);
                        if (index !== -1) {
                            this.positionsList.splice(index, 1);
                        }

                        this.positiontotal = this.positiontotal - 1
                        if (this.positiontotal < 0) return this.positiontotal = 0
                        // this.tabList[1].name = `我的仓位(${this.positionsTotal - 1})`
                    }
                    // else {
                    //     let setInterval = setTimeout(() => {
                    //         this.getOrderInfo(orderId)
                    //     }, 1000)
                    // }

                    return
                } else {
                    // 平仓中
                    let setInterval = setTimeout(() => {
                        this.getOrderInfo(orderId)
                    }, 1000)
                }
                console.log(res)

            } else {
                this.Toast(res.status.msg);
            }
        },
        /**
    * 获取委托单
    */
        async fetchEntrust() {
            // this.isLinkLoadding = true
            // this.$api.GetDelegatingOrder() // 查询委托单 等待成交
            let res = await this.$api.GetDelegatingOrder({
                pageNum: this.tradedPageNum,
                pageSize: 10
            })
            setTimeout(() => {
                // this.isLinkLoadding = false
            }, 300);
            if (res.status.code == 0) {
                // this.tabList[1].name = '我的仓位' + ' (' + this.positionsList.length + ')'
                this.entrusthasnext = res.result.hasNext
                if (this.tradedPageNum != 1) {
                    this.entrustList = this.entrustList.concat(res.result.list)
                } else {
                    this.entrustList = res.result.list
                }
                this.tabList[0].name = '等待成交' + ' (' + res.result.totalCount + ')'

                // this.positiontotal = 0
                this.entrusttotal = res.result.totalCount
            }
            // else if (res.status.code == 1002) {
            //     this.isLinkLoadding = true
            //     setTimeout(() => {
            //         this.isLinkLoadding = false
            //         this.$Router.push({
            //             name: "mainLogin"
            //         })
            //     }, 1500);
            // }
        },
        /**
         * 获取我的仓位
         */
        async fetchPositions() {
            // this.isLinkLoadding = true

            // this.$api.GetCurPosition() // 查询我的仓位
            let res = await this.$api.GetCurPosition({
                pageNum: this.pageNum,
                pageSize: 10
            })
            if (res.status.code == 0) {
                // this.tabList[0].name = '等待成交' + ' (' + this.entrustList.length + ')'
                // setTimeout(() => {
                //     this.isLinkLoadding = false
                // }, 300);
                this.positionhasnext = res.result.hasNext
                if (this.pageNum != 1) {
                    this.positionsList = this.positionsList.concat(res.result.list)
                } else {
                    this.positionsList = res.result.list
                }
                this.positiontotal = res.result.totalCount
                this.tabList[1].name = '我的仓位' + ' (' + res.result.totalCount + ')'

            }
        },
        async getPositionInfo() {
            // this.$api.GetDelegatingOrder() // 查询委托单 等待成交
            let res = await this.$api.GetDelegatingOrder({
                pageNum: this.tradedPageNum,
                pageSize: 10
            })
            if (res.status.code == 0) {
                this.entrustList = res.result.list
            }


            // this.$api.GetCurPosition() // 查询我的仓位
            let res1 = await this.$api.GetCurPosition({
                pageNum: this.pageNum,
                pageSize: 10
            })
            if (res1.status.code == 0) {
                this.positionsList = res1.result.list
            }
            this.positiontotal = res1.result.totalCount
            // this.positiontotal = 0
            this.entrusttotal = res.result.totalCount
            this.tabList[0].name = '等待成交' + ' (' + res.result.totalCount + ')'
            this.tabList[1].name = '我的仓位' + ' (' + res1.result.totalCount + ')'

        },

        beforeEnter(el) {
            el.style.transform = 'scale(1)';
            el.style.opacity = 1;
            el.style.position = 'absolute';
            el.style.top = '0';
            el.style.right = '0';
        },
        enter(el, done) {
            el.offsetHeight; // trigger reflow
            el.style.transition = 'transform 0.5s ease, opacity 0.5s ease';
            el.style.transform = 'scale(0)';
            el.style.opacity = 0;
            done();
        },
        leave(el, done) {
            el.style.transition = 'transform 0.5s ease, opacity 0.5s ease';
            el.style.transform = 'scale(0)';
            el.style.opacity = 0;
            done();
        },
        gomission() {
            // this.show = !this.show;
            // console.log(this.show);
            // this.oldusershow = false
            this.$Router.push({
                name: 'mission'
            })

        },
        async getUserShare() {
            let res = await this.$api.getUserInfotake({

            });
            console.log(res, '321');
            if (res.status.code == 0) {
                this.shareUser = res.result
                const jumpUrl =
                    `${getApp().globalData.url}pagesA/project/personal/appDownload`;
                this.get_share(jumpUrl)
                console.log(this.shareUser)
            } else {
                // uni.showToast({
                //     title: res.status.msg,
                //     icon: 'none',
                //     duration: 3000
                // });
            }
        },
        async get_share(jumpUrl) {
            console.error(jumpUrl)
            let res = await this.$api.getShortLink({
                longLink: jumpUrl
            });
            if (res.status.code == 0) {
                this.qrcodeUrl = res.result.shortUrl;

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        getDate() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (`0${now.getMonth() + 1}`).slice(-2);
            const date = (`0${now.getDate()}`).slice(-2);
            // 设置格式化的当前日期与时间
            return `${year}年${month}月${date}日`;
        },
        historyTakeOff() {
            let token = uni.getStorageSync('token')
            if (!token) {
                this.$Router.push({
                    name: "mainLogin"
                })
                return
            } else {
                this.$Router.push({
                    name: "historyposition"
                })
            }

        },
        changeTab(e) {
            if (this.current == e) return
            uni.setStorageSync('positionTab', e)

            console.log(e);
            this.current = e
            this.isLinkLoadding = true
            setTimeout(() => {
                this.isLinkLoadding = false
            }, 300);
            if (e == 0) {
                this.tradedPageNum = 1
                this.entrustList = []
                this.fetchEntrust()
            } else {
                this.pageNum = 1
                this.positionsList = []
                this.fetchPositions()
            }

        },
        openPop(item, type, index) {
            this.indexTag = index
            this.orderId = item.orderId
            this.status = item.status
            this.typePop = type
            if (type == 1) {
                this.pop_msg = "确认撤单？"
            } else {
                this.pop_msg = "确认不唱了？"
            }
            this.isConfirmation = true
        },
        async getInfo() {
            // let res = await this.$api.getMarketPrice({
            // });
            // this.marketPrice = uni.getStorageSync('realPrice');
            // if (res.status.code == 0) {
            //     this.marketPrice = res.result.marketPrice
            // }
        },
        openPop(item, type, index) {
            this.$emit('openPop', item, type, index)
        },
        //计算战损/战利
        calculateEarnings(item) {

            // 收益 
            // buy =  (marketPrice - price ) * volume - dept
            // sell =  (price - marketPrice) * volume - dept
            let str;
            const {
                price,
                volume,
                side,
                lever,
                debt
            } = item
            if (side == 'BUY') {
                str = (this.marketPrice - price) * volume - debt
                item.income = str
            } else {
                str = (price - this.marketPrice) * volume - debt
                item.income = str
            }
            str = Math.floor(str * 100) / 100
            // console.log(str)
            if (str >= 0) {
                item.red = true
            } else {
                item.red = false
            }
            str = str.toFixed(2)

            // this.$forceUpdate()
            return str >= 0 ? ' + ' + Math.abs(str) : ' - ' + Math.abs(str)

        },
        //计算收益率
        calculateYield(item) {

            // 收益率
            // 开仓价 price * volume  / leverageLevel 
            // buy = buy的收益 / 开仓价
            // sell = sell 的收益 / 开仓价

            let str;
            const {
                price,
                side,
                volume,
                leverageLevel,
                income
            } = item

            // if (side == 'BUY') { //唱多
            //     let shouyiNum = (this.marketPrice - price) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // } else { //唱空
            //     let shouyiNum = (price - this.marketPrice) * volume //收益
            //     let baozhenjinNum = (price * volume) / leverageLevel //保证金
            //     str = shouyiNum / baozhenjinNum
            // }
            // if(side == 'BUY'){
            let open = price * volume / leverageLevel
            str = income / open
            // } else {

            // }

            str = Math.floor(str * 10000) / 100
            if (str >= 0) {
                item.red2 = true
            } else {
                item.red2 = false
            }
            str = str.toFixed(2)
            return str >= 0 ? `+${str}%` : `${str}%`
        },
        openShare(item) {
            // this.shareUser = {}
            let zNum = this.calculateEarnings(item);
            let gNum = this.calculateYield(item)
            console.log(item)
            this.isShare = true
            this.shareUser = {
                ...this.shareUser,
                money: item.money,
                zNum,
                gNum,
                red: item.red,
                price: item.price,
                lever: item.lever,
                currentDateTime: this.getDate()
            }
            console.log(this.shareUser)
        }
    }
}
</script>

<style lang="scss" scoped>
page {
    background: #111;
}

.nav_login {
			width: 300rpx;
			height: 90rpx;
			background: var(--main-bg-color)FFF;
			border-radius: 24rpx;
			border: 2rpx solid #63EAEE;
			color: #63EAEE;
			font-size: 28rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 40rpx;
			font-weight: 600;
		}
::v-deep .u-tab-bar {
    left: 18rpx !important;
    // transform: translate(141px, -100%) !important;
}

.newuser {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 640rpx;
    text-align: center;
    width: 600rpx;
    background: transparent;


    .card {

        height: 447rpx;
        padding: 0 56rpx;
        width: 100%;
        background: #34323D;
        border-radius: 30rpx;
        position: relative;

    }

    .tops {
        z-index: 1;
        position: absolute;
        top: -127rpx;
        left: 168rpx;

        width: 261rpx;
        height: 218rpx;

    }

    .info {
        margin-top: 115rpx;
        display: flex;
        align-items: center;
        justify-content: center;



        .titles {

            font-weight: bold;
            font-size: 34rpx;
            color: #63EAEE;
        }

        .money {
            background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
            border-radius: 30%;
            width: 80rpx;
            height: 80rpx;
            margin-left: 20rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            .grey {
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 30%;
                background: #34323D;
                width: 76rpx;
                height: 76rpx;
            }

            image {
                height: 70rpx;
                width: 70rpx;
                border-radius: 30%;
            }
        }

        .tips {
            margin-left: 20rpx;

            font-weight: bold;
            font-size: 28rpx;
            color: #FFFFFF;
        }
    }

    .msg {
        // width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        margin-top: 25rpx;

        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 38rpx;
        // width: 256rpx;



    }

    .btn {
        margin: 0 auto;
        margin-top: 36rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        line-height: 80rpx;

        color: #141414;
    }
}

.share_body {
    width: 100%;
    background-color: #111111;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240204/550f0630e49369143403c4f267d8a391_750x900.png);
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 160rpx 55rpx 55rpx;
    height: 100vh;

    .cart_div {
        background-color: #2B2B2B;
        border-radius: 36rpx;
        width: 640rpx;
        height: 940rpx;
        padding: 40rpx 36rpx;
        position: relative;

        .buysell {
            margin-top: 30rpx;
            display: flex;
            align-items: center;

            image {
                width: 40rpx;
                height: 40rpx
            }

            text {
                margin-left: 12rpx;
                font-weight: bold;
                font-size: 28rpx;
                color: #FFFFFF;
            }
        }

        .title_image {

            font-family: DOUYUFont, DOUYUFont;
            font-weight: 400;
            font-size: 40rpx;
            color: #FFFFFF;

            background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240708/d98dd71d9800bf2af6a6e86da1322c8a_568x112.png);
            width: 100%;
            height: 111rpx;
            background-size: 100% 100%;
            line-height: 111rpx;
            text-align: center;
            letter-spacing: 2rpx;
        }

        .toux_image_div {
            width: 259rpx;
            margin: 20rpx auto;

            .toux_border {
                width: 100%;
                // height:116rpx;
                background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240221/bd0c5d99d72bbc127e778ca315ebdeab_260x110.png);
                background-size: 100%;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                padding-top: 40rpx;

                >text {
                    margin-top: 10rpx;
                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }

                .image_div {

                    width: 120rpx;
                    height: 120rpx;
                    background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
                    border-radius: 40rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .gray {
                        width: 116rpx;
                        height: 116rpx;
                        border-radius: 40rpx;
                        background: #2B2B2B;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    image {
                        border-radius: 30rpx;
                        width: 104rpx;
                        height: 104rpx;
                        object-fit: cover;
                    }
                }
            }
        }

        .toux_name {
            text-align: center;
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .yield {
            font-size: 90rpx;
            color: #6CFF8A;
            font-weight: 600;
            margin-top: 40rpx;

            text {
                font-size: 55rpx;
            }

            &.red {
                color: #FF5270;
            }
        }

        .shouyi {
            font-size: 48rpx;
            color: #6CFF8A;

            &.red {
                color: #FF5270;
            }
        }

        .info_div {
            color: #fff;
            margin-top: 30rpx;

            p {
                font-size: 28rpx;
                line-height: 38rpx;
            }
        }

        .yqm {
            background-color: rgb(255, 255, 255, 0.2);
            border: 1px solid #fff;
            width: 280rpx;
            height: 54rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 40rpx;
            border-radius: 36rpx;
            margin-bottom: 30rpx;
        }

        .msg_text {
            font-size: 28rpx;
            color: #fff;
            font-weight: 600;
        }

        .icon_bg {
            width: 292rpx;
            position: absolute;
            top: 450rpx;
            right: -30rpx;

            image {
                width: 292rpx;
            }
        }

        .qr_code {
            position: absolute;
            top: 724rpx;
            right: 36rpx;

            .right {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                .qr_div {
                    width: 140rpx;
                    height: 140rpx;
                    border-radius: 28rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #fff;
                    padding: 10rpx;
                }
            }

            .time {
                color: rgb(255, 255, 255, 0.3);
                margin-top: 30rpx;
                font-size: 22rpx;
            }
        }
    }

    .share_to_div {
        margin-top: 140rpx;
        display: flex;
        justify-content: center;

        >.li {
            width: 25%;
            text-align: center;
            color: #fff;
            font-size: 28rpx;
            margin-top: 10rpx;

            .icon_image {
                display: flex;
                justify-content: center;

                image {
                    width: 90rpx;
                    margin-bottom: 20rpx;
                }
            }
        }
    }

    .colse_div {
        margin-top: 46rpx;
        display: flex;
        justify-content: center;

        image {
            width: 80rpx;
        }
    }
}

.nomoney {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 300rpx;

    text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .btn {
        margin-top: 61rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;
    }
}

// 撤单
.newmoneys {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    // height: 367rpx;
    text-align: center;
    padding: 70rpx 60rpx 40rpx 60rpx;

    >text {
        font-weight: bold;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 38rpx;
    }

    .btnreal {
        width: 100%;
        margin-top: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .goreal {
            width: 300rpx;
            line-height: 80rpx;
            height: 80rpx;
            background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
            border-radius: 40rpx;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            font-size: 24rpx;
            color: #141414;
        }
    }

    >.btns {
        width: 100%;
        margin-top: 44rpx;
        display: flex;
        justify-content: space-between;

        >view {
            text-align: center;
            line-height: 70rpx;
            width: 220rpx;
            height: 70rpx;

            &:nth-of-type(1) {


                background: rgba(255, 255, 255, 0.2);
                border-radius: 50rpx;
                border: 1rpx solid #FFFFFF;
                font-weight: bold;
                font-size: 24rpx;
                color: #FFFFFF;
            }

            &:nth-of-type(2) {
                font-weight: bold;
                font-size: 24rpx;
                color: #141414;
                background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                border-radius: 40rpx
            }
        }


    }
}

// 缩放
.scale {
    width: 0;
    height: 0;

    transition: 3.5s;
    transform: scale(0.1, 0.1);
    transform: translate(450px, -600px);
    -webkit-transform: translate(450px, -600px);
    -moz-transform: translate(450px, -600px);
}

.rightscale {
    width: 0;
    height: 0;
    transition: 3.5s;
    transform: scale(0.1, 0.1);
    transform: translate(100px, 200px);
    -webkit-transform: translate(100px, 200px);
    -moz-transform: translate(100px, 200px);
}

::v-deep .u-mode-center-box {
    // transition: 5.5s !important;
    // transform: scale(0.5, 0.5);
    // transform: translate(0, -0) !important;
    // -webkit-transform: translate(450px, -600px);
    // -moz-transform: translate(450px, -600px);
}

.newmoney {
    border-radius: 36rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    height: 367rpx;
    text-align: center;
    background: #34323D;
    width: 600rpx;

    text {
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;

        &:nth-of-type(1) {
            margin-top: 50rpx;
            font-weight: bold;
            font-size: 34rpx;
            color: #63EAEE;
        }

        &:nth-of-type(2) {
            // margin-top: 30rpx;

        }
    }

    view {
        // margin-top: 61rpx;
        margin-bottom: 35rpx;
        width: 300rpx;
        height: 80rpx;
        background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
        border-radius: 40rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: bold;
        font-size: 24rpx;
        color: #141414;

    }
}

.modalpro {
    // height: 586rpx;
    background: #2B2B2B;
    border-radius: 36rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 30rpx 20rpx 20rpx;

    .m_head {
        width: 100%;
        display: flex;
        align-items: center;
        // flex-direction: row;
        justify-content: space-between;

        .left {
            display: flex;

            image {
                width: 50rpx;
                height: 50rpx;
            }

            >view {
                display: flex;
                flex-direction: column;
                // align-items: center;
                margin-left: 20rpx;

                .price {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #a6a6a6;
                }

                .num {
                    margin-top: 10rpx;

                    font-weight: bold;
                    font-size: 28rpx;
                    color: #FFFFFF;

                    .num2 {
                        margin-left: 10rpx;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #EC4068;
                    }
                }
            }
        }

        .right {
            margin-top: 10rpx;
            font-weight: bold;
            font-size: 30rpx;
            color: #FFFFFF;
        }
    }

    .m_line {
        margin: 31rpx 0 39rpx 0;
        width: 100%;
        height: 1rpx;
        background: #414141;
    }

    .m_openclose {
        .top {
            text {
                display: flex;
                align-items: center;

                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                }

                &:nth-of-type(2) {
                    margin-left: 50rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #FFFFFF;
                }
            }
        }

        >view {
            margin-bottom: 26rpx;
            display: flex;
            align-items: center;
            position: relative;

            &:last-child {
                margin-bottom: 0;
            }

            display: flex;

            text {
                &:nth-of-type(1) {
                    font-weight: 400;
                    font-size: 22rpx;

                    color: #FFFFFF;
                }


            }

            .rb {
                z-index: 1;
                position: absolute;
                right: 200rpx;
                top: 12rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #FFFFFF;

            }

            input {
                text-indent: 40rpx;
                margin-left: 36rpx;
                width: 237rpx;
                height: 51rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 18rpx;
                border: 1rpx solid #FFFFFF;

                font-weight: 400;
                font-size: 22rpx;
                color: #FFFFFF;
            }
        }
    }

    .m_btn {
        margin-top: 30rpx;
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;

        >view {
            width: 200rpx;
            height: 60rpx;
            background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
            border-radius: 50rpx;
            font-weight: bold;
            text-align: center;
            line-height: 60rpx;
            font-size: 26rpx;
            color: #141816;

            &:nth-of-type(1) {
                background: rgba(255, 255, 255, 0.2);
                border: 1rpx solid #FFFFFF;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                color: #FFFFFF;
            }
        }
    }
}

::v-deep .u-tab-bar {
    // margin-left: 20rpx;
}

.tabs_div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0rpx 55rpx 0 20rpx;
    // margin-top: 10rpx;

    .left {
        // margin-left: -50rpx;
        // width: 460rpx;
    }

    .right {
        image {
            width: 36rpx;
            margin-top: 14rpx;
        }
    }
}

.details {
    // padding: 20rpx;
    // margin-top: 20rpx;
    margin: 0 20rpx;

    .weituo {
        min-height: 600rpx;


        .li {
            // padding: 30rpx;
            padding: 16rpx 30rpx 16rpx 30rpx;
            background-color: #2B2B2B;
            border-radius: 36rpx;
            margin-bottom: 20rpx;
            position: relative;
            opacity: 1;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 34rpx;
                top: 22rpx;

                image {
                    width: 20rpx;
                    height: 22.5rpx
                }
            }

            .live_data {
                background-color: #141816;
                border-radius: 40rpx;
                height: 80rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 20rpx;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;
                // width: 500rpx;
                // color: #6CFF8A;

                text {
                    color: #fff;
                }

                .sy {
                    margin-right: 50rpx;
                    font-weight: 600;

                    text {
                        &:nth-of-type(1) {
                            font-weight: bold;
                            color: #fff;

                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-size: 28rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: bold;
                            font-size: 28rpx;
                            // color: #EC4068;
                        }
                    }

                    // .red {
                    //     // color: #EC4068;
                    // }

                    // .green {
                    //     // color: #6CFF8A;
                    // }
                }

                .syl {
                    text {
                        &:nth-of-type(1) {
                            font-weight: bold;
                            font-size: 30rpx;
                            color: #fff;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-weight: bold;
                            font-size: 30rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    image {
                        width: 50rpx;
                        margin-right: 20rpx;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        margin-right: 20rpx;
                    }

                    .sub {
                        font-size: 24rpx;
                        color: rgb(255, 255, 255, 0.3);
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 35rpx;

                        image {
                            width: 18rpx;
                            height: 24rpx;
                        }
                    }

                    .progress {
                        margin-right: 3px;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 6rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {

                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: row;
                        justify-content: center;

                        align-items: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }


            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 20rpx 0 0 0;

                .li_view {
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 21rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .close {
                margin-top: 20rpx;
                display: flex;
                justify-content: space-between;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none
                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 300rpx;
                    height: 60rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                }
            }
        }
    }

    .cangwei {
        min-height: 600rpx;


        .li {
            // padding: 30rpx;
            padding: 10rpx 30rpx 14rpx 30rpx;
            background-color: #2B2B2B;
            border-radius: 36rpx;
            margin-bottom: 20rpx;
            position: relative;
            opacity: 1;
            transition: opacity 1s ease-out;

            &.active {
                opacity: 0;
            }

            .right_fenx {
                position: absolute;
                right: 34rpx;
                top: 22rpx;

                image {
                    width: 20rpx;
                    height: 22.5rpx
                }
            }

            .live_data {
                margin: 0 34rpx 0 26rpx;
                width: 450rpx;
                height: 50rpx;
                background: #141816;
                border-radius: 40rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                // padding: 0rpx 78rpx;
                font-size: 24rpx;
                // width: 500rpx;
                // color: #6CFF8A;

                text {
                    color: #fff;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: bold;
                    font-size: 24rpx;
                }

                .sy {
                    // margin-right: 56rpx;
                    font-weight: 600;

                    text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 4rpx;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                            // color: #EC4068;
                        }
                    }

                    // .red {
                    //     // color: #EC4068;
                    // }

                    // .green {
                    //     // color: #6CFF8A;
                    // }
                }

                .syl {
                    margin-left: 20rpx;

                    text {
                        &:nth-of-type(1) {
                            color: #fff;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                        }

                        &:nth-of-type(2) {
                            margin-left: 10rpx;
                            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                            font-weight: bold;
                            font-size: 22rpx;
                            // color: #EC4068;
                        }
                    }

                    // &.red {
                    //     // color: #EC4068;
                    // }
                }
            }

            >.heads {
                display: flex;
                // justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    image {
                        width: 36rpx;
                        margin-right: 14rpx;
                    }

                    >.level {
                        color: #fff;
                        font-size: 28rpx;
                        font-weight: 600;
                        // margin-right: 20rpx;
                    }

                    .sub {
                        font-size: 24rpx;
                        color: rgb(255, 255, 255, 0.3);
                    }
                }

                .cd {
                    display: flex;
                    align-items: center;

                    .cancel {
                        width: 80rpx;
                        height: 44rpx;
                        font-size: 24rpx;
                        color: #fff;
                        background-color: rgb(255, 255, 255, 0.3);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 36rpx;
                        margin-left: 35rpx;

                    }

                    .progress {
                        margin-right: 3px;
                        display: flex;
                        // flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        >text {
                            // margin-bottom: 8rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #FFFFFF;
                        }

                        .bg {
                            margin-left: 12rpx;
                            width: 80rpx;
                            height: 6rpx;
                            background: #6B6B6B;
                            border-radius: 36rpx;

                            .bar {

                                background: #63EAEE;
                                border-radius: 36rpx;
                                height: 6rpx;
                            }
                        }
                    }

                }
            }

            .data_view {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 18rpx 0 0 0;

                .li_view {
                    text-align: center;

                    // width: 33.33%;
                    // display: flex;
                    // justify-content: center;
                    // flex
                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #a6a6a6;
                    }

                    .num {
                        margin-top: 10rpx;
                        line-height: 32rpx;
                        display: flex;
                        flex-direction: column;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                // margin-top: 10rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }


                    }
                }
            }


            .data_views {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                margin: 12rpx 0 0 0;

                .li_view {
                    text-align: center;
                    // width: 33.33%;

                    &.button {
                        width: 144rpx;
                        height: 48rpx;
                        border-radius: 36rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 22rpx;
                        background-color: #fff;
                    }

                    .label {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #FFFFFF;
                        opacity: .5;
                        line-height: 29rpx;
                    }

                    .num {
                        margin-top: 4rpx;
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #FFFFFF;

                        line-height: 32rpx;
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;


                        >text {
                            &:nth-of-type(1) {
                                font-weight: 400;
                                font-size: 24rpx;
                                color: #FFFFFF;
                            }

                            &:nth-of-type(2) {
                                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

                                margin-top: 4rpx;
                                color: #EC4068;
                                font-weight: 400;
                                font-size: 20rpx;
                            }
                        }
                    }
                }
            }

            .stoploss {
                margin-top: 16rpx;
                height: 50rpx;
                background: #404040;
                border-radius: 8rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 20rpx;

                .left {
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #FFFFFF;
                    opacity: .5;
                }

                .right {
                    display: flex;
                    align-items: center;

                    text {
                        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;

                        &:nth-of-type(1) {
                            color: #EC4068;
                        }

                        &:nth-of-type(2) {
                            color: #6CFF8A;

                        }

                    }

                    image {
                        width: 19.8rpx;
                        height: 19.8rpx;
                    }
                }
            }

            .close {
                // margin-top: 20rpx;
                display: flex;
                justify-content: space-between;

                .unlock {
                    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #141816;
                    border: none;
                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                }

                .oneclose {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 280rpx;
                    height: 50rpx;
                    border-radius: 14rpx;
                    font-weight: 400;
                    color: #2B2B2B;

                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                    background: #FFFFFF;
                    border-radius: 12rpx;
                }

                >view {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 280rpx;
                    height: 50rpx;
                    border-radius: 14rpx;
                    border: 1rpx solid #FFFFFF;
                    font-weight: 400;
                    color: #FFFFFF;

                    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                    font-size: 20rpx;

                    background: #2B2B2B;
                    border-radius: 12rpx;
                }
            }
        }
    }
}

.nodata {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    image {
        width: 240rpx;
        height: 240rpx;
    }

    text {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
    }
}

.red {
    color: #EC4068;
}

.green {
    color: #6CFF8A;
}

.utab {
    display: flex;
    font-weight: 400;
    font-size: 28rpx;
    color: #888888;
    position: relative;
    margin: 30rpx 0;

    view {
        &:nth-of-type(1) {
            margin: 0 51rpx 0 28rpx;
        }
    }
}

.utabact {
    font-weight: bold;
    color: #FFFFFF;
}

.utabbar {
    width: 80rpx;
    height: 8rpx;
    background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
    position: absolute;
    bottom: -18rpx;
    // left: 67rpx;
    transition: all 0.5s ease-in-out;
}

.turnLeft {
    // left: 305rpx;

}

.rotate-image {
    animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(180deg);
    }
}
</style>