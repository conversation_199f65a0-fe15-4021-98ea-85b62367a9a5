<template>
	<view class="centent">
		<view class="barHeight"></view>
		<view class="header">
			<view class="back" @click="nav_back()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240914/779998841e6181594c2650ceac009ea8_50x50.png"
					mode="widthFix"></image>
			</view>
			<view class="title">
				<view class="">
					<text>{{ info.title }}</text>
					<view>{{ info.titleCn }}</view>
				</view>
				<image @click="isModel=true"
					src="https://cdn-lingjing.nftcn.com.cn/image/20241014/5c080f497c893af2956fb0c36f65850a_32x32.png"
					mode="widthFix"></image>
			</view>
			<view class="icon" v-if="info.isSelect== 0" @click="checkUserSelect(1)">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240918/8adc16433f851aad458fa16cbb7d08dd_64x64.png"
					mode="widthFix"></image>
				自选
			</view>
			<view class="icon huise" v-if="info.isSelect== 1" @click="checkUserSelect(0)">
				已自选
			</view>
		</view>
		<view class="padding_30 tabs_view" v-if="typeIndex==0">
			<view class="tabs">
				<view class="li" :class="{ 'active': index == current }" v-for="(item, index) in timeList" :key="index"
					@click="check(item, index)">
					{{ item.name }}
				</view>
			</view>
		</view>
		<view class="padding_30 hold_view">
			<view class="left_hold">
				<view class="icon"></view>
				<view class="">我持有</view>
				<text>{{ info.userGoodsCount }}份</text>
			</view>
			<view class="right_tabs">
				<view class="bg_view">
					<view class="li " :class="{'active':index == typeIndex}" v-for="(item,index) in typeTabs"
						@click="checkTypeTabs(item,index)">{{item.name}}</view>
				</view>
			</view>
		</view>
		<view class="echarts_view">
			<KForm ref="child" :message="clickData" :period="period" :selectTopHeight='selectTopHeight'
				:selectBottomHeight='selectBottomHeight' :statusBarHeight="statusBarHeight" v-if="isStartForm">
			</KForm>
		</view>
		<view class="list_cart_view">
			<view class="head_view">
				<view class="left">
					最近成交 <text>￥{{ info.lastTradePrice?info.lastTradePrice:'-' }}</text>
				</view>
				<image @click="nav_lately()"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240919/c69c2283d7f4f666eac2fdd2a7bcb025_36x32.png"
					mode="widthFix"></image>
			</view>
			<!-- <view class="screen">
				<u-checkbox-group>
					<u-checkbox @change="checkboxChange" v-model="item.checked" v-for="(item, index) in checkList"
						:key="index" active-color="#35ECF2" :name="item.name">{{ item.name }}</u-checkbox>
				</u-checkbox-group>
			</view> -->
			<view class="goods_ul">
				<view class="loading_list" v-show="isLoadingStatus == 0 && !show_login">
					<view>
						<view class="flex">
							<view class="balls"></view>
						</view>
						<view class="text">
							玩命加载中...
						</view>
					</view>
				</view>
				<view class="li" v-for="(item, index) in seriesList" v-show="seriesList != '' && isLoadingStatus == 1">
					<view class="left_img">
						<image :src="`${item.cover.src}?x-oss-process=image/resize,m_lfit,h_100,w_100`"
							mode="aspectFill"></image>
					</view>
					<view class="right_font">
						<view class="font">
							<view class="title oneOver">
								{{ item.title }}
								<view class="tag" v-show="item.lockOrderStatus == 1">
									支付中
								</view>
							</view>
							<view class="tid">
								Token ID: {{ item.tid }}
							</view>
							<view class="price">
								￥{{ item.price }}
							</view>
						</view>
						<!-- <view class="arrow">
							<image src="../../../static/imgs/public/arrow-right.png" mode=""></image>
						</view> -->
					</view>
				</view>

				<view class="null_body" v-if="seriesList == '' && !show_login && isLoadingStatus == 2">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							暂无数据
						</view>
					</view>
				</view>
				<view class="null_body" v-if="show_login">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							您还未登录
						</view>
						<view class="nav_login" @tap="nav_login">
							登录/注册
						</view>
					</view>
				</view>
			</view>
			<view class="footer_ask_buy_sell" v-if="info.userGoodsCount>0">
				<view class="but_left">
					<view class="active" @click="openSell('fast')">
						快捷卖
					</view>
					<view class="border" @click="openSell('limited')">
						限价卖
					</view>
				</view>
				<view class="but_right">
					<view class="active" @click="openBuy('fast')">
						快捷买
					</view>
					<view class="border" @click="openBuy('limited')">
						限价买
					</view>
				</view>
			</view>
			<view class="footer_ask_buy" v-else>
				<view class="but_left" @click="openBuy('fast')">快捷买入</view>
				<view class="but" @click="openBuy('limited')">限价买入</view>
			</view>

		</view>

		<u-modal v-model="isAgreement" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false" :mask-close-able="true">
			<view class="agreement-modal-content">
				<view class="body">
					<view class="title"  @click="isAgreementIcon = !isAgreementIcon">
						<u-image class="icon" mode="widthFix" width="28rpx"
							:src="`../../../static/login/${isAgreementIcon?'jxs2x':'jx'}.png`">
						</u-image>
						我已阅读并同意以下协议
					</view>
					<view>
						<view class="text" @click="nav_link('模拟美股服务协议协议',1)">《模拟美股服务协议协议》</view>
						<view class="text" @click="nav_link('免责声明协议',1)">《免责声明协议》</view>
					</view>
				</view>
				<view class="button">
					<view class="but" @click="submitAgreement()">同意</view>
				</view>
			</view>
		</u-modal>

		<u-modal v-model="isFastSellBuy" width="710" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="fast-modal-content">
				<view class="right_close" @click="isFastSellBuy=false,clearTimerPrice()">
					<image
						src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240920/01a0a376de633fba5c470a604f187804_80x80.png"
						mode="widthFix"></image>
				</view>
				<view class="body">
					<view class="head">
						快捷{{isBuy?'买入':'卖出'}}
					</view>
					<view class="cententPop">
						<view class="item_msg">
							<view class="label">
								当前{{isBuy?'买入':'卖出'}}价
							</view>
							<text>￥{{referenceInfo.price?referenceInfo.price:'-'}}</text> <text
								class="huise">（参考）</text>
						</view>
						<view class="item_input">
							<view class="label">
								{{isBuy?'买入':'卖出'}}份数
							</view>
							<view class="input">
								<input type="number" v-model="quantity" @input="onInputChange" />
								<text @click="allNum()">全部</text>
							</view>
							<view class="error_msg" v-if="isBuy">
								当前余额可购买 <text>{{referenceInfo.maxNum}}</text>份（参考）
							</view>
						</view>
						<view class="button" @click="submitFast()">
							立即{{isBuy?'买入':'卖出'}}
						</view>
					</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isLimitedSellBuy" width="710" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="fast-modal-content">
				<view class="right_close" @click="isLimitedSellBuy=false,clearTimerPrice()">
					<image
						src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240920/01a0a376de633fba5c470a604f187804_80x80.png"
						mode="widthFix"></image>
				</view>
				<view class="body">
					<view class="head">
						限价{{isBuy?'买入':'卖出'}}
					</view>
					<view class="cententPop">
						<view class="item_input">
							<view class="label">
								{{isBuy?'买入':'卖出'}}价
							</view>
							<view class="input">
								<text class="rmb">￥</text>
								<input type="text" v-model="limitedPrice" @input="onInputChangePrice" />
							</view>
						</view>
						<view class="item_input">
							<view class="label">
								{{isBuy?'买入':'卖出'}}份数
							</view>
							<view class="input">
								<input type="number" v-model="limitedQuantity" @input="onInputChange" />
								<text v-if="!isBuy" @click="allNumLimited()">全部</text>
							</view>
						</view>
						<view class="button" @click="submitLimited()">
							立即{{isBuy?'买入':'卖出'}}
						</view>
					</view>
				</view>
			</view>
		</u-modal>
		<pay-popup :popup-show.sync="isPasswordImport" :title="passwordTitle" :mode="mode" :message="passwordMsg" order-type="O"
			@pay="finishPay"  @createSuccess="createSuccess" />
		<u-modal class="model" width="600" v-model="isModel" :show-title="true" :show-confirm-button="false"
			border-radius="24" title="" :title-style="titleObject" :mask-close-able="true">
			<view class="colse" @click="isModel=false">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="title_bg">
				<view class="icon"></view>
				系列简介
			</view>
			<view class="introduce">
				<view style="margin-bottom:50rpx;line-height:44rpx;" v-text="info.content"></view>
				<view class="num">当前铸造份数：<text>{{info.createNum}}份</text></view>
				<view class="num">当前流通份数：<text>{{info.activeNum}}份</text></view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<view class="loading_list" style="height:240rpx;" >
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isPassword" border-radius="30" :show-title="false" :show-confirm-button="false">
					<view class="new-modal-content">
						<view class="title_bg2">
							<view class="icon"></view>
							温馨提示
						</view>
						<view class="modal-content">
							<p>请先设置支付密码</p>
							非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
						</view>
						<view class="showModal-btn">
							<view class="img_cancel" @click="isPassword = false">取消</view>
							<view class="img_reasale" @click="SetPayPassword()">去设置</view>
						</view>
					</view>
				</u-modal>
	</view>
</template>

<script>
	import payPopup from "@/components/payPopup/index.vue";
	import introducePop from '@/components/public/introducePop.vue'
	// #ifdef H5
	const form = (resolve) => require(["./components/form2/index"], resolve);
	// #endif

	// #ifdef APP-PLUS
	import form from "./components/form2/index";
	// #endif
	export default {
		data() {
			return {
				timeList: [{
					name: "1M",
					value: 20001
				}, {
					name: "5M",
					value: 5
				}, {
					name: "30M",
					value: 7
				}, {
					name: "1H",
					value: 8
				}, {
					name: "1D",
					value: 12
				}, {
					name: "7D",
					value: 6
				}],
				current: 0,
				checkList: [{
					name: '仅看可购买',
					checked: false,
					value: 1
				}],
				isLoadingStatus: 0,
				pageNum: 1,
				seriesList: [],
				ctid: "",
				show_login: false,
				bgObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600'
				},
				isAgreement: false,
				isFastSellBuy: false, //快捷买卖弹窗
				isLimitedSellBuy: false, //限价买卖弹窗
				isBuy: true, //识别是否为买卖
				info: "", //头部信息
				referenceInfo: "", //参考价信息
				quantity: "", //快捷买卖 份数
				limitedPrice: "", //限价买卖 价格
				limitedQuantity: "", //限价买卖 份数
				isPasswordImport: false,
				passwordTitle: "",
				passwordMsg: "",
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				isModel: false,

				//以下是k线的字段
				clickData: '',
				period: 1, // 传过去的周期
				isStartForm: false, //用它来开启from表格组件是否开始渲染
				selectTopHeight: 0,
				selectBottomHeight: 0,
				statusBarHeight: 0,
				typeTabs: [{
						name: 'K线图',
						value: 20001,
					},
					{
						name: '趋势图',
						value: 'area'
					}
				],
				typeIndex: 0,
				refreshInterval: null,
				isLoadding:false,
				isPassword:false,
				mode:'pay',
				isAgreementIcon:false
			}
		},
		onLoad(options) {
			this.ctid = options.ctid
			uni.setStorageSync('klineTitle', options.title)
			this.seriesCollectionList()
			this.getSelectheight()
		},
		onShow() {
			if (!this.refreshInterval) {
				this.getInfo()
				this.startTimer();
			}
		},
		onHide() {
			console.log('页面销毁定时器')
			this.clearTimer();
		},
		onUnload (){
			console.log('页面销毁定时器')
			this.clearTimer();
			uni.removeStorageSync('klineTitle')
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.getInfo()
				this.pageNum = 1
				this.seriesList = []
				this.seriesCollectionList()
				this.clearTimer();
				this.startTimer();
				if(this.typeIndex == 0){
					this.$refs.child.createForm()
				}else{
					this.$refs.child.checkKLineArea()
				}
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 500);
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.seriesCollectionList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		components: {
			payPopup,
			introducePop,
			KForm: form
		},
		methods: {
			//头部时间选择
			check(item, index) {
				this.current = index
				console.log(item, 'k线周期item');
				this.period = item.value
			},
			// 选中某个复选框时，由checkbox时触发
			// 是否为仅显示可购买
			checkboxChange(e) {
				console.log(e.value);
				this.onlyCanBuy = e.value ? 1 : 0
				this.pageNum = 1
				this.seriesList = []
				this.seriesCollectionList()
			},
			//头部系列信息
			async getInfo() {
				let res = await this.$api.scaleSeriesInfo({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					this.info = res.result
					this.startTimer()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//作品列表
			async seriesCollectionList() {
				this.isRequest = true
				const {
					status,
					result
				} = await this.$api.java_marketRecommendGoodsList({
					ctid: this.ctid,
					pageNum: this.pageNum,
					pageSize: 10,
					onlyCanBuy: this.onlyCanBuy
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if (this.seriesList == "") {
							this.isLoadingStatus = 2
						}
					} else {
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
					}
				} else if (status.code == 1002) {
					this.show_login = true
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//弹窗卖出
			openSell(type) {
				let isAgreement = uni.getStorageSync('isAgreementShow')
				if(!isAgreement){
					this.isAgreement = true
					return 
				}
				if(uni.getStorageSync('isSetTradePassword') == 0){
					this.isPassword=true
					return 
				}
				this.isBuy = false
				if (type == 'fast') {
					this.isFastSellBuy = true
					this.quantity = ""
					this.getSellPrice()
					this.startTimerPrice()
				} else {
					this.limitedPrice = ""
					this.limitedQuantity = ""
					this.isLimitedSellBuy = true
				}
			},
			//弹窗买入
			openBuy(type) {
				let isAgreement = uni.getStorageSync('isAgreementShow')
				if(!isAgreement){
					this.isAgreement = true
					return 
				}
				if(uni.getStorageSync('isSetTradePassword') == 0){
					this.isPassword=true
					return 
				}
				this.isBuy = true
				if (type == 'fast') {
					this.isFastSellBuy = true
					this.quantity = ""
					this.getBuyPrice()
					this.startTimerPrice()
				} else {
					this.limitedPrice = ""
					this.limitedQuantity = ""
					this.isLimitedSellBuy = true
				}
			},
			submitAgreement(){
				if(!this.isAgreementIcon){
					uni.showToast({
						title: '请先勾选协议',
						icon: 'none',
						duration: 3000
					});
					return
				}
				this.isAgreement = false
				uni.setStorageSync("isAgreementShow",true)
			},
			nav_back() {
				this.$Router.back()
				this.clearTimer()
			},
			//查看历史列表
			nav_lately() {
				this.$Router.push({
					name: "mgUsList",
					params: {
						ctid: this.ctid,
						title:this.info.title
					}
				})
			},
			//自选/取消自选
			async checkUserSelect(isSelect) {
				let res = await this.$api.scaleUserSelect({
					ctid: this.ctid,
					isSelect
				});
				if (res.status.code == 0) {
					this.info.isSelect = isSelect
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//快捷 买入参考价 参考份数
			async getBuyPrice() {
				let res = await this.$api.scaleBuyPrice({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					this.referenceInfo = res.result
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//快捷 买入参考价 参考份数
			async getSellPrice() {
				let res = await this.$api.scaleSellPrice({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					this.referenceInfo = res.result
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			allNum() {
				if (this.isBuy) {
					this.quantity = this.referenceInfo.maxNum
				} else {
					this.quantity = this.info.userGoodsCount
				}
			},
			allNumLimited() {
				this.limitedQuantity = this.info.userGoodsCount
			},
			// 快捷买卖提交
			submitFast() {
				this.isFastSellBuy = false
				this.clearTimerPrice()
				if (this.isBuy) {
					//买
					this.submitBatch()
				} else {
					//卖
					this.submitTargetConfig()
				}
			},
			// 限价买卖提交
			submitLimited() {
				this.isLimitedSellBuy = false
				this.clearTimerPrice()
				if (this.isBuy) {
					//买
					this.limitedBuyConfig()
				} else {
					//卖
					this.limitedSellConfig()
				}
			},
			finishPay(e) {
				this.passwordImportPay = e;
				this.isPasswordImport = false;
				switch (this.passType) {
					case 1:
						//限价买入
						this.batchCreateItem(e)
						break
					case 2:
						this.submitTargetCreate(e)
						//限价买入
						break
					case 3:
						//快捷卖出
						this.submitTargetSell(e)
						break
					case 4:
						//限价卖入
						this.submitBatchOnSale(e)
						break
					default:
						console.log('未知')
				}
			},
			//快捷买入
			submitBatch() {
				// passType 1、快捷买入2、限价买入3、快捷卖出 4、限价买入
				if (this.quantity == 0) {
					uni.showToast({
						title: '需要买入的份数不能位0',
						icon: 'none',
						duration: 3000
					});
					return
				}
				if (this.quantity > 100) {
					uni.showToast({
						title: '需要买入的份数不能大于100',
						icon: 'none',
						duration: 3000
					});
					this.quantity = 1
					return
				}
				this.mode = "pay"
				this.passType = 1
				this.isPasswordImport = true
				this.passwordTitle = "快捷买入"
				this.passwordMsg = "请输入余额支付密码，用于快捷买入"
			},
			async batchCreateItem(tradePassword) {
				this.isLoadding = true
				let res = await this.$api.java_create_item({
					paymentScene: 1,
					ctid: this.ctid,
					batchBuyNum: this.quantity,
					tradePassword
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isBatchPopup = true
					this.successInfo = res.result
					this.info.userGoodsCount += res.result.buyNum
					this.quantity = 1
					uni.showToast({
						title: `您本次成功购买到${res.result.buyNum}份`,
						icon: 'none',
						duration: 3000
					});
					this.pageNum = 1
					this.seriesList = []
					this.seriesCollectionList()
				} else {
					this.isLoadding = false
					this.quantity = 1
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//限价买入
			limitedBuyConfig() {
				// passType=2 
				if (this.limitedPrice == '') {
					uni.showToast({
						title: '买入价不能为空',
						icon: 'none',
						duration: 3000
					});
					return
				}
				if (this.limitedQuantity == 0) {
					uni.showToast({
						title: '需要买入的份数不能位0',
						icon: 'none',
						duration: 3000
					});
					return
				}

				this.passType = 2
				this.mode = "pay"
				this.isPasswordImport = true
				this.passwordTitle = "限价买入"
				this.passwordMsg = "请输入余额支付密码，用于限价买入"
			},
			async submitTargetCreate(psw) {
				let res = await this.$api.java_targetCreate({
					ctid: this.ctid,
					targetNum: this.limitedQuantity,
					targetPrice: this.limitedPrice,
					tradePassword: psw,
					paymentScene: 1
				});
				if (res.status.code == 0) {
					uni.showToast({
						title: "操作成功",
						icon: 'none',
						duration: 3000
					});
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//限价卖出
			limitedSellConfig() {
				// passType=4
				if (this.limitedPrice == '') {
					uni.showToast({
						title: '卖出价不能为空',
						icon: 'none',
						duration: 3000
					});
					return
				}
				if (this.limitedQuantity == 0) {
					uni.showToast({
						title: '需要卖出的份数不能位0',
						icon: 'none',
						duration: 3000
					});
					return
				}

				this.passType = 4
				this.mode = "pay"
				this.isPasswordImport = true
				this.passwordTitle = "限价卖出"
				this.passwordMsg = "请输入余额支付密码，用于限价卖出"
			},
			//限价卖出
			async submitBatchOnSale(tradePassword) {
				let res = await this.$api.scaleBatchOnSale({
					ctid: this.ctid,
					num: this.limitedQuantity,
					price: this.limitedPrice,
					tradePassword
				});
				if (res.status.code == 0) {
					uni.showToast({
						title: '操作成功',
						icon: 'none',
						duration: 3000
					});
					this.pageNum = 1
					this.seriesList = []
					this.getInfo()
					this.seriesCollectionList()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//快捷买入
			submitTargetConfig() {
				// passType 1、快捷买入2、限价买入3、快捷卖出 4、限价买入
				if (this.quantity == 0) {
					uni.showToast({
						title: '需要买入的份数不能位0',
						icon: 'none',
						duration: 3000
					});
					return
				}
				if (this.quantity > 100) {
					uni.showToast({
						title: '需要买入的份数不能大于100',
						icon: 'none',
						duration: 3000
					});
					this.quantity = 1
					return
				}
				this.passType = 3
				this.mode = "pay"
				this.isPasswordImport = true
				this.passwordTitle = "快捷卖出"
				this.passwordMsg = "请输入余额支付密码，用于快捷卖出"
			},
			//快捷卖出
			async submitTargetSell() {
				this.isLoadding = true
				let res = await this.$api.targetQuickSell({
					ctid: this.ctid,
					num: this.quantity
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					uni.showToast({
						title: `您本次成功卖出${res.result.sellNum}份`,
						icon: 'none',
						duration: 3000
					});
					this.info.userGoodsCount -= res.result.sellNum
				} else {
					this.isLoadding = false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 获取手机bar的高度
			getDeviceBarHeight() {
				uni.getSystemInfo({
					success: (res) => {
						console.log("手机的所有状态", res);
						// res.statusBarHeight
						this.statusBarHeight = res.statusBarHeight;
						// #ifdef H5
						this.statusBarHeight = 10;
						// #endif
					},
				});
			},
			// 获取元素高度
			getSelectheight() {

				// #ifdef H5
				const DomTopHeight = uni.getStorageSync("selectTopHeight")
				const DomBottomHeight = uni.getStorageSync("selectBottomHeight")
				if (DomTopHeight && DomBottomHeight) {
					this.selectTopHeight = DomTopHeight
					this.selectBottomHeight = DomBottomHeight
					this.isStartForm = true
					return
				}
				// #endif

				// #ifdef APP-PLUS
				setTimeout(() => {
					uni.createSelectorQuery().in(this).select("#contentTop").boundingClientRect(data => {
						console.log("元素上面高度", JSON.parse(JSON.stringify(data)))
						// {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
						this.selectTopHeight = JSON.parse(JSON.stringify(data)).height
						console.log(this.selectTopHeight);

						uni.setStorageSync("selectTopHeight", this.selectTopHeight)
					}).exec()

					uni.createSelectorQuery().in(this).select("#contentBottom").boundingClientRect(data => {
						console.log("元素下面高度", JSON.parse(JSON.stringify(data)))
						// {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
						this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
						uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
					}).exec()

					this.isStartForm = true
				}, 1500)
				// #endif

				// #ifdef H5
				this.$nextTick(() => {
					setTimeout(() => {
						uni.createSelectorQuery().select("#contentBottom").boundingClientRect(data => {
							console.log("元素下面高度", JSON.parse(JSON.stringify(data)))
							this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
							uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
						}).exec()
						uni.createSelectorQuery().select("#contentTop").boundingClientRect(data => {
							console.log("元素上高度", JSON.parse(JSON.stringify(data)))
							// {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
							this.selectTopHeight = JSON.parse(JSON.stringify(data)).height
							uni.setStorageSync("selectTopHeight", this.selectTopHeight)
						}).exec()
						this.isStartForm = true
					}, 1500)
				})
				// #endif

			},
			checkTypeTabs(item, index) {
				this.current = 0
				this.period = item.value
				this.typeIndex = index
			},
			startTimer() {
				this.clearTimer(); // 清除之前的定时器
				this.refreshInterval = setInterval(() => {
					if(this.typeIndex == 0){
						this.$refs.child.createForm()
					}else{
						this.$refs.child.checkKLineArea()
					}
				}, 60000);
			},
			clearTimer() {
				if (this.refreshInterval) {
					clearInterval(this.refreshInterval);
					this.refreshInterval = null;
				}
			},
			onInputChange(event) {
				const value = event.detail.value;
				const newValue = value.replace(/[^0-9]/g, ''); // 只保留数字
				event.detail.value = newValue;
				this.$nextTick(() => {
					this.quantity = newValue;
					this.limitedQuantity = newValue;
				});
			},
			onInputChangePrice(event) {
				event.detail.value = (event.detail.value.match(/^\d*(.?\d{0,2})/g)[0]) || ""
				this.$nextTick(() => {
					this.limitedPrice = event.detail.value
				})
			},
			nav_link(title, index) {
				if (index === 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com/link/#/pages/index/mgUsAgreement"
						}
					})
				} else {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com/link/#/pages/index/mgUsMzsmAgreement"
						}
					})
				}
			},
			SetPayPassword() {
				this.isPassword = false
				this.mode = 'set'
				this.isPasswordImport = true
			},
			createSuccess(){
				this.isPasswordImport = false
				uni.setStorageSync("isSetTradePassword",1)
			},
			startTimerPrice() {
				this.clearTimerPrice(); // 清除之前的定时器
				this.refreshIntervalPrice = setInterval(() => {
					if(this.isBuy){
						//买参考价
						this.getBuyPrice()
					}else{
						//卖参考价
						this.getSellPrice()
					}
				}, 2000);
			},
			clearTimerPrice() {
				if (this.refreshIntervalPrice) {
					clearInterval(this.refreshIntervalPrice);
					this.refreshIntervalPrice = null;
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-checkbox__label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.5);
	}

	.padding_30 {
		padding: 0rpx 30rpx;
	}

	.centent {
		color: #fff;
		background-color: #111111;

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			padding: 0rpx 30rpx;
			height: 120rpx;

			.back {
				width: 90rpx;

				image {
					width: 50rpx;
				}
			}

			.title {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 400rpx;

				>view {

					text-align: center;

					text {
						font-weight: 600;
						font-size: 30rpx;
					}

					>view {
						font-size: 22rpx;
						color: rgba(255, 255, 255, 0.5);
						margin-top: 10rpx;
					}
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 12rpx;
				}
			}

			.icon {
				width: 90rpx;
				height: 36rpx;
				border-radius: 18rpx;
				background-color: #63EAEE;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #111111;
				font-size: 22rpx;

				&.huise {
					background-color: #5E5B6C;
				}

				image {
					width: 22rpx;
				}

			}
		}

		.tabs_view {
			.tabs {
				width: 100%;
				height: 50rpx;
				background-color: #25232E;
				border-radius: 25rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.li {
					font-size: 22rpx;
					width: 115rpx;
					height: 50rpx;
					text-align: center;
					line-height: 50rpx;

					&.active {
						background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
						border-radius: 25rpx;
						color: #111;
					}
				}
			}
		}

		.hold_view {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 22rpx;
			margin-top: 30rpx;

			.left_hold {
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.icon {
					width: 6rpx;
					height: 22rpx;
					border-radius: 6rpx;
					background-color: #63EAEE;
					margin-right: 10rpx;
				}

				>view {
					color: rgba(255, 255, 255, 0.5);
				}

				text {
					color: #FFF;
					margin-left: 18rpx;
				}
			}

			.right_tabs {
				.bg_view {
					background-color: #25232E;
					padding: 6rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					height: 50rpx;
					border-radius: 25rpx;

					.li {
						width: 86rpx;
						height: 40rpx;
						border-radius: 20rpx;
						line-height: 40rpx;
						text-align: center;
						color: rgba(255, 255, 255, 0.5);
						font-size: 22rpx;

						&.active {
							background-color: #5E5B6C;
							color: #fff;
						}
					}

				}


			}
		}

		.echarts_view {
			min-height: 340rpx;
			padding: 0rpx 20rpx;
		}

		.list_cart_view {
			background-color: #25232E;
			border-top-left-radius: 36rpx;
			border-top-right-radius: 36rpx;
			min-height: 65vh;
			padding: 32rpx 56rpx;

			.head_view {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.left {
					font-size: 26rpx;
					color: rgba(255, 255, 255, 0.5);

					text {
						margin-left: 20rpx;
						color: #fff;
						font-size: 30rpx;
					}
				}

				image {
					width: 36rpx;
					height: 31rpx;
				}
			}
		}

		.screen {
			margin-top: 33rpx;
		}

		.goods_ul {
			width: 638rpx;
			margin: 0 auto;
			padding-bottom: 100rpx;

			.li {
				display: flex;
				justify-content: flex-start;
				padding-top: 40rpx;

				.left_img {
					width: 96rpx;
					height: 96rpx;
					margin-right: 24rpx;

					image {
						width: 96rpx;
						height: 96rpx;
						border-radius: 10rpx;
					}

					text {
						width: 150rpx;
						text-overflow: ellipsis;
						white-space: normal;
						overflow: hidden;
					}
				}

				.right_font {
					display: flex;
					justify-content: space-between;
					align-items: center;
					width: 520rpx;
					border-bottom: 1px solid rgba(255, 255, 255, 0.2);
					padding-bottom: 30rpx;

					.font {
						width: 520rpx;
					}

					.title {
						font-size: 28rpx;
						font-weight: 600;
						position: relative;
						padding-right: 100rpx;
						width: 100%;
						height: 42rpx;
						color: var(--default-color1);

						.tag {
							width: 100rpx;
							height: 40rpx;
							border-radius: 8rpx;
							border: 1rpx solid #63EAEE;
							color: #63EAEE;
							font-size: 22rpx;
							line-height: 40rpx;
							text-align: center;
							position: absolute;
							right: 0rpx;
							top: 0;
							background-color: var(--main-bg-color);
						}
					}

					.tid {
						font-size: 20rpx;
						color: #A6A6A6;
						margin: 10rpx 0;
					}

					.price {
						font-size: 28rpx;
						font-weight: 600;
						color: var(--active-color1);
					}

					.arrow {
						image {
							width: 28rpx;
							height: 28rpx;
						}
					}
				}
			}
		}
	}

	.null_body {
		.null {

			.img {
				display: flex;
				justify-content: center;

				image {
					width: 242rpx;
				}
			}

		}

		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}

		.nav_login {
			width: 300rpx;
			height: 90rpx;
			background: var(--main-bg-color)FFF;
			border-radius: 24rpx;
			border: 2rpx solid #63EAEE;
			color: #63EAEE;
			font-size: 28rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 40rpx;
			font-weight: 600;
		}

		width:100%;
		height: 45vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.footer_ask_buy {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx 30rpx 40rpx 30rpx;
		display: flex;
		justify-content: center;

		.but_left {
			width: 300rpx;
			height: 80rpx;
			border-radius: 24rpx;
			text-align: center;
			line-height: 80rpx;
			background: linear-gradient(135deg, #EF91FB 0%, #40F8EC 100%);
			color: #141414;
			font-size: 28rpx;
			margin-right: 50rpx;
			font-weight: 600;
		}

		.but {
			width: 300rpx;
			height: 80rpx;
			border-radius: 24rpx;
			text-align: center;
			line-height: 80rpx;
			border: 2rpx solid #fff;
			background-color: #35333E;
			color: #fff;
			font-size: 28rpx;
		}
	}

	.footer_ask_buy_sell {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx 30rpx 40rpx 30rpx;
		display: flex;
		justify-content: center;

		.but_left,
		.but_right {
			width: 300rpx;
			height: 80rpx;
			border-radius: 24rpx;
			text-align: center;
			line-height: 80rpx;
			color: #141414;
			font-size: 28rpx;
			margin-right: 50rpx;
			font-weight: 600;
			display: flex;
			justify-content: space-between;
			align-items: center;

			>view {
				width: 50%;

				&.active {
					background: linear-gradient(135deg, #EF91FB 0%, #40F8EC 100%);
					color: #111;
					border-top-left-radius: 24rpx;
					border-bottom-left-radius: 24rpx;
				}

				&.border {
					border: 2rpx solid #FFF;
					border-left: none;
					background-color: #35333E;
					height: 80rpx;
					color: #fff;
					line-height: 80rpx;
					border-top-right-radius: 24rpx;
					border-bottom-right-radius: 24rpx;
				}
			}

		}
	}

	.agreement-modal-content {
		padding: 70rpx 35rpx 35rpx 35rpx;
		background-color: #34323D;

		.body {
			text-align: center;
			font-size: 28rpx;
			line-height: 36rpx;
			line-height: 46rpx;
			.title{
				// padding-left:80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				.icon{
					margin-right:14rpx;
				}
			}
			.text {
				color: #63EAEE;
				
			}
		}

		.button {
			margin-top: 56rpx;
			display: flex;
			justify-content: center;

			.but {
				width: 300rpx;
				height: 80rpx;
				border-radius: 40rpx;
				background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
				color: #111;
				text-align: center;
				line-height: 80rpx;
				font-size: 24rpx;
			}
		}

	}

	.fast-modal-content {
		background-color: #34323D;

		.right_close {
			position: absolute;
			right: 0rpx;
			top: 0rpx;

			image {
				width: 80rpx;
			}
		}

		.body {
			padding: 0rpx 30rpx 30rpx 30rpx;

			.head {
				height: 124rpx;
				line-height: 124rpx;
				text-align: center;
				border-bottom: 1px solid rgba(255, 255, 255, 0.1);
				font-size: 32rpx;
				font-weight: 600;
			}

			.cententPop {
				line-height: 80rpx;
				font-size: 30rpx;

				.item_msg {
					height: 80rpx;
					padding-left: 60rpx;
					margin-top: 30rpx;
					display: flex;

					.label {
						width: 160rpx;
					}

					text {
						color: #63EAEE;
						font-weight: 600;
					}

					.huise {
						color: rgba(255, 255, 255, 0.5);
						font-size: 22rpx;
						font-weight: 300;
					}
				}

				.item_input {
					height: 80rpx;
					display: flex;
					padding-left: 60rpx;
					margin-top: 40rpx;
					position: relative;

					.label {
						width: 160rpx;
					}

					.input {
						width: 360rpx;
						height: 80rpx;
						border: 1px solid #fff;
						background-color: rgba(255, 255, 255, 0.2);
						border-radius: 18rpx;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						input {
							height: 100%;
							width: 250rpx;
							padding-left: 20rpx;
							font-size:36rpx;
						}
						.rmb{
							font-size:36rpx;
							font-weight:600;
							color:#fff;
							width:50rpx;
							padding-left:21rpx;
						}
						text {
							color: #63EAEE;
							font-size: 30rpx;
							width: 100rpx;
							text-align: center;
						}
					}

					.error_msg {
						position: absolute;
						right: 70rpx;
						bottom: -70rpx;
						color: rgba(255, 255, 255, 0.5);
						font-size: 26rpx;

						text {
							color: #63EAEE;
						}
					}
				}

				.button {
					height: 90rpx;
					background: linear-gradient(144deg, #EF91FB 0%, #40F8EC 100%);
					border-radius: 50rpx;
					width: 100%;
					margin-top: 60rpx;
					text-align: center;
					color: #111;
					line-height: 90rpx;
					font-size: 34rpx;
					font-weight: 600;
				}
			}
		}
	}

	.model {

		// font-family: 'fonts';
		.colse {
			image {
				position: absolute;
				right: 20rpx;
				top: 20rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}

		.title_bg {
			font-size: 28rpx;
			width: 240rpx;
			position: relative;
			text-align: center;
			margin: 0 auto;
			margin-bottom: 10rpx;
			color: #fff;

			.icon {
				position: absolute;
				left: 0rpx;
				top: 10rpx;
				width: 240rpx;
				height: 8rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
				background-size: 100%;
			}
		}

		.introduce {
			color: rgba(255, 255, 255, 0.7);
			font-size: 26rpx;
			text-align: left;
			padding: 30rpx 40rpx 28rpx 40rpx;

			>view {
				margin-bottom: 14rpx;
				white-space:pre-line;
				text {
					color: #fff;
				}
			}
		}
	}
	.new-modal-content {
			padding:35rpx 40rpx;
			.success_img{
				display: flex;
				justify-content: center;
				align-items: center;
				image{
					width:160rpx;
					height:160rpx;
				}
			}
			.modal-content{
				padding:35rpx 0rpx;
				border-bottom:1rpx solid #EDEDED;
				font-size:28rpx;
				color:#fff;
				text-align: center;
			}
			.showModal-btn {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top:30rpx;
				>view {
					width: 226rpx;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 24rpx;
					font-weight: bold;
					border-radius: 14rpx;
					color:#fff;
				}
		
				.img_cancel {
					border: 1px solid #FFF;
					color:#FFF;
				}
				.img_reasale {
					color:#141414;
					background:var(--primary-button-color);
				}
			}
		}
		.title_bg2 {
			font-size: 34rpx;
			font-weight: 600;
			width: 240rpx;
			position: relative;
			text-align: center;
			margin: 0 auto;
			margin-bottom: 10rpx;
			color: #fff;
		
			.icon {
				position: absolute;
				left: 0rpx;
				top: 20rpx;
				width: 240rpx;
				height: 8rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
				background-size: 100%;
			}
		}
</style>