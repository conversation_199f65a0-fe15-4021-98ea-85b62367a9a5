<template>
	<view class="body">
		<view class="head_view">
			<view class="back" @click="nav_back()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20241021/e64ece7f32b95f6b07ea5c4e48b178cb_26x50.png"
					mode="widthFix"></image>
			</view>
			<view class="time_view">
				<view class="time_title">
					<text class="start" v-if="info.isOpen == 0"> 距离开放</text>
					<text class="end" v-if="info.isOpen == 1"> 距离关闭</text>
					<u-count-down :show-days="false" :show-hours="true" :timestamp="info.second" separator="zh"
						separator-size="24" :showBorderCenter="true" font-size="34" bg-color="#F3D6BC"
						separator-color="#F3D6BC" color="#111" @end="endCount"></u-count-down>
				</view>
				<view class="msg">
					开放时间：{{ info.timeText }}
				</view>
			</view>
		</view>
		<view class="tabs_view">
			<view class="tabs">
				<u-tabs name="cate_name" :bar-style="barStyle" :list="list" bold :is-scroll="false" :item-width="200"
					:active-item-style="itemStyle" inactive-color="rgba(255,255,255,0.5)" font-size="28"
					bg-color="transparent" active-color="#F3D6BC" :current="current" @change="change"></u-tabs>
			</view>
			<view class="right_ion">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20241021/53109f269cd07207d826550a3deb29f7_24x24.png"
					mode="widthFix"></image>
				<text @click="isMsg = true">玩法说明</text>
			</view>
		</view>
		<view class="list_view_ul">
			<view class="list_view" v-for="(item, index) in usList" @click="nav_mgDetails(item)">
				<view class="left_title">
					<view class="title">{{ item.title }} </view>
					<view class="icon">
						<view class="but">US</view>
						<text class="msg">{{ item.titleCn }}</text>
					</view>
				</view>
				<view class="echarts_view">
					<l-echart class="klink" ref="chartRef"></l-echart>
				</view>
				<view class="rose_view">
					<view class="num">
						{{ item.lastTradePrice ? item.lastTradePrice : '-' }}
					</view>
					<view class="percentage">
						<image v-show="item.increaseRatio > 0"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240914/f903805be445eac1e7ec0d1a25daffdd_28x28.png"
							mode="widthFix"></image>
						<image v-show="item.increaseRatio < 0"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240914/999707c7a76a506d05baaaaf01fee4e4_28x28.png"
							mode="widthFix"></image>
						<image v-show="item.increaseRatio === 0"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240914/f903805be445eac1e7ec0d1a25daffdd_28x28.png"
							mode="widthFix"></image>
						<image v-show="item.increaseRatio == null"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240914/999707c7a76a506d05baaaaf01fee4e4_28x28.png"
							mode="widthFix"></image>
						<text :class="{ 'red': item.increaseRatio >= 0 && item.increaseRatio != null }">{{
							`${item.increaseRatio > 0 ? '+' : ''}${item.increaseRatio != null ? item.increaseRatio :
								'-'}%` }}</text>
					</view>
				</view>
			</view>
			<view class="null_body" v-if="usList.length == 0">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						{{ current == 2 ? '暂时还没有加入自选的美股' : '暂无数据' }}
					</view>
				</view>
			</view>
		</view>
		<view class="optional_view" v-if="current == 2 && usList == ''">
			<view class="title ">
				推荐自选
			</view>
			<view class="optional_ul">
				<view class="li" v-for="(item, index) in recommendList">
					<view class="left">
						<view class="name">{{ item.title }}</view>
						<view class="msg">{{ item.titleCn }}</view>
						<view class="lv" :class="{ 'red': item.increaseRatio >= 0 && item.increaseRatio != null }">
							{{ `${item.increaseRatio > 0 ? '+' : ''}${item.increaseRatio != null ? item.increaseRatio :
								'-'}%` }}
						</view>
					</view>
					<view class="but">
						<view class="active" v-show="item.isOptional" @click="checkUserSelect(item, 1)">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240918/8adc16433f851aad458fa16cbb7d08dd_64x64.png"
								mode="widthFix"></image>自选
						</view>
						<view class="huise" v-show="!item.isOptional" @click="checkUserSelect(item, 0)">
							已自选
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal class="model" width="600" v-model="isMsg" :show-title="true" :show-confirm-button="false"
			border-radius="24" title="" :mask-close-able="true">
			<view class="colse" @click="isMsg = false">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="title_bg">
				<view class="icon"></view>
				玩法说明
			</view>
			<view class="introduce">
				<view style="margin-bottom:50rpx;line-height:44rpx;" v-html="msgIntroduce"></view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
		<!--<TabBar ref="TabBar" :initialActiveIndex="initialActiveIndex" v-if="initialActiveIndex == 2"></TabBar>-->
	</view>
</template>

<script>
import introducePop from '@/components/public/introducePop.vue'
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
import TabBar from '@/components/public/TabBar.vue'
export default {
	data() {
		return {
			list: [{
				cate_name: '精选'
			}, {
				cate_name: '涨幅'
			}, {
				cate_name: '自选',
			}],
			current: 0,
			barStyle: {
				'background': '#F3D6BC',
				'height': '6rpx',
				'width': '50rpx'
			},
			itemStyle: {
				'font-size': '32rpx',
				'min-width': '130rpx',
				'z-index': '2',
			},
			usList: [],
			info: "",
			recommendList: [],
			isMsg: false,
			msgTitle: "",
			msgIntroduce: "在数字藏品市场蓬勃发展的背景下，Bigverse 引入了一套创新的模拟美股交易板块。此板块通过独特的流动性池设计与跨市场价格同步机制，实现了数字藏品价格与美股市场价格的实时联动。与传统金融市场相比，这一设计不仅遵循了中国的合规要求，还为用户提供了一个稳定、公平的交易环境。",
			globalOption: {
				grid: {
					borderWidth: 0, // 隐藏网格边界线
					bottom: '15%', // 增加底部空间，防止标签遮挡
					top: '25%', // 增加顶部空间，防止标题遮挡
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: [],
					axisLine: { // 隐藏X轴线
						show: false
					},
					axisTick: { // 隐藏X轴刻度
						show: false
					},
					axisLabel: { // 隐藏X轴标签
						show: false
					}
				},
				yAxis: {
					type: 'value',
					splitLine: {
						show: false
					},
					axisLine: { // 隐藏Y轴线
						show: false
					},
					axisTick: { // 隐藏Y轴刻度
						show: false
					},
					axisLabel: { // 隐藏Y轴标签
						show: false
					}
				},
				series: [{
					data: [100, 222, 9999, 444, 888, 666],
					type: 'line',
					smooth: true,
					symbol: 'none', // 隐藏折线上的点
					lineStyle: {
						color: 'rgba(124, 228, 81, 1)',// 折线颜色
						width: 1
					},
					areaStyle: {
						color: new echarts.graphic.LinearGradient(
							0, 0, 0, 1,
							[{
								offset: 0,
								color: 'rgba(124, 228, 81, 1)'
							},
							{
								offset: 1,
								color: 'rgba(34, 161, 235, 0)'
							}
							]
						)
					},
				}],
			},
			isLoadding: false,
			initialActiveIndex: 99
		}
	},
	onLoad() {
		this.firstVisit()
	},
	onShow() {
		let currentTabbar = uni.getStorageSync('currentTabbar')
		if (currentTabbar == 'pay') {
			this.initialActiveIndex = 2
		}
		this.getInfo()
		this.change(this.current)

	},
	onPullDownRefresh() {
		setTimeout(() => {
			this.getInfo()
			this.change(this.current)
			this.firstVisit()

			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 500);
	},
	components: {
		introducePop,
		TabBar
	},
	methods: {
		async firstVisit() {

			let pages = getCurrentPages(); // 获取当前页面栈
			let currentPage = pages[pages.length - 1]; // 获取当前页面
			let currentRoute = currentPage.route; // 获取当前页面的路径

			let res = await this.$api.VisitorShare({
				module: "MEIGU",
				from: 'h5',
				page: currentRoute
			});
		},

		change(index) {
			this.current = index
			this.usList = []
			if (index == 0) {
				this.get_recommendList()
			} else if (index == 1) {
				this.get_increaseList()
			} else {
				this.get_userSelectList()
			}
		},
		nav_back() {
			// this.$Router.back()
			this.$Router.pushTab({
				name: 'contract-BITindex',
				params: {
					load: true
				}
			})
		},
		async getInfo() {
			let res = await this.$api.scaleInfo({

			});
			if (res.status.code == 0) {
				res.result.second + 1
				this.info = res.result
				if (this.info.second == 0) {
					setTimeout(() => {
						this.getInfo()
					}, 1000)
				}
			} else if (res.status.code == 1002) {
				this.isLoadding = true
				setTimeout(() => {
					this.isLoadding = false
					this.$Router.push({
						name: "mainLogin",
						// #ifdef H5
						params: {
							url: window.location.hash,
						},
						// #endif
					})
				}, 1500);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_increaseList(pageSize) {
			let res = await this.$api.scaleIncreaseList({
				pageNum: 1,
				pageSize: 20
			});
			if (res.status.code == 0) {
				this.usList = res.result.list
				setTimeout(() => {
					this.usList.forEach((item, index) => {
						let isRed = item.increaseRatio >= 0 && item.increaseRatio != null ? true : false
						console.log('isRed', isRed)
						this.init(index, item.kline, isRed)
					})
				}, 100)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_recommendList(pageSize) {
			let res = await this.$api.scaleRecommendList({
				pageNum: 1,
				pageSize: pageSize ? pageSize : ""
			});
			if (res.status.code == 0) {
				this.recommendList = []
				if (pageSize) {
					res.result.list.forEach((item) => {
						item.isOptional = true
						this.recommendList.push(item)
					})
					console.log(this.recommendList)
				} else {
					this.usList = res.result.list
					setTimeout(() => {
						this.usList.forEach((item, index) => {
							let isRed = item.increaseRatio >= 0 && item.increaseRatio != null ? true : false
							this.init(index, item.kline, isRed)
						})
					}, 100)
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_userSelectList() {
			let res = await this.$api.scaleUserSelectList({
				pageNum: 1,
				pageSize: 10
			});
			if (res.status.code == 0) {
				if (res.result.list != "") {
					this.usList = res.result.list
					setTimeout(() => {
						this.usList.forEach((item, index) => {
							let isRed = item.increaseRatio >= 0 && item.increaseRatio != null ? true : false
							this.init(index, item.kline, isRed)
						})
					}, 100)
				} else {
					this.get_recommendList(4)
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async checkUserSelect(item, isSelect) {
			let res = await this.$api.scaleUserSelect({
				ctid: item.ctid,
				isSelect
			});
			if (res.status.code == 0) {
				item.isOptional = isSelect == 0 ? true : false
				this.change(2)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_mgDetails(item) {
			this.$Router.push({
				name: 'mgUsDetails',
				params: {
					ctid: item.ctid,
					title: item.title
				}
			})
		},
		endCount() {
			this.getInfo()
		},
		//初始化k线图，循环加载折线图，通过下标传递过来
		async init(index, globalOption, isRed) {
			let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
			// chart 图表实例不能存在data里
			// 计算最大值和最小值
			const minValue = Math.min(...globalOption);
			const maxValue = Math.max(...globalOption);

			// 设置 y 轴的最小值和最大值
			data.yAxis.min = minValue;
			data.yAxis.max = maxValue;
			if (isRed) {
				data.series[0].lineStyle.color = 'rgba(236, 63, 103, 1)'
				data.series[0].areaStyle.color.colorStops[0].color = 'rgba(215, 57, 94, 1)'
				data.series[0].areaStyle.color.colorStops[1].color = 'rgba(255, 90, 117, 0)'
			}
			data.series[0].data = []
			// 扩展数组并填充 null
			const extendedData = this.extendArrayWithNull(globalOption, 11);
			data.series[0].data = extendedData
			const lEchart = await this.$refs.chartRef[index].init(echarts);
			lEchart.setOption(data)
		},
		extendArrayWithNull(arr, targetLength) {
			// 确保目标长度大于等于当前数组长度
			const fillLength = Math.max(0, targetLength - arr.length);
			const extendedArray = arr.concat(Array(fillLength).fill(null));
			return extendedArray;
		}
	}
}
</script>

<style lang="scss" scoped>
::v-deep .u-countdown-item {
	padding: 15rpx 8rpx;
	position: relative;

}

::v-deep .u-countdown-item::before {
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	height: 4rpx;
	background-color: #fff;

}

::v-deep .u-countdown-time {
	font-weight: 600;
}

::v-deep .u-countdown-colon {
	padding: 0rpx 10rpx;
}

.body {
	background-color: #1C1931;
	min-height: 100vh;
	padding-bottom: 200rpx;

	.head_view {
		width: 100%;
		height: 628rpx;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20241021/7ce7ffd2b729906e8c038dd470783e85_750x628.png);
		background-size: 100%;
		position: relative;

		.back {
			width: 50rpx;
			height: 50rpx;
			position: absolute;
			/* #ifdef APP */
			top: 90rpx;
			/* #endif */
			/* #ifdef H5 */
			top: 60rpx;
			/* #endif */

			left: 30rpx;

			image {
				width: 26rpx;
				height: 50rpx;
			}
		}

		.time_view {
			padding-top: 450rpx;
			color: #F3D6BC;
			text-align: center;

			.time_title {
				margin-bottom: 20rpx;
				font-size: 24rpx;

				text {
					margin-right: 20rpx;
				}

				.end {
					color: #EF64FF;
				}
			}

			.msg {
				font-size: 22rpx;
			}
		}
	}

	.tabs_view {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0rpx 42rpx 0rpx 0rpx;
		margin-bottom: 40rpx;

		.tabs {
			width: 400rpx;
		}

		.right_ion {
			width: 140rpx;
			height: 40rpx;
			border-radius: 20rpx;
			border: 1px solid #F3D6BC;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 24rpx;
				margin-right: 10rpx;
			}

			font-size:22rpx;
			color:#F3D6BC;
		}

	}

	.list_view_ul {
		min-height: 450rpx;

		.list_view {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0rpx 40rpx;
			color: #fff;
			margin-bottom: 42rpx;

			.left_title {
				width: 280rpx;

				.title {
					font-size: 30rpx;
					margin-bottom: 14rpx;
					font-weight: 600;
				}

				.icon {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-size: 24rpx;

					.but {
						color: #fff;
						background-color: #8D76DB;
						border-radius: 4rpx;
						font-size: 22rpx;
						width: 44rpx;
						height: 28rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-right: 10rpx;
					}

					.msg {
						color: rgba(255, 255, 255, 0.5);
					}

				}
			}

			.echarts_view {
				width: 175rpx;
				height: 56rpx;
			}

			.rose_view {
				width: 130rpx;

				.num {
					font-size: 30rpx;
					font-weight: 600;
					text-align: right;
				}

				.percentage {
					display: flex;
					justify-content: flex-end;
					align-items: center;
					font-size: 24rpx;
					margin-top: 12rpx;

					image {
						width: 28rpx;
					}

					text {
						margin-left: 15rpx;
						color: #7CE451;
						font-size: 22rpx;

						&.red {
							color: #EC3F67;
						}
					}
				}
			}
		}
	}

	.optional_view {
		padding: 40rpx;

		.title {
			font-size: 28rpx;
			margin-bottom: 26rpx;
			color: #fff;
		}

		.optional_ul {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;

			.li {
				width: 320rpx;
				height: 142rpx;
				background-color: #25232E;
				border-radius: 20rpx;
				padding: 12rpx 20rpx 18rpx 20rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;

				.left {
					.name {
						font-size: 28rpx;
						font-weight: 600;
						color: #fff;
					}

					.msg {
						font-size: 22rpx;
						color: rgba(255, 255, 255, 0.5);
						margin: 10rpx 0rpx;
					}

					.lv {
						font-size: 28rpx;
						color: #7CE451;

						&.red {
							color: #EC3F67;
						}
					}
				}

				.but {
					>view {
						width: 90rpx;
						height: 36rpx;
						border-radius: 18rpx;
						font-size: 22rpx;
						color: #111111;
					}

					.active {
						background-color: #F3D6BC;
						border-radius: 18rpx;
						display: flex;
						justify-content: center;
						align-items: center;

						image {
							width: 22rpx;
							height: 14rpx;
						}
					}

					.huise {
						background-color: #5E5B6C;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
			}
		}
	}
}

.null_body {
	.null {
		.img {
			display: flex;
			justify-content: center;

			image {
				width: 240rpx;
			}
		}
	}

	.text {
		color: rgba(255, 255, 255, 0.5);
		font-size: 24rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	width:100%;
	height: 450rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.klink {
	width: 175rpx;
	height: 56rpx;
}

.model {

	// font-family: 'fonts';
	.colse {
		image {
			position: absolute;
			right: 20rpx;
			top: 20rpx;
			width: 48rpx;
			height: 48rpx;
		}
	}

	.title_bg {
		font-size: 28rpx;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;

		.icon {
			position: absolute;
			left: 0rpx;
			top: 10rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}

	.introduce {
		color: rgba(255, 255, 255, 0.7);
		font-size: 26rpx;
		text-align: left;
		padding: 30rpx 40rpx 28rpx 40rpx;

		>view {
			margin-bottom: 14rpx;

			text {
				color: #fff;
			}
		}
	}
}
</style>