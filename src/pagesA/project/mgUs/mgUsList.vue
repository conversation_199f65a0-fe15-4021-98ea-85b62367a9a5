<template>
	<view class="body">
		<view class="barHeight"></view>
		<view class="header">
			<view class="back" @click="nav_back()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240914/779998841e6181594c2650ceac009ea8_50x50.png" mode="widthFix"></image>
				{{title}}
			</view>
			<view class="tabs">
				<u-tabs name="cate_name" :bar-style="barStyle" :list="list" bold :is-scroll="false" :item-width="200"
					:active-item-style="itemStyle" inactive-color="rgba(255,255,255,0.5)" font-size="28" bg-color="transparent" active-color="#fff"
					:current="current" @change="change"></u-tabs>
			</view>
		</view>
		<view class="content">
			<view class="sub_view" v-show="current == 0">
				<view class="left_subTabs">
					<u-tabs name="cate_name" :bar-style="barStyle" :list="subList" bold :is-scroll="false" :show-bar="false" :item-width="200"
						 inactive-color="rgba(255,255,255,0.5)" font-size="24" bg-color="transparent" active-color="#fff"
						:current="subCurrent" @change="subChange"></u-tabs>
				</view>
				<view class="right_button" @click="subCurrent==0?batchCance():batchUnSaleCollectionList()">
					全部撤销
				</view>
			</view>
			<view class="sub_view" v-show="current == 1">
				<view class="left_subTabs">
					<u-tabs name="cate_name" :bar-style="barStyle" :list="subListBuy" bold :is-scroll="false" :show-bar="false" :item-width="200"
						 inactive-color="rgba(255,255,255,0.5)" font-size="24" bg-color="transparent" active-color="#fff"
						:current="subCurrentBuy" @change="subChangeBuy"></u-tabs>
				</view>
			</view>
			<view class="wait" v-show="current == 0">
				<view class="list_view" v-show="subCurrent == 1">
					<view class="li" v-for="(item,index) in sellList">
						<view class="left">
								<image :src="item.cover" mode="scaleToFill"></image>
								<view class="font">
									<view class="tid">Token ID: {{item.tid}}</view>
									<view class="price">金额：￥{{item.price}}</view>
								</view>
						</view>
						<view class="right" @click="collectionOffShelfConfirm(item)">
							撤销
						</view>
					</view>
					<view class="null_body" v-if="sellList.length==0">
						<view class="null">
							<view class="img">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
									mode="widthFix"></image>
							</view>
							<view class="text">
								暂无数据
							</view>
						</view>
					</view>
				</view>
				<view class="list_view_buy" v-show="subCurrent == 0">
					<view class="li" v-for="(item,index) in dutyList" @click="revocation(item)">
						<view class="left">
								<image :src="item.cover.src" mode="scaleToFill"></image>
								<view class="font">
									<view class="view">
										<text class="price">
											金额 ￥{{item.targetPrice}}
										</text>
										<text>
											份数：{{item.targetNum}}份
										</text>
									</view>
									<view class="time">发起时间：{{item.dutyTime}}</view>
									<view class="msg" v-show="item.tip">
										余额不足，暂无法委托
									</view>
								</view>
						</view>
						<view class="right">
							撤销
						</view>
					</view>
					<view class="null_body" v-if="dutyList.length==0">
						<view class="null">
							<view class="img">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
									mode="widthFix"></image>
							</view>
							<view class="text">
								暂无数据
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="lately"  v-show="current == 1">
				<view class="list_view" >
					<view class="li" v-for="(item,index) in historyList">
						<view class="left">
								<image :src="item.cover" mode="scaleToFill"></image>
								<view class="font">
									<view class="tid">Token ID: {{item.tid}}</view>
									<view class="orderTime">下单时间：{{item.buyTimeSecond}}</view>
									<view class="payTime">成交时间：{{item.buyTimeSecond}}</view> 
								</view>
						</view>
						<view class="right">
							￥{{item.price}}
						</view>
					</view>
					<view class="null_body" v-if="historyList.length==0">
						<view class="null">
							<view class="img">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
									mode="widthFix"></image>
							</view>
							<view class="text">
								暂无数据
							</view>
						</view>
					</view>
				</view>
			</view>
			
		</view>  
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					cate_name: '等待成交'
				}, {
					cate_name: '已成交'
				}],
				subList: [{
					cate_name: '等待买入'
				}, {
					cate_name: '等待卖出'
				}],
				subListBuy: [{
					cate_name: '已买入'
				}, {
					cate_name: '已卖出'
				}],
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
					'height':'6rpx',
					'width':'50rpx'
				}, 
				itemStyle: {
					'font-size': '32rpx',
					'min-width': '130rpx',
					'z-index': '2' 
				},
				current: 0,
				subCurrent: 0,
				subCurrentBuy: 0,
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				historyList:[],//最近成交
				dutyList:[],//等待买入
				sellList:[],//等待卖出
				pageNum:1,
				title:""
			}
		},
		onLoad(options){
			this.ctid = options.ctid
			this.title = options.title
			this.getDutyList()
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.change(this.current)
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 500);
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					if(this.current == 0){
						if(this.subCurrent == 0){
							this.getDutyList()
						}else{
							this.getSellList()
						}
					}else{
						this.getList()
					}
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			change(index) {
				this.current = index 
				this.pageNum = 1
				this.isFooter = true
				this.sellList = []
				this.dutyList = []
				this.historyList = []
				if(index == 0){
					if(this.subCurrent == 0){
						this.getDutyList()
					}else{
						this.getSellList()
					}
				}else{
					if(this.subCurrentBuy == 0){
						this.isBuy = 1
						this.getList()
					}else{
						this.isBuy = 0
						this.getList()
					}
				}
			},
			subChange(index) {
				this.subCurrent = index 
				this.pageNum = 1
				this.isFooter = true
				this.sellList = []
				this.dutyList = []
				if(index == 0){
					this.getDutyList()
				}else{
					this.getSellList()
				}
			},
			subChangeBuy(index) {
				this.subCurrentBuy = index 
				this.pageNum = 1
				this.isFooter = true
				this.historyList = []
				if(index == 0){
					this.isBuy = 1
					this.getList()
				}else{
					this.isBuy = 0
					this.getList()
				}
			},
			nav_back(){
				this.$Router.back()
			},
			async getList() { //成交记录
				this.isRequest = true
				let res = await this.$api.java_seriesOrderList({
					ctid: this.ctid,
					pageNum:this.pageNum,
					isBuy:this.isBuy
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.historyList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getDutyList() { //等待买入
				this.isRequest = true
				let {
					result,
					status
				} = await this.$api.dutyList({
					pageNum: this.pageNum,
					pageSize: 10,
					ctid:this.ctid
				})
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							this.dutyList.push(item)
						})
					}
				}
			},
			async getSellList() { //等待卖出
				this.isRequest = true
				let {
					result,
					status
				} = await this.$api.scaleOnSaleList({
					pageNum: this.pageNum,
					pageSize: 10,
					ctid:this.ctid
				})
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							console.log(item)
							this.sellList.push(item)
						})
					}
				}
			},
			async revocation(item) { //等待买入撤销
				let {
					result,
					status
				} = await this.$api.java_targetCancel({
					dutyId: item.dutyId
				})
				if (status.code == 0) {
					this.pageNum = 1
					this.dutyList = []
					uni.showToast({
						title: '撤销成功',
						icon: 'none'
					})
					this.getDutyList()
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async batchCance(item) { //等待买入 批量撤销
				let {
					result,
					status
				} = await this.$api.targetBatchCancel({
					ctid: this.ctid
				})
				if (status.code == 0) {
					this.pageNum = 1
					this.dutyList = []
					uni.showToast({
						title: '全部撤销成功',
						icon: 'none'
					})
					this.getDutyList()
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 等待卖出 撤销
			async collectionOffShelfConfirm(item) {
				let res = await this.$api.unSale({
					tid: item.tid,
				})
				if (res.status.code == 0) {
					uni.showToast({
						title: '撤销成功',
						icon: 'none'
					})
					this.sellList = []
					this.pageNum = 1
					this.getSellList()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			//等待卖出批量撤销
			async batchUnSaleCollectionList() {
				let res = await this.$api.batchUnSaleCollectionList({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					this.sellList = []
					this.pageNum = 1
					uni.showToast({
						title: '全部撤销成功',
						icon: 'none',
						duration: 3000
					});
					this.getSellList()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	page{
		background-color:#111111;
	}
	.body{
		padding-bottom: 140rpx;
		.header{
			position:relative;
			width:100%;
			.back{
				position: absolute;
				left:30rpx;
				top:17rpx;
				color:#fff;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size:24rpx;
				image{
					width:50rpx;
					margin-right:10rpx;
				}
			}
			.tabs{
				width:50%;
				margin:0 auto;
			}
		}
		.content{
			.sub_view{
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding:0rpx 20rpx;
				.left_subTabs{
					width:280rpx;
				}
				.right_button{
					width:130rpx;
					height:44rpx;
					background-color:rgba(255, 255, 255, 0.3);
					font-size:24rpx;
					color:#fff;
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius:36rpx;
				}
			}
			.wait{
				.list_view,.list_view_buy{
					padding:0rpx 20rpx;
					margin-top:10rpx;
					.li{
						display: flex;
						justify-content: space-between;
						align-items: center;
						color:#fff;
						padding:35rpx 30rpx;
						background-color:#25232D;
						border-radius:24rpx;
						margin-bottom:20rpx;
						height:150rpx;
						.left{
							display: flex;
							justify-content: flex-start;
							align-items: center;
							image{
								width:80rpx;
								height:80rpx;
								border-radius:50%;
							}
							.font{
								font-size:24rpx;
								margin-left:20rpx;
								.tid{
									margin-bottom:18rpx;
								}
								.price{
									color:#63EAEE;
									font-weight:600;
								}
							}
						}
						.right{
							width:90rpx;
							height:44rpx;
							background-color:rgba(255, 255, 255, 0.3);
							font-size:24rpx;
							color:#fff;
							display: flex;
							justify-content: center;
							align-items: center;
							border-radius:36rpx;
						}
					}
				}
				.list_view_buy{
					.li{
						align-items: flex-start;
					}
					.view{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						.price{
							margin-right:20rpx;
						}
					}
					.time{
						margin-top:20rpx;
					}
					.msg{
						font-size: 20rpx;
						margin-top:10rpx;
						color:#DE3C61;
					}
				}
			}
			.lately{
				.list_view{
					padding:0rpx 20rpx;
					margin-top:10rpx;
					.li{
						display: flex;
						justify-content: space-between;
						align-items: center;
						color:#fff;
						padding:35rpx 30rpx;
						background-color:#25232D;
						border-radius:24rpx;
						margin-bottom:20rpx;
						height:150rpx;
						position: relative;
						.left{
							display: flex;
							justify-content: flex-start;
							align-items: center;
							image{
								width:80rpx;
								height:80rpx;
								border-radius:50%;
							}
							.font{
								font-size:24rpx;
								margin-left:20rpx;
								.orderTime{
									margin:15rpx 0rpx 10rpx 0rpx;
									color:rgba(255, 255, 255, 0.5);
								}
								.payTime{
									color:rgba(255, 255, 255, 0.5);
								}
							}
						}
						.right{
							color:#63EAEE;
							font-size:28rpx;
							font-weight:600;
							position: absolute;
							right:30rpx;
						}
					}
				}
			}
		}
	}
	.null_body {
		.null {
			.img {
				display: flex;
				justify-content: center;
				image {
					width: 240rpx;
				}
			}
		}
		.text {
			color: rgba(255, 255, 255, 0.5);
			font-size: 24rpx;
			text-align: center;
			margin-top: 30rpx;
		}
	
		width:100%;
		height: 60vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
