<template>
    <view class="contract-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="全部合约">
        </u-navbar>


        <view class="contract-list">
            <view class="contract-item" v-for="(item, index) in contractList" :key="index">
                <view class="top">
                    <view class="t_left">
                        <text class="symbol">{{ symbol }} 永续</text>
                        <text class="date">{{ formatTimestampms(item.createAt) }}</text>
                    </view>
                    <view class="b_right">
                        <view class="b_right_top">
                            <text class="type" :class="item.typeClass">{{ item.type == 1 ? '限价委托' : '市价委托' }}</text>
                            <text class="level">{{ item.leverageLevel + 'X' }}</text>
                        </view>
                        <view class="status" :class="item.status == 4 ? 'canceled' : item.status == 2 ? 'success' : ''">
                            {{ getStatusLabel(item.status) }}</view>
                    </view>
                </view>

                <view class="meta">
                    <view class="meta-item">
                        <text class="label">保证金</text>
                        <text class="value">{{ item.margin }}</text>
                    </view>
                    <view class="meta-item">
                        <text class="label">开仓价格</text>
                        <text class="value">{{ item.avgPrice }}</text>
                    </view>
                    <view class="meta-item">
                        <text class="label">委托数量</text>
                        <text class="value">{{ item.volume }}</text>
                    </view>
                    <view class="meta-item">
                        <text class="label">委托价格</text>
                        <text class="value">{{ item.price }}</text>
                    </view>
                    <view class="meta-item">
                        <text class="label">手续费</text>
                        <text class="value">{{ item.tradeFee }}</text>
                    </view>
                    <!-- metaList: [
                        { label: '保证金', value: '23111' },
                        { label: '开仓价格', value: '94045.00' },
                        { label: '委托数量', value: '2张' },
                        { label: '触发价', value: '0.0000' },
                        { label: '手续费', value: '0.0019' }
                    ] -->
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { formatTimestampms } from '@/utils/utils'

export default {
    data() {
        return {
            contractList: [],
            page: {
                pageNum: 1,
                pageSize: 10,
            },
            hasnext: false,
            symbol: ""
        }
    },
    onLoad() {
        this.gethistoryOrder();
        this.symbol = uni.getStorageSync("currentContract").symbol
    },
    onReachBottom() {
        console.log(123);

        if (this.hasnext) {
            this.page.pageNum++;
            this.gethistoryOrder();

        }
    },
    methods: {
        formatTimestampms,
        getStatusLabel(status) {
            const statusMap = {
                0: '初始化',
                1: '新订单',
                2: '已成交',
                3: '部分成交',
                4: '已取消',
                5: '取消中',
                6: '已过期'
            }
            return statusMap[status] || '--'
        },
        async gethistoryOrder() {
            let res = await this.$api.historyOrder({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                contractSymbolId: uni.getStorageSync("currentContract").id,
            })
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.page.pageNum == 1) {
                    this.contractList = res.result.data
                } else {
                    this.contractList = this.contractList.concat(res.result.data)
                }
                this.contractList = res.result.data
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: "none",
                })
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.contract-page {
    padding-top: 54rpx;

    .header {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 20rpx;

        .back-icon {
            margin-right: 20rpx;
        }

        .title {
            font-weight: 500;
        }
    }

    .contract-list {
        padding: 0 32rpx;

        .contract-item {
            background: #fff;
            border-radius: 16rpx;
            // padding: 24rpx;
            margin-bottom: 20rpx;

            .top {
                display: flex;
                justify-content: space-between;
                // align-items: center;
                flex-direction: column;

                .t_left {
                    justify-content: space-between;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;

                    .symbol {
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 28rpx;
                        line-height: 40rpx;
                        color: #000;
                    }


                    .date {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 40rpx;
                        color: rgba(0, 0, 0, .5);
                    }



                }

                .b_right {
                    // margin-top: 8rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .b_right_top {
                        height: fit-content;
                    }

                    .type {
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 20rpx;
                        line-height: 28rpx;
                        margin-right: 8rpx;
                        // padding: 2rpx 8rpx;
                        // border-radius: 6rpx;

                        &.short {
                            color: #FF82A3;
                            // background: #ffeaea;
                        }

                        &.long {
                            color: #30C147;
                            // background: #e8fff4;
                        }
                    }

                    .level {
                        padding: 6rpx 12rpx;
                        border-radius: 10rpx;
                        background: #F2F2F2;
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 20rpx;
                        color: #000;

                    }

                    .status {
                        font-size: 20rpx;
                        padding: 6rpx 16rpx;
                        border-radius: 10rpx;
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 20rpx;

                        &.canceled {
                            background: rgba(255, 130, 163, .1);
                            color: #FF82A3;
                        }

                        &.success {
                            background: rgba(48, 193, 71, .1);
                            color: #30C147;
                        }
                    }
                }
            }

            .meta {
                margin-top: 28rpx;
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;

                .meta-item {
                    // width: 33.33%;
                    margin-bottom: 18rpx;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .label {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: rgba(0, 0, 0, 0.5);
                    }

                    .value {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: #000;
                    }
                }
            }
        }
    }
}
</style>