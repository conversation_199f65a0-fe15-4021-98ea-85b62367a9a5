<template>
    <view class="close-popup">
        <view class="header">
            <text class="title">止盈止损</text>
            <image class="close" @click="$emit('close')"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377388318231715840.png" />
        </view>

        <view class="info">
            <view class="row">
                <text>合约</text>
                <text style="color: #FF82A3;">{{ contractInfo }}</text>
            </view>
            <view class="row">
                <text>开仓均价(USDT)</text>
                <text>{{ order.avgPrice }}</text>
            </view>
            <!-- <view class="row">
                <text>标记价格</text>
                <text>{{ order.markPrice }}</text>
            </view> -->
            <view class="row">
                <text>收益率</text>
                <text :class="{ red: rate < 0, green: rate >= 0 }">{{ rate
                    }}</text>
            </view>
        </view>

        <view class="form">
            <view class="input-group">
                <view class="label">
                    <text>止盈(USDT)</text>
                    <text> </text>
                </view>
                <view class="input-box">
                    <view class="left">
                        <!-- <text v-if="orderType == 'MARKET'">{{ order.price || '--' }}</text>
                         v-if="orderType == 'LIMIT'" -->
                        <u-input placeholder="请输入止盈价格" :height="80" class="input-price" :clearable="false"
                            v-model="takeProfitPrice" type="digit" maxlength="20" @input="validateTakeProfit" />
                        <text>USDT</text>
                    </view>


                    <!-- <input class="input" disabled :value="order.price || '--'" /> -->
                    <!-- <text class="unit">USDT</text> -->
                    <!-- <view @click="showmodePopup = true" class="type flex_all">{{ orderType == 'MARKET' ? '市价委托' : '限价委托'
                        }}</view> -->
                </view>
                <view class="tip">
                    当最新价格触达{{ takeProfitPrice || '0.00' }}时，将会触发市价止盈委托平仓当前仓位。预期盈亏为
                    <text style="color: #30C147;">{{ calculateTakeProfitPnL() }}</text>USDT。
                </view>
            </view>

            <view class="input-group">
                <text class="label">止损(USDT)</text>
                <view class="input-box">
                    <view class="left">

                        <input :height="80" class="input-price" v-model="stopLossPrice" placeholder="请输入止损价格"
                            type="digit" @input="validateStopLoss" />
                        <text>USDT</text>
                    </view>
                    <!-- @click="changeNumPopup = true" -->
                    <!-- <view class="unit">{{ formattedNumTypes(NumTypesvalue) }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384611272942247936.png" />
                    </view> -->
                </view>
                <view class="tip">
                    当最新价格触达{{ stopLossPrice || '0.00' }}时，将会触发市价止损委托平仓当前仓位。预期盈亏为
                    <text style="color: #FF82A3;">{{ calculateStopLossPnL() }}</text>USDT。
                </view>
            </view>
            <!-- 
            <view class="slider">
                <u-slider max="100" @end="endMove" v-model="sliderValue" inactive-color="rgba(231, 231, 231, .6)"
                    :active-color="tradeType == 'buy' ? 'rgba(48, 193, 71, .6)' : 'rgba(255, 130, 163, .6)'" step="1"
                    :levelList="levelList" :tradeType="tradeType" :use-slot="true">
                </u-slider>
                <view class="slidelevel">
                    <text v-for="(item, index) in levelList"
                        :style="{ color: currentfff >= index ? '#FF82A3' : 'rgba(0, 0, 0, .6)' }" :key="index">{{ item
                        }}
                    </text>
                </view>
            </view> -->

            <!-- <view class="summarys">
                <view class="summary">
                    <text>仓位数量</text>
                    <text>{{ order.volume }}张</text>
                </view>
                <view class="summary">
                    <text>预计盈亏</text>
                    <text class="pnl">{{ pnl }}USDT</text>
                </view>
            </view> -->
        </view>

        <view class="btns">
            <u-button hover-class="none" class="btn flex_all" @click="handleConfirm">确定</u-button>
        </view>

        <!-- 数量单位选择 -->
        <numunitPopup :show.sync="changeNumPopup" :options="NumTypes" @select="handleSelectNum" />



        <!-- 市价，限价，计划委托--委托模式选择 -->
        <ordermodePopup :show.sync="showmodePopup" @select="handleSelect" />
    </view>
</template>

<script>
import numunitPopup from './changenum'
import ordermodePopup from './mode'

import slider from "../components/slider"
export default {
    name: "ClosePositionPopup",
    components: {
        numunitPopup,
        slider,
        ordermodePopup
    },
    props: {
        order: {
            type: Object,
            default: () => ({})
        },
        rate: {
            type: String,
            default: 0
        },
        pnl: {
            type: String,
            default: 0
        },
    },
    watch: {
        sliderValue(newVal) {
            // 避免在手动输入时触发循环计算
            // if (!this.isManualInput) {
            // this.calculateOrderVolume();
            // }
        },
    },
    data() {
        return {
            contractInfo: "",
            orderprice: "",
            orderType: "MARKET",
            showmodePopup: false,
            levelList: ["0%", "20%", "40%", "60%", "80%", "100%"],
            tradeType: 'sell',
            sliderValue: "",
            NumTypesvalue: 'CONTRACT',
            NumTypes: [
                { label: '张', value: 'CONTRACT' },
                { label: 'USDT', value: 'QUOTE' },
                { label: 'BTC', value: 'BASE' }
            ],
            changeNumPopup: false,
            amount: '',
            percent: 0,
            percents: [0, 20, 40, 60, 80, 100],
            currentfff: 0,
            // 止盈止损相关
            takeProfitPrice: '', // 止盈价格
            stopLossPrice: ''    // 止损价格
        };
    },
    mounted() {
        console.log(this.order);
        let symbol = uni.getStorageSync('currentContract').symbol
        this.contractInfo = symbol + '永续·' + (this.order.side == 'BUY' ? '多·' : '空·') + this.order.leverageLevel + 'X';
        this.takeProfitPrice = this.order.otoProfitPrice || '';
        this.stopLossPrice = this.order.otoLossPrice || '';
    },
    methods: {
        handleSelect(item) {
            if (item) {
                this.orderType = item.value
            }
        },
        endMove(e) {
            if (e == 0) {
                this.currentfff = 0
            } else if (e == 20) {
                this.currentfff = 1
            } else if (e == 40) {
                this.currentfff = 2
            } else if (e == 65) {
                this.currentfff = 3
            } else if (e == 80) {
                this.currentfff = 4
            } else if (e == 100) {
                this.currentfff = 5
            }

            // 滑块结束移动时重新计算订单数量
            // this.calculateOrderVolume();
        },
        formattedNumTypes(e) {
            return this.NumTypes.find(item => item.value == e)?.label || ''
        },

        // 校验止盈价格
        validateTakeProfit(value) {
            this.takeProfitPrice = value;

            if (!value) {
                return;
            }

            let line = uni.getStorageSync('CurrentContract')
            const price = Number(line.closePrice || 1);
            const inputPrice = Number(value);

            if (this.order.side === 'BUY' && inputPrice <= price) {
                uni.showToast({
                    title: '多头止盈价格应该大于开仓价格',
                    icon: 'none'
                });
                this.takeProfitPrice = '';
            } else if (this.order.side === 'SELL' && inputPrice >= price) {
                uni.showToast({
                    title: '空头止盈价格应该小于开仓价格',
                    icon: 'none'
                });
                this.takeProfitPrice = '';
            }
        },

        // 校验止损价格
        validateStopLoss(value) {
            this.stopLossPrice = value.detail ? value.detail.value : value;

            if (!this.stopLossPrice) {
                return;
            }

            // const price = Number(this.order.price || 0);

            let line = uni.getStorageSync('CurrentContract')
            const price = Number(line.closePrice || 1);
            const inputPrice = Number(this.stopLossPrice);
            console.log(inputPrice, price);

            if (this.order.side === 'BUY' && inputPrice >= price) {
                uni.showToast({
                    title: '多头止损价格应该小于开仓价格',
                    icon: 'none'
                });
                this.stopLossPrice = '';
            } else if (this.order.side === 'SELL' && inputPrice <= price) {
                uni.showToast({
                    title: '空头止损价格应该大于开仓价格',
                    icon: 'none'
                });
                this.stopLossPrice = '';
            }
        },

        // 提交前校验
        validateBeforeSubmit() {
            if (!this.takeProfitPrice && !this.stopLossPrice) {
                uni.showToast({
                    title: '请至少设置止盈或止损价格',
                    icon: 'none'
                });
                return false;
            }
            return true;
        },

        // 计算止盈预期盈亏
        calculateTakeProfitPnL() {
            if (!this.takeProfitPrice || !this.order.volume || !this.order.multiplier) {
                return '0.00';
            }

            // const currentPrice = Number(this.order.price || 0);

            let line = uni.getStorageSync('CurrentContract')
            const currentPrice = Number(line.closePrice || 1);
            const takeProfitPrice = Number(this.takeProfitPrice);
            const volume = Number(this.order.volume);
            const multiplier = Number(this.order.multiplier || 1);

            let pnl = 0;
            if (this.order.side === 'BUY') {
                // 多头：(止盈价 - 开仓价) * 张数 * 面值
                pnl = (takeProfitPrice - currentPrice) * volume * multiplier;
            } else if (this.order.side === 'SELL') {
                // 空头：(开仓价 - 止盈价) * 张数 * 面值
                pnl = (currentPrice - takeProfitPrice) * volume * multiplier;
            }

            return pnl > 0 ? `+${pnl.toFixed(2)}` : pnl.toFixed(2);
        },

        // 计算止损预期盈亏
        calculateStopLossPnL() {
            if (!this.stopLossPrice || !this.order.volume || !this.order.multiplier) {
                return '0.00';
            }

            // const currentPrice = Number(this.order.price || 0);
            let line = uni.getStorageSync('CurrentContract')
            const currentPrice = Number(line.closePrice || 1);
            const stopLossPrice = Number(this.stopLossPrice);
            const volume = Number(this.order.volume);
            const multiplier = Number(this.order.multiplier || 1);

            let pnl = 0;
            if (this.order.side === 'BUY') {
                // 多头：(止损价 - 开仓价) * 张数 * 面值
                pnl = (stopLossPrice - currentPrice) * volume * multiplier;
            } else if (this.order.side === 'SELL') {
                // 空头：(开仓价 - 止损价) * 张数 * 面值
                pnl = (currentPrice - stopLossPrice) * volume * multiplier;
            }

            return pnl > 0 ? `+${pnl.toFixed(2)}` : pnl.toFixed(2);
        },

        formatPercent(value) {
            return (value * 100).toFixed(2) + "%";
        },
        handlePercent(p) {
            this.percent = p;
            this.amount = Math.floor((this.order.holdAmount || 0) * (p / 100));
        },
        async handleConfirm() {
            // 提交前校验
            if (!this.validateBeforeSubmit()) {
                return;
            }

            let res = await this.$api.otoPosition({
                positionId: this.order.positionId,
                takeProfitPrice: this.takeProfitPrice || "",
                stopLossPrice: this.stopLossPrice || "",
                price: this.order.price,
                volume: this.order.volume
            })
            if (res.code == 200) {
                uni.showToast({
                    title: '止盈止损设置成功',
                    icon: 'none'
                });
                this.$emit('confirm', true);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        handleSelectNum(item) {
            // this.ordervolume = ''
            // this.sliderValue = 0
            this.NumTypesvalue = item.value
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-slider__button {
    z-index: 9999999 !important;
    width: 20rpx !important;
    height: 20rpx !important;
    background: #ffffff;
    // border: 2rpx solid #DDDDDD;
    border-radius: 50%;
    border: 4rpx solid #FF82A3;

}

.close-popup {
    padding: 40rpx 32rpx 100rpx 32rpx;
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 30rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }

        .close {
            width: 48rpx;
            height: 48rpx;
        }
    }

    .info {
        margin: 28rpx 0 40rpx 0;
        padding-bottom: 28rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .row {
            display: flex;
            justify-content: space-between;
            // margin-bottom: 12rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;
            margin-bottom: 24rpx;

            &:last-child {
                margin-bottom: 0;
            }

            text {
                &:nth-of-type(1) {
                    color: rgba(0, 0, 0, .5);
                }

                &:nth-of-type(2) {
                    color: #000;
                }

            }

            .red {
                color: #FF82A3;
            }

            .green {
                color: #08B819;
            }
        }
    }

    .form {
        .input-group {
            margin-bottom: 24rpx;

            .label {
                margin-bottom: 16rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 34rpx;

                text {
                    &:last-child {
                        font-weight: 400;

                    }
                }

            }

            .input-box-num {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: #f6f6f6;
                height: 80rpx;
                border-radius: 8rpx;
                padding: 0 24rpx 0 20rpx;

                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }

                input {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: #000;
                }

                .unit {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                    display: flex;
                    align-items: center;
                    gap: 12rpx;

                    image {
                        width: 14rpx;
                        height: 10rpx;
                    }

                    &.rotated {
                        transform: rotate(180deg);
                    }
                }
            }

            .tip {
                margin-top: 20rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 40rpx;
                color: #00000080;
            }

            .input-box {
                display: flex;
                align-items: center;
                gap: 16rpx;


                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }

                ::v-deep input {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx !important;
                    line-height: 40rpx;
                    color: #000;
                }

                .left {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    // width: 538rpx;
                    width: 100%;
                    height: 80rpx;
                    background: #f6f6f6;
                    padding: 0 20rpx;
                    border-radius: 8rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: rgba(0, 0, 0, .5);
                }

                // .input {
                //     flex: 1;
                //     font-size: 28rpx;
                // }

                .type {
                    height: 80rpx;

                    background: #f6f6f6;
                    padding: 0 20rpx;
                    border-radius: 8rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }
            }
        }

        .slider {
            // display: flex;
            // justify-content: space-between;
            // margin: 24rpx 0;

            .slidelevel {
                // margin-top: 26rpx;
                margin: 17rpx 0 0 0;
                width: 100%;
                display: flex;
                justify-content: space-between;
                flex: 1;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;


                text {
                    display: block;
                    width: 50rpx;
                    // margin: 0 10rpx 0 10rpx;
                }
            }
        }

        .summarys {
            margin-top: 44rpx;

            .summary {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 28rpx;
                margin-bottom: 24rpx;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;


                text {
                    &:nth-of-type(1) {
                        color: rgba(0, 0, 0, .5);
                    }

                    &:nth-of-type(2) {
                        // color: rgba(0, 0, 0, 1);
                    }
                }

                .pnl {
                    color: #FF82A3;
                }
            }
        }


    }

    .btns {
        width: 100%;
        margin-top: 40rpx;

        .btn {
            background: #FF82A3;
            color: white;
            border-radius: 110rpx;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;

        }
    }

}
</style>