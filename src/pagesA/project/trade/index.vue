<template>
    <view class="trade-page">
        <view class="barHeight"></view>

        <!-- 顶部信息 -->
        <view class="header">
            <view class="symbol">
                <image @click="drawerShow = true" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375530215441784832.png" />
                <view class="name">BTC/USDT</view>
                <view class="change-rate" style="color: #30C147;">+5.07%</view>
                <!-- FF82A3 -->
            </view>

            <view class="candle">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375530604681584640.png" @click="nav_to('detail')" />
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375530755789774848.png" @click="open_more" />
            </view>
        </view>

        <view class="main-content">
            <!-- 左侧盘口区域 -->
            <depths />

            <!-- 右侧下单区域 -->
            <makeorder @submitorder="submitorder" />
        </view>

        <!-- 底部导航 -->
        <position ref="positions" />

        <!-- 币对列表 -->
        <marketDrawerPopup :show.sync="drawerShow" />

        <morepopup :show.sync="moreshow" />

        <!-- <BottomTabBar v-model="tabIndex" @change="onTabChange" /> -->

    </view>
</template>

<script>
import BottomTabBar from "../../../components/public/BottomTabBar"
import depths from "./components/depth"
import makeorder from "./components/makeorder"
import position from "./components/position"
import marketDrawerPopup from "./popup/marketDrawer"
import morepopup from "./popup/more"
export default {
    components: {
        depths,
        makeorder,
        position,
        marketDrawerPopup,
        BottomTabBar,
        morepopup
    },
    data() {
        return {
            moreshow: false,
            tabIndex: 2,
            drawerShow: false,
            animatedOrders: [],

        }
    },

    mounted() {
    },
    // onReachBottom() {
    // },
    methods: {
        open_more() {
            console.log(123);
            
            this.moreshow = true
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            });
        },
        onBottom() {
            console.log('onBottom');
            this.$refs.positions.rearchBottom()
        },
        submitorder(orderNo, side) {
            this.$refs.positions.fetchPositions(orderNo, side)
        },
        animateOrders() {
            let i = 0
            this.animatedOrders = []
            const interval = setInterval(() => {
                if (i >= this.orders.length) {
                    clearInterval(interval)
                } else {
                    this.animatedOrders.push(this.orders[i])
                    i++
                }
            }, 100)
        }
    }
}
</script>

<style scoped lang="scss">
.trade-page {
    min-height: 100vh;
    padding: 0 32rpx;

    .header {
        margin-top: 28rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 6rpx;

        .symbol {
            display: flex;
            align-items: center;

            image {
                width: 24rpx;
                height: 20rpx;
            }

            .name {
                margin: 0 16rpx 0 24rpx;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }

            .change-rate {
                // color: #16c784;
                // color: #FF82A3;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
            }

        }


        .candle {
            display: flex;
            align-items: center;
            gap: 28rpx;

            image {
                width: 52rpx;
                height: 50rpx;
            }
        }
    }

    .main-content {
        display: flex;
        gap: 20rpx;
        margin-top: 20rpx;
    }






    .no-data {
        margin-top: 60rpx;
        text-align: center;
        color: #999;
        font-size: 26rpx;

        .no-data-img {
            width: 160rpx;
            height: 160rpx;
            margin-bottom: 20rpx;
        }
    }
}
</style>