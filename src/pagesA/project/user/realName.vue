<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back"></u-navbar>
		<u-line-progress :percent="20" :show-percent="false" active-color="#F97C9A" inactive-color="#F3F3F3"
			:height="6"></u-line-progress>
		<CountrySelect :show.sync="show" @select="onCountrySelect" :type="selectType"></CountrySelect>
		<view class="form">
			<view class="form_item">
				<view class="form_label">居住国家/地区</view>
				<view class="select_box" @click="showCountrySelect('residence')">
					<image :src="formData.residence.icon" class="country_flag"
						mode="widthFix" />
					<text>{{ formData.residence.label }}</text>
				</view>
			</view>
			<view class="form_item">
				<view class="form_label">签证签发国家/地区</view>
				<view class="select_box" @click="showCountrySelect('visa')">
					<image :src="formData.visa.icon" class="country_flag"
						mode="widthFix" />
					<text>{{ formData.visa.label }}</text>
				</view>
			</view>
			<view class="form_item">
				<view class="form_label">证件类型</view>
				<view class="card_group">
					<view :class="['card_option', formData.idType==='0' ? 'card_option_active' : '']"
						@click="formData.idType='0'">
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385638829280026624.png" size="36"
							class="icon_id" />
						<text class="card_text">身份证</text>
						<text class="tag_recommend" v-if="formData.idType==='0'">推荐</text>
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639301734817792.png" size="32"
							v-if="formData.idType==='0'" class="icon_check" />
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639554466799616.png" size="32" v-else
							class="icon_check" />
					</view>

					<view :class="['card_option', formData.idType==='1' ? 'card_option_active' : '']"
						@click="formData.idType='1'">
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385638868211556352.png" size="36"
							class="icon_id" />
						<text class="card_text">护照</text>
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639301734817792.png" size="32"
							v-if="formData.idType==='1'" class="icon_check" />
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639554466799616.png" size="32" v-else
							class="icon_check" />
					</view>
					
					<view :class="['card_option', formData.idType==='2' ? 'card_option_active' : '']"
						@click="formData.idType='2'">
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385691256632991744.png" size="36"
							class="icon_id" />
						<text class="card_text">其他</text>
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639301734817792.png" size="32"
							v-if="formData.idType==='2'" class="icon_check" />
						<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385639554466799616.png" size="32" v-else
							class="icon_check" />
					</view>
				</view>
			</view>
			<view class="btn_box">
				<u-button class="btn_continue" hover-class="none" type="primary" shape="circle"
					@click="onContinue">继续</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import CountrySelect from '@/components/select/CountrySelect.vue'
	export default {
		components: {
			CountrySelect
		},
		data() {
			return {
				title: '身份认证',
				show: false,
				formData: {
					residence: {
						label: '中国',
						code: 'CN',
						icon:'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385640135935746048.png'
					},
					visa: {
						label: '中国',
						code: 'CN',
						icon:'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385640135935746048.png'
					},
					idType: '0',
				},
				selectType: ''
			}
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			showCountrySelect(type) {
				this.selectType = type
				this.show = true
			},
			onCountrySelect(item) {
				if (this.selectType === 'residence') {
					this.formData.residence = item
				} else {
					this.formData.visa = item
				}
				this.show = false
			},

			onContinue() {
				// 继续按钮逻辑
				let realNameInfo = {
					country: this.formData.residence.label,
					visaCountry: this.formData.visa.label,
					documentType: this.formData.idType,
				}
				uni.setStorageSync('realNameInfo', realNameInfo)
				this.$Router.push({
					name: 'realNameImg'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.body {
		background: #fff;
		min-height: 100vh;
		padding: 0 32rpx;
		font-family: PingFang SC;
		padding-top: 30rpx;
	}

	.form {
		margin-top: 32rpx;
	}

	.form_item {
		margin-bottom: 32rpx;
	}

	.form_label {
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 16rpx;
	}

	.select_box {
		background: #f8f8f8;
		border-radius: 16rpx;
		padding: 0 24rpx;
		min-height: 88rpx;
		align-items: center;
		display: flex;
	}

	.card_group {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.card_option {
		display: flex;
		align-items: center;
		border: 2rpx solid #f3f3f3;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		background: #fff;
		position: relative;
		height: 120rpx;
	}

	.card_option_active {
		border-color: #F97C9A;
		background: #fff6f8;
	}

	.icon_id {
		margin-right: 20rpx;
	}

	.card_text {
		font-size: 30rpx;
		font-weight: 500;
	}

	.tag_recommend {
		background: #F97C9A;
		color: #fff;
		font-size: 20rpx;
		border-radius: 8rpx;
		padding: 2rpx 12rpx;
		margin-left: 16rpx;
	}

	.icon_check {
		position: absolute;
		right: 32rpx;
		top: 50%;
		transform: translateY(-50%);
	}

	.btn_box {
		margin-top: 80rpx;
		display: flex;
		justify-content: center;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 32rpx;
	}

	.btn_continue {
		width: 90vw;
		height: 88rpx;
		font-size: 32rpx;
		background: #F97C9A;
		border-radius: 44rpx;
		color: #fff;
		font-weight: 500;
	}

	.country_flag {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-right: 24rpx;
		background: #f5f5f5;
	}
</style>