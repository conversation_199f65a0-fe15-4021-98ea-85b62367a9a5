<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="container">
			<view class="top_desc">
				为保障您的资产安全，更改或解绑邮箱后，该账户将禁用支付服务、提币及C2C卖币24小时
			</view>

			<view class="form_item">
				<view class="item_title">新邮箱</view>
				<view class="input_wrapper">
					<u-input v-model="newEmail" type="text" placeholder="请输入新邮箱地址"></u-input>
				</view>
			</view>

			<view class="form_item">
				<view class="item_title">填写验证码</view>
				<view class="input_wrapper">
					<u-input v-model="newCode" type="number" :maxlength="6" placeholder="请输入验证码"></u-input>
					<text class="code_tip" :class="{'active': codeTips === '获取验证码' || codeTips === '重新获取'}"
						@click="getCode">{{ codeTips }}</text>
				</view>
			</view>

			<u-verification-code ref="uCode" @change="codeChange" :seconds="60"></u-verification-code>
		</view>

		<view class="bottom_button">
			<u-button @click="submit" :custom-style="customStyle">提交</u-button>
		</view>

		<!-- 成功提示弹窗 -->
		<u-popup v-model="showSuccessPopup" mode="bottom" border-radius="20" :min-height="600" :closeable="true"
			@close="closePopup">
			<view class="popup_content">
				<view class="popup_title">邮箱更改成功</view>
				<view class="popup_desc">您的邮箱地址已修改，下次登录请使用新的邮箱地址</view>
				<view class="popup_button">
					<u-button @click="closePopup" :custom-style="customStyle">好的</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '更改邮箱',
				newEmail: '',
				newCode: '',
				codeTips: '',
				showSuccessPopup: false,
				customStyle: {
					backgroundColor: '#FF82A3',
					color: '#FFFFFF',
					borderRadius: '48rpx',
					height: '96rpx',
					fontSize: '30rpx'
				},
				email: '',
				code: '',
			}
		},
		onReady() {
			this.$refs.uCode.startText = '获取验证码';
		},
		onLoad(options) {
			this.email = options.email
			this.code = options.code
		},
		methods: {
			// 返回上一页
			back() {
				uni.navigateBack()
			},
			codeChange(text) {
				this.codeTips = text;
			},
			getCode() {
				if (!this.newEmail) {
					uni.showToast({
						title: '请输入新邮箱地址',
						icon: 'none'
					});
					return;
				}

				if (this.$refs.uCode.canGetCode) {
					// 模拟向服务器请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '验证码已发送',
							icon: 'none'
						});
						this.sendEmail()
						this.$refs.uCode.start();
					}, 1000);
				} else {
					uni.showToast({
						title: '倒计时结束后再发送',
						icon: 'none'
					});
				}
			},
			submit() {
				if (!this.newEmail || !this.newCode) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					})
					return
				}
				console.log('提交新邮箱:', this.newEmail, '验证码:', this.newCode);
				// 模拟提交成功
				this.submitEmail()

			},
			closePopup() {
				this.showSuccessPopup = false;
				// 更改成功后，返回上一页
				setTimeout(() => {
					this.$Router.push({
						name: "email",
					})
				}, 300); // 延迟执行，让弹窗关闭动画更流畅
			},
			async sendEmail() {
				let res = await this.$api.sendEmailCode({
					email: this.newEmail
				})
			},
			async submitEmail() {
				let res = await this.$api.updateEmail({
					oldEmail: this.email,
					oldEmailCaptcha: this.code,
					newEmail: this.newEmail,
					newEmailCaptcha: this.newCode
				})
				if (res.code == 200) {
					this.showSuccessPopup = true;
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.body {
		background: #fff;
		min-height: 100vh;
		font-family: PingFang SC;
		display: flex;
		flex-direction: column;
	}

	.container {
		padding: 30rpx;
		flex: 1;

		.top_desc {
			font-size: 26rpx;
			color: #999;
			line-height: 1.6;
			margin-top: 20rpx;
			margin-bottom: 60rpx;
		}

		.form_item {
			margin-bottom: 50rpx;

			.item_title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 20rpx;
			}

			.input_wrapper {
				position: relative;
				background-color: #f7f8fa;
				border-radius: 16rpx;

				::v-deep .u-input {
					background-color: transparent;
					padding: 12rpx 24rpx !important;
				}

				::v-deep .u-input__input {
					font-size: 32rpx;
					width: 60%;
				}

				.code_tip {
					position: absolute;
					right: 24rpx;
					top: 50%;
					transform: translateY(-50%);
					font-size: 28rpx;
					color: #999;

					&.active {
						color: #FF82A3;
					}
				}
			}
		}
	}

	.bottom_button {
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		background-color: #fff;

		.u-button::after {
			border: none;
		}
	}

	.popup_content {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.popup_title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			text-align: left;
			width: 100%;
			margin-bottom: 40rpx;
		}

		.popup_desc {
			font-size: 28rpx;
			color: #666;
			text-align: left;
			line-height: 1.6;
			margin-bottom: 120rpx;
			width: 100%;
		}

		.popup_button {
			width: 100%;
		}
	}

	::v-deep .u-close {
		top: 40rpx !important;
		right: 40rpx !important;
	}
</style>