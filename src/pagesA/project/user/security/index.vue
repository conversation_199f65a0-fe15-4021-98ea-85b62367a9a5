<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back"></u-navbar>
		
		<!-- 双重验证部分 -->
		<view class="section_header">
			<view class="section_title">双重验证（2FA）</view>
			<view class="section_desc">为保障账户安全，请至少启用两种双重身份验证方式。</view>
		</view>
		
		<!-- 验证方式列表 -->
		<view class="list_container">
			<u-cell-group :border="false">
				<u-cell-item 
					v-for="(item, index) in verifyList" 
					:key="index"
					:title="item.title"
					:arrow="true"
					:border-bottom="index !== verifyList.length - 1"
					hover-class="cell_hover"
					@click="handleVerifyClick(item.type)"
				>
					<u-icon slot="icon" :name="item.icon" size="36" class="custom_icon"></u-icon>
				</u-cell-item>
			</u-cell-group>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '账户安全',
			verifyList: [
				// { icon: 'scan', title: 'Face ID', type: 'face',name:'/pagesA/project/user/security/face' },
				// { icon: 'star', title: '谷歌身份验证', type: 'google',path:'/pagesA/project/user/security/google' },
				{ icon: 'email', title: '邮箱', type: 'email',path:'/pagesA/project/user/security/email' },
				{ icon: 'lock', title: '密码', type: 'password',path:'/pagesA/project/user/security/password' },
				{ icon: 'coupon', title: '支付PIN码', type: 'pinCode',path:'/pagesA/project/user/security/pinCode' }
			]
		}
	},
	onLoad() {
		this.getUserInfos()
	},
	methods: {
		// 返回上一页
		back() {
			uni.navigateBack()
		},
		// 处理验证方式点击
		handleVerifyClick(type) {
			const routes = {
				face: '/pagesA/project/user/security/face',
				google: '/pagesA/project/user/security/google',
				email: '/pagesA/project/user/security/email',
				password: '/pagesA/project/user/security/password',
				pinCode: '/pagesA/project/user/security/pinCode'
			}
			
			if (routes[type]) {
				uni.navigateTo({
					url: routes[type]
				})
			}
		},
		async getUserInfos() {
			let res = await this.$api.getUserInfo()
			console.log(res, 123);

			if (res.code == 200) {
				this.userInfo = res.result
				this.qrcodeUrl = res.result.email
			}

		},
	}
}
</script>

<style lang="scss" scoped>
.body {
	min-height: 100vh;
	padding-bottom: 120rpx;
	font-family: PingFang SC;
	padding-top: 60rpx;
}
 
.section_header {
	padding: 30rpx 30rpx 0 30rpx;
	
	.section_title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.section_desc {
		font-size: 26rpx;
		color: #999;
	}
}

.list_container {
	margin: 24rpx 30rpx 40rpx 30rpx;
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	
	::v-deep .u-cell {
		border:1px solid rgba(0,0,0,0.1);
		border-radius:16rpx;
		margin-bottom: 20rpx;
		height:120rpx;
		line-height: 120rpx;
		&::after {
			border-color: #f4f4f4;
			left: 100rpx;
		}
	}
	
	::v-deep .u-cell_title {
		margin-left: 20rpx;
		color: #333;
		font-size: 28rpx;
	}
	
	::v-deep .u-icon__icon {
		color: #333 !important;
		font-weight: 500 !important;
	}
	
	.custom_icon {
		color: #333;
	}
}

.cell_hover {
	background-color: #f8f8f8;
}
</style>
