<template>
	<view class="body">
        <u-navbar :border-bottom="false" :title="title" :customBack="back"></u-navbar>
		<view class="container">
			<view class="email_item">
				<view class="item_left">
					<u-icon name="email" size="44"></u-icon>
					<view class="info">
						<view class="email_text">{{ userInfo.email }}</view>
						<view class="date_text">{{ emailInfo.date }}</view>
					</view>
				</view>
				<view class="item_right" @click="openConfirmPopup">
					<u-icon name="edit-pen" size="36" color="#999"></u-icon>
				</view>
			</view>
		</view>
		<!-- 确认弹窗 -->
		<u-popup v-model="showConfirmPopup" mode="bottom" border-radius="20" :closeable="true">
			<view class="popup_content">
				<view class="popup_title">您确定要更改邮箱地址吗？</view>
				<view class="rule_list">
					<view class="rule_item">
						<view class="rule_text">为保障您的资产安全，更改邮箱后，提现和C2C交易将禁用<text>24</text>小时</view>
					</view>
					<view class="rule_item">
						<view class="rule_text">旧邮箱地址在更新后的<text>30</text>天内无法用于重新注册</view>
					</view>
				</view>
				<view class="popup_footer">
					<u-button class="cancel_btn" @click="closeConfirmPopup">取消</u-button>
					<u-button class="confirm_btn" @click="continueToUpdate">继续</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template> 

<script> 
export default {
    data() {
        return {
            title: '邮箱验证', 
			showConfirmPopup: false,
			emailInfo: { 
				address: '11***@gmail.com',
				date: '添加于: 2025年5月27日'
			},
			userInfo:{}
        }
    }, 
	onLoad(){
		this.getUserInfos()
	},
    methods: {
        // 返回上一页
		back() {
			uni.navigateBack()
		},
		openConfirmPopup() {
			this.showConfirmPopup = true;
		},
		closeConfirmPopup() {
			this.showConfirmPopup = false;
		},
		continueToUpdate() {
			this.closeConfirmPopup();
			setTimeout(() => {
				this.$Router.push({
					name:"emailVerify",
					params:{
						email:this.userInfo.email
					}
				})
			}, 200);
		},
		async getUserInfos() {
				let res = await this.$api.getUserInfo()
				if (res.code == 200) {
					this.userInfo = res.result
				}

			},
    }
}
</script>
 
<style lang="scss" scoped>
.body {
	background: #fff;
	min-height: 100vh;
	font-family: PingFang SC;
}

.container {
	padding: 30rpx;
}

.email_item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
}

.item_left {
	display: flex;
	align-items: center;
	
	.info {
		margin-left: 24rpx;
		
		.email_text {
			font-size: 30rpx;
			color: #333;
			font-weight: 500;
		}
		
		.date_text {
			font-size: 24rpx;
			color: #999;
			margin-top: 12rpx;
		}
	}
}

.popup_content {
	padding: 40rpx;
	
	.popup_title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		text-align: left;
		margin-bottom: 40rpx;
	}

	.rule_list {
		.rule_item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 40rpx;

			.icon_wrapper {
				margin-right: 16rpx;
				margin-top: 4rpx;

				.icon_circle {
					width: 32rpx;
					height: 32rpx;
					border: 1px solid #ccc;
					border-radius: 50%;
				}
			}
			
			.rule_text {
				flex: 1;
				font-size: 24rpx;
				color: #999899;
				line-height: 1.6;
				text{
					color: #FF82A3;
				}
			}
		}
	}
	
	.popup_footer {
		display: flex;
		justify-content: space-between;
		gap: 20rpx;
		margin-top: 60rpx;
		width: 100%;
		button {
			width:48%;
			height: 96rpx;
			border-radius: 48rpx;
			font-size: 30rpx;
			&::after {
				border: none;
			}
		}

		.cancel_btn {
			background-color: #fff;
			color: #FF82A3;
			border: 1px solid #FF82A3;
		}

		.confirm_btn {
			background-color: #FF82A3;
			color: #fff;
		}
	}
}

::v-deep .u-close {
    top: 40rpx !important;
    right: 40rpx !important;
}
</style>
