<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="container">
			<view class="top_desc">请输入{{ userInfo.email }}收到的6位验证码</view>

			<view class="form_item">
				<view class="item_title">邮箱验证码</view>
				<view class="input_wrapper">
					<u-input v-model="code" type="number" :maxlength="6" placeholder=" "></u-input>
					<text class="code_tip" @click="getCode"
						:class="{'active': codeTips === '获取验证码'||codeTips === '重新获取'}">{{ codeTips }}</text>
				</view>
			</view>

			<u-verification-code ref="uCode" @change="codeChange" :seconds="60"></u-verification-code>
		</view>

		<view class="bottom_button">
			<u-button @click="submit" :custom-style="customStyle">提交</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '邮箱验证',
				code: '',
				codeTips: '',
				customStyle: {
					backgroundColor: '#FF82A3',
					color: '#FFFFFF',
					borderRadius: '48rpx',
					height: '96rpx',
					fontSize: '30rpx'
				},
				userInfo:{}
			}
		},
		onReady() {
			this.$refs.uCode.startText = '获取验证码';
		},
		onLoad(options) {
			this.getUserInfos()
		},
		methods: {
			// 返回上一页
			back() {
				uni.navigateBack()
			},
			codeChange(text) {
				this.codeTips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向服务器请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '验证码已发送',
							icon: 'none'
						});
						// 通知验证码组件内部开始倒计时
						this.sendEmail()
						this.$refs.uCode.start();
					}, 1000);
				} else {
					uni.showToast({
						title: '倒计时结束后再发送',
						icon: 'none'
					});
				}
			},
			submit() {
				if (this.code.length !== 6) {
					uni.showToast({
						title: '请输入6位验证码',
						icon: 'none'
					})
					return
				}
				console.log('提交验证码', this.code);
				this.submitEmail()
				// 此处可添加提交到后端的逻辑
			},
			async getUserInfos() {
				let res = await this.$api.getUserInfo()
				if (res.code == 200) {
					this.userInfo = res.result
				}
			},
			async sendEmail() {
				let res = await this.$api.sendEmailCode({
					email: this.userInfo.email
				})
			},
			async submitEmail() {
				let res = await this.$api.emailCodeCompare({
					email: this.userInfo.email,
					code: this.code 
				})
				console.log(res)
				if (res.code == 200) {
					this.$Router.push({
						name: "passwordUpdata",
					})
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.body {
		background: #fff;
		min-height: 100vh;
		font-family: PingFang SC;
		display: flex;
		flex-direction: column;
	}

	.container {
		padding: 30rpx;
		flex: 1;

		.top_desc {
			font-size: 28rpx;
			color: #999;
			text-align: center;
			margin-top: 40rpx;
			margin-bottom: 80rpx;
		}

		.form_item {
			.item_title {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 20rpx;
			}

			.input_wrapper {
				position: relative;
				background-color: #f7f8fa;
				border-radius: 16rpx;
			}

			::v-deep .u-input {

				padding: 12rpx 24rpx !important;
				width: 60%;
			}

			::v-deep .u-input__input {
				font-size: 32rpx;
			}

			.code_tip {
				position: absolute;
				right: 24rpx;
				top: 50%;
				transform: translateY(-50%);
				font-size: 28rpx;
				color: #999;

				&.active {
					color: #FF82A3;
				}
			}
		}
	}

	.bottom_button {
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		background-color: #fff;

		.u-button::after {
			border: none;
		}
	}
</style>