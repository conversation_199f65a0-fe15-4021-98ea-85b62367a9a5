<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back"></u-navbar>
		<!-- 头像、昵称、ID、认证标识 -->
		 <view class="user_info_box">
			<view class="user_info">
				<image class="avatar" :src="avatar" mode="aspectFill" />
				<view class="user_name">{{ name }}</view>
				<view class="user_id">ID: {{ userId }}</view>
				<view class="cert_tag">
					<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385685463783268352.png" color="#4BC07D" size="24" />
					<text>标准身份认证</text>
				</view>
			</view>c
	
			<!-- 升级提示 -->
			<view class="upgrade_tip"> 
				升级认证等级以将您的法币限额提升至2M USD每日
				<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385685463783268352.png" color="#4BC07D" size="24" />
			</view>
			<u-button
				type="primary"
				class="upgrade_btn"
				:custom-style="{background:'#F97C9A',borderRadius:'50rpx'}"
			>
				开通高级身份认证
			</u-button>
		 </view>
		

		<!-- 额度列表 -->
		<view class="limit_block">
			<view class="limit_title">当前账户限额</view>
			<view class="limit_item">
				<text>法币充值限额</text>
				<text class="limit_val">XX USD 每日</text>
			</view>
			<view class="limit_item">
				<text>法币提现限额</text>
				<text class="limit_val">XX USD 每日</text>
			</view>
			<view class="limit_item">
				<text>加密货币充值限额</text>
				<text class="limit_val">XX USD 每日</text>
			</view>
			<view class="limit_item">
				<text>加密货币提现限额</text>
				<text class="limit_val">XX USD 每日</text>
			</view>
			<view class="limit_item">
				<text>C2C交易限额</text>
				<text class="limit_val">无限额</text>
			</view>
		</view>

		<!-- 认证等级弹窗 -->
		<u-popup v-model="showLevelPopup" mode="bottom" border-radius="24" height="600rpx">
			<view class="level_popup">
				<view class="level_popup_header">
					<text class="level_popup_title">认证等级</text>
					<u-icon name="close" size="36" color="#bbb" @click="showLevelPopup = false" />
				</view>
				<view class="level_popup_content">
					<u-time-line>
						<!-- 标准身份认证 -->
						<u-time-line-item>
							<template #node>
								<view class="u-node success">
									<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385681678662983680.png" color="#13C97F" size="30"></u-icon>
								</view>
							</template>
							<template #content>
								<view>
									<view class="step_title success">标准身份认证</view>
									<view class="step_desc">法币限额 XX USD 每日</view>
								</view>
							</template>
						</u-time-line-item>
						<!-- 高级身份认证 -->
						<u-time-line-item>
							<template #node>
								<view class="u-node gray">
									<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385681773093543936.png" color="#bbb" size="30"></u-icon>
								</view>
							</template>
							<template #content>
								<view>
									<view class="step_title gray">高级身份认证</view>
									<view class="step_desc">法币限额 XX USD 每日</view>
								</view>
							</template>
						</u-time-line-item>
					</u-time-line>
				</view>
			</view>
		</u-popup>

		<!-- 审核中弹窗 -->
		<u-popup v-model="showAuditPopup" mode="bottom" border-radius="24" height="600rpx">
			<view class="audit_popup">
				<view class="audit_popup_header">
					<text class="audit_popup_title">审核中</text>
					<u-icon name="close" size="36" color="#bbb" @click="showAuditPopup = false" />
				</view>
				<view class="audit_popup_desc">
					您已完成初级认证，我们将通过邮件将审核结果发送给您，审核预计在1小时内完成。
				</view>
				<view class="audit_popup_btn_box">
					<u-button
						type="primary"
						class="audit_popup_btn"
						:custom-style="{background:'#F97C9A',borderRadius:'50rpx'}"
						@click="goHome"
					>前往首页</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '认证中心',
			avatar: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385640135935746048.png', // 示例头像
			name: 'ZHANG SAN',
			userId: '1234999899',
			showLevelPopup: false,
			showAuditPopup: true,
		}
	},
	methods: {
		back() {
			uni.navigateBack();
		},
		goHome() {
			this.showAuditPopup = false
			uni.switchTab({ url: '/pages/index/index' }) // 跳转首页，按需修改
		}
	}
}
</script>

<style lang="scss" scoped>
.body {
	background: #fff;
	min-height: 100vh;
	padding-bottom: 120rpx;
	font-family: PingFang SC;
	padding:0rpx 32rpx;
	.user_info_box{
		padding-bottom:48rpx;
		border-bottom:1px solid rgba(0,0,0,0.1);
	}
}
.user_info {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 32rpx;
	margin-bottom: 32rpx;
}
.avatar {
	width: 94rpx;
	height: 94rpx;
	border-radius: 50%;
	margin-bottom: 16rpx;
	background: #f5f5f5;
}
.user_name {
	font-size: 32rpx;
	font-weight: 600;
	color: #222;
	margin-bottom: 8rpx;
}
.user_id {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 12rpx;
}
.cert_tag {
	display: flex;
	align-items: center;
	color: #08B819;
	font-size: 20rpx;
	border-radius: 12rpx;
	height: 46rpx;
	line-height: 46rpx;
	border:1px solid #08B819;
	padding:0rpx 12rpx;
}
.cert_tag .u-icon {
	margin-right: 8rpx;
}
.upgrade_tip {
	text-align: center;
	color: #222;
	font-size: 26rpx;
	margin-bottom: 24rpx;
}
.upgrade_btn {
	width: 442rpx;
	height: 80rpx;
	font-size: 32rpx;
	margin-bottom: 32rpx;
}
.limit_block {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx 0 0 0;
	margin-top: 16rpx;
}
.limit_title {
	font-size: 28rpx;
	font-weight: 600;
	color: #222;
	margin-bottom: 24rpx;
	padding-left: 32rpx;
}
.limit_item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 26rpx;
	color: #222;
	height: 64rpx;
}
.limit_item:last-child {
	border-bottom: none;
}
.limit_val {
	color: #222;
	font-size: 26rpx;
}
.level_popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 40rpx 32rpx 32rpx 32rpx;
}
.level_popup_header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}
.level_popup_title {
	font-size: 32rpx;
	font-weight: 600;
} 
.level_popup_content {
	margin-top: 12rpx;
}
.u-node {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	display: flex;
	align-items: center; 
	justify-content: center;
	background: #eafaf1; 
}
.u-node.gray {
	background: #f3f3f3;
}
.u-node.success {
	background: #eafaf1;
}
.step_title {
	font-size: 28rpx;
	color: #222;
	font-weight: 500;
}
.step_title.success {
	color: #13C97F;
}
.step_title.gray {
	color: #bbb;
}
.step_desc {
	font-size: 24rpx;
	color: #222;
	margin-top: 4rpx;
}
.audit_popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 40rpx 32rpx 32rpx 32rpx;
}
.audit_popup_header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}
.audit_popup_title {
	font-size: 32rpx;
	font-weight: 600;
}
.audit_popup_desc {
	font-size: 26rpx;
	color: #444;
	margin-bottom: 48rpx;
	line-height: 1.7;
}
.audit_popup_btn_box {
	display: flex;
	justify-content: center;
	position: absolute;
	bottom: 50rpx;
	left: 0;
	right: 0;
	padding: 0 32rpx;
}
.audit_popup_btn {
	width: 100%;
	height: 88rpx;
	font-size: 30rpx;
	background: #F97C9A;
	border-radius: 50rpx;
	color: #fff;
}
</style>
