<template>
  <view class="main">
    <u-icon name="arrow-left" @click="goback" color="#fff" size="48" :custom-style="styleicon"></u-icon>
    <text class="title">{{ title }}</text>
    <text class="content" v-if="content.content">{{ content.content }}</text>
    <div class="content" v-if="content.contentHTML" v-html="content.contentHTML"></div>

    <text class="content" v-if="content.remark">{{ content.remark }}</text>
    <view style="height: 80rpx"></view>
    <image class="contentImg" mode="widthFix" :style="{ width: content.width ? content.width + 'rpx' : '' }"
      v-if="content.images" v-for="(item, index) in content.images" :key="index" :src="item"
      @click="previewImages(content.images, index)">
    </image>
  </view>
</template>


<script>
export default {
  name: "answer",
  data() {
    return {
      loading: false,
      styleicon: {
        'padding': '0 0 40rpx 0'
      },
      title: '帮助中心',
      content: '',
      answers: [

        {
          "title": "如何实名",
          "content": "在首页界面点击左上角头像图标进入个人信息页面。点击实名验证一栏，进入实名认证。填写本人居民身份证的姓名与身份证号码，然后点击提交信息即可。",
          "remark": "Bigverse安全保障，身份信息仅作用于身份认证",
          "images": ["https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/d39eef7f1a87438c492fd748cff415b0_828x1431.png", "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/7592ef51d335b973c45461894ee2871c_828x1438.png", "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/6e89ef1041b2b08453e81aea61cfcfb6_828x1443.png"]

        },
        {
          "title": "如何开仓",
          "content": "在首页的下面找到对战按钮，点击进入BIT指数；进入BIT指数后选择唱多或者唱空进行开仓，分别代表做多、做空。然后可以选择市价单和限价单（市价单以当前市场价格成交BIT指数，限价单则以输入的期望价格成交，限价单需要输入期望价格），输入购入金额后，选择合适自己的杠杆倍数，点击唱多或唱空即可。(市价单能迅速成交，限价单只需等待市场价格满足后自动成交)",
          "images": ["https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/f64ee64adefdf3d4971d2968d4800365_828x1434.png", "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/80c2552338342470ba1f771feefed6c0_828x1383.png", "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/f59d30e65b5befdbcc04728c4ec53dbf_828x1436.png"]
        },
        {
          "title": "如何平仓",
          "content": "进入BIT指数中，点击我的仓位即可看到自己的所有未平仓位。选择要平的仓位，点击一键平仓即可。",
          "images": ["https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/414616176b2df2d126c832d68563f3ec_828x1376.png"]
        },
        {
          "title": "如何设置止盈止损",
          "content": "进入我的仓位界面中，选择需要设置止盈止损的仓位，点击设置止盈止损按钮，输入期望的止盈止损金额，点击确定即可。",
          "images": ["https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/e372d5df32eb52f757a7ece4f6b4c807_828x1372.png"
            , "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241111/dc9f352945dbc6f957c9240591789336_828x1431.png"]
        },
        {
          "title": "现在bit指数不同杠杆的强平系数是怎么计算？",
          "content": "于2024年12月9日的最新版，计算公式如下图，对标各大交易平台，在三倍波动率的基础上在各个杠杆档位尽可能的优化强平空间。",
          "images": ["https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241209/0e8016d855b041e160dbab06eea1998f_599x523.jpg"],
          "width": '552'
        },
        {
          "title": "BIT指数是如何计算的？",
          "contentHTML": "<p>简单地说，BIT指数&nbsp;是由6个币种的表现拟合计算而来，其中比特币的影响最大，占比40%。根据这6个币种的综合涨跌幅，再进行3倍放大，就决定了BIT指数的涨跌幅。如果比特币涨1%，那么BIT指数就会涨3%；同理，如果比特币跌1%，BIT指数就会跌3%。</p>\n" +
            "<br>\n" +
            "<p>详细地说：&nbsp;BIT指数的基础是六个主流加密货币（BTC、ETH、BNB、SOL、DOGE、XRP）的价格。主要受比特币的影响，不同币种权重分别为：BTC&nbsp;0.4，ETH&nbsp;0.2，BNB&nbsp;0.1，SOL&nbsp;0.1，DOGE&nbsp;0.1，XRP&nbsp;0.1。</p>\n" +
            "\n" +
            "<br><p>计算示例：<br>\n" +
            "假设初始BIT指数为100，以下是六大币种的价格变动情况：</p>\n" +
            "\n" +
            "<p>BTC上涨0.1%<br>\n" +
            "ETH上涨1%<br>\n" +
            "BNB上涨3%<br>\n" +
            "SOL上涨1%<br>\n" +
            "DOGE下跌3%<br>\n" +
            "XRP下跌1%<br>\n" +
            "<br>我们按照以下步骤计算BIT指数的涨跌幅：</p>\n" +
            "\n" +
            "<p>BTC:&nbsp;0.1%&nbsp;*&nbsp;0.4&nbsp;=&nbsp;0.04%<br>\n" +
            "ETH:&nbsp;1%&nbsp;*&nbsp;0.2&nbsp;=&nbsp;0.2%<br>\n" +
            "BNB:&nbsp;3%&nbsp;*&nbsp;0.1&nbsp;=&nbsp;0.3%<br>\n" +
            "SOL:&nbsp;1%&nbsp;*&nbsp;0.1&nbsp;=&nbsp;0.1%<br>\n" +
            "DOGE:&nbsp;-3%&nbsp;*&nbsp;0.1&nbsp;=&nbsp;-0.3%<br>\n" +
            "XRP:&nbsp;-1%&nbsp;*&nbsp;0.1&nbsp;=&nbsp;-0.1%<br>\n" +
            "<br>BIT指数的涨跌幅为：&nbsp;0.04%&nbsp;+&nbsp;0.2%&nbsp;+&nbsp;0.3%&nbsp;+&nbsp;0.1%&nbsp;-&nbsp;0.3%&nbsp;-&nbsp;0.1%&nbsp;=&nbsp;0.24%</p>\n" +
            "\n" +
            "<br><p>三倍放大：&nbsp;为了提高市场的敏感度，我们将涨跌幅乘以3：&nbsp;0.24%&nbsp;*&nbsp;3&nbsp;=&nbsp;0.72%</p>\n" +
            "\n" +
            "<br><p>最终，新的BIT指数为：&nbsp;100&nbsp;*&nbsp;(1&nbsp;+&nbsp;0.0072)&nbsp;=&nbsp;100.72</p>\n" +
            "\n" +
            "<br><p>那指数涨跌幅多久计算一次？&nbsp;答：实时计算。<br><br>BIT指数应用的是WebSocket长链接，没有传统意义上的“访问频次”一说。WebSocket是一种持续的链接，而不是请求-响应的模式。长链接在建立后，数据可以频繁地在客户端和服务器之间传输，不需要每次都重新发起新的连接。</p>\n" +
            "\n" +
            "<br><p>类似这样，您可以清楚地看到BIT指数的计算方式，可以随时自己验证结果，欢迎所有用户共同保障指数的客观性和公平性，守护最好的BV。</p>",
          "calculation_example": {
            "initial_index": 100,
            "price_changes": {
              "BTC": "上涨0.1%",
              "ETH": "上涨1%",
              "BNB": "上涨3%",
              "SOL": "上涨1%",
              "DOGE": "下跌3%",
              "XRP": "下跌1%"
            },
            "steps": [
              "BTC: 0.1% * 0.4 = 0.04%",
              "ETH: 1% * 0.2 = 0.2%",
              "BNB: 3% * 0.1 = 0.3%",
              "SOL: 1% * 0.1 = 0.1%",
              "DOGE: -3% * 0.1 = -0.3%",
              "XRP: -1% * 0.1 = -0.1%"
            ],
            "index_change": "0.24%",
            "amplified_change": "0.24% * 3 = 0.72%",
            "new_index": "100 * (1 + 0.0072) = 100.72"
          }
        },
        {
          "title": "指数涨跌幅多久计算一次？",
          "content": "实时计算。BIT指数应用的是WebSocket长链接，没有传统意义上的“访问频次”一说。WebSocket是一种持续的链接，而不是请求-响应的模式。长链接在建立后，数据可以频繁地在客户端和服务器之间传输，不需要每次都重新发起新的连接。"
        },
        {
          "title": "盘口位置下面的“标记价格”代表什么？",
          "content": "标记价格是根据6个币种拟合计算的指数，相当于是计算出的“理论价格”，这个“理论价格”和资金费用的计算有关，相当于是给用户的“参考价格”。"
        },
        {
          "title": "盘口中间的BIT指数价格代表什么？",
          "content": "盘口的BIT指数数据是最近一笔撮合成交价格。"
        },
        {
          "title": "BTC等对标物的价格是否有缩放？",
          "content": `为了方便大家交易对标物，我们对这些开杠产品加入了乘数因子，将其本身的价格数字进行缩放。具体乘数因子如下：
                  BTC：0.001
                  ETH：0.01
                  BNB：0.1
                  SOL：0.1
                  DOGE：10
                  XRP：10`
        },
        {
          "title": "如何阅读和理解K线图？",
          "contentHTML": "<p>K线图（也称蜡烛图）是一种在金融市场中广泛使用的技术分析工具，用于显示一段时间内资产的价格波动。K线图的每根“蜡烛”包含四个关键数据：开盘价、收盘价、最高价和最低价。让我们先了解最基础的几个元素：<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp1.实柱（粗线部分）：<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;K线的粗线部分称为“实体”，表示开盘价与收盘价之间的范围。<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;红色实柱：代表价格上涨，开盘价在实体的底部，收盘价在顶部。<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;绿色实柱：代表价格下跌，开盘价在实体的顶部，收盘价在底部。<br>\n" +
            "&nbsp;&nbsp;&nbsp;<br>2.&nbsp;上下影线（细线部分）：<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;实体上下的细线称为“影线”，表示在该时间段内的最高价和最低价。<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;上影线：实体顶部延伸到最高价，表示在这一时间段价格达到的最高点。<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;下影线：实体底部延伸到最低价，表示在这一时间段价格达到的最低点。<br>\n" +
            "&nbsp;&nbsp;&nbsp;<br>3.&nbsp;红线与绿线<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;红线：表示该时间段内价格上涨（收盘价高于开盘价）。<br>\n" +
            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;•&nbsp;&nbsp;绿线：表示该时间段内价格下跌（收盘价低于开盘价）。<br>\n" +
            "通过观察K线图中的这些元素，交易者可以初步了解市场的涨跌趋势和价格波动的幅度。</p>",
          "details": [
            {
              "element": "实柱（粗线部分）",
              "description": [
                "K线的粗线部分称为“实体”，表示开盘价与收盘价之间的范围。",
                "红色实柱：代表价格上涨，开盘价在实体的底部，收盘价在顶部。",
                "绿色实柱：代表价格下跌，开盘价在实体的顶部，收盘价在底部。"
              ]
            },
            {
              "element": "上下影线（细线部分）",
              "description": [
                "实体上下的细线称为“影线”，表示在该时间段内的最高价和最低价。",
                "上影线：实体顶部延伸到最高价，表示在这一时间段价格达到的最高点。",
                "下影线：实体底部延伸到最低价，表示在这一时间段价格达到的最低点。"
              ]
            },
            {
              "element": "红线与绿线",
              "description": [
                "红线：表示该时间段内价格上涨（收盘价高于开盘价）。",
                "绿线：表示该时间段内价格下跌（收盘价低于开盘价）。"
              ]
            }
          ],
          "remark": "通过观察K线图中的这些元素，交易者可以初步了解市场的涨跌趋势和价格波动的幅度。"
        },
        {
          "title": "平台的行情数据来源是什么？",
          "content": "BIT指数的行情数据来源是币安等头部加密货币交易所的数据的加权平均。"
        },
        {
          "title": "什么是行情深度？",
          "content": "行情深度也称为市场深度或订单深度，是指在金融市场中，特定资产的买卖订单在不同价格水平上的分布情况。主要组成部分为买单，卖单和订单簿。行情深度通常以订单簿的形式呈现，包含了所有未成交的买单和卖单。订单簿可以实时更新，展示当前市场的供需情况。行情深度越大，表示在当前价格附近有更多的买卖订单，市场流动性越好。这意味着较大规模的交易可以在不显著影响市场价格的情况下执行。当市场深度较浅时，较少的订单可能导致价格在小幅交易时剧烈波动。而在深度较大的市场中，较大的买卖订单可以更平稳地消化，减少价格波动。行情深度有助于投资者了解当前市场对资产的定价预期和供需情况，可以用于分析市场趋势和做出交易决策。",
          "details": [
            "行情深度主要组成部分为买单、卖单和订单簿。",
            "行情深度通常以订单簿的形式呈现，包含了所有未成交的买单和卖单。订单簿可以实时更新，展示当前市场的供需情况。",
            "行情深度越大，表示在当前价格附近有更多的买卖订单，市场流动性越好。这意味着较大规模的交易可以在不显著影响市场价格的情况下执行。",
            "当市场深度较浅时，较少的订单可能导致价格在小幅交易时剧烈波动。而在深度较大的市场中，较大的买卖订单可以更平稳地消化，减少价格波动。"
          ],
        },
        {
          "title": "手续费是如何计算的？",
          "content": "BIT指数开杠的手续费为千分之三即0.3%，手续费的计算公式为：手续费=实际开仓金额*杠杆倍数*0.3%",
          "formula": "手续费 = 实际开仓金额 * 杠杆倍数 * 0.3%"
        },
        {
          "title": "资金费率是什么？如何计算？",
          "contentHTML": "<p>资金费用机制是BIT指数的一大创新。这一机制的核心在于，它能够在市场价格和标记价格出现偏差时进行自动校准。&nbsp;如果没有资金费用，那可能比指数的价格会偏离实际计算出价格。慢慢地，市场可能会失去共识，受到“大资金”的影响。&nbsp;当价格偏离实际价格的时候，就会让比原本该赚的赚更多钱的人，补偿钱给比原本亏钱亏更多钱的人。</p><br>" +
            "<br>" +
            "<p>资金费用具体计算过程如下：</p><br>" +
            "<br>" +
            "<p>第一步：计算偏离比例<br><br>" +
            "偏离比例&nbsp;=(冲击买方出价+冲击卖方出价)/2/指数价格-1<br><br>" +
            "什么是冲击买方出价&nbsp;？<br><br>" +
            "以10000份的数量以市价唱空时得到的持仓均价。<br><br>" +
            "什么是冲击卖方出价&nbsp;？<br><br>" +
            "以10000份的数量以市场唱多时得到的持仓均价。</p><br>" +
            "<br>" +
            "<p>第二步：计算平均偏离比例<br><br>" +
            "偏离比例将会在每分钟自动计算一次，则每小时会有60个偏离数据<br><br>" +
            "n代表小时数，由于资金费用目前8小时收取一次，所以目前n=8<br><br>" +
            "平均偏离比例&nbsp;=（偏离比例_1+偏离比例_2+...偏离比例_n*60）/(n*60)</p><br>" +
            "<br>" +
            "<p>第三步：计算资金费用<br><br>" +
            "如果平均偏离比例小于万分之五，则认为是可容忍的偏离范围，不进行资金费用矫正，即:&nbsp;资金费率&nbsp;=&nbsp;0。<br><br>" +
            "否则：资金费率&nbsp;=&nbsp;clamp(平均偏离比例&nbsp;+&nbsp;clamp(-平均偏离比例,&nbsp;-0.05%,&nbsp;0.05%),&nbsp;-0.75%,&nbsp;0.75%)<br><br>" +
            "&nbsp;(clamp函数将限定资金费率的最大为+0.75%，最小值为-0.75%)</p><br>" +
            "<br>" +
            "<p>最终：资金费用&nbsp;=&nbsp;资金费率&nbsp;*&nbsp;持仓数量&nbsp;*&nbsp;实时指数价格</p><br>" +
            "<br>" +
            "<p>资金费用的结算时间：每日：00:00、08:00、16:00</p><br>" +
            "<br>" +
            "<p>资金费用计算举例：</p><br>" +
            "<br>" +
            "<p>举例1：假设此时指数价格为100，这个时候A和B分别持有了50份多仓和80份空仓。如果过去8个小时内，总计480个数据节点的平均偏离比例为0.15%。<br><br>" +
            "那么，资金费率&nbsp;=&nbsp;clamp(0.15%+clamp(-0.15%,-0.05%,0.05%),&nbsp;-0.75%,&nbsp;0.75%)&nbsp;=0.15%-0.05%&nbsp;=&nbsp;0.1%<br><br>" +
            "由于资金费率为正数，结算时多仓需要向空仓支付资金费用：<br><br>" +
            "B持有的空仓，B会收到的资金费用“补偿”为：<br><br>" +
            "资金费率*持仓数量*实时指数价格&nbsp;=0.1%&nbsp;*&nbsp;80&nbsp;&nbsp;*&nbsp;100&nbsp;=&nbsp;8，也就是B的该笔仓位将额外收到8元的资金费用。作为盘口偏离标记价的“补偿”，每8小时系统自动向B支付一次“资金费用”的“补偿”（分别为每日：00:00、08:00、16:00）。<br><br>" +
            "而A持有的是多仓，A会为其多仓的仓位付出的资金费用为：<br><br>" +
            "资金费率*持仓数量*实时指数价格&nbsp;=0.1%&nbsp;*&nbsp;50&nbsp;*&nbsp;100=&nbsp;5，也就是A的该笔仓位要付出的5元的资金费用。</p><br>" +
            "<br>" +
            "<p>举例2：假设此时指数价格是100，这个时候A和B分别持有了200份多仓和300份空仓。如果过去8个小时内，总计480个数据节点的平均值为-0.15%，那么<br><br>" +
            "资金费率&nbsp;=&nbsp;clamp(-0.15%+clamp(0.15%,-0.05%,0.05%),&nbsp;-0.75%,&nbsp;0.75%)=&nbsp;-0.15%+0.05%&nbsp;=&nbsp;-0.1%<br><br>" +
            "由于资金费率为负数，那么结算时空仓需要向多仓支付资金费用：<br><br>" +
            "A持有的多仓，A会收到的资金费用“补偿”为：<br><br>" +
            "资金费率&nbsp;*&nbsp;持仓数量&nbsp;*&nbsp;实时指数价格&nbsp;=&nbsp;0.1%*&nbsp;200&nbsp;*&nbsp;100=&nbsp;20，也就是A的该笔仓位将额外收到的20元的资金费用。<br><br>" +
            "B持有的空仓，B会其空仓的仓位付出的资金费用为：<br><br>" +
            "资金费率&nbsp;*&nbsp;持仓数量&nbsp;*&nbsp;实时指数价格&nbsp;=&nbsp;0.1%&nbsp;*&nbsp;300&nbsp;*&nbsp;100&nbsp;=&nbsp;-30，也就是B的该笔仓位要付出30元的资金费用。</p><br>" +
            "<br>" +
            "<p>BIT指数正是通过以上资金费用的机制，在持仓双方之间进行动态“补偿”和“惩罚”，帮助市场价格逐渐回归标记价格。<br><br>" +
            "</p>",
          "steps": [
            {
              "step": "第一步",
              "description": "计算偏离比例",
              "formula": "偏离比例 = (冲击买方出价 + 冲击卖方出价) / 2 / 指数价格 - 1",
              "definitions": [
                {
                  "term": "冲击买方出价",
                  "definition": "以10000份的数量以市价唱空时得到的持仓均价。"
                },
                {
                  "term": "冲击卖方出价",
                  "definition": "以10000份的数量以市场唱多时得到的持仓均价。"
                }
              ]
            },
            {
              "step": "第二步",
              "description": "计算平均偏离比例",
              "formula": "平均偏离比例 =（偏离比例_1 + 偏离比例_2 + ... 偏离比例_n * 60）/ (n * 60)",
              "note": "n代表小时数，由于资金费用每8小时收取一次，n=8"
            },
            {
              "step": "第三步",
              "description": "计算资金费用",
              "conditions": [
                {
                  "condition": "如果平均偏离比例小于万分之五",
                  "result": "资金费率 = 0"
                },
                {
                  "condition": "否则",
                  "formula": "资金费率 = clamp(平均偏离比例 + clamp(-平均偏离比例, -0.05%, 0.05%), -0.75%, 0.75%)",
                  "note": "clamp函数将限定资金费率的最大为+0.75%，最小值为-0.75%"
                }
              ]
            },
            {
              "step": "最终",
              "formula": "资金费用 = 资金费率 * 持仓数量 * 实时指数价格"
            }
          ],
          "settlement_time": "资金费用结算时间：每日：00:00、08:00、16:00",
          "examples": [
            {
              "title": "举例1",
              "details": [
                "假设指数价格为100，A持有50份多仓，B持有80份空仓，8小时内平均偏离比例为0.15%",
                "资金费率 = clamp(0.15% - 0.05%, -0.75%, 0.75%) = 0.1%",
                "B收到的资金费用补偿为：0.1% * 80 * 100 = 8元",
                "A支付的资金费用为：0.1% * 50 * 100 = 5元"
              ]
            },
            {
              "title": "举例2",
              "details": [
                "假设指数价格为100，A持有200份多仓，B持有300份空仓，8小时内平均偏离比例为-0.15%",
                "资金费率 = clamp(-0.15% + 0.05%, -0.75%, 0.75%) = -0.1%",
                "A收到的资金费用补偿为：0.1% * 200 * 100 = 20元",
                "B支付的资金费用为：0.1% * 300 * 100 = 30元"
              ]
            }
          ]
        },
        {
          "title": "强平价是如何计算的？",
          "content": "强平价格的计算基于开仓价格、杠杆倍数以及开仓金额综合计算。把开仓金额视作“保证金”，强平价即计算出在市场达到什么价格时，保证金将会出现不足以支持继续持仓。杠杆倍数越高、开仓金额越大，爆仓价格就会越接近开仓价格。这是因为较高的杠杆意味着风险增大，对市场价格波动的容忍度变小，持仓的安全边际更低。因此，当价格朝不利方向变化时，达到强平的条件会更容易触发，使得爆仓价格更加贴近原始开仓价格。这一机制有助于控制风险，避免更大的亏损，但同时也要求投资者在使用高杠杆时更加谨慎。",
          "note": "杠杆倍数越高、开仓金额越大，爆仓价格越接近开仓价格。该机制帮助控制风险。"
        },
        {
          "title": "提现多久能到账？",
          "content": "提现通常会在2至5分钟内到账。如遇特殊情况需人工审核，审核将在三个工作日内完成。"
        },
        {
          "title": "为什么实际开仓金额比输入金额少？",
          "contentHTML": "<p>假设您输入的数字是A。实际成交金额通常比A少一点点，命名为B。<br>\n" +
            "实际开仓金额：B*杠杆，命名为C<br>\n" +
            "手续费：C*&nbsp;0.3%<br>\n" +
            "用户实际支付&nbsp;=&nbsp;实际成交金额&nbsp;+&nbsp;手续费&nbsp;=&nbsp;B+C*0.3%&nbsp;=&nbsp;B&nbsp;+&nbsp;（B*杠杆*0.3%）<br>\n" +
            "也就是说，手续费会按照更小的开仓数字*手续费百分比。</p>",
          "note": "手续费按实际开仓金额计算。"
        },
        {
          "title": "在“退零钱”情况下的手续费计算",
          "formula": [
            "假设输入金额为A，实际成交金额为B",
            "实际开仓金额 = B * 杠杆，记为C",
            "手续费 = C * 0.3%",
            "用户实际支付 = 实际成交金额 + 手续费 = B + (B * 杠杆 * 0.3%)"
          ]
        },
        {
          "title": "体验金使用规则",
          "content": "体验金可用于“开杠吧”开仓时抵扣余额使用，开仓时优先扣除体验金，超出体验金部分将扣除余额。被使用的体验金平仓后将失效。"
        },
        {
          "title": "万能金使用规则",
          "contentHTML": `万能金可用于“开杠吧”开仓时抵扣余额使用，但与体验金不同，万能金的使用存在如下限制：<br />
                   <p> 1.万能金根据每次活动的不同，发放给您的杠杆倍数不同，常见的有2倍、5倍、10倍。需在开仓时选择对应的杠杆倍数才可以使用与之对应的万能金。</p>
                    <p>  2.万能金使用后的持仓时间最多为24小时，超时会自动平仓。</p>
                    <p>  3.万能金根据每次活动的不同，发放给您的有效时间不同，常见的有7天、15天、30天。如果未在有效期内使用，对应的万能金将会过期失效。</p>`
        }
      ]
    }
  },
  onLoad(option) {
    console.log(option)
    this.title = option.title
    this.content = this.getContentByTitle(option.title)
    console.log(this.content)

  },
  methods: {
    previewImages(images, currentIndex) {
      console.log(images, currentIndex)
      uni.previewImage({
        urls: images,               // 所有图片的数组
        current: images[currentIndex]  // 当前图片
      });
    },
    getContentByTitle(title) {
      const answer = this.answers.find(item => item.title == title);
      return answer ? answer : "未找到相关内容";
    },
    goback() {
      this.$Router.back()
    },
  },
}
</script>


<style scoped lang="scss">
.main {
  padding: 60rpx 36rpx 100rpx 36rpx;
  display: flex;
  flex-direction: column;

  .title {
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 34rpx;
    color: #63EAEE;
    line-height: 45rpx;
  }

  .content {
    margin-top: 35rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 26rpx;
    color: #FFFFFF;
    line-height: 40rpx;
  }

  .contentImg {
    width: 340rpx;
    height: 592rpx;
    margin: 0 auto 40rpx;
    border-radius: 15rpx;
    border: 1rpx solid #63EAEE;
    overflow: hidden;
  }
}
</style>
