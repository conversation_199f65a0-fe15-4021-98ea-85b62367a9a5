<template>
  <view class="container">

    <view class="code">
      <view class="title">
        <text>为了更快速响应您的问题</text>
        <text>请添加开杠企业微信客服</text>
      </view>
      <view class="qrcode">
        <!-- <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="value" size="150rpx" :options="options"
          @complete="complete"></uv-qrcode> -->
          <image :src="value" style="width: 150rpx;height: 150rpx;"/>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search_view">
      <u-search placeholder="请描述您遇到的问题" search-icon="/static/imgs/notic/sea.png" v-model="Kwd" bg-color='#35333E'
        color="#fff" :clearabled='false' @search="search" @clear="clear" :show-action="false"
        :input-style='searchStyle'></u-search>
    </view>
    <view class="category-collapsed" v-for="(item, index) in filteredList" @click="handleAnswerClick(item)" :key="index"
      v-if="Kwd">{{ item }}</view>
    <!-- 问题分类列表 -->
    <u-collapse class="category-collapse" :accordion="false">
      <u-collapse-item :open="true" v-for="(item, index) in itemList" :key="index" :title="item.head">
        <view v-for="(answer, aIndex) in item.body" :key="aIndex" class="answer-item"
          @click="handleAnswerClick(answer)">
          {{ answer }}
        </view>
      </u-collapse-item>
    </u-collapse>

    <!-- 帮助部分 -->
    <view class="help-text">需要更多帮助？</view>

    <!-- 选项卡 -->
    <view class="options">
      <view v-for="(option, index) in options" :key="index" class="option-card" @click="serviseceClick(option)">
        <image :src="option.icon" class="option-icon" />
        <view class="option-text">
          <view class="option-title">{{ option.title }}</view>
          <view class="option-description">{{ option.description }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue';
// #ifdef H5
import {
  nav_contactService
} from '@/utils/utils'
// #endif
// #ifdef APP
var qiyuModule = uni.requireNativePlugin("Netease-QiyuModule")
// #endif
export default {
  data() {
    return {
      Kwd: '',
      qqlink: "",
      searchStyle: {
        'textIndent': '12rpx',
        'background': '#35333E',
      },
      title: "",
      options: {
        useDynamicSize: false,
        errorCorrectLevel: 'Q',
        // margin: 10,
        areaColor: "#fff",
        // 指定二维码前景，一般可在中间放logo
        // foregroundImageSrc: require('static/image/logo.png')
      },
      questionsList: [
        "如何实名",
        "如何开仓",
        "如何平仓",
        "如何设置止盈止损",
        "BIT指数是如何计算的？",
        "盘口位置下面的“标记价格”代表什么？",
        "盘口中间的BIT指数价格代表什么？",
        "BTC等对标物的价格是否有缩放？",
        "现在bit指数不同杠杆的强平系数是怎么计算？",
        "如何阅读和理解K线图？",
        "平台的行情数据来源是什么？",
        "什么是行情深度？",
        "手续费是如何计算的？",
        "资金费率是什么？如何计算？",
        "强平价是如何计算的？",
        "提现多久能到账？",
        "为什么实际开仓金额比输入金额少？",
          "体验金使用规则",
            "万能金使用规则"
      ],
      itemList: [
        {
          head: "操作类",
          body: [
            "如何实名",
            "如何开仓",
            "如何平仓",
            "如何设置止盈止损",
          ]
        },
        {
          head: "BIT指数相关",
          body: [
            "BIT指数是如何计算的？",
            "盘口位置下面的“标记价格”代表什么？",
            "盘口中间的BIT指数价格代表什么？",
            "BTC等对标物的价格是否有缩放？",
            "现在bit指数不同杠杆的强平系数是怎么计算？"
          ]
        },
        {
          head: "K线与行情",
          body: [
            "如何阅读和理解K线图？",
            "平台的行情数据来源是什么？",
            "什么是行情深度？"
          ]
        },
        {
          head: "手续费与结算",
          body: [
            "手续费是如何计算的？",
            "资金费率是什么？如何计算？",
            "强平价是如何计算的？",
            "提现多久能到账？",
            "为什么实际开仓金额比输入金额少？"
          ]
        },
        {
          head:"体验金和万能金的解释",
          body:[
            "体验金使用规则",
            "万能金使用规则"
          ]
        }
      ],
      defaultExpanded: [0],
      options: [
        {
          title: "向帮助社区发问",
          description: "向社区成员寻求答案",
          icon: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241108/995749977c1acc83b7eb29935a87ba6b_51x48.png"
        },
        {
          title: "联系客服",
          description: "向我们提供更多信息，以便我们帮助您解决问题",
          icon: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241108/14a12461ca29b8c38a3e90cee23db594_54x52.png"

        }
      ],
      pageNum: 1,
      list: [],
      appUrl: '',
      value: '12312312',

    };
  },
  onLoad(options) {
    this.title = options.title
    let _this = this
    this.get_tab()
  },
  computed: {
    filteredList() {
      const reg = new RegExp(this.Kwd, 'i')
      return this.questionsList.filter(item => reg.test(item)
      )
    }
  }
  ,
  methods: {
    async get_tab() {
      let res = await this.$api.java_commonconfigInfo({
        name: 'ys_customer_qrcode'
      });
      if (res.status.code == 0) {
        this.value = res.result.value
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }


      let res2 = await this.$api.java_commonconfigInfo({
        name: 'ys_qq_chat_group_link'
      });
      if (res2.status.code == 0) {
        this.qqlink = res2.result.value
      } else {
        uni.showToast({
          title: res2.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    complete(event) {
      console.log(event, 'event');
    },
    // #ifdef H5
    nav_contactService,
    // #endif
    serviseceClick(e) {
      console.log(e)
      if (e.title == '联系客服') {
        // #ifdef H5
        this.nav_contactService()
        // #endif
        // #ifdef APP
        this.testOpenService()
        // #endif
      } else {
        let token = uni.getStorageSync('token')

        if (!token) {
          this.$Router.push({
            name: 'mainLogin',
          })
          return
        }
        // #ifdef H5
        // this.appUrl = getApp().globalData.Imurl
        // window.location.href = `${this.appUrl}im/#/pages/index/index?token=${token}`
        window.location.href = this.qqlink
        // #endif

        // #ifdef APP
        // this.appUrl = getApp().globalData.Imurl
        // let link = `${this.appUrl}im/#/pages/index/index`
        // console.log(link)
        this.$Router.push({
          name: 'webView',
          params: {
            url: this.qqlink
          }
        })
        // #endif
      }
    }
    ,
    testOpenService() {
      qiyuModule.openServiceActivity({
        title: 'NFTCN客服',
        source: {
          title: 'NFTCN客服',
          // vipLevel: 1, // 设置用户VIP等级
          // robotId: 2222, //分配机器人
          // staffId: 3444, //分配客服
          // groupId: 345, //客服组id
          // groupTmpId: 455, //分流客服组id
          // robotFirst: false, //是否客服组优先
          //访客头像
          ios_sendProduct: false,
        },
        //iOS打开界面的方式，push/present,默认push
        openMode: 'push',

      });
    }
    ,
    handleAnswerClick(answer) {
      // #ifdef H5
      this.$Router.push({
        name: 'answer',
        params: {
          title: answer
        }
      })
      // #endif


      // #ifdef APP-PLUS
      let link = `${getApp().globalData.url}pagesA/project/helpCenter/answer`
      this.$Router.push({
        name: 'webView',
        params: {
          url: link,
          title: answer
        }
      })
      // #endif

    }
    ,
    search() {
      this.list = [];
      // this.isLoadingStatus = 0
      this.pageNum = 1
      // this.getList()
    }
    ,
    clear() {
      this.title = ""
      this.pageNum = 1
      this.getList()
    }
    ,
    async getList() {
      this.isRequest = true
      if (this.pageNum == 1) {
        this.list = [];
      }
      const {
        status,
        result,
        traceId
      } = await this.$api.java_officialArticleList({
        type: this.type,
        pageSize: 10,
        pageNum: this.pageNum,
        templateType: this.templateType,
        title: this.title,
        tapType: 0
      });
      console.log("traceId：" + traceId)
      if (status.code === 0) {
        this.isRequest = false
        if (result.list == null || result.list == "") {
          this.isFooter = false
          if (this.list == "") {
            this.isLoadingStatus = 2
          }
        } else {
          this.isLoadingStatus = 1
          this.pageNum++
          result.list.forEach((item) => {
            this.list.push(item)
          })
        }
      } else {
        uni.showToast({
          title: status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    }
    ,
  }
}
  ;
</script>

<style scoped lang="scss">
.code {
  margin-top: 30rpx;
  height: 223rpx;
  background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241219/4ce9b4a2ad4e1a5a912bf41d0cc2bf29_1366x446.png");
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF;
  line-height: 38rpx;

  .title {
    display: flex;
    flex-direction: column;
  }

  .qrcode {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 102rpx;
    width: 179rpx;
    height: 179rpx;
    background: #fff;
    border-radius: 25rpx;
  }
}

::v-deep .u-hover-class {
  opacity: 1 !important;
}

::v-deep .answer-item {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF !important;
  line-height: 20rpx;
  border: none !important;
}

::v-deep .u-collapse-head {
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 34rpx;
  color: #63EAEE;
  line-height: 34rpx;
}

::v-deep .uni-input-placeholder {
  font-family: PingFang SC;
  font-weight: bold;
  font-size: 26rpx;
  color: #FFFFFF !important;
  line-height: 34rpx;
}

.search_view {
  margin-top: 32rpx;
  position: relative;
  border-radius: 28rpx;
  border: 1px solid rgba(255, 255, 255, 0.5);

  .right {
    font-size: 28rpx;
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
  }
}

.container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
  border-radius: 30px;
  background-color: #3A3A4F;
}

.category-collapse {
  margin-top: 26rpx;
  background: #25242C;
  border-radius: 34rpx;
  padding: 39rpx 35rpx 51rpx 35rpx;
}

.category-collapsed {
  margin-top: 26rpx;
  background: #25242C;
  border-radius: 34rpx;
  padding: 39rpx 35rpx 39rpx 35rpx;

  font-family: PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF !important;
  line-height: 20rpx;
  border: none !important;
}

.answer-item {
  padding: 10px 0;
  color: #A9A9B7;
  border-bottom: 1px solid #3A3A4F;
}

.help-text {
  text-align: center;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #FFFFFF;
  line-height: 34rpx;
  margin: 66rpx 0 39rpx 0;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 45rpx;
}

.option-card {
  height: 151rpx;
  display: flex;
  align-items: center;
  padding: 36rpx 0 46rpx 36rpx;
  background: #25242C;
  border-radius: 34rpx;
}

.option-icon {
  &:nth-of-type(1) {
    width: 51rpx;
    height: 48rpx;
  }

  &:nth-of-type(2) {
    width: 54rpx;
    height: 52rpx;
  }

}

.option-text {
  color: #FFFFFF;
  margin-left: 33rpx;
  font-family: PingFang SC;

}

.option-title {
  font-weight: bold;
  font-size: 34rpx;
  margin-bottom: 14rpx;
}

.option-description {
  font-weight: 400;
  font-size: 24rpx;
}
</style>
