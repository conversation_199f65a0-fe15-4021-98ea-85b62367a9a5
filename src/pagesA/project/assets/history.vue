<template>
    <view class="history-page">
        <!-- 头部 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="划转记录">

        </u-navbar>

        <!-- 记录列表 -->
        <view class="content">
            <view class="record-list">
                <view class="record-item" v-for="(record, index) in recordList" :key="index">
                    <view class="record-info">
                        <view class="transfer-direction">
                            <text class="direction-text">({{ getAccountParam(record.fromAccount) }} → {{ getAccountParam(record.toAccount) }})</text>
                        </view>
                        <view class="record-amount">
                            <text class="amount-text">{{ record.amount }} {{ record.coin }}</text>
                        </view>

                    </view>

                    <view class="record-time">
                        <text class="time-text">{{ formatTimestamp(record.crateAt) }}</text>
                    </view>
                </view>
                <nodata style="margin-top: 200px;" v-if="recordList.length === 0" />

            </view>
        </view>
    </view>
</template>

<script>
import nodata from "../trade/components/nodata"
export default {
    name: 'TransferHistory',
    components: {
        nodata
    },
    data() {
        return {
            pageNum: 1,
            hasNext: false,
            recordList: [
                // {
                //     fromAccount: '资金账户',
                //     toAccount: '现货钱包',
                //     amount: '94368.5',
                //     currency: 'USDT',
                //     time: '2024/05/31 22:00:00'
                // },
                // {
                //     fromAccount: '资金账户',
                //     toAccount: '现货钱包',
                //     amount: '94368.5',
                //     currency: 'USDT',
                //     time: '2024/05/31 22:00:00'
                // }
            ]
        }
    },
    // methods: {
    //     goBack() {
    //         uni.navigateBack()
    //     },
    //     loadMoreRecords() {
    //         // 加载更多记录的逻辑
    //         console.log('加载更多记录')
    //     }
    // },
    onLoad() {
        this.gettransferRecordList()
    },
    onReachBottom() {

        if (this.hasNext) {
            this.pageNum++
            this.gettransferRecordList()
        }
    },
    methods: {
        getAccountParam(accountType) {
            const accountParamMap = {
                'FUNDS': '资金账户',        // 资管账户
                'STOCK_US': '美股账户',  // 美股账户
                'CONTRACT': '合约账户',  // 合约账户
                'STOCK_HK': '港股账户'   // 港股账户
            };

            return accountParamMap[accountType] || 'CONTRACT'; // 默认返回 contract
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        async gettransferRecordList() {
            let res = await this.$api.transferRecordList({
                pageNum: this.pageNum,
                pageSize: 10
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.recordList = res.result.data
                } else {
                    this.recordList = this.recordList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
        },
        loadRecords() {
            // 加载划转记录的逻辑
            console.log('加载划转记录')
        }
    }
}
</script>

<style lang="scss" scoped>
.history-page {
    min-height: 100vh;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 32rpx;
        background: #fff;

        .back-btn {
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-icon {
            width: 24rpx;
            height: 24rpx;
        }

        .title {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
        }

        .placeholder {
            width: 48rpx;
            height: 48rpx;
        }
    }

    .content {
        margin-top: 68rpx;
        padding: 0 32rpx;

        .record-list {
            .record-item {
                background: #fff;
                border-radius: 16rpx;
                padding: 28rpx 0;
                // margin-bottom: 16rpx;
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                flex-direction: column;

                &:last-child {
                    margin-bottom: 0;
                }

                .record-info {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .transfer-direction {
                        // margin-bottom: 16rpx;

                        .direction-text {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 150%;
                            color: #000;
                        }
                    }

                    .record-time {
                        display: flex;

                        .time-text {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 150%;
                            letter-spacing: 0%;
                            text-align: right;
                            color: #000;
                            opacity: .5;
                            margin-top: 4rpx;
                        }
                    }
                }

                .record-amount {
                    .amount-text {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 100%;
                        color: #000;
                    }
                }
            }
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 120rpx 32rpx;

            .empty-icon {
                width: 120rpx;
                height: 120rpx;
                margin-bottom: 32rpx;
                opacity: 0.5;
            }

            .empty-text {
                font-size: 28rpx;
                color: #999;
            }
        }
    }
}
</style>