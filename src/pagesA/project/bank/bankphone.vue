<template>
	<view class="bankphone">
		<view class="text">
			<view class="h1">
				请输入验证码
			</view>
			<view class="phone">
				已发送至 +86 {{this.orderdata.phone}}
			</view>
		</view>
		<view class="ipt">
			<!-- <view class="man">
				验证码
			</view> -->
			<u-field type="number" class="input" v-model="verification" label-width='0' placeholder="验证码"
				:clearable='false' maxlength='6' :focus="true">
				<text slot="right" class="yzm" @click="getCode(true)" v-if="popupshow">获取验证码</text>
				<text slot="right" style="color:#616161;" class="yzm" v-else>重新获取({{count}}s)</text>
			</u-field>
		</view>
		<view class="submit">
			<button @click="config()">下一步</button>
		</view>
		<!-- 弹窗  成功 -->
		<!-- <u-popup v-model="correctpopup" mode="center" border-radius="12">
			<view class="eject">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/old/2022/02/09/76960d67f2a8893acf07d5d3941297c7_60x60.png"
					v-if="this.status == 0" mode=""></image>
				<image
					src="https://cdn-lingjing.nftcn.com.cn/old/2022/02/09/debe770e0389dc6debce7d604c8b861c_60x60.png"
					v-if="this.status == 1" mode=""></image>
				<text v-if="this.status == 0">恭喜您,添加成功</text>
				<text v-if="this.status == 1">{{errorMsg}}</text>
				<view class="submit">
					<button @click="popup()">完成</button>
				</view>
			</view>
		</u-popup> -->
		<!-- <popupBar v-model="correctpopup"></popupBar> -->
		<!-- 弹窗  成功 -->
		<!-- <u-popup v-model="show" mode="center" :mask-close-able='false' border-radius="12">
			<view class="eject">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/old/2022/02/09/debe770e0389dc6debce7d604c8b861c_60x60.png"
					mode=""></image>
				<text>参数错误,请重新绑卡</text>
				<view class="submit">
					<button @click="upaddbank()">确定</button>
				</view>
			</view>
		</u-popup> -->
		<u-toast ref="uToast" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				heightChange: true,
				banknum: '',
				verification: '',
				popupshow: false,
				count: 0,
				correctpopup: false, //正确弹窗
				status: 0,
				precode: '',
				show: false,
				phone: '',
				orderdata: {},
				errorMsg: ""

			}
		},
		onLoad() {
			this.getCode(false)
			this.orderdata = uni.getStorageSync('orderData')
			console.log(this.orderdata)
		},
		watch: {

		},
		methods: {
			upaddbank() {
				this.$router.back()
			},
			popup() {
				if (this.status == 0) {
					// this.correctpopup = false
					this.orderdata = {}
					this.verification = ''
					// #ifdef H5
					window.location.href = uni.getStorageSync("addbank_link")
					// #endif
					// #ifdef APP
					this.$Router.push({
						name:'myBalance'
					})
					// #endif
				} else {
					// this.correctpopup = false
				}

			},
			ph() {
				console.log('**********')
				this.$router.replace('banklist')
			},
			nav_privacyAgreement() {
				this.$Router.push({
					name: "privacyAgreement"
				})
			},
			config() {
				if (this.verification.length < 6 || this.verification == '') {
					this.$refs.uToast.show({
						title: '验证码错误，请重新输入。',
						icon: false,
						duration: 3000
					})
				} else {
					this.submit()
				}
			},
			//获取验证码倒计时
			async getCode(isSendCode) {
				const TIME_COUNT = 60;
				if (!this.timer) {
					this.count = TIME_COUNT;
					this.popupshow = false;
					this.timer = setInterval(() => {
						if (this.count > 0 && this.count <= TIME_COUNT) {
							this.count--;
						} else {
							this.popupshow = true;
							clearInterval(this.timer);
							this.timer = null;
						}
					}, 1000)
				}
				if(isSendCode == 1){
					//手动发送验证码
					this.sendBindSms()
					
				}
			},
			async submit() {
				let res = await this.$api.bank_bind_confirm({
					"captcha": this.verification,
					"preCode": this.orderdata.preCode
				})
				console.log(res)
				if (res.status.code == 0) {
					this.status = 0
					this.$refs.uToast.show({
						title: this.status ? this.errorMsg : '银行卡绑定成功',
						icon: false,
						duration: 2000,
						callback: () => {
							this.popup()
						}
					})
					// this.judgeInfo()
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						icon: false,
						duration: 3000
					})
				}


			},
			// // 娃娃预热活动，判断是否认证绑卡
			// async judgeInfo() {
			// 	let res = await this.$api.hotIsInvite();
			// },
			async sendBindSms() {
				let res = await this.$api.reSendBindSms({
					preCode: this.orderdata.preCode
				});
				if (res.status.code == 0) {
					
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	.u-drawer::v-deep {
		.u-mode-center-box {
			background-color: var(--dialog-bg-color) !important;
		}
	}

	.bankphone {
		// background-color: var(--message-box-point-color);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		flex-wrap: wrap;
		align-items: center;

		.text {
			margin-top: 40rpx;
			width: 662rpx;
			// height: 200rpx;
			// background-color: #FAcc22;
			position: relative;
			margin-bottom: 40rpx;

			.h1 {
				// width: 240rpx;
				height: 60rpx;
				font-size: 36rpx;
				font-weight: 500;
				color: var(--main-front-color);
				line-height: 50rpx;
				margin-bottom: 20rpx;
			}

			.phone {
				color: var(--default-color3);
				font-size: 28rpx;
			}

			.txt {
				// width: 588rpx;
				height: 40rpx;
				font-size: 24rpx;

				font-weight: 400;
				color: var(--default-color3);
				line-height: 40rpx;
				display: flex;
				margin-top: 16rpx;

			}

			.zc {
				// width: 112rpx;
				margin-left: 20rpx;
				height: 40rpx;
				font-size: 24rpx;

				font-weight: 400;
				color: #1890FF;
				line-height: 40rpx;
				// position: absolute;
				// top: 10rpx;
				// right: 44rpx;
			}
		}

		.ipt {
			margin-top: 20rpx;
			position: relative;
			// border: 1px solid red;

			.man {
				// width: 72rpx;
				height: 34rpx;
				font-size: 24rpx;

				font-weight: 400;
				color: #666666;
				line-height: 34rpx;
			}

			.input {
				width: 662rpx;
				padding-left: 0rpx;
				color: var(--main-front-color);
			}
		}

		.submit {
			margin-top: 96rpx;
			width: 662rpx;
			position: absolute;
			bottom:40rpx;
			button {
				width: 100%;
				height: 120rpx;
				border-radius: 60rpx;
				background: var(--primary-button-color);
				border: none;
				color: #1e1e1e;
				font-size: 34rpx;
				line-height: 120rpx;
				font-size: 660;
			}

			view {
				font-size: 24rpx;
				color: var(--secondary-front-color);
				text-align: center;
				margin-top: 30rpx;

			}
		} 

		.yzm {
			font-size: 28rpx;
			font-weight: 400;
			position: absolute;
			right: 0;
			bottom: 30rpx;
			color: #63EAEE;
			padding-left: 16rpx;

		}

		.eject { 
			width: 662rpx;
			height: 440rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				margin-top: 48rpx;
				width: 120rpx;
				height: 120rpx;
			}

			text {
				margin-top: 16rpx;
				// width: 160rpx;
				height: 56rpx;
				font-size: 40rpx;

				font-weight: 400;
				color: var(--main-front-color);
				line-height: 56rpx;
			}

			.submit {
				width: 584rpx;
				height: 88rpx;
				margin: 48rpx 34rpx 48rpx 44rpx;
				font-size: 30rpx;
			}
		}

	}
</style>
