<template>
	<view>
		<view class="top_msg">为了账户安全，仅支持平台已身份证实名用户绑定银行卡</view>
		<view class="addbank">
			<!-- <view class="text">
				<view class="h1">
					绑定银行卡
				</view>
				<view class="txt">
					为了账户安全，仅支持平台已实名用户的银行
				</view>
			</view> -->
			<view class="ipt">
				<view class="man">
					<text>姓名</text>
				</view>
				<view class="Disable" @click='aa'>
					<text class="name">{{personal.name}}</text>
					<u-icon name="question-circle" color="#616161" size="32"></u-icon>
				</view>
			</view>
			<!-- <view class="ipt" @click='show_picker'>
				<view class="man">
					<text >银行卡类型</text>
				</view>
				<view class="input" style="padding:40rpx 0rpx;color: #fff;">
					<text v-if="bankName==''" style="color: gray;">请选择银行卡类型</text>
					<text v-else>{{bankName}}</text>
				</view>
				<u-select v-model="cartShow" :list="cartList" confirm-color="#63EAEE" cancel-color="var(--secondary-front-color)" placeholder="请选择银行卡类型" @confirm="cartConfirm"></u-select>
				<view class="icon_jiantou">
					<image v-if="!cartShow" src="https://cdn-lingjing.nftcn.com.cn/image/********/d727f4ac33ee62a55bd33dae9a776790_48x40.png" mode="widthFix"></image>				 
					<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/********/f510ad1106b025a19ccf6ae1f14b0b1e_48x40.png" mode="widthFix"></image>
				</view>
			</view> -->
			<view class="ipt">
				<view class="man" >
					<text>开户行</text>
				</view>
				<u-field class="input" margin="0" :class="{'active':checkendName}" v-model="branchName"
					:border-bottom='false' label-width='0' placeholder="请输入开户行" :clearable='false' maxlength='19'
					minlength='13' @focus="checkendName=true" @blur="checkendName=false">
				</u-field>
			</view>
			<view class="ipt" @click='picker' >
				<view class="man">
					<text>开户地区</text>
				</view>
				<!-- <u-field class="input " label-width='0' :border-bottom='false' placeholder="请选择开户地区" v-model="region"
					:clearable='false' :disabled='false' >
				</u-field> -->
				<view class="input" style="padding:40rpx 0rpx;color: #fff;">
					<text v-if="region==''" style="color: gray;">请选择开户地区</text>
					<text v-else>{{region}}</text>
				</view>
				<u-picker class="picker" mode="region" v-model="onpicker" title="选择开户地区" :area-code='["44", "4401"]'
					:params="params" @confirm='confirm' confirm-color="#63EAEE"
					cancel-color="var(--secondary-front-color)">
				</u-picker>
				<view class="icon_jiantou">
					<image v-if="!onpicker" src="https://cdn-lingjing.nftcn.com.cn/image/********/d727f4ac33ee62a55bd33dae9a776790_48x40.png" mode="widthFix"></image>				 
					<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/********/f510ad1106b025a19ccf6ae1f14b0b1e_48x40.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="ipt">
				<view class="man" >
					<text>卡号</text>
				</view>
				<view class="Card_number">
					<u-field type="number" class="input" :class="{'active':checkendId}" :border-bottom='false'
						v-model="banknum" label-width='0' placeholder="请输入卡号" :clearable='false' maxlength='19'
						minlength='13' @blur='blur' @focus="checkendId=true">
					</u-field>
				</view>
			</view>
			<view class="ipt" v-if="type==102">
				<view class="man" :class="{'white':checkendCVV}">
					<text v-show="securityCode!=''||checkendCVV">CVV安全码</text>
				</view>
				<view class="Card_number">
					<u-field type="number" class="input" :class="{'active':checkendCVV}" :border-bottom='false'
						v-model="securityCode" label-width='0' placeholder="请输入CVV安全码" :clearable='false' maxlength='19'
						minlength='13'  @focus="checkendCVV=true" @blur="checkendCVV=false">
					</u-field>
				</view>
			</view>
			<view class="ipt" v-if="type==102">
				<view class="man">
					<text v-show="expiredTime">卡号有效期</text>
				</view>
				<u-field class="input " label-width='0' :border-bottom='false' placeholder="请选择卡号有效期" v-model="expiredTime"
					:clearable='false' :disabled='true' @click='expiredTimePicker'>
				</u-field>
				<u-picker mode="time" v-model="isSelect"  start-year='2023' :default-time="yearMonth"
					confirm-color="#63EAEE" cancel-color="#b3b1b1" @confirm="confirmTime" :params="paramsTime"></u-picker>
				<view class="icon_jiantou">
					<image v-if="!isSelect" src="https://cdn-lingjing.nftcn.com.cn/image/********/d727f4ac33ee62a55bd33dae9a776790_48x40.png" mode="widthFix"></image>				 
					<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/********/f510ad1106b025a19ccf6ae1f14b0b1e_48x40.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="ipt">
				<view class="man" >
					<text >预留手机号</text>
				</view>
				<view class="Card_number">
					<!-- <input-bar v-model="form[item.key]" :item="item" v-for="item in inputList" :key="item.key"
						@click.native.stop="clickInput(item.type)"></input-bar> -->
					<u-field type="number" :class="{'active':checkendPhone}" class="input" :border-bottom='false'
						v-model="phone" label-width='0' placeholder="请输入预留手机号" :clearable='false' maxlength='11'
						@focus="checkendPhone=true" @blur="checkPhone()">
					</u-field>
					<view class="error" v-if="isPhoneMsg">
						<!-- <image style="width: 48rpx" src="@/static/imgs/public/warn_icon.png" mode="widthFix"> -->
						</image>
					</view>
					<text class="text" v-if="isPhoneMsg">
						请输入11位有效手机号
					</text>
				</view>
			</view>
		<!-- 	<view class="bank_list " @click="ph">
				查看支持银行
			</view> -->
			<view class="footer_but">
				<!-- <view class="xieyi" v-if="heightChange">
					<view class="p">
						<image @click="j_isAgree" v-if="!isAgree" src="../../../static/imgs/public/check.png" mode="">
						</image>
						<image @click="j_isAgree" v-else src="../../../static/imgs/public/checked.png" mode=""></image>
						<view class="msg">
							我已仔细阅读
							<text @click="nav_privacyAgreement()">《绑卡协议》</text>
							<text @click="nav_link()" v-if="isAgreement">《{{agreementName}}》</text>
						</view>
					</view>
				</view> -->
				<view class="submit">
					<button @click="config()">同意绑定银行卡</button>
				</view>
			</view>
			<u-popup v-model="show" mode="center" border-radius="0">

				<view class="eject">
					<view class="colse" @click="show=false">
						<!-- <image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image> -->
					</view>
					<view class="hh">
						持卡人说明
					</view>
					<view class="tet">
						您的实名信息展示如下
					</view>
					<view class="from">
						<view class="name">
							<view class="label">姓名</view>
							<view> {{personal.name}}</view>
						</view>
						<view class="num">
							<view class="label">证件号码</view>
							<view> {{personal.idNumber}}</view>
						</view>
					</view>
				</view>
			</u-popup>
			<!-- <u-modal v-model="isRegistration"
				 border-radius="30" :show-title="false" :show-confirm-button="false">
				<view class="new-modal-content">
					<view class="title_bg">
						<view class="icon"></view>
						温馨提示
					</view>
					<view class="modal-content">
						<p>你还未实名认证</p>
						请前往实名认证
					</view>
					<view class="showModal-btn">
						<view class="img_cancel" @click="nav_back()">取消</view>
						<view class="img_reasale" @click="nav_realName()">前往实名</view>
					</view>
				</view>
			</u-modal> -->
			<u-toast ref="uToast" />
		</view>
		<popup-bar v-model="isRegistration" title="实名认证" @cancel="nav_back" @confirm="nav_realName()">
		</popup-bar>
	</view>
</template>

<script>
	import popupBar from "@/components/public/PopupBar";
	export default {
		data() {
			return {
				heightChange: true,
				banknum: '', //卡号
				phone: '', //手机号
				show: false,
				personal: {},
				params: { //地区
					province: true,
					city: true,
					area: false
				},
				onpicker: false, //选择器开关
				region: '', //地区
				bankName:"",
				province: '', //省份
				city: '', //城市
				branchName: '', //开户行名称
				isAgree: true, //勾选
				isVersions: false,
				bankname: '****银行', //银行名字
				encryption: '', //加密号码
				agreementName: "",
				agreementLink: "",
				isAgreement: false,
				checkendId: false,
				checkendName: false,
				checkendPhone: false,
				isPhoneMsg: false,
				cartList: [],
				cartShow:false,
				securityCode:"",
				expiredTime:"",
				checkendCVV:false,
				type:101,
				isSelect:false,
				paramsTime: {
					year: true,
					month: true,
					day: false,
					hour: false,
					minute: false,
					second: false,
				}, 
				isRegistration:false
			}
		},
		onLoad(option) {
			let month = 0
			var date = new Date;
			this.month = date.getMonth()
			this.year = date.getFullYear()
			this.defaultNum = date.getMonth()
			// console.log(date.getMonth()+1)
			if (date.getMonth() < 10) {
				month = "0" + (Number(date.getMonth()) + 1)
			} else {
				month = (Number(date.getMonth()) + 1)
			}
			this.yearMonth = date.getFullYear() + "-" + month
			console.error(this.yearMonth)
			this.get()
			this.getBankList()
		},
		watch: {

		},
		methods: {
			//获取实名信息
			async get() {
				let res = await this.$api.java_authInfo()
				console.log(res)
				if (res.status.code == 0) {
					//处理名字  身份证号码加密
					this.personal = res.result
					console.log(this.personal)
					if(res.result.authStatus !=1){
						this.isRegistration= true
					}
				}
			},
			//卡号查询银行名称
			async bankquery() { 
				let res = await this.$api.bank_card_info({
					"cardNumber": this.banknum,
					type:this.type
				})
				console.log(res)
				if (res.status.code == 0) {
					this.bankname = res.result.bankName
					if (res.result.agreementName != "") {
						this.agreementName = res.result.agreementName
						this.agreementLink = res.result.agreementLink
						this.isAgreement = true
					} else {
						this.isAgreement = false
					}
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						icon: false,
						duration: 3000
					})
				}
			},
			//预绑卡
			async onbank() {
				uni.showLoading({
					title: ''
				});
				let res = await this.$api.bank_bind_pre({
					"type": this.type,
					"cardNumber": this.banknum, //银行卡号
					"phone": this.phone, //预留手机号
					"securityCode": this.securityCode, //信用卡必填 安全码
					"expiredTime": this.expiredTime, //信用卡必填 有效期 （yymm）
					"branchName": this.branchName, //支行名称
					"province": this.province, //开户省份
					"city": this.city, //开户城市
					'clientScene':1  ,//客户端类型 
					'bankName':this.bankName
				})
				if (res.status.code == 0) {
					uni.hideLoading();
					//处理隐藏
					this.encryption = this.phone.substring(0, 3) + "****" + this.phone.substring(this.phone.length -
						4);
					console.log(this.encryption)
					const orderData = {
						'preCode': res.result.preCode,
						'phone': this.encryption
					}
					uni.setStorageSync('orderData', orderData)
					this.banknum = '', //卡号
						this.phone = '', //手机号
						this.branchName = '', //支行名称
						this.province = "", //省份
						this.city = '', //城市
						this.region = '' //开户地区 
					this.bankname = '****银行' //银行名字
					this.$Router.push({
						name: 'bankphone'
					})
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						icon: false,
						duration: 3000
					})
				}
			},
			//卡号失去焦点
			blur() {
				this.checkendId = false
				if (this.banknum.length == 0) {

				} else {
					// this.bankquery()
				}

			},
			// 售出后查看跳转
			nav_VersionsDetails() {
				this.isVersions = false
				this.$Router.push({
					name: "realName",
				})
			},
			ph() {
				console.log('**********')
				this.$Router.push({
					name: 'banklist'
				})
			},
			aa() {
				console.log('111111')
				this.show = true
			},
			nav_privacyAgreement() {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: "宝付支付协议",
						link: "https://www.nftcn.com.cn/link/#/pages/index/payTreasureAgreement"
					}
				})
			},
			nav_link() {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: this.agreementName,
						link: this.agreementLink
					}
				})
			},
			//勾选同意
			j_isAgree() {
				this.isAgree = !this.isAgree
			},
			config() {
				if (this.banknum.length <= 12 || this.banknum == '') {
					// console.log('进来了')
					this.$refs.uToast.show({
						title: '请填写正确的银行卡',
						icon: false,
						duration: 3000
					})
				} else if (this.city == '' || this.province == '') {
					this.$refs.uToast.show({
						title: '请选择开户地区',
						icon: false,
						duration: 3000
					})
				} else if (this.branchName == '') {
					this.$refs.uToast.show({
						title: '请填写开户行名称',
						icon: false,
						duration: 3000
					})
				} else if (this.phone.length <= 10 || this.phone == '') {
					this.$refs.uToast.show({
						title: '请填写正确的手机号码',
						icon: false,
						duration: 3000
					})
				} else if (this.isAgree == false) {
					this.$refs.uToast.show({
						title: '请勾选协议',
						icon: false,
						duration: 100000
					})
				} else {
					this.onbank()
				}
			},
			//选择器开关
			picker() {
				this.onpicker = true
			},
			confirm(e) {
				console.log(e)
				this.province = e.province.label
				this.city = e.city.label
				this.region = this.province + this.city
			},
			checkPhone() {
				console.log(this.encryption)
				this.checkendPhone = false
				this.isPhoneMsg = false
				if (this.phone.length < 11) {
					this.isPhoneMsg = true
				}
			},
			show_picker() {
				this.cartShow=true
			},
			cartConfirm(e){
				this.bankName=e[0].label
				this.type=e[0].value
				if(this.banknum){
					// this.bankquery()
				}
				console.log(e)
			},
			expiredTimePicker(){
				this.isSelect=true
			},
			confirmTime(e){
				console.log(e)
				this.expiredTime = e.year + "-" + e.month
			},
			async getBankList() {
				let res = await this.$api.bankList({})
				if (res.status.code == 0) {
					res.result.list.forEach((item)=>{
						this.cartList.push({
							label:item.name,
							value:item.name
						})
					})
					// this.$router.go(0)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			nav_realName(){
				this.$Router.push({
					name:"realName"
				})
			},
			nav_back(){
				this.$Router.back()
			}
		},
		mounted() {

		},
		components:{
			popupBar
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-flex-1 {
		margin: 0 !important;
	}

	.top_msg {
		background-color:rgba(99, 234, 238, 0.3);
		color: #63EAEE;
		font-size: 24rpx;
		width: 100%;
		padding: 30rpx 40rpx;
		position: fixed;
		/* #ifdef H5 */
		top:88rpx;
		/* #endif */
		/* #ifdef APP */
		top: 0rpx;
		/* #endif */
		left: 0rpx;
		z-index: 99;
	}

	.addbank {
		// border: 1px solid red;
		padding: 120rpx 40rpx 40rpx 40rpx;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		flex-direction: column;
		align-items: center;
		padding-bottom: 240rpx; 

		// background-color: var(--message-box-point-color);
		.text {
			margin-bottom: 48rpx;
			width: 662rpx;
			// background-color: #FAcc22;
			position: relative;

			.h1 {
				font-size: 36rpx;

				font-weight: 500;
				color: #63EAEE;
				line-height: 50rpx;
			}

			.txt {
				// width: 588rpx;
				height: 40rpx;
				font-size: 24rpx;

				font-weight: 400;
				color: var(--secondary-front-color);
				line-height: 40rpx;
				display: flex;
				margin-top: 24rpx;
			}

			.zc {
				color: #089E97;
				margin-left: 10rpx;
				// width: 112rpx;
				// margin-left: 20rpx;
				// height: 40rpx;
				// font-size: 24rpx;
				// 
				// font-weight: 400;

				// line-height: 40rpx;
				// text-decoration: underline;
				// position: absolute;
				// top: 10rpx;
				// right: 44rpx;
			}
		}

		.ipt {
			// border: 1px solid red;
			margin-bottom: 48rpx;
			position: relative;
			.man {
				// width: 72rpx;
				height: 34rpx;
				font-size: 24rpx;
				font-weight: 500;
				color: #fff;
				line-height: 34rpx;
				font-weight: 600;

				text {
					font-size: 24rpx;
					margin-right: 10rpx;
				}

				&.white {
					color: var(--message-box-point-color);
				}
			}

			.input {
				width: 662rpx;
				padding-left: 0rpx;
				// background: var(--dialog-bg-color);
				border-bottom: 1px solid #53505D;
				padding:40rpx 0rpx;
				font-size: 28rpx;
				color: #fff;
				&.active {
					border-bottom: 1px solid #63EAEE;
				}

				.uni-input-placeholder {
					font-size: 28rpx;
					// font-size: 50rpx !important;
				}
				
			}
			.icon_jiantou{
				position: absolute;
				right:10rpx;
				width:24rpx;
				top:80rpx;
				image{
					width:24rpx;
				}
			}
			.Disable {
				width: 662rpx;
				font-size: 28rpx;
				padding: 20rpx 28rpx;
				text-align: left;
				position: relative;
				color: var(--main-front-color);
				padding-left: 0rpx;
				border-bottom: 1px solid #53505D;
				// border: 1px solid #ccc;
				// background: var(--dialog-bg-color);
				// border-bottom: 0.5rpx solid #e4e7ed;
				display: flex;
				justify-content: space-between;

				.name {
					height: 40rpx;
					line-height: 40rpx;
					// padding-left: 24rpx;
				}
			}

			.Card_number {
				position: relative;

				text {
					display: inline-block;
					font-size: 24rpx;

					font-weight: 400;
					color: #63EAEE;
					margin-top: 16rpx;
					margin-bottom: 0;
					position: relative;
				}

				.error {
					position: absolute;
					top: 20rpx;
					right: 20rpx;
				}

				// padding-bottom: 26rpx;
				// border-bottom: 0.5rpx solid #e4e7ed;
			}

		}


		.footer_but {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 0;
			width: 100%;
			padding: 20rpx 40rpx 40rpx 40rpx;

			.submit {
				width: 662rpx;
				margin-top: 28rpx;
				button {
					width: 667rpx;
					height: 120rpx;
					border-radius: 0;
					background: var(--primary-button-color);
					border: none;
					color: #141414;
					font-size: 34rpx;
					line-height: 120rpx;
					font-size: 550;
					font-weight: 600;
					border-radius: 60rpx;
				}

				view {
					font-size: 24rpx;
					color: var(--secondary-front-color);
					text-align: center;
					margin-top: 30rpx;

				}
			}

			.xieyi {
				width: 662rpx;

				.p {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					image {
						width: 32rpx;
						height: 32rpx;
						margin-right: 10rpx;
					}

					.msg {
						font-size: 24rpx;
						color: var(--user-text-color);

						text {
							color: var(--active-color);
						}
					}
				}
			}
		}


		.eject {
			width: 662rpx;
			padding: 48rpx 32rpx;
			background-color:var(--main-bg-color);
			position: relative;
			color:#fff;
			.colse {
				image {
					position: absolute;
					right: 12rpx;
					top: 12rpx;
					width: 48rpx;
					height: 48rpx;
				}
			}

			.hh {
				width: 100%;
				height: 56rpx;
				font-size: 32rpx;
				font-weight: 500;
				line-height: 56rpx;
				text-align: center;
			}

			.tet {
				text-align: center;
				width: 100%;
				margin-top: 20rpx;
				height: 40rpx;
				font-size: 28rpx;
				font-weight: 400;
				line-height: 40rpx;
			}

			.from {
				margin-top: 50rpx;
				width: 598rpx;
				height: 204rpx;
				padding: 24rpx 24rpx 28rpx 24rpx;

				.name {
					height: 40rpx;
					font-size: 28rpx;
					line-height: 40rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-weight: 400;

					.label {
						width: 140rpx;
						text-align-last: justify;
						margin-right: 32rpx;
					}
				}

				.type {
					height: 40rpx;
					font-size: 28rpx;
					line-height: 40rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-weight: 400;
					color: #F9F9F9;

					.label {
						width: 140rpx;
						text-align-last: justify;
						color: #616161;
						margin-right: 32rpx;
					}

					font-weight: 400;
					margin-top: 16rpx;
				}

				.num {
					height: 40rpx;
					font-size: 28rpx;
					line-height: 40rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-weight: 400;
					font-weight: 400;
					margin-top: 16rpx;

					.label {
						width: 140rpx;
						text-align-last: justify;
						margin-right: 32rpx;
					}
				}
			}
		}

		//弹出框样式
		.modal-btn {
			padding: 10rpx 70rpx 50rpx 70rpx;

			.mb-cancel,
			.mb-confirm {
				height: 64rpx;
				line-height: 64rpx;
				text-align: center;
				border-radius: 37rpx;
				font-size: 30rpx;
				padding: 0rpx 20rpx;
			}

			.mb-cancel {
				color: #666;
				// background-color: #eee;
			}

			.mb-confirm {
				color: #eee;
				background-color: #BB3835;
			}
		}
	}

	.bank_list {
		width: 100%;
		color: var(--message-box-point-color);
		font-size: 24rpx;
		text-align: left;
		margin-top: 40rpx;
	}
	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;
	
		.icon {
			position: absolute;
			left: 0rpx;
			top: 20rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/********/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}
	
	.new-modal-content {
		padding:35rpx 40rpx;
		.success_img{
			display: flex;
			justify-content: center;
			align-items: center;
			image{
				width:160rpx;
				height:160rpx;
			}
		}
		.modal-content{
			padding:35rpx 0rpx;
			border-bottom:1rpx solid #53505D;
			font-size:28rpx;
			color:#fff;
			text-align: center;
			p{
				margin-bottom:20rpx;
			}
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color:#fff;
			}
	
			.img_cancel {
				border: 1px solid #fff;
			}
			.img_reasale {
				background-color: #63EAEE;
				color:#141816;
			}
		}
	}
</style>
