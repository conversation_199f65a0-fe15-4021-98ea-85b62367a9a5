<template>
	<view class="banklist">
		<view class="list" v-for="(item,index) in list" :key="index">
			<view class="yuan">
				<image :src="item.icon" mode="widthFix"></image>
			</view>
			<view class="yh">
				{{item.name}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: []
			}
		},
		onLoad(option) {
			this.get()

		},
		watch: {

		},
		methods: {
			//获取榜单
			async get() {
				let res = await this.$api.bankList()
				console.log(res)
				if (res.status.code == 0) {
					this.list = res.result.list
					// this.$router.go(0)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},

		},
		mounted() {

		},
	}
</script>

<style lang="scss">
	.banklist {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		flex-wrap: wrap;
		align-items: center;
		padding-bottom: 30rpx;

		.list {
			width: 662rpx;
			height: 120rpx;
			// background-color: #C2C2C2;
			display: flex;
			align-items: center;
			color: var(--main-front-color);
			border-bottom: 1rpx solid #1E1E1E;

			.yuan {
				width: 44rpx;
				height: 44rpx;
				// background-color: #000000;
				border-radius: 22rpx;

				image {
					width: 100%;
					height: auto;
					border-radius: 22rpx;
				}
			}

			.yh {
				margin-left: 16rpx;
				width: 602rpx;
				height: 90rpx;
				line-height: 90rpx;
			}
		}
	}
</style>
