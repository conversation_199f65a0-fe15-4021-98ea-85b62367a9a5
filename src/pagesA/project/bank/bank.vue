<template>
	<view class="bank">
		<view class="cart" v-if="llp_list!=''">
			<view class="content">
				<view v-if="llp_list!=''">
					<view class="item" v-for="(item, index) in llp_list" :key="index"
						:style="{background: 'url(' + item.background + ')','background-size': '100%'}">
						<view class="bankimg">
							<image :src="item.icon" mode="widthFix"></image>
						</view>
						<view class="bankNumber">
							<view class="bankTxt">
								<view class="box">
									<view class="bankName">
										{{ item.bankName }}
									</view>
									<u-button class="Unbound" size="mini" @click="unbound(item,5)" :hair-line="false">解除绑定
									</u-button>
								</view>
								<view class="bankType">&nbsp;</view>
								<!-- <view class="bankType" v-if="item.type == 101"> 储蓄卡 </view>
								<view class="bankType" v-if="item.type == 102"> 信用卡 </view> -->
							</view>
							<view class="Number">
								<view class=""> **** </view>
								<view class=""> **** </view>
								<view class=""> **** </view>
								<view class="">
									{{ item.bankCardNumber }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="bank_null" v-else>
			<view class="img">
			</view>
			<view class="h1_text">您暂时没有绑定的银行卡</view>
			<view class="msg">绑定银行卡，让您的支付流程更便捷</view>
			<view class="but" @click="addbank()">添加银行卡</view>
		</view>
		<!-- 银行卡解绑 -->
		<u-modal v-model="show" :title="itemTitle" border-radius="0" 
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn">
				<view class="but mb-cancel" @click="unpopup">取消</view>
				<view class="but mb-confirm" @click="unbank">确定</view>
			</view>
		</u-modal>
		<u-button class="tj" :custom-style="{ border:'none' }" :hair-line="false" :ripple="true" @click="addbank()"
			v-if="llp_list!=''">
			<view class="box">
				<view class="text"> 添加银行卡 </view>
			</view>
		</u-button>
		<popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false" @confirm="nav_realName()">
		</popup-bar>
	</view>
</template>

<script>
	import popupBar from "@/components/public/PopupBar";
	export default {
		data() {
			return {
				llp_list: [],
				show: false,
				id: "",
				isVersions: false,
				certification: "",
				isRegistration: false,
				titleObject: {
					"padding-top": "84rpx",
					"color":"#fff"
				},
				itemTitle: "",
				itemList: [{
					head: "连连支付",
					open: true
				}, {
					head: "宝付支付",
					open: true
				}],
				changeIndex: 0,
				headStyle: {
					'padding': "24rpx",
					'color': "#fff",
					'background': '#121212'
				},
				bodyStyle: {
					'height': 'none'
				},
				llp_flag:false,
				bf_flag:false
			};
		},
		onShow() {
			// this.getBank();
			this.getLianlianBank()
			this.certification = uni.getStorageSync("certification");
		},
		methods: {
			//解绑弹窗
			unbound(e, type) {
				this.show = true;
				this.itemTitle = "确认解绑该银行卡(" + e.bankCardNumber + ")吗？";
				if (type == 5) {
					this.id = e.id;
				} else {
					this.protocolNo = e.protocolNo,
					this.payMethod = 12
				}
			},
			unpopup() {
				this.show = false;
			},
			unbank() {
				this.getunbank();
			},
			//添加银行卡
			addbank() {
				// #ifdef H5
				uni.setStorageSync("addbank_link", window.location.href);
				// #endif
				if (this.certification == 1 ) {
					this.$Router.push({
						name:"addbank"
					});
				} else {
					this.isRegistration = true;
				}
			},
			//解绑
			async getunbank() {
				let res = await this.$api.bank_unbind({
					id: this.id,
					protocolNo: this.protocolNo,
					payMethod: this.payMethod
				});
				if (res.status.code == 0) {
					this.show = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
					this.getLianlianBank()
				} else {
					this.show = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
				}
			},
			//获取连连支付银行卡列表
			async getLianlianBank() {
				let res = await this.$api.bank_list({
				});
				console.log(res);
				if (res.status.code == 0) {
					this.llp_list = res.result.list;
					console.log(this.llp_list)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
				}
			},
			//验证实名信息
			async getinfo() {
				let res = await this.$api.bank_list();
				console.log(res);
			},
			nav_realName() {
				this.isRegistration = false;
				console.log(uni.getStorageSync("authStatus"));
				if (uni.getStorageSync("authStatus") == 30) {
					this.$Router.push({
						name: "authentication",
					});
				} else {
					this.$Router.push({
						name: "realName",
					});
				}
			},
			llcheck() {
				this.llp_flag = !this.llp_flag
			},
			bfcheck() {
				this.bf_flag = !this.bf_flag
			},
			
		},
		components:{
			popupBar
		}
	};
</script>

<style lang="scss" scoped>
	page {
		// background-color: #1E1E1E;
	}

	.bank {
		padding-bottom: 150rpx;
	}

	.head_title {
		background-color: var(--main-bg-color);
		text-align: left;
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		padding: 0rpx 30rpx;
		font-weight: 600;
		color: #F9F9F9;
		font-size: 30rpx;
		display:flex;
		justify-content: space-between;
		align-items: center;
		image{
			width:30rpx;
		}
	}

	.content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		flex-wrap: wrap;
		align-items: center;
		transition: display .25s;
		.item {
			margin-bottom: 20rpx;
			width: 702rpx;
			height: 268rpx;
			background-color: #007aff;
			border-radius: 12rpx;
			display: flex;
			background-size: 100%;
			position: relative;

			.bankimg {
				margin-top: 32rpx;
				margin-left: 32rpx;
				width: 90rpx;
				height: 90rpx;
				background-color: var(--message-box-point-color);
				border-radius: 50%;
				// background-color: #F0AD4E;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 8rpx;

				image {
					width: 100%;
				}
			}

			.bankNumber {
				width: 578rpx;
				font-size: 30rpx;
				font-weight: 700;
				color: var(--message-box-point-color);

				.bankTxt {
					.box {
						display: flex;
						justify-content: space-between;

						// margin-top: 22rpx;
						.bankName {
							margin-top: 32rpx;
							margin-left: 16rpx;
							width: 274rpx;
							height: 44rpx;
							font-size: 32rpx;
							font-weight: 600;
							color: #fefffe;
						}

						.Unbound {
							margin: 0;
							font-size: 24rpx;
							// width: 112rpx;
							background-color: transparent;
							padding: 10rpx 20rpx;
							line-height: 20rpx;
							border-radius: 8rpx;
							border: 1px solid #fff;
							position: absolute;
							right: 20rpx;
							top: 20rpx;
							color: #fff;
							font-weight: 500;
						}
					}

					.bankType {
						margin: 0;
						// width: 72rpx;
						height: 32rpx;
						margin-left: 16rpx;
						margin-top: 4rpx;
						font-size: 24rpx;
						font-weight: 400;

						color: #fefffe;
						line-height: 32rpx;
					}
				}

				.Number {
					display: flex;
					justify-content: space-between;
					width: 350rpx;
					height: 45rpx;
					margin-top: 36rpx;
					margin-left: 16rpx;
					font-size: 32rpx;

					font-weight: 600;
					color: #fefffe;
					line-height: 45rpx;
				}
			}
		}


	}

	.popup {
		width: 662rpx;
		height: 328rpx;
		border-radius: 6px;
		padding: 48rpx 32rpx;

		.hh {
			width: 200rpx;
			height: 56rpx;
			font-size: 40rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 56rpx;
		}

		.text {
			margin-top: 20rpx;
			height: 40rpx;
			font-size: 28rpx;

			font-weight: 400;
			color: #666666;
			line-height: 40rpx;
			margin-bottom: 70rpx;
		}

		.flex {
			// margin-top: 100rpx;
			display: flex;

			.submit {
				width: 228rpx;
				height: 64rpx;
				background-color: #000;
				color: var(--message-box-point-color);
				line-height: 64rpx;
			}

			.submit1 {
				width: 228rpx;
				height: 64rpx;
				background-color: var(--message-box-point-color);
				color: #999;
				line-height: 64rpx;
			}
		}
	}

	.modal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 84rpx;
		border-top: 1rpx solid #ccc;

		.but {
			width: 50%;
			text-align: center;
			line-height: 88rpx;
			height: 88rpx;
		}

		.mb-cancel {
			color:  rgba(255, 255, 255, 0.5);
		}

		.mb-confirm {
			color: #63EAEE;
			border-left: 1rpx solid #ccc;
		}
	}

	.tj {
		width: 100%;
		border-radius: 0;
		position: fixed;
		z-index: 11;
		bottom: 0rpx;
		left: 0rpx;
		padding: 0rpx;
		height: 160rpx;
		background-color:transparent;
		.box {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 100rpx;
			margin-bottom: 20rpx;
			border:2rpx solid #63EAEE;
			color:#63EAEE;
			border-radius:24rpx;
			.yuan {
				width: 44rpx;
				height: 44rpx;
			}
			.text {
				margin-left: 12rpx;
				width: 150rpx;
				height: 44rpx;
				font-size: 30rpx;
				color:#63EAEE;
				line-height: 44rpx;
			}
		}
	}

	.bank_null {
		text-align: center;
		padding: 200rpx 0rpx;

		>.img {
			display: flex;
			justify-content: center;

			image {
				width: 48rpx;
				height: 48rpx;
				margin-bottom: 40rpx;
			}
		}

		.h1_text {
			font-size: 36rpx;
			color: #fff;
			font-weight: 600;
			margin-bottom: 40rpx;
		}

		.msg {
			color: #616161;
			font-size: 28rpx;
		}

		.but {
			width: 550rpx;
			height: 80rpx;
			line-height: 80rpx;
			color: #63EAEE;
			border: 2rpx solid #63EAEE;
			margin: 0 auto;
			margin-top: 40rpx;
		}
	}
	.msg_div_null{
		padding:93rpx 30rpx;
		image{
			width:194rpx;
			margin:0 auto 60rpx auto;
		}
		.null_text{
			width:600rpx;
			margin:0 auto;
			line-height:42rpx;
			color:#838383;
			font-size:25rpx;
			text-align: center;
		}
	}
</style>
