<template>
    <view>

        <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false" v-if="!platform"
            :background="{ backgroundColor: 'var(--main-bg-color)' }" title="我的年度报告"
            title-color="var(--main-front-color)" :custom-back="back">
            <view slot="right" class="search-box">
                2024
            </view>
        </u-navbar>

        <view v-if="step == 1" class="step-one">
            <view class="bvbg" :style="{ height: Ydata.allBuyCount ? '1177rpx' : '1053rpx' }">
                <text class="title" v-if="showtitle">在2024年，我成为：</text>
                <image class="bg" mode="widthFix" :src="getImageByViewType(Ydata.viewType)" />
                <view class="font-box">
                    <text class="title1">在2024年，我总共买入了：</text>

                    <text class="title2"><text class="bold">{{ formatAmount(Ydata.allBuyCount) }}</text> 件 数字藏品</text>

                    <text class="title3"> {{ Ydata.allBuyCount ? '其中，我买入最多的数字藏品是' : '2025年，给自己立个Flag：' }}</text>


                    <view class="flag">
                        <view v-if="Ydata.allBuyCount" style="text-align: center;">
                            <view class="coll">
                                <image :src="Ydata.buyMaxNumCtidCover" />
                            </view>
                            <view class="ctidName">{{ Ydata.buyMaxNumCtidName }}</view>
                        </view>
                        <view v-else class="flag2">
                            我要至少拥有一件数字藏品！
                        </view>
                    </view>
                </view>
            </view>
            <view class="btn" v-if="!Ydata.allBuyCount" @click="step = 9">
                <!-- <view @click="JumpSale">立即拥有一张藏品</view>
                <view @click="show = true">分享我的Flag</view> -->
                <view class="next">
                    下一页
                </view>
            </view>
            <view class="btn" v-else @click="step += 1">
                <view class="next">
                    下一页
                </view>
            </view>
        </view>

        <view v-if="step == 2" class="step-two">
            <view class="bvbg" :style="{ height: '1053rpx' }">
                <view class="box">
                    <text class="stitle">在2024年，我总一共消费了： </text>
                    <text class="boldtitle">{{ formatCurrency(Ydata.allBuyAmount) }}</text>
                </view>
                <view class="box">
                    <text class="stitle">我大概盈利了：</text>
                    <view v-if="Ydata.a != Ydata.b" class="range">
                        <text class="bold">{{ formatCurrency(Ydata.a) }}</text>
                        <text class="bold">~</text>
                        <text class="bold">{{ formatCurrency(Ydata.b) }}</text>
                    </view>
                    <text v-else class="boldtitle">{{ formatCurrency(Ydata.a) }}</text>
                </view>
                <view class="box">
                    <text class="stitle">在所有用户中，我排名：</text>
                    <text class="boldtitle">第{{ formatAmount(Ydata.viewPm) }}名</text>
                </view>
            </view>
            <view class="btn" style="margin-top: 198rpx;" @click="step += 1">
                <view class="next">
                    下一页
                </view>
            </view>
        </view>

        <view v-if="step == 3" class="step-three">
            <view class="bvbg" :style="{ height: '1053rpx' }">
                <view style="height: 55rpx;"></view>

                <view class="report">
                    <text class="report_title">在2024年，我买入的第一张数字藏品：</text>
                    <view class="ctid">
                        <image :src="Ydata.firstBuyCtidCover" />
                        <text>{{ Ydata.firstBuyCtidName }}</text>
                    </view>
                </view>
                <view style="height: 99rpx;"></view>
                <view class="report">
                    <text class="report_title">在2024年，我买入的单价最高的一张数字藏品：</text>
                    <view class="ctid">
                        <image :src="Ydata.buyMaxPriceCtidCover" />
                        <text>{{ Ydata.buyMaxPriceCtidName }}</text>
                    </view>
                </view>
            </view>
            <view class="btns" style="margin-top: 201rpx;"
                v-if="Ydata.yingLiMaxSellPrice - Ydata.yingLiMaxBuyPrice > 0 && Ydata.yingLiMaxAmount > 0">
                <view @click="step -= 1" class="btn_prev">
                    上一页
                </view>
                <view @click="step += 1" class="btn_next">
                    下一页
                </view>
            </view>

            <!-- <view class="btn" style="margin-top: 201rpx;" @click="step = 5"
                v-else-if="Ydata.yingLiMaxSellPrice - Ydata.yingLiMaxBuyPrice <= 0 && Ydata.openBarAllCount > 0">
                <view class="next">
                    下一页
                </view>
            </view> -->

            <!-- v-if="Ydata.openBarAllCount > 0" -->
            <view class="btn" v-else @click="step = 9">
                <view class="next">
                    下一页
                </view>
                <!-- <view style="margin-top: 198rpx;" @click="show = true" class="share">分享我的年度报告，获得邀请奖励</view> -->
            </view>
        </view>

        <view v-if="step == 4" class="step-four">
            <view class="bvbg" :style="{ height: '1053rpx' }">
                <view style="height: 55rpx;"></view>

                <view class="report">
                    <text class="report_title">在2024年，我获利最多的数字藏品：</text>
                    <view class="ctid">
                        <image :src="Ydata.yingLiMaxCtidCover" />
                        <text>{{ Ydata.yingLiMaxCtidName }}</text>
                    </view>
                </view>
                <view style="height: 99rpx;"></view>
                <view class="report">
                    <view style="display: flex;flex-direction: column;">
                        <text class="report_title">在2024年，交易该数字藏品，我一共获利：</text>
                        <!-- <view class="ctid"> -->
                        <text class="bold" style="margin-top:62rpx">{{ formatCurrency(Ydata.yingLiMaxAmount) }}</text>
                    </view>
                    <!-- </view> -->
                </view>
            </view>
            <!-- v-if="Ydata.openBarAllCount > 0" -->
            <view class="btns" style="margin-top: 201rpx;">
                <view @click="step -= 1" class="btn_prev">
                    上一页
                </view>
                <view @click="step = 9" class="btn_next">
                    下一页
                </view>
            </view>
            <!-- <view class="btn" v-else @click="step = 9">
                <view class="next">
                    下一页
                </view>
            </view> -->
        </view>

        <view v-if="step == 5" class="step-five">
            <view class="bvbg" :style="{ height: '1053rpx' }">
                <view style="height: 55rpx;"></view>

                <view class="report">
                    <text class="report_title">在2024年，我参与了开杠的交易。</text>
                </view>
                <view style="height: 132rpx;"></view>
                <view class="report">

                    <view style="display: flex;flex-direction: column;"
                        v-if="Ydata.openBarAllClouse > 0 && Ydata.openBarYingLiMaxAmount > 0">
                        <text class="report_title">我交易盈利最多的一笔记录：</text>
                        <view style="display: flex;align-items: center;margin-top:62rpx">
                            <text class="bold" style="">{{ formatCurrencyPlus(Ydata.openBarYingLiMaxAmount) }}</text>
                            <view style="display: flex;flex-direction: column;margin-left: 24rpx;" class="text_msg">
                                <text>{{ Ydata.yingLiMaxSymbol || 'BTC-USDT' }}</text>
                                <text style="margin-top: 9rpx;">{{ Ydata.openBarYingLiMaxCreate }}{{
                                    Ydata.openBarYingLiMaxStatus == 0 ? '平仓' : '未平仓'
                                }}</text>
                            </view>
                        </view>
                    </view>

                    <view style="display: flex;flex-direction: column;" v-else>
                        <text class="report_title">我的第一笔交易记录：</text>
                        <view style="display: flex;align-items: center;margin-top:62rpx">
                            <text class="bold" style="">{{ formatCurrency(Ydata.openFirstPrice) }}</text>
                            <view style="display: flex;flex-direction: column;margin-left: 24rpx;" class="text_msg">
                                <text>{{ Ydata.firstTradeSymbol || 'BTC-USDT' }}</text>
                                <text style="margin-top: 9rpx;">{{ Ydata.openFirstCreate }}{{
                                    Ydata.openBarYingLiMaxStatus
                                        == 0 ? '平仓' : '未平仓'
                                }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view style="height: 132rpx;"></view>

                <view class="report">

                    <view style="display: flex;flex-direction: column;">
                        <text class="report_title">我的累计开杠交易数据：</text>
                        <view style="height: 65rpx;"></view>
                        <view class="open">
                            <view>
                                <text class="bold" style="line-height: 60rpx;">{{ formatCurrency(Ydata.openBarAllAmount)
                                    }}</text>
                                <text class="text">总金额</text>
                            </view>
                            <view style="width: 128rpx;"></view>
                            <view>
                                <text class="bold" style="line-height: 60rpx;">{{ formatAmount(Ydata.openBarAllCount)
                                    }}</text>
                                <text class="text">笔</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="btns">
                <!-- <view style="margin-top: 198rpx;" @click="show = true" class="share">分享我的年度报告，获得邀请奖励</view> -->
                <view style="margin-top: 198rpx;" @click="step = 9" class="share">下一页</view>
            </view>
        </view>

        <view v-if="step == 9" class="step-nine">
            <view class="bvbg" :style="{ height: '1053rpx' }">
                <view style="height: 75rpx;"></view>
                <view class="report">
                    <text class="report_title">在2024年，感谢您的一路陪伴，我们为您准备了
                        <br>
                        <text style="margin-top: 40rpx;display: block;"> 丰厚的礼物：</text>
                    </text>
                    <view class="ctid">
                        <text style="display: block;margin-top: 88rpx;">{{ collection.name }}</text>

                        <image :src="collection.image" />
                        <text style="letter-spacing: 2rpx;">{{ 'x' + collection.num + '份' }}</text>
                    </view>
                </view>
                <view class="btn" @click="showShare" style="margin-top: 98rpx;">
                    <view class="next">
                        分享我的年度报告，并领取平台礼物
                    </view>
                </view>
                <view class="center">
                    <p>分享您的年度报告，平台将在本活动结束之后</p>
                    <br>
                    <p> 将上述数藏礼物自动送到您的账户之中</p>
                </view>
            </view>

        </view>

        <u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
            <div class='sk-wave'></div>
            <view class="text_msg"
                style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                跳转登录中...
            </view>
        </u-modal>

        <u-popup v-model="show" mode="center">
            <view class="popup-content" id="mainbody">
                <view class="header">
                    <view class="title">
                        <image
                            src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/5451b3060906eb73e0db0be46faea361_668x56.png" />
                    </view>
                    <view class="subtitle">
                        <image src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250117/6c468558c177b5caab9d1c0e2cda994b_1044x128.png" />
                    </view>
                    <view class="year">2024</view>

                </view>
                <view class="content">
                    <text class="summary">在2024年，我总共买入了：</text>
                    <view class="count"><text class="bold">{{ formatAmount(Ydata.allBuyCount) + ' ' }}</text>件 数字藏品
                    </view>

                    <view v-if="!Ydata.allBuyCount" class="no-data">
                        <text class="flag-title">2025年，给自己立个FLAG：</text>
                        <text class="flag">我要至少拥有一件数字藏品！</text>
                    </view>

                    <view v-else class="buy_list">
                        <text class="title">其中，我买入最多的数字藏品是：</text>
                        <view class="item">
                            <view class="coll">
                                <image :src="Ydata.buyMaxNumCtidCover" />
                            </view>
                            <view class="ctidName">{{ Ydata.buyMaxNumCtidName }}</view>
                        </view>
                    </view>

                    <view class="most_buy" v-if="Ydata.allBuyCount">
                        <view class="box">
                            <text class="stitle">在2024年，我总一共消费了： </text>
                            <text class="boldtitle">{{ formatCurrency(Ydata.allBuyAmount) }}</text>
                        </view>
                        <view class="box">
                            <text class="stitle">我大概盈利了：</text>
                            <view v-if="Ydata.a != Ydata.b" class="range">
                                <text class="bold">{{ formatCurrency(Ydata.a) }}</text>
                                <text class="bold">~</text>
                                <text class="bold">{{ formatCurrency(Ydata.b) }}</text>
                            </view>
                            <text v-else class="boldtitle">{{ formatCurrency(Ydata.a) }}</text>
                        </view>
                        <view class="box">
                            <text class="stitle">在所有用户中，我排名：</text>
                            <text class="boldtitle">第{{ formatAmount(Ydata.viewPm) }}名</text>
                        </view>
                        <view style="height: 107rpx;"></view>
                        <view class="report">
                            <text class="report_title">在2024年，我买入的第一张数字藏品：</text>
                            <view class="ctid">
                                <image :src="Ydata.firstBuyCtidCover" />
                                <text>{{ Ydata.firstBuyCtidName }}</text>
                            </view>
                        </view>
                        <view style="height: 107rpx;"></view>

                        <view class="report">
                            <text class="report_title">在2024年，我买入的单价最高的一张数字藏品：</text>
                            <view class="ctid">
                                <image :src="Ydata.buyMaxPriceCtidCover" />
                                <text>{{ Ydata.buyMaxPriceCtidName }}</text>
                            </view>
                        </view>

                        <view v-if="Ydata.yingLiMaxSellPrice - Ydata.yingLiMaxBuyPrice > 0">
                            <view style="height: 55rpx;"></view>
                            <view class="report">
                                <text class="report_title">在2024年，我获利最多的数字藏品：</text>
                                <view class="ctid">
                                    <image :src="Ydata.yingLiMaxCtidCover" />
                                    <text>{{ Ydata.yingLiMaxCtidName }}</text>
                                </view>
                            </view>
                            <view style="height: 99rpx;"></view>
                            <view class="report">
                                <view style="display: flex;flex-direction: column;">
                                    <text class="report_title">在2024年，交易该数字藏品，我一共获利：</text>
                                    <!-- <view class="ctid"> -->
                                    <text class="bold" style="margin-top:62rpx">{{ formatCurrency(Ydata.yingLiMaxAmount)
                                        }}</text>
                                </view>

                                <!-- </view> -->
                            </view>
                        </view>

                        <!-- <view v-if="Ydata.openBarAllCount > 0">
                            <view style="height: 107rpx;"></view>
                            <view class="report">
                                <text class="report_title">在2024年，我参与了<text style="color: #DE86FF;">开杠</text>的交易。</text>
                            </view>
                            <view style="height: 132rpx;"></view>
                            <view class="report">
                                <view style="display: flex;flex-direction: column;"
                                    v-if="Ydata.openBarYingLiMaxStatus == 0">
                                    <text class="report_title">我交易盈利最多的一笔记录：</text>
                                    <view style="display: flex;align-items: center;margin-top:62rpx">
                                        <text class="bold" style="">{{ formatCurrencyPlus(Ydata.openBarYingLiMaxAmount)
                                            }}</text>
                                        <view style="display: flex;flex-direction: column;margin-left: 24rpx;"
                                            class="text_msg">
                                            <text>{{ Ydata.yingLiMaxSymbol || 'BIT' }}</text>
                                            <text style="margin-top: 9rpx;">{{ Ydata.openBarYingLiMaxCreate }}{{
                                                Ydata.openBarYingLiMaxStatus == 0 ? '平仓' : '未平仓'
                                            }}</text>
                                        </view>
                                    </view>
                                </view>
                                <view style="display: flex;flex-direction: column;"
                                    v-if="Ydata.openBarYingLiMaxStatus == 1">
                                    <text class="report_title">我的第一笔交易记录：</text>
                                    <view style="display: flex;align-items: center;margin-top:62rpx">
                                        <text class="bold" style="">{{ formatCurrency(Ydata.openFirstPrice) }}</text>
                                        <view style="display: flex;flex-direction: column;margin-left: 24rpx;"
                                            class="text_msg">
                                            <text>{{ Ydata.firstTradeSymbol || 'BIT' }}</text>
                                            <text style="margin-top: 9rpx;">{{ Ydata.openFirstCreate +
                                                Ydata.openBarYingLiMaxStatus
                                                == 0 ? '平仓' : '未平仓'
                                                }}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view style="height: 132rpx;"></view>

                            <view class="report">

                                <view style="display: flex;flex-direction: column;">
                                    <text class="report_title">我的累计开杠交易数据：</text>
                                    <view style="height: 65rpx;"></view>
                                    <view class="open">
                                        <view>
                                            <text class="bold" style="line-height: 60rpx;">{{
                                                formatCurrency(Ydata.openBarAllAmount)
                                            }}</text>
                                            <text class="text">总金额</text>
                                        </view>
                                        <view style="width: 128rpx;"></view>
                                        <view>
                                            <text class="bold" style="line-height: 60rpx;">{{
                                                formatAmount(Ydata.openBarAllCount)
                                            }}</text>
                                            <text class="text">笔</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view> -->
                    </view>



                </view>

                <view class="invite-section">
                    <view class="invite-card">
                        <view class="invite-image">
                            <view class="code">
                                <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="209rpx"
                                    :options="options"></uv-qrcode>
                            </view>
                            <text class="invite-code">邀请码：<text class="codes">{{ shareUser.invitationCode
                                    }}</text></text>

                        </view>

                        <view class="invite-text">
                            <text>我要和TA一起完成FLAG</text>
                            <text>立即注册，获得新用户奖励</text>
                        </view>

                    </view>
                    <view class="bv">
                        <view style="height: 112rpx;"></view>
                        <view class="btns">
                            <view @click="canvas.onClick" class="btn_next">
                                下载
                            </view>
                            <view @click="show = false" class="btn_prev">
                                关闭
                            </view>

                        </view>
                    </view>

                </view>
            </view>
        </u-popup>

        <u-popup mode="center" :mask-close-able="false" v-model="showNoData">
            <view class="no_data_exit">
                <!-- <image class="close" @click="showNoData = false"
                    src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/064f9301caab9302a4d0141944299030_160x160.png" /> -->
                <view class="contents">
                    <p>很遗憾Bigverse没有陪您度过2024</p><br>
                    <p style="color: #40F8EC;"> 2025让Bigverse陪您!</p>
                </view>
                <view class="btns">
                    <view class="btn_next_go" @click="goGet">
                        <text>立即拥有一张藏品</text>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showNoData: false,
            showtitle: true,
            platform: false,
            qrcodeUrl: "",
            shareUser: {},
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            show: false,
            step: 1,
            isLoadding: false,
            yearMonth: '',
            isSelect: false,
            Ydata: {}
        }
    },
    onLoad(options) {
        const { token, platform } = options
        if (token) {
            uni.setStorageSync('token', token)

        }
        if (platform) {
            this.platform = platform
            uni.setStorageSync('is_platform', platform)
        }
        this.getList()
        this.getUserShare()
    },
    computed: {
        collection() {
            return this.getCollectionByType(this.Ydata.viewType);
        },
    },
    methods: {
        save() {
            this.canvas.onClick
            this.show = false
        },
        getUrl(option) {
            console.log(option)
            uni.hideLoading()
            // this.postImg = option.base64
            // #ifdef APP
            this.saveHeadImgFile(option.base64)
            // #endif
            // #ifdef H5
            this.saveImage(option.base64)
            // #endif

        },
        saveImage(url) {
            // #ifdef H5
            let image = new Image()
            image.setAttribute("crossOrigin", 'Anonymous')
            image.src = url
            image.onload = function () {
                let canvas = document.createElement('canvas')
                canvas.width = image.width
                canvas.height = image.height
                let context = canvas.getContext('2d')
                context.drawImage(image, 0, 0, image.width, image.height)
                let url = canvas.toDataURL('image/png')
                let a = document.createElement('a')
                let event = new MouseEvent('click')
                a.download = '分享好友'
                a.href = url
                a.dispatchEvent(event)
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                })
            }
            // #endif
            // #ifdef APP-PLUS
            uni.saveImageToPhotosAlbum({
                filePath: url,
                success: () => {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success'
                    })
                },
                fail: () => {
                    uni.showToast({
                        title: '保存失败',
                        icon: 'none'
                    })
                }
            })
            // #endif
        },
        saveHeadImgFile(base64) {
            const bitmap = new plus.nativeObj.Bitmap("test");
            bitmap.loadBase64Data(base64, function () {
                const url = "_doc/" + new Date().getTime() + ".png"; // url为时间戳命名方式
                console.log('saveHeadImgFile', url)
                bitmap.save(url, {
                    overwrite: true, // 是否覆盖
                    // quality: 'quality'  // 图片清晰度
                }, (i) => {
                    uni.saveImageToPhotosAlbum({
                        filePath: url,
                        success: function () {
                            uni.showToast({
                                title: '图片保存成功',
                                icon: 'none'
                            })
                            bitmap.clear()
                        }
                    });
                }, (e) => {
                    uni.showToast({
                        title: '图片保存失败',
                        icon: 'none'
                    })
                    bitmap.clear()
                });
            }, (e) => {
                uni.showToast({
                    title: '图片保存失败',
                    icon: 'none'
                })
                bitmap.clear()
            });
        },
        goGet() {
            this.showNoData = false
            this.JumpSale()
        },
        async showShare() {
            this.show = true
            if (this.Ydata.isAlreadyShare == 0) {
                let res = await this.$api.shareYearReport2024({})
                if (res.status.code == 0) {
                    this.getList()
                }
            }

        },
        getCollectionByType(viewType) {
            const collectionsMap = {
                '独具慧眼': { name: '魔法学院-占卜师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/f89c56a2f4a8060f89134078d73562a5_1500x1500.jpeg", num: "5" },
                '疯狂收集者': { name: '魔法学院-占卜师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/f89c56a2f4a8060f89134078d73562a5_1500x1500.jpeg", num: "5" },
                '还得练机会一直在': { name: '魔法学院-预言家', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/7a5e22317831b699dec9a366e725e8e4_1500x1500.jpeg", num: "2" },
                '还是个宝宝': { name: '魔法学院-符咒师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/0cba847ee464a9f3ce7c31714926c216_1500x1500.jpeg", num: "1" },
                '妙手回春': { name: '魔法学院-占卜师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/f89c56a2f4a8060f89134078d73562a5_1500x1500.jpeg", num: "5" },
                '数藏小学生': { name: '魔法学院-符咒师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/0cba847ee464a9f3ce7c31714926c216_1500x1500.jpeg", num: "1" },
                '退后可以去群里装X了': { name: '魔法学院-预言家', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/7a5e22317831b699dec9a366e725e8e4_1500x1500.jpeg", num: "2" },
                '绣春刀': { name: '魔法学院-雷电法师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/2aa4440883db0da188c1510ee2ca5c0d_1500x1500.jpeg", num: "2" },
                '有点实力': { name: '魔法学院-雷电法师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/2aa4440883db0da188c1510ee2ca5c0d_1500x1500.jpeg", num: "2" },
                '猪脚饭战神': { name: '魔法学院-符咒师', image: "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/0cba847ee464a9f3ce7c31714926c216_1500x1500.jpeg", num: "1" },
            };

            return collectionsMap[viewType] || { name: '--', image: '' };
        },
        formatAmount(amount) {
            if (!amount) {
                return '0'
            }

            // 确保输入是一个数字
            let initstr = Number(amount)
            let integerPart = Math.floor(initstr)

            // 将数字转为字符串
            let integerStr = integerPart.toString();

            // 使用正则添加千位分隔符
            let formattedIntegerPart = integerStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            // 返回格式化后的金额
            return formattedIntegerPart;
        },
        formatCurrencyPlus(amount) {
            if (!amount) {
                return '¥ 0';
            }

            // 保证只有2位小数，取绝对值
            let initstr = Math.abs(Number(amount));
            let fixedAmount = initstr.toFixed(2);

            // 将整数部分和小数部分分开
            let [integerPart, decimalPart] = fixedAmount.split('.');

            // 使用正则添加千位分隔符
            let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            // 判断是否为负数，若是则加上负号
            if (amount < 0) {
                return '-¥' + formattedIntegerPart + '.' + decimalPart;
            } else {
                return '+¥ ' + formattedIntegerPart + '.' + decimalPart;
            }
        },
        formatCurrency(amount) {
            if (!amount) {
                return '¥ 0';
            }

            // 保证只有2位小数，取绝对值
            let initstr = Math.abs(Number(amount));
            let fixedAmount = initstr.toFixed(2);

            // 将整数部分和小数部分分开
            let [integerPart, decimalPart] = fixedAmount.split('.');

            // 使用正则添加千位分隔符
            let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

            // 判断是否为负数，若是则加上负号
            if (amount < 0) {
                return '-¥' + formattedIntegerPart + '.' + decimalPart;
            } else {
                return '¥ ' + formattedIntegerPart + '.' + decimalPart;
            }
        },
        async getUserShare() {
            let res = await this.$api.inviteUserInfo({

            });
            console.log(res, '321');
            if (res.status.code == 0) {
                this.shareUser = res.result
                this.qrcodeUrl = `${getApp().globalData.url}pages/project/login/register?code=${this.shareUser.invitationCode}`

            } else if (res.status.code == 1002) {
                this.isLoadding = true
                setTimeout(() => {
                    this.isLoadding = false
                    this.$Router.push({
                        name: "mainLogin"
                    })
                }, 1500);
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async getList() {
            let res = await this.$api.getYearReport2025({})
            if (res.status.code == 0) {
                this.Ydata = res.result
            } else if (res.status.code == 1002) {
                this.isLoadding = true
                setTimeout(() => {
                    this.isLoadding = false
                    this.$Router.push({
                        name: "mainLogin"
                    })
                }, 1500);
            } else {
                this.showtitle = false
                if (res.status.code == 9999) {
                    this.showNoData = true
                } else {
                    uni.showToast({
                        title: res.status.msg,
                        icon: 'none',
                        duration: 3000
                    });
                }
            }
        },
        back() {

            // uni.navigateBack()
            const pages = getCurrentPages();
            if (pages.length === 1) {
                const isbitFrom = uni.getStorageSync('isbitFrom')

                const currentUrl = window.location.href;
                // 判断 URL 是否包含 '/bit'
                if (currentUrl.includes("/bit")) {
                    // url: 'http://web-test.nftcn.com.cn/bit/#/', //测试
                    const Url = `http://web-test.nftcn.com.cn/bit/#/pages/project/index/indexYs`
                    // url: 'https://www.nftcn.com/bit/#/', //APP端线上 url
                    // url: 'http://web-test.nftcn.com.cn/bit/#/', //测试
                    window.location.href = Url
                } else {
                    // url: 'https://www.nftcn.com/h5/#/', //APP端线上 url
                    // url: 'http://web-test.nftcn.com.cn/h5/#/', //测试
                    const Url = `http://web-test.nftcn.com.cn/h5/#/pages/project/index/index`
                    window.location.href = Url
                }

                // if (isbitFrom == 1) {
                //     // url: 'http://web-test.nftcn.com.cn/bit/#/', //测试
                //     const Url = `http://web-test.nftcn.com.cn/bit/#/pages/project/index/indexYs`
                //     // url: 'https://www.nftcn.com/bit/#/', //APP端线上 url
                //     // url: 'http://web-test.nftcn.com.cn/bit/#/', //测试
                //     window.location.href = Url
                // } else {
                //     // url: 'https://www.nftcn.com/h5/#/', //APP端线上 url
                //     // url: 'http://web-test.nftcn.com.cn/h5/#/', //测试
                //     const Url = `http://web-test.nftcn.com.cn/h5/#/pages/project/index/index`
                //     window.location.href = Url
                // }
            } else {
                this.$Router.back();
            }
        },
        JumpSale() {
            uni.setStorageSync('currentTabbar', 'bv')
            this.$Router.pushTab({
                name: 'mall'
            })
        },
        getImageByViewType(viewType) {
            const viewTypeImages = {
                // "数藏宗师": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/cac630de1358a9e97971a1789efad9cb_1284x524.png",
                // "数藏青铜": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/8d2eb37fbe261c3de365eeb33db26ba5_1284x554.png",
                // "数藏帝王": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/723bff8ce843ab17ad138fde4c0a76c5_1278x616.png",
                // "数藏钻石": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/6d4ac81c4463df01d8d6547a35a4ac60_1284x572.png",
                // "数藏小趴菜": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/7681953f49b107aa92f7af19f728f174_1296x508.png",
                // "数藏大怨种": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/4f1d65b458effeab41c0912e343a4394_1284x530.png",
                // "数藏小萌新": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/a3603810e596a83857f574e72bce8e5f_1284x530.png",
                // "数藏旁观者": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/82a2f2b59f429eb4e65c4cb371969593_1284x566.png",
                // "数藏大师": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/03a41b4eb8a3f72f9b9a8da832ccf93d_1284x508.png",
                // "猪脚饭战神": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/87b0253eae1ef12fbdc4366b6221440c_1308x600.png",
                // "数藏高手": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/38580a5e86b19804d3b8ff0b15d8e33e_1284x508.png",
                // "数藏大王": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/11fe9e453a1c13d4c6cde564e239299f_1280x518.png"
                "妙手回春": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/126414b13ee01dfe3ae2ad2854c4b24c_1284x524.png",
                "数藏小学生": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/676ec53a801fa2f9aba50f19ea374533_1284x530.png",
                "有点实力": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/fb8c5aa65ee35c04058e109142b20315_1280x516.png",
                "独具慧眼": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/de90da9ca146b68e0cfead1051db5f87_1284x566.png",
                "猪脚饭战神": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/b03128b61fe7b2c9ca8f5a3204692b54_1308x600.png",
                "疯狂收集者": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/8f03cc4042044f0d89b799fd48df7573_1284x508.png",
                "绣春刀": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/f929709a06b4bdd3f088d54f82503f12_1284x508.png",
                "还得练机会一直在": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/a2120810e4b992535faf16af4944f71a_1296x508.png",
                "还是个宝宝": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/309e4409dcd98fc4ca959034265ece5d_1284x530.png",
                "退回可以去群里装X了": "https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/5c8f1939b53bb4b1c889d764b99ca585_1278x616.png"
            };
            // 返回对应的图片链接，如果找不到则返回默认图片
            return viewTypeImages[viewType] || 'https://example.com/default-image.png';
        }
    }
}
</script>

<script module="canvas" lang="renderjs">
	import html2canvas from 'html2canvas'
	export default {
		mounted() {
        },
		methods: {
			// 生成图片需要调用的方法
			generateImage(ownerInstance) {
                console.log(ownerInstance,'参数');
                
				let dom = document.getElementById('mainbody') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;

				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
                    console.log('到这里');
                    
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			generateImageOld(ownerInstance) {
				let dom = document.getElementById('inviteOld') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;
			
				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				console.log(event, ownerInstance,'触发了')
				this.generateImage(ownerInstance)
				// ownerInstance.callMethod('onViewClick', {
				// 	test: 'test'
				// })
			}
		},
	}
</script>

<style lang="scss" scoped>
.no_data_exit {
    width: 711rpx;
    height: 424rpx;
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250116/92d8b0544eb5c2a01950583885df1945_1422x848.png");
    background-size: 100% 100%;
    position: relative;

    .contents {
        padding-top: 140rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #F8F8F7;
    }

    .close {
        position: absolute;
        width: 80rpx;
        height: 80rpx;
        top: 26rpx;
        right: 0;
    }

}

.center {
    margin-top: 57rpx;
    flex-direction: column;
    display: flex;
    align-items: center;
    justify-content: center;
    // gap: 28rpx;
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
    opacity: 0.5;
}

/* 弹窗内容整体样式 */
.popup-content {
    // background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/4abe02b65e60d78fcfa7740032b4d1a9_1428x2354.png");
    // background-size: 100% 100%;
    background: linear-gradient(180deg, #25232D 11%, #3E3249 100%);
    padding: 99rpx 72rpx 30rpx 36rpx;
    border-radius: 35rpx;
    // margin: 0 20rpx;
    width: 711rpx;
    margin: 100rpx 0;
    // height: 1331rpx;
    // height: fit-content;
    color: #fff;
    // text-align: center;
    font-family: Arial, sans-serif;
    display: flex;
    overflow-y: auto;
    flex-direction: column;
}

.header {
    flex-direction: column;

    display: flex;
    position: relative;



    .subtitle {
        display: flex;
        justify-content: center;
        margin-top: 52rpx;
  

        image {
            width: 522rpx;
            height: 64rpx;
        }



    }

    .year {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0;
        right: -40rpx;
        width: 77rpx;
        height: 26rpx;
        background: #1B1B1B;
        border-radius: 12rpx;
        border: 1rpx solid #FFFFFF;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
    }
}

/* 头部样式 */
.header .title {
    text-align: center;
    margin: 0 auto;

    image {
        width: 334rpx;
        height: 28rpx;
    }
}

.content {
    margin-top: 115rpx;
    display: flex;
    flex-direction: column;

    .most_buy {
        .box {
            &:nth-child(1) {
                padding: 112rpx 0 0 0;
                margin: 0;
            }

            .range {
                margin-top: 83rpx;
                width: fit-content;
                display: flex;
                flex-direction: column;
                align-items: center;

                text {
                    font-family: HarmonyOS_Sans_SC_Black;
                    font-weight: 900;
                    font-size: 62rpx;
                    line-height: 60rpx;
                    color: #40F8EC;
                }
            }

            margin: 119rpx 0 0 0;
            display: flex;
            flex-direction: column;

            .stitle {
                font-family: HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #F8F8F7;
            }

            .boldtitle {
                margin-top: 81rpx;
                font-family: HarmonyOS_Sans_SC_Black;
                font-weight: 900;
                font-size: 62rpx;
                line-height: 60rpx;
                color: #40F8EC;
            }


        }

        .report {

            .open {
                display: flex;
                align-items: center;

                view {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .text {
                        margin-top: 31rpx;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #F8F8F7;
                    }
                }
            }

            .report_title {
                font-family: HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #F8F8F7;
            }

            .ctid {
                display: flex;
                flex-direction: column;
                align-items: center;

                image {
                    margin-top: 48rpx;
                    width: 272rpx;
                    height: 272rpx;
                    border-radius: 15rpx;
                }

                text {
                    font-family: HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    margin-top: 34rpx;
                    color: #40F8EC;
                }
            }
        }
    }

    .buy_list {

        .item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 0 auto;

            .ctidName {
                font-family: HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #40F8EC;
            }

            .coll {
                margin: 48rpx auto 34rpx auto;
                width: 272rpx;
                height: 272rpx;
                border-radius: 15rpx;
                background: linear-gradient(90deg, #EF91FB, #40F8EC);
                display: flex;
                align-items: center;
                justify-content: center;

                image {
                    margin: 0;
                    width: 268rpx;
                    border-radius: 15rpx;
                    height: 268rpx;
                }
            }
        }

        .title {
            display: flex;
            align-items: flex-start;
            text-align: left;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
        }
    }

    .summary {
        display: flex;
        align-items: flex-start;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #F8F8F7;
    }

    .no-data {

        display: flex;
        flex-direction: column;
        align-items: flex-start;

    }

    .count {
        display: flex;
        align-items: flex-end;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #F8F8F7;
        margin: 67rpx 0 115rpx 0;
    }

    .flag-title {
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #F8F8F7;
    }

    .flag {
        font-family: HarmonyOS Sans SC;
        font-weight: 900;
        font-size: 46rpx;
        color: #40F8EC;
        margin-top: 67rpx;
    }
}


/* 邀请卡片样式 */
.invite-section {
    margin-top: 115rpx;
}

.bv {
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250117/cc7599eea932e04562ca81c5c6528ece_1192x380.png");
    background-size: 100% 100%;
    width: 596rpx;
    height: 190rpx;
}

.invite-card {

    display: flex;
    align-items: center;
    justify-content: space-between;

    .invite-image {

        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;

        .code {
            width: 217rpx;
            height: 217rpx;
            border-radius: 15rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(90deg, #EF91FB, #40F8EC);
        }

        .invite-code {
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
            margin-top: 15rpx;
        }
    }

    .invite-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 31rpx;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #F8F8F7;
    }



}





.invite-code .code {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
}

.text_msg {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #F8F8F7;
}

.bold {
    font-family: HarmonyOS Sans SC;
    font-weight: 900;
    font-size: 68rpx;
    color: #40F8EC;
}

.btns {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 67rpx;

    .share {
        display: flex;
        width: 100%;
        margin: 0 55rpx;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        height: 80rpx;
        background: linear-gradient(90deg, #EF91FB, #40F8EC);
        border-radius: 35rpx;
    }

    .btn_prev {
        width: 279rpx;
        height: 80rpx;
        background: #35333E;
        border-radius: 35rpx;
        border: 2rpx solid #40F8EC;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #40F8EC;
    }

    .btn_next {
        width: 279rpx;
        height: 80rpx;
        border: none;
        background: linear-gradient(90deg, #EF91FB, #40F8EC);
        border-radius: 35rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000
    }

    .btn_next_go {
        width: 357rpx;
        height: 80rpx;
        border: none;
        margin-top: 69rpx;
        background: linear-gradient(90deg, #EF91FB, #40F8EC);
        border-radius: 35rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000
    }
}

.btn {
    display: flex;
    flex-direction: column;


    view {
        &:nth-of-type(1) {
            display: flex;
            margin: 0 55rpx;
            align-items: center;
            justify-content: center;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #000000;
            height: 80rpx;
            background: linear-gradient(90deg, #EF91FB, #40F8EC);
            border-radius: 35rpx;
        }

        &:nth-of-type(2) {
            display: flex;
            margin: 53rpx 55rpx 0 55rpx;
            align-items: center;
            justify-content: center;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #40F8EC;
            height: 80rpx;
            background: #35333E;
            border-radius: 35rpx;
            border: 2rpx solid #40F8EC;
            border-radius: 35rpx;
        }

    }
}

.bvbg {
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20250114/4abe02b65e60d78fcfa7740032b4d1a9_1428x2354.png");
    background-size: 100% 100%;
    // height: 1177rpx;
    margin: 10rpx 20rpx 74rpx 20rpx;

    .report {
        margin-left: 39rpx;

        .open {
            display: flex;
            align-items: center;

            view {
                display: flex;
                flex-direction: column;
                align-items: center;

                .text {
                    margin-top: 31rpx;
                    font-family: HarmonyOS Sans SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #F8F8F7;
                }
            }
        }

        .report_title {
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
        }

        .ctid {
            display: flex;
            flex-direction: column;
            align-items: center;

            image {
                width: 272rpx;
                height: 272rpx;
                border-radius: 15rpx;
            }

            text {
                font-family: HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 28rpx;
                margin-top: 34rpx;
                color: #40F8EC;
            }
        }
    }

    .box {
        &:nth-child(1) {
            padding: 112rpx 0 0 37rpx;
            margin: 0;
        }

        .range {
            margin-top: 83rpx;
            width: fit-content;
            display: flex;
            flex-direction: column;
            align-items: center;

            text {
                font-family: HarmonyOS_Sans_SC_Black;
                font-weight: 900;
                font-size: 62rpx;
                line-height: 60rpx;
                color: #40F8EC;
            }
        }

        margin: 119rpx 0 0 37rpx;
        display: flex;
        flex-direction: column;

        .stitle {
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
        }

        .boldtitle {
            margin-top: 81rpx;
            font-family: HarmonyOS_Sans_SC_Black;
            font-weight: 900;
            font-size: 62rpx;
            line-height: 60rpx;
            color: #40F8EC;
        }


    }

    .flag {
        .ctidName {
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #40F8EC;
        }

        .coll {
            margin: 48rpx auto 34rpx auto;
            width: 272rpx;
            height: 272rpx;
            border-radius: 15rpx;
            background: linear-gradient(90deg, #EF91FB, #40F8EC);
            display: flex;
            align-items: center;
            justify-content: center;

            image {
                margin: 0;
                width: 268rpx;
                border-radius: 15rpx;
                height: 268rpx;
            }
        }

        .flag2 {
            margin-top: 94rpx;
            font-family: HarmonyOS Sans SC;
            font-weight: 900;
            font-size: 46rpx;
            color: #40F8EC;
        }
    }

    .font-box {
        margin: 91rpx 0 0 35rpx;
        display: flex;
        flex-direction: column;

        .title1 {
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
        }

        .title2 {
            margin-top: 50rpx;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #F8F8F7;
        }

        .title3 {
            margin-top: 108rpx;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #F8F8F7;
        }
    }

    .title {
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #F8F8F7;
        display: block;
        padding: 80rpx 0 0 35rpx;
    }

    image {
        // margin-top: 37rpx;
        width: 640rpx;
        margin: 37rpx auto 0 auto;
    }
}

.search-box {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #F8F8F7;
    margin-right: 42rpx;
}
</style>