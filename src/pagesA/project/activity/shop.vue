<template>
	<view class="content">
		<view class="cover">
			<u-swiper :border-radius="0" height="750" :list="info.list"  :autoplay="false" :current="current" @change="change"></u-swiper>
		</view>
		<view class="body">
			<view class="cover_min">
				<view class="flex">
					<view class="text">款式选择</view>
					<view class="img" :class="{'active':index==current}" v-for="(item,index) in info.list" @click="checkCurrent(index)">
						<image :src="item.image"  mode="widthFix"></image>
					</view>
				</view>
				<view class="right_icon">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20241103/932ca2ebff2f910947e6b15e7778288d_24x40.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="title_view">
				{{info.title}}
			</view>
			<view class="button_view">
				<view class="price">￥{{info.price}}</view>
				<view class="button" @click="isNum = true">点击购买</view>
			</view>
			<view class="border_view"></view>
			<view class="details">
				<view class="head_title">
					商品详情
				</view>
				<view class="img">
					<image :src="item.src" v-for="(item,index) in info.detailsImg" mode="widthFix"></image>
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<view class="bg_model">
				<div class="sk-wave">
					<div class="sk-rect sk-rect-1"></div>
					<div class="sk-rect sk-rect-2"></div>
					<div class="sk-rect sk-rect-3"></div>
					<div class="sk-rect sk-rect-4"></div>
					<div class="sk-rect sk-rect-5"></div>
				</div>
				<view class="text_msg" style="
				  padding: 10rpx 20rpx 40rpx 20rpx;
				  text-align: center;
				  font-size: 26rpx;
				  line-height: 40rpx;
				  color:#000;
				">
					登录跳转中...
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isNum" :width="563" :show-title="false" :show-confirm-button="false" :mask-close-able="true"
			border-radius="36">
			<view class="model_view">
				<view class="body_view">
					<view class="num_view">
						数量：
						<u-number-box :input-width="107" bg-color="#F7F7F7" :input-height="70" :min="1" :max="10" v-model="value" ></u-number-box>
					</view>
					<view class="msg">
						<view> 买3送1    买5送2</view>
						<view> 每人限量最多可购买10份</view>
					</view>
				</view>
				<view class="button" @click="submit()">
					确认
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLoadding:false,
				current:0,
				info:{},
				isNum:false,
				value:1,
				platform:"",
				token:"",
				
			}
		},
		onLoad(options) {
			const {
				platform,
				token,
			} = options;
			this.platform = platform
			this.token = token
			if (token) {
				uni.setStorageSync('token', token)
			}
			this.get_info()
		},
		onShow() {

		},
		methods: {
			checkCurrent(index) {
				this.current = index
			},
			change(e){
				this.current = e
			},
			async get_info() {
				let res = await this.$api.java_commonconfigInfo({
					name: 'ACTIVE_SHOP',
				});
				if (res.status.code == 0) {
					console.log(res)
					this.info = JSON.parse(res.result.value)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async submit() {
				if(this.value == ""){
					uni.showToast({
						title: '请输入购买数量',
						icon: 'none',
						duration: 3000
					});
					return
				}
				let res = await this.$api.shopCheck({
					num:this.value
				});
				if (res.status.code == 0) {
					this.isNum =false
					uni.setStorageSync('activeOrderInfo',{
						...this.info,
						num:this.value
					})
					this.$Router.push({
						name:'shopOrder'
					})
				} else if(res.status.code == 1002){
					this.isLoadding = true
					this.isNum =false
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin",
							// #ifdef H5
								params: {
									url: window.location.hash,
								},
							// #endif
						})
					}, 1500);
				}else{
					this.isNum =false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	page{ 
		background-color:#fff;
	}
	::v-deep .u-model{
		background-color:#fff !important;
	}
	.content {
		.body{
		
			.cover_min{
				padding:24rpx 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.flex{
					display: flex;
					justify-content: space-between;
					align-items: center;
					.text{
						color:rgba(0, 0, 0, 0.4);
						font-size:28rpx;
						width:70rpx;
						margin-right: 10rpx;
					}
					.img{
						margin-right:12rpx;
						width:81rpx;
						height:81rpx;
						padding: 4rpx;
						
						&.active{
							border:4rpx solid #FD3E00;
							border-radius:12rpx;
							padding:0rpx;
							overflow: hidden;
						}
						image{
							width:100%;
							border-radius:8rpx;
						}
					}
				}
				.right_icon{
					image{
						width:16rpx;
						height: 25rpx;
					}
				}
			}
			.title_view{
				padding:0rpx 30rpx;
				font-size:32rpx;
				color:#000000;
			}
			.button_view{
				border-top:1px solid #EDEDED;
				padding:20rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				height:136rpx;
				margin-top:30rpx;
				.price{
					color:#FD3E00;
					font-size: 32rpx;
					font-weight:600;
				}
				.button{
					width: 458rpx;
					height: 84rpx;
					background-color:#FD3E00;
					line-height:84rpx;
					text-align: center;
					border-radius: 11rpx;
					font-size:28rpx;
					color:#fff;
					background-color: linear-gradient(75deg, rgba(255,117,0,0.51), rgba(255,73,11,0.51));;
				}
			}
			.border_view{
				background-color:#F4F5F8;
				height:12rpx;
			}
			.details{
				.head_title{
					height:100rpx;
					padding:0rpx 35rpx;
					line-height:100rpx;
					border-bottom:1px solid #EDEDED;
					width:100%;
					font-size:32rpx;
					font-weight:600;
				}
				.img{
					padding-top:40rpx;
					image{
						width:100%;
					}
				}
			}
		}
	}
	.model_view{
		background-color:#fff;
		min-height:363rpx;
		background-image:url(https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241103/f5c6b959a65ec2c6562a92f64cc2f7d7_563x363.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding:68rpx 40rpx 40rpx 40rpx;
		.body_view{
			border-bottom:1px solid #EDEDED;
			text-align: center;
		}
		.num_view{
			margin-bottom:40rpx;
		}
		.msg{
			>view{
				margin-bottom:20rpx;
				font-size:34rpx;
				white-space: pre;
			}
		}
		.button{
			margin-top:40rpx;
			width:228rpx;
			height:60rpx;
			border-radius:20rpx;
			color:#fff;
			background-color: #FD3E00;
			font-weight:600;
			text-align: center;
			line-height:60rpx;
			margin: 40rpx auto 0;
		}
	}
	.bg_model{
		background-color:#fff;
		.sk-wave {
			position: relative;
			width: 50rpx;
			height: 50rpx;
			border: 3px solid #FD3E00;
			overflow: hidden;
			animation: spin 3s ease infinite;
			margin: 40rpx auto;
			text-align: center;
			font-size: 20px;
		}
		
		.sk-wave {
			display: flex;
			width: 60rpx;
			height: 60rpx;
			margin: 40rpx auto;
			border: 3px solid transparent;
			border-top-color: #FD3E00;
			border-bottom-color: #FD3E00;
			border-radius: 50%;
			animation: spin 1.5s linear infinite;
		}
		
		.sk-wave:before {
			content: '';
			display: block;
			margin: auto;
			width: 0.75em;
			height: 0.75em;
			border: 3px solid #FD3E00;
			border-radius: 50%;
			animation: pulse 1s alternate ease-in-out infinite;
		}
		.text_msg{
			color:#000 !important;
		}
	}
</style>
