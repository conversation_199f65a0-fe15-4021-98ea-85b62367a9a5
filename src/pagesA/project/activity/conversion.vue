<template>
	<view class="content">
		<view class="back" @click="back()" >
			<image src="@/static/imgs/mall/mall_back.png" mode="widthFix"></image>
		</view>
		<view class="head">
			<view class="title_view">
				换取祈福贴
			</view>
			<view class="msg_view">
				我当前的《红衣小队》数量为：{{totalCount}}
			</view>
			<view class="button_text" @click="nav_shop()" v-if="totalCount==0">  
				去买红衣小队
			</view>
		</view>
		<view class="body_view">
			<view class="give_input">
				<view class="label">
					输入红衣小队数量
				</view>
				<view class="input">
					<u-input  class="input_u" v-model="magicSeeds" placeholder="" type="number" border
						border-color="transparent" :trim="true"  
						/>
				</view>
			</view>
			<view class="give_num">
				<view class="label">
					您可换取祈福贴
				</view>
				<view class="input">
					<u-input  class="input_u" v-model="showText" disabled  placeholder="" type="number" border
						border-color="transparent" :trim="true" 
						/>
				</view>
			</view>
		</view>
		<view class="footer" >
			<u-image mode="widthFix" width="30rpx" @click="isAgree = !isAgree"
				:src="`../../../static/imgs/public/${isAgree?'checked_new':'check_new'}.png`">
			</u-image>
			<view class="msg">
				我已阅读并同意
				<text @click="nav_link('祈福贴换取协议',1)">《祈福贴换取协议》</text>
				</text>
			</view>
		</view>
		<view class="submit" @click="submit()">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/********/8b2eb4df6cff85bce83ccb448b820b9a_400x131.png" mode="widthFix"></image>
		</view>
		<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
			email="333" :mode="mode" @pay="password" @createSuccess="createSuccess" />
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg" style="
			  padding: 10rpx 20rpx 40rpx 20rpx;
			  text-align: center;
			  font-size: 26rpx;
			  line-height: 40rpx;
			">
				换取中
			</view>
		</u-modal>
		<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false"
			border-radius="0">
			<view class="BankVerifyBody">
				<view class="head_title_y">
					<view class="right" @click="isPassword=false">
						<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
					</view>
					请先设置支付密码
				</view>
				<view class="msg_y">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="footer_y" @click="isPassword=false">
					<button>
						取消
					</button>
					<button class="active" @click="SetPayPassword()">
						去设置
					</button>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		data() {
			return {
				isLoadding:false,
				magicSeeds:"",
				isPasswordImport: false,
				passwordTitle: "确认兑换",
				passwordMsg: "请输入余额支付密码，用于兑换",
				mode: "pay",
				isPassword:false,
				showText:"",
				totalCount:"",
				isAgree:false,
				platform:"",
				activityNo:""
			}
		},
		onLoad(options) {
			let {
				platform,
				activityNo
			} = options
			if(platform){
				this.platform=platform
			}
			if(activityNo){
				this.activityNo=activityNo
			}
			this.goodslist()
		},
		onShow() {

		},
		components:{
			payPopup
		},
		watch:{
			magicSeeds(val,v){
				console.log(val,v)
				if(val){
					this.showText=val+"00"
				}else{
					this.showText=""
				}
				
			}
		},
		onReachBottom() {},
		methods: {
			check(index) {
				this.sun = index
			},
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall_new"
					})
				} else {
					this.$Router.back();
				}
			},
			submit(){
				if(this.isAgree){
					if(this.magicSeeds==""){
						uni.showToast({
							title: "请输入需要转增的contract addres",
							icon: 'none',
							duration: 3000
						});
					}else if(this.ticketGiveNum==""){
						uni.showToast({
							title: '请输入转增数量',
							icon: 'none',
							duration: 3000
						});
					}else{
						if(uni.getStorageSync('isSetTradePassword')==0){
							this.isPassword=true
						}else{
							this.isPasswordImport = true
						}
					}
				}else{
					uni.showToast({
						title: '请先勾选协议',
						icon: 'none',
						duration: 3000
					});
				}
			},
			async exchange(tradePassword) {
				this.isLoadding = true
				let res = await this.$api.java_exchange({
					magicSeeds: this.magicSeeds,
					tradePassword
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.totalCount = this.totalCount - this.magicSeeds
					this.magicSeeds=""
					uni.showToast({
						title: "兑换成功",
						icon: 'none',
						duration: 3000
					});
				} else {
					this.isLoadding = false
					uni.showToast({ 
						title: res.status.msg,
						icon: 'none',
						duration: 3000 
					});
				}
			},
			async goodslist() {
				let res = await this.$api.java_seriesDetails({ //请求出来的数据
					ctid: 'cs38679613671591940885172987507453'
				});
				if (res.status.code == 0) {
					this.totalCount=res.result.userGoodsCount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			password(e) {
				console.log(e)
				this.isPasswordImport = false
				this.exchange(e)
			},
			createSuccess(psw) {
				console.log(psw)
				this.exchange(psw)
			},
			SetPayPassword() {
				this.mode = "set"
				this.isPasswordImport = true
			},
			nav_link(title, index) {
				if (index === 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com/link/#/pages/index/newReplacement"
						}
					})
				}
			},
			back() {
				const {
					origin,
				} = window.location;
				window.location.href = `${origin}/active/#/klnifePlanning?activityNo=${this.activityNo?this.activityNo:"A64519374068985856"}`; 
			},
			nav_shop(){
				if (this.platform=='ios'||this.platform=='android') {
					this.myUni.webView.navigateTo({
						url: `/pagesA/project/mall/seriesList?ctid=cs38679613671591940885172987507453`
					});
				} else {
					this.$Router.push({
						name: "seriesList",
						params: {
							ctid: 'cs38679613671591940885172987507453',
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	@font-face {
		font-family: "ysbty";
		src: url("https://cdn-lingjing.nftcn.com.cn/h5/ttf/ysbty.otf");
	}

	// @font-face {
	// 	font-family: 'fonts';
	// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	// }

	// page {
	// 	font-family: 'fonts';
	// }

	.content {
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230926/dad143f0c6c1ea177537a8966ad45f7f_400x866.png);
		background-size: 100%;
		height: 100vh;
		background-repeat: no-repeat;
		background-color: #000;
		.head{
			text-align: center;
			padding-top:276rpx;
			 .title_view{
				 color:#FFCF4F;
				 font-size:80rpx;
				 color: #ffcf4f;
				 font-family: "ysbty";
				 -webkit-text-stroke: 2rpx #000000;
				  
				 -webkit-background-clip: text;
				 text-align: center;
				 font-weight: 500;
			 }
			 .msg_view{
				 color:#FFFFFF;
				 font-size:40rpx;
				 font-family: "ysbty";
				 text-align: center;
				 font-weight: 500;
				 margin-top:70rpx;
			 }
			 .button_text{
				 text-align: right;
				 color: #FFCF4F;
				 text-decoration:underline;
				 font-size: 34rpx;
				 margin-top:20rpx;
				 padding-right:40rpx;
				 color: #ffcf4f;
				 font-family: "ysbty";
				 // -webkit-text-stroke: 2rpx #000000;
				  letter-spacing:4rpx;
				 -webkit-background-clip: text;
				 // text-align: center;
			 }
		}
		.body_view{
			padding:0rpx 36rpx 0rpx 0rpx;
			margin-top:230rpx;
			.give_input,.give_num{
				display: flex;
				height:96rpx;
				line-height:96rpx;
				justify-content: flex-end;
				align-items: center;
				width:100%;
				margin-bottom:56rpx;
				.label{
					margin-right:32rpx;
					color:#FFFFFF;
					font-size:40rpx;
					font-family: "ysbty";
				}
				.input{
					height:96rpx;
					background-size:100% 100%;
					
				}
				.input_u::v-deep{
					.u-input__input {
						color: var(--main-front-color) !important;
						font-size: 30rpx;
						height:92rpx;
					}
					
					.uni-input-placeholder {
						color: rgba(0,0,0,0.4) !important;
						font-size: 26rpx;
						// font-size: 50rpx !important;
					}
				}
			}
			.give_input{
				.input{
					width:356rpx;
					background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/8983cba38c591e8e93f94eb4996bccbc_400x107.png);
				}
			}
			.give_num{
				.input{
					width:398rpx;
					background-image:url(https://cdn-lingjing.nftcn.com.cn/image/********/4eab3d1bed7a8934f6834724c26e1046_400x96.png);
				}
			}
		}
		.submit{
			margin-top:28rpx;
			display: flex;
			justify-content: center;
			image{
				width:328rpx;
			}
		}
	}
	
	.BankVerifyBody {
		padding: 42rpx;
	
		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
			color: var(--message-box-point-color);
	
			.right {
				position: absolute;
				right: 40rpx;
				top: 66rpx;
	
				image {
					width: 30rpx;
				}
			}
		}
	
		.item {
			margin-bottom: 46rpx;
	
			.labal {
				color: #999999;
				font-size: 24rpx;
			}
	
			.input {
				align-items: center;
				height: 88rpx;
	
				.left {
					width: 50%;
					color: #999999;
					padding: 6rpx;
					border-radius: 4rpx;
	
					.input {
						font-size: 24rpx;
						color: var(--message-box-point-color);
					}
				}
	
				.right {
					image {
						width: 40rpx;
					}
	
					text {
						color: #00FBEF;
						font-size: 28rpx;
					}
				}
			}
		}
	
		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: var(--message-box-point-color);
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}
	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;
		color: var(--message-box-point-color);
	
		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;
	
			image {
				width: 30rpx;
			}
		}
	}
	
	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;
	
		button {
			margin: 0rpx;
			width: 238rpx;
			height: 64rpx;
			width: 240rpx;
			line-height: 64rpx;
			text-align: center;
			background-color: #999999;
			border-radius: 0rpx;
			font-size: 30rpx;
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: var(--active-color);
			border-radius: 0rpx;
	
			&.active {
				color: var(--main-bg-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				background: var(--primary-button-color);
				color: #121212;
				border: 0rpx;
			}
		}
	}
	
	.msg_y {
		font-size: 28rpx;
		color: #999999;
		line-height: 40rpx;
	}
	.footer {
		// position: fixed;
		// bottom: 155rpx;
		// left: 0rpx;
		margin-top: 96rpx;
		display: flex;
		justify-content: center;
	
		.u-image {
			margin-right: 10rpx;
			margin-top: 8rpx;
		}
	
		.msg {
			font-size: 30rpx;
			color:#fff;
			text-align: left;
			line-height: 42rpx;
			text {
				color: #FFCF4F;
			}
		}
	}
	.back{
		position: fixed;
		top:30rpx;
		left:20rpx;
		image{
			width:80rpx;
		}
	}
</style>
