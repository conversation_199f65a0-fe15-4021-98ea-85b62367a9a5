<template>
    <view class="">
        <u-navbar class="list-nav" :is-back="isBack" title="宝石商城" back-icon-color="var(--message-box-point-color)"
            :border-bottom="false"
            back-icon-name="https://cdn-lingjing.nftcn.com.cn/image/20220613/b81fa4236e814e3b7cfeb8c8698568d3_48x48.png"
            :custom-back="custom" title-color="#fff"
            :background="{ backgroundColor: `${isShowNavIcon ? '#070707' : 'transparent'}` }">
            <view slot="right" class="slot-wrap" @click="nav_link('宝石规则')">
                <view class="tag">宝石规则</view>
            </view>
        </u-navbar>
        <view class="main">
            <view class="cart_view">
                <view class="cart">
                    <view class="title">我的宝石</view>
                    <view class="bug">
                        <view class="li">
                            <view class="img">
                                <image
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20241210/c471bd29d8bdfbd3ee25d859c82ae326_166x166.png"
                                    mode="aspectFill"></image>
                            </view>
                            <view class="name">钻石</view>
                            <view>{{ worm_info.goldenWorm }}</view>
                        </view>
                        <view class="li">
                            <view class="img">
                                <image
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20241210/b9a7f8b7223cbb75ddd8c90f7668644f_166x166.png"
                                    mode="aspectFill"></image>
                            </view>
                            <view class="name">蓝宝石</view>
                            <view>{{ worm_info.silveryWorm }}</view>
                        </view>
                        <view class="li">
                            <view class="img">
                                <image
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20241210/1f24d5bef19519bb0aaecd82da9c4670_166x166.png"
                                    mode="aspectFill"></image>
                            </view>
                            <view class="name">红宝石</view>
                            <view>{{ worm_info.copperWorm }}</view>
                        </view>
                        <view class="li">
                            <view class="img">
                                <image
                                    src="https://cdn-lingjing.nftcn.com.cn/image/20241210/fe461fb14a1e352f204d74fa32331f20_166x166.png"
                                    mode="aspectFill"></image>
                            </view>
                            <view class="name">紫宝石</view>
                            <view>{{ worm_info.commonWorm }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="content">
                <view class="tab_view">
                    <u-tabs name="cate_name" count="cate_count" font-size="36" active-color="#F9F9F9"
                        inactive-color="var(--secondary-front-color)" :show-bar="true" :list="tabList"
                        :is-scroll="false" bar-height="4" height="88" :current="current" style="background:none"
                        @change="change" :bar-style="{ 'background-color': '#1FEDF0' }"></u-tabs>
                </view>
                <view class="body_list">
                    <view class="screen">
                        <view class="li" :class="{ 'active': sun == index }" v-for="(item, index) in screenList"
                            @click="check(item, index)">
                            {{ item.name }}
                        </view>
                    </view>
                    <view class="index_explore">
                        <u-waterfall v-model="List" ref="uWaterfall">
                            <template v-slot:left="{ leftList }">
                                <view class="item" v-for="(item, index) in leftList" :key="index"
                                    @click="nav_open(item, index)">
                                    <template>
                                        <view class="image_bg">
                                            <u-image radius :src="item.itemCover" alt="" mode="aspectFill" />
                                            <view class="bg">
                                                <image class="bg_icon"
                                                    src="https://cdn-lingjing.nftcn.com.cn/image/20221107/6464b7f3a98f8d4dbbffae5f0b0c08e6_284x54.png"
                                                    mode="widthFix"></image>
                                                <view class="image_text">
                                                    <text>剩余：</text>
                                                    <view>{{ item.remainNum }}</view>
                                                    <text>份</text>
                                                </view>
                                            </view>
                                        </view>
                                        <view class="info">
                                            <view class="title oneOver">{{ item.itemName }}</view>
                                            <view class="progress">
                                                <u-line-progress height="20" inactive-color="#484848"
                                                    :show-percent="false" :striped="true" active-color="#1BE6DF"
                                                    :percent="item.remainNum / item.totalNum * 100"
                                                    :striped-active="true">
                                                    <!-- {{item.remainNum!=0?'兑换中':'已兑完'}} -->
                                                </u-line-progress>
                                                <view class="progress_text" v-if="item.remainNum != 0">抢兑中</view>
                                                <view class="progress_text" v-if="item.remainNum == 0">已兑完</view>
                                            </view>
                                            <view class="price">
                                                <view class="left">
                                                    <text>{{ item.price }}</text>
                                                    <view class="img">
                                                        <image :src="getImageByWalletType(item.walletType)"
                                                            mode="aspectFill"></image>
                                                    </view>
                                                </view>
                                                <view class="but" :class="{ 'huise': item.remainNum == 0 }">
                                                    {{ item.remainNum == 0 ? '已兑完' : '兑换' }}
                                                </view>
                                            </view>
                                        </view>
                                    </template>
                                </view>
                            </template>
                            <template v-slot:right="{ rightList }">
                                <view class="item" v-for="(item, index) in rightList" :key="index"
                                    @click="nav_open(item, index)">
                                    <template>
                                        <view class="image_bg">
                                            <u-image radius :src="item.itemCover" alt="" mode="aspectFill" />
                                            <view class="bg">
                                                <image class="bg_icon"
                                                    src="https://cdn-lingjing.nftcn.com.cn/image/20221107/6464b7f3a98f8d4dbbffae5f0b0c08e6_284x54.png"
                                                    mode="widthFix"></image>
                                                <view class="image_text">
                                                    <text>剩余：</text>
                                                    <view>{{ item.remainNum }}</view>
                                                    <text>份</text>
                                                </view>
                                            </view>
                                        </view>
                                        <view class="info">
                                            <view class="title oneOver">{{ item.itemName }}</view>
                                            <view class="progress">
                                                <u-line-progress height="20" inactive-color="#484848"
                                                    :show-percent="false" :striped="true" active-color="#1BE6DF"
                                                    :percent="item.remainNum / item.totalNum * 100"
                                                    :striped-active="true">
                                                </u-line-progress>
                                                <view class="progress_text" v-if="item.remainNum != 0">抢兑中</view>
                                                <view class="progress_text" v-if="item.remainNum == 0">已兑完</view>
                                            </view>
                                            <view class="price">
                                                <view class="left">
                                                    <text>{{ item.price }}</text>
                                                    <view class="img">
                                                        <image
                                                           :src="getImageByWalletType(item.walletType)"
                                                            mode="aspectFill"></image>
                                                    </view>
                                                </view>
                                                <view class="but" :class="{ 'huise': item.remainNum == 0 }">
                                                    {{ item.remainNum == 0 ? '已兑完' : '兑换' }}
                                                </view>
                                            </view>
                                        </view>
                                    </template>
                                </view>
                            </template>
                        </u-waterfall>
                    </view>
                    <view v-if="List == ''">
                        <u-empty class="null"
                            src="https://cdn-lingjing.nftcn.com.cn/image/20220615/d3193141d1f4e2b0d97a9051e204e09d_170x122.png"
                            :text="msg_text" mode="list" margin-top="340"></u-empty>
                    </view>
                </view>
            </view>
        </view>
        <u-popup v-model="show" mode="center">
            <view class="popop_body">
                <view class="text">
                    <view class="font_view">
                        <view>确定使用{{ openInfo.price }}个{{ openInfo.name }}兑换</view>
                        <view>{{ openInfo.itemName }}</view>
                    </view>
                </view>
                <view class="button">
                    <view class="submit" @click="antiShakeclick()">确认</view>
                    <view class="cancel" @click="show = false">再想想</view>
                </view>
            </view>
        </u-popup>
        <u-popup v-model="remark_show" mode="center" :closeable="true" :mask-close-able="false">
            <view class="popop_remark">
                <view class="title">
                    请添加备注
                </view>
                <view class="msg">
                    如：实物的型号/大小/颜色等
                </view>
                <view class="input">
                    <u-input v-model="remark_value" placeholder="请输入备注" :border="true" border-color="#9E9C9C"
                        placeholder-style="" type="textarea" />
                </view>
                <view class="button">
                    <button-bar class="submit" @click="realClick()" text="提交"></button-bar>
                </view>
            </view>
        </u-popup>
        <u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
            <div class='sk-wave'>
                <div class='sk-rect sk-rect-1'></div>
                <div class='sk-rect sk-rect-2'></div>
                <div class='sk-rect sk-rect-3'></div>
                <div class='sk-rect sk-rect-4'></div>
                <div class='sk-rect sk-rect-5'></div>
            </div>
            <view class="text_msg"
                style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
                兑换中
            </view>
        </u-modal>

        <u-popup class="address-popup" v-model="isShowAddressPopup" mode="bottom">
            <view class="ap-title">选择地址<text @click="isShowAddressPopup = false">取消</text></view>
            <view class="content">
                <scroll-view scroll-y="true" style="height: 800rpx;">
                    <view class="add-box space-between" v-for="(item, index) in addressList" :key="index">
                        <view class="add-info">
                            <view class="ai-top">
                                <span>{{ item.name }}</span>
                                <span>{{ item.phone }}</span>
                                <u-image @click="nav_updataAddress(item)"
                                    src="@/static/imgs/diamond/address-popup-icon.png" mode="aspectFit" width="32"
                                    height="32">
                                </u-image>
                            </view>
                            <view class="ai-bottom twoOver">
                                {{ item.address_s }}
                            </view>
                        </view>
                        <view class="ver-center">
                            <button-bar class="but" @click="checkAddress(item, index)" text="选择"></button-bar>
                        </view>
                        <!-- <u-icon name="arrow-right" class="icon-arrow"></u-icon> -->
                    </view>
                </scroll-view>
            </view>
            <view class="address_footer">
                <view class="add-btn" @click="nav_addAddress()">
                    +&nbsp;新建收货地址
                </view>
            </view>
        </u-popup>

    </view>
</template>

<script>
import ButtonBar from "@/components/public/ButtonBar";
import payPopup from "@/components/payPopup/index.vue";
import antiShake from "@/common/public.js"
export default {
    data() {
        return {
            tabList: [{
                cate_name: '藏品',
                value: 'NFT'
            }, {
                cate_name: '实物',
                value: 'REAL'
            }],
            screenList: [{
                name: "全部",
                value: ""
            }, {
                name: "钻石",
                value: "GOLDEN_WORM"
            }, {
                name: "蓝宝石",
                value: "SILVERY_WORM"
            }, {
                name: "红宝石",
                value: "COPPER_WORM"
            }, {
                name: "紫宝石",
                value: "COMMON_WORM"
            }],
            sun: 0,
            current: 1,
            List: [],
            isShowNavIcon: false,
            show: false,
            itemType: 'REAL',
            walletType: '',
            msg_text: '哎哟！暂时没有可兑换的物品哦',
            worm_info: "",
            openInfo: "",
            wormId: "",
            isBack: true,
            isLoadding: false,
            pageNum: 1,
            pageSize: 10,
            isRequest: false, //多次请求频繁拦截
            isFooter: true, //没有更多了

            isHaveAddress: false,
            isShowAddressPopup: false,
            addressList: [],
            remark_show: false,
            remark_value: '',
            addressId: ''
        };
    },
    onShow() {
        this.getAddress()
    },
    onLoad(option) {
        const {
            token,
            platform,
        } = option;
        if (token) {
            uni.setStorageSync('token', token);
        }
        this.platform = platform;
        switch (platform) {
            case 'ios':
                this.isBack = false;
                break;
            case 'android':
                this.isBack = false;
                break;
            default:
                this.isBack = true
        }
        this.get_info()
    },
    onReachBottom() {
        if (this.isFooter) {
            if (this.isRequest == false) {
                this.get()
            } else {
                console.log("请求超时，已经拦截")
            }
        } else {
            this.loadStatus = 'nomore';
            console.log("已经到底了")
        }
    },
    computed: {

    },
    methods: {
        getImageByWalletType(walletType) {
            const images = {
                GOLDEN_WORM: "https://cdn-lingjing.nftcn.com.cn/image/20241210/c471bd29d8bdfbd3ee25d859c82ae326_166x166.png",
                SILVERY_WORM: "https://cdn-lingjing.nftcn.com.cn/image/20241210/b9a7f8b7223cbb75ddd8c90f7668644f_166x166.png",
                COPPER_WORM: "https://cdn-lingjing.nftcn.com.cn/image/20241210/1f24d5bef19519bb0aaecd82da9c4670_166x166.png",
                COMMON_WORM: "https://cdn-lingjing.nftcn.com.cn/image/20241210/fe461fb14a1e352f204d74fa32331f20_166x166.png"
            };
            return images[walletType] || '';
        },
        custom() {
            const pages = getCurrentPages();
            if (pages.length === 1) {
                this.$Router.pushTab({
                    name: "mall_new"
                })
            } else {
                this.$Router.back();
            }
        },

        async get() {
            this.isFooter = true
            this.isRequest = false
            let res = await this.$api.java_exchangeitemList({
                itemType: this.itemType,
                walletType: this.walletType,
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.status.code == 0) {
                console.log(res.result.list)
                this.isLoadding = false
                if (res.result.list == null || res.result.list == "") {
                    console.log("没数据咯")
                    this.isFooter = false
                } else {
                    this.pageNum++
                    res.result.list.forEach((item) => {
                        this.List.push(item)
                    })
                }
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        nav_link(title) {
            this.$Router.push({
                name: "generalAgreement",
                params: {
                    title: title,
                    link: "https://www.nftcn.com.cn/link/#/pages/index/DiamondAgreement"
                }
            })
            // window.location.href = 'https://www.nftcn.com/link/#/pages/index/wormAgreement'
        },
        async get_info(psw) {
            let res = await this.$api.java_walletInfo();
            if (res.status.code == 0) {
                console.log(res)
                this.worm_info = res.result
                this.get()
            } else if (res.status.code == 1002) {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
                setTimeout(() => {
                    this.$Router.pushTab({
                        name: "mainLogin",
                        params: {
                            url: window.location.hash
                        }
                    })
                }, 1100);
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        antiShakeclick: antiShake._debounce(function () {
            if (this.openInfo.itemType == 'REAL') {
                this.show = false
                this.getAddress()
                this.isShowAddressPopup = true
            } else {
                this.submit()
            }
        }, 200),
        async submit() {
            this.isLoadding = true
            let res = await this.$api.java_exchangeExchange({
                ...this.openInfo,
                addressId: this.addressId,
                userRemark: this.remark_value
            });
            if (res.status.code == 0) {
                uni.showToast({
                    title: '兑换成功，等待奖品发放',
                    icon: 'none',
                    duration: 3000
                });
                this.show = false
                this.isLoadding = false
                if (this.$refs.uWaterfall != undefined) {
                    this.$refs.uWaterfall.clear()
                }
                this.pageNum = 1
                this.addressId = ""
                this.remark_value = ""
                this.get_info()
            } else {
                this.show = false
                this.isLoadding = false
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
                this.addressId = ""
                this.remark_value = ""
            }
        },
        realClick: antiShake._debounce(function () {
            this.remark_show = false
            this.submit()
        }, 200),
        nav_open(item, index) {
            if (item.remainNum != 0) {
                if (item.walletType == 'GOLDEN_WORM') {
                    item.name = '钻石'
                } else if (item.walletType == 'SILVERY_WORM') {
                    item.name = '蓝宝石'
                } else if (item.walletType == 'COPPER_WORM') {
                    item.name = '红宝石'
                } else if (item.walletType == 'COMMON_WORM') {
                    item.name = '紫宝石'
                }
                this.openInfo = item
                this.show = true
            }
        },
        change(e) {
            console.log(e)
            this.current = e
            this.pageNum = 1
            this.itemType = this.tabList[e].value
            this.isRequest = false //多次请求频繁拦截
            this.isFooter = true //没有更多了
            if (this.$refs.uWaterfall != undefined) {
                this.$refs.uWaterfall.clear()
            }
            this.get()
        },
        check(item, index) {
            this.sun = index
            this.sun = index
            this.pageNum = 1
            this.walletType = item.value
            if (this.$refs.uWaterfall != undefined) {
                this.$refs.uWaterfall.clear()
            }
            this.get()
        },
        async getAddress() {
            let res = await this.$api.address({});
            if (res.status.code === 0) {
                if (res.result.list != "[]") {
                    this.isHaveAddress = true;
                    this.addressList = res.result.list
                    this.addressList.forEach((item, index) => {
                        item.address_s = item.district + item.address
                        if (this.addressId == item.consigneeId) {
                            this.defaultAddress = item;
                        } else if (index == 0) {
                            this.defaultAddress = item;
                        }
                    });
                } else {
                    this.isHaveAddress = false;
                }
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: "none",
                    duration: 3000,
                });
            }
        },
        checkAddress(item, index) {
            console.log(item);
            this.defaultAddress = item;
            this.defaultAddress.address_s =
                this.defaultAddress.district +
                this.defaultAddress.address
            this.isShowAddressPopup = false;
            this.addressId = item.consigneeId
            this.remark_show = true
        },
        nav_updataAddress(item) {
            uni.setStorageSync("addressList", item);
            this.$Router.push({
                name: "updataAddress",
                params: {
                    addressId: item.consigneeId,
                    type: "integral",
                },
            });
        },
        nav_addAddress() {
            this.$Router.push({
                name: "addAddress",
                params: {
                    type: "integral",
                },
            });
        },
    },
    onPageScroll(res) {
        if (res.scrollTop >= 100) {
            this.isShowNavIcon = true;
        } else {
            this.isShowNavIcon = false;
        }
    },
    components: {
        ButtonBar,
        payPopup
    }

}
</script>

<style lang="scss" scoped>
// @font-face {
// 	font-family: 'iconfont';
// 	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/integral_font.ttf') format('truetype');
// }

page {
    background-color: #070707;
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230209/67da70dac1922338f25bc3aae52b51ef_2304x1766.png);
    background-size: 100%;
    background-repeat: no-repeat;
}

.main {
    .cart_view {
        width: 100%;
        padding: 40rpx;

        .cart {
            width: 100%;
            height: 266rpx;
            background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230209/3f39a936f7127a22524e7f202949d460_1920x756.png);
            background-size: 100% 100%;
            padding: 26rpx 40rpx;

            .bug {
                margin-top: 24rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .li {
                    .img {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-bottom: 12rpx;

                        image {
                            width: 84rpx;
                            height: 84rpx;
                        }
                    }

                    color:#fff;
                    font-size:20rpx;
                    text-align: center;

                    .name {
                        margin-bottom: 12rpx;
                    }
                }
            }

            .title {
                font-family: 'iconfont';
                font-size: 26rpx;
                color: #fff;
            }
        }
    }

    .content {
        background-color: #070707;
        height: 70vh;
        margin-top: -60rpx;
        border-top-left-radius: 34rpx;
        border-top-right-radius: 34rpx;

        .tab_view {
            padding-top: 10rpx;
            width: 300rpx;
        }

        .body_list {
            padding: 40rpx;

            .screen {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .li {
                    background-color: #4C4C4C;
                    color: #9E9C9C;
                    margin-right: 20rpx;
                    height: 44rpx;
                    line-height: 44rpx;
                    padding: 0rpx 20rpx;
                    font-size: 22rpx;
                    border-radius: 20rpx;

                    &.active {
                        background: linear-gradient(81deg, #F6AAF2 0%, #8CC9F3 54%, #00FBEF 100%);
                        color: #32323B;
                    }

                }
            }
        }
    }
}

.slot-wrap {
    color: #fff;
    display: flex;
    align-items: center;
    padding-right: 20rpx;

    .tag {
        font-size: 22rpx;
        color: #E9E8E8;
        border-radius: 20rpx;
        border: 1rpx solid #E9E8E8;
        height: 36rpx;
        width: 110rpx;
        text-align: center;
        line-height: 32rpx;
    }

    /* 如果您想让slot内容占满整个导航栏的宽度 */
    /* flex: 1; */
    /* 如果您想让slot内容与导航栏左右有空隙 */
    /* padding: 0 30rpx; */
}

.index_explore {
    margin-top: 52rpx;
    padding-left: 4rpx;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;

    .item {
        overflow: hidden;
        background-color: #212122;
        border-radius: 12rpx;
        margin-bottom: 16rpx;
        // padding:20rpx;
        width: 324rpx;
        margin-right: 30rpx;

        .image_bg {
            overflow: hidden;
            position: relative;
            width: 100% !important;
            height: 322rpx;

            // border-radius: 12rpx;
            .u-image {
                width: 100% !important;
                height: 322rpx !important;
                border-top-left-radius: 12rpx;
                border-top-right-radius: 12rpx;
            }

            .bg {
                position: absolute;
                left: 0rpx;
                bottom: 0rpx;
                height: 68rpx;
                width: 100%;
                display: flex;
                align-items: flex-end;

                .bg_icon {
                    width: 187rpx;
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                }

                .image_text {
                    color: #DEDEDE;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 38rpx;
                    width: 100%;
                    font-size: 22rpx;
                    z-index: 99;
                }

                .tuishi {
                    position: absolute;
                    bottom: 34rpx;
                    right: 10rpx;
                    width: 130rpx;
                    background-color: rgba(0, 0, 0, 0.3);
                    border-radius: 50%;

                    image {
                        width: 100%;
                    }
                }
            }

        }

        .name {
            color: var(--main-front-color);
            font-size: 28rpx;
            line-height: 32rpx;
            margin-top: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
        }

        .info {
            // background: #1E1E1E;
            position: relative;
            font-family: PingFangSC-Semibold, PingFang SC;
            border-bottom-left-radius: 8rpx;
            border-bottom-right-radius: 8rpx;
            padding: 20rpx 20rpx 20rpx 20rpx;

            .title {
                font-size: 28rpx;
                font-weight: bold;
                color: var(--main-front-color);
                line-height: 28rpx;
                width: 300rpx;
                font-family: PingFangSC-Semibold, PingFang SC;
            }

            .tag {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-top: 10rpx;
                background-color: #383838;
                border-radius: 4rpx;
                width: 170rpx;

                .red {
                    background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20221102/a009e0535dd8593616346de21b2416aa_108x56.png');
                    color: #111111;
                    width: 54rpx;
                    height: 34rpx;
                    background-size: 100% 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 22rpx;
                }

                .text {
                    color: #FFFFFF;
                    font-size: 22rpx;
                    padding-left: 10rpx;
                }
            }

            .price {
                // color: $uni-text-color-warn;
                display: flex;
                justify-content: flex-end;
                justify-content: space-between;
                color: #fff;
                margin-top: 20rpx;

                .left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    text {
                        font-weight: 600;
                    }

                    .img {
                        margin-left: 10rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }
                    }
                }

                .but {
                    background: linear-gradient(81deg, #F6AAF2 0%, #8CC9F3 54%, #00FBEF 100%);
                    color: #32323B;
                    font-size: 24rpx;
                    height: 40rpx;
                    line-height: 36rpx;
                    padding: 0 10rpx;
                    border-radius: 36rpx;

                    &.huise {
                        background: #484848 !important;
                        color: #A2A1A1;
                    }
                }
            }
        }
    }

    #u-right-column {
        .item {
            margin-right: 0rpx;
        }
    }
}


.progress {
    display: inline-flex;
    width: 170rpx;
    height: 20rpx;
    position: relative;

    .progress_text {
        width: 170rpx;
        text-align: center;
        position: absolute;
        left: 0;
        // top: 10rpx;
        line-height: 20rpx;
        height: 20rpx;
        font-size: 14rpx;
        color: #000000;
    }
}

.popop_body {
    background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20230210/47e1ec160b1802f716127f37854766f5_1920x2048.png);
    background-size: 100% 100%;
    height: 656rpx;
    width: 700rpx;
    position: relative;

    .text {
        width: 574rpx;
        height: 160rpx;
        // background-color:#111111;
        position: absolute;
        top: 230rpx;
        left: 60rpx;
        font-weight: 600;
        // font-family: 'iconfont';
        color: #000000;
        font-size: 32rpx;
        padding: 0rpx 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .font_view {
            text-align: center;

            >view {
                margin-bottom: 10rpx;
            }
        }
    }

    .button {
        width: 574rpx;
        height: 70rpx;
        // background-color:#111111;
        position: absolute;
        top: 550rpx;
        left: 60rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        >view {
            width: 234rpx;
            height: 70rpx;
            border: 1px solid #211D1D;
            text-align: center;
            line-height: 70rpx;
            background-color: #FFFFFF;
            font-size: 28rpx;
            font-weight: 600;
        }

        .submit {
            background-color: #04FFF6;
        }

        .cancel {}
    }
}

.null {
    width: 100%;

    ::v-deep .u-icon__img {
        width: 170rpx !important;
    }

    ::v-deep .u-icon__label {
        margin-top: 46rpx !important;
        font-size: 28rpx;
        color: #616161 !important;
    }
}

.address-popup {
    .ap-title {
        height: 120rpx;
        line-height: 120rpx;
        text-align: center;
        font-weight: 700;
        color: #F9F9F9;
        // background-color: var(--dialog-bg-color);
        background: #35333E;
        border-bottom: 1rpx solid #666666;
        position: relative;

        >text {
            position: absolute;
            top: 0;
            right: 42rpx;
            color: #888;
            font-size: 28rpx;
            font-weight: 400;
        }
    }

    // .ap-list {
    // 	.ap-item {
    // 		height: 160rpx;
    // 		padding: 0 42rpx;
    // 		align-items: center;

    // 		.api-left {

    // 		}
    // 	}
    // }
}

.content {
    height: 800rpx;
    background: #35333E;


    .add-box {
        height: 160rpx;
        padding: 20rpx 42rpx;
        border-bottom: 2rpx solid #282828;
        color: #F9F9F9;

        .add-info {
            width: 450rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .ai-top {
                font-weight: 700;

                span:nth-child(2) {
                    margin-left: 20rpx;
                    font-size: 28rpx;
                    margin-right: 30rpx;
                }

                display: flex;
                justify-content: flex-start;
                align-items: center;
            }

            .ai-bottom {
                line-height: 35rpx;
                font-size: 28rpx;
                color: var(--secondary-front-color);
            }
        }

        .icon-arrow {
            color: #999;
        }
    }
}

.address_footer {
    background: #35333E;

    padding: 32rpx 40rpx 90rpx;

    .add-btn {
        height: 90rpx;
        line-height: 90rpx;
        font-size: 32rpx;
        color: #000;
        background: var(--primary-button-color);
        text-align: center;
        font-weight: 600;
    }
}

.info {
    padding: 32rpx 40rpx;
    margin-top: 25rpx;
    // background-color: #1E1E1E;

    .i-top {
        .uni-image {
            width: 126rpx;
            height: 126rpx;
        }

        .it-center {
            flex: 1;
            max-width: 420rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: var(--secondary-front-color);
            font-size: 24rpx;
            line-height: 24rpx;
            margin-left: 24rpx;

            .it_name {
                font-size: 28rpx;
                line-height: 28rpx;
                font-weight: 400;
                color: #F9F9F9;
                margin-bottom: 24rpx;
            }

            .it_msg {
                margin-top: 60rpx;
            }
        }

        .it-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-family: MiSans-Demibold, MiSans;
            font-weight: 600;
            color: #F9F9F9;
            line-height: 28rpx;

            view:nth-child(2) {
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: var(--secondary-front-color);
                line-height: 24rpx;
                font-size: 24rpx;
                transform: scale(0.92);
                margin-top: 24rpx;
            }
        }
    }

    .i-center {
        height: 50rpx;
        line-height: 70rpx;
        margin: 10rpx 0 20rpx;
        font-size: 24rpx;
        font-weight: 700;

        // /deep/ .u-numberbox {
        // 	.u-number-input {
        // 		background: var(--message-box-point-color)!important;
        // 	}

        // 	.u-icon-minus,
        // 	.u-icon-plus {
        // 		border-radius: 5rpx;
        // 	}
        // }
    }

    .i-bottom {
        font-size: 24rpx;
        font-weight: 700;

        .way_content {
            margin-top: 30rpx;
            color: #777777;
        }
    }
}

.space-between {
    display: flex;
    justify-content: space-between;
}

.address {
    margin-top: 30rpx;
    padding: 20rpx 42rpx;
    overflow: hidden;
    color: var(--secondary-front-color);
    background-color: #1E1E1E;

    .icon-map {
        float: left;
        margin-top: -7rpx;
    }

    .add-info {
        height: 120rpx;
        width: 550rpx;
        padding-left: 15rpx;
        display: inline-flex;
        flex-direction: column;
        justify-content: space-between;

        .ai-top {
            span:nth-child(2) {
                margin-left: 20rpx;
                font-size: 28rpx;
            }
        }

        .ai-bottom {
            line-height: 35rpx;
            font-size: 28rpx;
        }
    }

}

.ver-center {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .but {
        width: 100rpx;
        height: 45rpx;
        border-radius: 10rpx;
        font-size: 24rpx;
        font-weight: 600;
    }

    // padding: 40rpx;
}

.popop_remark {
    width: 580rpx;
    background: #1e1e1e;
    border-radius: 10rpx;
    padding: 60rpx 40rpx;

    .title {
        color: #fff;
        text-align: center;
        font-size: 40rpx;
        margin-bottom: 40rpx
    }

    .msg {
        color: #f4f4f4;
        font-size: 24rpx;
        text-align: center;
        margin-bottom: 40rpx
    }

    .input {
        border-radius: 10rpx;
        // border:1rpx solid #9E9C9C;
        width: 100%;
        height: 200rpx;
        margin: 30rpx 0rpx;
        // padding:10rpx;
        color: #fff;
    }

    .button {
        height: 70rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 50rpx;

        >view {
            width: 100%;
            height: 70rpx;
            border: 1px solid #211D1D;
            text-align: center;
            line-height: 70rpx;
            background-color: #FFFFFF;
            font-size: 28rpx;
            font-weight: 600;
        }

        .submit {
            border-radius: 10rpx;
        }

        .cancel {}
    }

}

::v-deep .u-input__textarea {
    color: #FFF !important;
    height: 150rpx !important;
    overflow: auto;
}
</style>
