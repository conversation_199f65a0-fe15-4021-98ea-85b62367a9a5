<template>
	<view class="main">
		<u-verification-code :seconds="60" ref="uCode" @change="codeChange" change-text="重新获取 (xs)"
			@start="startStyle = true" @end="startStyle = false"></u-verification-code>
		<u-navbar back-icon-color="var(--main-front-color)" title-color="var(--default-color1)" title-bold
			:border-bottom="false" title="修改账号密码" :background="{backgroundColor: 'var(--main-bg-color)'}">
			<view slot="right" class="confirm" @click="verify">确认</view>
		</u-navbar>
		<!-- 顶部提示 -->
		<u-top-tips ref="warningTips"></u-top-tips>
		<!-- toast提示 -->
		<u-toast ref="uToast" />

		<view class="content">
			<view class="form">
				<u-form :model="form" ref="uForm">
					<u-form-item>
						<u-input v-model="form.account" placeholder="请输入邮箱号/手机号" />
						<view slot="right" v-if="showWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="请输入正确的手机号或邮箱号"></tooltip>
						</view>
					</u-form-item>
					<u-form-item>
						<u-input v-model="form.verifyCode" placeholder="请输入验证码" />
						<span slot="right" class="get-code"
							:style="{color:startStyle ? 'var(--info-front-color)' : 'var(--active-color)'}"
							@click="startCode">{{ tips }}</span>
					</u-form-item>
					<u-form-item>
						<u-input type="password" password-icon v-model="form.password" placeholder="请输入密码" />
						<view slot="right" v-if="showPwdWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="密码长度6位以上"></tooltip>
						</view>
					</u-form-item>
					<u-form-item>
						<u-input type="password" password-icon v-model="form.rePassword" placeholder="请确认新密码" />
						<view slot="right" v-if="showRePwdWarn">
							<image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
							<tooltip title="两次密码输入不一致"></tooltip>
						</view>
					</u-form-item>
				</u-form>
			</view>
			<!--            <view class="submit">-->
			<!--                <u-button  @click="forgetPassword()">确认</u-button>-->
			<!--            </view>-->

		</view>
		<view class="footer">
			收不到验证码?请发邮件至官方邮箱
			<view class="active"><EMAIL></view>
		</view>
	</view>
</template>

<script>
	import tooltip from "@/pages/project/login/components/Tooltip";
	export default {
		components: {
			tooltip
		},
		data() {
			return {
				form: {},
				showWarn: false,
				showRePwdWarn: false,
				showPwdWarn: false,
				startStyle: '',
				tips: '获取验证码',
				repeatPwd: '',
				is: {
					isShowVerificationTip1: true,
					isShowVerificationTip2: false
				},
				timeout: 60
			};
		},
		watch: {
			'form.account'() {
				this.showWarn = false
			},
			'form.password'() {
				this.showPwdWarn = false
				this.showRePwdWarn = false
			},
			'form.rePassword'() {
				this.showRePwdWarn = false
			},
		},
		methods: {
			PasswordStrength(pwd) {
				if (pwd.length >= 6) {
					return true
				}
			},
			codeChange(text) {
				this.tips = text;
			},
			// 判断账号类型 获取验证码
			async startCode() {
				if (!this.startStyle) {
					this.startStyle = true
					const {
						form: {
							account
						},
						$u: {
							test
						}
					} = this;
					let isEmail = test.email(account),
						isPhone = test.mobile(account);
					if (!isEmail && !isPhone) {
						this.showWarn = true;
						return;
					}
					if (isEmail) {
						await this.codeAction('email', {
							emailAddress: account,
							emailType: "RESET_PASSWORD"
						});
					} else if (isPhone) {
						await this.codeAction('phone', {
							mobPhone: account,
							aliYumSmsType: "RESET_PASSWORD"
						});
					}
				}
			},
			// 获取验证码
			async codeAction(type, obj) {
				let url = {
					email: 'java_sendAliYunEmail',
					phone: 'java_sendAliYunSms',
				}
				this.startStyle = false
				let {
					status: {
						code,
						msg
					}
				} = await this.$api[url[type]](obj)
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.$refs.uCode.start();
				}
			},
			// 找回密码接口
			async reSetPassword() {
				uni.showLoading({
					title: '登录中...',
				})
				const {
					form: {
						password
					}
				} = this;
				let {
					status: {
						code,
						msg
					}
				} = await this.$api.java_appUserLoginReSetPassword({
					password
				})
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.$refs.uToast.show({
						title: '修改成功，跳转到登录页',
						type: 'success',
					})
					uni.removeStorageSync("token")
					setTimeout(() => {
						this.$Router.pushTab({
							name: 'login',
						});
					}, 1500)
				}
			},
			// 验证接口
			async verify() {
				const {
					form: {
						account,
						verifyCode,
						password,
						rePassword
					},
					$u: {
						test
					},
					PasswordStrength
				} = this;
				// 校验账号是否为手机号或者邮箱号
				let isEmail = test.email(account),
					isPhone = test.mobile(account);
				if (!isEmail && !isPhone) {
					this.showWarn = true;
					return;
				}
				// 正则校验账号密码是否满足要求
				if (!PasswordStrength(password)) {
					this.showPwdWarn = true
					return
				}
				// 校验两次密码是否一致
				if (rePassword !== password) {
					this.showRePwdWarn = true
					return
				}
				// 判断使用手机号接口还是邮箱接口
				let url = isEmail ? 'java_verifyAliYunEmailCode' : 'java_verifyAliYunPhoneCode';
				let obj = {
					code: verifyCode,
				}
				// 判断字段
				obj[isEmail ? 'emailAddress' : 'mobPhone'] = account;
				obj[isEmail ? 'emailType' : 'aliYumSmsType'] = 'RESET_PASSWORD';
				// 发送请求
				let {
					status: {
						code,
						msg
					}
				} = await this.$api[url](obj)
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					await this.reSetPassword()
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		.confirm {
			margin-right: 30rpx;
			font-size: 28rpx;
			color: var(--active-color);
		}

		.content {
			margin: 40rpx 80rpx 0;

			.form {
				margin-top: 70rpx;
				margin-bottom: 64rpx;

				::v-deep {
					.u-input__input {
						color: var(--main-front-color);
					}
				}

				.uni-input-placeholder {
					color: var(--info-front-color) !important;
					font-size: 28rpx;
				}

				.u-border-bottom::after {
					border: none;
					border-bottom: 1px solid var(--form-border-color);
				}
			}

			.forget {
				margin-top: 28rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: var(--secondary-front-color);
				text-align: center;
			}
		}

		.footer {
			position: fixed;
			bottom: 155rpx;
			left: 0rpx;
			margin: auto 10%;
			font-size: 24rpx;
			color: var(--info-front-color);
			display: flex;
			justify-content: center;
			/*水平主轴居中*/
			flex-wrap: wrap;

			.active {
				margin-top: 10rpx;
				color: var(--active-color);
			}
		}
	}
</style>
