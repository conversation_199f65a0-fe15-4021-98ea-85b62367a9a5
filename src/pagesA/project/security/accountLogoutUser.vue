<template>
	<view class="main">
		<u-navbar back-icon-color="#000"  :background="{backgroundColor: '#35333E'}" title="注销申请" title-color="#fff" title-bold :custom-back='nav_index'></u-navbar>
		<!-- tab切换 -->
		<view class="ee">
			<view class="a">
				<view class="icon">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20220526/447733d64f6542dfdcf98b7ee1817dcf_170x122.png" mode=""></image>
				</view>
				<view class="text">
					该用户账号已注销
				</view>
			</view>
			
		</view>
	</view>
</template>
<script>
	import fa from "@/common/public.js"
	export default {
		data() {
			return {
				
			};
		},
		onLoad(options) {
		},
		// 加载更多
		onReachBottom() {
				this.loadStatus = 'loading';
				setTimeout(() => {
					this.addRandomData();
				}, 1000)
		},
		methods: {
			nav_index(){
				this.$Router.back(2)
			}

		},
	}
</script>

<style lang="scss">
	.ee{
		width: 100%;
		height: 90vh;
		display: flex;
		align-items: center;
		justify-content: space-around;
		.a{
			.icon{
				width: 170rpx;
				height: 122rpx;
				margin: 0 auto;
				margin-bottom: 46rpx;
				image{
					width: 100%;
					height: 100%;
				}
			}
			.text{
				text-align: center;
				font-size: 28rpx;
				font-weight: 400;
				color: #616161;
				line-height: 44rpx;
			}
		}
		
	}

</style>
