<template>
	<view class="main">
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="账户注销" title-color="var(--default-color1)"
			title-bold>
		</u-navbar>
		<!-- 顶部提示 -->
		<u-top-tips ref="warningTips"></u-top-tips>
		<!-- toast提示 -->
		<u-toast ref="uToast" />
		<view class="content">
			<view class="max_title">
				<image src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241018/1cf78121a7a91b1031f7e87fe4f3d3ef_30x30.png" mode="widthFix"></image>
				<text>账户注销后：</text>
			</view>
			<view class="list">
				<view class="li">
					・移除该账户所有登录方式且无法找回
				</view>
				<view class="li">
					・账户内所有数据将被清空
				</view>
				<view class="li">
					・账户所有铸造、交易流程将被终止
				</view>
				<view class="li">
					・账户持有的所有藏品将被销毁
				</view>
				<view class="li" v-if="balance">
					・账户余额：<text>¥{{balance}}</text>，您将无法提取该笔资金
				</view>
			</view>
			<!-- <view class="tips">
				轻按下方「下一步」按钮，即表示您已阅读并同意 <span @click="nav_link('NFT账号注销')">《Bigverse 账号注销须知》</span>
			</view>
			<view class="action">
				<button-bar @click="nav_accountLogoutApplication()" text="下一步"></button-bar>
			</view> -->
		</view>
		<view class="footer">
			<view class="xieyi">
				<view class="img" @click="isAgreement = !isAgreement">
					<image :src="`../../../static/login/${isAgreement?'jxs2x':'jx'}.png`" mode="widthFix"></image>
				</view>
				<view class=""  @click="isAgreement = !isAgreement">
					我了解账户注销的后果
				</view>
			</view>
			<view class="xieyi">
				<view class="img" >
					<image :src="`../../../static/login/${isAgreement2?'jxs2x':'jx'}.png`" mode="widthFix"></image>
				</view>
				<view class="text" >
					<view @click="isAgreement2 = !isAgreement2">我已阅读并同意</view><text @click="nav_link('BIGVERSE账号注销须知')">《BIGVERSE账号注销须知》</text>
				</view>
			</view>
			<view class="but" :class="{'active':isAgreement2&&isAgreement}" @click="nav_accountLogoutApplication()">
				下一步
			</view>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";

	export default {
		components: {
			ButtonBar
		},
		data() {
			return {
				uid: '',
				isAgreement:false,
				isAgreement2:false,
				balance:0
			};
		},
		onLoad() {
			this.uid = uni.getStorageSync("uid")
			this.getBalance()
		},
		methods: {
			//下一页
			async nav_accountLogoutApplication() {
				if(this.isAgreement&&this.isAgreement2){
					let {
						status: {
							code,
							msg
						}
					} = await this.$api.java_execute({
						code: "",
						mobPhone: "",
						aliYumSmsType: "ACCOUNT_CANCELLATION"
					});
					if (msg === '当前时间距离最后一次交易时间需大于30天') {
						this.$refs.uToast.show({
							title: msg,
							type: 'default',
							duration: 3000
						})
						return
					}
					this.$Router.push({
						name: 'accountLogoutApplication'
					})
				}else{
					this.$refs.uToast.show({
						title: '请先勾选协议',
						type: 'default',
						duration: 3000
					})
				}
			},
			async nav_link(title) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com.cn/link/#/pages/index/logout"
					}
				})
			},
			async getBalance() {
				let res = await this.$api.java_balance_info();
				this.balance = res.result.available
				console.log(res)
			},
			back() {
				this.$Router.back()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		margin: 40rpx 40rpx 0;
		letter-spacing: 2rpx;
		color:#fff;
		.tips {
			word-break: break-all;
			margin-top: 24rpx;
			font-size: 24rpx;
			line-height: 36rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			color: var(--default-color3);

			span {
				color: var(--active-color1);
			}
		}

		.tips-title {
			margin-top: 60rpx;
			font-size: 28rpx;
			font-weight: bold;
			font-family: PingFangSC-Regular, PingFang SC;
			color: var(--active-color1);
		}
		.max_title{
			font-size:34rpx;
			font-weight:600;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-bottom:30rpx;
			image{
				width:30rpx;
				margin-right:10rpx;
			}
		}
		.list{
			.li{
				color:#9A999F;
				font-size:28rpx;
				line-height: 50rpx;
				text{
					color:#42F7EC;
				}
			}
		}
		.action {
			margin-top: 24rpx;
			padding-bottom: 60rpx;
		}
	}
	.footer{
		width:100%;
		padding:40rpx;
		position:fixed;
		bottom:0;
		left:0;
		.xieyi{
			color:#A6A6A6;
			font-size:24rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-bottom:20rpx;
			text{
				color: #42F7EC;
			}
			.img{
				margin-right:12rpx;
				image{
					width:30rpx;
				}
			}
			.text{
				display: flex;
				justify-content:flex-start;
			}
		}
		.but{
			width:670rpx;
			height:120rpx;
			border-radius:60rpx;
			background: rgba(255,255,255,0.2);
			border: 1px solid #FFFFFF;
			color:#fff;
			line-height: 120rpx;
			text-align: center;
			margin-top:40rpx;
			font-size:34rpx;
			font-weight:600;
			&.active{
				background: linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%);
				color:#141414;
				border:none;
			}
		}
	}
	::v-deep {
		.u-toast {
			min-width: 90%;
		}
	}
</style>