<template>
	<view class="main">
		<u-toast ref="uToast" />
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="账户验证" title-color="var(--main-front-color)">
		</u-navbar>
		<view class="content">
			<view class="step-1" v-if="step === 1">
				<span v-if="phone">为保护您的帐号安全，需要对现在绑定的手机号进行验证码验证（{{ phone }})</span>
				<span v-else>为保护您的帐号安全，需要对现在绑定的邮箱账号进行验证码验证（{{ email }})</span>
				<button-bar class="action" text="下一步" @click="step = 2"></button-bar>
			</view>
			<view class="step-2" v-if="step === 2" :key="step">
				<view class="title">
					请输入验证码
				</view>
				<view class="tips">
					已发送至{{ phone || email }}
				</view>
				<input-bar v-model="phoneCode" :item="item" v-for="item in inputList" :key="item.key" showCode
					@getCode="start" :startCountdown="startCountdown" @end="startCountdown = false"></input-bar>
				<button-bar class="action" text="下一步" @click="verify(phoneCode)"></button-bar>
			</view>
			<view class="step-3" v-if="step === 3" :key="step">
				<input-bar v-model="form[item.key]" :item="item" v-for="item in inputList2" :key="item.key"
					:showCode="item.showCode" @end="startCountdown = false" @getCode="start('PHONE_BIND')"
					:startCountdown="startCountdown"></input-bar>
				<button-bar class="action" text="确认" @click="verify(form.phoneCode,'PHONE_BIND')"></button-bar>
			</view>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	import InputBar from "@/components/public/InputBar";

	export default {
		name: "emailVerify",
		components: {
			ButtonBar,
			InputBar
		},
		data() {
			return {
				inputList: [{
					type: 'input',
					key: 'emailCode',
					placeholder: '请输入验证码',
					showCode: true,
				}, ],
				inputList2: [{
						type: 'input',
						key: 'phoneAddress',
						placeholder: '请输入新的手机号',
						showCode: false,
					},
					{
						type: 'input',
						key: 'phoneCode',
						placeholder: '请输入验证码',
						showCode: true,
					},
				],
				email: '',
				phone: '',
				step: 1,
				form: {},
				phoneCode: '',
				startCountdown: false,
				vtype: ""
			}
		},
		onLoad(options) {
			this.email = uni.getStorageSync('email')
			this.phone = options.phone
			this.vtype = options.vtype
		},
		methods: {
			start(type) {
				const {
					step
				} = this
				if (this.step === 2) {
					if (this.phone) {
						this.phoneAction(type)
					} else {
						this.emailAction(type)
					}
				} else {
					this.phoneAction(type)
				}

			},
			async emailAction(type = this.vtype) {
				const {
					email
				} = this
				let {
					status: {
						code,
						msg
					}
				} = await this.$api.java_sendAliYunEmail({
					emailAddress: email,
					emailType: type
				})
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.startCountdown = true
				}
			},
			// 获取验证码
			async phoneAction(type = this.vtype) {
				const {
					phone,
					form: {
						phoneAddress
					},
					step
				} = this
				let {
					status: {
						code,
						msg
					}
				} = await this.$api.java_sendAliYunSms({
					mobPhone: step === 2 ? phone : phoneAddress,
					aliYumSmsType: type
				})
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.startCountdown = true
				}
			},
			verify(verify, type) {
				const {
					step
				} = this
				if (verify == "" || verify == undefined) {
					this.$refs.uToast.show({
						title: "请输入验证码",
						type: 'error',
					})
				} else {
					if (step === 2) {
						if (this.phone) {
							this.nextStep(verify, type)
						} else {
							this.emailNextStep(verify, type)
						}
					} else {
						this.nextStep(verify, type)
					}
				}

			},
			async emailNextStep(verify, type = this.vtype) {
				const {
					email,
					form: {
						emailAddress
					},
					step
				} = this
				let {
					result,
					status: {
						code,
						msg
					}
				} = await this.$api.java_verifyAliYunEmailCode({
					emailAddress: step === 2 ? email : emailAddress,
					code: verify,
					emailType: type
				})
				this.startCountdown = false
				this.showCode = false
				this.showCode = true
				if (code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				} else {
					this.step = 3
					if (result.emailUUID != '') {
						this.emailUUID = result.emailUUID
					}
				}
			},
			async nextStep(verify, type = this.vtype) {
				const {
					phone,
					form: {
						phoneAddress
					},
					step
				} = this
				let params;
				if (this.phone) {
					params = {
						mobPhone: step === 2 ? phone : phoneAddress,
						code: verify,
						aliYumSmsType: step === 2 ? type : 'PHONE_BIND',
						smsUUID: this.smsUUID,
						uuidOrigin: this.phone ? "phone" : "email",
						oldPhoneNum: phone
					}
				} else {
					params = {
						mobPhone: step === 2 ? phone : phoneAddress,
						code: verify,
						aliYumSmsType: step === 2 ? type : 'PHONE_BIND',
						emailUUID: this.emailUUID,
						uuidOrigin: this.phone ? "phone" : "email",
						oldPhoneNum: phone
					}
				}
				let {
					result,
					status: {
						code,
						msg
					}
				} = await this.$api.java_verifyAliYunPhoneCode(params)
				this.startCountdown = false
				this.showCode = false
				this.showCode = true
				if (step === 2 && code !== 0) {
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				}
				if (step === 2 && code === 0) {
					if (result.smsUUID != '') {
						this.smsUUID = result.smsUUID
					}
					this.step = 3
				} else if (step === 3 && code === 0) {
					this.$refs.uToast.show({
						title: '绑定成功',
						type: 'success',
					})
					setTimeout(() => {
						this.$Router.push({
							name: 'person'
						})
					}, 2000)
				} else if (step === 3 && code != 0) {
					uni.setStorageSync("lastTime", parseInt(new Date().valueOf() / 1000))
					this.$refs.uToast.show({
						title: msg,
						type: 'error',
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		padding: 0 40rpx;
		font-size: 28rpx;
		line-height: 44rpx;
		color: var(--main-front-color);

		.step-1 {
			padding-top: 60rpx;
			text-align: center;
		}

		.step-2 {
			padding-top: 60rpx;

			.title {
				font-size: 36rpx;
			}

			.tips {
				margin-top: 24rpx;
				font-size: 28rpx;
				color: var(--info-front-color);
			}
		}

		.action {
			margin-top: 60rpx;
		}
	}
</style>
