<template>
	<view>

		<!-- <u-keyboard v-model="show" 
			default=""
			ref="uKeyboard" 
			mode="number" 
			:mask="false" 
			:dot-enabled="false" 
			:safe-area-inset-bottom="true"
			:tooltip="false"
			@change="onChange"
			@backspace="onBackspace">
		</u-keyboard> -->
		<u-popup v-model="isPayPassword" mode="bottom" border-radius="40" height="50%">
			<view class="modal_head">
				<view class="left">
					<image src="@/static/imgs/public/tuichu.png" mode="widthFix"></image>
				</view>
				设置支付密码
			</view>
			<view class="modal_Body">
				<view class="bank_ul">
					<view class="pwd-box">
						<view class="set-pwd">
							<view class="set-pwd-tip"><text>{{title}}</text>
							</view>
							<view class="set-error">{{error}}</view>
						</view>
						<view class="u-flex u-row-center">
							<u-message-input mode="box" :focus="true" :maxlength="6" :dot-fill="true" v-model="password"
								:disabled-keyboard="false" @change="change" @finish="finish">
							</u-message-input>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: true,
				title: "请设置余额支付密码，用于支付验证。",
				password: '',
				psw: '',
				error: '',
				isPayPassword: true
			};
		},
		onLoad() {

		},
		methods: {
			change(e) {
				// console.log(e)
			},
			finish(e) {
				this.password=e
				this.pay()
			},
			pay() {
				let _this=this
				uni.showLoading({
					title: '设置中'
				})
				if (this.psw == "") {
					setTimeout(() => {
						this.psw = this.password
					}, 1000)
				} else {
					this.error = ""
					if (this.psw == this.password) {
						uni.showToast({
							icon: 'success',
							title: '设置成功'
						})
						setTimeout(() => {
							this.$Router.push({
								name: 'securityCenter'
							});
						}, 2000);
					} else {
						setTimeout(() => {
							this.error = "二次密码不一致"
						}, 2000);
					}
				}
				setTimeout(() => {
					uni.hideLoading();
					console.log(this.password)
					this.title = "再次填写以确认",
					this.password=""
				}, 2000);
			},
		}
	}
</script>

<style lang="scss">
	.pwd-box {
		margin-top: 50rpx;

		.set-pwd {
			position: relative;

			.set-pwd-tip {
				font-size: 32rpx;
				color: #333;
				padding-bottom: 64rpx;
				text-align: center;
			}

			.set-error {
				color: #BB3835;
				font-size: 24rpx;
				text-align: center;
				position: absolute;
				left: 290rpx;
				bottom: 20rpx;
			}
		}
	}

	.modal_head {
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 42rpx;
		position: relative;

		.left {
			position: absolute;
			left: 40rpx;
			top: 20rpx;

			image {
				width: 30rpx;
			}
		}
	}

	.modal_Body {
		padding: 24rpx;

	}
</style>
