<template>
    <view class="main">
        <u-toast ref="uToast"/>
        <u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
                  :background="{backgroundColor: 'var(--main-bg-color)'}" title="账户验证"
                  title-color="var(--main-front-color)">
        </u-navbar>
        <view class="content">
            <view class="step-1" v-if="step === 1">
                <span v-if="phone">为保护您的帐号安全，需要对现在绑定的手机号进行验证码验证（{{ phone }})</span>
                <span v-else>为保护您的帐号安全，需要对现在绑定的邮箱账号进行验证码验证（{{ email }})</span>
                <button-bar class="action" text="下一步" @click="step = 2"></button-bar>
            </view>
            <view class="step-2" v-if="step === 2" :key="step">
                <view class="title">
                    请输入验证码
                </view>
                <view class="tips">
                     已发送至{{ phone || email }}
                </view>
                <input-bar v-model="emailCode" :item="item" v-for="item in inputList" :key="item.key" showCode
                           @getCode="start" :startCountdown="startCountdown"  @end="startCountdown = false"></input-bar>
                <button-bar class="action" text="下一步" @click="verify(emailCode)"></button-bar>
            </view>
            <view class="step-3" v-if="step === 3" :key="step">
                <input-bar v-model="form[item.key]" :item="item" v-for="item in inputList2" :key="item.key"
                           :showCode="item.showCode" @end="startCountdown = false"
                           @getCode="start(vtype)" :startCountdown="startCountdown" ></input-bar>
                <button-bar class="action" text="确认" @click="verify(form.emailCode,vtype)"></button-bar>
            </view>
        </view>
    </view>
</template>

<script>
import ButtonBar from "@/components/public/ButtonBar";
import InputBar from "@/components/public/InputBar";

export default {
    name: "emailVerify",
    components: {
        ButtonBar,
        InputBar
    },
    data() {
        return {
            inputList: [
                {
                    type: 'input',
                    key: 'emailCode',
                    placeholder: '请输入验证码',
                    showCode: true,
                },
            ],
            inputList2: [
                {
                    type: 'input',
                    key: 'emailAddress',
                    placeholder: '请输入新的邮箱号',
                    showCode: false,
                },
                {
                    type: 'input',
                    key: 'emailCode',
                    placeholder: '请输入验证码',
                    showCode: true,
                },
            ],
            email: '',
            step: 1,
            form: {},
            emailCode: '',
            startCountdown: false,
			code:"",
			emailUUID:"",
			vtype:""
        }
    },
    onLoad(options) {
        this.email = uni.getStorageSync('email'),
		this.phone = options.phone
		this.vtype = options.vtype
    },
    methods: {
		start(type){
			 const {step} = this
			if(step === 2){
				if(this.phone){
				    this.phoneAction(type)
				} else {
				    this.emailAction(type)
				}
			}else{
				 this.emailAction(type)
			}
		   
		},
        async emailAction(type = this.vtype) {
            const {email, form: {emailAddress}, step} = this
            let {status: {code, msg}} = await this.$api.java_sendAliYunEmail({
                emailAddress: step === 2 ? email : emailAddress,
                emailType: step === 2 ? type : 'EMAIL_BIND',
            })
            if (code !== 0) {
                this.$refs.uToast.show({
                    title: msg,
                    type: 'error',
                })
            } else {
                this.startCountdown = true
            }
        },
        // 获取验证码
        async phoneAction(type = this.vtype) {
            const {phone, form: {phoneAddress}, step} = this
            let {result,status: {code, msg}} = await this.$api.java_sendAliYunSms({
                mobPhone: step === 2 ? phone : phoneAddress,
                aliYumSmsType: type,
				emailUUID:this.emailUUID,
				code:this.code
            })
            if (code !== 0) {
                this.$refs.uToast.show({
                    title: msg,
                    type: 'error',
                })
            } else {
				console.log(result)
                this.startCountdown = true
            }
        },
		verify(verify,type){
			console.log(verify)
			 const {step} = this
			 if(verify==""||verify==undefined){
				 this.$refs.uToast.show({
				     title: "请输入验证码",
				     type: 'error',
				 })
			 }else{
				 if(step==2){
					 if(this.phone){
						this.nextStep(verify,type)
					 } else {
						 this.emailNextStep(verify,type)
					 }
				 }else{
				 	 this.emailNextStep(verify,type)
				 }
			 }
		},
		async emailNextStep(verify, type = this.vtype) {
		    const {email, form: {emailAddress}, step} = this
			console.log("邮箱验证码"+email)
			let params;
			if(this.phone){
				params = {
				    emailAddress: step === 2 ? email : emailAddress,
				    code: verify,
				    emailType: step === 2 ? type : 'EMAIL_BIND',
					smsUUID:this.smsUUID,
					uuidOrigin:this.phone ? "phone" : "email",
					oldEmail:email
				}
			}else{
				params = {
				    emailAddress: step === 2 ? email : emailAddress,
				    code: verify,
				    emailType: step === 2 ? type : 'EMAIL_BIND',
					emailUUID:this.emailUUID,
					uuidOrigin:this.phone ? "phone" : "email",
					oldEmail:email
				}
			}
		    let {result,status: {code, msg}} = await this.$api.java_verifyAliYunEmailCode(params)
		    this.startCountdown = false
		    this.showCode = false
		    this.showCode = true
			if (step === 2 && code !== 0) {
			    this.$refs.uToast.show({
			        title: msg,
			        type: 'error',
			    })
			}
			if (step === 2 && code === 0) {
			    this.step = 3
				if(result.emailUUID!=''){
					this.emailUUID=result.emailUUID
				}
			} else if (step === 3 && code === 0) {
			    this.$refs.uToast.show({
			        title: '邮箱绑定成功',
			        type: 'success',
			    })
			    uni.setStorageSync('email', emailAddress)
			    setTimeout(() => {
			        this.$Router.push({
			            name: 'person'
			        })
			    }, 2000)
			}else{
				this.$refs.uToast.show({
				    title: msg,
				    type: 'error',
				})
			}
		},
        async nextStep(verify, type = this.vtype) {
			const {phone, form: {phoneAddress}, step} = this
			let {result,status: {code, msg}} = await this.$api.java_verifyAliYunPhoneCode({
			    mobPhone: step === 2 ? phone : phoneAddress,
			    code: verify,
			    aliYumSmsType: type,
			})
            this.startCountdown = false
            this.showCode = false
            this.showCode = true
			console.log(result)
            if (step === 2 && code !== 0) {
                this.$refs.uToast.show({
                    title: msg,
                    type: 'error',
                })
            }
            if (step === 2 && code === 0) {
                this.step = 3
				if(result.smsUUID!=''){
					this.smsUUID=result.smsUUID
				}
            } else if (step === 3 && code === 0) {
                this.$refs.uToast.show({
                    title: '邮箱绑定成功',
                    type: 'success',
                })
                uni.setStorageSync('email', emailAddress)
                setTimeout(() => {
                    this.$Router.push({
                        name: 'person'
                    })
                }, 2000)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.content {
    padding: 0 40rpx;
    font-size: 28rpx;
    line-height: 44rpx;
    color: var(--main-front-color);

    .step-1 {
        padding-top: 60rpx;
        text-align: center;
    }

    .step-2 {
        padding-top: 60rpx;

        .title {
            font-size: 36rpx;
        }

        .tips {
            margin-top: 24rpx;
            font-size: 28rpx;
            color: var(--info-front-color);
        }
    }

    .action {
        margin-top: 60rpx;
    }
}
</style>
