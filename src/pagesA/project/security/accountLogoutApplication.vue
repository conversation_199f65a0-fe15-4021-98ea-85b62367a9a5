<template>
	<view class="main">
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="注销申请" title-color="var(--default-color1)"
			title-bold>
		</u-navbar>
		<!-- toast提示 -->
		<u-toast ref="uToast" />
		<view class="">
			<view class="label_title">个人信息</view>
			<view class="ul">
				<view class="li">
					<text class="label">昵称</text>
					<view class="text">
						<input type="text" placeholder="" disabled v-model="form.name" />
					</view>
				</view>
				<view class="li">
					<text class="label">区块链号码</text>
					<view class="text">
						<input type="text" placeholder="" disabled v-model="form.address" />
					</view>
				</view>
			</view>
		</view>
		<view class="">
			<view class="label_title">请验证身份</view>
			<view class="ul">
				<view class="li">
					<text class="label">{{isphone?'手机号码':'邮箱号码'}}</text>
					<view class="text">
						<input type="text" placeholder="" disabled v-model="isphone?phone:email" />
					</view>
				</view>
				<view class="li">
					<view class="text space-between">
						<input type="text" placeholder="请输入验证码" v-model="form.code" />
						<text class="active" v-if="show" @click="getCode">获取验证码</text>
						<text v-else>重新发送 ({{ count }}s)</text>
					</view>
				</view>
				<!-- <view class="li">
					<view class="text">
						<input type="password" placeholder="请输入登录密码" v-model="form.password" />
					</view>
				</view> -->
			</view>
		</view>
		<view class="margin_bottom40 button">
			<button-bar @click="submit()">
				下一步
			</button-bar>
			<view class="font">
				无法接受验证码？
				<text @click="isShowContactService = true">联系客服</text>
			</view>
		</view>
		<!-- 弹出框 -->
		<popup-bar v-model="showPopup" @confirm="java_execute()" @cancel="showPopup = false" title="账户注销"
			content="个人账户一旦注销将不可撤销，账户内所有信息和数据将无法找回。请确认是否要注销账号"></popup-bar>

		<!-- 企业微信二维码 -->
		<u-modal v-model="isShowContactService" :show-title="false" :show-confirm-button="false" mask-close-able
			border-radius="35">
			<view class="code">
				<!-- <u-image src=""
             mode="aspectFit" width="600" height="924"></u-image> -->
				<view class="create">
					<image :src="nftcnOptionsVOList.creationImg" style="width:190rpx;height:190rpx;">
					</image>
					<view>创作者服务</view>
				</view>
				<view class="Collection">
					<image :src="nftcnOptionsVOList.collectionImg" style="width:190rpx;height:190rpx;">
					</image>
					<view>
						藏家服务
					</view>
				</view>
				<view class="footer_1">
					<image :src="nftcnOptionsVOList.official_communication_group" style="width:190rpx;height:190rpx;">
					</image>
					<view v-if="nftcnOptionsVOList.official_communication_group">
						官方交流群
					</view>
				</view>
				<view class="footer_2">
					<image :src="nftcnOptionsVOList.businessImg" style="width:190rpx;height:190rpx;">
					</image>
					<view v-if="nftcnOptionsVOList.businessImg">
						商务合作
					</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import PopupBar from "@/components/public/PopupBar";
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		components: {
			PopupBar,
			ButtonBar
		},
		data() {
			return {
				form: {
					name: "",
					address: "",
					phone: "",
					code: "",
				},
				count: 0, //时间
				show: true,
				uid: '',
				email: '',
				showPopup: false,
				isShowContactService: false, //二维码弹出框
				nftcnOptionsVOList: {
					creationImg: "", //创作者
					collectionImg: "", //藏家
					official_communication_group: "", //官方交流
					businessImg: "" //商务合作
				},
				isphone: false,
				phone: ""
			};
		},
		onLoad() {
			this.java_userInfo()
			this.appConfigList()
		},
		methods: {
			getCode() {
				if (this.isphone) {
					this.getCodePhone()
				} else {
					this.getCodeEmail()
				}
			},
			//获取验证码倒计时
			async getCodeEmail() {
				let _this = this
				const TIME_COUNT = 60;
				let res = await this.$api.java_sendAliYunEmail({
					emailType: 'ACCOUNT_CANCELLATION',
					emailAddress: this.email
				})
				if (res.status.code === 0) {
					this.$refs.uToast.show({
						title: '验证码已发送',
						type: 'default',
					})
					if (!this.timer) {
						this.count = TIME_COUNT;
						this.show = false;
						this.timer = setInterval(() => {
							if (this.count > 0 && this.count <= TIME_COUNT) {
								this.count--;
							} else {
								this.show = true;
								clearInterval(this.timer);
								this.timer = null;
							}
						}, 1000)
					}
				} else {
					this.$refs.uToast.show({
						title: res.msg,
						type: 'default',
					})
				}

			},
			async getCodePhone() {
				let _this = this
				const TIME_COUNT = 60;
				let res = await this.$api.java_sendAliYunSms({
					mobPhone: this.phone,
					aliYumSmsType: 'ACCOUNT_CANCELLATION',
				})
				if (res.status.code === 0) {
					this.$refs.uToast.show({
						title: '验证码已发送',
						type: 'default',
					})
					if (!this.timer) {
						this.count = TIME_COUNT;
						this.show = false;
						this.timer = setInterval(() => {
							if (this.count > 0 && this.count <= TIME_COUNT) {
								this.count--;
							} else {
								this.show = true;
								clearInterval(this.timer);
								this.timer = null;
							}
						}, 1000)
					}
				} else {
					this.$refs.uToast.show({
						title: res.msg,
						type: 'default',
					})
				}

			},
			// 提交
			async java_execute() {
				uni.showLoading({})
				const {
					uid,
					form: {
						code
					},
					email,
					isphone,
					phone
				} = this
				let params = {
					id: uid,
					code: code,
				}
				if (isphone) {
					params.mobPhone = phone,
						params.aliYumSmsType = "ACCOUNT_CANCELLATION"
				} else {
					params.emailAddress = email,
						params.emailType = "ACCOUNT_CANCELLATION"
				}
				let res = await this.$api.java_execute(params);
				if (res.status.code === 0) {
					this.showPopup = false
					this.$refs.uToast.show({
						title: '申请注销成功',
						type: 'default',
					})
					let _this = this
					setTimeout(function() {
						_this.$Router.push({
							name: 'mainLogin'
						})
					}, 1500);
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						type: 'default',
					})
					this.showPopup = false
				}
			},
			//个人资料
			async java_userInfo() {
				let res = await this.$api.userInfo({
					userId: ''
				})
				setTimeout(() => {
					console.log(res, 'res')
				}, 1000)
				if (res.status.code === 0) {
					this.form.name = res.result.name
					this.form.address = res.result.contractAddress
					this.form.phone = res.result.phone
					this.uid = res.result.userId
					this.email = res.result.email
					this.phone = res.result.phone
					console.log(this.email)
					if (this.email != "" && this.phone != "") {
						this.isphone = true
						console.log("都有")
					} else if (this.email != "" && this.phone == "") {
						this.isphone = false
						console.log("邮箱")
					} else if (this.phone != "" && this.email == "") {
						this.isphone = true
						console.log("只有手机号")
					}
				}
			},
			async appConfigList() {
				let res = await this.$api.java_getAppConfigList();

				if (res.status.code == 0) {
					this.nftcnOptionsVOList.creationImg = res.result.nftcnOptionsVOList[2].value
					this.nftcnOptionsVOList.businessImg = res.result.nftcnOptionsVOList[0].value
					this.nftcnOptionsVOList.collectionImg = res.result.nftcnOptionsVOList[1].value
					this.nftcnOptionsVOList.official_communication_group = res.result.nftcnOptionsVOList[3].value
					console.log(this.nftcnOptionsVOList)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			// 点击下一步
			submit() {
				if (this.form.code === '' || this.form.code == null) {
					this.$refs.uToast.show({
						title: '请输入验证码',
						type: 'default',
					})
				} else {
					this.showPopup = true
					console.log('开启弹窗')
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.main {
		padding: 40rpx;

		.margin_bottom40 {
			margin-bottom: 40rpx;
		}

		.label_title {
			font-size: 32rpx;
			margin-top: 60rpx;
			font-weight: bold;
			color: var(--main-front-color);
		}

		.msg {
			font-size: 26rpx;
			color: var(--secondary-front-color);
		}

		.ul {
			margin-top: 20rpx;

			.li {
				width: 100%;
				font-size: 26rpx;
				margin-bottom: 60rpx;
				line-height: 40rpx;
				border-bottom: 1px solid var(--form-border-color);

				.text {
					height: 70rpx;
					line-height: 70rpx;
					font-size: 28rpx;
					color: var(--default-color1);
					border-bottom: 1rpx solid var(--default-color3);
					box-sizing: border-box;

					input {
						height: 70rpx;
						line-height: 70rpx;
						color: var(--default-color1);
					}

					input::placeholder {
						font-size: 24rpx !important;
					}
				}

				.active {
					color: var(--default-color1);
					font-size: 28rpx;
				}

				.label {
					font-size: 24rpx;
					font-weight: bold;
					color: var(--default-color3);
				}
			}

			.li:last-child {
				margin-bottom: 0;
			}
		}

		.button {
			margin-top: 100rpx;

			.font {
				text-align: center;
				margin-top: 32rpx;
				font-size: 24rpx;

				text {
					color: var(--active-color);
				}
			}
		}
	}

	.u-form {
		margin-top: 70rpx;
	}

	.error-tip {
		margin-left: 30rpx;
		font-size: 24rpx;
		color: #BB3835;
	}

	.verification-tip {
		margin-left: 30rpx;
		font-size: 28rpx;
		font-weight: 700;
		color: #000;
	}

	.confirm {
		margin-top: 80rpx;
		color: var(--message-box-point-color);
		background-color: #333;
		border: none;

		&:active {
			color: #333;
			background-color: #eee;
		}

		&[disabled] {
			color: var(--message-box-point-color);
			background-color: #999;
		}
	}

	.cannot-received {
		margin-top: 16rpx;
		text-align: center;
		font-size: 24rpx;
		color: #999;
	}

	.success-modal {
		padding: 68rpx 107rpx;

		.sm-title {
			text-align: center;
			font-size: 36rpx;
			font-weight: 700;
		}

		.sm-count-down {
			margin-top: 50rpx;
			text-align: center;
			font-size: 28rpx;
		}
	}

	.code {
		position: relative;
		background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20220516/95e5b61c6dd7ed4c7dccd2b66cdc04a4_1084x1686.png');
		width: 100%;
		height: 910rpx;
		background-size: 100% 100%;

		.create {
			position: absolute;
			top: 240rpx;
			left: 70rpx;

			image {
				margin-bottom: 20rpx;
			}

			view {
				color: var(--message-box-point-color);
				text-align: center;
				font-size: 28rpx;
			}
		}

		.Collection {
			position: absolute;
			top: 240rpx;
			right: 70rpx;

			image {
				margin-bottom: 20rpx;
			}

			view {
				color: var(--message-box-point-color);
				text-align: center;
				font-size: 28rpx;
			}
		}

		.footer_1 {
			position: absolute;
			top: 502rpx;
			left: 70rpx;

			image {
				margin-bottom: 20rpx;
			}

			view {
				color: var(--message-box-point-color);
				text-align: center;
				font-size: 28rpx;
			}
		}

		.footer_2 {
			position: absolute;
			top: 502rpx;
			right: 70rpx;

			image {
				margin-bottom: 20rpx;
			}

			view {
				color: var(--message-box-point-color);
				text-align: center;
				font-size: 28rpx;
			}
		}

		.u-btn {
			background-image: var(--primary-button-color);
			border-radius: 0;
			border: node;
			color: var(--main-bg-color);
			font-size: 28rpx;
			font-weight: bold;

		}

		.u-hairline-border::after {
			border: none;
		}
	}
</style>