<template>
	<view>
		<view class="withdraw_box">
			<view class="method">
				<text>提现至</text>
				<view class="withdraw_ul">
					<view class="item">
						<view class="img">
							<image src="../../../static/imgs/public/zfb_icon.png" mode="widthFix"></image>
						</view>
						<text>支付宝账号</text>
						<!-- <u-radio-group style="margin-left:30rpx;" v-model="payMethod" active-color="#333" width="28"
							size="28">
							<u-radio name="zfb"></u-radio>
						</u-radio-group> -->
					</view>
					<view class="item">
						<!-- <view class="img">
							<image src="../../../static/imgs/public/weixin_icon.png" mode="widthFix"></image>
						</view>
						<text>微信</text> -->
						<!-- 	<u-radio-group style="margin-left:30rpx;" disabled v-model="payMethod" active-color="#333" width="28"
							size="28">
							<u-radio name="vxPay"></u-radio>
						</u-radio-group> -->
					</view>
					<view class="item">
						<!-- 	<view class="img">
							<image src="../../../static/imgs/public/bank_icon.png" mode="widthFix"></image>
						</view>
						<text>银行卡</text> -->
						<!-- <u-radio-group style="margin-left:30rpx;" disabled v-model="payMethod" active-color="#333" width="28"
							size="28">
							<u-radio name="yhkPay"></u-radio>
						</u-radio-group> -->
					</view>
				</view>
			</view>
			<u-form-item label="真实姓名" style="padding:0rpx;margin-top:20rpx;" label-width="160" :border-bottom="false"
				label-position="top">
				<u-input style="background-color:var(--message-box-point-color);border:none;" disabled placeholder-style="dStyle"
					v-model="name" border placeholder="请输入真实姓名" />
			</u-form-item>
			<u-form-item label="支付宝账号" style="padding:0rpx;" label-width="160" :border-bottom="false"
				label-position="top">
				<u-input style="background-color:var(--message-box-point-color);border:none;" placeholder-style="dStyle" v-model="alipay"
					border placeholder="请输入支付宝账号/手机号" />
			</u-form-item>
			<view class="withdraw_money">
				<view class="title">
					提现金额
				</view>
				<view class="money ">
					<text>￥</text>
					<u-input type="number" style="border:none;font-weight:700" :maxlength="9" :focus="true"
						:custom-style="inputStyle" v-model="money" border placeholder="" />
				</view>
				<view class="msg red" v-if="type">
					输入金额超过可用余额
				</view>
				<view class="msg" v-else>
					可提现金额为{{balanceNum}}元
				</view>
			</view>
			<!-- 	<view class="withdraw_msg">
				金额2000以上，免提现手续费，金额2000以下手续费5元
			</view> -->
			<view class="submit " :class="{'active':type==false&&alipay!=''&&name!=''&&money!=''}" @click="submit()">
				提现
			</view>
			<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false">
				<view class="BankVerifyBody">
					<view class="head_title_y">
						<view class="right" @click="isPassword=false">
							<image src="../../../static/imgs/public/tuichu.png" mode="widthFix"></image>
						</view>
						请先设置支付密码
					</view>
					<view class="msg_y">
						非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
					</view>
					<view class="footer_y" @click="isPassword=false">
						<button>
							取消
						</button>
						<button class="active" @click="SetPayPassword()">
							去设置
						</button>
					</view>
				</view>
			</u-modal>
			<u-modal class="" v-model="isMailboxVerify" width="80%" :show-title="false" :show-confirm-button="false">
				<view class="BankVerifyBody">
					<view class="head_title">
						<view class="right" @click="isBankVerifyColse(1)">
							<image src="../../../static/imgs/public/tuichu.png" mode="widthFix"></image>
						</view>
						支付验证
					</view>
					<view class="item">
						<view class="labal">
							邮箱验证码
						</view>
						<view class="flex_between_x input">
							<view class="left">
								{{this.email}}
							</view>
							<!-- <view class="right" @click="isShow=true">
								<image src="../../../static/imgs/public/why.png" mode="widthFix"></image>
							</view> -->
						</view>
					</view>
					<view class="item">
						<view class="labal">
							验证码
						</view>
						<view class="flex_between_x input">
							<view class="left">
								<input type="number" v-model="MailboxVerifyCode" maxlength="6" placeholder="请输入验证码" />
							</view>
							<view class="right">
								<text class="yzm" v-show="showVerify==true" @click="sendMailboxVerify()">获取验证码</text>
								<text class="yzm" v-show="showVerify==false">{{count}}秒后可重试</text>
							</view>
						</view>
					</view>
					<view class="footer">
						<u-button :throttle-time="1000" :hair-line="false" hover-class="none" shape="square"
							@click="mailboxVerifySubmit()">
							支付
						</u-button>
					</view>
				</view>
			</u-modal>
			<u-popup v-model="isSetPayPassword" mode="bottom" border-radius="40" height="50%">
				<view class="modal_head_password">
					<view class="left">
						<image src="@/static/imgs/public/tuichu.png" mode="widthFix"></image>
					</view>
					设置支付密码
				</view>
				<view class="modal_Body_password">
					<view class="bank_ul">
						<view class="pwd-box">
							<view class="set-pwd">
								<view class="set-pwd-tip"><text>{{title}}</text>
								</view>
								<view class="set-error">{{error}}</view>
							</view>
							<view class="u-flex u-row-center">
								<u-message-input mode="box" :focus="true" :maxlength="6" :dot-fill="true"
									v-model="password" :disabled-keyboard="false" @change="change" @finish="finish">
								</u-message-input>
							</view>
						</view>
					</view>
				</view>
			</u-popup>
			<u-popup v-model="isPasswordImport" mode="bottom" border-radius="40" height="900">
				<view class="modal_head_password">
					<view class="left" @click="isPasswordImport=false">
						<image src="@/static/imgs/public/tuichu.png" mode="widthFix"></image>
					</view>
					请输入支付密码
					<view class="right" @click="nav_forgetPayPassword()">
						忘记密码？
					</view>
				</view>
				<view class="modal_Body_password">
					<view class="bank_ul">
						<view class="pwd-box">
							<view class="set-pwd">
								<view class="set-pwd-tip"><text></text>
								</view>
							</view>
							<view class="u-flex u-row-center">
								<u-message-input mode="box" :focus="false" :maxlength="6" :dot-fill="true"
									v-model="passwordImportPay" :disabled-keyboard="false" @change="change"
									@finish="finishPay">
								</u-message-input>
							</view>
						</view>
					</view>
				</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				payMethod: 'zfb',
				money: "",
				name: "",
				alipay: "",
				balanceNum: 0.00,
				type: false,
				inputStyle: {
					"font-size": "50rpx",
					"background-color": "var(--message-box-point-color)",
					"margin": "20rpx 0rpx",
				},
				dStyle: {
					"font-weight": "500",
					'color': 'var(--secondary-front-color)'
				},

				isPassword: false,
				isSetTradePassword: 0, //是否设置过支付密码
				isSetPayPassword: false,
				isPasswordImport: false,
				title: "请设置余额支付密码，用于支付验证。",
				password: '',
				psw: '',
				error: '',
				passwordImportPay: "",
				isMailboxVerify: false,
				count: 0,
				email: "", //邮箱
				showVerify: false,
				MailboxVerifyCode: "",
			};
		},
		onLoad() {
			this.getMoney()
			this.isSetTradePassword = uni.getStorageSync("isSetTradePassword")
			this.email = uni.getStorageSync("email")
		},
		watch: {
			'money': function(newVal) {
				if (Number(newVal) > this.balanceNum) {
					this.type = true
					console.log("大于金额")
				} else {
					this.type = false
				}
			},

		},
		methods: {
			async getMoney() {
				let res = await this.$api.java_withdraw_info();
				if (res.status.code == 0) {
					this.balanceNum = res.result.amount
					this.name = res.result.name
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
				console.log(res)
			},
			async submit() {
				if (this.type == false && this.alipay != '' && this.name != '' && this.money != '') {
					if (this.isSetTradePassword == 0) {
						//设置交易密码
						this.isPassword = true
					} else {
						this.isPasswordImport = true
						//输入交易密码
						console.log("输入密码支付")
					}
				}

			},
			async withdrawSubmit(password) {
				uni.showLoading({
					title: '提现中'
				});
				let res = await this.$api.java_withdraw_create({
					accountType: 3,
					amount: this.money,
					accountNo:this.alipay,
					paymentScene: 1,
					tradePassword: password
				});
				if (res.status.code == 0) {
					console.log(2222)
					uni.setStorageSync("alipay",this.alipay)
					uni.setStorageSync("moneyWithdraw", this.money)
					uni.setStorageSync("name", "支付宝账号")
					this.$Router.push({
						name: "withdrawSuccess"
					})
					uni.hideLoading();
				} else {
					uni.hideLoading();
					this.passwordImportPay=""
					this.password=""
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			change(e) {
				// console.log(e)
			},
			finish(e) {
				this.password = e
				this.openSetPassword()
			},
			openSetPassword() {
				let _this = this
				uni.showLoading({
					title: '设置中'
				})
				if (this.psw == "") {
					setTimeout(() => {
						this.psw = this.password
						this.title = "请再次输入余额支付密码，用于二次确认密码"
					}, 1000)
					setTimeout(() => {
						this.title = "请再次输入余额支付密码，用于二次确认密码"
					}, 2000)
				} else {
					this.error = ""
					if (this.psw == this.password) {
						setTimeout(() => {
							_this.isSetPayPassword = false
							this.setPassword(this.psw)
						}, 2000);
					} else {
						setTimeout(() => {
							this.error = "二次密码不一致，请重新设置"
							setTimeout(() => {
								this.error = ""
							}, 2000);
							this.psw = ""
							this.title = "请设置余额支付密码，用于支付验证。",
								this.password = ""
						}, 2000);
					}
				}
				setTimeout(() => {
					uni.hideLoading();
					this.password = ""
				}, 2000);
			},
			async setPassword(password) { //设置支付密码
				let res = await this.$api.tradePassCreate({
					captcha: this.MailboxVerifyCode,
					password: password,
				});
				if (res.status == 200) {
					console.log(res)
					uni.setStorageSync("isSetTradePassword", 1)
					this.isSetTradePassword = 1
					// uni.setStorageSync("isSetTradePassword","1")
					console.log("刚设置的密码为：" + password)
					this.withdrawSubmit(password);
				} else {
					this.psw = ""
					this.title = "请设置余额支付密码，用于支付验证。",
						this.password = ""
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			finishPay(e) {
				this.passwordImportPay = e
				console.log(this.passwordImportPay)
				this.withdrawSubmit(e);
			},
			SetPayPassword() {
				this.isPassword = false
				this.isMailboxVerify = true
				this.MailboxVerifyCode = ""
				this.count = 0
				this.getCode()
				this.sendMailboxVerify()
			},
			getCode() {
				const TIME_COUNT = 60;
				if (!this.timer) {
					this.count = TIME_COUNT;
					this.show = false;
					this.timer = setInterval(() => {
						if (this.count > 0 && this.count <= TIME_COUNT) {
							this.count--;
						} else {
							this.show = true;
							clearInterval(this.timer);
							this.timer = null;
						}
					}, 1000)
				}
			},
			mailboxVerifySubmit() { //邮箱验证码提交
				if (this.MailboxVerifyCode == "") {
					uni.showToast({
						title: "请输入邮箱验证码哦",
						icon: "none",
						duration: 3000
					});
				} else {
					this.isMailboxVerify = false
					this.isSetPayPassword = true
				}
			},
			async sendMailboxVerify() {
				let res = await this.$api.sendEmailCaptcha({
					type: 1
				});
				if (res.status == 200) {
					const TIME_COUNT = 60;
					if (!this.timer) {
						this.count = TIME_COUNT;
						this.show = false;
						this.timer = setInterval(() => {
							if (this.count > 0 && this.count <= TIME_COUNT) {
								this.count--;
							} else {
								this.show = true;
								clearInterval(this.timer);
								this.timer = null;
							}
						}, 1000)
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					});

				}
			},
			isBankVerifyColse(index) {
				if (index == 0) {
					this.isBankVerify = false
					this.count = 0
					this.getCode()
				} else {
					this.isMailboxVerify = false
					this.count = 0
					this.getCode()
				}
			},
			nav_forgetPayPassword() {
				this.$Router.push({
					name: 'forgetPayPassword'
				});
			},

		}

	}
</script>

<style lang="scss">
	page {
		background-color: #F3F3F3;
	}

	.withdraw_box {
		padding: 40rpx;

		.method {
			.withdraw_ul {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-top: 30rpx;

				.item {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					width: 30%;
					font-size: 26rpx;
					color: #333333;

					.img {
						margin-right: 10rpx;

						image {
							width: 40rpx;

						}
					}
				}
			}
		}

		.withdraw_money {
			background-color: var(--message-box-point-color);
			border-radius: 20rpx;
			width: 100%;
			margin-top: 40rpx;

			.title {
				padding: 20rpx;
			}

			.money {
				padding: 20rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-bottom: 1rpx solid #CCCCCC;

				text {
					font-size: 40rpx;
					font-weight: 700;
				}
			}

			.msg {
				padding: 20rpx;
				color: var(--secondary-front-color);
				font-size: 26rpx;

				&.red {
					color: #BB3835
				}
			}
		}

		.withdraw_msg {
			font-size: 24rpx;
			color: #BB3835;
			margin: 30rpx;
			text-align: center;
		}

		.submit {
			width: 100%;
			height: 80rpx;
			background-color: var(--secondary-front-color);
			color: var(--message-box-point-color);
			text-align: center;
			line-height: 80rpx;
			border-radius: 40rpx;
			font-size: 30rpx;
			margin-top: 60rpx;

			&.active {
				background-color: #333333;
			}
		}
	}

	.BankVerifyBody {
		padding: 42rpx;

		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;

			.right {
				position: absolute;
				right: 40rpx;
				top: 66rpx;

				image {
					width: 30rpx;
				}
			}
		}

		.item {
			margin-bottom: 46rpx;

			.labal {
				color: #666666;
				font-size: 24rpx;
			}

			.input {
				align-items: center;
				height: 88rpx;
				border-bottom: 1rpx solid #EEEEEE;

				.left {
					width: 50%;
					color: #666666;
					padding: 6rpx;
					border-radius: 4rpx;

					.input {
						font-size: 24rpx;
					}
				}

				.right {
					image {
						width: 40rpx;
					}

					text {
						color: #027AFF;
						font-size: 28rpx;
					}
				}
			}
		}

		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: var(--message-box-point-color);
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}

	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;

		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;

			image {
				width: 30rpx;
			}
		}
	}

	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		button {
			margin: 0rpx;
			padding: 0rpx;
			width: 238rpx;
			height: 88rpx;
			line-height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			background-color: var(--secondary-front-color);
			color: var(--message-box-point-color);

			&.active {
				background-color: #333333;
				color: var(--message-box-point-color);
			}
		}
	}

	.msg_y {
		font-size: 28rpx;
		color: var(--secondary-front-color);
		line-height: 40rpx;
	}

	.pwd-box {
		margin-top: 50rpx;

		.set-pwd {
			position: relative;

			.set-pwd-tip {
				font-size: 32rpx;
				color: #333;
				padding-bottom: 64rpx;
				text-align: center;
			}

			.set-error {
				color: #BB3835;
				font-size: 24rpx;
				text-align: center;
				position: absolute;
				// left: 290rpx;
				width: 100%;
				bottom: 20rpx;
			}
		}
	}

	.modal_head_password {
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 42rpx;
		position: relative;

		.left {
			position: absolute;
			left: 40rpx;
			top: 20rpx;

			image {
				width: 30rpx;
			}
		}

		.right {
			position: absolute;
			right: 40rpx;
			top: 0rpx;
			font-weight: 500;
			font-size: 24rpx;
		}
	}

	.modal_Body_password {
		padding: 24rpx;

	}
</style>
