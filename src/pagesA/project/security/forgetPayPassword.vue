<template>
	<view class="main">
		<u-navbar back-icon-color="#000" title="忘记支付密码" title-color="#000" title-bold></u-navbar>
		<!-- 顶部提示 -->
		<u-top-tips ref="warningTips"></u-top-tips>
		<!-- toast提示 -->
		<u-toast ref="uToast" />
		
		<u-form :model="form" ref="uForm" :border-bottom="false">
			<u-form-item>
				<u-input v-model="form.email" :disabled="true" placeholder="请输入邮箱号" placeholder-style="color: #999" />
			</u-form-item>
			
			<u-form-item>
				<u-input v-model="form.verification" type="number" maxlength="6" placeholder="请输入验证码" placeholder-style="color: #999" />
				<text class="verification-tip" v-if="is.isShowVerificationTip1" @click="obtainVerification">获取验证码</text>
				<text class="verification-tip" v-if="is.isShowVerificationTip2">{{timeout}}s后重新获取</text>
			</u-form-item>
			
			<u-form-item>
				<u-input v-model="form.newPwd" type="password" maxlength="6" placeholder="请输入新密码" placeholder-style="color: #999" />
			</u-form-item>
			
			<u-form-item>
				<u-input v-model="repeatPwd" type="password" maxlength="6" placeholder="请确认新密码" placeholder-style="color: #999" />
				<text class="error-tip" v-if="is.isShowRepeatPwdTip">两次密码不一致</text>
			</u-form-item>
			
			<u-button class="confirm" hover-class="none" :hair-line="false" shape="circle" @click="checkFrom">确认</u-button>
			
			<!-- <view class="cannot-received">收不到验证码?请发邮件至官方邮箱<text style="color: #333;"><EMAIL></text></view> -->
		</u-form>
	</view>
</template>

<script>
export default {
	data() {
		return {
			form: {
				email: '',
				verification: '',
				newPwd: ''
			},
			repeatPwd: '',
			is: {
				isShowVerificationTip1: true,
				isShowVerificationTip2: false
			},
			timeout: 60
		};
	},
	onLoad() {
		this.form.email=uni.getStorageSync("email")
	},
	methods: {
		// 获取验证码
		obtainVerification() {
			this.is.isShowVerificationTip1 = false;
			this.is.isShowVerificationTip2 = true;
			this.countDown();
			if(this.form.email != '' && this.form.email != null) {
				this.sendCode();
			} else {
				this.$refs.warningTips.show({
					title: '邮箱号不能为空！',
					type: 'error',
					duration: '2300'
				});
				return;
			}
		},
		// 倒计时跳转
		countDown() {
			let time = this.timeout;
			let interval = setInterval(() => {
				this.timeout--;
				if(this.timeout === 0) {
					clearInterval(interval);
					this.timeout = time;
					this.is.isShowVerificationTip2 = false;
					this.is.isShowVerificationTip1 = true;
				}
			}, 1000);
		},
		// 发送验证码
		async sendCode() {
			let res = await this.$api.sendEmailCaptcha({
				 type: "2"
			});
		},
		// 判断空值
		checkFrom() {
			if(this.form.email == '' || this.form.email == null) {
				this.$refs.warningTips.show({
					title: '邮箱号不能为空！',
					type: 'error',
					duration: '2300'
				});
			} else if(this.form.verification == '' || this.form.verification == null) {
				this.$refs.warningTips.show({
					title: '验证码不能为空！',
					type: 'error',
					duration: '2300'
				});
			} else if(this.form.newPwd == '' || this.form.newPwd == null) {
				this.$refs.warningTips.show({
					title: '新密码不能为空！',
					type: 'error',
					duration: '2300'
				});
			} else if(this.form.newPwd.length <6) {
				this.$refs.warningTips.show({
					title: '设置的密码不得小于6位！',
					type: 'error',
					duration: '2300'
				});
			} else if(this.form.newPwd == '' || this.form.newPwd == null) {
				this.$refs.warningTips.show({
					title: '重复输入的密码不能为空！',
					type: 'error',
					duration: '2300'
				});
			} else if(this.form.newPwd != this.repeatPwd) {
				this.$refs.warningTips.show({
					title: '两次输入的密码不一致！',
					type: 'error',
					duration: '2300'
				});
			} else {
				this.verifyCode()
			}
			
		},
		// 写入新密码
		async verifyCode() {
			let _this=this
			let res = await this.$api.tradePassReset({
				captcha: this.form.verification,
				password: this.form.newPwd
			});
			if(res.status == 200){
				this.$refs.uToast.show({
					title: '支付密码重置成功！',
					type: 'success',
				})
				setTimeout(() => {
					_this.$Router.back()
				}, 2000)
			} else {
				this.$refs.uToast.show({
					title: res.msg,
					type: 'error',
				})
			}
		}
	}
}
</script>

<style lang="scss">
.space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.main {
	padding: 0 76rpx;
}

.u-form {
	margin-top: 70rpx;
}

.error-tip {
	margin-left: 30rpx;
	font-size: 24rpx;
	color: #BB3835;
}

.verification-tip {
	margin-left: 30rpx;
	font-size: 28rpx;
	font-weight: 700;
	color: #000;
}

.confirm {
	margin-top: 80rpx;
	color: var(--message-box-point-color);
	background-color: #333;
	border: none;
	
	&:active {
		color: #333;
		background-color: #eee;
	}
	
	&[disabled] {
		color: var(--message-box-point-color);
		background-color: #999;
	}
}

.cannot-received {
	margin-top: 16rpx;
	text-align: center;
	font-size: 24rpx;
	color: #999;
}

.success-modal {
	padding: 68rpx 107rpx;
	
	.sm-title {
		text-align: center;
		font-size: 36rpx;
		font-weight: 700;
	}
	
	.sm-count-down {
		margin-top: 50rpx;
		text-align: center;
		font-size: 28rpx;
	}
}
</style>
