<template>
    <view class="main">
        <u-navbar back-icon-color="#F9F9F9" :border-bottom="false"
                  :background="{backgroundColor: '#35333E'}"
                  title="账号注销进度" title-color="var(--default-color1)" title-bold :is-back="false">
        </u-navbar>
        <!-- 顶部提示 -->
        <u-top-tips ref="warningTips"></u-top-tips>
        <!-- toast提示 -->
        <u-toast ref="uToast"/>
        <view class="timeLine">
            <view class="first">
                <view class="icon">
                    <!-- <img src="https://cdn-lingjing.nftcn.com.cn/image/********/8cf61bc320719d4b9013aae60b83e500_20x20.png" alt=""> -->
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/********/8cf61bc320719d4b9013aae60b83e500_20x20.png"
                        mode=""></image>
                </view>
                <view class="text">
                    提交注销申请：{{ examine.commitTime }}
                </view>
            </view>
            <view class="division">

            </view>
            <view class="second">
                <view class="icon">
                    <!-- <img src="https://cdn-lingjing.nftcn.com.cn/image/********/8cf61bc320719d4b9013aae60b83e500_20x20.png" alt=""> -->
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/********/8cf61bc320719d4b9013aae60b83e500_20x20.png"
                        mode=""></image>
                </view>
                <view class="text">
                    注销审核中
                </view>
            </view>
            <view class="divisionbox">
                <view class="division" style="background-color: #616161;"
                      v-if="examine.checkStatus == 'ACCOUNT_CANCEL_CHECKING'">

                </view>
                <view class="division" v-else>

                </view>
                <view class="text">
                    预计审核时间：3个工作日
                </view>
            </view>

            <view class="third">
                <view class="icon">
                    <image v-if="examine.checkStatus == 'ACCOUNT_CANCEL_CHECKING'"
                           src="https://cdn-lingjing.nftcn.com.cn/image/********/e8542a6864f1913c0804ae44ce706e35_20x20.png"
                           mode=""></image>
                    <image v-else
                           src="https://cdn-lingjing.nftcn.com.cn/image/********/8cf61bc320719d4b9013aae60b83e500_20x20.png"
                           mode=""></image>
                </view>
                <view class="text" v-if="examine.checkStatus == 'ACCOUNT_CANCEL_CHECKING'">
                    注销成功
                </view>
                <view class="text" v-else>
                    注销失败
                </view>
            </view>
            <view class="end"
                  v-if="examine.checkStatus == 'ACCOUNT_CANCEL_CHECKING' || examine.checkStatus == 'ACCOUNT_CANCEL_SUCCESS' ">
                注销成功后，您将自动退出账号登录，若您想继续使用账号，需重新注册。
            </view>
            <view class="end" v-if="examine.checkStatus == 'ACCOUNT_CANCEL_FAIL'">
                <view class="reason">
                    <text>审核失败</text>
                    {{ examine.rejectReason }}
                </view>
                <view class="Tips">
                    您可依据以上失败原因重新提交注销申请或咨询客服。
                </view>
            </view>
        </view>
        <view class="margin_bottom40">
            <view class="title">账号注销为什么要审核：</view>
            <view class="ul">
                <view class="li" v-for="(item,index) in liList" :key="index">
                    <view>
                        {{ item }}
                    </view>
                </view>
            </view>
        </view>
        <view class="margin_bottom40 button">
            <u-button class="btn1" @click="nav_accountLogoutApplication()">
                退出登录
            </u-button>
            <u-button class="btn2" v-if="examine.checkStatus == 'ACCOUNT_CANCEL_CHECKING'" @click="showPopup = true">
                中止注销
            </u-button>
            <u-button class="btn3" v-else @click="nav_mall()">
                回到首页
            </u-button>
        </view>
        <!-- 弹出框 -->
        <!-- 弹出框 -->
        <popup-bar v-model="showPopup" @confirm="java_stop()" @cancel="showPopup = false"
                   title="终止注销" content="请确认是否要终止注销账号"></popup-bar>
    </view>
</template>

<script>
import popupBar from "@/components/public/PopupBar";
export default {
    components: {
        popupBar
    },
    data() {
        return {
            liList: [
                "1、账号申请注销后，将3个工作日内由后台运营进行审核，并反馈审核结果。",
                "2、注销审核会参考您过往交易记录、平台信用、有无纠纷等信息进行综合考量。",
                "3、若审核失败，则会中止注销，同时告知您相应的失败原因，可再次申请或联系客服人工咨询。",
                "4、在账号注销审核期间，为了避免产生交易等数据，系统会暂时冻结账号使用，您可在审核期间手动中止注销，恢复账号使用。",
                "5、账号注销成功后，您将自动退出账号登录，若想继续使用本平台可重新注册。"
            ],
            examine: {},
            showPopup: false,
        };
    },
    onLoad() {
        this.java_detail()
    },
    methods: {
        //退出登录
        nav_accountLogoutApplication() {
            // this.java_verifyInfo()
            uni.removeStorageSync("token")
            uni.removeStorageSync("uid")
            uni.removeStorageSync("contract_address")
            this.$Router.push({
                name: 'login'
            })
        },
        nav_mall() {
            console.log(11)
            uni.removeStorageSync("token")
            uni.removeStorageSync("uid")
            uni.removeStorageSync("contract_address")
            this.$Router.pushTab({
                name: 'index'
            })
        },
        // 注销进度
        async java_detail() {
            let res = await this.$api.java_detail({
                recordId: 0
            });

            if (res.status.code == 0) {
                console.log(res)
                this.examine = res.result
            } else {
                this.$refs.uToast.show({
                    title: res.status.msg,
                })
            }
        },
        async java_stop() {
            let res = await this.$api.java_stop({
                recordId: this.examine.recordId,
                checkStatus: this.examine.checkStatus
            })
            if (res.status.code == 0) {
                this.showPopup = false
                this.$refs.uToast.show({
                    title: '终止注销成功',
                    type: 'default',
                })
                let _this = this
                setTimeout(function () {
                    _this.$Router.pushTab({
                        name: 'personal'
                    })
                }, 1500);
            } else {
                this.$refs.uToast.show({
                    title: res.status.msg,
                    type: 'default',
                })
                this.showPopup = false
            }
        },
        // 判断空值
        checkFrom() {

        },
        nav_link(title) {
            this.$Router.push({
                name: "generalAgreement",
                params: {
                    title: title,
                    link: "https://www.nftcn.com.cn/link/#/pages/index/logout"
                }
            })
        },
        back() {
            this.$Router.back()
        }
    }
}
</script>

<style lang="scss" scoped>
	page{
		color:#999;
	}
.space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main {
    padding: 40rpx;
    .margin_bottom40{
        margin-bottom:40rpx;
    }
    .title {
        font-size: 32rpx;
        font-weight: 600;
        margin: 40rpx 0rpx;
        color: #F9F9F9;
    }

    .msg {
        font-size: 26rpx;
        color: #999999;
    }

    .ul {
        margin-top: 20rpx;

        .li {
            width: 100%;
            display: flex;
            justify-content: flex-start;
            font-size: 26rpx;
            margin-bottom: 20rpx;
            line-height: 40rpx;
            color: #999999;
        }
    }
    .button{
        margin-top: 122rpx;
        display: flex;
        justify-content: space-between;
        .btn1 {
            border-radius: 0;
            width: 324rpx;
            height: 80rpx;
            border: 2rpx solid #1FEDF0;
            color: #1FEDF0;
            background: transparent;
        }
        .btn2 {
            width: 324rpx;
            height: 80rpx;
            border-radius: 0;
            background-image: linear-gradient(132deg, #F6AAF2 0%, #8CC9F3 51%, #00FBEF 100%);
            border: node;
            color: #121212;
            font-size: 28rpx;
            font-weight: bold;
            margin-bottom: 32rpx;
            border: none;
        }
        .u-hairline-border::after {
            border: none;
        }
    }
}
//步进条
.timeLine{
    margin-top: 30rpx;
    padding-bottom: 60rpx;
    border-bottom: 2rpx solid rgba(255, 255, 255, 0.5);
    .first{
        display: flex;
        align-items: center;
        .icon{
            margin-right: 28rpx;
            width: 20rpx;
            height: 20rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .text{
            font-size: 28rpx;
            font-weight: 600;
            color:#F9F9F9;
            line-height: 28rpx;
        }
    }
    .second{
        margin: 8rpx 0;
        display: flex;
        align-items: center;
        .icon{
            margin-right: 28rpx;
            width: 20rpx;
            height: 20rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .text{
            font-size: 28rpx;
            font-weight: 600;
            color:#F9F9F9;
            line-height: 28rpx;
        }
    }
    .third{
        margin: 8rpx 0 18rpx;
        display: flex;
        align-items: center;
        .icon{
            margin-right: 28rpx;
            width: 20rpx;
            height: 20rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .text{
            font-size: 28rpx;
            font-weight: 600;
            color:#616161;
            line-height: 28rpx;
        }
    }
    .end{
        margin-top: 18rpx;
        margin-left: 48rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #616161;
        line-height: 36rpx;
        .reason{
            text{
                width: 100rpx;
                height: 34rpx;
                border: 2rpx solid #1FEDF0;
                padding: 2rpx 4rpx;
                color:  #1FEDF0;
            }
        }
        .Tips{
            margin-top: 24rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: #1FEDF0;
            line-height: 24rpx;
        }
    }
    .division{
        margin-left: 8rpx;
        height: 100rpx;
        width: 4rpx;
        background-color: #1FEDF0;
    }
    .divisionbox{
        display: flex;
        .division{
            height: 100rpx;
            width: 4rpx;
            background-color: #1FEDF0;
        }
        .text{
            margin-left: 36rpx;
            margin-top: 20rpx;
            font-size: 24rpx;
            color: #999999;
            line-height: 24rpx;
        }
    }
}
.Purchase_model {
    .Purchase_body {
        padding: 40rpx;
        background-color: #1E1E1E;
        .title {
            font-size: 32rpx;
            line-height: 60rpx;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40rpx;
            color: #F9F9F9;
        }
        .text{
            font-size: 28rpx;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 33rpx;
            margin-bottom: 60rpx;
            text-align: center;
        }
        .btn{
            .btn1 {
                border-radius: 0;
                background-image: linear-gradient(132deg, #F6AAF2 0%, #8CC9F3 51%, #00FBEF 100%);
                border: node;
                color: #121212;
                font-size: 28rpx;
                font-weight: bold;
                margin-bottom: 32rpx;
                border: none;
            }
            .btn2{
                border-radius: 0;
                width: 510rpx;
                height: 80rpx;
                background: #1E1E1E;
                font-size: 28rpx;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #F9F9F9;
                border: #616161 solid 2rpx;
            }
        }
    }

}
</style>
