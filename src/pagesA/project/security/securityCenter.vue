<template>
	<view class="main">
		<u-toast ref="uToast" />
		<view class="select space-between" @click="toModifyPwd">
			<text>重置密码</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<view class="select space-between" @click="nav_payManage()">
			<text>支付管理</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<view class="select space-between" @click="nav_accountLogout()">
			<text>账号注销</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		};
	},
	methods: {
		toModifyPwd() {
			this.$Router.push({name: 'findPwd', params: {isShowCurrentPwd: 'true'}});
		},
		nav_payManage() {
			this.$Router.push({name: 'payManage'});
		},
		nav_accountLogout(){
			this.$Router.push({name: 'accountLogout'});
		}
	}
}
</script>

<style lang="scss">
.space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

page {
	background-color: #f3f3f3;
}

.main {
	padding-top: 4rpx;
}

.select {
	height: 88rpx;
	margin-top: 16rpx;
	padding: 0 42rpx;
	background-color: var(--message-box-point-color);
	
	.u-icon {
		color: #999;
	}
}
</style>
