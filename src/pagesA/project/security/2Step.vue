<template>
    <view class="page">
        <u-navbar :border-bottom="false" title="Two-Step Verification">
        </u-navbar>

        <view class="authenticator-box">
            <view class="card" @click="nav_to('2FA')">
                <view class="card-left">
                    <image class="logo" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1369721691705008128.png" />
                    <view class="info">
                        <view class="title">Google Authenticator</view>
                        <view class="desc">
                            Once enabled, Google Authenticator will automatically generate a verification code
                            during login or financial transactions.
                        </view>
                    </view>
                </view>
                <image class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1369713703967612928.png"
                    mode="aspectFit" />
            </view>

            <view class="text-tip">
                Two-Step Verification can protect your account security. We recommend enabling Google
                Authenticator as your two-step verification method. Once enabled, you will need to enter a
                dynamic verification code when logging in or performing financial transactions to prevent
                unauthorized access to your account.
            </view>
        </view>

        <view class="btn">
            <!-- <text class="bom">processingFee:1,    networkFee:2</text> -->
            <u-button hover-class="none" class="exchange-btn " @click="nav_to('2FA')">Continue</u-button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {

        }
    },
    methods: {
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.page {
    width: 100%;
    margin: 0 auto;
}

.btn {
    position: fixed;
    bottom: 60rpx;
    // width: 90%;
    // width: 340*2rpx;
    display: flex;
    justify-content: center;
    width: 100%;

    text {
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;
        color: #000;
    }

    .exchange-btn {
        width: 340*2rpx;
        // margin: 0 32rpx;
        height: 100rpx;
        background: #FF82A3;
        border-radius: 64*2rpx;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        color: #fff;
    }

    .exchange-btn[disabled] {
        // background: #FF82A380; // 加透明度效果
        background: #D9D6D6;
        color: #666666;
    }
}

.authenticator-box {
    padding: 30rpx;
    margin-top: 35rpx;

    .card {
        border-radius: 24rpx;
        // background-color: #fff;
        background: rgba(223, 223, 223, .1);
        padding: 24rpx 28rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        border: 2rpx solid rgba(0, 0, 0, .1);

        .card-left {
            display: flex;
            // flex-direction: row;
            align-items: center;
            // flex: 1;

            .logo {
                width: 88rpx;
                height: 88rpx;
                margin-right: 24rpx;
                border-radius: 16rpx;
            }

            .info {
                display: flex;
                flex-direction: column;
                // width: 500rpx;
                width: fit-content;

                .title {
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 34rpx;
                    color: #000;
                    margin-bottom: 6rpx;
                }

                .desc {
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 24rpx;
                    line-height: 34rpx;
                    color: #8F8F8F;
                }
            }
        }

        .arrow {
            width: 24rpx;
            height: 24rpx;
            margin-left: 20rpx;
        }
    }

    .text-tip {
        font-family: Gilroy;
        font-weight: 500;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #8F8F8F;
        margin-top: 34rpx;
    }
}
</style>