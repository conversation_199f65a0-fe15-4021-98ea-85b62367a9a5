<template>
	<view class="main">
		<u-toast ref="uToast" />
		<view class="select space-between" @click="clickItem('set')" v-if="isPasswrod==0">
			<text>设置支付密码</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<view class="select space-between" @click="clickItem('reset')" v-if="isPasswrod==1">
			<text>修改余额支付密码</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<view class="select space-between" @click="clickItem('reset')" v-if="isPasswrod==1">
			<text>忘记支付密码</text>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<pay-popup email="" :popup-show.sync="popupShow" :mode="mode" @resetSuccess="popupShow = false"
			@createSuccess="popupShow = false"></pay-popup>
	</view>
</template>

<script>
	import payPopup from "@/components/payPopup";
	export default {
		components: {
			payPopup
		},
		data() {
			return {
				isPasswrod: 0,
				popupShow: false,
				orderNo: '',
				mode: '',
			};
		},
		onLoad() {
			this.isPasswrod = uni.getStorageSync("isSetTradePassword")
		},
		methods: {
			clickItem(mode) {
				console.log(mode)
				this.mode = mode;
				this.popupShow = true;
			},
			toModifyPwd() {
				// this.$Router.push({name: 'findPwd', params: {isShowCurrentPwd: 'true'}});
			},
			nav_updataPayPassword() {
				this.$Router.push({
					name: 'updataPayPassword'
				});
			},
			nav_forgetPayPassword() {
				this.$Router.push({
					name: 'forgetPayPassword'
				});
			},
			nav_setPassword() {
				this.$Router.push({
					name: 'setNewPayPassword'
				});
			}
		}
	}
</script>

<style lang="scss">
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.main {
		padding-top: 4rpx;
		color: #fff;
	}

	.select {
		height: 88rpx;
		margin-top: 16rpx;
		padding: 0 42rpx;
		background-color: var(--dialog-bg-color);
		color: var(--secondary-front-color);
		font-size: 28rpx;

		.u-icon {
			color: var(--secondary-front-color);
		}
	}
</style>