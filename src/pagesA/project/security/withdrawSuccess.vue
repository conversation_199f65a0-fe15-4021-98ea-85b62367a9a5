<template>
	<view>
		<u-navbar back-icon-color="#000" :background="bg" title="提现成功" title-color="var(--message-box-point-color)" title-bold :is-back="false"
			:border-bottom="false">
		</u-navbar>
		<view class="withdraw_box">
			<view class="flex_all img">
				<image src="../../../static/imgs/public/withdrawSuccess.png" mode="widthFix"></image>
			</view>
			<view>
				提现申请成功
			</view>
			<view>
				预计3个工作日内到账，请注意查收
			</view>
		</view>
		<view class="body">
			<view class="title">
				提现详情
			</view>
			<view class="item">
				<text class="left">
					{{name}}
				</text>
				<text>
					{{alipay}}
				</text>
			</view>
			<view class="item">
				<text class="left">
					提现金额
				</text>
				<text>
					{{money}}元
				</text>
			</view>
		</view>

		<view class="submit">
			<button-bar @click="nav_withdraw()" text="完成"></button-bar>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		data() {
			return {
				money: "",
				alipay: "",
				name: "",
				bg: {
					'background-color': '#121212'
				}
			};
		},
		onLoad() {
			this.money = uni.getStorageSync("moneyWithdraw")
			this.alipay = uni.getStorageSync("alipay")
			this.name = uni.getStorageSync("name")
		},
		methods: {
			nav_withdraw() {
				this.$Router.push({
					name: "myBalance"
				})
			}
		},
		components: {
			ButtonBar
		}
	}
</script>

<style lang="scss">
	.withdraw_box {
		padding: 60rpx;
		text-align: center;
		color: #F9F9F9;
		font-size: 28rpx;

		view {
			margin-bottom: 20rpx;
		}

		.img {
			image {
				width: 140rpx;
			}
		}
	}

	.body {
		.title {
			padding: 0rpx 30rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			font-weight: 600;
			color: #F9F9F9;
		}

		.item {
			height: 80rpx;
			line-height: 80rpx;
			// border-top: 1rpx solid #EEEEEE;
			font-size: 26rpx;
			display: flex;
			justify-content: space-between;
			padding: 0rpx 30rpx;
			color: var(--secondary-front-color);

			.left {
				color: #F9F9F9;
			}
		}
	}

	.submit {
		padding: 0rpx 40rpx;
		position: fixed;
		bottom: 150rpx;
		left: 0rpx;
		width: 100%;
	}
</style>
