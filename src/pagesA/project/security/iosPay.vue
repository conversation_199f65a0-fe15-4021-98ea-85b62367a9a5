<template>
	<view class="">
		<u-top-tips ref="uTips"></u-top-tips>
		<view class="navbar">
			<view class="left" @click="nav_back()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20220613/b81fa4236e814e3b7cfeb8c8698568d3_48x48.png"
					mode="widthFix"></image>
			</view>
			
		</view>
		<view class="pay_content ">
			<view class="pay_recommend">
				<view class="li" :class="{'active':isChckd==index}" v-for="(item,index) in payList" :key="index"
					@click="check_money(item,index)">
					<view>{{item.name}}</view>
					<view>{{item.value}}</view>
				</view>
			</view>
		</view>
		<view class="pay_footer" >
			<view class="xieyi">
				<view class="p">
					<image @click="j_isAgree" v-if="!isAgree" src="../../../static/imgs/public/check.png" mode="">
					</image>
					<image @click="j_isAgree" v-else src="../../../static/imgs/public/checked.png" mode=""></image>
					<view class="msg">
						我已阅读并同意<text @click="nav_link('Bigverse平台服务协议','userAgreement')">《Bigverse平台服务协议》</text><text
							@click="nav_link('法律声明及隐私政策', 'PrivacyAgreement')">《法律声明及隐私政策》</text>
					</view>
				</view>
			</view>
			<view class="but" >
				<u-button :hair-line="false" hover-class="none" shape="square" :throttle-time="1000" @click="unifyPay()">
					充值
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
	    Iap,
	    IapTransactionState
	  } from "./js/iap.js"
	export default {
		data() {
			return {
				payList: [
					{
					name:"148 Bigverse Gas",
					value:"148元",
					amount:148,
					productid:"com.nftcn.rech.148"
				},{
					name:"228 Bigverse Gas",
					value:"228元",
					amount:228,
					productid:"com.nftcn.rech.228"
				},{
					name:"288 Bigverse Gas",
					value:"288元",
					amount:288,
					productid:"com.nftcn.rech.288"
				},{
					name:"798 Bigverse Gas",
					value:"798元",
					amount:798,
					productid:"com.nftcn.rech.798"
				},{
					name:"1048 Bigverse Gas",
					value:"1048元",
					amount:1048,
					productid:"com.nftcn.rech.1048"
				},{
					name:"1598 Bigverse Gas",
					value:"1598元",
					amount:1598,
					productid:"com.nftcn.rech.1598"
				},{
					name:"3298 Bigverse Gas",
					value:"3298元",
					amount:3298,
					productid:"com.nftcn.rech.3298"
				},{
					name:"4498 Bigverse Gas",
					value:"4498元",
					amount:4498,
					productid:"com.nftcn.rech.4498"
				},{
					name:"6498 Bigverse Gas",
					value:"6498元",
					amount:6498,
					productid:"com.nftcn.rech.6498"
				}],
				isChckd:0,
				money:148,
				isAgree:false,
				_iap:"",
				productList:[
					"com.nftcn.rech.148",
					"com.nftcn.rech.228",
					"com.nftcn.rech.288",
					"com.nftcn.rech.798",
					"com.nftcn.rech.1048",
					"com.nftcn.rech.1598",
					"com.nftcn.rech.3298",
					"com.nftcn.rech.4498",
					"com.nftcn.rech.6498"
				],
				productId:"com.nftcn.rech.148"
			};
		},
		onLoad(options) {
			 // 创建示例
			  this._iap = new Iap({
				products: this.productList // 苹果开发者中心创建
			  })
		
			  this.init();
			// uni.getProvider({
			//   service: 'payment',
			//   success: (res) => {
			// 	  console.log(res)
			//     const iapChannel = res.providers.find((channel) => {
			//       return (channel.id === 'appleiap')
			//     })
			// 	console.log(iapChannel)
			//     // 如果 iapChannel 为 null，说明当前包没有包含iap支付模块。注意：HBuilder基座不包含 iap 通道
			//   }
			// });
			// this._iap = new Iap({
			// 	products: this.productList,
			// });
			// console.log(this.Iap)
		},
		onShow() {
		},
		onBackPress() {
			this.$Router.push({
				name: "myBalance"
			})
			return true
		},
		methods: {
			check_money(item,index){
				this.amount= item
				this.isChckd = index
				this.productId = item.productid
			},
			j_isAgree() {
				this.isAgree = !this.isAgree
			},
			unifyPay(){
				if (!this.isAgree) {
					uni.showToast({
						title: "请先勾选协议",
						icon: "none",
						duration: 3000
					});
				} else {
					console.log("苹果支付")
					this.payOrder()
				}
			},
			getChannels(success, fail) {
			    if (this._channel !== null) {
			        success(this._channel)
			        return
			    }
			
			    if (this._channelError !== null) {
			        fail(this._channelError)
			        return
			    }
			
			    uni.getProvider({
			        service: 'payment',
			        success: (res) => {
			            this._channel = res.providers.find((channel) => {
			                return (channel.id === 'appleiap')
			            })
			
			            if (this._channel) {
			                success(this._channel)
			            } else {
			                this._channelError = {
			                    errMsg: 'paymentContext:fail iap service not found'
			                }
			                fail(this._channelError)
			            }
			        }
			    });
			},
			async payOrder() {
				let res = await this.$api.java_deposit_create({
					paymentScene: 3,
					amount: this.money,
					version:uni.getSystemInfoSync().appVersion
				});
				if (res.status.code == 0) {
					this.payBubmit(res.result.orderNo)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			async payBubmit(orderId) {
				let res = await this.$api.java_order_pay_call({
					payMethod: 9,
					paymentScene: 3,
					orderId,
					orderType: "D",
				});
				if (res.status.code == 0) {
						console.log(res.result.orderNo)
						this.payment(res.result.orderNo)
						// let orderInfo = {
						// 	productid: this.amount.productid,
						// 	manualFinishTransaction: false,
						// 	username: orderNo, //根据业务需求透传参数，关联用户和订单关系
						// }
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			nav_link(title, pageName) {
				const link = `https://www.nftcn.com/link/#/pages/index/${pageName}`;
				if (!this.isApp) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link
						}
					})
				} else {
					// window.webkit?.messageHandlers?.navigateTo.postMessage(pageName);
					this.$native.openLink(link);
				}
			},
			
			
			async init() {
			        uni.showLoading({
			          title: '检测支付环境...'
			        });
			
			        try {
			          // 初始化，获取iap支付通道
			          await this._iap.init();
			
			          // 从苹果服务器获取产品列表
			          this.productList = await this._iap.getProduct();
					  console.log("远端产品列表",this.productList)
			//           this.productList[0].checked = true;
			//           this.productId = this.productList[0].productid;
			
			//           // 填充产品列表，启用界面
			//           this.disabled = false;
			        } catch (e) {
			          uni.showModal({
			            title: "init",
			            content: e.message,
			            showCancel: false
			          });
			        } finally {
			          uni.hideLoading();
			        }
			
			        if (this._iap.ready) {
			          this.restore();
			        }
			      },
			      async restore() {
			        // 检查上次用户已支付且未关闭的订单，可能出现原因：首次绑卡，网络中断等异常
			
			        // 在此处检查用户是否登录
			
			        uni.showLoading({
			          title: '正在检测已支付且未关闭的订单...'
			        });
			
			        try {
			          // 从苹果服务器检查未关闭的订单，可选根据 username 过滤，和调用支付时透传的值一致
			          const transactions = await this._iap.restoreCompletedTransactions({
			            username: ""
			          });
			
			          if (!transactions.length) {
			            return;
			          }
			
			          // 开发者业务逻辑，从服务器获取当前用户未完成的订单列表，和本地的比较
			          // 此处省略
			
			          switch (transaction.transactionState) {
			            case IapTransactionState.purchased:
			              // 用户已付款，在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
			              //let result = await this.validatePaymentResult();
			
			              // 验证通过，交易结束，关闭订单
			              // if (result) {
			              //   await this._iap.finishTransaction(transaction);
			              // }
			              break;
			            case IapTransactionState.failed:
			              // 关闭未支付的订单
			              await this._iap.finishTransaction(transaction);
			              break;
			            default:
			              break;
			          }
			        } catch (e) {
			          uni.showModal({
			            content: e.message,
			            showCancel: false
			          });
			        } finally {
			          uni.hideLoading();
			        }
			      },
			      async payment(orderNo) {
			        if (this.loading == true) {
			          return;
			        }
			        this.loading = true;
			
			        uni.showLoading({
			          title: '支付处理中...'
			        });
			
			        try {
					const transaction = await this._iap.requestPayment({
					   productid: this.productId,
					   manualFinishTransaction: true,
					   username: orderNo //根据业务需求透传参数，关联用户和订单关系
					 });
					 console.log(transaction)
			          // 请求苹果支付
			         
			          // 在此处请求开发者服务器，在服务器端请求苹果服务器验证票据
			          let obj =  await this.$api.applePayNotify({
			            orderNo: orderNo,
			            username: orderNo,
			            payload: transaction.transactionReceipt, // 不可作为订单唯一标识
			            transactionId: transaction.transactionIdentifier
			          });
					  console.log(obj)
						if(obj.status.code == 0){
							console.log("成功回调:"+obj)
							await this._iap.finishTransaction(transaction);
						}
			          // 支付成功
			        } catch (e) {
						console.log(e)
						uni.showModal({
			            content: '用户取消支付',
			            showCancel: false
			          });
			        } finally {
			          this.loading = false;
			          uni.hideLoading();
			        }
			      },
			      createOrder({
			        productId
			      }) {
			        return new Promise((resolve, reject) => {})
			      },
			      validatePaymentResult(data) {
			        return new Promise((resolve, reject) => {});
			      },
			      applePriceChange(e) {
			        this.productId = e.detail.value;
			      }
			
			
		},
		mounted() {
		},

	}
</script>

<style lang="scss" scoped>
	::v-deep .u-tips{
		top:130rpx !important;
	}
	.navbar {
		position: relative;
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		color: #F9F9F9;
		font-size: 32rpx;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;

		.left {
			position: absolute;
			top: 20rpx;
			left: 26rpx;

			image {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	::v-deep .uni-input-input {
		color: #DEDEDE;
	}

	::v-deep .u-mode-center-box {
		background-color: #1E1E1E;
	}

	.pay_content {
		padding: 24rpx 40rpx 0;
		font-family: PingFangSC-Regular, PingFang SC;

		.title {
			font-size: 28rpx;
			font-weight: 600;
			color: #DEDEDE;
			margin-bottom: 44rpx;
		}

		.pay_num {
			display: flex;
			// justify-content: flex-start;
			align-items: baseline;

			text {
				font-size: 28rpx;
				font-family: MiSans-Medium, MiSans;
				font-weight: 500;
				color: #F9F9F9;
				vertical-align: middle;
			}

			.u-input::v-deep {
				padding: 0rpx;

				.uni-input-input {
					font-size: 56rpx;
					font-weight: 500;
					color: #F9F9F9;
				}
			}

			border-bottom: 2rpx solid #282828;

			&.active {
				border-bottom: 2rpx solid #00FBEF;
			}
		}

		.pay_recommend {
			margin-top: 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;

			.li {
				width: 49%;
				border-radius: 4rpx;
				text-align: center;
				border: 2rpx solid var(--default-color3);
				margin-bottom: 20rpx;
				font-size: 24rpx;
				font-weight: 600;
				padding:30rpx 0rpx;
				color: #F9F9F9;
				>view:nth-child(1){
					margin-bottom:14rpx;
				}
				&.active {
					background: rgba(31, 237, 240, 0.1000);
					// box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
					color: #1FEDF0;
					border: 2rpx solid #1FEDF0;
				}
			}

			.li:nth-child(4),
			.li:nth-child(8) {
				margin-right: 0rpx;
			}
		}
	}

	.pay_methods {
		margin-top: 40rpx;
		padding: 0 40rpx 32rpx;
		color: #DEDEDE;
		background-color: var(--dialog-bg-color);

		.title {
			height: 92rpx;
			line-height: 92rpx;
			font-size: 28rpx;
			font-weight: 600;
			margin-bottom: 20rpx;
			border-bottom: 2rpx solid #282828;
		}

		.pay_ul {
			color: #F9F9F9;

			.li {
				width: 100%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				// border-bottom: 2rpx solid rgba(255, 255, 255, 0.05);
				&.maxPay{
					.right_text{
						display: flex;
						justify-content: flex-start;
						font-size:28rpx;
						text{
							font-size:24rpx;
							color:#686868;
						}
					}
				}
				.left_icon {
					margin-right: 20rpx;
					width: 42rpx;

					image {
						width: 42rpx;
					}
				}

				.right_text {
					width: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 28rpx;
					height: 100rpx;
					line-height: 100rpx;
					font-weight: 400;

					.img {
						image {
							width: 40rpx;
						}
					}

				}
			}

			.bank_list {

				width: 100%;
				// border-bottom: 2rpx solid rgba(255, 255, 255, 0.05);

				.left_icon {
					margin-right: 16rpx;
					width: 42rpx;
					line-height: 100rpx;
					height: 100rpx;
					display: flex;
					align-items: center;

					image {
						width: 42rpx;
					}
				}

				.right_text {
					width: 100%;
					font-size: 28rpx;
					height: 100rpx;
					line-height: 100rpx;

					.img {
						image {
							width: 38rpx;
						}
					}
				}

				.bank_subclass {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-left: 60rpx;
					margin-top: 20rpx;
					// height: 80rpx;
					// line-height: 80rpx;

					.left_bank_name {
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 24rpx;
						color: var(--default-color3);

						image {
							width: 40rpx;
							border-radius: 50%;
						}
					}

					.text_img {
						display: flex;
						justify-content: center;
						align-items: center;
						color: var(--default-color3);
						font-size: 24rpx;

						image {
							width: 32rpx;
							height: 32rpx;
							margin-left: 4rpx;
							margin-right: -10rpx;
						}
					}
				}
			}
		}
	}

	.pay_footer {
		margin-top: 350rpx;
		padding: 40rpx 52rpx;
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		width: 100%;
		background-color: var(--main-bg-color);

		.xieyi {
			width: 100%;
			transform: scale(0.94);

			.p {
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 12rpx;
				}

				.msg {
					font-size: 24rpx;
					color: #DEDEDE;

					text {
						// text-decoration: underline;
						color: #1FEDF0;
					}
				}
			}
		}

		.but {
			margin-top: 32rpx;

			button {
				width: 100%;
				height: 88rpx;
				line-height: 88rpx;
				text-align: center;
				background: var(--primary-button-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				border-radius: 1px;
				font-size: 36rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #121212;
			}
		}
	}

	.modal_head {
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #F2F2F7;

		.left {
			position: absolute;
			left: 40rpx;
			top: 20rpx;

			image {
				width: 20rpx;
			}
		}
	}

	.modal_Body {
		padding: 24rpx;

		.bank_ul {
			display: inline-block;
			background-color: var(--message-box-point-color);
			border-radius: 12rpx;
			overflow: hidden;
			width: 100%;

			.li {
				width: 100%;
				padding: 0rpx 24rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.left_icon {
					margin-right: 10rpx;
					width: 42rpx;

					image {
						width: 42rpx;
						border-radius: 50%;
					}
				}

				.right_text {
					width: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 24rpx 0rpx;
					border-bottom: 1rpx solid #F2F2F7;
					font-size: 30rpx;

					.img {
						image {
							width: 40rpx;
							border-radius: 50%;
						}
					}
				}
			}
		}
	}

	.BankVerifyBody {
		padding: 40rpx 42rpx 42rpx 42rpx;

		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 48rpx;
			line-height: 44rpx;
			color: #F9F9F9;
			margin-bottom: 40rpx;

			.right {
				position: absolute;
				right: 12rpx;
				top: 12rpx;

				image {
					width: 48rpx;
				}
			}
		}

		.item {
			margin-bottom: 46rpx;

			.labal {
				font-weight: 600;
				color: #616161;
				font-size: 24rpx;
			}

			.input {
				align-items: center;
				height: 88rpx;
				border-bottom: 2rpx solid #282828;

				.left {
					width: 50%;
					color: #F9F9F9;
					padding: 6rpx;
					border-radius: 4rpx;
					font-size: 28rpx;

					input {
						font-size: 28rpx;
					}
				}

				.right {
					image {
						width: 40rpx;
					}

					text {
						color: #616161;
						font-weight: 400;
						font-size: 28rpx;
					}
				}
			}
		}

		.footer {
			button {
				width: 100%;
				height: 80rpx;
				background: linear-gradient(132deg, #F6AAF2 0%, #8CC9F3 51%, #00FBEF 100%);
				color: #121212;
				font-weight: 600;
				line-height: 80rpx;
				font-size: 28rpx;
				border-radius: 0rpx;
				border: none;
			}
		}
	}

	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;

		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;

			image {
				width: 30rpx;
			}
		}
	}

	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		button {
			margin: 0rpx;
			padding: 0rpx;
			width: 238rpx;
			height: 88rpx;
			line-height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			background-color: var(--default-color3);
			color: var(--message-box-point-color);

			&.active {
				background-color: #333333;
				color: var(--message-box-point-color);
			}
		}
	}

	.msg_y {
		font-size: 28rpx;
		color: var(--default-color3);
		line-height: 40rpx;
	}
</style>
