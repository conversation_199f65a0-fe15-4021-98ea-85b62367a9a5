<template>
	<view class="main">
		<u-toast ref="uToast" />
		<view class="select space-between" @click="clickItem('phone')" v-show="phone_show==1">
			<view class="label">
				<image src="@/static/imgs/public/phone_icon.png" mode="widthFix"></image>
				<text>通过手机号码验证</text>
			</view>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<view class="select space-between" @click="clickItem('email')" v-show="email_show==1">
			<view class="label">
				<image src="@/static/imgs/public/phone_icon.png" mode="widthFix"></image>
				<text>通过邮箱号验证</text>
			</view>
			<u-icon name="arrow-right" size="28"></u-icon>
		</view>
		<!-- 	<pay-popup email="" :popup-show.sync="popupShow" :mode="mode" @resetSuccess="popupShow = false"
			@createSuccess="popupShow = false"></pay-popup> -->
	</view>
</template>

<script>
	import payPopup from "@/components/payPopup";
	export default {
		components: {
			payPopup
		},
		data() {
			return {
				isPasswrod: 0,
				popupShow: false,
				orderNo: '',
				mode: '',
				type: "", //场景参数
				path: "", //场景来源
				email: "",
				phone: "",
				show: "",
				phone_show: "",
				email_show: "",
				vtype: ""
			};
		},
		onLoad(options) {
			this.type = options.type
			this.path = options.path
			this.vtype = options.vtype
			this.email = JSON.parse(uni.getStorageSync("userInfo")).email
			this.phone = JSON.parse(uni.getStorageSync("userInfo")).phone
			if (this.phone != '') {
				console.log(this.phone)
				this.phone_show = 1
			}
			if (this.email != '') {
				this.email_show = 1
			}
			this.isPasswrod = uni.getStorageSync("isSetTradePassword")
		},
		methods: {
			clickItem(e) {
				if (this.path == "modifyPwd") {
					this.$Router.push({
						name: this.path,
						params: {
							'mode': e,
							'type': this.type
						}
					})
				} else if (this.path == 'emailVerify') {
					if (e == "phone") {
						this.$Router.push({
							name: this.path,
							params: {
								'phone': this.phone,
								vtype: this.vtype
							}
						})
					} else {
						this.$Router.push({
							name: this.path,
							params: {
								vtype: this.vtype
							}
						})
					}
				} else if (this.path == 'phoneVerify') {
					if (e == "phone") {
						this.$Router.push({
							name: this.path,
							params: {
								'phone': this.phone,
								vtype: this.vtype
							}
						})
					} else {
						this.$Router.push({
							name: this.path,
							params: {
								vtype: this.vtype
							}
						})
					}
				}
			},
		}
	}
</script>

<style lang="scss">
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.main {
		padding-top: 4rpx;
		color: #fff;
	}

	.select {
		height: 88rpx;
		margin-top: 16rpx;
		padding: 0 42rpx;
		color: var(--secondary-front-color);
		font-size: 28rpx;

		.label {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			color: var(--message-box-point-color);

			image {
				width: 40rpx;
				margin-right: 20rpx;
			}
		}

		.u-icon {
			color: var(--secondary-front-color);
		}
	}
</style>