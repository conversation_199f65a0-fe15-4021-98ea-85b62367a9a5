<template>
	<view class="content">
		<view>
			<view class="setting_title">作品和藏品</view>
			<view class="setting_item" @click="privacySet('userCreate','个人主页我的创作列表')">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/abb472eba3d180aa78d9df9c6881c53a_30x30.png"></image>
					<view class="item_title">我的创作</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.createPurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view> 
			</view>
			<view class="setting_item" @click="privacySet('userCollections','个人主页我的藏品列表' )">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit"   src="https://cdn-lingjing.nftcn.com.cn/image/20220614/f4b59c43e31890d0f90da9d1cfe46224_30x30.png"></image>
					<view class="item_title">我的藏品</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.collectionPurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view>
			</view>
			<view class="setting_item" @click="privacySet('domainName','个人主页我的DID列表' )">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit"   src="https://cdn-lingjing.nftcn.com.cn/image/20230307/8718d48dee48bd1d46bd638cd469d00c_74x75.png"></image>
					<view class="item_title">我的DID</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.domainNamePurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view>
			</view>
		</view>

		<view class="user">
			<view class="setting_title">用户关系</view>
			<view class="setting_item" @click="privacySet('userFavorite','我关注的艺术家、藏家用户列表')">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/9e7de543af426352367b1e9f2d8d0e35_26x30.png"></image>
					<view class="item_title">我关注的人</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.favoritePurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view>
			</view>
			<view class="setting_item" @click="privacySet('goodsFavorite','我关注的作品列表')">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/2c0c684876e839f3c0b386fd522701d9_30x30.png"></image>
					<view class="item_title">我关注的作品</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.favoriteGoodsPurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view>
			</view>
			<view class="setting_item" @click="privacySet('userFans','我的粉丝用户列表')">
				<view class="item_left">
					<image class="ietm_icon" mode="aspectFit" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/78e32d2d926517143ccb2d85af2d2f0a_32x30.png"></image>
					<view class="item_title">我的粉丝</view>
				</view>
				<view class="item_right">
					{{typeList[userPreview.fansPurview]}}
					<image class="right_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/c1aae9ca89f6492af0591b3fe6f5f631_12x22.png"></image>
				</view>
			</view>
		</view>

		<!-- 弹窗 -->
		<u-popup v-model="show" mode="bottom" @open="openPopup">
			<view class="set_content">
				<view class="set_title">
					{{popupTitle}}
				</view>
				<view class="select_item" v-for="(item, index) in settingList" :key="index" @click="handleSelect(item.id)">
					<view class="select_left">
						<image
							class="set_icon"
							:src="item.icon"
							mode="aspectFit"
						/>
						{{item.title}}
					</view>
					<image class="select_icon" src="https://cdn-lingjing.nftcn.com.cn/image/20220614/e351e7420ac39bf1f045f429dbb9a220_32x24.png" v-show="preview === item.id"></image>
				</view>
				<view class="cancel" @click="show = false">取消</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
        preview: 1,
				popupTitle: "",
        type:'',
				settingList: [
					{id: 0, title: '所有人可见', icon: "https://cdn-lingjing.nftcn.com.cn/image/20220614/c2a0b71a87f9da2ba951d7876fb66a9b_24x32.png"},
					{id: 1, title: '仅粉丝可见', icon: "https://cdn-lingjing.nftcn.com.cn/image/20220614/78e32d2d926517143ccb2d85af2d2f0a_32x30.png"},
					{id: 2, title: '仅自己可见', icon: "https://cdn-lingjing.nftcn.com.cn/image/20220614/cda3604660b9c5d1ef04052403119899_24x32.png"}
				],
				typeList: [
					'所有人可见',
					'仅粉丝可见',
					'仅自己可见'
				],
        userPreview: {},
        previewTemp: '',
			}
		},
		onLoad() {
            this.getPrivacy();
        },
		methods: {
            openPopup() {
                const { type,userPreview:{ collectionPurview, createPurview, favoritePurview, favoriteGoodsPurview, fansPurview,domainNamePurview }} = this;
                switch (type) {
                    case 'userCreate':
                        this.preview = createPurview;
                        break;
                    case 'userCollections':
                        this.preview = collectionPurview;
                        break;
                    case 'userFavorite':
                        this.preview = favoritePurview;
                        break;
                    case 'goodsFavorite':
                        this.preview = favoriteGoodsPurview;
                        break;
                    case 'userFans':
                        this.preview = fansPurview;
                        break;
					case 'domainName':
					    this.preview = domainNamePurview;
					    break;
                }
                this.previewTemp = this.preview;
            },
            async getPrivacy() {
                let { result } = await this.$api.java_userPreview({})
                this.userPreview = result;
            },
            async closePopup() {
                const { preview,type,$api,toast,previewTemp } = this
                // 如改变，则提交
				console.log(type)
                if (preview !== previewTemp) {
                    let {status: {code, msg}} = await $api[`java_${type}Preview`]({
                        preview 
                    })
                    await this.getPrivacy();
                    toast(code === 0 ? '设置成功' : msg)
					this.show = false
					
                }
            },
            toast(title) {
                uni.showToast({
                    title: title,
                    icon: "none",
                    duration: 2000
                });
            },
			privacySet(type,title) {
				this.show = true;
				this.popupTitle = title;
				this.type = type;
			},
			handleSelect(id) {
				this.preview = id;
				this.closePopup()
				console.log(this.preview)
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .uni-scroll-view-content {
		background-color: var(--dialog-bg-color);
	}
	.user {
		margin-top: 20rpx;
	}
	.setting_title {
		padding: 40rpx 40rpx 0;
		font-family: PingFangSC-Regular, PingFang SC;
		font-size: 24rpx;
		font-weight: 400;
		color: var(--secondary-front-color);
		letter-spacing: 2rpx;
	}
	.setting_item {
		padding: 32rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 2rpx solid rgba(255, 255, 255, 0.05);
		.item_left {
			display: flex;
			align-items: center;
			.ietm_icon {
				height: 30rpx;
				width: 30rpx;
				margin-right: 12rpx;
			}
			.item_title {
				font-size: 28rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #F9F9F9;
				letter-spacing: 2rpx;
				line-height: 40rpx;
			}
		}
		.item_right {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			font-size: 24rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: var(--secondary-front-color);
			.right_icon {
				height: 22rpx;
				width: 12rpx;
				margin-left: 20rpx;
			}
		}

	}

	.set_content {
		.set_title {
			padding: 32rpx 0;
			text-align: center;
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #F9F9F9;
			border-bottom: 2rpx solid #282828;
			letter-spacing: 2rpx;
			line-height: 44rpx;
			
		}
		.select_item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 32rpx 40rpx;
			font-size: 30rpx;
			font-weight: normal;
			color: #838693;
			border-bottom: 2rpx solid #282828;
			.select_left {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #F9F9F9;
				.set_icon {
					width: 24rpx;
					height: 32rpx;
					margin-right: 20rpx;
				}
			}
			.select_icon {
				width: 32rpx;
				height: 24rpx;
			}
		}

		.cancel {
			width: 100%;
			border-top: 2rpx solid rgba($color: #000000, $alpha: 0.08);
			text-align: center;
			padding: 30rpx;
			font-weight: 500;
			color: #F9F9F9;
			border-top: 24rpx solid #121212;
			border-bottom: 60rpx solid #121212;
		}
	}
</style>
