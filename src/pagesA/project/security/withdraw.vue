<template>
	<view>
		<u-navbar back-icon-color="#F9F9F9" title="提现" title-color="#F9F9F9" title-bold :border-bottom="false"
			:background="{'backgroundColor': 'var(--main-bg-color)'}" :custom-back="back"></u-navbar>
		<view class="withdraw_box">
			<view class="method">
				<text>提现到</text>
				<view class="withdraw_bank" v-if="bank_list==''" @click="nav_bank()">
					<view class="left">
						<view class="bank_msg">
							点击添加银行卡
						</view>
					</view>
					<view class="right">
						<image src="../../../static/imgs/public/nav_right.png" mode="widthFix"></image>
					</view>
				</view>
				<view class="withdraw_bank" v-else @click="$refs.selectBank.showPop=true">
					<view class="left">
						<view class="bank">
							<view class="bank_icon">
								<image :src="show_bank.icon" mode="widthFix"></image>
							</view>
							<view>
								{{show_bank.bankName}}(**** {{show_bank.bankCardNumber}})
							</view>
						</view>
					</view>
					<view class="right">
						<image src="../../../static/imgs/public/nav_right.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="withdraw_money">
				<view class="title">
					提现金额
				</view>
				<view class="money ">
					<text>￥</text>
					<u-input type="number" :border="false" :maxlength="9" :focus="true" :custom-style="inputStyle"
						v-model="money" placeholder="请输入提现金额" placeholder-style="color:#616161" />
					<text class="money_all" @click="allNum()">全部提现</text>
				</view>
				<view class="msg red" v-if="type">
					输入金额超过可用余额
				</view>
				<view class="msg" v-else>
					可提现金额为{{balanceNum}}元
				</view>
			</view>
			<view class="withdraw_msg">
				提示：为避免银行二类卡提现遇阻占用资金，我们强烈建议您使用一类卡。如您不得已须使用二类卡提现，请确保已上调二类卡的收款金额上限
			</view>
			<!-- <view  
				@click="submit()">
				
			</view> -->
			<u-button class="submit " :class="{'active':type==false&&money!=''&&this.isNoBank==false}"
				:throttle-time="1000" :hair-line="false" hover-class="none" shape="square" @click="submit()">
				提现
			</u-button>
			<!-- 选择银行卡 -->
			<select-bank ref="selectBank" @navbank="nav_bank" :bankList="bank_list" @select="check_bank" />

			<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false"
				border-radius="0">
				<view class="BankVerifyBody">
					<view class="head_title_y">
						<view class="right" @click="isPassword=false">
							<image src="../../../static/imgs/mall/mall_colse.png" mode="widthFix"></image>
						</view>
						请先设置支付密码
					</view>
					<view class="msg_y">
						非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
					</view>
					<view class="footer_y" @click="isPassword=false">
						<button>
							取消
						</button>
						<button class="active" @click="SetPayPassword()">
							去设置
						</button>
					</view>
				</view>
			</u-modal>
			<pay-popup ref="payPopup"  :popup-show.sync="isPasswordImport" :title="passwordTitle" :message="passwordMsg"
				order-type="" :mode="mode" @pay="finishPay" @createSuccess="createSuccess" />
		</view>
	</view>
</template>

<script>
	import selectBank from "@/components/selectBank/index.vue";
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		components: {
			selectBank,
			payPopup
		},
		data() {
			return {
				payMethod: 'zfb',
				money: "",
				name: "",
				accountNo: "",
				balanceNum: 0.00,
				type: false,
				inputStyle: {
					"margin": "0 20rpx",
				},
				dStyle: {
					"font-weight": "500",
					'color': 'var(--secondary-front-color)'
				},
				isBank: false,
				bank_list: [],
				show_bank: [],
				isBanCheck: 0,
				isNoBank: true,

				isPassword: false,
				isSetTradePassword: 0, //是否设置过支付密码
				isSetPayPassword: false,
				isPasswordImport: false,
				title: "请设置余额支付密码，用于支付验证。",
				password: '',
				psw: '',
				error: '',
				passwordImportPay: "",
				isMailboxVerify: false,
				count: 0,
				email: "", //邮箱
				showVerify: false,
				MailboxVerifyCode: "",
				mode: "pay",
				passwordTitle: "账户提现",
				passwordMsg: "请输入余额支付密码，用于账户提现"
			};
		},
		onLoad() {
			this.isSetTradePassword = uni.getStorageSync("isSetTradePassword")
			this.email = uni.getStorageSync("email")
			this.getMoney()
		},
		onShow() {
			this.getBank_list()
		},
		watch: {
			'money': function(newVal) {
				if (Number(newVal) > this.balanceNum) {
					this.type = true
					console.log("大于金额")
				} else {
					this.type = false
				}
			},
		},
		methods: {
			async getMoney() {
				let res = await this.$api.java_withdraw_info();
				if (res.status.code == 0) {
					this.balanceNum = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
				console.log(res)
			},
			async getBank_list() {
				let res = await this.$api.bank_list({
					payMethod:5,
					type:101
				});
				if (res.status.code == 0) {
					if (res.result.list != '') {
						this.isNoBank = false
						this.bank_list = res.result.list
						this.show_bank = this.bank_list[0]
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
				console.log(res)
			},
			async submit() {
				var filer = /^\d+$/;
				if (this.type == false && this.money != '' && this.isNoBank == false) {
					console.log(this.accountNo, this.name, this.money)
					if (this.money == 0) {
						uni.showToast({
							title: "提现金额不能为0哦",
							icon: "none",
							duration: 3000
						});
						this.money = ""
					} else if (this.money.toString().split(".")[1] != undefined && this.money.toString().split(".")[1]
						.length > 2) {
						uni.showToast({
							title: "提现金额只能输入2位小数哦",
							icon: "none",
							duration: 3000
						});
						this.money = Number(this.money).toFixed(2)
					} else if (this.money < 5 || this.money == 5) {
						uni.showToast({
							title: "提现金额不能低于5元哦",
							icon: "none",
							duration: 3000
						});
						this.money = parseInt(this.money)
					} else {
						if (this.isSetTradePassword == 0) {
							//设置交易密码
							this.isPassword = true
						} else {
							this.mode='pay'
							this.isPasswordImport = true
							//输入交易密码
							console.log("输入密码支付")
						}
					}
				}
			},

			onblur() {
				console.log(this.money)
			},
			check_bank(item, index) {
				this.show_bank = item
				this.isBanCheck = index
				this.isBank = false
			},
			nav_bank() {
				uni.setStorageSync("addbank_link", window.location.href)
				this.$Router.push({
					name: "addbank"
				})
				this.isBank = false
			},
			back() {
				this.$Router.push({
					name: "myBalance"
				})
			},


			change(e) {
				// console.log(e)
			},
			finish(e) {
				this.password = e
				this.openSetPassword()
			},
			openSetPassword() {
				let _this = this
				uni.showLoading({
					title: '设置中'
				})
				if (this.psw == "") {
					setTimeout(() => {
						this.psw = this.password
						this.title = "请再次输入余额支付密码，用于二次确认密码"
					}, 1000)
					setTimeout(() => {
						this.title = "请再次输入余额支付密码，用于二次确认密码"
					}, 2000)
				} else {
					this.error = ""
					if (this.psw == this.password) {
						setTimeout(() => {
							_this.isSetPayPassword = false
							this.setPassword(this.psw)
						}, 2000);
					} else {
						setTimeout(() => {
							this.error = "二次密码不一致，请重新设置"
							setTimeout(() => {
								this.error = ""
							}, 2000);
							this.psw = ""
							this.title = "请设置余额支付密码，用于支付验证。",
								this.password = ""
						}, 2000);
					}
				}
				setTimeout(() => {
					uni.hideLoading();
					this.password = ""
				}, 2000);
			},
			async setPassword(password) { //设置支付密码
				let res = await this.$api.tradePassCreate({
					captcha: this.MailboxVerifyCode,
					password: password,
				});
				if (res.status == 200) {
					console.log(res)
					uni.setStorageSync("isSetTradePassword", 1)
					this.isSetTradePassword = 1
					// uni.setStorageSync("isSetTradePassword","1")
					console.log("刚设置的密码为：" + password)
					this.submitWithdraw(password)
				} else {
					this.psw = ""
					this.title = "请设置余额支付密码，用于支付验证。",
						this.password = ""
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			createSuccess(psw) {
				uni.setStorageSync("isSetTradePassword", 1)
				this.isSetTradePassword = 1
				this.submitWithdraw(psw)
			},
			finishPay(e) {
				this.passwordImportPay = e
				console.log(this.passwordImportPay)
				this.submitWithdraw(e)
			},
			SetPayPassword() {
				this.mode = "set"
				this.isPasswordImport = true
				// this.isPassword = false
				// this.isMailboxVerify = true
				// this.MailboxVerifyCode = ""
				// this.count = 0
				// this.getCode()
				// this.sendMailboxVerify()
			},
			getCode() {
				const TIME_COUNT = 60;
				if (!this.timer) {
					this.count = TIME_COUNT;
					this.show = false;
					this.timer = setInterval(() => {
						if (this.count > 0 && this.count <= TIME_COUNT) {
							this.count--;
						} else {
							this.show = true;
							clearInterval(this.timer);
							this.timer = null;
						}
					}, 1000)
				}
			},
			mailboxVerifySubmit() { //邮箱验证码提交
				if (this.MailboxVerifyCode == "") {
					uni.showToast({
						title: "请输入邮箱验证码哦",
						icon: "none",
						duration: 3000
					});
				} else {
					this.isMailboxVerify = false
					this.isSetPayPassword = true
				}
			},
			async sendMailboxVerify() {
				let res = await this.$api.sendEmailCaptcha({
					type: 1
				});
				if (res.status == 200) {
					const TIME_COUNT = 60;
					if (!this.timer) {
						this.count = TIME_COUNT;
						this.show = false;
						this.timer = setInterval(() => {
							if (this.count > 0 && this.count <= TIME_COUNT) {
								this.count--;
							} else {
								this.show = true;
								clearInterval(this.timer);
								this.timer = null;
							}
						}, 1000)
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					});

				}
			},
			isBankVerifyColse(index) {
				if (index == 0) {
					this.isBankVerify = false
					this.count = 0
					this.getCode()
				} else {
					this.isMailboxVerify = false
					this.count = 0
					this.getCode()
				}
			},
			allNum() {
				console.log(111)
				this.money = this.balanceNum
			},
			nav_forgetPayPassword() {
				this.$Router.push({
					name: 'forgetPayPassword'
				});
			},
			async submitWithdraw(password) { //发起提现
				uni.showLoading({
					title: '提现中'
				});
				let res = await this.$api.java_withdraw_create({
					accountType: 1,
					amount: this.money,
					bankCardId: this.show_bank.id,
					paymentScene: 1,
					tradePassword: password
				});
				if (res.status.code == 0) {
					uni.setStorageSync("alipay", this.show_bank.bankName + "（" + "****" + this.show_bank
						.bankCardNumber + ")")
					uni.setStorageSync("moneyWithdraw", this.money)
					uni.setStorageSync("name", "银行卡卡号")
					this.$Router.push({
						name: "withdrawSuccess"
					})
					uni.hideLoading();
				} else {
					uni.hideLoading();
					this.passwordImportPay = ""
					this.password = ""
					this.$refs['payPopup'].password = "";
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
			},
		}

	}
</script>

<style lang="scss" scoped>
	::v-deep .u-btn--bold-border {
		border: none;
	}

	::v-deep .uni-input-input {
		color: #F9F9F9;
		font-size: 36rpx;
	}

	.withdraw_box {
		padding: 40rpx;

		.method {
			color: #F9F9F9;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;

			.withdraw_bank {
				width: 100%;
				margin-top: 40rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.left {
					.bank_msg {
						font-size: 28rpx;
						color: #666666;
					}

					.bank {
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.bank_icon {
							margin-right: 10rpx;

							image {
								width: 40rpx;
								border-radius: 50%;
							}
						}
					}
				}

				.right {
					image {
						width: 16rpx;
					}
				}
			}
		}

		.withdraw_money {
			background-color: var(--dialog-bg-color);
			width: 100%;
			margin-top: 40rpx;

			.title {
				padding: 20rpx;
				color: var(--secondary-front-color);
				font-size: 28rpx;
			}

			.money {
				padding: 20rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-bottom: 1rpx solid #282828;

				text {
					font-size: 30rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #00FBEF;
				}

				.money_all {
					color: #00FBEF;
					font-weight: 500;
					margin: 0 10rpx;
				}
			}

			.msg {
				padding: 20rpx;
				color: var(--secondary-front-color);
				font-size: 26rpx;

				&.red {
					color: #BB3835
				}
			}
		}

		.withdraw_msg {
			font-weight: 400;
			color: var(--secondary-front-color);
			line-height: 32rpx;
			font-size: 24rpx;
			margin: 20rpx 0rpx 46rpx;
			// transform: scale(0.94);
		}

		.submit {
			width: 100%;
			height: 80rpx;
			background: var(--primary-button-color);
			color: #121212;
			font-size: 32rpx;
			font-family: PingFangSC-Semibold, PingFang SC;
			font-weight: 600;
			text-align: center;
			line-height: 80rpx;
			margin-top: 60rpx;

			&.active {
				background-color: #333333;
			}
		}
	}

	.BankVerifyBody {
		padding: 42rpx;

		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
			color: var(--message-box-point-color);

			.right {
				position: absolute;
				right: 40rpx;
				top: 66rpx;

				image {
					width: 30rpx;
				}
			}
		}

		.item {
			margin-bottom: 46rpx;

			.labal {
				color: var(--secondary-front-color);
				font-size: 24rpx;
			}

			.input {
				align-items: center;
				height: 88rpx;

				.left {
					width: 50%;
					color: var(--secondary-front-color);
					padding: 6rpx;
					border-radius: 4rpx;

					.input {
						font-size: 24rpx;
						color: var(--message-box-point-color);
					}
				}

				.right {
					image {
						width: 40rpx;
					}

					text {
						color: #00FBEF;
						font-size: 28rpx;
					}
				}
			}
		}

		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: var(--message-box-point-color);
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}

	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;
		color: var(--message-box-point-color);

		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;

			image {
				width: 30rpx;
			}
		}
	}

	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		button {
			margin: 0rpx;
			width: 238rpx;
			height: 64rpx;
			width: 240rpx;
			line-height: 64rpx;
			text-align: center;
			background-color: var(--secondary-front-color);
			border-radius: 0rpx;
			font-size: 30rpx;
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: var(--active-color);
			border-radius: 0rpx;

			&.active {
				color: var(--main-bg-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				background: var(--primary-button-color);
				color: var(--message-box-point-color);
				border: 0rpx;
			}
		}
	}

	.msg_y {
		font-size: 28rpx;
		color: var(--secondary-front-color);
		line-height: 40rpx;
	}

	.pwd-box {
		margin-top: 50rpx;

		.set-pwd {
			position: relative;

			.set-pwd-tip {
				font-size: 32rpx;
				color: #333;
				padding-bottom: 64rpx;
				text-align: center;
			}

			.set-error {
				color: #BB3835;
				font-size: 24rpx;
				text-align: center;
				position: absolute;
				// left: 290rpx;
				width: 100%;
				bottom: 20rpx;
			}
		}
	}

	.modal_head_password {
		font-size: 32rpx;
		font-weight: 600;
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
		margin-top: 42rpx;
		position: relative;

		.left {
			position: absolute;
			left: 40rpx;
			top: 20rpx;

			image {
				width: 30rpx;
			}
		}

		.right {
			position: absolute;
			right: 40rpx;
			top: 0rpx;
			font-weight: 500;
			font-size: 24rpx;
		}
	}

	.modal_Body_password {
		padding: 24rpx;

	}
</style>
