<template>
    <view class="container">
        <u-navbar :border-bottom="false" :title="!next ? 'Setup Guidelines' : 'Safety Certification'">
        </u-navbar>

        <view class="setup-guide" v-if="!next">
            <view class="item">
                <text class="title">1. Download Google Authenticator</text>
                <text class="content">If you haven't installed Google Authenticator yet, please download from the App
                    Store first.</text>
            </view>


            <view class="item">
                <text class="title">2. Scan the QR code or copy the key</text>
                <text class="content">Please scan this QR code in Google Authenticator, or copy the key and paste it
                    into Google Authenticator.</text>

                <view class="Keys">
                    <view class="code">Keys</view>
                    <view class="key">
                        <view class="text"> {{ key }} </view>
                        <text class="copy" @click="copyKey(key)">Copy</text>
                    </view>
                </view>

                <view class="qr_div">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="84"
                        :options="options"></uv-qrcode>
                </view>
            </view>


            <view class="item">
                <text class="title">3. Copy and enter the verification code</text>
                <text class="content">After scanning the QR code or entering the key, Google Authenticator will generate
                    a set of authentication Authentication Code. Please copy the code and return to Pink Wallet to enter
                    the code.</text>
                <view class="Keys">
                    <view class="code">Verification code</view>
                    <view class="key" style="padding: 0;">
                        <u-input class="input" v-model="code" :height="86" placeholder="Enter the verification code"
                            placeholder-style="color: #999999;" :border="false"></u-input>
                    </view>
                </view>
            </view>
        </view>
        <view class="safety-certification" v-else>
            <view class="item">
                <text class="title">Enabling two-step verification (2FA)</text>
                <text class="content">To ensure the security of your assets, we recommend enabling Google Authenticator
                    as a security verification method. <br> Once enabled, you will need to enter a dynamic verification
                    code
                    during login or fund operations to prevent unauthorized access to your account.</text>

                <view class="qr_div">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="84"
                        :options="options"></uv-qrcode>
                </view>


                <view class="Keys">
                    <view class="code">Keys</view>
                    <view class="key">
                        <view class="text"> {{ key }} </view>
                        <text class="copy" @click="copyKey(key)">Copy</text>
                    </view>
                </view>
                <view class="Keys">
                    <view class="code">Verification code</view>
                    <view class="key" style="padding: 0;">
                        <u-input class="input" v-model="code" :height="86" placeholder="Enter the verification code"
                            placeholder-style="color: #999999;" :border="false"></u-input>
                    </view>
                </view>
            </view>
        </view>

        <view class="btn">
            <u-button hover-class="none" class="exchange-btn " @click="handle">Continue</u-button>
            <u-button v-if="next" hover-class="none" class="cancel-btn" @click="cancel">Setting up later</u-button>

        </view>
    </view>
</template>

<script>
import uInput from '../../../uni_modules/uview-ui/components/u-input/u-input.vue';
export default {
    components: { uInput },
    name: 'AuthSetup',
    data() {
        return {
            code: "",
            qrcodeUrl: '',
            next: false,
            key: '',
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                style: 'round',
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
        };
    },
    onLoad() {
        this.GetsetupMfaSecret()
    },
    methods: {
        async GetsetupMfaSecret() {
            let res = await this.$api.setupMfaSecret();
            if (res.code == 200) {
                this.qrcodeUrl = res.result.totpUri
                this.key = this.extractSecret(res.result.totpUri)
                this.next = res.result.secretExisted
            } else {
                this.$u.toast(res.msg)
            }
        },
        extractSecret(urlString) {
            const url = new URL(urlString);
            return url.searchParams.get('secret');
        },
        cancel() {
            this.$Router.back()
        },
        handle() {
            if (!this.next) {
                this.next = true;
            } else {
                this.verifyCodes()
            }
            // if (this.next) {
            // } else {
            // this.Bind()
            // }
        },
        async Bind() {
            let res = await this.$api.bindingSecret({
                code: this.code
            })
            if (res.code == 200 && res.result.valid) {
                this.$u.toast('Success')
                this.next = true;
                this.qrcodeUrl = res.result.totpUri
                this.key = this.extractSecret(res.result.totpUri)
                // 
            } else {
                this.$u.toast(res.result.msg)
            }
        },
        async verifyCodes() {
            let res = await this.$api.verifyCode({
                code: this.code
            })
            if (res.code == 200 && res.result.valid) {

                if (!this.next) {
                    this.$u.toast('Success')
                    this.next = true;
                    this.qrcodeUrl = res.result.totpUri
                    this.key = this.extractSecret(res.result.totpUri)
                } else {
                    this.$u.toast('Success')
                    setTimeout(() => {
                        this.$router.back()
                    }, 1500);
                }



            } else {
                this.$u.toast(res.result.msg)
            }
        },
        copyKey(e) {
            navigator.clipboard.writeText(e).then(() => {
                uni.showToast({
                    icon: 'none',
                    title: this.$t("title.copy"),
                    duration: 2000
                });
            });
        }
    }
};
</script>

<style lang="scss">
.container {
    display: flex;
    flex-direction: column;
    // padding: 20rpx;
    font-size: 28rpx;
}

.btn {
    position: fixed;
    bottom: 60rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    width: 100%;

    text {
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;
        color: #000;
    }

    .cancel-btn {
        margin-top: 19rpx;
        width: 694rpx;
        // margin: 0 32rpx;
        height: 100rpx;
        background: #fff;
        border-radius: 64*2rpx;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        color: #000;
        border: 2rpx solid rgba(153, 153, 153, 1);
    }

    .exchange-btn {
        width: 694rpx;
        // margin: 0 32rpx;
        height: 100rpx;
        background: #FF82A3;
        border-radius: 64*2rpx;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        color: #fff;
    }

    .exchange-btn[disabled] {
        // background: #FF82A380; // 加透明度效果
        background: #D9D6D6;
        color: #666666;
    }
}

.setup-guide,
.safety-certification {
    margin-bottom: 40rpx;
    margin-top: 35rpx;
    padding: 35rpx 32rpx 0 32rpx;

    .item {
        display: flex;
        flex-direction: column;
        margin-bottom: 80rpx;


        .qr_div {
            margin-top: 22rpx;
            // width: 166rpx;
            width: fit-content;
            // height: 166rpx;
            padding: 10rpx;
            background: #fff;
            border-radius: 12rpx;
            border: 2rpx solid rgba(0, 0, 0, 0.1)
        }

        .Keys {
            margin-top: 32rpx;

            .input {
                text-indent: 32rpx;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
                color: rgba(0, 0, 0, .6);
            }

            .code {
                font-family: Gilroy;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 32rpx;
                color: #000;
            }

            .key {
                margin-top: 12rpx;
                background: rgba(0, 0, 0, .02);
                border-radius: 16rpx;
                padding: 24rpx 32rpx;

                display: flex;
                justify-content: space-between;

                .text {
                    display: block;
                    width: 500rpx;
                    overflow-wrap: break-word;
                    hyphens: auto;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .6);
                }

                .copy {
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 38rpx;
                    text-align: right;
                    color: rgba(255, 130, 163, 1);
                }
            }
        }

        .title {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 34rpx;
            color: #000;
        }

        .content {
            margin-top: 12rpx;
            font-family: Gilroy;
            font-weight: 500;
            font-size: 24rpx;
            line-height: 28rpx;
            color: #8F8F8F;
        }
    }


    button {
        padding: 10rpx 20rpx;
        margin: 10rpx 0;
        border: none;
        border-radius: 5rpx;
        cursor: pointer;
    }

    .continue-btn {
        background: #ff69b4;
        color: white;
    }

    .confirm-btn {
        background: #ff69b4;
        color: white;
    }

    .setup-later-btn {
        background: #ccc;
        color: black;
    }
}
</style>