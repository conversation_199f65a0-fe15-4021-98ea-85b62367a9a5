<template>
	<view class="body">
		<TabBar :initialActiveIndex="0"></TabBar>
		<!-- <view class="barHeight" :class="{'black_bg':current==0}"></view> -->
		<view class="blur" v-if="current!=0"></view>
		<view class="position_top" :class="{'bg':isShowNav&&current!=0,'black_bg':isShowNav&&current==0}">
			<view class="head_view">
				<view class="tabbar_view">
					<u-tabs name="cate_name" bg-color="transparent" font-size="32" :show-bar="false" :list="tabList"
						bold :inactive-color="current==0?'rgba(255, 255, 255, 0.65)':'rgba(18, 18, 18, 0.3)'" :active-item-style="current==0?itemStyle2:itemStyle"
						active-color="#121212" :current="current" @change="change"></u-tabs>
				</view> 
				<view class="sousuo" @click="userInfo()">
					<image v-if="current==0"
						src="https://cdn-lingjing.nftcn.com.cn/image/20241204/b44eaf4fb5b3a3e9ebac4dcaaf9356cb_64x64.png"
						mode="widthFix">
					</image>
					<image v-else
						src="https://cdn-lingjing.nftcn.com.cn/image/20241128/0904586d6ece8e739c78c3b9880e197d_64x64.png"
						mode="widthFix">
					</image>
				</view>
			</view>
		</view>
		<view class="recommend" v-show="current == 0">
			<view class="header_view">
				<swiper :current="swiperCurrent" height="500" @change="onSwiperChange" indicator-dots="true"  interval="5000" duration="200" indicator-color="rgba(217, 217, 217, 0.25)" indicator-active-color="rgba(255, 255, 255, 1)">
					<swiper-item class="swiper-item" v-for="(item, index) in bannersList" :key="index" >
						<scroll-view scroll-y style="height:100vh !important;"  >
							<view class="item" v-if="swiperCurrent==0">
								<view class="content">
									<view class="rail">
										<view class="screen-reader-text"></view>
										<view class="rail_container">
											<view class="rail_clip">
												<view class="rail_color"> 
													<view class="rail_gradients">
														<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/66f0584d 9d732f80d9dccd62fcd2d4aa_50x50.png" width="50" height="50" alt="Core Gradient"
															class="rail_gradient -core" />
														<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/54128efe763f4eb9f0921a5a012f4ff2_50x50.png" width="50" height="50" alt="Pro Gradient"
															class="rail_gradient -pro" />
													</view>
												</view>
											</view>
											<view style="width:2400rpx;height:215rpx;" v-html="svgBody"></view>
										</view>
									</view>
									<view class="boxes">
										<view class="box -core">
											<view class="box_container">
												<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/66f0584d9d732f80d9dccd62fcd2d4aa_50x50.png" width="50" height="50" alt="Core Gradient"
													class="box_gradient" />
												<img alt="Darkroom Core" width="1200" height="1200" src="https://cdn-lingjing.nftcn.com.cn/image/20241204/d749bb36f810487b72c0727d4104b2fc_1307x1927.png"
													class="box_image" />
											</view>
										</view>
										<view class="box -pro">
											<view class="box_container">
												<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/54128efe763f4eb9f0921a5a012f4ff2_50x50.png" width="50" height="50" alt="Pro Gradient"
													class="box_gradient" />
												<img alt="Darkroom Pro" width="1200" height="1200" src="https://cdn-lingjing.nftcn.com.cn/image/20241204/a8be1c11bd57289f39391af652d454bb_1313x1621.png"
													class="box_image" />
											</view>
										</view>
									</view>
								</view>	
							</view>
							<view class="item" v-if="swiperCurrent==1">
								<view class="content">
									<view class="rail">
										<view class="screen-reader-text"></view>
										<view class="rail_container">
											<view class="rail_clip">
												<view class="rail_color"> 
													<view class="rail_gradients">
														<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/79cd61039acad1fca3b0ae222da0494b_100x100.png" width="50" height="50" alt="Core Gradient"
															class="rail_gradient -core" />
													</view>
												</view>
											</view>
											<view style="width:2400rpx;height:215rpx;" v-html="svgBody"></view>
										</view>
									</view>
									<view class="boxes">
										<view class="box -max">
											<view class="box_container">
												<img src="https://cdn-lingjing.nftcn.com.cn/image/20241204/356c8762c02cd9cb142fc0bdeecccc92_300x300.png" width="50" height="50" alt="Core Gradient"
													class="box_gradient" />
												<img alt="Darkroom Core" width="1200" height="1200" src="https://cdn-lingjing.nftcn.com.cn/image/20241204/6345821ccaaca0b088eb68dbdff2b480_384x576.png"
													class="box_image" />
											</view>
										</view>
									</view>
								</view>	
							</view>
							<!-- <view class="bannerText twoOver" @click="bannerClick">
								{{item.name}}
							</view> -->
						</scroll-view>
					</swiper-item>
				</swiper>
				<view class="bg_image">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20241206/759cb6e6fd4171316af0858426fafbcd_1500x360.png" ></image>
				</view>
				<view class="bg_long" @click="bannerClick">
					  <div class="frame" :class="{'left':swiperCurrent==1&&sun!=0,'right':swiperCurrent==0&&sun!=0}"></div>
				</view>
			</view>
			<view class="first_issue_view">
				<view class="first_item" v-for="(item,index) in seriesList" @click="nav_seriesList(item)">
					<view class="cover">
						<image :src="item.cover.src" mode="aspectFill"></image>
					</view>
					<view class="font_view">
						<view class="title one">{{item.title}}</view> 
						<view class="right_font" v-if="item.minPrice>0">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20241128/92bc006b545cebc9f601cc4ac9e9c786_40x40.png"
								mode="widthFix"></image>
							<text>￥{{item.minPrice}}</text>
						</view> 
					</view>
					<view class="but" :class="{'active':true}">
						<text class="submit" >
							立即购买
						</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import TabBar from "@/components/public/TabBar2";
	export default {
		data() {
			return {
				tabList: [{
					name: '推荐',
					value: 0
				}],
				current: 0,
				itemStyle: {
					'font-size': '40rpx',
					'min-width': '120rpx',
					'z-index': '2',
					'color': '#121212'
				},
				itemStyle2: {
					'font-size': '40rpx',
					'min-width': '120rpx',
					'z-index': '2',
					'color': '#FFF'
				},
				firstTabList: [{
					name: '正在发售',
					value: 0
				}, {
					name: '历史发售',
					value: 1
				}],
				firstCurrent: 0,
				firstBarStyle: {
					'background': '#6B63E5',
					'height': '8rpx',
					'width': '120rpx',
					'border-top-left-radius': '8rpx',
					'border-top-right-radius': '8rpx',
					'bottom': '-10rpx',
					'margin-left': '-40rpx',
					'z-index': '1'
				},
				firstItemStyle: {
					'font-size': '32rpx',
					'min-width': '120rpx',
					'z-index': '2',
					'color': '#6B63E5'
				},
				pageNum: 1,
				activityList: [],
				isShowNav: false,
				firstList: [],
				historyList: [],
				seriesList:[],
				lastOrderList:[],
				tabs: [1,0],
				swiperCurrent:0,
				sun:0,
				articleList:[],
				bannersList:[0,0],
				svgBody:`<svg width="3340" height="315" viewBox="0 0 3340 315" fill="none"
												xmlns="http://www.w3.org/2000/svg" class="rail_sizing"></svg>
											<svg class="rail_mask">
												<clipPath id="contentTitle" clipPathUnits="objectBoundingBox">
													<path
														d="M0.02,0.982 C0.025,0.996,0.03,1,0.035,1 C0.039,1,0.044,0.999,0.048,0.991 C0.052,0.982,0.055,0.972,0.058,0.96 V0.428 H0.031 V0.605 H0.044 V0.816 C0.042,0.819,0.041,0.822,0.04,0.823 C0.039,0.823,0.037,0.824,0.036,0.824 C0.033,0.824,0.03,0.819,0.027,0.81 C0.025,0.801,0.023,0.784,0.021,0.761 C0.02,0.737,0.019,0.704,0.018,0.663 C0.017,0.621,0.017,0.568,0.017,0.503 C0.017,0.442,0.017,0.391,0.018,0.351 C0.019,0.309,0.02,0.277,0.022,0.253 C0.023,0.227,0.025,0.21,0.028,0.201 C0.03,0.19,0.033,0.185,0.037,0.185 C0.04,0.185,0.043,0.189,0.046,0.195 C0.049,0.202,0.052,0.211,0.055,0.225 V0.038 C0.054,0.033,0.052,0.028,0.051,0.024 C0.049,0.02,0.048,0.016,0.046,0.013 C0.044,0.01,0.043,0.008,0.041,0.006 C0.039,0.004,0.037,0.003,0.035,0.003 C0.03,0.003,0.025,0.012,0.021,0.028 C0.016,0.045,0.013,0.073,0.01,0.112 C0.006,0.152,0.004,0.204,0.002,0.268 C0.001,0.331,0,0.41,0,0.503 C0,0.605,0.001,0.689,0.002,0.754 C0.004,0.818,0.006,0.869,0.009,0.907 C0.012,0.943,0.016,0.968,0.02,0.982 M0.456,0.982 C0.46,0.996,0.465,1,0.47,1 C0.473,1,0.477,0.999,0.48,0.992 C0.483,0.985,0.485,0.975,0.487,0.963 V0.781 C0.485,0.794,0.482,0.803,0.479,0.81 C0.476,0.816,0.474,0.818,0.471,0.818 C0.468,0.818,0.465,0.814,0.463,0.804 C0.46,0.795,0.458,0.779,0.457,0.755 C0.455,0.731,0.454,0.699,0.454,0.659 C0.453,0.618,0.453,0.565,0.453,0.502 C0.453,0.442,0.453,0.392,0.454,0.352 C0.454,0.312,0.455,0.28,0.457,0.255 C0.458,0.23,0.46,0.212,0.463,0.202 C0.465,0.191,0.468,0.185,0.471,0.185 C0.474,0.185,0.477,0.189,0.479,0.195 C0.482,0.201,0.484,0.209,0.487,0.219 V0.035 C0.482,0.014,0.477,0.003,0.47,0.003 C0.465,0.003,0.46,0.012,0.456,0.028 C0.452,0.045,0.448,0.073,0.445,0.112 C0.442,0.152,0.44,0.204,0.438,0.268 C0.436,0.331,0.436,0.41,0.436,0.503 C0.436,0.605,0.437,0.689,0.438,0.754 C0.44,0.818,0.442,0.869,0.445,0.907 C0.448,0.943,0.452,0.968,0.456,0.982 M0.53,1 C0.524,1,0.52,0.996,0.516,0.981 C0.512,0.966,0.508,0.94,0.506,0.902 C0.503,0.864,0.501,0.813,0.499,0.748 C0.498,0.684,0.497,0.602,0.497,0.503 C0.497,0.404,0.498,0.323,0.499,0.258 C0.501,0.194,0.503,0.143,0.506,0.105 C0.508,0.067,0.512,0.041,0.516,0.026 C0.52,0.011,0.524,0.003,0.53,0.003 C0.535,0.003,0.539,0.011,0.543,0.026 C0.547,0.041,0.551,0.067,0.553,0.105 C0.556,0.143,0.558,0.194,0.56,0.258 C0.561,0.323,0.562,0.404,0.562,0.503 C0.562,0.602,0.561,0.684,0.56,0.748 C0.558,0.813,0.556,0.864,0.553,0.902 C0.551,0.94,0.547,0.966,0.543,0.981 C0.539,0.996,0.535,1,0.53,1 M0.53,0.825 C0.533,0.825,0.535,0.82,0.537,0.809 C0.54,0.797,0.541,0.779,0.542,0.754 C0.544,0.729,0.544,0.696,0.545,0.654 C0.545,0.613,0.545,0.563,0.545,0.503 C0.545,0.444,0.545,0.394,0.545,0.353 C0.544,0.311,0.544,0.277,0.542,0.251 C0.541,0.225,0.54,0.206,0.537,0.195 C0.535,0.183,0.533,0.177,0.53,0.177 C0.526,0.177,0.524,0.183,0.522,0.195 C0.519,0.206,0.518,0.225,0.517,0.251 C0.516,0.277,0.515,0.311,0.514,0.353 C0.514,0.394,0.514,0.444,0.514,0.503 C0.514,0.563,0.514,0.613,0.514,0.654 C0.515,0.696,0.516,0.729,0.517,0.754 C0.518,0.779,0.519,0.797,0.522,0.809 C0.524,0.82,0.526,0.825,0.53,0.825 M0.599,0.017 H0.576 V0.989 H0.591 V0.25 H0.592 L0.616,0.989 H0.637 V0.017 H0.622 V0.712 H0.622 L0.599,0.017 M0.7,0.204 H0.681 V0.989 H0.666 V0.204 H0.647 V0.017 H0.7 V0.204 M0.725,0.017 H0.71 V0.989 H0.725 V0.017 M0.741,0.017 H0.765 L0.787,0.712 H0.787 V0.017 H0.803 V0.989 H0.781 L0.757,0.25 H0.756 V0.989 H0.741 V0.017 M0.863,0.989 V0.804 H0.834 V0.58 H0.86 V0.402 H0.834 V0.202 H0.862 V0.017 H0.818 V0.989 H0.863 M0.876,0.017 H0.9 L0.922,0.712 H0.922 V0.017 H0.938 V0.989 H0.916 L0.892,0.25 H0.891 V0.989 H0.876 V0.017 M1,0.204 H0.981 V0.989 H0.966 V0.204 H0.947 V0.017 H1 V0.204 M0.384,0.604 L0.358,0.017 H0.377 L0.392,0.405 H0.392 L0.408,0.017 H0.426 L0.4,0.6 V0.989 H0.384 V0.604 M0.327,0.017 H0.303 V0.989 H0.319 V0.69 H0.327 C0.332,0.69,0.337,0.684,0.34,0.673 C0.344,0.661,0.347,0.641,0.349,0.614 C0.351,0.587,0.353,0.552,0.354,0.509 C0.355,0.465,0.355,0.411,0.355,0.348 C0.355,0.289,0.355,0.239,0.354,0.198 C0.353,0.156,0.351,0.122,0.349,0.096 C0.347,0.069,0.344,0.049,0.341,0.037 C0.337,0.024,0.333,0.017,0.327,0.017 M0.332,0.512 C0.33,0.516,0.328,0.519,0.325,0.519 H0.319 V0.185 H0.325 C0.328,0.185,0.33,0.188,0.332,0.194 C0.334,0.199,0.335,0.209,0.336,0.222 C0.337,0.235,0.338,0.252,0.339,0.274 C0.339,0.295,0.34,0.321,0.34,0.352 C0.34,0.385,0.339,0.412,0.339,0.433 C0.338,0.454,0.337,0.471,0.336,0.484 C0.335,0.497,0.334,0.506,0.332,0.512 M0.213,0.017 H0.238 L0.25,0.565 H0.251 L0.263,0.017 H0.287 V0.989 H0.272 V0.225 H0.272 L0.258,0.806 H0.242 L0.229,0.225 H0.228 V0.989 H0.213 V0.017 M0.155,0.986 C0.159,0.998,0.163,1,0.168,1 C0.172,1,0.177,0.997,0.18,0.985 C0.184,0.972,0.187,0.952,0.19,0.925 C0.192,0.898,0.194,0.863,0.196,0.821 C0.197,0.778,0.198,0.727,0.198,0.667 V0.017 H0.182 V0.643 C0.182,0.67,0.182,0.695,0.181,0.718 C0.181,0.739,0.18,0.758,0.179,0.774 C0.178,0.788,0.177,0.8,0.175,0.809 C0.173,0.817,0.171,0.821,0.168,0.821 C0.165,0.821,0.163,0.817,0.161,0.81 C0.159,0.802,0.158,0.791,0.156,0.776 C0.155,0.761,0.155,0.743,0.154,0.72 C0.154,0.698,0.154,0.672,0.154,0.643 V0.017 H0.138 V0.667 C0.138,0.732,0.139,0.785,0.14,0.828 C0.141,0.871,0.143,0.906,0.146,0.932 C0.148,0.957,0.151,0.975,0.155,0.986 M0.113,0.989 L0.096,0.635 H0.088 V0.989 H0.073 V0.017 H0.097 C0.102,0.017,0.106,0.023,0.109,0.034 C0.113,0.045,0.116,0.063,0.118,0.087 C0.12,0.112,0.122,0.143,0.123,0.183 C0.124,0.222,0.124,0.269,0.124,0.325 C0.124,0.407,0.123,0.47,0.121,0.516 C0.119,0.562,0.116,0.594,0.111,0.612 L0.131,0.989 H0.113 M0.095,0.477 C0.097,0.477,0.099,0.475,0.101,0.471 C0.103,0.466,0.105,0.458,0.106,0.447 C0.107,0.436,0.108,0.421,0.108,0.401 C0.109,0.381,0.109,0.356,0.109,0.325 C0.109,0.296,0.109,0.273,0.108,0.255 C0.108,0.237,0.107,0.222,0.106,0.212 C0.105,0.202,0.103,0.195,0.101,0.191 C0.099,0.187,0.097,0.185,0.095,0.185 H0.088 V0.477 H0.095">
													</path>
												</clipPath>
											</svg>`
			}
		},
		onLoad() {
			uni.hideTabBar();
			this.change(this.current)
		},
		onHide() {
		  //销毁定时
		  if (this.timeout) {
		    console.log("销毁定时器")
		    clearTimeout(this.timeout)
		    this.timeout = null
		  }
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					if (this.current == 0) {
						this.getList()
						//推荐
					}else if(this.current == 1){
						//首发
						if(this.firstCurrent == 0){
							this.get_first_list()
						}else{
							this.get_history_list()
						}
					}else if(this.current == 2){
						//公告
						this.get_articleList()
					}else{
						//活动
						this.getActivityList()
					}
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.change(this.current)
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 500);
		},
		onPageScroll(res) {
			if (res.scrollTop > 100) {
				this.isShowNav = true;
			} else {
				this.isShowNav = false;
			}
		},
		methods: {
			change(index) {
				this.current = index
				this.pageNum = 1
				this.isFooter= true
				this.isRequest = false
				if (index == 0) {
					this.seriesList=[]
					this.getList()
					// this.get_banner()
					//推荐
				}else if(index == 1){
					//首发
					if(this.firstCurrent == 0){
						this.firstList = []
						this.get_first_list()
					}else{
						this.historyList = []
						this.get_history_list()
					}
				}else if(index == 2){
					//公告
					this.articleList = []
					this.get_articleList()
				}else{
					//活动
					this.activityList = []
					this.getActivityList()
				}
				
			},
			firstChange(index) {
				this.firstList = []
				this.historyList = []
				this.firstCurrent = index
				this.pageNum = 1
				if (index == 0) {
					this.get_first_list()
				} else {
					this.get_history_list()
				}
			},
			//发售
			async get_first_list() {
				this.isRequest = true
				let res = await this.$api.bzlSaleList({
					type: 1,
					pageNum: this.pageNum,
					pageSize: 20,
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.firstList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//历史
			async get_history_list() {
				let res = await this.$api.bzlSaleList({
					pageNum: this.pageNum,
					pageSize: 20,
					type: 2
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.historyList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getActivityList() {
				this.isLoadding = true
				this.isRequest = true
				let res = await this.$api.java_activityNewActivityPageList({
					pageNum: this.pageNum,
					pageSize: 20,
					platformType:'2'
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.activityList.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(item) {
				console.log(item.link.indexOf('?') != -1)
				if (this.token) {
					if (item.link.indexOf('?') != -1) {
						window.location.href =
							`${item.link}&platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					} else {
						window.location.href =
							`${item.link}?platform=${this.platform}&token=${this.token}&contract_address=${this.contract_address}`
					}
				} else {
					// #ifdef APP
					this.$Router.push({
						name: "webView",
						params: {
							url: item.link,
						}
					})
					// #endif
					// #ifdef H5
					window.location.href = item.link
					// #endif
				}
			},
			nav_seriesList(item) {
				if(!item.ctid){
					return
				}
				if (item.ctid) {
					this.$Router.push({
						name: 'storeShop',
						params: {
							ctid: item.ctid
						}
					})	
				}
			},
			//倒计时结束
			first_end(){
				this.firstChange(this.firstCurrent)
			},
			//推荐
			async getList() {
				const {
					status,
					result
				} = await this.$api.java_marketRecommendList({
					marketTabId: 160,
					pageNum: this.pageNum,
					platformType:'2'
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if (this.seriesList == "") {
							this.isLoadingStatus = 2
						}
					} else {
						if(result.list.length<14){
							this.isFooter = false
						}
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
						console.log(this.seriesList)
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}, 
			async get_lastOrderList() {
				let res = await this.$api.lastOrderList({
			   
				});
				if (res.status.code == 0) {
					this.lastOrderList = res.result
					if(!this.timeout){
						this.timeout = setTimeout(()=>{
							this.get_lastOrderList()
						},15000)
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//获取banner
			async get_banner() {
				let res = await this.$api.java_pgc_tab1({
					businessLine:2
				});
				if (res.status.code == 0) {
					this.bannersList= res.result.banners
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async get_articleList() {
				let res = await this.$api.java_officialArticleList({
					pageNum:this.pageNum,
					platformType:2
				});
				if (res.status.code == 0) {
					this.isLoadding = false
					this.isRequest = false
					console.log("没数据咯")
					if (res.result.list == null || res.result.list == "") {
						console.log("没数据咯")
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							console.log(111)
							this.articleList.push(item)
						})
						console.log(this.articleList)
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			onSwiperChange(e){
				this.sun++
				this.swiperCurrent = e.detail.current
			},
			bannerClick() {
				const {
					needLogin,
					linkType,
					link
				} = this.bannersList[this.swiperCurrent]
				console.log(this.bannersList[this.swiperCurrent].link)
				if (linkType == 0) {
					return false
				} else {
					if (needLogin == 1) {
						if (uni.getStorageInfoSync('token')) {
							// #ifdef APP
							this.$Router.push({
								name: "webView",
								params: {
									url: link,
								}
							})
							// #endif
							// #ifdef H5
							window.location.href = link
							// #endif
						} else {
							this.$Router.push({
								name: "mainLogin"
							})
						}
					} else {
						// #ifdef APP
						this.$Router.push({
							name: "webView",
							params: {
								url: link,
							}
						})
						// #endif
						// #ifdef H5
						window.location.href = link
						// #endif
					}
				}
			},
			nav_gonggao(item){
				this.$Router.push({
					name:"officialDetail",
					params:{
						id:item.id
					}
				})
			}
		},
		components: {
			TabBar
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #F6F4F7;
	}
	@import "base.css";
	::v-deep .uni-swiper-wrapper{
		height:500rpx !important;
	}
	::v-deep .uni-swiper-dots-horizontal{
		bottom:50rpx !important;
	}
	::v-deep .uni-swiper-dot{
		width:8rpx !important;
		height:4rpx !important;
		border-radius:8rpx !important;
	}
	::v-deep .uni-swiper-dot-active{
		width:24rpx !important;
	}
	.body {
		min-height: 100vh;
		background: #F6F4F7;
		padding-bottom: 200rpx;
		.position_top {
			position: fixed;
			top: 0rpx;
			left: 0;
			width: 100%;
			z-index: 99;
			/* #ifdef APP */
			padding: 60rpx 0rpx 0rpx 0rpx;
			/* #endif */
			/* #ifdef H5 */
			padding: 40rpx 0rpx 0rpx 0rpx;
			/* #endif */
			&.bg {
				background-color: rgba(255, 255, 255, 0.75);
				transition: background-color 0.5s ease-in-out;
				backdrop-filter: blur(64rpx);
			}
			&.black_bg{
				background-color: rgba(0, 0, 0, 0.75);
				transition: background-color 0.5s ease-in-out;
				backdrop-filter: blur(64rpx);
			}

			.head_view {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-top: 26rpx;
				padding: 0rpx 40rpx;

				.sousuo {
					width: 64rpx;
					height: 64rpx;

					image {
						width: 64rpx;
						height: 64rpx;
					}
				}
			}
		}

		.recommend {
			.header_view {
				height: 522rpx;
				position: relative;
				.swiper-item{
					height: 522rpx;
					position: relative;
					.item{
						width:100%;
						height: 522rpx;
						background-color:#000;
						position: relative;
					}
					.bannerText{
						color:#fff;
						font-size:68rpx;
						position: absolute;
						top:0rpx;
						left:0rpx;
						right:0rpx;
						margin:174rpx auto 0rpx auto;
						text-align: center;
						width:500rpx;
						height:160rpx;
						line-height: 84rpx;
						z-index: 29;
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
				.bg_image{
					width:100%;
					position: absolute;
					bottom:0;
					left:0;
					image{
						width:100%;
						height:170rpx;
					}
				}
				.bg_long{
					width:100%;
					position: absolute;
					bottom:40rpx;
					left:0;
					height:290rpx;
					overflow: hidden;
					position: relative;
					.frame {
					    width: 1410rpx; /* 长图的总宽度 */
					    height: 290rpx;
					    background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241204/2b50ba44b74c39becd42a7a898119a1a_1410x288.png'); /* 替换为你的长图路径 */
						background-size: 100% 100%; /* 或者使用 contain，根据需求选择 */
					    background-repeat: no-repeat;
						transform: translateX(52rpx); /* 第一帧 */
						&.left{
							animation: switchFrames 1.4s forwards; /* 动画持续时间和循环次数 */
						}
						&.right{
							animation: switchFrames2 1.4s forwards; /* 动画持续时间和循环次数 */
						}
					}
					
					@keyframes switchFrames {
					    0% {
					        transform: translateX(52rpx); /* 第一帧 */
					    }
					    100% {
					        transform: translateX(-750rpx); /* 第二帧，600rpx 是第一帧的宽度 */
					    }
					   
					}
					@keyframes switchFrames2 {
					    0% {
					        transform: translateX(-750rpx); /* 第一帧 */
					    }
					    100% {
					        transform: translateX(52rpx); /* 第二帧，600rpx 是第一帧的宽度 */
					    }
					   
					}
				}
			}
			.first_issue_view {
				padding: 32rpx;
			
				.first_item {
					width: 686rpx;
					padding: 32rpx;
					border-radius: 40rpx;
					background-color: #fff;
					margin-bottom: 16rpx;
			
					.cover {
						width: 622rpx;
						height: 622rpx;
						border-radius: 20rpx;
			
						image {
							width: 622rpx;
							height: 622rpx;
							border-radius: 20rpx;
						}
					}
			
					>.font_view {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin: 28rpx 0rpx;
			
						.title {
							font-size: 32rpx;
							color: #121212;
						}
			
						.right_font {
							display: flex;
							justify-content: flex-start;
							align-items: center;
			
							image {
								width: 40rpx;
								height: 40rpx;
								margin-right: 6rpx;
							}
			
							color:#6B63E5;
							font-size:32rpx;
						}
					}
			
					>.but {
						width: 100%;
						height: 96rpx;
						border-radius: 28rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: #FFF;
						border: 2rpx solid #939393;
			
						&.active {
							background-color: #6B63E5;
							border: none;
						}
			
						.time {
							color: #939393;
						}
			
						.submit {
							color: #fff;
						}
					}
				}
			}
			.collection_view {
				padding: 32rpx;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.item {
					border-radius: 32rpx;
					width: 332rpx;
					height: 450rpx;
					padding: 16rpx;
					background-color: #fff;
					margin-bottom: 16rpx;

					.cover {
						width: 300rpx;
						height: 300rpx;
						border-radius: 16rpx;

						image {
							width: 300rpx;
							height: 300rpx;
							border-radius: 16rpx;
						}
					}

					.title {
						font-weight: 500;
						font-size: 26rpx;
						color: #121212;
						line-height: 36rpx;
						margin-top: 12rpx;
					}

					.font_view {
						padding: 12rpx 0rpx 24rpx 0rpx;
					
						.title {
							width: 100%;
							font-size: 26rpx;
							font-weight: 600;
							color: #121212;
							line-height:36rpx;
							margin-top:0rpx;
						}
						.icon_price {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-top: 10rpx;
							.fudu{
								 width:124rpx;
								 border-radius:16rpx;
								 height:42rpx;
								 color:#52C41AA6;
								 font-size:24rpx;
								 background-color:#F6FFED;
								 display: flex;
								 justify-content: center;
								 align-items: center;
								 &.red{
									 color:rgba(255, 77, 79, 0.65);
									 background-color:#FFF1F0;
								 }
							}
							.left_huo {
								display: flex;
								justify-content: flex-start;
								align-items: center;
					
								image {
									width: 22rpx;
									height: 22rpx;
								}
							}
					
							.right_price {
								text-align:right;
								.label{
									color:#00000040;
									font-size:20rpx;
									line-height:26rpx;
									margin-bottom: 8rpx;
								}
								.price{
									color:#6B63E5;
									font-size:24rpx;
								}
							}
						}
					}
				}
			}
		}

		.first_issue {
			width: 100%;
			padding-top: 140rpx;
			position: relative;
			z-index: 10;

			.tab_view {
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.border {
				width: 100%;
				height: 2rpx;
				background-color: rgba(18, 18, 18, 0.08);
			}
			.history_item {
				padding: 32rpx;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.item {
					border-radius: 32rpx;
					width: 332rpx;
					padding: 16rpx;
					background-color: #fff;
					margin-bottom: 16rpx;

					.cover {
						width: 300rpx;
						height: 300rpx;
						border-radius: 16rpx;
						position: relative;

						.right_icon {
							background: linear-gradient(216.81deg, #141416 0%, rgba(20, 20, 22, 0.7) 100%);
							width: 64rpx;
							height: 44rpx;
							border-radius: 32rpx;
							font-size: 20rpx;
							color: #FFFFFF73;
							display: flex;
							justify-content: center;
							align-items: center;
							box-shadow: 0rpx 8rpx 12rpx -32rpx #00000040;
							position: absolute;
							right: 24rpx;
							top: 16rpx;
							z-index: 1;
						}

						.mask {
							width: 300rpx;
							height: 300rpx;
							position: absolute;
							right: 0rpx;
							top: 0rpx;
							z-index: 2;
							background: #00000066;
							border-radius: 16rpx;
						}

						image {
							width: 300rpx;
							height: 300rpx;
							border-radius: 16rpx;
						}
					}

					.title {
						font-weight: 500;
						font-size: 26rpx;
						color: #121212;
						line-height: 36rpx;
						margin-top: 12rpx;
						width: 100%;
					}

					.font_view {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.status {
							width: 124rpx;
							border-radius: 16rpx;
							height: 42rpx;
							color: rgba(18, 18, 18, 0.65);
							font-size: 24rpx;
							background-color: #F0F2F6;
							display: flex;
							justify-content: center;
							align-items: center;

							.circle {
								width: 12rpx;
								height: 12rpx;
								border-radius: 50%;
								background: #293345;
								margin-right: 8rpx;
								display: flex;
								justify-content: center;
								align-items: center;
							}
						}

						.right {
							text-align: right;

							.label {
								color: #00000040;
								font-size: 20rpx;
								line-height: 26rpx;
								margin-bottom: 8rpx;
							}

							.price {
								color: #6B63E5;
								font-size: 24rpx;
							}
						}
					}
				}
			}
		}

		.notice {
			padding-top: 140rpx;
			padding: 140rpx 32rpx;
			position: relative;
			z-index: 10;

			.notice_view {
				padding: 136rpx 16rpx 16rpx 16rpx;
				width: 686rpx;
				height: 328rpx;
				border-radius: 32rpx;
				border: 4rpx solid #6B63E5;
				margin-bottom: 16rpx;
				background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20241128/f01abd711ff22339c8a592a324072a42_400x191.png);
				background-size: 100% 100%;

				.view_text {
					padding: 32rpx;
					background-color: #FCFCFD;
					border-radius: 16rpx;
					height: 168rpx;

					.title {
						color: #121212;
						font-size: 36rpx;
						line-height: 48rpx;
						margin-bottom: 16rpx;
						width: 100%;
					}

					.day {
						font-size: 24rpx;
						color: #353945;
					}
				}

			}
		}

		.activity {
			padding-top: 140rpx;

			.content {
				width: 100%;
				padding-top: 20rpx;
			}

			.list {
				width: 678rpx;
				box-sizing: border-box;
				background-size: cover;
				margin: 0 auto 20rpx auto;
				position: relative;
				background-color: #fff;
				padding: 16rpx;
				border-radius: 32rpx;
				.online{
					 width:148rpx;
					 height:40rpx;
					 border-radius:12rpx;
					 display: flex;
					 justify-content: center;
					 align-items: center;
					 font-size:24rpx;
					 position: absolute;
					 left:24rpx;
					 top:24rpx;
					 z-index:10;
					 >.yuan{
						 width:12rpx;
						 height: 12rpx;
						 border-radius:50%;
						 margin-right:4rpx;
					 }
					 &.status0{
						 background-color:#FFFBE6;
						 >.yuan{
						 	background-color:#F8B500;	
						 }
					 				 
					 }
					 &.status1{
						 background-color:#F6FFED;
					 	>.yuan{
					 		background-color:#52C41A;	
					 	}			 
					 }
					 &.status2{
						 background-color:#F0F2F6;
						>.yuan{
							background-color:#293345;	
						}
					 }
				}
				.img {
					width: 100%;
					height: 326rpx;
					margin-bottom: 16rpx;
					background: #fff;
					border-radius: 16rpx;

					>image {
						width: 100%;
						height: 100%;
						border-radius: 16rpx;
					}
				}

				.tit {
					font-weight: 400;
					font-size: 28rpx;
					color: #363636;
					padding: 0 20rpx;
					margin-bottom: 10rpx;
					box-sizing: border-box;
				}

				.btn {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 20rpx;
					box-sizing: border-box;
					font-weight: 400;
					font-size: 24rpx;
					color: #121212;

					>view:nth-child(2) {
						width: 180rpx;
						height: 60 rpx;

						>image {
							width: 100%;
							height: 100%;
						}
					}
				}

				.nav_button {
					width: 180rpx;
					height: 60rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					border: 2rpx solid #6B63E5;
					color: #6B63E5;
					font-size: 24rpx;
					font-weight: 700;
					border-radius: 16rpx;
				}
			}
		}
	}
	.null_body {
		.null {
	
			.img {
				display: flex;
				justify-content: center;
	
				image {
					width: 242rpx;
				}
			}
	
		}
	
		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
	
		width:100%;
		height: 60vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>