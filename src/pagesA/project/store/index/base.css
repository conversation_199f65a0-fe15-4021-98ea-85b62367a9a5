*,
*::after,
*::before {
	box-sizing: border-box;
}

:root {
	font-size: 26rpx;
	--color-text: #f0f3f8;
	--color-bg: #000;
	--color-link: #f0f3f8;
	--color-link-hover: #b7bed3;
	--page-padding: 3rem 1rem;
}

body {
	margin: 0;
	color: var(--color-text);
	background-color: var(--color-bg);
	font-family: "Reddit Sans", -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial, sans-serif;
	font-optical-sizing: auto;
	font-weight: 550;
	font-style: normal;
	letter-spacing: 0.02em;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.content {
	padding: var(--page-padding);
	display: flex;
	flex-direction: column;
	width: 100vw;
	min-height: 522rpx;
	overflow: hidden;
	justify-content: center;
	align-items: center;
	position: relative;
	
}

@media screen and (min-width: 53em) {
	body {
		--page-padding: 2rem 3rem;
	}

	.frame {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: grid;
		flex-direction: unset;
		grid-template-columns: auto auto 1fr;
		grid-template-rows: auto auto;
		align-content: space-between;
		grid-template-areas: 'title buttons sponsor' 'tags hire sub';
	}

	.frame #cdawrap, .frame__sub {
		justify-self: end;
	}

	.frame__title {
		margin: 0;
	}

	.frame__tags {
		margin: 0;
	}
}

/* Darkroom by Kaploom®Creative House */
/* https://darkroom.kaploom.com/ */

.sprite {
    visibility: hidden;
    position: absolute;
    width: 0rpx;
    height: 0rpx;
    overflow: hidden;
}

.emoji {
	display: inline-block;
    vertical-align: middle;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    isolation: isolate;
}

.emoji svg {
	display: block;
    width: 0.9rem;
    height: 0.9rem;
}

.logo {
	width: auto;
	height: 0.85rem;
}

/* Screen Reader */
.screen-reader-text {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    margin: 0;
    padding: 0;
    width: 2rpx;
    height: 2rpx;
    border: 0;
}

/* Rail */

.rail {
	position: absolute;
	inset: 0;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	width: 100%;
	overflow: hidden;
	pointer-events: none;
}

.rail_container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.rail_sizing {
	position: relative;
	opacity: 0;
	height: auto;
	width: 420vw;

	@media screen and (min-width: 53em) {
		width: 232vw;
	}
}

.rail_clip {
	position: absolute;
	inset: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	-webkit-clip-path: url("#contentTitle");
	clip-path: url("#contentTitle");
	animation: clip-anim 20s linear infinite;
	background-color:#f0f3f8;
}

.rail_color {
	position: absolute;
	inset: 0;
	height: 100%;
	width: 100%;
	background-color: #0c0c0e;
	animation: color-anim 20s linear infinite;
}

.rail_heading {
	position: absolute;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.rail_gradients {
	display: flex;
	align-items: center;
	justify-content: center;
	backface-visibility: hidden;
	width: 100vw;
	height: 100%;
	transform: scale(1.75);

	@media screen and (min-width: 53em) {
		transform: scale(1);
	}
}

.rail_gradient {
	position: absolute;
	width: 52vw;
	height: 52vw;
	min-width: 52vw;
	min-height: 52vw;
}

.rail_gradient.-core {
	transform: translate(-7vw, calc(-2.3vw + 100vh));
	animation: intro-core 3s cubic-bezier(.04,1.15,0.4,.99) 0.5s forwards;
}

.rail_gradient.-pro {
	transform: translate(7vw, calc(5vw + 100vh));
	animation: intro-pro 2.75s cubic-bezier(.04,1.15,0.4,.99) 0.75s forwards;
}
.rail_gradient.-max {
	transform: translate(7vw, calc(5vw + 100vh));
	animation: intro-max 2.75s cubic-bezier(.04,1.15,0.4,.99) 0.75s forwards;
}
@keyframes clip-anim {
	from {
		transform: translateX(0%);
   }
	to {
		transform: translateX(-50%);
   }
}

@keyframes color-anim {
	from {
		transform: translateX(0%);
   }
	to {
		transform: translateX(50%);
   }
}

/* Boxes */

.boxes {
	position: absolute;
	inset: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	transform: scale(1.3);

	@media screen and (min-width: 53em) {
		transform: scale(0.5);
	}
}

.box {
	position: absolute;
}

.box_container {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
}

.box_gradient {
	position: absolute;
	width: 180%;
	height: 180%;
	opacity: 0.4;
}

.box_image {
	position: relative;
	height: auto;
}

.box.-core {
	transform: translate(-7vw, calc(-2.3vw + 100vh));
	animation: intro-core 3s cubic-bezier(.04,1.15,0.4,.99) 0.5s forwards;
}

.box.-core .box_container {
	animation: float-core 4s ease-in-out 0s alternate;
	animation-iteration-count: infinite;
}

.box.-core .box_image {
	width: 26vw;
	margin-top:30px;
}

.box.-core .box_gradient {
	transform: translateX(-2vw);
}

.box.-pro {
	transform: translate(17vw, calc(5vw + 100vh));
	animation: intro-pro 2.75s cubic-bezier(.04,1.15,0.4,.99) 0.75s forwards;
}

.box.-pro .box_container {
	animation: float-pro 3s ease-in-out 0s alternate;
	animation-iteration-count: infinite;
	margin-top:30px;
}

.box.-pro .box_image {
	width: 22vw;
	transform: scaleX(-1);
	margin-left:60rpx;
}

.box.-pro .box_gradient {
	transform: translate(2vw, 2vw);
}


.box.-max {
	transform: translate(-7vw, calc(-2.3vw + 100vh));
	animation: intro-max 3s cubic-bezier(.04,1.15,0.4,.99) 0.5s forwards;
}

.box.-max .box_container {
	animation: float-max 4s ease-in-out 0s alternate;
	animation-iteration-count: infinite;
}

.box.-max .box_image {
	width: 26vw;
	margin-top:70rpx;
	margin-right:100rpx;
}

.box.-max .box_gradient {
	transform: translateX(-2vw);
}
@keyframes float-core {
	from {
		transform: translateY(0%);
   }
	to {
		transform: translateY(5%);
   }
}
@keyframes float-pro {
	from {
		transform: translateY(4%);
   }
	to {
		transform: translateY(0%);
   }
}

@keyframes intro-core {
	from {
		transform: translate(-7vw, calc(-2.3vw + 100vh));
   }
	to {
		transform: translate(-7vw, -2.3vw);
   }
}
@keyframes intro-pro {
	from {
		transform: translate(7vw, calc(5vw + 100vh));
   }
	to {
		transform: translate(7vw, 5vw);
   }
}
@keyframes intro-max {
	from {
			transform: translate(-7vw, calc(-2.3vw + 100vh));
	}
	to {
			transform: translate(-7vw, -2.3vw);
	}
}