<template>
	<view class="main">
		<view class="blur"></view>
		<view class="position_top " :class="{'bg':isShowNav}">
			<view class="barHeight"></view>
			<view class="head_view">
				<view class="title" @tap="userInfo()">
					商城
				</view>
				<view class="sousuo" @click="getLoading()">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20241128/0904586d6ece8e739c78c3b9880e197d_64x64.png" mode="widthFix"></image>
				</view>
			</view>
		</view>
		<view class="content padding_lr" :style="{'padding-top':`${120+height}rpx`}">
			<scroll-view ref="scrollView" :refresher-threshold="50" refresher-background="transparent" scroll-top="scrollTop" class="scroll-Y" scroll-y :refresher-triggered="triggered"
				refresher-default-style="none" :refresher-enabled="true" @refresherrefresh="refresher" 
				@scrolltolower="lower">
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view class="collection" :style="{'padding-bottom':`${paddingBottom}rpx`}" v-show="seriesList!=''&&isLoadingStatus == 1">
				<view class="li" v-for="(item,index) in seriesList" @tap="nav_seriesList(item)">
					<view class="bg">
						<view class="cover">
							<image :src="item.cover.src" mode="aspectFill"></image>
							<!-- <view class="left_bottom_icon">
								流通{{item.activeNum}}份
							</view> -->
							<!-- <view class="tuishi" v-if="item.isExitMarket==1">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240310/89a70b6d15a2a87fcc8fd4628db673c2_274x274.png"
									mode="widthFix"></image>
							</view> -->
							<!-- <view class="right_active">
								<view class="li_view" v-for="(itemm,index) in item.tagList"
									:style="{'background':`url(${itemm.tagImage})`,'backgroundSize':'100% 100%'}">
									{{itemm.tagName}}
								</view>
							</view> -->
						</view>
						<!-- <view class="new_icon" v-if="item.newFlag==1">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240507/50e1f2e8af60b46eae85f74fd6c554b2_121x40.png"
								mode="widthFix"></image>
						</view> -->
						<!-- <view class="ji_icon" v-if="item.tag>6">
							<image :src="`../../../static/imgs/public/kun/list_ji_${item.tag}.png`" mode="heightFix">
							</image>
						</view> -->
						<!-- <view class="dan_icon" v-else>
							<image :src="`../../../static/imgs/public/kun/list_dan_${item.tag}.png`" mode="heightFix">
							</image>
						</view> -->
					</view>
					<view class="font_view">
						<view class="title oneOver">
							{{item.title}}
						</view>
						<view class="flex icon_price">
							<view class="">
								<view class="fudu " :class="{'red':item.increase&&item.increase >= 0}" v-if="item.increase">
									{{ item.increase&&item.increase >= 0 ? `+${item.increase}` : `${item.increase}` }}%
								</view>
							</view>
							<view class="right_price">
								<view class="right" v-if="item.minPrice>0">
									<view class="label">
										价格
									</view>
									<view class="price">
										￥{{item.minPrice}}
									</view>
								</view>
								<!-- <view v-if="item.minPrice>0">
									<text>￥</text>
									{{item.minPrice}}
									<text class="qi">起</text>
								</view>
								<view v-else style="color:#121212;">
									<text>{{item.isLimitPrice===1?"限价:￥":"￥"}}</text>
									<text>{{item.isLimitPrice===1?item.limitPrice:"—"}}</text>
								</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="null_body" v-show="seriesList==''&&isLoadingStatus == 2">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241203/730826fff97336a1bed5197ad60e2682_280x304.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						空空如也
					</view>
				</view>
			</view>
			<view slot="refresher">
				<view class="loadding">
					<view class="gif">
						<view class="loading" id="loading1"></view>
					</view>
				</view>
			</view>
			<!-- #ifdef APP -->
				<view class="footerText" v-show="!isFooter&&platform=='ios'&&seriesList!=''">我也是有底线的~</view>
			<!-- #endif -->
			</scroll-view>
		</view>
		<u-modal class="model" width="600" v-model="isModel" :show-title="true" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject" :mask-close-able="true">
			<view class="colse" @click="isModel=false">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="title">
				小火苗表示藏品火爆程度
			</view>
			<view class="introduce">
				<view>1个小火苗表示较前一日上涨>=10%</view>
				<view>2个小火苗表示较前一日上涨>=20%</view>
				<view>3个小火苗表示较前一日上涨>=30%</view>
				<view>4个小火苗表示较前一日上涨>=40%</view>
				<view>5个小火苗表示较前一日上涨>=50%</view>
			</view>
		</u-modal>
		<TabBar :initialActiveIndex="1"></TabBar>
	</view>
</template>
<script>
	import api from '@/common/api/index.js';
	import TabBar from "@/components/public/TabBar2";
	import * as head from "@/static/lottie/head/head.json";
	export default {
		data() {
			return {
				height: "",
				title: "",
				scrollFlag: true,
				tabList: [],
				seriesList: [],
				show: true,
				pageNum: 1,
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				current: 0,
				barStyle: {
					'background': '#6B63E5',
					'height': '8rpx',
					'border-top-left-radius':'8rpx',
					'border-top-right-radius':'8rpx',
					'bottom': '-10rpx',
					'z-index': '1'
				},
				itemStyle: {
					'font-size': '32rpx',
					'min-width': '120rpx',
					'z-index': '2'
				},
				option: {
					data: head,
					loop: false,
					autoplay: false
				},
				marketTabId: "",
				triggered: false,
				refresherState: true,
				showAnimation: false,
				animationInstance: null,
				isLoadingStatus: 0, //0 加载中  1 正常载入  2无数据
				appDisabled:false,
				platform:"",
				paddingBottom:100,
				isBv:1,
				isModel:false,
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600'
				},
				isShowNav: false,
			}
		},
		onPageScroll(res) {
			if (res.scrollTop > 50) {
				this.isShowNav = true;
			} else {
				this.isShowNav = false;
			}
		},
		onLoad(options) {
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					console.log('当前平台？？？？？', res.statusBarHeight)
					_this.height = res.statusBarHeight * 2
					_this.platform = res.platform
					// #ifdef APP
					if(res.platform=='ios'){
						_this.paddingBottom = 400
					}
					// #endif
					console.log(_this.height)
				}
			});
			// #ifdef APP
				this.appDisabled = true
			// #endif
			console.log(uni.getStorageSync('isBv'))
			if(uni.getStorageSync('isBv')){
				this.isBv = uni.getStorageSync('isBv')
			}
			this.getTab(() => {
				if (this.tabList.length > 0) {
					this.marketTabId = this.tabList[0].marketTabId
				}
				this.getList()
			})
		},
		// onPullDownRefresh() {
		// 	setTimeout(() => {
		// 		if (this.marketTabId) {
		// 			this.seriesList = []
		// 			this.pageNum = 1
		// 			this.isFooter = true
		// 			this.getList()
		// 		}
		// 		uni.stopPullDownRefresh(); //停止下拉刷新动画
		// 	}, 1000);
		// },
		onReachBottom() {
			// if (this.isFooter) {
			// 	if (this.isRequest == false) {
			// 		this.getList()
			// 	} else {
			// 		console.log("请求超时，已经拦截")
			// 	}
			// } else {
			// 	console.log("已经到底了")
			// }
		},
		methods: {
			change(index) {
				this.current = index
				this.marketTabId = this.tabList[index].value
				this.seriesList = []
				this.pageNum = 1
				this.isLoadingStatus = 0
				this.isFooter = true
				this.getList()
			},
			search() {
				this.pageNum = 1
				this.getList()
			},
			clear() {
				this.title = ""
				this.pageNum = 1
				this.getList()
			},
			async getTab(callback) {
				this.isLoadingStatus = 0
				const {
					status,
					result
				} = await api.java_recommendTabList({
					moduleId:7
				});
				if (status.code === 0) {
					if(result.list == null){
						this.scrollFlag=false
						this.isLoadingStatus=2
						setTimeout(() => {
							this.triggered = false
						}, 1000)
						return
					}
					const tabData = result.list.map((item) => {
						return {
							...item,
							name: item.title,
							value: item.marketTabId,
							// isFire: item.fire == 1 ? true : false,
							// xxx: new Date().getTime() // 本地测试tab数据变动
						}
					})
					
					const oldTabData = JSON.stringify(this.tabList);
					const newTabData = JSON.stringify(tabData);
					
					const hasChange = oldTabData != newTabData;
					
					if(hasChange) {
						/*
							数据发生变动时, scrollFlag 必须先修改为false, 销毁 uTabs 组件
							然后再修改为 true, 重新用新数据渲染 uTabs 组件
							否则 uTabs 组件底部滑块会错位 
						*/
						this.scrollFlag = false;
						this.tabList = tabData;
					}
					
					setTimeout(() => {
						this.scrollFlag = true
						callback && callback(hasChange);
					}, 1000)
					
				} else if (status.code == 1002) {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.pushTab({
							name: "storeMainLogin",
						})
					}, 1500);
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_search() {
				this.pageNum = 1
				this.type = null
				this.getList()
			},
			nav_details(item) {
				console.log(item.id)
				this.$router.push({
					name: "webView",
					params: {
						url: 'http://web-test.nftcn.com/h5/#/pagesA/project/official/detail_art?id=' + item.id
					}
				})
			},
			nav_to(name) {
				this.$Router.push({
					name
				})
			},
			async getList() {
				// this.isLoadingStatus = 0
				const {
					status,
					result
				} = await this.$api.java_marketRecommendList({
					marketTabId: 160,
					pageNum: this.pageNum,
					platformType:'2'
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if (this.seriesList == "") {
							this.isLoadingStatus = 2
						}
					} else {
						if(result.list.length<14){
							this.isFooter = false
						}
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
						console.log(this.seriesList)
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}, 
			nav_seriesList(item) {
				if(!item.ctid){
					return
				}
				if (item.ctid) {
					this.$Router.push({
						name: 'storeShop',
						params: {
							ctid: item.ctid
						}
					})
				}
			},
			lower() {
				console.log("触底了")
				if (this.isFooter) {
					if (this.isRequest == false) {
						this.getList()
					} else {
						console.log("请求超时，已经拦截")
					}
				} else {
					console.log("已经到底了")
				}
			},
			scroll(e) {
				console.log('下拉了2')
			},
			refresher() {
				this.triggered = true
				
				this.seriesList = []
				console.log('下拉了1')
					
				this.getTab((hasChange) => {
					this.triggered = false 
					if(hasChange) {
						this.change(0)
					} else {
						this.change(this.current)
					}
				})
				 
			},
			async userInfo() {
				let res = await this.$api.userInfo({});
				if (res.status.code == 0) {
					this.$Router.push({
						name: 'search'
					})
				} else if (res.status.code == 1002) {
					setTimeout(() => {
						this.$Router.push({
							name: 'mainLogin',
						})
					}, 1500)
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			getLoading(){
				if(this.isBv == 1){
					this.isBv = 6
				}else{
					this.isBv = 1
				}
				uni.setStorageSync('isBv',this.isBv)
				this.seriesList = []
				this.getTab((hasChange) => {
					if(hasChange) {
						this.change(0)
					} else {
						this.change(this.current)
					}
				})
			}
		},
		components: {
			TabBar
		},
	}
</script>
<style lang="scss" scoped>
	.text{
		color:#121212;
	}
	.padding_lr {
		padding: 0rpx 32rpx;
	}
	
	.head_bg {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 100%;
		height: 500rpx;
		z-index: -1;
		background-color: #121212;
		will-change: transform;
		transform: translateZ(0);
	}

	.main {
		flex: 1;
		padding-bottom: 250rpx;
		background: #F6F4F7;
		min-height:100vh;
	}

	.head_title {
		height: 170rpx;
		// padding:86rpx 0 0 0;
	}

	.title_1 {
		color: #141414;
		font-weight: 600;
		font-size: 44rpx;
	}

	.content {
	}

	.position_top {
		position: fixed;
		top: 0rpx;
		left: 0;
		width: 100%;
		z-index: 99;
		padding: 40rpx 0rpx 0rpx 0rpx;
		&.bg {
			background-color: rgba(255, 255, 255, 0.75);
			transition: background-color 0.5s ease-in-out;
			backdrop-filter: blur(64rpx);
		}
	}
	.border{
		width:100%;
		height:1px;
		background-color: rgba(18, 18, 18, 0.08);
	}
	.head_view{
		display: flex; 
		justify-content: space-between;
		align-items: center;
		padding-top:26rpx;
		padding:0rpx 40rpx;
		>.title{
			font-size:40rpx;
			line-height:56rpx;
			color:#121212;
		}
		.sousuo{
			width:64rpx;
			height:64rpx;
			image{
				width:64rpx;
				height:64rpx;
			}
		} 
	}
	

	.tabbar_view {
		margin-top: 30rpx;
		padding:0rpx 40rpx;
	}

	.list_view {
		width: 638rpx;
		margin-top: 40rpx;
	}

	.list_li {
		display: flex;
		flex-direction: row;
		align-items: center;
		background-color: #F5F5F5;
		padding: 32rpx;
		border-radius: 24rpx;
		margin-bottom: 40rpx;
	}

	.left_img {
		margin: 0 40rpx 0 0;
	}

	.right_font {
		&_title {
			color: #141414;
			font-size: 38rpx;
			font-weight: 600;
		}
	}

	.sub_title {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.time {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.collection {
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		/* #ifdef APP */
			padding-bottom: 400rpx;
		/* #endif */
		/* #ifdef H5 */
			padding-bottom: 100rpx;
		/* #endif */

		.li {
			width: 332rpx;
			height: 450rpx;
			background-color: #FFFFFF;
			margin-bottom: 16rpx;
			border-radius: 32rpx;
			padding: 16rpx;

			.bg {
				position: relative;
				width: 300rpx;
				height:300rpx;
				.cover {
					position: absolute;
					top: 0rpx;
					left: 0rpx;
					width: 300rpx;
					height: 300rpx;
					border-radius: 20rpx;

					>image {
						width: 300rpx;
						height: 300rpx;
						border-radius: 20rpx;
					}

					.left_bottom_icon {
						position: absolute;
						bottom: 0;
						left: -2rpx;
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240104/79eb4cb92625dd836b2749f657f1437d_140x44.png);
						background-size: 100% 100%;
						width: 140rpx;
						height: 44rpx;
						border-radius: 0px 30rpx 0px 30rpx;
						text-align: center;
						font-size: 20rpx;
						color: #121212;
						display: flex;
						justify-content: center;
						align-items: center;
					}

					.right_top_icon {
						position: absolute;
						right: 0rpx;
						top: 0rpx;
						width: 110rpx;
						height: 44rpx;

						image {
							width: 110rpx;
							height: 44rpx;
						}
					}

					.tuishi {
						position: absolute;
						right: 0rpx;
						bottom: 0rpx;
						width: 137rpx;
						height: 137rpx;

						image {
							width: 137rpx;
							height: 137rpx;
						}
					}
				}
			}

			.font_view {
				padding: 12rpx 0rpx 24rpx 0rpx;

				.title {
					width: 100%;
					font-size: 26rpx;
					font-weight: 600;
					color: #121212;
					line-height:36rpx;
				}
				.icon_price {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 10rpx;
					.fudu{
						 width:124rpx;
						 border-radius:16rpx;
						 height:42rpx;
						 color:#52C41AA6;
						 font-size:24rpx;
						 background-color:#F6FFED;
						 display: flex;
						 justify-content: center;
						 align-items: center;
						 &.red{
							 color:rgba(255, 77, 79, 0.65);
							 background-color:#FFF1F0;
						 }
					}
					.left_huo {
						display: flex;
						justify-content: flex-start;
						align-items: center;

						image {
							width: 22rpx;
							height: 22rpx;
						}
					}

					.right_price {
						text-align:right;
						.label{
							color:#00000040;
							font-size:20rpx;
							line-height:26rpx;
							margin-bottom: 8rpx;
						}
						.price{
							color:#6B63E5;
							font-size:24rpx;
						}
					}
				}
			}
		}
	}

	.null_body {
		.null {

			.img {
				display: flex;
				justify-content: center;

				image {
					width: 242rpx;
				}
			}

		}

		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}

		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.scroll-Y {
		max-height: 75vh;
		.footerText{
			text-align: center;
			color: rgba(255, 255, 255, 0.5);
			font-size:24rpx;
		}
	}

	.new_icon {
		position: absolute;
		top: 11rpx;
		left: 15rpx;

		image {
			width: 60rpx;
		}
	}

	.ji_icon {
		position: absolute;
		width: 120rpx;
		height: 36rpx;
		top: 0;
		right: 0;
		left: 0;
		margin: 0 auto;
		text-align: center;
		font-size: 24rpx;
		color: #DEDEDE;
		line-height: 38rpx;
		overflow: hidden;
		image {
			width: 120rpx;
			height: 36rpx;
		}
	}

	.dan_icon {
		position: absolute;
		width: 120rpx;
		height: 36rpx;
		top: 0;
		right: 0;
		left: 0;
		margin: 0 auto;
		text-align: center;
		font-size: 24rpx;
		color: #DEDEDE;
		line-height: 38rpx;

		image {
			width: 120rpx;
			height: 36rpx;
		}
	}

	.right_active {
		position: absolute;
		right: 0rpx;
		top: 20rpx;

		.li_view {
			width: 70rpx;
			height: 30rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 20rpx;
			color: #121212;
			margin-top:10rpx;
		}
	}
	.model {
		// font-family: 'fonts';
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}
		.title{
			text-align: center;
			color: #121212;
			margin-bottom: 40rpx;
			font-size:30rpx;
		}
		.introduce {
			color: rgba(255, 255, 255, 0.5);
			font-size: 26rpx;
			text-align: center;  
			padding: 0rpx 40rpx 28rpx 40rpx;
			>view{
				margin-bottom:14rpx;
			}
		}
	}
</style>