<template>
	<view class="main">
		<view class="address space-between" >
			<view v-if="isHaveAddress" @click="open()">
				<u-icon name="map" class="icon-map" size="45" color="#999"></u-icon>
				<view class="add-info">
					<view class="ai-top">
						<span>{{defaultAddress.name}}</span>
						<span>{{defaultAddress.phone}}</span>
					</view>
					<view class="ai-bottom twoOver">
						{{defaultAddress.address_s}}
					</view>
				</view>
			</view>
			<view style="height: 120rpx; display: flex; align-items: center;" v-else @click="nav_addAddress()">
				<u-icon name="map" size="45" color="#999"></u-icon>
				<text>请添加收货地址</text>
			</view>
			<u-icon name="arrow-right" size="30" color="#999"></u-icon>
		</view>
		<view class="border_view"></view>
		<!-- 下方选择地址弹出层 -->
		<u-popup class="address-popup" v-model="isShowAddressPopup" mode="bottom">
			<view class="ap-title">选择地址<text @click="isShowAddressPopup = false">取消</text></view>
			<view class="content">
				<scroll-view scroll-y="true" style="height: 800rpx;">
					<view class="add-box space-between" v-for="(item, index) in addressList" :key="index">
						<view class="add-info" @click="checkAddress(item,index)">
							<view class="ai-top">
								<span>{{item.name}}</span>
								<span>{{item.phone}}</span>
							</view>
							<view class="ai-bottom twoOver">
								{{item.address_s}}
							</view>
						</view>
						<view class="ver-center" @click="nav_updataAddress(item)">
							<u-image src="https://cdn-lingjing.nftcn.com.cn/image/20241103/c89c39930ac83a4bfe10490621c15fe4_64x64.png" mode="aspectFit" width="26"
								height="26">
							</u-image>
						</view>
						<u-icon name="arrow-right" class="icon-arrow"></u-icon>
					</view>
				</scroll-view>
			</view>
			<view class="address_footer">
				<view class="add-btn" @click="nav_addAddress()">
					+&nbsp;新建收货地址
				</view>
			</view>
		</u-popup>
		<view class="info">
			<view class="i-top space-between">
				<u-image :src="info.coverList[0]" border-radius="12" width="200" height="200"></u-image>
				<view class="it-center">
					<view class="it_name oneOver">{{info.title}}</view>
					<view class="it_version">x{{info.num}}</view>
					<view class="it_price">￥{{info.price}}</view>
				</view>
			</view>
		</view>
		<view class="border_view"></view>
		<view class="pay">
			<view class="actual-pay">
				总额：<view class="strong">{{(info.num*info.price).toFixed(2)}}</view>
			</view>
			<view class="pay-btn" @click="submitPay()">提交订单</view>
		</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg_loading" style="
		  padding: 10rpx 20rpx 40rpx 20rpx;
		  text-align: center;
		  font-size: 26rpx;
		  line-height: 40rpx;
		">
				跳转登录中...
			</view>
		</u-modal>
		<u-modal v-model="isBalance" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg2">
					<view class="icon"></view>
					余额不足
				</view>
				<view class="modal-content">
					非常抱歉，您的当前余额不足以支付本次订单，
					请您充值余额或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isBalance = false">取消</view>
					<view class="img_reasale" @click="nav_pay()">去充值</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isPassword" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg2">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content"> 
					<p>请先设置支付密码</p>
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isPassword = false">取消</view>
					<view class="img_reasale" @click="SetPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isWin" border-radius="24" :show-title="false" :show-confirm-button="false" :mask-close-able="true">
			<view class="new-modal-content">
				<view class="success_img">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20241205/f9364902360e7f8f80774e893a1dd80a_64x64.png" mode="widthFix"></image>
				</view>
				<view class="modal-content" style="border: none;">
					购买成功
				</view>
			</view>
		</u-modal>
		<pay-popup :popup-show.sync="isPasswordImport"  title="支付密码"
			order-type="O"  @pay="finishPay" ref="payPopup" :mode="mode"  @createSuccess="createSuccess"/>
	</view>
</template>

<script>
	import fa from "@/common/public.js";
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		data() {
			return {
				isShowAddressPopup: false,
				num: 1,
				tid: "",
				detailsList: [],
				addressList: [],
				version: "",
				defaultAddress: [],
				addressId: "",
				isSubmit: true,
				isWarning: false,
				isError: false,
				isRegistration: false,
				isPayError: false,
				isPayErrorText: "",
				isWarningText: "",
				validate: "",
				ctId: "",
				verifyID: "",
				leapPlanLevel: 0,
				isLoadding: false,
				isLeapPlan: 0,
				renewList: [],
				checkend: 0,
				appUrl: "",
				itemId: "",
				isHaveAddress:false,
				isPasswordImport: false,
				isBalance:false,
				isPassword:false,
				mode:'pay',
				isWin:false
			};
		},
		components: {
			payPopup,
		},
		onLoad(option) {
			// this.getUser();
			this.info = uni.getStorageSync('activeOrderInfo')
			console.log(this.info)
		},
		onShow() {
			this.getAddress()
		},
		methods: {
			SetPayPassword() {
				this.isPassword = false
				this.mode = 'set'
				this.isPasswordImport = true
			},
			createSuccess(){
				this.isPasswordImport = false
				uni.setStorageSync("isSetTradePassword",1)
			},
			//吊起支付 输入密码
			async submitPay() {
				if(!this.addressId){
					uni.showToast({
						title: '您还没有添加地址哦~',
						icon: 'none',
						duration: 3000
					});
					return
				}
				if(uni.getStorageSync('isSetTradePassword') == 0){
					this.isPassword=true
					return 
				}
				this.mode = 'pay'
				this.isPasswordImport = true
				
			},
			async finishPay(e) {
				console.log("密码为", e)
				this.isPasswordImport = false
				this.submit(e)
			},
			async submit(tradePassword) {
				let res = await this.$api.shopPay({
					num:this.info.num,
					tradePassword,
					consigneeId:this.addressId,
					ctid:this.info.ctid
				});
				if (res.status.code == 0) {
					this.isWin = true
					setTimeout(()=>{
						this.isWin = false
						this.$Router.push({
							name:'userIndex'
						})
					},2500)
				}else if(res.status.code == 1090){
					this.isBalance = true
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			
			async getAddress() {
				let res = await this.$api.address({});
				if (res.status.code === 0) {
					if (res.result.list != "[]") {
						this.isHaveAddress = true;
						this.addressList = res.result.list
						this.addressList.forEach((item, index) => {
							item.address_s = item.district + item.address
						});
						this.defaultAddress = this.addressList[0];
						this.addressId = this.addressList[0].consigneeId
					} else {
						this.isHaveAddress = false;
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
				}
			},
			checkAddress(item, index) {
				console.log(item);
				this.defaultAddress = item;
				this.defaultAddress.address_s =
					this.defaultAddress.district +
					this.defaultAddress.address
				this.isShowAddressPopup = false;
				this.addressId = item.consigneeId
			},
			open() {
				this.isShowAddressPopup = true;
			},
			nav_addAddress() {
				this.$Router.push({
					name: "storeAddAddress",
					params: {
						type: "orderAddress",
					},
				});
			},
			nav_updataAddress(item) {
				uni.setStorageSync("addressList", item);
				this.$Router.push({
					name: "storeUpdataAddress",
					params: {
						addressId: item.consigneeId,
					},
				});
			},
			nav_pay(){
				// #ifdef H5
				let { origin } = window.location
				window.location.href = `${origin}/bzl/#/pagesA/project/security/pay?returnUrl=/pagesA/project/activity/shopOrder`
				// #endif
			}
		},
	};
</script>

<style lang="scss">
	page {
		background-color: #fff;
	}
	::v-deep .u-model{
		background-color:#fff !important;
	}
	.border_view {
		background-color: #EDEDED;
		height: 12rpx;
		width: 100%;
	}

	.modal-btn {
		padding: 10rpx 70rpx 50rpx 70rpx;

		.mb-confirm {
			height: 64rpx;
			line-height: 64rpx;
			text-align: center;
			border-radius: 37rpx;
			font-size: 30rpx;
			padding: 0rpx 20rpx;
		}

		.mb-confirm {
			color: #eee;
			background-color: #BB3835;

			&.black {
				background-color: #333333;
			}
		}
	}

	.ver-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40rpx;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
	}

	.address {
		margin-top: 30rpx;
		padding: 20rpx 42rpx;
		overflow: hidden;
		color: var(--secondary-front-color);
		background-color: #FFF;

		.icon-map {
			float: left;
			margin-top: -7rpx;
		}

		.add-info {
			height: 120rpx;
			width: 550rpx;
			padding-left: 15rpx;
			display: inline-flex;
			flex-direction: column;
			justify-content: space-between;

			.ai-top {
				span:nth-child(2) {
					margin-left: 20rpx;
					font-size: 28rpx;
				}
			}

			.ai-bottom {
				line-height: 35rpx;
				font-size: 28rpx;
			}
		}

	}

	.address-popup {
		.ap-title {
			height: 120rpx;
			line-height: 120rpx;
			text-align: center;
			font-weight: 700;
			color: #000;
			border-bottom: 1rpx solid #EDEDED;
			position: relative;

			>text {
				position: absolute;
				top: 0;
				right: 42rpx;
				color: #888;
				font-size: 28rpx;
				font-weight: 400;
			}
		}


	}

	.content {
		height: 800rpx;
		background-color: var(--dialog-bg-color);

		.add-box {
			height: 160rpx;
			padding: 20rpx 42rpx;
			border-bottom: 2rpx solid #EDEDED;
			color: #000;

			.add-info {
				width: 550rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.ai-top {
					font-weight: 700;

					span:nth-child(2) {
						margin-left: 20rpx;
						font-size: 28rpx;
					}
				}

				.ai-bottom {
					line-height: 35rpx;
					font-size: 28rpx;
					color:#999999;
				}
			}

			.icon-arrow {
				color: #999;
			}
		}
	}

	.address_footer {
		background-color: var(--dialog-bg-color);
		padding: 32rpx 40rpx 90rpx;

		.add-btn {
			height: 90rpx;
			line-height: 90rpx;
			font-size: 32rpx;
			color: #fff;
			background: #6B63E5;
			text-align: center;
			border-radius:12rpx;
		}
	}

	.info {
		padding: 40rpx 40rpx;

		.i-top {
			.uni-image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 30rpx;
			}

			.it-center {
				flex: 1;
				max-width: 420rpx;
				font-weight: 400;
				font-size: 24rpx;
				line-height: 24rpx;
				margin-left: 24rpx;

				.it_name {
					font-size: 28rpx;
					line-height: 34rpx;
					font-weight: 400;
					margin-bottom: 24rpx;
					width: 100%;
					color: #000000;
				}

				.it_version {
					text-align: right;
					color: #A6A6A6;
				}

				.it_msg {
					margin-top: 60rpx;
				}

				.it_price {
					margin-top: 90rpx;
					font-size: 34rpx;
					color: #6B63E5;
					font-weight: 600;
				}
			}

		}

		.i-center {
			height: 50rpx;
			line-height: 70rpx;
			margin: 10rpx 0 20rpx;
			font-size: 24rpx;
			font-weight: 700;
		}

		.i-bottom {
			font-size: 24rpx;
			font-weight: 700;

			.way_content {
				margin-top: 30rpx;
				color: #777777;
			}
		}
	}

	.pay {
		padding: 32rpx 40rpx 40rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.pay-btn {
			width: 240rpx;
			height: 100rpx;
			line-height: 100rpx;
			float: right;
			font-size: 28rpx;
			font-weight: 600;
			color: #EDEDED;
			background: #6B63E5;
			margin-left: 30rpx;
			text-align: center;
			border-radius: 24rpx;

			&[disabled],
			&:active {
				background-color: #333333;
			}
		}

		.actual-pay {
			display: flex;
			align-items: flex-end;
			float: right;
			font-size: 24rpx;
			color: #000000;
			.strong {
				color: #6B63E5;
				font-size: 44rpx;
				font-weight: 600;
			}

			span {
				margin-left: 10rpx;
				color: #BB3835;
			}
		}
	}

	.top_msg {
		background-color: #132727;
		color: #1FEDF0;
		font-size: 24rpx;
		width: 100%;
		padding: 24rpx 40rpx;
		line-height: 36rpx;
	}

	.isBatchBuy {
		padding: 30rpx;

		.buy_view {
			background-color: #464646;
			border-radius: 30rpx;
			padding: 40rpx 30rpx 30rpx 30rpx;

			.img {
				display: flex;
				justify-content: center;
				margin-bottom: 40rpx;

				image {
					width: 180rpx;
				}
			}

			.font {
				font-size: 28rpx;
				line-height: 42rpx;
				color: #D1D9D9;
				letter-spacing: 2rpx;

				text {
					color: #0CFFF1;
				}
			}
		}
	}

	.isRenew {
		padding: 30rpx;

		.title {
			font-size: 34rpx;
			color: #F8F8F8;
		}

		.cart_view {
			display: flex;
			justify-content: space-between;
			margin-top: 40rpx;
			align-items: center;

			.cart {
				background-color: #46454F;
				border-radius: 16rpx;
				width: 328rpx;
				padding: 30rpx;

				.name {
					color: #F8F8F8;
					font-size: 26rpx;
				}

				.domain {
					color: #BBBABA;
					font-size: 24rpx;
					margin-top: 20rpx;
				}

				.bottom {
					margin-top: 36rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.price {
						font-size: 32rpx;
						font-weight: 600;
						color: #F8F8F8;
					}

					.icon {
						image {
							width: 30rpx;
							height: 30rpx;
						}
					}
				}
			}
		}
	}

	.bg_border {
		background-color: #1E1E1E;
		height: 10rpx;
		width: 100%;
	}

	.new-modal-content {
		padding: 35rpx 40rpx;
		
		.success_img {
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 60rpx;
				height: 60rpx;
			}
		}

		.modal-content {
			padding: 35rpx 0rpx;
			border-bottom: 1rpx solid #EDEDED;
			font-size: 28rpx;
			color: #141414;
			text-align: center;

			p {
				margin-bottom: 20rpx;
			} 
			text{
				color:#6B63E5;
			}
		}

		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color: #141414;
			}

			.img_cancel {
				border: 1px solid #141414;
			}

			.img_reasale {
				background-color: #6B63E5;
				color: #fff;
			}
		}
	}
</style>