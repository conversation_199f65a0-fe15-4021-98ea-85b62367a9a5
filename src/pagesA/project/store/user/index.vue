<template>
	<view class="body">
		<view class="blur"></view>
		<view class="position_top" :class="{'bg':isShowNav}">
			
		</view>
		<view class="cen_view">
			<view class="user_cart">
				<view class="image">
					<image
						:src="info.avatar"
						mode="widthFix" @click="uploadImg"></image>
					<view class="icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241202/1c877d4dd68418c50a3b40b3391ebf4f_25x24.png"
							mode="widthFix"></image>
					</view>
				</view>
				<view class="title_view">
					<view class="title">
						{{info.name}}
					</view>
				</view>
			</view>
			<view class="cart_view">
				<view class="li" @click="nav_to('storeOrderIndex')">
					<view class="cart_icon">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241202/65c863756d3d99f52ccdba8e1eeb9564_96x96.png"
							mode="widthFix"></image>
					</view>
					<text>我的订单</text>
				</view>
				<view class="li" @click="nav_to('storeAddressManagement')">
					<view class="cart_icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/bc315ac6af7e9d270452c161cf594473_97x96.png" mode="widthFix"></image>
					</view>
					<text>地址管理</text>
				</view>
				<view class="li" @click="nav_to('myBalance')">
					<view class="cart_icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/fa5cd4bf0ebfa9c6b6a18f67cc4e38c0_97x96.png" mode="widthFix"></image>
					</view>
					<text>我的钱包</text>
				</view>
				<!-- <view class="li" @click="nav_to('comingSoon')">
					<view class="cart_icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/0063ad0d1eec3b70181917ee57c4402a_96x96.png" mode="widthFix"></image>
					</view>
					<text>邀请好友</text>
				</view> -->
			</view>
			<view class="list_view">
				<view class="item" @click="nav_to('baseInfo')">
					<view class="label">基本信息 </view>
					<view class="icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view>
				</view>
				<!-- <view class="item">
					<view class="label">登录密码 </view>
					<view class="icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view>
				</view> -->
				<view class="item" @click="nav_to('payManage')">
					<view class="label">支付密码 </view>
					<view class="icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="logout" @click="nav_logout">
				退出登录
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg_loading"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;color:#121212 !important;">
				跳转登录中...
			</view>
		</u-modal>
		<TabBar :initialActiveIndex="2"></TabBar>
	</view>
</template>

<script>
	import TabBar from "@/components/public/TabBar2";
	export default {
		data() {
			return {
				list: [],
				isLoadding: false,
				isShowNav: false,
				info:{},
				filePath: '', //裁剪返回的圖片
				imgType: 0, //裁剪的圖片類型
			}
		},
		components: {
			TabBar
		},
		onLoad() {
			this.get_info()
			uni.$on('uAvatarCropper', val => {
				this.filePath = val.path;
				this.imgType = val.actionType
				// console.log(val, 'val')
				this.uploadPic()
			})
			this.appVersion = uni.getSystemInfoSync().appVersion
		},
		onPageScroll(res) {
			if (res.scrollTop > 50) {
				this.isShowNav = true;
			} else {
				this.isShowNav = false;
			}
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.get_info()
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 500);
		},
		onReachBottom() {},
		methods: {
			nav_to(name,params){
				this.$Router.push({
					name,
					params
				})
			},
			copy() { //复制区块链地址
				uni.setClipboardData({
					data: this.info.contractAddress,
					success() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			},
			//登出
			nav_logout() {
				uni.removeStorageSync('token')
				this.$Router.push({
					name: 'storeMainLogin'
				})
			},
			async get_info() {
				let res = await this.$api.baseInfo({
				
				});
				if (res.status.code == 0) {
					this.info = res.result
					let certification;
					if (res.result.authStatus == 31 && res.result.authType == 1) {
						certification = 1
					} else if (res.result.authStatus == 31 && res.result.authType == 2) {
						certification = 2
					} else {
						certification = 0
					}
					uni.setStorageSync("certification", certification);
					uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword);
				} else if(res.status.code == 1002){
					this.isLoadding = true
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "storeMainLogin",
							// #ifdef H5
							params: {
								url: window.location.hash,
							},
							// #endif
						})
					}, 1500);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			uploadImg() { //裁剪头像
				console.log(1)
				this.$Router.push({
					name: 'u-avatar-cropper',
					params: {
						destWidth: 300,
						rectWidth: 300,
						fileType: 'jpg',
						actionType: 0
					}
				})
			},
			uploadPic() { //上传
				// #ifdef APP
				let url = `${getApp().globalData.apiUrl}osscenter/appApi/uploadImage`
				// #endif
				// #ifdef H5
				let url = process.env.VUE_APP_JAVA_UPLOADIMAGE
				// #endif
				console.log("url", url)
				uni.uploadFile({
					url,
					filePath: this.filePath,
					header: {
						'Authorization': uni.getStorageSync("token"),
					},
					name: 'image',
					complete: (res) => {
						if (this.imgType === '1') {
							this.setBackPic(JSON.parse(res.data).result.url)
						} else {
							this.setHeadPic(JSON.parse(res.data).result.url)
						}
					}
				});
			},
			async setHeadPic(url) {
				let res = await this.$api.java_userEditAvatar({
					avatar: url
				})
				if (res.status.code === 0) {
					uni.showToast({
						title: '头像设置成功',
						icon: 'none',
					})
					await this.get_info()
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none ',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-model {
		background-color: #fff !important;
	
		.text_msg {
			color: #FFFFFF !important;
		}
	}
	.sk-wave {
		border-top-color:#6B63E5;
		border-bottom-color: #6B63E5;
	}
	
	.sk-wave:before {
		border: 3px solid #6B63E5;
	}
	.body {
		padding-bottom: 200rpx;
		background: #F6F4F7;
		min-height:100vh;
		.position_top {
			position: fixed;
			top: 0rpx;
			left: 0;
			width: 100%;
			z-index: 99;
			/* #ifdef APP */
			padding: 60rpx 0rpx 0rpx 0rpx;
			/* #endif */
			/* #ifdef H5 */
			padding: 40rpx 0rpx 0rpx 0rpx;
			/* #endif */

			&.bg {
				background-color: rgba(255, 255, 255, 0.75);
				transition: background-color 0.5s ease-in-out;
				backdrop-filter: blur(64rpx);
			}

			.head_view {
				display: flex;
				justify-content: center;
				align-items: center;
				padding-top: 26rpx;
				padding: 0rpx 40rpx;

				>.title {
					font-size: 40rpx;
					line-height: 56rpx;
					color: #121212;
				}
			}
		}

		.cen_view {
			padding-top: 150rpx;

			.user_cart {
				width: 750rpx;
				height: 312rpx;
				
				.image {
					width: 164rpx;
					height: 164rpx;
					position: relative;
					margin: 0 auto;
					border-radius:64rpx;
					image {
						width: 164rpx;
						height: 164rpx;
						border-radius:64rpx;
					}

					.icon {
						position: absolute;
						right: -8rpx;
						bottom: 0rpx;
						width: 48rpx;
						height: 48rpx;

						image {
							width: 48rpx;
							height: 48rpx;
						}
					}
				}

				.title_view {
					margin-top: 32rpx;

					.title {
						font-size: 32rpx;
						font-weight: 600;
						text-align: center;
						color:rgba(18, 18, 18, 1);
					}

					.address {
						display: flex;
						justify-content: center;
						align-items: center;
						margin-top: 8rpx;
						font-size: 24rpx;
						color: #71727A;

						.copy {
							margin-left: 10rpx;

							image {
								width: 32rpx;
							}
						}
					}
				}
			}

			.cart_view {
				width: 586rpx;
				height: 186rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin:0 auto;  
				.li {
					width: 144rpx;
					height: 186rpx;
					border-radius: 32rpx;
					background-color:#fff;
					padding:24rpx;
					.cart_icon {
						width: 96rpx;
						height: 96rpx;
						margin-bottom:16rpx;
						image {
							width: 96rpx;
							height: 96rpx;
						}
					}

					text {
						font-size:24rpx;
						color:#6B63E5;
						
					}
				}
			}
			.list_view{
				background-color:#F7F4FF;
				border-radius:32rpx;
				width:686rpx;
				padding:16rpx 32rpx;
				min-height:300rpx;
				margin:28rpx auto;
				.item{
					height:92rpx;
					width: 622rpx;
					padding:0rpx 32rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					border-bottom:2rpx solid rgba(212, 214, 221, 0.5);
					.label{
						font-size:28rpx;
						color:#1F2024;
					}
					.num{
						color:#6B63E5;
						font-size:24rpx;
					}
					.icon{
						display: flex;
						justify-content: flex-end;
						.text{
							font-size:24rpx;
							color:#6B63E5;
							margin-right:10rpx;
						}
						image{
							width:24rpx;
							height:24rpx;
						}
					}
				}
			}
		}
		.logout{
			width:654rpx;
			height:96rpx;
			border: 2rpx solid #6B63E5;
			color:#6B63E5;
			font-size:32rpx;
			line-height:96rpx;
			text-align: center;
			margin:0rpx auto;
			border-radius:28rpx;
			margin-top:300rpx;
		}		
	}
</style>