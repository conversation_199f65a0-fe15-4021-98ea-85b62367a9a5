<template>
	<view class="body">
		<view class="blur"></view>
		<u-navbar back-icon-color="#121212" :border-bottom="false" title="基本信息" title-color="#121212"></u-navbar>
		<view class="list_view">
			<view class="item">
				<view class="label">昵称 </view>
				<view class="right">
					<view class="inp">
						<input type="text" v-model="info.name" :height="30" @blur="blur" @focus="focus" />
					</view>
					<view class="icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="item">
				<view class="label">实名认证 </view>
				<view class="right">
					<view class="active" v-if="info.authStatus==31">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/792453de00764c17a6be3b9f62f4b564_21x21.png" mode="widthFix"></image>
						<text>已实名</text>
					</view>
					<view class="weishiming" v-else-if="info.authStatus==30">
						审核中
					</view>
					<view class="weishiming" v-else @click="nav_realName()">
						去实名
					</view>
					<view class="icon" v-if="info.authStatus==30">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="item">
				<view class="label">微信 </view>
				<view class="right">
					<view class="wx_active" v-if="wechatAvatar">
						<image :src="wechatAvatar" mode="widthFix"></image>
						<text>已绑定</text>
					</view >
					<view class="weibangding" v-else>
						未绑定
					</view>
					<!-- <view class="icon" >
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20241202/c46f307ffcf89c1aec21140b68aa4e99_24x24.png" mode="widthFix"></image>
					</view> -->
				</view>
			</view>
			<view class="item">
				<view class="label">手机号 </view>
				<view class="icon">
					<view class="text">
						{{info.phone}}
					</view>
				</view>
			</view>
			<view class="item">
				<view class="label">邮箱 </view>
				<view class="icon">
					<view class="text">
						{{info.email}}
					</view>
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg_loading"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;color:#121212 !important;">
				跳转登录中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLoadding:false,
				info:{
					authStatus:''
				},
				wechatAvatar:''
			};
		},
		onLoad(){
			this.get_info()
			this.secretInfo()
		},
		methods:{
			async get_info() {
				let res = await this.$api.userInfo({
				
				});
				if (res.status.code == 0) {
					this.info = res.result
				} else if(res.status.code == 1002){
					this.isLoadding = true
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin",
							// #ifdef H5
							params: {
								url: window.location.hash,
							},
							// #endif
						})
					}, 1500);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			focus() { //昵称 获取焦点
				this.userName = this.info.name
			},
			async blur() { //昵称 失去焦点后
				if (this.userName == this.info.name) {
					return
				}
				let {
					status,
					result
				} = await this.$api.java_appUserEditNickName({
					name: this.info.name
				})
				if(status.code == 0){
					uni.showToast({
						title: '修改成功',
						icon: 'none'
					})
				}else{
					uni.showToast({
						title: status.msg,
						icon: 'none'
					})
				}
				
			},
			copy() { //复制区块链地址
				uni.setClipboardData({
					data: this.contractAddress,
					success() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			},
			//查询绑定微信
			async secretInfo() {
				let res = await this.$api.secretInfo({
			
				});
				if (res.status.code == 0) {
					this.wechatAvatar = res.result.wechatAvatar
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_realName(){
				this.$Router.push({
					name:'realName'
				})
			}
		}
	}
</script>

<style lang="scss">
	.body{
		padding:32rpx;
		background: #F6F4F7;
		min-height:100vh;
		.list_view{
			border-radius:32rpx;
			height:800rpx;
			position: relative;
			z-index:9;
			.item{
				height:104rpx;
				padding:0rpx 32rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom:2rpx solid rgba(212, 214, 221, 0.5);
				.label{
					font-size:28rpx;
					color:#1F2024;
				}
				.right{
					display: flex;
					justify-content: flex-end;
					align-items: center;
					.inp {
						text-align: right;
						color: rgba(18, 18, 18, 0.65);
						font-size:24rpx;
						margin-right:12rpx;
						input{
							font-size:24rpx;
						}
					}
					.wx_active{
						width: 140rpx;
						height: 44rpx;
						font-size:26rpx;
						line-height:40rpx;
						color:rgba(18, 18, 18, 0.65);;
						display: flex;
						justify-content: center;
						align-items: center;
						image{
							width:40rpx;
							height:40rpx;
							margin-right: 8rpx;
							border-radius:50%;
						}
					}
					.active{
						width: 140rpx;
						height: 44rpx;
						background-color:#fff;
						border-radius:22rpx;
						font-size:24rpx;
						color:#6B63E5;
						display: flex;
						justify-content: center;
						align-items: center;
						image{
							width:28rpx;
							height:28rpx;
							margin-right: 8rpx;
						}
					}
					.weibangding{
						color:rgba(18, 18, 18, 0.65);
						font-size:26rpx;
					}
					.weishiming{
						color:rgba(18, 18, 18, 0.65);
						font-size:24rpx;
					}
					.icon{
						display: flex;
						justify-content: flex-end;
						.text{
							font-size:24rpx;
							color:#6B63E5;
							margin-right:10rpx;
						}
						image{
							width:24rpx;
							height:24rpx;
						}
					}
				}
				.icon{
					display: flex;
					justify-content: flex-end;
					.text{
						font-size:24rpx;
						color:rgba(18, 18, 18, 0.65);
						margin-right:10rpx;
					}
					image{
						width:24rpx;
						height:24rpx;
					}
				}
				.num{
					color:#6B63E5;
					font-size:24rpx;
				}
			}
		}
	}
</style>
