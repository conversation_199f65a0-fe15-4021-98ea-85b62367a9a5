<template>
	<view class="main">
		<view class="blur"></view>
		<u-navbar back-icon-color="#121212" :background="{'backgroundColor':isShowNav?'rgba(255, 255, 255, 0.75)':'transparent'}" :border-bottom="false" title="我的订单" title-color="#121212" :customBack="nav_user">
		</u-navbar>
		<view class="subTab" :style="current == 3 ? 'padding-left:40rpx;' : ''">
			<u-tabs bg-color="transparent" inactive-color="rgba(18, 18, 18, 0.3)" active-color="rgba(107, 99, 229, 1)"
				:item-width="80" font-size="24" :bar-style="barStyle" :list="filteredTabList[current].item" :is-scroll="false"
				:current="subCurrent" @change="subsChange" :show-bar="false" />
		</view>
		<view v-if="current == 0 || current == 3 || current == 4" style="padding-top:50rpx;">
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view v-for="item in list" v-show="list.length > 0 && isLoadingStatus == 1">
				<BasicCard :current="current" :type="typeNum" :subCurrent="subCurrent" @closeOrder="closeOrder"
					:item='item'></BasicCard>
			</view>
			<view class="null_body" v-show="list.length <= 0 && isLoadingStatus == 2">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241203/730826fff97336a1bed5197ad60e2682_280x304.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						您还没有订单哦～
					</view>
				</view>
			</view>
		</view>
		<view v-else-if="current == 1">
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view v-for="(item, index) in list" :key="index" v-show="list.length > 0 && isLoadingStatus == 1">
				<!--  -->
				<basicMore :item="item" :subCurrent="subCurrent" @revocation="revocation"></basicMore>
			</view>
			<view class="null_body" v-show="list.length <= 0 && isLoadingStatus == 2">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241203/730826fff97336a1bed5197ad60e2682_280x304.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						您还没有订单哦～
					</view>
				</view>
			</view>
		</view>
		<view v-else-if="current == 2">
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view v-for="item in list" :key="item.biddingId" v-show="list.length > 0 && isLoadingStatus == 1">
				<basicJing :item="item" :subCurrent="subCurrent" @remove="remove"></basicJing>
			</view>
			<view class="null_body" v-show="list.length <= 0 && isLoadingStatus == 2">
				<view class="null">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20241203/730826fff97336a1bed5197ad60e2682_280x304.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						您还没有订单哦～
					</view>
				</view>
			</view>
		</view>

		<u-modal v-model="isShowMsg" :content-style="bgObject" :title-style="titleObject" border-radius="30"
			:show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					温馨提示
				</view>
				<view class="modal-content">
					当您超时未支付和主动取消订单总数超过3次，将在1小时内无法继续购买
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isShowMsg = false">取消</view>
					<view class="img_reasale" @click="submitCloseOrder()">确认</view>
				</view>
			</view>
		</u-modal>
		<u-calendar @change="dateChange" v-model="show" :mode="mode"></u-calendar>
	</view>
</template>
<script>
import BasicCard from "./components/basicCard.vue";
import basicJing from '@/components/basicJing/basicJing.vue'
import basicMore from '@/components/basicMore/basicMore.vue'
export default {
	name: "setting",
	components: {
		BasicCard,
		basicJing,
		basicMore
	},
	data() {
		return {

			search: "",
			inputStyle: {
				fontSize: "24rpx",
			},
			show: false,
			mode: "range",
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)'
			},
			tabList: [{
				name: "常规订单",
				item: [{
					name: "全部",
				},
				{
					name: "待付款",
				},
				{
					name: "待发货",
				},
				{
					name: "待收货",
				},
				{
					name: "已取消",
				},
				],
			},
			{
				name: "批量订单",
				item: [{
					name: "批量购买中",
				},
				{
					name: "已批量买到",
				},
				{
					name: ""
				},
				{
					name: ""
				}],
			},
			{
				name: "空投/销毁",
				item: [{
					name: "空投/兑换得到的",
				},
				{
					name: "已销毁的",
				},
				{
					name: ""
				},
				{
					name: ""
				}],
			},
			{
				name: "转售",
				item: []
			}
			],
			current: 0,
			subCurrent: 0,
			list: [],
			pageNum: 1,
			typeNum: 0, // 0-全部 1-已卖出 2-已买入 3-支付中 4-已取消 5批量购买 6-竞价购买 7-空投 8-销毁
			startDate: '',
			endDate: '',
			buyAmount: '0', //总买入
			rewardAmount: '0', //奖励金
			sellAmount: '0', //总卖出
			CountType: null, //统计type

			isFooter: true,
			isRequest: false,
			titleObject: {
				'background-color': '#FFF',
				'color': '#121212'
			},
			bgObject: {
				'background-color': '#FFF',
				'color': '#121212'
			},
			isShowMsg: false,
			isLoadingStatus: 0,
			showAuction:false,
			auctionTab:{
				name: "竞价订单",
				item: [{
					name: "竞价购买中",
				},
				{
					name: "已竞价买到",
				},
				{
					name: ""
				},
				{
					name: ""
				}],
			},
			isShowNav:false
		};
	},
	onPageScroll(res) {
		if (res.scrollTop > 100) {
			this.isShowNav = true;
		} else {
			this.isShowNav = false;
		}
	},
	onLoad() {
		this.pageNum = 1
		this.list = []
		let date = new Date()
		let year = date.getFullYear()
		// 获取实际的当前月份，而不是硬编码为6
		let month = date.getMonth() + 1
		let startYear = year // 初始化为当前年份，避免null
		let startMonth = null

		console.log('当前月份', month)
		// 计算起始月份，若当前月份减去6个月小于等于0，则向前借一年并计算真实起始月
		if (month - 6 <= 0) {
			startMonth = 12 + month - 6 // 借一年的月份数
			startYear = year - 1       // 年份减一
		} else {
			startMonth = month - 6      // 直接减去6得到起始月
		}

		// 对月份进行格式化，不足两位前面补0
		month = month < 10 ? '0' + month : month
		startMonth = startMonth < 10 ? '0' + startMonth : startMonth

		// 赋值给对应的变量
		this.endDate = `${year}-${month}`
		this.startDate = `${startYear}-${startMonth}`
		this.getList();
	},
	computed: {
	    filteredTabList() {
	      let list = [...this.tabList];
	      if (this.showAuction) {
	        // 插入竞价订单到第三位
	        list.splice(2, 0, this.auctionTab);
	      }
	      return list;
	    },
	  },
	onPullDownRefresh() {
		setTimeout(() => {
			this.list = []
			this.pageNum = 1
			if (this.current == 0) {
				// 常规
				this.typeNum = 0
				this.getList()
			} else if (this.current == 1) {
				// 批量
				this.subsChange(0)
			} else if (this.current == 2) {
				// 竞价
				this.type = null
				this.subCurrent = 0
				this.biddingList()
			} else if (this.current == 3) {
				// 空投/销毁
				this.typeNum = 7
				this.getList()
			} else if (this.current == 4) {
				//转售
				this.typeNum = 9
				this.getList()
			}

			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 1000);
	},
	onReachBottom() {
		if (this.isFooter) {
			if (this.isRequest == false) {
				if (this.current == 0) {
					// 常规
					this.getList()
				} else if (this.current == 1) {
					// 批量
					if (this.subCurrent == 0) {
						this.getDutyList()
					} else {
						this.getListV2()
					}
				} else if (this.current == 2) {
					// 竞价
					this.type = null
					if (this.subCurrent == 0) {
						this.biddingList()
					} else {
						this.typeNum = 6
						this.getList()
					}
				} else if (this.current == 3) {
					// 空投/销毁
					this.getList()
				}
			} else {
				console.log("请求超时，已经拦截")
			}
		} else {
			console.log("已经到底了")
		}
	},
	methods: {
		async getList() {
			let {
				result,
				status
			} = await this.$api.storeOrderList({
				pageNum: this.pageNum,
				pageSize: 10,
				keyword: this.search,
				startTime: this.startDate,
				endTime: this.endDate,
				type: this.typeNum // 0-全部 1-已卖出 2-已买入 3-支付中 4-已取消 6-竞价购买 7-空投 8-销毁
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						if (item.tid) {
							item.show = false
							item.tidd = new Array(item.tid.toString().length).fill('*').join('');
						}
						this.list.push(item)
					})
				}
			}
			console.log(result, '订单列表');
		},
		async biddingList() { //竞价购买中列表
			let {
				result,
				status
			} = await this.$api.biddingBuying({
				pageNum: this.pageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						if (item.tid) {
							item.show = false
							item.tidd = new Array(item.tid.toString().length).fill('*').join('');
						}
						this.list.push(item)
					})
				}
			}
		},
		async revocation(item) { //批量 撤销
			let {
				result,
				status
			} = await this.$api.java_targetCancel({
				dutyId: item.dutyId
			})
			if (status.code == 0) {
				this.pageNum = 1
				this.list = []
				uni.showToast({
					title: '撤销成功',
					icon: 'none'
				})
				this.getDutyList()
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async remove(val) { //撤销竞价购买中
			let {
				result,
				status
			} = await this.$api.revoke_bidding({
				biddingId: val
			})
			if (status.code == 0) {
				uni.showToast({
					title: '撤销成功',
					icon: 'none'
				})
				this.list = []
				this.pageNum = 1
				this.biddingList()
			}
		},
		async CountV2() { //常规订单统计
			let {
				result,
				status
			} = await this.$api.listCountV2({
				type: this.CountType, //0-全部 1-已卖出 2-已买入 3-支付中 4-已取消 6-竞价购买 7-空投 8-销毁
				startTime: this.startDate,
				endTime: this.endDate,
			})
			if (status.code == 0) {
				this.buyAmount = result.buyAmount
				this.rewardAmount = result.rewardAmount
				this.sellAmount = result.sellAmount
			}
		},
		async getListV2() { //已批量买到的
			let {
				result,
				status
			} = await this.$api.dutyOperateListV2({
				pageNum: this.pageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.list.push(item)
					})
				}
			}
		},
		async getDutyList() { //批量购买中
			let {
				result,
				status
			} = await this.$api.dutyList({
				pageNum: this.pageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == '') {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.list.push(item)
					})
				}
			}
		},
		change(index) {
			this.list = []
			this.pageNum = 1
			this.current = index;
			this.subCurrent = 0;
			this.isFooter = true
			this.isLoadingStatus = 0
			if (this.current == 0) {
				// 常规
				this.typeNum = 0
				this.getList()
			} else if (this.current == 1) {
				// 批量
				this.getDutyList()
			} else if (this.current == 2) {
				// 竞价
				this.type = null
				this.subCurrent = 0
				this.biddingList()
			} else if (this.current == 3) {
				// 空投/销毁
				this.typeNum = 7
				this.getList()
			} else if (this.current == 4) {
				// 转售
				this.typeNum = 9
				this.getList()
			}
		},
		subsChange(index) {
			console.error(index)
			this.subCurrent = index;
			this.list = []
			this.pageNum = 1
			this.isFooter = true
			this.isLoadingStatus = 0
			if (this.subCurrent == 0) {
				// 常规
				this.typeNum = index
				this.getList()
			} else if (this.subCurrent == 1) {
				this.list = []
				this.isLoadingStatus = 2
				this.$forceUpdate()
			} else if (this.subCurrent == 2) {
				this.list = []
				this.getList()
			} else if (this.subCurrent == 3) {
				this.isLoadingStatus = 2
				this.list = []
			} else if (this.subCurrent == 4) {
				this.isLoadingStatus = 2
				this.list = []
			}
		},
		searchClick() { //搜索
			// 待完善 
			// 常规 空投 已竞价买到  使用getList()
			// 批量/竞价中 单独使用
			this.pageNum = 1
			this.list = []
			this.isLoadingStatus = 0
			if (this.current == 0) { //常规
				this.getList()
			} else if (this.current == 1) { //批量
				if (this.subCurrent == 0) {
					this.getDutyList()
				} else {
					this.getListV2()
				}
			} else if (this.current == 2) { //竞价 
				if (this.subCurrent == 0) {
					this.biddingList()
				} else {
					this.getList()
				}
			} else if (this.current == 3) { //空投
				this.getList()
			} else if (this.current == 4) { //转售
				this.getList()
			}
		},
		handleClick() {
			this.show = !this.show;
		},
		dateChange(value) {
			this.endDate = value.endDate.slice(0, 7)
			this.startDate = value.startDate.slice(0, 7)
			this.list = []
			this.pageNum = 1
			this.isLoadingStatus = 0
			this.CountV2()
			this.getList()
		},
		closeOrder(item) {
			console.log(item)
			this.isShowMsg = true
			this.orderId = item.orderNo
		},
		async submitCloseOrder() {
			let res = await this.$api.java_order_cancel({
				orderId: this.orderId
			});
			if (res.status.code == 0) {
				uni.showToast({
					title: '连续锁单3次并取消，将被惩罚一小时内不能购买',
					icon: 'none',
					duration: 3000
				});
				uni.showLoading()
				setTimeout(() => {
					this.list = []
					this.pageNum = 1
					this.getList()
					this.isShowMsg = false
					uni.hideLoading()
				}, 1000)
			} else {
				this.isShowMsg = false
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async get_version() {
			let res = await this.$api.java_commonconfigInfo({
				name: 'ios_apple_pay_version',
			});
			if (res.status.code == 0) {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = res.result.value
				console.log(curV)
				console.log(reqV)
				if (curV == reqV) {
					this.showAuction = false
				} else {
					this.showAuction = true
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_user(){
			this.$Router.push({
				name:'userIndex'
			})
		}
	},
};
</script>

<style lang="scss" scoped>
	
	.u-drawer-bottom{
		background-color:#fff !important;
	}
.main {
	min-height:100vh;
	background: #F6F4F7;
	min-height:100vh;
	.search {
		padding: 0 36rpx;
		margin-bottom: 14rpx;
		// opacity: 0.5;
		font-size: 12rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 60rpx;
		position: relative;
		z-index: 9;
		.border{
			border:1px solid #C5C6CC;
			border-radius:16rpx;
			height:58srpx;
		}
		input {
			font-size: 24rpx;
		}
	}

	.date {
		width: 268rpx;
		height: 60rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-left: 30rpx;
		border:1px solid #C5C6CC;
		border-radius:16rpx;
		.item {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 120rpx;
			height: 50rpx;
			/** 文本1 */
			font-size: 24rpx;
			font-weight: 400;
			color: var(--default-color2);
		}

		.line {
			width: 16rpx;
			height: 2rpx;
			opacity: 1;
			background: var(--default-color1);
		}
	}
}

.tabs {
	width: 100%;
	margin-top: -6rpx;
	border-bottom: 1px solid rgba(83, 80, 93, 1);
}

.subTab {
	padding: 0rpx 0;
}

.totalPrice {
	// margin: 0 36rpx;
	margin-left: 36rpx;
	// width: 240rpx;
	width: fit-content;
	height: 56rpx;
	text-align: center;
	/** 文本1 */
	font-size: 22rpx;
	font-weight: 400;
	letter-spacing: 0px;
	color: #121212;
	border-radius: 33rpx;
	box-sizing: border-box;
	background:#fff;
	.totalPrice-bg {
		// width: 240rpx;
		width: 100%;
		border-radius: 33rpx;
		padding: 0 20rpx;
		display: flex;
		height: 50rpx;
		justify-content: center;
		align-items: center;
		color:var(--active-color1);
		box-sizing: border-box;
		border:1px solid var(--active-color1);
	}
}

.null_body {
	.null {
		.img {
			display: flex;
			justify-content: center;

			image {
				width: 242rpx;
			}
		}

	}

	.text {
		color: #A6A6A6;
		font-size: 28rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	width:100%;
	height: 40vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

.title_bg {
	font-size: 34rpx;
	font-weight: 600;
	width: 240rpx;
	position: relative;
	text-align: center;
	margin: 0 auto;
	margin-bottom: 10rpx;
	color: #121212;

	.icon {
		position: absolute;
		left: 0rpx;
		top: 16rpx;
		width: 240rpx;
		height: 8rpx;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
		background-size: 100%;
	}
}

.new-modal-content {
	padding: 35rpx 40rpx;
	background: var(--default-bg);

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 160rpx;
			height: 160rpx;
		}
	}

	.modal-content {
		padding: 35rpx 0rpx;
		border-bottom: 1rpx solid #F2F2F2;
		font-size: 28rpx;
		color: #121212;

	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 226rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 14rpx;
			color: #121212;
		}

		.img_cancel {
			border: 1px solid #fff;
			color:var(--primary-button-color);
			border:1px solid var(--primary-button-color);
		}

		.img_reasale {
			color: var(--default-color2);
			background: var(--primary-button-color);
			color:#fff;
		}
	}
}
</style>