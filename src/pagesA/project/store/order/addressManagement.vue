<template>
	<view class="main">
		<u-toast ref="uToast" />
		<u-navbar back-icon-color="#121212" :border-bottom="false"
			 title="我的地址" title-color="var(--default-color1);"
			title-bold>
		</u-navbar>
		<view class="item" v-for="(item, index) in addressList" :key="index">
			<view class="left">
				<view class="address">
					{{ item.address_s }}
				</view>
				<view class="info">
					<span>{{ item.name }}</span>
					<span>{{ item.phone }}</span>
				</view>
			</view>
			<view class="right" @click="nav_updataAddress(item)">
				<u-image src="https://cdn-lingjing.nftcn.com.cn/image/20241204/b35c2e91734c13e64e79928cbb7e5e2c_64x64.png" mode="widthFix" width="30"></u-image>
			</view>
		</view>
		<view class="footer" v-if="addressList.length">
			<button-bar class="button" @click='nav_addAddress()' text="添加新地址"></button-bar>
		</view>

		<view class="empty" v-if="isEmpty">
			<u-image width="48rpx" src="@/static/imgs/public/location.png" mode="widthFix"></u-image>
			<view class="title">您暂时没有添加的地址</view>
			<view class="des">添加地址信息，让您的下单流程更便捷</view>
			<button-bar class="button" @click='nav_addAddress()' text="添加新地址"></button-bar>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";

	export default {
		components: {
			ButtonBar
		},
		data() {
			return {
				addressList: [],
				isEmpty: false,
			}
		},
		onLoad() {
			this.getAddress()
		},
		methods: {
			nav_addAddress() {
				this.$Router.push({
					name: "storeAddAddress"
				})
			},
			nav_updataAddress(item) {
				uni.setStorageSync("addressList", item)
				this.$Router.push({
					name: "storeUpdataAddress"
				})
			},
			async getAddress() {
				let res = await this.$api.address({})
				if (res.status.code === 0) {
					this.addressList = res.result.list
					this.addressList.forEach(item => {
						item.address_s = item.district + item.address
					})
					if (!this.addressList.length) {
						this.isEmpty = true
					}
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						type: 'default',
					})
				}
				// console.log(JSON.parse(res.data))
			},
		}
	}
</script>

<style lang="scss" scoped>
	:root {
	   
		--main-front-color: #121212;
	
	}
	.main {
		min-height: 100vh;
		background-color:#fff;
		.empty {
			margin-top: 514rpx;
			margin-left: calc(50% - 275rpx);
			width: 550rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			text-align: center;

			.title {
				width: 100%;
				margin: 40rpx 0;
				color: #121212;
				font-size: 36rpx;
				font-weight: bold;
			}

			.des {
				width: 100%;
				color: #121212;
				font-size: 28rpx;
				margin-bottom: 60rpx;
			}

			.button {
				border-radius: 0;
				width: 100%;
				height: 80rpx;
				background: var(--main-bg-color);
				font-size: 28rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: #6b63e5;
				border: #6b63e5 solid 2rpx;
			}
		}

		.item {
			min-height: 166rpx;
			border-bottom:2rpx solid #F2F2F2;
			padding: 40rpx;
			color: #121212;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.left {
				width: 80%;

				.address {
					word-break: break-all;
					font-size: 28rpx;
					font-weight: bold;
					margin-bottom: 32rpx;
					line-height: 44rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
				}

				.info {
					font-size: 28rpx;
					color: rgba(18, 18, 18, 0.55);
					span {
						margin-right: 32rpx;
					}
				}
			}
		}

		.footer {
			position: fixed;
			bottom: 32rpx;
			left: 0rpx;
			margin: auto 40rpx;
			width: calc(100% - 80rpx);
			.button{
				border-radius:24rpx;
				background-image:none;
				background-color:#6b63e5;
				color:#fff;
			}
		}
	}

	.ver-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
	}

	.main {
		height: 100%;
		position: relative;
	}

	.add-box {
		height: 160rpx;
		padding: 20rpx 42rpx;
		background-color: #6b63e5;
		border-top: 3rpx solid #eee;

		.add-info {
			width: 400rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.ai-top {
				font-weight: 700;

				span:nth-child(2) {
					margin-left: 20rpx;
					font-size: 28rpx;
				}
			}

			.ai-bottom {
				line-height: 35rpx;
				font-size: 28rpx;
				color: #666;
			}
		}

		.add-right {
			display: flex;
			justify-content: flex-start;
			align-items: flex-end;
			font-size: 28rpx;
		}

		.icon-arrow {
			color: #999;
		}
	}

	.add-btn {
		height: 90rpx;
		line-height: 90rpx;
		position: absolute;
		bottom: 76rpx;
		left: 42rpx;
		right: 42rpx;
		font-size: 32rpx;
		color: #6b63e5;
		background-color: #333;
		&:active {
			color: #333;
			background-color: #6b63e5;
		}
	}
</style>
