<template>
	<view class="card">
		<view class="title">
			<!-- <view class="title-left" >
				<view class="title-text">竞价购买</view>
			</view> -->
			<view>
				<text>下单时间：{{ item.orderTime }}</text>
			</view>
			<view >
				<text > 支付时间：{{ item.orderTime }}</text>
			</view>
		</view>
		<view class="card-ctx">
			<image class="card-img" :src="item.cover" mode="aspectFill"></image>
			<view class="card-bottom">
				<view class="sub-top">
					<text class="subTitle">{{ item.title }}</text>
					<view class="subTitle-right" v-if="item.type == 3">
						<text class="price">￥{{ item.price }}</text>
						<u-button class="subTitle-btn" shape="circle" size="mini" @click="payment(item)">
							<text class="toPrice">去支付</text>
						</u-button>
					</view>
					<view class="subTitle-right" >
						<view class="subTitle-right-btn huise">
							<view class="dian"></view>
							<view class="calc">待发货</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
				</view>
				<!-- 已取消 去支付 -->
				<view class="sub-bottom">
					<view class="item" style="opacity: .5;">
						<view>数量：x{{item.num}}</view>
					</view>
					<view class="item" style="opacity: .5;">
						<view>收货人：{{item.consigneeName}}</view>
					</view>
					<view class="item" style="opacity: .5;">
						<view>收货人手机号：{{item.consigneePhone}}</view>
					</view>
					<view class="item" style="opacity: .5;">
						<view>详细地址：{{item.consigneeAddress}}</view>
					</view>
					<!--  <view class="item">
            哈希值：NA
            <image
              class="copy-img"
              src="../../../../static//imgs//public/copy.png"
              mode=" aspectFill"
            ></image
          ></view> -->
					<view class="item" v-if="current !== 4">
						<view class="flex" v-show="item.orderNo">
							订单号：{{ item.orderNo }}
							<image class="copy-img" src="@/static/imgs/public/copy.png" mode=" aspectFill"
								@click="copy(item.orderNo)">
							</image>
							<u-button class="subTitle-btn-close" shape="circle" size="mini" @click="closeOrder(item)"
								v-if="item.type == 3">
								<text class="toPrice">取消订单</text>
							</u-button>
						</view>
					</view>
					<view class="itemtype" v-if="current == 4">
						<view class="flex_zs">
							<view class="price">￥{{ item.price }}</view>
							<u-button class="subTitle-btn-close" v-show="item.status == 0 && item.sellBuy == 2"
								shape="circle" size="mini" @click="closeOrder(item)">
								<text class="toPrice">取消转售</text>
							</u-button>
							<!--  v-show="item.status == 0 && item.sellBuy == 2"  v-show="item.status == 0 && item.sellBuy == 1"-->
							<u-button v-show="item.status == 0 && item.sellBuy == 1" class="subTitle-btn-pay "
								hover-class="none" shape="circle" size="mini" @click="payment(item)">
								<text class="payment">去支付</text>
							</u-button>


						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Badge 数字角标
	 * @description 数字角标一般和其它控件（列表、9宫格等）配合使用，用于进行数量提示，默认为实心灰色背景
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=21
	 * @property {String} text 角标内容
	 * @property {String} type = [default|primary|success|warning|error] 颜色类型
	 * 	@value default 灰色
	 * 	@value primary 蓝色
	 * 	@value success 绿色
	 * 	@value warning 黄色
	 * 	@value error 红色
	 * @property {String} size = [normal|small] Badge 大小
	 * 	@value normal 一般尺寸
	 * 	@value small 小尺寸
	 * @property {String} inverted = [true|false] 是否无需背景颜色
	 * @event {Function} click 点击 Badge 触发事件
	 * @example <uni-badge text="1"></uni-badge>
	 */
	export default {
		name: "BasicCard",
		props: {
			current: {
				type: Number,
				default: "default",
			},
			subCurrent: {
				type: Number,
				default: "default",
			},

			item: {
				type: Array,
				default: []
			},
			type: {
				type: Number,
			}
			// type: {
			//   type: String,
			//   default: "default",
			// },
			// inverted: {
			//   type: Boolean,
			//   default: false,
			// },
			// text: {
			//   type: [String, Number],
			//   default: "",
			// },
			// size: {
			//   type: String,
			//   default: "normal",
			// },
		},
		data() {
			return {
				//   badgeStyle: "",
				appUrl: ""
			};
		},
		watch: {
			// text() {
			//   this.setStyle();
			// },
		},
		mounted() {
			console.log(this.item);
			// this.setStyle();
			this.appUrl = getApp().globalData.urlZf
		},
		methods: {
			end() {
				this.$refs.orderTime.style.display = 'none'
			},
			payment(item) {
				// #ifdef APP
				let url = `${this.appUrl}pagesA/project/order/payOrder?orderId=${item.orderNo}`
				console.log(url)
				this.$Router.push({
					name: "webView",
					params: {
						url,
					}
				})

				// #endif
				// #ifdef H5
				let {
					origin
				} = window.location
				window.location.href = `${origin}/bzl/#/pagesA/project/order/payOrder?orderId=${item.orderNo}`
				// #endif

			},
			copy(text) { //复制
				uni.setClipboardData({
					data: text,
					success() {
						uni.showToast({
							title: '已复制',
							icon: 'none'
						})
					}
				})
			},
			closeOrder(item) {
				this.$emit('closeOrder', item)
			},
			check(item) {
				console.log(item.show)
				item.show = !item.show
			}
		}
		//   methods: {
		//     setStyle() {
		//       this.badgeStyle = `width: ${String(this.text).length * 8 + 12}px`;
		//     },
		//     onClick() {
		//       this.$emit("click");
		//     },
		//   },
	};
</script>

<style lang="scss" scoped>
	::v-deep .u-countdown-colon {
		color: #fff !important;
	}

	.flex_zs {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 8rpx 0rpx;
		width: 100%;

		.price {
			color: #6B63E5;
			font-size: 28rpx;
			font-weight: 600;
		}

		.subTitle-btn-close {
			border-radius: 40rpx;
			width: 150rpx;
			border: 2rpx solid #6B63E5;
			line-height: 46rpx;
			text-align: center;
			height: 46rpx;
			color: #6B63E5;
			margin: 0;

			.toPrice {
				font-size: 18rpx;
			}
		}

		.subTitle-btn-pay {
			border-radius: 40rpx;
			width: 150rpx;
			line-height: 46rpx;
			text-align: center;
			height: 46rpx;
			// color: #363636;
			background: var(--primary-button-color);
			margin: 0;

			.payment {
				z-index: 1 !important;
				color: #121212 !important;
			}

			.toPrice {
				font-size: 18rpx;
			}
		}
	}

	.card {
		margin: 0 36rpx;
		color: #363636;
		margin-bottom: 40rpx;
		height: 258rpx;
		background: #fff;
		border-radius: 30rpx;
		overflow: hidden;
		position: relative;
		z-index: 99;

		.title {
			padding: 18rpx 32rpx;
			// opacity: 0.5;
			font-size: 18rpx;
			font-weight: 400;
			color: #121212;
			background: rgba(232, 230, 255, 0.2);
			display: flex;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title-left {
				height: 24rpx;
				border-radius: 4rpx;
				background: #6B63E5;
				font-size: 24rpx;
				font-weight: 700;
				margin-right: 20rpx;
				padding: 2rpx 0;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}

		.card-img {
			width: 148rpx;
			height: 148rpx;
			margin-right: 24rpx;
		}

		.title-text {
			transform: scale(0.8);
			font-size: 24rpx;
			/** 文本1 */
			color: rgba(37, 35, 45, 1);
		}

		.copy-img {
			width: 20rpx;
			height: 22rpx;
			margin-left: 10rpx;
		}

		.card-ctx {
			padding: 20rpx 20rpx 30rpx 30rpx;
			display: flex;
			justify-content: space-between;

			.subTitle {
				/** 文本1 */
				font-size: 24rpx;
				font-weight: 400;
				color: #363636;
				white-space: nowrap;
				/* 防止文本换行 */
				overflow: hidden;
				/* 隐藏超出容器的内容 */
				text-overflow: ellipsis;
				/* 显示省略号 */
				width: 140rpx;
			}

			.subTitle-right {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.text {
					color: #363636;
					font-size: 22rpx;
				}

				.price {
					margin-right: 20rpx;
					font-size: 28rpx;
					font-weight: 700;
					color: #6b63e5;
				}

				.subTitle-btn {
					border-radius: 40rpx;
					background: #6B63E5;
					width: 100rpx;

					.toPrice {
						font-size: 20rpx;
						color: #fff;
					}
				}
			}

			.card-bottom {
				flex: 1;
				height: 100%;
			}

			.sub-top {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 40rpx;
			}

			.sub-bottom {
				// opacity: 0.5;
				/** 文本1 */
				font-size: 20rpx;
				font-weight: 400;
				color: #363636;
				margin-top: 10rpx;

				.item {
					margin-bottom: 8rpx;

					.flex {
						display: flex;
						align-items: center;
						// justify-content: flex-start;
						margin: 14rpx 0rpx;
					}

					.show {
						image {
							width: 30rpx;
						}
					}
				}

				.itemtype {
					opacity: 1 !important;

					margin-bottom: 8rpx;

					.flex {
						display: flex;
						align-items: center;
						// justify-content: flex-start;
						justify-content: space-between;
						margin: 14rpx 0rpx;
					}

					.show {
						image {
							width: 30rpx;
						}
					}
				}
			}
		}
	}

	.subTitle-right-btn {
		width: 124rpx;
		height: 40rpx;
		border-radius: 12rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 9rpx;
		background: #E6F4FF;
		color:rgba(18, 18, 18, 0.65);
		.dian {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
		}

		&.default {
			background: rgba(230, 244, 255, 1);
			.dian{
				background: rgba(22, 119, 255, 1);
			}
		}

		&.blue {
			background: rgba(230, 244, 255, 1);
			.dian{
				background: rgba(22, 119, 255, 1);
			}
		}

		&.red {
			background: rgba(232, 230, 255, 1);
			.dian{
				background: rgba(107, 99, 229, 1);
			}
		}

		&.huise {
			background: rgba(240, 242, 246, 1);
			.dian{
				background: rgba(22, 119, 255, 1);
			}
		}

		.calc {
			transform: scale(0.8);
			font-size: 22rpx;
			/** 文本1 */
		}
	}

	.activeColor {
		color: #6B63E5;
	}

	.subTitle-btn-close {
		border-radius: 40rpx;
		width: 100rpx;
		border: 2rpx solid #fff;
		background-color: transparent;

		.toPrice {
			font-size: 18rpx;
			color: #363636;
		}
	}
</style>