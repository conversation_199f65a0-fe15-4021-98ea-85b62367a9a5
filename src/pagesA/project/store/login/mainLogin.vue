<template>
	<view class="index_body">
		<view class="blur"></view>
		<view>
			<u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end"
				 ref="uCode" @change="codeChange"></u-verification-code>
			<view class="index_body_text">
				登录或注册
			</view>
			<view class="index_body_box">
				<view class="index_body_content1">
					<!--    输入手机号-->
					<view class="top">
						<view class="right">
							<u-input class="modal-resale-input" v-model="phone" type="number" placeholder="请输入手机号"
								:maxlength="11" />
						</view>
					</view>

				</view>
				<!--    输入验证码--> 
				<view class="index_body_content2">
					<view class="left">
						<u-input v-model="code" :clearable="false" type="number" placeholder="请输入验证码" :maxlength="6"
							input-align="text" class="modal-resale-input" />
					</view>
					<view class="right" :class="{ 'active': isActive }" @tap="getCode">
						{{ tips }}
					</view>
				</view>
				
				<view class="index_body_content3" v-if="ypcode">
					<view class="top">
						<view class="right" style="width: 85%;">
							<u-input :disabled="true" class="modal-resale-input2" v-model="ypcode" type="text"
							 	placeholder="请输入邀请码(可不填)" :maxlength="11" />
						</view>
					</view>
				</view>
				<view class="body_content_text">
					<view class="right_text" @click="nav_password()">
						账号密码登录
					</view>
				</view>
			</view>
			<view class="agreement">
				<u-image mode="widthFix" width="28rpx" @click="isAgreement = !isAgreement"
					:src="`../../../../static/login/${isAgreement ? 'jxs2x' : 'jx'}.png`">
				</u-image>
				<view class="text">
					<text @click="isAgreement = !isAgreement">我已阅读并同意Bigverse</text><span
						@tap="nav_link('Bigverse平台服务协议', 1)">《用户协议》</span>与 <span
						@tap="nav_link('Bigverse法律声明及隐私政策', 2)">《隐私协议》</span>
				</view>
			</view>
			<view class="submit_login" @tap="login_nav">
				注册并登录
			</view>
			<view class="tiaoguo" @click="nav_index">
				跳过 >>
			</view>
		</view>
		<view class="bottom_blur"></view>
	</view>
</template>

<script>
export default {
	name: "register",
	components: {},
	data() {
		return {
			// background: {
			// 	backgroundColor: '#35333E',
			// },
			region: '86',
			tips: '获取验证码',
			phone: '', //手机号码
			psw: '', //密码
			code: '', //验证码
			ypcode: '', //邀请码
			status: 0,
			isAgreement: false,
			seconds: 60,
			changeText: '重新获取(x)',
			isDisabled: false,
			activityNo: "",
			return_url: '',
			isActive:true
		}
	},
	onLoad(options) {
		if (options.code) {
			this.ypcode = options.code
		}
		if (options.activityNo) {
			this.isDisabled = true
			this.activityNo = options.activityNo
		}
		if (options.url === undefined) {
			this.return_url = ""
		} else {
			if (/^#/.test(options.url)) {
				this.return_url = `/h5/${options.url}`;
			} else {
				this.return_url = options.url;
			}
		}
	},
	methods: {
		// 
		getCode() {
			if (this.$refs.uCode.canGetCode) {
				// 模拟向后端请求验证码
				if (this.$u.test.mobile(this.phone)) {
					uni.showLoading({
						title: '正在获取验证码'
					})
					if(this.ypcode){
						this.registerSendPhoneVerifyCode()
					}else{
						this.sendPhoneVerifyCode()
					}
				} else {
					this.$u.toast('请输入正确手机号再获取');
				}

			} else {
				// this.$u.toast('倒计时结束后再发送');
			}
		},
		login_nav(){
			if(this.ypcode){
				this.register()
			}else{
				this.login()
			}
		},
		async sendPhoneVerifyCode() {
			let res = await this.$api.java_sendAliYunSms({
				aliYumSmsType: "LOGIN",
				mobPhonePrefix: this.region,
				mobPhone: this.phone
			});
			if (res.status.code == 0) {
				this.$u.toast('验证码已发送');
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
				this.isActive = false
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async login() {
			if (this.phone == "") {
				this.$u.toast('请输入手机号');
			} else if (this.code == "") {
				this.$u.toast('请输入验证码');
			} else if (!this.isAgreement) {
				this.$u.toast('请先勾选协议');
			} else {
				let res = await this.$api.appUserPhoneVerifyCodeLoginRegister({
					mobPhone: this.phone,
					code: this.code
				});
				if (res.status.code == 0) {
					uni.setStorageSync("token", res.result?.accessToken)
					this.$Router.push({
						name:"userIndex"
					})
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async registerSendPhoneVerifyCode() {
			let res = await this.$api.java_sendAliYunSms({
				aliYumSmsType: "REGISTER",
				mobPhonePrefix: 86,
				mobPhone:this.phone
			});
			if (res.status.code == 0) {
				this.$u.toast('验证码已发送');
				// 通知验证码组件内部开始倒计时
				this.isActive = false
				this.$refs.uCode.start();
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async register() {
			if(!this.phone){
				uni.showToast({
					title: '手机号不能为空',
					icon: 'none',
					duration: 3000
				});
				return
			}
			
			if(!this.code){
				uni.showToast({
					title: '验证码不能为空',
					icon: 'none',
					duration: 3000
				});
				return 
			}
			let res = await this.$api.registerByFriendInvitedCodeVerifyCode({
				mobPhone:this.phone,
				inviteCode:this.ypcode,
				code:this.code,
				codeType:'REGISTER'
			});
			if (res.status.code == 0) {
				this.$u.toast('注册成功');
				uni.setStorageSync("token", res.result?.accessToken)
				this.$Router.push({
					name:"userIndex"
				})
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_link(title, index) {
			if (index === 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						 link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
					}
				})
			} else {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
					}
				})
			}
		},
		codeChange(text) {
			this.tips = text;
		},
		end(){
			this.isActive = true
		},
		nav_index(){
			this.$Router.push({
				name:"storeIndex"
			})
		},
		nav_password(){
			this.$Router.push({
				name:"storeMoreLogin",
				params:{
					return_url:this.return_url
				}
			})
		}
	}
}
</script>

<style scoped lang="scss">
.index_body {
	background: #F6F4F7;
	min-height:100vh;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	z-index:9;

	.index_body_text {
		display: flex;
		justify-content: center;
		align-items: center;
		width: auto;
		line-height: 154rpx;
		font-size:48rpx;
		margin-bottom: 44rpx;
		color:#121212;
	}

	.index_body_box {
		display: flex;
		align-items: center;
		flex-direction: column;
		position: relative;
		z-index:9;
		.index_body_content1 {
			display: flex;
			justify-content: center;
			margin-bottom: 40rpx;
		}

		.top {
			width: 654rpx;
			height: 120rpx;
			border-radius: 28rpx;
			background: #FFFFFF;
			display: flex;
			// justify-content: center;
			align-items: center;
			padding: 10rpx 40rpx;

			.left {
				display: flex;
				align-items: center;
				padding-right: 40rpx;

				text {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0rpx;
					line-height: 37rpx;
					color: #121212;;
					text-align: left;
					vertical-align: top;
					padding-right: 4rpx;
				}

				image {
					width: 20rpx;
					height: 16rpx;
					margin-left: 10rpx;
				}

			}

			.right {
				width:100%;
				input {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0rpx;
					line-height: 37rpx;
					color: rgba(166, 166, 166, 1);
				}
			}
		}

		.index_body_content2 {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color:#fff;
			width: 654rpx;
			height: 120rpx;
			border-radius:28rpx;
			padding-right: 32rpx;
			.left {
				width: 300rpx;
				height: 90rpx;
				line-height: 90rpx;
				border-radius: 50rpx;
				border: 1rpx solid rgba(0, 0, 0, 0);
				display: flex;
				align-items: center;
				padding-left: 40rpx;
				.pla_inp {
					text-align: center;
				}

			}
			.right {
				width: 170rpx;
				height: 90rpx;
				line-height: 90rpx;
				border-radius: 50rpx;
				border: 1rpx solid rgba(0, 0, 0, 0);
				font-size: 28rpx;
				letter-spacing: 0rpx;
				color: #656565;
				text-align: center;
				margin-left: 20rpx;
				&.active{
					color:#6B63E5;
				}
			}

		}
		.index_body_content3{
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color:#fff;
			width: 654rpx;
			height: 120rpx;
			border-radius:28rpx;
			padding-right: 32rpx;
			margin-top:40rpx;
		}
		.body_content_text{
			display: flex;
			justify-content: flex-end;
			width: 624rpx;
			margin:30rpx auto 0rpx auto;
			.right_text{
				font-size:24rpx;
				color:#6B63E5;
			}
		}
		
	}

	.agreement {
		padding: 0rpx 105rpx;
		margin-top: 80rpx;
		display: flex;

		.u-image {
			margin-right: 10rpx;
			margin-top: 4rpx;
		}

		.text {
			font-size: 20rpx;
			color: #A6A6A6;
			line-height: 32rpx;

			span {
				text-decoration: underline;
				color: #6b63e5;
			}
		}
	}

	.submit_login {
		width: 654rpx;
		height: 120rpx;
		background: #6b63e5;
		border-radius: 14rpx;
		font-size: 34rpx;
		color: #fff;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 66rpx auto 0rpx auto;

	}
	.tiaoguo{
		color:#6B63E5;
		font-size:28rpx;
		text-align: center;
		margin-top:300rpx;
		position: relative;
		z-index:9;
	}
	.head_bg_view {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 100%;
		height: 500rpx;
		z-index: -1;
		background-color: #fff;
	}

}

:v-deep .uni-input-input {
	text-align: center;
}
.modal-resale-input2::v-deep {
	.u-input__input {
		color: #656565 !important;
	}
}
.modal-resale-input::v-deep {
	.u-input__input {
		color: #121212 !important;
	}

	.uni-input-placeholder {
		font-size: 28rpx !important;
	}
}
</style>