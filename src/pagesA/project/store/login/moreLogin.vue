<template>
	<view class="index_body">
		<view class="blur"></view>
		<view>
			<u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end"
				 ref="uCode" @change="codeChange"></u-verification-code>
			<view class="index_body_text">
				登录  
			</view>
			<view class="index_body_box">
				<view class="index_body_content1">
					<!--    输入手机号-->
					<view class="top">
						<view class="right">
							<u-input class="modal-resale-input" v-model="phone" type="text" placeholder="请输入手机号或邮箱"
								 />
						</view>
					</view>

				</view>
				<!--    输入验证码-->
				<view class="index_body_content2">
					<u-input v-model="password" :clearable="false" type="password" placeholder="请输入密码"
						input-align="text" class="modal-resale-input" />
				<!-- 	<view class="right" :class="{ 'active': isActive }" @tap="getCode">
						{{ tips }}
					</view> -->
				</view>
				<view class="body_content_text">
					<view class="left_text">
						
					</view>
					<view class="right_text" @click="nav_login">
						验证码登录
					</view>
				</view>
			</view>
			<view class="agreement">
				<u-image mode="widthFix" width="28rpx" @click="isAgreement = !isAgreement"
					:src="`../../../../static/login/${isAgreement ? 'jxs2x' : 'jx'}.png`">
				</u-image>
				<view class="text">
					<text @click="isAgreement = !isAgreement">我已阅读并同意Bigverse</text><span
						@tap="nav_link('Bigverse平台服务协议', 1)">《用户协议》</span>与 <span
						@tap="nav_link('Bigverse法律声明及隐私政策', 2)">《隐私协议》</span>
				</view>
			</view>
			<view class="submit_login" @tap="login">
				登录
			</view>
			<view class="tiaoguo" @click="nav_index">
				跳过 >>
			</view>
		</view>
		<view class="bottom_blur"></view>
	</view>
</template>

<script>
export default {
	name: "register",
	components: {},
	data() {
		return {
			region: '86',
			tips: '获取验证码',
			phone: '', //手机号码
			psw: '', //密码
			password: '', //验证码
			ypcode: '', //邀请码
			status: 0,
			isAgreement: false,
			seconds: 60,
			changeText: '重新获取(x)',
			isDisabled: false,
			activityNo: "",
			return_url: '',
			isActive:true
		}
	},
	onLoad(options) {
		if (options.code) {
			this.ypcode = options.code
		}
		if (options.activityNo) {
			this.isDisabled = true
			this.activityNo = options.activityNo
		}
		if (options.url === undefined) {
			this.return_url = ""
		} else {
			if (/^#/.test(options.url)) {
				this.return_url = `/h5/${options.url}`;
			} else {
				this.return_url = options.url;
			}
		}
	},
	methods: {
		async login() {
			// 判断不同账号类型请求不同接口
			if (this.phone == "") {
				this.$u.toast('请输入账号');
			} else if (this.password == "") {
				this.$u.toast('请输入密码');
			} else if (!this.isAgreement) {
				this.$u.toast('请先勾选协议');
			} else {
				// 邮箱
				if (this.$u.test.email(this.phone)) {
					console.log('邮箱')
					let eres = await this.$api.java_emailPasswordLogin({
						email: this.phone,
						password: this.password,
					});
					this.getsuss(eres)
				} else if (this.$u.test.mobile(this.phone)) {
					// 手机
					console.log('手机')
					let pres = await this.$api.java_phonePasswordLogin({
						mobPhone: this.phone,
						password: this.password,
					});
					this.getsuss(pres)
				} else {
					let res = await this.$api.java_usernamePasswordLogin({
						userName: this.phone,
						password: this.password,
					});
					this.getsuss(res)
				}
		
		
			}
		
		},
		getsuss(res) {
			if (res.status.code == 0) {
				uni.setStorageSync("token", res.result?.accessToken)
				
				this.$u.toast('登录成功');
				// #ifdef APP
				setTimeout(() => {
					this.$Router.push({
						name: 'userIndex'
					})
				}, 300);
				// #endif
				// #ifdef H5
				if(this.return_url){
					window.location.href = this.return_url
				}else{
					setTimeout(() => {
						this.nav_index()
					}, 300);
				}
				// #endif
				
			} else {
				if (res.status.code == 1005) {
					this.$Router.push({
						name: 'register',
						params: {
							phone: this.phone
						}
					})
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
		
			}
		},
		nav_link(title, index) {
			if (index === 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
					}
				})
			} else {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
					}
				})
			}
		},
		codeChange(text) {
			this.tips = text;
		},
		end(){
			this.isActive = true
		},
		nav_index(){
			this.$Router.push({
				name:"storeIndex"
			})
		},
		nav_login(){ 
			this.$Router.push({
				name:"storeMainLogin",
				params:{
					return_url:this.return_url
				}
			})
		},
		nav_modify(){
			this.$Router.push({
				name:"loginfyPwd",
			})
		}
	}
}
</script>

<style scoped lang="scss">
.index_body {
	background: #F6F4F7;
	min-height:100vh;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	z-index:9;

	.index_body_text {
		display: flex;
		justify-content: center;
		align-items: center;
		width: auto;
		line-height: 154rpx;
		font-size:48rpx;
		margin-bottom: 44rpx;
		color:#121212;
	}

	.index_body_box {
		display: flex;
		align-items: center;
		flex-direction: column;
		position: relative;
		z-index:9;
		.index_body_content1 {
			display: flex;
			justify-content: center;
			margin-bottom: 40rpx;
		}

		.top {
			width: 654rpx;
			height: 120rpx;
			border-radius: 28rpx;
			background: #FFFFFF;
			display: flex;
			// justify-content: center;
			align-items: center;
			padding: 10rpx 40rpx;

			.left {
				display: flex;
				align-items: center;
				padding-right: 40rpx;

				text {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0rpx;
					line-height: 37rpx;
					color: #121212;;
					text-align: left;
			name:"userIndex";
					padding-right: 4rpx;
				}

				image {
					width: 20rpx;
					height: 16rpx;
					margin-left: 10rpx;
				}

			}

			.right {
				width:100%;
				input {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0rpx;
					line-height: 37rpx;
					color: rgba(166, 166, 166, 1);
				}
			}
		}

		.index_body_content2 {
			display: flex;
			justify-content:flex-start;
			align-items: center;
			background-color:#fff;
			width: 654rpx;
			height: 120rpx;
			border-radius:28rpx;
			padding:10rpx 40rpx;
			.left {
				width: 300rpx;
				height: 90rpx;
				line-height: 90rpx;
				border-radius: 50rpx;
				border: 1rpx solid rgba(0, 0, 0, 0);
				display: flex;
				align-items: center;
				padding-left: 40rpx;
				.pla_inp {
					text-align: center;
				}

			}
			.right {
				width: 170rpx;
				height: 90rpx;
				line-height: 90rpx;
				border-radius: 50rpx;
				border: 1rpx solid rgba(0, 0, 0, 0);
				font-size: 28rpx;
				letter-spacing: 0rpx;
				color: #656565;
				text-align: center;
				margin-left: 20rpx;
				&.active{
					color:#6B63E5;
				}
			}

		}
		.index_body_content3{
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color:#fff;
			width: 654rpx;
			height: 120rpx;
			border-radius:28rpx;
			padding-right: 32rpx;
			margin-top:40rpx;
		}
		.body_content_text{
			display: flex;
			justify-content: space-between;
			width: 624rpx;
			margin:30rpx auto 0rpx auto;
			.right_text{
				font-size:24rpx;
				color:#6B63E5;
			}
			.left_text{
				font-size:24rpx;
				color:#808080;
				text{
					color:#6B63E5;
				}
			}
		}
		
	}

	.agreement {
		padding: 0rpx 105rpx;
		margin-top: 80rpx;
		display: flex;

		.u-image {
			margin-right: 10rpx;
			margin-top: 4rpx;
		}

		.text {
			font-size: 20rpx;
			color: #A6A6A6;
			line-height: 32rpx;

			span {
				text-decoration: underline;
				color: #6b63e5;
			}
		}
	}

	.submit_login {
		width: 654rpx;
		height: 120rpx;
		background: #6b63e5;
		border-radius: 14rpx;
		font-size: 34rpx;
		color: #fff;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 66rpx auto 0rpx auto;

	}
	.tiaoguo{
		color:#6B63E5;
		font-size:28rpx;
		text-align: center;
		margin-top:300rpx;
		position: relative;
		z-index:9;
	}
	.head_bg_view {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 100%;
		height: 500rpx;
		z-index: -1;
		background-color: #fff;
	}

}

:v-deep .uni-input-input {
	text-align: center;
}
.modal-resale-input2::v-deep {
	.u-input__input {
		color: #656565 !important;
	}
}
.modal-resale-input::v-deep {
	.u-input__input {
		color: #121212 !important;
	}

	.uni-input-placeholder {name:"userIndex"!important;
	}
}
</style>