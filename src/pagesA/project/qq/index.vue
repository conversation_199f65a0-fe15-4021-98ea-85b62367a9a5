<template>
	<view class="launch-container">
		<!-- 非微信环境 -->
		<view v-if="!isWeixin" class="normal-content">
			<button class="launch-btn" @click="handleLaunch">
				打开QQ小程序
			</button>
		</view>

		<!-- 微信环境引导层 -->
		<view v-else class="weixin-guide">
			<view class="guide-box">
				<view class="guide-title">
					请按以下步骤操作
				</view>
				<view class="guide-steps">
					<view class="step">1. 点击右上角 <text class="highlight">...</text></view>
					<view class="step">2. 选择 <text class="highlight">"在浏览器中打开"</text></view>
					<view class="step">3. 点击 <text class="highlight">"打开QQ小程序"</text> 按钮</view>
				</view>
				<image class="guide-arrow" src="/static/images/guide-arrow.png" mode="widthFix" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isWeixin: false,
				qqMiniProgramConfig: {
					appId:'1112417828', // QQ小程序的appId
					path:'pages/login/index', // 页面路径
					params:{}, // 页面参数
					extraData:{} // 额外数据
				}
			};
		},

		onLoad(options) {
			this.checkEnvironment();
			this.qqMiniProgramConfig.params = {
				inviteCode:options.inviteCode
			}
			setTimeout(() => {
				this.handleLaunch()
			}, 500)
		},

		methods: {
			checkEnvironment() {
				const ua = navigator.userAgent.toLowerCase();
				this.isWeixin = ua.indexOf('micromessenger') !== -1;
			},

			handleLaunch() {
				this.launchQQMiniProgram(this.qqMiniProgramConfig);
			},
			checkEnvironment() {
				const ua = navigator.userAgent.toLowerCase();
				return {
					isWeixin: ua.indexOf('micromessenger') !== -1,
					isQQ: ua.indexOf('qq/') !== -1,
					isIOS: !!ua.match(/\(i[^;]+;( u;)? cpu.+mac os x/),
					isAndroid: ua.indexOf('android') !== -1
				};
			},
			// 尝试唤起QQ小程序
			launchQQMiniProgram(options) {
				const env = this.checkEnvironment();
				const {
					appId, // QQ小程序的appId
					path = '', // 页面路径
					params = {}, // 页面参数
					extraData = {} // 额外数据
				} = options;

				// 构建完整路径
				let fullPath = path;
				console.log('options',options)
				// 处理参数
				const queryParams = new URLSearchParams();
				Object.keys(params).forEach(key => {
					queryParams.append(key, params[key]);
				});

				// 如果有参数，添加到路径中
				if (queryParams.toString()) {
					fullPath += `?${queryParams.toString()}`;
				}

				// 构建最终的URL Scheme
				const schemeUrl = `mqq://app/page?appId=${appId}&path=${encodeURIComponent(fullPath)}`;
				if (env.isWeixin) {
					// 在微信中显示引导
					this.showWeixinGuide();
					return;
				}

				// 非微信环境尝试直接唤起
				window.location.href = schemeUrl;
			},
			// 显示微信环境下的引导
			showWeixinGuide() {
				uni.showModal({
					title: '提示',
					content: '请点击右上角"..."，选择"在浏览器打开"后再尝试',
					confirmText: '我知道了',
					showCancel: false
				});
			}

		}
	};
</script>

<style lang="scss" scoped>
	.launch-container {
		min-height: 100vh;
		padding: 20px;

		.normal-content {
			min-height: 100vh;
			display: flex;
			align-items: center;
			justify-content: center;
			.launch-btn {
				background: #12B7F5;
				color: #fff;
				padding: 12px 24px;
				border-radius: 4px;
				font-size: 16px;

				&:active {
					opacity: 0.8;
				}
			}
		}

		.weixin-guide {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.8);
			z-index: 999;

			.guide-box {
				padding: 20px;
				color: #fff;

				.guide-title {
					font-size: 18px;
					font-weight: bold;
					margin-bottom: 20px;
				}

				.guide-steps {
					.step {
						margin-bottom: 15px;
						font-size: 16px;
						line-height: 1.5;

						.highlight {
							color: #12B7F5;
							font-weight: bold;
						}
					}
				}

				.guide-arrow {
					width: 80px;
					position: absolute;
					right: 30px;
					top: 20px;
				}
			}
		}
	}
</style>