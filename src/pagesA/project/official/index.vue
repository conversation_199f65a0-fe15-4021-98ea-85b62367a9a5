<template>
	<view class="main">
		<view class="content padding_lr">
			<view class="head_view">
				<view class="left" @click="nav_back">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20240508/fcac606b63194b1c8b0255f0e65934f8_26x50.png" mode="widthFix"></image>
				</view>
				<view class="barHeight"></view>
				<view class="search_view">
					<u-search placeholder="" search-icon="/static/imgs/notic/search.png" v-model="title" bg-color='#35333E'
						 color="#fff" :clearabled='false' @search="search" @clear="clear" :show-action="false"
						:input-style='searchStyle'></u-search>
					<view class="right" @click="search">搜索</view>
				</view>
				<!-- tabs -->
				<view class="tabbar_view">
					<u-tabs :list="tabList" :is-scroll="true" font-size='28' inactive-color='#ccc' active-color='#fff'
						:current="current" bg-color="#35333E" :bar-style='barStyle' @change="change"></u-tabs>
				</view>
			</view>
			<!-- 列表  -->
			<view class="loading_list" v-show="isLoadingStatus == 0">
				<view>
					<view class="flex">
						<view class="balls"></view>
					</view>
					<view class="text">
						玩命加载中...
					</view>
				</view>
			</view>
			<view class="barHeight"></view>
			<scroll-view scroll-y="true" style="width: 666rpx;padding-bottom: 100rpx; "
				v-show="list.length>0&&isLoadingStatus == 1">
				<view class="list_view">
					<view class="list_li" @click="nav_details(item)" v-for="(item, index) in list" :key="index">
						<view class="left_img">
							<image :src="item.cover.src" mode="heightFix"></image>
						</view>
						<view class="right_font">
							<view class="title twoOver">
								{{item.title}}
							</view>
							<view class="time">
								<view>
									<view v-if="item.templateType==1">白名单</view>
									<view v-if="item.templateType==2">合成</view>
									<view v-if="item.templateType==3">寄售</view>
									<view v-if="item.templateType==4">上新预告</view>
									<view v-if="item.templateType==5">空投</view>
									<view v-if="item.templateType==6">上新提醒</view>
									<view v-if="item.templateType==7">合成提醒</view>
									<view v-if="item.templateType==8">活动</view>
									<view v-if="item.templateType==9">运营</view>
									<view v-if="item.templateType==10">限额调整</view>
									<view v-if="item.templateType==11">分解</view>
									<view v-if="item.templateType==12">置换</view>
									<view v-if="item.templateType==13">提醒</view>
									<view v-if="item.templateType==14">展示位变更</view>
									<view v-if="item.templateType==15">竞价</view>
									<view v-if="item.templateType==16">冰封活动</view>
									<view v-if="item.templateType==17">分区调整</view>
									<view v-if="item.templateType==18">突袭公告</view>
									<view v-if="item.templateType==19">膨胀公告</view>
									<view v-if="item.templateType==20">开放转售</view>
									<view v-if="item.templateType==21">委托购买</view>
								</view>
								<view>
									{{ item.createdAt}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="null_body" v-show="list.length==0&&isLoadingStatus == 2">
				<view class="null">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						mode="widthFix"></image>
					<view class="text">
						暂无数据
					</view>
				</view>
			</view>
		</view>
		<TabBar :initialActiveIndex="2"></TabBar>
	</view>
</template>
<script>
	import api from '@/common/api/index.js';
	import * as head from "@/static/lottie/head/head.json";
	export default {
		data() {
			return {
				height: "",
				title: "",
				tabList: [{
					name: '全部公告',
					value: '',
				}, {
					name: '合成',
					value: '2',
				}, {
					name: '寄售',
					value: '3',
				}, {
					name: '上新',
					value: '4',
				}, {
					name: '空投',
					value: '5',
				}, {
					name: '活动',
					value: '8',
				}, {
					name: '运营',
					value: '9',
				}],
				list: [],
				show: true,
				pageNum: 1,
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				current: 0,
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				},
				templateType: "",
				searchStyle: {
					'background': '#35333E',
				},
				option: {
					data: head,
					loop: false,
					autoplay: false
				},
				showAnimation: false,
				animationInstance: null,
				title:"",
				platform:"",
				isApp:false,
				token:"",
				isLoadingStatus:0  //0 加载中  1 正常载入  2无数据
			}
		},
		onLoad(options) {
			this.title = options.title
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if(platform){
				this.isApp=true
			}
			if (token) {
				this.token = token
				uni.setStorageSync('token', token);
			}
			this.getList()
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.pageNum = 1
				this.list = []
				this.getList()
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 1000);
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			change(index) {
				this.current = index
				this.list = [];
				this.pageNum = 1;
				this.templateType = this.tabList[index].value
				this.isLoadingStatus = 0
				this.isFooter = true
				this.getList()
			},
			search() {
				this.isLoadingStatus=0
				this.pageNum = 1
				this.getList()
			},
			clear() {
				this.title = ""
				this.pageNum = 1
				this.getList()
			},
			async getList() {
				this.isRequest = true
				if (this.pageNum == 1) {
					this.list = [];
				}
				const {
					status,
					result
				} = await api.java_officialArticleList({
					type: this.type,
					pageSize: 10,
					pageNum: this.pageNum,
					templateType: this.templateType,
					title: this.title
				});
				if (status.code === 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
						if(this.list == ""){
							this.isLoadingStatus = 2
						}
					} else {
						this.isLoadingStatus = 1
						this.pageNum++
						result.list.forEach((item) => {
							this.list.push(item)
						})
					}
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_search() {
				this.pageNum = 1
				this.type = null
				this.getList()
			},
			nav_details(item) {
				console.log(item.id)
				if (item.linkType == "NO_LINK") {
					return
				} else if (item.linkType == "WEB_LINK" && item.h5Link) {
					this.$Router.push({
						name: "webView",
						params: {
							url: item.h5Link
						}
					})
					return
				} else if (item.linkType == "DETAIL_LINK") {
					// #ifdef H5
					this.$Router.push({
						name: "officialDetail",
						params: {
							id: item.id,
							linkType:item.linkType
						}
					})
					// #endif
					// #ifdef APP
					let url = `${getApp().globalData.url}pagesA/project/official/detail?id=${item.id}`
					this.$Router.push({
						name: "webView",
						params: {
							url
						}
					})
					// #endif
					return
				}
				if(this.isApp){
					this.$Router.push({
						name: "officialDetail",
						params: {
							id: item.id,
							platform:this.platform,
							token:this.token
						}
					})
				}else{
					this.$Router.push({
						name: "officialDetail",
						params: {
							id: item.id,
						}
					})
				}
			},
			nav_back(){
				this.$Router.back()
			}
		},
	}
</script>
<style lang="scss" scoped>
	@font-face {
		font-family:'YouSheBiaoTiHei'; 
		src: url(https://cdn-lingjing.nftcn.com.cn/h5/ttf/YouSheBiaoTiHei-2.ttf);
	}
	.head_bg {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 100%;
		height: 500rpx;
		z-index: -1;
		background-color: #fff;
		will-change: transform;
		transform: translateZ(0);
	}

	.padding_lr {
		padding: 0rpx 56rpx;
	}

	.main {
		flex: 1;
	}

	.head_title {
		// height: 170rpx;
		line-height: 100rpx;
		// padding:86rpx 0 0 0;
	}

	.title_1 {
		color: #fff;
		font-weight: 600;
		font-size: 44rpx;
		text-align: center;
	}

	.content {
		padding-top:200rpx;
	}
	.head_view{
		position: fixed;
		width:100%;
		top:0;
		left:0;
		z-index: 99;
		background-color:#35333E;
		padding: 0rpx 56rpx;
		.left{
			position:absolute;
			/* #ifdef APP */
			top:136rpx;
			/* #endif */
			/* #ifdef H5 */
			top:36rpx;
			/* #endif */
			
			left:12rpx;
			image{
				width:30rpx;
			}
		}
	}
	.search_view {
		margin-top: 30rpx;
		position: relative;
		border-radius:28rpx;
		border:1px solid var(--default-color3);
		display: flex;
		justify-content:space-between;
		align-items: center;
		
		.right {
			font-size: 28rpx;
			position: absolute;
			right: 30rpx;
			top: 16rpx;
			color: #fff;
		}
	}

	.tabbar_view {
		margin-top: 30rpx;
	}

	.list_view {
		width: 638rpx;
		margin-top: 40rpx;
	}

	.list_li {
		width: 100%;
		// height: 200rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 40rpx;
		background: #35333E;
		border-radius: 16px;
		transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		border: 1rpx solid #717171;
		box-sizing: border-box;
		overflow: hidden;
		position: relative;
	}

	.left_img {
		// margin: 0 40rpx 0 0;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center; 
		>image {
			height:190rpx;
		}
	}

	.right_font {
		position: absolute;
		right: 20rpx;
		width: 361rpx;
		.title {
			width: 361rpx;
			line-height: 50rpx;
			font-family: 'YouSheBiaoTiHei';
			font-weight: 400;
			color: #FFFFFF;
			font-size:36rpx;
		}
	}



	.time {
		display: flex;
		align-items: center;
		margin-top: 10rpx;

		>view:nth-child(1) {
			height: 30rpx;
			line-height: 30rpx;
			background: #FFFFFF;
			text-align: center;
			font-weight: 400;
			font-size: 20rpx;
			color: #35333E;
			transform: skewX(30deg);
			margin-right: 30rpx;
			padding:0rpx 10rpx;
			
			>view {
				transform: skewX(-30deg);
			}
		}

		>view:nth-child(2) {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
			font-weight: 400;
			font-size: 22rpx;
		}
	}

	.null_body {
		.null {
			image {
				width: 242rpx;
			}
		}

		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}

		width:100%;
		height: 60vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>