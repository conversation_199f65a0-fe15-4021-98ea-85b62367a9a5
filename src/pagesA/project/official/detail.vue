<template>
	<view class="content">
		<view class="back" @click="back()" v-show="!isApp">
			<image src="@/static/imgs/mall/mall_back.png" mode="widthFix"></image>
		</view>
		<view class="" v-if="linkType == 'DETAIL_LINK'">
			<view class="noticeHead">
				<view>
					<img class="imgOne"
						src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png"
						alt="" srcset="">
				</view>
				<view style="padding:80rpx 30rpx">
					<view>
						<img class="imgTwo"
							src="https://cdn-lingjing.nftcn.com.cn/image/20241030/3b11d88245184e64bd4b91327f8f1e11_524x31.png"
							alt="" srcset="">
					</view>
					<view class="contentDiv">
						<view class="title">
							<view>
								{{info.title}}
							</view>
						</view>
						<view>
							<img class="imgBd"
								src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png"
								alt="" srcset="">
						</view>
					</view>
				</view>
			</view>
		</view>


		<view class="htmlContent" style="padding: 0rpx 28rpx 90rpx 28rpx;color:#fff;margin-top:80rpx;"
			v-html="info.content" v-if="linkType=='DETAIL_LINK'"></view>
		<view class="htmlContent" v-else v-html="info.content"></view>
		<view class="showNum">
			浏览量：{{info.scanCount}}
		</view>
		<view class="gonggao">
			<view class="xg" v-if="cpList != null && cpList.length != 0">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20230606/60f0a0623232c446f69c0d1d8dccbd82_400x100.png"
					mode="widthFix"></image>
			</view>
			<view class="cp_view" v-if="cpList!=null">
				<view class="li" v-for="(item,index) in cpList">
					<view class="cover">
						<image :src="item.cover.src" mode="aspectFill"></image>
					</view>
					<view class="info">
						<view class="title">
							{{item.csName}}
						</view>
						<view class="price">
							当前地板价：<text>￥{{item.floorPrice}}</text>
						</view>
						<view class="but">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20230609/3bdf3aecdc9ef8579cf67e2b8e6983a0_214x78.png"
								mode="widthFix" @click="nav(item)"></image>
							<image @click="submitOrder(item)" class="active"
								src="https://cdn-lingjing.nftcn.com.cn/image/20230609/bcf04d093cc788f82daba94ec1038223_215x78.png"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="wq_gg" v-if="wqList != null && wqList.length != 0">
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20230606/94022bcba23a7aaa54cc5fb68e6e8751_400x122.png"
					mode="widthFix"></image>
			</view>
			<view class="wq_view" v-if="wqList!=null">
				<view class="li" v-for="(item,index) in wqList">
					<view class="time">
						{{item.publishTimeStr}}
					</view>
					<view class="title oneOver">
						{{item.title}}
					</view>
					<view class="info">
						<view v-if="item.templateType==0"></view>
						<view class="describe twoOver" v-else>
							<text v-if="item.templateType==1">白名单公告</text>
							<text v-if="item.templateType==2">合成公告</text>
							<text v-if="item.templateType==3">寄售公告</text>
							<text v-if="item.templateType==4">上新预告</text>
							<text v-if="item.templateType==5">空投公告</text>
							<text v-if="item.templateType==6">上新提醒</text>
							<text v-if="item.templateType==7">合成提醒</text>
							<text v-if="item.templateType==8">活动公告</text>
							<text v-if="item.templateType==9">运营公告</text>
							<text v-if="item.templateType==10">限额调整</text>
							<text v-if="item.templateType==11">分解公告</text>
							<text v-if="item.templateType==12">置换公告</text>
							<text v-if="item.templateType==13">提醒公告</text>
							<text v-if="item.templateType==14">展示位变更</text>
							<text v-if="item.templateType==15">竞价</text>
						</view>
						<view class="but" @click="nav_details(item)">
							<image class="active"
								src="https://cdn-lingjing.nftcn.com.cn/image/20230609/0f0cb23053b551aaab7129a9bdb5549a_187x80.png"
								mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg" style="
	  padding: 10rpx 20rpx 40rpx 20rpx;
	  text-align: center;
	  font-size: 26rpx;
	  line-height: 40rpx; 
	">
				跳转登录中...
			</view> 
		</u-modal>
		<u-modal class="" v-model="isSubmit" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				订单提交中...
			</view>
		</u-modal>
		<u-modal v-model="isWarning" font-size="40" :show-title="false" width="65%" :mask-close-able="true"
			:show-confirm-button="false">
			<view style="padding:35rpx;">
				<view style="text-align:center;margin:20rpx 0rpx;font-size:34rpx;">
					温馨提示
				</view>
				<view class="flex_all" style="font-size:28rpx;line-height:40rpx;text-align:left;color: #fff;">
					{{isWarningText}}
				</view>
			</view>
			<view class="modal-btn flex_all">
				<!-- <view class="mb-cancel" style="width:40%;" @click="">取消</view> -->
				<view class="mb-confirm" style="width:70%;" @click="isWarning=false">我知道了</view>
			</view>
		</u-modal>
		<u-modal v-model="isRegistration" font-size="40" :show-title="false" width="80%" :mask-close-able="true"
			:show-confirm-button="false">
			<view class="autonym_model">
				<view class="title">
					<view>请尽快完成</view>
					<view>身份实名认证</view>
				</view>
				<view class="msg">
					您未进行实名认证，根据监管要求，<text>在当您举报作品、购买价值过高作品、提现,充值功能时，</text>需要进行实名认证，为了避免影响您的后续使用不便，请尽快进行实名认证。
				</view>
				<view class="button_db">
					<view class="button active" @click="nav_realName()">去实名</view>
					<view class="button" @click="isRegistration=false">暂不实名</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isError" font-size="40" :show-title="false" width="65%" :mask-close-able="true"
			:show-confirm-button="false">
			<view style="padding:35rpx;">
				<view style="text-align:center;margin:20rpx 0rpx;font-size:34rpx;color:#BB3835;font-weight:600;">
					风险提示
				</view>
				<view class="flex_all" style="font-size:28rpx;line-height:44rpx;text-align:center;color: #fff;">
					如果您执意购买高风险作品，
				</view>
				<view class="flex_all" style="font-size:28rpx;line-height:44rpx;text-align:center;color: #fff;">
					您本人将承担全部损失，您确定要购买吗？
				</view>
			</view>
			<view class="modal-btn flex_all">
				<!-- <view class="mb-cancel" style="width:40%;" @click="">取消</view> -->
				<view class="mb-confirm" style="width:70%;" @click="isError=false">确认</view>
			</view>
		</u-modal>
		<u-modal v-model="isPayError" font-size="40" :show-title="false" width="80%" :mask-close-able="true"
			:show-confirm-button="false">
			<view class="autonym_model" style="padding:40rpx;">
				<view class="title">
					<view style="text-align: center;font-size:40rpx;">温馨提示</view>
					<view>
						<!-- 身份实名认证 -->
					</view>
				</view>
				<view class="msg" style="font-size:30rpx;text-align: center;">
					{{isPayErrorText}}
				</view>
				<view class="button_db flex_between_x" style="font-size:22rpx;">
					<view class="button" style="margin-right:40rpx;" @click="isPayError=false">取消</view>
					<view class="button active" @click="nav_order()">去支付</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import uniCopy from '@/js_sdk/uni-copy.js';
	export default {
		data() {
			return {
				id: '',
				info: {},
				isAgree: false,
				isPC: false,
				cpList: [],
				wqList: [],
				isSubmit: false,
				isWarning: false,
				isError: false,
				isRegistration: false,
				RegistrationText: "",
				isPayError: false,
				isPayErrorText: "",
				isWarningText: "",
				isLoadding: false,
				platform: '',
				isApp: false,
				linkType: '',
			}
		},
		onLoad(options) {
			console.log(options)
			this.id = options.id;
			this.linkType = options.linkType
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if (platform) {
				uni.setStorageSync('is_platform', platform);
				this.isApp = true
			}
			if (token) {
				uni.setStorageSync('token', token);
			}
			this.getInfo();
			// this.read();
		},
		methods: {
			async getInfo() {
				uni.showLoading({
					title: '加载中...'
				});
				const {
					status,
					result
				} = await this.$api.java_officialArticleDetail({
					id: this.id
				});
				if (status.code == 0) {
					uni.hideLoading();
					this.info = result;
					this.cpList = result.ctidCsNameList
					this.wqList = result.relatedArticleDetailVOList
					document.title = result.title
				} else if (status.code == 1002) {
					setTimeout(() => {
						this.$Router.pushTab({
							name: "mainLogin",
						})
					}, 1500);
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			goBack() {
				this.$Router.push({
					name: "official",
					params: {
						cid: this.cid,
					}
				})
			},
			back() {
				this.$Router.back()
			},
			nav_details(item) {
				this.$Router.push({
					name: 'officialDetail',
					params: {
						id: item.id
					}
				})
			},
			async agree() {
				if (this.isAgree) return uni.showToast({
					title: "已经点过赞啦~",
					icon: 'none'
				});
				// 修改点赞状态
				this.isAgree = true;
				const {
					status,
				} = await this.$api.java_officialThumpUp({
					id: this.id
				});
				if (status.code == 0) {
					uni.showToast({
						title: "操作成功~",
						icon: 'none'
					})
					this.getInfo();
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async submitOrder(item) {
				this.isSubmit = true;
				let res = await this.$api.java_create_item({
					paymentScene: 1,
					ctid: item.ctid,
				});
				setTimeout(() => {
					this.isSubmit = false;
				}, 10000)
				if (res.status.code == 0) {
					uni.showToast({
						title: "下单成功~",
						icon: "none",
						duration: 3000,
					});
					this.isSubmit = false;
					// #ifdef APP
					let url = `${getApp().globalData.urlZf}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
					console.log(url)
					this.$Router.push({
						name: "webView",
						params: {
							url,
						}
					})
					// #endif
					// #ifdef H5
					let {
						origin
					} = window.location
					window.location.href =
						`${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
					// #endif
				} else if (res.status.code == 1002) {
					this.isSubmit = false;
					this.nav_login()
				} else if (res.status.code == 502) {
					this.isSubmit = false;
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000,
					});
				} else if (res.status.code == 504) {
					this.isSubmit = false;
					this.isWarning = true;
					this.isWarningText = res.msg;
				} else if (res.status.code == 510) {
					this.isSubmit = false;
					this.isRegistration = true;
					this.RegistrationText = res.msg;
				} else if (res.status.code == 511) {
					this.isSubmit = false;
					this.isPayError = true;
					this.isPayErrorText = res.status.msg
				} else {
					this.isSubmit = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
					// this.$refs.captcha.refresh()
				}
			},
			nav_realName() {
				this.isRegistration = false;
				this.$Router.push({
					name: "realName",
				});
			},
			nav_order() {
				this.isPayError = false;
				if (uni.getStorageSync('is_platform')) {
					this.$native.orderList();
				} else {
					this.$Router.push({
						name: "order",
					});
				}

			},
			nav_login() {
				this.isLoadding = true
				setTimeout(() => {
					this.$Router.push({
						name: "mainLogin",
						params: {
							url: window.location.hash,
						},
					});
					this.isLoadding = false
				}, 1500);
			},
			nav(item) {
				this.$Router.push({
					name: "seriesList",
					params: {
						ctid: item.ctid,
						title: item.csName,
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	@font-face {
		font-family: 'fonts_title';
		src: url('https://cdn-lingjing.nftcn .com.cn/h5/ttf/jtz.otf');
	}

	@font-face {
		font-family: 'fonts';
		src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
	}

	page {
		width: 100%;
		background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
		background-size: 100%;
	}

	.content {
		width: 100%;
		margin: 0 auto;
		width: 100%;
		background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
		background-size: 100%;

		.noticeHead {
			width: 100%;
			height: 500rpx;

			.imgOne {
				width: 100%;
				height: auto;
			}

			.imgTwo {
				width: 440rpx;

			}

			.contentDiv {
				width: 705rpx;
				height: 362rpx;
				background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241021/538a071da3d57a3d07c551a3d0284ad4_1015x521.png');
				background-size: 100%;
				margin: auto;
				margin-top: 13px;
				position: relative;

				.title {
					overflow-wrap: break-word;
					width: 420rpx;
					text-align: center;
					font-size: 44rpx;
					font-family: 'fonts_title';
					height: 190rpx;
					line-height: 50rpx;
					color: #fff;
					font-family: 'fonts_title';
					margin-top: 20rpx;
					overflow-wrap: break-word;
					position: absolute;
					left: 0;
					right: 0;
					top: 0;
					bottom: 0;
					margin: auto;
					display: flex;
					justify-content: center;
					align-items: center;
					padding-bottom: 22rpx;
				}

				.imgBd {
					width: 100rpx;
					height: 82rpx;
					position: absolute;
					bottom: 80rpx;
					right: -5rpx;
				}
			}
		}

		.back {
			position: fixed;
			/* #ifdef APP */
			top: var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top: 30rpx;
			/* #endif */

			left: 20rpx;

			image {
				width: 80rpx;
			}
		}

		.showNum {
			position: absolute;
			right: 40rpx;
			top: 86rpx;
			color: #fff;
			font-size: 28rpx;
		}

		.gonggao {
			width: 100%;
			margin-top: -10rpx;
			padding-bottom: 20rpx;

			.xg {
				image {
					width: 674rpx;
				}
			}

			.cp_view {
				width: 614rpx;
				margin: 30rpx auto;

				.li {
					background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/e83f3eddf762d9bf88537f5056659e23_400x173.png');
					background-size: 100% 100%;
					margin-bottom: 30rpx;
					width: 100%;
					height: 266rpx;
					display: flex;
					justify-content: flex-start;
					padding: 37rpx 22rpx 33rpx 29rpx;

					.cover {
						border: 4rpx solid #4CF7DE;
						border-radius: 8rpx;
						width: 196rpx;
						height: 196rpx;
						overflow: hidden;

						image {
							width: 100%;
							height: 196rpx;
						}
					}

					.info {
						width: 344rpx;
						margin-left: 20rpx;
						color: #fff;

						.title {
							font-size: 40rpx;
							font-family: 'fonts_title';
							line-height: 50rpx;
							margin-bottom: 10rpx;
						}

						.price {
							font-size: 34rpx;
							line-height: 50rpx;
							font-family: 'fonts_title';
						}

						.but {
							display: flex;
							justify-content: space-between;
							align-items: center;
							margin-top: 20rpx;

							image {
								width: 165rpx;
							}

							// .active{
							// 	border:4rpx solid #fff;
							// 	border-radius:12rpx;
							// }
						}
					}
				}
			}

			.wq_gg {
				width: 283rpx;

				image {
					width: 283rpx;
				}
			}

			.wq_view {
				width: 614rpx;
				margin: 30rpx auto;

				// font-family: 'fonts';
				.li {
					background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/a70d410eb1ab15c1cfa5d711a97ef028_400x178.png');
					background-size: 100% 100%;
					margin-bottom: 30rpx;
					width: 100%;
					height: 274rpx;
					padding: 51rpx 22rpx 33rpx 22rpx;

					.time {
						background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/4d0b7587857fec246a73c4f40217a734_400x36.png');
						background-size: 100% 100%;
						width: 520rpx;
						height: 47rpx;
						line-height: 47rpx;
						color: #fff;
						font-family: 'fonts_title';
						margin: 0 auto 20rpx auto;
						font-size: 28rpx;
						text-align: center;
					}

					.title {
						font-size: 28rpx;
						font-family: 'fonts';
						line-height: 50rpx;
						margin-bottom: 10rpx;
						width: 100%;
						color: #fff;
					}

					.info {
						color: #fff;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.describe {
							font-size: 28rpx;
							font-family: '黑体';
							width: 400rpx;
							font-style: italic;
							padding-left: 40rpx;
							color: #4CF7DE;
							text-align: center;
							padding: 6rpx 0px;
							color: #4CF7DE;
							width: 200rpx;
							border-radius: 12rpx;
							border: 1px solid #4CF7DE;
							margin-left: 20rpx;
							// height:56rpx;
						}

						.but {
							display: flex;
							justify-content: space-between;
							align-items: center;

							image {
								width: 132rpx;
							}

							// .active{
							// 	border:4rpx solid #fff;
							// 	border-radius:12rpx;
							// }
						}
					}
				}
			}
		}


	}

	.flex_view {
		width: 123rpx;
		height: 123rpx;
		position: fixed;
		right: 0rpx;
		bottom: 280rpx;
		z-index: 99;

		img {
			width: 100%;
			height: auto;
		}
	}

	.htmlContent {
		font-size: 28rpx;
	}

	::v-deep p {
		font-size: 30rpx;
		line-height: 1.5;

		font {
			line-height: normal;
		}
	}
</style>