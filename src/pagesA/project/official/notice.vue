<template>
	<view class="notice">
		<view class="box">
			<view>2023-12-10 10:25:12</view>
			<view>
				<view>
					<view></view>
					<view>新订单提醒</view>
				</view>
				<view>您的藏品《XXX》已售出，售出价格为￥xx.xx。</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onReachBottom() {},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 678rpx;
		margin: 40rpx auto;

		>view:nth-child(1) {
			width: 100%;
			text-align: center;
			margin-bottom: 20rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #999;
		}

		>view:nth-child(2) {
			width: 678rpx;
			height: 170rpx;
			background: #46454F;
			border-radius: 28rpx;
			padding-top: 14rpx;
			box-sizing: border-box;

			>view:nth-child(1) {
				width: 650rpx;
				height: 70rpx;
				background: #25232E;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				margin: 0 auto;
				font-weight: 400;
				font-size: 28rpx;
				color: #63EAEE;
				padding-left: 16rpx;
				box-sizing: border-box;
				margin-bottom: 20rpx;
			}

			>view:nth-child(2) {
				width: 650rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF;
				margin: 0 auto;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
		}
	}
</style>