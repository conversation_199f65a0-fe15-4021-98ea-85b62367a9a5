<template>
	<view class="notice">
		<view class="box" v-for="(item,index) in list" >
			<view>{{item.msgReceiveDate}}</view>
			<view>
				<view>
					<view class="icon">
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240407/c8f9432028ed42dc37e4bf0bcf179380_80x80.png" mode="widthFix"></image>
					</view>
					<view>{{item.fillTitle}}</view>
				</view>
				<view class="threeOver">{{item.fillContent}}</view>
			</view>
		</view>
		<view class="null_body" v-show = "list == ''">
			<view class="null">
				<view class="img">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
						mode="widthFix"></image>
				</view>
				<view class="text">
					暂无数据
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list:[],
				isLoadding:false
			}
		},
		onLoad(){
			this.getList()
		},
		onReachBottom() {},
		methods: {
			async getList() {
				let res = await this.$api.java_listPageUserBoxMsg({
					boxId: "3",
					pageNum: 1,
					pageSize: 15,
				});
				if (res.status.code == 0) {
					this.list = res.result.list
				}else if(res.status.code == 1002){
					this.isLoadding=true
					setTimeout(() => {
						this.isLoadding = false
						this.$Router.push({
							name: "mainLogin",
							// #ifdef H5
								params: {
									url: window.location.hash,
								},
							// #endif
						})
					}, 1500);
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.notice{
		padding-bottom:50rpx;
	}
	.box {
		width: 678rpx;
		margin: 40rpx auto;

		>view:nth-child(1) {
			width: 100%;
			text-align: center;
			margin-bottom: 20rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #999;
		}

		>view:nth-child(2) {
			width: 678rpx;
			background: #46454F;
			border-radius: 28rpx;
			padding:24rpx 0; 
			box-sizing: border-box;

			>view:nth-child(1) {
				width: 650rpx;
				height: 70rpx;
				background: #25232E;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				margin: 0 auto;
				font-weight: 400;
				font-size: 28rpx;
				color: #63EAEE;
				padding-left: 16rpx;
				box-sizing: border-box;
				margin-bottom: 20rpx;
			}

			>view:nth-child(2) {
				width: 650rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF;
				margin: 0 auto;
				line-height:34rpx;
			}
		}
	}
	.null_body {
		.null {
	
			.img {
				display: flex;
				justify-content: center;
	
				image {
					width: 242rpx;
				}
			}
	
		}
	
		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
	
		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.icon{
		width:40rpx;
		margin-right: 10rpx;
		image{
			width:40rpx;
			
		}
	}
</style>