<template>
	<view class="main">
		<u-toast ref="uToast" />
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}" title="我的地址" title-color="var(--default-color1)"
			title-bold>
		</u-navbar>
		<view class="item" v-for="(item, index) in addressList" :key="index">
			<view class="left">
				<view class="address">
					{{ item.address_s }}
				</view>
				<view class="info">
					<span>{{ item.name }}</span>
					<span>{{ item.phone }}</span>
				</view>
			</view>
			<view class="right" @click="nav_updataAddress(item)">
				<u-image src="@/static/imgs/public/edit.png" mode="widthFix" width="48"></u-image>
			</view>
		</view>
		<view class="footer" v-if="addressList.length">
			<button-bar class="button" @click='nav_addAddress()' text="添加新地址"></button-bar>
		</view>

		<view class="empty" v-if="isEmpty">
			<u-image width="48rpx" src="@/static/imgs/public/location.png" mode="widthFix"></u-image>
			<view class="title">您暂时没有添加的地址</view>
			<view class="des">添加地址信息，让您的下单流程更便捷</view>
			<button-bar class="button" @click='nav_addAddress()' text="添加新地址"></button-bar>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";

	export default {
		components: {
			ButtonBar
		},
		data() {
			return {
				addressList: [],
				isEmpty: false,
			}
		},
		onLoad() {
			this.getAddress()
		},
		methods: {
			nav_addAddress() {
				this.$Router.push({
					name: "addAddress"
				})
			},
			nav_updataAddress(item) {
				uni.setStorageSync("addressList", item)
				this.$Router.push({
					name: "updataAddress"
				})
			},
			async getAddress() {
				let res = await this.$api.address({})
				if (res.status.code === 0) {
					this.addressList = res.result.list
					this.addressList.forEach(item => {
						item.address_s = item.district + item.address
					})
					if (!this.addressList.length) {
						this.isEmpty = true
					}
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						type: 'default',
					})
				}
				// console.log(JSON.parse(res.data))
			},
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		.empty {
			margin-top: 514rpx;
			margin-left: calc(50% - 275rpx);
			width: 550rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			text-align: center;

			.title {
				width: 100%;
				margin: 40rpx 0;
				color: var(--main-front-color);
				font-size: 36rpx;
				font-weight: bold;
			}

			.des {
				width: 100%;
				color: var(--default-color3);
				font-size: 28rpx;
				margin-bottom: 60rpx;
			}

			.button {
				border-radius: 0;
				width: 100%;
				height: 80rpx;
				background: var(--main-bg-color);
				font-size: 28rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: var(--active-color1);
				border: var(--active-color1) solid 2rpx;
			}
		}

		.item {
			min-height: 166rpx;
			border-bottom: #25232D 4rpx solid;
			padding: 40rpx;
			color: var(--main-front-color);
			display: flex;
			justify-content: space-between;
			align-items: center;
			.left {
				width: 80%;

				.address {
					word-break: break-all;
					font-size: 28rpx;
					font-weight: bold;
					margin-bottom: 32rpx;
					line-height: 44rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
				}

				.info {
					font-size: 28rpx;
					color: var(--secondary-front-color);
					font-family: MiSans-Normal, MiSans;

					span {
						margin-right: 32rpx;
					}
				}
			}
		}

		.footer {
			position: fixed;
			bottom: 32rpx;
			left: 0rpx;
			margin: auto 40rpx;
			width: calc(100% - 80rpx);
			.button{
				border-radius:40rpx;
			}
		}
	}

	.ver-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
	}

	.main {
		height: 100%;
		position: relative;
	}

	.add-box {
		height: 160rpx;
		padding: 20rpx 42rpx;
		background-color: var(--message-box-point-color);
		border-top: 3rpx solid #eee;

		.add-info {
			width: 400rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.ai-top {
				font-weight: 700;

				span:nth-child(2) {
					margin-left: 20rpx;
					font-size: 28rpx;
				}
			}

			.ai-bottom {
				line-height: 35rpx;
				font-size: 28rpx;
				color: #666;
			}
		}

		.add-right {
			display: flex;
			justify-content: flex-start;
			align-items: flex-end;
			font-size: 28rpx;
		}

		.icon-arrow {
			color: #999;
		}
	}

	.add-btn {
		height: 90rpx;
		line-height: 90rpx;
		position: absolute;
		bottom: 76rpx;
		left: 42rpx;
		right: 42rpx;
		font-size: 32rpx;
		color: var(--message-box-point-color);
		background-color: #333;
		&:active {
			color: #333;
			background-color: var(--message-box-point-color);
		}
	}
</style>
