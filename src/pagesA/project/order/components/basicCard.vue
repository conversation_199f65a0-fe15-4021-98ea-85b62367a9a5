<template>
	<view class="card">
		<view class="title">
			<!-- <view class="title-left" >
				<view class="title-text">竞价购买</view>
			</view> -->
			<view>
				<!--  0-全部 1-已卖出 2-已买入 3-支付中 4-已取消 6-竞价购买 7-空投 8-销毁 -->
				<text v-if="item.type == 1"> 卖出时间：{{ item.payTime }}</text>
				<text
					v-else-if="item.type == 2 || item.type == 6 || item.type == 3 || item.type == 4 || item.type == 5 || item.type == 9">
					下单时间：{{ item.orderTime }}</text>
				<text v-else-if="type == 7"> 到账时间：{{ item.orderTime }}</text>
				<text v-else-if="type == 8"> 销毁时间：{{ item.orderTime }}</text>
			</view>
			<view v-if="item.type != 3 || item.type != 4">
				<!--  0-全部 1-已卖出 2-已买入 3-支付中 4-已取消 6-竞价购买 7-空投 8-销毁 -->
				<text v-if="item.type == 2 || item.type == 6 || item.type == 5"> 支付时间：{{ item.payTime }}</text>
				<text class="activeColor" v-if="item.type == 1">实际获得 :￥{{ item.sellerAmount ? item.sellerAmount : '-'
					}}</text>
			</view>
			<view v-if="item.type != 3 || item.type != 4">
				<text v-if="item.type == 9">
					<text v-show="item.sellBuy == 2">转售给其他人</text>
					<text v-show="item.sellBuy == 1">他人转售给我</text>
				</text>
			</view>
		</view>
		<view class="card-ctx">
			<image class="card-img" :src="item.cover" mode="aspectFill"></image>
			<view class="card-bottom">
				<view class="sub-top">
					<text class="subTitle">{{ item.title }}</text>
					<view class="subTitle-right" v-if="item.type == 3">
						<text class="price">￥{{ item.price }}</text>
						<u-button class="subTitle-btn" shape="circle" size="mini" @click="payment(item)">
							<text class="toPrice">去支付</text>
						</u-button>
					</view>
					<view class="subTitle-right" v-if="item.type === 4">
						<view class="subTitle-right-btn huise">
							<view class="calc">已取消</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
					<view class="subTitle-right" v-if="item.type === 1">
						<view class="subTitle-right-btn blue">
							<view class="calc">已卖出</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
					<view class="subTitle-right" v-if="item.type === 2">
						<view class="subTitle-right-btn red">
							<view class="calc">已买入</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
					<view class="subTitle-right" v-if="item.type == 6">
						<view class="subTitle-right-btn default">
							<view class="calc">竞价购买</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
					<view class="subTitle-right" v-if="item.type == 5">
						<view class="subTitle-right-btn default">
							<view class="calc">批量购买</view>
						</view>
						<text class="price">￥{{ item.price }}</text>
					</view>
					<view class="subTitle-right" v-if="item.type == 9">
						<view class="text">
							<view class="calc" v-if="item.status == 7">已取消</view>
							<view class="calc" v-if="item.status == 8">交易成功</view>

							<view class="calc" ref="paytime" v-if="item.status == 0 && item.sellBuy == 1">剩余支付时间
								<u-count-down @end="end" :show-days="false" :show-hours="true"
									:timestamp="item.restPaySecond" separator="zh" separator-size="22" font-size="22"
									bg-color="#25232d" color="#63eaee"></u-count-down>
							</view>

							<view class="calc" v-if="item.status == 0 && item.sellBuy == 2">等待他人付款</view>
						</view>
					</view>
				</view>
				<!-- 已取消 去支付 -->
				<view class="sub-bottom">
					<view class="item" style="opacity: .5;">
						<view>token ID：</view>
						<view class="flex">
							<text v-show="item.show">{{ item.tid }}</text>
							<text v-show="!item.show">{{ item.tidd }}</text>
							<image class="copy-img" src="../../../../static/imgs/public/copy.png" mode=" aspectFill"
								@click="copy(item.tid)">
							</image>
							<view class="show">
								<image class="copy-img" v-show="item.show"
									src="https://cdn-lingjing.nftcn.com.cn/image/20240308/8812274a59ce41c3cc2753903ac889bc_72x72.png"
									mode="widthFix" @click="check(item)">
								</image>
								<image class="copy-img" v-show="!item.show"
									src="https://cdn-lingjing.nftcn.com.cn/image/20240308/fe895b568b5e4befab21850132e60ad3_72x72.png"
									mode="widthFix" @click="check(item)">
								</image>
							</view>
						</view>

					</view>
					<!--  <view class="item">
            哈希值：NA
            <image
              class="copy-img"
              src="../../../../static//imgs//public/copy.png"
              mode=" aspectFill"
            ></image
          ></view> -->
					<view class="item" v-if="current !== 4">
						<view class="flex" v-show="item.orderNo">
							订单号：{{ item.orderNo }}
							<image class="copy-img" src="../../../../static//imgs//public/copy.png" mode=" aspectFill"
								@click="copy(item.orderNo)">
							</image>
							<u-button class="subTitle-btn-close" shape="circle" size="mini" @click="closeOrder(item)"
								v-if="item.type == 3">
								<text class="toPrice">取消订单</text>
							</u-button>
						</view>
					</view>
					<view class="itemtype" v-if="current == 4">
						<view class="flex_zs">
							<view class="price">￥{{ item.price }}</view>
							<u-button class="subTitle-btn-close" v-show="item.status == 0 && item.sellBuy == 2"
								shape="circle" size="mini" @click="closeOrder(item)">
								<text class="toPrice">取消转售</text>
							</u-button>
							<!--  v-show="item.status == 0 && item.sellBuy == 2"  v-show="item.status == 0 && item.sellBuy == 1"-->
							<u-button v-show="item.status == 0 && item.sellBuy == 1" class="subTitle-btn-pay "
								hover-class="none" shape="circle" size="mini" @click="payment(item)">
								<text class="payment">去支付</text>
							</u-button>


						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
/**
 * Badge 数字角标
 * @description 数字角标一般和其它控件（列表、9宫格等）配合使用，用于进行数量提示，默认为实心灰色背景
 * @tutorial https://ext.dcloud.net.cn/plugin?id=21
 * @property {String} text 角标内容
 * @property {String} type = [default|primary|success|warning|error] 颜色类型
 * 	@value default 灰色
 * 	@value primary 蓝色
 * 	@value success 绿色
 * 	@value warning 黄色
 * 	@value error 红色
 * @property {String} size = [normal|small] Badge 大小
 * 	@value normal 一般尺寸
 * 	@value small 小尺寸
 * @property {String} inverted = [true|false] 是否无需背景颜色
 * @event {Function} click 点击 Badge 触发事件
 * @example <uni-badge text="1"></uni-badge>
 */
export default {
	name: "BasicCard",
	props: {
		current: {
			type: Number,
			default: "default",
		},
		subCurrent: {
			type: Number,
			default: "default",
		},

		item: {
			type: Array,
			default: []
		},
		type: {
			type: Number,
		}
		// type: {
		//   type: String,
		//   default: "default",
		// },
		// inverted: {
		//   type: Boolean,
		//   default: false,
		// },
		// text: {
		//   type: [String, Number],
		//   default: "",
		// },
		// size: {
		//   type: String,
		//   default: "normal",
		// },
	},
	data() {
		return {
			//   badgeStyle: "",
			appUrl: ""
		};
	},
	watch: {
		// text() {
		//   this.setStyle();
		// },
	},
	mounted() {
		console.log(this.item);
		// this.setStyle();
		this.appUrl = getApp().globalData.urlZf
	},
	methods: {
		end() {
			this.$refs.paytime.style.display = 'none'
		},
		payment(item) {
			// #ifdef APP
			let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${item.orderNo}`
			console.log(url)
			this.$Router.push({
				name: "webView",
				params: {
					url,
				}
			})

			// #endif
			// #ifdef H5
			let { origin } = window.location
			window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${item.orderNo}`
			// #endif

		},
		copy(text) { //复制
			uni.setClipboardData({
				data: text,
				success() {
					uni.showToast({
						title: '已复制',
						icon: 'none'
					})
				}
			})
		},
		closeOrder(item) {
			this.$emit('closeOrder', item)
		},
		check(item) {
			console.log(item.show)
			item.show = !item.show
		}
	}
	//   methods: {
	//     setStyle() {
	//       this.badgeStyle = `width: ${String(this.text).length * 8 + 12}px`;
	//     },
	//     onClick() {
	//       this.$emit("click");
	//     },
	//   },
};
</script>

<style lang="scss" scoped>
::v-deep .u-countdown-colon {
	color: #fff !important;
}

.flex_zs {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 8rpx 0rpx;
	width: 100%;

	.price {
		color: #63eaee;
		font-size: 28rpx;
		font-weight: 600;
	}

	.subTitle-btn-close {
		border-radius: 20px;
		width: 150rpx;
		border: 1px solid #63eaee;
		line-height: 46rpx;
		text-align: center;
		height: 46rpx;
		color: #63eaee;
		margin: 0;

		.toPrice {
			font-size: 18rpx;
		}
	}

	.subTitle-btn-pay {
		border-radius: 20px;
		width: 150rpx;
		line-height: 46rpx;
		text-align: center;
		height: 46rpx;
		// color: #fff;
		background: var(--primary-button-color);
		margin: 0;

		.payment {
			z-index: 1 !important;
			color: #121212 !important;
		}

		.toPrice {
			font-size: 18rpx;
		}
	}
}

.card {
	margin: 0 36rpx;
	color: #fff;
	margin-bottom: 40rpx;
	height: 258rpx;
	background: #25232d;
	border-radius: 30rpx;
	overflow: hidden;

	.title {
		padding: 18rpx 32rpx;
		// opacity: 0.5;
		font-size: 18rpx;
		font-weight: 400;
		color: var(--default-color3);
		background: rgba(29, 27, 37, 0.5);
		display: flex;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title-left {
			height: 24rpx;
			border-radius: 4rpx;
			background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
			font-size: 24rpx;
			font-weight: 700;
			margin-right: 20rpx;
			padding: 2rpx 0;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.card-img {
		width: 148rpx;
		height: 148rpx;
		margin-right: 24rpx;
	}

	.title-text {
		transform: scale(0.8);
		font-size: 12px;
		/** 文本1 */
		color: rgba(37, 35, 45, 1);
	}

	.copy-img {
		width: 20rpx;
		height: 22rpx;
		margin-left: 10rpx;
	}

	.card-ctx {
		padding: 20rpx 20rpx 30rpx 30rpx;
		display: flex;
		justify-content: space-between;

		.subTitle {
			/** 文本1 */
			font-size: 24rpx;
			font-weight: 400;
			color: #fff;
			white-space: nowrap;
			/* 防止文本换行 */
			overflow: hidden;
			/* 隐藏超出容器的内容 */
			text-overflow: ellipsis;
			/* 显示省略号 */
			width: 140rpx;
		}

		.subTitle-right {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.text {
				color: #fff;
				font-size: 22rpx;
			}

			.price {
				margin-right: 20rpx;
				font-size: 28rpx;
				font-weight: 700;
				color: rgba(99, 234, 238, 1);
			}

			.subTitle-btn {
				border-radius: 20px;
				background: #63eaee;
				width: 100rpx;

				.toPrice {
					font-size: 20rpx;
					font-weight: 700;
					color: rgba(37, 35, 45, 1);
				}
			}
		}

		.card-bottom {
			flex: 1;
			height: 100%;
		}

		.sub-top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 40rpx;
		}

		.sub-bottom {
			// opacity: 0.5;
			/** 文本1 */
			font-size: 20rpx;
			font-weight: 400;
			color: #fff;
			margin-top: 10rpx;

			.item {
				margin-bottom: 8rpx;

				.flex {
					display: flex;
					align-items: center;
					// justify-content: flex-start;
					margin: 14rpx 0rpx;
				}

				.show {
					image {
						width: 30rpx;
					}
				}
			}

			.itemtype {
				opacity: 1 !important;

				margin-bottom: 8rpx;

				.flex {
					display: flex;
					align-items: center;
					// justify-content: flex-start;
					justify-content: space-between;
					margin: 14rpx 0rpx;
				}

				.show {
					image {
						width: 30rpx;
					}
				}
			}
		}
	}
}

.subTitle-right-btn {
	width: 90rpx;
	height: 24rpx;
	border-radius: 4rpx;

	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 9rpx;
	background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);

	&.default {
		background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
	}

	&.blue {
		background: linear-gradient(90deg, #44F0E7 0%, #4699EC 100%);
	}

	&.red {
		background: linear-gradient(90deg, #FE95CD 0%, #E54FB3 100%);
	}

	&.huise {
		background: linear-gradient(90deg, #80799F 0%, #524D68 100%);
	}

	.calc {
		transform: scale(0.8);
		font-size: 22rpx;
		/** 文本1 */
		color: #25232D;
	}
}

.activeColor {
	color: #63EAEE;
}

.subTitle-btn-close {
	border-radius: 20px;
	width: 100rpx;
	border: 1px solid #fff;
	background-color: transparent;

	.toPrice {
		font-size: 18rpx;
		color: #fff;
	}
}
</style>