<template>
	<view class="main">
		<view class="cart_view">
			<view class="log"></view>
			<view class="title_bg">
				<view class="icon"></view>
				恭喜您
			</view>
			<view class="head" v-if="heightChange">
				<view class="ver-center">
					<u-image src="@/static/imgs/public/checked.png" width="50" height="50"></u-image>
					<text style="margin-left: 20rpx;color:#fff;font-size:34rpx;font-weight:600 ;">购买成功</text>
				</view>
			</view>

			<view class="content" v-if="heightChange">
				<view class="c-bottom ver-space-around">
					<view><text>作品名称:</text>{{ List.item.name || '--' }}</view>
					<view><text>下单时间:</text>{{ List.order.orderTime || '--' }}</view>
					<view class="price"><text>订单金额:</text>￥{{ List.item.price || '--' }}</view>
					<view v-show="showActive"><text>支付方式:</text>{{ List.order.payMethod == 10 ? '余额支付' : '连连支付' }}
					</view>
				</view>
			</view>

			<view class="back_buttons" v-if="detailList && detailList.notSaleSign != 0">
				<view class="img_cancel"
					style="width: 100%;height: 80rpx;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;border: 1px solid #fff; color: #fff;display: flex;align-items: center;justify-content: center;" @click="toOrder()">
					返回
				</view>
			</view>

			<view class="back_buttons" v-if="detailList && detailList.notSaleSign == 0 && isfrombit == 1">
				<view class="img_cancel"
					style="width: 100%;height: 80rpx;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;border: 1px solid #fff; color: #fff;display: flex;align-items: center;justify-content: center;" @click="toOrder()">
					返回
				</view>
			</view>

			<view class="title_bg" v-if="detailList && detailList.notSaleSign == 0 && isfrombit == ''">
				<view class="icon"></view>
				快捷寄售
			</view>

			<view class="content quickUp" v-if="detailList && detailList.notSaleSign == 0 && isfrombit == ''">
				<view class="flex_input">
					<view class="label">寄售金额￥</view>
					<view class="input">
						<u-input class="modal-resale-input" v-model="resalePrice" placeholder="请输入金额" type="number"
							border border-color="transparent" :trim="true" :adjust-position="true"
							:show-confirmbar="true" :custom-style="{ 'padding-left': '25rpx' }" @input="changePrice"
							:clearable="false" />
					</view>
				</view>
				<view class="bili">
					<view class="li" v-for="(item, index) in biliList" :class="{ 'active': item.disable }"
						@click="checkend(item)">
						+{{ item.value }}%
					</view>
				</view>

				<view class="msg_tishi">
					<view class="li" v-for="(item, index) in contentList" v-if="index < 2">
						<view class="icon">
							{{ item.key }}
						</view>
						<view class="text">
							{{ item.value }}
						</view>
					</view>
				</view>

			</view>

			<view class="xieyi" v-if="detailList && detailList.notSaleSign == 0 && isfrombit == ''">
				<view class="p">
					<view class="xieyi_msg">
						<image class="img" @click="j_isAgree" v-if="!isAgree"
							src="https://cdn-lingjing.nftcn.com.cn/image/20240105/44d31c4b1ea6610c58e7d58968abf866_30x30.png"
							mode="">
						</image>
						<image class="img" @click="j_isAgree" v-else
							src="https://cdn-lingjing.nftcn.com.cn/image/20240305/a23c845a88f3c94e44741df44a0ad101_30x30.png"
							mode="">
						</image>
						<text>
							<text @click="j_isAgree">已阅读并同意上述价格规则及</text>
							<text class="protocol" @click="nav_link('平台用户售卖服务协议', 1)">《平台用户售卖服务协议》</text>
						</text>
					</view>
				</view>
			</view>

			<view class="buttons" v-if="detailList && detailList.notSaleSign == 0 && isfrombit == ''">
				<view class="img_cancel" @click="toOrder()">
					返回
				</view>
				<view class="img_reasale" @click="reasaleConfirm()">
					寄售
				</view>
			</view>

		</view>
		<!-- 快捷寄售弹窗 -->
		<!-- 	<resale-pop :isShowModalResale.sync="isShowModalResale" :itemId="List.tid" @closeResale="closeResale">
		</resale-pop> -->
		<pay-popup :popup-show.sync="isPasswordImport" order-type="O" @pay="finishPay" />
		<u-modal v-model="isShowMsg" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					恭喜您
				</view>
				<view class="modal-content">
					{{ tip }}
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="confirm()">仍要寄售</view>
					<view class="img_reasale" @click="cancel()">修改价格</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isConfirmWorks" :content-style="bgObject" :title-style="titleObject" border-radius="30"
			:show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content">
					是否以{{ resalePrice }}元寄售此作品？
				</view>
				<view class="showModal-btn">
					<view class="img_reasale" @click="confirmWorks()">仍要寄售</view>
					<view class="img_cancel" @click="cancelWorks()">修改价格</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isSuccess" :content-style="bgObject" :title-style="titleObject" border-radius="30"
			:mask-close-able="true" :show-title="false" :show-confirm-button="false" width="480">
			<view class="new-modal-content">
				<view class="success_img">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240322/942264f29d34245fa78a23becfe96b87_480x480.png"
						mode="widthFix"></image>
				</view>
				<view class="modal-content" style="border-bottom:none;">
					作品寄售成功
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import payPopup from "@/components/payPopup/index.vue";
import antiShake from "@/common/public.js"
export default {
	components: {
		payPopup
	},
	data() {
		return {
			isfrombit: '',
			orderId: "",
			List: {
				item: "",
				order: ""
			},
			detailList: {},
			isShowModalResale: false, // 是否快捷寄售
			firstBuy: false, // 是否第一次购买弹窗
			fastCon: false, // 快捷寄售
			isBlindBox: true,
			picPath: "https://cdn-lingjing.nftcn.com.cn/upload/2021/10/15/<EMAIL>",
			method: "",
			is: false,
			isBoxWin: false,
			isPasswordImport: false, // 密码弹窗
			passwordImportPay: '',
			resalePrice: '', // 寄售价格
			isSuccess: false, // 寄售成功
			resale: false, // 寄售成功之后的状态
			batchBuyNum: 1,
			platform: "",
			isApp: false,
			isSeize: 0,
			biliList: [{
				value: "5",
				disable: false
			}, {
				value: "10",
				disable: false
			}, {
				value: "15",
				disable: false
			}, {
				value: "20",
				disable: false
			}, {
				value: "25",
				disable: false
			}, {
				value: "30",
				disable: false
			}],
			isAgree: '', // 是否同意
			contentList: [],
			tip: "",
			titleObject: {
				'background-color': '#FFF',
				'color': '#FFFFFF'
			},
			bgObject: {
				'background-color': '#FFF',
				'color': '#FFFFFF'
			},
			isShowMsg: false,
			topTip: "",
			isConfirmWorks: false,
			// #ifdef H5
			docmHeight: document.documentElement.clientHeight, //默认屏幕高度
			showHeight: document.documentElement.clientHeight, //实时屏幕高度
			// #endif

			heightChange: true,
			url: "",
			showActive: false

		};
	},
	watch: {
		isSuccess(newValue, oldValue) {
			if (newValue) {
				setTimeout(() => {
					this.isSuccess = false
					this.toIndex()
				}, 3000);
			}
		},
		resalePrice(newValue, oldValue) {
			if (newValue) {
				this.changePrice()
			}
		},
		showHeight() {
			if (this.docmHeight > this.showHeight) {
				this.heightChange = false;
			} else {
				this.heightChange = true;
			}
		},
	},
	onLoad(option) {
		this.isfrombit = uni.getStorageSync('isfromEx')

		this.orderId = option.orderId
		this.getOrderId()

		const {
			platform,
		} = option;
		if (platform) {
			this.platform = platform;
			switch (platform) {
				case 'ios':
					this.isApp = true;
					break;
				case 'android':
					this.isApp = true;
					break;
				default:
			}
		}
		// if(uni.getStorageSync("mold")==2){
		// 	this.isBlindBox=true
		// }
		// this.List=uni.getStorageSync("orderList")
		// this.List.created_at = this.$u.timeFormat(this.List.created_at, "yyyy-mm-dd hh:MM")
		// #ifdef H5
		window.onresize = () => (() => {
			console.log(document.body.clientHeight)
			this.showHeight = document.body.clientHeight;
		})();
		// #endif
		// #ifdef APP
		if (uni.getSystemInfoSync().platform == 'ios') {
			this.get_version()
		}
		// #endif
	},
	methods: {
		toOrder() {
			let from = uni.getStorageSync('isfromEx')
			if (from) {
				this.$Router.pushTab({
					name: "contract-BITindex",
				})
				uni.removeStorageSync('isfromEx');
			} else {
				if (this.isApp) {
					console.log(333)
					// // this.$native.orderList();
					this.myUni.webView.switchTab({
						url: `/pages/project/personal/index`
					});
					// uni.switchTab({
					// 	url: '/pages/project/personal/index'
					// });
				} else {
					this.$Router.pushTab({
						name: "personal",
					})
				}
			}

		},
		toIndex() {
			if (this.isApp) {
				this.myUni.webView.switchTab({
					url: `/pages/project/index/index`
				});
			} else {
				this.$Router.pushTab({
					name: "mall"
				})
			}
		},
		async getOrderId() {

			let res = await this.$api.java_order_detail({
				orderId: this.orderId,
				isBuyer: 1
			});

			if (res.status.code == 0) {
				this.detailList = res.result.item
				console.log(this.detailList);
				this.List = res.result

				console.log(this.List, this.detailList, '订单详情');
				if (this.List.onSaleMaxPrice) {
					this.biliList.forEach((item) => {
						let itemPrice = parseInt(this.List.price) + (parseInt(this.List.price) *
							item.value / 100)
						if (itemPrice > this.List.onSaleMaxPrice) {
							item.disable = true
						}
					})
				}
			} else {
				uni.showToast({
					title: "请求错误",
					icon: "none",
					duration: 3000
				});
			}

		},
		close() {
			this.isBoxWin = false
			// this.$Router.push({
			// 	name: "giveFaceBuy"
			// })
		},
		// 新手寄售指引之后跳转至藏品
		goSelf() {
			if (this.isApp) {
				this.myUni.webView.switchTab({
					url: `pages/project/personal/index`
				});
			} else {
				this.$Router.pushTab({
					name: "personal",
					params: {
						tabCurrent: 1
					}
				})
			}
		},
		// 新手快捷寄售
		goFastCon() {
			this.isShowModalResale = true
		},
		// 关闭快捷寄售
		closeResale(data) {
			this.isShowModalResale = false
			if (!data) return
			// console.log('取消关闭');
			this.resalePrice = data
			this.isPasswordImport = true
		},
		// 关闭成功寄售弹框之后的处理逻辑
		changeClose() {
			// 寄售之后, 关闭弹窗设置已经寄售状态
			this.resale = true
			// console.log('触发了吗');
			// this.getOrderId()
		},
		finishPay(e) {
			this.passwordImportPay = e
			this.isPasswordImport = false
			console.log(this.passwordImportPay, '3333333333')
			this.collectionPutaway(e)
		},
		// 忘记密码
		nav_forgetPayPassword() {
			this.$Router.push({
				name: 'forgetPayPassword'
			});
		},
		async collectionPutaway(password) { //提交藏品寄售
			const params = {
				tid: this.List.item.tokenId,
				price: this.resalePrice,
				tradePassword: password
			}
			// 寄售成功按钮
			let res = await this.$api.visibility(params);
			if (res.status.code === 0) {
				// 关掉快捷寄售弹窗
				this.passwordImportPay = ""
				this.isPasswordImport = false
				// 弹出成功寄售弹窗
				this.isSuccess = true
				setTimeout(() => {
					this.$Router.pushTab({
						name: "putaway"
					})
				}, 2000)
			} else {
				this.passwordImportPay = ""
				this.isPasswordImport = false
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 2000
				});
			}
		},
		closeSuccess() { },
		backShop() {
			console.log(this.$Router)
			this.$Router.back(2)
		},
		j_isAgree() {
			this.isAgree = !this.isAgree
		},
		nav_link(title, index) {
			if (index == 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com.cn/link/#/pages/index/saleAgreement"
					}
				})
			}
		},
		changePrice() {
			if (this.resalePrice == "") {
				this.contentList = []
			} else {
				let servicePrice, royalty, buyPrice
				servicePrice = (this.resalePrice * 0.04).toFixed(2)
				royalty = (this.resalePrice * 0.025).toFixed(2)
				buyPrice = (this.resalePrice - servicePrice - royalty).toFixed(2)
				let list = [{
					"key": "服  务  费",
					"value": `成交价的4%, 为${servicePrice}元`
				},
				{
					"key": "版        税",
					"value": `成交价的2.5%, 为${royalty}元`
				},
				{
					"key": "购入价格",
					"value": `预计寄售可得${buyPrice}元`
				}
				]
				this.contentList = list
				console.log(this.contentList);
			}
		},
		antiShakeclick: antiShake._debounce(function () {
			this.calculatePrice(this.resalePrice)
		}, 200),
		async calculatePrice(price) {
			if (!price) return
			let res = await this.$api.calculatePrice({
				price: price,
				tid: this.List.item.tokenId
			})
			if (res.result != "") {
				this.contentList = res.result.list
				this.tip = res.result.tip,
					this.topTip = res.result.topTip
			} else {
				uni.showToast({
					title: res.msg,
					icon: "none",
					duration: 3000
				});
			}
			// console.log(this.contentList)
		},
		checkend(item) {
			if (!item.disable) {
				console.log(parseInt(this.List.item.price) * item.value / 100)
				this.resalePrice = Math.round(parseInt(this.List.item.price) + (parseInt(this.List.item.price) * item
					.value / 100))
			}
		},
		// 一键寄售确定按钮
		async reasaleConfirm() {
			if (this.isAgree) {
				if (this.resalePrice === '') {
					uni.showToast({
						title: "金额不能为空",
						icon: "none",
						duration: 2000
					});
				} else if (this.resalePrice < 1) {
					uni.showToast({
						title: `金额不能低于1元`,
						icon: "none",
						duration: 2000
					});
				} else if (this.List.onSaleMaxPrice) {
					if (this.resalePrice > this.List.onSaleMaxPrice) {
						uni.showToast({
							title: `该藏品挂售价暂不可高于${this.List.onSaleMaxPrice}元!`,
							icon: "none",
							duration: 2000
						});
					} else if (this.tip != "") {
						this.isShowMsg = true
					} else {
						this.isConfirmWorks = true
					}
				} else if (this.resalePrice > 100000000 || this.resalePrice == 100000000) {
					uni.showToast({
						title: "作品必须在1亿以内",
						icon: "none",
						duration: 2000
					});
				} else if (this.tip != "") {
					this.isShowMsg = true
				} else {
					this.isConfirmWorks = true
				}
			} else {
				uni.showToast({
					title: "请先勾选协议",
					icon: "none",
					duration: 2000
				});
			}
		},
		cancel() {
			this.isShowMsg = false
		},
		confirm() {
			this.isShowMsg = false
			this.isPasswordImport = true
			// this.$emit('closeResale', this.resalePrice)
		},
		confirmWorks() {
			this.isConfirmWorks = false
			this.isPasswordImport = true
			// this.resalePrice = ''
			// this.contentList = ''
		},
		cancelWorks() {
			this.isConfirmWorks = false
		},
		copy(text) { //复制log
			uni.setClipboardData({
				data: text,
				success() {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				}
			})
		},
		async get_version() {
			let res = await this.$api.java_commonconfigInfo({
				name: 'ios_apple_pay_version',
			});
			if (res.status.code == 0) {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = res.result.value
				console.log(curV)
				console.log(reqV)
				if (curV == reqV) {
					if (uni.getSystemInfoSync().platform == 'ios') {
						this.showActive = false
					} else {
						this.showActive = true
					}
				} else {
					this.showActive = true
				}
				console.log(this.showActive)
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},

	},
}
</script>

<style lang="scss" scoped>
.u-drawer::v-deep {
	.u-mode-center-box {
		background: var(--dialog-bg-color) !important;
	}

}

.back_buttons {
	>.img_cancel {
		height: 80rpx;
		font-size: 24rpx;
		font-weight: bold;
		border-radius: 14rpx;
		border: 1px solid #fff;
		color: #fff;
	}

}

.buttons {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;

	>view {
		width: 266rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		font-weight: bold;
		border-radius: 14rpx;
	}

	.img_cancel {
		border: 1px solid #fff;
		color: #fff;
	}

	.img_reasale {
		width: 266rpx;
		background: var(--primary-button-color);
	}
}

.xieyi {
	// width: 100%;
	margin: 40rpx 0rpx;

	.p {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 34rpx;
			height: 34rpx;
			margin-right: 10rpx;
		}

		.xieyi_msg {
			display: flex;

			.img {
				margin-top: 4rpx;
				display: inline-block;
			}

			font-size: 24rpx;
			color: #999999;
			line-height: 36rpx;

			.protocol {
				color: #63EAEE;
				text-decoration: underline;
			}
		}
	}
}

.password-pop::v-deep {
	.u-drawer-content {
		background: var(--dialog-bg-color) !important;
	}
}

.flex-start {
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
}

.ver-center {
	display: flex;
	// flex-direction: column;
	justify-content: center;
	align-items: center;
}

.ver-space-around {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.space-between {
	display: flex;
	justify-content: space-between;
}

page {
	background: var(--main-bg-color);
}

.head {
	padding: 40rpx 0 40rpx 0;
	text-align: center;
	font-size: 34rpx;
	color: var(--message-box-point-color);
	font-size: 32rpx;

	.back {
		position: absolute;
		top: 36rpx;
		left: 40rpx;
		z-index: 99;

		image {
			width: 46rpx;
		}
	}

	.head-price {
		margin-top: 36rpx;
		font-size: 36rpx;
	}

	// background-color: #333;
}

.main {
	background-color: rgba(0, 0, 0, 0.4);
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0rpx;
	min-height: 100vh;

	.cart_view {
		width: 638rpx;
		border-radius: 30rpx;
		background-color: #35333E;
		padding: 40rpx;
	}
}

.content {
	margin: 0 auto 32rpx;
	padding: 10rpx 24rpx;
	height: 260rpx;
	border-radius: 4rpx;

	.c-bottom {
		height: 232rpx;
		font-size: 28rpx;
		color: #fff;

		.price {
			color: #63EAEE;
		}

		color: #fff;

		text {
			margin-right: 40rpx;
			vertical-align: top;
			color: #A6A6A6;
		}

		.btn {
			width: 622rpx;
			height: 68rpx;
			margin-top: 32rpx;
			border-radius: 2rpx;
			font-size: 28rpx;
		}
	}

	&.c-content {
		height: 328rpx;
		padding: 48rpx 24rpx;
	}

	.seccess-content {
		text-align: center;
		font-size: 32Rpx;
		font-weight: 500;
		color: var(--main-front-color);

		.u-image {
			margin: 100rpx auto 24rpx;
		}
	}

}

.bottom {
	padding: 60rpx 42rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;

	.u-btn {
		width: 322rpx;
		height: 68rpx;
		border-radius: 2rpx;
		background-color: var(--main-bg-color);
		border: 2rpx solid #616161;
		color: #00FBEF;
	}
}

.warp {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.rect {
	width: 600rpx;
	height: 600rpx;
	display: flex;
	justify-content: center;
	align-items: center;

	image {
		width: 600rpx;
	}
}

.box_model {
	.header {
		display: flex;
		justify-content: flex-end;
		align-items: center;

		image {
			width: 50rpx;
			height: 50rpx;
		}
	}

	padding: 20rpx 30rpx 100rpx 30rpx;

	.icon_view {
		width: 340rpx;
		margin: 60rpx auto;

		image {
			width: 280rpx;
			margin-right: 20rpx;
		}

		font-size:32rpx;
		font-weight:600;
		color:#48446E;
	}

	.font_view {
		text-align: center;

		view {
			font-size: 32rpx;
			color: var(--main-text-color);
			font-weight: 600;
			margin-bottom: 20rpx;
		}

		text {
			font-size: 24rpx;
			color: #888888;
		}
	}

	.but {
		margin-top: 30rpx;

		button {
			background: var(--primary-button-color);
			border-radius: 0;
			height: 88rpx;
			width: 100%;
			line-height: 88rpx;
			color: #000;
			font-size: 30rpx;
		}

		.msg {
			font-size: 24rpx;
			margin-top: 30rpx;
			color: #888888;
			text-align: center;
		}
	}
}



// .blind-box-frame {
// 	width: 100%;
// 	height: 100%;
// 	background: url(../../../static/imgs/public/blind-box-frame.png) top/cover no-repeat;
// }

.modal_head_password {
	font-size: 32rpx;
	font-weight: 600;
	text-align: center;
	height: 80rpx;
	line-height: 80rpx;
	margin-top: 42rpx;
	position: relative;
	color: var(--main-front-color);

	.left {
		position: absolute;
		left: 40rpx;
		top: 20rpx;

		image {
			width: 30rpx;
		}
	}

	.right {
		position: absolute;
		right: 40rpx;
		top: 0rpx;
		font-weight: 500;
		font-size: 24rpx;
	}
}

.quickUp {
	padding: 24rpx 0rpx;
	height: auto;

	.title {
		padding: 40rpx 0rpx 50rpx 0rpx;
		color: #00FFF0;
		font-size: 32rpx;
		text-align: center;
	}

	.flex_input {
		display: flex;
		justify-content: center;
		align-items: center;

		.label {
			color: #fff;
			font-size: 28rpx;
		}

		.input {}
	}

	.bili {
		padding: 40rpx 0rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.li {
			width: 120rpx;
			margin-right: 40rpx;
			height: 50rpx;
			font-size: 24rpx;
			color: #BBBABB;
			display: flex;
			justify-content: center;
			align-items: center;
			border-radius: 10rpx;
			border: 1px solid #A6A6A6;

			&.active {
				background-color: #424242;
				border: none;
			}

		}

		.li:nth-child(3),
		.li:nth-child(6) {
			margin-right: 0;
		}

		.li:nth-child(1),
		.li:nth-child(2),
		.li:nth-child(3) {
			margin-bottom: 44rpx;
		}
	}

	.msg_tishi {
		padding: 35rpx 0rpx 30rpx 0rpx;

		.li {
			display: flex;
			justify-content: flex-start;
			margin-bottom: 40rpx;

			.icon {
				font-size: 24rpx;
				width: 160rpx;
				height: 44rpx;
				border-radius: 10rpx;
				line-height: 44rpx;
				padding: 0rpx 8rpx;
				color: #A6A6A6;
				text-align: right;
			}

			.text {
				font-size: 24rpx;
				line-height: 42rpx;
				margin-left: 30rpx;
				color: #fff;
				width: 460rpx;
			}
		}

		.li:last-child {
			margin-bottom: 0rpx;
		}

	}




}

.modal-resale-input::v-deep {
	margin: 0 auto;
	color: #FFF;
	border-radius: 0;

	.u-input__input {
		color: #63EAEE !important;
		font-weight: 600;
		font-size: 34rpx;
		padding: 0rpx 20rpx;
		background-color: #25232D;
		border-radius: 14rpx;
		width: 240rpx;
	}

	.uni-input-placeholder {
		font-weight: 400 !important;
		font-size: 32rpx !important;
	}
}

.tip {
	background-color: #2B2B2B;
	font-size: 24rpx;
	height: 88rpx;
	color: #999999;
	display: flex;
	align-items: center;
	padding: 0rpx 40rpx;
}

.modal-content {
	padding: 40rpx 40rpx 0rpx 40rpx;
	font-size: 28rpx;
	line-height: 40rpx;
	text-align: center;
}

.title_bg {
	font-size: 34rpx;
	font-weight: 600;
	width: 240rpx;
	position: relative;
	text-align: center;
	margin: 0 auto;
	margin-bottom: 10rpx;
	color: #fff;

	.icon {
		position: absolute;
		left: 0rpx;
		top: 16rpx;
		width: 240rpx;
		height: 8rpx;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
		background-size: 100%;
	}
}

.new-modal-content {
	padding: 35rpx 40rpx;
	background: var(--main-bg-color);

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 160rpx;
			height: 160rpx;
		}
	}

	.modal-content {
		padding: 35rpx 0rpx;
		border-bottom: 1rpx solid #53505D;
		font-size: 28rpx;
		color: #fff;

	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 226rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 14rpx;
			color: #fff;
		}

		.img_cancel {
			border: 1px solid #fff;
		}

		.img_reasale {
			color: var(--default-color2);
			background: var(--primary-button-color);
		}
	}
}
</style>