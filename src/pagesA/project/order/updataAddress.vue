<template>
	<view class="main">
		<u-toast ref="uToast" />
		<u-navbar back-icon-color="var(--main-front-color)" :border-bottom="false"
			:background="{backgroundColor: 'var(--main-bg-color)'}"  back-icon-size="0" title="编辑地址"
			title-color="var(--default-color1)" title-bold>
			<view slot="right" class="confirm" @click="config()"> 确认</view>
		</u-navbar>
		<view class="content">
			<input-bar v-model="form[item.key]" :item="item" v-for="item in inputList" :key="item.key"
				@click.native.stop="clickInput(item.type)"></input-bar>
			<u-picker mode="region" :params="form.address" v-model="isShowPicker" @confirm="confirm($event)"
				confirm-color="#00FBEF" cancel-color="#b3b1b1">
			</u-picker>
			<view class="switch">
				<view class="label">设置为默认地址</view>
				<u-switch v-model="form.isDefault" size="40" active-color="var(--active-color1)"
					inactive-color="var(--default-color3)"></u-switch>
			</view>
		</view>

		<view class="footer">
			<button-bar class="button" @click='showPopup = true' text="删除该地址"></button-bar>
		</view>
		<!-- 弹出框 -->
		<popup-bar v-model="showPopup" @confirm="deleteAddress()" content="" @cancel="showPopup = false" title="确认删除该地址吗?">
		</popup-bar>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	import InputBar from "@/components/public/InputBar";
	import popupBar from "@/components/public/PopupBar";
	export default {
		components: {
			ButtonBar,
			InputBar,
			popupBar
		},
		data() {
			return {
				inputList: [{
						label: '姓名',
						type: 'input',
						key: 'name',
						placeholder: '请输入姓名',
					},
					{
						label: '手机号',
						type: 'input',
						key: 'phone',
						placeholder: '请输入手机号',
					},
					{
						label: '所在省市',
						type: 'select',
						key: 'showAddress',
						placeholder: '请选择所在省市',
						disabled: true,
					},
					{
						label: '详细地址',
						type: 'input',
						key: 'detail',
						placeholder: '请输详细地址',
					},
				],
				form: {
					name: '',
					phone: '',
					address: {
						province: true,
						city: true,
						area: true
					},
					showAddress: '',
					detail: '',
					isDefault: false
				},
				address: '',
				isShowPicker: false,
				addressList: [],
				addressId: "",
				showPopup: false,
				type:''
			}
		},
		onLoad(option) {
			this.addressId = option.addressId
			this.addressList = uni.getStorageSync("addressList")
			this.form.name = this.addressList.name
			this.form.phone = this.addressList.phone
			this.form.detail = this.addressList.address
			this.form.showAddress = this.address = this.addressList.district
			this.form.isDefault = this.addressList.isDefault == 1;
			this.type = option.type
		},
		methods: {
			clickInput(key) {
				if (key === 'select') {
					this.isShowPicker = true;
				}
			},
			confirm(e) {
				this.form.address.province = e.province;
				this.form.address.city = e.city;
				this.form.address.area = e.area;
				this.address = this.form.address.province.label + this.form.address.city.label + this.form.address.area
					.label;
				this.form.showAddress = this.address;
			},
			config() {
				if (!this.form.name) {
					this.$refs.uToast.show({
						title: '请填写收货人姓名',
						type: 'default',
					})
				} else if (!this.form.phone) {
					this.$refs.uToast.show({
						title: '请填写收货人手机号',
						type: 'default',
					})
				} else if (this.form.phone.length < 11) {
					this.$refs.uToast.show({
						title: '收货人手机号不正确',
						type: 'default',
					})
				} else if (!this.form.showAddress) {
					this.$refs.uToast.show({
						title: '请选择省市区',
						type: 'default',
					})
				} else if (!this.form.detail) {
					this.$refs.uToast.show({
						title: '请填写详细地址',
						type: 'default',
					})
				} else {
					this.updateAddress()
				}
			},
			async updateAddress() {
				uni.showLoading({})
				let address, status;
				// address = this.address + "|" + this.form.detail
				console.log(address)
				if (this.form.isDefault) {
					status = 1
				} else {
					status = 0
				}
				let res = await this.$api.editAddress({
					consigneeId: this.addressList.consigneeId,
					name: this.form.name,
					phone: this.form.phone,
					district: this.address,
					address: this.form.detail,
					isDefault: status,
				})
				if (res.status.code === 0) {
					this.$refs.uToast.show({
						title: "修改成功",
						type: 'default',
					})
					setTimeout(() => {
						if (this.addressId !== undefined) {
							if(this.type=='integral'){
								this.$Router.back()
							}else{
								this.$Router.push({
									name: "shopOrder",
									params: {
										tid: uni.getStorageSync("detailsList").tid,
										addressId: this.addressId
									}
								})
							}
							
						} else {
							this.$Router.push({
								name: "addressManagement"
							})
						}
					}, 1000)
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						type: 'default',
					})
				}
			},
			async deleteAddress() {
				uni.showLoading({})
				let res = await this.$api.delAddress({
					consigneeId: this.addressList.consigneeId,
				})
				if (res.status.code === 0) {
					this.$refs.uToast.show({
						title: "删除成功",
						type: 'default',
					})
					this.showPopup = false
					setTimeout(() => {
						this.$Router.push({
							name: "addressManagement"
						})
					}, 1000)
				} else {
					this.$refs.uToast.show({
						title: res.status.msg,
						type: 'default',
					})
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	::v-deep .uni-picker-view-group {
		background-color: #1E1E1E;
	}

	::v-deep .u-column-item {
		color: #b3b1b1;
		background-color: #1E1E1E;
	}

	::v-deep .uni-picker-view-mask {
		opacity: 0.2;
	}

	::v-deep .u-picker-header {
		background-color: #474747;
	}

	.ver-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
	}

	.main {
		height: 100%;
		position: relative;

		::v-deep {
			.u-back-wrap {
				padding-left: 40rpx;
				color: var(--default-color3);
				font-size: 28rpx;
			}

			.label {
				color: var(--default-color3);
			}
		}

		.confirm {
			margin-right: 40rpx;
			color: var(--active-color1);
			font-weight: bold;
			font-size: 28rpx;
		}

		.content {
			padding: 0 40rpx;

			.switch {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 104rpx;
				font-size: 28rpx;

				.label {
					color: var(--main-front-color);
				}
			}
		}
	}

	//.top,
	//.center,
	//.bottom {
	//    padding: 0 42rpx;
	//    background-color: var(--message-box-point-color);
	//}
	//
	//.top {
	//    border-top: 3rpx solid #eee;
	//}
	//
	//.center {
	//    margin: 20rpx 0;
	//}
	//
	//.bottom {
	//    span {
	//        line-height: 40rpx;
	//        font-weight: 700;
	//    }
	//
	//    i {
	//        line-height: 40rpx;
	//        color: #666;
	//    }
	//}

	.footer {
		position: fixed;
		bottom: 32rpx;
		left: 0rpx;
		margin: auto 40rpx;
		width: calc(100% - 80rpx);
	}

	.button {
		border-radius: 0;
		width: 100%;
		height: 80rpx;
		background: var(--main-bg-color);
		font-size: 28rpx;
		font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: var(--active-color1);
		border: var(--active-color1) solid 2rpx;
	}
</style>
