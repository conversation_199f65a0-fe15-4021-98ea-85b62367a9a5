<template>
	<view class="main">
		<view class="border_view"></view>
		<view class="info">
			<view class="i-top space-between">
				<u-image :src="this.detailsList.item.coverImage.src" border-radius="30" width="200" height="200"></u-image>
				<view class="it-center">
					<view class="it_name oneOver">{{this.detailsList.item.name}}</view>
					<view class="it_version" >x1</view>
					<view class="it_price" >￥{{this.detailsList.item.price}}</view>
				</view>
			</view>
		</view>
		<view class="border_view"></view>
		<view class="isRenew" v-if="renewList!=''">
			<view class="title">延长续费</view>
			<view class="cart_view">
				<view class="cart" v-for="(item,index) in renewList" v-if="index>0" @click="checkRenew(item,index)">
					<view class="name">
						{{item.title}}
					</view>
					<view class="domain">
						{{detailsList.item.showVersion}}
					</view>
					<view class="bottom">
						<view class="price">
							￥{{item.price}}
						</view>
						<view class="icon">
							<image v-if="checkend==index"  src="https://cdn-lingjing.nftcn.com.cn/image/20230307/ce03341d23cd26dd6ec99996cddd03a9_37x37.png" mode="widthFix"></image>
							<image v-else src="https://cdn-lingjing.nftcn.com.cn/image/20230307/9223ea3204600e847b6d7e234d164f90_37x37.png" mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="pay">
			<view class="actual-pay">
				总额：<view class="strong">{{this.detailsList.item.price}}</view>
			</view>
			<view class="pay-btn" @click="config()">{{isDomain? isDomain==1?'注册并支付':isDomain==2?'确认续费':'确认支付':"提交订单"}}</view>
		</view>
		<u-modal class="" v-model="isSubmit" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				订单提交中...
			</view>
		</u-modal>
		<u-modal class="" v-model="isAnquan" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				安全校验中...
			</view>
		</u-modal>
		<u-modal v-model="isWarning" font-size="40" :show-title="false" width="65%" :mask-close-able="true"
			:show-confirm-button="false">
			<view style="padding:35rpx;">
				<view style="text-align:center;margin:20rpx 0rpx;font-size:34rpx;">
					温馨提示
				</view>
				<view class="flex_all" style="font-size:28rpx;line-height:40rpx;text-align:left;">
					{{isWarningText}}
				</view>
			</view>
			<view class="modal-btn flex_all">
				<view class="mb-confirm" style="width:70%;" @click="isWarning=false">我知道了</view>
			</view>
		</u-modal>
		<u-modal v-model="isRegistration"
			 border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content">
					<p>你还未实名认证</p>
					请前往实名认证
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isRegistration = false">取消</view>
					<view class="img_reasale" @click="nav_realName()">前往实名</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isPayError" font-size="40" :show-title="false" width="80%" :mask-close-able="true"
			:show-confirm-button="false">
			<view class="autonym_model" style="padding:40rpx;">
				<view class="title">
					<view style="text-align: center;font-size:40rpx;">温馨提示</view>
					<view>
						<!-- 身份实名认证 -->
					</view>
				</view>
				<view class="msg" style="font-size:30rpx;text-align: center;">
					{{isPayErrorText}}
				</view>
				<view class="button_db flex_between_x" style="font-size:22rpx;">
					<view class="button" style="margin-right:40rpx;" @click="isPayError=false">取消</view>
					<view class="button active" @click="nav_order()">去支付</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class="sk-wave">
				<div class="sk-rect sk-rect-1"></div>
				<div class="sk-rect sk-rect-2"></div>
				<div class="sk-rect sk-rect-3"></div>
				<div class="sk-rect sk-rect-4"></div>
				<div class="sk-rect sk-rect-5"></div>
			</div>
			<view class="text_msg" style="
		  padding: 10rpx 20rpx 40rpx 20rpx;
		  text-align: center;
		  font-size: 26rpx;
		  line-height: 40rpx;
		">
				跳转登录中...
			</view>
		</u-modal>
	</view>
</template>

<script>
	import fa from "@/common/public.js";
	export default {
		data() {
			return {
				isShowAddressPopup: false,
				num: 1,
				tid: "",
				detailsList: [],
				addressList: [],
				version: "",
				defaultAddress: [],
				addressId: "",
				isSubmit: false,
				isWarning: false,
				isError: false,
				isRegistration: false,
				isPayError: false,
				isPayErrorText: "",
				isWarningText: "",
				validate: "",
				ctId: "",
				verifyID: "",
				isAnquan: false,
				collection_id: "",
				collection_name: "",
				creator_id: "",
				creator_name: "",
				item_id: "",
				item_name: "",
				price: "",
				leapPlanLevel: 0,
				isLoadding: false,
				isLeapPlan: 0,
				renewList:[],
				checkend:0,
				appUrl:"",
				itemId:""
			};
		},
		onLoad(option) {
			this.detailsList = uni.getStorageSync("detailsList");
			console.log(this.detailsList.item);
			this.tid = option.tid?option.tid:this.detailsList.item.tid
			this.itemId = this.detailsList.item.id
			this.appUrl = getApp().globalData.urlZf
			this.ctId = option.ctId;
			this.isDomain = option.isDomain
			if (option.ctId != undefined) {
				console.log("系列下单");
				this.verifyID = this.ctId;
			} else if(option.isDomain==1){
				console.log("域名下单");
				this.verifyID = this.detailsList.item.name
			} else if(option.isDomain==2){
				this.getRenewList()
			}else {
				this.verifyID = this.tid;
			}
			// this.getUser();
		},
		onShow() {
			
		},
		methods: {
			config() {
				if(this.isDomain == 1){
					this.registerDomain()
				}else if(this.isDomain==2){
						this.createdomainRenew()
				}else{
					this.submitOrder();
				}
			},
			async submitOrder() {
				this.isSubmit = true;
				let res = await this.$api.java_create_item({
					itemId:this.itemId,
					tid:this.tid,
					paymentScene: 1,
					price: this.detailsList.item.price,
					isSeize:this.detailsList.isSeize
				});
				if (res.status.code == 0) {
					uni.showToast({
						title: "下单成功~",
						icon: "none",
						duration: 3000,
					});
					this.isSubmit = false;
					// #ifdef APP
					let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
					console.log(url)
					this.$Router.push({
						name:"webView",
						params:{
							url,
						}
					})
					// #endif
					// #ifdef H5
					let { origin } = window.location
					window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}&isSeize=${this.detailsList.isSeize}`
					// #endif
				
				} else if (res.status.code == 502) {
					this.isSubmit = false;
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000,
					});
				} else if (res.status.code == 504) {
					this.isSubmit = false;
					this.isWarning = true;
					this.isWarningText = res.msg;
				} else if (res.status.code == 510) {
					this.isSubmit = false;
					this.isRegistration = true;
				} else if (res.status.code == 511) {
					this.isSubmit = false;
					this.isPayError = true;
				} else {
					this.isSubmit = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
					// this.$refs.captcha.refresh()
				}
			},
			nav_order() {
				this.isPayError = false;
				this.$Router.push({
					name: "order",
				});
			},
			nav_details() {
				let _this = this;
				this.isAnquan = true;
				setTimeout(function() {
					_this.$Router.push({
						name: "mallDetails",
						params: {
							tid: _this.tid,
						},
					});
				}, 2200);
			},
			nav_realName(){
				this.$Router.push({
					name:"realName"
				})
			},
			async registerDomain() {
				this.isSubmit = true;
				let res = await this.$api.java_createDomain({
					paymentScene: 1,
					price: this.detailsList.item.price,
					title:this.detailsList.item.name,
				});
				if (res.status.code == 0) {
					this.isSubmit = false;
					if(res.result.orderNo){
						// #ifdef APP
						let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
						console.log(url)
						this.$Router.push({
							name:"webView",
							params:{
								url,
							}
						})
						// #endif
						// #ifdef H5
						let { origin } = window.location
						window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
						// #endif
					}
				} else if (res.status.code == 502) {
					this.isSubmit = false;
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000,
					});
				} else if (res.status.code == 504) {
					this.isSubmit = false;
					this.isWarning = true;
					this.isWarningText = res.msg;
				} else if (res.status.code == 510) {
					this.isSubmit = false;
					this.isRegistration = true;
					this.RegistrationText = res.msg;
				} else if (res.status.code == 511) {
					this.isSubmit = false;
					this.isPayError = true;
				} else {
					this.isSubmit = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
			
					// this.$refs.captcha.refresh()
				}
			},
			async getRenewList() {
				let res = await this.$api.java_domainRenewList({});
				if (res.status.code == 0) {
				console.log(res)
				this.renewList=res.result.list
				this.detailsList.item.name=res.result.list[0].title
				this.detailsList.item.price=res.result.list[0].price
				this.detailsList.item.renewTime=res.result.list[0].renewTime
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//确认续费
			async createdomainRenew() {
				let res = await this.$api.java_createdomainRenew({
					paymentScene:1,
					renewTime:this.detailsList.item.renewTime,
					tid:this.detailsList.item.tid,
					price:this.detailsList.item.price
				});
				if (res.status.code == 0) {
					if(res.result.orderNo){
						// #ifdef APP
						let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
						console.log(url)
						this.$Router.push({
							name:"webView",
							params:{
								url,
							}
						})
						// #endif
						// #ifdef H5
						let { origin } = window.location
						window.location.href = `${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
						// #endif
					}
				} else if (res.status.code == 502) {
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000,
					});
				} else if (res.status.code == 504) {
					this.isSubmit = false;
					this.isWarning = true;
					this.isWarningText = res.msg;
				} else if (res.status.code == 510) {
					this.isSubmit = false;
					this.isRegistration = true;
					this.RegistrationText = res.msg;
				} else if (res.status.code == 511) {
					this.isSubmit = false;
					this.isPayError = true;
				} else {
					this.isSubmit = false;
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 3000,
					});
							
					// this.$refs.captcha.refresh()
				}
			},
			checkRenew(item,index){
				if(this.checkend==index){
					this.checkend=-1
					this.detailsList.item.name=this.renewList[0].title
					this.detailsList.item.price=this.renewList[0].price
					this.detailsList.item.renewTime=this.renewList[0].renewTime
				}else{
					this.checkend=index
					this.detailsList.item.name=item.title
					this.detailsList.item.price=item.price
					this.detailsList.item.renewTime=item.renewTime
				}
			}
		},
	}; 
</script>

<style lang="scss">
	.border_view{
		background-color:#46454F;
		height:12rpx;
		width:100%;
	}
	.modal-btn {
		padding: 10rpx 70rpx 50rpx 70rpx;
 
		.mb-confirm {
			height: 64rpx;
			line-height: 64rpx;
			text-align: center;
			border-radius: 37rpx;
			font-size: 30rpx;
			padding: 0rpx 20rpx;
		}

		.mb-confirm {
			color: #eee;
			background-color: #BB3835;

			&.black {
				background-color: #333333;
			}
		}
	}

	.ver-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40rpx;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
	}

	.address {
		margin-top: 30rpx;
		padding: 20rpx 42rpx;
		overflow: hidden;
		color: var(--secondary-front-color);
		background-color: #1E1E1E;

		.icon-map {
			float: left;
			margin-top: -7rpx;
		}

		.add-info {
			height: 120rpx;
			width: 550rpx;
			padding-left: 15rpx;
			display: inline-flex;
			flex-direction: column;
			justify-content: space-between;

			.ai-top {
				span:nth-child(2) {
					margin-left: 20rpx;
					font-size: 28rpx;
				}
			}

			.ai-bottom {
				line-height: 35rpx;
				font-size: 28rpx;
			}
		}

	}

	.address-popup {
		.ap-title {
			height: 120rpx;
			line-height: 120rpx;
			text-align: center;
			font-weight: 700;
			color: #F9F9F9;
			background-color: var(--dialog-bg-color);
			border-bottom: 1rpx solid #666666;
			position: relative;

			>text {
				position: absolute;
				top: 0;
				right: 42rpx;
				color: #888;
				font-size: 28rpx;
				font-weight: 400;
			}
		}

		// .ap-list {
		// 	.ap-item {
		// 		height: 160rpx;
		// 		padding: 0 42rpx;
		// 		align-items: center;

		// 		.api-left {

		// 		}
		// 	}
		// }
	}

	.content {
		height: 800rpx;
		background-color: var(--dialog-bg-color);

		.add-box {
			height: 160rpx;
			padding: 20rpx 42rpx;
			border-bottom: 2rpx solid #282828;
			color: #F9F9F9;

			.add-info {
				width: 550rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.ai-top {
					font-weight: 700;

					span:nth-child(2) {
						margin-left: 20rpx;
						font-size: 28rpx;
					}
				}

				.ai-bottom {
					line-height: 35rpx;
					font-size: 28rpx;
					color: var(--secondary-front-color);
				}
			}

			.icon-arrow {
				color: #999;
			}
		}
	}

	.address_footer {
		background-color: var(--dialog-bg-color);
		padding: 32rpx 40rpx 90rpx;

		.add-btn {
			height: 90rpx;
			line-height: 90rpx;
			font-size: 32rpx;
			color: #000;
			background: var(--primary-button-color);
			text-align: center;
		}
	}

	.info {
		padding:40rpx 40rpx;
		// background-color: #1E1E1E;
		.i-top {
			.uni-image {
				width: 200rpx;
				height: 200rpx;
				border-radius:30rpx;
			}
			.it-center {
				flex: 1;
				max-width: 420rpx;
				font-weight: 400;
				font-size: 24rpx;
				line-height: 24rpx;
				margin-left: 24rpx;

				.it_name {
					font-size: 28rpx;
					line-height: 28rpx;
					font-weight: 400;
					margin-bottom: 24rpx;
					width:100%;
					color: #fff;
				}
				.it_version{
					text-align: right;
					color:#A6A6A6;
				}
				.it_msg {
					margin-top: 60rpx;
				}
				.it_price{
					margin-top:90rpx;
					font-size:34rpx;
					color:#63EAEE;
					font-weight:600;
				}
			}

		}

		.i-center {
			height: 50rpx;
			line-height: 70rpx;
			margin: 10rpx 0 20rpx;
			font-size: 24rpx;
			font-weight: 700;

			// /deep/ .u-numberbox {
			// 	.u-number-input {
			// 		background: var(--message-box-point-color)!important;
			// 	}

			// 	.u-icon-minus,
			// 	.u-icon-plus {
			// 		border-radius: 5rpx;
			// 	}
			// }
		}

		.i-bottom {
			font-size: 24rpx;
			font-weight: 700;

			.way_content {
				margin-top: 30rpx;
				color: #777777;
			}
		}
	}

	.pay {
		padding: 32rpx 40rpx 90rpx;
		position: fixed;
		bottom: 0;
		left: 0; 
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.pay-btn {
			width: 240rpx;
			height: 100rpx;
			line-height: 100rpx;
			float: right;
			font-size: 28rpx;
			font-weight: 600;
			color: #121212;
			background: #63EAEE;
			margin-left: 30rpx;
			text-align: center;
			border-radius:24rpx;
			&[disabled],
			&:active {
				background-color: #333333;
			}
		}

		.actual-pay {
			display: flex;
			align-items: flex-end;
			float: right;
			font-size: 24rpx;
			color: #fff;

			.strong {
				color: #63EAEE;
				font-size: 44rpx;
				font-weight: 600;
			}
			span {
				margin-left: 10rpx;
				color: #BB3835;
			}
		}
	}

	.top_msg {
		background-color: #132727;
		color: #1FEDF0;
		font-size: 24rpx;
		width: 100%;
		padding: 24rpx 40rpx;
		line-height: 36rpx;
	}
	.isBatchBuy{
		padding:30rpx;
		.buy_view{
			background-color:#464646 ;
			border-radius:30rpx;
			padding:40rpx 30rpx 30rpx 30rpx;
			.img{
				display: flex;
				justify-content: center;
				margin-bottom:40rpx;
				image{
					width:180rpx;
				}
			}
			.font{
				font-size:28rpx;
				line-height:42rpx;
				color:#D1D9D9;
				letter-spacing:2rpx;
				text{
					color:#0CFFF1;
				}
			}
		}
	}
	.isRenew{
		padding:30rpx;
		.title{
			font-size:34rpx;
			color:#F8F8F8;
		}
		.cart_view{
			display: flex;
			justify-content: space-between;
			margin-top:40rpx;
			align-items: center;
			.cart{
				background-color:#46454F;
				border-radius:16rpx;
				width:328rpx;
				padding:30rpx;
				.name{
					color:#F8F8F8;
					font-size:26rpx;
				}
				.domain{
					color:#BBBABA;
					font-size:24rpx;
					margin-top:20rpx;
				}
				.bottom{
					margin-top:36rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					.price{
						font-size:32rpx;
						font-weight:600; 
						color:#F8F8F8;
					}
					.icon{
						image{
							width:30rpx;
							height:30rpx;
						}
					}
				}
			}
		}
	} 
	.bg_border{
		background-color:#1E1E1E;
		height:10rpx;
		width:100%;
	}
	.new-modal-content {
		padding:35rpx 40rpx;
		.success_img{
			display: flex;
			justify-content: center;
			align-items: center;
			image{
				width:160rpx;
				height:160rpx;
			}
		}
		.modal-content{
			padding:35rpx 0rpx;
			border-bottom:1rpx solid #EDEDED;
			font-size:28rpx;
			color:#141414;
			text-align: center;
			p{
				margin-bottom:20rpx;
			}
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color:#141414;
			}
	
			.img_cancel {
				border: 1px solid #141414;
			}
			.img_reasale {
				background-color: #63EAEE;
				color:#141414;
			}
		}
	}
</style>
