<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('Swap.title')">
            <view slot="right" class="search-box" @click="nav_to('Record', 'swap')">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>



        <!-- <view class="time-info">
            <span>按当前市场价进行兑换：</span>
            <span>UTC {{ currentTime }}</span>
        </view> -->

        <!-- 兑换模块 -->
        <view class="form-wrapper">
            <!-- :style="{ backgroundColor: isSwapped ? '#e6f0ff' : '' }" -->
            <view class="form-row from">
                <view class="float_box">
                    <view class="from-currency">{{ $t("Swap.From") }}</view>
                    <view class="flex_divide padding">
                        <u-input v-model="fromAmount" class="from-input" @input="onfromAmountInputChange"
                            :placeholder="nowPair.minQuantityPerTrade && nowPair.baseCoin == fromCurrency ? nowPair.minQuantityPerTrade + '~' + nowPair.maxQuantityPerTrade : '0'"></u-input>
                        <view class="frominfo" @click="Choose()">
                            <transition name="expand-slide">
                                <view class="helpoption" v-show="fromShow">
                                    <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                        @click="SetCoin(item)">
                                        <text>{{ item.name }}</text>
                                    </view>
                                </view>
                            </transition>

                            <!-- <image :src="fromCurrency.icon" /> -->
                            <text>{{ fromCurrency || '-' }}</text>
                            <image :class="{ rotated: isChoose }"
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                        </view>
                    </view>
                </view>


                <view class="available">{{ $t("Swap.Available") }} : {{ balanceFrom || 0.00 }}
                    <!-- <text v-if="fromAmount" style="margin-left: 14rpx;">最大</text> -->
                </view>
            </view>

            <!-- Swap Button -->
            <view class="swap_btn" :class="{ rotated: isSwapped }" @click="swap()">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/4d06d309ed8535459783ea435e356d7e_61x69.png" />
            </view>

            <!-- :style="{ backgroundColor: isSwapped ? '' : '#e6f0ff' }" -->
            <!-- <view class="form-row to"> -->

            <view class="float_box">
                <view class="to-currency">{{ $t("Swap.To") }}</view>
                <view class="flex_divide padding">
                    <u-loading :show="showToAmount" style="margin: 20rpx 20rpx 0 0;"></u-loading>
                    <u-input :disabled="true" v-model="toAmount" class="to-input" placeholder="0"></u-input>
                    <view class="frominfo" @click="Choosed()">
                        <transition name="expand-slide">
                            <view class="helpoption" v-show="toShow">
                                <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                    @click="SetCointo(item)">
                                    <text>{{ item.name }}</text>
                                </view>
                            </view>
                        </transition>
                        <!-- <image :src="toCurrency.icon" /> -->
                        <text>{{ toCurrency || '-' }}</text>
                        <image :class="{ rotated: isChoosed }"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                    </view>
                </view>
            </view>
            <!-- <view class="available">Available : {{ balanceTo || 0.00 }}</view> -->
            <!-- </view> -->
            <!-- <view class="bom"> -->
            <text class="bom" v-if="nowPair.handFeeRate && fromCurrency && toCurrency && fromAmount && toAmount">{{
                $t("Swap.Fee") }}: {{
                    Number(nowPair.handFeeRate *
                        toAmount).toFixed(nowPair.priceSignificantDigits + 2) ||
                    0.00 }}{{ feeSymbol }}</text>
            <!-- <text>预计到账时间：数秒内</text> -->
            <!-- <text> </text> -->
            <!-- </view> -->
            <!-- <view class="tips">请在9999秒有效期内完成兑换下单。</view> -->



        </view>

        <view class="swap-price flex_divide" v-if="fromCurrency && toCurrency">
            <view class="flex-column">
                <text class="market-price">{{ $t("Swap.ExchangeRate") }}:</text>
                <view class="price">1 {{ fromCurrency }} = {{ nowPair.baseCoin == fromCurrency ? hui.p : hui.op }}
                    {{ toCurrency }}
                    <view class="load">
                        <u-loading :show="true"></u-loading>
                    </view>
                </view>
            </view>


            <view class="Compared">{{ $t("Swap.Compared") }}<text :style="{
                color: (nowPair.baseCoin == fromCurrency ? rateNow.p : rateNow.op) >= 0 ? '#008E28' : '#FF82A3'
            }" v-if="rateNow.p && fromCurrency && toCurrency">
                    {{
                        nowPair.baseCoin == fromCurrency
                            ? (Number(rateNow.p) * 100).toFixed(2) || '--'
                            : (Number(rateNow.op) * 100).toFixed(2) || '--'
                    }} %
                </text>
                <text v-else>--</text>
                <image v-if="(nowPair.baseCoin == fromCurrency ? rateNow.p : rateNow.op) >= 0"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250319/cc8b0548d4d9a8a72a3b463467dca8fc_64x65.png" />
                <image v-else
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250411/ead1567073dc90460a99a2347cb66657_200x200.png" />

            </view>
        </view>

        <!-- 汇率趋势图 -->
        <!-- <div class="chart-wrapper" ref="chart"></div> -->
        <view class="chart-wrapper" v-if="fromCurrency && toCurrency">
            <l-echart ref="chartRef"></l-echart>
        </view>


        <view class="time" v-if="fromCurrency && toCurrency">
            <text v-for="(item, index) in times" :key="index" @click="checks(item, index)"
                :class="{ timeActive: nowTime == index }">{{ item.name }}
            </text>
        </view>

        <view class="btn flex-column-all" v-if="fromCurrency && toCurrency">
            <text v-if="timeDiff">{{ $t("Swap.tips") }}{{ timeDiff }}{{ $t("Swap.secong") }}</text>
            <view class="exchange-btn flex_all" @click="convert">{{ $t("Types.Swap") }}</view>
        </view>

        <u-popup v-model="ConfirmSwapPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="content">
                <image class="close" @click="ConfirmSwapPopup = false"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250320/091a4c4c3cf1693a900753a9a4c37563_104x104.png" />
                <!-- confirm -->
                <view class="type flex-column-all" v-if="swapType == 'confirm'">
                    <image
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250320/598426eabe7d829403c540d78ebb3e94_448x448.png" />
                    <text class="title">{{ $t("Swap.PopupSwapTitle") }}</text>
                    <text class="contents">{{ $t("Swap.PopupSwapContent") }} {{ fromAmount }} {{ fromCurrency }} {{
                        $t("Swap.for") }} {{
                            toAmount }}{{ toCurrency }}</text>
                </view>
                <!-- confirmSuccess -->
                <view class="type flex-column-all" v-if="swapType == 'confirmSuccess'">

                    <!-- <view class="video-wrapper">
                        <video class="success-video" :src="webmSrc" autoplay muted playsinline object-fit="contain"
                            @ended="onVideoEnd" />
                    </view> -->

                    <!-- <video :src="webmSrc" class="webm-icon" autoplay muted playsinline object-fit="contain"
                        @ended="onVideoEnd" /> -->

                    <!-- <video :src="webmSrc" class="webm-icon" autoplay muted playsinline object-fit="contain"
                        show-center-play-btn="false" show-mute-btn="false" show-play-btn="false" v-if="canPlayVideo" /> -->
                    <image
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250320/7e44d1f91c1b2a9e1ea71d864bee4cd9_448x448.png" />
                    <text class="title">{{ $t("Swap.Successfully") }}</text>
                    <text class="contentsSuccess">{{ $t("Swap.completed") }}</text>
                </view>

                <!-- Timeout -->
                <view class="type flex-column-all" v-if="swapType == 'timeout'">
                    <image
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250320/a1ef7235a3200134bda3a38148a2357d_448x448.png" />
                    <text class="title">{{ $t("Swap.PopupQuoteExpiredTitle") }}</text>
                    <text class="contents">{{ $t("Swap.PopupQuoteExpiredContent") }}</text>
                </view>

                <view class="btns" v-if="swapType == 'confirm'">
                    <view class="confirm flex_all" @click="StartSwap">{{ $t("title.confirm") }}</view>
                    <view class="cancel flex_all" @click="ConfirmSwapPopup = false">{{ $t("title.cancel") }}</view>
                </view>

                <view class="btns" v-if="swapType == 'confirmSuccess'">
                    <view class="confirm flex_all" @click="nav_to_tab('index')">{{ $t("Transaction.Button.GoHome") }}
                    </view>
                </view>


                <view class="btns" v-if="swapType == 'timeout'">
                    <view class="confirm flex_all" @click="RegetRate">{{ $t("Swap.GetNewRate") }}</view>
                    <view class="cancel flex_all" @click="ConfirmSwapPopup = false">{{ $t("title.cancel") }}</view>
                </view>
            </view>
        </u-popup>

        <zero-loading type="sword" v-if="loading"></zero-loading>
    </view>
</template>

<script>
// import * as echarts from "echarts";
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min';
import store from '@/store/index.js'
export default {
    data() {
        return {
            videoEnded: false,
            webmSrc: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/Timeline%203.webm',
            canPlayVideo: true,
            loading: false,
            option: {
                legend: {
                    show: false
                },
                title: {
                    show: false,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    confine: true
                },
                // legend: {
                //     data: ['热度', '正面', '负面']
                // },
                grid: {
                    left: 20,
                    right: 20,
                    bottom: 15,
                    top: 40,
                    containLabel: true
                },
                yAxis: [
                    {
                        show: false,
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: '#999999'
                            }
                        },
                        axisLabel: {
                            color: '#666666'
                        }
                    }
                ],
                xAxis: [
                    {
                        type: 'category',
                        axisTick: { show: false },
                        data: ['汽车之家', '今日头条', '百度贴吧', '一点资讯', '微信', '微博', '知乎'],
                        axisLine: {
                            lineStyle: {
                                color: '#999999'
                            }
                        },
                        axisLabel: {
                            color: '#666666'
                        }
                    }
                ],
                series: [
                    {
                        name: '热度',
                        type: 'line',
                        label: {
                            normal: {
                                show: true,
                                position: 'inside'
                            }
                        },
                        data: [300, 270, 340, 344, 300, 320, 310],
                    }
                ]
            },
            rateNow: {},
            timerHui: null,
            hui: {
                p: ''
            },
            showToAmount: false,
            timeArray: [],
            priceArray: [],
            timer: null,
            timeDiff: 0, // 记录秒差
            swapType: "", // confirmSuccess
            ConfirmSwapPopup: false, // 确认swap
            RFQObj: {},
            rfqId: "",
            inputTimer: null, // 用于存储定时器 ID
            nowPair: {},
            balanceTo: 0,
            balanceFrom: 0,
            CoinList: [],
            toShow: false,
            fromShow: false,
            isChoose: false,
            isChoosed: false,

            nowTime: 0,
            timeNode: "minute_div5_part",
            times: [
                {
                    name: '5min',
                    value: 'minute_div5_part'
                },
                {
                    name: "10min",
                    value: 'minute_div10_part',
                },
                {
                    name: "30min",
                    value: 'minute_div30_part',
                },
                {
                    name: "1H",
                    value: 'hour',
                },
                {
                    name: "1D",
                    value: 'day'
                },
                {
                    name: "1M",
                    value: 'month',
                },
                {
                    name: "1Y",
                    value: 'year',
                },
            ],
            isSwapped: false,
            fromAmount: '',
            toAmount: '',
            // 货币数据
            // fromCurrency: { symbol: 'ETH', name: 'Ethereum', icon: '../../../static/imgs/public/ETH.png' },
            // toCurrency: { symbol: 'BTC', name: 'Bitcoin', icon: '../../../static/imgs/public/BTC.png' },
            coins: [
                { name: 'Bitcoin', symbol: 'BTC', price: '$42,000', approx: '≈ ¥300,000', icon: 'path/to/bitcoin-icon.png' },
                { name: 'Ethereum', symbol: 'ETH', price: '$3,000', approx: '≈ ¥21,000', icon: 'path/to/ethereum-icon.png' },
                // 添加其他币种数据
            ],
            fromCurrency: '', // 默认选择的币种
            toCurrency: '',   // 默认选择的币种
            amount: 1000,        // 默认金额
            currentTime: new Date().toISOString().slice(0, 19).replace('T', ' '), // 获取当前UTC时间
            page: {
                pageNum: 1,
                pageSize: 20
            },
            hasInitCalled: false // ✅ 标记是否已初始请求过
        };
    },
    async onLoad(option) {
        uni.setNavigationBarTitle({
            title: this.$t("page.swap") // 切换语言后重新设置标题
        })
        await this.getAvailableBalance()
        if (option.fromCurrency) {
            this.fromCurrency = (option.fromCurrency)
            for (let i = 0; i < this.CoinList.length; i++) {
                const e = this.CoinList[i];
                if (e.symbol == this.fromCurrency) {
                    console.log(e);
                    this.balanceFrom = e.balance
                }
            }
        }
        // setTimeout(() => {
        //     this.init()
        // }, 100);
        this.initTimer();
    },
    onUnload() {
        this.clearTimer();

        clearInterval(this.timer)
        this.timer = null
        console.log('onUnload');

    },
    onHide() {
        console.log('onHide');

        clearInterval(this.timerHui);
        this.timerHui = null;
    },
    watch: {
        fromCurrency: {
            handler(newVal) {
                if (!newVal || !this.toCurrency) return
                // fromCurrency 后续变动才进这里
                if (newVal && this.toCurrency) {
                    this.debounceQuery()
                }
            }
        },
        toCurrency: {
            handler(newVal) {
                if (!newVal || !this.fromCurrency) return
                if (newVal && this.fromCurrency) {
                    this.debounceQuery()
                    setTimeout(() => {
                        this.init()
                    }, 100);
                    if (this.fromCurrency && this.toCurrency) {
                        // 先清理旧的
                        this.clearTimer();
                        this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency);
                    }
                }
            },
            immediate: true
        },
        fromAmount() {
            console.log(555);
            this.debounceQuery();
        }
    },
    computed: {
        feeSymbol() {
            if (this.fromCurrency && this.nowPair.baseCoin && this.nowPair.targetCoin) {
                if (this.Check() == 'buy') {
                    return this.nowPair.baseCoin
                } else if (this.Check() == 'sell') {
                    return this.nowPair.targetCoin
                }
            }
            // Check
        }
    },
    onHide() {
        this.clearTimer();
        if (this.timer) clearInterval(this.timer); // 组件销毁时清除定时器，避免内存泄漏
    },
    methods: {
        onVideoEnd() {
            this.videoEnded = true;
        },
        initTimer() {
            if (this.fromCurrency && this.toCurrency) {
                // 先清理旧的
                this.clearTimer();
                // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency);
                this.timerHui = setInterval(() => {
                    this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency);
                }, 10000);
            }
        },

        clearTimer() {
            if (this.timerHui) {
                clearInterval(this.timerHui);
                this.timerHui = null;
            }
        },
        async init() {
            await this.getLine()
            const chart = await this.$refs.chartRef.init(echarts);
            const option = {
                dataZoom: [{
                    type: 'inside', //1平移 缩放
                    throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
                    minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
                    start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
                    end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
                    zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
                }],
                title: {
                    show: false,
                },
                tooltip: {
                    trigger: 'axis',
                    // renderMode: 'html', // 强制使用 HTML 渲染
                    backgroundColor: "#F4F4F4",
                    borderColor: "#008E28",
                    borderWidth: 1,
                    textStyle: {
                        color: "#000000",
                        fontStyle: "normal",
                        fontWeight: "400",
                        fontFamily: "Gilroy-SemiBold"
                    },
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: (params) => {
                        const date = params[0].name;
                        const value = (params[0].value).toFixed(2);
                        return `${value} ${date}`;
                    },
                    extraCssText: `
                        border-radius: 10px !important;
                        padding: 10px 12px;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
                        color: "#000000",
                        fontStyle: "normal",
                        fontWeight: "400",
                        fontFamily: "Gilroy-SemiBold !important"
                    `
                },
                grid: {
                    left: '4%', // 距离左边的距离
                    right: '0%', // 距离右边的距离
                    top: '10%', // 距离顶部的距离
                    bottom: '22%' // 距离底部的距离
                },
                xAxis: {
                    type: 'category', // x轴类型为类目轴
                    boundaryGap: true, // 取消x轴两端空白
                    interval: 0,
                    // data: ['3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', "11:00", "12:00", "13:00", '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'], // x轴类目数据
                    data: this.timeArray,
                    axisTick: {
                        show: false, // 不显示刻度
                    },
                    axisLabel: { //x轴文字的配置
                        color: "#666666", //Y轴内容文字颜色
                        interval: 'auto', // 可以设置为具体的数字，如 5，表示显示每隔 5 个标签
                        fontSize: 12,//调整坐标轴字体大小
                        fontFamily: "Gilroy-SemiBold",
                        // margin: 
                    },
                    axisLine: {
                        lineStyle: {
                            // color: '#E0E7FF' // x轴线的颜色
                            color: 'transparent'
                        }
                    },
                    splitLine: {
                        // 纵向分割线
                        show: false,
                        lineStyle: {
                            // color: '#D2DAE3'
                        }
                    }
                },
                yAxis: {
                    show: false,
                    type: 'value', // y轴类型为数值轴
                    // name: '单位：斤/元', //单位
                    nameLocation: 'end', // (单位个也就是在在Y轴的最顶部)
                    //单位的样式设置
                    nameTextStyle: {
                        color: "#999", //颜色
                        padding: [0, 20, 0, 40], //间距分别是 上 右 下 左
                        fontSize: 14,
                    },
                    axisLabel: { //y轴文字的配置
                        color: "#777", //Y轴内容文字颜色
                    },
                    axisLine: { //y轴线的配置
                        show: true, //是否展示
                        lineStyle: {
                            color: "#E0E7FF", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
                            width: 1, //y轴线的宽度
                            //type: "solid" //y轴线为实线
                        },
                    },
                    axisTick: {
                        show: false // y轴上的小横线
                    },
                    // 横向分割线
                    splitLine: {
                        show: true, // 显示分割线。
                        lineStyle: {
                            // 分割线样式。
                            color: '#D2DAE3', // 分割线颜色。
                            type: 'dotted' // 分割线类型。 solid实线  dotted点型虚线  dashed线性虚线
                        }
                    },
                    splitNumber: 4, // 指定横向分割线的数量
                },
                //  图例
                // legend: {
                //     data: ['近7天价格变化'],
                //     left: 'center',
                //     top: 'bottom'
                // },
                series: [{
                    /* 
                    // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，
                    // 相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，
                    // 则该四个值是绝对的像素位置
                    */
                    type: 'line', // 图表类型为折线图
                    // datasetId: 'dataset_since_1950_of_germany',
                    showSymbol: false,
                    // data: [120, 180, 150, 80, 70, 110, 130, 70, 110, 130, 70, 110, 130, 2, 20, 50, 145, 200, 5], // 折线图数据
                    data: this.priceArray,
                    // smooth: true, // 平滑曲线
                    // 区域颜色渐变
                    // areaStyle: {
                    //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                    //         [{
                    //             offset: 0,
                    //             color: "rgba(254, 235, 215, 1)",
                    //         },
                    //         {
                    //             offset: 1,
                    //             color: "rgba(254, 235, 215, 0)",
                    //         },
                    //         ], false
                    //     ),
                    // },

                    // 折线颜色
                    lineStyle: {
                        // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                        // color: {
                        //     type: 'linear',
                        //     x: 0,
                        //     y: 0,
                        //     x2: 0,
                        //     y2: 1,
                        //     colorStops: [{
                        //         offset: 0, color: 'red' // 0% 处的颜色
                        //     }, {
                        //         offset: 1, color: 'blue' // 100% 处的颜色
                        //     }],
                        //     global: false // 缺省为 false
                        // }
                        color: "#FF95B1",
                        width: '2',
                    },
                    color: "#008E28", //拐点颜色
                    // symbol: 'circle', // 设置拐点形状、
                    symbolSize: 10, // 设置拐点大小
                    // 拐点处显示值
                    itemStyle: {
                        symbol: 'none', // 隐藏所有节点
                        normal: {
                            node: { show: false },
                            label: { show: false }
                        }
                    },
                }],
            }
            chart.setOption(option)
        },
        onfromAmountInputChange(event) {
            console.log(event);
            let decimalDigits = this.nowPair.priceSignificantDigits - 1 || 2;
            let pattern = new RegExp(`^\\d*(\\.\\d{0,${decimalDigits}})?`);
            event = (event.match(pattern)?.[0]) || "";
            this.$nextTick(() => {
                this.fromAmount = event
            })
        },
        async getLine() {
            this.loading = true
            let res = await this.$api.priceLine({
                pageNum: 1,
                // instrument: this.fromCurrency ? this.fromCurrency + this.toCurrency + '.SPOT' : "",
                fromCoin: this.fromCurrency,
                toCoin: this.toCurrency,
                priceLineType: this.timeNode
            })
            if (res.code == 200) {
                this.timeArray = []
                this.priceArray = []
                // this.timeArray = res.result.map(item => {
                //     const date = new Date(item.crateAt * 1000);
                //     return `${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
                // }).reverse();
                this.loading = false
                this.timeArray = res.result.map(item => {
                    return this.formatTimeByNode(item.crateAt, this.timeNode);
                }).reverse();
                this.priceArray = res.result.map(item => item.price).reverse();;

            } else {
                this.loading = false
            }
        },
        formatTimeByNode(timestamp, type) {
            const date = new Date(timestamp * 1000);

            const pad = (n) => String(n).padStart(2, '0');

            const floorTo = (value, step) => Math.floor(value / step) * step;

            switch (type) {
                case 'minute_div5_part':
                    date.setMinutes(floorTo(date.getMinutes(), 5));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'minute_div10_part':
                    date.setMinutes(floorTo(date.getMinutes(), 10));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'minute_div30_part':
                    date.setMinutes(floorTo(date.getMinutes(), 30));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'hour':
                    return `${pad(date.getHours())}:00`;
                case 'day':
                    return `${pad(date.getMonth() + 1)}月${pad(date.getDate())}日`;
                case 'month':
                    return `${date.getFullYear()}/${pad(date.getMonth() + 1)}`;
                case 'year':
                    return `${date.getFullYear()}`;
                default:
                    return '--';
            }
        },
        startCountdown() {
            if (this.timer) clearInterval(this.timer); // 避免重复启动
            if (this.timeDiff > 0) {
                this.timer = setInterval(() => {
                    this.timeDiff--;
                    if (this.timeDiff <= 0) {
                        // this.swapType = "timeout";
                        clearInterval(this.timer); // 倒计时结束，清除定时器
                    }
                }, 1000);
            }
        },
        RegetRate() {
            this.ConfirmSwapPopup = false
            this.getCoinPair()
        },
        debounceQuery() {
            console.log(1);
            clearTimeout(this.inputTimer); // 清除上一次的定时器
            this.inputTimer = setTimeout(() => {
                // if (this.fromAmount) {
                this.checkAndFetchPair();
                // }
            }, 300); // 300ms 之内有新的输入，就不会请求
        },
        SetCointo(e) {
            this.balanceTo = e.balance
            this.toCurrency = e.symbol
            this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)
            if (this.fromCurrency && this.toCurrency) {
                setTimeout(() => {
                    this.init()
                }, 100);
            }

        },
        SetCoin(e) {
            this.balanceFrom = e.balance
            this.fromCurrency = e.symbol
            this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)
            if (this.fromCurrency && this.toCurrency) {
                setTimeout(() => {
                    this.init()
                }, 100);
            }

        },
        // app-查询自己币对和可用余额信息
        async getAvailableBalance() {
            let res = await this.$api.userAvailableCoinList({
                // pageNum: this.page.pageNum,
                // pageSize: this.page.pageSize
                pageSize: 100
            })
            if (res.code == 200) {
                if (this.page.pageNum == 1) {
                    this.CoinList = res.result.data
                } else {
                    this.CoinList = this.CoinList.concat(res.result.data)
                }
                // this.fromCurrency = this.CoinList[0].name
                // this.toCurrency = this.CoinList[1].name


                // this.balanceFrom = this.CoinList[0].balance
                // this.balanceTo = this.CoinList[1].balance

            }
        },
        // 获取10s汇率
        async getfineGrainedCoinPrice(a, b) {
            if (a && b) {
                let res = await this.$api.fineGrainedCoinPrice({
                    // coinPair: a + b + '.SPOT'
                    fromCoin: a,
                    toCoin: b
                })
                if (res.code == 200 && res.result) {
                    this.hui = res.result
                } else if (res.code != 200) {
                    uni.showToast({
                        icon: 'none',
                        title: res.msg,
                        duration: 2000
                    });
                }
                this.getpriceGn(this.fromCurrency, this.toCurrency)
            } else {
                // uni.showToast({
                //     icon: 'none',
                //     title: this.$t('Swap.selectpair'),
                //     duration: 2000
                // });
            }


        },
        // 获取相比昨日涨跌幅
        async getpriceGn(a, b) {
            let res = await this.$api.priceGn({
                // coinPair: a + b + '.SPOT'
                fromCoin: a,
                toCoin: b
            })
            if (res.code == 200 && res.result) {
                this.rateNow = res.result
            } else if (res.code != 200) {
                uni.showToast({
                    icon: 'none',
                    title: res.msg,
                    duration: 2000
                });
            }
        },
        checkAndFetchPair() {
            if (this.fromCurrency && this.toCurrency) {
                this.getCoinPair();
            }
        },
        // app-查询币对的组合
        async getCoinPair() {
            let res = await this.$api.coinPair({
                coin0: this.fromCurrency,
                coin1: this.toCurrency
            })
            if (res.code == 200) {
                this.nowPair = res.result
                if (res.result.isTradable != 'true') {
                    this.$u.toast(this.$t("Swap.NotTradable"))
                    return
                } else {
                    if (this.fromAmount) {
                        this.queryPrice()
                    }
                }
            } else {
                // this.$u.toast(res.msg)
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        // app-闪兑询价
        async queryPrice() {
            if (!this.balanceFrom || !this.balanceTo) {
                uni.showToast({
                    icon: 'none',
                    title: this.$t("Swap.nobalance"),
                    duration: 2000
                });
                return
            }
            if (this.fromAmount <= 0) {
                return
            }
            this.showToAmount = true
            this.loading = true
            let res = await this.$api.Askrfq({
                coinPair: this.nowPair.coinPair.split('.')[0],
                baseCoin: this.nowPair.baseCoin,
                targetCoin: this.nowPair.targetCoin,
                amount: this.fromAmount,
                side: this.Check()
            })
            if (res.code == 200) {
                this.RFQObj = res.result
                this.loading = false
                const now = Date.now();
                const validUntil = new Date(res.result.validUntil).getTime();
                this.timeDiff = Math.floor((validUntil - now) / 1000); // 计算秒差
                this.showToAmount = false // loading动画
                this.toAmount = res.result.finalAddCoinAmount
                this.rfqId = res.result.rfqId
                this.startCountdown();

            } else {
                this.loading = false
                this.toAmount = ''
                this.fromAmount = ''
                this.showToAmount = false // loading动画
                this.$u.toast(res.msg)
            }
        },
        // 获取买卖方向
        Check() {
            if (this.fromCurrency && this.nowPair.baseCoin && this.nowPair.targetCoin) {
                //    return this.fromCurrency == this.nowPair.baseCoin ? 'sell' : 'buy'
                if (this.fromCurrency == this.nowPair.baseCoin) {
                    return 'sell'
                } else {
                    return 'buy'
                }

                if (this.fromCurrency == this.nowPair.targetCoin) {
                    return 'buy'
                } else {
                    return 'sell'
                }
            }
        },
        nav_to(e, name) {
            this.$Router.push({
                name: e,
                params: {
                    type: name
                }
            })
        },
        nav_to_tab(e) {
            this.$Router.pushTab({
                name: e
            })
        },
        Choose() {
            this.fromShow = !this.fromShow
            this.isChoose = !this.isChoose;

        },
        Choosed() {
            this.toShow = !this.toShow
            this.isChoosed = !this.isChoosed;

        },
        checks(item, index) {
            if (this.nowTime == index) return
            // this.period = item.value
            this.nowTime = index;
            this.timeNode = item.value
            if (this.fromCurrency && this.toCurrency) {
                setTimeout(() => {
                    this.init()
                }, 100);
            }
        },
        // 折线图
        initChartLine() {
            const chart = echarts.init(this.$refs.chart);
            const option = {
                dataZoom: [{
                    type: 'inside', //1平移 缩放
                    throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
                    minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
                    start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
                    end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
                    zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
                }],
                title: {
                    show: false,
                },
                tooltip: {
                    formatter: (params) => {
                        const date = params[0].name;  // 获取日期
                        const value = params[0].value; // 获取汇率  
                        // 格式化日期为易读格式
                        // const formattedDate = new Date(date).toLocaleDateString();

                        // 返回自定义内容：日期和汇率
                        return `
                      <div>
                        ${value} ${date}
                      </div>
                    `;
                    },
                    formatter: (params) => { },
                    backgroundColor: "#F4F4F4",
                    borderColor: "#008E28",
                    trigger: 'axis',
                    borderRadius: "6",
                    // 可以在这里添加更多 tooltip 配置
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    textStyle: {
                        color: "#000000", // 文字的颜色
                        fontStyle: "normal", // 文字字体的风格（'normal'，无样式；'italic'，斜体；'oblique'，倾斜字体）
                        fontWeight: "400", // 文字字体的粗细（'normal'，无样式；'bold'，加粗；'bolder'，加粗的基础上再加粗；'lighter'，变细；数字定义粗细也可以，取值范围100至700）
                        fontFamily: "Gilroy-SemiBold"
                    },
                },
                grid: {
                    left: '4%', // 距离左边的距离
                    right: '0%', // 距离右边的距离
                    top: '10%', // 距离顶部的距离
                    bottom: '22%' // 距离底部的距离
                },
                xAxis: {
                    type: 'category', // x轴类型为类目轴
                    boundaryGap: true, // 取消x轴两端空白
                    interval: 0,
                    // data: ['3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', "11:00", "12:00", "13:00", '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'], // x轴类目数据
                    data: this.timeArray,
                    axisTick: {
                        show: false, // 不显示刻度
                    },
                    axisLabel: { //x轴文字的配置
                        color: "#666666", //Y轴内容文字颜色
                        interval: 'auto', // 可以设置为具体的数字，如 5，表示显示每隔 5 个标签
                        fontSize: 12,//调整坐标轴字体大小
                        fontFamily: "Gilroy-SemiBold",
                        // margin: 
                    },
                    axisLine: {
                        lineStyle: {
                            // color: '#E0E7FF' // x轴线的颜色
                            color: 'transparent'
                        }
                    },
                    splitLine: {
                        // 纵向分割线
                        show: false,
                        lineStyle: {
                            // color: '#D2DAE3'
                        }
                    }
                },
                yAxis: {
                    show: false,

                    type: 'value', // y轴类型为数值轴
                    // name: '单位：斤/元', //单位
                    nameLocation: 'end', // (单位个也就是在在Y轴的最顶部)
                    //单位的样式设置
                    nameTextStyle: {
                        color: "#999", //颜色
                        padding: [0, 20, 0, 40], //间距分别是 上 右 下 左
                        fontSize: 14,
                    },
                    axisLabel: { //y轴文字的配置
                        color: "#777", //Y轴内容文字颜色
                    },
                    axisLine: { //y轴线的配置
                        show: true, //是否展示
                        lineStyle: {
                            color: "#E0E7FF", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
                            width: 1, //y轴线的宽度
                            //type: "solid" //y轴线为实线
                        },
                    },
                    axisTick: {
                        show: false // y轴上的小横线
                    },
                    // 横向分割线
                    splitLine: {
                        show: true, // 显示分割线。
                        lineStyle: {
                            // 分割线样式。
                            color: '#D2DAE3', // 分割线颜色。
                            type: 'dotted' // 分割线类型。 solid实线  dotted点型虚线  dashed线性虚线
                        }
                    },
                    splitNumber: 4, // 指定横向分割线的数量
                },
                //  图例
                // legend: {
                //     data: ['近7天价格变化'],
                //     left: 'center',
                //     top: 'bottom'
                // },
                series: [{
                    /* 
                    // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，
                    // 相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，
                    // 则该四个值是绝对的像素位置
                    */
                    type: 'line', // 图表类型为折线图
                    // datasetId: 'dataset_since_1950_of_germany',
                    showSymbol: false,
                    name: '近7天价格变化',
                    // data: [120, 180, 150, 80, 70, 110, 130, 70, 110, 130, 70, 110, 130, 2, 20, 50, 145, 200, 5], // 折线图数据
                    data: this.priceArray,
                    // smooth: true, // 平滑曲线
                    // 区域颜色渐变
                    // areaStyle: {
                    //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                    //         [{
                    //             offset: 0,
                    //             color: "rgba(254, 235, 215, 1)",
                    //         },
                    //         {
                    //             offset: 1,
                    //             color: "rgba(254, 235, 215, 0)",
                    //         },
                    //         ], false
                    //     ),
                    // },

                    // 折线颜色
                    lineStyle: {
                        // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                        // color: {
                        //     type: 'linear',
                        //     x: 0,
                        //     y: 0,
                        //     x2: 0,
                        //     y2: 1,
                        //     colorStops: [{
                        //         offset: 0, color: 'red' // 0% 处的颜色
                        //     }, {
                        //         offset: 1, color: 'blue' // 100% 处的颜色
                        //     }],
                        //     global: false // 缺省为 false
                        // }
                        color: "#FF95B1",
                        width: '2',
                    },
                    color: "#008E28", //拐点颜色
                    // symbol: 'circle', // 设置拐点形状、
                    symbolSize: 11, // 设置拐点大小
                    // 拐点处显示值
                    itemStyle: {
                        symbol: 'none', // 隐藏所有节点
                        normal: {
                            node: { show: false },
                            label: { show: false }
                        }
                    },
                }],
            }
            chart.setOption(option)
        },
        swap() {

            this.isSwapped = !this.isSwapped;
            // 交换 from 和 to 的货币数据
            const tempCurrency = this.fromCurrency;
            this.fromCurrency = this.toCurrency;
            this.toCurrency = tempCurrency;

            const tempBalance = this.balanceFrom;
            this.balanceFrom = this.balanceTo;
            this.balanceTo = tempBalance;
            // 交换金额
            // const tempAmount = this.fromAmount;
            // this.fromAmount = this.toAmount;
            // this.toAmount = tempAmount;
            if (this.fromAmount) {
                this.checkAndFetchPair()
            }
            this.getLine()
            // this.initChartLine()
            this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)

        },
        async convert() {
            if (store.state.shouldVibrate) {
                uni.vibrateShort()
            }
            if (this.fromAmount && this.fromCurrency && this.toCurrency && this.toAmount) {
                this.ConfirmSwapPopup = true
                this.swapType = 'confirm'
            } else {
                this.$u.toast(this.$t("Swap.error"))
            }
            // console.log(`兑换 ${this.amount} ${this.fromCurrency} 到 ${this.toCurrency}`);
        },
        async StartSwap() {
            const validUntil = new Date(this.RFQObj.validUntil).getTime();
            const now = Date.now();
            if (validUntil < now) {
                this.swapType = 'timeout'
                return
            }
            // this.swapType = validUntil < now ? "timeout" : "valid";

            let res = await this.$api.addOrder({
                requestForQuoteId: this.rfqId
            })
            if (res.code == 200) {
                this.swapType = 'confirmSuccess'
                this.$u.toast(res.msg)
                this.fromShow = false
                this.toShow = false
                this.fromCurrency = ''
                this.toCurrency = ''
                this.fromAmount = ''
                this.toAmount = ''
                this.balanceFrom = ''
            } else {
                this.$u.toast(res.msg)
            }
        },
    },
};
</script>

<style scoped lang="scss">
::v-deep .u-input {
    height: 60rpx !important;
    width: 400rpx !important;
}

.padding {
    align-items: center;
    margin: 10rpx 40rpx 0 40rpx;
}

.container {
    padding: 30px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    // align-items: center;
    height: 100%;

    .content {
        position: relative;
        // height: 389*2rpx;
        border-radius: 40rpx;
        padding: 42rpx 44rpx;
        background: #FFFFFF;
        box-shadow: 14rpx 20rpx 200rpx 0 #0000001A;
        width: 90vw;

        .type {
            margin-top: 60rpx;

            .video-wrapper {
                width: 224rpx;
                height: 224rpx;
                background-color: #fff; // 白色背景 ✅
                border-radius: 999rpx;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;

                .success-video {
                    width: 100%;
                    height: 100%;
                    background-color: transparent; // 确保不被透明遮罩
                }
            }

            .webm-icon {
                width: 224rpx;
                height: 224rpx;
                background: #fff;
            }

            image {
                width: 224rpx;
                height: 224rpx;
            }

            .title {
                margin-top: 32rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 40rpx;
                line-height: 120%;
                text-align: center;
                color: #000;
            }

            .contents {
                margin-top: 22rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 160%;
                letter-spacing: 0%;
                text-align: center;
                color: #666;
            }

            .contentsSuccess {
                margin-top: 22rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 160%;
                letter-spacing: 0%;
                text-align: center;
                color: #999;
            }
        }

        .btns {
            margin-top: 32rpx;

            .confirm {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                height: 100rpx;
                letter-spacing: 0%;
                text-align: center;
                background: #FF82A3;
                border-radius: 64*2rpx;
                vertical-align: middle;
                color: #fff;
            }

            .cancel {
                margin-top: 32rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                height: 100rpx;
                color: #000;
                font-size: 32rpx;
                border-radius: 64*2rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;
                border: 2rpx solid #999999
            }

        }

        .close {
            position: absolute;
            top: 46rpx;
            right: 44rpx;
            width: 52rpx;
            height: 52rpx;



        }
    }

    .swap-price {
        margin-top: 60rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .Compared {
            white-space: nowrap;
            display: flex;
            align-items: center;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 120%;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #000000;

            image {
                width: 32rpx;
                height: 32rpx;
            }
        }

        .market-price {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 12*2rpx;
            line-height: 14.4*2rpx;
            color: #000;
        }

        .price {
            white-space: nowrap;
            display: flex;
            align-items: center;
            margin-top: 6rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            color: #000;

            .load {
                margin-left: 10rpx;
                margin-top: -5rpx;
            }
        }
    }

    .chart-wrapper {
        z-index: 999;
        // width: 100vw;
        height: 140px;
        // height: 100%; /* 根据父容器大小自适应 */
        margin: 0 30rpx 0 -30rpx;
    }

    .time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // color: #ffffff;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 12*2rpx;
        line-height: 14.4*2rpx;

        // border-radius: 20rpx;
        // height: 26*2rpx;
        // margin: 0 22rpx 40rpx 22rpx;
        // margin-bottom: 32rpx;
        padding: 16rpx 0;
        border-top: 2rpx solid #E0E0E0;
        border-bottom: 2rpx solid #E0E0E0;

        .timeActive {
            // background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
            background: #008E28;


            font-weight: 800;
            font-size: 24rpx;
            color: #fff;
            line-height: 7rpx;
            border-radius: 40rpx;
            border: 1rpx solid #DFE2E4;
        }

        text {
            //background-color: #2b2b2b;
            padding: 19rpx 19rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            //color: #FFFFFF;
            line-height: 7rpx;
        }
    }

    .form-wrapper {
        width: 100%;
        border-radius: 6px;
        position: relative;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    // .to {
    //     background-color: #e6f0ff;
    // }

    // .from {
    //     background-color: #f9f9f9;
    // }


    .swap_btn {
        // position: absolute;
        // left: 50%;
        cursor: pointer;
        transition: transform 0.3s ease-in-out;
        height: 40rpx;
        margin: -20rpx 0;
        // top: 220rpx;
        z-index: 999;
        // margin: 0 auto;
        border-radius: 50%;
        background-color: #FF82A3;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 68rpx;
        height: 68rpx;


        image {
            width: 34rpx;
            height: 30rpx;
        }
    }

    .float_box {
        // position: absolute;
        // top: -2rpx;
        // left: -2rpx;
        border-radius: 17*2rpx;
        background: #FFFFFF;
        height: 94*2rpx;
        width: 101%;
        border: 2rpx solid #D9D6D6;

        .to-currency {
            display: block;
            padding: 30rpx 0 0 30rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #666;
        }

        .frominfo {
            display: flex;
            transition: transform 0.3s ease-in-out;
            align-items: center;
            justify-content: space-between;
            // padding: 5rpx 8rpx;
            padding: 0 22rpx;
            width: 88*2rpx;
            height: 32*2rpx;
            border-radius: 20*2rpx;
            background: #FF82A333;
            position: relative;

            .helpoption {
                width: 85*2rpx;
                transition: transform 0.3s ease, opacity 0.3s ease;
                transform-origin: top;
                /* 设置变换的起点为顶部 */
                z-index: 9999;
                position: absolute;
                top: 80rpx;
                left: 0;
                box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
                max-height: 400rpx;
                overflow-y: auto;
                // background-color: rgba(0, 0, 0, .5);
                background: #fff;
                border-radius: 16*2rpx;
                padding: 16*2rpx;
                opacity: 1;
                //padding: 100rpx;
                // height: 446rpx;
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                &.collapse {
                    transform: scaleY(0) translateY(-100%);
                    /* 缩小至0，并向上移动 */
                    opacity: 0;
                }

                &.expand {
                    transform: scaleY(1) translateY(0%);
                    /* 恢复到正常大小，并位置恢复 */
                    opacity: 1;

                }

                >view {

                    padding: 15rpx 0;
                    display: flex;
                    align-items: center;

                    image {
                        width: 40rpx;
                        height: 30rpx;
                    }

                    text {
                        margin-left: 20rpx;
                        display: block;
                        font-family: Gilroy-Bold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        color: #000;
                    }
                }
            }

            image {
                width: 48rpx;
                height: 48rpx;
            }

            text {
                margin-left: 10rpx;
                color: #333333;
                line-height: 38rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                letter-spacing: 0%;

            }
        }
    }

    .form-row {
        width: 100%;
        // display: flex;
        height: 274rpx;
        // align-items: center;
        // margin: -20rpx 0;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        background: #FF82A326;
        border: 2rpx solid #D9D6D6;
        border-radius: 17*2rpx;
        position: relative;

        .float_box {
            position: absolute;
            top: -2rpx;
            left: -2rpx;
            border-radius: 17*2rpx;
            background: #FFFFFF;
            height: 94*2rpx;
            width: 101%;
            border: 2rpx solid #D9D6D6;



            .frominfo {
                display: flex;
                transition: transform 0.3s ease-in-out;
                align-items: center;
                justify-content: space-between;
                // padding: 5rpx 8rpx;
                padding: 0 22rpx;
                width: 88*2rpx;
                height: 32*2rpx;
                border-radius: 20*2rpx;
                background: #FF82A333;
                position: relative;

                .helpoption {
                    width: 85*2rpx;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 80rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                image {
                    width: 48rpx;
                    height: 48rpx;
                }

                text {
                    margin-left: 10rpx;
                    color: #333333;
                    line-height: 38rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    letter-spacing: 0%;

                }
            }
        }

        .available {
            font-size: 24rpx;
            color: #121212;
            display: block;
            // padding: 40rpx 0 0 40rpx;
            position: absolute;
            bottom: 24rpx;
            left: 30rpx;

            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .from-currency,
        .to-currency {
            display: block;
            padding: 30rpx 0 0 30rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #666;
        }

        label {
            flex: 1;
            font-size: 16px;
            color: #333;
        }

        select,
        input {
            flex: 2;
            padding: 10px;
            font-size: 16px;
            border-radius: 6px;
            border: 1px solid #ddd;
            margin-left: 20px;
        }
    }

    .bom {
        white-space: nowrap;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        color: #121212;
        margin-top: 16rpx;
        font-weight: 400;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;

        text {
            display: block;
        }

        width: 100%;
    }

    .tips {
        display: flex;
        width: 100%;
        justify-content: flex-start;
        margin-top: 20px;
        font-weight: 400;
        color: #FF5567;
        font-size: 24rpx;
    }

    .btn {
        width: 100%;

        margin-top: 314rpx;

        text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .exchange-btn {
            width: 100%;

            height: 100rpx;
            background: #FF82A3;
            border-radius: 64*2rpx;


            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;
            margin-top: 24rpx;
        }
    }



    .time-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        font-size: 14px;
        color: #999;
        width: 100%;
        margin-bottom: 20rpx;
    }
}

.search-box {
    margin-top: 10rpx;
    margin-right: 32rpx;
    background: #F1F1F1;
    border-radius: 50%;
    width: 100rpx;
    height: 100rpx;
    // height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
        // padding: 25rpx;
        width: 40rpx;
        height: 40rpx;
    }
}

::v-deep .u-input__input {
    font-family: Gilroy-ExtraBold;
    font-weight: 400;
    font-size: 30*2rpx;
    line-height: 36*2r12px;
    letter-spacing: 0%;
    color: #000;
}

.rotated {
    transform: rotate(180deg);
}
</style>