<template>
    <view class="container">
        <!-- 顶部返回和搜索框 -->
        <view style="display: flex;align-items: center;">
            <image class="back-icon" src="https://img.icons8.com/ios/50/000000/back.png" @click="goBack" />
            <view class="search-bar">
                <input v-model="searchQuery" class="search-input" placeholder="搜索代币和钱包"
                    placeholder-class="placeholder" />
            </view>
        </view>
        <!-- 热门代币标题 -->
        <view class="section-title">
            <text>热门代币</text>
            <image class="info-icon" src="https://img.icons8.com/ios/50/000000/info.png" />
        </view>

        <!-- 代币列表 -->
        <view class="token-list">
            <view class="token-item" v-for="token in filteredTokens" :key="token.name">
                <image class="token-icon" :src="token.icon" />
                <view class="token-info">
                    <text class="token-name">{{ token.name }}</text>
                    <text class="token-symbol">{{ token.symbol }}</text>
                </view>
                <text class="token-address">{{ token.address }}</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            searchQuery: "",
            tokens: [
                {
                    name: "Ethereum",
                    symbol: "ETH",
                    address: "",
                    icon: "https://cryptologos.cc/logos/ethereum-eth-logo.png?v=025",
                },
                {
                    name: "USDC",
                    symbol: "USDC",
                    address: "0xA0b8...eB48",
                    icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png?v=025",
                },
                {
                    name: "Tether",
                    symbol: "USDT",
                    address: "0xdAC1...1ec7",
                    icon: "https://cryptologos.cc/logos/tether-usdt-logo.png?v=025",
                },
                {
                    name: "Base ETH",
                    symbol: "ETH",
                    address: "",
                    icon: "https://cryptologos.cc/logos/ethereum-eth-logo.png?v=025",
                },
                {
                    name: "USD Coin",
                    symbol: "USDC",
                    address: "0xaf88...5831",
                    icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png?v=025",
                },
                {
                    name: "Wrapped Bitcoin",
                    symbol: "WBTC",
                    address: "0x2260...C599",
                    icon: "https://cryptologos.cc/logos/bitcoin-btc-logo.png?v=025",
                },
            ],
        };
    },
    computed: {
        filteredTokens() {
            if (!this.searchQuery) return this.tokens;
            return this.tokens.filter(token =>
                token.name.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }
    },
    methods: {
        goBack() {
            uni.navigateBack();
        }
    }
};
</script>

<style scoped>
/* 页面容器 */
.container {
    padding: 10px 16px;
    /* background-color: #fff; */
	background: linear-gradient(180deg, #e6f0ff, #ffffff);

}

/* 搜索栏 */
.search-bar {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f6f7fb;
    border-radius: 12px;
    width: 100%;
}

.back-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.search-input {
    flex: 1;
    font-size: 16px;
    background: transparent;
    border: none;
    outline: none;
}

/* 占位符样式 */
.placeholder {
    color: #b0b3b8;
}

/* 热门代币标题 */
.section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    margin: 12px 0;
}

.info-icon {
    width: 18px;
    height: 18px;
    margin-left: 6px;
}

/* 代币列表 */
.token-list {
    margin-top: 10px;
}

.token-item {

    display: flex;
    align-items: center;
    padding: 12px 10px;
    border-bottom: 1px solid #f0f0f0;
}

.token-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.token-info {
    flex: 1;
    display: flex;
    flex-direction: column;
	font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;

}

.token-name {
    font-size: 16px;
    font-weight: 500;
}

.token-symbol {
    margin-top: 4rpx;
    font-size: 14px;
    color: #888;
    font-weight: 400;
}

.token-address {
    font-size: 12px;
    color: #aaa;
}
</style>