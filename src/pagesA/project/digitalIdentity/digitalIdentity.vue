<template>
	<view class="digitalIdentity">
		<view class="back" @click="back">
			<image src="@/static/imgs/invite/back.png" mode="aspectFill"></image>
		</view>
		<view class="headImg" @click="nav()">
			<image src="@/static/digitalIdentity/headImg.png" mode="widthFix"></image>
		</view>
		<view class="tit">
			我的数字身份（{{list.length}}）
		</view>
		<template v-if="list.length!==0">
			<view class="list" v-for="(item,index) in list" :key="item.id">
				<view>
					<view>
						<image :src="item.cover.src" mode="aspectFill"></image>
					</view>
					<view class="text">
						<view>
							<view>
								<view class="oneOver1">{{item.title}}</view>
								<view v-if="item.saleStatus == 0">停售</view>
								<view v-if="item.saleStatus == 1">寄售</view>
							</view>
							<view>
								<view>{{item.expireTime}}</view>
								<view class="renew" @click="nav_renew(item)">续费
									<text class="renew_before" v-if="item.renewIconStatus==1"></text>
								</view>
							</view>
						</view>
						<view>￥{{item.price}}</view>
					</view>
				</view>
				<view>
					<image src="@/static/digitalIdentity/more.png" mode="widthFix" @click="select(item,index)"></image>
					<Transition >
						<view class="select" v-show="showId == index">
							<view  v-show="item.saleStatus == 0" @click="domainUp(item,index)">寄售</view>
							<view  v-show="item.saleStatus == 1" @click="domainDow(item,index)">停售</view>
							<view  @click="openPopIn(item,index)">应用</view>
						</view>
					</Transition>
				</view>
			</view>
		</template>
		<view class="none" v-else>
			<view>
				<image src="@/static/digitalIdentity/none.png" mode="widthFix"></image>
			</view>
			<view>
				<view>您还没有数字身份，去上面市场看看</view>
				<view>为自己挑选一个喜欢的web3数字身份吧</view>
			</view>
		</view>

		<u-popup v-model="didShow" mode="center">
			<view class="box">
				<view class="title">
					应用于
				</view>
				<view class="chekbox">
					<u-checkbox-group  :label-disabled="true"
						active-color="rgb(215,192,192)" :wrap="true">
						<u-checkbox  v-model="item.checked" v-for="(item, index) in check"
							:key="index" :name="item.name">
							<view>{{item.name}}</view>
						</u-checkbox>
					</u-checkbox-group>
				</view>
				<view class="btns">
					<view  @click="didShow = false">取消</view>
					<view @click="setMeta()">确定</view>
				</view>
			</view>
		</u-popup>
		<u-modal v-model="isSuccess"  :content-style="bgObject"
			:title-style="titleObject" border-radius="30" :mask-close-able="true" :show-title="false" :show-confirm-button="false" width="480">
			<view class="new-modal-content">
				<view class="success_img">
					<image src="https://cdn-lingjing.nftcn.com.cn/image/20240322/942264f29d34245fa78a23becfe96b87_480x480.png" mode="widthFix"></image>
				</view>
				<view class="modal-content" style="border-bottom:none;">
					{{text_msg}}
				</view>
			</view>
		</u-modal>
		<pay-popup :popup-show.sync="isPasswordImport" title="确认寄售"
				message="请输入余额支付密码，用于寄售" order-type="O" @pay="finishPay" />
		<resalePop :isShowModalResale.sync="isShowModalResale" :itemId="tid" @closeResale="closeResale">
		</resalePop>
		<u-modal v-model="isShowMsg" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content">
					是否停售数字身份
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isShowMsg=false">取消</view>
					<view class="img_reasale" @click="collectionOffShelfConfirm()">确认</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import payPopup from "@/components/payPopup/index.vue";
	import resalePop from '@/components/public/resalePop';
	export default {
		data() {
			return {
				showId: -1,
				list: [],
				check: [{
						name: 'contract address',
						checked: false,
						disabled: false
					},
					{
						name: '作品持有者',
						checked: false,
						disabled: false
					},
					{
						name: '群聊交易播报',
						checked: false,
						disabled: false
					},
					{
						name: '我的元宇宙',
						checked: false,
						disabled: false
					}
				],
				tid: '',
				isPasswordImport: false, // 密码弹窗
				titleObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				bgObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				isSuccess:false,
				text_msg:"",
				isShowModalResale:false,
				isShowMsg:false,
				didShow: false,
				pageNum: 1,
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				sun:-1
			}
		},
		components:{
			payPopup,
			resalePop
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getDomainNamelist()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		onLoad() {
			this.getDomainNamelist()
		},
		methods: {
			nav() {
				this.$Router.push({
					name: 'domain_search'
				})
			},
			select(item,index) {
				this.sun = index
				if(this.showId == index){
					this.showId =-1
				}else{
					this.showId = index
				}
			},
			back() {
				uni.navigateBack()
			},
			async getDomainNamelist() {
				let res = await this.$api.java_mallcenter_domainNamelist({
					pageNum:this.pageNum,
					pageSize: 10,
				});
				if (res.status.code == 0) {
					this.isRequest = false
					if (res.result.list == null || res.result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						res.result.list.forEach((item) => {
							this.list.push(item)
						})
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getMeta() { //获取check列表
				let res = await this.$api.java_getMetaApplication({
					tid: this.tid //item.tid
				});
				if (res.status.code == 0) {
					this.didShow = true
					this.showId = -1
					this.check[0].checked = false
					this.check[1].checked = false
					this.check[2].checked = false
					this.check[3].checked = false
					if (res.result.applicationType != null) {
						res.result.applicationType.forEach((item) => {
							console.log(item);
							if (item == 1) {
								this.check[0].checked = true
							}
							if (item == 2) {
								this.check[1].checked = true
							}
							if (item == 3) {
								this.check[2].checked = true
							}
							if (item == 4) {
								this.check[3].checked = true
							}
						});
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async setMeta() {
				console.log(this.check)
				let str = []
				this.check.forEach((item,index) => {
					if (item.checked) {
						str.push(index+1)
					}
				})
				console.log(str.toString())
				let res = await this.$api.java_setMetaApplication({
					tid: this.tid,
					applicationType: str.toString()
				});
				if (res.status.code == 0) {
					this.didShow = false
					uni.showToast({
						title: '设置成功',
						icon: 'none',
						duration: 3000
					});
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			//域名寄售
			domainUp(item, index) {
				this.tid = item.tid
				this.showId = -1
				// this.isPasswordImport=true
				this.isShowModalResale = true
				// this.colseMaskShowPop()
				// this.openModalResale(index)
			},
			//域名停售
			domainDow(item, index) {
				this.tid = item.tid
				this.isShowMsg =true
				this.showId = -1
				// this.colseMaskShowPop()
				// this.openModalCollectionOffShelf(index)
			},
			finishPay(e) {
				this.isPasswordImport = false
				console.log("密码为", e)
				this.collectionPutaway(e) 
			},
			closeResale(data) {
				this.isShowModalResale = false
				this.contentList = []
				if (data) {
					this.mode = "pay"
					this.resalePrice = data
					this.isPasswordImport = true
				}
			},
			async collectionPutaway(password) { //提交藏品寄售
				let res = await this.$api.visibility({
					tid: this.tid,
					price: this.resalePrice,
					tradePassword: password
				});
				if (res.status.code == 0) {
					this.isSuccess = true
					this.text_msg = "数字身份寄售成功！"
					this.list[this.sun].saleStatus = 1
					setTimeout(() => { 
						this.isSuccess = false
						this.getDomainNamelist()
					}, 2000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			// 藏品一键停售确定按钮
			async collectionOffShelfConfirm() {
				this.isShowMsg=false
				let res = await this.$api.unSale({
					tid: this.tid,
					price: this.resalePrice,
					tradePassword: ""
				})
				if (res.status.code == 0) {
					this.isSuccess = true
					this.text_msg = '数字身份停售成功'
					this.list[this.sun].saleStatus = 0
					setTimeout(() => {
						this.isSuccess = false
						this.getDomainNamelist()
					}, 2000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			openPopIn(item,index){
				
				this.tid = item.tid
				this.getMeta(item)
			},
			//续费
			nav_renew(item) {
				console.log(item)
				let orderList = {
					"needConsignee": false,
					"alipay": true,
					"wechat": false,
					"isFirst": 0,
					"item": {
						"id": 0,
						"type": 0,
						"tokenId": "",
						"name": "",
						"desc": "",
						"content": "",
						"showVersion": "1/1",
						"version": 1,
						"photoShow": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
						"photo": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
						"price": "",
						"fondCount": 0,
						"scanCount": 0,
						"commentCount": 0,
						"collectCount": 0,
						"time": "",
						"qrLink": "http://web-test.nftcn.com.cn/h5/#/pagesA/project/mall/mallDetails?tid=43465200249102937606166236479231",
						"link": "nftcn://item?itemTokenId=43465200249102937606166236479231",
						"coverImage": {
							"src": "https://cdn-lingjing.nftcn.com.cn/image/20220826/e0bd9f66fcaa3397493ee15e28b6cba1_240x240.png",
							"w": 240,
							"h": 240
						},
						"isFromMysteryBox": false,
						"isBlind": false,
						"endTime": "",
						"notSaleSign": 0
					},
					"kingVersion": null,
					"copyrightFeeShow": null,
					"tip": null,
					"iversion": null
				}
				orderList.item.showVersion = item.title
				orderList.item.price = item.price
				orderList.item.name = item.title
				orderList.item.tid = item.tid
				orderList.item.coverImage = item.cover
				orderList.batchBuyNum = 1
				orderList.domain = true
				uni.setStorageSync("detailsList", orderList);
				this.$Router.push({
					name: "checkOrder",
					params: {
						isDomain: 2
					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-checkbox__label{
		color: var(--default-color3);
	}
	.box {
		width: 500rpx;
		min-height: 300rpx;
		background: var(--main-bg-color);
		border-radius: 25rpx;
		padding: 15rpx;
		box-sizing: border-box;

		.title {
			width: 100%;
			text-align: center;
			color: #fff;
			padding-top: 15rpx;
			box-sizing: border-box;
		}

		.chekbox {
			width: 90%;
			margin: 30rpx auto 30rpx auto;
			background: #00000040;
			border-radius: 10rpx;
			padding: 30rpx;
			box-sizing: border-box;
		}

		.btns {
			width: 90%;
			display: flex;
			align-items: center;
			margin:30rpx auto;
			justify-content: space-around;
			>view {
				width: 120rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				border-radius: 15rpx;
				font-weight: bold;
				font-size: 28rpx;
			}

			>view:nth-child(1) {
				background: #35333E;
				border: 1rpx solid #fff;

				color: #FFFFFF;
			}

			>view:nth-child(2) {
				background: linear-gradient(135deg, #EF91FB 0%, #40F8EC 100%);
				color: #141414;
			}
		}
	}

	.digitalIdentity {
		width: 100%;
		height: 100vh;
		padding:0 36rpx;
		box-sizing: border-box;
	}

	.back {
		width: 60rpx;
		height: 60rpx;
		/* #ifdef APP */
		margin-top:var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		margin-top:51rpx;
		/* #endif */
		>image {
			width: 100%;
			height: 100%;
		}
	}

	.headImg {
		width: 100%;

		>image {
			width: 100%;
		}
	}

	.tit {
		margin: 40rpx auto;
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;
	}

	.list {
		width: 678rpx;
		height: 140rpx;
		background: #25232D;
		border-radius: 24rpx;
		margin: 0 auto 35rpx auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		box-sizing: border-box;

		>view:nth-child(1) {
			display: flex;
			align-items: center;

			>view:nth-child(1) {
				width: 80rpx;
				height: 80rpx;
				margin-right: 14rpx;

				>image {
					width: 100%;
					height: 100%;
				}
			}

			.text {
				flex: 1;
				display: flex;
				align-items: center;


				>view:nth-child(1) {
					height: 80rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					>view {
						display: flex;
						align-items: center;
					}

					>view:nth-child(1) {
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;

						>view:nth-child(2) {
							margin-left: 10rpx;
							width: 70rpx;
							height: 32rpx;
							line-height: 32rpx;
							text-align: center;
							background: #25232D;
							border-radius: 10rpx;
							border: 1rpx solid #63EAEE;
							box-sizing: border-box;
							font-weight: 400;
							font-size: 18rpx;
							color: #63EAEE;
						}
					}

					>view:nth-child(2) {
						font-weight: 400;
						font-size: 22rpx;
						color: #ccc;

						>view:nth-child(2) {
							margin-left: 14rpx;
							width: 80rpx;
							height: 30rpx;
							line-height: 30rpx;
							text-align: center;
							background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
							border-radius: 35rpx;
							font-weight: bold;
							font-size: 18rpx;
							color: #141816;
						}
					}
				}

				>view:nth-child(2) {
					font-weight: bold;
					font-size: 28rpx;
					color: #63EAEE;
					margin-left: 25rpx;
				}
			}
		}

		>view:nth-child(2) {
			width: 50rpx;
			height: 32rpx;
			position: relative;

			>image {
				width: 100%;
				height: 100%;
			}

			.select {
				width: 120rpx;
				height: 136rpx;
				background: #35333E;
				box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(0, 0, 0, 0.3);
				border-radius: 14rpx;
				position: absolute;
				top: 42rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
				right: 0;
				text-align: center;
				z-index: 33;

				>view {
					width: 100%;
					height: 68rpx;
					line-height: 68rpx;
				}
			}
		}
	}

	.none {
		width: 100%;
		margin-top: 100rpx;
		text-align: center;
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;

		>view:nth-child(1) {
			width: 240rpx;
			height: 240rpx;
			margin: 0 auto 10rpx auto;

			>image {
				width: 100%;
				height: 100%;
			}
		}

		>view:nth-child(2) {
			line-height: 1.5;
		}
	}


	.v-enter-active,
	.v-leave-active {
		transition: opacity 0.5s ease;
	}

	.v-enter-from,
	.v-leave-to {
		opacity: 0;
	}
	.new-modal-content {
		padding:35rpx 40rpx;
		background:var(--main-bg-color);
		.success_img{
			display: flex;
			justify-content: center;
			align-items: center;
			image{
				width:160rpx;
				height:160rpx;
			}
		}
		
		.modal-content{
			padding:45rpx 0rpx;
			border-bottom:1rpx solid #53505D;
			font-size:28rpx;
			color:#fff;
			text-align: center;
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color:#fff;
			}
			.img_cancel {
				border: 1px solid #fff;
			}
			.img_reasale {
				color:var(--default-color2);
				background: var(--primary-button-color);
			}
		}
	}
	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;
	
		.icon {
			position: absolute;
			left: 0rpx;
			top: 16rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}
	/* 超出一行省略 */
	.oneOver1 {
		display: inline-block;
		/*超出部分隐藏*/
		white-space: nowrap;
		overflow: hidden;
		/*不换行*/
		max-width:300rpx;
		text-overflow: ellipsis;
		/*超出部分文字以...显示*/
	}
	.renew{
		position: relative;
	}
	.renew_before{
		position: absolute;
		top:-8rpx;
		right:-5rpx;
		width:16rpx;
		height:16rpx;
		background-color:#EC4068;
		border-radius:50%;
	}
</style>