<template>
    <div>
        <C2CIndex v-if="activeIndex === 1" :nowCoins="nowCoins"></C2CIndex>
        <C2COrder v-if="activeIndex === 2" :onReachBottoms="onReachBottoms"></C2COrder>
        <C2CMerchant v-if="activeIndex === 3"></C2CMerchant>
        <tabbar :current="activeIndex" @changeTab="changeTab" />
    </div>
</template>

<script>
import C2CIndex from "./index";
import C2COrder from "./order"
import C2CMerchant from "./merchant"
import tabbar from "./components/tabbar";
export default {
    components: {
        C2CIndex,
        C2COrder,
        C2CMerchant,
        tabbar
    },
    onReachBottom() {
        console.log(123);

        this.onReachBottoms++
    },
    onLoad(option) {
        if (option) {
            if (option.coin) {
                this.nowCoins = JSON.parse(decodeURIComponent(option.coin));
            }
            console.log(this.nowCoins);

        }

    },
    data() {
        return {
            activeIndex: 1,
            onReachBottoms: 1,
            nowCoin: {}
        }
    },
    methods: {
        changeTab(e) {
            this.activeIndex = e
        }
    },
}
</script>

<style lang="scss" scoped></style>