<template>
    <view class="cancel-reason-container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="">
        </u-navbar>
        <view class="title">请您选择取消原因</view>
        <view class="subtitle">当前状态下，买卖双方取消订单，可能面临处罚。</view>
        <radio-group @change="radioChange" class="reason-list">
            <label v="for" class="reason-item" :key="item.value" v-for="item in reasons">
                <view class="radio-wrapper">
                    <radio :value="item.value" :checked="item.value === selectedReason" color="#FF82A3"
                        style="transform:scale(0.8)" />
                </view>
                <view class="reason-text">{{ item.text }}</view>
            </label>
        </radio-group>


        <view class="btn ">
            <!-- <text class="bom">processingFee:1,    networkFee:2</text> -->
            <u-button hover-class="none" class="exchange-btn " @click="StartBuy">下一步</u-button>
        </view>
    </view>
</template>

<script>
import uButton from '../../../../uni_modules/uview-ui/components/u-button/u-button.vue';
export default {
    components: { uButton },
    data() {
        return {
            reasons: [
                { value: '我不想交易了', text: '我不想交易了' },
                { value: '价格不合适', text: '价格不合适' },
                { value: '对方被风控了，无法交易', text: '对方被风控了，无法交易' },
                // 您可以根据需要添加更多原因
            ],
            selectedReason: '', // 用于存储当前选中的原因
            orderId: ""
        };
    },
    onLoad(option) {
        this.orderId = option.orderNo;
    },
    methods: {
        async StartBuy() {
            let res = await this.$api.c2cOrderCancel({
                orderNo: this.orderId,
                cancelMsg: this.selectedReason
            });
            if (res.code == 200) {
                uni.showToast({
                    title: '取消成功',
                    icon: "none",
                    duration: 2000
                });
                setTimeout(() => {
                    this.$Router.push({
                        name: 'Main'
                    })
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: "none",
                    duration: 2000
                });
            }
        },
        radioChange(evt) {
            this.selectedReason = evt.detail.value;
            console.log('当前选择的原因是：', this.selectedReason);
            // 在这里可以触发一些事件或者进行其他操作
            // 例如：this.$emit('reasonSelected', this.selectedReason);
        },
        confirmCancel() {
            if (!this.selectedReason) {
                uni.showToast({
                    title: '请选择取消原因',
                    icon: 'none',
                });
                return;
            }
            // 执行确认取消的逻辑
            console.log('最终确认取消，原因为：', this.selectedReason);
            // 例如：调用API提交取消请求等
        }
    },
};
</script>

<style lang="scss" scoped>
.cancel-reason-container {
    background-color: #ffffff;
    padding: 30rpx;
    font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif; // 保持和图片一致的字体风格

    .btn {
        position: fixed;
        bottom: 60rpx;
        // width: 90%;
        width: 340*2rpx;

        text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .exchange-btn {
            // margin: 0 32rpx;
            height: 100rpx;
            background: #FF82A3;
            border-radius: 64*2rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;
        }

        .exchange-btn[disabled] {
            // background: #FF82A380; // 加透明度效果
            background: #D9D6D6;
            color: #666666;
        }
    }


    .title {

        font-family: PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
        margin: 32rpx 0 0 0;
    }

    .subtitle {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 34rpx;
        color: rgba(128, 128, 128, 1);
        margin: 8rpx 0 0 0;
        border-bottom: 1rpx solid rgba(0, 0, 0, .1);
        padding-bottom: 24rpx;
    }

    .reason-list {
        margin-top: 26rpx;

        .reason-item {
            display: flex;
            align-items: center;
            padding: 14rpx 0;

            &:last-child {
                border-bottom: none; // 最后一个item不需要下边框
            }

            .radio-wrapper {
                // margin-right: 16rpx;
                // uni-app radio 默认样式可能比较难调整，这里仅做基础包裹
                // 如果需要完全自定义样式，可能需要使用 view 模拟 radio
            }

            .reason-text {
                font-family: Gilroy;
                font-weight: 300;
                font-size: 28rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .6);
            }

            // 点击效果 (可选，uni-app label 默认就有一定的点击区域)
            &:active {
                background-color: #f9f9f9;
            }
        }
    }

    // 确认按钮样式 (如果需要)
    /*
    .confirm-button {
      margin-top: 50rpx;
      background-color: #FF5500;
      color: #FFFFFF;
      border-radius: 50rpx;
      font-size: 32rpx;
      padding: 20rpx 0;
      text-align: center;
  
      &[disabled] {
        background-color: #cccccc;
        color: #999999;
      }
    }
    */
}
</style>