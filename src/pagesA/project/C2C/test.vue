<template>
    <view class="animated-input-wrapper">
      <!-- 透明输入框：用于收集输入 + 光标显示 -->
      <u-input
        v-model="inputValue"
        placeholder=" "
        type="number"
        class="hidden-input"
        :custom-style="inputStyle"
        :focus="true"
        :clearable="false"
      ></u-input>
  
      <!-- 字符动画展示层 -->
      <view class="animated-text">
        <view
          v-for="(char, index) in displayChars"
          :key="char.id"
          class="char-wrapper"
        >
          <text
            class="char-text"
            :class="[char.status]"
            :style="{ fontSize: fontSize + 'rpx' }"
          >
            {{ char.value }}
          </text>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  let uid = 0
  export default {
    data() {
      return {
        inputValue: '',
        displayChars: []
      }
    },
    computed: {
      fontSize() {
        const baseSize = 60
        const minSize = 30
        const len = this.inputValue.length
        return len === 0 ? baseSize : Math.max(minSize, baseSize - (len - 1) * 3)
      },
      inputStyle() {
        return {
          backgroundColor: 'transparent',
          fontSize: this.fontSize + 'rpx',
          fontWeight: 'bold',
          width: '100%',
          padding: '0',
          textAlign: 'left'
        }
      }
    },
    watch: {
      inputValue(newVal, oldVal) {
        const newChars = newVal.split('')
        const oldChars = this.displayChars.map((c) => c.value)
        const result = []
        const maxLen = Math.max(newChars.length, oldChars.length)
  
        for (let i = 0; i < maxLen; i++) {
          const oldChar = oldChars[i]
          const newChar = newChars[i]
  
          if (oldChar === newChar) {
            result.push({
              ...this.displayChars[i],
              status: 'stay'
            })
          } else if (newChar !== undefined) {
            result.push({
              id: uid++,
              value: newChar,
              status: 'enter'
            })
          }
        }
  
        if (newChars.length < oldChars.length) {
          for (let i = newChars.length; i < oldChars.length; i++) {
            result.push({
              ...this.displayChars[i],
              status: 'leave'
            })
          }
        }
  
        this.displayChars = result
  
        setTimeout(() => {
          this.displayChars = this.displayChars.filter((c) => c.status !== 'leave')
        }, 300)
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .animated-input-wrapper {
    position: relative;
    height: 100rpx;
    border-bottom: 2rpx solid #ccc;
    overflow: hidden;
  }
  
  /* 输入框区域完全透明，仅显示光标 */
  .hidden-input {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
  }
  
  /* ✅ 强制隐藏 u-input 的字体 */
  .hidden-input ::v-deep .u-input__input {
	color: transparent !important;
}

  
  /* 字符动画展示层（与输入层重叠） */
  .animated-text {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100rpx;
    width: 100%;
    z-index: 1;
    pointer-events: none;
  }
  
  /* 单字符 */
  .char-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .char-text {
    font-weight: bold;
    color: #000;
    display: inline-block;
    transform: translateY(0);
    opacity: 1;
    transition: all 0.3s ease;
  }
  
  /* 进入动画 */
  .char-text.enter {
    transform: translateY(100%);
    opacity: 0;
    animation: slideUp 0.3s forwards;
  }
  
  /* 离开动画 */
  .char-text.leave {
    transform: translateY(0%);
    opacity: 1;
    animation: slideDown 0.3s forwards;
  }
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0%);
      opacity: 1;
    }
  }
  
  @keyframes slideDown {
    from {
      transform: translateY(0%);
      opacity: 1;
    }
    to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
  </style>
  