<template>
    <view class="ban-info-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="封禁详情">
        </u-navbar>
        <view style="height: 48rpx;"></view>
        <view class="title">您的商家身份已被封禁</view>
        <view class="sub">如有疑问可以到问题反馈中申述</view>

        <view class="tips">
            <view>预计7个工作日内完成审核，请耐心等待</view>
            <view>退回商家保证金会收取一定的手续费用</view>
            <view>商家保证金在审核通过后会立即释放到账户中</view>
            <view>退回保证金后默认解除认证商家身份，且30天内不允许重新申请</view>
        </view>

        <view class="btn-group">
            <u-button hover-class="none" class="btn-primary" @click="goAppeal">去申述</u-button>
            <u-button hover-class="none" class="btn-outline" @click="returnDeposit">退回商家保证金</u-button>
        </view>
    </view>
</template>

<script>
export default {
    methods: {
        goAppeal() {
            uni.showToast({ title: '跳转到申述页面', icon: 'none' });
            this.$Router.push({
                name: 'C2Cappeal'
            })
        },
        async returnDeposit() {
            let res = await this.$api.c2cshopiceback({
                status: 3,
                message: ""
            })
            if (res.code == 200) {
                uni.showToast({ title: '退保证金流程启动', icon: 'none' });
                setTimeout(() => {
                    this.$Router.push({
                        name: 'merchant'
                    })
                }, 1000);
            } else {
                uni.showToast({ title: res.msg, icon: 'none' });
            }
            // uni.showToast({ title: '退保证金流程启动', icon: 'none' });
        }
    }
}
</script>
<style lang="scss" scoped>
.ban-info-page {
    padding: 32rpx;

    .title {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        line-height: 32rpx;
        color: #000;
        margin-bottom: 16rpx;
    }

    .sub {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 32rpx;
        color: #000;
        opacity: .5;
        margin-bottom: 28rpx;
    }

    .tips {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #000;
        opacity: .5;

        view {
            // margin-bottom: 16rpx;
            line-height: 40rpx;

        }
    }

    .btn-group {
        width: 694rpx;
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;

        .btn-primary {
            background-color: #FF82A3;
            color: #fff;
            text-align: center;
            padding: 26rpx 0;
            border-radius: 112rpx;
            width: 100%;
        }

        .btn-outline {
            border: 2rpx solid #FF82A3;
            color: #FF82A3;
            text-align: center;
            width: 100%;
            padding: 26rpx 0;
            border-radius: 112rpx;
        }
    }
}
</style>