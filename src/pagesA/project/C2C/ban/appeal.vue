<template>
    <view class="feedback-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="提交申诉">
        </u-navbar>
        <view class="label">反馈内容</view>
        <view class="textarea">
            <textarea v-model="content" placeholder="请输入反馈内容"
                placeholder-style="color:rgba(0, 0, 0, .5);font-size:24rpx;font-weight: 400;" />
        </view>

        <view class="submit-btn" @click="submit">提交</view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            content: ''
        }
    },
    methods: {
        async submit() {

            if (!this.content.trim()) {
                return uni.showToast({ title: '请输入反馈内容', icon: 'none' });
            }

            let res = await this.$api.c2cshopiceback({
                status: 2,
                message: this.content
            })
            if (res.code == 200) {
                uni.showToast({ title: '申诉成功', icon: 'none' });
                setTimeout(() => {
                    this.$Router.push({
                        name: 'merchant'
                    })
                }, 1000);
                this.content = '';
            } else {
                uni.showToast({ title: res.msg, icon: 'none' });
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.feedback-page {
    padding: 32rpx;
    width: 100%;

    .label {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        line-height: 32rpx;
        color: #000;
        margin: 48rpx 0 24rpx 0;
    }

    .textarea {
        // flex: 1;
        overflow: hidden;
        background-color: #f2f2f2;
        border-radius: 8rpx;
        font-size: 24rpx;
        padding: 24rpx 28rpx;
        color: #000;
        width: 100%;

        font-family: PingFang SC;
        font-weight: 400;
        line-height: 32rpx;

    }

    .submit-btn {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        background-color: #FF82A3;
        color: #fff;
        text-align: center;
        padding: 24rpx 0;
        border-radius: 112rpx;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;

    }
}
</style>