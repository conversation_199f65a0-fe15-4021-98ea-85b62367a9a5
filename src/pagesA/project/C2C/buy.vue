<template>
    <view class="c2c-page">
        <!-- 顶部导航 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="'购买' + items.coin">
        </u-navbar>
        <view style="height: 16rpx;"></view>
        <view class="toggle-container">
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab === 'Buy' }" @tap="switchTab('Buy')">
                按金额购买
            </view>
            <view class="toggle-item" :class="{ active: activeTab === 'Sell' }" @tap="switchTab('Sell')">
                按数量购买
            </view>
        </view>



        <view class="c2c-buy">
            <!-- 金额显示区域 -->
            <view class="amount-box">
                <view class="amount">
                    <u-input :clearable="false" placeholder="0.00" v-model="amountOrPrice" />
                </view>
                <view class="all"><text style="color: #010101;margin-right: 20rpx" v-if="activeTab === 'Buy'">{{
                    getCurrencySymbol(items.fait) }}</text>
                    <text style="color: #010101;margin-right: 20rpx" v-else>{{ items.coin }}</text>
                    <text @click="allNum()">全部</text>
                </view>
            </view>
            <!-- {{ items.coin }} -->
            <view class="limit">限额 {{ items.minAmount + ' - ' + items.maxAmount }} </view>
            <view class="receive">{{ activeTab == 'Buy' ? '可得' : "出售" }}
                <text style="color: #000;margin-left: 6rpx;">{{ canget }}</text>
            </view>

            <!-- 支付方式 -->
            <view class="pay-method">
                <view class="title">C2C充值</view>
                <view class="method">{{ items.payType == 1 ? '微信支付' : items.payType == 2 ? '支付宝支付' : "银行卡支付" }}</view>
            </view>

            <!-- 商家信息 -->
            <view class="merchant-info">
                <view class="title">商家信息</view>
                <view class="info-row" style="padding: 32rpx 0 28rpx 0;">
                    <view class="label">支付时间</view>
                    <view class="value">20分钟</view>
                </view>
                <view class="info-row">
                    <view class="label">商家昵称</view>
                    <view class="value">{{ items.shopName || '--' }}
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1371889604490780672.png" />
                    </view>
                </view>
            </view>

            <!-- 提示信息 -->
            <view class="tips">
                <view class="title">C2C交易资格</view>
                <view class="content">
                    请买家注意：必须您本人实名一致的账户支付，买家保证资金来源合法，如出现问题，承担一切经济责任以及法律责任。
                    切勿轻信、听讼他人引导把币提现到别的任何平台，以上属于个人行为，不听劝导致被骗和商家无关，请仔细阅读条款再下单，下单默认接受以上所述条款！
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="footer">
                <u-button @click="handleBuy" hover-class="none" class="buy-btn">0手续费买入{{ items.coin }}</u-button>
            </view>
        </view>

        <u-popup border-radius="16" v-model="isPopupVisible" mode="bottom" :mask="true" :close-on-click-mask="true">
            <TransferReminderPopup :amount="currentTransferAmount" @close="handlePopupClose" @cancel="handlePopupCancel"
                @confirm="handlePopupConfirm" />
        </u-popup>

        <u-popup v-model="showValid" mode="bottom" :mask-close-able="false" border-radius="16">
            <view class="verify-popup">
                <view class="popup-header">
                    <text class="title">验证码</text>
                    <text class="close" @click="showValid = false">
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1371931948699181056.png" />
                    </text>
                </view>

                <view class="form-group">
                    <view class="label">资金密码</view>
                    <view class="bg">
                        <u-input height="80" v-model="form.fundPassword" placeholder="请输入资金密码" type="password"
                            class="form-input" />
                    </view>
                </view>

                <view class="form-group">
                    <view class="label">邮箱验证码</view>
                    <view class="input-send-row">
                        <view class="bg">
                            <u-input height="80" v-model="form.emailCode" placeholder="请输入邮箱验证码" class="form-input" />
                        </view>
                        <text class="send-btn" :class="{ disabled: countdown > 0 }" @click="sendCode">
                            {{ countdown > 0 ? `${countdown}s` : '发送' }}
                        </text>
                    </view>
                </view>

                <view class="confirm-btn" @click="confirm">确定</view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import TransferReminderPopup from './components/TransferReminderPopup.vue'; // 路径根据你的项目结构调整
import { getCurrencySymbol } from "@/utils/utils.js"
import nodata from "./components/nodata.vue"
export default {
    components: {
        nodata,
        TransferReminderPopup
    },
    data() {
        return {
            amountOrPrice: "",
            form: {
                fundPassword: '',
                emailCode: ''
            },
            countdown: 0,
            timer: null,
            showValid: false,
            isPopupVisible: false,
            currentTransferAmount: 7, // 或者从其他地方获取
            // shownext: false,
            activeTab: 'Buy',
            currentTab: 'buy',
            list: [
                { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' },
                { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' },
                { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' }
            ],
            iconMap: {
                usdt: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
                arrowDown: 'https://cdn-icons-png.flaticon.com/512/60/60995.png',
                c2c: 'https://cdn-icons-png.flaticon.com/512/857/857681.png',
                order: 'https://cdn-icons-png.flaticon.com/512/1828/1828911.png',
                me: 'https://cdn-icons-png.flaticon.com/512/847/847969.png'
            },
            items: {},
            coins: []
        };
    },
    onLoad(option) {
        let item = JSON.parse(decodeURIComponent(option.item));
        this.items = item;
        console.log(item);
        this.getc2csupportCoin()
    },
    unLoad() {
        clearInterval(this.timer);
    },
    computed: {
        canget() {
            // 默认精度为 2
            let coinDecimalCount = 2

            // 1. 获取当前币种的精度
            for (let i = 0; i < this.coins.length; i++) {
                const e = this.coins[i]
                if (this.items.coin === e.coin) {
                    coinDecimalCount = e.coinDecimalCount
                    break
                }
            }

            // 2. 获取价格与金额
            const price = parseFloat(this.items?.price) || 0
            const amount = parseFloat(this.amountOrPrice) || 0
            if (!price) return 0

            // 3. 向上进位函数（基于第 coinDecimalCount + 1 位是否 > 0）
            const smartCeil = (val, digits = 2) => {
                const baseFactor = Math.pow(10, digits)
                const checkDigit = Math.floor(val * Math.pow(10, digits + 1)) % 10
                const base = Math.floor(val * baseFactor)
                return (checkDigit > 0 ? base + 1 : base) / baseFactor
            }

            // 4. 按照交易类型计算
            if (this.activeTab === 'Buy') {
                return smartCeil(amount / price, coinDecimalCount)
            } else {
                // return smartCeil(this.items.countNum, coinDecimalCount)

                return smartCeil(amount * price, coinDecimalCount)
            }
        }

    },
    methods: {
        getCurrencySymbol,
        allNum() {
            // 默认精度为 2
            let coinDecimalCount = 2

            // 1. 获取当前币种的精度
            for (let i = 0; i < this.coins.length; i++) {
                const e = this.coins[i]
                if (this.items.coin === e.coin) {
                    coinDecimalCount = e.coinDecimalCount
                    break
                }
            }

            // if (this.activeTab === 'Buy') { // 金额
            //     this.amountOrPrice = (this.items.maxAmount * this.items.price).toFixed(coinDecimalCount)
            // } else {
            //     this.amountOrPrice = (this.items.maxAmount).toFixed(coinDecimalCount)
            // }

            if (this.activeTab === 'Buy') { // 金额
                // this.amountOrPrice = (this.items.maxAmount * this.items.price).toFixed(coinDecimalCount)
                this.amountOrPrice = (this.items.maxAmount).toFixed(coinDecimalCount)
            } else {
                this.amountOrPrice = (this.items.coinMax).toFixed(coinDecimalCount)
                // this.amountOrPrice = (this.items.coin_max).toFixed(coinDecimalCount)
                // this.amountOrPrice = ((this.items.maxAmount.toFixed(2)) / this.items.price).toFixed(coinDecimalCount)
                // this.amountOrPrice = (this.items.maxAmount).toFixed(coinDecimalCount)
            }
        },
        async getc2csupportCoin() {
            let res = await this.$api.c2csupportCoin()
            if (res.code == 200) {
                this.coins = res.result
            }
        },
        sendCode() {
            if (this.countdown > 0) return;
            // 模拟发送验证码逻辑
            uni.showToast({ title: '验证码已发送', icon: 'success' });

            this.countdown = 60;
            this.timer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    clearInterval(this.timer);
                }
            }, 1000);
        },
        confirm() {
            if (!this.form.fundPassword || !this.form.emailCode) {
                return uni.showToast({ title: '请输入完整信息', icon: 'none' });
            }
            uni.showToast({ title: '验证成功', icon: 'success' });
            this.showPopup = false;
        },
        showReminder() {
            this.currentTransferAmount = 100; // 示例：动态修改金额
            this.isPopupVisible = true;
        },
        handlePopupClose() {
            this.isPopupVisible = false;
            console.log('弹窗已关闭');
        },
        handlePopupCancel() {
            console.log('用户点击了取消');
            // isPopupVisible 会在组件内部的 closePopup 中设置为 false
        },
        handlePopupConfirm(data) {
            console.log('用户确认转账，金额:', data.amount);
            this.$Router.push({
                name: "progressBuy"
            })
            // isPopupVisible 会在组件内部的 closePopup 中设置为 false
            // 在这里执行实际的业务逻辑，例如通知卖家
        },
        switchTab(tab) {
            this.activeTab = tab
            // this.allNum()
            this.amountOrPrice = ''
        },
        handleBuy() {
            // this.$Router.push({
            //     name: "progressBuy"
            // })
            this.makeorder()
        },
        async makeorder() {
            let isBuy = this.activeTab === 'Buy'
            let res = await this.$api.c2cOrderCreate({
                shopOrderId: this.items.orderNo,
                // orderType: 1,
                operType: isBuy ? 1 : 0,
                ...(isBuy
                    ? { amount: this.amountOrPrice, canGetCount: this.canget }
                    : { countNum: this.amountOrPrice, canGetAmount: this.canget })
            })
            if (res.code == 200) {
                uni.showToast({
                    title: '下单成功',
                    icon: 'none',
                });
                // this.isPopupVisible = true

                setTimeout(() => {
                    // this.$Router.back()
                    this.$Router.push({
                        name: "progressBuy",
                        params: {
                            orderNo: res.result
                        }
                    })
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                });
            }
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .uni-input-placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(0, 0, 0, .3) !important;
}

.c2c-page {
    height: 100%;
    width: 100%;

    .verify-popup {
        background-color: #fff;
        padding: 44rpx 32rpx;
        border-radius: 16rpx;

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32rpx;

            .title {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 32rpx;
                line-height: 48rpx;
                color: #000;
            }

            .close {
                font-size: 40rpx;
                color: #999;

                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .form-group {
            margin-bottom: 32rpx;

            .label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 32rpx;
                margin-bottom: 18rpx;
                color: rgba(0, 0, 0, .5);
            }

            .bg {
                background: #F2F2F2;
                border-radius: 8rpx;
                padding: 0 28rpx;


            }

            .form-input {
                font-size: 28rpx;
            }

            .input-send-row {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .form-input {
                    flex: 1;
                    // width: 602rpx;
                }

                .bg {
                    width: 100%;
                }

                .send-btn {
                    display: block;
                    min-width: 60rpx;
                    margin-left: 28rpx;
                    font-size: 28rpx;
                    color: #FF82A3;
                    font-family: PingFang SC;
                    font-weight: 400;
                    line-height: 32rpx;

                    &.disabled {
                        color: #ccc;
                    }
                }
            }
        }

        .confirm-btn {
            background-color: #FF82A3;
            color: #fff;
            text-align: center;
            padding: 24rpx 0;
            border-radius: 50rpx;
            font-size: 28rpx;
            margin-top: 80rpx;
            font-family: PingFang HK;
            font-weight: 500;

        }
    }

    .c2c-buy {
        padding: 32rpx;

        .amount-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1rpx solid rgba(0, 0, 0, .1);
            padding-bottom: 10rpx;

            .amount {
                font-size: 56rpx;
                font-weight: 600;
                color: #000;
            }

            .all {
                font-size: 28rpx;
                color: #f75e96;
            }
        }

        .limit {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .4);
            margin-top: 24rpx;
        }

        .receive {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .4);
            margin-top: 16rpx;
        }

        .pay-method {
            margin-top: 41rpx;
            padding: 38rpx 32rpx;
            background-color: rgba(223, 223, 223, .1);
            border-radius: 32rpx;
            border: 1.5px solid rgba(0, 0, 0, .1);
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;

            .title {
                color: rgba(0, 0, 0, .4);
                margin-bottom: 6rpx;
            }

            .method {
                color: #000;
            }
        }

        .merchant-info {
            margin-top: 40rpx;

            .title {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 32rpx;
                color: #000;
            }

            .info-row {
                display: flex;
                justify-content: space-between;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;

                // &:nth-of-type(1) {
                //     padding: 32rpx 0 28rpx 0;
                // }

                .label {
                    color: rgba(0, 0, 0, .5);
                }

                .value {
                    display: flex;
                    align-items: center;
                    color: rgba(0, 0, 0, 1);

                    image {
                        width: 24rpx;
                        height: 24rpx;
                        margin-left: 12rpx;
                    }
                }
            }
        }

        .tips {
            margin-top: 188rpx;
            font-size: 24rpx;
            color: #999;
            line-height: 36rpx;

            .content {
                margin-top: 28rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .5);
            }

            .title {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 32rpx;
                color: #000;
            }

        }

        .footer {
            position: fixed;
            bottom: 40rpx;
            left: 32rpx;
            right: 32rpx;

            .buy-btn {
                width: 100%;
                height: 88rpx;
                line-height: 88rpx;
                background-color: #FF82A3;
                color: #fff;
                font-family: PingFang HK;
                font-weight: 500;
                font-size: 28rpx;
                border-radius: 112rpx;
            }
        }
    }

    .toggle-container {
        position: relative;
        // width: 500rpx;
        margin: 27rpx 28rpx 0 28rpx;
        // padding-top: 16rpx;
        // width: 100%;
        height: 96rpx;
        // background-color: #F6F6F6;
        border-radius: 80rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6rpx;
        // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370432022240649216.png");
        background-size: 100% 100%;

        // overflow: hidden;
        // filter: blur(13.800000190734863px);
        .rounded-triangle-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .toggle-item {
            width: 50%;
            text-align: center;
            font-size: 28rpx;
            z-index: 1;
            font-family: Gilroy;
            font-weight: 500;
            line-height: 32rpx;
            transition: color 0.3s;
            color: rgba(0, 0, 0, .4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .toggle-item.active {
            color: #fff;
        }

        .toggle-bg {
            height: 80rpx;
            position: absolute;
            // top: -10rpx;
            // border: 1.07px solid #FFFFFF61;
            // bottom: 6rpx;
            width: 328rpx;
            border-radius: 80rpx;
            background-color: #ff6f96;
            // z-index: 0;
            transition: left 0.3s;
            border: 1.07px solid #FFFFFF61;
            display: flex;
            justify-content: center;
            align-items: center;
            // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
        }

        .toggle-bg.Buy {
            left: 6rpx;
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.Sell {
            left: 362rpx; // 500rpx - 50% + padding
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }
    }


}
</style>