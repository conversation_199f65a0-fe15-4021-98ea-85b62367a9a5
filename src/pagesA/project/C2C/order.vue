<template>
    <view class="c2c-page">
        <!-- 顶部导航 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="C2C交易">
        </u-navbar>
        <!-- Tab切换 -->
        <!-- <view class="tab-bar">
            <view :class="['tab', currentTab === 'buy' ? 'active' : '']" @click="switchTab('buy')">购买</view>
            <view :class="['tab', currentTab === 'sell' ? 'active' : '']" @click="switchTab('sell')">出售</view>
        </view> -->

        <view style="height: 16rpx;"></view>
        <view class="toggle-container">
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab === 'Buy' }" @tap="switchTab('Buy')">
                未完成
            </view>
            <view class="toggle-item" :class="{ active: activeTab === 'Sell' }" @tap="switchTab('Sell')">
                已结束
            </view>
        </view>

        <!-- 列表 -->
        <scroll-view scroll-y>
            <!-- v-if="activeTab == 'Buy'" -->
            <view class="order-list">
                <view class="order-card" @click="nav_to('progressOrder', item)" v-for="(item, index) in list"
                    :key="index">

                    <view class="amount">
                        <text class="left">{{ item.userBuyOrSellStatus == 1 ? '卖出' : '买入' }}{{ item.coin }}</text>
                        <view class="price">{{ formatter(item) }}
                            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372234160495222784.png" />
                        </view>
                    </view>
                    <view class="limit">
                        <text class="num">价格 {{ item.coinPrice }}</text>
                        <view class="lim">
                            <text>数量 {{ item.coinCount }} USDT</text>
                            <view class="prices">{{ item.fiat == 'CNY' ? '￥' : item.fiat == 'US' ? '$' : item.fiat ==
                                'HK' ? 'HK$' : '' }}
                                {{ item.fiatAmount }}</view>
                        </view>
                    </view>

                    <view class="top">
                        <view class="user">
                            <view class="bg"></view>
                            <text>{{ item.buyerName }}</text>
                        </view>
                        <text class="rate">{{ formatTimestamp(item.createTime) }}</text>
                    </view>
                </view>
                <nodata v-if="list.length == 0" />
            </view>
            <!-- <view class="order-list" v-if="activeTab == 'Sell'">
                <nodata />

            </view> -->
        </scroll-view>

        <!-- <div class="element"></div> -->

        <!-- 底部导航 -->
        <!-- <view class="bottom-tab">
            <view class="tab-item active">
                <image :src="iconMap.c2c" />
                <text>C2C</text>
            </view>
            <view class="tab-item">
                <image :src="iconMap.order" />
                <text>订单</text>
            </view>
            <view class="tab-item">
                <image :src="iconMap.me" />
                <text>我的</text>
            </view>
        </view> -->

        <!-- <SelectableTabs :options="['房贷', '消费贷', '车贷', '企业贷']" v-model="selectedLoan" @change="onChange" /> -->
        <!-- <view class="content">
            当前选中：{{ tabIndex }}
        </view> -->

        <!-- <tabbar :current="2" /> -->
    </view>
</template>

<script>
import tabbar from "./components/tabbar.vue"
import nodata from "./components/nodata.vue"
import SelectableTabs from './components/SelectableTabs.vue'
export default {
    props: ['onReachBottoms'],
    components: {
        tabbar,
        nodata,
        SelectableTabs
    },
    data() {
        return {
            activeTab: 'Buy',
            selectedLoan: 1,
            hasNext: false,
            currentTab: 'buy',
            list: [
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85', rate: '0%' },
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85', rate: '0%' },
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85', rate: '0%' }
            ],
            iconMap: {
                usdt: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
                arrowDown: 'https://cdn-icons-png.flaticon.com/512/60/60995.png',
                c2c: 'https://cdn-icons-png.flaticon.com/512/857/857681.png',
                order: 'https://cdn-icons-png.flaticon.com/512/1828/1828911.png',
                me: 'https://cdn-icons-png.flaticon.com/512/847/847969.png'
            },
            page: {
                pageNum: 1,
                pageSize: 10
            },
            tabIndex: 0
        };
    },
    mounted() {
        this.getc2corderlist()
    },
    watch: {
        onReachBottoms(val) {
            console.log(val);

            if (val) {
                if (this.hasNext) {
                    this.page.pageNum += 1
                    this.getc2corderlist()
                }
            }
        }
    },
    onReachBottom() {
        console.log(123);
        if (this.hasNext) {
            this.page.pageNum += 1
            this.getc2corderlist()
        }
    },
    methods: {
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatter(e) {
            if (e.userBuyOrSellStatus == 1) {// 1-卖单 0-买单
                if (e.status == 0) {
                    return '已创建'
                } else if (e.status == 1) {
                    return '已取消'
                } else if (e.status == 2) {
                    return '已付款'
                } else if (e.status == 3) {
                    return '已完成'
                }
            } else {
                if (e.status == 0) {
                    return '已创建'
                } else if (e.status == 1) {
                    return '已取消'
                } else if (e.status == 2) {
                    return '待放币'
                } else if (e.status == 3) {
                    return '已完成'
                }
            }
        },
        onChange(index) {
            console.log('选中：', index)
        },
        onTabChange(index) {
            console.log('当前选中索引：', index)
        },
        async getc2corderlist() {
            let res = await this.$api.c2corderlist({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                orderStatus: this.activeTab == 'Buy' ? 0 : 1
            });
            if (res.code == 200) {
                this.hasNext = res.result.hasNext;
                if (this.page.pageNum == 1) {
                    if (res.result.data) {
                        this.list = res.result.data;
                    } else {
                        this.list = [];
                    }
                } else {
                    if (res.result.data) {
                        this.list = this.list.concat(res.result.data);
                    } else {
                        this.list = [];
                    }
                }
            } else {
                this.$u.toast(res.msg);
            }
        },
        nav_to(e, item) {
            this.$Router.push({
                name: e,
                params: {
                    item: encodeURIComponent(JSON.stringify(item))
                }
            })
        },
        switchTab(tab) {
            this.list = []
            this.page.pageNum = 1
            this.activeTab = tab
            this.getc2corderlist()
        },
        buyNow(item) {
            this.$Router.push({
                name: 'C2Cbuy'
            })
            // uni.showToast({ title: `购买 ${item.price} USDT`, icon: 'none' });
        },
        goBack() {
            uni.navigateBack();
        }
    }
};
</script>

<style lang="scss" scoped>
.c2c-page {
    height: 100%;
    width: 100%;

    .element {
        position: relative;
        width: 300px;
        height: 200px;
        background: #f0e6f6;
        /* 淡紫色背景 */
        margin: 50px auto;
    }

    .element::before,
    .element::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 70px;
        /* 进一步增加高度 */
        background: #fff;
        /* 假设外部背景为白色 */
        left: 0;
    }

    .element::before {
        top: -35px;
        /* 增加偏移量 */
        border-bottom-left-radius: 150px 70px;
        /* 匹配高度 */
        border-bottom-right-radius: 150px 70px;
    }

    .element::after {
        bottom: -35px;
        /* 增加偏移量 */
        border-top-left-radius: 150px 70px;
        border-top-right-radius: 150px 70px;
    }

    .toggle-container {
        position: relative;
        // width: 500rpx;
        margin: 27rpx 28rpx 0 28rpx;
        // padding-top: 16rpx;
        // width: 100%;
        height: 96rpx;
        // background-color: #F6F6F6;
        border-radius: 80rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6rpx;
        // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370432022240649216.png");
        background-size: 100% 100%;

        // overflow: hidden;
        // filter: blur(13.800000190734863px);
        .rounded-triangle-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .toggle-item {
            width: 50%;
            text-align: center;
            font-size: 28rpx;
            z-index: 1;
            font-family: Gilroy;
            font-weight: 500;
            line-height: 32rpx;
            transition: color 0.3s;
            color: rgba(0, 0, 0, .4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .toggle-item.active {
            color: #fff;
        }

        .toggle-bg {
            height: 80rpx;
            position: absolute;
            // top: -10rpx;
            // border: 1.07px solid #FFFFFF61;
            // bottom: 6rpx;
            width: 328rpx;
            border-radius: 80rpx;
            background-color: #ff6f96;
            // z-index: 0;
            transition: left 0.3s;
            border: 1.07px solid #FFFFFF61;
            display: flex;
            justify-content: center;
            align-items: center;
            // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
        }

        .toggle-bg.Buy {
            left: 6rpx;
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.Sell {
            left: 362rpx; // 500rpx - 50% + padding
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }
    }


    .tab-bar {
        display: flex;
        justify-content: space-around;
        margin: 20rpx 30rpx;
        background-color: #f0f0f0;
        border-radius: 60rpx;
        padding: 6rpx;

        .tab {
            flex: 1;
            text-align: center;
            padding: 20rpx 0;
            border-radius: 50rpx;
            font-size: 28rpx;
            transition: all 0.3s;

            &.active {
                background-color: #ff7fa6;
                color: #fff;
                box-shadow: 0 6rpx 12rpx rgba(255, 127, 166, 0.4);
            }
        }
    }

    .coin-select {
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 0 30rpx;
        // font-size: 28rpx;
        margin: 12rpx 0 0 28rpx;
        padding: 8rpx;
        width: 204rpx;
        height: 72rpx;
        border-radius: 54rpx;
        // filter: blur(13.800000190734863px);
        background: #F6F6F6;

        text {
            margin: 0 8rpx 0 16rpx;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;

        }

        .icon {
            width: 54rpx;
            height: 54rpx;
            // margin-right: 16rpx;
        }

        .arrow {
            width: 32rpx;
            height: 32rpx;
            // margin-left: 6rpx;
        }
    }

    .order-list {
        margin: 0 34rpx 0 28rpx;
        overflow: hidden;
        padding-bottom: 160rpx;

        .order-card {
            padding-bottom: 32rpx;
            border-bottom: 1rpx solid rgba(0, 0, 0, .1);
            margin-top: 32rpx;

            &:first-child {
                margin-top: 28rpx;
            }

            .top {
                display: flex;
                justify-content: space-between;
                // margin-bottom: 14rpx;
                margin-top: 28rpx;

                .user {
                    display: flex;
                    align-items: center;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .5);

                    .bg {
                        margin-right: 8rpx;
                        border-radius: 50%;
                        width: 28rpx;
                        height: 28rpx;
                        opacity: 0.2;
                        background: #000;
                    }
                }


                .rate {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .6);
                }
            }

            .amount {
                margin-bottom: 14rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .left {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #000;
                }

                .price {
                    display: flex;
                    align-items: center;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .6);

                    image {
                        width: 36rpx;
                        height: 36rpx;
                    }
                }
            }

            .limit {
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                .lim {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    width: 100%;

                    .prices {
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 28rpx;
                        line-height: 32rpx;
                        color: #000;
                    }

                    text {
                        &:nth-of-type(2) {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 28rpx;
                            line-height: 32rpx;
                            color: #000;
                        }
                    }
                }

                .num,
                .lim {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .4);


                }
            }

            .pay {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;

                .pay-way {
                    display: flex;
                    align-items: center;

                    .bg {
                        margin-right: 8rpx;
                        width: 8rpx;
                        height: 8rpx;
                        border-radius: 50%;
                        background: rgba(73, 160, 177, 1);
                    }

                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .6);
                }

                .buy-btn {
                    background: rgba(255, 130, 163, 1);
                    width: 108rpx;
                    height: 58rpx;
                    border-radius: 12rpx;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #fff;
                    margin: 0;
                }
            }


        }
    }

    .bottom-tab {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 120rpx;
        display: flex;
        background: #fff;
        border-top-left-radius: 60rpx;
        border-top-right-radius: 60rpx;
        box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.06);
        z-index: 10;

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 20rpx 0;
            font-size: 24rpx;
            color: #999;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-bottom: 4rpx;
            }

            &.active {
                color: #ff7fa6;
                font-weight: bold;
            }
        }
    }
}
</style>