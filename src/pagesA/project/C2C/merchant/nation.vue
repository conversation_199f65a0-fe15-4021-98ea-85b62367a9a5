<template>
    <view class="country-select-page">
        <view class="search-box">
            <u-input v-model="keyword" placeholder="搜索" prefixIcon="search" border="none" class="search-input" />
        </view>

        <view class="country-item" v-for="item in filteredList" :key="item.code" @click="selectCountry(item)">
            <image :src="item.flag" class="flag" />
            <text class="name">{{ item.name }}</text>
            <view class="right">
                <text class="code">{{ item.code }}</text>
                <u-icon name="arrow-right" size="28" color="#ccc" />
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keyword: '',
            countryList: [
                { name: '中国', code: '86', flag: 'https://flagcdn.com/w40/cn.png' },
                { name: '日本', code: '81', flag: 'https://flagcdn.com/w40/jp.png' },
                { name: '韩国', code: '82', flag: 'https://flagcdn.com/w40/kr.png' },
                { name: '中国台湾', code: '886', flag: 'https://flagcdn.com/w40/tw.png' },
                { name: '英国', code: '44', flag: 'https://flagcdn.com/w40/gb.png' }
            ]
        }
    },
    computed: {
        filteredList() {
            const kw = this.keyword.trim();
            if (!kw) return this.countryList;
            return this.countryList.filter(c => c.name.includes(kw));
        }
    },
    methods: {
        selectCountry(item) {
            uni.showToast({ title: `已选择 ${item.name}`, icon: 'none' });
            // 你可以通过 uni.$emit / 路由返回 / 存到 store
        }
    }
}
</script>
<style lang="scss" scoped>
.country-select-page {
    padding: 24rpx;

    .search-box {
        margin-bottom: 24rpx;

        .search-input {
            background-color: #f2f2f2;
            border-radius: 50rpx;
            padding: 16rpx 20rpx;
            font-size: 26rpx;
        }
    }

    .country-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 0;
        border-bottom: 1px solid #f0f0f0;

        .flag {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
        }

        .name {
            flex: 1;
            margin-left: 20rpx;
            font-size: 28rpx;
            color: #000;
        }

        .right {
            display: flex;
            align-items: center;

            .code {
                margin-right: 12rpx;
                font-size: 26rpx;
                color: #666;
            }
        }
    }
}
</style>