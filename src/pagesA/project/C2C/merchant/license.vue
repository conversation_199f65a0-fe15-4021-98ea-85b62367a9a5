<template>
    <view class="doc-type-page">
        <view class="label">证件类型</view>

        <view v-for="(item, index) in docTypes" :key="index" class="type-item"
            :class="{ selected: selectedType === item }" @click="selectType(item)">
            <view class="type-text">{{ item }}</view>
            <u-icon v-if="selectedType === item" name="checkmark" color="#FF5B99" size="32" />
        </view>

        <u-button class="next-btn" type="primary" hover-class="none" color="#FF82A3" @click="nextStep">
            下一步
        </u-button>
    </view>
</template>

<script>
export default {
    data() {
        return {
            docTypes: ['护照', '驾驶证', '身份证'],
            selectedType: '护照'
        }
    },
    methods: {
        selectType(type) {
            this.selectedType = type;
        },
        nextStep() {
            uni.showToast({ title: `已选择：${this.selectedType}`, icon: 'none' });
        }
    }
}
</script>
<style lang="scss" scoped>
.doc-type-page {
    padding: 32rpx;
    padding-bottom: 120rpx;

    .label {
        font-size: 26rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        color: #000;
    }

    .type-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 26rpx;
        margin-bottom: 20rpx;
        border-radius: 12rpx;
        border: 2rpx solid #eee;
        transition: all 0.3s ease;

        &.selected {
            border-color: #FF5B99;
            background-color: #fff0f5;
        }

        .type-text {
            font-size: 28rpx;
            color: #000;
        }
    }

    .next-btn {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        border-radius: 50rpx;
        font-size: 28rpx;
        font-weight: bold;
    }
}
</style>