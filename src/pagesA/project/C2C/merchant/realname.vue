<template>
    <view class="id-upload-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="身份认证">
        </u-navbar>
        <view class="desc">
            请上传清晰的证件照片，必须能看清证件号和姓名，仅支持 PNG、JPG、JPEG 格式，每张大小限制在 2MB 内<br />
            <text class="warn">手持证件照需包含 “SE” 和当日日期</text>
        </view>

        <view class="upload-list">
            <view class="upload-box" v-for="(item, index) in uploads" :key="index" @click="chooseImage(index)">
                <image v-if="item.url" :src="item.url" mode="aspectFill" class="preview-img" />
                <view v-else class="placeholder">
                    <u-icon name="camera" size="58" color="rgba(0, 0, 0, .3)" />
                    <view class="label">{{ item.label }}</view>
                </view>
            </view>
        </view>

        <u-button type="primary" hover-class="none" class="next-btn" :disabled="!canProceed" @click="submit">
            下一步
        </u-button>
    </view>
</template>

<script>
export default {
    data() {
        return {
            uploads: [
                { label: '身份证正面照', url: '' },
                { label: '身份证反面照', url: '' },
                { label: '手持身份证及声明包含“SE”和当日日期', url: '' }
            ]
        }
    },
    computed: {
        canProceed() {
            return this.uploads.every(img => img.url);
        }
    },
    methods: {
        chooseImage(index) {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    const filePath = res.tempFilePaths[0];
                    uni.getFileInfo({
                        filePath,
                        success: (info) => {
                            const sizeMB = info.size / (1024 * 1024);
                            if (sizeMB > 2) {
                                return uni.showToast({ title: '图片不能超过2MB', icon: 'none' });
                            }

                            const ext = filePath.split('.').pop().toLowerCase();
                            if (!['jpg', 'jpeg', 'png'].includes(ext)) {
                                return uni.showToast({ title: '仅支持 JPG/PNG/JPEG 格式', icon: 'none' });
                            }

                            this.uploads[index].url = filePath;
                        },
                        fail: () => {
                            uni.showToast({ title: '获取文件失败', icon: 'none' });
                        }
                    });
                }
            });
        },
        submit() {
            // 模拟提交
            uni.showToast({ title: '提交成功', icon: 'success' });
        }
    }
}
</script>
<style lang="scss" scoped>
.id-upload-page {
    padding: 32rpx;

    .desc {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 32rpx;
        margin: 48rpx 0 24rpx 0;
        color: #000000B2;

        .warn {
            color: #FF82A3;
        }
    }

    .upload-list {
        display: flex;
        flex-direction: column;
        gap: 24rpx;
        margin-bottom: 60rpx;

        .upload-box {
            // background: #DFDFDF;
            border-radius: 16rpx;
            height: 296rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border: 3rpx solid rgba(0, 0, 0, .1);

            .placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, 0.7);

                .label {
                    margin-top: 12rpx;
                }
            }

            .preview-img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }

    .next-btn {
        position: fixed;
        background: #FF82A3 !important; // 加透明度效果

        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        border-radius: 112rpx;
        font-family: PingFang HK;
        font-weight: 500;
        font-size: 24rpx;
        color: #fff;

    }

    // 禁用状态样式
    .next-btn[disabled] {
        background: #FF82A3 !important; // 加透明度效果
        color: #fff;
        opacity: 0.6;
    }
}
</style>