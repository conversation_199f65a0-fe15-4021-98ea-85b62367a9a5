<template>
    <view class="remove-merchant-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="解除商家身份">
        </u-navbar>
        <view class="title-row">
            <!-- <u-icon name="info-circle" size="40" color="#000" /> -->
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372250622651883520.png" />
            <view class="title">申请解除商家身份</view>
        </view>

        <view class="desc">
            解除认证商家后，30天内不允许重新申请。<br />
            预计7个工作日内完成审核，审核期间不允许发起出售交易。<br />
            冻结的商家保证金在解除商家身份后立即释放到账户中。
        </view>

        <u-button type="primary" color="#FF82A3" class="submit-btn" hover-class="none" @click="showConfirm = true">
            确认解除
        </u-button>

        <!-- 二次确认模态框 -->
        <u-popup v-model="showConfirm" mode="center" border-radius="20" safe-area-inset-bottom>
            <view class="popup-wrapper">
                <view class="header">
                    <view class="popup-title">提示</view>
                    <view class="popup-content">是否确认申请解除商家身份</view>
                </view>

                <view class="popup-actions">
                    <view class="btn cancel" @click="showConfirm = false">取消</view>
                    <view class="btn confirm" @click="confirmRemove">确定</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showConfirm: false
        }
    },
    methods: {
        async unbind() {
            try {
                const res = await this.$api.c2cShopCancel({csMsg:""})
                if (res.code == 200) {
                    uni.showToast({
                        title: '解除申请已提交',
                        icon: 'success'
                    })
                }else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.log(error)
            }
        },
        confirmRemove() {
            this.unbind()
        
            this.showConfirm = false
        }
    }
}
</script>
<style lang="scss" scoped>
.remove-merchant-page {
    padding: 32rpx;
    padding-bottom: 140rpx;

    .title-row {
        display: flex;
        align-items: center;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
        margin: 48rpx 0 28rpx 0;

        image {
            width: 40rpx;
            height: 40rpx;
        }

        .title {
            margin-left: 12rpx;
        }
    }

    .desc {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 32rpx;
        color: rgba(0, 0, 0, .5);
    }

    .submit-btn {
        background: #FF82A3 !important;
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        border-radius: 112rpx;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #fff;
    }

    .popup-wrapper {
        width: 80vw;
        background-color: #fff;
        border-radius: 24rpx;
        overflow: hidden;

        .header {
            padding: 44rpx 0 42rpx 50rpx;

            .popup-title {
                font-family: PingFang SC;
                font-weight: 600;
                line-height: 40rpx;
                font-size: 28rpx;
                color: #000;
            }

            .popup-content {
                margin-top: 12rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: rgba(0, 0, 0, .7);
            }

        }

        .popup-actions {
            display: flex;
            border-top: 1rpx solid rgba(0, 0, 0, .1);

            .btn {
                flex: 1;
                text-align: center;
                padding: 20rpx 0 28rpx 0;
                font-size: 28rpx;

                font-family: PingFang SC;
                font-weight: 400;

            }

            .cancel {
                color: rgba(0, 0, 0, .5);
                border-right: 1rpx solid rgba(0, 0, 0, .1);
            }

            .confirm {
                color: #FF82A3;
            }
        }
    }
}
</style>