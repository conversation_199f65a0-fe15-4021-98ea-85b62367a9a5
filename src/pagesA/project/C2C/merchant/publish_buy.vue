<template>
    <view class="publish-sell-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="发布购买信息">
        </u-navbar>

        <view style="height: 16rpx;"></view>
        <view class="toggle-container">
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab === 'Buy' }" @tap="switchTab('Buy')">
                按数量购买
            </view>
            <view class="toggle-item" :class="{ active: activeTab === 'Sell' }" @tap="switchTab('Sell')">
                按金额购买
            </view>
        </view>

        <!-- 购买数量 -->
        <view class="form-group">
            <view class="label">所有购买{{ activeTab === 'Buy' ? '数量' : '金额' }}</view>
            <view class="row">
                <u-input v-model="form.amount" placeholder="请输入购买数量" class="form-input" />
                <view class="symbol-select" @click="showSymbolPopup = true">
                    {{ activeTab === 'Buy' ? form.symbol : '' }}
                    <!-- <u-icon name="arrow-down" size="24" /> -->
                </view>
            </view>
        </view>

        <!-- 客户限额 -->
        <view class="form-group">
            <view class="label">未完单客户限额</view>
            <view class="row2">
                <view class="input">
                    <u-input :clearable="false" v-model="form.limitMin" placeholder="请输入最低限额" class="form-input"
                        type="number" @input="handleLimitMinInput" @blur="validateLimitMin" />
                    <view class="symbol-select" v-if="activeTab === 'Buy'" @click="showSymbolPopup = true">{{
                        form.symbol }}</view>
                </view>
                <view class="gang">-</view>
                <view class="input">
                    <u-input :clearable="false" v-model="form.limitMax" placeholder="请输入最高限额" class="form-input"
                        type="number" @input="handleLimitMaxInput" @blur="validateLimitMax" />
                    <view class="symbol-select" v-if="activeTab === 'Buy'" @click="showSymbolPopup = true">{{
                        form.symbol }}</view>
                </view>
            </view>
        </view>

        <!-- 收款币种 -->
        <view class="form-group">
            <view class="label">收款币种</view>
            <view class="payway" @click="opencoin">
                <view class="left" :style="{ color: selectedCoin ? '#000' : 'rgba(0, 0, 0, .4)' }">{{ form.coin ||
                    '请选择收款币种' }}</view>
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372249936161759232.png" />
            </view>
        </view>

        <!-- 汇率 -->
        <view class="form-group">
            <view class="label">发布汇率 <text class="tip">基准汇率：{{ baseRate || '--' }}</text></view>
            <view class="row2">
                <view class="symbol-value" @click="sub">0.01 <text class="sym">-</text></view>
                <u-input :clearable="false" :disabled="true" v-model="baseRate" height="80" input-align="center"
                    placeholder="--" class="form-input2" />
                <view class="symbol-value" @click="add">0.01 <text class="sym">+</text></view>
            </view>
            <view class="sub-tip">汇率限制区间</view>
        </view>

        <!-- 支付方式 -->
        <view class="form-group">
            <view class="label">支付方式</view>
            <!-- <u-input v-model="form.payType" placeholder="请选择收款方式" readonly suffixIcon="arrow-down"
                @click="showPayPopup = true" /> -->
            <view class="payway" @click="openpay">
                <view class="left" :style="{ color: selectedPay ? '#000' : 'rgba(0, 0, 0, .4)' }">{{ form.payType
                    || '请选择收款方式' }}</view>
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372249936161759232.png" />
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit">
            <view class="footer-note">
                如果您愿意与SE进行C2C交易，即表示您已经接受SE的C2C交易法律 & 免责声明。
            </view>
            <u-button type="primary" color="#FF82A3" class="submit-btn" hover-class="none" @click="submit">
                确认发布
            </u-button>
        </view>

        <!-- 币种选择弹窗 -->
        <u-popup v-model="showSymbolPopup" mode="bottom" border-radius="16">
            <view class="popup-wrapper">
                <u-input v-model="searchKeyword" placeholder="搜索币种" class="search-input" prefixIcon="search" />
                <scroll-view scroll-y class="symbol-list">
                    <view v-for="item in filteredSymbols" :key="item.id" class="symbol-item"
                        @click="selectSymbol(item)">
                        {{ item.coin }}
                    </view>
                </scroll-view>
            </view>
        </u-popup>

        <!-- 支付方式弹窗 -->
        <u-popup v-model="showPayPopup" mode="bottom" border-radius="20">
            <view class="pay-popup">
                <view class="popup-header">
                    <view class="title">支付方式</view>
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372530187802599424.png"
                        @click="showPayPopup = false" />
                </view>
                <SelectableTabs :dotss="true" :options="payOptions" v-model="selectedType" @change="onChangeType" />

                <!-- <view class="pay-item" v-for="(item, index) in payOptions" :key="item.id"
                    :class="{ active: selectedPay == item.code }" @click="selectPay(item)">
                    <view class="dot" :style="{ backgroundColor: getDotColor(index) }" />
                    <view class="text">{{ item.payType }}</view>
                    <u-icon name="checkmark" color="#FF5B99" size="28" v-if="selectedPay == item.code" />
                </view> -->
            </view>
        </u-popup>

        <!-- 收款法币弹窗 -->
        <u-popup v-model="showCoinPopup" mode="bottom" border-radius="20">
            <view class="pay-popup">
                <view class="popup-header">
                    <view class="title">收款法币</view>
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372530187802599424.png"
                        @click="showCoinPopup = false" />
                </view>

                <SelectableTabs :dotss="dot" :options="fiatCoins" v-model="selected" @change="onChange" />
                <!-- <view class="dot" :style="{ backgroundColor: getDotColor(index) }" /> -->

                <!-- <view class="pay-item" v-for="(item, index) in fiatCoins" :key="item"
                    :class="{ active: selectedCoin == item.fiatType }" @click="selectCoin(item)">
                    <view class="text">{{ item.fiatType }}</view>
                    <u-icon name="checkmark" color="#FF5B99" size="28" v-if="selectedCoin === item.fiatType" />
                </view> -->
            </view>
        </u-popup>
    </view>
</template>

<script>
import SelectableTabs from "../components/SelectableTabs"
export default {
    components: {
        SelectableTabs
    },
    data() {
        return {
            dot: false,
            selectedType: 1,
            selected: 1,
            fiatCoins: [],
            showCoinPopup: false,
            activeTab: 'Buy',
            form: {
                fiat: "",
                amount: '',
                symbol: '',
                limitMin: '',
                limitMax: '',
                rateMin: '',
                rateMax: '',
                payType: ''
            },
            showSymbolPopup: false,
            showPayPopup: false,
            searchKeyword: '',
            allSymbols: [],
            payTypes: ['支付宝', '微信', '银行卡'],
            baseRate: "",
            showPayPopup: false,
            selectedPay: '',
            selectedCoin: "",
            payOptions: []
        }
    },
    computed: {
        filteredSymbols() {
            const keyword = this.searchKeyword.trim().toUpperCase();
            if (!keyword) return this.allSymbols;

            return this.allSymbols.filter(item => {
                return (item.coin || '').toUpperCase().includes(keyword);
            });
        }
    },
    onLoad() {
        this.getCoin()
        this.getc2cSupportPayType()
    },
    methods: {
        openpay() {
            this.showPayPopup = true;
            this.form.payType = this.payOptions[1].label
            this.selectedPay = this.payOptions[1].value
        },
        opencoin() {
            this.form.coin = this.fiatCoins[1].label
            this.selectedCoin = this.fiatCoins[1].label
            this.baseRate = this.fiatCoins[1].value
            this.showCoinPopup = true;
        },
        // 处理最低限额输入
        handleLimitMinInput(value) {
            // 只允许输入正整数
            const cleanValue = this.formatPositiveInteger(value);
            this.form.limitMin = cleanValue;
        },

        // 处理最高限额输入
        handleLimitMaxInput(value) {
            // 只允许输入正整数
            const cleanValue = this.formatPositiveInteger(value);
            this.form.limitMax = cleanValue;
        },

        // 格式化为正整数
        formatPositiveInteger(value) {
            if (!value) return '';

            // 移除所有非数字字符
            let cleanValue = value.toString().replace(/[^\d]/g, '');

            // 移除前导零
            cleanValue = cleanValue.replace(/^0+/, '');

            // 如果为空或者为0，返回空字符串
            if (!cleanValue || cleanValue === '0') {
                return '';
            }

            return cleanValue;
        },

        // 验证最低限额
        validateLimitMin() {
            if (this.form.limitMin && parseInt(this.form.limitMin) <= 0) {
                this.form.limitMin = '';
                uni.showToast({
                    title: '最低限额必须大于0',
                    icon: 'none'
                });
            }
        },

        // 验证最高限额
        validateLimitMax() {
            if (this.form.limitMax && parseInt(this.form.limitMax) <= 0) {
                this.form.limitMax = '';
                uni.showToast({
                    title: '最高限额必须大于0',
                    icon: 'none'
                });
            }

            // 检查最高限额是否大于最低限额
            if (this.form.limitMin && this.form.limitMax) {
                const min = parseInt(this.form.limitMin);
                const max = parseInt(this.form.limitMax);
                if (max <= min) {
                    this.form.limitMax = '';
                    uni.showToast({
                        title: '最高限额必须大于最低限额',
                        icon: 'none'
                    });
                }
            }
        },

        onChangeType(index) {
            this.form.payType = this.payOptions[index].label
            this.selectedPay = this.payOptions[index].value
            // setTimeout(() => {
            //     this.showPayPopup = false
            // }, 2000);
        },
        onChange(index) {
            // console.log('选中：', index)

            this.form.coin = this.fiatCoins[index].label
            this.selectedCoin = this.fiatCoins[index].label
            this.baseRate = this.fiatCoins[index].value
            // setTimeout(() => {
            //     this.showCoinPopup = false
            // }, 2000);
        },
        async getc2cSupportPayType() {
            let res = await this.$api.c2cSupportPayType()
            if (res.code == 200) {
                this.payOptions = res.result.map(item => ({
                    label: item.payType,
                    value: item.code
                }));
            }
        },
        sub() {
            this.baseRate = (Number(this.baseRate) - 0.01).toFixed(2)
        },
        add() {
            this.baseRate = (Number(this.baseRate) + 0.01).toFixed(2)
        },
        async getCoin() {
            let res = await this.$api.c2csupportCoin()
            if (res.code == 200) {
                this.allSymbols = res.result
                this.form.symbol = res.result[0].coin
                // this.fiatCoins = res.result[0].priceList
                this.fiatCoins = res.result[0].priceList.map(item => ({
                    label: item.fiatType,
                    value: item.price
                }));
            }
        },
        getDotColor(index) {
            const colors = ['#2ebac6', '#4c6ef5', '#f59f00']
            return colors[index] || '#ccc' // fallback
        },
        switchTab(tab) {
            this.activeTab = tab
        },
        selectCoin(item) {
            this.form.coin = item.fiatType
            this.selectedCoin = item.fiatType
            this.showCoinPopup = false
            this.baseRate = item.price
        },
        selectPay(item) {
            this.form.payType = item.payType
            this.selectedPay = item.code
            this.showPayPopup = false
        },
        selectSymbol(symbol) {
            this.form.symbol = symbol.coin
            // this.fiatCoins = symbol.priceList
            this.fiatCoins = symbol.priceList.map(item => ({
                label: item.fiatType,
                value: item.price
            }));
            this.showSymbolPopup = false
        },
        async submit() {
            // 字段验证
            if (!this.form.symbol) {
                uni.showToast({
                    title: '请选择交易币种',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.form.coin) {
                uni.showToast({
                    title: '请选择法币类型',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.form.amount || parseFloat(this.form.amount) <= 0) {
                uni.showToast({
                    title: '请输入有效的交易数量',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.baseRate || parseFloat(this.baseRate) <= 0) {
                uni.showToast({
                    title: '请设置有效的交易价格',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.form.limitMin || parseInt(this.form.limitMin) <= 0) {
                uni.showToast({
                    title: '请输入有效的最低限额',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.form.limitMax || parseInt(this.form.limitMax) <= 0) {
                uni.showToast({
                    title: '请输入有效的最高限额',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            // 验证最高限额必须大于最低限额
            if (parseInt(this.form.limitMax) <= parseInt(this.form.limitMin)) {
                uni.showToast({
                    title: '最高限额必须大于最低限额',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }

            if (!this.form.payType) {
                uni.showToast({
                    title: '请选择支付方式',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }


            let coinDecimalCount = ''
            for (let i = 0; i < this.allSymbols.length; i++) {
                const e = this.allSymbols[i];
                if (this.form.symbol == e.coin) {
                    coinDecimalCount = e.coinDecimalCount
                }
            }

            let type = this.activeTab == 'Buy' ? 0 : 1

            // 计算原始值
            let rawSellAmount = this.activeTab == 'Buy'
                ? this.form.amount
                : this.form.amount

            let factor = Math.pow(10, coinDecimalCount)
            let sellAmount = Math.ceil(rawSellAmount * factor) / factor

            let data = {
                // uid: uni.getStorageSync('uid'),
                coin: this.form.symbol,
                price: this.baseRate,
                orderType: 0,
                type: type,
                // sellAmount: this.form.amount * this.baseRate,
                // payType: this.selectedPay,
                payType: 1,
                fiatType: this.form.coin
            }

            let limitParams = type == 0
                ? {
                    sellCount: this.form.amount,
                    limitMinCount: this.form.limitMin,
                    limitMaxCount: this.form.limitMax,
                }
                : {
                    sellAmount,
                    limitMinAmount: this.form.limitMin,
                    limitMaxAmount: this.form.limitMax,
                }

            let res = await this.$api.c2cShopOrderAdd({
                ...data,
                ...limitParams
            })
            if (res.code == 200) {
                uni.showToast({
                    title: '发布成功',
                    icon: 'none',
                    duration: 2000
                })
                setTimeout(() => {
                    this.$Router.back()
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                })
            }
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .uni-input-placeholder {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 24rpx !important;
    line-height: 32rpx;
    color: rgba(0, 0, 0, .4) !important;
}

.publish-sell-page {
    padding: 32rpx;
    padding-bottom: 140rpx;

    .toggle-container {
        position: relative;
        // width: 500rpx;
        margin: 27rpx 0 0 0;
        // padding-top: 16rpx;
        // width: 100%;
        height: 96rpx;
        // background-color: #F6F6F6;
        border-radius: 80rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6rpx;
        // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370432022240649216.png");
        background-size: 100% 100%;

        // overflow: hidden;
        // filter: blur(13.800000190734863px);
        .rounded-triangle-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .toggle-item {
            width: 50%;
            text-align: center;
            font-size: 28rpx;
            z-index: 1;
            font-family: Gilroy;
            font-weight: 500;
            line-height: 32rpx;
            transition: color 0.3s;
            color: rgba(0, 0, 0, .4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .toggle-item.active {
            color: #fff;
        }

        .toggle-bg {
            height: 80rpx;
            position: absolute;
            // top: -10rpx;
            // border: 1.07px solid #FFFFFF61;
            // bottom: 6rpx;
            width: 328rpx;
            border-radius: 80rpx;
            background-color: #ff6f96;
            // z-index: 0;
            transition: left 0.3s;
            border: 1.07px solid #FFFFFF61;
            display: flex;
            justify-content: center;
            align-items: center;
            // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
        }

        .toggle-bg.Buy {
            left: 6rpx;
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.Sell {
            left: 362rpx; // 500rpx - 50% + padding
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }
    }

    .form-group {
        margin: 48rpx 0 36rpx 0;

        .payway {
            background: #F7F7F7;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24rpx 28rpx;

            .left {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                // color: rgba(0, 0, 0, .4);
            }

            image {
                transform: rotate(90deg);
                width: 36rpx;
                height: 36rpx;
            }
        }

        .label {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 32rpx;
            color: #000;
            margin-bottom: 19rpx;

            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tip {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;
            color: #FF82A3;
        }

        .row2 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10rpx;

            .symbol-value {
                width: 222rpx;
                height: 80rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24rpx;
                color: rgba(0, 0, 0, .4);
                background-color: #f5f5f5;
                border-radius: 8rpx;
                line-height: 32rpx;

                .sym {
                    display: block;
                    line-height: 32rpx;
                    margin-left: 8rpx;
                    color: #000;
                }
            }

            .form-input2 {
                background-color: #f5f5f5;
                border-radius: 8rpx;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24rpx;
                // line-height: 32rpx;
                color: #000;
                width: 222rpx;
            }

            .input {
                background-color: #f5f5f5;
                border-radius: 8rpx;
                position: relative;
                padding: 5rpx 28rpx;

                .symbol-select {
                    position: absolute;
                    top: 24rpx;
                    right: 20rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    // line-height: 32rpx;
                    color: #000;
                }
            }

            .gang {
                margin: 0 -2rpx;
            }
        }

        .row {
            display: flex;
            gap: 16rpx;
            flex-wrap: wrap;
            background-color: #f5f5f5;
            border-radius: 8rpx;
            padding: 5rpx 28rpx;

            .form-input {
                flex: 1;
                font-size: 26rpx;
            }

            .symbol-select {
                // background-color: #f5f5f5;
                // border-radius: 12rpx;
                // padding: 20rpx 24rpx;
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }

        }

        .sub-tip {
            margin-top: 20rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .5);
        }
    }

    .submit {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;

        .footer-note {
            text-align: center;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            margin-bottom: 34rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .5);

        }

        .submit-btn {
            background: #FF82A3;
            border-radius: 112rpx;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            color: #fff;
        }

    }


    .pay-popup {
        padding: 44rpx 32rpx;

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;

            .title {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 32rpx;
                line-height: 48rpx;
                color: #000;
            }

            image {
                width: 48rpx;
                height: 48rpx;
            }
        }

        .pay-item {
            display: flex;
            align-items: center;
            padding: 32rpx;
            border-radius: 24rpx;
            background-color: #fff;
            margin-bottom: 16rpx;
            transition: all 0.25s;
            border: 3rpx solid rgba(0, 0, 0, .1);

            .dot {
                width: 8rpx;
                height: 8rpx;
                border-radius: 50%;
                // background-color: #1c1c1c;
                margin-right: 16rpx;
            }

            .text {
                flex: 1;
                font-family: PingFang HK;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }

            &:active {
                background-color: #f5f5f5;
            }

            &.active {
                border-color: #FF5B99;
                background-color: #fff0f5;
            }
        }
    }

    .popup-wrapper {
        padding: 32rpx;

        .search-input {
            margin-bottom: 24rpx;
        }

        .symbol-list {
            max-height: 500rpx;

            .symbol-item {
                padding: 24rpx;
                font-size: 28rpx;
                border-bottom: 1px solid #f0f0f0;
                color: #333;

                &:active {
                    background-color: #f9f9f9;
                }
            }
        }
    }
}
</style>