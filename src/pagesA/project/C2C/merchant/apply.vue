<template>
    <view class="step-status-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="申请成为商家">
        </u-navbar>
        <view class="step-item" v-for="item in steps" :key="item.key">
            <view class="step-left">
                <!-- <u-icon :name="item.done ? 'checkbox-circle' : 'checkbox-blank-circle-outline'"
                    :color="item.done ? '#FF5B99' : '#ccc'" size="32" /> -->
                <view class="step-info">
                    <image class="check" :style="{ opacity: item.done ? '1' : '.6' }"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372281789908344832.png" />
                    <view class="title">{{ item.title }}</view>
                </view>
                <view class="desc" v-if="item.desc" @click="item.action && item.action()"
                    :style="{ color: item.done ? '#FF5B99' : '#FF5B99' }">
                    {{ item.desc }}
                    
                    <view class="arrow" v-if="!item.done">
                        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372282670426972160.png" />
                    </view>
                </view>
            </view>
        </view>

        <u-button type="primary" class="apply-btn" :disabled="canApply" hover-class="none" @click="submitApply">
            立即申请
        </u-button>
    </view>
</template>

<script>
export default {
    data() {
        return {
            usedCoinPrice: "",
            coinBalance: "",
            usedCoin: "",
            kyclink: "",
            fetching: false,
            NEW_ACCESS_TOKEN: "",
            c2cShopIceInfo: {},
            steps: [
                {
                    key: 'realname',
                    title: '完成实名认证',
                    desc: '',
                    done: null,
                    action: () => {
                        if (this.steps[0].done == true) {
                            return
                        } else {
                            this.startVerification('id-and-liveness')
                        }
                    }
                },
                {
                    key: 'deposit',
                    title: '',
                    desc: 'C2C账户充值',
                    done: null,
                    action: () => {
                        this.nav_to('charge')
                    }
                }
            ]
        }
    },

    onShow() {
        this.getc2cShopIceInfo()
        this.getC2cuserInfo()
    },
    computed: {
        canApply() {
            // 循环steps里面的done,如果都为true，则返回false
            return this.steps.some(s => !s.done);
        }
    },
    methods: {
        async fetchCoinBalance() {
            let res = await this.$api.userAvailableCoinList({
                symbol: this.usedCoin
            })
            if (res.code == 200) {
                // 循环res.result.data,找到里面symbol跟this.usedCoin一样的项，拿到里面的balance
                this.coinBalance = res.result.data.find(item => item.symbol == this.usedCoin).balance
                console.log(this.coinBalance);
                if (Number(this.coinBalance) < Number(this.usedCoinAmount)) {
                    this.steps[1].desc = 'C2C账户充值'
                } else {
                    this.steps[1].desc = '已满足'
                    this.steps[1].done = true

                }
            }
        },
        async startVerification(e) {
            await this.fetchNEW_ACCESS_TOKEN(e)
            if (this.fetching) {
                this.kyclink = '/static/kyc.html'
                this.$Router.push({
                    name: 'webView',
                    params: {
                        url: this.kyclink,
                        NEW_ACCESS_TOKEN: this.NEW_ACCESS_TOKEN
                    }
                })
            }

        },
        async fetchNEW_ACCESS_TOKEN(e) {
            this.fetching = false
            uni.showLoading();
            let res = await this.$api.getAccToken({
                levelName: e,
                uid: uni.getStorageSync('uid'),
            });
            if (res.code != 200) {
                this.$u.toast(res.msg);
                return
            }
            if (res.code == 200) {
                this.fetching = true
                uni.hideLoading();
                const parsedData = JSON.parse(res.result);
                const token = parsedData.token;
                this.NEW_ACCESS_TOKEN = token
            } else {

            }
        },
        async getC2cuserInfo() {
            let res = await this.$api.c2cuserInfo({});
            if (res.code == 200) {
                this.steps[0].done = res.result.realNameStatus == 1
                this.steps[0].desc = res.result.realNameStatus == 1 ? '已完成' : '未完成'
            }
        },
        async getc2cShopIceInfo() {
            let res = await this.$api.c2cShopIceInfo({});
            if (res.code == 200) {
                this.usedCoin = res.result.coin
                this.usedCoinPrice = res.result.iceAmount
                // this.c2cShopIceInfo = res.result;
                this.steps[1].title = `冻结保证金 ${res.result.iceAmount} ${res.result.coin}`
                this.fetchCoinBalance()

            }
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        submitApply() {
            // this.$Router.push({
            //     name: 'merchantForm'
            // })
            this.bind();
            if (!this.canApply) return;
            uni.showToast({ title: '已申请', icon: 'success' });
        },
        async bind() {
            let res = await this.$api.c2cShopApply({});
            if (res.code == 200) {
                uni.showToast({
                    title: '申请成功',
                    icon: 'none'
                });
                setTimeout(() => {
                    uni.navigateBack();
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.step-status-page {
    padding: 32rpx;
    padding-bottom: 140rpx;

    .step-item {
        border-bottom: 1rpx solid rgba(0, 0, 0, .1);
        padding: 40rpx 0;

        .step-left {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .step-info {
                // margin-left: 20rpx;
                display: flex;
                align-items: center;

                .check {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 16rpx;
                }

                .title {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #000;
                }




            }

            .desc {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #FF82A3;
                margin-top: 16rpx;
                display: flex;
                align-items: center;

                .arrow {
                    width: 30rpx;
                    height: 30rpx;

                    image {
                        width: 30rpx;
                        height: 30rpx;
                    }

                    margin-left: 4rpx;
                }
            }
        }
    }

    .apply-btn {
        background: #FF82A3 !important;
        color: #fff;
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        border-radius: 50rpx;
        font-size: 28rpx;
    }


    // 禁用状态样式
    .apply-btn[disabled] {
        background: #FF82A3 !important; // 加透明度效果
        color: #fff;
        opacity: 0.6;
    }
}
</style>