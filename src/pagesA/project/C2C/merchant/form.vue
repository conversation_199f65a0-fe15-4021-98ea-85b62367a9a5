<template>
    <view class="verify-form-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="申请成为商家">
        </u-navbar>

        <view class="form-group" v-for="item in formItems" :key="item.key">
            <view class="label">{{ item.label }}</view>
            <view v-if="item.type === 'input'">
                <view class="bg">
                    <u-input height="80" v-model="form[item.key]" :placeholder="item.placeholder" class="form-input" />
                </view>
            </view>

            <view v-else-if="item.type == 'select-date'">
                <!-- <u-picker mode="date" :value="form.birthday" start="1900-01-01" end="2050-12-31" @change="onDateChange"> -->
                <view class="select-input" @click="showPicker = true">
                    <text class="value" :style="getTextStyle(form[item.key])">{{ form.birthday || '请选择出生日期'
                        }}</text>
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372249936161759232.png" />
                </view>
                <!-- </u-picker> -->


            </view>

            <view v-else class="select-input" @click="handleSelect(item.key)">
                <text class="value" :style="getTextStyle(form[item.key])">
                    {{ form[item.key] || item.placeholder }}
                </text>
                <!-- <u-icon name="arrow-right" size="28" color="#ccc" /> -->
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1372249936161759232.png" />
            </view>

        </view>

        <u-button type="primary" hover-class="none" class="submit-btn" color="#FF82A3" :disabled="!isValid"
            @click="submit">
            提交
        </u-button>
        <!-- v-model="form.birthday" -->
        <u-picker mode="time" v-model="showPicker" confirm-color="#FF82A3" confirm-text="确定" cancel-text="取消"
            @confirm="onConfirm" @cancel="showPicker = false" :safe-area-inset-bottom="true" closeOnClickOverlay />
    </view>
</template>

<script>
export default {
    data() {
        return {
            showPicker: false,

            form: {
                country: '',
                firstName: '',
                lastName: '',
                docType: '身份证',
                docNumber: '',
                birthday: ''
            },
            formItems: [
                { label: '国家/地区', key: 'nation', type: 'select', placeholder: '选择国家或地区' },
                { label: '名字', key: 'firstName', type: 'input', placeholder: '请输入名字' },
                { label: '姓氏', key: 'lastName', type: 'input', placeholder: '请输入姓氏' },
                { label: '证件类型', key: 'license', type: 'select', placeholder: '选择证件类型' },
                { label: '证件号码', key: 'docNumber', type: 'input', placeholder: '请输入证件号码' },
                { label: '出生日期', key: 'birthday', type: 'select-date', placeholder: '请输入出生日期' }
            ]
        }
    },
    computed: {
        isValid() {
            return (
                this.form.country &&
                this.form.firstName &&
                this.form.lastName &&
                this.form.docType &&
                this.form.docNumber &&
                this.form.birthday
            );
        }
    },
    methods: {
        onConfirm(val) {
            console.log(val);

            this.form.birthday = val.year + '-' + val.month + '-' + val.day;
            this.showPicker = false;
        },
        getTextStyle(value) {
            return {
                color: value ? '#000' : 'rgba(0, 0, 0, 0.5)'
            }
        },
        handleSelect(key) {
            if (key == 'license' || key == 'nation') {
                this.$Router.push({
                    name: key
                })
            }
            uni.showToast({
                title: `选择 ${key}`,
                icon: 'none'
            });
            // 示例：可用 uni.showActionSheet / 日期选择器 / 国家列表弹窗
        },
        submit() {
            if (!this.isValid) return;
            uni.showToast({ title: '提交成功', icon: 'success' });
        },
        onDateChange(e) {
            this.form.birthday = e.detail.value; // 格式为 yyyy-mm-dd
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .uni-input-placeholder {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 32rpx;
    color: rgba(0, 0, 0, 0.5) !important
}

::v-deep .u-btn-picker {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 28rpx !important;

}

::v-deep .u-datetime-picker {
    border-radius: 16rpx !important;
    border-top-left-radius: 16rpx !important;
    border-top-right-radius: 16rpx !important;
}

::v-deep .u-drawer-content {
    border-top-left-radius: 16rpx !important;
    border-top-right-radius: 16rpx !important;
}

::v-deep .u-picker-header {
    padding: 0 16rpx;
    background: transparent;
    overflow: hidden;
}

::v-deep .u-picker-body {
    background-color: #FFFFFF !important;
}

::v-deep .uni-picker-view-group {
    background-color: #FFFFFF !important;
}

.verify-form-page {
    padding: 32rpx;
    padding-bottom: 120rpx;


    .form-group {
        margin: 48rpx 0 32rpx 0;

        .label {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 32rpx;
            margin-bottom: 20rpx;
            color: #000;
        }

        .bg {
            background-color: #f5f5f5;
            border-radius: 12rpx;
            padding: 0 28rpx;


            .form-input {

                font-size: 28rpx;

            }
        }



        .select-input {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #F7F7F7;
            border-radius: 8rpx;
            padding: 24rpx 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;

            image {
                width: 36rpx;
                height: 36rpx;
            }


        }
    }

    .submit-btn {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        border-radius: 50rpx;
        font-size: 28rpx;
        font-weight: bold;
        background: #FF82A3 !important; // 加透明度效果

    }

    // 禁用状态样式
    .submit-btn[disabled] {
        background: #FF82A3 !important; // 加透明度效果
        color: #fff;
        opacity: 0.6;
    }
}
</style>