<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="选择币种">
        </u-navbar>
        <view class="search-box-n">
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374823362269372416.png" />
            <input v-model="searchKeyword" placeholder="搜索" class="search-input" @input="onSearch" />
        </view>

        <view class="coin-list">
            <view class="coin-item" v-for="coin in filteredCoins" :key="coin.name" @click="selectCoin(coin)">
                <image :src="coin.coinUrl" class="coin-icon" />
                <text class="coin-name">{{ coin.coin }}</text>
                <!-- <text class="arrow">></text> -->
                <image class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374825043983949824.png" />
            </view>
            <nodata v-if="filteredCoins.length === 0" />
        </view>
    </view>
</template>

<script>
import nodata from "./components/nodata"
export default {
    components: {
        nodata
    },
    data() {
        return {
            searchKeyword: '',
            coins: [
                // { name: 'BTC', icon: 'https://cryptoicon-api.vercel.app/api/icon/btc' },
                // { name: 'ETH', icon: 'https://cryptoicon-api.vercel.app/api/icon/eth' },
                // { name: 'USDT', icon: 'https://cryptoicon-api.vercel.app/api/icon/usdt' },
                // { name: 'BNB', icon: 'https://cryptoicon-api.vercel.app/api/icon/bnb' },
            ],
        }
    },
    onLoad() {
        this.getCoin()
    },
    computed: {
        filteredCoins() {
            if (!this.searchKeyword) {
                return this.coins
            }
            const keyword = this.searchKeyword.trim().toLowerCase()
            return this.coins.filter(coin =>
                coin.coin.toLowerCase().includes(keyword)
            )
        }
    },
    methods: {
        onSearch() {
            const keyword = this.searchKeyword.trim().toLowerCase()
            this.filteredCoins = this.coins.filter(coin =>
                coin.coin.toLowerCase().includes(keyword)
            )
        },
        selectCoin(coin) {
            // console.log('Selected:', coin.coin)
            this.$Router.push({
                name: 'Main',
                params: {
                    coin: encodeURIComponent(JSON.stringify(coin))
                }
            })
        },
        async getCoin() {
            let res = await this.$api.c2csupportCoin()
            if (res.code == 200) {
                this.coins = res.result
            }
        },
    }
}
</script>

<style scoped lang="scss">
::v-deep .uni-input-placeholder {
    font-family: PingFang HK;
    font-weight: 400;
    font-size: 28rpx;
    letter-spacing: -0.43px;
    color: #000;
    opacity: .3;
}

.container {
    background: #fff;
    height: 100vh;
}

.search-box-n {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
    padding-top: 40rpx;
    position: relative;

    image {
        position: absolute;
        top: 50rpx;
        left: 44rpx;
        width: 52rpx;
        height: 52rpx;
    }
}

.search-input {
    text-indent: 76rpx;
    border: none;
    outline: none;
    // width: 692rpx;
    width: 100%;
    margin: 0 32rpx;
    height: 38*2rpx;
    background: rgba(217, 217, 217, .2);
    border-radius: 34rpx;

}

.coin-list {
    padding: 0 32rpx;
    display: flex;
    flex-direction: column;
}

.coin-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
}

.coin-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 18rpx;
    border-radius: 50%;
}

.coin-name {
    flex: 1;
    font-family: PingFang HK;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #000;
}

.arrow {

    width: 36rpx;
    height: 36rpx;
}
</style>