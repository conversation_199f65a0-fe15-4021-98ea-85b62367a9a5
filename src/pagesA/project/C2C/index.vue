<template>
    <view class="c2c-page">
        <!-- 顶部导航 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="C2C交易">
        </u-navbar>
        <!-- Tab切换 -->
        <!-- <view class="tab-bar">
            <view :class="['tab', currentTab === 'buy' ? 'active' : '']" @click="switchTab('buy')">购买</view>
            <view :class="['tab', currentTab === 'sell' ? 'active' : '']" @click="switchTab('sell')">出售</view>
        </view> -->

        <view style="height: 16rpx;"></view>
        <view class="toggle-container">
            <!-- {{ activeTab }} -->
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab == 'Buy' }" @tap="switchTab('Buy')">
                Buy
            </view>
            <view class="toggle-item" :class="{ active: activeTab == 'Sell' }" @tap="switchTab('Sell')">
                Sell
            </view>
        </view>

        <!-- 币种选择 -->
        <view class="coin-select" @click="nav_to('C2Ccoin')" v-if="!nowCoin.coin">
            <image class="icon" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1371561853963558912.png" />
            <text>USDT</text>
            <image class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1371562068481236992.png" />
        </view>
        <view class="coin-select" @click="nav_to('C2Ccoin')" v-else>
            <image class="icon" :src="nowCoin.coinUrl" />
            <text>{{ nowCoin.coin }}</text>
            <image class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1371562068481236992.png" />
        </view>
        <!-- 列表 -->
        <scroll-view scroll-y>
            <!-- v-if="activeTab == 'Buy'" -->
            <view class="order-list">
                <view class="order-card" v-for="(item, index) in list" :key="index">
                    <view class="top">
                        <view class="user">
                            <view class="bg">
                                <view class="online flex_all" v-if="item.onLine == 1">
                                    <view class="dot"></view>
                                </view>
                            </view>
                            <text>{{ item.shopName }}</text>
                        </view>
                        <text class="rate">成交量{{ item.dealCount +'/' +  (item.dealRate * 100).toFixed(2) + '%' }}</text>
                    </view>
                    <view class="amount">{{ getCurrencySymbol(item.fait) }}{{ item.price }}</view>
                    <view class="limit">
                        <text class="num">数量 {{ item.countNum }} {{ item.coin }}</text>
                        <text class="lim">限额 {{ item.minAmount + '-' + item.maxAmount }}</text>
                    </view>
                    <view class="pay">
                        <view class="pay-way">
                            <view class="bg"></view>
                            <text>{{ item.payType == 1 ? '微信支付' : item.payType == 2 ? '支付宝支付' : "银行卡支付" }}</text>
                        </view>
                        <u-button hover-class="none" class="buy-btn" @click="buyNow(item)">{{ activeTab == 'Buy' ? '购买'
                            : '卖出' }}</u-button>
                    </view>
                </view>
                <nodata v-if="list.length == 0" />
            </view>
            <!-- <view class="order-list" v-if="activeTab == 'Sell'">
                <nodata />
            </view> -->
        </scroll-view>

        <!-- <div class="element"></div> -->

        <!-- 底部导航 -->
        <!-- <view class="bottom-tab">
            <view class="tab-item active">
                <image :src="iconMap.c2c" />
                <text>C2C</text>
            </view>
            <view class="tab-item">
                <image :src="iconMap.order" />
                <text>订单</text>
            </view>
            <view class="tab-item">
                <image :src="iconMap.me" />
                <text>我的</text>
            </view>
        </view> -->
        <!-- <tabbar :current="1" /> -->
    </view>
</template>

<script>
import tabbar from "./components/tabbar.vue"
import nodata from "./components/nodata.vue"
import { getCurrencySymbol } from "@/utils/utils.js"
export default {
    props: ['nowCoins'],
    components: {
        tabbar,
        nodata
    },
    data() {
        return {
            activeTab: 'Buy',
            currentTab: 'buy',
            list: [
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' },
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' },
                // { user: '2985506228', price: 6.85, amount: 6.85, limit: '6.85–68.50', rate: '0%' }
            ],
            iconMap: {
                usdt: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
                arrowDown: 'https://cdn-icons-png.flaticon.com/512/60/60995.png',
                c2c: 'https://cdn-icons-png.flaticon.com/512/857/857681.png',
                order: 'https://cdn-icons-png.flaticon.com/512/1828/1828911.png',
                me: 'https://cdn-icons-png.flaticon.com/512/847/847969.png'
            },
            page: {
                pageNum: 1,
                pageSize: 10
            },
            nowCoin: {
                coin: ""
            },
            hasNext: false
        };
    },
    onReachBottom() {
        if (this.hasNext) {
            this.page.pageNum += 1
            this.getc2corderlist()
        }
    },
    mounted(option) {
        if (this.nowCoins) {
            this.nowCoin = this.nowCoins
        }
        this.getshopOrderList()
    },
    methods: {
        getCurrencySymbol,
        async getshopOrderList() {
            let res = await this.$api.shopOrderList({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                type: this.activeTab == 'Buy' ? 1 : 0,
                coin: this.nowCoin?.coin || 'USDT'
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.page.pageNum == 1) {
                    if (res.result.data) {
                        this.list = res.result.data
                    }
                } else {
                    this.list = this.list.concat(res.result.data)
                }
            } else {
                this.$toast(res.msg)
            }
        },
        switchTab(tab) {
            this.list = []
            this.page.pageNum = 1
            this.activeTab = tab

            this.getshopOrderList()

        },
        buyNow(item) {
            if (this.activeTab == 'Buy') {
                this.$Router.push({
                    name: 'C2Cbuy',
                    params: {
                        item: encodeURIComponent(JSON.stringify(item))
                    }
                })
            } else {
                this.$Router.push({
                    name: 'C2Csell',
                    params: {
                        item: encodeURIComponent(JSON.stringify(item))
                    }
                })
            }

            // uni.showToast({ title: `购买 ${item.price} USDT`, icon: 'none' });
        },
        goBack() {
            uni.navigateBack();
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        }
    }
};
</script>

<style lang="scss" scoped>
.c2c-page {
    height: 100%;
    width: 100%;

    .element {
        position: relative;
        width: 300px;
        height: 200px;
        background: #f0e6f6;
        /* 淡紫色背景 */
        margin: 50px auto;
    }

    .element::before,
    .element::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 70px;
        /* 进一步增加高度 */
        background: #fff;
        /* 假设外部背景为白色 */
        left: 0;
    }

    .element::before {
        top: -35px;
        /* 增加偏移量 */
        border-bottom-left-radius: 150px 70px;
        /* 匹配高度 */
        border-bottom-right-radius: 150px 70px;
    }

    .element::after {
        bottom: -35px;
        /* 增加偏移量 */
        border-top-left-radius: 150px 70px;
        border-top-right-radius: 150px 70px;
    }

    .toggle-container {
        position: relative;
        // width: 500rpx;
        margin: 27rpx 28rpx 0 28rpx;
        // padding-top: 16rpx;
        // width: 100%;
        height: 96rpx;
        // background-color: #F6F6F6;
        border-radius: 80rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6rpx;
        // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370432022240649216.png");
        background-size: 100% 100%;

        // overflow: hidden;
        // filter: blur(13.800000190734863px);
        .rounded-triangle-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .toggle-item {
            width: 50%;
            text-align: center;
            font-size: 28rpx;
            z-index: 1;
            font-family: Gilroy;
            font-weight: 500;
            line-height: 32rpx;
            transition: color 0.3s;
            color: rgba(0, 0, 0, .4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .toggle-item.active {
            color: #fff;
        }

        .toggle-bg {
            height: 80rpx;
            position: absolute;
            // top: -10rpx;
            // border: 1.07px solid #FFFFFF61;
            // bottom: 6rpx;
            width: 328rpx;
            border-radius: 80rpx;
            background-color: #ff6f96;
            // z-index: 0;
            transition: left 0.3s;
            border: 1.07px solid #FFFFFF61;
            display: flex;
            justify-content: center;
            align-items: center;
            // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
        }

        .toggle-bg.Buy {
            left: 6rpx;
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.Sell {
            left: 362rpx; // 500rpx - 50% + padding
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }
    }


    .tab-bar {
        display: flex;
        justify-content: space-around;
        margin: 20rpx 30rpx;
        background-color: #f0f0f0;
        border-radius: 60rpx;
        padding: 6rpx;

        .tab {
            flex: 1;
            text-align: center;
            padding: 20rpx 0;
            border-radius: 50rpx;
            font-size: 28rpx;
            transition: all 0.3s;

            &.active {
                background-color: #ff7fa6;
                color: #fff;
                box-shadow: 0 6rpx 12rpx rgba(255, 127, 166, 0.4);
            }
        }
    }

    .coin-select {
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 0 30rpx;
        // font-size: 28rpx;
        margin: 12rpx 0 0 28rpx;
        padding: 8rpx;
        width: 204rpx;
        height: 72rpx;
        border-radius: 54rpx;
        // filter: blur(13.800000190734863px);
        background: #F6F6F6;

        text {
            margin: 0 8rpx 0 16rpx;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;

        }

        .icon {
            width: 54rpx;
            height: 54rpx;
            // margin-right: 16rpx;
        }

        .arrow {
            width: 32rpx;
            height: 32rpx;
            // margin-left: 6rpx;
        }
    }

    .order-list {
        margin: 0 34rpx 0 28rpx;
        overflow: hidden;
        padding-bottom: 160rpx;

        .order-card {
            padding-bottom: 32rpx;
            border-bottom: 1rpx solid rgba(0, 0, 0, .1);
            margin-top: 32rpx;

            &:first-child {
                margin-top: 28rpx;
            }

            .top {
                display: flex;
                justify-content: space-between;
                margin-bottom: 14rpx;

                .user {
                    display: flex;
                    align-items: center;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .5);

                    .bg {
                        margin-right: 8rpx;
                        border-radius: 50%;
                        width: 28rpx;
                        height: 28rpx;
                        opacity: 0.2;
                        background: #000;
                        position: relative;

                        .online {
                            position: absolute;
                            bottom: 2rpx;
                            right: 0rpx;
                            background: #fff;
                            border-radius: 50%;
                            width: 12rpx;
                            height: 12rpx;

                            .dot {
                                background: #2EE16E;
                                border-radius: 50%;
                                width: 8rpx;
                                height: 8rpx;
                            }
                        }
                    }
                }


                .rate {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .6);
                }
            }

            .amount {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
                margin-bottom: 14rpx;
            }

            .limit {
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                .num,
                .lim {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .4);
                }
            }

            .pay {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;

                .pay-way {
                    display: flex;
                    align-items: center;

                    .bg {
                        margin-right: 8rpx;
                        width: 8rpx;
                        height: 8rpx;
                        border-radius: 50%;
                        background: rgba(73, 160, 177, 1);
                    }

                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .6);
                }

                .buy-btn {
                    background: rgba(255, 130, 163, 1);
                    width: 108rpx;
                    height: 58rpx;
                    border-radius: 12rpx;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #fff;
                    margin: 0;
                }
            }


        }
    }

    .bottom-tab {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 120rpx;
        display: flex;
        background: #fff;
        border-top-left-radius: 60rpx;
        border-top-right-radius: 60rpx;
        box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.06);
        z-index: 10;

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 20rpx 0;
            font-size: 24rpx;
            color: #999;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-bottom: 4rpx;
            }

            &.active {
                color: #ff7fa6;
                font-weight: bold;
            }
        }
    }
}
</style>