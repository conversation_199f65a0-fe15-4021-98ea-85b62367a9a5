<template>

    <view class="main">
        <u-navbar class="list-nav" title="邀请好友" title-color="#fff" back-icon-color="#fff" :border-bottom="false"
            back-icon-name="https://cdn-lingjing.nftcn.com.cn/image/20220613/b81fa4236e814e3b7cfeb8c8698568d3_48x48.png"
            :background="{ backgroundColor: 'trasparent' }">
        </u-navbar>

        <view class="inviteBox">
            <view class="top">
                <image :src="shareUser.avatar" class="topinfo" mode="aspectFill"></image>
            </view>
            <view class="nickname">
                <text class="name">{{ shareUser.nickname || 'Bigverse' }}</text>
                <text class="code">邀请码：<text style="font-weight: bold;">{{ shareUser.invitationCode || '--'
                        }}</text></text>

            </view>

            <view class="qrcode">
                <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="204rpx"
                    :options="options"></uv-qrcode>
            </view>

            <view class="tips">扫描二维码,立刻开杠!</view>
        </view>


        <view style="margin: 0 55rpx;
        ">
            <scroll-view :scroll-y="true" @scrolltolower="bottomOut()" ref="scrollContainer" class="list-box">
                <view class="header">
                    <text class="title">邀请记录</text>
                    <text class="invited-count">已邀请 <span>{{ list.length || 0 }}人</span></text>
                </view>
                <view class="list">
                    <view class="list-item" v-for="(item, index) in list" :key="index">
                        <view style="display: flex;align-items: center;">
                            <!-- <view class="avatar"> -->
                            <image :src="item.avatar" mode="aspectFill" class="avatar"></image>
                            <!-- </view> -->
                            <view class="info">
                                <text class="name">{{ item.conAdd }}</text>
                                <text class="time" v-if="item.inviteDate">{{ item.inviteDate.slice(0, 16) }}</text>
                            </view>
                        </view>
                        <!-- 已实名不显示 -->
                        <view class="status" :class="statusClassReal(item)">
                            {{ item.isRealName && item.isOpen ? '已开仓' : item.isRealName && !item.isOpen ? '未开仓' :
                                !item.isRealName && !item.isOpen ? '未实名' : item.isRealName && !item.isOpen ? '已实名' : '' }}
                        </view>
                        <!-- <view class="status" :class="statusClassopen(item.isOpen)">
                            {{ item.isOpen ? '已开仓' : '未开仓' }}
                        </view> -->

                    </view>
                </view>

                <view class="nodata" v-if="!list.length">
                    <image mode="widthFix"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240807/657a04e8b91b68a710c25c8308e6ae85_480x480.png" />
                    <text>暂时无数据</text>
                </view>
            </scroll-view>
        </view>
        <view class="bottom-buttons">
            <view class="btn gradient-border" @click="save">
                <view>下载图片</view>
            </view>
            <button class="btn gradient-fill" @click="copylink">保存链接</button>
        </view>


        <u-popup v-model="inviteShow" mode="center">
            <view class="share" id="mainbody">

                <view class="share-box-top">
                    <view class="left">
                        <image :src="shareUser.avatar" mode="aspectFill" class="avatar"></image>
                    </view>
                    <view class="right">
                        <view style="display: flex;align-items: center;">
                            <text> {{ shareUser.nickname }}</text>
                            <text>邀请你</text>
                        </view>
                        <text>一起开杠吧！</text>
                    </view>
                </view>

                <view class="qrcode">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="204rpx"
                        :options="options"></uv-qrcode>
                </view>

                <view class="tips">扫描二维码,立刻开杠!</view>
                <view class="invitecode">邀请码：<text style="font-weight: bold;">{{
                    shareUser.invitationCode }}</text>
                </view>


            </view>
            <view class="share_to_div">
                <view class="li" @click="canvas.onClick">
                    <view class="icon_image">
                        <image
                            src="https://cdn-lingjing.nftcn.com.cn/image/20240221/8009764422266b1d25da7da177e19d90_80x80.png"
                            alt="" srcset="" mode="widthFix"></image>
                    </view>
                    <p>保存海报</p>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    name: "inviteYs",
    data() {
        return {
            inviteShow: false,
            pageNum: 1,
            list: [
            ],
            inviteYs: [],
            qrcodeUrl: "",
            shareUser: {},
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
        }
    },
    onLoad() {
        this.getUserShare()
        this.fetchInviteList()
    },
    methods: {
        save() {
            this.inviteShow = true
            setTimeout(() => {
                this.canvas.onClick()
            }, 1000);
        },
        getUrl(option) {
            console.log(option)
            uni.hideLoading()
            // this.postImg = option.base64
            // #ifdef APP
            this.saveHeadImgFile(option.base64)
            // #endif
            // #ifdef H5
            this.saveImage(option.base64)
            // #endif

        },
        saveImage(url) {
            // #ifdef H5
            let image = new Image()
            image.setAttribute("crossOrigin", 'Anonymous')
            image.src = url
            image.onload = function () {
                let canvas = document.createElement('canvas')
                canvas.width = image.width
                canvas.height = image.height
                let context = canvas.getContext('2d')
                context.drawImage(image, 0, 0, image.width, image.height)
                let url = canvas.toDataURL('image/png')
                let a = document.createElement('a')
                let event = new MouseEvent('click')
                a.download = '分享好友'
                a.href = url
                a.dispatchEvent(event)
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                })
            }
            // #endif
            // #ifdef APP-PLUS
            uni.saveImageToPhotosAlbum({
                filePath: url,
                success: () => {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success'
                    })
                },
                fail: () => {
                    uni.showToast({
                        title: '保存失败',
                        icon: 'none'
                    })
                }
            })
            // #endif
        },
        saveHeadImgFile(base64) {
            const bitmap = new plus.nativeObj.Bitmap("test");
            bitmap.loadBase64Data(base64, function () {
                const url = "_doc/" + new Date().getTime() + ".png"; // url为时间戳命名方式
                console.log('saveHeadImgFile', url)
                bitmap.save(url, {
                    overwrite: true, // 是否覆盖
                    // quality: 'quality'  // 图片清晰度
                }, (i) => {
                    uni.saveImageToPhotosAlbum({
                        filePath: url,
                        success: function () {
                            uni.showToast({
                                title: '图片保存成功',
                                icon: 'none'
                            })
                            bitmap.clear()
                        }
                    });
                }, (e) => {
                    uni.showToast({
                        title: '图片保存失败',
                        icon: 'none'
                    })
                    bitmap.clear()
                });
            }, (e) => {
                uni.showToast({
                    title: '图片保存失败',
                    icon: 'none'
                })
                bitmap.clear()
            });
        },
        //触底加载数据
        bottomOut() {
            if (this.hasNext && this.list.length > 0) {
                this.pageNum += 1; //请求页数+1
                this.fetchInviteList(); //调用数据请求
            } else {
                uni.showToast({
                    title: '没有更多数据了',
                    icon: 'none',
                    duration: 2000

                })
            }
        },
        copylink() {
            uni.setClipboardData({
                data: this.qrcodeUrl,
                success: () => {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'success'
                    })
                }
            })
        },
        async fetchInviteList() {
            let res = await this.$api.getInviteList({
                pageNum: this.pageNum,
                pageSize: 5,
            });
            if (res.status.code == 0) {
                this.hasNext = res.result.hasNext

                if (this.pageNum == 1) {
                    this.list = res.result.list
                } else {
                    this.list = this.list.concat(res.result.list)
                }
            }
        },
        statusClassopen(status) {
            switch (status) {
                case true:
                    return "opened";
                case false:
                    return "not-opened";
                default:
                    return "";
            }
        },
        statusClassReal(item) {
            if (item.isRealName && item.isOpen) {
                return 'opened'
            } else if (item.isRealName && !item.isOpen) {
                return 'not-opened'
            } else if (!item.isRealName && !item.isOpen) {
                return "unverified";
            } else if (item.isRealName && !item.isOpen) {
                return "verified";
            } else {
                return 'opened'
            }

            // switch (status) {
            //     case true:
            //         return "verified";
            //     case false:
            //         return "unverified";
            //     default:
            //         return "";
            // }
        },
        async getUserShare() {
            let res = await this.$api.GetExchangeUserInfo({

            });
            console.log(res, '321');
            if (res.status.code == 0) {
                this.shareUser = res.result
                this.qrcodeUrl = `${getApp().globalData.url}pages/project/login/register?code=${this.shareUser.invitationCode}`

            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
    },
}
</script>

<script module="canvas" lang="renderjs">
	import html2canvas from 'html2canvas'
	export default {
		mounted() {
        },
		methods: {
			// 生成图片需要调用的方法
			generateImage(ownerInstance) {
                console.log(ownerInstance,'参数');
                
				let dom = document.getElementById('mainbody') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;

				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
                    console.log('到这里');
                    
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			generateImageOld(ownerInstance) {
				let dom = document.getElementById('inviteOld') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;
			
				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				console.log(event, ownerInstance,'触发了')
				this.generateImage(ownerInstance)
				// ownerInstance.callMethod('onViewClick', {
				// 	test: 'test'
				// })
			}
		},
	}
</script>

<style lang="scss" scoped>
.share_to_div {
    z-index: 900000;
    margin-top: 60rpx;
    display: flex;
    justify-content: center;

    >.li {
        width: 25%;
        text-align: center;
        color: #fff;
        font-size: 28rpx;
        margin-top: 10rpx;

        .icon_image {
            display: flex;
            justify-content: center;

            image {
                width: 90rpx;
                margin-bottom: 20rpx;
            }
        }
    }
}

.share {
    width: 639rpx;
    height: 881rpx;
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241227/4c586ae4abb219240feed2266e5c1782_1278x1762.png");
    background-size: 100% 100%;

    .invitecode {
        margin: 0 auto;
        margin-top: 41rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #000000;
        ;
        width: 395rpx;
        height: 62rpx;
        background: #FFFFFF;
        border-radius: 31rpx;
    }

    .tips {
        text-align: center;
        margin-top: 26rpx;
        font-family: HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #63EAEE;
    }

    .qrcode {
        margin: 0 auto;
        margin-top: 36rpx;
        background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241227/3a260572cfc43a7cb68964066612191e_540x576.png");
        background-size: 100% 100%;
        width: 270rpx;
        height: 288rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .share-box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 119rpx 0 45rpx 78rpx;

        .left {
            width: 166rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 166rpx;
            background: linear-gradient(90deg, #EF91FB, #40F8EC);
            border-radius: 50%;

            image {
                width: 161rpx;
                height: 161rpx;
                border-radius: 50%;
            }
        }

        .right {
            width: 600rpx;

            margin-left: 61rpx;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            font-family: HarmonyOS Sans SC;
            font-weight: 500;
            gap: 25rpx;
            font-size: 28rpx;
            color: #F8F8F7;

        }
    }

}

.main {
    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241227/c2ff410d6ea943215ec8a28df7365fe9_1500x3248.png");
    background-size: 100% 100%;
    height: 100%;
    width: 100%;
    padding-bottom: 100rpx;

    .list-box {
        padding: 50rpx 0rpx 50rpx 0rpx;

        overflow-y: auto;
        height: 670rpx;
        background: #2B2B2B;
        border-radius: 35rpx;
        // margin: 37rpx 55rpx 0 55rpx;
        margin-top: 37rpx;
        color: #fff;
        font-size: 28rpx;

        .header {
            padding: 0 30rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .title {
                font-size: 16px;
                color: #63EAEE;
            }

            .invited-count {
                font-family: HarmonyOS Sans SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;

                span {
                    margin-left: 10rpx;
                    color: #63EAEE;
                }
            }
        }

        .list {
            padding: 0 30rpx;

            .list-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20rpx 0;

                .avatar {
                    width: 81rpx;
                    height: 81rpx;
                    background-color: #000;
                    border-radius: 50%;
                    margin-right: 19rpx;
                }

                .info {
                    display: flex;
                    flex-direction: column;

                    .name {
                        font-size: 24rpx;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        color: #fff;
                    }

                    .time {
                        color: rgba(255, 255, 255, .5);
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        margin-top: 21rpx;
                    }
                }

                .status {
                    padding: 4px 12px;
                    border-radius: 16px;
                    font-size: 12px;
                    text-align: center;
                    white-space: nowrap;

                    &.verified {
                        background-color: #63EAEE;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #000000;
                    }

                    &.unverified {
                        background: #555555;
                        border-radius: 21rpx;
                        border: 1rpx solid #63EAEE;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #63EAEE;
                    }

                    &.opened {
                        width: 109rpx;
                        height: 41rpx;
                        background: #FFFFFF;
                        border-radius: 21rpx;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #000000;
                    }

                    &.not-opened {
                        background-color: #333;
                        color: #b3b3b3;

                        background: #555555;
                        border-radius: 21rpx;
                        border: 1rpx solid #FFFFFF;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: rgba(255, 255, 255, .5);
                    }
                }
            }
        }
    }


    .inviteBox {
        margin: 226rpx 55rpx 0 55rpx;
        height: 618rpx;
        background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241227/45132c677e9f85ceb1b6c83f3b7e8856_1278x1236.png");
        background-size: 100% 100%;
        position: relative;

        .tips {
            text-align: center;
            margin-top: 26rpx;
            font-family: HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #63EAEE;
        }

        .qrcode {
            margin: 0 auto;
            margin-top: 36rpx;
            background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241227/3a260572cfc43a7cb68964066612191e_540x576.png");
            background-size: 100% 100%;
            width: 270rpx;
            height: 288rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nickname {
            padding-top: 111rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .name {
                font-family: HarmonyOS Sans SC;
                font-weight: 500;
                font-size: 28rpx;
                color: #F8F8F7;
            }

            .code {
                margin-top: 25rpx;
                font-family: HarmonyOS Sans SC;
                font-weight: 500;
                font-size: 28rpx;
                color: #F8F8F7;
            }
        }

        .top {
            position: absolute;
            top: -82rpx;
            left: 238rpx;
            width: 166rpx;
            height: 166rpx;
            background: linear-gradient(90deg, #EF91FB, #40F8EC);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .topinfo {
                width: 161rpx;
                height: 161rpx;
                border-radius: 50%;
            }
        }
    }

    .bottom-buttons {
        position: fixed;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 16px;
        z-index: 10;

        .btn {
            width: 297rpx;
            height: 70rpx;
            font-size: 14px;
            text-align: center;

            &.gradient-border {
                border-radius: 35rpx;
                background: linear-gradient(90deg, #EF91FB, #40F8EC);
                display: flex;
                justify-content: center;
                align-items: center;

                >view {
                    width: 292rpx;
                    height: 66rpx;
                    background: #36343F;
                    color: #FFFFFF;
                    display: flex;
                    justify-content: center;
                    border-radius: 35rpx;

                    align-items: center;
                    font-weight: 400;
                    font-size: 28rpx;
                }
            }

            &.gradient-fill {
                background: linear-gradient(90deg, #EF91FB, #40F8EC);
                border-radius: 35rpx;
                font-weight: 400;
                font-size: 28rpx;
                font-family: HarmonyOS Sans SC;
                color: #000000;
            }
        }
    }

}

::v-deep .u-navbar-fixed {
    top: 95rpx !important;
}

.nodata {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    image {
        width: 240rpx;
        height: 240rpx;
    }

    text {
        margin-top: 10rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
    }
}
</style>