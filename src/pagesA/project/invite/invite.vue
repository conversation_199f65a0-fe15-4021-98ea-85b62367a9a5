<template>
	<view class="invite">
		<view class="back" @click="back">
			<image src="@/static/imgs/invite/back.png" mode="aspectFill"></image>
		</view>

		<view class="headBgImg">
			<image src="@/static/imgs/invite/headBgImg.png"></image>
		</view>
		<view class="addLis">
			<view>
				<view>邀请</view>
				<view>好运值</view>
			</view>
			<view class="lis">
				<view>邀请一位未在Bigverse实名过的好友(新身份证)</view>
				<view>+5</view>
			</view>
			<view class="lis">
				<view>邀请的好友(新身份证)累计消费￥100以上</view>
				<view>+15</view>
			</view>
		</view>

		<view class="lucky">
			<view>
				<view>
					<image src="@/static/imgs/invite/lucky.png" mode="aspectFill"></image>
				</view>
				<view>
					<view>{{invitedLuckNumText}}</view>
					<u-line-progress active-color="rgba(99, 234, 238)" :percent="invitedLuckNum" :show-percent='false'
						inactive-color='rgba(36, 48, 58)' style="width: 300rpx;"></u-line-progress>
				</view>
			</view>
			<view>每集满100分好运值可在概率活动中兑换一张必中卡</view>
		</view>

		<view class="linkBox">
			<view>
				<view>
					<view>我的邀请码</view>
					<view @click="copyCode">{{invitedCode}}</view>
				</view>
				<view @click="copy">复制邀请链接</view>
			</view>

			<view>
				<view>
					<view class="qrCode">
						<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="value" size="236rpx" :options="options"
							@complete="complete"></uv-qrcode>
					</view>
				</view>
				<view>
					<view>截图保存</view>
					<view>邀请二维码</view>
				</view>
			</view>
		</view>

		<view class="friendLis">
			<view class="tit">
				好友列表
			</view>
			<view class="lisBox">
				<view class="headTit">
					<view>我的好友</view>
					<view>状态</view>
					<view>好运值</view>
				</view>
				<scroll-view scroll-y="true" :style="`width: 100%; height:${scrollHeight}rpx;`"
					@scrolltolower="bottomOut">
					<!-- friendsList -->
					<view class="list" id="friendLis" v-for="(item,index) in friendsList" :key="index" >
						<view>
							<view>
								<image src="@/static/imgs/invite/friend.png" mode="aspectFill"></image>
							</view>
							<view>
								<view>{{item.friendName}}</view>
								<view>
									<text v-show="item.realName==1">(已实名)</text>
									<text v-show="item.realName==0">(未实名)</text>
									<text v-show="item.realName==2">(重复实名)</text>
								</view>
							</view>
						</view>
						<view>
							<!-- 状态0 未实名 1已实名且消费达到100 2已实名且消费未达到100 -->
							<text v-show="item.status==0">未实名</text>
							<text v-show="item.status==1">已实名且消费达到100</text>
							<text v-show="item.status==2">已实名且消费未达到100</text>
							<text v-show="item.status==3">该身份证已实名过</text>
						</view>
						<view>+{{item.luckNum}}</view>
					</view>
					<view class="null_body" v-if="friendsList==''">
						<view class="null">
							<view class="img">
								<image
									src="@/static/imgs/invite/none.png"
									mode="widthFix"></image>
							</view>
							<view class="text">
								你还没有邀请的好友
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="onTopGo" v-if="friendsList!=''">
					<view>
						<image src="@/static/imgs/invite/topGo.png" mode="widthFix"></image>
					</view>
					<view>上划加载更多...</view>
					<view>
						<image src="@/static/imgs/invite/topGo.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue';
	export default {

		data() {
			return {
				scrollHeight: 0,
				invitedLuckNum: 0, //受邀幸运数
				invitedLuckNumText: 0, //受邀幸运数
				invitedCode: '', //邀请码
				pageNum: 1,
				friendsList: [], //好友列表
				totalCount: 0, //好友数量
				value: '',
				options: {
					useDynamicSize: false,
					errorCorrectLevel: 'Q',
					// margin: 10,
					areaColor: "#fff",
					// 指定二维码前景，一般可在中间放logo
					// foregroundImageSrc: require('static/image/logo.png')
				},
			}
		},
		onShow() {
			this.friendsList = []
			this.pageNum = 1
			this.scrollHeight = 120 * 5
			this.getInfo() //邀请好友基本信息
			this.getFriendsList() //好友列表


		},
		methods: {
			async getInfo() { //邀请好友基本信息
				// #ifdef H5
				let { origin } = window.location;
				// #endif
				
				let {
					result
				} = await this.$api.java_invitedFriend()
					this.invitedLuckNum = result.invitedLuckNum % 100, //受邀幸运数
					this.invitedLuckNumText = result.invitedLuckNum
					this.invitedCode = result.invitedCode, //邀请码
					// #ifdef APP
					this.value = getApp().globalData.url + 'pages/project/login/register?code=' + this.invitedCode
				// #endif
				// #ifdef H5
					this.value = origin + '/h5/#/pages/project/login/register?code=' + this.invitedCode
				// #endif
			},
			async getFriendsList() { //好友列表
				let {
					result
				} = await this.$api.java_friendsList({
					pageNum: this.pageNum,
					pageSize: 6
				})
				this.friendsList = [...this.friendsList, ...result.list]
				this.totalCount = result.totalCount
				setTimeout(() => {
					console.log(result, 'result');
				}, 1000)
			},
			bottomOut() { //好友滚动列表到底了
				if (this.pageNum * 6 < this.totalCount) {
					this.pageNum++
					uni.showLoading({
						title: '加载中...'
					})
					this.getFriendsList()
				} else {
					uni.showToast({
						title: '没有更多了',
						icon: 'none'
					})
				}
				console.log('好友滚动列表到底了！');
			},
			complete(event) {
				console.log(event, 'event');
			},
			copy() {
				uni.setClipboardData({
					data: this.value,
					success() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			},
			copyCode() {
				uni.setClipboardData({
					data: this.invitedCode,
					success() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			},
			back() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.invite {
		width: 100%;
		position: relative;
		color: #fff;
		background: #35333E;
		padding-bottom: 50rpx;
		box-sizing: border-box;
	}

	.back {
		position: absolute;
		/* #ifdef APP */
		top:var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top:40rpx;
		/* #endif */
		left: 40rpx;
		z-index: 33;

		>image {
			width: 70rpx;
			height: 70rpx;
		}
	}

	.headBgImg {
		width: 100%;
		height: 500rpx;
		mask-image: linear-gradient(to bottom, black 90%, transparent 100%);

		>image {
			width: 100%;
			height: 100%;
		}
	}

	.addLis {
		width: 696rpx;
		margin: 0 auto;
		border-radius: 20rpx;
		overflow: hidden;
		background: #25232D;


		>view {
			width: 100%;
			height: 150rpx;
			text-align: center;
			display: flex;
			align-items: center;
			background: #25232D;

			>view:nth-child(1) {
				width: 70%;
			}

			>view:nth-child(2) {
				width: 30%;
			}
		}

		>view:nth-child(1) {
			height: 100rpx;
			background: #1D1B25;
			color: rgb(142, 141, 146);
		}

		>view:nth-child(2) {
			border-top: 0;
		}

		.lis {
			width: 95%;
			border-top: 1rpx solid #53505D;
			box-sizing: border-box;
			margin: 0 auto;
			line-height:36rpx;
			>view:nth-child(1) {
				font-size: 28rpx;
			}

			>view:nth-child(2) {
				color: rgba(99, 234, 238);
				font-size: 35rpx;
			}
		}
	}

	.lucky {
		width: 696rpx;
		margin: 30rpx auto;
		padding: 20rpx;
		box-sizing: border-box;
		background: rgb(37, 35, 45);
		border-radius: 30rpx;

		>view:nth-child(1) {
			display: flex;
			align-items: center;
			margin-bottom: 18rpx;

			>view:nth-child(1) {
				width: 179rpx;
				height: 83rpx;

				>image {
					width: 100%;
					height: 100%;
				}
			}

			>view:nth-child(2) {
				flex: 1;
				display: flex;
				align-items: center;
				color: rgba(99, 234, 238, 1);
				font-size: 70rpx;
				margin-left: 30rpx;
				justify-content: space-between;
				font-weight: bold;
				font-size: 54rpx;
				color: #63EAEE;
			}
		}

		>view:nth-child(2) {

			font-weight: 400;
			font-size: 22rpx;
			color: #63EAEE;
		}
	}

	.linkBox {
		width: 696rpx;
		margin: 0 auto;
		background: rgb(37, 35, 45);
		border-radius: 30rpx;
		overflow: hidden;
		padding: 30rpx 40rpx;
		box-sizing: border-box;
		
		>view:nth-child(1) {
			width: 100%;
			padding: 15rpx 0rpx;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
			>view:nth-child(1) {
				>view:nth-child(1) {
					color: #ccc;
					font-weight: 400;
					font-size: 24rpx;
				}

				>view:nth-child(2) {
					font-weight: 900;
					font-size: 54rpx;
					margin-top: 10rpx;
				}

			}

			// 复制链接
			>view:nth-child(2) {
				width: fit-content;
				height: 70rpx;
				line-height: 70rpx;
				text-align: center;
				font-weight: bold;
				font-size: 24rpx;
				color: #141816;
				background: linear-gradient(180deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
				padding: 0 15rpx;
				box-sizing: border-box;
				border-radius: 50rpx;
			}

		}

		>view:nth-child(2) {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			justify-content: space-between;
			background: #1D1B25;
			border-radius: 25rpx;
			padding-right: 40rpx;
			padding:20rpx 40rpx 20rpx 20rpx;
			>view:nth-child(1) {
				width: 280rpx;
				height: 280rpx;
				background: #fff;
				border-radius: 24rpx;
				position: relative;

				.qrCode {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
				}
			}

			>view:nth-child(2) {
				color: rgb(99, 97, 104);
				font-weight: bold;
				font-size: 50rpx;

				>view:nth-child(1) {
					margin-bottom: 30rpx;
				}
			}
		}
	}

	.friendLis {
		width: 696rpx;
		margin: 30rpx auto;

		.tit {
			width: 100%;
			text-align: center;
			margin-bottom: 50rpx;
			position: relative;
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;

			&::after {
				width: 40rpx;
				height: 6rpx;
				background: #63EAEE;
				content: '';
				position: absolute;
				bottom: -16rpx;
				left: 50%;
				transform: translateX(-50%);
			}
		}

		.lisBox {
			width: 100%;
			border-radius: 30rpx;
			overflow: hidden;
			background: #25232D;

			.headTit {
				width: 100%;
				height: 100rpx;
				text-align: center;
				display: flex;
				align-items: center;
				background: #1D1B25;
				color: #ccc;
				font-weight: 400;
				font-size: 24rpx;

				>view:nth-child(1) {
					width: 30%;
				}

				>view:nth-child(2) {
					width: 40%;
				}

				>view:nth-child(3) {
					width: 30%;
				}
			}

			.list {
				width: 95%;
				height: 120rpx;
				color: #fff;
				border-bottom: 1rpx solid rgb(62, 60, 71);
				margin: 0 auto;
				background: #25232D;
				text-align: center;
				display: flex;
				align-items: center;
				box-sizing: border-box;

				>view:nth-child(1) {
					width: 4	0%;
					display: flex;
					align-items: center;

					>view:nth-child(1) {
						width: 60rpx;
						height: 60rpx;
						margin-right: 10rpx;

						>image {
							width: 100%;
							height: 100%;
						}
					}

					>view:nth-child(2) {
						text-align: left;
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;

						>view:nth-child(1) {
							width: 126rpx;
							text-overflow: ellipsis;
							overflow: hidden;
							white-space: nowrap;
						}

						>view:nth-child(2) {
							color: #ccc;
							margin-top: 5rpx;
							font-size: 22rpx;
						}
					}
				}

				>view:nth-child(2) {
					width: 50%;
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;
				}

				>view:nth-child(3) {
					width: 20%;
					font-weight: bold;
					font-size: 28rpx;
					color: #63EAEE;
				}

			}

			.onTopGo {
				width: 100%;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: rgba(123, 122, 129, 1);
				font-weight: 400;
				font-size: 22rpx;
				box-sizing: border-box;

				>view:nth-child(2) {
					margin: 0 10rpx;
				}

				>view:nth-child(1),
				>view:nth-child(3) {
					width: 22rpx;
					height: 28rpx;

					>image {
						width: 100%;
						height: 100%;
					}
				}
			}

		}


		.none {
			width: 100%;
			height: auto;
			text-align: center;
			padding-bottom: 100rpx;
			font-size: 24rpx;
			color: #ccc;

			>image {
				width: 300rpx;
				margin: 0 auto;
			}
		}
	}

	::v-deep .u-active[data-v-26adb0f2] {
		border-radius: 60rpx;
	}
	.null_body {
		.null {
	
			.img {
				display: flex;
				justify-content: center;
	
				image {
					width: 242rpx;
				}
			}
	
		}
	
		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
	
		width:100%;
		height: 34vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>