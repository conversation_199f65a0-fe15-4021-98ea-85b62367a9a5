<template>
    <view class="page">
        <!-- 顶部导航 -->
        <view class="nav">
            <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381638686293712896.png"
                size="88" @click="back"></u-icon>
            <view class="nav_title">
                <view class="stock_name">阿里巴巴</view>
                <view class="stock_code">300321</view>
            </view>
            <!-- <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381638642836529152.png" size="88"></u-icon> -->
            <!-- info.selfSelect info.selfSelect-->
            <view class="star">
                <image @click="starClick()" v-if="!selfSelect"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985216803233792.png" />
                <image @click="starClick()" v-else
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985542650322944.png" />
            </view>
        </view>

        <!-- 股票主信息 -->
        <view class="main_info">
            <view class="price">250.12</view>
            <view class="change">
                <view class="change_amount up">+636.74</view>
                <view class="change_percent up">+0.71%</view>
            </view>
        </view>

        <!-- 详细数据 -->
        <view class="detail_info">
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">高</view>
                    <view class="value left ">221.67</view>
                </view>
                <view class="label_box">
                    <view class="label right">昨收</view>
                    <view class="value right">221.67</view>
                </view>
            </view>
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">低</view>
                    <view class="value left ">221.67</view>
                </view>
                <view class="label_box">
                    <view class="label right">换</view>
                    <view class="value right">8.75B</view>
                </view>
            </view>
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">开</view>
                    <view class="value left ">3.273T</view>
                </view>
                <view class="label_box">
                    <view class="label right">额</view>
                    <view class="value right">221.67</view>
                </view>
            </view>
        </view>

        <!-- 盘后信息 -->
        <view class="after_market">
            <view>
                盘后:115.820 <text class=" ">+2231.07</text><text class="after_up">+3.17%</text>
            </view>
            <view>20:00 美东时间</view>
        </view>

        <!-- 时间选择器 -->
        <view class="tab_select">
            <view class="left_view">

                <view class='tab_item' @click="tabClick(item)">盘前
                    <image style="margin-left: 10rpx;"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381650876635635712.png">
                    </image>
                </view>

                <view v-for="(item, index) in tabList" :key="item.id || index"
                    :class="['tab_item', { tab_active: item.active }]" @click="tabClick(item)">{{ item.name }}
                </view>
            </view>
            <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381650747664982016.png"
                size="74"></u-icon>
        </view>

        <view class="tab_select">
            <view class="left_view">
                <view v-for="(item, index) in panList" :key="item.id || index"
                    :class="['tab_item', { tab_active: item.active }]" @click="SubtabClick(item)">{{ item.label }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
// import { startSocket, onSocketOpen } from "./websockets.js"

export default {
    components: {

    },
    data() {
        return {
            selfSelect: false,
            panList: [
                {
                    value: "PreMarket",
                    label: "盘前"
                },
                {
                    value: "Regular",
                    label: "盘中"
                },
                {
                    value: "AfterHours",
                    label: "盘后"
                }
            ],
            tabList: [
                // {
                //     name: '盘前',
                //     active: false
                // },
                // {
                //     name: '盘后',
                //     active: false
                // },
                {
                    name: '日K',
                    active: false
                },
                {
                    name: '周K',
                    active: false
                },
                {
                    name: '月K',
                    active: false
                },
                {
                    name: '五日',
                    active: false
                }
            ]

        }
    },
    onLoad() {
        // startSocket()
    },
    methods: {
        async getTradeTicker() {
            let res = await this.$api.tradeTick({
                tradeSession: ""
            })
        },
        // tradeTick
        async starClick(e) {
            // if (e) {
            //     let res = await this.$api.deleteContractSelf({
            //         id: uni.getStorageSync("currentContract").id
            //     })
            //     if (res.code == 200) {
            //         this.getContractSymbols()
            //         uni.showToast({
            //             title: "取消自选成功",
            //             icon: 'none'
            //         })
            //     }
            // } else {
            //     let res = await this.$api.addContractSelf({
            //         id: uni.getStorageSync("currentContract").id
            //     })
            //     if (res.code == 200) {
            //         this.getContractSymbols()
            //         uni.showToast({
            //             title: "添加自选成功",
            //             icon: 'none'
            //         })
            //     }
            // }
        },
        tabClick(item) {
            this.tabList.forEach(item => {
                item.active = false;
            });
            item.active = true;
        },
        back() {
            this.$Router.back();
        }
    }
} 
</script>

<style scoped lang="scss">
.page {
    background: #fff;
    min-height: 100vh;
    padding: 0 20rpx;
    font-family: Gilroy;
}

.nav {
    display: flex;
    align-items: center;
    padding: 30rpx 0 20rpx 0;

    .nav_title {
        flex: 1;
        margin: 0 20rpx;

        .stock_name {
            font-size: 32rpx;
            font-weight: bold;
        }

        .stock_code {
            font-size: 22rpx;
            color: #888;
        }
    }

    .star {
        image {
            width: 48rpx;
            height: 48rpx;
        }
    }
}

.main_info {
    display: flex;
    margin-top: 20rpx;

    .price {
        font-size: 48rpx;
        font-weight: bold;
        font-family: PingFang SC;
    }

    .change {
        margin-left: 20rpx;
        display: flex;
        align-items: center;
        font-family: Gilroy;

        .change_amount,
        .change_percent {
            font-size: 26rpx;
            border-radius: 10rpx;
            height: 44rpx;
            line-height: 44rpx;
            text-align: center;
            padding: 0 10rpx;
            margin-left: 10rpx;
            font-weight: 500;
        }

        .up {
            background: #eaffea;
            color: #30C147;
        }

        .down {
            background: #ffeaea;
            color: #f56c6c;
        }
    }
}

.detail_info {
    margin: 20rpx 0 0;
    border-bottom: 1rpx solid #E1E1E1;
    padding-bottom: 30rpx;

    .detail_row {
        display: flex;
        justify-content: space-between;
        font-size: 22rpx;
        color: #888;
        margin-bottom: 6rpx;

        .label_box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 284rpx;
        }

        .label,
        .value {
            width: 25%;
            text-align: right;
        }

        .left {
            font-weight: 500;
        }

        .right {
            color: #888;
        }

        .label {
            color: rgba(0, 0, 0, 0.8);
            text-align: left;
        }

        .up {
            color: #4ecb73;
        }
    }
}

.after_market {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    margin-bottom: 20rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-bottom: 1rpx solid #E1E1E1;
    color: #000;
    font-family: Gilroy;

    .after_up {
        color: #4ecb73;
        margin-left: 20rpx;
    }
}

.tab_select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    width: 100%;
    font-family: Gilroy;

    .left_view {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .tab_item {
            white-space: nowrap;
            background: #f6f6f6;
            border-radius: 34rpx;
            padding: 11rpx 15rpx;
            font-size: 22rpx;
            color: #222;
            min-width: 70rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            image {
                width: 10rpx;
                height: 8rpx;
            }
        }

        .tab_active {
            background: #FF82A3;
            color: #fff;
        }
    }

}

.granularity {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;

    .gran_item {
        background: #f6f6f6;
        border-radius: 20rpx;
        padding: 8rpx 24rpx;
        margin-right: 12rpx;
        margin-bottom: 12rpx;
        font-size: 22rpx;
        color: #222;
    }

    .gran_active {
        background: #ffdbe1;
        color: #ff4d6a;
    }
}
</style>