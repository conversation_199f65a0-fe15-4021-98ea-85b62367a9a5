<template>
    <u-popup width="666" mode="left" v-model="show" @close="close" safe-area-inset-bottom :mask="true" round>
        <view class="market-drawer">
            <view class="title">
                <text>{{ current == 0 ? '美股列表' : '港股列表' }}</text>
            </view>

            <!-- 顶部 Tabs -->
            <view class="tabs">
                <view v-for="(tab, index) in tabs" :key="index" :class="['tab', { active: currentTab === index }]"
                    @click="checktab(index)">
                    {{ tab }}
                </view>
            </view>

            <!-- 合约列表 -->
            <view class="contract-list">
                <!-- 表头 -->
                <view class="contract-header">
                    <text class="header-label">股票/代码</text>
                    <text class="header-label">价格</text>
                    <text class="header-label">涨跌</text>
                </view>

                <scroll-view class="list-scroll" scroll-y="true" @scrolltolower="onReachBottombom"
                    :refresher-enabled="true" :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh"
                    @refresherrestore="onRefreshRestore">

                    <view class="list">
                        <!-- 合约列表 -->
                        <view @click="checkedContract(item)" class="contract-item" v-for="(item, index) in contractList"
                            :key="index">
                            <view class="left">
                                <text class="pair">{{ item.name }}</text>
                                <text class="symbol">{{ item.symbol }}</text>
                            </view>
                            <view class="price">{{ item.price || '--' }}</view>
                            <view class="right">
                                <text :class="[
                                    'change',
                                    Number(item.closePrice) - Number(item.openPrice) >= 0 ? 'rise' : 'fall'
                                ]">
                                    <!-- {{
                                        Number(item.closePrice) - Number(item.openPrice) >= 0 ? '+' : ''
                                    }}{{ (Number(item.closePrice) - Number(item.openPrice)).toFixed(item.pricePrecision)
                                    }}% -->
                                    --

                                </text>
                            </view>
                        </view>

                        <!-- 加载更多提示 -->
                        <!-- <view class="load-more" v-if="showLoadMore">
                            <view class="loading-text" v-if="isLoading">
                                <text class="loading-icon">⟳</text>
                                <text>加载中...</text>
                            </view>
                            <view class="no-more-text" v-else-if="noMoreData">
                                <text>没有更多数据了</text>
                            </view>
                        </view> -->
                        <nodata v-if="contractList.length == 0" />

                    </view>

                </scroll-view>
            </view>
        </view>
    </u-popup>
</template>

<script>
import nodata from "../components/nodata"
export default {
    name: 'MarketDrawer',
    components: {
        nodata
    },
    props: {
        show: Boolean,
        current: Number,
    },
    data() {
        return {
            currentTab: 1,
            tabs: ['自选', '常规'],
            // 滚动加载相关状态
            isRefreshing: false,
            isLoading: false,
            noMoreData: false,
            showLoadMore: false,
            currentPage: 1,
            contractList: [
                // { pair: 'BTC/USDT', price: '62,500.00', change: 621.56 },
                // { pair: 'BTC/USDT', price: '62,500.00', change: -621.56 },
                // { pair: 'BTC/USDT', price: '62,500.00', change: 621.56 },
                // { pair: 'BTC/USDT', price: '62,500.00', change: 621.56 },
                // { pair: 'BTC/USDT', price: '62,500.00', change: 621.56 }
            ],
            pageNum: 1,
            pageSize: 10
        }
    },
    mounted() {
        this.getcontractSymbolList()
    },
    methods: {
        checktab(index) {
            this.currentTab = index
            this.currentPage = 1
            this.noMoreData = false
            this.contractList = []

            if (index == 0) {
                this.getself(true)
            } else {
                this.getcontractSymbolList()
            }
        },
        async getself(isRefresh = false) {
            let res = await this.$api.selfStockList({
                pageNum: isRefresh ? 1 : this.currentPage,
                pageSize: this.pageSize,
                market: this.current == 0 ? 'US' : 'HK'
            });
            if (res.code == 200) {
                const newData = res.result.data || [];

                if (isRefresh) {
                    // 刷新时替换数据
                    this.contractList = newData;
                } else {
                    // 加载更多时追加数据
                    this.contractList = [...this.contractList, ...newData];
                }

                // 判断是否还有更多数据
                if (newData.length < this.pageSize) {
                    this.noMoreData = true;
                    this.showLoadMore = true;
                } else {
                    this.showLoadMore = false;
                }
            } else {
                uni.showToast({
                    title: res.msg || '加载失败',
                    icon: 'none'
                });
            }
        },
        checkedContract(item) {
            uni.setStorageSync("currentContract", item)
            this.$emit('update:show', false)
        },
        // 下拉刷新
        async onRefresh() {
            this.isRefreshing = true;
            this.currentPage = 1;
            this.noMoreData = false;
            try {
                await this.loadContractList(true);
            } catch (error) {
                console.error('刷新失败:', error);
            } finally {
                this.isRefreshing = false;
            }
        },
        // 刷新完成
        onRefreshRestore() {
            this.isRefreshing = false;
        },

        // 上拉加载更多
        async onReachBottombom(e) {
            console.log('到底了',e);
            
            if (this.isLoading || this.noMoreData) {
                return;
            }

            this.isLoading = true;
            this.showLoadMore = true;
            this.currentPage++;



            try {
                if (this.currentTab == 0) {
                    await this.getself(false);
                } else {
                    await this.loadContractList(false);
                }
            } catch (error) {
                console.error('加载更多失败:', error);
                this.currentPage--; // 失败时回退页码
            } finally {
                this.isLoading = false;
            }
        },

        // 加载合约列表数据
        async loadContractList(isRefresh = false) {
            let res = await this.$api.stockList({
                pageNum: this.currentPage,
                // pageSize: this.pageSize,
                market: this.current == 0 ? 'US' : 'HK'
            });

            if (res.code == 200) {
                const newData = res.result || [];

                if (isRefresh) {
                    // 刷新时替换数据
                    this.contractList = newData;
                } else {
                    // 加载更多时追加数据
                    this.contractList = [...this.contractList, ...newData];
                }

                // if (res.result.length) {
                //     // uni.setStorageSync("currentContract", res.result[0])
                // }



                // 判断是否还有更多数据
                if (newData.length < this.pageSize) {
                    this.noMoreData = true;
                    this.showLoadMore = true;
                } else {
                    this.showLoadMore = false;
                }
            } else {
                uni.showToast({
                    title: res.msg || '加载失败',
                    icon: 'none'
                });
            }
        },

        // 兼容原有方法
        async getcontractSymbolList() {
            this.currentPage = 1;
            this.noMoreData = false;
            await this.loadContractList(true);
        },

        close() {
            this.$emit('update:show', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.market-drawer {
    background-color: #fff;
    height: 100%;

    .title {
        margin: 40rpx 0 0 32rpx;
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
    }

    .tabs {
        display: flex;
        padding: 24rpx 0 28rpx 32rpx;
        gap: 24rpx;


        .tab {
            padding: 11rpx 24rpx;
            border-radius: 8rpx;
            font-family: PingFang SC;
            color: rgba(0, 0, 0, 0.4);
            font-weight: 400;
            background-color: transparent;
            transition: none;
            font-size: 24rpx;
            line-height: 34rpx;

            &.active {
                color: #000;
                font-weight: 500;
                background: rgba(246, 246, 246, 1);
            }
        }
    }

    .contract-list {
        padding: 0 32rpx;
        height: calc(100vh - 200rpx);
        /* 设置合适的高度 */

        .list-scroll {
            height: 100%;

            .list {
                min-height: 100%;
            }
        }

        .contract-header {
            display: flex;
            justify-content: space-between;
            // padding: 20rpx 0;

            .header-label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                color: rgba(0, 0, 0, 0.4);
                width: 20%;

                &:last-child {
                    text-align: right;
                }

                &:nth-child(1) {
                    text-align: left;
                }

                &:nth-child(2) {
                    text-align: center;
                }
            }
        }

        .contract-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 0;

            &:nth-of-type(1) {
                padding: 16rpx 0 20rpx 0;
            }

            .left {
                display: flex;
                flex-direction: column;
                flex: 1;

                .pair {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, 0.5);
                }

                .symbol {
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, 0.5);
                }

            }


            .price {
                flex: 1;
                text-align: center;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 40rpx;
                color: #000;
            }

            .right {
                flex: 1;
                text-align: right;

                .change {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 24rpx;
                    line-height: 40rpx;

                    &.rise {
                        color: #FF82A3;
                    }

                    &.fall {
                        color: #30C147;
                    }
                }
            }
        }

        /* 加载更多提示样式 */
        .load-more {
            padding: 30rpx 0;
            text-align: center;

            .loading-text {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10rpx;
                color: rgba(0, 0, 0, 0.6);
                font-size: 24rpx;

                .loading-icon {
                    animation: rotate 1s linear infinite;
                    font-size: 28rpx;
                }
            }

            .no-more-text {
                color: rgba(0, 0, 0, 0.4);
                font-size: 24rpx;
            }
        }
    }

}

/* 旋转动画 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>