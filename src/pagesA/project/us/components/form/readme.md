    <view class="Kline">
        <KForm ref="child" :period="period" :selectTopHeight='selectTopHeight'
          :selectBottomHeight='selectBottomHeight' :statusBarHeight="statusBarHeight"
         >
        </KForm>
      </view>
    period为times的value
     times: [
        {
          name: '分时图',
          value: 'area'
        },
        {
          name: "5分钟",
          value: 5,
        },
        {
          name: "30分钟",
          value: 7,
        },
        {
          name: "1小时",
          value: 8,
        },
        {
          name: "4小时",
          value: 4,
        },
        {
          name: "1天",
          value: 12,
        },
        {
          name: "1周",
          value: 6,
        },
      ],

        // 获取手机bar的高度
    getDeviceBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          // res.statusBarHeight
          this.statusBarHeight = res.statusBarHeight;
          // #ifdef H5
          this.statusBarHeight = 10;
          // #endif
        },
      });
    },
    // 获取元素高度
    getSelectheight() {

      // #ifdef H5
      const DomTopHeight = uni.getStorageSync("selectTopHeight")
      const DomBottomHeight = uni.getStorageSync("selectBottomHeight")
      if (DomTopHeight && DomBottomHeight) {
        this.selectTopHeight = DomTopHeight
        this.selectBottomHeight = DomBottomHeight
        this.isStartForm = true
        return
      }
      // #endif

      // #ifdef APP-PLUS
      setTimeout(() => {
        uni.createSelectorQuery().in(this).select("#contentTop").boundingClientRect(data => {
          // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
          this.selectTopHeight = JSON.parse(JSON.stringify(data)).height

          uni.setStorageSync("selectTopHeight", this.selectTopHeight)
        }).exec()

        uni.createSelectorQuery().in(this).select("#contentBottom").boundingClientRect(data => {
          // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
          this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
          uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
        }).exec()

        this.isStartForm = true
      }, 1500)
      // #endif

      // #ifdef H5
      this.$nextTick(() => {
        setTimeout(() => {
          uni.createSelectorQuery().select("#contentBottom").boundingClientRect(data => {
            this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
            uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
          }).exec()
          uni.createSelectorQuery().select("#contentTop").boundingClientRect(data => {
            // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
            this.selectTopHeight = JSON.parse(JSON.stringify(data)).height
            uni.setStorageSync("selectTopHeight", this.selectTopHeight)
          }).exec()
          this.isStartForm = true
        }, 1500)
      })
      // #endif

    },

Req 请求历史 k 线
{

    "event":"req",
    "params" {
        "channel": "market_e_aaveusdt_kline_1min", "cb_id":"e_btcusdt", "since":"1506602880"
    }

}

实时推送 订阅
market_e_aaveusdt_kline_5min
market_e_aaveusdt_kline_30min 1day、4h、60min
{
"event":"sub",
"params":{
"channel":"market_e_aaveusdt_kline_1min","cb_id":"自定义"
}
}

返回参数 解读
"event*rep":"rep",
"channel":"market*$base$quote*kline*[5min/30min/60min/4h/1day]",
"cb_id":"原路返回",
"since":"1506602880",//since 缺省时返回最新 300 条，有值时返回大于 since 的最多 1 小时数据，since 有强校验，不能早于当前 1 小时
"ts":1506584998239,//请求时间
"amount":123.1221,//交易额
"vol":1212.12211,//交易量
"open":2233.22,//开盘价
"close":1221.11,//收盘价
"high":22322.22,//最高价
"low":2321.22//最低价

返回具体的 json
{
"channel":"market_e_aaveusdt_kline_1min",
"data":[ {
"amount": 51031956.861,
"close":80.03,
"ds":"2023-01-20 06:00:00",
"high":80.124,
"id":1674194400,
"idx":1674194400,
"low":80.03,
"open":80.1,
"tradeId":0,
"vol":637267
}

    ],
    "event_rep":"rep",
    "status":"ok",
    "tick":null,
    "ts":1722360401000

}

深度请求 盘口
{

    "event":"sub",
    "params": {
        "channel": "market_e_aaveusdt_depth_step[0-2]", "cb_id":"自定义", "asks":150, "bids":150
    }

}

响应参数解读
{
"channel":"market\_$base$quote_depth_step[0-2]",//$base$quote 表示 aaveusdt 等,深度有 3 个维度，0、1、2
"ts":1506584998239,//请求时间
"tick":{
"asks":[ //卖盘
[22112.22,0.9332], // 价格
[22112.21,0.2] // 数量
],
"buys":[ //买盘
[22111.22,0.9332],
[22111.21,0.2]
]
}
}

实时成交信息
{
"event": "sub",
"params": {
"channel": "market_e_aaveusdt_trade_ticker",
"cb_id": "1"
}
}

响应数据
{
"channel":"market\_$base$quote_trade_ticker",//订阅的交易对行情$base$quote 表示 btckrw 等
"ts":1506584998239,//请求时间
"tick":{
"id":12121,//data 中最大交易 ID
"ts":1506584998239,//data 中最大时间
"data":[
{
"id":12121,//交易 ID
"side":"buy",//买卖方向 buy,sell
"price":32.233,//单价
"vol":232,//数量
"amount":323,//总额
"ts":1506584998239,//数据产生时间
"ds":'2017-09-10 23:12:21'
},
{
"id":12120,//交易 ID
"side":"buy",//买卖方向 buy,sell
"price":32.233,//单价
"vol":232,//数量
"amount":323,//总额
"ts":1506584998239,//数据产生时间
"ds":'2017-09-10 23:12:21'
}
]
}
}

indexYs live_data history_position_data
<view class="live_data"
                                            :style="{ borderRadius: (item.trailType && item.epProfit != null) ? '20rpx' : '' }">
<view class="sy red">
<text class="text">收益</text>
<view
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">
<text class="text" v-if="item.status == 0"
                                                        style="margin-left: 4rpx;"
                                                        :class="[item.trailType == 2 ? 'textline' : '']"
                                                        :style="{ color: (item.profit) >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            (item.profit) >= 0 ? '+' : '' }} {{ item.profit.toFixed(2) || 0
                                                        }}
</text>
<!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
<text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>
<!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>
                                                </view>
                                                <text style="margin-left: 10rpx;"
                                                    :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateEarnings(item) }}
                                                </text>

                                            </view>
                                            <view class="syl red">
                                                <!-- :class="{ 'red': item.red2 }" -->
                                                <text>收益率</text>
                                                <!-- <text v-if="item.status == 0"
                                                    :style="{ color: item.profit >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                        item.profit >= 0 ? '+'
                                                            : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                </text> -->
                                                <view v-if="item.status == 0"
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">

                                                    <text v-if="item.status == 0"
                                                        :class="[item.trailType == 2 ? 'textline' : '']"
                                                        :style="{ color: item.profitRate >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            item.profitRate >= 0 ? '+'
                                                                : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                    </text>
                                                    <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>
                                                    <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>
                                                </view>
                                                <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateYield(item) }}
                                                </text>
                                            </view>
                                        </view>
