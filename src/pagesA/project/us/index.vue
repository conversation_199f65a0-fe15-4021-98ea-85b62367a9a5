<template>
    <view class="page">
        <view class="barHeight"></view>

        <view class="tabber">
            <u-tabs bg-color="transparent" name="cate_name" :font-size="32" :list="list" :is-scroll="false"
                :item-width="100" :active-item-style="itemStyle" inactive-color="rgba(0, 0, 0, 0.4)"
                active-color="#000000" :current="current" @change="change"></u-tabs>
        </view>
        <view class="us_apple">
            <view class="us_apple_item">
                <view class="us_apple_item_name" @click="drawerShow = true">
                    <view class="us_apple_item_name_title">
                        300023 阿里巴巴
                    </view>
                    <view class="us_apple_item_name_tag">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1380576011052736512.png"
                            mode="widthFix"></image>
                    </view>
                </view>
                <view class="rise">
                    <view class="left">
                        <view>最新:<text>123.45</text></view>
                        <view>额：5.04亿</view>
                        <view>换：0.75%</view>
                        <view @click="goDetails">行情></view>
                    </view>
                    <view class="right">
                        收起
                    </view>
                </view>
            </view>
        </view>
        <buy-sell-order></buy-sell-order>
        <position></position>

        <!-- stock List -->
        <marketDrawerPopup :show.sync="drawerShow"  :current="current"/>

    </view>
</template>

<script>
import marketDrawerPopup from "./components/marketDrawer"
import buySellOrder from './components/buySellOrder.vue'
import position from "./components/position"
export default {
    components: {
        buySellOrder,
        position,
        marketDrawerPopup
    },
    data() {
        return {
            drawerShow: false,
            list: [{
                cate_name: '美股'
            }, {
                cate_name: '港股'
            }],
            current: 0,
            itemStyle: {
                'font-size': '32rpx',
                'color': '#000',
            },

        }
    },
    onLoad() {
        console.log(this.$)
    },
    methods: {
        change(index) {
            this.current = index
        },
        goDetails() {
            this.$Router.push({
                name: 'usDetails'
            })
        }
    }
} 
</script>

<style scoped lang="scss">
.page {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 10rpx 30rpx;

    .tabber {
        width: 300rpx;
        height: 80rpx;
        background-color: #fff;
    }

    .us_apple {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 32rpx;
        font-family: 'PingFang SC';

        .us_apple_item {
            width: 100%;

            .us_apple_item_name {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .us_apple_item_name_title {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #000;
                }

                .us_apple_item_name_tag {
                    font-size: 24rpx;
                    color: #000;
                    height: 48rpx;
                    line-height: 48rpx;
                    padding: 0 20rpx;
                    border-radius: 24rpx;
                    font-weight: 600;

                    image {
                        width: 24rpx;
                        height: 24rpx;
                    }
                }
            }

            .rise {
                font-size: 24rpx;
                height: 48rpx;
                line-height: 40rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .left {
                    display: flex;
                    justify-content: space-between;

                    >view {
                        color: #000;
                        margin-right: 24rpx;
                        min-width: 120rpx;
                    }
                }

                .right {
                    color: #FF82A3;
                }
            }
        }

        .right_icon {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rpx;

            image {
                width: 72rpx;
                height: 72rpx;
            }
        }
    }
}
</style>