<template>
    <view class="list">
        <view style="height: 40rpx"></view>
        <view v-for="(item, index) in list" :key="index" v-show="list.length > 0 && isLoadingStatus === 1">
            <!--  -->
            <basicMore :item="item" :subCurrent="subCurrent" @revocation="revocation"></basicMore>
        </view>
        <view class="null_body" v-show="list.length <= 0 && isLoadingStatus === 2">
            <view class="null">
                <view class="img">
                    <image
                        src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
                        mode="widthFix"></image>
                </view>
                <view class="text">
                    暂无数据
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import basicMore from './components/basicMore.vue'

export default {
    name: "MyE<PERSON>ru",
    components: {
        basicMore,
    },
    data() {
        return {
            pageNum: 1,
            list: [],
            isLoadingStatus: 0,
            subCurrent: 0,
            hasNext: false,
        };
    },
    onReachBottom() {
        if (this.hasNext) {
            this.pageNum++
            this.getDutyList()
        }
    },
    onLoad() {
        this.getDutyList()
    },
    methods: {
        async revocation(item) { //批量 撤销
            console.log(item)
            let {
                result,
                status
            } = await this.$api.java_targetCancel({
                id: item.dutyId
            })
            if (status.code === 0) {
                this.hasNext = result.hasNext
                this.pageNum = 1
                this.list = []
                uni.showToast({
                    title: '撤销成功',
                    icon: 'none'
                })
                this.getDutyList()
            } else {
                uni.showToast({
                    title: status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async getDutyList() { //批量购买中
            let {
                result,
                status
            } = await this.$api.java_targetDutyList({
                pageNum: this.pageNum,
                pageSize: 10
            })
            if (status.code === 0) {
                if (result.list == null || result.list.length === 0) {
                    // this.isFooter = false
                    // if (this.list == []) {
                    // this.list = [
                    //     {
                    //         "title": "高空交易",
                    //         "cover": "https://cdn-lingjing.nftcn.com.cn/image/20241111/e87047da410e878ebdfc5493a84c4335_400x400.png",
                    //         "createNum": 1000,
                    //         "minPrice": 101,
                    //         "dutyId": 10454,
                    //         "dutyTime": "2024-11-22 14:47:50",
                    //         "targetPrice": 1,
                    //         "targetNum": 1,
                    //         "tip": null
                    //     }
                    // ]
                    this.isLoadingStatus = 2
                    // }
                } else {
                    this.isLoadingStatus = 1


                    this.pageNum++
                    result.list.forEach((item) => {
                        this.list.push(item)
                    })
                }
            }
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-search .u-icon__img {
    width: 44rpx !important;
    height: 44rpx !important;
}

.main {
    .search {
        margin-top: 33rpx;
        padding: 0 32rpx;
        margin-bottom: 10rpx;
        // opacity: 0.5;
        font-size: 12rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .border {
            width: 100%;
            background-color: #3D3022;
        }

        input {
            font-size: 24rpx;
        }

    }

    // .date {
    // 	width: 268rpx;
    // 	height: 50rpx;
    // 	display: flex;
    // 	justify-content: space-between;
    // 	align-items: center;
    // 	margin-left: 30rpx;

    // 	.item {
    // 		display: flex;
    // 		justify-content: center;
    // 		align-items: center;
    // 		width: 120rpx;
    // 		height: 50rpx;
    // 		border-radius: 30rpx;
    // 		/** 文本1 */
    // 		font-size: 24rpx;
    // 		font-weight: 400;
    // 		color: #666666;
    // 		border: 1px solid #B6B6B6;
    // 	}

    // 	.line {
    // 		width: 16rpx;
    // 		height: 2rpx;
    // 		opacity: 1;
    // 		background: #666666;
    // 	}
    // }
}

.tabs_view {
    padding: 0rpx 10rpx 0rpx 10rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80rpx;

    .u_tabs {
        width: 80%;
    }

    .right_date {
        width: 220rpx;
        height: 40rpx;
        font-size: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #241E15;
        color: #D8B662;

        image {
            width: 16rpx;
        }
    }
}

.tabs {
    width: 100%;
    margin-top: -6rpx;
}

.subTab {
    width: 560rpx;
    padding: 26rpx 0;
}

.totalPrice {
    // margin: 0 36rpx;
    margin-left: 36rpx;
    // width: 240rpx;
    width: fit-content;
    height: 54rpx;
    text-align: center;
    line-height: 54rpx;
    /** 文本1 */
    font-size: 22rpx;
    font-weight: 400;
    letter-spacing: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    color: #D8B662;
    background-color: #241E15;

    .totalPrice-bg {
        // width: 240rpx;
        width: 100%;
        height: 54rpx;
        background: transparent;
        border-radius: 33rpx;
        padding: 0 20rpx;
        box-sizing: border-box;

    }
}

.null_body {
    .null {
        .img {
            display: flex;
            justify-content: center;

            image {
                width: 242rpx;
            }
        }

    }

    .text {
        color: #A6A6A6;
        font-size: 28rpx;
        text-align: center;
        margin-top: 30rpx;
    }

    width:100%;
    height: 40vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.new-modal-content {
    padding: 35rpx 40rpx;
    background: #241E15;

    .title_bg {
        font-weight: bold;
        color: #FFFFFF;
        font-size: 34rpx;
        width: 240rpx;
        position: relative;
        text-align: center;
        margin: 0 auto;

        image {
            width: 252rpx;
            margin: 0 auto;
        }
    }

    .success_img {
        display: flex;
        justify-content: center;
        align-items: center;

        image {
            width: 160rpx;
            height: 160rpx;
        }
    }

    .modal-content {
        padding: 35rpx 0rpx;
        font-size: 28rpx;
        color: #FFFFFF;
        text-align: center;

    }

    .showModal-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30rpx;

        >view {
            width: 266rpx;
            height: 80rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24rpx;
            font-weight: bold;
            color: #FFFFFF;
        }

        .img_cancel {}

        .img_reasale {
            color: #130E07;
        }
    }
}

.title_bg {
    font-weight: bold;
    color: #FFFFFF;
    font-size: 34rpx;
    width: 240rpx;
    position: relative;
    text-align: center;
    margin: 0 auto;

    image {
        width: 252rpx;
        margin: 0 auto;
    }
}
</style>