<template>
	<view class="">
		<view class="showbox">
			<view :class="[!flag?'green':'red']">
				<view>￥{{priceInfo.price||0}}</view>
				<view>
					<view></view>
					<view> <text v-show="flag">+</text> {{priceInfo.percent||0}}%</view>
				</view>
			</view>
			<view>
				<view>
					<view class="jusy">
						<view>今日最高</view>
						<view>￥{{priceInfo.dayHighPrice||0}}</view>
					</view>
					<view class="jusy">
						<view>今日最低</view>
						<view style="color: #6CFF8A;">￥{{priceInfo.dayLowPrice||0}}</view>
					</view>
				</view>
				<view>
					<view class="jusy">
						<view>今日交易量</view>
						<view style="color: #fff;">{{priceInfo.dayQuantity||0}}</view>
					</view>
					<view class="jusy">
						<view>1h成交量</view>
						<view style="color: #fff;">{{priceInfo.hourQuantity||0}}</view>
					</view>
				</view>
			</view>
		</view>
		<view class="tabs">
			<u-tabs :list='tabsList' :is-scroll="true" font-size='28' height="65" inactive-color='#ccc'
				active-color='#fff' :current="current" bg-color="#35333E" :bar-style='barStyle'
				@change="change"></u-tabs>
		</view>
		<view v-if="current==0">
			<view class="tabs_small">
				<u-tabs :list='tabsSmallList' :is-scroll="true" font-size='28' height="70" inactive-color='#ccc'
					active-color='#fff' :current="currentSmall" bg-color="#25232E" :bar-style='barSmallStyle'
					@change="smallChange"></u-tabs>
			</view>

			<view class="tit">
				价格￥
			</view>

			<view style="width: 100%;height: 617rpx;margin-bottom: 50rpx;padding-bottom: 30rpx;">
				<priceUcharts :series='series' :categories='categories' :quantityList='quantityList' :currentSmallVal='currentSmallVal'>
				</priceUcharts>

			</view>
			<view class="tit">
				交易量
			</view>
			<view style="width: 100%;height: 300rpx;">
				<transactionUcharts :quantityList='quantityList' :categories='categories'></transactionUcharts>
			</view>
		</view>
		<view class="body" v-else>
			<view class="list" v-if="list!=''">
				<view class="li flex_start" v-for="(item,index) in list">
					<view class="img">
						<image :src="item.cover" mode="aspectFill"></image>
					</view>
					<view class="font">
						<view class="left">
							<view class="title">{{item.title}}</view>
							<view class="tid">token ID: {{item.tid}} </view>
							<view class="msg flex_start">
								<view class="msg_view flex_start">
									<view class="tag">
										时间
									</view>
									{{item.buyTime}}
								</view>
								<view class="msg_view flex_start ">
									<view class="tag">
										用户
									</view>
									<view class="oneOver yumin">{{item.userMark}}</view>
								</view>
							</view>
						</view>
					</view>
					<view class="price">
						￥{{item.price}}
					</view>
				</view>
				<view class="msg_msg">
					仅展示最近20条记录
				</view>
				<!-- <view class="footer_ask_buy">
					<button-bar class="but" @click='nav_ask_buy' text="去求购"></button-bar>
				</view> -->
			</view>
			<view class="null" v-else>
				<image
					src="https://cdn-lingjing.nftcn.com.cn/image/20230407/426736c67835242ade91c201ea664e01_515x843.png"
					mode="widthFix"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	import priceUcharts from '@/components/priceUcharts/priceUcharts.vue' //K线图
	import transactionUcharts from '@/components/transactionUcharts/transactionUcharts.vue' //柱状折现混合图
	export default {
		components: {
			ButtonBar,
			transactionUcharts, //折现
		},
		data() {
			return {
				flag: false, //百分比大于0  true  小于0 false
				tabsList: [{
					name: '行情',
					value: ''
				}, {
					name: '成交记录',
					value: ''
				}],
				tabsSmallList: [{
					name: '5分',
					value: '5'
				}, {
					name: '1时',
					value: '60'
				}, {
					name: '1天',
					value: '1440'
				}],
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				},
				barSmallStyle: {
					'background': '#63EAEE'
				},
				current: 1, //行情
				currentSmall: 1, //天数index
				currentSmallVal: 60, //天数val
				background: {
					backgroundColor: 'var(--main-bg-color)',
				},
				list: [], //成交记录
				Klist: [], //K线图数据
				id: '',
				ctid: '',
				time: '',
				priceInfo: {
					percent:""
				},
				series: [], //K线图数据
				categories: [], //k线图时间数据
				quantityList: [], //交易量数据

			};
		},
		onLoad(options) {
			this.ctid = options.ctid
			this.id = options.id
			let date = new Date()
			let year = date.getFullYear()
			let month = date.getMonth() + 1
			let day = date.getDate()
			let house = date.getHours()
			let minutes = date.getMinutes()
			let getSeconds = date.getSeconds()
			if (month < 10) {
				month = '0' + month
			}
			if (day < 10) {
				day = '0' + day
			}
			if (house < 10) {
				house = '0' + house
			}
			if (minutes < 10) {
				minutes = '0' + minutes
			}
			if (getSeconds < 10) {
				getSeconds = '0' + getSeconds
			}
			this.time = `${year}-${month}-${day} ${house}:${minutes}:${getSeconds}.000`
			this.get()
			this.getInfo()
			this.priceKline()
		},
		onReachBottom() {},
		methods: {
			async getInfo() { //系列价格信息
				let {
					result,
					status
				} = await this.$api.getSeriesTodayPriceInfo({
					ctid: this.ctid
				})
				if (status.code == 0) {
					this.priceInfo = result
					if (this.priceInfo.percent < 0) {
						this.flag = false
						this.priceInfo.percent = (this.priceInfo.percent * 100).toFixed(2)
					} else {
						this.flag = true
						this.priceInfo.percent = (this.priceInfo.percent * 100).toFixed(2)
					}
				}
			},
			async priceKline() { //系列价格k线
				let {
					result,
					status
				} = await this.$api.getSeriesPriceKline({
					ctid: this.ctid,
					pageNum: 1,
					pageSize: 15,
					searchTime: this.time,
					type: this.currentSmallVal
				})
				if (status.code == 0) {
					this.Klist = result.list
					this.Klist.forEach(item => {
						this.series.push([item.openPrice, item.closePrice, item.lowPrice, item.highPrice])
						if(this.currentSmall==0){
							this.categories.push(this.$u.timeFormat(item.startTime*5*60, 'hh:MM'))
						}else if(this.currentSmall==1){
							this.categories.push(this.$u.timeFormat(item.startTime*60*60, 'mm-dd hh:MM'))
						}else{
							this.categories.push(this.$u.timeFormat(item.startTime*60*60*24, 'mm-dd hh:MM'))
						}
						this.quantityList.push(item.quantity)
					})
					this.series.reverse()
					this.categories.reverse()
					this.quantityList.reverse()
					console.log(this.quantityList, 'quantityList');
				}
			},
			getTim(tim) {
				let date = new Date(tim)
				let year = date.getFullYear()
				let month = date.getMonth() + 1
				let day = date.getDate()
				let house = date.getHours()
				let minutes = date.getMinutes()
				if (minutes <= 0) {
					minutes = '0' + minutes
				}
				return `${year}/${month}/${day} ${house}:${minutes}`
			},
			async get() { //成交记录
				let res = await this.$api.java_getSeriesBuyRecord({
					ctId: this.ctid
				});
				if (res.status.code == 0) {
					this.list = res.result.seriesBuyRecordListVOS
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_ask_buy() {
				this.$Router.push({
					name: "askBuy",
					params: {
						id: this.id
					}
				})
			},
			change(index) {
				this.current = index
			},
			smallChange(index) {
				this.currentSmall = index
				this.currentSmallVal = this.tabsSmallList[index].value
				this.series = []
				this.categories = []
				this.priceKline()
			}
		},



	}
</script>

<style lang="scss" scoped>
	.showbox {
		width: 678rpx;
		height: 180rpx;
		background: rgba(70, 49, 79, 0.1);
		box-shadow: 0rpx 6rpx 24rpx 1rpx rgba(74, 75, 99, 0.1);
		border-radius: 24rpx;
		margin: 0 auto 50rpx auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 59rpx 0 30rpx;
		box-sizing: border-box;

		.green {
			color: #6CFF8A;

			>view:nth-child(2) {
				>view:nth-child(1) {
					border-top: 10rpx solid #6CFF8A;
					border-left: 10rpx solid transparent;
					border-right: 10rpx solid transparent;
				}
			}
		}

		.red {
			color: #EC4068;

			>view:nth-child(2) {
				>view:nth-child(1) {
					border-bottom: 10rpx solid #EC4068;
					border-left: 10rpx solid transparent;
					border-right: 10rpx solid transparent;
				}
			}
		}

		>view:nth-child(1) {
			>view:nth-child(1) {
				font-weight: 900;
				font-size: 48rpx;
			}

			>view:nth-child(2) {
				display: flex;
				align-items: center;
				font-weight: 400;
				font-size: 24rpx;
				margin-top: 10rpx;

				>view:nth-child(1) {

					margin: 0 10rpx 0 15rpx;
				}
			}
		}

		>view:nth-child(2) {
			display: flex;
			align-items: center;

			>view {
				text-align: center;
				margin-left: 60rpx;

				.jusy {
					font-weight: 400;
					font-size: 22rpx;
					color: var(--default-color3);
					margin-bottom: 13rpx;

					>view:nth-child(2) {
						font-weight: 400;
						font-size: 28rpx;
						color: #EC4068;
						margin-top: 5rpx;
					}
				}

			}
		}
	}

	.tabs {
		width: 100%;
		height: 70rpx;
		border-bottom: 1rpx solid #53505D;
		box-sizing: border-box;

	}

	.tabs_small {
		width: 320rpx;
		height: 80rpx;
		background: #25232E;
		box-shadow: 0rpx 6rpx 24rpx 1rpx rgba(74, 75, 99, 0.1);
		border-radius: 24rpx;
		overflow: hidden;
		margin: 50rpx 0 40rpx 32rpx;
	}

	.tit {
		font-weight: bold;
		font-size: 24rpx;
		color: #FFFFFF;
		margin: 0 0 40rpx 32rpx;
	}

	.body {
		padding: 20rpx;

		.list {
			.li {
				padding: 30rpx;
				border-radius: 13rpx;
				// background: #232222;
				margin-bottom: 20rpx;
				position: relative;
				display: flex;
				align-items: flex-start;

				.img {
					width: 82rpx;
					height: 82rpx;
					margin-right: 20rpx;

					image {
						width: 82rpx;
						height: 82rpx;
						border-radius: 50%;
					}
				}

				.font {
					width: 550rpx;
					display: flex;
					justify-content: space-between;
					border-bottom: 1rpx solid #53505D;
					padding-bottom: 33rpx;
					box-sizing: border-box;

					.title {
						color: #F4F4F4;
						font-size: 28rpx;
						margin-bottom: 20rpx;
					}

					.tid {
						color: #757575;
						font-size: 20rpx;
						margin-bottom: 20rpx;
					}

					.msg {
						color: #D7D7D7;
						font-size: 24rpx;

						.msg_view {
							margin-right: 14rpx;

							.yumin {
								width: 200rpx;
							}

							.tag {
								font-size: 18rpx;
								color: #D7D7D7;
								width: 60rpx;
								height: 30rpx;
								border-radius: 4rpx;
								background-color: #404040;
								line-height: 30rpx;
								text-align: center;
								margin-right: 10rpx;
							}
						}
					}
				}

				.price {
					color: #73F7EF;
					font-weight: 600;
					font-size: 28rpx;
					position: absolute;
					top: 40rpx;
					right: 40rpx;
				}
			}

			.msg_msg {
				text-align: center;
				font-size: 24rpx;
				color: #757575;
				margin-top: 87rpx;
			}
		}

		.null {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			margin: auto;

			image {
				width: 171rpx;
			}
		}
	}

	.footer_ask_buy {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20rpx 30rpx 40rpx 30rpx;

		// background-color:#121212;
		.but {
			width: 418rpx;
			border-radius: 40rpx;
		}
	}
</style>