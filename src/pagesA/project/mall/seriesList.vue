<template>
	<view class="main">
		<!-- <view class="top" :style="{'height':height+'rpx'}"></view> -->
		<view class="head_icon">
			<view class="left_icon" @tap="nav_back()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240304/bb92e20e90212f1c693f90df3df739e7_80x80.png"
					mode="widthFix"></image>
			</view>
			<view class="right_icon" @tap="nav_to('comingSoon')">
				<!-- <view class="img">
					<image @tap.stop="nav_imview()"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240614/05424a069ec31e6b09eb0a48eb48abb4_54x50.png"
						mode="widthFix"></image>
				</view> -->
				<image @tap.stop="submitOperationSelfSelection()"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240307/84051734b240743a3e44272b1f842675_120x120.png"
					mode="widthFix"></image>

				<view class="view_div">
					<image @tap.stop="nav_userSearch(info)"
						src="https://cdn-lingjing.nftcn.com.cn/image/20240304/c23ec1edafa8965a549d8b355d12dd03_60x60.png"
						mode="widthFix"></image>
					<text class="tag" v-if="info.userGoodsCount > 0">{{ info.userGoodsCount > 99 ? '99+' :
						info.userGoodsCount }}</text>
				</view>
				<image @tap.stop="nav_notice()"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240304/6fb56c31affa5479ce27aa89cf78923a_60x60.png"
					mode="widthFix"></image>
				<image @tap.stop="nav_History()"
					src="https://cdn-lingjing.nftcn.com.cn/image/20240304/19d05eabed8e6e9c3cc33bf90b746d0b_60x60.png"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="head_cart">
			<view class="head_view" :style="{ 'backgroundImage': `url(${info.cover.src})` }">
				<view class="bg_view">
					<view class="tx">
						<view class="tx_view">
							<image :src="info.cover.src" mode="aspectFill"></image>
							<view class="closed" v-if="info.marketClosureStatus == 1">暂休市</view>

						</view>
					</view>
					<view class="title">
						{{ info.title }}
					</view>
					<view class="data_cart">
						<view class="text">
							<view class="num">
								{{ info.createNum }}份
							</view>
							<span>总量</span>
						</view>
						<view class="text">
							<view class="num">
								{{ info.activeNum }}份
							</view>
							<span>流通</span>
						</view>
						<view class="text">
							<view class="num">
								￥{{ info.releasePrice }}
							</view>
							<span>发售价</span>
						</view>
						<view class="text">
							<view class="num">
								￥{{ info.onSaleMaxPrice ? info.onSaleMaxPrice : "一" }}
							</view>
							<span>限价</span>
						</view>
					</view>
				</view>

			</view>

			<view class="tabs" v-if="info.isOnceSale == 1">
				<u-tabs :list="tabList" :is-scroll="true" font-size='28' inactive-color='#ccc' active-color='#fff'
					:current="current" bg-color="#35333E" :bar-style='barStyle' @change="change"></u-tabs>
			</view>



			<view class="goods_ul" v-if="tabList[current].name == '当前寄售'">
				<view class="loading_list" v-show="isLoadingStatus == 0 && !show_login">
					<view>
						<view class="flex">
							<view class="balls"></view>
						</view>
						<view class="text">
							玩命加载中...
						</view>
					</view>
				</view>
				<view class="li" v-for="(item, index) in seriesList" @tap="nav_details(item)"
					v-show="seriesList != '' && isLoadingStatus == 1 && info.isOnceSale == 1">
					<view class="left_img">
						<image :src="`${item.cover.src}?x-oss-process=image/resize,m_lfit,h_100,w_100`"
							mode="aspectFill"></image>
					</view>
					<view class="right_font">
						<view class="font">
							<view class="title oneOver">
								{{ item.title }}

								<view>
									<view class="price"
										:style="{ marginRight: item.lockOrderStatus == 1 ? '180rpx' : '0' }">
										￥{{ item.price }}
									</view>
									<!--  -->
									<view class="tag" v-show="item.lockOrderStatus == 1">
										<view class="inside">支付中</view>

									</view>
								</view>
							</view>
							<text class="tid">
								Token ID: {{ item.tid }}
							</text>

						</view>
						<view class="arrow">
							<image src="../../../static/imgs/public/arrow-right.png" mode=""></image>
						</view>
					</view>
				</view>

				<view class="null_body" v-if="seriesList == '' && !show_login && isLoadingStatus == 2">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							暂无数据
						</view>
					</view>
				</view>
				<view class="null_body" v-if="show_login">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							您还未登录
						</view>
						<view class="nav_login" @tap="nav_login">
							登录/注册
						</view>
					</view>
				</view>
			</view>

			<!-- 竞价 -->
			<view class="goods_ul" v-if="tabList[current].name == '当前竞价'">
				<view class="loading_list" v-show="isLoadingStatus == 0 && !show_login">
					<view>
						<view class="flex">
							<view class="balls"></view>
						</view>
						<view class="text">
							玩命加载中...
						</view>
					</view>
				</view>
				<view class="box" v-show="list != '' && isLoadingStatus == 1">
					<view class="title_top">
						<text class="w184">出价人</text>
						<text class="w130">出价</text>
						<text class="w130">份数</text>
						<text class="w130">操作</text>

					</view>
					<view class="li" v-for="(item, index) in list" :key="index"
						v-show="list != '' && isLoadingStatus == 1 && info.isOnceSale == 1">
						<view class="left_img  whit_tex" style="width: 200rpx;">
							<image :src="item.cover" mode="aspectFill"></image>
							<text>{{ item.biddingUserName }}</text>
						</view>
						<view class="w130 price">￥{{ item.offerPrice }}</view>
						<view class="fs_text">{{ item.okNumber }}/{{ item.number }}份</view>
						<view class="w130 active_button" v-show="item.ownStatus == 1" @click="openSall(item)">卖给TA
						</view>
						<view class="w130 active_button noneBuy" v-show="item.ownStatus == 0">卖给TA</view>
					</view>
				</view>
				<view class="null_body" v-if="list.length == 0 && !show_login && isLoadingStatus == 2">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							暂无数据
						</view>
					</view>
				</view>
				<view class="null_body" v-if="show_login">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							您还未登录
						</view>
						<view class="nav_login" @tap="nav_login">
							登录/注册
						</view>
					</view>
				</view>


			</view>

			<!-- 委托 -->
			<view class="goods_ul" v-if="tabList[current].name == '当前委托'">
				<view class="loading_list" v-show="isLoadingStatus == 0 && !show_login">
					<view>
						<view class="flex">
							<view class="balls"></view>
						</view>
						<view class="text">
							玩命加载中...
						</view>
					</view>
				</view>
				<view class="Entrust_box" v-show="Entrulist != [] && isLoadingStatus == 1">
					<view class="li" v-for="(item, index) in Entrulist" :key="index">
						<view class="container">
							<!-- 左边 -->
							<view class="left-section">
								<text class="price">总价: ¥{{ Number(item.allNum * item.price).toFixed(0) }}</text>
								<text class="unit-price">单价: ¥{{ item.price }}</text>
							</view>

							<!-- 右边 -->
							<view class="right-section">
								<view class="ing">进行中</view>
								<view class="progress">
									<view class="inside">
										<text class="front">{{ item.getNum }}</text>
										/{{ item.allNum }}份
									</view>

								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="null_body" v-if="Entrulist.length == 0 && !show_login && isLoadingStatus == 2">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							暂无数据
						</view>
					</view>
				</view>
				<view class="null_body" v-if="show_login">
					<view class="null">
						<view class="img">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
								mode="widthFix"></image>
						</view>
						<view class="text">
							您还未登录
						</view>
						<view class="nav_login" @tap="nav_login">
							登录/注册
						</view>
					</view>
				</view>


			</view>


			<view class="footer_ask_buy" v-show="!show_login && tabList[current].name == '当前寄售'">
				<view class="but_left" v-show="info.isOnceSale == 0" @click='batchBuy'>批量购买</view>
				<view class="but_left" v-show="info.enableTargetBuy == 1 && info.isOnceSale != 0"
					:class="{ 'active': info.isSupportTargetBuy == 1 }" @click='nav_ask_buy'>批量下单</view>
				<view class="but" @click='checkSubmit'>{{ info.isOnceSale == 0 ? '立即购买' : '快捷下单' }}</view>
				<!-- <view class="but" @click='checkSubmit' v-if="info.isOnceSale==1">快捷下单</view> -->
			</view>
			<view class="footer_ask_buys" v-show="!show_login && tabList[current].name == '当前竞价'">
				<view class="but" @click="openMyBidding">我的竞价</view>
				<view class="but active" @click='showPrice = true'>立即竞价</view>
			</view>
			<view class="footer_ask_buys" v-show="!show_login && tabList[current].name == '当前委托'">
				<view class="but" @click="goEntru('me')">我的委托</view>
				<view class="but active" @click="goEntru('')">立即委托</view>
			</view>
			<u-modal v-model="isRegistration" border-radius="30" :show-title="false" :show-confirm-button="false">
				<view class="new-modal-content">
					<view class="title_bg">
						<view class="icon"></view>
						温馨提示
					</view>
					<view class="modal-content">
						<p>你还未实名认证</p>
						请前往实名认证
					</view>
					<view class="showModal-btn">
						<view class="img_cancel" @click="isRegistration = false">取消</view>
						<view class="img_reasale" @click="nav_realName()">前往实名</view>

					</view>
				</view>
			</u-modal>
			<u-popup v-model="meshow" class="popup-box" mode="bottom" width="500rpx" height="700rpx" border-radius="36">
				<view class="pop_body">
					<view class="head_title">
						我的竞价
						<view class="close_box" @click='meshow = false'><u-icon name="close" color="#ffffff"
								size="18"></u-icon>
						</view>
					</view>
					<view class="cont_view">
						<u-row gutter="20" class="title_tip">
							<u-col span="4" text-align="center">
								出价
							</u-col>
							<u-col span="4" text-align="center">
								份数
							</u-col>
							<u-col span="4" text-align="center">
								操作
							</u-col>
						</u-row>
						<scroll-view scroll-y="true" style="height:500rpx;" @scrolltolower="bottomOut">
							<u-row gutter="20" class="price_li" v-for="(item, index) in myList" :key="item.id">
								<u-col span="4" class="blue">
									<text class="pop_pl">￥{{ item.offerPrice }}</text>
								</u-col>
								<u-col span="4" class="pop_whit_tex" text-align="center">
									<text>{{ item.okNumber }}/{{ item.number }}份</text>
								</u-col>
								<u-col span="4" text-align="center" @click="revokeBidding(item.id)">
									<view class="button-quit ">撤销</view>
								</u-col>
							</u-row>
						</scroll-view>
					</view>
				</view>
			</u-popup>
			<u-popup v-model="showPrice" class="popup-box" mode="bottom" width="500rpx" height="528rpx"
				border-radius="36">
				<view class="pop_body">
					<view class="head_title">
						立即竞价
						<view class="close_box" @click='showPrice = false'><u-icon name="close" color="#ffffff"
								size="18"></u-icon></view>
					</view>
					<view class="money_box">
						<view class="money">
							<view class="left">
								<text>出价</text>
							</view>
							<view class="right">
								<text>￥</text>
								<u-input class="modal-resale-input" v-model="price" type="number"
									placeholder="请输入竞价金额" />
							</view>
						</view>
						<view class="money mb_40">
							<view class="left">
								<text>想要几份</text>
							</view>
							<view class="right">
								<u-input class="modal-resale-input" v-model="number" type="number"
									placeholder="请输入份数" />
							</view>
						</view>
						<view class="submit_login" @click="addPrice">
							确认出价
						</view>
					</view>
				</view>
			</u-popup>
			<u-popup v-model="isSell" class="popup-box" mode="bottom" width="500rpx" height="528rpx" border-radius="36">
				<view class="pop_body">
					<view class="head_title">
						卖给TA
						<view class="close_box" @click='isSell = false'><u-icon name="close" color="#ffffff"
								size="18"></u-icon></view>
					</view>
					<view class="money_box">
						<view class="money mb_40">
							<view class="left" style="width: 160rpx;">
								<text>卖给TA几份</text>
							</view>
							<view class="right">
								<u-input class="modal-resale-input" v-model="sellNumber" type="number"
									placeholder="请输入份数" />
							</view>
						</view>
						<view class="submit_login" @click="buyIts">
							确认出价
						</view>
					</view>
				</view>
			</u-popup>
			<pay-popup :popup-show.sync="isPasswordImport" :title="passwordTitle" :message="passwordMsg" order-type="O"
				@pay="finishPay" />
			<u-modal v-model="isShowMsg" border-radius="30" :content-style="bgObject" :show-title="false"
				:title-style="titleObject" :show-confirm-button="false">
				<view class="new-modal-content">
					<view class="modal-content">
						<view class="title">
							您尚未开通批量下单功能
						</view>
						<view class="msg_view">
							是否确认消耗{{ info.askBuyNum }}张《BV起飞卡》开通本功能?（功能有效期：30天）
						</view>
					</view>
					<view class="showModal-btn">
						<view class="img_cancel" @click="isShowMsg = false">取消</view>
						<view class="img_reasale" @click="submitAskBuy()">确认开通</view>
					</view>
				</view>
			</u-modal>
			<u-modal v-model="isError" border-radius="30" :content-style="bgObject" :show-title="false"
				:title-style="titleObject" :show-confirm-button="false" :mask-close-able="true">
				<view class="new-modal-content">
					<view class="modal-content">
						<view class="title">
							WHOOPS!
						</view>
						<view class="msg_view">
							您的仓库还没有{{ info.askBuyNum }}张《BV起飞卡》
							暂时无法开通本功能
						</view>
					</view>
					<view class="showModal-btn center">
						<view class="img_reasale" @click="nav_seriesList()">去购买</view>
					</view>
				</view>
			</u-modal>
			<u-modal v-model="isSueess" border-radius="30" :content-style="bgObject" :show-title="false"
				:title-style="titleObject" :show-confirm-button="false">
				<view class="new-modal-content">
					<view class="modal-content">
						<view class="title">
							恭喜您！
						</view>
						<view class="msg_view">
							您的批量下单功能已开通成功！
							<view class="active_msg">
								功能有效期至：{{ endTime }}
							</view>
						</view>
					</view>
					<view class="showModal-btn center">
						<view class="img_reasale" @click="isSueess = false">确认</view>
					</view>
				</view>
			</u-modal>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				{{ loaddingMsg }}
			</view>
		</u-modal>
		<u-popup v-model="isBatch" class="popup-box" mode="bottom" width="500rpx" height="428rpx" border-radius="36">
			<view class="pop_body">
				<view class="head_title">
					批量购买
					<view class="close_box" @click='isBatch = false'><u-icon name="close" color="#ffffff"
							size="18"></u-icon></view>
				</view>
				<view class="money_box">
					<view class="money mb_40">
						<view class="left" style="width:180rpx;">
							<text>需要购买几份</text>
						</view>
						<view class="right">
							<input class="modal-resale-input1" v-model="batchBuyNum" @input="onInputChange"
								type="number" placeholder="请输入需要购买几份" />
						</view>
					</view>
					<view class="submit_login" @click="submitBatch">
						确认购买
					</view>
				</view>
			</view>
		</u-popup>
		<!--批量购买popup -->
		<u-popup v-model="isBatchPopup" mode="center" border-radius="30" duration='300'>
			<view class="collect_card">
				<view class="collect_card_head">
					<image class="bg1"
						src='https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png'
						mode="">
					</image>
					<view class="title">恭喜您</view>
				</view>
				<view class="head">
					<view class="ver-center">
						<u-image src="@/static/imgs/public/checked.png" width="50" height="50"></u-image>
						<text style="margin-left: 20rpx;color:#fff;font-size:34rpx;font-weight:600 ;">购买成功</text>
					</view>
				</view>
				<view class="content">
					<view class="c-bottom ver-space-around">
						<view><text>系列名称:</text>{{ successInfo.title }}</view>
						<view><text>下单时间:</text>{{ successInfo.createTime }}</view>
						<view><text>购买数量:</text>{{ successInfo.buyNum }}份</view>
						<view class="price"><text>购买金额:</text>￥{{ successInfo.price }}</view>
					</view>
				</view>
				<view class="btn_view" @click="isBatchPopup = false">
					确认
				</view>
			</view>
		</u-popup>

		<!-- 30s后弹出关闭bit通知 -->
		<u-modal class="" v-model="showSetting" :maskCloseAble="false" width="600rpx" borderRadius="36"
			:show-title="false" :mask-close-able="true" :show-confirm-button="false">
			<view class="closenotice">
				<text class="bittitle">图片</text>
				<view class="bitline"></view>
				<text class="midcolor">是否接受"图片"板块相关公告提醒?</text>

				<!-- <text class="bitbody ">
        </text> -->

				<view class="bitbtn">
					<view @click="showSetting = false">不，下次</view>
					<view @click="gosettingnotice">好的，关注</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>
<script>
import api from '@/common/api/index.js';
import payPopup from "@/components/payPopup/index.vue";
import antiShake from "@/common/public.js"
export default {
	data() {
		return {
			timerSetting: null,
			showSetting: false,
			tabList: [{
				name: '当前寄售',
				value: ''
			}],
			barStyle: {
				'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
			},
			current: 0,
			info: {
				cover: {}
			},
			seriesList: [],
			list: [], //竞价
			ctid: "",
			show_login: false,
			isFooter: true, //没有更多了
			isRequest: false, //多次请求频繁拦截
			pageNum: 1,
			appUrl: "",
			isRegistration: false,
			meshow: false,
			showPrice: false,
			price: '', //竞价金额
			number: '', //竞价份数
			myList: [], //我的竞价
			isPasswordImport: false,
			passwordTitle: "确认竞价",
			passwordMsg: "请输入余额支付密码，用于竞价",
			isModelFooter: true,
			ModelPageNum: 1,
			isSell: false,
			sellNumber: "",
			biddingId: "",
			isLoadingStatus: 0,
			isShowMsg: false,
			titleObject: {
				'background-color': '#FFF',
				'color': '#FFFFFF'
			},
			bgObject: {
				'background-color': '#FFF',
				'color': '#FFFFFF'
			},
			isError: false,
			isSueess: false,
			endTime: "",
			isLoadding: false,
			isBatch: false, //批量购买
			batchBuyNum: 1,
			passType: 1, //1竞价  2批量购买
			loaddingMsg: '开通中...',
			isBatchPopup: false,
			successInfo: {},
			Entrulist: []
		}
	},
	components: {
		payPopup
	},
	onLoad(options) {


		//#ifdef APP-PLUS
		let isPushBv = uni.getStorageSync('isPushBv')
		if (isPushBv) {
			this.timerSetting = setTimeout(() => {
				this.showSetting = true; // 设置变量为 true，弹窗显示
			}, 30000); // 30秒
		}
		// #endif

		this.ctid = options.ctid
		let _this = this
		uni.getSystemInfo({
			success: function (res) {
				console.log('当前平台？？？？？', res)
				_this.height = res.statusBarHeight * 2
				console.log(_this.height)
			}
		});
		this.appUrl = getApp().globalData.urlZf
	},

	onShow() {
		this.seriesInfo()

	},
	onPullDownRefresh() {
		setTimeout(() => {
			this.isFooter = true
			this.isLoadingStatus = 0
			this.seriesInfo()
			uni.stopPullDownRefresh(); //停止下拉刷新动画
		}, 300);
	},
	onReachBottom() {
		if (this.isFooter) {
			if (this.isRequest == false) {
				if (this.tabList[this.current].name == '当前寄售') {
					this.seriesCollectionList()
				} else if (this.tabList[this.current].name == '当前委托') {
					this.fetchEntrust()
				} else {
					this.getListBidding()
				}

			} else {
				console.log("请求超时，已经拦截")
			}
		} else {
			console.log("已经到底了")
		}
	},

	methods: {
		async get_tag_list() {
			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform
			let res = await this.$api.tag_list({
				deviceToken,
				phoneType
			});
			if (res.status.code == 0) {
				let tags = res.result.tags
				if (tags) {
					let isPushBv = this.containsCharacter(tags, 'NO_COLLECTIBLES_TAG')
					let isPushBit = this.containsCharacter(tags, 'TRADE_PRICE_CHANGE_TAG')
					uni.setStorageSync('isPushBit', isPushBit)
					this.$forceUpdate()
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		containsCharacter(str, char) {
			// 使用 String.prototype.includes 方法检查字符串 str 是否包含字符 char
			return str.includes(char);
		},
		async gosettingnotice() {
			let token = uni.getStorageSync('token')
			if (!token) {
				this.$Router.push({
					name: 'mainLogin',
				})
				return
			}
			let isPushBv = uni.getStorageSync('isPushBv')

			let deviceToken = uni.getStorageSync('deviceToken')
			let phoneType = uni.getSystemInfoSync().platform

			let params2 = {
				deviceToken,
				operationType: 0,
				phoneType,
				tag: 'NO_COLLECTIBLES_TAG'
			}
			let res2 = await this.$api.subscribe(params2);

			if (res2.status.code == 0) {
				this.showSetting = false
				uni.showToast({
					title: '成功',
					icon: 'none',
					duration: 3000
				});
				// this.get_tag_list()
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		goEntru(e) {
			if (!e) {
				this.$Router.push({
					name: 'askBuyEntru',
					params: {
						ctid: this.ctid
					}
				})
			} else {
				this.$Router.push({
					name: 'MyEntru',
					params: {
						ctid: this.ctid
					}
				})
			}

		},
		async fetchEntrust(e) {
			const { status, result } = await this.$api.EntrustingList({
				ctid: this.ctid,
				pageNum: this.pageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {

					this.isLoadingStatus = 2
					this.isFooter = false
					if (this.Entrulist == '') {
						this.isLoadingStatus = 2
					}
				} else {

					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.Entrulist.push(item)
					})
				}
			} else {
				this.isLoadingStatus = 2
			}
			console.log(this.Entrulist, 1231231)
		},
		async getListBidding() { //当前竞价
			this.isRequest = true
			let {
				result,
				status
			} = await this.$api.list_bidding({
				ctid: this.ctid,
				pageNum: this.pageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.list == "") {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.list.push(item)
					})
				}
			}
			console.log(result, '当前竞价');
		},
		async addPrice() { //立即竞价
			if (!this.price) {
				return uni.showToast({
					title: '请输入竞价金额',
					icon: 'none'
				})
			}
			if (!this.number) {
				return uni.showToast({
					title: '请输入竞价份数',
					icon: 'none'
				})
			}
			this.passwordTitle = "确认竞价"
			this.passwordMsg = "请输入余额支付密码，用于竞价",
				this.passType = 1
			this.isPasswordImport = true
		},
		finishPay(e) {
			this.passwordImportPay = e
			this.isPasswordImport = false
			if (this.passType == 1) {
				this.submitAddPrice(e)
			} else {
				this.batchCreateItem(e)
			}

		},
		async submitAddPrice(password) {
			let {
				result,
				status
			} = await this.$api.add_bidding({
				ctid: this.ctid,
				number: this.number,
				offerPrice: this.price,
				bidingUid: '1',
				password
			})
			if (status.code == 0) {
				uni.showToast({
					title: '已发布竞价',
					icon: 'none'
				})
				this.list = []
				this.pageNum = 1
				this.showPrice = false
				this.getListBidding()
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none'
				})
			}
		},
		async revokeBidding(id) { //撤销竞价
			uni.showLoading({
				title: '撤销中...'
			})
			let {
				result,
				status
			} = await this.$api.revoke_bidding({
				biddingId: id
			})
			if (status.code == 0) {
				uni.showToast({
					title: '撤销成功',
					icon: 'none',
					duration: 3000
				})
				this.ModelPageNum = 1
				this.isModelFooter = true
				this.myList = []
				this.getMyBidding()
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none'
				})
			}
		},
		async getMyBidding() { //我的竞价
			let {
				result,
				status
			} = await this.$api.my_bidding({
				ctid: this.ctid,
				pageNum: this.ModelPageNum,
				pageSize: 10
			})
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isModelFooter = false
				} else {
					this.ModelPageNum++
					result.list.forEach((item) => {
						this.myList.push(item)
					})
				}
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none'
				})
			}
			console.log(result, '我的竞价');
		},
		async buyIts() { //卖给他
			if (!this.$u.test.digits(this.sellNumber)) {
				uni.showToast({
					title: '请输入整数',
					icon: 'none'
				})
				return false
			}
			uni.showLoading({
				title: '正在卖给TA中'
			});
			let {
				result,
				status
			} = await this.$api.sell({
				biddingId: this.biddingId,
				seriesId: this.ctid,
				sellNumber: this.sellNumber
			})
			if (status.code === 0) {
				uni.hideLoading();
				this.isSell = false
				this.sellNumber = ""
				uni.showToast({
					title: '售卖成功',
					icon: 'none'
				})
				this.list = []
				this.pageNum = 1
				this.getListBidding()
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
			console.log(result, 'MaiGeiTa');
		},
		bottomOut() {
			if (this.isModelFooter) {
				this.getMyBidding()
			} else {
				console.log("Model已经到底了")
			}
		},
		change(index) {
			this.current = index
			this.isFooter = true
			this.isLoadingStatus = 0
			if (this.tabList[index].name == '当前寄售') {

				this.pageNum = 1
				this.seriesList = []
				this.seriesCollectionList()
			} else if (this.tabList[index].name == '当前委托') {

				this.Entrulist = []
				this.pageNum = 1
				this.fetchEntrust()
			} else {
				this.pageNum = 1
				this.list = []
				this.getListBidding()
			}
		},
		search() {
			this.pageNum = 1
		},
		clear() {
			this.title = ""
			this.pageNum = 1
		},
		nav_search() {
			this.pageNum = 1
			this.type = null
			this.getList()
		},
		nav_back() {
			this.$Router.back()
		},
		nav_to(name) {
			this.$Router.push({
				name
			})
		},
		async seriesInfo() {
			uni.showLoading({
				title: '加载中...'
			});
			let res = await this.$api.java_marketRecommendTabInfo({
				ctid: this.ctid
			});
			if (res.status.code == 0) {
				uni.hideLoading();
				this.info = res.result
				if (this.tabList[this.current].name == '当前寄售') {
					this.pageNum = 1
					this.seriesList = []
					this.seriesCollectionList()
				} else if (this.tabList[this.current].name == '当前委托') {
					this.pageNum = 1
					this.Entrulist = []
					this.fetchEntrust()
				} else {
					this.pageNum = 1
					this.list = []
					this.getListBidding()
				}
				// #ifdef APP
				if (uni.getSystemInfoSync().platform == 'ios') {
					this.get_version()
				} else {
					if (this.info.biddingStatus == 1 && this.info.entrustStatus != 1) {
						this.tabList = [{
							name: '当前寄售',
							value: ''
						}, {
							name: '当前竞价',
							value: ''
						}]
					}
					if (this.info.biddingStatus != 1 && this.info.entrustStatus == 1) {
						this.tabList = [{
							name: '当前寄售',
							value: ''
						}, {
							name: '当前委托',
							value: ''
						}]
					}
					if (this.info.biddingStatus == 1 && this.info.entrustStatus == 1) {
						this.tabList = [{
							name: '当前寄售',
							value: ''
						}, {
							name: '当前竞价',
							value: ''
						}, {
							name: '当前委托',
							value: ''
						}]
					}
				}
				// #endif
				// #ifdef H5

				if (this.info.biddingStatus == 1 && this.info.entrustStatus != 1) {
					this.tabList = [{
						name: '当前寄售',
						value: ''
					}, {
						name: '当前竞价',
						value: ''
					}]
				}
				if (this.info.biddingStatus != 1 && this.info.entrustStatus == 1) {
					this.tabList = [{
						name: '当前寄售',
						value: ''
					}, {
						name: '当前委托',
						value: ''
					}]
				}
				if (this.info.biddingStatus == 1 && this.info.entrustStatus == 1) {
					this.tabList = [{
						name: '当前寄售',
						value: ''
					}, {
						name: '当前竞价',
						value: ''
					}, {
						name: '当前委托',
						value: ''
					}]
				}
				// #endif
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		async seriesCollectionList() {
			this.isRequest = true
			const {
				status,
				result
			} = await this.$api.java_marketRecommendGoodsList({
				ctid: this.ctid,
				pageNum: this.pageNum,
				pageSize: 10
			});
			if (status.code == 0) {
				this.isRequest = false
				if (result.list == null || result.list == "") {
					this.isFooter = false
					if (this.seriesList == "") {
						this.isLoadingStatus = 2
					}
				} else {
					this.isLoadingStatus = 1
					this.pageNum++
					result.list.forEach((item) => {
						this.seriesList.push(item)
					})
				}
			} else if (status.code == 1002) {
				this.show_login = true
			} else {
				uni.showToast({
					title: status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_details(item) {
			if (item.tid == '请点击右下方“快捷下单”购买首发藏品') {

			} else {
				this.$Router.push({
					name: "detailsShop",
					params: {
						tid: item.tid
					}
				})
			}
		},
		nav_login() {
			this.$Router.push({
				name: 'mainLogin',
			})
		},
		nav_realName() {
			this.$Router.push({
				name: "realName"
			})
		},
		checkSubmit: antiShake._debounce(function () {
			this.submitOrder()
		}, 200),
		async submitOrder() {
			this.isSubmit = true;
			let res = await this.$api.java_create_item({
				paymentScene: 1,
				ctid: this.ctid
			});
			setTimeout(() => {
				this.isSubmit = false;
			}, 10000)
			if (res.status.code == 0) {
				uni.showToast({
					title: "下单成功~",
					icon: "none",
					duration: 3000,
				});
				this.isSubmit = false;
				console.log(res.result.orderNo)
				// #ifdef APP
				let url = `${this.appUrl}pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
				console.log(url)
				this.$Router.push({
					name: "webView",
					params: {
						url,
					}
				})
				// #endif
				// #ifdef H5
				let {
					origin
				} = window.location
				window.location.href =
					`${origin}/orderView/#/pagesA/project/order/zfOrder?orderId=${res.result.orderNo}`
				// #endif
			} else if (res.status.code == 1002) {
				this.nav_login()
			} else if (res.status.code == 502) {
				this.isSubmit = false;
				uni.showToast({
					title: res.msg,
					icon: "none",
					duration: 3000,
				});
			} else if (res.status.code == 504) {
				this.isSubmit = false;
				this.isWarning = true;
				this.isWarningText = res.msg;
			} else if (res.status.code == 510) {
				this.isSubmit = false;
				this.isRegistration = true;
				this.RegistrationText = res.msg;
			} else if (res.status.code == 511) {
				this.isSubmit = false;
				this.isPayError = true;
				this.isPayErrorText = res.status.msg
			} else {
				this.isSubmit = false;
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 3000,
				});
				// this.$refs.captcha.refresh()
			}
		},
		async submitOperationSelfSelection() {
			let res = await this.$api.java_operationSelfSelection({
				type: 0,
				ctid: this.ctid
			});
			if (res.status.code == 0) {
				uni.showToast({
					title: '自选添加成功',
					icon: 'none',
					duration: 3000
				});
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_ask_buy() {
			if (this.info.isSupportTargetBuy == 1) {
				this.$Router.push({
					name: "askBuy",
					params: {
						ctid: this.info.ctid
					}
				})
			} else {
				this.isShowMsg = true
			}
		},
		async submitAskBuy() {
			this.isShowMsg = false
			this.isLoadding = true
			this.loaddingMsg = '开通中...'
			let res = await this.$api.createRecord({});
			if (res.status.code == 0) {
				this.isSueess = true
				this.endTime = res.result
				this.info.isSupportTargetBuy = 1
				this.isLoadding = false
			} else if (res.status.code == 9998) {
				this.isLoadding = false
				this.isError = true
			} else {
				this.isLoadding = false
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		nav_History() {
			this.$Router.push({
				name: "choicenessHistory",
				params: {
					ctid: this.info.ctid
				}
			})
		},
		nav_notice() {
			this.$Router.push({
				name: "official",
				params: {
					title: this.info.title
				}
			})
		},
		nav_imview() {
			console.log(11)
			this.$Router.pushTab({
				name: "im"
			})
		},
		nav_userSearch(info) {
			if (info.userGoodsCount > 0) {
				this.$Router.push({
					name: "userSearch",
					params: {
						keyword: info.title,
						ctid: info.ctid
					}
				})
			}
		},
		openSall(item) {
			this.biddingId = item.biddingId
			this.isSell = true
		},
		openMyBidding() {
			this.ModelPageNum = 1
			this.meshow = true
			this.myList = []
			this.getMyBidding()
		},
		nav_seriesList() {
			this.isError = false
			this.$Router.push({
				name: 'seriesList',
				params: {
					ctid: 'cs14729210152792764673170846406530'
				}
			})
		},
		async get_version() {
			let res = await this.$api.java_commonconfigInfo({
				name: 'ios_apple_pay_version',
			});
			if (res.status.code == 0) {
				let curV = uni.getSystemInfoSync().appVersion
				let reqV = res.result.value
				console.log(curV)
				console.log(reqV)
				if (curV == reqV) {
					this.tabList = [{
						name: '当前寄售',
						value: ''
					}]
				} else {
					if (this.info.biddingStatus == 1) {
						this.tabList = [{
							name: '当前寄售',
							value: ''
						}, {
							name: '当前竞价',
							value: ''
						}]
					}
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		batchBuy() {
			this.isBatch = true
		},
		submitBatch() {
			if (this.batchBuyNum == 0) {
				uni.showToast({
					title: '需要购买的份数不能位0',
					icon: 'none',
					duration: 3000
				});
				return
			}
			if (this.batchBuyNum > 100) {
				uni.showToast({
					title: '需要购买的份数不能大于100',
					icon: 'none',
					duration: 3000
				});
				this.batchBuyNum = 1
				return
			}
			this.passType = 2
			this.isBatch = false
			this.isPasswordImport = true
			this.passwordTitle = "批量购买"
			this.passwordMsg = "请输入余额支付密码，用于批量购买"
		},
		async batchCreateItem(tradePassword) {
			this.isLoadding = true
			this.loaddingMsg = '购买中...'
			let res = await this.$api.java_create_item({
				paymentScene: 1,
				ctid: this.ctid,
				batchBuyNum: this.batchBuyNum,
				tradePassword
			});
			if (res.status.code == 0) {
				this.isLoadding = false
				this.isBatchPopup = true
				this.successInfo = res.result
				this.info.userGoodsCount += res.result.buyNum
				this.batchBuyNum = 1
			} else {
				this.isLoadding = false
				this.batchBuyNum = 1
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		onInputChange(event) {
			// 只允许输入整数，并且保留整数部分
			let value = event.target.value;
			if (value.includes('.')) {
				value = value.split('.')[0];
			}
			value = value.replace(/[^0-9]/g, '') || "";
			this.$nextTick(() => {
				this.batchBuyNum = value
			})
		},

	}
}
</script>
<style lang="scss">
.tabs {
	width: fit-content;
	margin: 10rpx auto;
	color: #fff;
}

.closenotice {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	// height: 300rpx;
	margin: 48rpx 0 62rpx 0;

	.bittitle {
		background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20240909/d9d2812e11a698835cbe18445017a526_648x16.png");
		background-size: 100% 100%;
		width: 226rpx;
		height: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #ffffff;

		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: bold;
		font-size: 28rpx;
	}

	.bitline {
		margin: 50rpx 0 0rpx 0;
		height: 1rpx;
		width: 100%;
		background: #53505d;
	}

	.midcolor {
		text-align: left;

		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 26rpx;
		// line-height: 44rpx;
		text-align: left;
		color: #63eaee;
		margin: 41rpx 0 58rpx 0;

	}

	.bitbody {
		// margin: 29rpx 34rpx;
		font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
		font-weight: 400;
		font-size: 26rpx;
		// color: #ffffff;
		line-height: 44rpx;
		text-align: left;


	}

	.bitbtn {
		display: flex;
		width: 100%;
		padding: 0 40rpx;
		justify-content: space-between;
		align-items: center;

		view {
			&:nth-of-type(1) {
				width: 220rpx;
				height: 70rpx;
				line-height: 70rpx;
				text-align: center;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50rpx;
				border: 1rpx solid #ffffff;
				font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #ffffff;
			}

			&:nth-of-type(2) {
				width: 220rpx;
				height: 70rpx;
				line-height: 70rpx;
				text-align: center;
				background: linear-gradient(90deg, #ef91fb 0%, #40f8ec 100%);
				border-radius: 40rpx;
				font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #141414;
			}
		}
	}

	text {
		font-weight: bold;
		font-size: 28rpx;
		color: #ffffff;
	}

	.charge {
		margin-top: 61rpx;
		width: 300rpx;
		height: 80rpx;
		background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
		border-radius: 40rpx;
		text-align: center;
		line-height: 80rpx;
		font-weight: bold;
		font-size: 24rpx;
		color: #141414;
	}
}

.padding_lr {
	padding: 0rpx 35rpx;
}

.main {
	flex: 1;
	position: relative;

	.head_icon {
		position: absolute;
		/* #ifdef APP */
		top: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5 */
		top: 50rpx;
		/* #endif */
		left: 36rpx;
		right: 36rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		z-index: 99;
		width: 638rpx;

		.left_icon {
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 70rpx;
			}
		}

		.right_icon {
			display: flex;
			justify-content: center;
			align-items: center;

			.view_div {
				position: relative;

				.tag {
					position: absolute;
					right: -10rpx;
					width: 40rpx;
					top: -10rpx;
					font-size: 22rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					text-align: center;
					background-color: #EC4068;
					width: 45rpx;
					height: 45rpx;
					border-radius: 50%;
				}
			}

			image {
				width: 70rpx;
				margin-left: 30rpx;
			}

			.img {
				padding: 10rpx;

				image {
					width: 56rpx;
					margin-left: 0rpx;
				}
			}
		}
	}

	.head_cart {
		.head_view {
			color: var(--default-color1);
			background-color: var(--default-color1);
			padding-bottom: 30rpx;
			height: 700rpx;
			background-size: 100% 100%;
			position: relative;

			.bg_view {
				width: 100%;
				height: 700rpx;
				background-color: rgba(0, 0, 0, 0.3);
				position: absolute;
				left: 0;
				top: 0;
				padding-top: 250rpx;
			}

			.tx {
				.tx_view {
					position: relative;
					width: 180rpx;
					height: 180rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 30rpx;
					margin: 0 auto;

					image {
						width: 180rpx;
						height: 180rpx;
						border-radius: 30rpx;
					}

					.closed {
						position: absolute;
						bottom: 0;
						/* right: 65rpx; */
						width: 111rpx;
						height: 33rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						background: #ffffff;
						border-radius: 17rpx;
						font-family: HarmonyOS Sans SC;
						font-weight: 400;
						font-size: 22rpx;
						color: #000000;
					}
				}
			}

			.title {
				text-align: center;
				font-size: 24rpx;
				margin: 30rpx 0rpx 40rpx 0rpx;
				font-weight: 600;
			}

			.data_cart {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 638rpx;
				height: 140rpx;
				background: var(--main-bg-color);
				box-shadow: 0px 6rpx 24rpx 1rpx rgba(74, 75, 99, 0.1);
				border-radius: 24rpx;
				margin: 0 auto;

				.text {
					width: 25%;
					text-align: center;

					.num {
						font-size: 28rpx;
						font-weight: 600;
					}

					span {
						font-size: 24rpx;
						color: #A6A6A6;
					}
				}

			}
		}
	}

	.goods_ul {
		width: 638rpx;
		margin: 0 auto;
		padding-bottom: 100rpx;

		.li {
			display: flex;
			justify-content: flex-start;
			padding-top: 15rpx;

			.left_img {
				width: 90rpx;
				height: 90rpx;
				margin-right: 16rpx;

				image {
					width: 90rpx;
					height: 90rpx;
					border-radius: 10rpx;
				}

				text {
					width: 150rpx;
					text-overflow: ellipsis;
					white-space: normal;
					overflow: hidden;
				}
			}

			.right_font {
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				align-items: center;
				width: 520rpx;
				/* height: 2px;
background: #F3FEFF;
opacity: 0.1; */
				border-bottom: 2rpx solid rgba(243, 254, 255, .1);
				padding-bottom: 17rpx;

				.font {
					margin-top: 14rpx;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: center;
					width: 520rpx;
				}

				.title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 26rpx;
					font-weight: 400;
					position: relative;
					padding-right: 100rpx;
					width: 100%;
					padding: 8rpx 0;
					// height: 42rpx;
					margin-left: 5rpx;
					color: var(--default-color1);

					.tag {
						width: 124rpx;
						height: 44rpx;
						border-radius: 22rpx;

						/* border: 1rpx solid #63EAEE; */
						color: #63EAEE;
						font-size: 22rpx;
						line-height: 40rpx;
						text-align: center;
						position: absolute;
						background: linear-gradient(0deg, #EF91FB, #40F8EC);

						display: flex;
						justify-content: center;
						align-items: center;
						right: 0rpx;
						top: -3rpx;
						/* background-color: var(--main-bg-color); */

						.inside {
							border-radius: 22rpx;
							height: 40rpx;

							font-weight: 400;
							font-size: 22rpx;
							color: #63EAEE;
							width: 122rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							font-family: PingFang SC;
							line-height: 45rpx;
							/* height: 84rpx; */
							background: #36343F;
						}
					}
				}

				.tid {
					/* margin-top: 21rpx; */
					font-weight: 300;
					font-size: 15rpx;
					color: #FFFFFF;
					opacity: 0.5;
				}

				.price {
					font-size: 27rpx;
					font-weight: 600;
					color: var(--active-color1);
				}

				.arrow {
					width: 100%;
					display: flex;
					justify-content: flex-end;

					image {
						width: 28rpx;
						height: 28rpx;
					}
				}
			}
		}
	}
}

.null_body {
	.null {

		.img {
			display: flex;
			justify-content: center;

			image {
				width: 242rpx;
			}
		}

	}

	.text {
		color: #A6A6A6;
		font-size: 28rpx;
		text-align: center;
		margin-top: 30rpx;
	}

	.nav_login {
		width: 300rpx;
		height: 90rpx;
		background: var(--main-bg-color)FFF;
		border-radius: 24rpx;
		border: 2rpx solid #63EAEE;
		color: #63EAEE;
		font-size: 28rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 40rpx;
		font-weight: 600;
	}

	width:100%;
	height: 40vh;
	display: flex;
	justify-content: center;
	align-items: center;
}

.footer_ask_buy {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 20rpx 30rpx 40rpx 30rpx;
	display: flex;
	justify-content: center;

	.but_left {
		width: 300rpx;
		height: 80rpx;
		border-radius: 24rpx;
		text-align: center;
		line-height: 80rpx;
		background-color: var(--main-bg-color);
		color: #FFFFFF;
		font-size: 28rpx;
		margin-right: 50rpx;
		border: 1px solid #FFFFFF;

		&.active {
			background: var(--primary-button-color);
			border: none;
			color: var(--default-color2);
			margin-right: 50rpx;
		}
	}

	.but {
		width: 300rpx;
		height: 80rpx;
		border-radius: 24rpx;
		text-align: center;
		line-height: 80rpx;
		border: 2rpx solid #fff;
		background-color: var(--main-bg-color);
		color: #fff;
		font-size: 28rpx;

		&.active {
			background: var(--primary-button-color);
			border: none;
			color: var(--default-color2);
			margin-right: 50rpx;
		}
	}
}

.new-modal-content {
	padding: 35rpx 40rpx;

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 160rpx;
			height: 160rpx;
		}
	}

	.modal-content {
		padding: 0rpx 0rpx 30rpx 0rpx;
		border-bottom: 1rpx solid #EDEDED;
		font-size: 28rpx;
		text-align: center;
	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 226rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 14rpx;
			color: #141414;
		}

		.img_cancel {
			border: 1px solid #141414;
		}

		.img_reasale {
			background-color: #63EAEE;
			color: #141414;
		}
	}
}

.title_bg {
	font-size: 34rpx;
	font-weight: 600;
	width: 240rpx;
	position: relative;
	text-align: center;
	margin: 0 auto;
	margin-bottom: 10rpx;
	color: #141414;

	.icon {
		position: absolute;
		left: 0rpx;
		top: 20rpx;
		width: 240rpx;
		height: 8rpx;
		background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
		background-size: 100%;
	}
}

.goods_ul {
	line-height: 31rpx;

	.tip_blue {
		height: 92rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		font-weight: 400;
		color: var(--active-color1);
		border-bottom: 1px solid rgba(83, 80, 93, 1);
	}

	.Entrust_box {
		.li {
			height: 80 rpx;
			margin-bottom: 5rpx;
			padding: 30rpx 24rpx 28rpx 30rpx;
			background: #25232D;
			border-radius: 25rpx;

			/* 容器样式 */
			.container {
				position: relative;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				//justify-content: space-between;
				/* padding: 10rpx; */
				color: #fff;
			}

			/* 左边部分 */
			.left-section {
				font-weight: 400;
				font-size: 27rpx;
				color: #FFFFFF;
				display: flex;
				align-items: center;

				.price {
					/* width: 200rpx; */
				}

				.unit-price {
					margin-left: 55rpx;
					/* width: 162rpx; */

					white-space: nowrap;
					/* 防止文字折行 */
				}

			}


			/* 右边部分 */
			.right-section {
				position: absolute;
				right: 21rpx;
				bottom: -14rpx;
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				.ing {
					margin-right: 6rpx;
					font-weight: 400;
					font-size: 17rpx;
					color: #FFFFFF;
					opacity: 0.5;
				}
			}

			.progress {
				margin-top: 10rpx;
				/* padding: 7rpx 12rpx 6rpx 13rpx; */

				.front {
					color: #40F8EC;
				}

				display: flex;
				justify-content: center;
				align-items: center;
				width: 83rpx;
				height: 29rpx;
				background: #35333E;
				border-radius: 22rpx;

				font-weight: 400;
				font-size: 16rpx;
				color: #fff;


				background: linear-gradient(0deg, #EF91FB, #40F8EC);
				overflow: hidden;
				/* .tag {
						width: 124rpx;
						height: 44rpx;
						border-radius: 22rpx;

						/* border: 1rpx solid #63EAEE; */
				/* color: #63EAEE;
				font-size: 22rpx;
				line-height: 40rpx;
				text-align: center;
				position: absolute;


				right: 0rpx;
				top: -5rpx; */

				/* background-color: var(--main-bg-color); */

				.inside {
					/* padding: 7rpx 12rpx 6rpx 13rpx; */
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 22rpx;
					height: 25rpx;
					width: 80rpx;

					font-weight: 400;
					/* font-size: 22rpx; */
					font-size: 19rpx;

					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					font-family: PingFang SC;
					/* line-height: 45rpx; */
					/* height: 84rpx; */
					background: #36343F;
				}

				/* } */
			}

		}
	}


	.box {
		width: 678rpx;
		margin: 0 auto;

		.w184 {
			width: 194rpx;
		}

		.w130 {
			width: 124rpx;
		}

		.title_top {
			margin: 15rpx auto;
			text-align: center;
			opacity: 0.5;
			font-size: 24rpx;
			font-weight: 400;
			color: var(--default-color1);
			display: flex;
			gap: 50rpx;
			justify-content: space-around;
		}

		.li {
			text-align: left;
			display: flex;
			gap: 50rpx;
			align-items: center;
			justify-content: space-around;
			margin-bottom: 40rpx;

			.whit_tex {
				font-size: 24rpx;
				font-weight: 400;
				color: var(--default-color1);
			}

			.fs_text {
				text-align: left;
				min-width: 120rpx;
				font-size: 24rpx;
				font-weight: 400;
				color: var(--default-color1);
			}

			.left_img {
				height: 60rpx;
				display: flex;
				align-items: center;

				image {
					width: 60rpx;
					height: 60rpx;
					border-radius: 18rpx;
					border-radius: 10rpx;
					margin-right: 12rpx;
				}
			}

			.price {
				font-size: 24rpx;
				font-weight: 400;
				color: var(--active-color1);
			}

			.noneBuy {
				border: 1rpx solid #ccc !important;
				color: #ccc !important;
				opacity: .6;
			}

			.active_button {
				width: 120rpx;
				height: 48rpx;
				border-radius: 12rpx;
				background: rgba(53, 51, 62, 1);
				border: 1px solid rgba(255, 255, 255, 1);
				font-size: 22rpx;
				font-weight: 400;
				display: flex;
				align-items: center;
				justify-content: center;
				color: var(--default-color1);

			}
		}
	}
}

.footer_ask_buys {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 20rpx 30rpx 40rpx 30rpx;
	display: flex;
	justify-content: center;

	.but {
		width: 300rpx;
		height: 80rpx;
		border-radius: 24rpx;
		text-align: center;
		line-height: 80rpx;
		border: 2rpx solid #fff;
		background-color: var(--main-bg-color);
		color: #fff;
		font-size: 28rpx;

		&.active {
			background: var(--primary-button-color);
			border: none;
			color: var(--default-color2);
			margin-left: 50rpx;
		}
	}
}

.popup-box::v-deep {
	.u-drawer__scroll-view {
		background-color: var(--login-bg-color);
	}
}

uni-view .pop_body {
	color: var(--default-color1);

	.head_title {
		height: 98rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid rgba(83, 80, 93, 1);

		.close_box {
			display: flex;
			align-items: center;
			justify-content: center;
			position: absolute;
			top: 0;
			right: 0;
			width: 80rpx;
			height: 80rpx;
			border-radius: 0px 36rpx 0px 36rpx;
			background: rgba(255, 255, 255, 0.2);
		}
	}

	.cont_view {
		.title_tip {
			margin: 30rpx auto;
			opacity: 0.5;
			font-size: 24rpx;
			font-weight: 400;
			color: rgba(255, 255, 255, 1);
		}

		.price_li {
			margin-bottom: 30rpx;

			.pop_pl {
				padding-left: 60rpx;
			}

			.blue {
				color: var(--active-color1);
				font-size: 28rpx;
				font-weight: 400;
			}

			.button-quit {
				font-size: 22rpx;
				font-weight: 400;
				color: var(--default-color1);
				display: flex;
				align-items: center;
				justify-content: center;
				width: 120rpx;
				height: 48rpx;
				border-radius: 12rpx;
				background: var(--login-bg-color);
				border: 1px solid rgba(255, 255, 255, 1);
				margin: 0 auto;
			}

			.pop_whit_tex {
				font-size: 28rpx;
				font-weight: 400;
				color: var(--default-color1);
			}


		}
	}

	.money_box {
		margin-top: 50rpx;

		.mb_40 {
			margin-top: 40rpx;
		}

		.money {
			display: flex;
			justify-content: center;
			align-items: center;

			.left {
				width: 120rpx;
				margin-right: 40rpx;
				text-align: right;

				text {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 37rpx;
					color: rgba(255, 255, 255, 1);
					text-align: right;
				}

			}

			.right {
				text {
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 37rpx;
					color: var(--active-color1) !important;
					// padding-right: 4rpx;
				}

				width: 320rpx;
				height: 80rpx;
				border-radius: 50rpx;
				background: rgba(70, 69, 79, 1);
				display: flex;
				justify-content: center;
				align-items: center;
				padding:0 40rpx 0 30rpx;

				input {
					width: 180rpx;
					font-size: 28rpx;
					font-weight: 400;
					letter-spacing: 0px;
					line-height: 37rpx;
					color: rgba(166, 166, 166, 1);
				}
			}
		}

		.submit_login {
			width: 560rpx;
			height: 100rpx;
			background: var(--primary-button-color);
			border-radius: 60rpx;
			font-size: 34rpx;
			color: #141414;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 50rpx auto 0rpx auto;

		}
	}
}

.modal-resale-input::v-deep {
	.u-input__input {
		color: var(--active-color1) !important;
		text-align: center;
	}

	.uni-input-placeholder {
		font-size: 28rpx !important;
	}
}

.loading_list {
	height: 30vh;
}

.new-modal-content {
	padding: 35rpx 40rpx;
	background: var(--main-bg-color);

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 160rpx;
			height: 160rpx;
		}
	}

	.modal-content {
		padding: 0rpx 0rpx 30rpx 0rpx;
		border-bottom: 1rpx solid #53505D;
		font-size: 28rpx;
		color: #fff;

		.title {
			font-size: 34rpx;
			font-weight: 600;
		}

		.msg_view {
			font-size: 28rpx;
			line-height: 34rpx;
			margin-top: 30rpx;

			.active_msg {
				font-size: 24rpx;
				color: #63EAEE;
				margin-top: 10rpx;
			}
		}

	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 236rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 14rpx;
			color: rgba(255, 255, 255, 0.5);
		}

		.img_cancel {
			border: 1px solid rgba(255, 255, 255, 0.5);
		}

		.img_reasale {
			color: var(--default-color2);
			background: var(--primary-button-color);
		}

		&.center {
			justify-content: center;

			.img_reasale {
				width: 300rpx;
			}
		}
	}
}

.collect_card {
	width: 630rpx;
	min-height: 506rpx;
	background: #35333E;
	border-radius: 30rpx;
	font-size: 32rpx;
	color: #000000;
	font-style: normal;
	text-transform: none;
	padding: 0rpx 40rpx 40rpx 40rpx;

	.collect_card_head {
		width: 100%;
		height: 80rpx;
		position: relative;

		.bg1 {
			width: 250rpx;
			height: 8rpx;
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			margin: auto;
		}

		.title {
			text-align: center;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #fff;
			font-style: normal;
			text-transform: none;
		}
	}

	.head {
		padding: 20rpx 0 40rpx 0;
		text-align: center;
		font-size: 34rpx;
		color: var(--message-box-point-color);
		font-size: 32rpx;

		// background-color: #333;
		.ver-center {
			display: flex;
			// flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}

	.content {
		margin: 0 auto 32rpx;
		padding: 10rpx 24rpx;
		height: 260rpx;
		border-radius: 4rpx;

		.c-bottom {
			height: 232rpx;
			font-size: 28rpx;
			color: #fff;

			.price {
				color: #63EAEE;
			}

			color: #fff;

			text {
				margin-right: 40rpx;
				vertical-align: top;
				color: #A6A6A6;
			}

			.btn {
				width: 622rpx;
				height: 68rpx;
				margin-top: 32rpx;
				border-radius: 2rpx;
				font-size: 28rpx;
			}
		}

		&.c-content {
			height: 328rpx;
			padding: 48rpx 24rpx;
		}

		.seccess-content {
			text-align: center;
			font-size: 32Rpx;
			font-weight: 500;
			color: var(--main-front-color);

			.u-image {
				margin: 100rpx auto 24rpx;
			}
		}

	}

}

.ver-space-around {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

.btn_view {
	border-radius: 12rpx;
	border: 1px solid #fff;
	height: 80rpx;
	line-height: 80rpx;
	color: #fff;
	width: 240rpx;
	text-align: center;
	width: 100%;
	font-size: 28rpx;
}

.modal-resale-input1 {
	color: var(--active-color1) !important;
	text-align: center;
	width: 260rpx !important;
}

.modal-resale-input1::placeholder {
	font-size: 28rpx;
}
</style>