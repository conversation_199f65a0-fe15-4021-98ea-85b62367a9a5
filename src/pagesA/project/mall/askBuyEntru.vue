<template>
	<view class="">
		<u-navbar class="list-nav" title="立即委托" title-color="#fff"  back-icon-color="#fff" :border-bottom="false"
			back-icon-name="https://cdn-lingjing.nftcn.com.cn/image/20220613/b81fa4236e814e3b7cfeb8c8698568d3_48x48.png"
			:custom-back="custom" :background="{ backgroundColor: 'var(--main-bg-color)' }">
		</u-navbar>
		<view class="content">
			<view class="shop_cart">
				<view class="img">
					<image :src="list.cover.src" mode="aspectFill"></image>
				</view>
				<view class="info">
					<view class="title">
						{{list.title}}
					</view>
					<view class="msg">
						<view class="">总量: {{list.createNum}} 份</view>
						<view class="">当前最低价: {{list.minPrice==null?'NA无人出售':'￥'+list.minPrice}} </view>
					</view>
				</view>
			</view>
			<view class="ask_buy_view">
				<view class="bg">
					<view class="item" style="margin-bottom:30rpx;">
						<view>
							求购价：
						</view>
						<view class="number_box">
							<u-number-box v-model="ask_buy_price" :min="1">
							</u-number-box>
						</view>
					</view>
					<view class="item">
						<view>
							求购份数:
						</view>
						<view class="number_box">
							<u-number-box v-model="ask_buy_num" :min="1" :max="list.createNum">
							</u-number-box>
						</view>
					</view>
				</view>
			</view>
			<view class="active_endTime" v-show="list.endTime">
				批量委托功能即将在{{list.endTime}}到期
			</view>
			<view class="explain">
				<view class="title">
					说明 
					<view class="icon"></view>
				</view>
				<view class="li">
					1.在您点击确认发起批量委托后，即表示您同意以求购价购买批量委托的藏品，购买数量为您发起购买时填写的求购份数。平台上如有您发起批量委托的藏品出售，并且出售的价格小于或等于您的发起的批量委托价时,将会为您提交订单并购买。当同一件藏品有多个批量购买时，优先匹配批量委托价更高的订单。
				</view>
				<view class="li">
					2.请确保您的账号内有充足的余额。如余额不足以支付，系统将无法提交订单及购买。
				</view>
				<view class="li">
					3.订单提交成功后，将会立即从您的余额进行支付。您使用批量委托功能即表明您已同意自动买入购买的藏品并从余额进行支付。如果您不同意从余额扣款，请勿使用该功能。
				</view>
				<view class="li">
					4.藏品购买成功前，您可以在“我的”-“批量购买”中，撤销已发起的批量购买。
				</view>
				<view class="li">
					5.平台不为藏品购买成功与否做任何的承诺和保障。
				</view>
			</view>
			<view class="xieyi">
				<u-image mode="widthFix" width="28rpx" @click="isAgree = !isAgree"
					:src="`../../../static/imgs/public/${isAgree?'checked':'check'}.png`">
				</u-image>
				<view class="msg">
					我已知晓并同意
					<text @click="nav_link('批量委托协议',1)">《批量委托协议》</text>
					</text>
				</view>
			</view>
			<view class="footer_view">
				<view class="revocation" @click="$Router.back(1)">撤销</view>
				<button-bar class="submit_div" @click='submit()' text="确认批量委托"></button-bar>
			</view>
			<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
				email="333" :mode=mode @pay="password" @createSuccess="createSuccess" />
			<u-modal class="" v-model="isPassword" width="80%" :show-title="false" :show-confirm-button="false"
				border-radius="0">
				<view class="BankVerifyBody">
					<view class="head_title_y">
						<view class="right" @click="isPassword=false">
							<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
						</view>
						请先设置支付密码
					</view>
					<view class="msg_y">
						非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
					</view>
					<view class="footer_y" @click="isPassword=false">
						<button>
							取消
						</button>
						<button class="active" @click="SetPayPassword()">
							去设置
						</button>
					</view>
				</view>
			</u-modal>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	import payPopup from "@/components/payPopup/index.vue";
	export default {
		data() {
			return {
				isAgree: true,
				ask_buy_num: 1,
				ask_buy_price: 1,
				ctid: "",
				list: {
					cover: {}
				},
				isPasswordImport: false,
				passwordTitle: "确认委托",
				passwordMsg: "请输入余额支付密码，用于批量委托",
				mode: "pay",
				balance: 0,
				isPassword:false,
				isDomain:false
			};
		},
		onLoad(options) {
			this.ctid = options.ctid
			if(options.isDomain){
				this.list=uni.getStorageSync('askBuyList')
				console.log(this.list)
			}else{
				this.get()
				this.getInfo()
			}
		},
		onReachBottom() {},
		computed: {

		},
		methods: {
			//点击icon返回上一页
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			async get() {
				let res = await this.$api.java_marketRecommendTabInfo({
					ctid: this.ctid
				});
				if (res.status.code == 0) {
					console.log(res)
					this.list = res.result
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_link(title, index) {
				if (index === 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com.cn/link/#/pages/index/askBuyAgreement"
						}
					})
				}
			},
			password(e) {
				console.log(e)
				this.isPasswordImport = false
				this.balance = Number(uni.getStorageSync("balance"))
				if (this.balance > (this.ask_buy_num * this.ask_buy_price)) {
					console.log("余额充足")
					this.submitTargetCreate(e)
				} else {
					uni.showToast({
						title: "余额不足",
						icon: 'none',
						duration: 3000
					});
				}
			},
			createSuccess(psw) {
				console.log(psw)
				this.balance = Number(uni.getStorageSync("balance"))
				if (this.balance >= (this.ask_buy_num * this.ask_buy_price)) {
					console.log("余额充足")
					this.submitTargetCreate(e)
				} else {
					uni.showToast({
						title: "余额不足",
						icon: 'none',
						duration: 3000
					});
				}
			},
			async submitTargetCreate(psw) {
				let res = await this.$api.java_targetCreate({
					ctid: this.list.ctid,
					targetNum: this.ask_buy_num,
					targetPrice: this.ask_buy_price,
					tradePassword: psw,
					paymentScene: 1,
					tid:this.list.tid
				});
				if (res.status.code == 0) {
					uni.showToast({
						title: "已成功发起求购",
						icon: 'none',
						duration: 3000
					});
					setTimeout(() => {
						this.$Router.back(1)
					}, 1500)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			submit() {
				if (this.isAgree) {
					console.log(uni.getStorageSync('isSetTradePassword'))
					if(uni.getStorageSync('isSetTradePassword')==0){
						this.isPassword=true
					}else{
						this.isPasswordImport = true
					}
				} else {
					uni.showToast({
						title: '请先勾选协议',
						icon: 'none',
						duration: 3000
					});
				}

			},
			SetPayPassword() {
				this.mode = "set"
				this.isPasswordImport = true
			},
			async getInfo() {
				let res = await this.$api.userInfo({
					userId: ""
				});
				if (res.status.code == 0) {
					uni.setStorageSync("isSetTradePassword", res.result.isSetTradePassword)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		},
		components: {
			ButtonBar,
			payPopup
		}

	}
</script>

<style lang="scss" scoped>

	::v-deep .u-icon-minus{
		border-radius: 50% !important;
		width: 40rpx !important;
		height: 40rpx !important;
		border: 1rpx solid #70F6EB;
		background-color: transparent !important;

		&.u-icon-disabled {
			color: #FFFFFF !important;
			border: 1rpx solid #B2B2B2 !important;
		}
	}

	::v-deep .uicon-minus {
		color: #fff !important;
		font-size: 20rpx !important;

	}

	::v-deep .uicon-plus {
		color: #000 !important;
		font-size: 20rpx !important;
	}

	::v-deep .u-icon-plus {
		border-radius: 50% !important;
		width: 36rpx !important;
		height: 36rpx !important;
		background-color: #70F6EB !important;

		&.u-icon-disabled {
			color: #1F1F1F !important;
			background: #B2B2B2 !important;
		}
	}

	::v-deep .u-number-input {
		background-color: #46454F !important;
		color: #63EAEE !important;
		width: 200rpx !important;
		border-radius: 30rpx !important;
		margin: 0rpx 20rpx !important;
	}

	.border {
		width: 100%;
		height: 12rpx;
		background-color: #25232E;
	}

	.content {
		padding-bottom: 230rpx;
        margin-top: -88rpx;
		.shop_cart {
			padding: 50rpx;
			display: flex;
			justify-content: flex-start;
			
			.img {
				width: 206rpx;
				height: 206rpx;
				margin-right: 30rpx;
				background-color: #fff;
				border-radius:30rpx;
				image {
					width: 206rpx;
					height: 206rpx;
					border-radius:30rpx;
				}
			}

			.info {
				width: 446rpx;

				.title {
					color: #fff;
					height: 66rpx;
					font-size: 32rpx;
					overflow: hidden;
					margin-bottom: 46rpx;
				}

				.msg {
					color: #BFBFBF;
					font-size: 28rpx;

					>view {
						margin-top: 30rpx;
					}
				}
			}
		}

		.ask_buy_view {
			padding: 50rpx 50rpx 30rpx 50rpx;
			color: #fff;
			.bg{
				width:100%;
				height:190rpx;
				background-image:url(https://cdn-lingjing.nftcn.com.cn/image/20240430/881ac50caaefc39583b45400eeaaff3a_1356x380.png);
				background-size: 100% 100%;
				padding:30rpx 93rpx 30rpx 103rpx;
			}
			.item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				font-size:28rpx;
			}
		}
	.active_endTime{
		font-size:24rpx;
		color:rgba(99, 234, 238, 1);
		text-align: center;
		margin-bottom:20rpx;
	}
	.explain {
			color: #fff;
			font-size: 28rpx;
			padding: 0rpx 50rpx 50rpx 50rpx;
			.title {
				margin-bottom: 50rpx;
				font-size:34rpx;
				color:#fff;
				text-align: center;
				position:relative;
				.icon{
					position: absolute;
					left:50%;
					bottom:-10rpx;
					transform: translateX(-50%);
					width:40rpx;
					height:6rpx;
					background-color: #63EAEE;
				}
			}

			.li {
				margin-bottom: 30rpx;
				line-height: 40rpx;
			}
		}

		.xieyi {
			display: flex;
			justify-content: flex-start;
			padding: 0rpx 50rpx;

			.u-image {
				margin-right: 10rpx;
				margin-top: 8rpx;
			}

			.msg {
				font-size: 24rpx;
				color: #888888;
				text-align: left;
				line-height: 40rpx;

				text {
					color: var(--active-color);
				}
			}
		}

		.footer_view {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			padding: 20rpx 30rpx 40rpx 30rpx;
			display: flex;
			justify-content: flex-start;
			background-color:rgba(37, 35, 45, 0.9);
			.revocation {
				width: 248rpx;
				height:80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #26242E;
				border-radius: 24rpx;
				margin-right: 20rpx;
				font-weight: 600;
				font-size: 32rpx;
				color:#929196;
				border:2rpx solid #929196;
			}
			.submit_div{
				width:400rpx;
				border-radius:24rpx;
			}
		}
	}
	.BankVerifyBody {
		padding: 42rpx;
	
		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
			color: #fff;
	
			.right {
				position: absolute;
				right: 40rpx;
				top: 66rpx;
	
				image {
					width: 30rpx;
				}
			}
		}
	
		.item {
			margin-bottom: 46rpx;
	
			.labal {
				color: #999999;
				font-size: 24rpx;
			}
	
			.input {
				align-items: center;
				height: 88rpx;
	
				.left {
					width: 50%;
					color: #999999;
					padding: 6rpx;
					border-radius: 4rpx;
	
					.input {
						font-size: 24rpx;
						color: #fff;
					}
				}
	
				.right {
					image {
						width: 40rpx;
					}
	
					text {
						color: #00FBEF;
						font-size: 28rpx;
					}
				}
			}
		}
	
		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: #fff;
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}
	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;
		color: #fff;
	
		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;
	
			image {
				width: 30rpx;
			}
		}
	}
	
	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;
	
		button {
			margin: 0rpx;
			width: 238rpx;
			height: 64rpx;
			width: 240rpx;
			line-height: 64rpx;
			text-align: center;
			background-color: #999999;
			border-radius: 0rpx;
			font-size: 30rpx;
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: #fff;
			border-radius: 0rpx;
	
			&.active {
				color: var(--main-bg-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				background: var(--primary-button-color);
				color: var(--main-bg-color);
				border: 0rpx;
			}
		}
	}
	
	.msg_y {
		font-size: 28rpx;
		color: #999999;
		line-height: 40rpx;
	}
</style>
