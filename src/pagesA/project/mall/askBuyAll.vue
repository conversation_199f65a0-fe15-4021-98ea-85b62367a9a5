<template>
	<view class="">
		<view class="head">
			<view class="back" @click="custom">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20220613/b81fa4236e814e3b7cfeb8c8698568d3_48x48.png" mode="widthFix"></image>
			</view>
			<u-tabs name="cate_name" count="cate_count" font-size="28" active-color="#1FEDF0"
				inactive-color="var(--secondary-front-color)" :show-bar="false" :list="tabList"
				:is-scroll="false" bar-height="4" height="88" :current="current" style="background:none"
				@change="change"  bar-width="40"></u-tabs>
		</view>
		<view class="content">
			<view class="ask_buy_view" v-show="current===0">
				<view class="li" v-for="(item,index) in askBuyList" >
					<view class="shop_cart">
						<view class="img">
							<image :src="item.cover.src" mode="aspectFill"></image>
						</view>
						<view class="info">
							<view class="title oneOver">
								{{item.title}}
							</view>
							<view class="msg">
								<view class="">总量: {{item.createNum}} 份</view>
								<view class="">当前最低价: {{item.minPrice==null?'NA无人出售':'¥'+item.minPrice}} </view>
								<view class="">发起时间: {{item.dutyTime.split(".000")[0]}}</view>
							</view>
							<view class="price_num"> 
								<view class="item">
									求购出价：¥{{item.targetPrice}}
								</view>
								<view class="item"> 
									求购份数：{{item.targetNum}} 份 
								</view>
							</view>
							<view class="right_repeal" @click="revocation(item)">
								 撤销
							</view>
							<view class="error" v-if="item.tip!=null">
								<image src="https://cdn-lingjing.nftcn.com.cn/image/20221228/f1055ea5ce62a01a6116399665d499e1_63x57.png" mode="widthFix"></image>
								<view>提示：账户余额不足，无法抢购成功</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="ask_buy_view_succeed" v-show="current===1">
				<view class="li" v-for="(item,index) in askBuySucceedList" >
					<view class="shop_cart">
						<view class="img">
							<image :src="item.cover.src" mode="aspectFill"></image>
							<view class="left_bottom_icon">
								{{item.buyNum}}
							</view>
						</view>
						<view class="info">
							<view class="title oneOver">
								{{item.title}}
							</view>
							<view class="price_num"> 
								<view class="item">
									求购出价：¥{{item.targetPrice}}
								</view>
								<view class="item"> 
									买入均价：￥{{item.avgBuyPrice}} 
								</view>
							</view>
							<view class="price_num">
								<view class="item">
									累计买入：¥{{item.sumRealPrice}}
								</view>
								<view class="item"> 
									仍需买入：{{item.remainNum}} 份 
								</view>
							</view>
							<view class="msg">
								<view class="">最新买入时间: {{item.lastBuyTime}}</view>
								<!-- <view class="">求购发起时间: {{item.dutyTime.split(".000")[0]}}</view> -->
							</view>
							<view class="right_repeal">
								已买入：{{item.buyNum}}份
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <u-modal v-model="show" title="确认撤销本次求购？" border-radius="0" :content-style="bgObject" :title-style="titleObject"
				:show-confirm-button="false">
				<view class="modal-btn">
					<view class="but mb-cancel" @click="show = false">取消</view>
					<view class="but mb-confirm" @click="targetCancel">确定</view>
				</view>
			</u-modal> -->
			<u-modal v-model="show" title="确认撤销本次批量购买？" border-radius="0" :content-style="bgObject"
				:title-style="titleObject" :show-confirm-button="false">
				<view class="modal-btn space-between">
					<view class="mb-cancel" @click="show=false">取消</view>
					<view class="mb-confirm" @click="targetCancel">确定</view>
				</view>
			</u-modal>
		</view>
	</view>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		data() {
			return {
				tabList: [{
					cate_name: '批量购买中'
				}, {
					cate_name: '已批量买到的'
				}],
				current: 0,
				askBuyList:[],
				askBuySucceedList:[],
				pageNum:1,
				show:false,
				titleObject: {
					"background-color": "#1E1E1E",
					color: "#FFFFFF",
					"padding-top": "84rpx",
				},
				bgObject: {
					"background-color": "#1E1E1E",
					color: "#FFFFFF",
				},
			};
		},
		onLoad(options) {
			this.getList()
		},
		onReachBottom() {
			if(this.current==0){
				this.getList()
			}else{
				this.getListSucceed()
			}
			
		},
		computed: {
			
		},
		methods: {
			//点击icon返回上一页
			custom() {
				const pages = getCurrentPages();
				if (pages.length === 1) {
					this.$Router.pushTab({
						name: "mall"
					})
				} else {
					this.$Router.back();
				}
			},
			async getList() {
				let res = await this.$api.java_targetDutyList({
					pageNum:this.pageNum,
					pageSize:15
				});
				if (res.status.code == 0) {
					res.result.list.forEach((item)=>{
						this.askBuyList.push(item)
					})
					this.pageNum++
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getListSucceed() {
				let res = await this.$api.java_targetDutyOperateListV2({
					pageNum:this.pageNum,
					pageSize:15
				});
				if (res.status.code == 0) {
					res.result.list.forEach((item)=>{
						console.log(item)
						this.askBuySucceedList.push(item)
					})
					this.pageNum++
					console.log(res)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			revocation(item){
				this.show=true
				this.dutyId=item.dutyId
			},
			async targetCancel() {
				let res = await this.$api.java_targetCancel({
					dutyId:this.dutyId
				});
				if (res.status.code == 0) {
					this.show=false
					this.pageNum=1
					this.askBuyList=[]
					this.getList()
				} else {
					this.show=false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			change(e){
				this.pageNum=1
				if(e==0){
					this.askBuyList=[]
					this.getList()
				}else{
					this.askBuySucceedList=[]
					this.getListSucceed()
				}
				this.current=e
			}
		},
		components: {
			ButtonBar
		}

	}
</script>

<style lang="scss" scoped>
	.head{
		display: flex;
		justify-content:center;
		align-items: center;
		position:fixed;
		width:100%;
		background-color:#121212;
		z-index:99;
		.back{
			position: absolute;
			left:20rpx;
			top:20rpx;
			image{
				width:48rpx;
			}
		}
	}
	.u-tabs {
		width: 50%;
	
		::v-deep {
			.u-tabs {
				margin-top: -20rpx;
			}
	
			.u-tab-item {
				// text-align: start;
				font-size: 36rpx;
				padding-left: 0rpx !important;
			}
		}
	
	}
	.content{
		padding-top:50rpx;
	.ask_buy_view{
		padding:50rpx 18rpx;
		.li{
			background-color:#232222;
			border-radius:16rpx;
			margin-bottom:20rpx;
			.shop_cart{
				display: flex;
				justify-content: flex-start;
				padding:30rpx;
				.img{
					width:193rpx;
					height:193rpx;
					margin-right:30rpx;
					border-radius:8rpx;
					background-color:rgba(255, 2555, 255, 0.1);
					display: flex;
					justify-content: center;
					align-items: center;
					image{
						width:183rpx;
						height:183rpx;
					}
				}
				.info{
					width:420rpx;
					position:relative;
					.title{
						color:#fff;
						font-size:26rpx;
						overflow:hidden;
						width:70%;
					}
					.msg{
						color:#BFBFBF;
						font-size:20rpx;
						>view{
							margin-top:14rpx;
							
						}
					}
					.price_num{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						margin-top:14rpx;
						.item{
							height:40rpx;
							line-height:40rpx;
							color:#CBD7DD;
							border-radius:8rpx;
							padding:0rpx 10rpx;
							font-size:20rpx;
							background-color:rgba(172, 169, 169, 0.2);
							margin-right:20rpx;
						}
					}
					.right_repeal{
						position:absolute;
						right:0;
						top:0;
						font-size:20rpx;
						text-align: center;
						line-height: 36rpx;
						border-radius:8rpx;
						width:80rpx;
						color:#E5F1F3;
						// background:linear-gradient(137deg, #838c93 0%, #5b6366 100%);
						background-image:url('https://cdn-lingjing.nftcn.com.cn/image/20230103/099689be5f03c3d338735b7b4d12e055_272x94.png');	
						background-size:100% 100%;
					}
					.error{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						font-size:22rpx;
						margin-top:10rpx;
						color:#fff;
						image{
							width:26rpx;
							margin-right:10rpx;
						}
					}
				}
				
			}
		}
		
	}
	}
	.ask_buy_view_succeed{
		padding:50rpx 18rpx;
		.li{
			background-color:#232222;
			border-radius:16rpx;
			margin-bottom:20rpx;
			.shop_cart{
				display: flex;
				justify-content: flex-start;
				padding:30rpx;
				.img{
					width:193rpx;
					height:193rpx;
					margin-right:30rpx;
					border-radius:8rpx;
					background-color:rgba(255, 2555, 255, 0.1);
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					image{
						width:183rpx;
						height:183rpx;
					}
					.left_bottom_icon{
						position: absolute;
						left:-10rpx;
						bottom:-10rpx;
						background-image:url('https://cdn-lingjing.nftcn.com.cn/image/20230605/209d5e3ff15016eb889045c94d5f2b4b_198x210.png');
						background-size:100% 100%;
						background-repeat: no-repeat;
						width:90rpx;
						height:80rpx;
						color:#000;
						padding:38rpx 35rpx 16rpx 12rpx;
						font-size:22rpx;
						text-align: center;
					}
				}
				.info{
					width:440rpx;
					position:relative;
					.title{
						color:#fff;
						font-size:26rpx;
						overflow:hidden;
						width:60%;
					}
					.msg{
						color:#BFBFBF;
						font-size:20rpx;
						>view{
							margin-top:14rpx;
							line-height:30rpx;
						}
					}
					.price_num{
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top:26rpx;
						.item{
							height:40rpx;
							line-height:40rpx;
							color:#CBD7DD;
							border-radius:8rpx;
							padding:0rpx 10rpx;
							font-size:20rpx;
							background-color:#403C3E;
							text-align: center;
							min-width:193rpx;
							// margin-right:14rpx;
						}
					}
					.right_repeal{
						position:absolute;
						right:0;
						top:0;
						font-size:20rpx;
						text-align: center;
						border-radius:16rpx;
						padding:6rpx 10rpx;
						background: linear-gradient(90deg, #F4ABF2, #06F9EF);
						min-width:119rpx;
						color:#000;
					}
					.error{
						display: flex;
						justify-content: flex-start;
						align-items: center;
						font-size:20rpx;
						margin-top:10rpx;
						color:#fff;
						image{
							width:40rpx;
							margin-right:10rpx;
						}
					}
				}
				
			}
		}
		
	}
	.modal-btn {
		padding: 60rpx 40rpx;
	
		.mb-cancel,
		.mb-cancel1,
		.mb-confirm {
			width: 240rpx;
			height: 64rpx;
			line-height: 64rpx;
			text-align: center;
		}
	
		.mb-cancel {
			color: #666;
			background-color: var(--main-bg-color);
			border: 2rpx solid #616161;
			color: var(--active-color);
			border-radius: 4rpx;
		}
	
		.mb-cancel1 {
			width: 100%;
			color: #666;
			background-color: #eee;
			border-radius: 4rpx;
		}
	
		.mb-confirm {
			color: var(--main-bg-color);
			box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
			background: var(--primary-button-color);
		}
	}
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>
