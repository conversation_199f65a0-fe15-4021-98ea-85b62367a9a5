<template>
	<view class="list">
		<view>
			<view v-if="subCurrent==0" class="zore"> 发起时间: {{item.dutyTime}}</view>
			<view v-else class="zore"> 最新买入时间:{{item.lastBuyTime}}</view>
		</view>
		<view class="cont">
			<view class="lef">
				<view>
					<image :src="item.cover.src" mode="aspectFill"></image>
					<view v-if="subCurrent==1" class="num"> {{item.buyNum}}</view>
				</view>
				<view v-if="subCurrent==0" class="load">
					<view>{{item.title}}</view>
					<view>
						<view>限量：</view>
						<view>{{item.createNum}}份</view>
					</view>
					<view>
						<view>当前最低价：</view>
						<view>¥{{item.minPrice==null?'NA无人出售':item.minPrice}}</view>
					</view>
					<view class="btn">
						<view>
							求购出价：￥{{item.targetPrice}}
						</view>
						<view>求购份数：{{item.targetNum}} 份</view>
					</view>
				</view>
				<view v-else class="yes">
					<view>
						<view>{{item.title}}</view>
						<view class="totalPrice">
							<view class="totalPrice-bg"> 已买入:{{item.buyNum}}份</view>
						</view>
					</view>
					<view class="btns">
						<view>求购价格：￥{{item.targetPrice}}</view>
						<view>买入均价：￥{{item.avgBuyPrice}} </view>
						<view>累计买入：￥{{item.sumRealPrice}}</view>
						<view>仍需买入：{{item.remainNum}} 份</view>
					</view>
				</view>
			</view>
			<view class="rig" v-if="subCurrent==0" @click="det(item)"> 
				<view>撤销</view>
			</view>
			<!-- 	<view v-else class="rigYes">
				已买入:10份
			</view> -->
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item', 'subCurrent'],
		name: "basicJIng",
		data() {
			return {

			};
		},
		methods: {
			det(item) {
				this.$emit('revocation', item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.list {
		width: 678rpx;
		height: 258rpx;
		background: #25232d;
		border-radius: 30rpx;
		margin: 0 auto 40rpx auto;
		border-radius: 30rpx;
		overflow: hidden;

		.zore {
			width: 100%;
			height: 60rpx;
			line-height: 60rpx;
			background: rgba(29, 27, 37, 0.5);
			border-radius: 30rpx 30rpx 0px 0px;
			font-weight: 400;
			font-size: 18rpx;
			color: #999;
			padding-left: 32rpx;
			box-sizing: border-box;
		}

		.one {
			width: 100%;
			height: 60rpx;
			background: #35333E;
			border-radius: 30rpx 30rpx 0px 0px;
			display: flex;
			align-items: center;
			color: #999;
			font-weight: 400;
			font-size: 18rpx;
			padding-left: 32rpx;
			box-sizing: border-box;

			>view:nth-child(1) {
				width: 80rpx;
				height: 24rpx;
				line-height: 24rpx;
				text-align: center;
				background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 4rpx;
				margin-right: 20rpx;
				font-weight: bold;
				font-size: 16rpx;
				color: #25232D;
			}
		}

		.cont {
			width: 100%;
			height: 198rpx;
			background: #25232D;
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			padding: 20rpx 36rpx 0 30rpx;
			box-sizing: border-box;
			position: relative;

			.lef {
				display: flex;
				align-items: flex-start;

				>view:nth-child(1) {
					width: 148rpx;
					height: 148rpx;
					background: rgba(245, 245, 245, 0.5);
					border-radius: 12rpx;
					box-sizing: border-box;
					margin-right: 24rpx;
					position: relative;
					overflow: hidden;
					padding: 4rpx;
					box-sizing: border-box;

					>image {
						width: 100%;
						height: 100%;
					}

					.num {
						width: 60rpx;
						height: 28rpx;
						line-height: 28rpx;
						text-align: center;
						background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
						border-radius: 0rpx 12rpx 0rpx 12rpx;
						position: absolute;
						left: 0;
						bottom: 0;
						font-weight: bold;
						font-size: 18rpx;
						color: #141816;
						z-index: 3;
					}
				}

				.load {
					font-weight: 400;
					font-size: 24rpx;
					color: #999;

					>view {
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;
					}

					>view:nth-child(1) {
						color: #FFFFFF;
						margin-bottom: 18rpx;
					}

					.btn {
						>view {
							width: fit-content;
							height: 40rpx;
							line-height: 40rpx;
							text-align: center;
							background: rgba(255,255,255,0.1);
							border-radius: 8rpx;
							font-weight: 400;
							font-size: 18rpx;
							color: #999;
							margin-right: 20rpx;
							padding: 0 10rpx;
							box-sizing: border-box;
						}
					}
				}

				.yes {
					flex: 1;

					>view:nth-child(1) {
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
						margin-bottom: 24rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.totalPrice {
							width: 190rpx;
							height: 40rpx;
							text-align: center;
							line-height: 40rpx;
							/** 文本1 */
							font-weight: bold;
							font-size: 18rpx;
							color: #FFFFFF;
							border-radius: 33rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							background: linear-gradient(180deg,
									rgba(239, 145, 251, 1) 0%,
									rgba(64, 248, 236, 1) 100%);

							.totalPrice-bg {
								width: 190rpx;
								height: 40rpx;
								background: var(--main-bg-color);
								border-radius: 33rpx;
							}
						}
					}

					.btns {
						display: flex;
						align-items: center;
						flex-wrap: wrap;
						justify-content: space-between;

						>view {
							width: 200rpx;
							height: 40rpx;
							line-height: 40rpx;
							text-align: center;
							color: #999;
							background: rgba(255,255,255,0.1);
							font-weight: 400;
							font-size: 18rpx;
							border-radius: 8rpx;
							margin-bottom: 12rpx;
						}
					}
				}
			}

			.rigYes {
				font-weight: bold;
				font-size: 24rpx;
				color: #63EAEE;
				position: absolute;
				right: 0;
			}

			.rig {
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				>view:nth-child(1) {
					width: 80rpx;
					height: 36rpx;
					line-height: 36rpx;
					text-align: center;
					background: #25232D;
					border-radius: 18rpx;
					border: 1rpx solid #FFFFFF;
					font-weight: 400;
					font-size: 20rpx;
					color: #FFFFFF;
					box-sizing: border-box;
					margin-bottom: 40rpx;
				}

				>view:nth-child(2) {
					font-weight: bold;
					font-size: 24rpx;
					color: #63EAEE;

					>text {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
</style>