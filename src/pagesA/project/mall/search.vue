<template>
	<view class="main">
		<view class="top" :style="{'height':height+'rpx'}"></view>
		<view class="content padding_lr">
			<view class="search_view">
				<view class="back" @click="nav_back">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240329/442de86adcfcf12f3454c0b1e126b70e_52x100.png"
						mode="widthFix"></image>
				</view>
				<u-search placeholder="" search-icon="https://cdn-lingjing.nftcn.com.cn/image/20240118/9b03ce9292767ae8eec9722d8eac0ebf_44x44.png" focus shape="square" v-model="keyword" @search="search" :clearabled="false" @clear="clear" :show-action="false"></u-search>
				<view class="right" @tap="search">搜索</view>
			</view>
			<view class="search_lishi" v-if="searchHistory!=''">
				<view class="title">
					搜索历史
				</view>
				<view class="lishi" >
					<view class="li" v-for="(item,index) in searchHistory">
						<text @tap="clickItem(item)">{{item}}</text>
						<image @tap="delHistory(index)" src="https://cdn-lingjing.nftcn.com.cn/image/20240515/a25d76043c6295594b105b546b04850e_200x200.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="collection" v-if="seriesList!=''">
				<view class="li" v-for="(item,index) in seriesList" @tap="nav_seriesList(item)">
					<view class="bg">
						<view class="cover">
							<image
								:src="item.cover.src"
								mode="aspectFill"></image>
							<view class="left_bottom_icon">
								流通{{item.activeNum}}份
							</view>
							<!-- <view class="right_top_icon">
								<image src="https://cdn-lingjing.nftcn.com.cn/image/20240114/5c0c251af8f94f2c5f071fa03881e909_110x44.png" mode="widthFix"></image>
							</view> -->
							<view class="tuishi" v-if="item.isExitMarket==1">
								<image src="https://cdn-lingjing.nftcn.com.cn/image/20240310/89a70b6d15a2a87fcc8fd4628db673c2_274x274.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
					<view class="font_view">
						<view class="title oneOver">
							{{item.title}}
						</view>
						<view class="sub_title">
							总量：{{item.createNum}}份
						</view>
						<view class="flex icon_price" >
							<view class="left_huo"  >
									<image v-if="item.heat" v-for="(item,index) in item.heat" src="https://cdn-lingjing.nftcn.com.cn/image/20240114/2b3bb71487aa70afc402885f23810a58_22x22.png" mode="widthFix"></image>
							</view>
							<view class="right_price">
								<view v-if="item.minPrice">
									<span>￥</span>
									{{item.minPrice}}
									<span class="qi">起</span>
								</view>
								<view v-else style="color:var(--default-color1)">
									<span>￥</span>
									{{item.minPrice?item.minPrice:"-"}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="null_body" v-else>
				<view class="null" v-if="!isLogin">
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						您还未登录
					</view>
					<view class="nav_login" @tap="nav_login">
						登录/注册
					</view>
				</view>
				<view class="null" v-else>
					<view class="img">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
							mode="widthFix"></image>
					</view>
					<view class="text">
						暂无数据
					</view>
				</view>
			</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
	</view>
	</view>
</template>
<script>
	import api from '@/common/api/index.js';
	export default {
		data() {
			return {
				height: "",
				keyword: "",
				tabList: [],
				seriesList: [],
				show: true,
				pageNum: 1,
				marketTabId: "",
				searchHistory: [],
				isFooter: true, //没有更多了
				isRequest: false, //多次请求频繁拦截
				isLogin:false,
				isLoadding:false
			}
		},
		onLoad(options) {
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			});
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					console.log('当前平台？？？？？', res.statusBarHeight)
					_this.height = res.statusBarHeight * 2
					console.log(_this.height)
				}
			});
			this.searchHistory=this.getSearchHistory()
			this.userInfo()
		},
		onShow() {
			console.log(22222)
		},
		onPullDownRefresh() {
			setTimeout(() => {
				this.pageNum = 1
				this.getList()
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 1000);
		},
		onReachBottom() {
			if (this.isFooter) {
				if (this.isRequest == false) {
					this.getList()
				} else {
					console.log("请求超时，已经拦截")
				}
			} else {
				console.log("已经到底了")
			}
		},
		methods: {
			change(index) {
				this.current = index
				this.marketTabId = this.tabList[index].value
				this.pageNum = 1
				this.getList()
			},
			search() {
				if(this.keyword){
					this.seriesList = []
					this.pageNum = 1
					this.getList()
					this.addSearchHistory(this.keyword)
				}else{
					uni.showToast({
						title: "请输入关键字",
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 添加搜索记录
			addSearchHistory(keyword) {
				let history = this.getSearchHistory();

				// 删除已存在的关键词
				history = history.filter(item => item !== keyword);

				// 将新的关键词添加到历史记录的最前面
				history.unshift(keyword);

				// 限制历史记录的数量（例如只保留10条）
				if (history.length > 10) {
					history.pop();
				}

				// 存储到本地
				uni.setStorageSync('searchHistory', history);

				// 更新当前组件的数据
				this.searchHistory = history;
			},
			// 获取搜索历史
			getSearchHistory() {
				const history = uni.getStorageSync('searchHistory') || [];
				return history;
			},
			clear() {
				this.keyword = ""
				this.pageNum = 1
				this.getList()
			},
			nav_details(item) {
				console.log(item.id)
				this.$Router.push({
					name: "webView",
					params: {
						url: 'http://web-test.nftcn.com/h5/#/pagesA/project/official/detail_art?id=' + item.id
					}
				})
			},
			nav_to(name) {
				this.$Router.push({
					name
				})
			},
			async getList() {
				const {
					status,
					result
				} = await this.$api.pgcSearch({
					keyword: this.keyword,
					pageNum:this.pageNum
				});
				if (status.code == 0) {
					this.isRequest = false
					if (result.list == null || result.list == "") {
						this.isFooter = false
					} else {
						this.pageNum++
						result.list.forEach((item) => {
							this.seriesList.push(item)
						})
					}
				}else if(status.code == 1002){
					this.isLoadding =true
					setTimeout(()=>{
						this.nav_login()
					},2000)
				} else {
					uni.showToast({
						title: status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_seriesList(item) {
				if (item.ctid) {
					this.$Router.push({
						name: 'seriesList',
						params: {
							ctid: item.ctid
						}
					})
				}
			},
			nav_back() {
				this.$Router.back()
			},
			clickItem(item){
				this.keyword=item
				this.search()
			},
			//删除指定位置
			removeElementFromArrayWithSplice(array, element) {
			     let index = array.indexOf(element);
			     if (index > -1) {
			       
			     }
			     return array;
			},
			// 删除历史记录
			delHistory(index){
				this.searchHistory.splice(index, 1);
				console.log(this.searchHistory)
				uni.setStorageSync('searchHistory', this.searchHistory);
			},
			async userInfo() {
				let res = await this.$api.userInfo({});
				if (res.status.code == 0) {
					this.isLogin = true
				} else {
					// uni.showToast({
					// 	title: res.status.msg,
					// 	icon: 'none',
					// 	duration: 3000
					// });
				}
			},
			nav_login() {
				this.$Router.push({
					name: 'mainLogin',
				})
			},
		}
	}
</script>
<style lang="scss">
	::v-deep .uni-input-input{
		width:85% !important;
	}
	.padding_lr {
		padding: 0rpx 34rpx;
	}

	.head_bg {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		width: 1000rpx;
		height: 500rpx;
		z-index: -1;
	}

	.main {
		flex: 1;
		min-height: 100vh;
	}

	.head_title {
		height: 170rpx;
		// padding:86rpx 0 0 0;
	}

	.title_1 {
		color: #141414;
		font-weight: 600;
		font-size: 44rpx;
	}

	.content {}

	.search_view {
		padding-top: 30rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		height:100rpx;
		position: relative;
		.right{
			font-size:28rpx;
			position: absolute;
			right:30rpx;
			color:#999999;
		}
		.back {
			width: 26rpx;
			margin-right: 20rpx;

			image {
				width: 26rpx;
			}
		}
	}

	.search_lishi {
		padding: 0rpx 32rpx;
		margin-top: 37rpx;
		
		.title {
			font-size: 28rpx;
			font-weight: 600; 
			color:#fff;
		}

		.lishi {
			height: 60rpx;
			overflow: auto;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-top: 30rpx;
			
			.li {
				border-radius: 10rpx;
				border: 1px solid #A6A6A6;
				color: #A6A6A6;
				font-size: 20rpx;
				padding: 0rpx 18rpx;
				height: 44rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 20rpx;
				flex: 1 0 auto;
				max-width: max-content; /* 或者指定一个最大宽度 */
				position: relative;
				image{
					width:30rpx;
					position: absolute;
					right:-14rpx;
					top:-14rpx;
				}
			}
		}
	}

	.tabbar_view {
		margin-top: 0rpx;
	}

	.list_view {
		width: 638rpx;
		margin-top: 40rpx;
	}

	.list_li {
		display: flex;
		flex-direction: row;
		align-items: center;
		background-color: #F5F5F5;
		padding: 32rpx;
		border-radius: 24rpx;
		margin-bottom: 40rpx;
	}

	.left_img {
		margin: 0 40rpx 0 0;
	}

	.right_font {
		&_title {
			color: #141414;
			font-size: 38rpx;
			font-weight: 600;
		}
	}

	.sub_title {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.time {
		&_text {
			color: $uni-color-gray;
			font-size: $uni-font-size-h4;
			line-height: 44rpx;
		}
	}

	.collection {
		margin-top: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		padding-bottom:100rpx;
		.li {
			width: 320rpx;
			height:450rpx;
			background-color:#25232D;
			margin-bottom: 30rpx;
			border-radius:30rpx;
			padding:20rpx;
			.bg {
				position: relative;
				width: 288rpx;
				height: 288rpx;
	
				.cover {
					position: absolute;
					top: 0rpx;
					left: 0rpx;
					width: 280rpx;
					height: 280rpx;
					border-radius: 30rpx;
					>image {
						width: 280rpx;
						height: 280rpx;
						border-radius: 30rpx;
					}
	
					.left_bottom_icon {
						position: absolute;
						bottom: 0;
						left: 0;
						background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240104/79eb4cb92625dd836b2749f657f1437d_140x44.png);
						background-size: 100% 100%;
						width: 140rpx;
						height: 44rpx;
						border-radius: 0px 20rpx 0px 30rpx;
						text-align: center;
						font-size: 20rpx;
						color: #fff;
						display: flex;
						justify-content: center;
						align-items: center;
					}
					.right_top_icon{
						position: absolute;
						right:0rpx;
						top:0rpx;
						width:110rpx;
						height:44rpx;
						image{
							width:110rpx;
							height:44rpx;
						}
					}
					.tuishi{
						position: absolute;
						right:0rpx;
						bottom:0rpx;
						width:137rpx;
						height:137rpx;
						image{
							width:137rpx;
							height:137rpx;
						}
					}
				}
			}
			.font_view{
				padding:12rpx 0rpx 24rpx 0rpx;
				.title{
					width:100%;
					font-size:24rpx;
					font-weight:600;
					color:#fff;
				}
				.sub_title{
					font-size:22rpx;
					color:#FFFFFF;
					margin-top:10rpx;
				}
				.icon_price{
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top:10rpx;
					.left_huo{
						image{
							width: 22rpx;
							height:22rpx;
						}
					}
					.right_price{
						color:var(--active-color1);
						font-weight:600;
						font-size:34rpx;
						min-width:50%;
						text-align:right;
						span{
							font-size: 24rpx;
						}
						.qi{
							color:var(--active-color1);
						}
					}
				}
			}
		}
	}

	.null_body {
		.null {

			.img {
				display: flex;
				justify-content: center;

				image {
					width: 142rpx;
				}
			}

		}

		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
		.nav_login {
			width: 200rpx;
			height: 60rpx;
			background: var(--main-bg-color);
			border-radius: 12rpx;
			border: 2rpx solid #63EAEE;
			color: #63EAEE;
			font-size: 26rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 40rpx;
		}
		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>