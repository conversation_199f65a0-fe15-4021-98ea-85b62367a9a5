<template>
	<view class=" main">
		<view class="head_icon">
			<view class="left_icon" @tap="nav_back()">
				
				
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20241025/b0cea2b4a1f41d442f8f71fd22c9e91d_160x160.png"
					mode="scaleToFill" ></image>
			</view>
			<view class="right_icon" @tap="toest()">
				<image src="https://cdn-lingjing.nftcn.com.cn/image/20240305/e2d4513dd42d88bef4d30acb5810d98e_50x50.png"
					mode="widthFix"></image>
			</view>
		</view>
		<view class="body_details">
			<view class="cover" v-show="!exhibitionShow" @click="showExhibitionAfterThree">
				<image :src="detailsList.cover.src" mode="widthFix"></image>
				<view class="notSaleSign" v-show="detailsList.notSaleSign == 1">
					仅供收藏
				</view>
				<view class="notSaleSign" v-show="detailsList.notSaleSign == 3">
					仅供转售
				</view>
			</view>
			<view :style="{ 'height': `750rpx` }" v-if="exhibitionShow">
				<view class="exhibition-mask"  v-if="exhibitionShow"
					@click="hideExhibition"></view>
				<web-view v-if="exhibitionShow"  class="webView_class" 
					:src="exhibitionUrl"></web-view>
			</view>
			<view class="shop_details">
				<view class="price">
					<text>￥</text>
					{{ detailsList.price }}
				</view>
				<view class="title">
					<view class="left" @click="copy(detailsList.name)">
						<view class="tit"  >
							{{ detailsList.name }}
						</view>
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240425/bb07bffbae8941b4e34ce1e3d9ed2885_48x52.png" mode="widthFix"></image>
					</view>
					<image class="right_qtjs" @click="nav_series()" src="https://cdn-lingjing.nftcn.com.cn/image/20240425/97c9aedaa8510e87797a0161210df290_280x88.png" mode="widthFix"></image>
				</view>
				<view class="tag_list" v-if="detailsList.marketTabNameList">
					<view class="li" v-for="(item, index) in detailsList.marketTabNameList">
						{{ item }}
					</view>
				</view>
				<view class="data_view">
					<view>
						<text>限量</text>
						{{ detailsList.csGoodsCount }}份
					</view>
					<view>
						<text>流通</text>
						{{ detailsList.csActiveNum }}份
					</view>
				</view>
				<view class="border"></view>
			</view>
			<!-- <view class="user_view" @tap="nav_seriesList">
				<view class="flex">
					<view class="user_left">
						<image :src="detailsList.cover.src" mode="aspectFill"></image>
						<view class="title oneOver">
							{{detailsList.title}}
						</view>
					</view>
					<view class="user_right">
						<text>其他寄售</text>
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240118/a88eeb14a477475cd1d4a7fd7fd0952a_28x28.png" mode="widthFix"></image>
					</view>
				</view>
				<view class="border"></view>
			</view> -->
			<view class="address_view">
				<view class="label">
					持有信息
				</view>
				<view class="value">
					<view class="dosc">
						持有者
					</view>
					<view class="val">
						{{ detailsList.ownerUser.contractAddress }}
					</view>
				</view>
				<view class="value">
					<view class="dosc">
						Token ID
					</view>
					<view class="val" @click="copy(detailsList.tokenId)">
						<view class="tid">{{ detailsList.tokenId }}</view>
						<image src="https://cdn-lingjing.nftcn.com.cn/image/20240425/bb07bffbae8941b4e34ce1e3d9ed2885_48x52.png" mode="widthFix"></image>
					</view>
				</view>
				<view class="border"></view>
			</view>
			<view class="details_font">
				<view class="img_title">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240305/54604b6a676c8685a30c785852c67d65_112x43.png"
						mode="widthFix"></image>
				</view>
				<view class="item">
					<view class="title">
						作品描述
					</view>
					<view class="text">
						{{ detailsList.content }}
					</view>
				</view>
				<view class="item">
					<view class="title">
						艺术家介绍
					</view>
					<view class="text">
						{{ detailsList.artistIntro }}
					</view>
				</view>
				<view class="item">
					<view class="title">
						作品权益
					</view>
					<view class="text">
						是否拥有实物：不含实物</br>
						数字作品：用户购买数字作品后，即拥有该数字作品，但数字作品的知识产权仍由作品知识产权的权利人拥有。数字作品的知识产权并不因数字作品的交易行为行为而发生任何转移或共享。用户可以进行学习、研究、欣赏、收藏。
					</view>
				</view>
				<view class="item">
					<view class="title">
						交易须知
					</view>
					<view class="text">
						<view class="margin-bottom" v-for="(item, index) in detailsList.transactionNotes">
							{{ item }}
						</view>
						<!-- {{detailsList.transactionNotes}} -->
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="details_bottom">
			<view class="price">
				￥{{ detailsList.price }}
			</view>
			<view class="submit_button">
				<text @tap="orderCheck" v-show="detailsList.status == 1">立即购买</text>
				<text @tap="nav_seriesList" v-show="detailsList.status == 2">其他寄售</text>
			</view>
		</view> -->

		<u-modal v-model="isSuccess" border-radius="30" :mask-close-able="true" :show-title="false"
			:show-confirm-button="false" width="480">
			<view class="new-modal-content">
				<view class="success_img">
					<image
						src="https://cdn-lingjing.nftcn.com.cn/image/20240322/942264f29d34245fa78a23becfe96b87_480x480.png"
						mode="widthFix"></image>
				</view>
				<view class="modal-content" style="border-bottom:none;">
					{{ msg }}
				</view>
			</view>
		</u-modal>
		<resale-pop :isShowModalResale.sync="isShowModalResale" :itemId="tid" @closeResale="closeResale"></resale-pop>
		<pay-popup :popup-show.sync="isPasswordImport" order-type="O" @pay="finishPay" />
		<u-modal v-model="isShowMsg" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					停售
				</view>
				<view class="modal-content">
					是否确认停售该作品
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isShowMsg = false">取消</view>
					<view class="img_reasale" @click="confirm()">确认</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isShowMsg" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="modal-content">
					<view class="title">
						您尚未开通抢单功能
					</view>
					<view class="msg_view">
						是否确认消耗{{detailsList.askBuyNum}}张《BV起飞卡》开通本功能?（功能有效期：30天）
					</view>
				</view>
				 <view class="showModal-btn">
				 	<view class="img_cancel" @click="isShowMsg=false">取消</view>
				 	<view class="img_reasale" @click="submitAskBuy()">确认开通</view>
				 </view>
			</view>
		</u-modal>
		<u-modal v-model="isError" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false" :mask-close-able="true">
			<view class="new-modal-content">
				<view class="modal-content">
					<view class="title">
						WHOOPS!
					</view>
					<view class="msg_view">
						您的仓库还没有{{detailsList.askBuyNum}}张《BV起飞卡》
						暂时无法开通本功能
					</view>
				</view>
				<view class="showModal-btn center" >
					<view class="img_reasale" @click="nav_series_qifeika()">去购买</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isSueess" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="modal-content">
					<view class="title">
						恭喜您！
					</view>
					<view class="msg_view">
						您的抢单功能已开通成功！
						<view class="active_msg">
							功能有效期至：{{ endTime }}
						</view>
					</view>
				</view>
				<view class="showModal-btn center">
					<view class="img_reasale" @click="isSueess = false">确认</view>
				</view>
			</view>
		</u-modal>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				开通中...
			</view>
		</u-modal>
		<puoupPoster :show="show" :detailsList="detailsList" :value="value" @close='close'></puoupPoster>
		<mallFooter v-if="footerShow" :detailsList="detailsList" :isTips="isTips" :TipsText="TipsText"
			:isCanSale.sync="isCanSale" :isCollection="isCollection" :isUser="isUser" :btnText="btnText"
			@submit="configSubmit" @getDetails="details" :isOpacity.sync="isOpacity" @robSubmit="robSubmit"
			@hideWebView="hideWebView">
		</mallFooter>
	</view>
</template>
<script>
	import api from '@/common/api/index.js';
	import resalePop from '@/components/public/resalePop.vue'
	import payPopup from "@/components/payPopup/index.vue";
	import puoupPoster from '@/components/puoupPoster/puoupPoster.vue'
	import mallFooter from "@/components/mall/details-bottom.vue";
	import uniCopy from "@/js_sdk/uni-copy.js";
	export default {
		data() {
			return {
				detailsList: {
					cover: {},
					ownerUser: {},
					createUser: {}
				},
				tid: "",
				sale: "",
				isShowModalResale: false,
				isPasswordImport: false, // 密码弹窗
				passwordImportPay: '',
				resalePrice: '',
				isSuccess: false,
				msg: "作品寄售成功",
				isShowMsg: false,
				exhibitionUrl: "",
				coverHeight: "",
				exhibitionShow: false, // 3d旋转显示
				show: false,
				footerShow: true, // 是否显示底部按钮
				isTips: false, //底部提示
				isOpacity: false,
				isCanSale: true, //按钮状态
				btnText: "",
				isCollection: false,
				TipsText: "TA还不想出售此作品", //底部提示
				isUser: false,
				value: '',
				isShowMsg:false,
				titleObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				bgObject: {
					'background-color': '#FFF',
					'color': '#FFFFFF'
				},
				isError:false,
				isSueess:false,
				endTime:"",
				isLoadding:false,
				exhibitionPath:""
			}
		},
		components: {
			resalePop,
			payPopup,
			puoupPoster,
			mallFooter
		},
		onLoad(options) {
			this.tid = options.tid
			this.exhibitionPath = getApp().globalData.exhibitionUrl
			if (options.sale) {
				this.sale = options.sale
			}
			this.details()
			
			// #ifdef H5
				let { origin } = window.location;
					this.value = `${origin}/h5/#/pagesA/project/mall/detailsShop?tid=${this.tid}`
			// #endif
			// #ifdef APP
				let origin = getApp().globalData.url
					this.value = `${origin}pagesA/project/mall/detailsShop?tid=${this.tid}`
			// #endif
			// this.value = this.$url + '?ctid=' + this.tid
		},
		onShow() {},
		onPullDownRefresh() {
			setTimeout(() => {
				uni.stopPullDownRefresh(); //停止下拉刷新动画
			}, 1000);

		},
		methods: {
			async details() {
				let res = await this.$api.java_goodsDetails({
					itemTokenId: this.tid
				});
				if (res.status.code == 0) {
					this.detailsList = res.result
					this.detailsList.transactionNotes = res.result.transactionNotes.split("\n\n")
					// 拼接3d旋转页面的链接;
					const {
						category,
						archiveName,
						photoShow
					} = res.result;
					this.coverHeight = (res.result.cover.h * 750) / res.result.cover.w;
					console.log("请求详情页")
					// let imageUrl = new URL('https://example.com/path/to/image.jpg')
					let origin = "https://cdn-lingjing.nftcn.com.cn/"
					const address = category === 4 ? archiveName : photoShow;
					// #ifdef APP
					this.exhibitionUrl =
						`${this.exhibitionPath}?address=${address}&category=${category}&v=${Date.now()}&url=${origin}`;
					// #endif
					// #ifdef H5
					this.exhibitionUrl =
						`${process.env.VUE_APP_EXHIBITION_PATH}?address=${address}&category=${category}&v=${Date.now()}&url=${origin}`;
					// #endif
					
					console.log(this.exhibitionUrl)
					// if (category === 1 || category === 4) this.showExhibitionAfterThree();
					if (this.detailsList.uid == uni.getStorageSync("uid")) {
						this.isUser = false;
					} else if (this.detailsList.status == 1) {
						this.isTips = false;
						this.isCanSale = true;
						console.log(this.typeWorks, this.version_maxNnm);
						if (this.typeWorks == 32 && this.version_maxNnm > 1) {
							if (this.detailsList.showStatus == 3) {
								//未寄售
								if (this.detailsList.saleStatus == 1) {
									this.btnText = "选择版号购买"; //有部分赠送
									this.isCanSale = true;
								} else if (this.detailsList.saleStatus == 2) {
									//无赠送
									this.btnText = "其他寄售";
									this.isCanSale = true;
									this.isSeriesLink = true;
								} else if (this.detailsList.saleStatus == 3) {
									//全部出售未寄售
									this.btnText = "其他寄售";
									this.isCanSale = true;
									this.isSeriesLink = true;
								}
							} else if (this.detailsList.showStatus == 1) {
								//新寄售
								this.btnText = "选择版号购买"; //有部分赠送
								this.isCanSale = true;
							} else if (this.detailsList.showStatus == 2) {
								//寄售中-二级市场
								this.btnText = "选择版号购买"; //有部分赠送
								this.isCanSale = true;
							}
						} else {
							this.btnText = "立即购买";
						}
						this.isUser = true;
					} else if (this.detailsList.status == 2) {
						this.isSeriesLink = true;
						this.isCanSale = true;
						// this.isTips = true;
						// this.TipsText = "TA现在还不想出售此作品";
						this.btnText = "其他寄售";
						this.isUser = true;
					} else if (this.detailsList.status == 3) {
						if (this.detailsList.showStatus == 1) {
							this.btnText = "立即购买";
						} else {
							this.isSeriesLink = false;
							this.isTips = false;
							this.isCanSale = false;
							this.isOpacity = true;
							this.btnText = "仅供收藏";
						}
					} else if (this.detailsList.status == 4) {
						this.isSeriesLink = false;
						this.isTips = false;
						this.isCanSale = false;
						this.btnText = "审核中";
						this.isUser = true;
					} else if (this.detailsList.status == 6) {
						if (this.detailsList.showStatus == 1) {
							this.btnText = "立即购买";
						} else {
							this.isSeriesLink = false;
							this.isTips = false;
							this.isCanSale = false;
							this.isOpacity = true;
							this.btnText = "仅供转赠";
						}
					} else if (this.detailsList.status == 7) {
						this.isSeriesLink = true;
						this.isTips = true;
						this.TipsText = "当前有人正在支付";
						this.isCanSale = true;
						this.btnText = "其他寄售";
						this.isUser = true;
					} else if (this.detailsList.status == 8) {
						this.isTips = true;
						this.isOpacity = true
						this.isCanSale = false;
						// this.isCanSale = false;
						this.btnText = this.$u.timeFormat(this.detailsList.openSaleTime.replace(/-/g, "/"),
							'mm-dd hh:MM:ss 开启公售')
					} else if (this.detailsList.status == 9) {
						this.isOpacity = true
						this.isTips = true;
						this.isCanSale = false;
						this.btnText = this.$u.timeFormat(this.detailsList.openSaleTime.replace(/-/g, "/"),
							'mm-dd hh:MM:ss 开启优先购')
					} else if (this.detailsList.status == 10) {
						this.isSeriesLink = false;
					} else if (this.detailsList.status == 11 && this.saleNum > 0) {
						console.log(this.saleNum);
						this.isTips = true;
						this.TipsText = "本作品已全部售出，可选择版号进入二级市场购买";
						this.isCanSale = true;
						this.btnText = "我想要";
						this.isUser = true;
					} else if (this.detailsList.status == 11) {
						this.isSeriesLink = true;
						this.isTips = true;
						this.TipsText = "本作品已全部售出，可选择版号进入二级市场购买";
						this.isCanSale = true;
						this.btnText = "其他寄售";
						this.isUser = true;
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			toest() {
				this.show = true
			},
			nav_seriesList() {
				this.$Router.push({
					name: 'seriesList',
					params: {
						ctid: this.detailsList.ctId
					}
				})
			},
			nav_back() {
				this.$Router.back()
			},
			async orderCheck(type) {
				let res = await this.$api.java_order_check({
					itemId: this.detailsList.itemId, // 作品id(父版id)(普通系列必填)
					version: 1, // 版本号(普通系列必填)
					paymentScene: 1, // 1-H5 2-PC 3-IOS 4-Android 5-H5的PC
					price: this.detailsList.price,
					batchBuyNum: 1,
					isSeize: type
				});
				if (res.status.code == 0) {
					console.log(res.result)
					if(type == 1){
						res.result.isSeize = 1
					}
					uni.setStorageSync("detailsList", res.result);
					this.$Router.push({
						name: "checkOrder",
						params: {
							tid: this.tid,
						},
					});
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			OnSale() {
				this.isShowModalResale = true
			},
			UnSale() {
				this.isShowMsg = true
			},
			closeResale(data) {
				this.isShowModalResale = false
				console.log(data)
				if (!data) return
				this.resalePrice = data
				this.isPasswordImport = true
			},
			finishPay(e) {
				this.passwordImportPay = e
				this.isPasswordImport = false
				console.log(this.passwordImportPay, '3333333333')
				this.collectionPutaway(e)
			},
			async collectionPutaway(password) { //提交藏品寄售
				const params = {
					tid: this.tid,
					price: this.resalePrice,
					tradePassword: password
				}
				// 寄售成功按钮
				let res = await this.$api.collectionOnSale(params);
				if (res.status.code === 0) {
					// 关掉快捷寄售弹窗
					this.passwordImportPay = ""
					this.isPasswordImport = false
					this.detailsList.bottomButton = 3
					this.isSuccess = true
					// 关掉快捷寄售弹窗
					this.msg = "作品寄售成功"
					// 弹出成功寄售弹窗
					setTimeout(() => {
						this.isSuccess = false
					}, 2000)
				} else {
					this.passwordImportPay = ""
					this.isPasswordImport = false
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			confirm() {
				this.collectionPutawayUnSale()
			},
			async collectionPutawayUnSale() { //提交藏品寄售
				const params = {
					tid: this.tid,
				}
				// 寄售成功按钮
				let res = await this.$api.collectionUnSale(params);
				if (res.status.code === 0) {
					this.isShowMsg = false
					this.isSuccess = true
					// 关掉快捷寄售弹窗
					this.msg = "作品停售成功"
					this.detailsList.bottomButton = 4
					setTimeout(() => {
						this.isSuccess = false
					}, 2000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			/**
			 * 隐藏3d旋转,并在3秒后复原
			 */
			hideExhibition() {
				if (this.exhibitionShow) {
					this.exhibitionShow = false;
				}
			},
			/**
			 * 3秒后显示3d旋转
			 */
			showExhibitionAfterThree() {
				// #ifdef H5
					this.exhibitionShow = true;
				// #endif
			},
			robSubmit() {
				if(this.detailsList.isSeize==0){
					//引导开通
					this.isShowMsg=true
				}else{
					//去抢单
					this.orderCheck(1)
				}
			},
			async configSubmit() {
				if (this.typeWorks == 32) {
					console.log(this.isSeriesLink);
					if (this.isSeriesLink) {
						this.nav_series_link()
					} else if (this.isVersionsBut) {
						this.open_versions();
					} else {
						if (
							this.detailsList.maxVersion > 1 &&
							this.detailsList.type != 100
						) {
							if (this.version == "") {
								uni.showToast({
									title: "请选择版号",
									icon: "none",
									duration: 3000,
								});
							} else {
								console.log("版号为 " + this.version);
								if (this.isOrderCheck) {
									this.isRiskWindow = true;
								} else {
									if (this.isSeriesLink) {
										this.nav_series_link()
									} else {
										this.orderCheck(0);
									}
								}
							}
						} else {
							this.version = this.detailsList.maxVersion;
							console.log("版号为" + this.version);
							if (this.isSeriesLink) {
								this.nav_series_link()
							} else if (this.isOrderCheck) {
								this.isRiskWindow = true;
							} else if (this.detailsList.batchBuyNum > 0) {
								this.isBatchBuy = true
							} else {
								this.orderCheck(0);
							}
						}
					}
				} else {
					this.version = this.detailsList.maxVersion;
					if (this.isSeriesLink) {
						this.nav_series_link()
					} else {
						if (this.isOrderCheck) {
							this.isRiskWindow = true;
						} else if (this.detailsList.batchBuyNum > 0) {
							this.isBatchBuy = true
						} else {
							this.orderCheck(0);
						}
					}
				}
			},
			close() {
				this.show = false
			},
			nav_series_link() {
				this.$Router.push({
					name: "seriesList",
					params: {
						ctid: this.detailsList.seriesId
					}
				})
			},
			hideWebView() {
				this.exhibitionShow = false;
			},
			//复制token ID
			copy(val) {
				uniCopy({
					content: val,
					success: (res) => {
						uni.showToast({
							title: res,
							icon: "none",
						});
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: "none",
							duration: 3000,
						});
					},
				});
			},
			nav_series(){
				this.$Router.push({
					name:"seriesList",
					params:{
						ctid:this.detailsList.ctId
					}
				})
			},
			nav_series_qifeika(){
				this.$Router.push({
					name:'seriesList',
					params:{
						ctid:'cs14729210152792764673170846406530'
					}
				})
			},
			async submitAskBuy() {
				this.isShowMsg = false
				this.isLoadding = true
				let res = await this.$api.createSeizeRecord({});
				if (res.status.code == 0) {
					this.isSueess =true
					this.endTime = res.result
					this.detailsList.isSeize = 1
					this.isLoadding = false
				} else if(res.status.code ==9998){
					this.isLoadding = false
					this.isError = true
				} else{
					this.isLoadding = false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				} 
			},
		}
	}
</script>
<style lang="scss">
	.padding_lr {
		padding: 0rpx 35rpx;
	}

	.main {
		flex: 1;
		position: relative;

		.head_icon {
			position: fixed;
			/* #ifdef APP */
			top:var(--status-bar-height);
			/* #endif */
			/* #ifdef H5 */
			top:50rpx;
			/* #endif */
			left: 56rpx;
			right: 56rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			z-index: 99;
			width: 638rpx;

			.left_icon {
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.right_icon {
				image {
					width: 50rpx;
					margin-left: 30rpx;
				}
			}
		}

		.body_details {
			.animation {
				transition: height 500ms ease-in-out;
			}

			.exhibition-mask {
				height:750rpx;
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				z-index: 99;
			}

			.cover {
				width: 100%;
				position: relative;

				image {
					width: 100%;
				}

				.notSaleSign {
					width: 140rpx;
					height: 50rpx;
					line-height: 50rpx;
					background-color: rgba(0, 0, 0, 0.5);
					position: absolute;
					bottom: 40rpx;
					right: 40rpx;
					font-size: 24rpx;
					color: var(--default-color1);
					border-radius: 6rpx;
					text-align: center;
				}
			}

			.shop_details {
				padding: 0rpx 36rpx;
				margin-top: 45rpx;
				padding-bottom: 40rpx;

				.price {
					color: var(--default-color1);
					font-size: 56rpx;
					font-weight: 600;

					text {
						font-size: 36rpx;
					}
				}

				.title {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 20rpx 0rpx;
					.left{
						display: flex;
						justify-content: flex-start;
						.tit{
							display: inline-block;
							max-width: 400rpx; /* 调整为你所需的容器最大宽度 */
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							position: relative;
							font-size: 28rpx;
							font-weight: 600;
							color: var(--default-color1);
							
							letter-spacing:3rpx;
						}
						image{
							width:24rpx;
							margin-left:10rpx;
						}
					}
					.right_qtjs{
						width:140rpx;
					}
					
				}

				.tag_list {
					height: 60rpx;
					overflow: auto;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.li {
						border-radius: 10rpx;
						border: 1px solid #A6A6A6;
						color: #A6A6A6;
						font-size: 22rpx;
						padding: 0rpx 18rpx;
						height: 44rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-right: 20rpx;
						flex: 1 0 auto;
						max-width: max-content;
						/* 或者指定一个最大宽度 */
					}
				}

				.data_view {
					margin-top: 26rpx;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					>view {
						margin-right: 40rpx;
						color: var(--default-color1);

						text {
							color: #A6A6A6;
							margin-right: 10rpx;
							font-size: 24rpx;
						}

						font-size: 28rpx;
					}
				}
			}

			.user_view {
				padding: 0rpx 56rpx;

				.flex {
					display: flex;
					justify-content: space-between;
					align-items: center;
				}

				.user_left,
				.user_right {
					display: flex;
					justify-content: flex-start;
					align-items: center;
				}

				.user_left {
					.title {
						font-size: 24rpx;
						margin-left: 10rpx;
						font-weight: 600;
						width: 400rpx;
					}

					image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
					}
				}

				.user_right {
					font-size: 24rpx;
					color: #A6A6A6;

					image {
						width: 28rpx;
					}
				}
			}

			.address_view {
				padding: 0rpx 56rpx 40rpx 56rpx;

				.label {
					font-size: 24rpx;
					font-weight: 600;
					margin-bottom: 20rpx;
					color: var(--default-color1);
				}

				.value {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-size: 28rpx;
					margin-bottom: 20rpx;

					.dosc {
						color: #A6A6A6;
						width: 140rpx;

					}

					.val {
						color: var(--default-color1);
						width: 500rpx;
						display: flex;
						justify-content:flex-start;
						align-items: center;
						.tid{
							display: inline-block;
							max-width: 600rpx; /* 调整为你所需的容器最大宽度 */
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
						image{
							width:24rpx;
						}
					}
				}
			}

			.details_font {
				padding: 0rpx 56rpx;

				.img_title {
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 112rpx;
					}
				}

				.item {
					margin-bottom: 40rpx;
					color: var(--default-color1);

					>.title {
						font-size: 28rpx;
						font-weight: 600;
					}

					.text {
						color: var(--default-color1);
						opacity: .5;
						font-size: 28rpx;
						margin-top: 20rpx;
						line-height: 38rpx;
						
						.margin-bottom {
							margin-bottom: 10rpx;
						}
					}
				}

				padding-bottom:200rpx;
			}
		}
	}

	.border {
		border-bottom: 1px solid rgba(255, 255, 255, 0.1);
		margin-top: 40rpx;
	}

	.new-modal-content {
		padding: 35rpx 40rpx;

		.success_img {
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 160rpx;
				height: 160rpx;
			}
		}

		.modal-content {
			padding: 35rpx 0rpx;
			border-bottom: 1rpx solid #EDEDED;
			font-size: 28rpx;
			color: var(--default-color1);
			text-align: center;
		}

		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color: var(--default-color1);
			}

			.img_cancel {
				border: 1px solid var(--default-color1);
			}

			.img_reasale {
				background-color: #C0A572;
				color: var(--default-color1);
			}
		}
	}

	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: var(--default-color1);

		.icon {
			position: absolute;
			left: 0rpx;
			top: 20rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}

	.webView_class {
		height: 750rpx;
	}
	.new-modal-content {
		padding: 35rpx 40rpx;
		background-color: #34323D;
		.success_img {
			display: flex;
			justify-content: center;
			align-items: center;
	
			image {
				width: 160rpx;
				height: 160rpx;
			}
		}
	
		.modal-content{
			padding:0rpx 0rpx 30rpx 0rpx;
			border-bottom:1rpx solid #53505D;
			font-size:28rpx;
			color:#fff;
			.title{
				font-size:34rpx;
				font-weight:600;
			}
			.msg_view{
				font-size:28rpx;
				line-height:34rpx;
				margin-top:30rpx;
				.active_msg{
					font-size:24rpx;
					color:#63EAEE;
					margin-top:10rpx;
				}
			}
			
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			
			>view {
				width: 236rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx; 
				font-weight: bold;
				border-radius: 14rpx;
				color:rgba(255, 255, 255, 0.5);
			}
			.img_cancel {
				border: 1px solid rgba(255, 255, 255, 0.5);
			}
			.img_reasale {
				color:var(--default-color2);
				background: var(--primary-button-color);
			}
			&.center{
				justify-content: center;
				.img_reasale {
					width:300rpx;
				}
			}
		}
	}
</style>