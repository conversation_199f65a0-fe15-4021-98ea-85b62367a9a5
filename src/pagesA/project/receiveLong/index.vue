<template>
  <view id="app">
	 <view style="text-align: center;height:100vh;line-height: 100vh;background-color:#fff;">
	 		欢迎来到暴躁龙领取页
	 </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
		code:''
    };
  }, 
  onLoad(option) {
		this.code = option.code
		setTimeout(()=>{
			this.nav_wx()
		},100)
  },
  methods: {
    nav_wx(){
		const obj = {
			code:this.code
		 }
		 let queryString = Object.keys(obj).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`).join('&') // 把对象转换成 a=1&b=2&c=3&d=4 的字符串格式
		        const res = encodeURIComponent(queryString)
		console.log(`weixin://dl/business/?appid=wxe10732abb53b15a1&path=pages/long/receive/receive&query=${res}&env_version=release`)
		window.location.href = `weixin://dl/business/?appid=wxe10732abb53b15a1&path=pages/long/receive/receive&query=${res}&env_version=release`
	}
  }
};
</script>

<style>

</style>