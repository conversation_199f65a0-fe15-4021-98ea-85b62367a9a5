<template>
	<view class="plutocrat">
		<view class="barHeight"></view>
		<view class="left_icon" @tap="nav_back()" :style="{'top':`${10+height}rpx`}" v-if="!isApp">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20240304/bb92e20e90212f1c693f90df3df739e7_80x80.png"
				mode="widthFix"></image>
		</view>
		<view class="tab">
			<u-tabs :list="tabsList" font-size='28' inactive-color='#ccc' active-color='#fff' :current="current"
				bg-color="#35333E" :bar-style='barStyle' @change="change"></u-tabs>
		</view>
		<view class="headImg">
			<image src="https://cdn-lingjing.nftcn.com.cn/image/20240409/63c181bdedb311b66a856f4ef380e95e_750x400.gif" mode="widthFix"></image>
			<image src="@/static/imgs/plutocrat/wenhao.png" mode="aspectFill" v-show="current==1" @click="wenShow=true">
			</image>
		</view>
		<view class="position">
			<view class="box">
				<view class="head">
					
					{{current==0?'每10分钟更新一次':'每日8:00更新一次'}}
				</view>
				<view class="tit">
					<view>排名</view>
					<view>用户</view>
					<view>{{current==0?'今日':'综合'}}{{current==0?'买入':'盈利 '}}</view>
				</view>
				<scroll-view scroll-y="true" style="width: 100%;height:60vh;">
					<view class=" list" v-for="item in list" :key="item.index" @click="clickId=item.index">
						<view>
							<view class="topHead" v-if="item.index<=5">
								TOP{{item.index}}
							</view>
							<view class="topFoot" v-else>
								TOP{{item.index}}
							</view>
						</view>
						<view>
							<image :src="`${item.avatar}?x-oss-process=image/resize,m_lfit,h_50,w_50`" mode="aspectFill"></image>
							<view>{{item.userMark}}</view>
						</view>
						<view class="btn" v-if="clickId==item.index" @click="details(item)">查看他的买入</view>
						<view class="text" v-else>￥ {{item.score.toLocaleString('en-US')}}</view>
					</view>
					<view class="null_body" v-show="list==''">
						<view class="null">
							<view class="img">
								<image
									src="https://cdn-lingjing.nftcn.com.cn/image/20240310/99a2327013020342e6b3b771db9faa0a_480x480.png"
									mode="widthFix"></image>
							</view>
							<view class="text">
								暂无数据
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="bot login" v-if="selfInfo">
			<view>
				<view class="nonebang" v-if="selfInfo.index==null">
					<view>未上榜</view>
				</view>
				<view class="bang" v-else>
					<view>排名</view>
					<view>{{selfInfo.index}}</view>
				</view>

				<view class="one">
					<view>
						<image :src="selfInfo.avatar" mode="aspectFill"></image>
					</view>
					<view>{{selfInfo.userMark}}</view>
					<view class="btn" @click="show=true">隐私设置</view>
				</view>
			</view>
			<view class="color">
				<view>￥{{selfInfo.score}}</view>
				<!-- <view class="none" v-show="selfInfo.index>100">距离上榜还差{{selfInfo.gapScore}}</view> -->
				<!-- <view class="none" v-show="selfInfo.index<=100">距离{{selfInfo.gapScore}}</view> -->
				<view v-if="selfInfo.index==1" class="none">恭喜您成为{{current==1?"买手榜":"富豪榜"}}榜首</view>
				 <view class="none" v-else-if="selfInfo.index<101&&selfInfo.index">
					距离上一名还差{{selfInfo.gapScore}}
				 </view >
				<view class="none" v-else>
					距离上榜还差{{selfInfo.gapScore}}
				</view >
			</view>
		</view>
		<view class="bot" v-else>
			<view class="one">
				<view>
					<image src="@/static/imgs/invite/friend.png" mode="aspectFill"></image>
				</view>
				<view>未登录</view>
			</view>
			<view @click="login" class="logBtn">一键登录</view>
		</view>
		<u-modal class="" v-model="isLoadding" width="40%" :show-title="false" :show-confirm-button="false">
			<div class='sk-wave'></div>
			<view class="text_msg"
				style="padding: 10rpx 20rpx 40rpx 20rpx;text-align: center;font-size:26rpx;line-height:40rpx;">
				跳转登录中...
			</view>
		</u-modal>
		<u-popup v-model="show" mode="center">
			<view class="privacy">
				<view>
					<view @click="isAgreement= 1">
						<view>
							<image mode="aspectFill" src="@/static/login/jxs2x.png" v-if="isAgreement==1">
							</image>
							<image mode="aspectFill" src="@/static/login/jx.png" v-else>
							</image>
						</view>
						<view>允许他人看我的买入</view>
					</view>
					<view @click="isAgreement= 0">
						<view>
							<image mode="aspectFill" src="@/static/login/jxs2x.png" v-if="isAgreement==0">
							</image>
							<image mode="aspectFill" src="@/static/login/jx.png" v-else>
							</image>
						</view>
						<view>不让他人看我的买入</view>
					</view>

				</view>
				<view>
					<view @click="show=false">取消</view>
					<view @click="submit">确认</view>
				</view>
			</view>
		</u-popup>
		<u-popup v-model="wenShow" mode="center">
			<view class="wenhao">
				根据用户的实际情况，计算用户的实盈+浮盈。实盈=钱包变化=余额变化-充值-非余额购买+提现，浮盈=仓位变化。
			</view>
		</u-popup>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				wenShow: false,
				clickId: null,
				flag: false,
				tabsList: [{
					name: '今日富豪榜',
					value: ''
				}, {
					name: '专业买手榜',
					value: ''
				}],
				barStyle: {
					'background': 'linear-gradient( 90deg, #EF91FB 0%, #40F8EC 100%)',
				},
				current: 0,
				isAgreement: 1,
				show: false,
				show_login: false,
				isLoadding: false,
				selfInfo: {
					index:-1
				},
				height:"",
				isApp:false
			}
		},
		onLoad(options) {
			const {
				token,
				platform,
			} = options;
			this.platform = platform
			if (token) {
				uni.setStorageSync('token', token);
				this.isApp = true
			}
			if (platform) {
				uni.setStorageSync('is_platform', platform);
			}
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					_this.height = res.statusBarHeight*2
				}
			});
			this.getList()
		},
		methods: {
			async getList() { //富豪榜列表
				let {
					status,
					result
				} = await this.$api.java_rankingList()
				if (status.code == 0) {
					this.list = result.list
					this.selfInfo = result.selfInfo
				} else if (status.code == 1002) {
					this.show_login = true
				}
			},
			async getIncomeList() { //买手榜
				let {
					result,
					status
				} = await this.$api.incomeList()
				if (status.code == 0) {
					this.list = result.list
					this.selfInfo = result.selfInfo
				}
			},
			async submit() { //是否允许他人看我的买入
				let {
					result,
					status
				} = await this.$api.java_setShow({
					isShow: this.isAgreement, //0不让 1让
				})
				if (status.code == 0) {
					uni.showToast({
						title: '设置成功',
						icon: 'none'
					})
				}
			},
			details(item) { //买入详情
				this.$Router.push({
					name: "buyDetails",
					params: {
						name: item.userMark,
						index:item.index?item.index:"",
						platform:this.platform,
						type:this.current==0?1:2
					},
				});
			},
			change(e) {
				this.current = e
				this.list = []
				this.selfInfo = {}
				if (this.current == 0) {
					this.getList()
				} else {
					this.getIncomeList()
				}
			},
			login() {
				this.isLoadding = true
				setTimeout(() => {
					this.isLoadding = false
					this.$Router.push({
						name: "mainLogin"
					})
				}, 1500);
			},
			nav_back() {
				this.$Router.back()
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		height: 100%;
		color: #fff;
	}


	.plutocrat {
		width: 100%;
		height: 100%;
		background: linear-gradient(180deg, #35333E 50%, #EC93FB 100%);
		overflow: hidden;
		box-sizing: border-box;
		.left_icon {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			left:30rpx;
			top:10rpx;
			image {
				width: 70rpx;
			}
		}
	}

	.tab {
		width: fit-content;
		margin: 0 auto;
	}

	.headImg {
		width: 100%;
		height: 400rpx;
		position: relative;

		>image:nth-child(1) {
			width: 100%;
			height: 100%;
		}

		>image:nth-child(2) {
			width: 60rpx;
			height: 60rpx;
			position: absolute;
			top: 436rpx;
			right: 49rpx;
			z-index: 99;
		}
	}

	.position {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.box {
		width: 660rpx;
		background: #35333E;
		border-radius: 36rpx;
		overflow: hidden;
		position: absolute;
		top: -50rpx;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 20rpx;
		box-sizing: border-box;
		overflow: hidden;

		.head {
			width: 100%;
			height: 70rpx;
			text-align: center;
			line-height: 70rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #ccc;
			background: #000;
		}

		.tit {
			width: 100%;
			height: 90rpx;
			font-weight: 400;
			font-size: 24rpx;
			color: #ccc;
			display: flex;
			align-items: center;
			text-align: center;

			>view {
				width: 33.33%;
			}
		}

		.list {
			width: 100%;
			height: 100rpx;
			display: flex;
			align-items: center;
			text-align: center;
			>view {
				width: 33.33%;
			}

			>view:nth-child(1) {
				font-weight: bold;
				font-size: 20rpx;
				color: #FFFFFF;

				.topHead {
					background: url('@/static/imgs/plutocrat/topHead.png') no-repeat;
					background-size: cover;
				}

				.topFoot {
					background: url('@/static/imgs/plutocrat/topFoot.png') no-repeat;
					background-size: cover;
				}

				>view {
					width: 70rpx;
					margin: 0 auto;
				}
			}

			>view:nth-child(2) {
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				text-align: left;

				>view {
					flex: 1;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
				}

				>image {
					width: 60rpx;
					height: 60rpx;
					margin-right: 12rpx;
					border-radius: 18rpx;
				}
			}

			.btn {
				width: 150rpx;
				height: 44rpx;
				line-height: 44rpx;
				background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 30rpx;
				text-align: center;
				font-weight: bold;
				font-size: 18rpx;
				color: #141816;
			}

			.text {
				font-weight: 400;
				font-size: 24rpx;
				color: #63EAEE;
			}
		}
	}

	.login {
		>view:nth-child(1) {
			display: flex;
			align-items: center;
			text-align: center;

			.bang {
				font-weight: 400;
				font-size: 18rpx;
				margin-right: 40rpx;
				margin-bottom: 5rpx;
				color: #ccc;

				>view:nth-child(2) {
					color: #FFFFFF;
					font-weight: bold;
					font-size: 30rpx;
				}
			}

			.nonebang {
				font-weight: bold;
				font-size: 24rpx;
				color: #FFFFFF;
				margin-right: 30rpx;
			}
		}

		.color {
			font-weight: 400;
			font-size: 28rpx;
			color: #63EAEE;
			text-align: right;

			.none {
				font-weight: 400;
				font-size: 22rpx;
				color: #ccc;

			}
		}

	}

	.bot {
		width: 700rpx;
		height: 120rpx;
		background: #46454F;
		box-shadow: 0rpx 4rpx 12rpx 1rpx rgba(0, 0, 0, 0.4);
		border-radius: 28rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40rpx;
		box-sizing: border-box;
		position: absolute;
		bottom: 50rpx;
		left: 50%;
		transform: translateX(-50%);

		.one {
			display: flex;
			align-items: center;

			>view:nth-child(1) {
				width: 60rpx;
				height: 60rpx;
				margin-right: 12rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
				overflow: hidden;

				>image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}

			>view:nth-child(2) {
				width: 130rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color:#fff;
			}

			.btn {
				width: 90rpx;
				height: 30rpx;
				line-height: 30rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				text-align: center;
				font-weight: bold;
				font-size: 16rpx;
				color: #141816;
				text-align: center;
				margin-left: 10rpx;
			}
		}

		.logBtn {
			width: 180rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
			border-radius: 30rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #141816;
		}
	}

	.privacy {
		width: 500rpx;
		height: 300rpx;
		background: #35333E;
		border-radius: 15rpx;
		padding: 50rpx;
		box-sizing: border-box;
		text-align: center;

		>view:nth-child(1) {
			width: 100%;
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;

			>view {
				width: 100%;
				display: flex;
				align-items: center;
				margin-bottom: 50rpx;
				justify-content: center;

				>view:nth-child(1) {
					width: 30rpx;
					height: 30rpx;
					margin-right: 20rpx;

					>image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}

		>view:nth-child(2) {
			display: flex;
			align-items: center;
			justify-content: space-around;

			>view:nth-child(1) {
				width: 150rpx;
				height: 44rpx;
				line-height: 44rpx;
				text-align: center;
				font-weight: bold;
				font-size: 20rpx;
				color: #fff;
				border: 1rpx solid #EC93FB;
				border-radius: 30rpx;
				box-sizing: border-box;
			}

			>view:nth-child(2) {
				width: 150rpx;
				height: 44rpx;
				line-height: 44rpx;
				background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 30rpx;
				text-align: center;
				font-weight: bold;
				font-size: 20rpx;
				color: #141816;
			}
		}
	}

	.wenhao {
		width: 500rpx;
		// height: 300rpx;
		background: #141e25;
		border-radius: 30rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
		line-height:42rpx;
		>view {
			margin-bottom: 30rpx;
			display: flex;
			align-items: flex-start;
			line-height:30rpx;
			>view:nth-child(1) {
				width: 120rpx;
				color: #63EAEE;
				margin-right: 10rpx;
				text-align: right;
				
			}
		}
		.jinri{
			
			>view:nth-child(1) {
				width:350rpx;
			}
		}
	}
	.null_body {
		.null {
			.img {
				display: flex;
				justify-content: center;
	
				image {
					width: 242rpx;
				}
			}
	
		}
	
		.text {
			color: #A6A6A6;
			font-size: 28rpx;
			text-align: center;
			margin-top: 30rpx;
		}
		width:100%;
		height: 40vh;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>