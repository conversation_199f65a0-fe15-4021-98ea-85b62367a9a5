<template>
    <div class="referral-page">
        <!-- 顶部介绍区域 -->
        <div class="intro">
            <div class="left">
                <span class="tag">邀请好友</span>
                <span class="title">即享20%佣金</span>
                <div class="plan-btn" @click="nav_to('broker_plan')">代理人计划</div>
            </div>

            <img v-if="!token" class="hero-img" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377671534675058688.png"
                alt="illustration" />
            <div class="invite-box" v-else>
                <!-- 推荐码 -->
                <div class="invite-row">
                    <div class="label">推荐码</div>
                    <div class="value-box">
                        <span class="value">{{ inviteCodes.inviteCode || '--' }}</span>
                        <div class="copy-icon" @click="copy(inviteCodes.inviteCode)">
                            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382312631980351488.png" />
                        </div>
                    </div>
                </div>

                <!-- 两个邀请链接 -->
                <!-- <div class="invite-link-group">
                    <div class="invite-row" style="flex:1" v-for="(item, index) in links" :key="index">
                        <span class="label">{{ item.label }}</span>
                        <div class="value-box">
                            <span class="value">{{ item.url }}</span>
                            <div class="copy-icon" @click="copy(item.url)">
                                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377728141459021824.png" />
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- 按钮 + 二维码 -->
                <div class="invite-action">
                    <div class="invite-btn" @click="show = true">邀请好友</div>
                    <div class="qrcode-btn" @click="postshow = true">
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382313404885721088.png" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 三个步骤 -->
        <section class="steps" v-if="!token">
            <div class="title-container-card">
                <span class="title">邀请返佣</span>
            </div>
            <div class="cards">
                <div class="card" v-for="item in stepCards" :key="item.title">
                    <img :src="item.icon" class="icon" />
                    <div class="right">
                        <span class="card-title">{{ item.title }}</span>
                        <span class="card-desc">{{ item.desc }}</span>
                    </div>

                </div>
            </div>
        </section>

        <!-- 数据总览 -->
        <section class="overview" v-if="token">
            <div class="overview-title">
                <span>数据总览</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382315740962054144.png" />
            </div>
            <div class="overview-tabs">
                <div class=" tab" @click="isMenuVisible = !isMenuVisible">
                    <span>{{ rebateTabs2[nowref].label }}</span>
                    <img class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382317018425417728.png" alt="">

                    <transition name="fade">
                        <div v-if="isMenuVisible" class="language-menu" @click.stop ref="languageMenu">
                            <div v-for="(language, index) in rebateTabs2" :key="index" class="item"
                                @click="selectLanguage(language, index)">
                                {{ language.label }}
                                <img v-if="index == nowref"
                                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250327/046a950e72e553c6f91489c57eb7d08b_64x64.png" />
                            </div>
                        </div>
                    </transition>
                </div>
                <div class="ops-container">
                    <div v-for="(item, index) in tabOptions" :key="index"
                        :class="['tab-btn', { active: currentTab === item.key }]" @click="changeTab(item.key)">
                        {{ item.label }}
                    </div>
                </div>
            </div>
            <div class="overview-panels">
                <div class="panel">
                    <div class="panel-title">我的返佣

                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382319801862021120.png" alt="">
                    </div>
                    <div class="panel-value">{{ mockData.totalRebateAmount || 0 }} <span class="symbol">USDT</span>
                    </div>
                </div>
                <div class="panel">
                    <div class="panel-title">邀请好友数
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382319801862021120.png" alt="">
                    </div>
                    <div class="panel-value">{{ mockData.totalTradedInvitee || 0 }} <span class="symbol">人</span></div>
                </div>
            </div>
        </section>

        <!-- 我的好友 -->
        <section class="friends" v-if="token">
            <div class="overview-title">
                <span>我的好友</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382315740962054144.png" />
            </div>
            <div class="friend-list">
                <div class="friend-card" v-for="(row, index) in pagedFriends" :key="index">
                    <div class="item-row">
                        <span class="label">注册时间</span>
                        <span class="value">{{ formatTimestamp(row.registerTime) }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">邀请好友</span>
                        <span class="value">{{ formatPhone(row.phone) }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">是否交易</span>
                        <span class="value">{{ formatTrade(row) }}</span>
                    </div>
                </div>

                <nodata v-if="!pagedFriends.length" />

                <div class="load-more" v-if="pagedFriends.length > 0">
                    <div class="more-button" @click="nextPage(totalFriendPages)">更多
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382326534319726592.png" alt="">
                    </div>
                </div>
            </div>
        </section>

        <!-- 我的返佣 -->
        <section class="friends" v-if="token">
            <div class="overview-title">
                <span>我的返佣</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382315740962054144.png" />
            </div>
            <!-- <div class="rebate-tabs">
                <button v-for="item in rebateTabs" :key="item.key"
                    :class="['tab-btn', { active: currentRebateTab === item.key }]"
                    @click="currentRebateTab = item.key">
                    {{ item.label }}
                </button>
            </div> -->

            <div class="ops-container">
                <div v-for="(item, index) in rebateTabs" :key="index"
                    :class="['tab-btn', { active: currentRebateTab === item.key }]" @click="checkRebateTab(item.key)">
                    {{ item.label }}
                </div>
            </div>
            <div class="friend-list">
                <div class="friend-card" v-for="(row, index) in pagedRebates" :key="index">
                    <div class="item-row">
                        <span class="label">日期</span>
                        <span class="value">{{ formatTimestamp(row.rebateTime) }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">手机号</span>
                        <span class="value">{{ formatPhone(row.inviteePhone) }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">类型</span>
                        <span class="value">{{ formatType(row.tradeType) }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">返佣笔数</span>
                        <span class="value">{{ row.rebateAmount }}</span>
                    </div>
                    <div class="item-row">
                        <span class="label">状态</span>
                        <span class="value">{{ row.rebateStatus == 'WAIT' ? '待发放' : '已发放' }}</span>
                    </div>
                </div>
                <nodata v-if="!pagedRebates.length" />

                <div class="load-more" v-if="pagedRebates.length > 0">
                    <div class="more-button" @click="nextPageRebate(totalRebatePages)">更多
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382326534319726592.png" alt="">
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="faq">
            <div class="title-container-card">
                <span class="title">FAQ</span>
            </div>

            <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
                <div class="faq-question" @click="toggle(index)">
                    <span>{{ item.question }}</span>
                    <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382307585527996416.png" alt="arrow" />
                </div>

                <transition name="faq-slide">
                    <div class="faq-answer" v-show="activeIndex === index">
                        {{ item.answer }}
                    </div>
                </transition>
            </div>

            <!-- <div class="faq-list">
                <div v-for="(item, index) in faqList" :key="index" class="faq-item">
                    <div class="question">{{ item.q }}</div>
                </div>
            </div> -->
        </section>

        <!-- 底部按钮 -->
        <footer class="footer">
            <!-- <button class="footer-btn"></button>
            <button class="login-btn">登录</button> -->

            <div class="inner" v-if="!token">
                <span>立即赚取返佣</span>
                <div class="login-btn" @click="nav_to('login')">登录</div>
            </div>
            <div class="inner" v-else>
                <span>PinkWallet 代理人申请</span>
                <div class="login-btn" @click="nav_to('login')">申请</div>
            </div>
        </footer>
    </div>

    <InviteQRCodeModal v-model:visible="show" :invite-code="inviteCodes.inviteCode" />
    <SharePosterModal v-model:visible="postshow"
        :link="`https://pinkwallet.com/h5/pages/login/register?code=${inviteCodes.inviteCode}`" />
    <van-calendar color="#EF88A3" v-model:show="showCal" type="range" @confirm="onConfirm" />
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { GetUserInfo } from "@/api/pinkwallet"
import { getMyRebate, getDataOverview, getMyFriends, getInviteCode, submitAgentApply } from "@/api/pinkexchange.js"
// import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
import InviteQRCodeModal from './component/InviteQRCodeModal.vue'
import SharePosterModal from './component/SharePosterModal.vue'
import nodata from "./component/nodata.vue"
import { Toast, Calendar } from 'vant'
import { judgePlatform } from '@/utils/platform';
const startTimestampS = ref(null)
const endTimestampS = ref(null)

const showCal = ref(false)
const postshow = ref(false)
const show = ref(false)
const nowref = ref(0)
const router = useRouter()

const { platform, token } = route.query;

// const { t } = useI18n();
const activeIndex = ref(null)
const currentTab = ref(null)

// const token = computed(() => {
//     return localStorage.getItem('token')
// })
console.log(token);
const isMenuVisible = ref(false)


const nav_to = (e) => {
    if (e == 'login') {
        Toast('请先登录')
        return
    }
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    })
}
const currentRebateTab = ref(null)
// formatsubStatus(value) {
//             const item = this.Substatus.find(i => i.value === value)
//             return item ? item.label : value
//         },
const formatType = (type) => {
    const item = rebateTabs2.find(i => i.key == type)
    return item ? item.label : type
}


const formatTimestamp = (seconds) => {
    if (!seconds) {
        return '--'
    }
    const date = new Date(seconds * 1000); // 转成毫秒时间戳
    const Y = date.getFullYear();
    const M = String(date.getMonth() + 1).padStart(2, '0');
    const D = String(date.getDate()).padStart(2, '0');
    const h = String(date.getHours()).padStart(2, '0');
    const m = String(date.getMinutes()).padStart(2, '0');
    const s = String(date.getSeconds()).padStart(2, '0');
    return `${Y}-${M}-${D} ${h}:${m}:${s}`;
}

const friends = ref([
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // },
    // {
    //     registerTime: '2018-09-09',
    //     phone: 12345678901,
    //     contractTraded: true,
    //     usStockTraded: true,
    //     hkStockTraded: true,
    // }
])


const fetchgetMyFriends = async () => {
    let res = await getMyFriends({ itemLimit: 100 })
    if (res.code == 200 && res.result.length) {
        friends.value = res.result
    }

}

const rebates = ref([
    // {
    //     rebateTime: '2018-09-09',
    //     phone: '186****1223',
    //     rebateType: '发放返佣',
    //     count: 5,
    //     rebateTime: '2018-09-09 04:00:00',
    //     rebateStatus: '已发放'
    // },
    // {
    //     rebateTime: '2018-09-09',
    //     phone: '186****1223',
    //     rebateType: '发放返佣',
    //     count: 5,
    //     rebateTime: '2018-09-09 04:00:00',
    //     rebateStatus: '已发放'
    // },
    // {
    //     rebateTime: '2018-09-09',
    //     phone: '186****1223',
    //     rebateType: '发放返佣',
    //     count: 5,
    //     rebateTime: '2018-09-09 04:00:00',
    //     rebateStatus: '已发放'
    // },
    // {
    //     rebateTime: '2018-09-09',
    //     phone: '186****1223',
    //     rebateType: '发放返佣',
    //     count: 5,
    //     rebateTime: '2018-09-09 04:00:00',
    //     rebateStatus: '已发放'
    // },
    // {
    //     rebateTime: '2018-09-09',
    //     phone: '186****1223',
    //     rebateType: '发放返佣',
    //     count: 5,
    //     rebateTime: '2018-09-09 04:00:00',
    //     rebateStatus: '已发放'
    // }
])


/**
 * 我的返佣
 */
const FetchgetMyRebate = async () => {
    let res = await getMyRebate({
        itemLimit: 500,
        tradeType: currentRebateTab.value
    })
    if (res.code == 200 && res.result.length) {
        rebates.value = res.result
    } else {
        rebates.value = []
    }
}

const checkRebateTab = (tab) => {
    currentRebateTab.value = tab
    FetchgetMyRebate()
}



const formatPhone = (phone) => {
    if (!phone) {
        return '--'
    }
    const str = String(phone)
    return str.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
}



const formatTrade = (row) => {
    return [
        row.contractTraded ? '合约' : '',
        row.usStockTraded ? '美股' : '',
        row.hkStockTraded ? '港股' : ''
    ].filter(Boolean).join('、')
}

const pagination = reactive({ page: 0, size: 4 })
const paginationRebate = reactive({ page: 0, size: 4 })

const totalFriendPages = computed(() => Math.ceil(friends.value.length / pagination.size))
const totalRebatePages = computed(() => Math.ceil(rebates.value.length / paginationRebate.size))

const pagedFriends = computed(() => friends.value.slice(pagination.page * pagination.size, (pagination.page + 1) * pagination.size))
const pagedRebates = computed(() => rebates.value.slice(paginationRebate.page * paginationRebate.size, (paginationRebate.page + 1) * paginationRebate.size))

function nextPage(totalPages) {
    if (pagination.page < totalPages - 1) pagination.page++
}
function prevPage() {
    if (pagination.page > 0) pagination.page--
}
function nextPageRebate(totalPages) {
    if (paginationRebate.page < totalPages - 1) paginationRebate.page++
}
function prevPageRebate() {
    if (paginationRebate.page > 0) paginationRebate.page--
}
const nowreflabel = ref('')

const selectLanguage = (language, index) => {
    console.log(currentTab.value);
    
    nowref.value = index
    isMenuVisible.value = false;
    nowreflabel.value = language.key
    getDataOverviewDatas(startTimestampS.value, endTimestampS.value);
};

const mockData = {
    totalRebateAmount: '',
    totalTradedInvitee: ""
}
const formatYMD = (date) => {
    const y = date.getFullYear();
    const m = `${date.getMonth() + 1}`.padStart(2, '0');
    const d = `${date.getDate()}`.padStart(2, '0');
    return `${y}.${m}.${d}`;
};
// const date = ref('');
const formatDate = (date) => `${date.getMonth() + 1}/${date.getDate()}`;
const onConfirm = (values) => {
    const [start, end] = values;
    console.log(formatDate(start)); // 7/3 Fri Jul 11 2025 00:00:00 GMT+0800 (中国标准时间)
    // 转换为秒级时间戳（毫秒转秒）
    const startTimestamp = Math.floor(start.getTime() / 1000);
    const endTimestamp = Math.floor(end.getTime() / 1000);
    showCal.value = false;
    startTimestampS.value = startTimestamp;
    endTimestampS.value = endTimestamp;
    // 格式化年月日
    const label = `${formatYMD(start)} - ${formatYMD(end)}`;
    console.log(label);

    // 替换 tabOptions 中的 decent 的 label
    const decentTab = tabOptions.find(item => item.key == 'decent');
    console.log(decentTab);

    if (decentTab) {
        decentTab.label = label;
    }

    //     const tabOptions = [
    //     { key: 'all', label: '全部时间' },
    //     { key: 'today', label: '昨日' },
    //     { key: 'top', label: '最近7天' },
    //     { key: '30day', label: '最近30天' },
    //     { key: 'decent', label: '2025.02.11 - 2025.05.12' },
    // ]
    getDataOverviewDatas(startTimestamp, endTimestamp);
    // date.value = `${formatDate(start)} - ${formatDate(end)}`;
};

const changeTab = (index) => {
    if (index == 'decent') {
        showCal.value = true
    }
    currentTab.value = index
    // activeIndex.value = index
    getDataOverviewDatas()
};


/**
 * 数据总览
 */
const getDataOverviewDatas = async (a, b) => {

    const now = new Date();
    let timeBegin = null;
    let timeEnd = null;

    // const key = tabOptions[currentTab.value].key;
    const key = currentTab.value

    switch (key) {
        case 'today': // 昨日
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case 'top': // 最近7天（不含今天）
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case '30day': // 最近30天（不含今天）
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case 'decent': // 固定时间范围
            // timeBegin = new Date('2025-09-11T00:00:00').getTime() / 1000;
            // timeEnd = new Date('2025-09-12T23:59:59').getTime() / 1000;
            timeBegin = null
            timeEnd = null
            break;

        case 'all': // 全部时间
        default:
            timeBegin = null;
            timeEnd = null;
            break;
    }

    let res = await getDataOverview({
        timeBegin: timeBegin ? timeBegin : a,
        timeEnd: timeEnd ? timeEnd : b,
        tradeType: rebateTabs2[nowref.value].key
    })
    if (res.code == 200) {
        const { totalRebateAmount, totalTradedInvitee } = res.result;
        mockData.totalRebateAmount = totalRebateAmount;
        mockData.totalTradedInvitee = totalTradedInvitee;
    }
};

const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}


const rebateTabs = [
    { key: null, label: '全部' },
    { key: 'CONTRACT', label: '合约' },
    { key: 'HK_STOCK', label: '港股' },
    { key: 'US_STOCK', label: '美股' }
]

const rebateTabs2 = [
    { key: 'CONTRACT', label: '合约' },
    { key: 'HK_STOCK', label: '港股' },
    { key: 'US_STOCK', label: '美股' }
]

const tabOptions = [
    { key: null, label: '全部时间' },
    { key: 'today', label: '昨日' },
    { key: 'top', label: '最近7天' },
    { key: '30day', label: '最近30天' },
    { key: 'decent', label: '2025.02.11 - 2025.05.12' },
]

const inviteCodes = ref({})
const getInviteCodes = async () => {
    let res = await getInviteCode()
    if (res.code == 200) {
        inviteCodes.value = res.result
        // links[0].url = import.meta.env.VITE_APP_BASE_URL + res.result.inviteCode
        // links[1].url = import.meta.env.VITE_APP_BASE_URL + res.result.inviteCode

    }
    // console.log(res)
}

const stepCards = [
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377671064850096128.png",
        title: '查询邀请链接',
        desc: '立即获取你的专属邀请链接'
    },
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377671184031244288.png",
        title: '邀请好友',
        desc: '将您的推荐链接或二维码发送好友或者发布在社交媒体'
    },
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377671326096515072.png",
        title: '畅想收益',
        desc: '当好友开始交易后，赢取高额佣金'
    }
]


const faqListZh = computed(() => [
    {
        question: "什么是PinkWallet邀请返佣计划？",
        answer: "PinkWallet邀请返佣计划让您推荐好友加入PinkWallet平台，并从好友的交易手续费中赚得奖励。 作为邀请人，您最多可以获得受邀好友支付的净交易手续费的 20%作为奖励， 当您邀请的好友交易额达到一定门槛，即可一键申请 合伙人 计划，享45%起的无限层级返佣。"
    },
    {
        question: "为何要加入PinkWallet邀请返佣计划？",
        answer: "1.返佣场景多，覆盖合约，以及美股、港股多种交易。2.返佣速度快，次日即返，无需等待多个工作日。3.返佣比例业内最高水平，PinkWallet邀请返佣计划最高可返20%。4.潜在收益高，满足门槛要求，即可升级为 合伙人 享受无限级返佣，最低45%起。"
    },
    {
        question: "如何通过PinkWallet邀请返佣计划赚取收益？",
        answer: `1.设置佣金分享比例
        设置您想与好友分享多少推荐佣金。

2.邀请好友，建立联系
与好友和社交媒体分享您的推荐链接或二维码。

3.共同获利
在好友开始交易后赚取高达20%的佣金。`
    },
    {
        question: "如何能升级成为PinkWallet合伙人计划，享受无限返佣？",
        answer: "只要达到对应的门槛要求，你可以升级为PinkWallet并享受无限层级，终身返佣。在邀请返佣界面即可一键申请成为 PinkWallet 合伙人。"
    }]);

const faqList = [
    { q: '什么是PinkWallet邀请返佣计划？' },
    { q: '为何要加入PinkWallet邀请返佣计划？' },
    { q: '如何通过PinkWallet邀请返佣计划赚取收益？' },
    { q: '如何将我升级为PinkWallet返佣计划，享受无限返佣？' }
]

// // 获取ios/android传来的token
judgePlatform(platform, token).then((res) => {
    // info()
    FetchgetMyRebate()
    getDataOverviewDatas(startTimestampS.value, endTimestampS.value)
    fetchgetMyFriends()
    getInviteCodes()


});
</script>

<style lang="scss" scoped>
.referral-page {
    // padding: 40px 20px;
    min-height: 100vh;
    // margin: 0 auto;
    width: 100vw;
    overflow: hidden;

    .intro {
        margin-top: px2vw(42px*2);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        text-align: center;
        gap: px2vw(40px);
        width: 100%;

        .left {
            display: flex;
            flex-direction: column;
            align-items: center;

            .tag {
                line-height: px2vw(19px);
                color: #EF88A3;
                font-family: MiSans;
                font-weight: 700;
                font-size: px2vw(14px*2);
                text-align: center;
                text-transform: capitalize;
            }

            .title {
                font-family: MiSans;
                font-weight: 700;
                font-size: px2vw(28px*2);
                line-height: px(37px);
                text-transform: capitalize;
                color: #000;
                margin: px2vw(10px) 0 px2vw(13px) 0;
            }

            .plan-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #EF88A3;
                color: #fff;
                border: none;
                width: px2vw(117px*2);
                height: px2vw(39px*2);
                border-radius: 8px;

                font-family: MiSans;
                font-weight: 600;

                font-size: px2vw(14px*2);
            }
        }



        .hero-img {
            display: flex;
            align-items: center;
            cursor: pointer;
            width: px2vw(263px*2);
            height: px2vw(211px*2);
            // margin: 0 auto;
            display: block;
        }

        .invite-box {
            width: 100%;
            padding: 0 px2vw(16px*2);
            display: flex;
            flex-direction: column;
            // gap: 12px;
            // max-width: 500px;
            // margin: 0 auto;

            .invite-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // align-items: center;
                border-radius: 8px;
                padding: 15px 20px;
                // font-size: 14px;
                background: #FBFBFB;

                // border: 1.13px solid #FFFFFF33;

                .label {
                    font-family: MiSans;
                    font-weight: 400;


                    font-size: px2vw(14px*2);
                    line-height: 150%;

                    color: #000;
                }

                .value-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .value {
                        width: 82px;
                        text-overflow: ellipsis;
                        color: #000;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 150%;
                        opacity: 0.6;
                    }

                    .copy-icon {
                        width: px2vw(17px*2);
                        ;
                        height: px2vw(17px*2);
                        ;

                        img {
                            cursor: pointer;
                            margin-left: 4px;
                            width: px2vw(17px*2);
                            height: px2vw(17px*2);
                            ;
                        }
                    }
                }
            }

            .invite-link-group {
                margin-top: px2vw(8px*2);
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .invite-action {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 14px;

                .invite-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                    background: #EF88A3;
                    border: none;
                    color: #fff;
                    cursor: pointer;

                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 16px;
                    height: px2vw(48px*2);
                    border-radius: 12px;

                }

                .qrcode-btn {

                    border-radius: 12px;
                    // font-size: 14px;
                    background: #FBFBFB;

                    border: 1.13px solid #FFFFFF33;
                    margin-left: 8px;
                    width: px2vw(48px*2);
                    ;
                    height: px2vw(48px*2);
                    ;

                    display: flex;
                    align-items: center;
                    justify-content: center;

                    cursor: pointer;

                    img {
                        width: 44px;
                        height: 44px;
                    }
                }
            }
        }
    }

    .steps {
        margin-top: px2vw(80px);


        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: px2vw(20px*2);
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #000;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: px2vw(20px*2); // 横线宽度
                    height: px2vw(2px); // 横线高度
                    background-color: rgba(0, 0, 0, .2)
                }

                &::before {
                    left: px2vw(-100px);
                }

                &::after {
                    right: px2vw(-100px);
                }
            }
        }

        .cards {
            margin: px2vw(28px*2) px2vw(16px*2) 0 px2vw(16px*2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: column;
            gap: 9px;
            // max-width: 1280px;

            .card {
                cursor: pointer;
                padding: 0;
                // flex: 1;
                min-height: px2vw(89px*2);
                width: 100%;
                // min-width: 421px;
                background: #FBFBFB;

                // border: 1.13px solid #363636;
                border-radius: 8px;
                // padding: 20px;
                text-align: center;
                display: flex;
                align-items: center;
                // justify-content: center;
                // flex-direction: column;

                .icon {
                    width: px2vw(31px*2);
                    height: px2vw(31px*2);
                    margin: 0 px2vw(5px*2) 0 px2vw(16px*2);
                }

                .right {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .card-title {
                        font-family: MiSans;
                        font-weight: 600;
                        font-size: px2vw(14px*2);
                        line-height: 150%;
                        text-align: center;
                        color: #000000;
                        margin: 0 0 px2vw(3px*2) 0;
                    }

                    .card-desc {
                        // width: 240px;
                        // white-space: wrap;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: px2vw(12px*2);
                        line-height: 150%;
                        text-align: center;
                        color: rgba(0, 0, 0, .5);
                    }
                }


            }
        }
    }

    .overview {
        // margin-bottom: 60px;
        margin: px2vw(80px*2) px2vw(16px*2) 0 px2vw(16px*2);

        .overview-title {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
                font-family: MiSans;
                font-weight: 600;
                font-size: px2vw(14px*2);
                line-height: 100%;
                color: #000;
            }

            img {
                cursor: pointer;
                width: px2vw(16px*2);
                ;
                height: px2vw(16px*2);
                ;
            }
        }

        .overview-tabs {
            display: flex;
            flex-direction: column;
            // gap: 12px;
            margin: 20px 0 20px 0;

            .tab {
                width: 100%;
                position: relative;
                background: #FBFBFB;
                height: px2vw(38px*2);
                border-radius: 28px;
                font-family: MiSans;
                font-weight: 500;
                font-size: px2vw(13px*2);
                line-height: 100%;
                color: #000;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;

                .arrow {
                    width: px2vw(11px*2);
                    height: px2vw(11px*2);
                }

                .language-menu {
                    position: absolute;
                    top: 79px;
                    left: 0px;
                    // width: 200px;
                    border-radius: 8px;
                    z-index: 10;
                    animation: slide-in 0.8s ease-out;
                    border-radius: 8px;
                    background: #1C1D1F;
                    transition: all .8s;

                    .items {
                        width: 150px;
                        height: 39px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        transition: all .8s;
                        padding-left: 14px;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 137%;
                        letter-spacing: 0px;
                        text-transform: capitalize;
                        color: rgba(255, 255, 255, .6);
                    }

                    .item {
                        width: 150px;
                        height: 39px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        cursor: pointer;
                        transition: all .8s;
                        padding: 0 17px 0 14px;
                        font-family: MiSans;
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 137%;
                        letter-spacing: 0px;
                        text-transform: capitalize;
                        color: #fff;

                        img {
                            width: 16px;
                            height: 16px;
                        }

                        &:hover {
                            &:last-child {
                                border-bottom-right-radius: 8px;
                                border-bottom-left-radius: 8px;

                            }

                            background-color: rgba(255, 255, 255, .1);
                        }
                    }
                }


                .fade-enter-active,
                .fade-leave-active {
                    transition: opacity 0.3s ease;
                }

                .fade-enter,
                .fade-leave-to {
                    opacity: 0;
                }

                @keyframes slide-in {
                    from {
                        transform: translateY(-20px);
                        opacity: 0;
                    }

                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
            }

            .ops-container {
                margin-top: 8px;
                display: flex;
                align-items: center;
                gap: 9px;
                overflow-x: auto;

                // 隐藏滚动条（适配 Chrome、Safari）
                &::-webkit-scrollbar {
                    display: none;
                }

                .tab-btn {
                    cursor: pointer;
                    white-space: nowrap;
                    padding: 14px 35px 16px;
                    border-radius: 28px;
                    border: none;
                    font-family: MiSans;
                    font-weight: 500;
                    font-size: 18px;
                    transition: background-color 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #FBFBFB;


                    font-family: MiSans;
                    font-weight: 500;
                    font-size: px2vw(13px*2);
                    line-height: 100%;
                    letter-spacing: 0px;
                    text-align: center;
                    color: #000;


                    img {
                        margin-left: 20px;
                        width: 16px;
                        height: 16px;
                    }

                    &.active {
                        color: #fff;

                        background: #EF88A3;
                    }
                }
            }


        }

        .overview-panels {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            gap: 12px;

            .panel {
                width: 100%;
                // background: #141414;
                // border-radius: 18px;
                // padding: 20px;
                // width: 316px;
                // height: 165px;
                display: flex;
                align-items: center;
                // flex-direction: column;
                justify-content: space-between;
                // padding-left: 28px;

                .panel-title {
                    color: #000;
                    opacity: .5;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    font-family: MiSans;
                    font-weight: 400;
                    font-size: px2vw(14px*2);

                    img {
                        width: px2vw(13px*2);
                        height: px2vw(13px*2);
                    }
                }

                .panel-value {

                    // margin-top: 14px;



                    color: #000;
                    display: flex;
                    align-items: center;

                    font-family: MiSans;
                    font-weight: 500;
                    font-size: px2vw(14px*2);
                    ;
                    line-height: 100%;
                    letter-spacing: 0px;
                    text-align: right;



                    // .symbol {
                    //     font-family: MiSans;
                    //     font-weight: 400;
                    //     font-size: 14px;
                    //     line-height: 19px;
                    //     margin: 20px 0 0 14px;
                    // }
                }
            }
        }
    }

    .friends {
        margin: px2vw(80px*2) px2vw(16px*2) 0 px2vw(16px*2);

        .ops-container {
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 9px;
            overflow-x: auto;

            // 隐藏滚动条（适配 Chrome、Safari）
            &::-webkit-scrollbar {
                display: none;
            }

            .tab-btn {
                cursor: pointer;
                white-space: nowrap;
                padding: 14px 35px 16px;
                border-radius: 28px;
                border: none;
                font-family: MiSans;
                font-weight: 500;
                font-size: 18px;
                transition: background-color 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #FBFBFB;


                font-family: MiSans;
                font-weight: 500;
                font-size: px2vw(13px*2);
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                color: #000;


                img {
                    margin-left: 20px;
                    width: 16px;
                    height: 16px;
                }

                &.active {
                    color: #fff;

                    background: #EF88A3;
                }
            }
        }

        .overview-title {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
                font-family: MiSans;
                font-weight: 600;
                font-size: px2vw(14px*2);
                line-height: 100%;
                color: #000;
            }

            img {
                cursor: pointer;
                width: px2vw(16px*2);
                ;
                height: px2vw(16px*2);
                ;
            }
        }

        .friend-list {

            // padding: 20rpx;
            .friend-card {
                // background: #fff;
                // border-radius: 16rpx;
                // padding: 24rpx;
                margin: 24px 0;
                padding-bottom: 24px;
                border-bottom: 1px solid rgba(0, 0, 0, .05);
                // margin-bottom: 20rpx;
                // box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

                .item-row {
                    margin-bottom: 24px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    //font-size: 28rpx;
                    // margin-bottom: 16rpx;

                    .label {
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: px2vw(14px*2);
                        line-height: 100%;
                        letter-spacing: 0px;
                        text-align: center;
                        color: rgba(0, 0, 0, .5);

                    }

                    .value {
                        font-family: MiSans;
                        font-weight: 500;
                        font-size: px2vw(14px*2);
                        ;
                        line-height: 100%;
                        letter-spacing: 0px;

                        color: #000;
                    }
                }
            }

            .load-more {
                display: flex;
                justify-content: center;
                margin-top: px2vw(24px*2);

                .more-button {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid rgba(0, 0, 0, .2);
                    border-radius: 12px;
                    gap: 4px;
                    // padding: 16rpx 40rpx;
                    font-size: 28rpx;
                    color: #000;
                    cursor: pointer;
                    height: px2vw(48px*2);

                    font-family: MiSans-normal;
                    font-weight: 500;
                    font-size: px2vw(14px*2);

                    img {
                        width: px2vw(11px*2);
                        height: px2vw(11px*2);
                    }
                }
            }
        }

    }


    .faq {
        margin: px2vw(80px*2) auto 0 auto;
        // max-width: 1280px;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-weight: bold;
                color: #000;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: px2vw(20px*2);
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: px2vw(20px*2); // 横线宽度
                    height: px2vw(2px); // 横线高度
                    background-color: rgba(0, 0, 0, .2)
                }

                &::before {
                    left: px2vw(-100px);
                }

                &::after {
                    right: px2vw(-100px);
                }
            }
        }

        .faq-item {
            border-bottom: 1px solid rgba(0, 0, 0, .05);
            padding: 40px 0 32px 0;
            margin: 0 px2vw(16px*2);

        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;

            span {

                font-family: MiSans;
                font-weight: 600;
                font-size: px2vw(16px*2);
                line-height: px2vw(21px*2);
                color: #000;
            }
        }



        .arrow-icon {
            width: px2vw(30px*2);
            ;
            height: px2vw(30px*2);
            ;
            transition: transform 0.3s ease;
        }

        .arrow-icon.rotated {
            transform: rotate(180deg);
        }

        .faq-answer {
            overflow: hidden;
            padding-top: 24px;
            font-family: MiSans;
            font-weight: 400;
            // line-height: 100%;
            text-align: left;
            font-size: px2vw(14px*2);
            line-height: px2vw(21px*2);
            color: rgba(0, 0, 0, .5);


        }

        /* 动效过渡 */
        .faq-slide-enter-active,
        .faq-slide-leave-active {
            transition: all 0.3s ease;
        }

        .faq-slide-enter-from,
        .faq-slide-leave-to {
            opacity: 0;
            max-height: 0;
        }

        .faq-slide-enter-to,
        .faq-slide-leave-from {
            opacity: 1;
            max-height: 200px;
        }

        // .faq-list {
        //     margin-top: 8px;
        //     .faq-item {
        //         background-color: #111;
        //         border-radius: 10px;
        //         padding: 16px;
        //         margin-bottom: 12px;

        //         .question {
        //             font-size: 14px;
        //             color: #fff;
        //         }
        //     }
        // }
    }

    .footer {
        margin-top: px2vw(80px*2);
        text-align: center;
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382308354167758848.png");
        background-size: 100% 100%;
        height: px2vw(200px*2);
        color: #000;

        .inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 21px;
        }

        span {
            font-family: MiSans;
            font-weight: 700;
            line-height: px2vw(29px*2);

            font-size: px2vw(22px*2);
            text-align: center;
            text-transform: capitalize;

        }

        .login-btn {
            background-color: #EF88A3;
            border: none;
            // padding: 12px 30px;
            // border-radius: 20px;
            font-family: MiSans;
            font-weight: 500;
            font-size: px2vw(14px*2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: px2vw(117px*2);
            height: px2vw(39px*2);
            border-radius: 8px;
            color: #fff;
            cursor: pointer;
        }
    }
}
</style>