<template>
  <d2-container class="page">
    <div class="header">
      <div class="form">
        <div class="item" v-for="item in searchList" :key="item.prop">
          <span>{{ item.label }}</span>
          <el-input v-model="search[item.prop]" :placeholder="`请输入${item.label}`"></el-input>
        </div>
      </div>
      <div class="action">
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="info" @click="resetSearch()">重置</el-button>
      </div>
    </div>
    <div class="form">
      <el-table
        :data="tableData"
        stripe
        style="width: 100%">
        <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :width="item.width || 'auto'"
          :prop="item.prop"
          :label="item.label">
          <template scope="scope">
            <span v-if="item.prop === 'status'"> {{ dictionary.status[scope.row[item.prop]]  }}</span>
            <span v-else-if="item.prop === 'type'"> {{ dictionary.cardType[scope.row[item.prop]]  }}</span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <footer-bar :total="totalCount" @current-change="currentChange" :current="currentPage"></footer-bar>
  </d2-container>
</template>
<script>
import footerBar from '@/views/configurationDict/window/footer'
export default {
  name: 'BankList',
  components: {
    footerBar
  },
  data () {
    return {
      search: {
        bankCardNumber: '',
        bankName: '',
        name: '',
        nickName: ''
      },
      dictionary: {
        cardType: {
          101: '借记卡',
          102: '信用卡'
        },
        status: {
          1: '预绑卡',
          2: '绑定成功',
          3: '已解绑'
        }
      },
      searchList: [
        {
          label: '户名',
          prop: 'name'
        },
        {
          label: '用户名',
          prop: 'nickName'
        },
        {
          label: '银行卡号',
          prop: 'bankCardNumber'
        },
        {
          label: '银行名称',
          prop: 'bankName'
        }
      ],
      tableData: [],
      columnList: [
        {
          label: '序号',
          prop: 'id',
          width: '80'
        },
        {
          label: '用户名',
          prop: 'nickName',
          width: '200'
        },
        {
          label: '卡类型',
          prop: 'type',
          width: '80'
        },
        {
          label: '户名',
          prop: 'name',
          width: '100'
        },
        {
          label: '银行卡号',
          prop: 'bankCardNumber',
          width: '150'
        },
        {
          label: '银行名称',
          prop: 'bankName',
          width: '150'
        },
        {
          label: '银行编码',
          prop: 'bankCode',
          width: '100'
        },
        {
          label: '支行名称',
          prop: 'branchBankName',
          width: '100'
        },
        {
          label: '开户行省份',
          prop: 'province',
          width: '100'
        },
        {
          label: '开户行城市',
          prop: 'city',
          width: '100'
        },
        {
          label: '手机号',
          prop: 'phone',
          width: '100'
        },
        {
          label: '状态',
          prop: 'status',
          width: '100'
        }
      ],
      totalCount: 0,
      currentPage: 1
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    currentChange (val) {
      this.currentPage = val
      this.getList()
    },
    resetSearch () {
      this.search = {
        bankCardNumber: '',
        bankName: '',
        name: '',
        nickName: ''
      }
      this.currentPage = 1
      this.getList()
    },
    async getList (type) {
      if (type === 'search') {
        this.currentPage = 1
      }
      const {
        bankCardNumber,
        bankName,
        name,
        nickName
      } = this.search
      const { result: { list, totalCount } } = await this.$api.bankcardList({
        bankCardNumber,
        bankName,
        name,
        nickName,
        pageNum: this.currentPage,
        pageSize: 15
      })
      this.totalCount = totalCount
      this.tableData = list
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;

  .form {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    flex-wrap: wrap;
    width: 100%;
    .item {
      margin-top: 10px;
      display: flex;
      justify-content: center;
      align-items: center;

      .el-select,
      .el-input {
        width: 200px;
        margin-left: 20px;
      }

      span {
        width: fit-content;
        min-width: 70px;
        margin-right: -10px;
      }
    }
  }

  .action {
    display: flex;
    justify-content: flex-end;
    width: fit-content;
    min-width: 150px;
    height: 40px;

    .el-button {
      padding: 0 20px;
    }

    span {
      line-height: 40px;
    }
  }
}
</style>
