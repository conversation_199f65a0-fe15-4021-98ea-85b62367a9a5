<template>
  <d2-container class="page">
    <div class="header">
      <div class="form">
        <div class="item" v-for="item in searchList" :key="item.prop">
          <span>{{ item.label }}</span>
          <el-select  v-model="search[item.prop]" v-if="item.type === 'select'" :placeholder="`请选择${item.label}`"
                     clearable>
            <el-option
              v-for="item in item.options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model="search[item.prop]" v-else :placeholder="`请输入${item.label}`"></el-input>
        </div>
      </div>
      <div class="action">
        <el-button type="primary" @click="getList('search')">搜索</el-button>
        <el-button type="info" @click="resetSearch()">重置</el-button>
      </div>
    </div>
    <div class="form">
      <el-table
        :data="tableData"
        height="70vh"
        stripe
        style="width: 100%">
        <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :width="item.width || 'auto'"
          :prop="item.prop"
          :label="item.label">
          <template slot-scope="scope">
            <span> {{ keyToLabel(item.prop, scope.row[item.prop]) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <footer-bar :total="totalCount" @current-change="currentChange" :current="currentPage"></footer-bar>
  </d2-container>
</template>
<script>
import footerBar from '@/views/configurationDict/window/footer'

export default {
  name: 'DepositList',
  components: {
    footerBar
  },
  data () {
    return {
      search: {
        depositNo: '',
        nickName: '',
        payMethod: '',
        paymentScene: '',
        status: ''
      },
      searchList: [
        {
          label: '订单号',
          prop: 'depositNo'
        },
        {
          label: '用户名',
          prop: 'nickName'
        },
        {
          label: '支付方式',
          prop: 'payMethod',
          type: 'select',
          options: [
            {
              label: '微信',
              value: 2
            },
            {
              label: '支付宝',
              value: 3
            },
            {
              label: '云闪付',
              value: 4
            },
            {
              label: '银行卡',
              value: 5
            },
            {
              label: '赠送',
              value: 6
            },
            {
              label: '五虎赠送',
              value: 7
            },
            {
              label: '苹果支付',
              value: 9
            },
            {
              label: '未知',
              value: 0
            }
          ]
        },
        {
          label: '支付场景',
          prop: 'paymentScene',
          type: 'select',
          options: [
            {
              label: 'H5',
              value: 1
            },
            {
              label: 'PC',
              value: 2
            },
            {
              label: 'IOS',
              value: 3
            },
            {
              label: 'Android',
              value: 4
            }
          ]
        },
        {
          label: '支付状态',
          prop: 'status',
          type: 'select',
          options: [
            {
              label: '待支付',
              value: 1
            },
            {
              label: '已支付',
              value: 2
            }
          ]
        }
      ],
      tableData: [],
      columnList: [
        {
          label: '序号',
          prop: 'id',
          width: '80'
        },
        {
          label: '订单号',
          prop: 'depositNo',
          width: '200'
        },
        {
          label: '用户名',
          prop: 'nickName',
          width: '100'
        },
        {
          label: '充值金额',
          prop: 'amount',
          width: '150'
        },
        {
          label: '支付金额',
          prop: 'payAmount',
          width: '150'
        },
        {
          label: '充值成功时间',
          prop: 'payTime',
          width: '150'
        },
        {
          label: '支付场景',
          prop: 'paymentScene',
          width: '100'
        },
        {
          label: '支付方式',
          prop: 'payMethod',
          width: '100'
        },
        {
          label: '支付状态',
          prop: 'status',
          width: '100'
        },
        {
          label: '创建时间',
          prop: 'createdAt',
          width: '150'
        }
      ],
      totalCount: 0,
      currentPage: 1
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 字典
     * @param prop
     * @param value
     * @returns {*}
     */
    keyToLabel (prop, value) {
      const obj = this.searchList.find(item => {
        return item.prop === prop
      })
      const res = obj?.options?.find(item => {
        return +item.value === value
      })
      value = res?.label || value
      return value
    },
    currentChange (val) {
      this.currentPage = val
      this.getList()
    },
    resetSearch () {
      this.search = {
        depositNo: '',
        nickName: '',
        payMethod: '',
        paymentScene: '',
        status: ''
      }
      this.currentPage = 1
      this.getList()
    },
    async getList (type) {
      if (type === 'search') {
        this.currentPage = 1
      }
      const {
        depositNo,
        nickName,
        payMethod,
        paymentScene,
        status
      } = this.search
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.depositList({
        depositNo,
        nickName,
        payMethod,
        paymentScene,
        status,
        pageNum: this.currentPage,
        pageSize: 15
      })
      this.totalCount = totalCount
      this.tableData = list
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20px;

  .form {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    flex-wrap: wrap;
    width: 100%;
    .item {
      margin-top: 10px;
      display: flex;
      justify-content: center;
      align-items: center;

      .el-select,
      .el-input {
        width: 200px;
        margin-left: 20px;
      }

      span {
        width: fit-content;
        min-width: 70px;
        margin-right: -10px;
      }
    }
  }

  .action {
    display: flex;
    justify-content: flex-end;
    width: fit-content;
    min-width: 150px;
    height: 40px;

    .el-button {
      padding: 0 20px;
    }

    span {
      line-height: 40px;
    }
  }
}
</style>
