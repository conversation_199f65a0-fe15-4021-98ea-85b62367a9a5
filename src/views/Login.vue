<template>
    <div class="page-container">
        <div class="promo-section">
            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/aa3daf33b859f4e88624325309fa0c2d_1492x2300.png"
                alt="Promo Image" class="promo-image" />
        </div>

        <div class="auth-box">
            <span class="titles">{{ $t("login.title") }}</span>
            <div class="input-group">
                <label>{{ $t("login.mode") }}</label>
                <input maxlength="30" v-model="account" :placeholder="$t('login.plea')" />
            </div>

            <!-- <div class="input-group" style="margin: 0 24px 20px 0;" v-if="loginMode == 'password'">
                <label>{{ $t("login.pwd") }}</label>
                <input v-model="password" type="password" :placeholder="$t('login.pwdplz')" />
                <div class="forgot-password">{{ $t("login.forget") }}</div>
            </div> -->
            <!-- v-else -->
            <div class="input-group captcha-input-wrapper">
                <label>{{ $t("login.code") }}</label>
                <div class="input-with-button">
                    <input maxlength="6" type="text" v-model="captcha" :placeholder="$t('login.copeplz')" />
                    <div class="send-btn" :disabled="isSending" @click="sendCaptcha">
                        {{ isSending ? `${countdown} ${$t("login.retry")}` : $t("login.send") }}
                    </div>
                </div>
            </div>
            <!-- 
            <div class="select-wrapper">
                <label>{{ $t("login.loginmode") }}</label>

                <div class="custom-arrow"></div>
                <select v-model="loginMode">
                    <option value="password">{{ $t("login.pwdlogin") }}</option>
                    <option value="code">{{ $t("login.codelogin") }}</option>
                </select>
            </div> -->

            <!-- <button class="btn-next">登录</button> -->
            <button class="login-btn" @click="handleLogin" :disabled="!account">{{ $t("login.title") }}</button>


            <!-- <div class="divider">或通过以下方式登录</div>
            <div class="login-buttons">
                <button class="btn-google"><img src="/google-icon.png" /> Google</button>
                <button class="btn-apple"><img src="/apple-icon.png" /> Apple</button>
            </div> -->

            <div class="alternative-login">
                <!-- <p>或通过以下方式登录</p> -->
                <div class="title-container-card">
                    <span class="titlein">{{ $t("login.other") }}</span>
                </div>
                <div class="login-options">
                    <button class="google-btn">
                        <img src="https://www.google.com/favicon.ico" alt="Google" /> Google
                    </button>
                    <button class="apple-btn">
                        <img src="https://www.apple.com/favicon.ico" alt="Apple" /> Apple
                    </button>
                </div>
            </div>

            <p class="register-link">{{ $t("login.notyet") }} <span @click="nav_to('register')">{{
                $t("login.registernow") }}</span></p>

            <!-- <div class="footer"><span @click="nav_to('register')">免费注册</span></div> -->
        </div>

        <!-- 
        <v-snackbar v-model="showSnackbar" location="top center" :timeout="3000" color="success">
            发送验证码成功！
        </v-snackbar> -->
        <!-- <v-snackbar v-model="showSnackbar" location="top center" :timeout="3000" color="success">
            发送验证码成功！
        </v-snackbar> -->
    </div>

</template>

<script setup>
import { Login, SendEmailCode } from "../api/pinkwallet.js"
import { ref } from 'vue';
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'
const { locale, t } = useI18n();
const router = useRouter();
const account = ref('');
const captcha = ref('');
const password = ref('');
const loginMode = ref('password')
const showSnackbar = ref(true)
// 倒计时相关
const isSending = ref(false);
const countdown = ref(60);
let timer = null;

// 获取 router 实例

const sendCaptcha = () => {
    if (!account.value) return;
    console.log(isSending.value);

    if (isSending.value) return;

    isSending.value = true;
    countdown.value = 60;
    SendEmailCodes()
    timer = setInterval(() => {
        countdown.value -= 1;
        if (countdown.value <= 0) {
            clearInterval(timer);
            isSending.value = false;
        }
    }, 1000);

    // console.log('验证码已发送');
};

const SendEmailCodes = async () => {
    if (!account.value) return
    // try {
    const res = await SendEmailCode({ email: account.value })
    console.log(res);

    if (res.code === 200) {
        showSnackbar.value = true
    }
    // } catch (error) {
    //     console.error('发送验证码失败', error)
    // }
}


const handleLogin = async () => {
    let data = {
        loginType: 'email',
        verifyType: 1,
        area: '+86',
        captcha: captcha.value,
        email: account.value
    }
    let res = await Login(data)
    console.log(res);
    
    if (res.code == 200) {
        localStorage.setItem('token', res.result)
        router.push({
            path: 'home',
            // query: {
            //     title: '666'
            // }
        })

    }

    // if (phone.value.length !== 11) {
    //     uni.showToast({
    //         title: '请输入正确的手机号',
    //         icon: 'none',
    //     });
    //     return;
    // }

    // if (code.value.length !== 6) {
    //     uni.showToast({
    //         title: '请输入正确的验证码',
    //         icon: 'none',
    //     });
    //     return;
    // }

}

const nav_to = (e) => {
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    });
};
</script>

<style scoped lang="scss">
.page-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    background: #0a0a0a;
    color: #fff;
    min-height: 100vh;
    gap: 100px;
    font-family: MiSans;
}

.promo-section {
    width: 400px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: center;

    .promo-image {
        cursor: pointer;
        width: 373px;
        height: 575px;
        transition: all 0.8s ease-in-out;

        &:hover {
            scale: 1.05;
        }
    }

}

.promo-img {
    width: 300px;
}

.promo-text {
    text-align: center;
}

.promo-text h2 {
    font-size: 20px;
    font-weight: bold;
}

.promo-text .highlight {
    color: #FF95B2;
}

.telegram-box {
    background: #111;
    border-radius: 20px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.icon-telegram {
    font-size: 24px;
}

.auth-box {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.05);
    padding: 40px;
    border-radius: 24px;
    background-color: #161616;
    color: #fff;
    border: 1px solid #252629;
    transition: all 0.8s ease-in-out;
    min-width: 630px;
    height: 575px;

    &:hover {
        scale: 1.05;
    }
}

.titles {
    font-family: MiSans;
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    text-transform: capitalize;
    margin-bottom: 40px;
    color: #fff;
    display: block;
}

.input-group {
    text-align: left;
    margin: 0 24px 20px 0;
    position: relative;



    .send-btn {
        position: absolute;
        right: 0;
        bottom: 7px;
        // padding: 10px 20px;
        // background-color: #FF95B2;
        border: none;
        border-radius: 10px;
        color: rgba(255, 255, 255, .6);
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.3s;



        // &:disabled {
        //     // background-color: #666;
        //     color: red;
        //     cursor: not-allowed;
        // }
    }
}

.select-wrapper {
    margin-bottom: 16px;
    text-align: left;

    label {
        display: block;
        font-size: 14px;
        margin-bottom: 6px;
        color: #aaa;
    }
}

.select-wrapper select {
    width: 100%;
    font-size: 14px;
    padding: 12px;
    outline: none;
    border: none;
    border-radius: 10px;
    background: #222;
    color: #ccc;
}

select.custom-arrow {
    position: relative;
    padding: 8px;
    background-color: #fff;
    border: 1px solid #ccc;
}

/* 创建箭头 */
select.custom-arrow::after {
    content: '';
    position: absolute;
    right: 8px;
    top: calc(50% - 2px);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #000;
    transform: rotate(90deg);
}

.input-group label {
    display: block;
    font-size: 14px;
    margin-bottom: 6px;
    color: #aaa;
}

.input-group input {
    width: 100%;
    padding: 12px;
    outline: none;
    border: none;
    // margin:  0 12px;
    border-radius: 10px;
    background: #222;
    color: white;
}

.forgot-password {
    margin-top: 10px;
    display: block;
    text-align: right;
    color: #999;
    font-size: 12px;
    cursor: pointer;
}

.login-btn {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    background-color: #FF95B2;
    border: none;
    outline: none; // 去除选中状态边框
    border-radius: 12px;
    font-size: 16px;
    color: #000;
    cursor: pointer;
    font-family: HarmonyOS Sans;

    &:disabled {
        background-color: #666;
        cursor: not-allowed;
    }
}

.alternative-login {
    margin-top: 40px;
    text-align: center;

    .title-container-card {
        background-color: rgba(255, 255, 255, 0.2);
        height: 1px;
        display: flex;
        align-items: center;
        justify-content: center;

        .titlein {
            display: block;
            font-weight: bold;
            background-color: #161616;
            z-index: 9999;
            position: relative;
            padding: 0 32px;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 14px;

            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;
        }
    }

    p {
        font-size: 14px;
        color: #aaa;
        margin-bottom: 10px;
    }

    .login-options {
        margin-top: 30px;
        display: flex;
        justify-content: center;
        gap: 10px;

        .google-btn,
        .apple-btn {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            outline: none; // 去除选中状态边框
            padding: 15px 20px;
            border-radius: 9999px;
            color: #fff;
            font-size: 14px;
            border: 1px solid #38393d;
            cursor: pointer;

            img {
                width: 20px;
                height: 20px;
                margin-right: 10px;
            }
        }
    }
}


.register-link {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: #aaa;

    span {
        color: #FF95B2;
        text-decoration: none;
    cursor: pointer;

    }
}
</style>