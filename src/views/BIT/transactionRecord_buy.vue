<template>
  <d2-container class="page">
    <common-query :showExport="true" @onExport="houseExport" :query-schema="querySchema" @onSubmit="onQueryChange"
      ref="query" :data="query" @onReset="onQueryReset"></common-query>

    <common-table :table-schema="tableSchema" :table-data="tableData" :loading='listLoading'>

    </common-table>
    <!-- <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination> -->

  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: "transactionBuyRecord",

  components: {
    CommonQuery,
    CommonTable
  },
  props: {},
  data() {

    return {
      listLoading: true,
      tableData: [],
      query: {
        userType: '1',
        createStart: '',
        createEnd: '',
        offsets: 1

      },
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      page: {
        totalCount: 0,
        pageSize: 10
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '订单号：',
          placeholder: '订单号',
          field: 'orderId'
        },
        {
          type: 'datetimerange',
          label: '下单时间：',
          placeholder: '请输入下单时间',
          field: 'createStart',
          field2: 'createEnd',
        },
        // {
        //   type: 'datetimerange',
        //   label: '首笔成交时间：',
        //   placeholder: '请输入首笔成交时间',
        //   field: 'firstStart',
        //   field2: 'firstEnd',
        // },
        {
          type: 'datetimerange',
          label: '完全成交时间：',
          placeholder: '请输入完全成交时间',
          field: 'tradedStart',
          field2: 'tradedEnd',
        },
        {
          type: 'select',
          label: '订单状态：',
          field: 'status',
          placeholder: '请选择订单状态',
          options: [
            // {
            //   label: '委托中',
            //   value: '0'
            // },
            {
              label: '部分成交',
              value: '1'
            },
            {
              label: '完全成交',
              value: '2'
            },
            // {
            //   label: '下单失败',
            //   value: '3'
            // }, {
            //   label: '平仓中',
            //   value: '4'
            // },
            // {
            //   label: '已平仓',
            //   value: '5'
            // },
            // {
            //   label: '部分成交',
            //   value: '10'
            // },
          ]
        },
        {
          type: 'select',
          label: '用户类型：',
          field: 'userType',
          placeholder: '请选择用户类型',
          options: [{
            label: '主力',
            value: "4"
          },
          {
            label: '大户',
            value: '1'
          },
          {
            label: '中户',
            value: '2'
          },
          {
            label: '小户',
            value: '3'
          }
          ]
        },
        {
          type: 'select',
          label: '仓位方向：',
          field: 'longShort',
          placeholder: '请选择仓位方向',
          options: [{
            label: '多',
            value: '1'
          },
          {
            label: '空',
            value: '2'
          },
          ]
        },
        {
          type: 'input',
          label: '用户con add：',
          placeholder: '请输入用户con add',
          field: 'conAdd'
        },
        {
          type: 'input',
          label: '用户昵称：',
          placeholder: '用户昵称',
          field: 'nickname'
        },
      ],
      tableSchema: [ // 表格架构
        {
          label: '成交id',
          field: 'id',
          width: '170px'
        },
        {
          label: '订单号',
          field: 'orderId',
          width: '170px'
        },
        {
          label: '对手订单号',
          field: 'counterOrderId',
          width: '170px'
        },
        {
          label: '下单时间',
          field: 'createAt',
          width: '200px'
        },
        {
          label: '用户con add',
          field: 'contractAddress',
          width: '210px'
        },
        {
          label: '对手conadd',
          field: 'counterContractAddress',
          width: '210px'
        },
        {
          label: '用户昵称',
          field: 'nickname',
          width: '150px'
        },
        {
          label: '对手昵称',
          field: 'counterNickname',
          width: '120px',

        },
        {
          label: '用户类型',
          field: 'userType',
          type: 'tag',
          width: '100px',
          tagMap: {
            4: {
              label: '主力',

            },
            1: {
              label: '大户',
              tagType: 'info'
            },
            2: {
              label: '中户',
              tagType: 'info'
            },
            3: {
              label: '小户',
              tagType: 'info'
            }
          },
        },
        {
          label: '份数',
          field: 'quantity',
          width: '150px'
        },
        {
          label: '开仓价格',
          field: 'price',
          width: '150px'
        },
        {
          label: '仓位方向',
          field: 'longShort',
          type: 'tag',
          tagMap: {
            1: {
              label: '多',

            },
            2: {
              label: '空',
              tagType: 'info'
            }
          },
          width: '150px'
        },
        {
          label: '成交价格',
          field: 'tradePrice',
          width: '150px'
        },
        {
          label: '首笔成交时间',
          field: 'firstAt',
          width: '150px'
        },
        {
          label: '完全成交时间',
          field: 'tradeTime',
          width: '150px'
        },
        {
          label: '订单状态',
          field: 'status',
          type: 'tag',
          tagMap: {
            1: {
              label: '部分成交',

            },
            2: {
              label: '完全成交',
              tagType: 'success'
            },
          },
          width: '80px'
        },
        // {
        // 	label: '操作',
        // 	slot: 'action',

        // 	width: '140px',
        // 	fixed:'right'
        // }
      ],
      tableData: [{}],

      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query1: {
        userType: '1',
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async houseExport(e) {
      // ExportOpenRecords
      // ExportMarketRecords
      // ExportDealHistory
      const res = await this.$api.ExportDealHistory({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '开仓明细' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.listLoading = true
      this.getList()
    },
    onQueryReset() {
      this.query = {
        userType: '1'
      }
      this.query1 = {
        userType: '1',
      }
      this.listLoading = true
      this.getList()
    },
    onQueryChange(data) {
      this.listLoading = true
      this.query1 = data
      this.getList(true)
    },
    // 获取列表
    async getList(isInit) {
      // let ctid;
      // if (this.query.ctid) {
      //   ctid = this.query.ctid.split("(")[1].split(")")[0]
      // }
      const params = {
        ...this.query1,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        offsets: 1
      }
      console.log('列表数据', params)
      const {
        status,
        result
      } = await this.$api.qifeiHistory(params)
      console.log('获取列表数据', result)
      if (status.code === 0) {
        this.listLoading = false
        let dataList = []
        this.tableData = []
        const data = result.list
        data.forEach((item) => {
          dataList.push(item)
        })
        this.tableData = dataList
        console.log("this.tableData", this.tableData)
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
        // this.page.pageNum = result.pageCount
        // this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize

      }
    },
  }
}
</script>

<style></style>
