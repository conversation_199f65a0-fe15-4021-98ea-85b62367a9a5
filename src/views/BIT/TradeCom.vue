<template>
  <d2-container class="page" v-loading="loading">
    <!--  -->
    <!-- <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange"
          ref="query" :data="query" @onReset="onQueryReset"></common-query> -->

    <div class="header">
      <el-select v-model="selectedActivityType" placeholder="请选择活动类型" clearable style="margin:0  20px 0 0  ">
        <el-option v-for="item in activityTypeOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-input style="width: 190px;margin: 0 20px 0 0 " v-model="idStr" placeholder="请输入活动ID" clearable>
      </el-input>
      <el-button type="primary" @click="getList" style="margin-bottom: 20px;">查询</el-button>
      <el-button type="primary" @click="handleAdd" style="margin-bottom: 20px;">添加</el-button>

    </div>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
      <template #createTime="scope">
        <span type="span" size="small">{{ removeMilliseconds(scope.row.createTime) }}</span>
      </template>
      <template #distribute="scope">
        <el-button type="warning" size="mini" @click="opendialog(scope.row)"
          v-if="scope.row.distribute === 0">待发放</el-button>
        <el-button type="primary" size="mini" @click="opendialog(scope.row)"
          v-if="scope.row.distribute === 1">已发放</el-button>

      </template>
      <template #actime="scope">
        <span type="span" size="small">{{ removeMilliseconds(scope.row.beginTime) + '--' +
          removeMilliseconds(scope.row.endTime) }}</span>
      </template>
      <template #percent="scope">
        <span type="span" size="small">{{ scope.row.percent + '%' }}</span>
      </template>
      <template #updateTime="scope">
        <span type="span" size="small">{{ removeMilliseconds(scope.row.updateTime) }}</span>

      </template>
      <template #action="scope">
        <el-button type="warning" size="mini" :disabled="scope.row.distribute == 1"
          @click="handleDetail(scope.row)">编辑</el-button>
      </template>
      <template #imgUrl="scope">
        <img :src="scope.row.imgUrl" alt="">
        <!-- <span type="span" size="small">{{ scope.row.percentage + '%' }}</span> -->
      </template>

      <template #activityType="scope">
        {{ scope.row.activityType == 1 ? '成交量额' : scope.row.activityType == 2 ? '总收益额' : '收益率' }}
      </template>
      <template #bonusType="scope">
        {{ scope.row.bonusType == 1 ? '固定奖池' : scope.row.bonusType == 2 ? '递增奖池' : '无' }}

      </template>
    </common-table>
    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination>

    <!-- 排行榜 -->
    <el-dialog title="排行榜" :visible.sync="dialogVisible" width="80%" @close="handleClose">
      <!-- 排行榜表格 -->
      <el-table :data="paginatedData" border style="width: 100%" @row-click="handleRowClick">
        <!-- 排名列 -->
        <!-- <el-table-column prop="rank" label="排名" align="center" width="80">
                  <template slot-scope="scope">
                      <img v-if="scope.row.rankIcon" :src="scope.row.rankIcon" class="rank-icon" />
                      <span v-else>{{ scope.row.rank }}</span>
                  </template>
              </el-table-column> -->
        <el-table-column prop="rank" label="排名" align="center" />

        <!-- 用户id列 -->
        <el-table-column prop="uid" label="用户id" align="center" />

        <!-- 总收益额列 -->
        <el-table-column prop="num" label="总收益额" align="center" />

        <!-- 奖金列 -->
        <el-table-column prop="reward" label="奖金" align="center" />

        <!-- 是否发放列 -->
        <el-table-column prop="isDistribute" label="是否发放" align="center">
          <template slot-scope="scope">
            <!-- <el-icon v-if="scope.row.isDistribute === 0" class="released-icon"> -->
            <i class="el-icon-check" v-if="scope.row.isDistribute === 1 && scope.row.userType == 1"></i>
            <!-- </el-icon> -->
            <span v-else-if="scope.row.isDistribute === 0 && scope.row.userType == 1">{{
              scope.row.isDistribute == 0 ? '未发放' : '' }}</span>
            <span v-else>机器人</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination background :page-size="pageSize" :current-page.sync="currentPage"
          layout="prev, pager, next,total" :total="rankings.length" @current-change="handlePageChange"></el-pagination>
      </div>

      <!-- 确认发放按钮 -->
      <div class="confirm-button-wrapper">
        <el-button type="primary"
          :disabled="nowrow.distribute == 1 || isTimeOutsideRange(nowrow.beginTime, nowrow.endTime)"
          @click="handleConfirm">确认发放</el-button>
        <!--  -->

      </div>
    </el-dialog>

    <!-- 新增修改 -->
    <el-dialog :title="isEditMode ? '编辑活动' : '新增活动'" :visible.sync="AdddialogVisible" width="80%" append-to-body>
      <div style="display: flex;">
        <div class="dialog-content">
          <div class="form-group">
            <label>活动类型：</label>
            <el-select :disabled="isEditMode && nostart" v-model="formData.activityType" placeholder="请选择"
              value-key="value">
              <el-option v-for="option in activityTypeOptions" :key="option.value" :label="option.label"
                :value="option.value"></el-option>
            </el-select>
          </div>
          <div class="form-group">
            <label>活动时间：</label>
            <el-date-picker v-model="formData.activityTime" v-if="isEditMode == false" type="datetimerange"
              align="right" placeholder="选择日期及时间" format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
            <el-date-picker v-model="formData.dates" v-if="isEditMode == true" type="datetimerange" align="right"
              placeholder="选择日期及时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
          </div>
          <div class="form-group">
            <label>奖池类型：</label>
            <el-select v-model="formData.bonusType" :disabled="isEditMode && nostart" placeholder="请选择">
              <!-- <el-option label="固定奖池" value="1"></el-option>
                          <el-option label="递增奖池" value="2"></el-option> -->
              <el-option v-for="option in bonusTypeOptions"
                :disabled="formData.activityType === 2 && option.value === 2" :key="option.value" :label="option.label"
                :value="option.value"></el-option>
            </el-select>
          </div>
          <div class="form-group">
            <label>奖池金额：</label>
            <el-input type="number" :disabled="isEditMode && nostart" style="width: 190px;"
              v-model="formData.bonus"></el-input>
          </div>
          <div class="form-group">
            <label>实际发放比率：</label>
            <el-input type="number" :disabled="isEditMode && nostart" v-model="formData.percent"
              style="width: 190px;"></el-input>%
          </div>
          <div class="table-container">
            <el-button style="margin: 0 0 20px 0;" type="primary" :disabled="isEditMode && nostart"
              @click="calculateReward">计算奖金和机器人分配</el-button>
            <!-- 固定奖池 -->
            <h3>
              固定奖池
            </h3>
            <el-table :data="formData.Insiderankings" style="width: 80%">
              <el-table-column prop="rankStart" label="排名" width="100">
                <template slot-scope="scope">
                  <div style="display: flex;align-items: center;">
                    <el-input :disabled="isEditMode && nostart" v-model="scope.row.rankStart" style="width: 60px;"
                      placeholder="排名开始" />
                    <el-input v-model="scope.row.rankEnd" :disabled="isEditMode && nostart"
                      v-if="scope.row.rankEnd != scope.row.rankStart" style="width: 60px;" placeholder="排名结束" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="bonus" label="奖金(0-1)" width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.bonus" :disabled="isEditMode && nostart" placeholder="奖金" />
                </template>
              </el-table-column>
              <el-table-column prop="robotNum" label="机器人" width="100">
                <template slot-scope="scope">
                  <el-input :disabled="isEditMode && nostart" v-model="scope.row.robotNum" placeholder="机器人数量" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button :disabled="isEditMode && nostart" @click="removeRow(scope.$index)"
                    type="danger">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="display: flex;align-items: center;margin: 20px 20px 10px 0 ">
              <el-button type="primary" :disabled="isEditMode && nostart" @click="suan = false">动态计算打开</el-button>
              <el-button type="primary" :disabled="isEditMode && nostart" @click="addRow">增加行</el-button>
              <el-button type="primary" @click="preview">预览</el-button>
            </div>
            <!-- 递增奖池 -->
            <h3>
              递增奖池
            </h3>
            <el-table :data="formData.Insiderankings2" style="width: 80%" v-if="formData.activityType != 2">
              <el-table-column prop="bonus" label="奖金(金额)" width="100">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.bonus" :disabled="isEditMode && nostart" placeholder="奖金" />
                </template>
              </el-table-column>
              <el-table-column prop="tradeAmount" label="成交量" width="100">
                <template slot-scope="scope">
                  <el-input :disabled="isEditMode && nostart" v-model="scope.row.tradeAmount" placeholder="成交量" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button :disabled="isEditMode && nostart" @click="removeRow2(scope.$index)"
                    type="danger">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="display: flex;align-items: center;margin: 20px 20px 10px 0 " v-if="formData.activityType != 2">
              <el-button type="primary" :disabled="isEditMode && nostart" @click="addRow2">增加行</el-button>
              <el-button type="primary" @click="preview">预览</el-button>
            </div>

            <span style="font-size: 12px;color: #ccc;">tips:点击动态计算后可自由编辑排名，奖金，机器人，上面奖池金额和发放比率会变化</span>
            <div style="height: 20px"></div>
          </div>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存修改' : '新增活动' }}</el-button>
          </div>
        </div>

        <Editor :catchData="catchData" :content="formData.activityRule" v-if="isEditMode" style="height: 800px;">
        </Editor>
        <!-- addcontent -->
        <Editor :catchData="catchData2" :content="addcontent" v-else style="height: 800px;">
        </Editor>

      </div>

    </el-dialog>


    <!-- 整体 -->
    <div class="container" ref="allpost" style="margin-top: 100000px">
      <div class="head_top">
        <div class="back">
          <img src="https://cdn-lingjing.nftcn.com.cn/image/20240204/9b3968d02180a791567f49f1d8966feb_50x50.png" alt=""
            srcset="" />
        </div>
        <div class="shareright" @click="isShare = true">
          <span>分享</span>
          <img
            src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/747bcd9cd8ebb10fa817f12fe2156c60_50x50.png"
            alt="" srcset="" />
        </div>
      </div>
      <div class="countdown-container">
        <span class="countdown-title">距离结束还有</span>
        <div class="countdown-wrapper">
          <div class="countdown-box" v-for="(unit, index) in countdownUnits" :key="index">
            <div class="countdown-line"></div>
            <div class="countdown-value">{{ unit.value }}</div>
            <span class="countdown-label">{{ unit.label }}</span>
          </div>
        </div>
      </div>
      <div class="btn" style="margin-top: 41px;">立即参与
      </div>

      <!-- 比赛奖池 -->
      <div class="match_box">
        <div class="prize-pool-container">
          <div class="prize-pool-header">
            <span class="title">比赛奖池</span>
          </div>

          <div class="current-prize-container">
            <div class="current-prize-label">
              <!-- <span class="label">当前奖池：</span> -->
              <span class="amount">¥{{ formData.bonus }}</span>
            </div>

            <div class="prize-amount">
              <img class="coin-icon" mode="widthFix"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241008/469a38bb1dbc4de8fc762f438c656838_166x142.png" />
            </div>
          </div>

          <div class="progress-container" v-if="false">
            <div class="progress-column">
              <span class="progress-title">交易量</span>
              <span class="progress-unit"> ¥1,000,000 </span>
              <div class="progress-wrapper">
                <div class="progress-bar">
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="progress"></div>

                </div>

                <div class="progress-values">
                  <span>0</span>
                  <span>50M</span>
                  <span>100M</span>
                  <span>500M</span>
                  <span>1B</span>
                </div>

              </div>

              <!-- <span class="progress-amount">¥1,000,000</span> -->
            </div>

            <div class="progress-column">
              <span class="progress-title">总奖池</span>
              <span class="progress-unit"> ¥{{ formData.bonus }} </span>

              <div class="progress-wrapper">
                <div class="progress-bar">
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="ball"></div>
                  <div class="progress"></div>
                </div>
                <div class="progress-values">
                  <span>5K</span>
                  <span>10K</span>
                  <span>20K</span>
                  <span>30K</span>
                  <span>50K</span>
                </div>

              </div>
              <!-- <span class="progress-amount">¥50,000</span> -->
            </div>
          </div>
        </div>
      </div>

      <!-- 排名 -->
      <div class="ranking-containerOuter">
        <div class="ranking-container">
          <div class="ranking-title">
            <span class="left">{{ formData.activityType == 1 ? '交易量' : formData.activityType == 2
              ? '收益额'
              :
              '收益率'
              }}排行榜</span>
            <span class="right">每小时更新一次</span>
          </div>

          <!-- 表头 -->
          <div class="ranking-header">
            <span class="header-cell" style="margin-left: -40px;">排名</span>
            <span class="header-cell" style="margin-left: 40px;">用户</span>
            <span class="header-cell" style="margin-left: 90px;">{{ formData.activityType == 1 ?
              '交易量' :
              formData.activityType == 2 ? '收益额' :
                '收益率'
              }}</span>
            <span class="header-cell" style="margin-right :-60px;">预计奖励</span>
          </div>

          <!-- 排行数据 -->
          <div class="ranking-body">
            <div class="nodata">
              <img mode="widthFix"
                src="https://cdn-lingjing.nftcn.com.cn/image/20240813/0168b66bda9880ba60c05ca8f52b6981_480x480.png" />
              <span>暂时没有数据</span>
            </div>
          </div>
        </div>
      </div>
      <!-- contenteditable="true" @input="updateContent" @blur="saveContent"
          :class="{ editable: isEditing }" -->
      <!-- 规则 -->
      <div class="rule-container" v-html="content">
      </div>
    </div>
  </d2-container>
</template>
<!-- <span class="rule-content" v-if="!isEditMode"
style="margin-left: 21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">
<p v-if="formData.activityTime[0] && formData.activityTime[1]"
  style="margin-left: 21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">
  {{ (formData.activityTime[0]).split('.')[0] }} - {{
      (formData.activityTime[1]).split('.')[0]
  }}</p>
</span>
<span v-else>
<p v-if="formData.dates[0] && formData.dates[1]"
  style="margin-left: 21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">
  {{ formData.dates[0].split('.')[0] }} - {{ formData.dates[1].split('.')[0] }}</p>
</span> -->
<script>
import Editor from '@/components/editoritem/editoritem'
import { exportBitDetail, addBitContest, getBitContest, getBitRank, grant } from '@/api/bit'
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import html2canvas from 'html2canvas'
import ImgUploader from '@/components/ImgUploader'
import {
  uploadImgToOss
} from '@/api/ossCenter'
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: 'BITTrade',
  components: {
    CommonQuery,
    CommonTable,
    Editor
  },
  data() {
    return {
      addcontent: `     <div v-else ref="rules" 
                  style="padding: 0 38rpx 45rpx 40rpx;border-radius: 10px;margin: 0 auto;color: #9F8BFF;">
                  <div class="activity-rules">
                      <div class="rules-title"
                          style="margin: 18px 0 18px 20px; color: #fff;line-height: 24px;font-weight: bold;">
                          活动规则</div>
                      <div class="rule-item" style="margin: 0 20px 15px 20px;font-size: 14px;">
                          <span class="rule-index"
                              style="font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;font-weight: bold; color: #9F8BFF;line-height: 20px;">1.起止时间：</span>

                      </div>
                      <div class="rule-item" style="margin: 0 20px 15px 20px;font-size: 14px;
                      ">
                          <span class="rule-index"
                              style="font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;font-weight: bold; color: #9F8BFF;line-height: 20px;">2.比赛奖池：</span>
                          <span class="rule-content"
                              style="margin-left:21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">

                              <p v-if="formData.bonusType == 1" style="margin-left:21px;">奖池金额: 10000元
                              </p>
                              <p v-else>
                                  根据总交易量逐步提升奖池金额
                              </p>
                          </span>
                      </div>
                      <div class="rule-item" style="margin: 0 20px 15px 20px;font-size: 14px;">
                          <span class="rule-index"
                              style="font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;font-weight: bold; color: #9F8BFF;line-height: 20px;">3.奖励发放：</span>
                          <br>
                          <div class="rule-content"
                              style="margin-left: 21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">
                              根据总交易量排名瓜分奖池
                              <br />前三名根据排名瓜分奖池的36%；
                              <br />第4名到第10名瓜分奖池的28%；
                              <br />其余前100名用户瓜分奖池的36%；
                              <br />奖金将在活动结束后5个工作日内发放。
                          </div>
                      </div>
                      <div class="rule-item" style="margin: 0 20px 15px 20px;font-size: 14px;">
                          <span class="rule-index"
                              style="font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;font-weight: bold;color: #9F8BFF;line-height: 20px;">4.参与限制：</span>
                          <br>
                          <span class="rule-content"
                              style="margin-left: 21px;font-family: HarmonyOS Sans SC, HarmonyOS Sans SC; font-weight: 400; color: #9F8BFF;line-height: 20px;">同样的实名认证身份仅能参与一次。</span>
                      </div>
                  </div>
              </div>
`,
      countdownUnits: [
        { label: '天', value: '00' },
        { label: '时', value: '00' },
        { label: '分', value: '00' },
        { label: '秒', value: '00' }
      ],
      title1: '前三名根据排名瓜分奖池的36%；',
      title2: '第4名到第10名瓜分奖池的28%；',
      title3: '其余前100名用户瓜分奖池的36%；',
      title4: '奖金将在活动结束后5个工作日内发放。',
      loading: false,
      isEditing: false,
      content: null,
      idStr: '',
      nostart: false,
      suan: false,
      selectedActivityType: '',
      activityTypeOptions: [
        { label: '总收益额', value: 2 },
        { label: '交易量', value: 1 }
      ],
      bonusTypeOptions: [
        { label: '固定奖池', value: 1 },
        { label: '递增奖池', value: 2 }
      ],
      pageSize: 10,
      currentPage: 1, // 当前页
      formData: {
        activityType: '',
        activityTime: [],
        bonusType: '',
        bonus: 0,
        percent: 0,
        Insiderankings: [
          { rankStart: 1, rankEnd: '1', bonus: '', robotNum: '' },
          { rankStart: 2, rankEnd: '2', bonus: '', robotNum: '' },
          { rankStart: 3, rankEnd: '3', bonus: '', robotNum: '' },
          { rankStart: 4, rankEnd: '10', bonus: '', robotNum: '' },
          { rankStart: 11, rankEnd: '100', bonus: '', robotNum: '' }
        ],
        Insiderankings2: [
          { bonus: '', tradeAmount: '' },
          { bonus: '', tradeAmount: '' },
          { bonus: '', tradeAmount: '' },
          { bonus: '', tradeAmount: '' },
          { bonus: '', tradeAmount: '' }
        ]
      },
      isEditMode: false, // 用来区分是新增还是编辑模式
      AdddialogVisible: false,
      activityType: '',
      activityTime: [],
      prizePoolType: '',
      prizePoolAmount: '', // 奖池金额
      actualReleaseRatio: '', // 实际发放比率
      rewardDistributionRule: '',
      Insiderankings: [
        { rankStart: 0, rankEnd: '1', bonus: '', robotNum: '' },
        { rankStart: 0, rankEnd: '2', bonus: '', robotNum: '' },
        { rankStart: 0, rankEnd: '3', bonus: '', robotNum: '' },
        { rankStart: 4, rankEnd: '10', bonus: '', robotNum: '' },
        { rankStart: 11, rankEnd: '100', bonus: '', robotNum: '' }
      ],
      rankRanges: ['1', '2', '3', '4-10', '11-100', '101-200', '201-300', '301-400', '401-500'],
      nowrow: {},
      dialogVisible: false, // 控制对话框显示
      rankings: [

      ],
      total: '',
      pageNum: 1,
      // pageSize: 20,
      tableData: [{}],

      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query1: {
        userType: '1'
      },
      listLoading: false,
      tableData: [],
      query: {
        userType: '1',
        createStart: '',
        createEnd: '',
        offsets: 1
      },
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      page: {
        totalCount: 0,
        pageSize: 20
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '订单号：',
          placeholder: '订单号',
          field: 'orderId'
        },
        {
          type: 'datetimerange',
          label: '成交时间：',
          placeholder: '请输入成交时间',
          field: 'createStart',
          field2: 'createEnd'
        },
        // {
        //   type: 'datetimerange',
        //   label: '首笔成交时间：',
        //   placeholder: '请输入首笔成交时间',
        //   field: 'firstStart',
        //   field2: 'firstEnd',
        // },
        // {
        //     type: 'datetimerange',
        //     label: '完全成交时间：',
        //     placeholder: '请输入完全成交时间',
        //     field: 'tradedStart',
        //     field2: 'tradedEnd',
        // },
        // {
        //     type: 'select',
        //     label: '订单状态：',
        //     field: 'status',
        //     placeholder: '请选择订单状态',
        //     options: [
        //         // {
        //         //   label: '委托中',
        //         //   value: '0'
        //         // },
        //         {
        //             label: '部分成交',
        //             value: '1'
        //         },
        //         {
        //             label: '完全成交',
        //             value: '2'
        //         },
        //         // {
        //         //   label: '下单失败',
        //         //   value: '3'
        //         // }, {
        //         //   label: '平仓中',
        //         //   value: '4'
        //         // },
        //         // {
        //         //   label: '已平仓',
        //         //   value: '5'
        //         // },
        //         // {
        //         //   label: '部分成交',
        //         //   value: '10'
        //         // },
        //     ]
        // },
        // {
        //     type: 'select',
        //     label: '用户类型：',
        //     field: 'userType',
        //     placeholder: '请选择用户类型',
        //     options: [{
        //         label: '主力',
        //         value: "4"
        //     },
        //     {
        //         label: '大户',
        //         value: '1'
        //     },
        //     {
        //         label: '中户',
        //         value: '2'
        //     },
        //     {
        //         label: '小户',
        //         value: '3'
        //     }
        //     ]
        // },
        // {
        //     type: 'select',
        //     label: '仓位方向：',
        //     field: 'longShort',
        //     placeholder: '请选择仓位方向',
        //     options: [{
        //         label: '多',
        //         value: '1'
        //     },
        //     {
        //         label: '空',
        //         value: '2'
        //     },
        //     ]
        // },
        {
          type: 'input',
          label: '用户con add：',
          placeholder: '请输入用户con add',
          field: 'conAdd'
        },
        {
          type: 'input',
          label: '用户昵称：',
          placeholder: '用户昵称',
          field: 'nickname'
        }
      ],
      tableSchema: [ // 表格架构

        {
          label: 'id',
          field: 'id',
          width: '50px'

        },
        {
          label: '活动类型',
          slot: 'activityType',
          width: '120px'
          // type: 'tag',
          // tagMap: [
          //     {
          //         label: '成交量额',
          //         value: 1
          //     },
          //     {
          //         label: '总收益额',
          //         value: 2
          //     },
          //     {
          //         label: '收益率',
          //         value: 3
          //     }
          // ]
        },
        {
          label: '活动时间',
          slot: 'actime',
          width: '300px'
        },
        {
          label: '奖池类型',
          slot: 'bonusType',
          width: '150px'

        },
        {
          label: '奖池金额',
          field: 'bonus',
          width: '170px'
        },
        {
          label: '实际发放比例',
          slot: 'percent',
          width: '210px'
        },
        {
          label: '发放状态',
          slot: 'distribute',
          width: '210px'
          // type: 'tag',
          // tagMap: [{
          //     label: '待发放',
          //     value: 0
          // }, {
          //     label: '已发放',
          //     value: 1
          // }]

        },
        // {
        //     label: '奖池金额开始',
        //     field: 'bonusBegin',
        //     width: '240px'
        // },
        // {
        //     label: '奖池金额结束',
        //     field: 'bonusEnd',
        //     width: '240px'
        // },
        // {
        //     label: '递增奖池规则',
        //     field: "bonusRule",
        //     width: '240px'

        // },
        // {
        //     label: '发放规则',
        //     field: "distributeRule",
        //     width: '240px'
        // },
        // {
        //     label: '创建admin',
        //     field: "createAdminId",
        //     width: '240px'
        // },
        // {
        //     label: '创建时间',
        //     slot: "createTime",
        //     width: '240px'
        // },
        // {
        //     label: '更新admin',
        //     field: "updateAdminId",
        //     width: '240px'
        // },
        // {
        //     label: '更新时间',
        //     slot: "updateTime",
        //     width: '400px'
        // },
        // {
        //     label: '图片',
        //     slot: 'imgUrl',
        //     width: '220px'
        // },
        {
          label: '操作',
          slot: 'action',
          width: '120px'
        }
      ]
    }
  },
  computed: {
    // 分页后的数据
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.rankings.slice(start, end)
    }
  },
  watch: {
    'formData.Insiderankings': {
      deep: true, // 深度监听数组内部变化
      // handler(newVal, oldVal) {
      //   if (newVal) {
      //     console.log('bianhua', this.suan, this.formData.Insiderankings)
      //     if (!this.suan) {
      //       this.formData.bonus = this.formData.Insiderankings.reduce((sum, item) => {
      //         return sum + ((Number(item.bonus) * (Number(item.rankEnd) - item.rankStart + 1)))
      //       }, 0)
      //       this.calculateRobotRatio(newVal)
      //     } else {

      //     }
      //   }
      // }
    },
    AdddialogVisible: {
      handler(val) {
        if (!val) {
          // this.formData = {}
        }
      }
    },
    title1: {
      handler(val) {
        this.title1 = val
        console.log(this.title1, '修改了')
      }
    }
  },
  mounted() {
    console.log(new Date().getTime())

    // this.getDetail()
    this.getList()
  },
  methods: {
    catchData(e) {
      console.log('1e=====?>', e)
      // this.richTxt = e
      this.formData.activityRule = e
    },
    catchData2(e) {
      this.formData.activityRule = e
    },
    getTimestamp(dateString) {
      // 创建一个新的 Date 对象，并将其转换为时间戳
      const timestamp = new Date(dateString).getTime()
      return timestamp
    },
    startCountdown() {
      setInterval(() => {
        const now = new Date().getTime()
        const distance = (this.isEditMode ? this.getTimestamp(this.formData.dates[1]) : this.getTimestamp(this.formData.activityTime[1])) - now
        console.log(distance, 1231212)

        // this.formData.dates[1]
        // this.formData.activityTime[1]
        if (distance > 0) {
          const days = Math.floor(distance / (1000 * 60 * 60 * 24))
          const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
          const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
          const seconds = Math.floor((distance % (1000 * 60)) / 1000)

          this.countdownUnits[0].value = String(days).padStart(2, '0')
          this.countdownUnits[1].value = String(hours).padStart(2, '0')
          this.countdownUnits[2].value = String(minutes).padStart(2, '0')
          this.countdownUnits[3].value = String(seconds).padStart(2, '0')
        }
      }, 1000)
    },
    preview() {
      if (this.isEditMode) {
        this.content = this.formData.activityRule
      } else {
        this.content = this.formData.activityRule
      }
      this.startCountdown()

      setTimeout(() => {
        console.log(this.title1)
        this.open(true)
      }, 1000)
    },
    async open(type) {
      this.loading = true
      const dom = this.$refs.allpost // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, // 支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2 // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {
        this.updateImg(canvas.toDataURL('image/jpeg', 1), type)
      }).catch(err => {

      })
    },
    async updateImg(url, type) {
      console.log(url)
      const formData = new FormData()
      const fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.loading = false
      console.log(1)
      window.open(
        `https://cdn-lingjing.nftcn.com.cn/${result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]}`,
        'name',
        'height=820, width=414, top=0, left=2, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=no, status=no'
      )
    },
    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    updateContent(event) {
      this.content = this.$refs.rules.innerHTML
      // const rulesEl = this.$refs.rules.innerHTML;

      // this.content = event.target.innerText;
      console.log(this.content, '内容')
      // this.content = event.target.innerspan;
      // this.isEditing = true;
    },
    saveContent() {
      console.log(this.title1, '修噶玩')

      // this.isEditing = false;
    },
    calculateRobotRatio(data) {
      const totalSum = data.reduce((sum, item) => {
        return sum + Number(item.robotNum) * item.bonus
      }, 0)

      const rate = totalSum / this.formData.bonus
      console.log('rate', rate, totalSum, (1 - rate))

      this.formData.percent = Math.ceil((1 - rate) * 100)
      return
      let totalRangePeople = 0
      let totalRobotNum = 0

      data.forEach(item => {
        const rankStart = Number(item.rankStart)
        const rankEnd = Number(item.rankEnd)
        const rangePeople = rankEnd - rankStart + 1 // 区间人数

        const robotNum = Number(item.robotNum) // robotNum

        totalRangePeople += rangePeople
        if (robotNum > 0) { // 只计算机器人数量大于0的情况
          totalRobotNum += robotNum
        }
      })

      // 计算比例并更新
      this.formData.percent = totalRangePeople > 0
        ? (100 - (totalRobotNum / totalRangePeople) * 100).toFixed(0) // 转换为百分比并保留两位小数
        : 0
    },
    suanBonus(arr) {
      arr.forEach(item => {
        const range = Number(item.rankEnd) - Number(item.rankStart) + 1 // 区间人数
        item.bonus = (Math.floor(Number(item.bonus) / range))
      })
    },
    // 计算 bonus
    computedBonus(row) {
      console.log(row, '奖金变化了')

      console.log(row.rankStart, Number(row.bonus), (this.formData.percent / 100), (Number(row.rankEnd) - row.rankStart - row.robotNum + 1), 2323232)

      const range = Number(row.rankEnd) - Number(row.rankStart) + 1 // 区间人数
      return (Math.floor(Number(row.bonus) / range))
      // const range1 = Number(row.rankEnd) - Number(row.rankStart); // 区间人数

      // if (row.rankStart > 11) {
      //     return row.bonus
      // }
      // if (range1 > 0) {
      //     return (Math.floor(Number(row.bonus) * (this.formData.percent / 100) / (Number(row.rankEnd) - row.rankStart - row.robotNum + 1)))
      // } else {  // 1,2,3名

      //     if (row.robotNum > 0) {
      //         return 0
      //     } else {

      //         return row.bonus
      //     }
      // }
    },
    // 更新 bonus 时反向计算
    updateBonus(value, row) {
      // const range1 = Number(row.rankEnd) - Number(row.rankStart); // 区间人数

      // if (range1 > 0) {
      //     return (Math.floor(Number(row.bonus) * (this.formData.percent / 100) / (Number(row.rankEnd) - row.rankStart - row.robotNum + 1)))
      // } else {  // 1,2,3名

      //     if (row.robotNum > 0) {
      //         return 0
      //     } else {

      //         return row.bonus
      //     }
      // }
      row.bonus = value // 手动更新 bonus 的值
      this.$forceUpdate() // 强制 Vue 重新渲染组件，确保 computedBonus 重新计算
    },
    handleRowClick(row, index) {
      // 使用 this.$set 来更新数据，确保响应式更新
      if (row.userType == 0) {
        this.$message.error('机器人不能发放')
        return
      }
      // this.$set(row, 'isDistribute', 1);
      this.$set(row, 'isDistribute', row.isDistribute === 0 ? 1 : 0)
      if (row.isDistribute == 1) {
        this.$message.success(`用户 ${row.uid} 已勾选`)
      }
      // this.$message.success(`用户 ${row.uid} 已勾选`);
      console.log(this.paginatedData, '123')
    },
    async submitForm() {
      console.log(this.formData.Insiderankings, '表格')
      if (this.isEditMode) {
        console.log(this.formData.dates, 6666)

        console.log('编辑模式下保存修改')
        // const rulesEl = this.$refs.Edrules.innerHTML;

        const data = {
          idStr: this.formData.id,
          // activityRule: rulesEl,
          activityRule: this.formData.activityRule,
          activityType: this.formData.activityType,
          beginTime: this.formData.dates[0] + '.000',
          endTime: this.formData.dates[1] + '.000',
          bonus: this.formData.bonus,
          bonusType: this.formData.bonusType,
          percent: this.formData.percent,
          // distributeRuleList: this.formData.Insiderankings,
          distributeRule: JSON.stringify(this.formData.Insiderankings),
          bonusStart: this.formData.bonus,
          bonusEnd: this.formData.bonus
        }
        if (this.formData.bonusType == 2) {
          data.bonusRule = JSON.stringify(this.formData.Insiderankings2);
        }
        if (this.formData.activityType == 2 && this.formData.bonusType == 2) {
          if (data.formData.bonusRule) {

            delete data.formData.bonusRule
          }
        }
        const res = await addBitContest(data)
        if (res.status.code == 0) {
          this.AdddialogVisible = false
          this.$message.success('修改成功')
          this.getList()
        }
      } else {
        // console.log(this.$refs.rules, this.formData.Insiderankings);
        // const rulesEl = this.$refs.rules.innerHTML;

        //     const range = Number(row.rankEnd) - Number(row.rankStart) + 1; // 区间人数
        // return (Math.floor(Number(row.bonus) / range));

        // this.formData.Insiderankings.forEach(item => {
        //     item.bonus = Number(item.bonus) / (Number(item.rankEnd) - item.rankStart - item.robotNum + 1);
        // });
        // this.formData.Insiderankings = this.suanBonus(this.formData.Insiderankings)
        if (!this.formData.activityType) {
          this.$message.error('活动类型不能为空')
          return
        }
        // if (!this.formData.activityTime) {
        //     this.$message.error('活动时间不能为空')
        //     return
        // }
        // bonusType
        if (this.formData.bonusType == '') {
          this.$message.error('奖金类型不能为空')
          return
        }
        if (!this.formData.bonus) {
          this.$message.error('奖金不能为空')
          return
        }
        if (!this.formData.percent) {
          this.$message.error('活动比例不能为空')
          return
        }
        console.log(this.formData);

        const data = {
          // activityRule: this.formData.activityRule,
          activityRule: this.formData.activityRule,
          distributeRule: JSON.stringify(this.formData.Insiderankings),
          activityType: this.formData.activityType,
          beginTime: this.formData.activityTime[0] + '.000',
          endTime: this.formData.activityTime[1] + '.000',
          bonus: this.formData.bonus,
          bonusType: this.formData.bonusType, // 1 固定 2 递增
          percent: this.formData.percent,
          bonusStart: this.formData.bonus,
          bonusEnd: this.formData.bonus
        };
        // bonusRule
        // 根据 bonusType 设置 bonusRule 和 distributeRule
        if (this.formData.bonusType == 1) {
        } else if (this.formData.bonusType == 2) {
          data.bonusRule = JSON.stringify(this.formData.Insiderankings2);
        }
        if (this.formData.activityType == 2 && this.formData.bonusType == 2) {
          if (data.formData.bonusRule) {
            delete data.formData.bonusRule
          }
        }
        const res = await addBitContest(data)
        if (res.status.code == 0) {
          this.AdddialogVisible = false
          this.$message.success('添加成功')
          this.getList()
        }
        console.log('新增模式下提交表单')
      }
      this.dialogVisible = false
    },
    addRow2() {
      this.suan = false
      this.formData.Insiderankings2.push({
        tradeAmount: '',
        bonus: '',
      })
    },
    addRow() {
      this.suan = false
      this.formData.Insiderankings.push({
        rankStart: '',
        rankEnd: '',
        bonus: 0,
        robotNum: ''
      })
      // const lastRank =this.formData.Insiderankings[this.Insiderankings.length - 1];
      // const nextRank = this.getNextRank(lastRank.rankEnd);
      // console.log(lastRank,nextRank);

      // if (nextRank) {
      //    this.formData.Insiderankings.push({
      //         rankStart: nextRank.rankStart,
      //         rankEnd: nextRank.rankEnd,
      //         bonus: "",
      //         robotNum: ""
      //     });
      // } else {
      //     this.$message.warning("已经没有更多排名区间了");
      // }
    },
    removeRow2(index) {
      this.suan = false

      this.formData.Insiderankings2.splice(index, 1)
    },
    removeRow(index) {
      this.suan = false

      this.formData.Insiderankings.splice(index, 1)
    },
    getNextRank(currentRankEnd) {
      const currentIndex = this.rankRanges.findIndex(item => item.rankEnd === currentRankEnd)
      return this.rankRanges[currentIndex + 1] || null
    },
    calculateReward() {
      this.formData.Insiderankings = [
        { rankStart: 1, rankEnd: '1', bonus: '', robotNum: '' },
        { rankStart: 2, rankEnd: '2', bonus: '', robotNum: '' },
        { rankStart: 3, rankEnd: '3', bonus: '', robotNum: '' },
        { rankStart: 4, rankEnd: '10', bonus: '', robotNum: '' },
        { rankStart: 11, rankEnd: '100', bonus: '', robotNum: '' }
      ]
      this.suan = true

      console.log(this.formData, 12312)

      const r = this.formData.percent / 100 // 将百分比转化为小数
      const rewardPercentages = {
        1: 16,
        2: 12,
        3: 8,
        4: 28,
        11: 36
      }
      // var totalBonus = 0;  // 初始化总奖金金额
      const remainingReward = this.formData.bonus * 0.36 * r // 计算剩余奖金

      // 动态计算每个排名区间的奖金
      this.formData.Insiderankings.forEach(item => {
        // 根据 rankEnd 获取相应的奖金比例
        const rangeKey = item.rankStart
        const percentage = rewardPercentages[rangeKey] // 每个区间的金额

        // if (percentage) {
        //     // 计算该区间的奖金金额
        //     const reward = (this.formData.bonus * (percentage / 100)) / (Number(item.rankEnd) - Number(item.rankStart) + 1);
        //     item.bonus = Math.ceil(reward);  // 更新奖金，保留两位小数
        // }
        item.bonus = Math.floor((this.formData.bonus * (percentage / 100)) / (Number(item.rankEnd) - Number(item.rankStart) + 1)) / this.formData.bonus
      })

      // 机器人分配逻辑
      this.calculateRobotDistribution(r)
    },
    calculateRobotDistribution(r) {
      const totalPercentage = (36 * r)
      // r ：实际发放比率
      // 前三名机器人分配
      if (totalPercentage >= 28.8) {
        this.setRobotCount(1, 0)
        this.setRobotCount(2, 0)
        this.setRobotCount(3, 0)
        const a = (r - (0.36)) // 0.64
        const fixrate = a / 0.64
        const numRobots4To10 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To10))) // 2

        const b = (a - (Math.ceil(numRobots4To10)) * 0.04)
        const c = (b / 0.36)
        const remianBnum = Math.ceil(90 * c)
        console.log(a, numRobots4To10, b, remianBnum, '1231212')

        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 24) {
        this.setRobotCount(1, 0)
        this.setRobotCount(2, 0)
        this.setRobotCount(3, 1)
        const a = (r - (0.28))

        const fixrate = a / 0.64

        const numRobots4To102 = (fixrate * 7)

        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)
        const remianBnum = Math.ceil(90 * c)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 19.2) {
        this.setRobotCount(1, 0)
        this.setRobotCount(2, 1)
        this.setRobotCount(3, 0)

        const a = (r - (0.24))

        const fixrate = a / 0.64

        const numRobots4To102 = (fixrate * 7)

        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)
        const remianBnum = Math.ceil(90 * c)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 16) {
        this.setRobotCount(1, 1)
        this.setRobotCount(2, 0)
        this.setRobotCount(3, 0)

        const a = (r - (0.2)) // 剩余的比例
        const fixrate = a / 0.64
        const numRobots4To102 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)
        const remianBnum = Math.ceil(90 * c)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 12.8) {
        this.setRobotCount(1, 0)
        this.setRobotCount(2, 1)
        this.setRobotCount(3, 1)

        const a = (r - (0.16)) // 剩余的比例
        const fixrate = a / 0.64
        const numRobots4To102 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)
        const remianBnum = Math.ceil(90 * c)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 9.6) {
        this.setRobotCount(1, 1)
        this.setRobotCount(2, 0)
        this.setRobotCount(3, 1)

        const a = (r - (0.12)) // 剩余的比例
        const fixrate = a / 0.64
        const numRobots4To102 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)

        const remianBnum = Math.ceil(c * 90)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage > 6.4) {
        this.setRobotCount(1, 1)
        this.setRobotCount(2, 1)
        this.setRobotCount(3, 0)

        const a = (r - (0.08)) // 剩余的比例
        const fixrate = a / 0.64
        const numRobots4To102 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)

        const remianBnum = Math.ceil(c * 90)
        this.setRobotCount('100', (90 - remianBnum))
      } else if (totalPercentage < 6.4) {
        this.setRobotCount(1, 1)
        this.setRobotCount(2, 1)
        this.setRobotCount(3, 1)

        const a = (r) // 剩余的比例
        const fixrate = a / 0.64
        const numRobots4To102 = (fixrate * 7)
        this.setRobotCount('10', (7 - Math.ceil(numRobots4To102)))

        const b = (a - (Math.ceil(numRobots4To102)) * 0.04)
        const c = (b / 0.36)

        const remianBnum = Math.ceil(c * 90)
        this.setRobotCount('100', (90 - remianBnum))
      }

      // 第4-10名机器人计算
      // const aa = 64;
      // const a = aa / (28 + 36);
      // const numRobots4To10 = Math.ceil((a * 7).toFixed(2));
      // this.setRobotCount("10", numRobots4To10);
      // console.log(a, numRobots4To10, 12312312312312);

      // 第11-100名机器人计算
      // const b = totalPercentage / 36;
      // const numRobots11To100 = Math.ceil((b * 90).toFixed(2));
      // this.setRobotCount("100", numRobots11To100);
    },
    setRobotCount(rankEnd, count) {
      console.log(rankEnd, count, '123123123')

      const row = this.formData.Insiderankings.find(item => item.rankEnd == rankEnd.toString())
      if (row) {
        row.robotNum = count
      }
    },
    handleAdd() {
      this.AdddialogVisible = true
      this.formData.activityType = ''
      this.formData.activityTime = []
      this.formData.bonusType = ''
      this.formData.bonus = ''
      this.formData.percent = ''
      this.formData.Insiderankings = [
        { rankStart: 1, rankEnd: '1', bonus: '', robotNum: '' },
        { rankStart: 2, rankEnd: '2', bonus: '', robotNum: '' },
        { rankStart: 3, rankEnd: '3', bonus: '', robotNum: '' },
        { rankStart: 4, rankEnd: '10', bonus: '', robotNum: '' },
        { rankStart: 11, rankEnd: '100', bonus: '', robotNum: '' }
      ];
      this.formData.Insiderankings2 = [
        { bonus: '', tradeAmount: '' },
        { bonus: '', tradeAmount: '' },
        { bonus: '', tradeAmount: '' },
        { bonus: '', tradeAmount: '' },
        { bonus: '', tradeAmount: '' }
      ],
        // this.formData = {
        //     activityName: '',
        //     activityType: '',
        //     activityStart: '',
        // this.formData = {
        //     activityTime: []
        // }
        // }
        this.isEditMode = false
    },
    handlePageChange(page) {
      console.log('当前页:', page)
      this.currentPage = page
      // 处理分页逻辑
    },
    async handleConfirm() {
      // 找到 userType 为 1 且 isDistribute 为 0 的 uid
      const uids = this.rankings.filter(item => item.userType === 1 && item.isDistribute === 1).map(item => item.uid)
      console.log(uids, '12312312')

      // 将 uid 用逗号隔开并放入一个数组
      const resultArray = [uids.join(',')]
      console.log(resultArray, 666)
      const data = {
        activityIdStr: this.nowrow.id,
        uidListStr: `${JSON.stringify(uids)}`
      }
      grant(data).then(res => {
        if (res.status.code == 0) {
          this.dialogVisible = false
          this.$message.success('发放成功')
          this.getList()
        }
      })

      // 确认发放逻辑
      console.log('确认发放')
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleDetail(item) {
      this.AdddialogVisible = true
      const beginDate = new Date(item.beginTime)
      // 获取当前时间
      const currentDate = new Date()
      // 判断当前时间是否大于 beginTime
      if (currentDate > beginDate) {
        this.nostart = true
      } else {
        this.nostart = false
      }
      this.isEditMode = true

      this.suan = true
      console.log(item, this.formData)
      // return
      // this.formData.activityTime[0] = this.removeMilliseconds(item.startTime)
      // this.formData.activityTime[1] = this.removeMilliseconds(item.endTime)
      // this.formData = item
      this.formData.bonus = item.bonus
      this.formData.dates = [item.beginTime, item.endTime]
      this.formData.Insiderankings2 = JSON.parse(item.bonusRule)
      this.formData.Insiderankings = JSON.parse(item.distributeRule)
      this.formData = {
        ...this.formData,
        ...item
      }
      console.log(this.formData, 66666666666666)
    },
    removeMilliseconds(dateTimeString) {
      return dateTimeString.split('.')[0]
    },
    async houseExport(e) {
      // ExportOpenRecords
      // ExportMarketRecords
      // ExportDealHistory
      const res = await exportBitDetail({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new spanDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '开仓明细' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.listLoading = true
      this.getList()
    },
    onQueryReset() {
      this.query = {
        userType: '1'
      }
      this.query1 = {
        userType: '1'
      }
      this.listLoading = true
      this.getList()
    },
    onQueryChange(data) {
      const filteredObj = {}

      for (const key in data) {
        if (data[key] !== '' && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
          filteredObj[key] = data[key]
        }
      }
      this.listLoading = true
      this.query1 = filteredObj
      this.getList(true)
    },
    // 获取列表
    async getList(isInit) {
      // let ctid;
      // if (this.query.ctid) {
      //   ctid = this.query.ctid.split("(")[1].split(")")[0]
      // }
      // return
      const params = {
        ...this.query1,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        offsets: 1,
        activityType: this.selectedActivityType,
        idStr: this.idStr
      }
      console.log('列表数据', params)
      const {
        status,
        result
      } = await getBitContest(params)
      console.log('获取列表数据', result)
      if (status.code === 0) {
        this.listLoading = false
        const dataList = []
        this.tableData = []
        const data = result.list
        data.forEach((item) => {
          dataList.push(item)
        })
        this.tableData = dataList
        console.log('this.tableData', this.tableData)
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
        // this.page.pageNum = result.pageCount
        // this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize
      }
    },
    isTimeOutsideRange(start, end) {
      const beginTime = new Date(start)
      const endTime = new Date(end)
      const currentTime = new Date() // 当前时间

      // 判断当前时间是否在开始时间和结束时间之间
      if (currentTime >= beginTime && currentTime <= endTime) {
        return true // 当前时间在范围内，返回
      } else {
        return false // 当前时间不在范围内，返回
      }
    },
    opendialog(e) {
      this.nowrow = e
      this.dialogVisible = true
      this.fetchrank(e)
      console.log(this.isTimeOutsideRange(this.nowrow.beginTime, this.nowrow.endTime), 123123112312)
    },
    async fetchrank(e) {
      const res = await getBitRank({
        activityIdStr: e.id
      })
      if (res.status.code === 0) {
        this.rankings = res.result
        this.total = res.result.length
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  width: 50%;

  .form-group {
    margin-bottom: 20px;
  }
}

.rank-icon {
  width: 20px;
  height: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.confirm-button-wrapper {
  text-align: center;
  margin-top: 20px;
}

.released-icon {
  color: green;
}

// .rule-container {
//     margin: 0 35px 30px 35px;
//     border-radius: 36px;
//     overflow: hidden;

//     .activity-rules {
//         // background-color: #2e184e;
//         padding: 0 38px 45px 40px;
//         border-radius: 10px;
//         max-width: 400px;
//         margin: 0 auto;
//         color: #9F8BFF;
//         // color: #fff;
//     }

//     .rules-title {
//         color: #fff;
//         padding: 35px 0 36px 0px;
//         line-height: 48px;
//         font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
//         font-weight: bold;
//         font-size: 36px;
//     }

//     .rule-item {
//         margin-bottom: 15px;
//     }

//     .rule-index {
//         font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
//         font-weight: bold;
//         font-size: 28px;
//         color: #9F8BFF;
//         line-height: 40px;
//     }

//     .rule-content {
//         margin-left: 42px;
//         font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
//         font-weight: 400;
//         font-size: 28px;
//         color: #9F8BFF;
//         line-height: 40px;
//     }

// }

.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 50px 0px;

  img {
    height: 240px;
    width: 240px;
  }

  span {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24px;
    color: #9A999F;
  }
}

.share_body {

  width: 600px;
  // margin-bottom: 200px;
  // background-color: #111111;
  // background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/e319063b91316c0a29dce409165d5324_1344x2064.png");
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  // padding: 160px 55px 55px;
  // height: 960px;
  // position: rela/tive;
  // display: flex;

  .cart_div {
    // width: 100%;
    // background-color: #2B2B2B;
    // border-radius: 36px;
    // width: 640px;
    // height: 940px;
    // padding: 40px 36px;
    position: relative;
    height: 990px;

    background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/e319063b91316c0a29dce409165d5324_1344x2064.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .code {
      display: flex;
      justify-content: space-between;
      margin: 18px 80px 80px 80px;

      .left {
        .mycode {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 24px;
          color: #FFFFFF;
          opacity: .5;
          line-height: 32px;
        }

        .codes {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 900;
          font-size: 54px;
          color: #FFFFFF;
          line-height: 72px;
          margin-bottom: 9px;
        }

        .tips {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 24px;
          color: #FFFFFF;
          line-height: 32px;
          opacity: .5;
        }
      }

      .right {
        .qr_code {
          width: 140px;
          height: 140px;
          background: #EDEDED;
          border-radius: 28px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .top {
      // position: absolute;
      padding-top: 89px;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 64px;
        height: 64px;
        border-radius: 50%;
      }

      .name {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24px;
        color: #41F7EC;
        line-height: 32px;
        margin: 0 10px 0 20px;
      }

      .content {
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: 400;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 32px;
      }
    }

    .line {
      height: 2px;
      background: #FFFFFF;
      border: 1px solid #707070;
      opacity: 0.38;
      margin: 0 74px;
    }

    .bom {
      margin-top: 400px;
      // position: absolute;
      bottom: 301px;

      .title {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .time {
          margin: 15px 0 13px 0;
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 22px;
          color: #FFFFFF;
          line-height: 29px;
          opacity: .5;
        }

        .titleup {
          display: flex;
          justify-content: center;
          // text-align: center;
          margin: 0 auto;
          width: 421px;
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 50px;
          color: #FFFFFF;
          line-height: 67px;
        }
      }

    }
  }

  .share_to_div {
    margin-top: 140px;
    display: flex;
    justify-content: center;

    >.li {
      width: 25%;
      text-align: center;
      color: #fff;
      font-size: 28px;
      margin-top: 10px;

      .icon_image {
        display: flex;
        justify-content: center;

        img {
          width: 90px;
          margin-bottom: 20px;
        }
      }
    }
  }

  .colse_div {
    text-align: center;
    margin: 0 auto;
    // margin-top: -100px;
    // position: absolute;
    // bottom: -110px;
    // margin-top: 146px;
    display: flex;
    justify-content: center;
    align-items: flex-end;

    img {
      width: 80px;
    }
  }
}

.rule-container {
  margin: 0 35px 30px 35px;
  background: #312958;
  border-radius: 36px;
  border: 1px solid #ED90F9;
  overflow: hidden;

}

.activity-rules {
  background-color: #2e184e;
  padding: 0 38px 45px 40px;
  border-radius: 10px;
  // max-width: 400px;
  margin: 0 auto;
  color: #9F8BFF;
  // color: #fff;
}

.rules-title {
  color: #fff;
  padding: 35px 0 36px 0px;
  line-height: 48px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: bold;
  // font-size: 36px;
}

.rule-item {
  margin-bottom: 15px;
}

.rule-index {
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: bold;
  // font-size: 28px;
  color: #9F8BFF;
  line-height: 40px;
}

.rule-content {
  margin-left: 42px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 400;
  // font-size: 28px;
  color: #9F8BFF;
  line-height: 40px;
}

.ranking-containerOuter {
  margin: 0 35px 30px 35px;
  background: #312958;
  border-radius: 36px;
  border: 1px solid #ED90F9;
  overflow: hidden;

  .ranking-container {
    // background-color: #2e184e;
    // padding: 20px;

  }

  .ranking-title {
    padding: 35px 53px 36px 40px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    .left {
      line-height: 48px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
    }

    .right {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: 400;
      font-size: 24px;
      color: #9894AC;
      line-height: 32px;
    }
  }

  .ranking-header {
    padding: 36px 64px 16px 64px;
    display: flex;
    justify-content: space-around;
    border-radius: 5px;
    font-size: 16px;
    color: #89e4ff;
  }

  .header-cell {
    // flex: 1;
    text-align: center;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24px;
    opacity: .5;
    color: #FFFFFF;
  }

  .ranking-body {
    padding: 29px 53px 40px 62px;

  }

  .ranking-row {
    display: flex;
    height: 90px;
    justify-content: space-between;
    padding: 10px 0;
    align-items: center;
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .ranking-row.highlighted {
    padding: 0 30px;
    margin: 0 -30px;
    // background-color: #6a28a6;
    height: 90px;
    background: linear-gradient(270deg, rgba(121, 15, 226, 0) 0%, #513A9F 100%);
    border-radius: 14px
  }

  .ranking-cell {
    width: 440px;
    // text-align: center;
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24px;
    color: #FFFFFF;
  }

  .rank-icon {
    width: 50px;
    height: 20px;
    margin-right: 10px;
  }

  .price {
    color: #00d7f9;
    font-weight: bold;
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding-bottom: 40px;
  }

  .pagination-item {
    width: 44px;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    // padding: 5px 10px;
    // background-color: rgba(0, 0, 0, 0.2);
    margin: 0 6px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
  }

  .pagination-item.active {
    color: #000;
    background-color: #6a28a6;
  }

}

.match_box {
  margin: 40px 35px 30px 35px;
  background: #312958;
  border-radius: 36px;
  border: 1px solid #ED90F9;
  overflow: hidden;

  .prize-pool-container {
    // background-color: #443A76;
    border-radius: 10px;
    // padding: 20px;
    color: #fff;
    // max-width: 400px;
    margin: 0 auto;

    .prize-pool-header {
      padding: 35px 0 19px 40px;

      .title {
        display: flex;
        flex-direction: column;
        font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
        font-weight: bold;
        font-size: 36px;
        color: #FFFFFF;
      }
    }

    .current-prize-container {
      margin: 19px 24px 40px 24px;
      // width: 1000px;
      // background-color: rgba(0, 0, 0, 0.2);
      background: #443A76;
      border-radius: 10px;
      padding: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .current-prize-label {
        display: flex;
        flex-direction: column;

        // font-size: 16px;
        // color: #89e4ff;
        .label {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 28px;
          color: #FFFFFF;
          opacity: .5;
        }

        .amount {
          text-align: center;
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: bold;
          font-size: 66px;
          width: 292px;
          color: #63EAEE;
          line-height: 88px;
        }
      }

      .prize-amount {
        display: flex;
        align-items: center;

        .amount {
          font-size: 30px;
          font-weight: bold;
          color: #00d7f9;
        }

        .coin-icon {
          width: 83px;
          height: 71px;
          margin-right: 17px;
          // margin-left: 10px;
        }
      }
    }

    .progress-container {
      display: flex;
      justify-content: space-between;
      padding: 20px 0;

      .progress-column {
        width: 50%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin: 0 40px;

        .progress-wrapper {
          margin-left: 10px;
          display: flex;

          .progress-values {
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: 400;
            font-size: 30px;
            color: #FFFFFF;
            line-height: 30px;
            margin-left: 24px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 120px;
          }
        }

        .progress-unit {
          margin: 7px 0 14px;
          line-height: 50px;
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: bold;
          font-size: 38px;
          color: #FFFFFF;
        }

        .progress-title {
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          font-weight: 400;
          font-size: 28px;
          opacity: .5;
          color: #FFFFFF;
        }

        .progress-bar {
          width: 5px;
          background-color: #9894AC;
          height: 229px;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;

          .progress {
            // width: 100%;
            // height: 40%; // 可根据进度动态调整
            background-color: #00d7f9;
            position: absolute;
            // height: 100px;
            width: 2px;
            top: 0;
            z-index: 99;
            border-radius: 5px;
          }

          .ball {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #959595;

            &:first-of-type {
              width: 20px;
              height: 20px;
            }
          }

        }

        .progress-amount {
          font-size: 16px;
          color: #ffffff;
          margin-top: 10px;
        }
      }
    }
  }

}

.btn {
  // padding-top: 41px;
  height: 93px;
  background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
  border-radius: 60px;
  margin: 0 63px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 500;
  font-size: 34px;
  color: #000000;
}

.head_top {
  display: flex;
  justify-content: space-between;
  padding: 66px -66px 66px 0px;

  // position: fixed;
  // top: 0;
  // left: 0;
  z-index: 99;
  width: 100%;
  /* #ifdef APP */
  padding-top: 40px;
  /* #endif */

  .shareright {
    width: 141px;
    height: 48px;
    background: #A76FFF;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
      font-weight: bold;
      font-size: 28px;
      color: #1F0858;
      line-height: 0px;
      margin-right: 9px;
    }

    img {
      width: 25px;
      height: 25px;
    }
  }

  .back {
    // position: absolute;
    /* #ifdef APP */
    // top: var(--status-bar-height);
    // top: calc(32px + var(--status-bar-height));

    /* #endif */
    /* #ifdef H5 */
    // top: 64px;
    /* #endif */
    // left: 30px;

    img {
      width: 50px;
    }
  }

  .tabs_div {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 46px 55px 10px 55px;

    .left {
      // width: 460px;
    }

    .right {
      img {
        width: 36px;
      }
    }
  }
}

page {
  background: #070313;
  height: 100%;
  /* 确保html和body高度占满视口 */
  margin: 0;
  padding: 0;
}

.container {
  // scale: 0.3;
  // margin-top: 10000px;
  width: 750px;
  /* 设置宽度为视口宽度 */
  height: fit-content;
  /* 高度为父元素的100%，即整个视口 */
  background-image: url("https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20241008/05bd2f9c4cfe150fc4fd1513fc3e7402_1512x3430.png");
  background-size: 100% 100%;
  /* 背景图片按比例缩放，覆盖整个容器 */
  background-position: center;
  /* 背景图片居中显示 */
  background-repeat: no-repeat;
  /* 防止图片重复 */
  position: relative;
  /* 可以根据需要使用 absolute/fixed 也可以使用 relative */
}

.countdown-container {
  padding-top: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  // background: linear-gradient(to bottom, #3e0169, #020216);
  /* 背景渐变色 */
  // padding: 20px;
  border-radius: 10px;
}

.countdown-title {
  margin-bottom: 13px;
  line-height: 32px;
  font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
  font-weight: 400;
  font-size: 24px;
  color: #63EAEE;
}

.countdown-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.countdown-box {
  display: flex;
  // flex-direction: column;
  align-items: center;
  // padding: 10px;
  // margin: 0 5px;
  padding: 0 16px;
  border-radius: 5px;
  margin-top: 13px;
  position: relative;
  width: 100%;

  .countdown-line {
    width: 47px;
    height: 1.6px;
    background: #000;
    z-index: 99999;
    // margin: 0 8px;
    position: absolute;
    // left: 50%;

  }

  .countdown-value {
    display: flex;
    justify-content: center;
    align-items: center;
    // background-color: #00d7f9;
    width: 47px;
    height: 52px;
    background: #63EAEE;
    border-radius: 3px;
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 33px;
    color: #000000;

    // margin-bottom: 5px;
  }

  .countdown-label {
    font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 20px;
    color: #63EAEE;
    line-height: 0px;
    margin-left: 8px;
  }
}

.bold {
  font-weight: bold;
}
</style>
