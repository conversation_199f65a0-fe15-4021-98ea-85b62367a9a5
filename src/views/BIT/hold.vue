<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange" ref="query" :data="query"
            @onReset="onQueryReset"></common-query>
        <!-- :showExport="true" @onExport="houseExport" -->
        <common-table :table-schema="tableSchema"  :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #action="scope">
                <div class="bg">
                    <span v-if="scope.row.income != null && isCheck">{{ scope.row.income }}</span>
                    <el-button v-else type="text"  @click="checkIncome(scope.row)">查看</el-button>
                </div>

            </template>

            <template #ctime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.ctime) }}</span>
            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>
        <!-- <div
            style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="xuanzeSize">
            </el-pagination>
        </div> -->

    </d2-container>
</template>

<script>
import { getBitHold,exportBitHold } from "@/api/bit"
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "BIThold",
    components: {
        CommonQuery,
        CommonTable
    },
    props: {},
    data() {
        return {
            listLoading: true,
            tableData: [],
            query: {
                userType: '1',
                orderType: '4',
                status: "1"
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            page: {
                pageSize: 10
            }, // 分页数据
            querySchema: [ // 搜索组件架构
                {
                    type: 'select',
                    label: '排序：',
                    field: 'orderType',
                    placeholder: '请排序',
                    options: [{
                        label: '按持仓份数从大到小',
                        value: '1'
                    },
                    {
                        label: '按持仓价格从高到低',
                        value: '2'
                    },
                    {
                        label: '按持仓价格从低到高',
                        value: '3'
                    },
                    {
                        label: '按时间从近到远',
                        value: '4'
                    },
                    {
                        label: '按时间从远到近',
                        value: '5'
                    },
                    ]
                },
                {
                    type: 'input',
                    label: '用户con add：',
                    placeholder: '请输入用户con add',
                    field: 'conAdd'
                },
                {
                    type: 'select',
                    label: '用户类型：',
                    field: 'userType',
                    placeholder: '请选择用户类型',
                    options: [{
                        label: '主力',
                        value: '4'
                    },
                    {
                        label: '大户',
                        value: '1'
                    },
                    {
                        label: '中户',
                        value: '2'
                    },
                    {
                        label: '小户',
                        value: '3'
                    }
                    ]
                },
                {
                    type: 'select',
                    label: '仓位方向：',
                    field: 'side',
                    placeholder: '请选择仓位方向',
                    options: [{
                        label: '多',
                        value: 'BUY'
                    },
                    {
                        label: '空',
                        value: 'SELL'
                    },
                    ]
                },
                {
                    type: 'select',
                    label: '杠杆：',
                    field: 'leverageLevel',
                    placeholder: '请选择杠杆',
                    options: [{
                        label: '1倍',
                        value: '1'
                    },
                    {
                        label: '2倍',
                        value: '2'
                    },
                    {
                        label: '5倍',
                        value: '5'
                    },
                    {
                        label: '10倍',
                        value: '10'
                    },
                    {
                        label: '20倍',
                        value: '20'
                    },
                    {
                        label: '50倍',
                        value: '50'
                    },
                    ]
                },
                {
                    type: 'select',
                    label: '状态：',
                    field: 'status',
                    placeholder: '请选择状态',
                    options: [{
                        label: '有效',
                        value: '1'
                    },
                    {
                        label: '失效',
                        value: '0'
                    },

                    ]
                },
            ],
            tableSchema: [ // 表格架构
                {
                    label: '持仓ID',
                    field: 'positionId',
                    width: 100
                },
                {
                    label: '开始持仓时间',
                    slot: 'ctime',
                    width: '170px'
                },
                {
                    label: '用户con add',
                    field: 'conAdd',
                    width: '200px'
                },
                {
                    label: '用户昵称',
                    field: 'nickname',
                    width: '200px'
                },
                {
                    label: '用户类型',
                    field: 'userType',
                    type: 'tag',
                    tagMap: {
                        4: {
                            label: '主力',

                        },
                        1: {
                            label: '大户',
                            tagType: 'info'
                        },
                        2: {
                            label: '中户',
                            tagType: 'info'
                        },
                        3: {
                            label: '小户',
                            tagType: 'info'
                        },
                    },
                    width: '100px'
                },
                {
                    label: '仓位方向',
                    field: 'side',
                    type: 'tag',
                    tagMap: {
                        'BUY': {
                            label: '唱多',

                        },
                        'SELL': {
                            label: '唱空',
                            tagType: 'info'
                        }
                    },
                    width: '120px'
                },
                {
                    label: '持仓份数',
                    field: 'volume',
                    width: '150px'
                },
                {
                    label: '开仓份数',
                    field: 'openVolume',
                    width: '150px'
                },
                {
                    label: '平仓份数',
                    field: 'closeVolume',
                    width: '150px'
                },
                {
                    label: '杠杆',
                    field: 'leverageLevel',
                    width: '120px',
                    type: 'tag',
                    tagMap: {
                        1: {
                            label: '1倍',
                        },
                        2: {
                            label: '2倍',
                        },
                        5: {
                            label: '5倍',
                        },
                        10: {
                            label: '10倍',
                        },

                    },
                },
                {
                    label: '持仓价格',
                    field: 'avgPrice',
                    width: '120px'
                },
                {
                    label: '开仓价格',
                    field: 'openPrice',
                    width: '120px'
                },

                {
                    label: '开仓实际支付',
                    field: 'openPay',
                    width: '120px'
                },
                {
                    label: '强平价',
                    field: 'reducePrice',
                    width: '120px'
                },
                {
                    label: '当前收益',
                    field: 'profit',
                    width: '140px',

                },
                {
                    label: '状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '失效',
                        },
                        1: {
                            label: '有效',
                            tagType: 'success'
                        },

                    },
                    width: '80px'
                },
                {
                    label: '强平状态',
                    field: 'reduceStatus',
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '正常',
                        },
                        1: {
                            label: '强平',
                            tagType: 'success'
                        },

                    },
                    width: '80px'
                },
                // debt totalDebt
                {
                    label: '欠债',
                    field: 'debt',
                    width: '120px'
                },
                {
                    label: '总负债',
                    field: 'totalDebt',
                    width: '120px'
                },
                // experienceMoney  totalExperienceMoney closeProfit closeRiskProfit closeFee
                {
                    label: '体验金',
                    field: 'experienceMoney',
                    width: '120px'
                },
                {
                    label: '总体验金',
                    field: 'totalExperienceMoney',
                    width: '120px'
                },
                {
                    label: '已平仓收益',
                    field: 'closeProfit',
                    width: '120px'
                },
                {
                    label: '已平仓风控收益',
                    field: 'closeRiskProfit',
                    width: '120px'
                },
                {
                    label: '已平仓手续费',
                    field: 'closeFee',
                    width: '120px'
                },
            ],

            tableData: [{}],

            page: {
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            isCheck: false,
            query1: {
                userType: '1',
                orderType: '4',
                status: '1'
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async checkIncome(row) {
            console.log(row)
            this.isCheck = true
            // this.getList(row.ctid)
            // const res = await this.$api.qifeiHoldHistory({
            //    orderType:1,
            //   ctid: row.ctid
            // })

        },
        onQueryReset() {
            this.query = {
                userType: '1',
                orderType: '4'
            }
            this.query1 = {
                userType: '1',
                orderType: '4',
                status: '1'
            }
            this.listLoading = true
            this.getList()
        },
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        //分页数量切换
        xuanzeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            const params = {

                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            delete params.totalCount
            const {
                status,
                result
            } = await getBitHold(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []

                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        async houseExport(e) {
            const res = await exportBitHold({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '当前持仓' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
    }
}
</script>

<style></style>