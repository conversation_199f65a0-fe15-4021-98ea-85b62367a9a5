<template>
    <d2-container class="page">
        <!-- :showExport="true" @onExport="houseExport" -->
        <common-query :query-schema="querySchema" @onSubmit="onQueryChange" ref="query" :data="query"
            @onReset="onQueryReset"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #ctime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.ctime) }}</span>
            </template>

            <template #action="scope">
                <el-button type="primary" size="small" @click="cancel(scope.row)">撤销</el-button>
            </template>
        </common-table>
        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

    </d2-container>
</template>

<script>
import { getBitDetail, cancelBitMain, getBitMain } from "@/api/bit"
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    components: {
        CommonQuery,
        CommonTable
    },
    data() {
        return {
            pageNum: 1,
            pageSize: 20,
            tableData: [{}],

            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
            },
            listLoading: false,
            tableData: [],
            query: {
                createStart: '',
                createEnd: '',
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型

            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '价格：',
                    placeholder: '价格',
                    field: 'price'
                },

            ],
            tableSchema: [ // 表格架构
                {
                    label: '订单id',
                    field: "id",
                    width: '210px'

                },
                {
                    label: '仓位id',
                    field: "positionId",
                    width: '210px'

                },
                {
                    label: '时间',
                    slot: 'ctime',
                    width: '210px'

                },
                {
                    label: '开平方向',
                    field: 'open',
                    width: '120px',
                    type: 'tag',
                    tagMap: {
                        'OPEN': {
                            label: '开仓',

                        },
                        'CLOSE': {
                            label: '平仓',
                            tagType: 'info'
                        }
                    },
                },
                {
                    label: '开仓价格',
                    field: 'price',
                    width: '120px'
                },
                {
                    label: '委托金额',
                    field: 'money',
                    width: '120px'
                },
                {
                    label: '支付价格',
                    field: 'payMoney',
                    width: '120px'

                },
                {
                    label: '成交均价',
                    field: 'avgPrice',
                    width: '120px'
                },
                {
                    label: '已成交金额',
                    field: 'dealMoney',
                    width: '150px'
                },
                {
                    label: '体验金',
                    field: 'experienceMoney',
                    width: '150px'
                },
                {
                    label: '杠杆倍数',
                    field: 'leverageLevel',
                    width: '170px'
                },


                {
                    label: '买卖方向',
                    field: 'side',
                    width: '120px',
                    type: 'tag',
                    tagMap: {
                        'BUY': {
                            label: '买',

                        },
                        'SELL': {
                            label: '卖',
                            tagType: 'info'
                        }
                    },
                },


                {
                    label: '状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: [{
                        label: '初始订单',
                        value: 0
                    }, {
                        label: '委托单',
                        value: 1
                    },
                    {
                        label: '完全成交',
                        value: 2
                    },
                    {
                        label: '部分成交',
                        value: 3
                    }, {
                        label: '撤销',
                        value: 4
                    },
                    {
                        label: '待撤销',
                        value: 5
                    },
                    {
                        label: '异常',
                        value: 6
                    },
                    ],
                    width: '80px'
                },
                {
                    label: '操作',
                    slot: 'action',
                },
            ],
        }
    },

    mounted() {
        // this.getDetail()
        this.getList()

    },
    methods: {
        cancel(e) {
            // 二次确认
            this.$confirm('确定要撤销吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                cancelBitMain({
                    orderIdStr: e.id
                }).then(res => {
                    if (res.status.code === 0) {
                        this.$message.success('撤销成功')
                        this.getList()
                    }
                })
            }).catch()
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async houseExport(e) {
            // ExportOpenRecords
            // ExportMarketRecords
            // ExportDealHistory
            const res = await this.$api.ExportDealHistory({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '开仓明细' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {

            this.listLoading = true
            this.getList()
        },
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            this.getList(true)
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            //   ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            // return
            const params = {
                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            if (params.price) {
                params.price = params.price - 0
            }
            const {
                status,
                result
            } = await getBitMain(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                let dataList = []
                this.tableData = []
                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                console.log("this.tableData", this.tableData)
                this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageSize = result.pageSize

            }
        },
        getDetail() {
            let data = {
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }
            getBitDetail(data).then(res => {
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>