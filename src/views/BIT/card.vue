<template>
    <d2-container class="card-container page">

        <!-- v-loading="isLoading" -->
        <div class="box-card" v-loading="isLoading">
            <div slot="header" class="card-header">
                <h3>Dashboard Risk Control 看板风控后台</h3>

                <!-- <common-query :showExport="false" :query-schema="querySchema" @onSubmit="onQueryChange" :data="query"
                    @onReset="onQueryReset"></common-query> -->
                <el-date-picker style="margin: 0 20px 20px 0;" clearable format="yyyy-MM-dd HH:mm:ss" v-model="query"
                    type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>

                <el-button type="primary" @click="onQueryChange">查询</el-button>
                <el-button type="primary" @click="onReset">清除</el-button>
            </div>
            <span>{{ tips }}</span>
            <div style="height: 20px;"></div>
            <div>
                <table cellspacing="0" cellpadding="5" style="width: 100%;height: 700px; text-align: center;">
                    <!-- 第一行 -->
                    <tr>
                        <th rowspan="2">账户</th>
                        <td class="highlight-green">主力账户</td>
                        <td>实际余额：{{ importantData.actualBalance }}</td>
                        <td>公共账户余额：{{ card.commonAccountAmount }}</td>
                        <td>机器人余额：{{ card.robotAccountAmount }}</td>

                        <td>资金费率：{{ card.fundingProfit }}</td>

                        <td>相比较前一日：{{ yest }}</td>
                        <td class="highlight-yellow">资金流向：{{ card.financeFlow }}</td>
                    </tr>
                    <tr>
                        <td class="highlight-green">用户账户</td>
                        <td>用户累计收益：{{ importantData.userTotalProfit }}</td>
                        <td class="highlight-red">截止当日总手续费：{{ importantData.utilCurDayTradeFee }}</td>
                        <td>当前时间段手续费：{{ card.curDayTradeFee }}</td>
                        <td>用户委托待成交金额：{{ importantData.userDelegatingAmount }}</td>
                    </tr>
                    <br />
                    <!-- 第二行 -->
                    <tr>
                        <th rowspan="1">当前时间段流量</th>
                        <td>访问用户数：{{ card.visitUserCount }}</td>
                        <td>参与用户数：{{ card.participateUserCount }}</td>
                        <td>首次参与用户数：{{ card.firstParticipateUserCount }}</td>
                        <td>开仓用户数：{{ importantData.openUserCount }}</td>
                        <td>平仓用户数：{{ card.closeUserCount }}</td>
                        <td>爆仓用户数：{{ card.explosionUserCount }}</td>
                        <td>次日复购率：{{ card.nextDayRepurchaseRate }}</td>

                    </tr>
                    <br />

                    <!-- 第三行 -->
                    <!-- <tr>
                        <th colspan="10">成交</th>
                    </tr> -->
                    <tr>
                        <th rowspan="3">成交</th>
                        <td class="highlight-green">当前时间段成交</td>
                        <td>成交额（不算杠杆）：{{ card.tradeAmountWithoutLeverage }}</td>
                        <!-- <td class="highlight-red">成交额（算杠杆）</td> -->
                        <td>交易人数：{{ card.tradeUserCount }}</td>

                    </tr>
                    <tr>
                        <td rowspan="2" class="highlight-green">成交明细</td>
                        <td>主力买：{{ card.mainOpen }}</td>
                        <td>用户买：{{ card.userOpen }}</td>
                        <!-- <td>主力买%：{{ (card.mainOpenPercent * 100).toFixed(4) + '%' }}</td> -->
                        <td>主力卖：{{ card.mainClose }}</td>
                        <td>用户卖：{{ card.userClose }}</td>
                        <!-- <td>主力卖%：{{ (card.mainClosePercent * 100).toFixed(4) + '%' }}</td> -->
                    </tr>
                    <tr>
                        <td>主力开多：{{ card.mainOpenLong }}</td>
                        <td colspan="1">主力平多：{{ card.mainCloseLong }}</td>
                        <td colspan="2">主力开空：{{ card.mainOpenShort }}</td>
                        <td colspan="2">主力平空：{{ card.mainCloseShort }}</td>

                    </tr>
                    <br />

                    <!-- 第四行 -->
                    <tr>
                        <th rowspan="3">持仓</th>
                        <td rowspan="2" class="highlight-green">主力持仓</td>
                        <td>主力总持仓</td>
                        <td>主力多仓剩余：{{ card.mainHoldLongVolume }}</td>
                        <td>主力空仓剩余：{{ card.mainHoldShortVolume }}</td>

                    </tr>

                    <tr>
                        <td>当前时间段持仓变化</td>
                        <td>主力多仓变化：{{ card.mainHoldLongVolumeChange }}</td>
                        <td>主力空仓变化：{{ card.mainHoldShortVolumeChange }}</td>

                    </tr>


                    <tr>
                        <td class="highlight-green">用户持仓</td>
                        <td>6-3用户持仓总计</td>
                        <td>用户多仓份数：{{ card.userHoldLongVolume }}</td>
                        <td>用户多仓持仓均价：{{ card.userHoldLongAvgPrice }}</td>
                        <td>用户空仓份数：{{ card.userHoldShortVolume }}</td>
                        <td>用户空仓持仓均价:{{ card.userHoldShortAvgPrice }}</td>
                        <td>不算杠杆的用户多仓：{{ card.userHoldLongVolumeWithLeverage }}</td>
                        <td>不算杠杆的用户空仓:{{ card.userHoldShortVolumeWithLeverage }}</td>

                    </tr>

                    <br />
                    <tr>
                        <th rowspan="4">持仓</th>
                        <td rowspan="4" class="highlight-green">用户持仓</td>

                        <td rowspan="4" class="highlight-green">用户持仓详细</td>
                        <td rowspan="4" colspan="1">开多总份数：{{ card.userOpenLongVolume }}/开多总人数：{{
                            card.userOpenLongCount }}</td>
                        <td>杠杆10</td>
                        <td v-if="card.userOpenLongInfo">总份数：{{ card.userOpenLongInfo.lever10Volume }}</td>
                        <td v-if="card.userOpenLongInfo">总人数:{{ card.userOpenLongInfo.lever10Count }}</td>
                        <td rowspan="4">开空总份数：{{ card.userOpenShortVolume }}/开空总人数：{{ card.userOpenShortCount }}</td>
                        <td>杠杆10</td>
                        <td v-if="card.userOpenShortInfo">总份数：{{ card.userOpenShortInfo.lever10Volume }}</td>
                        <td v-if="card.userOpenShortInfo">总人数：{{ card.userOpenShortInfo.lever10Count }}</td>
                    </tr>




                    <tr>
                        <td>杠杆5</td>
                        <td v-if="card.userOpenLongInfo">总份数：{{ card.userOpenLongInfo.lever5Volume }}</td>
                        <td v-if="card.userOpenLongInfo">总人数：{{ card.userOpenLongInfo.lever5Count }}</td>
                        <td>杠杆5</td>
                        <td v-if="card.userOpenShortInfo">总份数：{{ card.userOpenShortInfo.lever5Volume }}</td>
                        <td v-if="card.userOpenShortInfo">总人数：{{ card.userOpenShortInfo.lever5Count }}</td>
                    </tr>
                    <tr>
                        <td>杠杆2</td>
                        <td v-if="card.userOpenLongInfo">总份数：{{ card.userOpenLongInfo.lever2Volume }}</td>
                        <td v-if="card.userOpenLongInfo">总人数：{{ card.userOpenLongInfo.lever2Count }}</td>
                        <td>杠杆2</td>
                        <td v-if="card.userOpenShortInfo">总份数：{{ card.userOpenShortInfo.lever2Volume }}</td>
                        <td v-if="card.userOpenShortInfo">总人数：{{ card.userOpenShortInfo.lever2Count }}</td>
                    </tr>
                    <tr>
                        <td>杠杆1</td>
                        <td v-if="card.userOpenLongInfo">总份数：{{ card.userOpenLongInfo.lever1Volume }}</td>
                        <td v-if="card.userOpenLongInfo">总人数：{{ card.userOpenLongInfo.lever1Count }}</td>
                        <td>杠杆1</td>
                        <td v-if="card.userOpenShortInfo">总份数：{{ card.userOpenShortInfo.lever1Volume }}</td>
                        <td v-if="card.userOpenShortInfo">总人数：{{ card.userOpenShortInfo.lever1Count }}</td>
                    </tr>

                    <br />

                    <!-- 第六行 -->
                    <tr>
                        <th>市场行情</th>
                        <td>当前时间段最高：{{ card.todayHighestPrice }}</td>
                        <td>当前时间段最低:{{ card.todayLowestPrice }}</td>
                        <!-- <td>当前时间段平均市价：{{ card.todayAvgPrice > 0 ? card.todayAvgPrice.toFixed(3) : card.todayAvgPrice }} </td> -->
                        <td>方差:{{ card.variance }}</td>
                        <td>15分钟涨跌幅:{{ card.fifteenMaxPriceDiff }}</td>
                        <!-- <td>2-4市价方差</td> -->

                    </tr>
                    <br />

                    <tr>
                        <th rowspan="1">爆仓统计</th>
                        <td>强平单数：{{ card.explosionOrderCount }}</td>
                        <td>强平份数:{{ card.explosionVolume }}</td>
                        <td>强平用户数：{{ card.explosionUserCount }}</td>
                        <td>8-4强平手续费</td>
                        <td>8-5强平实际收益</td>
                        <td>8-6强平收款差额</td>
                    </tr>

                </table>


            </div>

        </div>
    </d2-container>
</template>

<script>
import { getBitBoard, getDashboard, getDashboardCompare } from "@/api/bit"
import CommonQuery from '@/components/CommonQuery_h'

export default {
    name: "BITCard",

    components: {
        CommonQuery,
    },
    data() {
        return {
            yest: '',
            tips: '',
            isLoading: false,
            card: {},
            importantData: {},
            querySchema: [ // 搜索组件架构
                {
                    type: 'datetimerange',
                    label: '时间区间：',
                    placeholder: '请输入时间区间：',
                    field: 'timeStart',
                    field2: 'timeEnd',
                    originTime: true,
                },
            ],
            query: [

            ],
        };
    },
    created() {
        const todayMidnight = this.getTodayMidnight();
        const yesterdayMidnight = this.getYesterdayMidnight();

        this.query[0] = this.formatDate(yesterdayMidnight) + '.000';
        this.query[1] = this.formatDate(todayMidnight) + '.000';
    },
    mounted() {
        this.getDashboard()

        // timeEnd:"2024-09-08 00:00:00.000"
        // timeStart: "2024-09-07 00:00:00.000"
        this.fetchBitCard();
    },
    methods: {
        // async getyesterdar() {
        // },
        async getDashboard(e) {
            this.isLoading = true
            const todayMidnight = this.getTodayMidnight();
            const yesterdayMidnight = this.getYesterdayMidnight();

            let timeStart = this.formatDate(yesterdayMidnight) + '.000';
            let timeEnd = this.formatDate(todayMidnight) + '.000';
            const data = e ? { timeStart: e[0] + '.000', timeEnd: e[1] + '.000' } : { timeStart, timeEnd };
            // let res = await 
            // if (res.status.code == 0) {
            //     this.yest = res.result
            // }
            getDashboardCompare(data).then(res => {
                this.yest = res.result
            })
            getDashboard(data).then(res => {
                // this.card = res.result
                this.isLoading = false
                this.importantData = res.result
            })
        },
        async fetchBitCard(e) {
            this.tips = '其他数据获取中...'
            // this.isLoading = true
            const todayMidnight = this.getTodayMidnight();
            const yesterdayMidnight = this.getYesterdayMidnight();

            let timeStart = this.formatDate(yesterdayMidnight) + '.000';
            let timeEnd = this.formatDate(todayMidnight) + '.000';
            // e?.timeStart 


            const data = e ? { timeStart: e[0] + '.000', timeEnd: e[1] + '.000' } : { timeStart, timeEnd };
            getBitBoard(data).then(res => {
                this.card = res.result
                this.tips = '其他数据获取成功'

                // this.isLoading = false

            })
            // let res = await getBitBoard()
        },
        // 过滤查询
        onQueryChange() {
            this.getDashboard(this.query)
            this.fetchBitCard(this.query)
        },
        onQueryReset() { },
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        // 获取当天 0 点时间
        getTodayMidnight() {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // 设置时间为当天 0 点
            return today;
        },

        // 获取昨天 0 点时间
        getYesterdayMidnight() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1); // 减去一天
            yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
            return yesterday;
        },
    }
};
</script>

<style scoped>
table {
    font-size: 12px;
    border-collapse: collapse;
}

th,
td {
    padding: 5px;
    border: 1px solid #ccc;
    text-align: center;
}

.highlight-green {
    background-color: #409eff;
}

.highlight-yellow {
    /* background-color: yellow; */
}

.highlight-red {
    /* background-color: lightcoral; */
}

.card-container {
    padding: 20px;
}

.box-card {
    width: 100%;
    height: 100%;
    margin-bottom: 20px;
    /* display: flex; */
    /* flex-direction: column; */
    /* flex-wrap: wrap; */
    /* justify-content: space-between; */
}

.el-col {
    margin-bottom: 20px;
}

.fixed-cell {
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
}

.el-divider {
    margin: 0 0 10px 0;
}

.el-card {
    padding: 10px;
    text-align: center;
}
</style>

<!-- // 第一行：账户
account: 'Account Data',                         // 账户
mainAccount: 'Main Account Data',                // 主力账户
publicAccountAmount: 'Public Account Amount',    // 公共账户金额

// 第二行：当前时间段流量
todaysVisits: 'Today’s Visits Data',             // 当前时间段访问用户数
engagementRate: 'Engagement Rate Data',          // 参与用户数
topUsers: 'Top Users Data',                      // 首次用户数

// 第三行：成交
todaysTransactions: 'Today’s Transactions Data', // 当前时间段成交
transactionAmount: 'Transaction Amount Data',    // 成交额
activeUsers: 'Active Users Data',                // 活跃人数

// 第四行：持仓
mainForceBuy: 'Main Force Buy Data',             // 主力买
userBalance: 'User Balance Data',                // 用户平空

// 第五行：主力持仓
mainForcePosition: 'Main Force Position Data',   // 主力持仓
mainAccountChange: 'Main Account Change Data',   // 主力仓位变化
userPositionChange: 'User Position Change Data', // 用户持仓变化

// 第六行：市场行情
marketHigh: 'Market High Data',                  // 市场最高
marketLow: 'Market Low Data',                    // 市场最低

// 第七行：爆仓统计
liquidationStatistics: 'Liquidation Statistics Data', // 爆仓统计

// 第八行：其他统计
flatPositionCount: 'Flat Position Count Data',   // 平单数
winningTrades: 'Winning Trades Data',            // 胜率单数 -->