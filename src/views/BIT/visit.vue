<template>
    <d2-container class="card-container page" v-loading="loading">
        <el-date-picker style="margin: 0 20px 20px 0;" clearable format="yyyy-MM-dd HH:mm:ss" v-model="querytime"
            type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>

        <el-button type="primary" @click="fetchinfo">查询</el-button>
        <div id="funnel-chart" style="width: 60%; height: 500px;margin:0 auto"></div>
    </d2-container>
</template>

<script>
import * as echarts from 'echarts';
import { visitInfo } from "@/api/bit"
export default {
    data() {
        return {
            querytime: [],
            info: {},
            loading: false,

        }
    },
    created() {
        const todayMidnight = this.getTodayMidnight();
        const yesterdayMidnight = this.getYesterdayMidnight();

        this.querytime[0] = this.formatDate(yesterdayMidnight)
        this.querytime[1] = this.formatDate(todayMidnight)
    },
    mounted() {
        this.fetchinfo()
        // this.initFunnelChart();
    },
    methods: {
        initFunnelChart() {
            const funnelChart = echarts.init(document.getElementById('funnel-chart'));

            const option = {
                title: {
                    text: '',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: (params) => {

                        const name = params.name;
                        const value = params.value;
                        let extraInfo = '';

                        // 为不同的节点定义额外的tooltip信息
                        if (name == '参与用户数：' + this.info.participateUserCount) {
                            extraInfo = `<br/>新增参与用户数: ${this.info.fisrtParticipateUserCount}`;
                        } else if (name == '开仓用户数：' + this.info.openUserCount) {
                            extraInfo = `<br/>平仓用户数: ${this.info.closeUserCount}<br/>爆仓用户数: ${this.info.explosionUserCount}`;
                        }
                        return `${name}${extraInfo}`;
                    }
                    // ${value}
                },
                series: [
                    {
                        name: '漏斗图',
                        type: 'funnel',
                        left: '10%',
                        top: 50,
                        bottom: 50,
                        width: '80%',
                        sort: 'descending',
                        gap: 2,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                        data: [
                            // 首页访问用户数 (梯形)
                            { value: this.info.homePageVisitCount, name: '首页访问用户数：' + this.info.homePageVisitCount, itemStyle: { color: '#3CB371' } },
                            // 开杠吧访问用户数 (梯形)
                            { value: this.info.bitVisitCount, name: '开杠吧访问用户数：' + this.info.bitVisitCount, itemStyle: { color: '#4682B4' } },
                            // 参与用户数 (方形)
                            { value: this.info.participateUserCount, name: '参与用户数：' + this.info.participateUserCount, itemStyle: { color: '#FFA500' } },
                            // 开仓用户数 (三角形)
                            { value: this.info.openUserCount, name: '开仓用户数：' + this.info.openUserCount, itemStyle: { color: '#9370DB' } }
                        ]
                    }
                ]
            };

            funnelChart.setOption(option);
        }
        ,
        async fetchinfo() {
            this.loading = true
            let data = {
                startTime: this.querytime[0] + '.000',
                endTime: this.querytime[1] + '.000'
            }
            let res = await visitInfo(data)
            if (res.status.code == 0) {
                this.loading = false
                this.info = res.result
                console.log(this.info);
                this.initFunnelChart();


            }
            console.log(res);

        },
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        // 获取当天 0 点时间
        getTodayMidnight() {
            const now = new Date();
            // 设置日期为明天
            now.setDate(now.getDate() + 1);
            // 重置小时、分钟、秒、毫秒为 0
            now.setHours(0, 0, 0, 0);
            return now;
        },
        // 获取昨天 0 点时间
        getYesterdayMidnight() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate()); // 减去一天
            yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
            return yesterday;
        },
    }
}
</script>

<style>
.page {
    display: flex;
    justify-content: center;

}
</style>
