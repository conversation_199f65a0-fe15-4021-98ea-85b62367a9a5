<template>
    <d2-container class="page">
        <el-form :inline="true" :model="formInline" class="demo-form-inline"
            style="background-color: #ffffff; padding: 20px">
            <el-form-item label="是否删除">
                <el-select  v-model="formInline.isDeleted" placeholder="是否删除">
                    <el-option label="是" value="1"></el-option>
                    <el-option label="否" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="用户id">
                <el-input v-model="formInline.userId" placeholder="请输入用户id" clearable type="number"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="getList(1)">查询</el-button>
                <el-button type="primary" @click="clear(1)">清除</el-button>
                <el-button type="primary" @click="add_click()">新增</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%">
            <el-table-column fixed prop="userId" label="用户id" align="center"></el-table-column>
            <el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
            <el-table-column prop="isDeleted" label="是否删除" align="center">
                <template scope="scope">
                    <el-tag v-if="scope.row.isDeleted == '1'" type="danger">是</el-tag>
                    <el-tag v-if="scope.row.isDeleted == '0'">否</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="createAdminUserName" label="创建的管理员用户名" align="center"></el-table-column>
            <el-table-column prop="updateAdminUserName" label="修改的管理员用户名" align="center"></el-table-column>
            <el-table-column prop="extra" label="其他信息" align="center"></el-table-column>
            <el-table-column prop="remark" label="备注" align="center"></el-table-column>
            <el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
            <el-table-column prop="updateAt" label="修改时间" align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button type="text"  @click="audit_click(scope.row)">修改</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
                style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
            </el-pagination>
        </div>

        <!-- 新增修改 -->
        <el-dialog :title="title" :visible.sync="isDialog" center>
            <el-form :model="form">
                <el-form-item label="用户id:" :label-width="formLabelWidth" required v-if="this.title == '新增'">
                    <el-input v-model="form.userId" placeholder="请输入用户id" clearable style="width: 80%"
                        type="number"></el-input>
                </el-form-item>

                <!-- <el-form-item label="用户地址:" :label-width="formLabelWidth" required v-if="this.title == '新增'">
                    <el-input v-model="form.contractAddress" placeholder="请输入用户地址" clearable style="width: 80%"
                        type="number"></el-input>
                </el-form-item>

                <el-form-item label="选择名单类型" :label-width="formLabelWidth" required v-if="this.title == '新增'">
                    <el-select  v-model="form.subType" placeholder="请选择名单类型" clearable style="width: 80%">
                        <el-option v-for="option in subTypeOptions" :key="option.type" :label="option.desc"
                            :value="option.type">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="选择名单类型" :label-width="formLabelWidth" required v-if="this.title == '新增'">
                    <el-select  v-model="form.listType" placeholder="请选择名单类型" clearable style="width: 80%">
                        <el-option label="黑名单" value="BLACK">
                        </el-option>
                        <el-option label="白名单" value="WHITE">
                        </el-option>
                    </el-select>
                </el-form-item> -->


                <el-form-item label="备注:" :label-width="formLabelWidth" v-if="this.title == '新增'">
                    <el-input v-model="form.remark" placeholder="请输入备注" clearable style="width: 80%"></el-input>
                </el-form-item>
                <el-form-item label="是否删除" v-if="this.title == '修改'">
                    <el-select  v-model="form.isDeleted" placeholder="是否删除" style="width: 80%">
                        <el-option label="是" :value="1"></el-option>
                        <el-option label="否" :value="0"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="isDialog = false">取 消</el-button>
                <el-button type="primary" @click="click_submit">确 定</el-button>
            </div>
        </el-dialog>
    </d2-container>
</template>

<script>
import { getBitUser, updateBitUser, addBitUser } from '@/api/bit'
export default {
    name: 'bitriskBoard',
    data() {
        return {
            tableData: [],
            total: 1,

            formInline: {
                isDeleted: '',
                userId: '',
                contractAddress: '',
                subType: '',
                listType: ''
            },
            isDialog: false,
            formLabelWidth: '120px',
            form: {
                remark: '',
                userId: '',
                isDeleted: ''
            },
            title: '',
            userId: '',
            subTypeOptions: [
                { type: 'GET_ITEM', desc: '购买作品/受赠作品' },
                { type: 'BUY_ITEM_LIMIT', desc: '锁单限制' },
                { type: 'BUY_ITEM', desc: '购买作品' },
                { type: 'BUY_ITEM_CAPTCHA', desc: '购买作品验证码' },
                { type: 'WITHDRAW', desc: '提现' },
                { type: 'ITEM_VERIFY', desc: '作品审核' },
                { type: 'TARGET_DUTY', desc: '求购任务' },
                { type: 'ID_CARD_NO_AUTH', desc: '实名-证件号限制' }
            ]
        }
    },
    mounted() {
        this.getList(1)
    },
    methods: {
        // 查询列表
        async getList(page) {
            const res = await getBitUser({
                pageNum: page,
                pageSize: 15,
                userId: this.formInline.userId,
                isDeleted: this.formInline.isDeleted,
                type: 'BLACK',
                subType: 'BIT_CREATE_ORDER'
            })
            if (res.result == null) {
                this.tableData = []
            } else {
                this.tableData = res.result.list
                this.total = res.result.totalCount
            }
        },
        xuanze(val) {
            this.getList(val)
        },
        clear() {
            this.formInline.userId = ''
            this.formInline.isDeleted = ''
        },
        // 点击新增
        add_click() {
            this.isDialog = true
            this.title = '新增'
        },
        // 点击修改
        audit_click(val) {
            this.isDialog = true
            this.title = '修改'
            this.userId = val.userId
            this.form.isDeleted = val.isDeleted
        },
        // 新增编辑确定
        async click_submit() {
            if (this.title === '新增') {
                await addBitUser({
                    remark: this.form.remark,
                    userId: this.form.userId,
                    type: 'BLACK',
                    subType: 'BIT_CREATE_ORDER'
                })
                this.getList(1)
                this.isDialog = false
                this.form = {
                    remark: '',
                    userId: '',
                    isDeleted: ''
                }
                this.$message.success('新增成功')
            } else {
                await updateBitUser({
                    isDeleted: this.form.isDeleted,
                    userId: this.userId,
                    type: 'BLACK',
                    subType: 'BIT_CREATE_ORDER'
                })
                this.getList(1)
                this.isDialog = false
                this.form = {
                    remark: '',
                    userId: '',
                    isDeleted: ''
                }
                this.$message.success('修改成功')
            }
        }
    }
}
</script>

<style></style>