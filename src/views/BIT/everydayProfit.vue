<template>
    <div v-loading="loading">
        <el-form :inline="true" label-position="top" style="margin: 20px">
            <!-- 日期时间选择器 -->
            <el-form-item>
                <el-date-picker v-model="selectedDatetime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    @change="TimeChange" placeholder="选择日期时间" />
            </el-form-item>

            <!-- 查询按钮 -->
            <el-form-item>
                <el-button type="primary" @click="handleQuery">查询</el-button>
            </el-form-item>
        </el-form>
        <!-- 第一个柱状图 -->
        <div id="barChart" style="width: 100%; height: 400px;"></div>
        <!-- 第二个折线图 -->
        <div id="lineChart" style="width: 100%; height: 400px;"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { profitInfo } from "@/api/bit"
export default {
    data() {
        return {
            selectedDatetime: '', // 保存选择的日期时间
            queryTime: '', // 保存查询结果
            chartData: {
                // '07-15', '07-17', '07-19', '07-21', '07-23', '07-25', '07-27', '07-29', '7-30', '7-31', '8-3', '8-4', '8-5', '8-6', '8-7', '8-8'
                dates: [],
                values: [
                    // { date: '07-15', profit: 50000, loss: 0, balance: 150000, floatingProfit: 10000 },
                    // { date: '07-17', profit: 0, loss: -50000, balance: 100000, floatingProfit: -10000 },
                    // { date: '07-19', profit: 50000, loss: 0, balance: 150000, floatingProfit: 20000 },
                    // { date: '07-21', profit: 50000, loss: 0, balance: 200000, floatingProfit: 30000 },
                    // { date: '07-23', profit: 0, loss: -50000, balance: 150000, floatingProfit: 0 },
                    // { date: '07-25', profit: 50000, loss: 0, balance: 200000, floatingProfit: 10000 },
                    // { date: '07-27', profit: 0, loss: -50000, balance: 150000, floatingProfit: -5000 },
                    // { date: '07-29', profit: 50000, loss: 0, balance: 200000, floatingProfit: 25000 },
                    // { date: '7-30', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '7-31', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-3', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-4', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-5', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-6', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-7', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                    // { date: '8-8', profit: 50000, loss: 0, balance: 250000, floatingProfit: 10000 },
                ],
                floatingProfit: [10000, -10000, 20000, 30000, 0, 10000, -5000, 25000, 10000],
                actualProfit: [10000, -10000, 15000, 30000, 0, 10000, -5000, 25000, 10000]
            },
            pageNum: 1,
            pageSize: 10,
            loading: false
        };
    },
    mounted() {
        this.initBarChart();
        this.initLineChart();
        this.getlist()
    },
    // watch: {
    //     // 监听日期时间选择器的值变化
    //     selectedDatetime(newValue) {
    //         if (newValue) {
    //             const date = new Date(newValue); // 将字符串转为 Date 对象
    //             this.queryTime = date.toISOString(); // 格式化为 ISO 8601 格式
    //         } else {
    //             this.queryTime = ''; // 清空查询时间
    //         }
    //     },
    // },
    methods: {
        TimeChange(e) {
            var that = this
            console.log(e, 12312312312)  // 打印结果为：Thu Jun 30 2022 10:19:19 GMT+0800 (中国标准时间)
        },
        handleQuery() {
            if (this.selectedDatetime) {
                // console.log(this.queryTime);
                this.pageNum = 1
                this.getlist(this.selectedDatetime + '.000')
                this.$message.success('查询成功');
            } else {
                this.$message.error('请选择日期和时间');
            }

        },
        async getlist(e) {
            this.loading = true
            let data = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                searchDay: e ? e : ''
            }
            let res = await profitInfo(data)
            if (res.status.code == 0) {
                this.loading = false
                this.values = res.result.list

                this.values.forEach(item => {
                    const date = new Date(item.recordDay);
                    const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，补零
                    const day = String(date.getDate()).padStart(2, '0'); // 获取日期，补零
                    item.date = `${month}-${day}`;
                });

                this.dates = res.result.list.map(item => {
                    const date = new Date(item.recordDay);
                    const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，补零
                    const day = String(date.getDate()).padStart(2, '0'); // 获取日期，补零
                    return `${month}-${day}`;
                });
                console.log(this.values, this.dates, 12312312312);

            }
        },
        initBarChart() {
            const barChart = echarts.init(document.getElementById('barChart'));
            const option = {
                title: {
                    text: '每日盈亏统计',
                    left: 'center',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                    formatter: (params) => {
                        const dataIndex = params[0].dataIndex;
                        const data = this.chartData.values[dataIndex];
                        return `
                时间: ${data.date}<br/>
                余额: ${data.balance}<br/>
                当日盈亏: +${data.profit || data.loss}<br/>
                浮动盈亏: ${data.floatingProfit}
              `;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.dates,
                    boundaryGap: true,
                },

                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100,
                    }
                ],
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        name: '盈利',
                        type: 'bar',
                        stack: 'total',
                        label: { show: true, position: 'inside' },
                        data: this.chartData.values.map(item => item.profit),
                        itemStyle: { color: '#FF4D4D' },
                        barWidth: 20
                    },
                    {
                        name: '亏损',
                        type: 'bar',
                        stack: 'total',
                        label: { show: true, position: 'inside' },
                        data: this.chartData.values.map(item => item.loss),
                        itemStyle: { color: '#4CAF50' },
                        barWidth: 20
                    }
                ]
            };
            barChart.setOption(option);
        },
        initLineChart() {
            const lineChart = echarts.init(document.getElementById('lineChart'));
            const option = {
                title: {
                    text: '浮动和实际盈亏对比',
                    left: 'center',
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: (params) => {
                        const dataIndex = params[0].dataIndex;
                        const data = this.chartData.values[dataIndex];
                        return `
                时间: ${data.date}<br/>
                余额: ${data.balance}<br/>
                当日盈亏: +${data.profit || data.loss}<br/>
                浮动盈亏: ${data.floatingProfit}
              `;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.dates,
                },
                yAxis: {
                    type: 'value',
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100,
                    }
                ],
                series: [
                    {
                        name: '浮动盈亏',
                        type: 'line',
                        data: this.chartData.floatingProfit,
                        itemStyle: { color: '#FFD700' },
                        smooth: true,
                    },
                    {
                        name: '实际盈亏',
                        type: 'line',
                        data: this.chartData.actualProfit,
                        itemStyle: { color: '#00BFFF' },
                        smooth: true,
                    }
                ]
            };
            lineChart.setOption(option);
        }
    }
};
</script>

<style scoped>
#barChart,
#lineChart {
    margin-top: 20px;
}
</style>