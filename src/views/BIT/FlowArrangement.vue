<template>
    <d2-container class="page">
        <common-form :is-edit="!isDetail" :submit="saveOrUpdate" :data="formData" :schema="formSchema"
            label-width="300px">
            <template #code="scope">
                <el-upload :action="action" :headers="token" :on-success="handlePicSuccess"
                    :class="{ hide: hideUpload_introduce }" :on-change="handleIntroduceUploadHide"
                    :on-remove="handleIntroduceRemove" :file-list="fileListImg" class="avatar-uploader">
                    <img v-if="qrcode.value" :src="qrcode.value" class="avatar">
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </template>
            <!-- qqqun -->
            <template #qqqun="scope">
                <el-input size="mini" style="width: 340px;" v-model="qqlink.value"></el-input>
            </template>
            <!-- banner -->
            <template #banner="scope">
                <el-button type="text" @click="gobanner">点击配置</el-button>
                <!-- <el-upload
                    class="avatar-uploader"
                    :action="uploadUrl"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                    :headers="headers"
                    :data="{type:'banner'}"
                    :on-error="handleError"
                /> -->
            </template>
        </common-form>
    </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import {
    mapActions
} from 'vuex'
import {
    downloadBlob
} from '@/utils/helper'

export default {
    name: 'casting',
    components: {
        CommonForm,
        CommonTable,
        FileUploader
    },
    data() {
        return {
            limitCount: 1,

            fileListImg: [],
            hideUpload_introduce: false,
            action:
                process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/upload',
            token: { AdminAuthorization: localStorage.getItem('usertoken') },
            qrcode: {},
            qqlink: {},
            templateUrl: '', // 盲盒内作品模板地址
            templateUrl1: '', // 盲盒内作品模板地址
            isDetail: false, // 详情
            activityNo: null, // 活动编号
            formData: {
                email: '',
                joinLeapPlan: 1,
                // leader:'云龙',
                castingTiming: 0,
                price: 0,
                ctid: "",
                creation: ""
            },
            formSchema: [
                {
                    type: 'input',
                    label: '客服二维码：​',
                    placeholder: '请选择二维码：​',
                    slot: 'code',
                },
                {
                    type: 'input',
                    label: 'QQ群链接：​',
                    placeholder: '请输入QQ群链接：​',
                    slot: 'qqqun',
                },
                {
                    label: 'banner配置：',
                    slot: 'banner'
                },
                {
                    type: 'action'
                    // exclude: ['reset', 'submit', 'back']
                }
            ],
            creationNum: 'x',
            collectionNum: 'x',
            formSchemaAppend: [{
                type: 'search',
                label: '系列ID/系列名：',
                placeholder: '请输入系列ID/系列名',
                field: 'ctid',
                rules: [{
                    required: true,
                    message: '请输入系列ID/系列名',
                    trigger: 'blur'
                }]
            }, {
                type: 'number-input',
                label: '补充多少个token：',
                placeholder: '请输入补充多少个token',
                field: 'createNum',
                rules: [{
                    required: true,
                    message: '请输入补充多少个token',
                    trigger: 'blur'
                }]
            }, {
                type: 'action',
                exclude: ['reset']
            }],
            formDataAppend: {
                ctid: "",
                createNum: "",
            },
            loading: false
        }
    },
    mounted() {
        this.getSelete()
    },
    methods: {
        gobanner() {
            this.$router.push({
                name: 'addBannerYs',
                query: {
                    // businessLine: this.businessLine
                }
            })
        },
        async saveOrUpdate() {
            await this.$api.saveOrUpdate({
                name: this.qqlink.name,
                value: this.qqlink.value,
                tips: this.qqlink.tips
            })
            await this.$api.saveOrUpdate({
                name: this.qrcode.name,
                value: this.qrcode.value,
                tips: this.qrcode.tips
            })
            this.getSelete()
            this.$message.success('保存成功')
        },
        // 图片移除
        handleIntroduceRemove(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
            this.qrcode.value = ''
        },
        handlePicSuccess(res, file) {
            console.log(res, '上传');
            this.qrcode.value = res.result.url
        },
        handleIntroduceUploadHide(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
        },
        async getSelete(page) {
            const res = await this.$api.getPageOptionsVo({
                pageNum: page,
                pageSize: 15,
                tips: '衍生QQ群链接'
            })

            const res1 = await this.$api.getPageOptionsVo({
                pageNum: page,
                pageSize: 15,
                tips: '衍生客服二维码'
            })
            this.qqlink = res.result.list[0]
            this.qrcode = res1.result.list[0]
            // this.tableData = res.result.list
            // this.total = res.result.totalCount
        },
        async submit() {

        }

    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-top: 80px;
}
</style>