<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :showExport="false" @onExport="houseExport" @onSubmit="onQueryChange"
            ref="query" :data="query" @onReset="onQueryReset">
        </common-query>
        <el-button type="primary" size="small" @click="add" style="margin-bottom: 20px;">新增</el-button>

        <!-- :showExport="true" @onExport="houseExport" -->
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #ctime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.createAt) }}</span>
            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

        <!-- 窗口 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import { getBitHold, exportBitHold, getExperienceList, addExperience } from "@/api/bit"
import CommonQuery from '@/components/CommonQuery_h'
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "Experience",
    components: {
        CommonQuery,
        CommonForm,
        CommonTable
    },
    props: {},
    data() {
        return {
            formSchema: [
                {
                    type: 'input',
                    label: '活动名称：',
                    placeholder: '请输入活动名称',
                    field: 'title',
                    rules: [{
                        required: true,
                        message: '请输入活动名称',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'textarea',
                    label: '发放人群：',
                    placeholder: '请输入发放人群',
                    field: 'userInfo',
                    rules: [{
                        required: true,
                        message: '请输入发放人群',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '体验金金额：',
                    placeholder: '请输入体验金金额',
                    field: 'amount',
                    rules: [{
                        required: true,
                        message: '请输入体验金金额',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'textarea',
                    label: '备注：',
                    placeholder: '请输入备注',
                    field: 'remark',
                    rules: [{
                        required: true,
                        message: '请输入备注',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'action'
                }
            ],
            formData: {},
            dialogVisible: false,
            dialogTitle: "新增体验金",
            listLoading: true,
            tableData: [],
            query: {

            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            page: {
                pageSize: 10
            }, // 分页数据
            querySchema: [ // 搜索组件架构

                {
                    type: 'select',
                    label: '状态：',
                    field: 'status',
                    placeholder: '请选择状态',
                    options: [
                        {
                            label: '部分完成',
                            value: '3'
                        },
                        {
                            label: '完成',
                            value: '2'
                        },
                        {
                            label: '执行中',
                            value: '1'
                        },
                        {
                            label: '初始',
                            value: '0'
                        },
                    ]
                },
                {
                    label:"用户地址conadd",
                    type: 'input',
                    field: 'userInfo',
                    placeholder: '请输入用户地址conadd',
                },
                {
                    label:"发放时间",
                    type: 'datetimerange',
                    field: 'startDate',
                    field2: 'endDate',
                    placeholder: '请选择发放时间',
                }
            ],
            tableSchema: [ // 表格架构
                {
                    label: '活动名称',
                    field: 'title',
                },
                {
                    label: '发放人群',
                    field: 'userInfo',
                },


                {
                    label: '体验金金额',
                    field: 'amount',
                },
                {
                    label: '备注',
                    field: 'remark',
                },
                {
                    label: '状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: [{
                        label: '初始',
                        value: 0
                    }, {
                        label: '执行中',
                        value: 1
                    },
                    {
                        label: '完成',
                        value: 2
                    },
                    {
                        label: '部分完成',
                        value: 3
                    }
                    ],
                },
                {
                    label: '发放时间',
                    slot: 'ctime',
                },

            ],

            tableData: [{}],

            page: {
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            isCheck: false,
            query1: {
                // status: '1'
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        async submit() {
            this.formData.amount = Number(this.formData.amount)
            this.formData.userInfo = this.formData.userInfo.replace(/\n+/g, ',');
            const res = await this.$api.addExperience(this.formData)
            if (res.status.code == 0) {
                this.$message.success('操作成功')
                this.dialogVisible = false
                this.getList()
            }
        },
        add() {
            this.dialogVisible = true
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async checkIncome(row) {
            console.log(row)
            this.isCheck = true
            // this.getList(row.ctid)
            // const res = await this.$api.qifeiHoldHistory({
            //    orderType:1,
            //   ctid: row.ctid
            // })

        },
        onQueryReset() {
            this.query = {
                userType: '1',
                orderType: '4'
            }
            this.query1 = {
                userType: '1',
                orderType: '4',
                status: '1'
            }
            this.listLoading = true
            this.getList()
        },
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        //分页数量切换
        xuanzeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            const params = {

                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            delete params.totalCount
            const {
                status,
                result
            } = await this.$api.getExperienceList(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []

                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        async houseExport(e) {
            const res = await exportBitHold({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '当前持仓' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
    }
}
</script>

<style></style>