<template>
    <d2-container class="page">
        <!--  -->
        <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange"
            ref="query" :data="query" @onReset="onQueryReset"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #tradeTime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.tradeTime) }}</span>
            </template>
        </common-table>
        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

    </d2-container>
</template>

<script>
import { getBitDetail, exportBitDetail } from "@/api/bit"
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    components: {
        CommonQuery,
        CommonTable
    },
    data() {
        return {
            pageNum: 1,
            pageSize: 20,
            tableData: [{}],

            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
                userType: '1',
            },
            listLoading: false,
            tableData: [],
            query: {
                userType: '1',
                createStart: '',
                createEnd: '',
                offsets: 1
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            page: {
                totalCount: 0,
                pageSize: 20
            }, // 分页数据
            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '订单号：',
                    placeholder: '订单号',
                    field: 'orderId'
                },
                {
                    type: 'datetimerange',
                    label: '成交时间：',
                    placeholder: '请输入成交时间',
                    field: 'createStart',
                    field2: 'createEnd',
                },
                // {
                //   type: 'datetimerange',
                //   label: '首笔成交时间：',
                //   placeholder: '请输入首笔成交时间',
                //   field: 'firstStart',
                //   field2: 'firstEnd',
                // },
                // {
                //     type: 'datetimerange',
                //     label: '完全成交时间：',
                //     placeholder: '请输入完全成交时间',
                //     field: 'tradedStart',
                //     field2: 'tradedEnd',
                // },
                // {
                //     type: 'select',
                //     label: '订单状态：',
                //     field: 'status',
                //     placeholder: '请选择订单状态',
                //     options: [
                //         // {
                //         //   label: '委托中',
                //         //   value: '0'
                //         // },
                //         {
                //             label: '部分成交',
                //             value: '1'
                //         },
                //         {
                //             label: '完全成交',
                //             value: '2'
                //         },
                //         // {
                //         //   label: '下单失败',
                //         //   value: '3'
                //         // }, {
                //         //   label: '平仓中',
                //         //   value: '4'
                //         // },
                //         // {
                //         //   label: '已平仓',
                //         //   value: '5'
                //         // },
                //         // {
                //         //   label: '部分成交',
                //         //   value: '10'
                //         // },
                //     ]
                // },
                // {
                //     type: 'select',
                //     label: '用户类型：',
                //     field: 'userType',
                //     placeholder: '请选择用户类型',
                //     options: [{
                //         label: '主力',
                //         value: "4"
                //     },
                //     {
                //         label: '大户',
                //         value: '1'
                //     },
                //     {
                //         label: '中户',
                //         value: '2'
                //     },
                //     {
                //         label: '小户',
                //         value: '3'
                //     }
                //     ]
                // },
                // {
                //     type: 'select',
                //     label: '仓位方向：',
                //     field: 'longShort',
                //     placeholder: '请选择仓位方向',
                //     options: [{
                //         label: '多',
                //         value: '1'
                //     },
                //     {
                //         label: '空',
                //         value: '2'
                //     },
                //     ]
                // },
                {
                    type: 'input',
                    label: '用户con add：',
                    placeholder: '请输入用户con add',
                    field: 'conAdd'
                },
                {
                    type: 'input',
                    label: '用户昵称：',
                    placeholder: '用户昵称',
                    field: 'nickname'
                },
            ],
            tableSchema: [ // 表格架构

                {
                    label: '时间',
                    slot: 'tradeTime',
                    width: '210px'

                },
                {
                    label: '金额',
                    field: 'tradePrice',
                    width: '120px'
                },
                {
                    label: '份数',
                    field: 'quantity',
                    width: '120px'
                },
                {
                    label: '买单用户昵称',
                    field: 'bidNickname',
                    width: '150px'
                },
                {
                    label: '卖单用户昵称',
                    field: 'askNickname',
                    width: '170px'
                },
                {
                    label: '买单订单号',
                    field: 'bidId',
                    width: '210px'
                },
                {
                    label: '卖单订单号',
                    field: 'askId',
                    width: '210px'

                },
                {
                    label: '买单用户 conadd',
                    field: 'bidContractAddress',
                    width: '240px'
                },
                {
                    label: '卖单用户  conadd',
                    field: "askContractAddress",
                    width: '240px'

                },

                {
                    label: '主动单方向',
                    field: 'trendSide',
                    width: '120px',
                    type: 'tag',
                    tagMap: {
                        'BUY': {
                            label: '买',

                        },
                        'SELL': {
                            label: '卖',
                            tagType: 'info'
                        }
                    },
                },


                {
                    label: '卖单开平方向',
                    field: 'askOpen',
                    width: '150px',
                    type: 'tag',
                    tagMap: {
                        'OPEN': {
                            label: '开仓',

                        },
                        'CLOSE': {
                            label: '平仓',
                            tagType: 'info'
                        }
                    },
                },


                {
                    label: '买单开平方向',
                    field: 'bidOpen',
                    width: '150px',
                    type: 'tag',
                    tagMap: {
                        'OPEN': {
                            label: '开仓',

                        },
                        'CLOSE': {
                            label: '平仓',
                            tagType: 'info'
                        }
                    },
                },
                {
                    label: '成交订单id',
                    field: 'id',
                    width: '120px',
                },
                // {
                //     label: '用户类型',
                //     field: 'userType',
                //     type: 'tag',
                //     width: '100px',
                //     tagMap: {
                //         4: {
                //             label: '主力',

                //         },
                //         1: {
                //             label: '大户',
                //             tagType: 'info'
                //         },
                //         2: {
                //             label: '中户',
                //             tagType: 'info'
                //         },
                //         3: {
                //             label: '小户',
                //             tagType: 'info'
                //         }
                //     },
                // },
            ],
        }
    },

    mounted() {
        // this.getDetail()
        this.getList()

    },
    methods: {
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async houseExport(e) {
            // ExportOpenRecords
            // ExportMarketRecords
            // ExportDealHistory
            const res = await exportBitDetail({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '开仓明细' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {
            this.query = {
                userType: '1'
            }
            this.query1 = {
                userType: '1',
            }
            this.listLoading = true
            this.getList()
        },
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            this.getList(true)
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            //   ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            // return
            const params = {
                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
                offsets: 1
            }
            console.log('列表数据', params)
            const {
                status,
                result
            } = await getBitDetail(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                let dataList = []
                this.tableData = []
                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                console.log("this.tableData", this.tableData)
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize

            }
        },
        getDetail() {
            let data = {
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }
            getBitDetail(data).then(res => {
            })
        }
    }
}
</script>

<style lang="scss" scoped></style>