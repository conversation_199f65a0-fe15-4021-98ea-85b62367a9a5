<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="发送状态">
        <el-select  v-model="formInline.status" placeholder="发送状态" clearable>
          <el-option label="未发送" value="0"></el-option>
          <el-option label="已发送" value="1"></el-option>
          <el-option label="发送中" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型">
        <el-select  v-model="formInline.type" placeholder="类型" clearable>
          <el-option label="消费" value="1"></el-option>
          <el-option label="充值" value="2"></el-option>
          <el-option label="奖励" value="3"></el-option>
          <el-option label="NFTCN-大学通知" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="邮件">
        <el-input
          v-model="formInline.emailTo"
          placeholder="请输入邮件"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="download()">下载模板</el-button>
        <el-button type="primary" @click="upload()">导入邮件</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        fixed
        prop="userId"
        label="用户id"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="userName"
        label="用户名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userNickName"
        label="用户昵称"
        align="center"
      ></el-table-column>
		<el-table-column
		  prop="createdAt"
		  label="创建时间	"
		  align="center"
		></el-table-column>
      <el-table-column
        prop="contractAddress"
        label="用户地址"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="status"
        label="邮件发送状态"
        align="center"
        width="120"
      >
        <template scope="scope">
          <el-tag v-if="scope.row.status == '1'" type="success">已发送</el-tag>
          <el-tag v-if="scope.row.status == '0'" type="danger">未发送</el-tag>
          <el-tag v-if="scope.row.status == '2'">发送中</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="emailTo"
        label="发送邮件地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="title"
        label="邮件标题"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="content"
        label="邮件内容"
        align="center"
		 width="420"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            
            @click="modif_click(scope.row)"
            :disabled="scope.row.status != '2'"
            >修改状态</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>

    <el-dialog
      title="上传"
      :visible.sync="dialogFileUploadVisible"
      class="file_dialog"
      width="30%"
    >
      <el-upload
        ref="dataFormFile"
        :multiple="false"
        :limit="1"
        :data="fileUpData"
        :file-list="fileUpList"
        :auto-upload="false"
        :action="upload_file_url"
        :on-success="fileUpSuccess"
        :on-error="fileUpError"
        :headers="token"
      >
        <el-button type="success">选择文件</el-button>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFileUploadVisible = false">取消</el-button>
        <el-button type="primary" @click="fileUpload()">上传</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改状态" :visible.sync="isDialog" width="35%" center>
      <el-form :model="form">
        <el-form-item label="选择状态" :label-width="120">
          <el-select  v-model="form.status" placeholder="发送状态" clearable>
            <el-option label="重新发送" value="0"></el-option>
            <el-option label="发送成功" value="1"></el-option>
            <!-- <el-option label="发送中" value="2"></el-option> -->
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="modif_submit()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'send_email',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        status: undefined,
        type: undefined,
        emailTo: undefined
      },
      form: {
        status: undefined
      },
      headers: {
        authorization: ''
      },
      formLabelWidth: '120px',
      isDialog: false,
      fileUpData: {}, // 文件上传附带参数
      fileUpList: [], // 上传的文件列表
      dialogFileUploadVisible: false,
      upload_file_url:
        process.env.VUE_APP_BASE_URL + 'appusercenter/adminApi/smsEmails/missWebSign/importEmailsExcel', // 文件上传地址
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      send_id: undefined
    }
  },
  mounted () {
    this.getSelete(1)
  },
  methods: {
    // 查询
    async getSelete (page) {
      const res = await this.$api.listPageEmails({
        pageNum: page,
        pageSize: 10,
        status: this.formInline.status,
        type: this.formInline.type,
        emailTo: this.formInline.emailTo
      })
      if (res.result == null) {
        this.tableData = []
      } else {
        this.tableData = res.result.list
        this.total = res.result.totalCount
      }
    },
    xuanze (val) {
      this.getSelete(val)
    },
    clear () {
      this.formInline.status = undefined
      this.formInline.type = undefined
      this.formInline.emailTo = undefined
    },
    // 下载模板
    async download () {
      const res = await this.$api.downloadEmailsImportTemplate({})
      window.location.href = res.result.emailsTemplateUrl
      this.$message.success('下载成功')
    },
    // 导入邮件
    upload () {
      this.dialogFileUploadVisible = true
    },
    /**
     * 文件上传成功时的钩子
     */
    fileUpSuccess (res, file, fileList) {
			if(res.status.code===0){
				console.log('文件上传成功', res)
				this.$notify.success({
				  title: '成功',
				  message: '文件上传成功'
				})
				this.fileUpList = []
				this.dialogFileUploadVisible = false
			}else{
				this.$notify.error({
				  title: '错误',
				  message: res.status.msg
				})
				this.fileUpList = []
				this.dialogFileUploadVisible = false
			}
      
    },

    /**
     * 文件上传失败时的钩子
     */
    fileUpError (err, file, fileList) {
      console.log('文件上传失败', err)
      this.$notify.error({
        title: '错误',
        message: '文件上传失败'
      })
    },
    fileUpload () {
      this.$refs.dataFormFile.submit()
    },
    // 修改状态
    modif_click (val) {
      this.isDialog = true
      this.send_id = val.id
    },
    async modif_submit () {
      await this.$api.updateEmailsSendStatus({
        status: this.form.status,
        id: this.send_id
      })
      this.isDialog = false
      this.$message.success('修改成功')
      this.getSelete(1)
    }
  }
}
</script>
