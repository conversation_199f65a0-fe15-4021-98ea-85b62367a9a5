<template>
  <div class="container">
    <div class="img_title">
      <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374776145693204480.png" alt="">
    </div>
    <div class="title-container-card">
      <span class="title">{{$t("realTimeER.title")}}</span>
    </div>
    <div class="cart_div">
      <div class="input_black">
        <div class="input_div">
          <p>{{$t("realTimeER.label1")}}</p>
          <div class="black">
            <div class="left_name" ref="leftNameRef" @click="openCurrencySelect('left')">
              <img class="tx" :src="currencyList[leftCurrency].flag" alt="" />
              {{ currencyList[leftCurrency].code }}
              <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374799119729123328.png" alt="" />
            </div>
            <div class="input">
              <input type="text">
            </div>
          </div>
        </div>
        <div class="icon">
          <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374798937302065152.png" alt="" srcset="">
        </div>
        <div class="input_div">
          <p>{{$t("realTimeER.label2")}}</p>
          <div class="black">
            <div class="left_name" ref="rightNameRef" @click="openCurrencySelect('right')">
              <img class="tx" :src="currencyList[rightCurrency].flag" alt="" />
              {{ currencyList[rightCurrency].code }}
              <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374799119729123328.png" alt="" />
            </div>
            <div class="input">
              <input type="text">
            </div>
          </div>
        </div>
      </div>
      <div class="button_text">
        <div>1.000CAD = 0.7232 USD</div>
        <button>{{$t("realTimeER.button")}}</button>
      </div>
    </div>
    <div class="title-container-card">
      <span class="title">{{$t("realTimeER.title2")}}</span>
    </div>
    <div class="hv_list">
      <ul class="header">
        <li class="icon">
          <p>{{$t("realTimeER.tableTh1")}}</p>
        </li>
        <li class="parities">{{$t("realTimeER.tableTh2")}}</li>
        <li class="service_charge">{{$t("realTimeER.tableTh3")}}</li>
      </ul>
      <ul class="table mb_206">
        <li v-for="(item,index) in compareList" :key="index">
          <div class="icon">
            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png" alt="" srcset="">
          </div>
          <div class="parities">
            {{item.rate}}
          </div>
          <div class="service_charge">
            {{item.fee}}
          </div>
        </li>
      </ul>
    </div>
    <div class="title-container-card mt_80">
      <span class="title">{{$t("realTimeER.title3")}}</span>
    </div>
    <div class="pink_wallet_div flex ">
      <div class="wallet_div_item p_24 mr_4 ml_4 cursor_pointer transition" :class="{'active': activeIndex === index}"
        v-for="(item,index) in advantages" :key="index" @click="handleClick(index)">
        <div class="wallet_div_item_img ">
          <img :src="item.img" alt="">
        </div>
        <div class="wallet_div_title fz_24 pl_30 pr_30">{{item.title}}</div>
        <div class="wallet_div_desc fz_18 mt_10 pl_10 pb_10 pr_10">{{item.desc}}</div>
      </div>
    </div>

    <!-- 下拉弹窗 -->
    <div
      v-if="showCurrencySelect"
      class="currency_select_popup"
      :style="popupStyle"
    >
      <div
        v-for="(item, idx) in currencyList"
        :key="item.code"
        class="currency_select_item"
        :class="{ selected: idx === (selecting === 'left' ? leftCurrency : rightCurrency) }"
        @click="selectCurrency(idx)"
      >
        <img :src="item.flag" class="currency_flag" />
        <span class="currency_code">{{ item.code }}</span>
        <span class="currency_name">{{ item.name }}</span>
        <span v-if="idx === (selecting === 'left' ? leftCurrency : rightCurrency)" class="currency_selected_icon">
          <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377975913617645568.png" alt="" srcset="">
        </span>
      </div>
    </div>
    <!-- 遮罩 -->
    <div v-if="showCurrencySelect" class="currency_select_mask" @click="closeCurrencySelect"></div>
  </div>
</template>

<script setup>
  import { ref, computed, watchEffect, nextTick, onMounted, onBeforeUnmount } from 'vue'
  import { useI18n } from "vue-i18n"; 
  const { locale, t } = useI18n();
  const activeIndex = ref(0)
  const compareList = [
    { name: 'WISE', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1288', fee: '6.44USD' },
    { name: 'BMO', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1256', fee: '37.89USD' },
    { name: 'CITIBANK', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1256', fee: '50.00USD' },
    { name: 'CIBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1252', fee: '65.00USD' },
    { name: 'OCBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1251', fee: '65.00USD' },
    { name: 'RBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1254', fee: '115.00USD' },
    { name: 'OFX', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/ima ge/1374815832294121472.png', rate: '0.1252', fee: '100.00USD' },
    { name: 'TD', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1253', fee: '230.00USD' },
    { name: 'PayPal', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1205', fee: '38.99USD' }
  ]
  const advantages = computed(() => [
    {
      img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376617913472606208.png',
      title: t("realTimeER.advantage1_title"),
      desc: t("realTimeER.advantage1_desc")
    },
    {
      img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376617992510070784.png',
      title: t("realTimeER.advantage2_title"),
      desc: t("realTimeER.advantage2_desc")
    },
    {
      img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376618091109769216.png',
      title: t("realTimeER.advantage3_title"),
      desc: t("realTimeER.advantage3_desc")
    },
    {
      img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376618144067051520.png',
      title: t("realTimeER.advantage4_title"),
      desc: t("realTimeER.advantage4_desc")
    }
  ])

  const handleClick = (index) => {
    activeIndex.value = index
  }
  watchEffect(() => {
    // features.value.forEach((item) => {
    //     item.title = t(item.titleKey); // 监听语言变化，自动更新 title
    // });
    console.log("语言切换为：", locale.value);
    localStorage.setItem('lang', locale.value)

  });

  const currencyList = [
    { code: 'USD', name: '美元', flag: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374776145693204480.png' },
    { code: 'CAD', name: '加拿大', flag: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374799011251838976.png' },
    { code: 'GBP', name: '英镑', flag: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374776180739641344.png' }
  ]

  const leftCurrency = ref(1)  // 默认CAD
  const rightCurrency = ref(0) // 默认USD
  const showCurrencySelect = ref(false)
  const selecting = ref('left') // 当前弹窗属于左还是右
  const popupStyle = ref({})
  const leftNameRef = ref(null)
  const rightNameRef = ref(null)

  function openCurrencySelect(side) {
    selecting.value = side
    nextTick(() => {
      updatePopupPosition()
      showCurrencySelect.value = true
    })
  }

  function selectCurrency(idx) {
    if (selecting.value === 'left') {
      leftCurrency.value = idx
    } else {
      rightCurrency.value = idx
    }
    showCurrencySelect.value = false
  }

  function closeCurrencySelect() {
    showCurrencySelect.value = false
  }

  function updatePopupPosition() {
    let refEl = selecting.value === 'left' ? leftNameRef.value : rightNameRef.value
    if (refEl) {
      const rect = refEl.getBoundingClientRect()
      popupStyle.value = {
        position: 'fixed',
        top: `${rect.top + rect.height + 14}px`,
        left: `${rect.left}px`,
        minWidth: `${rect.width}px`,
        zIndex: 1001
      }
    }
  }

  function handleScrollOrResize() {
    if (showCurrencySelect.value) {
      updatePopupPosition()
    }
  }

  onMounted(() => {
    window.addEventListener('scroll', handleScrollOrResize, true)
    window.addEventListener('resize', handleScrollOrResize)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScrollOrResize, true)
    window.removeEventListener('resize', handleScrollOrResize)
  })
</script>

<style lang="scss" scoped>
  @import "../pxto.scss";

  .title-container-card {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    opacity: 1;
    margin-bottom: 79px; 
    transition: all 0.8s;

    &.loaded-title {
      transform: translateY(60px);
      opacity: 1;
    }

    .title {
      font-size: 18px;
      font-weight: bold;
      position: relative;

      font-family: MiSans-bold;
      font-weight: 700;
      font-size: 36px;
      line-height: 100%;
      letter-spacing: 0px;
      text-align: center;
      text-transform: capitalize;
      color: #fff;

      &::before,
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 76px;
        height: 2px;
        background-color: rgba(255, 255, 255, 0.2);
      }

      &::before {
        left: -100px;
      }

      &::after {
        right: -100px;
      }
    }
  }

  .container {
    width: 100%;
    max-width: 1440px;
    padding: 0 20px;
    box-sizing: border-box;
    margin: 60px auto;
    font-family: 'MiSans';

    .img_title {
      img {
        width: 271px;
        height: 271px;
      }
    }

    .cart_div {
      width: 1194px;
      background-color: #222222;
      border: 1px solid rgba(255, 255, 255, 0.2);
      margin: 0 auto;
      border-radius: 10px;
      padding: 28px;
      margin-bottom: px(207);

      .input_black {
        display: flex;
        justify-content: space-between;
        justify-items: flex-end;

        .input_div {
          p {
            font-size: px(24);
            text-align: left;
            color: rgba(255, 255, 255, 0.6);
            margin: 0px;
            margin-bottom: 15px;
            font-weight: 400;
          }

          .black {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #fff;
            border-radius: 15px;
            width: 503px;
            padding: 13px 12px 13px 17px;

            .left_name {
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              font-size: 17px;
              font-weight: 600;

              .tx {
                width: 33px;
                height: 33px;
                margin-right: 14px;
                margin-left: 0px;
              }

              img {
                width: 18px;
                height: 18px;
                margin-left: 12px;
              }
            }

            .input {
              width: 100%;
              height: 100%;

              input {
                width: 100%;
                height: 100%;
                border: none;
                background-color: transparent;
                font-size: 17px;
                font-weight: 600;
                color: #fff;
                text-align: right;
              }
            }
          }

        }

        .icon {
          display: flex;
          align-items: flex-end;
          padding-bottom: 14px;

          img {
            width: px(54);
            height: px(54);
          }
        }
      }

      .button_text {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: px(31);

        >div {
          font-size: px(24);
          font-weight: 600;
          color: #fff;
        }

        button {
          width: px(180);
          height: px(59);
          border-radius: px(29);
          color: #000000;
          font-size: px(20);
          background-color: #FF95B2;
          outline: none;
        }
      }
    }

    .hv_list {
      width: 1194px;
      margin: 0 auto;

      .header {
        display: flex;
        justify-content: space-between;
        margin-bottom: px(40);
        padding: 0 px(25);

        li {
          font-size: px(24);
          color: #EF88A3;

          &.icon {
            width: 384px;
            text-align: left;

            p {
              margin: 0;
              margin-left: px(107);
            }
          }

          &.parities {
            text-align: center;
            width: 450px;
          }

          &.service_charge {
            text-align: center;
            width: 359px;
          }
        }
      }
    }

    .table {
      width: 1194px;

      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #212121;
        height: px(120);
        border-radius: px(16);
        border: 1px solid #4B4B4B;
        margin-bottom: px(15);
        padding: px(25);
        font-size: px(20);
      }

      .icon {
        width: 384px;
        text-align: left;
        height: px(70);
        line-height: px(70);

        img {
          width: px(74);
          margin-left: px(107);
        }
      }

      .parities {
        text-align: center;
        width: 450px;
        border-right: 1px solid rgba(255, 255, 255, 0.2);
        border-left: 1px solid rgba(255, 255, 255, 0.2);
        flex: 1;
        height: px(70);
        line-height: px(70);
      }

      .service_charge {
        text-align: center;
        width: 359px;
        height: px(70);
        line-height: px(70);
      }
    }
  }

  .pink_wallet_div {
    margin: 0 auto;
    width: 1194px;
    margin-bottom: px(208);

    .wallet_div_item {
      width: 384px;
      height: 541px;
      background-color: #212121;
      border: 1px solid #4B4B4B;
      border-radius: 16px;
      border: 4px solid rgba(255, 255, 255, 0.2);

      &.active {
        border: 4px solid rgba(239, 136, 163, 0.2);
        background-color: #EF88A3;
      }

      .wallet_div_item_img {
        height: 270px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
          width: 245px;
        }
      }
    }

    .wallet_div_title {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      height: px(120);
    }

    .wallet_div_desc {
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .currency_select_mask {
    position: fixed;
    left: 0; top: 0; right: 0; bottom: 0;
    background: transparent;
    z-index: 1000;
  }
  .currency_select_popup {
    background: #232323;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.18);
    min-width: 221px;
    padding: 0;
    overflow: hidden;
    border: 1px solid #333;
    z-index: 1001;
  }
  .currency_select_item {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    cursor: pointer;
    color: #fff;
    font-size: 14px;
    position: relative;
    background: transparent;
    transition: background 0.2s;
    border-bottom: 1px solid #2d2d2d;
    width: 221px;
    &:last-child {
      border-bottom: none;
    }
    &:hover, &.selected {
      background: #313131;
    }
    .currency_flag {
      width: 28px; height: 28px; margin-right: 12px; border-radius: 50%;
      background: #fff;
      object-fit: cover;
    }
    .currency_code { font-weight: bold; margin-right: 8px; }
    .currency_name { color: #ccc; }
    .currency_selected_icon {
      margin-left: auto;
      color: #FF95B2;
      font-size: 18px;
      font-weight: bold;
      img {
        width: 16px;
        height: 16px;
      }
    }
  }
</style>