<template>
    <div class="referral-dashboard">
        <!-- 顶部 Banner -->
        <section class="intro">
            <div class="left">
                <span class="tag">{{ $t('broker.invite') }}</span>
                <span class="title">{{ $t('broker.invite_title') }}</span>
                <div class="plan-btn" @click="nav_to('partenerplan')">{{ $t('broker.plan_btn') }}</div>
            </div>
            <div class="invite-box">
                <!-- 推荐码 -->
                <div class="invite-row" style="width: 583px;">
                    <div class="label">{{ $t("broker.invite_code") }}</div>
                    <div class="value-box">
                        <span class="value">{{ inviteCodes.inviteCode || '--' }}</span>
                        <div class="copy-icon" @click="copy(inviteCodes.inviteCode)">
                            <img
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377728141459021824.png" />
                        </div>
                    </div>
                </div>

                <!-- 两个邀请链接 -->
                <!-- <div class="invite-link-group">
                    <div class="invite-row" style="flex:1" v-for="(item, index) in links" :key="index">
                        <span class="label">{{ item.label }}</span>
                        <div class="value-box">
                            <span class="value">{{ item.url }}</span>
                            <div class="copy-icon" @click="copy(item.url)">
                                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377728141459021824.png" />
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- 按钮 + 二维码 -->
                <div class="invite-action">
                    <div class="invite-btn" @click="show = true">{{ $t('broker.invite') }}</div>
                    <div class="qrcode-btn" @click="postshow = true">
                        <img
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377730701905453056.png" />
                    </div>
                </div>
            </div>
        </section>

        <!-- 三步骤 -->
        <section class="steps">
            <div class="num">
                <div class="bg">01</div>
                <div class="text">{{ $t('broker.copy_link') }}</div>
            </div>
            <div class="dash"></div>
            <div class="num">
                <div class="bg">02</div>
                <div class="text">{{ $t('broker.invite') }}</div>
            </div>
            <div class="dash"></div>
            <div class="num">
                <div class="bg">03</div>
                <div class="text">{{ $t('broker.earn_title') }}</div>
            </div>
        </section>

        <!-- 数据总览 -->
        <section class="overview">
            <div class="overview-title">
                <span>{{ $t('broker.data_overview') }}</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377734056811782144.png" />
            </div>
            <div class="overview-tabs">
                <div class="tab-btn tab" @click="isMenuVisible = !isMenuVisible">
                    <span>{{ rebateTabs2[nowref].label }}</span>
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377736457270681600.png"
                        alt="">

                    <transition name="fade">
                        <div v-if="isMenuVisible" class="language-menu" @click.stop ref="languageMenu">
                            <div v-for="(language, index) in rebateTabs2" :key="index" class="item"
                                @click="selectLanguage(language, index)">
                                {{ language.label }}
                                <img v-if="index == nowref"
                                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250327/046a950e72e553c6f91489c57eb7d08b_64x64.png" />
                            </div>
                        </div>
                    </transition>
                </div>
                <div v-for="(item, index) in tabOptions" :key="index"
                    :class="['tab-btn', { active: currentTab === item.key }]" @click="changeTab(item.key)">
                    {{ item.label }}
                </div>
            </div>
            <div class="overview-panels">
                <div class="panel">
                    <div class="panel-title">{{ $t('broker.my_earn') }}

                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377737590722617344.png"
                            alt="">
                    </div>
                    <div class="panel-value">{{ mockData.totalRebateAmount || 0 }} <span class="symbol">USDT</span>
                    </div>
                </div>
                <div class="panel">
                    <div class="panel-title">{{ $t('broker.invite_friends') }}
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377737590722617344.png"
                            alt="">
                    </div>
                    <div class="panel-value">{{ mockData.totalTradedInvitee || 0 }} <span class="symbol">{{
                        $t("broker.invite_friends_num") }}</span></div>
                </div>
            </div>
        </section>

        <!-- 我的好友 -->
        <section class="friends" v-if="pagedFriends.length">
            <div class="overview-title">
                <span>{{ $t('broker.my_friends') }}</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377734056811782144.png" />
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>{{ $t('broker.register_time') }}</th>
                        <th style="text-align: center;">{{ $t('broker.phone') }}</th>
                        <th style="text-align: right;">{{ $t('broker.is_trade') }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in pagedFriends" :key="index">
                        <td>{{ row.registerTime }}</td>
                        <td style="text-align: center;">{{ row.phone }}</td>
                        <td style="text-align: right;">
                            {{ [
                                row.contractTraded ? $t('broker.contract') : '',
                                row.usStockTraded ? $t('broker.us_stock') : '',
                                row.hkStockTraded ? $t('broker.hk_stock') : ''
                            ].filter(Boolean).join('、') }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <div class="left">
                    <div class="n" @click="prevPage" :disabled="pagination.page === 0">
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377741220217446400.png"
                            alt="">
                    </div>
                    <span>{{ pagination.page + 1 }} / {{ totalFriendPages }}</span>
                    <div class="n" @click="nextPage(totalFriendPages)"
                        :disabled="pagination.page >= totalFriendPages - 1">
                        <img class="re"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377741220217446400.png"
                            alt="">
                    </div>
                </div>
                <div class="right">
                    {{ $t("broker.export_data") }}
                </div>
            </div>
        </section>

        <!-- 我的返佣 -->
        <section class="rebates" v-if="pagedRebates.length">
            <div class="overview-title">
                <span>{{ $t('broker.my_earn') }}</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377734056811782144.png" />
            </div>
            <!-- <div class="rebate-tabs">
                <button v-for="item in rebateTabs" :key="item.key"
                    :class="['tab-btn', { active: currentRebateTab === item.key }]"
                    @click="currentRebateTab = item.key">
                    {{ item.label }}
                </button>
            </div> -->
            <div class="rebates-tabs">
                <div v-for="(item, index) in rebateTabs" :key="index"
                    :class="['tab-btn', { active: currentRebateTab === item.key }]" @click="checkRebateTab(item.key)">
                    {{ item.label }}
                </div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>{{ $t('broker.date') }}</th>
                        <th>{{ $t('broker.phone') }}</th>
                        <th>{{ $t('broker.type') }}</th>
                        <th>{{ $t('broker.commission_count') }}</th>
                        <th>{{ $t('broker.issue_time') }}</th>
                        <th>{{ $t('broker.status') }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in pagedRebates" :key="index">
                        <td>{{ row.rebateTime }}</td>
                        <td>{{ row.phone }}</td>
                        <td>{{ row.rebateType }}</td>
                        <td>{{ row.count }}</td>
                        <td>{{ row.rebateTime }}</td>
                        <td>{{ row.rebateStatus }}</td>
                    </tr>
                </tbody>
            </table>
            <!-- <div class="pagination">
                <button @click="prevPageRebate" :disabled="paginationRebate.page === 0">上一页</button>
                <span>{{ paginationRebate.page + 1 }} / {{ totalRebatePages }}</span>
                <button @click="nextPageRebate(totalRebatePages)"
                    :disabled="paginationRebate.page >= totalRebatePages - 1">下一页</button>
            </div>
            <div class="export-btn">导出数据</div> -->

            <div class="pagination">
                <div class="left">
                    <div class="n" @click="prevPageRebate" :paginationRebate="pagination.page === 0">
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377741220217446400.png"
                            alt="">
                    </div>
                    <span>{{ paginationRebate.page + 1 }} / {{ totalRebatePages }}</span>
                    <div class="n" @click="nextPageRebate(totalRebatePages)"
                        :disabled="paginationRebate.page >= totalRebatePages - 1">
                        <img class="re"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377741220217446400.png"
                            alt="">
                    </div>
                </div>
                <div class="right">
                    {{ $t("broker.export_data") }}
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="faq">
            <div class="title-container-card">
                <span class="title">FAQ</span>
            </div>

            <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
                <div class="faq-question" @click="toggle(index)">
                    <span>{{ item.question }}</span>
                    <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377683340055371776.png"
                        alt="arrow" />
                </div>

                <transition name="faq-slide">
                    <div class="faq-answer" v-show="activeIndex === index">
                        {{ item.answer }}
                    </div>
                </transition>
            </div>

            <!-- <div class="faq-list">
                <div v-for="(item, index) in faqList" :key="index" class="faq-item">
                    <div class="question">{{ item.q }}</div>
                </div>
            </div> -->
        </section>

        <!-- 底部按钮 -->
        <footer class="footer">
            <!-- <button class="footer-btn"></button>
            <button class="login-btn">登录</button> -->

            <div class="inner">
                <span>{{ $t('broker.apply_agent') }}</span>
                <div class="login-btn" @click="visible = true">{{ $t('broker.apply_agent_title') }}</div>
            </div>
        </footer>


        <div v-show="visible" class="modal-mask">
            <transition name="fade-zoom">
                <div class="modal-wrapper">
                    <div class="modal-container">
                        <!-- Header -->
                        <div class="modal-header">
                            <div class="title">{{ $t('broker.apply_agent_content') }}</div>

                            <img class="close-btn" @click="visible = false"
                                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379224833215782912.png"
                                alt="">
                        </div>

                        <!-- Form -->
                        <div class="modal-body">

                            <div class="sub-title">{{ $t('broker.apply_agent_content_1') }}</div>
                            <div class="email-box">
                                <div class="email">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379228054437060608.png"
                                        alt="">
                                    {{ userInfo.email || '--' }}
                                </div>
                                <div class="uid">UID: {{ uid }}</div>
                            </div>

                            <div class="form-group">
                                <div class="required label">{{ $t('broker.apply_agent_content_2') }}</div>
                                <div class="form-row">
                                    <input maxlength="30" type="text" :placeholder="$t('broker.apply_agent_content_5')"
                                        v-model="form.name" />
                                    <input type="text" maxlength="30" disabled :value="userInfo.email" />
                                </div>

                                <div class="form-row">
                                    <input type="text" maxlength="30" class="full"
                                        :placeholder="$t('broker.apply_agent_content_6')" v-model="form.telegram" />
                                    <div></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="required label">{{ $t('broker.apply_agent_content_7') }}</div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_7')"
                                        v-model="form.address" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_8')"
                                        v-model="form.BDcode" />
                                </div>


                            </div>

                            <div class="form-group" style="margin-bottom: 0;">
                                <div class="required label">{{ $t('broker.apply_agent_content_3') }}</div>

                                <div class="proof" v-if="hasX">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        X
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_9')" v-model="formPl.Xlink" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_10')" v-model="formPl.Xfans" />
                                    </div>
                                </div>

                                <div class="proof" v-if="hasYoutube">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        Youtube
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_9')"
                                            v-model="formPl.YoutubeLink" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_10')"
                                            v-model="formPl.YoutubeFans" />
                                    </div>
                                </div>

                                <div class="proof" v-if="hasFacebook">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        Facebook
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_9')"
                                            v-model="formPl.FacebookLink" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_10')"
                                            v-model="formPl.FacebookFans" />
                                    </div>
                                </div>

                                <div class="proof" v-if="hasTikTok">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        TikTok
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_9')"
                                            v-model="formPl.TikTokLink" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_10')"
                                            v-model="formPl.TikTokFans" />
                                    </div>
                                </div>

                                <div class="proof" v-if="hasTelegram">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        Telegram
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_14')"
                                            v-model="formPl.TelegramLink" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_15')"
                                            v-model="formPl.TelegramPersonalLink" />
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30" class="full"
                                            :placeholder="$t('broker.apply_agent_content_16')"
                                            v-model="formPl.TelegramChannelNum" />
                                        <div></div>
                                    </div>
                                </div>

                                <div class="proof" v-if="hasOthers">
                                    <div class="labelX">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379492820443291648.png"
                                            alt="">
                                        Others
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_11')"
                                            v-model="formPl.OthersName" />
                                        <input type="text" maxlength="30"
                                            :placeholder="$t('broker.apply_agent_content_12')"
                                            v-model="formPl.OthersLink" />
                                    </div>
                                    <div class="form-row">
                                        <input type="text" maxlength="30" class="full"
                                            :placeholder="$t('broker.apply_agent_content_13')"
                                            v-model="formPl.OthersFans" />
                                        <div></div>
                                    </div>
                                </div>

                                <div class="checkbox-group">
                                    <label v-for="(item, i) in platforms" :key="i">
                                        <input type="checkbox" v-model="form.selectedPlatforms" :value="item" />
                                        {{ item }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="modal-footer">
                            <button class="submit-btn" :disabled="!form.name" @click="submitForm">{{
                                $t('broker.apply_agent_submit') }}</button>
                        </div>
                    </div>
                </div>
            </transition>
        </div>


        <InviteQRCodeModal v-model:visible="show" :invite-code="inviteCodes.inviteCode" />
        <SharePosterModal v-model:visible="postshow" :link="url" />
        <!-- `https://pinkwallet.com/h5/pages/login/register?code=${inviteCodes.inviteCode}` -->
    </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { GetUserInfo } from "../../api/pinkwallet"
import { getMyRebate, getDataOverview, getMyFriends, getInviteCode, submitAgentApply } from "../../api/pinkexchange.js"
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'
const router = useRouter();

import InviteQRCodeModal from './component/InviteQRCodeModal.vue'
import SharePosterModal from './component/SharePosterModal.vue'
const postshow = ref(false)
const show = ref(false)
const { t } = useI18n();
const activeIndex = ref(null);
// https://test-www.pinkwallet.xyz/h5/#/pages/project/login/register

const nowref = ref(0)
const isMenuVisible = ref(false)
const nowreflabel = ref('')
const uid = localStorage.getItem("uid") || ''
const userInfo = ref({})
const visible = ref(false);

const form = reactive({
    name: '',
    telegram: '',
    influence: [],
    selectedPlatforms: [],
    platformData: {} // 存储每个平台的三个字段
});

const fetchGetUserInfo = async () => {
    let res = await GetUserInfo()
    if (res.code == 200) {
        userInfo.value = res.result
    }
}
fetchGetUserInfo()

const formPl = reactive({})

const platforms = ['X', 'Youtube', 'Facebook', 'TikTok', 'Telegram', 'Others'];

const hasX = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'x')
})

const hasYoutube = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'youtube')
})

const hasFacebook = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'facebook')
})

const hasTikTok = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'tiktok')
})

const hasTelegram = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'telegram')
})

const hasOthers = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'others')
})


const submitForm = async () => {
    
    let data = {
        name: form.name,
        // phone: form.telegram,
        email: userInfo.value.email,
        // form.telegram
        otherContracts: "",
        address: form.address,
        bdCode: form.BDcode,
        influenceProof: JSON.stringify(formPl),
    }
    if (!form.address) {
        return
    }
    if (!form.telegram) {
        return
    }
    // 判断formPl是否为空
    if (Object.keys(formPl).length === 0) {
        // ElMessage.warning('请填写影响力证明信息');
        return
    }

    // 进一步验证formPl中是否有有效的数据
    const hasValidData = Object.values(formPl).some(value =>
        value !== null && value !== undefined && value !== ''
    );

    if (!hasValidData) {
        ElMessage.warning('请填写完整的影响力证明信息');
        return
    }
    let res = await submitAgentApply(data)
    if (res.code == 200) {
        visible.value = false;
    }
    // console.log('提交信息：', form.value);
    // 提交逻辑
};

const rebateTabs2 = computed(() => [
    { key: 'CONTRACT', label: t('broker.contract1') },
    { key: 'HK_STOCK', label: t('broker.hk_stock1') },
    { key: 'US_STOCK', label: t('broker.us_stock1') }
])

const selectLanguage = (language, index) => {
    nowref.value = index
    isMenuVisible.value = false;
    nowreflabel.value = language.key
    getDataOverviewDatas(language.key)
};

const changeTab = (index) => {
    currentTab.value = index
    // activeIndex.value = index
    getDataOverviewDatas()
    isMenuVisible.value = false;
};
const currentTab = ref(null)

/**
 * 数据总览
 */
const getDataOverviewDatas = async (e) => {

    const now = new Date();
    let timeBegin = null;
    let timeEnd = null;

    // const key = tabOptions[currentTab.value].key;
    const key = currentTab.value || ''

    switch (key) {
        case 'today': // 昨日
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case 'top': // 最近7天（不含今天）
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case '30day': // 最近30天（不含今天）
            timeBegin = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30).getTime() / 1000;
            timeEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000 - 1;
            break;

        case 'decent': // 固定时间范围
            timeBegin = new Date('2025-09-11T00:00:00').getTime() / 1000;
            timeEnd = new Date('2025-09-12T23:59:59').getTime() / 1000;
            break;

        case 'all': // 全部时间
        default:
            timeBegin = null;
            timeEnd = null;
            break;
    }

    let res = await getDataOverview({
        timeBegin,
        timeEnd,
        tradeType: rebateTabs2[nowref.value].key
    })
    if (res.code == 200) {
        const { totalRebateAmount, totalTradedInvitee } = res.result;
        mockData.totalRebateAmount = totalRebateAmount;
        mockData.totalTradedInvitee = totalTradedInvitee;
    }
};

getDataOverviewDatas()

const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}


const inviteCodes = ref({})

const url = ref('')
const getInviteCodes = async () => {
    let res = await getInviteCode()
    if (res.code == 200) {
        inviteCodes.value = res.result
        // import.meta.env.VITE_APP_BASE_URL;
        url.value = import.meta.env.VITE_APP_URL + '?code=' + res.result.inviteCode
        links[0].url = import.meta.env.VITE_APP_BASE_URL + res.result.inviteCode
        links[1].url = import.meta.env.VITE_APP_BASE_URL + res.result.inviteCode

    }
    // console.log(res)
}


getInviteCodes()
console.log(inviteCodes);



const currentRebateTab = ref(null)

/**
 * 我的返佣
 */
const FetchgetMyRebate = async () => {
    let res = await getMyRebate({
        itemLimit: 500,
        tradeType: currentRebateTab.value
    })
    if (res.code == 200 && res.result.length) {
        rebates.value = res.result
    }
}
FetchgetMyRebate()

const checkRebateTab = (tab) => {

    currentRebateTab.value = tab
    FetchgetMyRebate()
}



const links = [
    { label: '邀请链接（主网）', url: '' },
    { label: '邀请链接（加速）', url: '' }
]

function copy(text) {
    navigator.clipboard.writeText(text)
    alert(`copied：${text}`)
}

const nav_to = (e) => {
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    });
};


// const faqListZh = computed(() => [
//     {
//         question: t("broker.apply_agent_content"),
//         answer: t("broker.faqa1"),
//     },
//     {
//         question: "为何要加入PinkWallet邀请返佣计划？",
//         answer: "1.返佣场景多，覆盖合约，以及美股、港股多种交易。2.返佣速度快，次日即返，无需等待多个工作日。3.返佣比例业内最高水平，PinkWallet邀请返佣计划最高可返20%。4.潜在收益高，满足门槛要求，即可升级为 代理商 享受无限级返佣，最低45%起。"
//     },
//     {
//         question: "如何通过PinkWallet邀请返佣计划赚取收益？",
//         answer: `1.设置佣金分享比例
//         设置您想与好友分享多少推荐佣金。

// 2.邀请好友，建立联系
// 与好友和社交媒体分享您的推荐链接或二维码。

// 3.共同获利
// 在好友开始交易后赚取高达20%的佣金。`
//     },
//     {
//         question: "如何能升级成为PinkWallet代理商计划，享受无限返佣？",
//         answer: "只要达到对应的门槛要求，你可以升级为PinkWallet并享受无限层级，终身返佣。在邀请返佣界面即可一键申请成为 PinkWallet 代理商。"
//     }]);

const faqListZh = computed(() => [
    {
        question: t("broker.faq_5_q"),
        answer: t("broker.faq_5_a")
    },
    {
        question: t("broker.faq_6_q"),
        answer: t("broker.faq_6_a")
    },
    {
        question: t("broker.faq_7_q"),
        answer: t("broker.faq_7_a")
    },
    {
        question: t("broker.faq_8_q"),
        answer: t("broker.faq_8_a")
    }]);

const tabOptions = [
    { key: null, label: t('broker.all_time') },
    { key: 'today', label: t('broker.yesterday') },
    { key: 'top', label: t('broker.last_7_days') },
    { key: '30day', label: t('broker.last_30_days') },
    { key: 'decent', label: '2025.09.11 - 2025.09.12' },
]

const rebateTabs = computed(() => [
    { key: null, label: t('broker.all') },
    { key: 'CONTRACT', label: t('broker.contract') },
    { key: 'HK_STOCK', label: t('broker.hk_stock') },
    { key: 'US_STOCK', label: t('broker.us_stock') }
])


const mockData = {
    totalRebateAmount: '',
    totalTradedInvitee: ""
}


const friends = ref([])

const fetchgetMyFriends = async () => {
    let res = await getMyFriends({ itemLimit: 100 })
    if (res.code == 200 && res.result.length) {
        friends.value = res.result
    }

}
fetchgetMyFriends()

const rebates = ref([])

const pagination = reactive({ page: 0, size: 4 })
const paginationRebate = reactive({ page: 0, size: 4 })

const totalFriendPages = computed(() => Math.ceil(friends.value.length / pagination.size))
const totalRebatePages = computed(() => Math.ceil(rebates.value.length / paginationRebate.size))

const pagedFriends = computed(() => friends.value.slice(pagination.page * pagination.size, (pagination.page + 1) * pagination.size))
const pagedRebates = computed(() => rebates.value.slice(paginationRebate.page * paginationRebate.size, (paginationRebate.page + 1) * paginationRebate.size))

function nextPage(totalPages) {
    if (pagination.page < totalPages - 1) pagination.page++
}
function prevPage() {
    if (pagination.page > 0) pagination.page--
}
function nextPageRebate(totalPages) {
    if (paginationRebate.page < totalPages - 1) paginationRebate.page++
}
function prevPageRebate() {
    if (paginationRebate.page > 0) paginationRebate.page--
}
</script>

<style lang="scss" scoped>
.referral-dashboard {
    color: #fff;
    // padding: 40px 20px;
    margin: 0 auto;


    .intro {
        margin-top: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        gap: 154px;

        .left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .tag {
                font-family: MiSans;
                font-weight: 600;
                font-size: 20px;
                line-height: 27px;
                color: #EF88A3;
            }

            .title {
                white-space: nowrap;
                font-family: MiSans;
                font-weight: 700;
                font-size: 54px;
                line-height: 72px;
                color: #fff;
                margin: 4px 0 36px 0;
            }

            .plan-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #EF88A3;
                color: #fff;
                border: none;
                width: 178px;
                height: 54px;
                border-radius: 12px;
                font-family: MiSans;
                font-weight: 600;
                font-size: 20px;

            }
        }

        .invite-box {
            display: flex;
            flex-direction: column;
            // gap: 12px;
            // max-width: 500px;
            // margin: 0 auto;

            .invite-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // align-items: center;
                border-radius: 12px;
                padding: 15px 20px;
                // font-size: 14px;
                background: #191919;
                border: 1.13px solid #FFFFFF33;

                .label {
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 150%;
                    color: #fff;
                }

                .value-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .value {
                        width: 82px;
                        text-overflow: ellipsis;
                        color: #fff;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 150%;
                        opacity: 0.6;
                    }

                    .copy-icon {
                        width: 17px;
                        height: 17px;

                        img {
                            cursor: pointer;
                            margin-left: 4px;
                            width: 17px;
                            height: 17px;
                        }
                    }
                }
            }

            .invite-link-group {
                margin-top: 8px;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .invite-action {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 14px;

                .invite-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                    background: #EF88A3;
                    border: none;
                    color: #fff;
                    cursor: pointer;

                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 16px;
                    height: 58px;
                    border-radius: 12px;

                }

                .qrcode-btn {

                    border-radius: 12px;
                    // font-size: 14px;
                    background: #191919;
                    border: 1.13px solid #FFFFFF33;
                    margin-left: 8px;
                    width: 58px;
                    height: 58px;

                    display: flex;
                    align-items: center;
                    justify-content: center;

                    cursor: pointer;

                    img {
                        width: 44px;
                        height: 44px;
                    }
                }
            }
        }
    }

    .steps {
        margin-top: 255px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #FF95B2;
        height: 174px;
        gap: 97px;

        .num {
            display: flex;
            align-items: center;
            color: #FF95B2;
            gap: 18px;

            .bg {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 35px;
                height: 35px;
                font-family: MiSans;
                font-weight: 700;
                font-size: 14px;
                background: #0A0A0A;
                border-radius: 50%;
            }

            .text {
                font-family: MiSans;
                font-weight: 600;
                font-size: 20px;
                line-height: 100%;
                color: #000;
            }
        }

        .dash {
            width: 102px;
            height: 1px;
            border-top: 1px dashed #000000
        }
    }

    .overview {
        // margin-bottom: 60px;
        max-width: 1280px;
        margin: 120px auto 0 auto;

        .overview-title {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
                font-family: MiSans;
                font-weight: 700;
                font-size: 32px;
                line-height: 100%;
                color: #fff;
            }

            img {
                cursor: pointer;
                width: 24px;
                height: 24px;
            }
        }

        .overview-tabs {
            display: flex;
            gap: 12px;
            margin: 20px 0 32px 0;

            .tab {
                position: relative;
                white-space: nowrap;

                .language-menu {
                    position: absolute;
                    top: 79px;
                    left: 0px;
                    // width: 200px;
                    border-radius: 8px;
                    z-index: 10;
                    animation: slide-in 0.8s ease-out;
                    border-radius: 8px;
                    background: #1C1D1F;
                    transition: all .8s;

                    .items {
                        width: 150px;
                        height: 39px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        transition: all .8s;
                        padding-left: 14px;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 137%;
                        letter-spacing: 0px;
                        text-transform: capitalize;
                        color: rgba(255, 255, 255, .6);
                    }

                    .item {
                        width: 150px;
                        height: 39px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        cursor: pointer;
                        transition: all .8s;
                        padding: 0 17px 0 14px;
                        font-family: MiSans;
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 137%;
                        letter-spacing: 0px;
                        text-transform: capitalize;
                        color: #fff;

                        img {
                            width: 16px;
                            height: 16px;
                        }

                        &:hover {
                            &:last-child {
                                border-bottom-right-radius: 8px;
                                border-bottom-left-radius: 8px;

                            }

                            background-color: rgba(255, 255, 255, .1);
                        }
                    }
                }


                .fade-enter-active,
                .fade-leave-active {
                    transition: opacity 0.3s ease;
                }

                .fade-enter,
                .fade-leave-to {
                    opacity: 0;
                }

                @keyframes slide-in {
                    from {
                        transform: translateY(-20px);
                        opacity: 0;
                    }

                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
            }

            .tab-btn {
                cursor: pointer;
                padding: 14px 35px 16px;
                border-radius: 40px;
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                white-space: nowrap;
                color: #fff;
                font-family: MiSans;
                font-weight: 500;
                font-size: 18px;
                transition: background-color 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    margin-left: 20px;
                    width: 16px;
                    height: 16px;
                }

                &.active {
                    background: #EF88A3;
                }
            }
        }

        .overview-panels {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            .panel {
                background: #141414;
                border-radius: 18px;
                // padding: 20px;
                width: 316px;
                height: 165px;
                border: 1.13px solid #363636;
                display: flex;
                align-items: flex-start;
                flex-direction: column;
                justify-content: center;
                padding-left: 28px;

                .panel-title {
                    color: #fff;
                    opacity: .5;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 14px;

                    img {
                        width: 13px;
                        height: 13px;
                    }
                }

                .panel-value {
                    margin-top: 14px;
                    font-family: MiSans;
                    font-weight: 700;
                    font-size: 38px;
                    line-height: 50px;
                    color: #fff;
                    display: flex;
                    align-items: center;

                    .symbol {
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 19px;
                        margin: 20px 0 0 14px;
                    }
                }
            }
        }
    }

    .friends {
        max-width: 1280px;
        margin: 80px auto 0 auto;

        .overview-title {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
                font-family: MiSans;
                font-weight: 700;
                font-size: 32px;
                line-height: 100%;
                color: #fff;
            }

            img {
                cursor: pointer;
                width: 24px;
                height: 24px;
            }
        }

    }

    .rebates {
        max-width: 1280px;
        margin: 80px auto 0 auto;

        .overview-title {
            display: flex;
            align-items: center;
            gap: 12px;

            span {
                font-family: MiSans;
                font-weight: 700;
                font-size: 32px;
                line-height: 100%;
                color: #fff;
            }

            img {
                cursor: pointer;
                width: 24px;
                height: 24px;
            }
        }


        .rebates-tabs {
            display: flex;
            gap: 12px;
            margin: 20px 0 32px 0;

            .tab-btn {
                cursor: pointer;
                padding: 14px 35px 16px 35px;
                white-space: nowrap;
                border-radius: 40px;
                background-color: rgba(255, 255, 255, 0.1);
                border: none;
                color: #fff;
                font-family: MiSans;
                font-weight: 500;
                font-size: 18px;
                transition: background-color 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    margin-left: 20px;
                    width: 16px;
                    height: 16px;
                }

                &.active {
                    background: #EF88A3;
                }
            }
        }


    }

    .table {
        margin-top: 28px;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 16px;

        thead {
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            color: #fff;
            opacity: .5;
        }

        tbody {
            font-family: MiSans;
            font-weight: 500;
            font-size: 16px;
            color: #fff;

        }

        th,
        td {
            padding: 24px 0;
            border-bottom: 1.13px solid rgba(255, 255, 255, .1);
            text-align: left;
        }
    }

    .pagination,
    .export-btn {
        text-align: right;
        color: #f682bc;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
            display: flex;
            align-items: center;

            span {
                font-family: HarmonyOS Sans;
                font-weight: 400;
                font-size: 20px;
                line-height: 20px;
                color: #BDBDBD;
                margin: 0 16px;
            }

            .n {
                width: 32px;
                height: 32px;
                border-radius: 4px;
                background: rgba(217, 217, 217, .1);
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;

                img {
                    width: 12px;
                    height: 12px;
                }

                .re {
                    rotate: 180deg;
                }
            }
        }

        .right {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 159px;
            height: 54px;
            border-radius: 12px;
            background: #EF88A3;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            color: #fff;
        }
    }

    .rebate-tabs {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;

        .tab-btn {
            padding: 6px 14px;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.6);
            background: none;
            border: none;
            font-weight: 400;
            white-space: nowrap;

            &.active {
                background: rgba(246, 246, 246, 1);
                color: #000;
                font-weight: 500;
            }
        }
    }

    .faq {
        margin: 117px auto 0 auto;
        max-width: 1280px;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;
            margin-bottom: 8px;
            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-size: 18px;
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: 36px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #fff;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: 76px; // 横线宽度
                    height: 2px; // 横线高度
                    background-color: rgba(255, 255, 255, 0.2);
                }

                &::before {
                    left: -100px;
                }

                &::after {
                    right: -100px;
                }
            }
        }


        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, .1);
            padding: 40px 0 32px 0;
        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;

            span {
                font-family: MiSans;
                font-weight: 600;
                font-size: 24px;
                line-height: 32px;
                color: #fff;
            }
        }



        .arrow-icon {
            width: 29px;
            height: 29px;
            transition: transform 0.3s ease;
        }

        .arrow-icon.rotated {
            transform: rotate(180deg);
        }

        .faq-answer {
            overflow: hidden;
            padding-top: 24px;
            color: rgba(255, 255, 255, .8);
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            // line-height: 100%;
            text-align: left;

        }

        /* 动效过渡 */
        .faq-slide-enter-active,
        .faq-slide-leave-active {
            transition: all 0.3s ease;
        }

        .faq-slide-enter-from,
        .faq-slide-leave-to {
            opacity: 0;
            max-height: 0;
        }

        .faq-slide-enter-to,
        .faq-slide-leave-from {
            opacity: 1;
            max-height: 200px;
        }

        // .faq-list {
        //     margin-top: 8px;
        //     .faq-item {
        //         background-color: #111;
        //         border-radius: 10px;
        //         padding: 16px;
        //         margin-bottom: 12px;

        //         .question {
        //             font-size: 14px;
        //             color: #fff;
        //         }
        //     }
        // }
    }

    .footer {
        margin-top: 120px;
        text-align: center;
        // background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377684264547082240.png");
        // background-size: 100% 125%;
        background: #151515;
        height: 338px;
        color: #fff;

        .inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 21px;
        }

        span {
            font-family: MiSans;
            font-weight: 700;
            font-size: 40px;
            line-height: 53px;
        }

        .login-btn {
            background-color: #EF88A3;
            border: none;
            white-space: nowrap;
            // padding: 12px 30px;
            // border-radius: 20px;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            // width: 178px;
            padding: 0 20px;
            height: 54px;
            border-radius: 12px;
            cursor: pointer;
        }
    }
}

.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .modal-wrapper {
        width: 910px;
        background: #151515;

        border-radius: 20px;
        color: #fff;
        position: relative;

        .modal-container {
            display: flex;
            flex-direction: column;

            .modal-header {
                // padding: 22px 18px 48px 20px;
                padding: 22px 18px 12.5px 20px;

                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 0.5px solid rgba(255, 255, 255, .1);

                .title {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 20px;
                    letter-spacing: 0px;
                    color: #fff;
                }


                .close-btn {
                    cursor: pointer;
                    width: 24px;
                    height: 24px;
                }
            }

            .modal-body {
                padding: 10.5px 18px 0 20px;
                max-height: 600px;
                overflow-y: auto;

                .sub-title {
                    text-align: left;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    color: #8A8A8A;
                    // margin-top: 6px;
                }

                .email-box {
                    background: #212121;
                    border: 1px solid #3E3E3E;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 100%;
                    color: rgba(255, 255, 255, .5);
                    border-radius: 12px;
                    margin-top: 16.5px;
                    padding: 9px 22px 8px 12px;
                    display: flex;
                    justify-content: space-between;

                    .uid {

                        display: flex;
                        align-items: center;
                    }

                    .email {
                        display: flex;
                        align-items: center;
                        margin-right: 4px;

                        img {
                            width: 34px;
                            height: 34px;
                        }
                    }
                }

                .form-group {
                    // margin-bottom: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;

                    .proof {
                        .labelX {
                            text-align: left;
                            margin: 4px 0 13px 0;
                            font-size: 14px;
                            // margin-bottom: 8px;
                            // display: inline-block;
                            color: #fff;
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 16px;
                            line-height: 20px;
                            display: flex;
                            align-items: center;

                            img {
                                margin-right: 8px;
                                width: 12px;
                                height: 12px;
                            }
                        }
                    }


                    .label {
                        text-align: left;
                        margin: 24px 0 13px 0;
                        font-size: 14px;
                        // margin-bottom: 8px;
                        // display: inline-block;
                        color: #fff;
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 16px;
                        line-height: 20px;
                    }

                    .required::before {
                        content: "* ";
                        color: #EF88A3;
                    }

                    .form-row {
                        display: flex;
                        gap: 14px;
                        margin-bottom: 14px;

                        div {
                            flex: 1;
                            padding: 16px 22px;

                        }

                        input {
                            flex: 1;
                            background: #212121;
                            border: 1px solid #3E3E3E;

                            font-family: MiSans;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 100%;
                            color: #fff;
                            border-radius: 12px;
                            padding: 16px 22px;

                            &:focus {
                                outline: none;
                                // border: none;
                                box-shadow: none;
                            }
                        }

                        input[disabled] {
                            opacity: 0.5;
                        }
                    }

                    .full {
                        flex: 1;
                        background: #212121;
                        border: 1px solid #3E3E3E;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 100%;
                        color: #fff;
                        border-radius: 12px;
                        padding: 16px 22px;

                        // margin-left: 14px;
                        &:focus {
                            outline: none;
                            // border: none;
                            box-shadow: none;
                        }
                    }

                    .checkbox-group {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 24px;
                        // margin-top: 17px;
                        cursor: pointer;

                        label {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            font-size: 14px;
                            cursor: pointer;

                            input {
                                accent-color: #fff;
                            }
                        }
                    }
                }
            }

            .modal-footer {
                text-align: right;
                margin: 12px 18px 48px 0;
                display: flex;
                justify-content: flex-end;

                .submit-btn {
                    width: 130px;
                    height: 49px;
                    border-radius: 12px;
                    background: #EF88A3;
                    color: #fff;
                    border: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: MiSans;
                    font-weight: 500;
                    font-size: 14px;


                    &:not(:disabled) {
                        opacity: 1;
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    &:focus,
                    &:active {
                        outline: none;
                        border: none;
                        box-shadow: none;
                    }
                }
            }

            .fade-zoom-enter-active,
            .fade-zoom-leave-active {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .fade-zoom-enter-from,
            .fade-zoom-leave-to {
                opacity: 0;
                transform: scale(0.95);
            }

            .fade-zoom-enter-to,
            .fade-zoom-leave-from {
                opacity: 1;
                transform: scale(1);
            }
        }

    }

}
</style>