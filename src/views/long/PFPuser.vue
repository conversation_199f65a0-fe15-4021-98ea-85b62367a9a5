<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showReset="false" :show-export="true" @onExport="tableExport"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action="scope">
      	<el-button @click="open_set(scope.row)" type="text">设置</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
      </common-pagination>
    </div>
    <!--    修改用户信息-->
    <el-dialog title="修改" :visible.sync="isDialog" center :show-close="false">
      <el-form :model="userInfo" ref="form">
        <el-form-item label="用户昵称：" required>
          {{userInfo.nickname}}
        </el-form-item>
        <el-form-item label="用户地址："  required>
         {{userInfo.contractAddress}}
        </el-form-item>
        <el-form-item label="用户手机号："  required>
         {{userInfo.phone}}
        </el-form-item>
        <el-form-item label="用户身份："  required>
          <el-radio-group v-model="userInfo.userLevel">
            <el-radio :label="1">普通</el-radio>
            <el-radio :label="2">二代</el-radio>
            <el-radio :label="3">总代</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import CommonPagination from '@/components/CommonPagination'
  import {
      downloadBlob
    } from '@/utils/helper'
  export default {
    name: 'PFPuser',
    components: {
      CommonQuery,
      CommonTable,
      CommonPagination
    },
    props: {},
    data() {
      return {
        querySchema: [
          // 搜索组件架构
          {
            type: 'input',
            label: '用户con add：',
            placeholder: '用户con add',
            field: 'contractAddress'
          },
          {
            type: 'input',
            label: '用户昵称：',
            placeholder: '用户昵称',
            field: 'nickname'
          },
          {
            type: 'input',
            label: '用户手机号：',
            placeholder: '请输入用户手机号',
            field: 'phone'
          },
          {
            type: 'datetimerange',
            label: '注册时间：',
            field: 'registerTimeStart',
            field2: 'registerTimeEnd'
          },
          {
            type: 'select',
            label: '身份：',
            placeholder: '',
            field: 'userLevel',
            options: [{
              label: '普通',
              value: '1'
            },{
              label: '二代',
              value: '2'
            },{
              label: '总代',
              value: '3'
            }],
            rules: [{
              required: true,
            }]
          }
        ],
        tableSchema: [ // 表格架构
          {
            label: '用户昵称',
            field: 'nickname'
          },
          {
            label: '用户address',
            field: 'contractAddress',
          },
          {
            label: '用户手机号',
            field: 'phone',
          },
          {
            label: '实名姓名',
            field: 'realname',
          },
          {
            label: '身份证号',
            field: 'idCardNo',
          },
          {
            label: '微信昵称',
            field: 'wechatNickname',
          },
          {
            label: '微信头像',
            type:'img',
            field: 'wechatAvatar',
          },
          {
            label: '邀请码',
            field: 'inviteCode',
          },
          {
            label: '宝石数量',
            field: 'bzlStone',
          },
          {
            label: '身份',
            field: 'userLevel',
            type:'tag',
            tagMap: {
            	1: {
            		label: '普通',
            		tagType: 'info'
            	},
            	2: {
            		label: '二代',
            		tagType: 'info'
            	},
              3: {
              	label: '总代',
              	tagType: 'info'
              },
            },
          },
          {
            label: '注册时间',
            field: 'registerTime',
          },
          // {
          //   label: '是否付费玩家',
          //   field: 'isPaid',
          //   type:'tag',
          //   tagMap: {
          //   	1: {
          //   		label: '是',
          //   		tagType: 'success'
          //   	},
          //   	0: {
          //   		label: '否',
          //   		tagType: 'info'
          //   	},
          //   },
          // },
          {
          	label: '操作',
          	slot: 'action',
          	headerSlot: 'action-header',
          	width: '140px',
          	fixed: 'right'
          }
        ],
        tableData: [{}],
        page: {
          totalCount: 0,
          pageSize: 10,
          pageNum: 1
        }, // 分页数据
        query: {},
        userInfo:{},
        isDialog:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 获取列表
      async getList() {
        const params = {
          ...this.query,
          ...this.page
        }
        const {
          status,
          result
        } = await this.$api.bzlUserList(params)
        if (status.code === 0) {
          this.tableData = result.list
          this.page.totalCount = result.totalCount
          this.page.pageSize = result.pageSize
          this.page.pageCount = result.pageCount
        }
      },
      open_set(item){
        this.isDialog = true
        this.userInfo = item
      },
      async submit() {
        const {
          status,
          result
        } = await this.$api.bzlUserLevelUpdate({
            uid:this.userInfo.uid,
            userLevel:this.userInfo.userLevel
        })
        if (status.code === 0) {
            this.isDialog = false
            this.$message.success('修改成功')
        }else{
          this.isDialog = false
        }
      },
      // 导出结果
              async tableExport(item,type) {
                const params = {
                  ...this.query,
                  ...this.page
                }
                const res = await this.$api.bzlUserListExport(params)
                if (res.type === 'application/json') {
                  // blob 转 JSON
                  const enc = new TextDecoder('utf-8')
                  res.arrayBuffer().then(buffer => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                  })
                } else {
                  downloadBlob(res, 'PFP用户列表')
                  this.$message.success('导出成功')
                }
              },
    }
  }
</script>

<style lang="scss" scoped></style>
