<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showReset="false"
      ></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action="scope">
        <el-button @click="nav_set(scope.row)" type="text">修改</el-button>
      </template>
      <template #show1="scope">
        <el-button v-if="!scope.row.showText" @click="listCount(scope.row)" type="text">查看</el-button>
        <p v-else>{{scope.row.inviteActiveCount}}</p>
      </template>
      <template #show2="scope">
        <el-button v-if="!scope.row.showText" @click="listCount(scope.row)" type="text">查看</el-button>
        <p v-else>{{scope.row.inviteCount}}</p>
      </template>
      <template #show3="scope">
        <el-button v-if="!scope.row.showText" @click="listCount(scope.row)" type="text">查看</el-button>
        <p v-else>{{scope.row.totalIncome}}</p>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
      </common-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
  import CommonPagination from '@/components/CommonPagination'
  import CommonQuery from '@/components/CommonQuery_h'
  export default {
    name: 'PFPinvite',
    components: {
      CommonTable,
      CommonPagination,
      CommonQuery
    },
    props: {},
    data() {
      return {
        querySchema: [
          // 搜索组件架构
          {
            type: 'input',
            label: '总代昵称：',
            placeholder: '总代昵称',
            field: 'pInviteNickname'
          },
          {
            type: 'input',
            label: ' 邀请人昵称：',
            placeholder: '邀请人昵称',
            field: 'inviteNickname'
          },
          {
            type: 'input',
            label: '邀请人con add ：',
            placeholder: '请输入邀请人con add',
            field: 'inviteContractAddress'
          },
         {
           type: 'select',
           label: '邀请人身份：',
           placeholder: '',
           field: 'inviteUserLevel',
           options: [{
             label: '普通',
             value: '1'
           }, {
             label: '二代',
             value: '2'
           }, {
             label: '总代',
             value: '3'
           }],
           rules: [{
             required: true,
           }]
         },
          {
            type: 'input',
            label: '被邀请人昵称：',
            placeholder: '请输入被邀请人昵称',
            field: 'invitedNickname'
          },
          {
            type: 'input',
            label: '被邀请人con add：',
            placeholder: '请输入被邀请人con add',
            field: 'invitedContractAddress'
          },
          {
            type: 'select',
            label: '被邀请人身份：',
            placeholder: '',
            field: 'invitedUserLevel',
            options: [{
              label: '普通',
              value: '1'
            }, {
              label: '二代',
              value: '2'
            }, {
              label: '总代',
              value: '3'
            }],
            rules: [{
              required: true,
            }]
          },
        ],
        query: {},
        page: {
          totalCount: 0,
          pageSize: 10
        }, // 分页数据
        tableSchema: [ // 表格架构
          {
            label: '总代昵称',
            field: 'pinviteNickname'
          },
          {
            label: '邀请人昵称',
            field: 'inviteNickname',
          },
          {
            label: '邀请人con add',
            field: 'inviteContractAddress',
          },
          {
            label: '邀请人身份',
            field: 'inviteUserLevel',
            type:'tag',
            tagMap: {
            	1: {
            		label: '普通',
            		tagType: 'info'
            	},
            	2: {
            		label: '二代',
            		tagType: 'info'
            	},
              3: {
              	label: '总代',
              	tagType: 'info'
              },
            },
          },
          {
            label: '被邀请人昵称',
            field: 'invitedNickname',
          },
          {
            label: '被邀请人con add',
            field: 'invitedContractAddress',
          },
          {
            label: '被邀请人身份',
            field: 'invitedUserLevel',
            type:'tag',
            tagMap: {
            	1: {
            		label: '普通',
            		tagType: 'info'
            	},
            	2: {
            		label: '二代',
            		tagType: 'info'
            	},
              3: {
              	label: '总代',
              	tagType: 'info'
              },
            },
          },
          {
            label: '邀请人已激活下级',
            slot: 'show1',
          },
          {
            label: '邀请人总邀请人数',
            slot: 'show2',
          },
          {
            label: '邀请人总获取分佣宝石数',
             slot: 'show3',
          },
          {
            label: '该被邀请人为该邀请人总贡献宝石数',
            field: 'income',
          },
          {
            label: '该被邀请人为总代总贡献宝石数',
            field: 'pincome',
          },
        ],
        tableData: [{}],
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 获取列表
      async getList() {
        const params = {
          ...this.query,
          ...this.page
        }
        const {
          status,
          result
        } = await this.$api.bzlInviteList(params)
        if (status.code === 0) {
          result.list.forEach((item)=>{
            item.showText = false
          })
          this.tableData = result.list
          this.page.totalCount = result.totalCount
          this.page.pageSize = result.pageSize
          this.page.pageCount = result.pageCount
        }
      },
      // 获取列表
      async submit(value, type) {
        const {
          status,
          result
        } = await this.$api.bzlStoneConfigUpdate({
          num: value,
          type
        })
        if (status.code === 0) {
          this.getList()
          this.$message.success('修改成功')
        }
      },
      nav_set(item) {
        //我需要把item.num传入输入框 才能修改
        this.$prompt('请输入你要修改的数值', '修改数值', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: item.num
        }).then(({
          value
        }) => {
          this.submit(value, item.type)
        }).catch(() => {

        });
      },
      async listCount(item) {
        const {
          status,
          result
        } = await this.$api.inviteListCount({
          uid:item.inviteUid
        })
        if (status.code === 0) {
            item.showText = true
            item.inviteActiveCount=result.inviteActiveCount
            item.inviteCount=result.inviteCount
            item.totalIncome=result.totalIncome
        }
      },
    }
  }
</script>

<style lang="scss" scoped></style>
