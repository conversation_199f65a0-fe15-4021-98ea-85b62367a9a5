<template>
  <d2-container class="page">
    <!-- :is-edit="!isDetail" -->
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #banner_theme>
        <div class="banner_flex">
          <div class="banner_ul">
            <div class="li" :class="{ 'active': index == bannerSun }" v-for="(item, index) in bannerList"
              @click="checkendBanner(item, index)">
              <img :src="item.src" alt="" srcset="">
            </div>
          </div>
          <div class="yulan">
            <div class="canvas" ref="canvas" :style="{ backgroundImage: `url(${bannerInfo.src})` }">
              <div class="font">
                <div class="title"
                  :style="{ marginTop: `${bannerInfo.titleMarginTop}px`, color: `${bannerInfo.titleColor}` }">
                  {{ formData.title }}</div>
                <div class="subtitle"
                  :style="{ marginTop: `${bannerInfo.subTitleMarginTop}px`, color: `${bannerInfo.subTitleColor}` }">
                  {{ formData.subTitle }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #rebuild-collection>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="collectionTableSchema" :table-data="collectionTableData">
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItem(1)" type="primary" size="mini">新增</el-button>
                <el-button @click="downloadTemplate('REBUILD_ACTIVITY_EXCEL')" type="primary" size="mini">下载模版
                </el-button>
              </template>
              <template #template-upload="scope">
                11
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, collectionTableData)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template>
      <template #dddNum>
        <div style="width:300px;display: flex;">
          <el-input placeholder="请输入内容" v-model="formData.dddNum">
          </el-input>
          <el-button type="primary" style="margin-left:10px" size="mini"
            @click="addCollectionItemSeries(formData.dddNum, 'itemSeries')">确认</el-button>
        </div>
      </template>
      <template #maxMergeNumEveryUser>
        <el-row justify="start">
          <el-col :span="8">
            <div class="grid-content bg-purple">
              <el-input style="width:300px" placeholder="请输入每人最多兑换几次"
                v-model="formData.maxMergeNumEveryUser"></el-input>
            </div>
          </el-col>
          <el-col :span="10">
            <div style="color:#F56C6C;">
              <!-- <div>选择tid导入，定时开始时间必须是当前时间后5分钟</div> -->
            </div>
          </el-col>
        </el-row>
      </template>
      <template #maxMergeNumEveryUserHide>
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.maxMergeNumEveryUserHide1"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.maxMergeNumEveryUserHide2"></el-input>
            </div>
          </el-col>
          <el-col :span="10">
            <div style="color:#F56C6C;margin-left:10px">
              <!-- <div>选择tid导入，定时开始时间必须是当前时间后5分钟</div> -->
            </div>
          </el-col>
        </el-row>
      </template>
      <template #rebuild-collection-series>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="collectionTableSchemaSeries"
              :table-data="collectionTableDataSeries" :showIndex="false">
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItemSeries(-1, 'itemSeries')" type="primary" size="mini">新增</el-button>
              </template>
              <template #SchemaSeries="scope">
                <div class="" v-for="(item, index) in scope.row.csList">
                  <el-autocomplete style="width:340px;margin-bottom:10px;" v-model="item.nameCtid"
                    :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
                  </el-autocomplete>
                  <div>
                    <el-checkbox style="margin-left:10px;"
                      v-model="item.isCancelTarget">是否在活动结束前5秒撤销相关材料的求购？</el-checkbox>
                    <el-checkbox style="margin-left:10px;" v-model="item.isActivityTag">活动期间是否展示“活动中”</el-checkbox>
                    <el-checkbox style="margin-left:10px;" v-model="item.isExitMarket">活动结束后是否移出楼层进入吉物仓</el-checkbox>
                  </div>
                </div>
                <el-button type="primary" style="margin-top:10px;" size="mini" v-if="!isDetail"
                  @click="addCollectionItemnameCtid(scope.row)">添加“或”的材料</el-button>
              </template>
              <template #template-upload="scope">
                <template v-if="!isDetail">
                  <file-uploader :value.sync="scope.row.smeltingCollectionExcelUrl"></file-uploader>
                </template>
                <el-button v-else @click="downloadExcel(scope.row)">下载 Excel</el-button>
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, collectionTableDataSeries)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template>
      <template #hasSpecialGoods_msg>
        <div style="color:#F56C6C">
          <div>存在小熔炉时，盲盒内作品只能按照TID方式导入，请知晓</div>
          <!-- <div>2、作品图片不同的慎用，入群会随机获取群主创作的NFT作为头像，如作品是唯一的可能会被用户看到头像相同的；</div> -->
          <!-- <div>3、退群顺序，优先把通过定时任务进群的小号踢出，然后踢群内已有的小号。踢至群内小号数量不足时任务中断；</div> -->
        </div>
      </template>
      <template #yunying_msg>
        <div style="color:#F56C6C">
          <div>运营注意：每人最多兑换几次非必填，如两项都填，将默认只读（明说）的次数。</div>
        </div>
      </template>
      <template #time_msg>
        <div style="color:#F56C6C">
          <div>选择tid导入，定时开始时间必须是当前时间后5分钟</div>
        </div>
      </template>
      <template #zige_Table="scope">
        <common-table :is-edit="!isDetail" :table-schema="zigeSchema" :showIndex="false" :table-data="zigeTable">
          <template #series="scopee">
            <div class="">
              <el-autocomplete style="width:340px;margin-bottom:10px;" v-model="scopee.row.nameCtid"
                :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
              </el-autocomplete>
            </div>
          </template>
          <template #action-header="scopee">
            <div class="">
              <el-button slot="reference" type="text" @click="add_zigeItem()" v-if="!isDetail">新增</el-button>
            </div>
          </template>
          <template v-if="!isDetail" #action="scopee">
            <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scopee.$index, zigeTable)">
              <el-button slot="reference" type="text">删除</el-button>
            </el-popconfirm>
          </template>
        </common-table>
      </template>

      <!-- 盲盒内作品按照tid -->
      <!-- <template #works>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="worksTableSchema" :table-data="worksTableData">
              <template v-if="!isDetail" #action-header>
                <file-uploader v-if="!isDetail" @success="importTemplate" :value.sync="templateUrl"
                  style="margin-bottom: 10px;" text="上传模版"></file-uploader>
                <el-button @click="downloadTemplate('REBUILD_BOX_EXCEL')" type="primary" size="mini">
                  下载模版</el-button>
              </template>
              <template #open="scope">
                <span v-show="scope.row.isOpen==0">否</span>
                <span v-show="scope.row.isOpen==1">是</span>
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, worksTableData)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template> -->
      <!-- 盲盒内作品按照ctid -->
      <template #goodsBlindBoxNum>
        <div style="width:300px;display: flex;">
          <el-input placeholder="请输入内容" v-model="formData.goodsBlindBoxNum">
          </el-input>
          <el-button type="primary" style="margin-left:10px" size="mini"
            @click="addCollectionItemSeries(formData.goodsBlindBoxNum, 'goodsBlindBoxSeries')">确认</el-button>
        </div>
      </template>


      <template #goodsBlindBoxSeries="scope">
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="goodsBlindBoxTableSchemaSeries"
              :table-data="goodsBlindBoxTableDataSeries">
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItemSeries(-1, 'goodsBlindBoxSeries')" type="primary" size="mini">新增
                </el-button>
              </template>
              <template #SchemaSeries="scope">
                <common-table :is-edit="!isDetail" :table-schema="serializableTableSchemaSeries" :showIndex="false"
                  :table-data="scope.row.csList">
                  <template #series="scopee">
                    <div class="">
                      <el-autocomplete style="width:340px;margin-bottom:10px;" v-model="scopee.row.nameCtid"
                        :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect2">
                      </el-autocomplete>
                    </div>
                    <div>
                      <el-checkbox style="margin-left:10px;"
                        v-model="scopee.row.isActivityTag">活动期间是否展示“活动中”</el-checkbox>
                    </div>
                  </template>
                  <template v-if="!isDetail" #action="scopee">
                    <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scopee.$index, scope.row.csList)">
                      <el-button slot="reference" type="text">删除</el-button>
                    </el-popconfirm>
                  </template>
                </common-table>
                <el-button type="primary" style="margin-top:10px;" size="mini" v-if="!isDetail"
                  @click="addSerializable(scope.row.csList)">添加“和”的藏品</el-button>
              </template>

              <template #template-upload="scope">
                <template v-if="!isDetail">
                  <file-uploader :value.sync="scope.row.smeltingCollectionExcelUrl"></file-uploader>
                </template>
                <el-button v-else @click="downloadExcel(scope.row)">下载 Excel</el-button>
              </template>

              <template v-if="!isDetail" #action="scope">
                <div style="display: flex;flex-direction: column;align-items: center;">
                  <el-button size="mini" type="primary" style="width: 55px;" @click="getRow(scope.row)">查询</el-button>
                  <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, goodsBlindBoxTableDataSeries)">
                    <el-button slot="reference" type="text">删除</el-button>
                  </el-popconfirm>
                </div>

              </template>

            </common-table>
          </el-col>
        </el-row>
        <div style="margin-left:50%;">
          <span> 合成材料共计：{{ aggregate }}份</span>
          <span> 合成出共计：{{ blindBoxesTotal }}份</span>
        </div>
      </template>



      <!-- v-if="formData.isNeedShip==1&&formData.hasSpecialGoods==1" -->
      <template #activityShip>
        <div class="flex_div">
          <el-button @click="downloadTemplate('ACTIVITY_SHIP_USER')" type="primary" size="mini">
            下载模版</el-button>
          <file-uploader v-if="!isDetail" :value.sync="formData.activityShip" style="width:200px;margin-left:10px"
            text="上传模版"></file-uploader>

        </div>
      </template>
      <template #activityShip2>
        <div class="flex_div">
          <el-button @click="downloadTemplate('ACTIVITY_SHIP_USER')" type="primary" size="mini">
            下载模版</el-button>
          <file-uploader v-if="!isDetail" :value.sync="formData.activityShipScanImportUrl"
            style="width:200px;margin-left:10px" text="上传模版"></file-uploader>

        </div>
      </template>

      <template #merge0>
        <div class="flex_div">
          <el-button @click="downloadTemplate('MERGE0_USER_IMPORT')" type="primary" size="mini">
            下载模版</el-button>
          <file-uploader v-if="!isDetail" :value.sync="formData.merge0ImportUrl" style="width:200px;margin-left:10px"
            text="上传模版"></file-uploader>
        </div>
      </template>
      <template #reminder>
        <div style="color:#F56C6C">
          <div>使用规则：</div>
          <div>1.若存在小熔炉, 则必须上传用户资格, 小熔炉资格次数必填, 盲盒内作品excel "是否小熔炉作品"一列必须有"是"</div>
          <div>2.有小熔炉资格次数的用户, 在合成作品时, 优先取 小熔炉作品作为奖品, 若小熔炉作品全部发放完了, 则取大熔炉内作品作为奖品</div>
          <div>3.若大小熔炉均需要资格时, 同一个用户的大熔炉资格次数必须>=小熔炉资格次数</div>
          <div>4.举例, A用户有大熔炉资格次数2次, 小熔炉资格次数1次,
            当A用户第1次合成时, 取小熔炉内作品作为奖品, A用户剩余大熔炉次数为1, 小熔炉次数为0,
            当A用户第2次合成时, 取大熔炉内作品作为奖品, A用户剩余大熔炉次数为0, 小熔炉次数为0,
            当A用户第3次合成时, 没有次数, 合成失败</div>
        </div>
      </template>
      <template #activityShip_msg>
        <div style="color:#F56C6C">
          <div>【开始活动之前10分钟】</div>
        </div>
      </template>
      <template #activityShipMultiply_msg>
        <div style="color:#F56C6C">
          <div>扫描出来的次数乘以这个输入的资格，就是本场活动能参与的总资格</div>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import html2canvas from 'html2canvas';
import {
  mapActions
} from 'vuex'
import {
  downloadBlob
} from '@/utils/helper'
import {
  uploadImgToOss
} from '@/api/ossCenter'
export default {
  name: 'ActivityRebuild',
  components: {
    CommonForm,
    CommonTable,
    FileUploader
  },
  data() {
    const validateMysteryBox = (rule, value, callback) => {
      if (this.tableData.length <= 0) {
        callback(new Error('请添加数据'))
      } else {
        this.tableData.forEach(item => {
          if (!item.prizePicture) {
            callback(new Error('请上传图片'))
          } else if (!item.prizeName) {
            callback(new Error('请输入名称'))
          }
        })
        callback()
      }
    }
    const validateCollection = (rule, value, callback) => {
      if (this.collectionTableData.length <= 0) {
        callback(new Error('请添加数据'))
      } else {
        this.collectionTableData.forEach(item => {
          if (!item.picCoverSmelting) {
            callback(new Error('请上传封面图片'))
          } else if (!item.quantityNeedSmelting) {
            callback(new Error('请输入每次熔炼需要数量'))
          } else if (!item.smeltingCollectionExcelUrl) {
            callback(new Error('请上传数据表单'))
          } else if (!item.nameCollection) {
            callback(new Error('请输入名称'))
          }
        })
        callback()
      }
    }
    const validateCollectionSeries = (rule, value, callback) => {
      if (this.collectionTableDataSeries.length <= 0) {
        callback(new Error('请添加数据'))
      } else {
        this.collectionTableDataSeries.forEach(item => {
          if (!item.goodsNum) {
            callback(new Error('请输入每次熔炼需要数量'))
          } else if (!item.csList[0]) {
            callback(new Error('请添加材料'))
          }
        })
        callback()
      }
    }

    // const validateWorks = (rule, value, callback) => {
    //   if (this.worksTableData.length <= 0) {
    //     callback(new Error('请导入模版数据'))
    //   } else {
    //     callback()
    //   }
    // }
    const validategoodsBlindBoxSeries = (rule, value, callback) => {
      if (this.goodsBlindBoxTableDataSeries.length <= 0) {
        callback(new Error('请导入模版数据'))
      } else {
        callback()
      }
    }
    return {
      templateUrl: '', // 盲盒内作品模板地址
      templateUrl2: '',
      isDetail: false, // 详情
      activityNo: null, // 活动编号
      tableSchema: [ // 表格架构
        {
          label: '奖品名称',
          field: 'prizeName',
          type: 'input'
        },
        {
          label: '奖品图片',
          field: 'prizePicture',
          type: 'img'
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],
      collectionTableSchema: [ // 收藏表格架构
        {
          label: '名称',
          field: 'nameCollection',
          type: 'input'
        },
        {
          label: '每次熔炼需要数量',
          field: 'quantityNeedSmelting',
          type: 'input'
        },
        {
          label: '作品总数/系列总数',
          field: 'smeltingGoodsTotal'
        },
        {
          label: '数据表单',
          field: 'smeltingCollectionExcelUrl',
          slot: 'template-upload',
          width: '120px'
        },
        {
          label: '封面图片',
          field: 'picCoverSmelting',
          type: 'img',
          width: '170px'
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],
      collectionTableSchemaSeries: [ // 收藏表格架构
        {
          label: '名称',
          slot: 'SchemaSeries',
          width: 300
        },
        {
          label: '每次熔炼需要数量',
          field: 'goodsNum',
          type: 'input',
          width: 100
        },
        // {
        //   label: '封面图片',
        //   field: 'picCoverSmelting',
        //   type: 'img',
        //   width: '170px'
        // },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],
      worksTableSchema: [ // 表格架构
        {
          label: 'token ID',
          field: 'tokenId'
        },
        {
          label: '作品名称',
          field: 'goodsName'
        },
        // {
        //   label: '奖品类型',
        //   field: 'prizeType'
        // },
        {
          label: '是否已开出',
          field: 'isOpen',
          slot: 'open',
        },
        {
          label: '作品状态',
          field: 'goodsStatus'
        },
        {
          label: '是否为小熔炉作品',
          field: 'isSpecialGoods',
          type: 'tag',
          tagMap: {
            1: {
              label: '是',
              tagType: 'error'
            },
            0: {
              label: '否',
              tagType: 'success'
            },
          },
          width: '80px',
        },
        {
          label: '操作',
          slot: 'action',
          width: '120px',
          headerSlot: 'action-header'
        }
      ],
      goodsBlindBoxTableSchemaSeries: [ // 表格架构
        {
          label: '系列',
          slot: 'SchemaSeries',
          width: 500
        },
        // {
        //   label: '每包装几个(最多150)',
        //   field: 'eachNum',
        //   type: 'input',
        //   width: 100
        // },
        {
          label: '最多合成次数',
          field: 'maxNum',
          type: 'input',
          width: 100
        },
        {
          label: '主力需要多少个',
          field: 'mergeNum4',
          type: 'input',
          width: 100
        },
        {
          label: '操作',
          slot: 'action',
          width: '100x',
          headerSlot: 'action-header'
        }
      ],
      tableData: [], // 盲盒可以开出内容的数据
      collectionTableData: [], // 收藏表格数据
      collectionTableDataSeries: [{
        csList: [{
          nameCtid: '',
          isCancelTarget: true,
          isActivityTag: true,
          isExitMarket: true
        }]
      }],
      goodsBlindBoxTableDataSeries: [{
        csList: [{
          nameCtid: "",
          ctid: "",
          name: "",
          eachNum: "",
          isActivityTag: true
        }],
        mergeNum4: '',
        maxNum: ''
      }],
      worksTableData: [], // 盲盒内作品数据
      formData: {
        backgroundImage: 'https://cdn-lingjing.nftcn.com.cn/image/20230528/c0662711054c5e25340f7768c804884c_400x1296.png',
        type: 'REBUILD',
        topMargin: '65',
        color: '#79e0ef',
        borderBg: 'https://cdn-lingjing.nftcn.com.cn/image/20230528/c4c80be79ec2ac872a48a69f976e93fc_400x491.png',
        ruleIcon: 'https://cdn-lingjing.nftcn.com.cn/image/20230215/aacf86330261fa78c555a2b934a3889d_131x35.png',
        butBg: 'https://cdn-lingjing.nftcn.com.cn/image/20230322/dc829f156c4dafb62d5f95e23fc915e9_400x114.png',
        isTiming: 1,
        startTime: ['2023-01-26 20:37:58', '2023-01-27 20:37:58'],
        smeltingCollectionType: 'CTID',
        goodsBlindBoxRequestType: 'CTID',
        goodsBlindBoxRequestJson: [{
          csList: []
        }],
        isNeedShip: 0,
        hasSpecialGoods: 0,
        isShow: 0,
        isShow2: 0,
        dddNum: 1,
        activityNewShow: 1,
        activityNewBannerImage: "",
        show_set: '0',
        blindBoxesTotal: "",
        goodsBlindBoxNum: 1,
        mergeNum4: "",
        mergeTime4: "",
        subTitle: "",
        activityNewBannerType: "1",
        maxMergeNumEveryUser: "",
        mergeTime0: "",
        isReplenish: 0,
        activityShipCtid: "",
        activityShipNum: "",
        activityShipTime: "",
        activityShipScanImportUrl: "",
        activityShipSale: 2,
        activityShipIsRepeat: 1,
        activityShipAndOr: 1,
        activityShipConfigJson: "",
        activityShipScanType: 1,
        activityShipScanType2: -1,//扫描资格方式 判断
        activityShipActivityNo: "",
        mustGapNum: '',
        mustIndex: '',
        activityShipMultiply: 1,

      },
      activityShipTableData: [], // 盲盒内作品数据
      activityShipTableSchema: [ // 表格架构
        {
          label: '用户contractAddress',
          field: 'contractAddress'
        },
        {
          label: '大熔炉资格次数',
          field: 'num'
        },
        {
          label: '小熔炉资格次数',
          field: 'specialNum'
        },
        {
          label: '操作',
          slot: 'action',
          width: '120px',
          headerSlot: 'action-header'
        }
      ],
      formSchema: [{
        type: 'select',
        label: '活动类型：',
        placeholder: '请选择活动类型',
        field: 'type',
        disabled: true,
        options: [{
          label: '拉新',
          value: 'INVITE_NEW'
        },
        {
          label: '熔炉',
          value: 'REBUILD'
        },
        {
          label: '合成',
          value: 'MERGE'
        },
        {
          label: '抽奖',
          value: 'GET_REWARD'
        }
        ],
        rules: [{
          required: true,
          message: '请选择活动类型',
          trigger: 'change'
        }]
      },
      {
        type: 'input',
        label: '活动名称：',
        placeholder: '请输入活动名称',
        field: 'title',
        maxlength: '17',
        rules: [{
          required: true,
          message: '请输入活动名称',
          trigger: 'blur'
        }]
      },
      {
        type: 'input',
        label: '副标题：',
        placeholder: '请输入副标题',
        field: 'subTitle',
        maxlength: '31',
        rules: [{
          required: true,
          message: '请输入副标题',
          trigger: 'blur'
        }]
      },
      {
        type: 'switch',
        label: '更多高级设置：',
        field: 'show_set',
      },
      {
        label: '活动banner：',
        field: 'activityNewBannerType',
        type: 'radio',
        options: [{
          label: '推荐',
          value: "1"
        }, {
          label: '自定义',
          value: "0"
        }]
      },
      {
        slot: 'banner_theme',
        show: {
          relationField: 'activityNewBannerType',
          value: "1"
        },
      },
      {
        type: 'img',
        placeholder: '请选择活动banner',
        field: 'activityNewBannerImage',
        rules: [{
          required: true,
          message: '请选择活动banner',
          trigger: 'change'
        }],
        show: {
          relationField: 'activityNewBannerType',
          value: "0"
        },
      },
      {
        type: 'img',
        label: '活动背景图片：',
        placeholder: '请选择活动背景图片',
        field: 'backgroundImage',
        rules: [{
          required: true,
          message: '请选择活动背景图片',
          trigger: 'change'
        }],
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      // {
      // 	type: 'input',
      // 	label: '活动链接：',
      // 	placeholder: '请输入跳转路径h5以及网页跳转',
      // 	field: 'link',
      // 	maxlength: 500,
      // 	rules: [{
      // 		required: true,
      // 		message: '请输入活动链接',
      // 		trigger: 'blur'
      // 	}]
      // },
      {
        type: 'img',
        label: '活动边框图片：',
        placeholder: '活动边框图片',
        field: 'borderBg',
        rules: [{
          required: true,
          message: '活动边框图片',
          trigger: 'change'
        }],
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      {
        type: 'input',
        label: '活动边框距离顶部距离：',
        placeholder: '请输入活动边框距离顶部距离(默认值为500)',
        field: 'topMargin',
        rules: [{
          required: true,
          message: '请输入活动边框距离顶部距离',
          trigger: 'change'
        }],
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      {
        type: 'img',
        label: '规则icon图标：',
        placeholder: '规则icon图标',
        field: 'ruleIcon',
        rules: [{
          required: true,
          message: '规则icon图标',
          trigger: 'change'
        }],
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      {
        type: 'img',
        label: '合成按钮图片：',
        placeholder: '合成按钮图片',
        field: 'butBg',
        rules: [{
          required: true,
          message: '合成按钮图片',
          trigger: 'change'
        }],
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      // {
      //   type: 'input',
      //   label: '合成材料款数：',
      //   placeholder: '请输入合成材料款数（例如，1）',
      //   field: 'styleNum',
      //   rules: [{
      //     required: true,
      //     message: '合成材料数量',
      //     trigger: 'blur'
      //   }]
      // },
      // {
      //   type: 'input',
      //   label: '最多兑换数量：',
      //   placeholder: '请输入最多兑换数量',
      //   field: 'exchangeQuantity',
      //   rules: [{
      //     required: true,
      //     message: '请输入最多兑换数量',
      //     trigger: 'blur'
      //   }]
      // },
      {
        type: 'input',
        label: '最多兑换数量:',
        placeholder: '请输入最多兑换数量',
        field: 'blindBoxesTotal',
        // rules: [{ required: true, message: '请输入最多兑换数量', trigger: 'blur' }]
      },
      {
        type: 'number-input',
        label: '每人最多兑换几次(明说):',
        placeholder: '请输入每人最多兑换几次',
        slot: 'maxMergeNumEveryUser',
        // rules: [{ required: true, message: '请输入最多兑换数量', trigger: 'blur' }]
      },
      {
        type: 'number-input',
        label: '每人最多兑换几次:(隐藏)',
        placeholder: '请输入每人最多兑换几次',
        slot: 'maxMergeNumEveryUserHide',
        // rules: [{ required: true, message: '请输入最多兑换数量', trigger: 'blur' }]
      },
      {
        slot: 'yunying_msg',
        label: '',
      },
      {
        type: 'textarea',
        label: '合成规则：',
        placeholder: '请输入合成规则',
        field: 'compositionRule',
        rules: [{
          required: true,
          message: '请输入合成规则',
          trigger: 'blur'
        }],
      },
      {
        type: 'img',
        label: '活动规则：',
        placeholder: '请选择活动规则',
        field: 'ruleImage',
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      {
        type: 'input',
        label: '活动规则字体颜色：',
        placeholder: '请填写活动规则字体颜色',
        field: 'color',
        show: {
          relationField: 'show_set',
          value: '1'
        },
      },
      {
        label: '可熔炼材料选择类型：',
        field: 'smeltingCollectionType',
        type: 'radio',
        options: [{
          label: '按系列导入',
          value: 'CTID'
        }]
      },
      {
        label: '兑换所需材料款数：',
        slot: 'dddNum',
      },
      {
        slot: 'rebuild-collection',
        field: 'rebuild',
        label: '可熔炼材料按作品导入：',
        rules: [{
          required: true,
          validator: validateCollection,
          trigger: 'blur'
        }],
        show: {
          relationField: 'smeltingCollectionType',
          value: ['TID']
        },
      },
      {
        slot: 'rebuild-collection-series',
        field: 'rebuildSerie',
        label: '可熔炼材料按系列导入：',
        rules: [{
          required: true,
          validator: validateCollectionSeries,
          trigger: 'blur'
        }],
        show: {
          relationField: 'smeltingCollectionType',
          value: ['CTID']
        },
      },
      {
        label: '是否存在小熔炉：',
        field: 'hasSpecialGoods',
        type: 'radio',
        rules: [{
          required: true,
          message: '请选择是否存在小熔炉',
          trigger: 'change'
        }],
        options: [{
          label: '是',
          value: 1
        }, {
          label: '否',
          value: 0
        }]
      },
      {
        slot: 'hasSpecialGoods_msg',
      },
      {
        label: '盲盒内作品选择类型：',
        field: 'goodsBlindBoxRequestType',
        type: 'radio',
        options: [{
          label: '按作品导入',
          value: 'TID'
        }, {
          label: '按系列导入',
          value: 'CTID'
        }]
      },
      // {
      //   slot: 'works',
      //   label: '盲盒内作品按作品：',
      //   field: 'collection',
      //   rules: [{
      //     required: true,
      //     validator: validateWorks,
      //     trigger: 'blur'
      //   }],
      //   show: {
      //     relationField: 'goodsBlindBoxRequestType',
      //     value: ['TID']
      //   },
      // },
      {
        label: '合成出的作品款数：',
        slot: 'goodsBlindBoxNum',
        show: {
          relationField: 'goodsBlindBoxRequestType',
          value: ['CTID']
        },
      },
      {
        slot: 'goodsBlindBoxSeries',
        label: '盲盒内作品按系列：',
        field: 'goodsBlindBoxRequestJson',
        rules: [{
          required: true,
          // validator: validategoodsBlindBoxSeries,
          trigger: 'blur'
        }],
        show: {
          relationField: 'goodsBlindBoxRequestType',
          value: ['CTID']
        },
      },

      {
        type: 'input',
        label: '每人最多投入几次必中：',
        placeholder: '请输入每人最多投入几次必中',
        field: 'mustGapNum',
        // rules: [{
        //   required: true,
        //   message: '请输入每人最多投入几次必中',
        //   trigger: 'blur'
        // }]
      },
      {
        type: 'input',
        label: '必中盲盒内作品按系列序号：',
        placeholder: '请输入必中盲盒内作品按系列序号',
        field: 'mustIndex',
        // rules: [{
        //   required: true,
        //   message: '请输入必中盲盒内作品按系列序号',
        //   trigger: 'blur'
        // }]
      },
      {
        label: '大熔炉是否需要资格：',
        field: 'isNeedShip',
        type: 'radio',
        rules: [{
          required: true,
          message: '请选择大熔炉是否需要资格',
          trigger: 'change'
        }],
        options: [{
          label: '需要',
          value: 1
        }, {
          label: '不需要',
          value: 0
        }]
      },
      // {
      //   label: '主力用户需要多少个：',
      //   field: 'mergeNum4',
      //   type: 'input',
      //   placeholder: '请输入主力用户需要多少个',
      //   // rules: [{
      //   //   required: true,
      //   //   message: '请输入主力用户需要多少个',
      //   //   trigger: 'blur'
      //   // }]
      //   show: {
      //     relationField: 'goodsBlindBoxRequestType',
      //     value: ['CTID']
      //   },
      // },
      {
        type: 'input',
        label: '主力用户合成耗时：',
        placeholder: '请输入主力用户合成耗时/秒',
        field: 'mergeTime4',
        show: {
          relationField: 'goodsBlindBoxRequestType',
          value: ['CTID']
        },
        // rules: [{
        //   required: true,
        //   message: '请输入主力用户合成耗时/秒',
        //   trigger: 'blur'
        // }]
      },
      {
        type: 'input',
        label: '外挂准许合成时间：',
        placeholder: '请输入外挂准许合成时间/秒',
        field: 'mergeTime0',
        show: {
          relationField: 'goodsBlindBoxRequestType',
          value: ['CTID']
        },
      },
      {
        slot: 'merge0',
        label: '外挂准许名单：',
        field: 'merge0',
        show: {
          relationField: 'goodsBlindBoxRequestType',
          value: ['CTID']
        },
      },
      {
        slot: 'activityShip',
        label: '资格人员列表：',
        field: 'activityShip',
        rules: [{
          required: true,
          trigger: 'blur',
          message: '请上传模板',
        }],
        show: {
          relationField: 'isShow',
          value: [1]
        },
      },
      {
        label: '资格扫描方式：',
        field: 'activityShipScanType',
        type: 'radio',
        options: [{
          label: '资格扫描',
          value: 1
        }, {
          label: '数据导入',
          value: 2
        }, {
          label: '共用其他活动资格',
          value: 3
        }],
        show: {
          relationField: 'isShow2',
          value: [1]
        },
      },
      {
        type: 'input',
        label: '活动编号:',
        placeholder: '请输入活动编号',
        field: 'activityShipActivityNo',
        show: {
          relationField: 'activityShipScanType',
          value: [3]
        },
      },
      {
        label: '资格扫描：',
        field: 'activityShipAndOr',
        type: 'radio',
        options: [{
          label: '和的关系，需同时满足。',
          value: 1
        }, {
          label: '或的关系，满足一行即可',
          value: 0
        }],
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        label: '资格是否叠加：',
        field: 'activityShipIsRepeat',
        type: 'radio',
        options: [{
          label: '叠加',
          value: 1
        }, {
          label: '不叠加',
          value: 0
        }],
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        label: '扫描藏品：',
        field: 'activityShipSale',
        type: 'radio',
        options: [{
          label: '全部',
          value: 2
        }, {
          label: '只扫描下架的',
          value: 0
        }],
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        type: 'datetime',
        label: '资格扫描时间：',
        field: 'activityShipTime',
        rules: [{
          required: true,
          trigger: 'blur',
          message: '请选择资格扫描时间',
        }],
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        slot: 'activityShip_msg',
        label: '',
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        label: '',
        slot: 'zige_Table',
        show: {
          relationField: 'activityShipScanType2',
          value: [1]
        },
      },
      {
        slot: 'activityShip2',
        label: '数据导入：',
        field: 'activityShip2',
        show: {
          relationField: 'activityShipScanType2',
          value: [2]
        },
      },
      {
        type: 'input',
        label: '有几次资格',
        placeholder: '请输入有几次资格',
        field: 'activityShipMultiply',
        show: {
          relationField: 'isNeedShip',
          value: [1]
        },
        // rules: [{
        //   required: true,
        //   message: '请输入每人最多投入几次必中',
        //   trigger: 'blur'
        // }]
      },
      {
        slot: 'activityShipMultiply_msg',
        label: '',
        show: {
          relationField: 'isNeedShip',
          value: [1]
        },
      },
      {
        label: '是否开启定时：',
        field: 'isTiming',
        type: 'radio',
        options: [{
          label: '开启',
          value: 1
        }, {
          label: '关闭',
          value: 0
        }]
      },
      {
        type: 'datetimerange',
        label: '活动时间：',
        field: 'startTime',
        // rules: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        show: {
          relationField: 'isTiming',
          value: [1]
        },
      },
      {
        label: '未合成完毕时主力是否补充：',
        field: 'isReplenish',
        type: 'radio',
        options: [{
          label: '补充',
          value: 1
        }, {
          label: '不补充',
          value: 0
        }],
        show: {
          relationField: 'isTiming',
          value: [1]
        },
      },
      {
        type: 'datetime',
        label: '补充时间：',
        field: 'supplementTime',
        rules: [{
          required: true,
          message: '请选择补充时间',
          trigger: 'change'
        }],
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        show: {
          relationField: 'isReplenish',
          value: [1]
        },
      },

      {
        slot: 'time_msg',
        label: '',
      },
      {
        label: '',
        field: 'isShow',
        slot: 'ddd'
      },
      {
        label: '是否立即同步到金刚区“活动”：',
        field: 'activityNewShow',
        type: 'radio',
        options: [{
          label: '是',
          value: 1
        }, {
          label: '否',
          value: 0
        }]
      },
      {
        label: '',
        slot: 'reminder'
      },
      {
        type: 'action'
        // exclude: ['reset', 'submit', 'back']
      }
      ],
      searchList: [],
      csName: '',
      restaurants: [],
      timeout: null,
      results: [],
      aggregate: 0,
      blindBoxesTotal: 0,
      bannerList: [{
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/1.png",
        titleMarginTop: 24,
        titleColor: "#D0FFF7",
        subTitleMarginTop: 30,
        subTitleColor: "#83F0FB"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/2.png",
        titleMarginTop: 24,
        titleColor: "#F7DEFC",
        subTitleMarginTop: 30,
        subTitleColor: "#EE78F6"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/3.png",
        titleMarginTop: 24,
        titleColor: "#D2FEF9",
        subTitleMarginTop: 30,
        subTitleColor: "#16F7D7"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/4.png",
        titleMarginTop: 10,
        titleColor: "#FFFFFF",
        subTitleMarginTop: 60,
        subTitleColor: "#FFFFFF"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/5.png",
        titleMarginTop: 24,
        titleColor: "#F3CD8E",
        subTitleMarginTop: 30,
        subTitleColor: "#DDD4A2"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/6.png",
        titleMarginTop: 10,
        titleColor: "#CCF3FD",
        subTitleMarginTop: 30,
        subTitleColor: "#A1C7E5"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/7.png",
        titleMarginTop: 24,
        titleColor: "#FBCE84",
        subTitleMarginTop: 26,
        subTitleColor: "#1F1409"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/8.png",
        titleMarginTop: 16,
        titleColor: "#000",
        subTitleMarginTop: 28,
        subTitleColor: "#001F07"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/9.png",
        titleMarginTop: 16,
        titleColor: "#fff",
        subTitleMarginTop: 28,
        subTitleColor: "#88E7F3"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/10.png",
        titleMarginTop: 16,
        titleColor: "#85FEFF",
        subTitleMarginTop: 28,
        subTitleColor: "#fff"
      }, {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/11.png",
        titleMarginTop: 24,
        titleColor: "#85FEFF",
        subTitleMarginTop: 20,
        subTitleColor: "#fff"
      }],
      bannerSun: 0,
      bannerInfo: {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/1.png",
        titleMarginTop: 16,
        titleColor: "#85FEFF",
        subTitleMarginTop: 28,
        subTitleColor: "#fff"
      },
      bannerUrl: "",
      serializableTableSchemaSeries: [ // 表格架构
        {
          label: '系列',
          slot: 'series',
          width: 300
        },
        {
          label: '每次合成出',
          field: 'eachNum',
          type: 'input',
          width: 100
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header',
          width: 100
        }
      ],
      zigeSchema: [ // 表格架构
        {
          label: '系列',
          slot: 'series',
          width: 400
        },
        {
          label: '持有几个',
          field: 'num',
          type: 'input',
          width: 100
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],
      zigeTable: [],
      showTitle: ""
    }
  },
  mounted() {
    const {
      activityType,
      activityNo
    } = this.$route.query
    this.isDetail = activityType === 'detail'
    this.activityNo = activityNo
    activityNo && this.getDetail()
    let type = this.$route.query.type
    if (type == 'resolve') {
      this.showTitle = '分解'
    } else if (type == 'lottery') {
      this.showTitle = '抽奖'
    } else {
      this.showTitle = '合成'
    }
    // this.goodsBlindBoxTableDataSeries = [{
    //   "csList": [{
    //     "nameCtid": "3344(cs09257022155570272068167755541377)"
    //   }],
    //   "eachNum": "2",
    //   "maxNum": "2",
    //   "name": "3344",
    //   "ctid": "cs09257022155570272068167755541377"
    // }]
  },
  watch: {
    // 如果只需要监听对象中的一个属性值，则可以做以下优化：使用字符串的形式监听对象属性：
    formData: {
      // 执行方法
      handler(newValue) {
        console.log("小熔炉：" + newValue.hasSpecialGoods, "大熔炉：" + newValue.isNeedShip)
        if (newValue.hasSpecialGoods == 1 || newValue.isNeedShip == 1) {
          if (newValue.hasSpecialGoods == 0 && newValue.isNeedShip == 1) {
            this.formData.isShow2 = 1
            this.formData.isShow = 0
            this.formData.activityShipScanType2 = this.formData.activityShipScanType
          } else {
            this.formData.isShow2 = 0
            this.formData.isShow = 1
            this.formData.activityShipScanType2 = -1
          }
          if (newValue.hasSpecialGoods == 1) {
            this.formData.goodsBlindBoxRequestType = 'TID'
            this.formSchema[24].options = [{
              label: '按tid导入',
              value: 'TID'
            }]
          }
        } else {
          this.formData.isShow = 0
          this.formData.isShow2 = 0
          this.formData.activityShipScanType2 = -1
          if (newValue.hasSpecialGoods == 0) {
            this.formSchema[24].options = [{
              label: '按tid导入',
              value: 'TID'
            }, {
              label: '按系列导入',
              value: 'CTID'
            }]
          }
        }
      },
      deep: true, // 深度监听
    },
    "formData.title": {
      handler(newValue) {
        if (newValue.length >= 17) {
          this.$message.error('活动名称长度不能超过17个字符')
        }
      }
    },
    "formData.subTitle": {
      handler(newValue) {
        if (newValue.length >= 31) {
          this.$message.error('副标题长度不能超过31个字符')
        }
      }
    },
    goodsBlindBoxTableDataSeries: {
      // 执行方法
      handler(newValue) {
        // console.log(newValue)
        // if(!this.isDetail){
        this.aggregate = 0
        this.blindBoxesTotal = 0
        newValue.forEach((item) => {
          item.csList.forEach((itemm) => {
            this.aggregate += parseInt(itemm.eachNum ? itemm.eachNum : 1) * parseInt(item.maxNum ? item
              .maxNum : 1)
          })
          this.blindBoxesTotal += parseInt(item.maxNum ? item.maxNum : 1)
        })
        // }
        // setTimeout(()=>{
        //   this.aggregate=0
        //   newValue.forEach((item)=>{
        //      this.aggregate+=item.total
        //   })
        // },300)
      },
      deep: true, // 深度监听
    },
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    getCtid(str) {
      const match = str.match(/\(([^)]+)\)/);
      return match ? match[1] : null;
    },
    generateCtidList(arr) {
      const ctidList = arr.csList.map(item => {
        const ctid = this.getCtid(item.nameCtid);
        return {
          ctid: ctid || "",
          num: item.eachNum
        };
      });
      return ctidList;
    },
    // 获取最多合成次数和主力需要多少个
    async getRow(item) {
      const ctidListStr = this.generateCtidList(item);
      let res = await this.$api.getMaxTimes({ ctidListStr: JSON.stringify(ctidListStr) })
      console.log(this.goodsBlindBoxTableDataSeries, item, 123123111);
      if (res.status.code == 0) {
        if (res.result.maxNum4 != 0) {
          item.mergeNum4 = res.result.maxNum4
        }
        item.maxNum = res.result.maxNum

      }
      this.$forceUpdate()
      console.log(item, '当前行');
    },
    // 导入模版
    async importTemplate(data) {
      const {
        status,
        result
      } = await this.$api.rebuildImportTemplate({
        excelUrl: data.result?.url
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.worksTableData = result
      }
    },
    //导入资格模板
    routerBack() {
      const {
        fullPath
      } = this.$route
      this.close({
        tagName: fullPath
      })
      this.$router.back()
    },
    async getDetail() {
      const {
        status,
        result
      } = await this.$api.activityDetail({
        activityNo: this.activityNo
      })
      if (status.code === 0) {
        result.startTime = [result.startTime, result.endTime]
        const {
          smeltingCollection,
          blindBoxPrize,
          goodsBlindBox,
          purchaseSpecNft,
          collectiblesAvailable,
          activityShipVOS,
          inItemJson,
          ...rest
        } = result
        console.log(rest)
        this.formData = {
          ...rest,
          ...JSON.parse(result.extraJson)
        }
        this.tableData = blindBoxPrize
        if (this.formData.smeltingCollectionType == 'CTID') {
          this.collectionTableDataSeries = inItemJson
        } else {
          this.collectionTableData = smeltingCollection.map(item => ({
            ...item,
            smeltingCollectionExcelUrl: item.goodsSelection
          }))
        }
        this.worksTableData = goodsBlindBox
        this.activityShipTableData = activityShipVOS
        console.error(this.formData.goodsBlindBoxRequestJson)
        this.goodsBlindBoxTableDataSeries = this.formData.goodsBlindBoxRequestJson
        this.blindBoxesTotal = this.formData.blindBoxesTotal
      }
      if (this.formData.activityShipConfigJson) {
        this.zigeTable = JSON.parse(this.formData.activityShipConfigJson)
      }
      if (this.activityNo) {
        this.formData.activityNewBannerType = "0"
      }
      console.table(this.formData)
    },
    // 添加可熔炼材料
    addCollectionItem() {
      this.collectionTableData.push({
        picCoverSmelting: '',
        smeltingCollectionExcelUrl: ''
      })
    },
    // 添加可熔炼材料
    addCollectionItemSeries(value, type) {
      if (value == 1) {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries = []
          this.collectionTableDataSeries.push({
            csList: [{
              nameCtid: "",
              ctid: "",
              name: "",
              eachNum: "",
            }],
          })
        } else {
          this.goodsBlindBoxTableDataSeries = []
          this.goodsBlindBoxTableDataSeries.push({
            csList: [{
              nameCtid: "",
              ctid: "",
              name: "",
              eachNum: "",
              isActivityTag: true
            }],
          })
        }
      } else if (value > 1) {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries = []
          var i = 0
          for (i; i < value; i++) {
            this.collectionTableDataSeries.push({
              csList: [{
                nameCtid: "",
                ctid: "",
                name: "",
                eachNum: ""
              }],
            })
          }
        } else {
          this.goodsBlindBoxTableDataSeries = []
          var i = 0
          for (i; i < value; i++) {
            this.goodsBlindBoxTableDataSeries.push({
              csList: [{
                nameCtid: "",
                ctid: "",
                name: "",
                eachNum: "",
                isActivityTag: true
              }],
            })
          }
        }
      } else {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries.push({
            csList: [{
              nameCtid: "",
              ctid: "",
              name: "",
              eachNum: "",
              isCancelTarget: true,
              isActivityTag: true,
              isExitMarket: true
            }],
          })
        } else {
          console.log(111)
          this.goodsBlindBoxTableDataSeries.push({
            csList: [{
              nameCtid: "",
              ctid: "",
              name: "",
              eachNum: "",
              isActivityTag: true
            }],
          })
        }
      }
    },
    addCollectionItemnameCtid(item) {
      item.csList.push({
        nameCtid: '',
        isCancelTarget: true,
        isActivityTag: true,
        isExitMarket: true
      })
      // this.collectionTableDataSeries.forEach((item)=>{
      //     item.csList.push({nameCtid: ''})
      // })
    },
    // 添加盲盒结果
    addResultItem() {
      this.tableData.push({
        prizePicture: ''
      })
    },
    deleteItem(index, data) {
      data.splice(index, 1)
    },
    async downloadTemplate(templateTag) {
      const {
        status,
        result
      } = await this.$api.downLoadTemplateExcel({
        templateTag
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    async downloadExcel(item) {

      try {
        const res = await this.$api.activityDownloadExcel({
          materialsUnqNo: item.materialsUnqNo,
          activityNo: this.formData.activityNo
        })
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '下载')
          this.$message.success('下载成功')
        }
      } finally {

      }
      // if (res) {
      // 	downloadBlob(res, '作品')
      // 	this.$message.success('下载成功')
      // }
    },
    async submit() {
      if (this.formData.activityNewBannerType == 1) {
        this.addBanner()
      } else {
        this.configSubmit()
      }
    },
    async search() {
      let res = await this.$api.searchPgc({
        name: this.csName
      });
      if (res.status.code == 0) {
        this.searchList = res.result.list
      }
    },
    clickName(item) {
      let repetition = false
      if (this.formData.goodsBlindBoxRequestJson != "") {
        this.formData.goodsBlindBoxRequestJson.forEach((itemm) => {
          if (item.name == itemm.name) {
            repetition = true
          }
        })
        if (!repetition) {
          this.formData.goodsBlindBoxRequestJson.push(item)
        } else {
          this.$message.error('该系列已经被选择了哦')
        }
      } else {
        this.formData.goodsBlindBoxRequestJson.push(item)
      }
    },
    delName(item, index) {
      if (!this.isDetail) {
        this.formData.goodsBlindBoxRequestJson.splice(index, 1)
      }
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item);
    },
    handleSelect2(item) {
      console.log(item, this.goodsBlindBoxTableDataSeries, 123123);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
                'text': `${item.ctid}`
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    checkendBanner(item, index) {
      this.bannerSun = index
      this.bannerInfo = item
    },
    addBanner() {
      const loading = this.$loading({
        lock: true,
        text: '图片生成中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const dom = this.$refs.canvas // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, //支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2, // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {
        this.updateImg(canvas.toDataURL('image/jpeg', 1))
      }).catch(err => {

      })
    },
    async updateImg(url) {
      const formData = new FormData()
      let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.bannerUrl = result.smallImageUrl
      this.formData.activityNewBannerImage = this.bannerUrl
      const loading = this.$loading({});
      loading.close();
      this.configSubmit()
    },
    //最终提交
    configSubmit() {
      if (this.formData.blindBoxesTotal) {

      } else {
        if (this.formData.goodsBlindBoxRequestType == 'CTID') {
          this.formData.blindBoxesTotal = this.blindBoxesTotal
        }
      }
      if (this.formData.goodsBlindBoxRequestType == 'CTID') {
        if (this.formData.blindBoxesTotal == this.blindBoxesTotal) {
          this.submitAll()
        } else {
          this.$message.error('最多兑换数与合成总数不一致，请确认后重试')
        }
      } else {
        this.submitAll()
      }
    },
    async submitAll() {
      this.collectionTableDataSeries.forEach((item) => {
        item.csList.forEach((itemm) => {
          if (itemm.nameCtid != "") {
            itemm.name = itemm.nameCtid.split('(')[0]
            itemm.ctid = itemm.nameCtid.split('(')[1].split(')')[0]
          }
        })
      })
      this.goodsBlindBoxTableDataSeries.forEach((item) => {
        item.csList.forEach((itemm) => {
          if (itemm.nameCtid != "") {
            itemm.name = itemm.nameCtid.split('(')[0]
            itemm.ctid = itemm.nameCtid.split('(')[1].split(')')[0]
          }
        })
      })
      this.zigeTable.forEach((item) => {
        if (item.nameCtid != "") {
          item.ctid = item.nameCtid.split('(')[1].split(')')[0]
        }
      })
      console.log(this.goodsBlindBoxTableDataSeries, '表格123')
      console.error(this.collectionTableDataSeries)
      console.log("==================", this.zigeTable)
      let startTime, endTime, activityShipCtid = ""
      if (this.formData.startTime[0]) {
        startTime = this.formData.startTime[0]
      }
      if (this.formData.startTime[1]) {
        endTime = this.formData.startTime[1]
      }
      if (this.formData.activityShipCtid[0]) {
        activityShipCtid = this.formData.activityShipCtid.split('(')[1].split(')')[0]
      }
      this.formData.activityShipConfigJson = JSON.stringify(this.zigeTable)
      this.formData.goodsBlindBoxRequestJson = this.goodsBlindBoxTableDataSeries
      this.formData.showTitle = this.showTitle
      const data = {
        activityNo: this.activityNo,
        ...this.formData,
        blindBoxPrizeJson: JSON.stringify(this.tableData),
        goodsBlindBoxRequestJson: this.formData.goodsBlindBoxRequestType == 'TID' ? JSON.stringify(this
          .worksTableData) : JSON.stringify(this.formData.goodsBlindBoxRequestJson),
        inItemJson: this.formData.smeltingCollectionType == 'TID' ? JSON.stringify(this
          .collectionTableData) : JSON.stringify(this.collectionTableDataSeries),
        extraJson: JSON.stringify(this.formData),
        activityShipImportUrl: this.formData.activityShip,
        activityShipCtid,
        startTime,
        endTime,
        activityShipConfigJson: JSON.stringify(this.zigeTable),
        mustGapNum: this.formData.mustGapNum,
        mustIndex: this.formData.mustIndex
      }
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (this.activityNo) {
          const {
            status
          } = await this.$api.activityEdit(data)
          if (status.code === 0) {
            this.routerBack()
          }
        } else {
          const {
            status
          } = await this.$api.activityAdd(data)
          if (status.code === 0) {
            this.routerBack()
          }
        }
      })
    },

    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    addSerializable(itemList) {
      itemList.push({
        ctid: "",
        name: "",
        eachNum: "",
        nameCtid: "",
        isActivityTag: true,
        isExitMarket: true
      })
    },
    seleteData() {
      console.table(this.goodsBlindBoxTableDataSeries)
    },
    add_zigeItem() {
      this.zigeTable.push({
        ctid: "",
        num: "",
        nameCtid: ""
      })
    }
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'fonts1_rebuild';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/admin/fonts/1_title.ttf');
}

@font-face {
  font-family: 'sub_fonts_rebuild';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/admin/fonts/1_subtitle.ttf');
}

.page {
  padding-top: 80px;
}

.banner_ul {
  width: 600px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .li {
    margin-right: 20px;
    width: 128px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;

    &.active {
      img {
        border: 4px solid #409EFF;
      }
    }

    img {
      width: 120px;
      height: auto;
      border-radius: 8px;
    }
  }
}

.banner_flex {
  display: flex;
  justify-content: flex-start;
  width: 1300px;
}

.yulan {
  width: 600px;

  .canvas {
    width: 100%;
    height: 270px;
    background-size: 100%;
    background-repeat: no-repeat;

    // display:flex;
    // justify-content: center;
    // align-items: center;
    .font {
      width: 85%;
      color: #fff;
      text-align: center;
      margin: 0 auto 0px auto;
      padding-top: 90px;

      .title {
        font-size: 28px;
        font-family: 'fonts1_rebuild';
      }

      .subtitle {
        font-size: 20px;
        font-family: 'sub_fonts_rebuild';
      }
    }
  }
}

.flex_div {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 700px;
  height: 50px;
}

.uploader {
  margin-top: 0 !important;
}

.danwei {
  margin-left: 6px;
}
</style>
