<template>
    <d2-container class="page" v-loading="loading">

        <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">

            <el-tab-pane label="固定提限价类" name="first">
                <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>

                <common-table :table-schema="NotableSchema" :showIndex="false" :table-data="NotableData">
                    <!-- 系列限价 -->
                    <template #seriesLimitPrice="scope">
                        {{ scope.row.onSaleMinPrice + ' - ' + scope.row.onSaleMaxPrice }}
                    </template>

                    <!-- 手续费 -->
                    <template #systemFeeRatio="scope">
                        {{ scope.row.systemFeeType == 1 ? '利润' : '卖出价' }} x {{ scope.row.systemFeeRatio }}
                    </template>
                    <!-- 可交易时间 -->
                    <template #tradingTime="scope">
                        <!-- {{ scope.row.tradeDay == 1 ? '每日' : '周一至周五' }} <br> -->
                        {{ '周' + scope.row.minTradeDay + ' - ' + '周' + scope.row.maxTradeDay }}<br>
                        {{ scope.row.minTradeTime + ' - ' + scope.row.maxTradeTime }}
                    </template>
                    <!-- 可挂售时间 onSellTime-->
                    <template #onSellTime="scope">
                        {{ '周' + scope.row.minOnSaleDay + ' - ' + '周' + scope.row.maxOnSaleDay }}<br>
                        {{ scope.row.minOnSaleTime + ' - ' + scope.row.maxOnSaleTime }}
                    </template>
                    <!-- forceSellFeeRatio -->
                    <template #forceSellFeeRatio="scope">
                        {{ scope.row.forceSellFeeRatio }}
                    </template>

                    <!-- 流通数/一级剩余 circulationOrProfit -->
                    <template #circulationOrProfit="scope">
                        {{ scope.row.firstMarketNum }}
                        <el-button type="text" size="small" @click="open_destroy(scope.row)">销毁一级剩余</el-button>
                    </template>

                    <!-- 楼层权重 -->
                    <template #location="scope">
                        <el-button type="text" size="small" @click="look(scope.row)">查看</el-button>
                    </template>


                    <!-- 主力/小户份数 mainOrSmallShares -->
                    <template #mainOrSmallShares="scope">
                        <!-- item.u4GoodsCount = res.result.u4GoodsCount // 大户持有
                        item.holdNumMin = Math.round(u0GoodsCount * 0.7) // 小户持有 -->
                        <span v-if="scope.row.u4GoodsCount != null && scope.row.holdNumMin != null">{{
                            scope.row.u4GoodsCount + ' / ' + scope.row.holdNumMin }}</span>
                        <el-button type="text" size="small" v-else @click="open_data(scope.row)">查看</el-button>
                    </template>

                    <!-- 小户人数 -->
                    <template #smallHolders="scope">
                        <span v-if="scope.row.userNumMin != null">{{ scope.row.userNumMin }}</span>
                        <el-button type="text" size="small" v-else @click="open_data(scope.row)">查看</el-button>
                    </template>

                    <template #action="scope">
                        <el-button type="text" size="small" @click="NohandleEdit(scope.row)">修改</el-button>
                    </template>
                </common-table>
                <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
                    <common-pagination ref="commonPagination" :page.sync="page" @change="getFist">
                    </common-pagination>
                </div>

                <el-dialog title="固定提限价类修改" :visible.sync="dialogVisible1">
                    <common-form :submit="submit1" :data="formData" :schema="formSchema" label-width="300px">
                        <!-- tradeDay 交易天 -->
                        <template #tradeDay="scope">
                            每周<el-input style="width: 80px;" v-model="formData.minTradeDay"></el-input> - 每周<el-input
                                style="width: 80px;" v-model="formData.maxTradeDay"></el-input>
                        </template>
                        <!-- error_msg -->
                        <template #error_msg="scope">
                            <span style="color: red;">请输入阿拉伯数字1-7，前面数字不能大于后面数字</span>
                        </template>
                        <!-- tradeTime -->
                        <template #tradeTime="scope">
                            <el-input style="width: 100px;" v-model="formData.minTradeTime"></el-input> - <el-input
                                style="width: 100px;" v-model="formData.maxTradeTime"></el-input>
                        </template>

                        <!-- OnSaleDay -->
                        <template #OnSaleDay="scope">
                            每周<el-input style="width: 100px;" v-model="formData.minOnSaleDay"></el-input> - 每周<el-input
                                style="width: 100px;" v-model="formData.maxOnSaleDay"></el-input>
                        </template>

                        <!-- OnSaleTime -->
                        <template #OnSaleTime="scope">
                            <el-input style="width: 100px;" v-model="formData.minOnSaleTime"></el-input> - <el-input
                                style="width: 100px;" v-model="formData.maxOnSaleTime"></el-input>
                        </template>
                    </common-form>
                </el-dialog>
            </el-tab-pane>


            <el-tab-pane label="实物类" name="thrid">
                <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
                <el-button type="primary" size="small" @click="add()">新增</el-button>

                <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">

                    <!-- onSaleTime -->
                    <template #onSaleTime="scope">
                        {{ formattedTime(scope.row.onSaleTime) }}
                    </template>

                    <template #sellOutTime="scope">
                        {{ formattedTime2(scope.row.sellOutTime) }}
                    </template>
                    <!-- coverList -->
                    <template #image="scope">
                        <!-- {{ typeof(scope.row.cover) == 'object'  ? }} -->
                        <el-image :src="scope.row.coverList[0]" v-if="typeof (scope.row.coverList) == 'object'"
                            style="width: 50px; height: 50px;"></el-image>
                        <el-image :src="scope.row.coverList" v-else style="width: 50px; height: 50px;"></el-image>
                    </template>
                    <!-- posterDetail -->
                    <template #posterDetail="scope">
                        <el-image :src="scope.row.detailList[0]" v-if="typeof (scope.row.detailList) == 'object'"
                            style="width: 50px; height: 50px;"></el-image>
                        <el-image :src="scope.row.detailList" v-else style="width: 50px; height: 50px;"></el-image>
                    </template>
                    <template #action="scope">
                        <el-button type="text" size="small" @click="handleEdit(scope.row)">修改</el-button>
                    </template>
                </common-table>

                <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
                    <common-pagination ref="commonPagination" :page.sync="page3" @change="getthrid">
                    </common-pagination>
                </div>

                <el-dialog :title="dialogTitle3" :visible.sync="dialogVisible3">
                    <common-form :submit="submit3" :data="formData3" :schema="formSchema3" label-width="300px">

                        <!-- 每日上架时间： -->
                        <template #onSaleTime="scope">
                            <el-time-picker v-model="formData3.onSaleTime" :disabled-hours="disabledHours"
                                :disabled-minutes="disabledMinutes" placeholder="请选择时间" format="HH:mm" />
                        </template>

                        <!-- 每日售罄时间、 -->
                        <template #sellOutTime="scope">
                            <el-time-picker v-model="formData3.sellOutTime" :disabled-hours="disabledHours"
                                :disabled-minutes="disabledMinutes" placeholder="请选择时间" format="HH:mm" />
                        </template>

                        <template #cover="scope">
                            <el-upload class="upload-demo" :action="action" :headers="token" :on-success="handleSuccess"
                                :on-remove="handleRemove" :file-list="fileList" :show-file-list="true"
                                list-type="picture-card" :multiple="true" :limit="5" :before-upload="beforeUpload">
                                <!-- <img v-for="(item) in fileList" v-if="formData3.coverList" :src="item" class="avatar"> -->
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </template>

                        <template #detail="scope">
                            <el-upload class="upload-demo" :action="action" :headers="token" :on-remove="handleRemove2"
                                :on-success="handleSuccess2" :file-list="fileList2" :show-file-list="true"
                                list-type="picture-card" :multiple="true" :limit="5" :before-upload="beforeUpload2">
                                <!-- <img v-for="(item) in fileList2" v-if="formData3.detailList" :src="item" class="avatar"> -->
                                <i class="el-icon-plus"></i>
                            </el-upload>
                        </template>

                    </common-form>
                </el-dialog>
            </el-tab-pane>
        </el-tabs>

    </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import CommonPagination from '@/components/CommonPagination'
import {
    mapActions
} from 'vuex'
import {
    downloadBlob
} from '@/utils/helper'

export default {
    name: 'casting',
    components: {
        CommonForm,
        CommonTable,
        CommonPagination,
        FileUploader,
        CommonQuery

    },
    data() {
        return {
            action:
                process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/upload',
            token: { AdminAuthorization: localStorage.getItem('usertoken') },
            fileList: [], // 存储上传的文件列表
            fileList2: [], // 存储上传的文件列表

            dialogTitle3: "",

            formData: {
              startTime:""
            },
            formData2: {
                destory: 0
            },
            formData3: {},

            dialogVisible1: false,
            dialogVisible2: false,
            dialogVisible3: false,
            page: {
                pageNum: 1,
                pageSize: 10
            },
            page2: {
                pageNum: 1,
                pageSize: 10
            },
            page3: {
                pageNum: 1,
                pageSize: 10
            },
            searchForm: {
                title: null,
                ctid: null,
            },
            query: {
                title: null,
                ctid: null,
            },
            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '系列名称：',
                    placeholder: '请输入系列名称',
                    field: 'title'
                },
                {
                    type: 'input',
                    label: '系列ID：',
                    placeholder: '请输入系列ID',
                    field: 'ctid'
                },],
            activeName: 'first',
            NotableData: [],
            tableData: [],
            MidtableData: [],
            NotableSchema: [
                {
                    label: '系列名',
                    field: 'title',
                },
                {
                    label: '系列ID',
                    field: 'ctid',
                },
                {
                    label: '图片',
                    field: 'cover',
                    type: 'img', // 使用插槽以便显示图片
                },
                {
                    label: '总份数',
                    field: 'goodsCount',
                },
                {
                    label: '每日限价提价百分比',
                    field: 'onSaleMaxPriceRatio',
                },
                {
                    label: '系列限价',
                    slot: 'seriesLimitPrice',
                },
                {
                    label: '可挂售时间',
                    slot: 'onSellTime',
                },
                {
                    label: '可交易时间',
                    slot: 'tradingTime',
                },
                {
                    label: '正常交易手续费',
                    slot: 'systemFeeRatio',
                },
                {
                    label: '强制换手期限（天）',
                    field: 'forceSellDay',
                },
                {
                    label: '逾期未换手额外手续费/天',
                    slot: 'forceSellFeeRatio',
                },
                {
                    label: '主力/小户份数',
                    slot: 'mainOrSmallShares',
                },
                {
                    label: '小户人数',
                    slot: 'smallHolders',
                },
                {
                    label: '流通数/一级剩余',
                    slot: 'circulationOrProfit',
                },
                {
                    label: '铸造时间',
                    field: 'createdAt',
                },
                {
                    label: '所在楼层及权重',
                    slot: 'location',
                },
                {
                    label: '修改',
                    slot: 'action', // 用于放置操作按钮
                }
            ],
            tableSchema: [
                {
                    label:'系列ID',
                    field: 'ctid',

                },
                {
                    label: '图片',
                    field: 'image',
                    slot: 'image', // 使用插槽以便自定义显示图片
                },
                {
                    label: '名称',
                    field: 'title',
                },
                {
                    label: '单价',
                    field: 'price'
                },
                {
                    label: '海报详情图',
                    field: 'posterDetail',
                    slot: 'posterDetail', // 可用于显示自定义上传按钮
                },
                {
                    label: '每日定时上库存时间',
                    slot: 'onSaleTime',
                },
                {
                    label: '每日上库存数量',
                    field: 'onSaleNum',
                },
                {
                    label: '当前剩余库存',
                    field: 'remainNum',

                },
                {
                    label: '每日定时显示“售罄”时间',
                    slot: 'sellOutTime',
                },
                // {
                //     label: '所在楼层及板块',
                //     field: 'locationAndSection',
                // },
                // {
                //     label: '楼层是否显示',
                //     field: 'floorDisplay',
                // },
                {
                    label: '链接',
                    field: 'link',

                },
                {
                    label: '修改',
                    slot: 'action', // 用于放置操作按钮
                },
            ],
            MidtableSchema: [
                {
                    label: '系列名',
                    field: 'title',
                },
                {
                    label: '系列ID',
                    field: 'ctid',
                },
                {
                    label: '图片',
                    field: 'cover',
                    type: 'img', // 插槽用于显示图片或上传图片控件
                },
                {
                    label: '总份数',
                    field: 'goodsCount',
                },
                {
                    label: '流通数/一级剩余',
                    slot: 'firstMarketNum',
                },
                {
                    label: '系列限价',
                    slot: 'seriesLimitPrice',
                },
                {
                    label: '地板价',
                    field: 'floorPrice',
                },
                {
                    label: '主力/小户份数',
                    slot: 'mainOrSmallShares',
                },
                {
                    label: '小户人数',
                    slot: 'smallHolders',
                },
                {
                    label: '主力寄售/非主力寄售',
                    slot: 'mainAndNonMainConsignment',
                },
                {
                    label: '铸造时间',
                    field: 'createdAt',
                },
                {
                    label: '修改',
                    slot: 'action', // 插槽用于显示操作按钮
                },
            ],
            templateUrl: '', // 盲盒内作品模板地址
            templateUrl1: '', // 盲盒内作品模板地址
            isDetail: false, // 详情
            activityNo: null, // 活动编号
            formData: {
                bzlMold: "11"
            },

            creationNum: 'x',
            collectionNum: 'x',
            formSchemaAppend: [{
                type: 'search',
                label: '系列ID/系列名：',
                placeholder: '请输入系列ID/系列名',
                field: 'ctid',
                rules: [{
                    required: true,
                    message: '请输入系列ID/系列名',
                    trigger: 'blur'
                }]
            }, {
                type: 'number-input',
                label: '补充多少个token：',
                placeholder: '请输入补充多少个token',
                field: 'createNum',
                rules: [{
                    required: true,
                    message: '请输入补充多少个token',
                    trigger: 'blur'
                }]
            }, {
                type: 'action',
                exclude: ['reset']
            }],
            formDataAppend: {
                ctid: "",
                createNum: "",
            },
            loading: false
        }
    },
    computed: {

        formSchema() {
            return [
                {
                    type: 'input',
                    label: '系列最低限价：',
                    placeholder: '请输入系列最低限价',
                    field: 'onSaleMinPrice',
                    rules: [{
                        required: true,
                        message: '请输入系列最低限价',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '系列最高限价：',
                    placeholder: '请输入系列最高限价',
                    field: 'onSaleMaxPrice',
                    rules: [{
                        required: true,
                        message: '请输入系列最高限价',
                        trigger: 'blur'
                    },]
                },
                // 每日最高限价提价百分比：【2% 】​
                {
                    label: '每日最高限价提价百分比：',
                    placeholder: '请输入每日最高限价提价百分比',
                    field: 'onSaleMaxPriceRatio',
                    rules: [{
                        required: true,
                        message: '请输入每日最高限价提价百分比',
                        trigger: 'blur'
                    }]
                },
                {
                    type: 'datetimerange',
                    label: '挂售/交易期间：',
                    field: 'startTime',
                    pickerOptions: {
                      disabledDate: (time) => {
                        return time.getTime() < Date.now() - 86400000
                      }
                    }
                },
                {
                    type: 'radio',
                    label: '可挂售时间(天)：',
                    slot: 'OnSaleDay',
                    rules: [{
                        required: true,
                    }]
                },
                {
                    slot: 'error_msg'
                },
                {
                    type: 'datetimerange',
                    label: '可挂售时间段：',
                    slot: 'OnSaleTime',
                    rules: [{
                        required: true,
                        message: '请选择可挂售时间段',
                        trigger: 'change'
                    }]
                },
                {
                    type: 'radio',
                    label: '可交易时间(天)：',
                    slot: 'tradeDay',
                    rules: [{
                        required: true,
                    }]
                },
                {
                    slot: 'error_msg'
                },
                // {
                //     type: 'radio',
                //     label: '可交易时间(天)：',
                //     field: 'tradeDay',
                //     options: [
                //         {
                //             label: '每日',
                //             value: 1
                //         },
                //         {
                //             label: '周一至周五',
                //             value: 2
                //         }
                //     ],
                //     rules: [{
                //         required: true,
                //     }]
                // },
                // 可交易时间段
                {
                    type: 'datetimerange',
                    label: '可交易时间段：',
                    slot: 'tradeTime',
                    rules: [{
                        required: true,
                        message: '请选择可交易时间段',
                        trigger: 'change'
                    }]
                },

                {
                    type: 'radio',
                    label: '正常交易手续费：',
                    field: 'systemFeeType',
                    options: [
                        {
                            label: '利润差的百分比',
                            value: 1
                        },
                        {
                            label: '售出价的百分比',
                            value: 2
                        }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '利润差的百分比：',
                    placeholder: '利润差的百分比',
                    disabled: this.formData.systemFeeType != 1,
                    field: this.formData.systemFeeType != 1 ? '' : 'systemFeeRatio',
                    rules: [{
                        required: true,
                        message: '请输入利润差的百分比',
                        trigger: 'blur'
                    }]
                },
                {
                    type: 'input',
                    label: '售出价的百分比：',
                    disabled: this.formData.systemFeeType == 1,
                    placeholder: '售出价的百分比',
                    field: this.formData.systemFeeType == 1 ? '' : 'systemFeeRatio',
                    rules: [{
                        required: true,
                        message: '请输入售出价的百分比',
                        trigger: 'blur'
                    }]
                },

                {
                    type: 'input',
                    label: '强制换手时间：',
                    placeholder: '强制换手时间',
                    field: 'forceSellDay',
                    rules: [{
                        required: true,
                        message: '请输入强制换手时间',
                        trigger: 'blur'
                    }]
                },
                {
                    type: 'input',
                    label: '逾期未换手额外手续费/天：',
                    placeholder: '逾期未换手额外手续费/天',
                    field: 'forceSellFeeRatio',
                    rules: [{
                        required: true,
                        message: '请输入逾期未换手额外手续费/天',
                        trigger: 'blur'
                    }]
                },
                // {
                //     type: 'img',
                //     label: '作品图片：',
                //     placeholder: '作品图片',
                //     field: 'photo',
                //     multigraph: true,
                //     rules: [{
                //         required: true,
                //         message: '请选择作品图片',
                //         trigger: 'change'
                //     }]
                // },
                // {
                //   type: 'search_num',
                //   label: '查询其他系列数量(非必填)：',
                //   placeholder: '请输入铸造数量',
                //   field: 'ctid',
                // },
                // {
                //     type: 'number-input',
                //     label: '铸造数量：',
                //     placeholder: '请输入铸造数量',
                //     field: 'createNum',
                //     rules: [{
                //         required: true,
                //         message: '请输入铸造数量',
                //         trigger: 'blur'
                //     }]
                // },
                // {
                //     type: 'number-input',
                //     label: '铸造价格：',
                //     placeholder: '请输入铸造价格',
                //     field: 'price',
                //     rules: [{
                //         required: true,
                //         message: '请输入铸造价格',
                //         trigger: 'blur'
                //     }]
                // },
                // {
                //     slot: 'msg_info',
                // },
                {
                    type: 'action'
                    // exclude: ['reset', 'submit', 'back']
                }
            ]
        },
        formSchema2() {
            return [
                {
                    type: 'input',
                    label: '系列最低限价：',
                    placeholder: '请输入系列最低限价',
                    field: 'onSaleMinPrice',
                    rules: [{
                        required: true,
                        message: '请输入系列最低限价',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '系列最高限价：',
                    placeholder: '请输入系列最高限价',
                    field: 'onSaleMaxPrice',
                    rules: [{
                        required: true,
                        message: '请输入系列最高限价',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'radio',
                    label: '销毁一级剩余：',
                    field: 'destory',
                    options: [
                        {
                            label: '销毁',
                            value: 1
                        },
                        {
                            label: '保留',
                            value: 0
                        }
                    ]
                },
                {
                    type: 'action'
                }
            ]
        },
        formSchema3() {
            return [
                {
                    // title
                    type: 'input',
                    label: '名称：',
                    placeholder: '请输入名称',
                    field: 'title',
                    rules: [{
                        required: true,
                        message: '请输入名称',
                        trigger: 'blur'
                    }]
                },
                {
                    // price
                    type: 'number-input',
                    label: '单价：',
                    placeholder: '请输入单价',
                    field: 'price',
                    rules: [{
                        required: true,
                        message: '请输入单价',
                        trigger: 'blur'
                    }]
                },
                {
                    type: 'img',
                    label: '图片：',
                    placeholder: '图片',
                    slot: 'cover',
                    multigraph: true,
                    rules: [{
                        required: true,
                        message: '请选择图片',
                        trigger: 'change'
                    }]
                },
                {
                    type: 'img',
                    label: '详情页图片：',
                    placeholder: '详情页图片',
                    slot: 'detail',
                    multigraph: true,
                    rules: [{
                        required: true,
                        message: '请选择详情页图片',
                        trigger: 'change'
                    }]
                },
                {
                    type: 'input',
                    label: '上架数量：',
                    placeholder: '请输入上架数量',
                    field: 'onSaleNum',
                    rules: [{
                        required: true,
                        message: '请输入上架数量',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '每日上架时间：',
                    placeholder: '请选择每日上架时间',
                    slot: 'onSaleTime',
                    rules: [{
                        required: true,
                        message: '请选择每日上架时间',
                        trigger: 'change'
                    }]
                },
                {
                    type: 'input',
                    label: '每日售罄时间：',
                    placeholder: '请选择每日售罄时间',
                    slot: 'sellOutTime',
                    rules: [{
                        required: true,
                        message: '请选择每日售罄时间',
                        trigger: 'change'
                    }]
                },
                {
                    type: 'action'
                }
            ]
        },
        // 限制只能选择当天的小时（当前小时为最大选择小时）
        disabledHours() {
            const currentHour = new Date().getHours();
            return (time) => time > currentHour;
        },
        // 限制只能选择当天的分钟（当前分钟为最大选择分钟）
        disabledMinutes() {
            const currentMinute = new Date().getMinutes();
            const currentHour = new Date().getHours();
            return (time, hour) => {
                if (hour === currentHour) {
                    return time > currentMinute;
                }
                return false;
            };
        },

    },
    mounted() {
        this.getFist()
    },
    methods: {
        formattedTime(e) {
            console.log(e instanceof Date);

            if (e instanceof Date) {
                const hours = this.time.getHours().toString().padStart(2, '0');
                const minutes = this.time.getMinutes().toString().padStart(2, '0');
                return `${hours}:${minutes}`; // 从 Date 对象中提取并显示小时和分钟
            } else {
                return e; // 如果是分钟格式，直接显示
            }
        },
        formattedTime2(e) {
            if (e instanceof Date) {
                const hours = this.time.getHours().toString().padStart(2, '0');
                const minutes = this.time.getMinutes().toString().padStart(2, '0');
                return `${hours}:${minutes}`; // 从 Date 对象中提取并显示小时和分钟
            } else {
                return e; // 如果是分钟格式，直接显示
            }
        },
        convertToTime(dateString) {
            const date = new Date(dateString); // 将字符串转换为 Date 对象
            const hours = date.getHours().toString().padStart(2, '0'); // 获取小时并格式化为两位数
            const minutes = date.getMinutes().toString().padStart(2, '0'); // 获取分钟并格式化为两位数
            return `${hours}:${minutes}`; // 拼接成 HH:mm 格式
        },
        // 提交时将 Date 对象格式化为字符串 "HH:mm"
        formatTimeToString(time) {
            const hours = time.getHours().toString().padStart(2, '0');
            const minutes = time.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        formatTime(timeStr) {
            const [hours, minutes] = timeStr.split(':');
            return new Date(0, 0, 0, hours, minutes);
        },
        handleRemove(file, fileList) {
            console.log(file, fileList)
            this.fileList = fileList
        },
        handleRemove2(file, fileList) {
            console.log(fileList)
            this.fileList2 = fileList
        },
        // 上传成功后的回调
        handleSuccess2(response, file, fileList) {
            console.log(file, response);

            // 假设接口返回的路径是 response.url
            file.url = response.result.url;  // 给文件对象加上url属性，保存上传后的路径
            this.fileList2.push(response.result.url); // 将上传成功的文件对象添加到 fileList 中
        },
        // 上传前的钩子函数，用于做一些校验
        beforeUpload2(file) {
            const isImage = file.type.startsWith("image/");
            if (!isImage) {
                this.$message.error("只能上传图片文件!");
            }
            return isImage;
        },
        // 上传成功后的回调
        handleSuccess(response, file, fileList) {
            console.log(file, response);

            // 假设接口返回的路径是 response.url
            file.url = response.result.url;  // 给文件对象加上url属性，保存上传后的路径
            this.fileList.push(response.result.url); // 将上传成功的文件对象添加到 fileList 中
        },
        // 上传前的钩子函数，用于做一些校验
        beforeUpload(file) {
            const isImage = file.type.startsWith("image/");
            if (!isImage) {
                this.$message.error("只能上传图片文件!");
            }
            return isImage;
        },
        add() {
            this.dialogVisible3 = true
            this.dialogTitle3 = '添加实物类'
            this.fileList = []
            this.fileList2 = []
            this.formData3 = {
                onSaleTime: this.formatTime('09:00'),
                sellOutTime: this.formatTime('12:00'),
            }
        },
        async submit1() {
            // onSaleMinPrice
            this.formData.onSaleMinPrice = this.formData.onSaleMinPrice - 0

            this.formData.onSaleMaxPrice = this.formData.onSaleMaxPrice - 0
            let startTime,endTime;
            if(this.formData.startTime){
              startTime= this.formData.startTime[0]
              endTime= this.formData.startTime[1]
            }
            let res = await this.$api.fixedUpdate({
              ...this.formData,
              startTime,
              endTime
            })
            if (res.status.code == 0) {
                this.dialogVisible1 = false
                this.$message.success('修改成功')
                this.getFist()
            }
            // fixedUpdate
        },
        async submit2() {
            this.formData2.onSaleMinPrice = this.formData2.onSaleMinPrice - 0
            this.formData2.onSaleMaxPrice = this.formData2.onSaleMaxPrice - 0
            if (this.formData2.destory == 1) {
                await this.destroy(this.formData2.ctid)
            }
            let res = await this.$api.bzlUpdate(this.formData2)
            if (res.status.code == 0) {
                this.dialogVisible2 = false
                this.$message.success('修改成功')
                this.getSecond()
            }
            // bzlUpdate
        },
        async submit3() {

            if (this.dialogTitle3 == '添加实物类') {
                this.formData3.cover = JSON.stringify(this.fileList)
                this.formData3.detail = JSON.stringify(this.fileList2)
                this.formData3.onSaleTime = this.convertToTime(this.formData3.onSaleTime)
                this.formData3.sellOutTime = this.convertToTime(this.formData3.sellOutTime)
                console.log(this.formData3.onSaleTime, 123);
                let res = await this.$api.batchCreateReal(this.formData3)
                if (res.status.code == 0) {
                    this.dialogVisible3 = false
                    this.$message.success('添加成功')
                    this.getthrid()
                } else {
                    this.formData3.onSaleTime = ''
                    this.formData3.sellOutTime = ''
                }
                // batchCreateReal
            } else {
                console.log(this.formData3.onSaleTime, this.formData3.sellOutTime, 123123123121);

                console.log(this.fileList, this.fileList2, 12312311111);

                this.formData3.onSaleTime = this.formatTimeToString(this.formData3.onSaleTime);
                this.formData3.sellOutTime = this.formatTimeToString(this.formData3.sellOutTime);

                this.fileList = this.fileList.map(item => {
                    if (typeof item === 'object' && item.url) {
                        return item.url;
                    } else if (typeof item === 'string') {
                        return item;
                    }
                });

                this.fileList2 = this.fileList2.map(item => {
                    if (typeof item === 'object' && item.url) {
                        return item.url;
                    } else if (typeof item === 'string') {
                        return item;
                    }
                });

                // this.formData3.cover = JSON.stringify(this.fileList)
                // this.formData3.detail = JSON.stringify(this.fileList2)
                this.formData3.cover = JSON.stringify(this.fileList)
                this.formData3.detail = JSON.stringify(this.fileList2)
                let res = await this.$api.realUpdate(this.formData3)
                if (res.status.code == 0) {
                    this.dialogVisible3 = false
                    this.$message.success('修改成功')
                    this.getthrid()

                }
            }
            // realUpdate
        },
        async open_destroy(item) {
            this.$confirm('是否确认销毁系列内未产生交易的创作品', '系列内作品销毁', {
                confirmButtonText: '确认销毁创作品',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                this.destroy(item.ctid)
            }).catch(() => {

            })
        },
        async destroy(ctid) {
            this.loading = true
            this.loadingText = '正在销毁，请耐心等待'
            const res = await this.$api.destroyByCtid({
                ctid
            })
            if (res.status.code == 0) {
                this.$message.success('销毁成功')
                this.loading = false
                this.getFist()
                this.getSecond()
            } else {
                this.$message.error(res.status.msg)
                this.loading = false
            }
        },
        onQueryChange(data) {
            // this.query = data
            this.searchForm = data
            if (this.activeName == 'first') {
                this.getFist()
            } else if (this.activeName == 'second') {
                this.getSecond()
            } else if (this.activeName == 'thrid') {
                this.getthrid()
            }
        },

        look(item) {
            this.$router.push({
                name: 'floorConfig2',
                query: {
                    moduleIds: 7
                }
            })
        },
        async getFist() {
            const {
                status,
                result
            } = await this.$api.fixedList({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.searchForm
            })

            if (status.code === 0) {
                result.list.forEach(item => {
                    item.u4GoodsCount = null; // 大户持有
                    item.holdNumMin = null;  // 小户持有
                    item.userNumMin = null;  // 小户人数
                });
                this.NotableData = result.list

            }
        },
        async getSecond() {
            const {
                status,
                result
            } = await this.$api.bzlList({
                pageNum: this.page2.pageNum,
                pageSize: this.page2.pageSize,
                ...this.searchForm
            })

            if (status.code === 0) {
                result.list.forEach(item => {
                    item.u4GoodsCount = null; // 大户持有
                    item.holdNumMin = null;  // 小户持有
                    item.userNumMin = null;  // 小户人数
                    item.u4OnSaleGoodsCount = null; // 主力寄售
                    item.u0OnSaleGoodsCount = null; // 非主力寄售
                });
                this.MidtableData = result.list
            }
        },
        async getthrid() {
            const {
                status,
                result
            } = await this.$api.realList({
            })

            if (status.code === 0) {
                result.list.forEach(item => {
                    item.u4GoodsCount = null; // 大户持有
                    item.holdNumMin = null;  // 小户持有
                    item.userNumMin = null;  // 小户人数
                    item.link = 'https://www.nftcn.com/bzl/#/pagesA/project/activity/shop?ctid=' + item.ctid
                });
                this.tableData = result.list
            }
        },
        handleClick(tab, event) {
            this.query = {}
            if (this.activeName == 'first') {
                this.getFist()
            } else if (this.activeName == 'second') {
                this.getSecond()
            } else if (this.activeName == 'thrid') {
                this.getthrid()
            }
            console.log(tab, event, this.activeName, 123123123123);
        },
        MidhandleEdit(item) {
            this.dialogVisible2 = true
            this.formData2 = item
            this.formData2.destory = 0
        },
        NohandleEdit(item) {
            this.formData = item
            if(this.formData.startTime){
              this.formData.startTime = [this.formData.startTime,this.formData.endTime]
            }
            console.log(this.formData.startTime)
            this.dialogVisible1 = true
        },
        handleEdit(item) {
            this.dialogTitle3 = '修改实物类'
            this.dialogVisible3 = true
            this.formData3 = item
            this.fileList = []
            this.fileList2 = []
            this.formData3.onSaleTime = this.formatTime(item.onSaleTime)
            this.formData3.sellOutTime = this.formatTime(item.sellOutTime)
            console.log(this.formData3.onSaleTime, 'onSaleTime');

            this.fileList = item.coverList.map(url => ({
                name: url.split('/').pop(), // 从 URL 中提取文件名
                url: url
            }));

            this.fileList2 = item.detailList.map(url => ({
                name: url.split('/').pop(), // 从 URL 中提取文件名
                url: url
            }));

            // this.fileList2 = item.detailList
            console.log(this.fileList, this.fileList2, 12312311);

        },
        async open_data(item) {
            // item.u4GoodsCount=1
            // item.u0GoodsCount=2
            // item.u4UserCount=3
            // item.u0UserCount=4
            // item.u0OnSaleGoodsCount=5
            const res = await this.$api.seriesGoodsCount({
                ctid: item.ctid
            })
            if (res.status.code == 0) {

                const u0GoodsCount = res.result.u0GoodsCount // 大户持有数
                const u0UserCount = res.result.u0UserCount // 大户人数


                item.u4UserCount = res.result.u4UserCount
                item.u0OnSaleGoodsCount = res.result.u0OnSaleGoodsCount
                item.u4OnSaleGoodsCount = res.result.u4OnSaleGoodsCount
                item.holdNumMax = Math.round(u0GoodsCount * 0.1)
                item.holdNumMiddle = Math.round(u0GoodsCount * 0.2)
                item.u4GoodsCount = res.result.u4GoodsCount // 大户持有
                item.holdNumMin = Math.round(u0GoodsCount * 0.7) // 小户持有

                item.userNumMax = Math.round(u0UserCount * 0.1)
                item.userNumMiddle = Math.round(u0UserCount * 0.2)

                item.userNumMin = Math.round(u0UserCount * 0.7) // 小户人数
                console.error(item)
            } else {
                this.$message.error(res.status.msg)
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-top: 80px;
}
</style>
