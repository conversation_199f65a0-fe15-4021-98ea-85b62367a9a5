<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showReset="false"
            :show-export="false" @onExport="tableExport"></common-query>
        <!-- 增加 -->
        <el-button type="primary" size="mini" style="margin-bottom: 20px;" icon="el-icon-plus"
            @click="add">新增</el-button>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
            <!-- reward -->
            <template #exportLeagueMembers="scope">
                <el-button type="text" size="mini" @click="exportLeagueMembers(scope.row)">导出</el-button>
            </template>
            <template #action="scope">
                <!-- <el-button type="text" size="mini" @click="editAvatar(scope.row)">修改头像</el-button> -->
                <el-button type="text" size="mini" @click="editname(scope.row)">修改联盟</el-button>
            </template>

            <!-- createAt -->
            <template #createAt="scope">
                {{ removeMilliseconds(scope.row.createAt) }}
            </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
            </common-pagination>
        </div>
        <el-dialog :title="titles" :visible.sync="dialogVisible" width="800px" center @close="dialogVisible = false"
            destroy-on-close :close-on-click-modal="false">
            <CommonForm :schema="formSchema" :isEdit="!details" :data="formData" :submit="submit" label-width="150px"
                :isBack="true">

                <template #avatar="scope">
                    <el-upload :action="action" :headers="token" :on-success="handlePicSuccess"
                        :class="{ hide: hideUpload_introduce }" :on-change="handleIntroduceUploadHide"
                        :on-remove="handleIntroduceRemove" :file-list="fileListImg" class="avatar-uploader">
                        <img v-if="formData.avatar" :src="formData.avatar" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </template>
            </CommonForm>
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
import CommonPagination from '@/components/CommonPagination'
import { backgroundList, bodyList, clothList, emojiList, headList } from '../data.json'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: 'PFPshop',
    components: {
        CommonQuery,
        CommonTable, CommonForm,
        CommonPagination
    },
    props: {},
    data() {
        return {
            titles: '创建联盟',
            limitCount: 1,

            fileListImg: [],
            hideUpload_introduce: false,
            action:
                process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/upload',
            token: { AdminAuthorization: localStorage.getItem('usertoken') },
            formData: {
                groupType: 1,
                avatar: ""
            },

            dialogVisible: false,
            query1: {
                groupType: 1
            },
            querySchema: [ // 搜索组件架构
                {
                    type: 'select',
                    label: '排序：',
                    placeholder: '',
                    field: 'sort',
                    // multiple: true,
                    options: [
                        {
                            label: '按成员人数', value: '1'
                        },
                        {
                            label: '按创建时间', value: '2'
                        },
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'datetimerange',
                    label: '创建时间：',
                    placeholder: '请输入创建时间',
                    field: 'registerTimeStart',
                    field2: 'registerTimeEnd',
                },
                {
                    type: 'input',
                    label: '联盟名称：',
                    placeholder: '请输入联盟名称',
                    field: 'name'
                },
                {
                    type: 'twoinput',
                    label: '联盟成员人数：',
                    placeholder: '请输入联盟成员人数',
                    field: ['minMemberNum', 'maxMemberNum'],
                },
                //
                {
                    type: 'select',
                    label: '联盟类型：',
                    placeholder: '',
                    field: 'groupType',
                    // multiple: true,
                    options: [
                        { label: '黄金', value: 1 },
                        { label: '铂金', value: 2 }

                    ],
                    rules: [{
                        required: true,
                    }]
                },

            ],
            tableSchema: [
                {
                    label: '联盟类型',
                    field: 'groupType',
                    type: 'tag',
                    tagMap: {

                        1: {
                            label: '黄金',
                            tagType: 'error'
                        },
                        2: {
                            label: '铂金',
                            tagType: 'info'
                        }
                    },
                },
                {
                    label: '联盟创建时间',
                    slot: 'createAt',
                },
                {
                    label: '联盟长用户昵称',
                    field: 'nickname',
                },
                {
                    label: 'con add',
                    field: 'contractAddress',
                },
                {
                    label: '联盟名称',
                    field: 'name',
                },
                {
                    type: 'img',
                    label: '联盟头像',
                    field: 'avatar',
                },
                {
                    label: '联盟成员人数',
                    field: 'memberNum',
                },
                {
                    label: '联盟成员人员导出',
                    slot: 'exportLeagueMembers',
                },
                {
                    label: '修改信息',
                    // field: 'updateInfo',
                    slot: 'action'
                },
            ],
            tableData: [{}],
            page: {
                totalCount: 0,
                pageSize: 10,
                pageNum: 1
            }, // 分页数据
            query: {
                groupType: 1

            }
        }
    },
    computed: {
        formSchema() {
            return [
                {
                    type: 'input',
                    label: '联盟长用户地址：',
                    disabled: this.titles == '修改联盟',
                    placeholder: '请输入联盟长用户地址',
                    field: 'contractAddress',
                    rules: [
                        {
                            required: true,
                            message: '请输入联盟长用户地址',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '联盟名称：',
                    placeholder: '请输入联盟名称',
                    field: 'name',
                    rules: [
                        {
                            required: true,
                            message: '请输入联盟名称',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    label: '联盟头像：',
                    slot: 'avatar',
                    rules: [
                        {
                            required: true,
                            message: '请选择联盟头像',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'radio',
                    label: '联盟类型:',
                    field: 'groupType',
                    disabled: this.titles == '修改联盟',
                    options: [
                        { label: '黄金', value: 1 },
                        { label: '铂金', value: 2 }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'action'
                }
            ]
        }

    },
    mounted() {
        this.getList()
    },
    methods: {
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        add() {
            this.titles = '创建联盟'
            this.dialogVisible = true
            this.formData = {
                groupType: 1,
                avatar: ""
            }
        },
        // 图片移除
        handleIntroduceRemove(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
            this.formData.avatar = ''
        },
        handlePicSuccess(res, file) {
            console.log(res, '上传');
            this.formData.avatar = res.result.url
        },
        handleIntroduceUploadHide(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
        },
        async submit() {
            if (this.titles == '创建联盟') {
                let res = await this.$api.bzlCreateGroup(this.formData)
                if (res.status.code == 0) {
                    this.$message.success('创建成功')
                    this.dialogVisible = false
                    this.getList()
                }
            } else if (this.titles == '修改联盟') {
                let res = await this.$api.bzlGroupUpdate(this.formData)
                if (res.status.code == 0) {
                    this.$message.success('修改成功')
                    this.dialogVisible = false
                    this.getList()
                }
            }

            // bzlCreateGroup
        },
        editAvatar(e) { },
        editname(e) {
            this.titles = '修改联盟'
            this.formData = e
            this.dialogVisible = true
        },
        async exportLeagueMembers(e) {
            let res = await this.$api.bzlGroupExport({
                id: e.id,
                name: e.name,
                avatar: e.avatar
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.loading = false
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                this.loading = false
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '联盟信息' + Date.now() + '.csv')
                this.$message.success('0联盟信息导出成功')
                this.loading = false
            }
            // bzlGroupExport
        },
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            // this.query = data
            this.getList(true)
        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 创建活动
        toFormPage(item = {}, activityType) {
            this.dialogVisible = false
            console.log(item)
            const typeMap = {
                INVITE_NEW: 'ActivityPullNew',
                REBUILD: 'ActivityRebuild',
                GET_REWARD: 'ActivityPullNew',
                MERGE: 'ActivityPullNew'
            }
            this.$router.push({
                name: item.type ? typeMap[item.type] : typeMap[this.selected],
                query: {
                    type: item.type || this.selected,
                    activityNo: item.activityNo,
                    activityType
                }
            })
        },
        // 获取列表
        async getList() {
            const params = {
                ...this.query1,
                ...this.page,
                backgroundList: JSON.stringify(this.query.backgroundList),
                bodyList: JSON.stringify(this.query.bodyList),
                clothList: JSON.stringify(this.query.clothList),
                emojiList: JSON.stringify(this.query.emojiList),
                headList: JSON.stringify(this.query.headList)
            }
            const {
                status,
                result
            } = await this.$api.bzlGroupList(params)
            if (status.code === 0) {
                this.tableData = result.list
                this.tableData.forEach(item => {
                    item.reward = JSON.parse(item.reward);
                });
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
            }
        },
        async clearRedis(item) {
            const {
                status
            } = await this.$api.clearRedis({
                activityNo: item.activityNo,
                activityType: item.type
            })
            if (status.code === 0) {
                this.$message.success(status.msg)
            }
        },
        // 删除数据
        async deleteItem(item) {
            if (item.isCanEdit === 0) {
                this.$message.error("不可删除！！！！")
            } else {
                const {
                    status
                } = await this.$api.dutyDelete({
                    dutyId: item.dutyId,
                    dutyType: item.dutyType
                })
                if (status.code === 0) {
                    this.$message.success(status.msg)
                    this.getList()
                }
            }
        },
        nav_add() {
            this.$router.push({
                name: 'batchAddupdate'
            })
        },
        nav_update(item) {
            this.$router.push({
                name: 'batchAddupdate',
                query: {
                    dutyId: item.dutyId,
                    type: 'details',
                    dutyType: item.dutyType
                }
            })
        },
        // 导出结果
        async tableExport(item, type) {
            const params = {
                ...this.query,
                ...this.page,
            }
            const res = await this.$api.bzlGroupExport(params)
            if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then(buffer => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '联盟列表')
                this.$message.success('导出成功')
            }
        },
    }
}
</script>

<style lang="scss" scoped></style>
