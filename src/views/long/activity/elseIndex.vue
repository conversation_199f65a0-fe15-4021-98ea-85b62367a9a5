<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px"></common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'elseIndex',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          title:'',
          activityNewBannerImage:'',
          startTime:'',
          activityNewShow:1,
          activityNewStartTime:'',
          activityNewEndTime:'',
          linkApp:'',
          type:'THREE_H5'
        },
        formSchema: [{
            type: 'input',
            label: '活动名称：',
            placeholder: '请输入活动名称',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入活动名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'img',
            label: '活动banner：',
            placeholder: '请选择活动banner',
            field: 'activityNewBannerImage',
            rules: [{
              required: true,
              message: '请选择活动banner',
              trigger: 'change'
            }]
          },
          {
            type: 'datetimerange',
            label: '活动时间：',
            field: 'startTime',
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            }
          },
          {
            label: '何时展示在“活动”中：',
            field: 'activityNewShow',
            type: 'radio',
            options: [{
              label: '立即',
              value: 1
            }, {
              label: '定时',
              value: 2
            }]
          },
          {
            type: 'datetime',
            label: '展示开始时间：',
            field: 'activityNewStartTime',
            // rules: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            show: {
              relationField: 'activityNewShow',
              value: [2]
            },
          },
          {
            type: 'input',
            label: '活动跳转链接：',
            placeholder: '请输入活动跳转链接',
            field: 'link',
            rules: [{
              required: true,
              message: '请输入活动跳转链接',
              trigger: 'blur'
            }]
          },
          {
            type: 'action'
          },
        ],
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
         let startTime, endTime = ""
         if (this.formData.startTime[0]) {
           startTime = this.formData.startTime[0]
         }
         if (this.formData.startTime[1]) {
           endTime = this.formData.startTime[1]
         }
         const data = {
           activityNo: this.activityNo,
           ...this.formData,
           activityNewShow:1,
           startTime,
           endTime,
           activityNewStartTime:this.formData.activityNewStartTime
         }
         this.$confirm('是否确认提交保存？', '确认提交保存', {
           confirmButtonText: '确定',
           cancelButtonText: '取消',
           type: 'warning'
         }).then(async () => {
           if (this.activityNo) {
             const {
               status
             } = await this.$api.activityEdit(data)
             if (status.code === 0) {
               this.routerBack()
             }
           } else {
             const {
               status
             } = await this.$api.activityAdd(data)
             if (status.code === 0) {
               this.routerBack()
             }
           }
         })
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid: this.formData.ctid,
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
    justify-content: flex-start;

    .shuru {
      width: 500px;
      height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      padding: 40px 0px;
    }

    .yulan {
      width: 100px;
      height: 812px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 40px 0px;
    }

    .preview {
      min-height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      background-image: url('https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20230320/4933c07760529e700a4d7b085a9583a5_1520x2006.jpg');
      background-repeat: no-repeat;
      background-size: 100%;
      background-color: #dbdbdb;
      padding: 140px 30px 100px 30px;

      .title {
        font-size: 28px;
        font-weight: 600;
        line-height: 50px;
      }

      .body {
        margin-top: 40px;
        // min-height:300px;
        background-color: rgb(220, 220, 220, 0.6);
        border-radius: 4px;
        // box-shadow: 0px 0px 20px 6px rgb(255, 255, 255);
        border: 1px solid #bebebe;
        padding: 30px;
        font-size: 18px;


        .text {
          margin-bottom: 20px;
        }

        .footer {
          text-align: right;
          margin-top: 40px;
          font-size: 20px;

          >div {
            margin-bottom: 6px;
          }
        }
      }

      .code {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 40px 0px;

        img {
          width: 200px;
          height: auto;
        }
      }

      .body .text:first-child {
        font-weight: 600;
      }

    }
  }

  .el-textarea {
    textarea {
      height: 200px !important;
    }
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
