<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #pull-new>
        老用户每拉
        <el-input-number
          :disabled="isDetail"
          v-model="formData.inviteQuantity"
          :min="1"
          :step="1"
          style="width: 100px"
        ></el-input-number>
        个新用户可获得1份奖励，上限
        <el-input-number
          :disabled="isDetail"
          v-model="formData.rewardLimit"
          :min="1"
          :step="1"
          style="width: 100px"
        ></el-input-number>
        份
      </template>
      <template #nft>
        <el-row><el-col :span="20">
          <common-table :is-edit="!isDetail" :table-schema="tableSchema" :table-data="tableData">
            <template #action-header>
              <template v-if="!isDetail">
                <el-button @click="addItem" type="primary" size="mini">新增</el-button>
                <el-button @click="downloadTemplate" type="primary" size="mini">下载模版</el-button>
              </template>
            </template>
            <template #works-select="scope">
              <template v-if="scope.row.nftType === 2">
                <el-input v-if="!isDetail" v-model="scope.row.ctid"></el-input>
                <template v-else>{{ scope.row.ctid }}</template>
              </template>
              <template v-else-if="scope.row.nftType === 1">
                <file-uploader v-if="!isDetail" :value.sync="scope.row.goodsExcelUrl"></file-uploader>
                <el-button v-else @click="downloadExcel(scope.row.materialsUnqNo)">下载 Excel</el-button>
              </template>
            </template>
            <template #action="scope">
              <el-popconfirm
                title="确定要删除吗？"
                @confirm="deleteItem(scope.$index)"
              >
                <el-button v-if="!isDetail" slot="reference" type="text">删除</el-button>
              </el-popconfirm>
            </template>
          </common-table>
        </el-col></el-row>
      </template>
      <template #collection>
        <el-row><el-col :span="20">
          <common-table :is-edit="!isDetail" :table-schema="collectionTableSchema" :table-data="collectionTableData"></common-table>
        </el-col></el-row>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import { mapActions } from 'vuex'
import { downloadBlob } from '@/utils/helper'

export default {
  name: 'ActivityPullNew',
  components: {
    CommonForm,
    CommonTable,
    FileUploader
  },
  data () {
    const validateNft = (rule, value, callback) => {
      if (this.tableData.length <= 0) {
        callback(new Error('请添加数据'))
      } else {
        this.tableData.forEach(item => {
          console.log(item.nftType)
          console.log(item.goodsExcelUrl)
          if (!item.coverPicture) {
            callback(new Error('请上传封面图片'))
          } else if (!item.jumpLink || item.jumpLink.length >= 500) {
            callback(new Error('请输入跳转链接，跳转链接不得大于 500 字符'))
          } else if (!item.nftType) {
            callback(new Error('请选择类型'))
          } else if (item.nftType === 1 && !item.goodsExcelUrl) {
            callback(new Error('请上传 Excel 文件'))
          } else if (item.nftType === 2 && !item.ctid) {
            callback(new Error('请输入系列 ID'))
          }
        })
        callback()
      }
    }
    const validateCollection = (rule, value, callback) => {
      this.collectionTableData.forEach(item => {
        if (!item.priorityBuyCover) {
          callback(new Error('请上传封面图片'))
        } else if (!item.linkJump) {
          callback(new Error('请输入跳转链接'))
        }
      })
      callback()
    }
    return {
      isDetail: false, // 详情
      activityNo: false, // 活动编号
      tableSchema: [ // 表格架构
        {
          label: '封面图片',
          field: 'coverPicture',
          type: 'img',
          // isEdit: true,
          width: '170px'
        },
        {
          label: '类型',
          field: 'nftType',
          type: 'select',
          options: [
            { value: 1, label: '作品' },
            { value: 2, label: '系列' }
          ]
        },
        {
          label: '跳转链接',
          field: 'jumpLink',
          type: 'input'
        },
        {
          label: '作品选择',
          slot: 'works-select',
          width: '170px'
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],
      collectionTableSchema: [ // 表格架构
        {
          label: '封面图片',
          field: 'priorityBuyCover',
          type: 'img',
          // isEdit: true,
          width: '170px'
        },
        {
          label: '跳转链接',
          field: 'linkJump',
          type: 'input'
        }
      ],
      tableData: [{
        coverPicture: '',
        goodsExcelUrl: ''
      }],
      collectionTableData: [{
        priorityBuyCover: '',
        linkJump: ''
      }],
      formData: {
        inviteQuantity: '',
        rewardLimit: '',
        ruleImage: '',
        backgroundImage: '',
        type: ''
      },
      formSchema: [
        {
          type: 'select',
          label: '活动类型：',
          placeholder: '请选择活动类型',
          field: 'type',
          disabled: true,
          options: [
            { label: '拉新', value: 'INVITE_NEW' },
            { label: '熔炉', value: 'REBUILD' },
            { label: '合成', value: 'MERGE' },
            { label: '抽奖', value: 'GET_REWARD' }
          ],
          rules: [{ required: true, message: '请选择活动类型', trigger: 'change' }]
        },
        {
          type: 'number-input',
          label: '活动ID：',
          placeholder: '请输入活动ID',
          field: 'feishuNo',
          maxlength: 10,
          rules: [
            { required: true, message: '请输入活动 ID', trigger: 'blur' },
            { type: 'number', message: '活动 ID 只能为数字', trigger: 'blur' }
          ]
        },
        {
          type: 'input',
          label: '活动名称：',
          placeholder: '请输入活动名称',
          field: 'title',
          rules: [{ required: true, message: '请输入活动名称', trigger: 'blur' }]
        },
        {
          type: 'img',
          label: '活动背景图片：',
          placeholder: '请选择活动背景图片',
          field: 'backgroundImage',
          rules: [{ required: true, message: '请选择活动背景图片', trigger: 'change' }]
        },
        {
          type: 'input',
          label: '活动链接：',
          placeholder: '请输入跳转路径h5以及网页跳转',
          field: 'link',
          maxlength: 500,
          rules: [{ required: true, message: '请输入活动链接', trigger: 'blur' }]
        },
        {
          type: 'datetimerange',
          label: '活动倒计时时长：',
          field: 'startTime',
          rules: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          }
        },
        {
          slot: 'pull-new',
          label: '拉新人数：',
          rules: [{ required: true, message: '请选择活动时间', trigger: 'change' }]
        },
        {
          type: 'input',
          label: '邀请新用户并购买指定NFT奖励上限：',
          placeholder: '请输入数值',
          field: 'buyNftRewardLimit',
          rules: [{ required: true, message: '请输入数值', trigger: 'blur' }]
        },
        {
          type: 'img',
          label: '活动规则：',
          placeholder: '请选择活动规则',
          field: 'ruleImage'
        },
        {
          slot: 'nft',
          field: 'nft',
          label: '购买指定NFT获取优先购次数：',
          rules: [{ required: true, validator: validateNft, trigger: 'blur' }]
        },
        {
          slot: 'collection',
          label: '优先购次数可购买藏品：',
          field: 'collection',
          rules: [{ required: true, validator: validateCollection, trigger: 'blur' }]
        },
        {
          type: 'action',
          currentRouter: '/activity/invite'
          // exclude: ['reset', 'submit', 'back']
        }
      ]
    }
  },
  mounted () {
    const { type, activityType, activityNo } = this.$route.query
    this.isDetail = activityType === 'detail'
    this.activityNo = activityNo
    this.formData.type = type
    activityNo && this.getDetail()
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    routerBack () {
      const { fullPath } = this.$route
      this.close({ tagName: fullPath })
      this.$router.back()
    },
    async getDetail () {
      const { status, result } = await this.$api.activityDetail({ activityNo: this.activityNo })
      if (status.code === 0) {
        result.startTime = [result.startTime, result.endTime]
        result.feishuNo = Number.parseInt(result.feishuNo)
        const { smeltingCollection, blindBoxPrize, goodsBlindBox, purchaseSpecNft, collectiblesAvailable, ...rest } = result
        this.formData = rest
        this.tableData = purchaseSpecNft.map(item => {
          const data = {
            ...item,
            nftType: Number.parseInt(item.nftType)
          }
          console.log(item.nftType)
          data.nftType === 1 && (data.goodsExcelUrl = item.goodsSelection)
          data.nftType === 2 && (data.ctid = item.goodsSelection)
          return data
        })
        this.collectionTableData = collectiblesAvailable.map(item => ({
          ...item,
          priorityBuyCover: item.coverPicture,
          linkJump: item.jumpLink
        }))
      }
      console.log(result)
    },
    addItem () {
      this.tableData.push({
        coverPicture: '',
        goodsExcelUrl: ''
      })
    },
    deleteItem (index) {
      this.tableData.splice(index, 1)
    },
    async downloadTemplate () {
      const { status, result } = await this.$api.downLoadTemplateExcel({ templateTag: 'INVITE_NEW_ACTIVITY_GOODS_EXCEL' })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    async downloadExcel (materialsUnqNo) {
      const res = await this.$api.activityDownloadExcel({ materialsUnqNo })
      if (res) {
        downloadBlob(res)
        this.$message.success('下载成功')
      }
    },
    async submit () {
      console.log(this.formData)
      console.log(this.tableData)
      console.log(this.collectionTableData)
      const data = {
        activityNo: this.activityNo,
        linkType: 20,
        ...this.formData,
        startTime: `${this.formData.startTime[0]}`,
        endTime: `${this.formData.startTime[1]}`,
        activityMaterialsRequestJson: JSON.stringify(this.tableData),
        ...this.collectionTableData[0]
      }
      console.log(data)
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (this.activityNo) {
          const { status } = await this.$api.activityEdit(data)
          if (status.code === 0) {
            this.routerBack()
          }
        } else {
          const { status } = await this.$api.activityAdd(data)
          if (status.code === 0) {
            this.routerBack()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding-top: 40px;
}
</style>
