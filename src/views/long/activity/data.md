            <el-tab-pane label="非固定提限价类" name="second">
                <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>

                <common-table :table-schema="MidtableSchema" :showIndex="false" :table-data="MidtableData">
                    <!-- 系列限价 -->
                    <template #seriesLimitPrice="scope">
                        {{ scope.row.onSaleMinPrice + ' - ' + scope.row.onSaleMaxPrice }}
                    </template>
                    <template #firstMarketNum="scope">
                        {{ scope.row.firstMarketNum }}
                        <el-button type="text" size="small" @click="open_destroy(scope.row)">销毁一级剩余</el-button>

                    </template>
                    <!-- 主力/小户份数 mainOrSmallShares -->
                    <template #mainOrSmallShares="scope">
                        <!-- item.u4GoodsCount = res.result.u4GoodsCount // 大户持有
                        item.holdNumMin = Math.round(u0GoodsCount * 0.7) // 小户持有 -->
                        <span v-if="scope.row.u4GoodsCount != null && scope.row.holdNumMin != null">{{
                            scope.row.u4GoodsCount + ' / ' + scope.row.holdNumMin }}</span>
                        <el-button type="text" size="small" v-else @click="open_data(scope.row)">查看</el-button>
                    </template>

                    <!-- 小户人数 -->
                    <template #smallHolders="scope">
                        <span v-if="scope.row.userNumMin != null">{{ scope.row.userNumMin }}</span>
                        <el-button type="text" size="small" v-else @click="open_data(scope.row)">查看</el-button>
                    </template>

                    <!-- 主力 非主力寄售 -->
                    <template #mainAndNonMainConsignment="scope">
                        <span v-if="scope.row.u4OnSaleGoodsCount != null && scope.row.u0OnSaleGoodsCount != null">{{
                            scope.row.u4OnSaleGoodsCount + ' / ' + scope.row.u0OnSaleGoodsCount }}</span>
                        <el-button type="text" size="small" v-else @click="open_data(scope.row)">查看</el-button>
                    </template>

                    <template #action="scope">
                        <el-button type="text" size="small" @click="MidhandleEdit(scope.row)">修改</el-button>
                    </template>
                </common-table>
                <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
                    <common-pagination ref="commonPagination" :page.sync="page2" @change="getSecond">
                    </common-pagination>
                </div>

                <el-dialog title="非固定提限价类修改" :visible.sync="dialogVisible2">
                    <common-form :submit="submit2" :data="formData2" :schema="formSchema2" label-width="300px">
                        <!-- tradeTime -->
                        <!-- <template #tradeTime="scope">
                            <el-input style="width: 100px;" v-model="formData.minTradeTime"></el-input> - <el-input
                                style="width: 100px;" v-model="formData.maxTradeTime"></el-input>
                        </template> -->
                    </common-form>
                </el-dialog>
            </el-tab-pane>