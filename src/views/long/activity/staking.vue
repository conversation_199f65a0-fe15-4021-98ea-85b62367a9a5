<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showReset="false"
            :show-export="true" @onExport="tableExport"></common-query>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
            <!-- reward -->
            <template #reward="scope">
                <!-- 循序reward数组，显示title * num + title * num -->
                <!-- <div v-if="scope.row.reward.length > 0"> -->
                    <div v-for="(item, index) in (scope.row.reward)" :key="index">
                        <span>{{ item.title + ' * ' +  item.num }}</span><br>
                    </div>
                <!-- </div> -->
            </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
            </common-pagination>
        </div>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonPagination from '@/components/CommonPagination'
import { backgroundList, bodyList, clothList, emojiList, headList } from '../data.json'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: 'PFPshop',
    components: {
        CommonQuery,
        CommonTable,
        CommonPagination
    },
    props: {},
    data() {
        return {
            query1: {
            },
            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '用户手机号：',
                    placeholder: '用户手机号',
                    field: 'phone'
                },
                {
                    type: 'input',
                    label: 'token ID：',
                    placeholder: 'token ID',
                    field: 'tid'
                },
                {
                    type: 'input',
                    label: '持有人con add：',
                    placeholder: '请输入持有人con add',
                    field: 'contractAddress'
                },
                {
                    type: 'input',
                    label: '持有人昵称：',
                    placeholder: '请输入持有人昵称d',
                    field: 'nickname'
                },
                {
                    type: 'select',
                    label: '背景：',
                    placeholder: '',
                    field: 'backgroundList',
                    multiple: true,
                    options: [
                        ...backgroundList
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '身体：',
                    placeholder: '',
                    field: 'bodyList',
                    multiple: true,
                    options: [
                        ...bodyList
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '衣服：',
                    placeholder: '',
                    field: 'clothList',
                    multiple: true,
                    options: [
                        ...clothList
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '表情：',
                    placeholder: '',
                    field: 'emojiList',
                    multiple: true,
                    options: [
                        ...emojiList
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '头饰：',
                    placeholder: '',
                    field: 'headList',
                    multiple: true,
                    options: [
                        ...headList
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'datetimerange',
                    label: '质押时间：',
                    placeholder: '请输入质押时间',
                    field: 'startTimeMin',
                    field2: 'startTimeMax',
                },
                {
                    type: 'datetimerange',
                    label: '质押到期时间：',
                    placeholder: '请输入质押到期时间',
                    field: 'endTimeMin',
                    field2: 'endTimeMax',
                },
            ],
            tableSchema: [ // 表格架构
                {
                    label: '作品tid',
                    field: 'tid'
                },

                {
                    label: '暴躁龙背景',
                    field: 'background',
                },
                {
                    label: '暴躁龙身体',
                    field: 'body',
                },
                {
                    label: '暴躁龙衣服',
                    field: 'cloth',
                },
                {
                    label: '暴躁龙表情',
                    field: 'emoji',
                },
                {
                    label: '暴躁龙头饰',
                    field: 'head',
                },
                {
                    label: '用户昵称',
                    field: 'nickname',
                },
                {
                    label: '手机号',
                    field: 'phone',
                },
                {
                    label: '用户地址',
                    field: 'contractAddress',
                },
                {
                    label: '质押时间',
                    field: 'startTime',
                },
                {
                    label: '质押到期时间',
                    field: 'endTime',
                },
                {
                    label: '质押奖励 * 数量',
                    slot: 'reward',
                },
                // {
                //     label: '状态',
                //     field: 'onSaleStatus',
                // },
            ],
            tableData: [{}],
            page: {
                totalCount: 0,
                pageSize: 10,
                pageNum: 1
            }, // 分页数据
            query: {
                dutyType: ""
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            // this.query = data
            this.getList(true)
        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 创建活动
        toFormPage(item = {}, activityType) {
            this.dialogVisible = false
            console.log(item)
            const typeMap = {
                INVITE_NEW: 'ActivityPullNew',
                REBUILD: 'ActivityRebuild',
                GET_REWARD: 'ActivityPullNew',
                MERGE: 'ActivityPullNew'
            }
            this.$router.push({
                name: item.type ? typeMap[item.type] : typeMap[this.selected],
                query: {
                    type: item.type || this.selected,
                    activityNo: item.activityNo,
                    activityType
                }
            })
        },
        // 获取列表
        async getList() {
            const params = {
                ...this.query1,
                ...this.page,
                backgroundList: JSON.stringify(this.query.backgroundList),
                bodyList: JSON.stringify(this.query.bodyList),
                clothList: JSON.stringify(this.query.clothList),
                emojiList: JSON.stringify(this.query.emojiList),
                headList: JSON.stringify(this.query.headList)
            }
            const {
                status,
                result
            } = await this.$api.getPledgeList(params)
            if (status.code === 0) {
                this.tableData = result.list
                this.tableData.forEach(item => {
                    item.reward = JSON.parse(item.reward);
                });
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
            }
        },
        async clearRedis(item) {
            const {
                status
            } = await this.$api.clearRedis({
                activityNo: item.activityNo,
                activityType: item.type
            })
            if (status.code === 0) {
                this.$message.success(status.msg)
            }
        },
        // 删除数据
        async deleteItem(item) {
            if (item.isCanEdit === 0) {
                this.$message.error("不可删除！！！！")
            } else {
                const {
                    status
                } = await this.$api.dutyDelete({
                    dutyId: item.dutyId,
                    dutyType: item.dutyType
                })
                if (status.code === 0) {
                    this.$message.success(status.msg)
                    this.getList()
                }
            }
        },
        nav_add() {
            this.$router.push({
                name: 'batchAddupdate'
            })
        },
        nav_update(item) {
            this.$router.push({
                name: 'batchAddupdate',
                query: {
                    dutyId: item.dutyId,
                    type: 'details',
                    dutyType: item.dutyType
                }
            })
        },
        // 导出结果
        async tableExport(item, type) {
            const params = {
                ...this.query,
                ...this.page,
                backgroundList: JSON.stringify(this.query.backgroundList),
                bodyList: JSON.stringify(this.query.bodyList),
                clothList: JSON.stringify(this.query.clothList),
                emojiList: JSON.stringify(this.query.emojiList),
                headList: JSON.stringify(this.query.headList)
            }
            const res = await this.$api.getPledgeExport(params)
            if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then(buffer => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '暴躁龙质押记录')
                this.$message.success('导出成功')
            }
        },
    }
}
</script>

<style lang="scss" scoped></style>