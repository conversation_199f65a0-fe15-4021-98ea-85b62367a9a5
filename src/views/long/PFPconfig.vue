<template>
	<d2-container class="page">
		<h4>邀新宝石</h4>
		<common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
			<template #action="scope">
				<el-button @click="nav_set(scope.row)" type="text">修改</el-button>
			</template>
			<template #updateAt="scope">
				{{ removeMilliseconds(scope.row.updateAt) }}
			</template>
		</common-table>


		<h4>联盟返佣</h4>
		<common-table :table-schema="tableSchemaLM" :showIndex="false" :table-data="tableDataLM">
			<template #num="scope">
				{{ scope.row.num * 100 }}%
			</template>
			<template #updateAt="scope">
				{{ removeMilliseconds(scope.row.updateAt) }}
			</template>
			<template #action="scope">
				<el-button @click="nav_setLM(scope.row)" type="text">修改</el-button>
			</template>
		</common-table>
	</d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonPagination from '@/components/CommonPagination'
export default {
	name: 'PFPconfig',
	components: {
		CommonTable,
		CommonPagination
	},
	props: {},
	data() {
		return {
			tableDataLM: [],
			tableSchemaLM: [
				{
					label: '名称',
					field: 'name'
				},
				{
					label: '平台手续费百分比',
					slot: 'num',
				},
				{
					label: '修改时间',
					slot: 'updateAt',
				},
				{
					label: '修改',
					slot: 'action',
					// headerSlot: 'action-header',
				}
			],
			page: {
				totalCount: 0,
				pageSize: 10
			}, // 分页数据
			tableSchema: [ // 表格架构
				{
					label: '名称',
					field: 'name'
				},
				{
					label: '宝石数值',
					field: 'num',
				},
				{
					label: '修改时间',
					slot: 'updateAt',
				},
				{
					label: '修改',
					slot: 'action',
					// headerSlot: 'action-header',
				}
			],
			tableData: [{}],
		}
	},
	mounted() {
		this.getList()
		this.getLM()
	},
	methods: {
		removeMilliseconds(dateTimeString) {
			return dateTimeString.split('.')[0];
		},
		nav_setLM(item) {
			//我需要把item.num传入输入框 才能修改
			this.$prompt('请输入你要修改的数值', '修改数值', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputValue: item.num
			}).then(({ value }) => {
				this.submit2(value, item.type)
			}).catch(() => {

			});
		},
		// 修改值
		async submit2(value, type) {
			const {
				status,
				result
			} = await this.$api.bzlGroupConfigUpdate({
				num: value,
				type
			})
			if (status.code === 0) {
				this.getLM()
				this.$message.success('修改成功')
			}
		},
		// 过滤查询
		onQueryChange(data) {
			this.query = data
			this.getList(true)
		},
		// 分页改变
		currentChange(value) {
			this.page.pageNum = value
			this.getList()
		},
		async getLM() {
			let res = await this.$api.bzlGroupConfig({})
			if (res.status.code === 0) {
				this.tableDataLM = res.result
			}
		},
		// 获取列表
		async getList(isInit) {
			const {
				status,
				result
			} = await this.$api.bzlStoneConfigList({})
			if (status.code === 0) {
				this.tableData = result
			}
		},
		// 获取列表
		async submit(value, type) {
			const {
				status,
				result
			} = await this.$api.bzlStoneConfigUpdate({
				num: value,
				type
			})
			if (status.code === 0) {
				this.getList()
				this.$message.success('修改成功')
			}
		},
		nav_set(item) {
			//我需要把item.num传入输入框 才能修改
			this.$prompt('请输入你要修改的数值', '修改数值', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				inputValue: item.num
			}).then(({ value }) => {
				this.submit(value, item.type)
			}).catch(() => {

			});
		},
	}
}
</script>

<style lang="scss" scoped></style>
