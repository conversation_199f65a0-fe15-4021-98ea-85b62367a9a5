<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="APP分享入群二维码" >
        <el-form ref="form" :model="formData"  size="mini"  label-width="150px">
          <el-form-item label="官方渠道购买链接:">
            <el-input v-model="formData.url" style="width:300px" placeholder="请输入抖音店铺名称"></el-input>
          </el-form-item>
          <el-form-item label="抖音店铺名称:">
            <el-input v-model="formData.title" style="width:300px" placeholder="请输入抖音店铺名称"></el-input>
          </el-form-item>
          <el-form-item label="抖音店铺链接:">
            <el-input v-model="formData.douyinUrl" style="width:300px" placeholder="请输入抖音店铺链接"></el-input>
          </el-form-item>
          <el-form-item label="抖音店铺头像:">
            <ImgUploader :value.sync="formData.portrait" @success="succe1"></ImgUploader>
            <p>最近替换时间:{{formData.portraitTime}}</p>
          </el-form-item>
          <el-form-item label="社群二维码:">
            <ImgUploader :value.sync="formData.qrCode" @success="succe2"></ImgUploader>
            <p>最近替换时间:{{formData.qrCodeTime}}</p>
          </el-form-item>
          <el-form-item label="客服二维码:">
            <ImgUploader :value.sync="formData.serviceQrCode" @success="succe3"></ImgUploader>
            <p>最近替换时间:{{formData.serviceQrCodeTime}}</p>
          </el-form-item>
           <el-form-item>
              <el-button type="primary" @click="submit">提交</el-button>
            </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </d2-container>
</template>

<script>
  import util from '@/libs/util.js'
  import CommonForm from "@/components/CommonForm";
  import ImgUploader from "@/components/ImgUploader";
  export default {
    key: 'PFPshare',
    data() {
      return {
        formData: {
          title: "",
          portrait: "",
          portraitTime: "",
          qrCode: "",
          qrCodeTime: "",
          serviceQrCode: "",
          serviceQrCodeTime: "",
          url:""
        },
      }
    },
    mounted() {
      this.get()
    },
    components: {
      ImgUploader
    },
    methods: {
      // 查询
      async get() {
        const {
          result,
          status
        } = await this.$api.getValueByName({
          name: 'longShareInfo'
        })
        if (status.code === 0) {
          this.remark = result.remark
          this.key = result.name
          this.formData = JSON.parse(result.value)
        } else {
          this.$message.error(status.msg);
        }
      },
      succe1() {
        this.formData.portraitTime = this.getCurrentTime()
      },
      succe2() {
        this.formData.qrCodeTime = this.getCurrentTime()
      },
      succe3() {
        this.formData.serviceQrCodeTime = this.getCurrentTime()
      },
      getCurrentTime() {
        const now = new Date(); // 获取当前时间

        // 获取各个时间部分
        const year = now.getFullYear(); // 年
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月（注意：月份从0开始，所以要加1）
        const day = String(now.getDate()).padStart(2, '0'); // 日
        const hours = String(now.getHours()).padStart(2, '0'); // 时
        const minutes = String(now.getMinutes()).padStart(2, '0'); // 分
        const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒

        // 格式化为“年月日时分秒”
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      async submit() {
        console.log(this.formData)
        const {
          result,
          status
        } = await this.$api.saveOrUpdate({
          name: this.key,
          value: JSON.stringify(this.formData),
          tips:  '暴躁龙分享配置'
        })
        if (status.code === 0) {
          this.get()
          this.$message.success('操作成功');
        } else {
          this.$message.error(status.msg);
        }
      },

    },
  }
</script>
<style>
  p{
    margin:0px;
    font-size:14px;
  }
  .msg_red {
    margin-left: 10px;
  }
</style>
