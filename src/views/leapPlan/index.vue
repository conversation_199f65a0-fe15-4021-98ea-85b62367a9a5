<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户名称">
        <el-input
          v-model="formInline.nickname"
          placeholder="请输入用户名称"
        ></el-input>
      </el-form-item>
	  <el-form-item label="用户地址">
	    <el-input
	      v-model="formInline.address"
	      placeholder="请输入用户地址"
	    ></el-input>
	  </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getquery()">查询</el-button>
        <el-button type="primary" @click="orderexport()">清除</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      v-loading="loading"
      element-loading-text="正在导出"
    >
      <el-table-column
        fixed
        prop="userId"
        label="id"
        type="selection"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="nickname"
        label="账号昵称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="address"
        label="用户地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="goodsNum"
        label=" 作品"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="collectionNum"
        label="藏品"
        align="center"
      ></el-table-column>
      <el-table-column prop="cover" label="头像" align="center">
        <template scope="scope">
          <div style="width: 100%" @click="ddd(scope.row.avatar)">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.avatar"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="balance"
        label="账户余额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="gas"
        label="燃料卡"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="creator"
        label="创作者／团队"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mobPhone"
        label="联系方式"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="applyTime"
        label="申请时间"
        align="center"
      ></el-table-column>
	  <el-table-column
	    prop="systemFeeFirstRadio"
	    label="分成比例"
	    align="center"
	  ></el-table-column>
	  <el-table-column fixed="right" label="操作" width="120" align="center">
	    <template slot-scope="scope">
	      <el-button type="text"  @click="open_model(scope.row)">修改分成比例</el-button>
	    </template>
	  </el-table-column>
    </el-table>
    <div
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        :current-page="current_page"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="修改分成比例" :visible.sync="isUpdata" width="30%">
      <el-form label-position="right" ref="form" :model="proportionForm" label-width="150px">
		<el-form-item label="账号昵称">
			<span>{{proportionForm.name}}</span>
		</el-form-item>
		<el-form-item label="当前首发比例分成">
			<span>{{proportionForm.proportion}}</span>
		</el-form-item>
		<el-form-item label="修改后首发分成比例">
		  <el-input v-model="proportionForm.proportioValue" placeholder="请输入0-20整数"></el-input>
		</el-form-item>
	 </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isUpdata = false">取 消</el-button>
        <el-button type="primary" @click="submut_modifyInfo()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import { downloadBlob } from '@/utils/helper'

export default {
  name: 'order',
  data () {
    return {
      fileList: [],
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      form: {},
      dialogVisible: false,
      cancelDialogVisible: false,
      tableData: [],
      srcList: [],
      total: 1,
      current_page: 1,
      radio1: 'ACCOUNT_CANCEL_SUCCESS', // 1 同意  2 拒绝
      textarea: '手动录入', // 文本
      textarea1: '', // 文本
      options: [
        {
          value: '手动录入',
          label: '4'
        }
      ],
      isPupop: false, // 拒绝文本控制
      isPupops: false,
      state: 0,
      formInline: {
        nickname: null, 
		address:null,
        beginTime: null,
        endTime: null
      },
      isAuthority: false,
      isUpdata: false,
      idlist: [],
      deleteid: '',
      isimgDelete: false,
      imgurl: '',
      scrollTop: 0,
      goodsSynopsis: '',
      goodsDesc: '',
      remark: '', // 备注信息
      recordId: null, // id
      isDialogReson: false, // 驳回原因
      reason: '', // 驳回原因
      loading: false,
	  proportionForm:{
		  name: '',
		  proportion:'',
		  proportioValue:''
	  },
	  uid:""
    }
  },
  computed: {
    /**
     * 搜索栏创建时间
     */
    createAt: {
      get () {
        const { beginTime, endTime } = this.formInline
        if (beginTime && endTime) { return [beginTime, endTime] } else { return [] }
      },
      set (val) {
        Object.assign(this.formInline, {
          beginTime: val?.[0] + ".000",
          endTime: val?.[1]+ ".000"
        })
		return [this.formInline.beginTime,this.formInline.endTime]
		console.log(this.formInline)
      }
    }
  },
  mounted () {
    this.getSelete(1)
    this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
      this.scrollTop = res.target.scrollTop
      console.log(res.target.scrollTop)
    })
  },

  activated () {
    this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
  },
  methods: {
    credited (row) {
      this.form.orderNo = row.orderNo
      this.dialogVisible = true
    },
    async submit () {
      console.log(this.form)
      const {
        status: { code, msg }
      } = await this.$api.rmbComplete(this.form)
      if (!code) {
        this.$message.success(msg)
        this.getquery()
        this.dialogVisible = false
      } else {
        this.$message.error(msg)
      }
    },
    async closeOrder (row) {
      this.cancelDialogVisible = true
      this.form.orderNo = row.orderNo
    },
    async confirmToClose () {
      const {
        status: { code, msg }
      } = await this.$api.rmbCancel({
        orderNo: this.form.orderNo
      })
      if (!code) {
        this.getquery()
        this.cancelDialogVisible = false
        this.$message.success(msg)
      } else {
        this.$message.error(msg)
      }
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.payAnnexFileUrl = res.result.url
    },
    handleIntroduceUploadHide (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    beforeAvatarUpload (file) {
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.form.icon = ''
    },
    // 大图
    ddd (e) {
      this.isimgDelete = true
      this.imgurl = e
      console.log(e)
    },
    // 查询
    getquery () {
      this.getSelete(1)
    },
    // 拒绝理由下拉框
    change () {
      console.log(this.textarea)
      if (this.textarea === '4') {
        console.log('我是手动输入')
        this.isPupops = true
      } else {
        this.isPupops = false
      }
    },
    // 导出
    async batchExport () {
      this.loading = true
      const res = await this.$api.orderlistExport(this.formInline)
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.loading = false
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '订单' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.loading = false
        this.getList()
      }
    },
    async getSelete (page) {
      this.current_page = page
      const res = await this.$api.leapPlanUserList({
        pageNum: page,
        pageSize: 15,
        ...this.formInline
      })
      if (res.status.code === 0) {
        if (res.result == null) {
          this.$message.error('无搜索结果')
        } else {
          this.tableData = res.result.list
          console.log(this.tableData)
          this.total = res.result.totalCount
        }
      } else if (res.status.code === 1002) {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 审核
    async check () {
      this.recordId = parseInt(this.recordId)
      if (this.textarea1 === '' || this.textarea1 == null) {
        this.textarea1 = null
      }
      const res = await this.$api.check({
        recordId: this.recordId,
        checkStatus: this.radio1,
        reason: this.textarea1 // 原因
      })
      console.log(res)
      if (res.status.code === 0) {
        this.isAuthority = false
        this.$message.success(res.status.msg)
        this.textarea1 = ''
        await this.getSelete(1)
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 驳回原因
    async rejectReason () {
      this.recordId = parseInt(this.recordId)
      const res = await this.$api.rejectReason({
        recordId: this.recordId
      })
      if (res.status.code === 0) {
        this.isDialogReson = true
        this.reason = res.result.reason
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 批量审核
    async getpeopleVerify (e) {
      console.log(e)
      e = e.toString()
      const res = await this.$api.peopleVerify({
        ids: e,
        peopleVerifyStatus: this.radio1,
        rejectExplain: this.textarea1
      })
      if (res.status.code === 0) {
        this.isAuthority = false
        this.$message.success(res.status.msg)
        this.textarea1 = ''
        await this.getSelete(1)
      } else {
        this.$message.error(res.status.msg)
      }
    },
    to_isDelete (e) {
      console.log(e)
      this.remark = ''
      this.recordId = e.id
      this.isDelete = true
    },
    to_isDialogReson (e) {
      this.recordId = e.id

      this.rejectReason()
    },
    // 备注
    async to_remark () {
      console.log()
      const res = await this.$api.remark({
        recordId: this.recordId,
        remark: this.remark // 备注内容
      })
      if (res.status.code === 0) {
        this.getSelete(1)
        this.isDelete = false
        this.$message.success('备注添加成功')
      } else {
        this.$message.error(res.status.msg)
      }
    },
    selete () {
      console.log(this.formInline)
    },
    // 分页
    xuanze (val) {
      this.getSelete(val)

      this.$refs.multipleTable.bodyWrapper.scrollTop = 0
    },

    // 批量选择
    handleSelectionChange (val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 清除
    orderexport () {
      this.formInline = {
        orderNo: null, // 订单号
        tid: null, // 作品tid
        buyerNickname: null, // 买方昵称
        buyerId: null, // 买方用户id
        sellerNickname: null, // 卖方昵称
        sellerId: null, // 卖方用户id
        status: null, // 订单状态0未付款 1已付款 2已发货 3已签收 4退货申请 5退货中 6已退货 7取消交易 8交易成功
        paymentScene: null, // 订单支付场景 1-H5 2-PC 3-IOS 4-Android 5-H5的PC
        payMethod: null, // 订单支付方式付款方式:1余额,2微信,3支付宝,4云闪付,5银行卡,6赠送,7五虎赠送,8流水,9苹果10余额支付 20 合成
        mold: null // 订单类型 1-作品 4-燃料
      }
      this.getSelete(1)
    },
    // 批量审核
    batch_audit () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length >= 1) {
        this.state = 1
        this.isAuthority = true
      } else {
        this.$message.error('请选择作品')
      }
    },
    // 拒绝原因弹出框
    pupop () {
      if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
        this.isPupops = false
        this.isPupop = false
      } else {
        this.isPupop = true
        this.isPupops = true
      }
    },
    // 跳转详情
    nav_details (item) {
      console.log(item)
      // this.$refs.multipleTable.bodyWrapper.addEventListener('scroll',(res) =>{
      // this.scrollTop = res.target.scrollTop
      // console.log(res.target.scrollTop)
      // })
      this.$router.push({
        name: 'works_details',
        query: {
          id: item.tid
        }
      })
    },
    open_model(item){
		this.uid=item.userId
		this.isUpdata=true
		this.proportionForm.name=item.nickname
		this.proportionForm.proportion=item.systemFeeFirstRadio
	},
	async submut_modifyInfo () {
		if(this.proportionForm.proportioValue>20&&this.proportionForm.proportioValue!=0){
			 this.$message.error("请输入0-20整数")
		}else{
			const res = await this.$api.modifyInfo({
			  userId: this.uid,
			  systemFeeFirstRadio : this.proportionForm.proportioValue // 备注内容
			})
			if (res.status.code === 0) {
			  this.getSelete(1) 
			  this.isUpdata = false
			} else {
			  this.$message.error(res.status.msg)
			}
		}
	 
	},
  }
}
</script>
<style lang="scss" scoped>
.action {
  .el-button {
    width: 130px;
    margin: 10px 0;
  }
}
.content {
  .item {
    display: flex;
    margin-bottom: 20px;
    .title {
      width: 100px;
    }
  }
}
.tag {
  margin: 0px 15px 15px 0px;
  cursor: pointer !important;
}
</style>
