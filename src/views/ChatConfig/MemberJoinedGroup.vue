<template>
  <d2-container class="page">
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 头像 -->
            <template v-if="item.prop === 'groupIcon'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <!-- 持有群主作品 -->
            <template v-else-if="item.prop === 'holder'">
              <el-button type="text"  @click="holder(scope.row)">查看</el-button>
            </template>
            <!-- 管理／加入的群-->
            <template v-else-if="item.prop === 'groupStats'">
              <el-button type="text" >{{ scope.row[item.prop] }}</el-button>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'MemberJoinedGroup',
  computed: {
    memberId () {
      return this.$route.query.memberId
    }
  },
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'groupId',
          label: '群聊ID'
        },
        {
          prop: 'groupName',
          label: '群聊名称'
        },
        {
          prop: 'groupIcon',
          label: '群头像'
        },
        {
          prop: 'attribute',
          label: '群内身份'
        },
        {
          prop: 'memberStats',
          label: 'DAO成员／群成员'
        },
        {
          prop: 'createAt',
          label: '群创建时间'
        },
        {
          prop: 'status',
          label: '群聊状态'
        }
      ],
      pageNum: 1,
      pageSize: 15,
      total: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const {
        memberId,
        pageNum,
        pageSize
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.manageOrJoin({
        memberId,
        pageNum,
        pageSize
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 持有群主作品
     * @param row {Object} 当前行数据
     */
    holder (row) {
      this.$router.push({
        name: 'MemberGood',
        query: {
          groupId: this.groupId,
          memberId: row.memberId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
