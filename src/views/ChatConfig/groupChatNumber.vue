<template>
  <d2-container class="page" ref="returnTop">
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_task()">添加任务</el-button>
      </template>
     <!-- <template #action="scope">
        <el-button @click="setSellStop(scope.row.dutyId)" type="text">终止任务</el-button>
      </template> -->
      <template #newNum="scope">
        <el-button type="text"  @click="getGoodsAmount(scope.row)" v-if="scope.row.memberNum==null">
          查看
        </el-button>
         <div v-elses>{{scope.row.memberNum}}</div>
      </template>
      <template #remark="scope">
        <div class="oneOver" @click="openText(scope.row.remark)">{{scope.row.remark}}</div>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    dutyList,
  } from '@/api/mallCenter'
  export default {
    name: 'group_number',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'dutyId',
            width: '任务编号',
            width: '40px'
          },
          // {
          //   label: '创建时间',
          //   field: 'createAt',
          //   width: '120px'
          // },
          {
            label: '群聊名称',
            field: 'groupName',
            width: '200px'
          },
          {
            label: '改动前人数',
            field: 'startNum',
            width: '100px'
          },
          {
            label: '变动需求',
            field: 'changeNum',
            width: '100px'
          },
          {
            label: '需要时间',
            field: 'changeTime',
            width: '100px'
          },
          {
            label: '改动后人数',
            field: 'endNum',
            width: '100px'
          },
          {
            label: '当前最新人数',
            slot: 'newNum',
            width: '120px'
          },
          {
            label: '开始时间',
            field: 'startTime',
            width: '160px'
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'error'
              },
            },
            width: '80px',
          },
          {
            label: '备注',
            slot: 'remark',
            width: '100px',
          },
          {
            label: '操作',
            headerSlot: 'action-header',
            width: '100px',
          }
        ],
        tableData: []
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_task() {
        this.$router.push({
          name: 'group_number_add',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'GROUP_MEMBER_NUM_CHANGE'
        }
        let dataList = []
        dutyList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.groupMemberNumChangeExtra,
              dutyId: item.dutyId,
              dutyStatus: item.dutyStatus,
              startTime:item.startTime,
              remark:item.remark
            })
          })
          this.tableData = dataList
          console.log(this.tableData)
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      setSellStop(id) {
        this.$confirm(`确定终止该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          tradeStop({
            dutyId: id,
            dutyType: 'AUTO_TRADE'
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      //查看群聊最新人数
      async getGoodsAmount(item) {
        const res = await this.$api.groupMemberNum({
          groupId:item.groupId
        })
        if (res.status.code === 0) {
         item.memberNum=res.result.memberNum
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
