<template>
  <d2-container class="page">
   <div class="list">
     <div class="item" v-for="(item,index) in list" :key="index">
       <el-image
         style="width: 200px; height: 200px; margin-right: 4px;"
         :src="item.cover"
         :preview-src-list="[item.cover]">
       </el-image>
       <div class="title">
         {{item.title}}
       </div>
       <div class="time">
         {{item.holdTime}}
       </div>
     </div>
   </div>
  </d2-container>
</template>

<script>
export default {
  name: 'MemberGood',
  data () {
    return {
      list: [],
      pageNum: 1,
      pageSize: 100
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getList () {
      const { pageNum, pageSize, $route } = this
      const { result: { list } } = await this.$api.memberGoods({
        groupId: $route.query.groupId,
        memberId: $route.query.memberId,
        pageNum,
        pageSize
      })
      this.list = list
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 30px;
  }
}
</style>
