<template>
	<d2-container class="page" ref="returnTop">
    <div style="margin-bottom:10px">
      <el-button type="primary" size="mini" @click="getList()">刷新页面</el-button>
    </div>
		<common-table :table-schema="tableSchema" :table-data="tableData">
			<template #action-header>
				<el-button type="primary" size="mini" @click="openEdit()">替换二维码</el-button>
			</template>
      <template #action="scope">
      	<el-button type="text" size="mini" @click="openEdit(scope.row)">编辑</el-button>
        <el-button type="text" size="mini" @click="qrCodeDel(scope.row)">删除</el-button>
      </template>
		</common-table>
    <div class="footer">
      <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
         :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
          style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="xuanzeSize">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="替换二维码" :visible.sync="isDialogVisible" width="600px" center @close="closeDialog" destroy-on-close>
    	<CommonForm :schema="formSchema" :data="formData" :submit="submit" label-width="150px">
    	</CommonForm>
    </el-dialog>
	</d2-container>

</template>
<script>
	import CommonQuery from '@/components/CommonQuery'
	import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
	import {
		qrcodeList,
		qrcodeAdd,
    qrcodeEdit,
    qrcodeDelete
	} from '@/api/queqiao'

	export default {
		name: 'ReportList',
		components: {
			CommonQuery,
			CommonTable,
      CommonForm
		},
		data() {
			return {
				page: {
					totalCount: 0,
					pageSize: 20,
					pageNum: 1
				}, // 分页数据
				query: {
					autherName: '',
					autherId: '',
					seoKeywords: '',
					seoTitle: '',
					subTitle: '',
					title: '',
					isAccessOpenPlatform: ''
				},
				tableSchema: [ // 表格架构
					{
						label: '启动日期',
						field: 'startTime',
						width: '220px',
            align:'center'
					},
					{
            type:"img",
						label: '二维码',
						field: 'qrcode',
						width: '300px',
            align:'center'
					},
					{
						label: '备注',
						field: 'remark',
            align:'center',
            showOverflowTooltip:true
					},
         {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '150px',
          align:'center',
         },
				],
				tableData: [], // 表格数据
        isDialogVisible:false,
        formSchema: [
        	{
        		type: 'img',
        		label: '上传二维码：',
        		placeholder: '请上传二维码',
        		field: 'qrcode',
        		limit: 1,
        		rules: [{
        			required: true,
        			message: '请上传二维码',
        			trigger: 'change'
        		}]
        	},
          {
          	type: 'datetime',
          	label: '开始启动时间：',
          	placeholder: '请输入开始启动时间',
          	field: 'startTime',
          	rules: [{
          		required: true,
          		message: '请输入开始启动时间',
          		trigger: 'change'
          	}]
          },
          {
          	type: 'textarea',
          	label: '备注：',
          	placeholder: '请输入备注',
          	field: 'remark',
          	// rules: [{
          	// 	required: true,
          	// 	message: '请输入备注',
          	// 	trigger: 'change'
          	// }]
          },
        	{
        	  type: 'action',
        	  exclude: ['back']
        	}
        ],
        formData:{
        	startTime:'',
        	qrcode:'',
        	remark:""
        },
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			toTemplatePage(item = {}) {
				// this.$store.commit('SAVE_INFO', item)
				// mapActions('save_stateInfo', item)
        if(item.businessType&&item.businessType!=0){
          this.$router.push({
          	name: 'NoticeEdit',
          	query: {
          		templateId: item.id,
              businessType:item.businessType
          	}
          })
        }else{
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
          	name: 'platformPublish',
          	query: {
          		templateId: item.id
          	}
          })
        }

			},
			scrollEvent(e) {
				console.log(e.y) // 获取目标元素的滚动高度
			},
			// 过滤查询
			onQueryChange(data) {
				this.query = data
				this.getList(true)
			},
      // 分页改变
      xuanzeSize(value) {
      	this.page.pageSize = value
      	this.getList()
      },
			// 分页改变
			currentChange(value) {
				this.page.pageNum = value
				this.getList()
			},
			getList() {
				const params = {
					...this.page,
				}
				qrcodeList(params).then(res => {
					const data = res.result
					this.tableData = data.list
					this.page.totalCount = data.totalCount
					this.page.pageSize = data.pageSize
					this.page.pageCount = data.pageCount
				})
			},
			// 删除二维码
			qrCodeDel(item) {
				this.$confirm(`确定删除该条微信二维码吗？`, '', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
          qrcodeDelete({
            id:item.id
          }).then(res => {
            this.$message.success('删除成功');
          	this.getList()
          })
				})
			},
      openEdit(item){
        console.log(item)
        this.isDialogVisible=true
        if(item){
          this.formData.qrcode=item.qrcode
          this.formData.remark=item.remark
          this.formData.startTime=item.startTime
          this.formData.id=item.id
          this.isEdit=true
        }else{
          this.isEdit=false
        }
      },
    submit() {
    	const params = {
    		...this.formData,
    	}
      if(this.isEdit){
        qrcodeEdit(params).then(res => {
          this.isDialogVisible=false
           this.$message.success('修改成功');
          this.getList()
        })
      }else{
        qrcodeAdd(params).then(res => {
           this.isDialogVisible=false
           this.formData={
            startTime:'',
            qrcode:'',
            remark:""
          }
           this.$message.success('替换成功');
          this.getList()
        })
      }

    },
    closeDialog(){
      this.isDialogVisible=false
      this.formData={
        startTime:'',
        qrcode:'',
        remark:""
      }
    }
		}
	}
</script>

<style lang="scss" scoped>

</style>
