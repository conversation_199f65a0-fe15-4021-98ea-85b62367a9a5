<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
        <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
          <el-select  v-model="formInline[item.prop]" v-if="item.type === 'select'">
            <el-option :label="v.label" :value="v.value" v-for="(v,i) in item.options" :key="i"></el-option>
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'dateRange'"
            v-model="formInline[item.prop]"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input v-model="formInline[item.prop]" v-else></el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 头像 -->
            <template v-if="item.prop === 'avatar' || item.prop === 'memberAvatar'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <!-- 成员属性-->
            <template v-else-if="item.prop === 'attribute'">
              <el-tag>{{ scope.row[item.prop] }}</el-tag>
            </template>
            <!-- 会员类别-->
            <template v-else-if="item.prop === 'member'">
              <el-tag v-if="scope.row[item.prop] === 0">普通用户</el-tag>
              <el-tag v-if="scope.row[item.prop] === 4">一级用户</el-tag>
            </template>
            <!-- 持有群主作品 -->
            <template v-else-if="item.prop === 'holder'">
              <el-button type="text"  @click="holder(scope.row)">查看</el-button>
            </template>
            <!-- 发言记录 -->
            <template v-else-if="item.prop === 'record'">
              <el-button type="text"  @click="chatHistory(scope.row)">查看</el-button>
            </template>
            <!-- 管理／加入的群-->
            <template v-else-if="item.prop === 'groupStats'">
              <el-button type="text"  @click="memberJoinedGroup(scope.row)"> {{ scope.row[item.prop] }}</el-button>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80">
          <template slot-scope="scope">
            <el-button type="text"  @click="onAction(scope.row)">操作</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog
      title="群聊操作"
      :visible.sync="actionDialogVisible"
      width="30%">
      <div class="dialog-action">
        <div>选择确认后，在客户端将以伯德护卫机器人身份对该用户进行请出群聊或全群拉黑。</div>
        <el-radio-group v-model="operate">
          <template v-if="attribute !== '群主'">
            <el-radio :label="11">本群内禁言</el-radio>
            <el-radio :label="12">请出该群聊</el-radio>
            <el-radio :label="13">全平台群拉黑</el-radio>
            <el-radio :label="15">取消平台群拉黑</el-radio>
          </template>
          <el-radio :label="11" v-else>本群内禁言</el-radio>
          <el-radio :label="14">取消成员禁言</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="actionDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="actionConfirm">确 定</el-button>
    </span>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'MemberList',
  computed: {
    groupId () {
      return this.$route.query.groupId
    }
  },
  data () {
    return {
      formInline: {},
      formList: [
        {
          label: '用户地址：',
          prop: 'address',
          type: 'input'
        },
        {
          label: '成员群昵称：',
          prop: 'cardName',
          type: 'input'
        },
        {
          label: '成员账户昵称：',
          prop: 'nickname',
          type: 'input'
        },
        {
          label: '成员属性：',
          prop: 'memberType',
          type: 'select',
          options: [
            {
              label: '群主',
              value: '0'
            },
            {
              label: '管理员',
              value: '1'
            },
            {
              label: '普通成员',
              value: '2'
            },
            {
              label: '机器人',
              value: '3'
            }
          ]
        },
        {
          label: '入群时间：',
          prop: 'dateRange',
          type: 'dateRange'
        },
        {
          label: '群成员状态：',
          prop: 'status',
          type: 'select',
          options: [
            {
              label: '正常',
              value: '1'
            },
            {
              label: '禁言',
              value: '2'
            },
            {
              label: '请出',
              value: '3'
            },
            {
              label: '拉黑',
              value: '4'
            }
          ]
        },
        {
          label: '会员类别：',
          prop: 'member',
          type: 'select',
          options: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '普通用户',
              value: 0
            },
            {
              label: '一级用户',
              value: 4
            }
          ]
        }
      ],
      tableData: [],
      tableList: [
        {
          prop: 'contractAddress',
          label: '用户地址',
          width: '100'
        },
        {
          prop: 'memberName',
          label: '成员群昵称',
          width: '100'
        },
        {
          prop: 'nickname',
          label: '成员账号昵称',
          width: '100'
        },
        {
          prop: 'memberAvatar',
          label: '成员群头像'
        },
        {
          prop: 'avatar',
          label: '成员账号头像'
        },
        {
          prop: 'attribute',
          label: '成员属性',
          width: '100'
        },
        {
          prop: 'member',
          label: '会员类别',
          width: '100'
        },
        {
          prop: 'holder',
          label: '持有群主作品',
          width: '80'
        },
        {
          prop: 'record',
          label: '发言记录',
          width: '80'
        },
        {
          prop: 'groupStats',
          label: '管理／加入的群',
          width: '100'
        },
        {
          prop: 'memberTime',
          label: '入群／离群时间',
          width: '100'
        },
        {
          prop: 'status',
          label: '成员群状态',
          width: '80'
        }
      ],
      pageSize: 15,
      pageNum: 1,
      total: 0,
      memberTypeDict: {
        0: '群主',
        1: '管理员',
        2: '成员',
        3: '机器人'
      },
      actionDialogVisible: false,
      operate: 0,
      memberId: '',
      attribute: ''
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const { dateRange } = this.formInline
      if (dateRange) {
        this.formInline.beginTime = dateRange[0] + '.000'
        this.formInline.endTime = dateRange[1] + '.000'
      }
      const {
        groupId,
        pageNum,
        pageSize
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.groupMemberInfo({
        ...this.formInline,
        groupId,
        pageNum,
        pageSize
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 点击操作
     * @method
     */
    onAction (row) {
      this.memberId = row?.memberId
      this.attribute = row?.attribute
      this.actionDialogVisible = true
    },
    // 操作后点击确定
    async actionConfirm () {
      const {
        groupId,
        operate,
        memberId,
        attribute
      } = this
      /* eslint-disable */
      const attributeDict = {
        '普通成员': 1,
        'DAO成员': 2,
        '群主': 3,
        'DAO群管理员': 4,
        '藏家群': 11,
        'DAO群': 12
      }
      /* eslint-disable */
      await this.$api.memberOperate({
        attribute:attributeDict[attribute],
        memberId,
        groupId,
        operate
      })
      this.$message.success('操作成功')
      this.actionDialogVisible = false
      this.memberId = ''
      await this.getList()
    },
    /**
     * 发言记录
     * @param row {Object} 当前行数据
     */
    chatHistory (row) {
      this.$router.push({
        name: 'GroupChatHistory',
        query: {
          groupId: this.groupId,
          memberId: row.memberId
        }
      })
    },
    /**
     * 持有群主作品
     * @param row {Object} 当前行数据
     */
    holder (row) {
      this.$router.push({
        name: 'MemberGood',
        query: {
          groupId: this.groupId,
          memberId: row.memberId
        }
      })
    },
    /**
     * 管理/加入的群
     * @param row {Object} 当前行数据
     */
    memberJoinedGroup (row) {
      this.$router.push({
        name: 'MemberJoinedGroup',
        query: {
          memberId: row.memberId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;

  .demo-form-inline {
    flex: 1;
  }

  .action {
    width: 200px;
  }
}

.dialog-action {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
