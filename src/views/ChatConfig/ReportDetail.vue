<template>
  <d2-container class="page">
    <div class="info">
      <div>举报对象：{{ info.name }}</div>
      <div>对象属性：{{ info.target }}</div>
      <div>人/群属性：{{ info.attribute }}</div>
      <div>举报时间：{{ info.reportTime }}</div>
      <div>举报类型：{{ info.type }}</div>
      <el-image
        v-for="(item,index) in info.photos"
        :key="index"
        style="width: 100px; height: 100px"
        :src="item"
        :preview-src-list="info.photos">
      </el-image>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'ReportDetail',
  data () {
    return {
      info: {},
      pageNum: 1,
      pageSize: 100
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getList () {
      const {
        pageNum,
        pageSize,
        $route
      } = this
      const { result } = await this.$api.reportDetail({
        reportId: $route.query.reportId,
        pageNum,
        pageSize
      })
      this.info = result
    }
  }
}
</script>

<style lang="scss" scoped>
.info {
  margin-bottom: 20px;

  div {
    margin-bottom: 5px;
  }
}
</style>
