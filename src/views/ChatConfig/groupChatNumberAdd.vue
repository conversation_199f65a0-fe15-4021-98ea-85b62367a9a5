<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #groupName="scope">
        <el-input placeholder="请输入群聊名称" style="width:300px" @keyup.enter.native="search" v-model="formData.groupName">
        </el-input>
        <el-button type="primary" style="margin-left: 10px;" @click="search">搜索</el-button>
        <div class="search" >
          <span >搜索结果</span >
          <div v-if="searchList!=''">
            <el-tag type="" style="margin-right: 10px;cursor: pointer;margin-bottom:5px;"
              v-for="(item,index) in searchList" @click="clickName(item)">{{item.groupName}}</el-tag>
          </div>
          <div v-else style="color:rgb(255, 29, 29)">
            没有搜索结果，请重新输入关键字后重试
          </div>
        </div>
      </template>
      <template #changeNum-msg>
        <div style="color:#F56C6C">
          <div>变动需求数量（正数加人，负数减人）</div>
        </div>
      </template>
      <template #hint>
      	<div style="color:#F56C6C">
      		<div>注：</div>
      		<div>1、群主创作的作品必须要有卖出，用户正常持有该作品；</div>
      		<div>2、作品图片不同的慎用，入群会随机获取群主创作的NFT作为头像，如作品是唯一的可能会被用户看到头像相同的；</div>
          <div>3、退群顺序，优先把通过定时任务进群的小号踢出，然后踢群内已有的小号。踢至群内小号数量不足时任务中断；</div>
      	</div>
      </template>
      <template #floorPrice="scope">
        <el-row justify="start">
          <el-col :span="4">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" type="number" v-model="formData.changeTime"></el-input>
              <span class="danwei">min</span>
            </div>
          </el-col>
        </el-row>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    dutyAdd
  } from '@/api/mallCenter'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'group_number_add',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          changeNum: '',
          groupId: '',
          groupName: '',
          changeTime: '',
          isTiming: 1
        },
        formSchema: [{
            type: 'input',
            label: '群聊名称：',
            placeholder: '请输入群聊名称',
            slot: 'groupName',
            rules: [{
              required: true,
              message: '请输入群聊名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '变动需求：',
            placeholder: '请输入变动需求数量',
            field: 'changeNum',
            rules: [{
              required: true,
              message: '请输入变动需求数量',
              trigger: 'blur'
            }]
          },
          {
            slot: 'changeNum-msg',
            label: '',
          },
          {
            label: '变动时间：',
            placeholder: '请输入变动时间',
            slot: 'floorPrice',
            rules: [{
              required: true,
              message: '请输入变动时间',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '是否需要定时：',
            placeholder: '是否需要定时',
           	field: 'isTiming',
            options: [{
                label: '定时执行',
                value: 1
              },
              {
                label: '立即执行',
                value: 0
              },
            ],
           	rules: [{
              required: true,
            }]
          },
          {
            type: 'datetime',
            label: '执行时间：',
            placeholder: '请选择执行时间',
            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择执行时间',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isTiming',
              value: [1]
            },
          },
          {
            slot: 'hint',
            label: '',
          },
          {
            type: 'action'
          },

        ],
        searchList: {},
        isError: '',
        isSearch:false
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        let startTime = this.formData.startTime
        let extra = JSON.stringify(this.formData)
        const data = {
          dutyType: 'GROUP_MEMBER_NUM_CHANGE',
          startTime,
          isTiming:this.formData.isTiming,
          extra
        }
        console.log(data)
        // }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.setSellAdd(data);
          if (res.status.code == 0) {
            this.routerBack()
          }
        })
      },
      async search() {
        this.isSearch=true
        let res = await this.$api.groupChatList({
          groupName: this.formData.groupName
        });
        if (res.status.code == 0) {
          this.searchList = res.result.list
        }
      },
      clickName(item) {
        this.formData.groupName = item.groupName
        this.formData.groupId = item.groupId
         this.isSearch=false
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid: this.formData.ctid,
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
