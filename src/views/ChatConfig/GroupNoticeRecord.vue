<template>
  <d2-container class="page">
    <div class="table">
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 'auto'">
          <template slot-scope="scope">
            <!--聊天内容-->
            <template v-if="item.prop === 'noticePic'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'GroupNoticeRecord',
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'releaseTime',
          label: '发布时间',
          width: '180'
        },
        {
          prop: 'content',
          label: '文字内容'
        },
        {
          prop: 'noticePic',
          label: '图片内容',
          width: '200'
        }
      ],
      total: 0,
      pageNum: 1,
      pageSize: 50
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取聊天记录
     * @method
     */
    async getList () {
      const { pageNum, pageSize, $route } = this
      const { result: { list, totalCount } } = await this.$api.noticeRecord({
        groupId: $route.query.groupId,
        memberId: $route.query.memberId,
        pageNum,
        pageSize
      })
      this.total = totalCount
      this.tableData = list.map(item => {
        item.releaseTime = item.releaseTime.replace('.000', '')
        return item
      })
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
