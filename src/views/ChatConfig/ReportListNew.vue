<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData">
      <template #records="scope">
        <el-button type="text" @click="chatHistory(scope.row)">查看</el-button>
      </template>
      <template #reportName="scope">
        <div v-if="!scope.row.showReportName">
            <el-button type="text" @click="checkShow(scope.row)">查看</el-button>
        </div>
        <div v-else>
          <div>
            {{scope.row.reportNickname}}
          </div>
          <div>
            {{scope.row.reportContractAddress}}
          </div>
        </div>
      </template>
      <template #liaotian_body="scope">
        <!-- {{scope.row.speakContent[0].MsgType}} -->
        <template >
            <div v-if="scope.row.speakContent[0].MsgType === 'TIMTextElem' ">
              {{scope.row.speakContent[0].MsgContent.Text }}
            </div>
            <el-image
              v-else-if="scope.row.speakContent[0].MsgType === 'TIMImageElem'"
              style="width: 100px; height: 100px"
              :src="scope.row.speakContent[0].MsgContent.ImageInfoArray[0].URL"
              :preview-src-list="[scope.row.speakContent[0].MsgContent.ImageInfoArray[0].URL]">
            </el-image>
             <a v-else-if="scope.row.speakContent[0].MsgType === 'TIMVideoFileElem'" target="_blank" :href="scope.row.speakContent[0].MsgContent.VideoUrl">查看举报视频</a>
            <div v-if="scope.row.speakContent[0].MsgType === 'TIMSoundElem' ">
               <el-tag type="success"> 语音长度{{scope.row.speakContent[0].MsgContent.Second}}s</el-tag><el-button type="text" @click="audio_pay(scope.row.speakContent[0].MsgContent.Url)">播放语音</el-button>
            </div>
          </template>
      </template>
      <template #reported_nickname="scope">
        <el-button type="text" @click="reportNameDetail(scope.row)">{{scope.row.reportedNickname}}</el-button>
      </template>
      <template #action="scope">
        <el-button type="text" @click="reportRecordOperate(scope.row,'NOT_HANDLED')">不处理</el-button>
        <el-button type="text" @click="reportRecordOperate(scope.row,'WITHDRAW_MSG')">撤回发言</el-button>
        <el-button type="text" @click="reportRecordOperate(scope.row,'NO_SPEAKING_3DAY')">禁言3天</el-button>
        <el-button type="text" @click="reportRecordOperate(scope.row,'NO_SPEAKING_7DAY')">禁言7天</el-button>
        <el-button type="text" @click="reportRecordOperate(scope.row,'NO_SPEAKING_FOREVER')">永久禁言</el-button>
      </template>
      <template #operate_record="scope">
        <div>
          <el-tag type="info" style="margin-right:6px;margin-bottom:6px" v-for="(item,index) in scope.row.operateRecordList">{{item}}
          </el-tag>
        </div>
      </template>
    </common-table>
    <div class="footer">
      <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
          :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
          style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="xuanzeSize">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="" :visible.sync="infoDialogVisible" width="600px" >
      <el-form ref="form" label-width="200px" v-loading="loading">
        <el-form-item label="昵称:" >
         {{infoDialogData.nickname}}
        </el-form-item>
        <el-form-item label="contractAddress:" >
         {{infoDialogData.contractAddress}}
        </el-form-item>
        <el-form-item label="仓位:" >
         {{infoDialogData.goodsAmount}}
        </el-form-item>
        <el-form-item label="净买入:" >
        {{infoDialogData.buySubSellAmount}}
        </el-form-item>
        <el-form-item label="最近购买时间:" >
         {{infoDialogData.lastBuyOrderTime}}
        </el-form-item>
        <el-form-item label="注册时间:" >
        {{infoDialogData.registerTime}}
        </el-form-item>
        <el-form-item label="查看他的藏品:" >
           <el-button type="text" @click="nav_shop()">{{infoDialogData.collections}}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </d2-container>
</template>
<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    chatReportList,
  } from '@/api/queqiao'

  export default {
    name: 'ReportListNew',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          dailyReportUserCount:'3'
        },
        tableSchema: [ // 表格架构
          {
            label: '举报时间',
            field: 'reportTime',
            width: '200px',
            align: 'center'
          },
          {
            label: '发言时间',
            field: 'speakTime',
            width: '200px',
            align: 'center'
          },
          {
            label: '所在群',
            field: 'groupName',
            width: '150px',
            align: 'center',
          },
          {
            label: '被举报聊天内容',
            slot: 'liaotian_body',
            width: '200px',
            align: 'center'
          },
          {
            label: '被举报人',
            slot: 'reported_nickname',
            width: '200px',
            align: 'center'
          },
          {
            label: '单日举报他的人数',
            field: 'dailyReportedUserCount',
            width: '100px',
            align: 'center'
          },
          {
            label: '他的聊天内容',
            slot: 'records',
          	 width: '100px',
            align: 'center'
          },
          {
           	label: '操作记录',
            slot: 'operate_record',
            width: '200px',
            align: 'center'
          },
          {
            label: '操作',
            slot: 'action',
            width: '200px',
            align: 'center'
          },
          {
            label: '举报人昵称',
            slot: 'reportName',
            width: '150px',
            align: 'center'
          },

        ],
        tableData: [], // 表格数据
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '时间：',
            field: 'speakTimeStart',
            field2: 'speakTimeEnd',
          },
          {
            type: 'input',
            label: '被举报人用户昵称：',
            placeholder: '请输入被举报人用户昵称',
            field: 'reportedNickname'
          },
          {
            type: 'input',
            label: ' 被举报人con add：',
            placeholder: '请输入 被举报人con add',
            field: 'reportedContractAddress'
          },
          {
            type: 'input',
            label: '举报人用户昵称：',
            placeholder: '请输入举报人用户昵称',
            field: 'reportNickname'
          },
          {
            type: 'input',
            label: '举报内容：',
            placeholder: '请输入举报内容',
            field: 'speakContent'
          },
          {
            type: 'input',
            label: '所在群 ：',
            placeholder: '请输入举报内容',
            field: 'groupName'
          },
          {
            type: 'select',
          	 label: '操作记录：',
            field: 'operateRecordListJson',
            placeholder: '',
            multiple: true,
            options: [{
              'label': '不处理',
              'value': 'NOT_HANDLED',
            }, {
            		'label': '撤回发言',
              'value': 'WITHDRAW_MSG',
            }, {
              'label': '禁言3天',
              'value': 'NO_SPEAKING_3DAY',
            }, {
              'label': '禁言7天',
              'value': 'NO_SPEAKING_7DAY',
            }, {
              'label': '永久禁言',
              'value': 'NO_SPEAKING_FOREVER',
            }]
          },
          {
            type: 'select',
            label: '单日举报人数：',
            field: 'dailyReportUserCount',
            placeholder: '请选择',
            options: [{
              'label': '全部',
              'value': '',
            }, {
              'label': '三人及以上',
              'value': '3',
            }]
          },
        ],
        infoDialogVisible: false,
        infoDialogData:{},
        infoDialogList: [{
            label: '昵称：',
            prop: 'nickname'
          },
          {
            label: 'contract_address：',
            prop: 'contractAddress'
          },
        ],
        loading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.page.pageNum=1
        this.query = data
        this.getList(true)
      },
      // 分页改变
      xuanzeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      getList() {
        let operateRecordListJson;
        if (this.query.operateRecordListJson) {
          operateRecordListJson = JSON.stringify(this.query.operateRecordListJson)
        }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          operateRecordListJson
        }
        chatReportList(params).then(res => {
          const data = res.result
          data.list.forEach((item)=>{
            if(item.speakContent){
               item.speakContent = JSON.parse( item.speakContent)
               console.log(item.speakContent)
            }
            item.showReportName=false
          })
          this.tableData = data.list
          console.log(this.tableData)
          this.page.totalCount = data.totalCount
          this.page.pageSize = data.pageSize
          this.page.pageCount = data.pageCount
        })
      },
      // 发表/下架
      statusToggle(id, status) {
        const changeText = status === 0 ? '发表' : '下架'
        this.$confirm(`确定${changeText}该条公告吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          noticePublish({
            id,
            status: status === 0 ? 1 : 0
          }).then((res) => {
            this.$message.success(res.status.msg)
            this.getList()
          })
        })
      },
      /**
       * 发言记录
       * @param row {Object} 当前行数据
       */
      chatHistory(row) {
        this.$router.push({
          name: 'GroupChatHistory',
          query: {
            // groupId:'@TGS#_@TGS#c4HQ3HIM62C5',
            speakTime: row.speakTime,
            groupId: row.groupId,
            bobao:0
          }
      	 })
      },
      /**
       * 举报者信息查看
       * @param row
       */
      async reportNameDetail(row) {
        this.infoDialogData = {}
        this.infoDialogVisible = true
        this.loading=true
        const {
          result
        } = await this.$api.ownerInfo({
          groupId: row.groupId,
          nickname: row.reportedNickname
        })
         this.loading=false
        this.infoDialogData = result
      },
      async reportRecordOperate(row,type) {
        let res = await this.$api.reportRecordOperate ({
          id: row.id,
          operateType: type
        })
        if(res.status.code===0){
          this.getList()
          this.$message.success("操作成功")
        }
      },
      nav_shop(){
        this.infoDialogVisible=false
        this.$router.push({
          name: 'indexOperation',
          query: {
            nickname:  this.infoDialogData.nickname
          }
         })
      },
      checkShow(item){
       item.showReportName=true
      },
      audio_pay(link){
        window.location.href = link
      }

    }
  }
</script>

<style lang="scss" scoped>

</style>
