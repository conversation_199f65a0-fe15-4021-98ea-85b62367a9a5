<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
        <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
          <el-select  v-model="formInline[item.prop]" v-if="item.type === 'select'">
            <el-option :label="v.label" :value="v.value" v-for="(v,i) in item.options" :key="i"></el-option>
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'dateRange'"
            v-model="formInline[item.prop]"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input v-model="formInline[item.prop]" v-else></el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 成员属性-->
            <template v-if="item.prop === 'attribute'">
              <el-tag>{{ attributeDict[scope.row[item.prop]] }}</el-tag>
            </template>
            <!-- 举报者昵称 -->
            <template v-else-if="item.prop === 'reportName'">
              <el-button type="text"  @click="reportNameDetail(scope.row)">{{
                  scope.row[item.prop]
                }}
              </el-button>
            </template>
            <!-- 举报对象属性 -->
            <template v-else-if="item.prop === 'target'">
              <el-tag>{{ +scope.row[item.prop] ? '用户' : '群' }}</el-tag>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80">
          <template slot-scope="scope">
            <el-button type="text"  @click="onRead(scope.row)">查看</el-button>
            <el-button type="text"  @click="onAction(scope.row)">操作</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog
      title="群聊操作"
      :visible.sync="actionDialogVisible"
      width="30%">
      <div class="dialog-action">
        <div v-if="!row.memberId">
          选择确认后，在客户端将以伯德护卫机器人身份对群进行全员禁言（包含群主或管理）以及对该群进行解散。
        </div>
        <div v-else>选择确认后，在客户端将以伯德护卫机器人身份对该用户进行请出群聊或全群拉黑。</div>
        <el-radio-group v-model="operate">
          <template v-if="!row.memberId">
            <el-radio :label="1">禁言群</el-radio>
            <el-radio :label="2">解散群</el-radio>
            <el-radio :label="3">取消群禁言</el-radio>
          </template>
          <template v-else-if="row.attribute !== '3'">
            <el-radio :label="11">本群内禁言</el-radio>
            <el-radio :label="12">请出该群聊</el-radio>
            <el-radio :label="13">全平台群拉黑</el-radio>
            <el-radio :label="15">取消全平台群拉黑</el-radio>
          </template>
          <el-radio :label="11" v-else>本群内禁言</el-radio>
          <el-radio v-if="row.memberId" :label="14">取消成员禁言</el-radio>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="actionDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="actionConfirm">确 定</el-button>
    </span>
    </el-dialog>
    <!--    用户信息-->
    <el-dialog
      title=""
      :visible.sync="infoDialogVisible"
      width="30%">
      <el-form ref="form" label-width="200px">
        <el-form-item :label="item.label" v-for="(item,index) in infoDialogList" :key="index">
          {{ infoDialogData[item.prop] }}
        </el-form-item>
      </el-form>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'ReportList',
  data () {
    return {
      formInline: {},
      formList: [
        {
          label: '用户地址：',
          prop: 'contactAddress',
          type: 'input'
        },
        {
          label: '群聊名称：',
          prop: 'groupName',
          type: 'input'
        },
        {
          label: '举报者昵称：',
          prop: 'reportName',
          type: 'input'
        },
        {
          label: '举报类型：',
          prop: 'type',
          type: 'select',
          options: [
            {
              label: '发布色情／违法信息',
              value: '1'
            },
            {
              label: '存在赌博行为',
              value: '2'
            },
            {
              label: '存在欺诈骗钱行为',
              value: '3'
            },
            {
              label: '侵犯未成年人权益',
              value: '4'
            },
            {
              label: '传播谣言信息',
              value: '5'
            },
            {
              label: '其他违规行为',
              value: '6'
            }
          ]
        },
        {
          label: '举报对象信息：',
          prop: 'memberName',
          type: 'input'
        },
        {
          label: '举报对象：',
          prop: 'targetType',
          type: 'select',
          options: [
            {
              label: '用户',
              value: '1'
            },
            {
              label: '群',
              value: '0'
            }
          ]
        },
        {
          label: '举报时间：',
          prop: 'dateRange',
          type: 'dateRange'
        }
      ],
      tableData: [],
      tableList: [
        {
          prop: 'contactAddress',
          label: '举报者地址',
          width: '140'
        },
        {
          prop: 'reportName',
          label: '举报者昵称',
          width: '100'
        },
        {
          prop: 'type',
          label: '举报类型',
          width: '70'
        },
        {
          prop: 'target',
          label: '举报对象属性',
          width: '70'
        },
        {
          prop: 'memberInfo',
          label: '举报对象信息'
        },
        {
          prop: 'reportContent',
          label: '投诉内容',
          width: '150'
        },
        {
          prop: 'attribute',
          label: '人/群属性',
          width: '80'
        },
        {
          prop: 'reportTime',
          label: '举报时间',
          width: '120'
        }
      ],
      attributeDict: {
        1: '普通成员',
        2: 'DAO成员',
        3: '群主',
        4: '藏家群管理',
        11: '藏家群',
        12: 'DAO群'
      },
      actionDialogVisible: false,
      infoDialogVisible: false,
      infoDialogData: {},
      infoDialogList: [
        {
          label: '昵称：',
          prop: 'nickname'
        },
        {
          label: 'contract_address：',
          prop: 'contractAddress'
        },
        {
          label: '作品：',
          prop: 'goodsNum'
        },
        {
          label: '藏品：',
          prop: 'collections'
        },
        {
          label: '管理的藏家群／DAO群：',
          prop: 'manageGroup'
        },
        {
          label: '加入的群：',
          prop: 'joinGroup'
        }
      ],
      row: '',
      operate: 1,
      pageSize: 15,
      pageNum: 1,
      total: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 点击操作
     * @method
     */
    onAction (row) {
      this.row = row
      this.actionDialogVisible = true
    },
    // 操作后点击确定
    async actionConfirm () {
      const {
        attribute,
        groupId,
        memberId,
        reportId,
        target
      } = this.row
      await this.$api.reportOperate({
        attribute,
        groupId,
        memberId,
        reportId,
        target,
        operate: this.operate
      })
      this.$message.success('操作成功')
      this.actionDialogVisible = false
      this.groupId = ''
      await this.getList()
    },
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const { dateRange } = this.formInline
      if (dateRange) {
        this.formInline.beginTime = dateRange[0] + '.000'
        this.formInline.endTime = dateRange[1] + '.000'
      }
      const {
        pageNum,
        pageSize
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.reportList({
        ...this.formInline,
        pageNum,
        pageSize
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 跳转查看页
     * @param row
     */
    onRead (row) {
      this.$router.push({
        name: 'ReportDetail',
        query: {
          reportId: row.reportId
        }
      })
    },
    /**
     * 举报者信息查看
     * @param row
     */ async reportNameDetail (row) {
      this.infoDialogData = {}
      this.infoDialogVisible = true
      const { result } = await this.$api.ownerInfo({
        groupId: row.groupId,
        nickname: row.reportName
      })
      this.infoDialogData = result
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;

  .demo-form-inline {
    flex: 1;
  }

  .action {
    width: 200px;
  }
}

.table {
  margin-bottom: 50px;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
