<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" size="mini">
        <el-form-item label="用户地址：">
          <el-input v-model="formInline.address" placeholder="用户地址"></el-input>
        </el-form-item>
        <el-form-item label="成员账号昵称：">
          <el-input v-model="formInline.nickname" placeholder="成员账号昵称"></el-input>
        </el-form-item>
        <el-form-item label="会员类别：">
          <el-select  v-model="formInline.member">
            <el-option label="全部" value=""></el-option>
            <el-option label="普通用户" value="0"></el-option>
            <el-option label="一级用户" value="4"></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div class="action">
        <el-button @click="onReset" size="mini">重置</el-button>
        <el-button type="primary" @click="getList(true)" size="mini">查询</el-button>
        <el-button type="primary" @click="joinToGroup" size="mini">添加群</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
        style="width: 100%">
        <el-table-column
          type="selection"
          width="30">
        </el-table-column>
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <template v-if="item.prop === 'member'">
              <el-tag v-if="scope.row[item.prop] === 0">普通用户</el-tag>
              <el-tag v-if="scope.row[item.prop] === 4">一级用户</el-tag>
            </template>
            <!-- 头像 -->
            <template v-else-if="item.prop === 'avatar' || item.prop === 'memberAvatar'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <!-- 持有群主作品 -->
            <template v-else-if="item.prop === 'holder'">
              <el-button type="text"  @click="holder(scope.row)">查看</el-button>
            </template>
            <!-- 管理／加入的群-->
            <template v-else-if="item.prop === 'groupStats'">
              <el-button type="text"  @click="memberJoinedGroup(scope.row)">{{
                  scope.row[item.prop]
                }}
              </el-button>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <common-pagination :page.sync="page" @change="getList"></common-pagination>
    </div>
  </d2-container>
</template>

<script>
import { joinToGroup } from '@/api'
import common from '@/mixins/common'

export default {
  name: 'NotInGroupList',
  mixins: [common],
  computed: {
    groupId () {
      return this.$route.query.groupId
    }
  },
  data () {
    return {
      formInline: {},
      tableData: [],
      tableList: [
        {
          prop: 'address',
          label: '用户地址'
        },
        {
          prop: 'nickname',
          label: '成员账号昵称'
        },
        {
          prop: 'avatar',
          label: '成员账号头像'
        },
        {
          prop: 'member',
          label: '会员类别'
        },
        {
          prop: 'holder',
          label: '持有群主作品'
        },
        {
          prop: 'groupStats',
          label: '管理／加入的群'
        }
      ],
      multipleSelection: []
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * @method
     */
    async getList (init) {
      init && this.initPage()
      const {
        pageNum,
        pageSize
      } = this.page
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.memberNotIn({
        ...this.formInline,
        groupId: this.groupId,
        pageNum: init ? 1 : pageNum,
        pageSize
      })
      this.tableData = list
      this.page.totalCount = totalCount
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.page.pageNum = 1
      this.getList()
    },
    /**
     * 持有群主作品
     * @param row {Object} 当前行数据
     */
    holder (row) {
      this.$router.push({
        name: 'MemberGood',
        query: {
          groupId: this.groupId,
          memberId: row.memberId
        }
      })
    },
    /**
     * 管理/加入的群
     * @param row {Object} 当前行数据
     */
    memberJoinedGroup (row) {
      this.$router.push({
        name: 'MemberJoinedGroup',
        query: {
          memberId: row.memberId
        }
      })
    },
    async joinToGroup () {
      if (this.multipleSelection.length === 0) {
        this.$message({
          message: '请选择成员',
          type: 'warning'
        })
        return
      }
      this.$confirm('', '确认添加？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await joinToGroup({
          groupId: this.groupId,
          invitedUserIdStr: JSON.stringify(this.multipleSelection.map(item => item.memberId))
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    handleSelectionChange (val) {
      console.log(val)
      this.multipleSelection = val
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;

  .demo-form-inline {
    flex: 1;
  }

  .action {
    //width: 200px;
  }
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
