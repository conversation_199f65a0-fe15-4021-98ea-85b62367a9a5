<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-data="tableData" :showIndex="false" :table-schema="tableSchema" :loading="loading">
      <template #nick="scope">
        <el-button @click="reportNameDetail(scope.row)" type="text">{{ scope.row.nickname }}</el-button>
      </template>
      <template #chatContent1="scope">
        <div v-if="scope.row.chatContent.MsgType === 'TIMTextElem'">
          <template v-if="scope.row.compliance === 0">
            <div :class="{ 'blue': scope.row.withdraw == 1 }">{{ scope.row.chatContent.MsgContent.Text }}</div>
          </template>
          <template v-else>
            <div v-html="keyWordHighlight(scope.row.chatContent.MsgContent.Text, scope.row.badWord)"></div>
          </template>
        </div>
        <el-image v-else-if="scope.row.chatContent.MsgType === 'TIMImageElem'" style="width: 100px; height: 100px"
          :src="scope.row.chatContent.MsgContent.ImageInfoArray[0].URL"
          :preview-src-list="[scope.row.chatContent.MsgContent.ImageInfoArray[0].URL]">
        </el-image>

        <a v-else-if="scope.row.chatContent.MsgType === 'TIMVideoFileElem'" target="_blank"
          :href="scope.row.chatContent.MsgContent.VideoUrl">查看视频</a>

      </template>
      <template #all="scope">
        <el-button type="text" @click="openChatList(scope.row)">查看</el-button>
      </template>

      <!-- <template #pic="scope">
        <template v-if="scope.row.pic">
          <video v-if="scope.row.chatContentType === 2" class="home-mint" autoplay loop muted preload="auto">
            <source :src="scope.row.pic" type="video/mp4">
          </video>
          <el-image style="width:60px;"  v-else :src="scope.row.pic" :preview-src-list="[scope.row.pic]"></el-image>
        </template>
      </template> -->
      <template #action="scope">
        <el-button @click="openActionDialogVisible(scope.row)" type="text">操作</el-button>
        <el-button @click="openSpeakDialogVisible(scope.row)" type="text">伯德发言</el-button>
        <el-button @click="open(scope.row)" type="text">水军发言</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <!--群成员信息-->
    <el-dialog title="群成员信息" :visible.sync="dialogVisible">
      <common-form label-width="200px" :is-edit="false" :schema="infoSchema" :data="data"></common-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog width="500px" title="用户操作" :visible.sync="actionDialogVisible">
      <div style="margin-bottom: 20px;">选择确认后，在客户端将以伯德护卫机器人身份对该用户进行请出群聊或全群拉黑。</div>
      <el-radio-group v-model="actionSelected">
        <div v-for="item in actionList" :key="item.value" style="display: inline-block; margin-right: 16px;">
          <template v-if="curInfo.memberType === 0">
            <el-radio v-if="[11, 16].includes(item.value)" :label="item.value">{{ item.label }}</el-radio>
          </template>
          <el-radio v-else :label="item.value">{{ item.label }}</el-radio>
        </div>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="actionDialogVisible = false">关闭</el-button>
        <el-button @click="operateChatRecord" type="primary">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog width="500px" title="发言" :visible.sync="speakDialogVisible">
      <ul style="margin-top: 0; margin-bottom: 20px;">
        <li>输入文案并提交发送后，在客户端该群中将以伯德护卫机器人身份进行发言，文字和图片一次性提交将以两条信息进行发送。</li>
        <li>针对某条聊天记录进行发言时，机器人在前端将自动@该用户。</li>
      </ul>
      <common-form label-width="120px" :schema="speakSchema" :data="speakData"></common-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="speakDialogVisible = false">关闭</el-button>
        <el-button @click="chatRecords" type="primary">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="infoDialogVisible" width="600px">
      <el-form ref="form" label-width="200px" v-loading="loading">
        <el-form-item label="昵称:">
          {{ infoDialogData.nickname }}
        </el-form-item>
        <el-form-item label="contractAddress:">
          {{ infoDialogData.contractAddress }}
        </el-form-item>
        <el-form-item label="仓位:">
          {{ infoDialogData.goodsAmount }}
        </el-form-item>
        <el-form-item label="净买入:">
          {{ infoDialogData.buySubSellAmount }}
        </el-form-item>
        <el-form-item label="最近购买时间:">
          {{ infoDialogData.lastBuyOrderTime }}
        </el-form-item>
        <el-form-item label="注册时间:">
          {{ infoDialogData.registerTime }}
        </el-form-item>
        <el-form-item label="查看他的藏品:">
          <el-button type="text" @click="nav_shop()">{{ infoDialogData.collections }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="水军发言" :visible.sync="isJoinQun" width="80%">
      <div class="content_dialog">
        <div class="left_div">
          <el-input type="textarea" autosize placeholder="请输入发言内容" v-model="textarea_speak">
          </el-input>
        </div>
        <div class="right_div">
          <el-form :inline="true" :model="formScreen" class="demo-form-inline" size="mini">
            <el-form-item label="昵称">
              <el-input v-model="formScreen.nickname" placeholder="请输入昵称"></el-input>
            </el-form-item>
            <!-- <el-form-item label="用户ID">
          		<el-input v-model="formScreen.id" placeholder="请输入用户ID"></el-input>
          	</el-form-item>
          	<el-form-item label="邮箱">
          		<el-input v-model="formScreen.email" placeholder="请输入邮箱" clearable></el-input>
          	</el-form-item> -->
            <el-form-item label="contractAddress">
              <el-input v-model="formScreen.contractAddress" placeholder="请输入contractAddress" clearable>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getUserList">查询</el-button>
              <el-button type="success" @click="submitJoinInGroup()">水军一键发言</el-button>
            </el-form-item>
          </el-form>
          <common-table :table-schema="userColumn" :table-data="userData" :multipleSelection.sync="multipleSelection"
            :showSelection="true">
          </common-table>
        </div>
      </div>

    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
  name: 'ChatRecord',
  components: {
    CommonTable,
    CommonForm,
    CommonQuery
  },
  data() {
    return {
      curInfo: {}, // 当前操作信息
      tableData: [],
      query: {
        memberNameCard: "",
        contractAddress: "",
        nickName: "",
        groupAccount: "",
        beginTime: "",
        endTime: ""
      },
      querySchema: [{
        type: 'input',
        label: '群id',
        placeholder: '请输入群id',
        field: 'groupAccount'
      },
      {
        type: 'input',
        label: '群名称',
        placeholder: '请输入群名称',
        field: 'groupName'
      },
      {
        type: 'input',
        label: '用户昵称',
        placeholder: '请输入用户昵称',
        field: 'nickName'
      },
      {
        type: 'input',
        label: ' 用户con add',
        placeholder: '请输入 用户con add',
        field: 'contractAddress'
      },
      {
        type: 'input',
        label: '用户手机号',
        placeholder: '请输入用户手机号',
        field: 'phone'
      },
      {
        type: 'input',
        label: '群成员昵称',
        placeholder: '请输入群成员昵称',
        field: 'memberNameCard'
      },
      {
        type: 'datetimerange',
        label: '发言时间',
        field: 'beginTime',
        field2: 'endTime',
        placeholder: '请选择发言时间'
      }
        // {
        //   type: 'select',
        //   label: '成员属性',
        //   field: 'memberType',
        //   placeholder: '请选择成员属性',
        //   options: [
        //     { label: '全部', value: -1 },
        //     { label: '群主', value: 0 },
        //     { label: '管理员', value: 1 },
        //     { label: '普通用户', value: 2 },
        //     { label: '机器人', value: 3 }
        //   ]
        // },
        // {
        //   type: 'select',
        //   label: '发言规范',
        //   field: 'compliance',
        //   placeholder: '请选择状态',
        //   options: [
        //     { label: '全部', value: -1 },
        //     { label: '合规', value: 0 },
        //     { label: '文本违规', value: 1 },
        //     { label: '图片违规', value: 2 }
        //   ]
        // },
      ],
      tableSchema: [{
        label: '群名',
        field: 'groupName'
      },
      {
        label: '发言时间',
        field: 'chatTime',
      },
      {
        label: '成员昵称',
        field: 'memberNameCard'
      },
      {
        label: '用户昵称',
        slot: 'nick',
        field: 'nickname'
      },
      {
        label: '发言内容',
        slot: 'chatContent1'
      },
      {
        label: '查看他的所有发言',
        slot: 'all'
      },
      {
        label: '群id',
        field: 'groupAccount',
        width: '220px'
      },
      {
        label: '操作',
        slot: 'action'
      }
      ],
      dialogVisible: false,
      actionDialogVisible: false,
      speakDialogVisible: false,
      data: {}, // 成员信息
      speakData: {
        photos: ''
      }, // 成员信息
      actionSelected: 11, // 操作值
      actionList: [ // 操作列表
        {
          label: '本群禁言',
          value: 11
        },
        {
          label: '移出本群',
          value: 12
        },
        {
          label: '撤回该发言',
          value: 16
        },
        {
          label: '全平台拉黑',
          value: 13
        },
        {
          label: '取消禁止发言',
          value: 14
        },
        {
          label: '取消全平台拉黑',
          value: 15
        }
      ],
      infoSchema: [{
        label: '账户昵称：',
        field: 'nickname'
      },
      {
        label: 'contract_address：',
        field: 'contractAddress'
      },
      {
        label: '作品：',
        field: 'originGoodsNum'
      },
      {
        label: '藏品：',
        field: 'collectGoodsNum'
      },
      {
        label: '管理的DAO群：',
        field: 'daoGroupNum'
      },
      {
        label: '管理的藏家群：',
        field: 'groupNum'
      },
      {
        label: '加入的群：',
        field: 'joinGroupNum'
      }
      ],
      speakSchema: [{
        label: '发言文案信息：',
        field: 'content',
        type: 'textarea'
      },
      {
        label: '图片：',
        field: 'photos',
        type: 'img'
      }
      ],
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      },
      loading: true,
      infoDialogVisible: false,
      infoDialogData: {},
      isJoinQun: false,
      userColumn: [{
        label: '用户id',
        field: 'memberId'
      },
      {
        label: '用户昵称',
        field: 'nickname'
      },
      {
        label: '用户地址',
        field: 'contractAddress'
      },
      {
        label: '用户邮箱',
        field: 'email'
      }
      ],
      keyword: "",
      formScreen: {
        email: '',
        nickname: '',
        id: '',
        contractAddress: ''
      },
      multipleSelection: [],
      userData: "",
      groupId: "",
      textarea_speak: "",
      bobao: "",
      query1:{}
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query.memberName) {
      this.query.nickName = this.$route.query.memberName
    }
    if (this.$route.query.groupAccount) {
      this.query.groupAccount = this.$route.query.groupAccount
    }
    console.log(this.query)
    this.getList()
  },
  methods: {
    // 过滤查询
    onQueryChange(data) {

      const filteredObj = {};

      for (const key in data) {
        if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
          filteredObj[key] = data[key];
        }
      }

      this.query1 = filteredObj
      // this.query = data
      // delete this.query.beginTime
      // delete this.query.endTime

      // this.query.
      this.getList(true)
    },
    async getList(isInit) {
      console.log(this.query, '123123');
      // if (this.query.beginTime) {
      //   var beginTime = this.query.beginTime
      // }
      // if (this.query.endTime) {
      //   var endTime = this.query.endTime
      // }
      this.loading = true
      const params = {
        // beginTime,
        // endTime,
        ...this.query1,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const {
        status,
        result
      } = await this.$api.chatRecordList(params)
      if (status.code === 0) {
        this.loading = false

        this.tableData = result.list.map(item => {
          item.chatContent = JSON.parse(item.chatContent)[0]
          return item
        })
        console.log(this.tableData)
        this.page.totalCount = result.totalCount
        console.log(this.query, 3333333);
        // this.query.beginTime = "2024-11-28 00:00:00"
        // this.query.endTime = "2024-11-28 23:59:59"
        // this.page.pageSize = result.pageSize
        // this.page.pageCount = result.pageCount
      }
      console.log(this.page)
    },
    async getMemberInfo(memberId) {
      const {
        result
      } = await this.$api.chatRecordMemberInfo({
        memberId
      })
      this.data = result
      this.dialogVisible = true
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    currentChangeSize(value) {
      this.page.pageSize = value
      this.getList()
    },
    // 发言
    async chatRecords() {
      console.log(this.speakData)
      const {
        groupAccount: groupId,
        memberId
      } = this.curInfo
      const data = {
        ...this.speakData,
        groupId,
        memberId
      }
      const {
        status
      } = await this.$api.chatRecords(data)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.speakDialogVisible = false
      }
    },
    // 操作列表
    async operateChatRecord() {
      const {
        groupAccount: groupId,
        memberId,
        attribute,
        chatId: chatRecordId
      } = this.curInfo
      const data = {
        attribute,
        chatRecordId,
        groupId,
        memberId,
        operate: this.actionSelected
      }
      const {
        status
      } = await this.$api.operateChatRecord(data)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.actionDialogVisible = false
      }
    },
    openActionDialogVisible(item) {
      this.actionDialogVisible = true
      this.curInfo = item
    },
    openSpeakDialogVisible(item) {
      this.speakDialogVisible = true
      this.curInfo = item
    },
    keyWordHighlight(rawText, keyWord) {
      if (!rawText || !keyWord) return ''
      return keyWord.split(',').reduce(
        (pre, cur) => pre.replaceAll(cur, `<span style="color: red;">${cur}</span>`),
        rawText
      )
    },
    openChatList(item) {
      this.query1.nickName = item.nickname
      // this.query.memberId=item.memberId
      this.query.nickName = item.nickname
      console.log(this.query,2222)
      this.page.pageNum = 1
      this.getList(true)
    },
    /**
     * 举报者信息查看
     * @param row
     */
    async reportNameDetail(row) {
      this.infoDialogData = {}
      this.infoDialogVisible = true
      this.loading = true
      const {
        result
      } = await this.$api.ownerInfo({
        groupId: row.groupAccount,
        nickname: row.nickname
      })
      this.loading = false
      this.infoDialogData = result
    },
    nav_shop() {
      this.infoDialogVisible = false
      this.$router.push({
        name: 'indexOperation',
        query: {
          nickname: this.infoDialogData.nickname
        }
      })
    },
    async submitJoinInGroup() {
      this.numLength = this.multipleSelection.length
      console.log(this.multipleSelection)
      if (this.numLength === 0) {
        this.$message.error('请选择你要发言的人')
      } else if (this.numLength > 1) {
        this.$message.error('暂不支持多人同时发言，请勿选择多个水军')
      } else {
        const res = await this.$api.groupSystemMsg({
          groupId: this.groupId,
          content: this.textarea_speak,
          speakMemberId: this.multipleSelection[0].memberId,
          speakType: 1
        })
        if (res.status.code === 0) {
          this.$message.success('发言成功')
          this.getList()
        } else {
          this.$message.error(res.status.msg)
        }
      }
    },
    async getUserList() {
      const res = await this.$api.groupMemberInfo({
        groupId: this.groupId,
        member: 4,
        pageNum: 1,
        pageSize: 50,
        nickname: this.formScreen.nickname,
        // email: this.formScreen.email,
        address: this.formScreen.contractAddress,
        // id: this.formScreen.id,
      })
      this.userData = res.result.list
    },
    open(item) {
      this.groupId = item.groupAccount
      this.getUserList()
      this.isJoinQun = true
    },
  }
}
</script>

<style lang="scss" scoped>
.blue {
  color: #409EFF;
}

.content_dialog {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .left_div {
    width: 500px;
    margin-right: 20px;
    border-radius: 6px;
    border: 2px solid #ccc;
    min-height: 226px;
    padding: 10px;
  }

  .right_div {
    width: 700px;
  }
}

::v-deep .success-row {
  background-color: #ff9999;
}
</style>
