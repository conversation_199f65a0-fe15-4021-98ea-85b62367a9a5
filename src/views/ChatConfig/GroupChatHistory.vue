<template>
  <d2-container class="page">
    <div class="div_right">
       <el-button type="primary" @click="open()">发言</el-button>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :row-class-name="tableRowClassName"
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 'auto'">
          <template slot-scope="scope">
            <!--聊天内容-->
            <template v-if="item.prop === 'chatContent'">
              <div v-if="scope.row[item.prop].MsgType === 'TIMTextElem' ">
                {{ scope.row[item.prop].MsgContent.Text }}
              </div>
              <el-image
                v-else-if="scope.row[item.prop].MsgType === 'TIMImageElem'"
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop].MsgContent.ImageInfoArray[0].URL"
                :preview-src-list="[scope.row[item.prop].MsgContent.ImageInfoArray[0].URL]">
              </el-image>

              <a v-else-if="scope.row[item.prop].MsgType === 'TIMVideoFileElem'" target="_blank" :href="scope.row[item.prop].MsgContent.VideoUrl">查看举报视频</a>
            </template>
            <template v-else-if="item.prop === 'memberName'">
               <el-button type="text" @click="nav_ChatRecord(scope.row[item.prop])">{{ scope.row[item.prop] }}</el-button>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="加入群聊" :visible.sync="isJoinQun" width="80%">
      <div class="content_dialog">
        <div class="left_div">
           <el-input
             type="textarea"
             autosize
             placeholder="请输入发言内容"
             v-model="textarea_speak">
           </el-input>
        </div>
        <div class="right_div">
          <el-form :inline="true" :model="formScreen" class="demo-form-inline"  size="mini">
          	<el-form-item label="昵称">
          		<el-input v-model="formScreen.nickname" placeholder="请输入昵称"></el-input>
          	</el-form-item>
          	<!-- <el-form-item label="用户ID">
          		<el-input v-model="formScreen.id" placeholder="请输入用户ID"></el-input>
          	</el-form-item>
          	<el-form-item label="邮箱">
          		<el-input v-model="formScreen.email" placeholder="请输入邮箱" clearable></el-input>
          	</el-form-item> -->
          	<el-form-item label="contractAddress">
          		<el-input v-model="formScreen.contractAddress" placeholder="请输入contractAddress" clearable>
          		</el-input>
          	</el-form-item>
          	<el-form-item>
          		<el-button type="primary" @click="getUserList">查询</el-button>
          		<el-button type="success" @click="submitJoinInGroup()">水军一键发言</el-button>
          	</el-form-item>
          </el-form>
          <common-table :table-schema="userColumn" :table-data="userData" :multipleSelection.sync="multipleSelection" :showSelection="true">
          </common-table>
        </div>
      </div>

    </el-dialog>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
export default {
  name: 'GroupChatHistory',
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'chatTime',
          label: '发送时间',
          width: '180'
        },
        {
          prop: 'memberName',
          label: '昵称',
          width: '200'
        },
        {
          prop: 'chatContent',
          label: '消息内容'
        }
      ],
      total: 0,
      pageNum: 1,
      pageSize: 50,
      isJoinQun: false,
      userColumn: [
      	{
      		label: '用户id',
      		field: 'memberId'
      	},
      	{
      		label: '用户昵称',
      		field: 'nickname'
      	},
      	{
      		label: '用户地址',
      		field: 'contractAddress'
      	},
      	{
      		label: '用户邮箱',
      		field: 'email'
      	}
      ],
      keyword: "",
      formScreen: {
      	email: '',
      	nickname: '',
      	id:'',
      	contractAddress:''
      },
      multipleSelection:[],
      userData:"",
      groupId:"",
      textarea_speak:"",
      bobao:""
    }
  },
  components: {
  	CommonTable,
  },
  mounted () {
    console.log(this.$route.query)
    this.bobao=this.$route.query.bobao
    this.getList()
  },
  methods: {
    /**
     * 获取聊天记录
     * @method
     */
    async getList () {
      const {
        pageNum,
        pageSize,
        $route,
      } = this
      let  startTime,endTime
      let methods = 'groupChatRecords'
      if ($route.query.memberId) {
        methods = 'memberChatRecords'
      }
      if($route.query.speakTime){
          startTime= this.timestampToTime(Date.parse($route.query.speakTime)-600000)
          endTime= this.timestampToTime(Date.parse($route.query.speakTime)+600000)
      }
      const { result: { list, totalCount } } = await this.$api[methods]({
        groupId: $route.query.groupId,
        memberId: $route.query.memberId,
        startTime,
        endTime,
        bobao:this.bobao,
        pageNum,
        pageSize
      })
      this.tableData = list.map(item => {
        item.chatContent = JSON.parse(item.chatContent)[0]
        return item
      })
      this.total = totalCount
    },
    // 查询列表
    async getUserList() {
    	const res = await this.$api.groupMemberInfo({
        groupId:this.$route.query.groupId,
        member: 4,
    		pageNum: 1,
    		pageSize: 50,
    		nickname: this.formScreen.nickname,
    		// email: this.formScreen.email,
    		address: this.formScreen.contractAddress,
    		// id: this.formScreen.id,
    	})
    	this.userData = res.result.list
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    open(){
      this.getUserList()
      this.isJoinQun=true
    },
    async submitJoinInGroup(){
    	this.numLength=this.multipleSelection.length
      console.log(this.multipleSelection)
    	if(this.numLength===0){
    		this.$message.error('请选择你要发言的人')
    	}else if(this.numLength>1){
        this.$message.error('暂不支持多人同时发言，请勿选择多个水军')
      }else{
        const res = await this.$api.groupSystemMsg({
        	groupId: this.$route.query.groupId,
        	content:this.textarea_speak,
          speakMemberId:this.multipleSelection[0].memberId,
          speakType:1
        })
        if(res.status.code===0){
        	this.$message.success('发言成功')
          this.getList()
        }else{
        	this.$message.error(res.status.msg)
        }
      }
    },
    timestampToTime(cjsj){
      //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      var date = new Date(cjsj);
      var Y = date.getFullYear() + '-';
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth()+1) : date.getMonth() + 1) + '-';
      var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
      var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
      var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
      var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
      return  Y + M + D + h + m + s + '.000'
    },
     tableRowClassName({row}) {
       if(row.isReported==1){
          return 'success-row';
       }else{
         return '';
       }

        // if (rowIndex === 1) {
        //   return 'warning-red';
        // }

      },
      nav_ChatRecord(name){
          this.$router.push({
          	name: 'ChatRecord',
          	query: {
              memberName:name
          	}
          })
      }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.div_right{
  display: flex;
  justify-content:flex-end;
  padding:10px;
}
.table{
  padding-bottom:40px;
}
.content_dialog{
  display: flex;
  justify-content:flex-start;
  align-items: flex-start;
  .left_div{
    width: 500px;
    margin-right:20px;
    border-radius:6px;
    border:2px solid #ccc;
    min-height:226px;
    padding:10px;
  }
  .right_div{
    width:700px;
  }
}
 ::v-deep .success-row {
  background-color: #ff9999 ;
}

</style>
