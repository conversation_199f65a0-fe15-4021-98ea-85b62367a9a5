<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="ugc代铸造">
        <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
        </common-form>
      </el-tab-pane>
    </el-tabs>

  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import {
  mapActions
} from 'vuex'
import {
  downloadBlob
} from '@/utils/helper'

export default {
  name: 'ugcCreation',
  components: {
    CommonForm,
    CommonTable,
    FileUploader
  },
  data() {
    return {
      templateUrl: '', // 盲盒内作品模板地址
      templateUrl1: '', // 盲盒内作品模板地址
      isDetail: false, // 详情
      activityNo: null, // 活动编号
      formData: {},
      formSchema: [{
        type: 'input',
        label: '创作者地址：',
        placeholder: '请输入创作者地址',
        field: 'contractAddress',
        rules: [{
          required: true,
          message: '请输入创作者地址',
          trigger: 'blur'
        },]
      },
      {
        type: 'input',
        label: '名称：',
        placeholder: '请输入名称',
        field: 'title',
        rules: [{
          required: true,
          message: '请输入名称',
          trigger: 'blur'
        }]
      },
      {
        type: 'img',
        label: '作品图片：',
        placeholder: '作品图片',
        field: 'photo',
        multigraph: true,
        rules: [{
          required: true,
          message: '请选择作品图片',
          trigger: 'change'
        }]
      },
      {
        type: 'number-input',
        label: '铸造数量：',
        placeholder: '请输入铸造数量',
        field: 'createNum',
        rules: [{
          required: true,
          message: '请输入铸造数量',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '铸造价格：',
        placeholder: '请输入铸造价格',
        field: 'price',
        rules: [{
          required: true,
          message: '请输入铸造价格',
          trigger: 'blur'
        }]
      },
      {
        type: 'action'
      }
      ],
      creationNum: 'x',
      collectionNum: 'x',
      loading: false
    }
  },
  mounted() {

  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    async submit() {
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        this.sub()
      })
    },
    async sub() {
      console.table(this.formData)
      const data = {
        ...this.formData,
        photoShow: JSON.parse(localStorage.getItem('img_result')).mediumImageUrl,
        cover: JSON.parse(localStorage.getItem('img_result')).smallImageUrl,
      }
      console.log(data)
      const {
        status
      } = await this.$api.batchCreateUgc(data)
      if (status.code === 0) {
        this.$message.success('铸造成功')
        this.routerBack()
      } else {

      }
    },


  }
}
</script>

<style lang="scss" scoped>
.page {
  padding-top: 80px;
}
</style>
