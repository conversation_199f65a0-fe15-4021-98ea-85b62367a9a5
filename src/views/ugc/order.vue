<template>
  <d2-container class="page">
   <!-- <el-form class="demo-form-inline" style="background-color: #ffffff; padding: 10px">
      <el-form-item>
        <el-button type="primary" @click="downloadBtn">下载模板</el-button>
        <el-button type="primary" @click="uploadBtn">上传表格</el-button>
      </el-form-item>

    </el-form> -->




    <el-tabs type="border-card">
      <el-tab-pane label="ugc创建订单">
        <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
        </common-form>


      </el-tab-pane>

      <template>
        <div class="flex_div" style="margin-top: 50px;">
          <div class='flex_in'>
            批量导入
          </div>
          <el-button  style="padding-left: 20px;"  v-if="!details"  @click="downloadTemplate('CREATE_ORDER')" type="text" size="mini">
            下载模版</el-button>
          <file-uploader v-if="!details"  :value.sync="createformData.userImportUrl"
            style="width:200px;margin-left:10px" text="上传模版"></file-uploader>

        </div>
        <el-button class='flex_button' type="primary" size="mini"   @click='uploadForm(createformData.userImportUrl)'>
          提交模板</el-button>
      </template>
    </el-tabs>





  </d2-container>
</template>

<script>

import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import {
  mapActions
} from 'vuex'
import {
  downloadBlob
} from '@/utils/helper'

export default {
  name: 'ugcOrder',
  components: {

    CommonForm,
    CommonTable,
    FileUploader
  },
  data() {
    return {
      dialogVisible:false,
      details:false,
      templateUrl: '', // 盲盒内作品模板地址
      templateUrl1: '', // 盲盒内作品模板地址
      isDetail: false, // 详情
      activityNo: null, // 活动编号
      formData: {
        payMethod: 16
      },
      formSchema: [{
        type: 'input',
        label: '买家地址：',
        placeholder: '请输入买家地址',
        field: 'buyerContractAddress',
        rules: [{
          required: true,
          message: '请输入买家地址：',
          trigger: 'blur'
        },]
      },
      {
        type: 'input',
        label: 'tokenID',
        placeholder: '请输入tokenID',
        field: 'tid',
        rules: [{
          required: true,
          message: '请输入tokenID',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '交易价格：',
        placeholder: '请输入交易价格',
        field: 'price',
        rules: [{
          required: true,
          message: '请输入交易价格',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '空投/交易',
        field: 'payMethod',
        options: [{
          label: '空投',
          value: 16
        },
        {
          label: '交易',
          value: 10
        },
        ],
        rules: [{
          required: true,
        }]
      },
      {
        type: 'action'
      }
      ],
      creationNum: 'x',
      collectionNum: 'x',
      loading: false,
      //下载模板
      uploadFormSchema:[{
        type: 'textarea',
        label: '买家地址：',


      },

      {
        type: 'action'
      }
      ],
      //上传表格
      uploadformData:{

      },
      uploadformSchema:{

      },



      createformSchema: [
        {
          label: '',
          slot: 'token_view',
          show: {
            relationField: 'fromType',
            value: '2'
          },
        },
       {
         label: '快捷操作：',
         slot: 'jieshou',
       },

      ],

      createformData:{
          fromType:1,
          isTiming:2
      },
    }
  },
  mounted() {

  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    async submit() {
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        this.sub()
      })
    },
    async sub() {
      console.table(this.formData)
      const data = {
        ...this.formData,
      }
      const {
        status
      } = await this.$api.ugcCreateOrder(data)
      if (status.code === 0) {
        this.$message.success('订单创建成功')
        this.routerBack()
      } else {

      }
    },

    //上传表格
    async downloadTemplate(templateTag) {
      console.log("templateTag",templateTag)
      const {
        status,
        result
      } = await this.$api.userCenterDownLoadTemplate({
        templateTag
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    //下载模板
    async downloadExcel (res) {
      window.open(res)
      this.$message.success('下载成功')
    },
    //
    async uploadForm(res){
      console.log(res)
      if(!res){
        this.$message.warning('请上传模板')
      }
      const {status,result} = await this.$api.batchCreateOrder({
        importUrl:res
      })
      if(status.code == 0 ){
        this.createformData.userImportUrl=''
        this.details=false
        this.$message.success('上传模板成功')

      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding-top: 80px;
}

.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
  .flex_div{
    display: flex;
    justify-content: center;
    align-items: flex-start;
    width:700px;
    height:50px;
    margin-top:10px;
    margin-left: 50px;
  }
  .flex{
    display: flex;
  }
  .flex_button{
    margin-left: 370px;

  }
  .flex_in{
    font-size: 14px;
    color:rgba(0,0,0,0.8);
    position:relative;
    display:inline-block
  }
  .flex_in::before{
    content:'*';
    color:red;
    position:absolute;
    left:-10px
  }
</style>
