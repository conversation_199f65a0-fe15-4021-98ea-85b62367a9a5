<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="true" :table-data="tableData" :loading="loading"
      :showSelection="true" :multipleSelection.sync="multipleSelection">
      <template #action-header>
        <el-button type="primary" size="mini" @click="openDialog(null, 1)">添加</el-button>
      </template>
      <template #action="scope">
        <el-button @click="openDialog(scope.row, 2)" type="text">编辑</el-button>
        <el-button @click="deleteList(false, scope.row.id)" type="text">删除</el-button>
      </template>
    </common-table>
    <div class="button">
      <el-button type="primary" @click="deleteList(true, null)">批量删除</el-button>
    </div>
    <el-dialog title="" :visible.sync="dialogVisible" width="1000px" @close="dialogVisible = false">
      <common-form :data="formData" :schema="formSchema" :loading="loading" :labelPosition="'top'" :submit="submit"
        :isBack="true" @nav_back="nav_back">
        <template #produceTime="scope">
          <div>
            <el-button @click="addItem(1)" type=" " size="mini">新增</el-button>
          </div>
          <div class="div_ul">
            <div class="li" v-for="(item, index) in produceTimeArray" :key="index">
              <el-input-number v-model="item.value" :step="0.01" :min="0.01" :precision="2">
              </el-input-number>
              <div class="icon">
                <i class="el-icon-error" @click="delItem(produceTimeArray, index)"></i>
              </div>
            </div>
          </div>
        </template>
        <template #produceNum="scope">
          <div>
            <el-button @click="addItem(2)" type=" " size="mini">新增</el-button>
          </div>
          <div class="div_ul">
            <div class="li" v-for="(item, index) in produceNumArray" :key="index">
              <el-input-number v-model="item.value" :step="0.01" :min="0.01" :precision="2">
              </el-input-number>
              <div class="icon">
                <i class="el-icon-error" @click="delItem(produceNumArray, index)"></i>
              </div>
            </div>
          </div>
        </template>
        <template #animalTable="scope">
          <common-table :table-schema="dropOutTableSchema" :showIndex="false" :table-data="dropOutTableData"
            :loading="loading" :showSelection="true" :multipleSelection.sync="multipleSelectionDropOut" :rows.sync="rows"
            @selection-change="handleSelectionChangeDropOut" ref="multipleTable">
          </common-table>
        </template>
      </common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from "@/components/CommonQuery_h";
import CommonTable from "@/components/CommonTable";
import CommonForm from "@/components/CommonForm";
export default {
  name: "sprog",
  data() {
    return {
      info: {
        lock: 1,
        freeze: 1,
        crazy: 1,
      },
      tableData: [],
      dropOutTableData: [],
      querySchema: [
        // 搜索组件架构
        {
          type: "input",
          label: "方案名称",
          placeholder: "请输入方案名称",
          field: "name",
        },
      ],
      tableSchema: [
        // 表格架构
        {
          label: "ID",
          field: "id",
          width: "100px",
        },
        {
          label: "方案名称",
          field: "name",
          width: "200px",
        },
        {
          label: "动物倍率",
          field: "animalMultiplier",
          width: "120px",
        },
        {
          label: "时间间隔（秒）",
          field: "gapSecond",
          width: "120px",
        },
        {
          label: "产生个数",
          field: "num",
          width: "120px",
        },
        {
          label: "最多存活",
          field: "maxNum",
          width: "110px",
        },
        {
          label: "状态",
          field: "status",
          width: "110px",
        },
        {
          label: "操作",
          slot: "action",
          headerSlot: "action-header",
          width: "200px",
        },
      ],
      dropOutTableSchema: [
        // 表格架构
        {
          label: "动物ID",
          field: "id",
          width: "100px",
        },
        {
          label: "动物昵称",
          field: "name",
          width: "200px",
        },
        {
          label: "动物倍数",
          field: "multiplierMinMax",
          width: "120px",
        },
        {
          label: "移动速度",
          field: "moveSpeed",
          width: "120px",
        },
        {
          label: "草团权重",
          field: "grassWeight",
          width: "120px",
        },
        {
          label: "牛奶碎片权重",
          field: "milkPieceWeight",
          width: "110px",
        },
        {
          label: "道具权重",
          field: "toolWeight",
          width: "110px",
        },
      ],
      query: {},
      loading: false,
      dialogVisible: false,
      form: {
        id: "",
        name: "",
        multiplier: "",
        moveSpeed: "",
        grassWeight: 0,
        milkPieceWeight: 0,
        toolWeight: 0,
      },
      openInfo: {
        lock: 0,
        freeze: 0,
        crazy: 0,
      },
      type: 1, //1.添加 2.修改
      idArray: [],
      multipleSelection: [],
      formSchema: [
        {
          type: "input",
          label: "方案名称",
          field: "name",
          rules: [
            {
              required: true,
              message: "请输入方案名称",
              trigger: "blur",
            },
          ],
        },
        {
          label: "产生间隔(秒)",
          slot: "produceTime",
          rules: [
            {
              required: true,
              message: "请输入产生间隔(秒)",
              trigger: "blur",
            },
          ],
        },
        {
          label: "产生个数(个)",
          slot: "produceNum",
          rules: [
            {
              required: true,
              message: "请输入产生个数(个)",
              trigger: "blur",
            },
          ],
        },
        {
          type: "number-input",
          label: "最多存活(个)",
          field: "maxNum",
          rules: [
            {
              required: true,
              message: "请输入最多存活(个)",
              trigger: "blur",
            },
          ],
        },
        {
          label: "状态",
          field: "status",
          type: "radio",
          options: [
            {
              label: "禁用",
              value: 0,
            },
            {
              label: "启用",
              value: 1,
            },
          ],
          rules: [
            {
              required: true,
              trigger: "blur",
            },
          ],
        },
        {
          label: "动物倍率",
          slot: "animalTable",
          rules: [
            {
              required: true,
              message: "请输入动物倍率",
              trigger: "blur",
            },
          ],
        },
        {
          type: "action",
        },
      ],
      formData: {
        status: 0,
      },
      tableData2: [],
      produceTimeArray: [{
        value: "1",
      }],
      produceNumArray: [{
        value: "1",
      }],
      multipleSelectionDropOut: [],
      rows: []
    };
  },
  components: {
    CommonQuery,
    CommonTable,
    CommonForm
  },
  mounted() {
    this.getList();
    this.getDropOutList()
  },
  methods: {
    // 过滤查询
    onQueryChange(data) {
      this.query = data;
      this.getList();
    },
    /**
     * 获取通用道具权重查询
     */
    async getWeightInf() {
      const res = await this.$api.toolWeightInfo({});
      if (res.status.code === 0) {
        this.info = res.result;
      }
    },
    /**
     *修改通用道具权重
     */
    async updateWeightInf() {
      this.dialogVisible = true;
      const res = await this.$api.toolWeightUpdate(this.openInfo);
      if (res.status.code === 0) {
        this.dialogVisible = false;
        this.info = this.openInfo;
        this.$message({
          type: "success",
          message: "修改成功",
        });
      } else {
        this.dialogVisible = false;
      }
    },
    /**
     * 获取动物出场列表
     */
    async getList() {
      const res = await this.$api.animalEnterList(this.query);
      if (res.status.code === 0) {
        this.tableData = res.result.list;
      }
    },
    /**
     * 获取动物掉落权重列表
     */
    async getDropOutList() {
      const res = await this.$api.animalList({});
      if (res.status.code === 0) {
        this.dropOutTableData = res.result.list;
      }
    },
    /**
     *修改弹窗
     */
    openDialog(item, type) {
      this.type = type;
      this.dialogVisible = true;
      if (type == 2) {
        this.formData = item;

        let gapSecond = [], num = [];
        JSON.parse(this.formData.gapSecond).forEach((item) => {
          gapSecond.push({
            value: item,
          });
        });
        JSON.parse(this.formData.num).forEach((item) => {
          num.push({
            value: item,
          });
        });
        this.produceNumArray = num;
        this.produceTimeArray = gapSecond;
        let rows = []
        JSON.parse(this.formData.animalIdStr).forEach((item) => {
          this.dropOutTableData.forEach(itemm => {
            if (item == itemm.id) {
              rows.push(itemm);
            }
          });
        });
        console.log(rows);
        if (rows.length > 0) {
          this.rows = rows
        }
      }
    },
    /**
      *新增修改动物出场配置
      */
    async submit() {
      let num = [], gapSecond = [], animalIdStr = [];
      this.produceTimeArray.forEach((item) => {
        gapSecond.push(item.value);
      });
      this.produceNumArray.forEach((item) => {
        num.push(item.value);
      });
      this.multipleSelectionDropOut.forEach((item) => {
        animalIdStr.push(item.id);
      });
      if (this.type == 1) {
        const res = await this.$api.animalEnterAdd({
          ...this.formData,
          gapSecond: JSON.stringify(gapSecond),
          num: JSON.stringify(num),
          animalIdStr: JSON.stringify(animalIdStr)
        });
        if (res.status.code === 0) {
          this.dialogVisible = false;
          this.formData = {
            status: 0,
          };
          this.getList();
          this.$message({
            type: "success",
            message: "新增成功",
          });
        } else {
        }
      } else if (this.type == 2) {
        const res = await this.$api.animalEnterUpdate({
          ...this.formData,
          gapSecond: JSON.stringify(gapSecond),
          num: JSON.stringify(num),
          animalIdStr: JSON.stringify(animalIdStr)
        });
        if (res.status.code === 0) {
          this.dialogVisible = false;
          this.formData = {
            status: 0,
          };
          this.getList();
          this.$message({
            type: "success",
            message: "修改成功",
          });
        } else {
        }
      }
    },
    /**
     * 二次弹窗确认 动物掉落权重批量删除 二次确认
     * @param {boolean} isBatch 是否批量
     * @param {string} id 批量删除id
     */
    async deleteList(isBatch, id) {
      let idd = [id];
      const str = [];
      if (isBatch) {
        this.multipleSelection.forEach((item) => {
          str.push(item.id);
        });
      }
      console.log(str);
      const res = await this.$api.animalEnterBatchDelete({
        idList: isBatch ? JSON.stringify(str) : JSON.stringify(idd),
      });
      if (res.status.code === 0) {
        this.getList();
        this.$message({
          type: "success",
          message: "删除成功",
        });
      }
    },
    addItem(type) {
      if (type == 1) {
        this.produceTimeArray.push({
          value: "1",
        });
      } else {
        this.produceNumArray.push({
          value: "1",
        });
      }
    },
    /**
     * 删除指定tableData2
     */
    delItem(item, index) {
      item.splice(index, 1);
    },
    openMel() {
      this.openInfo.lock = this.info.lock;
      this.openInfo.freeze = this.info.freeze;
      this.openInfo.crazy = this.info.crazy;
      this.dialogVisible = true;
    },
    closeMel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  background: #ccc;
  height: 50px;
  line-height: 50px;
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
  margin: 20px 0px;
}

.button {
  margin: 20px 0px;
}

.body {
  .flex_left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }
}

.div_ul {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px;
  flex-wrap: wrap;

  .li {
    width: 180px;
    border: 1px solid #ccc;
    height: 50px;
    line-height: 50px;
    text-align: center;
    margin-right: 20px;
    border-radius: 5px;
    margin-bottom: 10px;
    position: relative;

    .icon {
      width: 40px;
      height: 30px;
      position: absolute;
      right: -20px;
      top: -20px;

      i {
        font-size: 30px;
        cursor: pointer;
      }
    }
  }
}
</style>
  
 