<template>
  <d2-container class="page">
    <div class="head">通用道具权重</div>
    <div class="button">
      <el-button type="primary" @click="openMel">编辑</el-button>
    </div>
    <div>
      <div class="div_ul">
        <div class="li">道具</div>
        <div class="li">锁定</div>
        <div class="li">冰冻</div>
        <div class="li">狂暴</div>
      </div>
      <div class="div_ul">
        <div class="li">道具权重</div>
        <div class="li">{{ info.lock }}</div>
        <div class="li">{{ info.freeze }}</div>
        <div class="li">{{ info.crazy }}</div>
      </div>
    </div>
    <div class="head">动物掉落权重</div>
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading"
      :showSelection="true" :multipleSelection.sync="multipleSelection">
      <template #action-header>
        <el-button type="primary" size="mini" @click="openDialog(null, 1)">添加</el-button>
      </template>
      <template #action="scope">
        <el-button @click="openDialog(scope.row, 2)" type="text">编辑</el-button>
        <el-button @click="deleteList(false, scope.row.id)" type="text">删除</el-button>
      </template>
    </common-table>
    <div class="button">
      <el-button type="primary" @click="deleteList(true, null)">批量删除</el-button>
    </div>
    <el-dialog title="" :visible.sync="dialogVisible" width="450px" @close="dialogVisible = false">
      <el-descriptions title="道具权重编辑" direction="horizontal" :column="1" border>
        <el-descriptions-item label="道具名称">道具权重</el-descriptions-item>
        <el-descriptions-item label="锁定"><el-input-number v-model="openInfo.lock" :min="1"
            :step="1"></el-input-number></el-descriptions-item>
        <el-descriptions-item label="冰冻"><el-input-number v-model="openInfo.freeze" :min="1"
            :step="1"></el-input-number></el-descriptions-item>
        <el-descriptions-item label="狂暴"><el-input-number v-model="openInfo.crazy" :min="1"
            :step="1"></el-input-number></el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateWeightInf">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="" :visible.sync="dialogVisible2" width="850px" @close="closeMel">
      <el-descriptions title="动物掉落权重" direction="horizontal" :column="1" border>
        <el-descriptions-item label="动物id">
          <el-input v-model="form.id" type="number" :disabled="type == 2"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="动物昵称">
          <el-input v-model="form.name" :disabled="type == 2"></el-input></el-descriptions-item>
        <el-descriptions-item label="动物速度">
          <el-input v-model="form.moveSpeed" :disabled="type == 2"></el-input></el-descriptions-item>
        <el-descriptions-item label="动物倍数">
          <el-button type="primary" size="mini" @click="addEventListener()" v-if="type == 1">新增一行</el-button>
          <el-table :data="tableData_bs" border style="width: 650px">
            <el-table-column type="index" label="倍数序号" align="center" >
            </el-table-column>
            <el-table-column prop="minMultiplier" label="最小倍数" width="180px" align="center">
              <template scope="scope">
                <el-input-number size="mini" v-model="scope.row.minMultiplier"  :min="1"
                  :step="1"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="maxMultiplier" label="最大倍数" width="180px" align="center">
              <template scope="scope">
                <el-input-number size="mini" v-model="scope.row.maxMultiplier"  :min="1"
                  :step="1"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="weight" label="出现权重" align="center">
              <template scope="scope">
                <el-input v-model="scope.row.weight"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="gap" label="倍数间隔" align="center">
              <template scope="scope">
                <el-input v-model="scope.row.gap"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="草团权重"><el-input-number v-model="form.grassWeight" :min="0"
            :step="1"></el-input-number></el-descriptions-item>
        <el-descriptions-item label="牛奶碎片权重"><el-input-number v-model="form.milkPieceWeight" :min="0"
            :step="1"></el-input-number></el-descriptions-item>
        <el-descriptions-item label="道具权重"><el-input-number v-model="form.toolWeight" :min="0"
            :step="1"></el-input-number></el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from "@/components/CommonQuery_h";
import CommonTable from "@/components/CommonTable";
export default {
  name: "sprog",
  data() {
    return {
      info: {
        lock: 1,
        freeze: 1,
        crazy: 1,
      },
      tableData: [],
      querySchema: [
        // 搜索组件架构
        {
          type: "input",
          label: "动物昵称模糊搜索",
          placeholder: "请输入动物昵称",
          field: "name",
        },
      ],
      tableSchema: [
        // 表格架构
        {
          label: "动物ID",
          field: "id",
          width: "100px",
        },
        {
          label: "动物昵称",
          field: "name",
          width: "200px",
        },
        {
          label: "动物倍数",
          field: "multiplierMinMax",
          width: "120px",
        },
        {
          label: "移动速度",
          field: "moveSpeed",
          width: "120px",
        },
        {
          label: "草团权重",
          field: "grassWeight",
          width: "120px",
        },
        {
          label: "牛奶碎片权重",
          field: "milkPieceWeight",
          width: "110px",
        },
        {
          label: "道具权重",
          field: "toolWeight",
          width: "110px",
        },
        {
          label: "操作",
          slot: "action",
          headerSlot: "action-header",
          width: "200px",
        },
      ],
      query: {},
      loading: false,
      dialogVisible: false,
      dialogVisible2: false,
      form: {
        id: "",
        name: "",
        multiplier: "",
        moveSpeed: "",
        grassWeight: 0,
        milkPieceWeight: 0,
        toolWeight: 0,
      },
      openInfo: {
        lock: 0,
        freeze: 0,
        crazy: 0,
      },
      type: 1, //1.添加 2.修改
      idArray: [],
      multipleSelection: [],
      tableData_bs: [{
        index: 1,
        minMultiplier: '1',
        maxMultiplier: '1',
        weight: '1',
        gap: '1'
      }]
    };
  },
  components: {
    CommonQuery,
    CommonTable,
  },
  mounted() {
    this.getWeightInf();
    this.getList();
  },
  methods: {
    // 过滤查询
    onQueryChange(data) {
      this.query = data;
      this.getList();
    },
    /**
     * 获取通用道具权重查询
     */
    async getWeightInf() {
      const res = await this.$api.toolWeightInfo({});
      if (res.status.code === 0) {
        this.info = res.result;
      }
    },
    /**
     *修改通用道具权重
     */
    async updateWeightInf() {
      this.dialogVisible = true;
      const res = await this.$api.toolWeightUpdate(this.openInfo);
      if (res.status.code === 0) {
        this.dialogVisible = false;
        this.info = this.openInfo;
        this.$message({
          type: "success",
          message: "修改成功",
        });
      } else {
        this.dialogVisible = false;
      }
    },
    /**
     * 获取动物掉落权重列表
     */
    async getList() {
      const res = await this.$api.animalList(this.query);
      if (res.status.code === 0) {
        this.tableData = res.result.list;
      }
    },
    /**
     *修改弹窗
     */
    openDialog(item, type) {
      this.type = type;
      this.dialogVisible2 = true;
      if (type == 2) {
        this.form.id = item.id;
        this.form.name = item.name;
        this.form.multiplier = item.multiplier;
        this.form.moveSpeed = item.moveSpeed;
        this.form.grassWeight = item.grassWeight;
        this.form.milkPieceWeight = item.milkPieceWeight;
        this.form.toolWeight = item.toolWeight;
        this.tableData_bs = JSON.parse(item.multiplier);
      }
    },
    /**
     *修改动物掉落权重
     */
    async submit() {
      this.dialogVisible2 = true;
      let multiplier = JSON.stringify(this.tableData_bs);
      if (this.type == 1) {
        const res = await this.$api.animalAdd({
          ...this.form,
          multiplier
        });
        if (res.status.code === 0) {
          this.form = {
            id: "",
            name: "",
            multiplier: "",
            moveSpeed: "",
            grassWeight: 0,
            milkPieceWeight: 0,
            toolWeight: 0,
          };
          this.tableData_bs = [{
            index: 1,
            minMultiplier: '1',
            maxMultiplier: '1',
            weight: '1',
            gap: '1'
          }]
          this.dialogVisible2 = false;
          this.getList();

          this.$message({
            type: "success",
            message: "新增成功",
          });
        } else {
          this.dialogVisible = false;
        }
      } else if (this.type == 2) {
        const res = await this.$api.animalUpdate({
          ...this.form,
          multiplier
        });
        if (res.status.code === 0) {
          this.dialogVisible2 = false;
          this.getList();
          this.form = {
            id: "",
            name: "",
            multiplier: "",
            moveSpeed: "",
            grassWeight: 0,
            milkPieceWeight: 0,
            toolWeight: 0,
          };
          this.tableData_bs = [{
            index: 1,
            minMultiplier: '1',
            maxMultiplier: '1',
            weight: '1',
            gap: '1'
          }]
          this.$message({
            type: "success",
            message: "修改成功",
          });
        } else {
          this.dialogVisible = false;
        }
      }
    },
    /**
     * 二次弹窗确认 动物掉落权重批量删除 二次确认
     * @param {boolean} isBatch 是否批量
     * @param {string} id 批量删除id
     */
    async deleteList(isBatch, id) {
      let idd = [id];
      const str = [];
      if (isBatch) {
        this.multipleSelection.forEach((item) => {
          str.push(item.id);
        });
      }
      console.log(str);
      const res = await this.$api.animalBatchDelete({
        idList: isBatch ? JSON.stringify(str) : JSON.stringify(idd),
      });
      if (res.status.code === 0) {
        this.getList();
        this.$message({
          type: "success",
          message: "删除成功",
        });
      }
    },

    openMel() {
      this.openInfo.lock = this.info.lock;
      this.openInfo.freeze = this.info.freeze;
      this.openInfo.crazy = this.info.crazy;
      this.dialogVisible = true;
    },
    closeMel() {
      this.dialogVisible = false;
    },
    /**
     * 表格新增行
     */
    addEventListener() {
      this.tableData_bs.push({
        index: this.tableData_bs.length + 1,
        minMultiplier: '1',
        maxMultiplier: '1',
        weight: '1',
        gap: '1'
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  background: #ccc;
  height: 50px;
  line-height: 50px;
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
  margin: 20px 0px;
}

.button {
  margin: 20px 0px;
}

.body {
  .flex_left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }
}

.div_ul {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #ccc;
  width: 800px;

  .li {
    width: 200px;
    border: 1px solid #ccc;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-bottom: none;
  }
}
</style>
