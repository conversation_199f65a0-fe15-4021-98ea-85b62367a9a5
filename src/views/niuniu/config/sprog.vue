<template>
  <d2-container class="page">
    <div class="head">新手池</div>
    <div class="body">
      <div class="flex_left">
        <P class="title">税收</P>
        <div class="input_div">
          <el-input v-model="form.taxRatio" type="number"></el-input>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">库存始初值</P>
        <div class="input_div">
          <el-input v-model="form.initInventory" type="number"></el-input>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">出新手池进入过渡池条件</P>
        <div class="flex_left" style="margin: 0px 0px 0px 20px">
          <P>当日累计流水超过</P>
          <div class="input_div">
            <el-input
              v-model="form.maxInventoryNumOut"
              type="number"
            ></el-input>
          </div>
          <P>或&nbsp;&nbsp;草团数量超过</P>
          <div class="input_div">
            <el-input v-model="form.maxGrassNumOut" type="number"></el-input>
          </div>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">过渡池/正常池回到新手池条件</P>
        <div class="flex_left" style="margin: 0px 0px 0px 20px">
          <P>当日累计流水少于</P>
          <div class="input_div">
            <el-input v-model="form.minInventoryNumIn" type="number"></el-input>
          </div>
          <P>且&nbsp;&nbsp;草团数量少于</P>
          <div class="input_div">
            <el-input v-model="form.minGrassNumIn" type="number"></el-input>
          </div>
          <P>且&nbsp;&nbsp;打包草垛次数为</P>
          <div class="input_div">
            <el-input v-model="form.packageGrassNumIn" type="number"></el-input>
          </div>
        </div>
      </div>
      <P class="title">新人buff</P>
      <div class="">
        <div class="div_xinren" style="margin-bottom: 10px">
          <div class="title">条件</div>
          <div class="title">新手系数(W)min</div>
          <div class="title">新手系数(W)max</div>
        </div>
        <div class="div_xinren" style="margin-bottom: 10px">
          <div class="title"><el-button>默认按钮</el-button></div>
          <div class="num">
            <el-input-number
              v-model="form.minRatioFirst"
              :min="0.01"
              :max="10"
              :step="0.01"
              label="描述文字"
              precision="2"
            ></el-input-number>
          </div>
          <div class="num">
            <el-input-number
              v-model="form.maxRatioFirst"
              :min="0.01"
              :max="10"
              :step="0.01"
              label="描述文字"
            ></el-input-number>
          </div>
        </div>
        <div class="div_xinren">
          <div class="title"><el-button>非首次</el-button></div>
          <div class="num">
            <el-input-number
              v-model="form.minRatio"
              :min="0.01"
              :max="10"
              :step="0.01"
              label="描述文字"
            ></el-input-number>
          </div>
          <div class="num">
            <el-input-number
              v-model="form.maxRatio"
              :min="0.01"
              :max="10"
              :step="0.01"
              label="描述文字"
            ></el-input-number>
          </div>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">库存配置</P>
        <el-button @click="addRow">新增一行</el-button>
      </div>
      <div class="table">
        <el-table :data="tableData" size="medium" style="width: 100%">
          <el-table-column prop="num" label="库存区间" align="center">
            <template slot-scope="scope">
              <el-input-number
                style="width: 250px"
                v-model="scope.row.num"
                :min="1"
                label="描述文字"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="valueP" label="P值" align="center">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.valueP"
                :min="0.01"
                :max="10"
                :step="0.01"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                @click.native.prevent="deleteRow(scope.$index, tableData)"
                type="text"
                
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="button" style="margin-top: 100px; text-align: center">
        <el-button type="primary" size="medium" @click="handleSubmit"
          >保存</el-button
        >
      </div>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: "sprog",
  data() {
    return {
      tableData: [],
      form: {},
    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    /**
     * 新增一行
     */
    addRow() {
      this.tableData.push({
        num: 0,
        valueP: 0,
      });
    },
    /**
     * 删除表格行 二次确认
     * @param {*} index
     * @param {*} tableData
     */
    deleteRow(index, tableData) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        tableData.splice(index, 1);
      });
    },
    async getInfo() {
      const res = await this.$api.newPoolInfo({});
      if (res.status.code === 0) {
        this.form = res.result;
        this.tableData = res.result.inventoryList;
        console.log(this.form);
      }
    },
    /**
     * 保存提交 调用接口 增加二次确认弹窗
     * @param {*} form
     */
    async handleSubmit() {
      this.$confirm("此操作将永久保存该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let inventoryList = null;
        let inventoryListStr = JSON.stringify(this.tableData);
        const res = await this.$api.newPoolUpdate({
          ...this.form,
          inventoryListStr,
          inventoryList,
        });
        if (res.status.code === 0) {
          this.$message.success("保存成功");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  background: #ccc;
  height: 50px;
  line-height: 50px;
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
}
.body {
  .flex_left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }
}
.input_div {
  width: 250px;
  margin: 0px 20px;
}
.title {
  font-weight: 600;
  width: 250px;
}
.div_xinren {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  div {
    width: 250px;
    text-align: center;
    margin-right: 50px;
    .el-button {
      width: 160px;
    }
  }
}
.table {
  width: 1200px;
}
::v-deep .el-table__cell {
  padding: 12px !important;
}
</style>
  
 