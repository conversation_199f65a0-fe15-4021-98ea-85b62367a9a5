<template>
  <d2-container class="page">
    <div class="head">普通池</div>
    <div class="body">
      <div class="flex_between">
        <div class="flex_left">
          <P class="title">税收</P>
          <div class="input_div">
            <el-input v-model="form.taxRatio" type="number"></el-input>
          </div>
          <P class="title">进入仓库 (奖池)税收</P>
          <div class="input_div">
            <el-input
              v-model.number="form.inventoryRatio"
              precision="3"
              type="number"
            ></el-input>
          </div>
        </div>
        <div class="right">
          <el-switch
            v-model="form.isOpenDepositBuff"
            style="margin-right: 20px"
            active-text="充值buff"
          >
          </el-switch>
          <el-switch
            v-model="form.isOpenT"
            active-text="个人T值(波动系数)"
          ></el-switch>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">库存始初值</P>
        <div class="input_div">
          <el-input v-model="form.initInventory" type="number"></el-input>
        </div>
      </div>
      <div class="flex_left">
        <P class="title">库存配置</P>
        <el-button @click="addRow">新增一行</el-button>
      </div>
      <div class="table">
        <el-table :data="tableData" size="medium" style="width: 100%">
          <el-table-column prop="num" label="库存区间" align="center">
            <template slot-scope="scope">
              <el-input-number
                style="width: 250px"
                v-model="scope.row.num"
                :min="0.01"
                label="描述文字"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="valueP" label="P值" align="center">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.valueP"
                :min="0.01"
                :max="10"
                :step="0.01"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                @click.native.prevent="deleteRow(scope.$index, tableData)"
                type="text"
                
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="button" style="margin-top: 100px; text-align: center">
        <el-button type="primary" size="medium" @click="handleSubmit"
          >保存</el-button
        >
      </div>
    </div>
  </d2-container>
</template>
  
  <script>
export default {
  name: "sprog",
  data() {
    return {
      tableData: [],
      form: {},
    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    /**
     * 删除表格行 二次确认
     * @param {*} index
     * @param {*} tableData
     */
    deleteRow(index, tableData) {
      this.$confirm("此操作将永久删除该行数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        tableData.splice(index, 1);
      });
    },
    async getInfo() {
      const res = await this.$api.normalPoolInfo({});
      if (res.status.code === 0) {
        this.form = res.result;
        this.tableData = res.result.inventoryList;
        this.form.isOpenDepositBuff =
          res.result.isOpenDepositBuff == 1 ? true : false;
        this.form.isOpenT = res.result.isOpenT == 1 ? true : false;
        console.log(this.form);
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: "login",
        });
      } else {
        this.$message.error(res.status.msg);
      }
    },
    /**
     * 保存提交 调用接口 增加二次确认弹窗
     * @param {*} form
     */
    async handleSubmit() {
      this.$confirm("此操作将永久保存该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let inventoryList = null;
        let isOpenDepositBuff = this.form.isOpenDepositBuff ? 1 : 0;
        let isOpenT = this.form.isOpenT ? 1 : 0;
        let inventoryListStr = JSON.stringify(this.tableData);
        const res = await this.$api.normalPoolUpdate({
          ...this.form,
          inventoryListStr,
          inventoryList,
          isOpenDepositBuff,
          isOpenT,
        });
        if (res.status.code === 0) {
          this.$message.success("保存成功");
        } else if (res.status.code === 1002) {
          this.$router.push({
            name: "login",
          });
        } else {
          this.$message.error(res.status.msg);
        }
      });
    },
    /**
     * 新增一行
     */
    addRow() {
      this.tableData.push({
        num: 0,
        valueP: 0,
      });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.head {
  background: #ccc;
  height: 50px;
  line-height: 50px;
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
}
.body {
  .flex_left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }
  .flex_between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .right {
      padding-right: 20px;
    }
  }
}
.input_div {
  width: 250px;
  margin: 0px 20px;
}
.title {
  font-weight: 600;
  width: 250px;
}
.div_xinren {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  div {
    width: 250px;
    text-align: center;
    margin-right: 50px;
    .el-button {
      width: 160px;
    }
  }
}
.table {
  width: 1200px;
}
::v-deep .el-table__cell {
  padding: 12px !important;
}
</style>
    
   