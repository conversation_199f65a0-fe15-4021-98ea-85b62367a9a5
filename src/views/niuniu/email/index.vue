<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading"
            :multipleSelection.sync="multipleSelection" :showSelection="true">
            <template #action-header>
                <el-button type="primary" size="mini" @click="nav_add()">添加</el-button>
            </template>
            <template #action="scope">
                <el-button @click="nav_updata(scope.row)" type="text">编辑</el-button>
                <el-button @click="deleteList(false, scope.row.id)" type="text">删除</el-button>
            </template>
            <template #chakan="scope">
                <el-button type="text" @click="showDetail(scope.row)">查看</el-button>
            </template>
            <template #accessory="scope">
                <el-button type="text" @click="showAccessory(scope.row)">查看</el-button>
            </template>
            <template #emailMode="scope">
                <div>
                    <el-button v-if="scope.row.sendType == 1" type="primary" size="mini"
                        @click="nav_details(scope.row)">全服</el-button>
                    <el-button v-if="scope.row.sendType == 2" type="primary" size="mini" @click="nav_details(scope.row)">个人
                        <span v-if="scope.row.sendToNum">/ {{ scope.row.sendToNum
                        }}</span></el-button>
                </div>
            </template>

        </common-table>
        <div class="button">
            <el-button type="primary" @click="deleteList(true, null)">批量删除</el-button>
        </div>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>
        <el-dialog title="" :visible.sync="dialogVisible" width="700px" @close="colseModal">
            <common-form :data="formData" :schema="formSchema" :loading="loading" :submit="submit" :isBack="true"
                @nav_back="nav_back">
            </common-form>
        </el-dialog>
        <el-dialog title="内容查看" :visible.sync="dialogShow" width="700px">
            <div style="max-height:800px;overflow: auto;">
                <div v-if="openType == 'IMAGE'">
                    <img :src="content" style="width: 100%;height: 100%;">
                </div>
                <div v-else>
                    <div v-html="content"></div>
                </div>
            </div>
        </el-dialog>
        <el-dialog title="附属物件" :visible.sync="dialogAccessory" width="700px">
            <common-table :table-schema="accessoryTableSchema" :showIndex="false" :table-data="accessoryTableData"
                :loading="loading" ref="multipleTable">
            </common-table>
        </el-dialog>

    </d2-container>
</template>
  
<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from "@/components/CommonForm";
import * as LocalStorage from 'lowdb/adapters/LocalStorage';
import { showAdminUserInfo } from '@/api';
export default {
    name: 'deal',
    components: {
        CommonQuery,
        CommonTable,
        CommonForm
    },
    data() {
        return {
            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query: {},
            tableSchema: [ // 表格架构
                {
                    label: '邮件ID',
                    field: 'id',
                },
                {
                    label: '邮件类型',
                    field: 'emailType',
                    type: 'tag',
                    tagMap: {
                        UPDATE: {
                            label: '更新',
                            tagType: 'info'
                        },
                        COMPENSATE: {
                            label: '补偿',
                            tagType: 'info'
                        },
                        WELFARE: {
                            label: '福利',
                            tagType: 'info'
                        },
                        OTHER: {
                            label: '其他',
                            tagType: 'info'
                        },
                    },
                },
                {
                    label: '邮件标题',
                    field: 'title',
                },
                {
                    label: '邮件内容',
                    slot: 'chakan',
                },
                {
                    label: '附件物品',
                    slot: 'accessory',
                },
                {
                    label: '过期时间',
                    field: 'expireTime',
                    width: '170',
                },
                {
                    label: '邮件模式',
                    slot: 'emailMode',
                    width: '170',
                },
                {
                    label: '发送状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '未发送',
                            tagType: 'info'
                        },
                        1: {
                            label: '已发送',
                            tagType: 'info'
                        },
                    },
                },
                {
                    label: '创建时间',
                    field: 'createAt',
                    width: '170',
                },
                {
                    label: '修改时间',
                    field: 'updateAt',
                    width: '170',
                },
                {
                    label: '发送时间',
                    field: 'sendTime',
                    width: '170',
                },
                {
                    label: '操作',
                    slot: 'action',
                    headerSlot: 'action-header',
                    width: '240px',
                    fixed: 'right'
                },
            ],
            tableData: [],
            querySchema: [ // 搜索组件架构
                {
                    type: 'select',
                    label: '邮件类型',
                    placeholder: '',
                    field: 'emailType',
                    options: [
                        {
                            label: '更新',
                            value: 'UPDATE'
                        },
                        {
                            label: '补偿',
                            value: 'COMPENSATE'
                        },
                        {
                            label: '福利',
                            value: 'WELFARE'
                        },
                        {
                            label: '其他',
                            value: 'OTHER'
                        }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '邮件ID：',
                    placeholder: '请输入邮件ID',
                    field: 'id'
                },
                {
                    type: 'select',
                    label: '邮件模式',
                    placeholder: '',
                    field: 'sendType',
                    options: [
                        {
                            label: '全服',
                            value: '1'
                        },
                        {
                            label: '个人',
                            value: '2'
                        },
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '邮件标题:',
                    placeholder: '请输入邮件标题',
                    field: 'title'
                },
            ],
            loading: false,
            query: {
            },
            dialogVisible: false,
            loading: false,
            formSchema: [
                {
                    type: 'select',
                    label: '公告类型',
                    placeholder: '',
                    field: 'emailType',
                    options: [{
                        label: '更新',
                        value: 'UPDATE'
                    },
                    {
                        label: '补偿',
                        value: 'COMPENSATE'
                    },
                    {
                        label: '福利',
                        value: 'WELFARE'
                    },
                    {
                        label: '其他',
                        value: 'OTHER'
                    }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '公告标题:',
                    placeholder: '请输入公告标题',
                    field: 'title'
                },
                {
                    type: 'textarea',
                    placeholder: '请输入模板内容',
                    label: "模板内容",
                    field: 'contentText',
                    width: '100%',
                    rules: [{
                        required: true,
                        message: '请输入模板内容',
                        trigger: 'change'
                    }],
                },
                {
                    type: "action",
                },
            ],
            formData: {
                emailType: "UPDATE",
            },
            multipleSelection: [],
            content: "",
            openType: "",
            dialogShow: false,
            dialogAccessory: false,
            accessoryTableSchema: [
                // 表格架构
                {
                    label: "物品昵称",
                    field: "name",
                    width: "100px",
                },
                {
                    label: "物品类型",
                    field: "type",
                    width: "200px",
                },
                {
                    label: "数量",
                    field: "num",
                    width: "120px",
                },
            ],
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        /**
     *修改弹窗
     */
        openDialog(item, type) {
            this.type = type;
            this.dialogVisible = true;
            if (type == 2) {
                this.formData = item;
                if (this.formData.contentType == 'TEXT') {
                    this.formData.contentText = this.formData.content
                }
            }
        },
        // 过滤查询
        onQueryChange(data) {
            this.query = data
            this.getList()
        },
        onRefresh(data) {
            this.query = data
            this.getList()
        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        async getList() {
            const res = await this.$api.emailList(this.query);
            if (res.status.code === 0) {
                this.page.totalCount = res.result.totalCount
                res.result.list.forEach(item => {
                    if (item.sendType == 2) {
                        let num = JSON.parse(item.sendTo)
                        if (num) {
                            item.sendToNum = num.length
                        }
                        // item.sendToNum = JSON.parse(sendTo.sendTo).length
                    }
                })
                this.tableData = res.result.list;

            }
        },
        nav_back() {
            this.$router.go(-1)
        },
        colseModal() {
            this.dialogVisible = false;
            this.formData = {
                status: 0,
                contentType: 'IMAGE',
                isTiming: 1,
                type: "NORMAL",
                content: ""
            };
        },
        /**
     *修改动物掉落权重
     */
        async submit() {
            if (this.formData.contentType == 'TEXT') {
                this.formData.content = this.formData.contentText
            }
            if (this.type == 1) {
                const res = await this.$api.emailTemplateAdd({
                    ...this.formData,
                });
                if (res.status.code === 0) {
                    this.dialogVisible = false;
                    this.formData = {
                        status: 0,
                        contentType: 'IMAGE',
                        isTiming: 1,
                        type: "NORMAL",

                    };
                    this.getList();
                    this.$message({
                        type: "success",
                        message: "新增成功",
                    });
                }
            } else if (this.type == 2) {
                const res = await this.$api.emailTemplateUpdate({
                    ...this.formData,
                });
                if (res.status.code === 0) {
                    this.dialogVisible = false;
                    this.formData = {
                        status: 0,
                        contentType: 'IMAGE',
                        isTiming: 1,
                        type: "NORMAL",
                        content: ""
                    };
                    this.getList();
                    this.$message({
                        type: "success",
                        message: "修改成功",
                    });
                }
            }
        },
        /**
    * 二次弹窗确认 动物掉落权重批量删除 二次确认
    * @param {boolean} isBatch 是否批量
    * @param {string} id 批量删除id
    */
        async deleteList(isBatch, id) {
            let idd = [id];
            const str = [];
            if (isBatch) {
                this.multipleSelection.forEach((item) => {
                    str.push(item.id);
                });
            }
            console.log(str);
            const res = await this.$api.emailBatchDelete({
                idList: isBatch ? JSON.stringify(str) : JSON.stringify(idd),
            });
            if (res.status.code === 0) {
                this.getList();
                this.$message({
                    type: "success",
                    message: "删除成功",
                });
            }
        },
        /**
         * 查看详情
         */
        showDetail(item) {
            this.content = item.content
            this.openType = item.contentType
            this.dialogShow = true

        },
        //修改
        nav_updata(item) {
            localStorage.setItem('email_details', JSON.stringify(item))
            this.$router.push({
                name: 'addAndUpdata',
                query: {
                    type: 'updata',
                }
            })
        },
        //增加
        nav_add() {
            this.$router.push({
                name: 'addAndUpdata',
            })

        },
        /**
        * 查看附件物品
        */
        showAccessory(item) {
            this.accessoryTableData = JSON.parse(item.attachment)
            this.dialogAccessory = true
        },

        //跳转明细
        nav_details(item) {
            this.$router.push({
                name: 'niuniuEmailDetails',
                query: {
                    id: item.id
                }
            })
        },
    }
}
</script>
  
<style lang="scss" scoped>
.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
}
</style>
  