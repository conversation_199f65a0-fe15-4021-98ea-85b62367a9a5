<template>
    <d2-container class="page">
        <common-form :data="formData" :schema="formSchema" :loading="loading" :isSeleteChange="true"
            @changeSelect="changeSelect" :submit="submit" label-width="300px">
            <template #accessory="scope">
                <el-button type="primary" style="margin-bottom: 20px;" size="mini"
                    @click="openDialog(null, 1)">添加物品</el-button>
                <common-table :table-schema="accessoryTableSchema" :showIndex="false" :table-data="accessoryTableData"
                    :loading="loading" ref="multipleTable">
                    <template #action="scope">
                        <el-button type="primary" size="mini" @click="openDialog(scope.row, 2)">修改</el-button>
                        <el-button type="primary" size="mini" @click="deleteAccessoryConfirm(scope.$index)">删除</el-button>
                    </template>
                </common-table>
            </template>
        </common-form>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="700px" @close="colseModal">
            <common-form :data="formDataGoods" :schema="formSchemaGoods" @changeSelect="changeSelect" :loading="loading"
                :submit="submitGoods" :isBack="true" @nav_back="colseModal" label-width="150px">
            </common-form>
        </el-dialog>
    </d2-container>
</template>
  
<script>
import CommonForm from "@/components/CommonForm";
import CommonTable from "@/components/CommonTable";
import {
    mapActions
} from 'vuex'
export default {
    name: 'addAndUpdata',
    components: {
        CommonForm,
        CommonTable
    },
    data() {
        return {
            formSchema: [
                {
                    type: 'select',
                    label: '邮件类型',
                    placeholder: '',
                    field: 'emailType',
                    options: [{
                        label: '更新',
                        value: 'UPDATE'
                    },
                    {
                        label: '补偿',
                        value: 'COMPENSATE'
                    },
                    {
                        label: '福利',
                        value: 'WELFARE'
                    },
                    {
                        label: '其他',
                        value: 'OTHER'
                    }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '标题:',
                    placeholder: '请输入标题',
                    field: 'title',
                    rules: [{
                        required: true,
                        message: '请输入标题',
                        trigger: 'change'
                    }],
                },
                {
                    type: 'radio',
                    label: '内容:',
                    field: 'isTemplate',
                    options: [
                        {
                            label: "不使用模板",
                            value: "0",
                        },
                        {
                            label: "使用模板",
                            value: "1",
                        },
                    ],
                    rules: [{
                        required: true,
                        message: '请输入模板内容',
                        trigger: 'change'
                    }],
                },
                {
                    type: 'select',
                    label: '',
                    field: 'templateId',
                    options: [],
                    rules: [{
                        required: true,
                    }],
                    show: {
                        relationField: 'isTemplate',
                        value: '1'
                    },
                },
                {
                    type: 'textarea',
                    placeholder: '请输入模板内容',
                    label: "",
                    field: 'content',
                    width: '500',
                    rules: [{
                        required: true,
                        message: '请输入模板内容',
                        trigger: 'change'
                    }],
                },
                {
                    type: 'radio',
                    label: '邮件模式:',
                    field: 'sendType',
                    options: [
                        {
                            label: "全服邮件",
                            value: "1",
                        },
                        {
                            label: "个人邮件",
                            value: "2",
                        },
                    ],
                    rules: [{
                        required: true,
                        message: '请输入邮件模式',
                        trigger: 'change'
                    }],
                },
                {
                    type: 'textarea',
                    placeholder: '请输入玩家ID',
                    label: "",
                    field: 'sendTo',
                    width: '500',
                    rules: [{
                        required: true,
                        message: '请输入玩家ID',
                        trigger: 'change'
                    }],
                    show: {
                        relationField: 'sendType',
                        value: '2'
                    },
                },
                {
                    label: "附件物品",
                    slot: 'accessory',
                },
                {
                    label: '过期时间：',
                    field: 'expireTime',
                    type: 'datetime',
                    placeholder: '请输入过期时间',
                    rules: [{
                        required: true,
                        message: '请输入过期时间',
                        trigger: 'change'
                    }],
                    pickerOptions: {
                        disabledDate: (time) => {
                            return time.getTime() < Date.now() - 86400000
                        }
                    }
                },
                {
                    label: '发送形式：',
                    field: 'isTiming',
                    type: 'radio',
                    options: [{
                        label: '立即发送',
                        value: 0
                    }, {
                        label: '定时发送',
                        value: 1
                    }],
                    rules: [{
                        required: true,
                        message: '请选择发送形式',
                        trigger: 'change'
                    }],

                },
                {
                    type: 'datetime',
                    label: '发送时间：',
                    field: 'sendTime',
                    placeholder: '请输入发送时间',
                    pickerOptions: {
                        disabledDate: (time) => {
                            return time.getTime() < Date.now() - 86400000
                        }
                    },
                    rules: [{
                        required: true,
                        message: '请选择发送时间',
                        trigger: 'change'
                    }],
                    show: {
                        relationField: 'isTiming',
                        value: '1'
                    },
                },
                {
                    type: "action",
                },
            ],
            formData: {
                emailType: "UPDATE",
                isTemplate: "0",
                sendType: "1",
                isTiming: 0,
                templateId: "",
                content: "",
                attachment: [],
                sendTo: "",
            },
            type: 'add',
            loading: false,
            accessoryTableSchema: [
                // 表格架构
                {
                    label: "物品昵称",
                    field: "name",
                    width: "100px",
                },
                {
                    label: "物品类型",
                    field: "type",
                    width: "200px",
                },
                {
                    label: "数量",
                    field: "num",
                    width: "120px",
                },
                {
                    label: '操作',
                    slot: 'action',
                    width: '240px',
                    fixed: 'right'
                },
            ],
            accessoryTableData: [],
            dialogVisible: false,
            title: '添加物品',
            formSchemaGoods: [
                {
                    type: 'select',
                    label: '物品类型',
                    placeholder: '',
                    field: 'type',
                    options: [{
                        label: '道具',
                        value: '道具'
                    },
                    {
                        label: '草团',
                        value: '草团'
                    },
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '物品昵称',
                    placeholder: '',
                    field: 'name',
                    options: [{
                        label: '锁定',
                        value: '锁定'
                    },
                    {
                        label: '狂暴',
                        value: '狂暴'
                    },
                    {
                        label: '冰冻',
                        value: '冰冻'
                    },
                    ],
                    rules: [{
                        required: true,
                    }],
                    show: {
                        relationField: 'type',
                        value: '道具'
                    },
                },
                {
                    type: 'inputNumber',
                    label: '物品数量',
                    placeholder: '',
                    field: 'num',
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: "action",
                },
            ],
            formDataGoods: {
                num: 1,
                name: "",
                type: "道具"
            }
        }
    },
    mounted() {
        this.getList(this.formData.emailType)
        this.isEdit = this.$route.query.type == 'updata'
        if (this.$route.query.type == 'updata') {
            this.formData = JSON.parse(localStorage.getItem('email_details'))
            console.error(this.formData)
            this.formData.isTemplate = this.formData.templateId ? "1" : "0"
            this.formData.sendType = this.formData.sendType.toString()
            this.accessoryTableData = JSON.parse(this.formData.attachment)
            this.formData.sendTo = JSON.parse(this.formData.sendTo).toString().replace(/,/g, '\n')
        }
    },
    methods: {
        ...mapActions('d2admin/page', ['close']),
        async getList(emailType, id) {
            const res = await this.$api.emailTemplateList({
                emailType,
                id: id ? id : ""
            });
            if (res.status.code === 0) {
                if (!id) {
                    res.result.list.forEach((item) => {
                        this.formSchema[3].options.push({
                            label: item.title,
                            value: item.id,
                        })
                    })
                } else {
                    console.log(res.result.list[0].content)
                    this.formData.content = res.result.list[0].content
                }

            }
        },
        /**
         * 监听表单提交
         * @param {Object} form 表单对象
         */
        async submit() {
            let sendTo, attachment;
            console.table(this.formData)
            console.error(JSON.stringify(this.formData.sendTo.split('\n')));
            if (this.formData.sendType == 2) {
                sendTo = JSON.stringify(this.formData.sendTo.split('\n'))
            }
            attachment = JSON.stringify(this.accessoryTableData)
            if (this.isEdit) {
                const res = await this.$api.emailUpdate({
                    ...this.formData,
                    sendTo,
                    attachment,
                });
                if (res.status.code === 0) {
                    this.$message.success('修改成功');
                    this.routerBack()
                }
            } else {
                const res = await this.$api.emailAdd({
                    ...this.formData,
                    sendTo,
                    attachment,
                });
                if (res.status.code === 0) {
                    this.$message.success('添加成功');
                    this.routerBack()
                }
            }

        },
        /**
         * 选择框的组件回调
         */
        changeSelect(e) {
            if (e == 'UPDATE' || e == 'COMPENSATE' || e == 'WELFARE' || e == 'OTHER') {
                this.formSchema[3].options = []
                this.formData.templateId = ""
                this.getList(e)
            } else if (e == '道具') {
                this.formDataGoods.name = ""
            } else if (e == '锁定' || e == '狂暴' || e == '冰冻') {

            } else {
                this.getList(this.formData.emailType, e)
            }
        },
        //关闭模态框
        colseModal() {
            this.formDataGoods = {
                type: "道具",
                num: 1,
                name: ""
            }
            this.dialogVisible = false;
        },
        /**
         * 模态框表单提交
         */
        submitGoods() {
            if (this.type == 1) {
                this.$message.success('物品添加');
                if (this.formDataGoods.type == "草团") {
                    this.formDataGoods.name = "草团"
                }
                this.accessoryTableData.push(this.formDataGoods);
                this.colseModal()
            } else {
                this.$message.success('物品修改');
                if (this.formDataGoods.type == "草团") {
                    this.formDataGoods.name = "草团"
                }
                this.dialogVisible = false;
            }
        },
        /**
         * 删除表格accessoryTableData中的数据 二次确认
         * @param {Object} index 索引
         */
        deleteAccessoryConfirm(index) {
            this.$confirm('此操作将永久删除该物品, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.accessoryTableData.splice(index, 1);
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },
        /**
         * 打开添加物品模态框
         */
        openDialog(item, type) {
            this.type = type;
            this.dialogVisible = true;
            if (type == 2) {
                this.title = "修改物品"
                this.formDataGoods = item
            } else {
                this.title = "添加物品"
            }
        },
        routerBack() {
            const {
                fullPath
            } = this.$route
            this.close({
                tagName: fullPath
            })
            this.$router.back()
        },
    }
}
</script>
  
<style lang="scss" scoped>
.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
}
</style>
  