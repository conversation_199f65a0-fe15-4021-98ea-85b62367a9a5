<template>
  <d2-container class="page">
    <!-- <common-query
      :query-schema="querySchema"
      :data="query"
      @onSubmit="onQueryChange"
    ></common-query> -->
    <common-table :table-schema="tableSchema" :showIndex="true" :table-data="tableData" :loading="loading"
      :showSelection="true" :multipleSelection.sync="multipleSelection">
      <template #action-header>
        <el-button type="primary" size="mini" @click="openDialog(null, 1)">添加</el-button>
      </template>
      <template #price="scope">
        {{ scope.row.minPrice }} - {{ scope.row.maxPrice }}
      </template>
      <template #gapSecond="scope">
        {{ scope.row.minGrassNum }} - {{ scope.row.maxGrassNum }}
      </template>
      <template #num="scope">
        {{ scope.row.minMultiplier }} - {{ scope.row.maxMultiplier }}
      </template>
      <template #action="scope">
        <el-button @click="openDialog(scope.row, 2)" type="text">编辑</el-button>
        <el-button @click="deleteList(false, scope.row.id)" type="text">删除</el-button>
      </template>
    </common-table>
    <div class="button">
      <el-button type="primary" @click="deleteList(true, null)">批量删除</el-button>
    </div>
    <el-dialog title="" :visible.sync="dialogVisible" width="700px" @close="dialogVisible = false">
      <common-form :data="formData" :schema="formSchema" :loading="loading" :labelPosition="'top'" :submit="submit"
        :isBack="true" @nav_back="nav_back">
        <template #buffTable="scope">
          <div>
            <el-button @click="addItem()" type=" " size="mini">新增</el-button>
          </div>
          <div class="div_ul">
            <div class="li" v-for="(item, index) in tableData2" :key="index">
              <el-input-number v-model="item.value" :step="0.01" :min="0.01" :precision="2">
              </el-input-number>
              <div class="icon">
                <i class="el-icon-error" @click="delItem(index)"></i>
              </div>
            </div>
          </div>
        </template>
      </common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from "@/components/CommonQuery_h";
import CommonTable from "@/components/CommonTable";
import CommonForm from "@/components/CommonForm";
export default {
  name: "sprog",
  data() {
    return {
      info: {
        lock: 1,
        freeze: 1,
        crazy: 1,
      },
      tableData: [],
      querySchema: [
        // 搜索组件架构
        {
          type: "input",
          label: "方案名称",
          placeholder: "请输入方案名称",
          field: "name",
        },
      ],
      tableSchema: [
        // 表格架构
        {
          label: "ID",
          field: "id",
          width: "100px",
        },
        {
          label: "获得buff概率 (%)",
          field: "successRatio",
          width: "200px",
        },
        {
          label: "单次购买草垛区间 ((RMB)",
          slot: "price",
          width: "120px",
        },
        {
          label: "发射草团区间",
          slot: "gapSecond",
          width: "120px",
        },
        {
          label: "倍率区间",
          slot: "num",
          width: "120px",
        },
        {
          label: "随机概率(%)",
          field: "randRatio",
          width: "110px",
        },
        {
          label: "BUFF值",
          field: "buffListStr",
          width: "110px",
        },
        {
          label: "操作",
          slot: "action",
          headerSlot: "action-header",
          width: "200px",
        },
      ],
      query: {},
      loading: false,
      dialogVisible: false,
      type: 1, //1.添加 2.修改
      idArray: [],
      multipleSelection: [],
      formSchema: [
        {
          type: "inputNumber",
          label: "获得buff概率(%)",
          field: "successRatio",
          rules: [
            {
              required: true,
              message: "请输入获得buff概率(%)",
              trigger: "blur",
            },
          ],
        },
        {
          type: "inputNumberRange",
          label: "单次购买草垛区间:(单位:RMB)",
          field: "minPrice",
          field2: "maxPrice",
          rules: [
            {
              required: true,
              message: "请输入单次购买草垛区间:(单位:RMB)",
              trigger: "blur",
            },
          ],
        },
        {
          type: "inputNumberRange",
          label: "发射草团区间",
          field: "minGrassNum",
          field2: "maxGrassNum",
          rules: [
            {
              required: true,
              message: "请输入单次购买草垛区间:(单位:RMB)",
              trigger: "blur",
            },
          ],
        },
        {
          type: "inputNumberRange",
          field: "minMultiplier",
          field2: "maxMultiplier",
          label: "倍率区间",
          rules: [
            {
              required: true,
              message: "请输入单次购买草垛区间:(单位:RMB)",
              trigger: "blur",
            },
          ],
        },
        {
          type: "inputNumber",
          label: "随机概率(%)",
          field: "randRatio",
          rules: [
            {
              required: true,
              message: "请输入单次购买草垛区间:(单位:RMB)",
              trigger: "blur",
            },
          ],
        },
        {
          label: "状态",
          field: "status",
          type: "radio",
          options: [
            {
              label: "关闭",
              value: 0,
            },
            {
              label: "正常",
              value: 1,
            },
          ],
        },
        {
          label: "充值buff",
          slot: "buffTable",
        },
        {
          type: "action",
        },
      ],
      formData: {
        status: 0,
      },
      tableData2: [
        {
          value: "1",
        },
      ],
    };
  },
  components: {
    CommonQuery,
    CommonTable,
    CommonForm,
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 过滤查询
    onQueryChange(data) {
      this.query = data;
      this.getList();
    },
    /**
     * 获取通用道具权重查询
     */
    async getWeightInf() {
      const res = await this.$api.toolWeightInfo({});
      if (res.status.code === 0) {
        this.info = res.result;
      }
    },
    /**
     *修改通用道具权重
     */
    async updateWeightInf() {
      this.dialogVisible = true;
      const res = await this.$api.toolWeightUpdate(this.openInfo);
      if (res.status.code === 0) {
        this.dialogVisible = false;
        this.info = this.openInfo;
        this.$message({
          type: "success",
          message: "修改成功",
        });
      } else {
        this.dialogVisible = false;
      }
    },
    /**
     * 获取动物出场列表
     */
    async getList() {
      const res = await this.$api.depositBuffList(this.query);
      if (res.status.code === 0) {
        // res.result.list.forEach((item) => {
        //   item.buffListStr = JSON.parse(item.buffListStr.toString());
        // });
        this.tableData = res.result.list;
      }
    },
    /**
     *修改弹窗
     */
    openDialog(item, type) {
      this.type = type;
      this.dialogVisible = true;
      if (type == 2) {
        let buffListStr = [];
        this.formData = item;
        console.log(JSON.parse(item.buffListStr));
        JSON.parse(this.formData.buffListStr).forEach((item) => {
          buffListStr.push({
            value: item,
          });
        });
        // console.log(buffListStr);
        this.tableData2 = buffListStr;
      }
    },
    /**
     *修改动物掉落权重
     */
    async submit() {
      let buffListStr = [];
      this.tableData2.forEach((item) => {
        buffListStr.push(item.value);
      });
      if (this.type == 1) {
        const res = await this.$api.depositBuffAdd({
          ...this.formData,
          buffListStr: JSON.stringify(buffListStr),
        });
        if (res.status.code === 0) {
          this.dialogVisible = false;
          this.formData = {
            status: 0,
          };
          this.getList();
          this.$message({
            type: "success",
            message: "新增成功",
          });
        } else {
          this.dialogVisible = false;
        }
      } else if (this.type == 2) {
        const res = await this.$api.depositBuffUpdate({
          ...this.formData,
          buffListStr: JSON.stringify(buffListStr),
        });
        if (res.status.code === 0) {
          this.dialogVisible = false;
          this.formData = {
            status: 0,
          };
          this.getList();
          this.$message({
            type: "success",
            message: "修改成功",
          });
        } else {
          this.dialogVisible = false;
        }
      }
    },
    /**
     * 二次弹窗确认 动物掉落权重批量删除 二次确认
     * @param {boolean} isBatch 是否批量
     * @param {string} id 批量删除id
     */
    async deleteList(isBatch, id) {
      let idd = [id];
      const str = [];
      if (isBatch) {
        this.multipleSelection.forEach((item) => {
          str.push(item.id);
        });
      }
      console.log(str);
      const res = await this.$api.depositBuffBatchDelete({
        idList: isBatch ? JSON.stringify(str) : JSON.stringify(idd),
      });
      if (res.status.code === 0) {
        this.getList();
        this.$message({
          type: "success",
          message: "删除成功",
        });
      }
    },
    /**
     * 新增tableData2 数据
     */
    addItem() {
      this.tableData2.push({
        value: "1",
      });
    },
    /**
     * 删除指定tableData2
     */
    delItem(index) {
      this.tableData2.splice(index, 1);
    },
    openMel() {
      this.openInfo.lock = this.info.lock;
      this.openInfo.freeze = this.info.freeze;
      this.openInfo.crazy = this.info.crazy;
      this.dialogVisible = true;
    },
    closeMel() {
      this.dialogVisible = false;
    },
    nav_back() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  background: #ccc;
  height: 50px;
  line-height: 50px;
  text-align: left;
  font-size: 20px;
  font-weight: 600;
  padding: 0px 20px;
  margin: 20px 0px;
}

.button {
  margin: 20px 0px;
}

.body {
  .flex_left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }
}

.div_ul {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 10px;
  flex-wrap: wrap;

  .li {
    width: 180px;
    border: 1px solid #ccc;
    height: 50px;
    line-height: 50px;
    text-align: center;
    margin-right: 20px;
    border-radius: 5px;
    margin-bottom: 10px;
    position: relative;

    .icon {
      width: 40px;
      height: 30px;
      position: absolute;
      right: -20px;
      top: -20px;

      i {
        font-size: 30px;
        cursor: pointer;
      }
    }
  }
}
</style>
  
 