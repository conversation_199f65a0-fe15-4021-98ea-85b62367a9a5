<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading"
            :multipleSelection.sync="multipleSelection" :showSelection="true">
            <template #action-header>
                <el-button type="primary" size="mini" @click="openDialog(null, 1)">添加</el-button>
            </template>
            <template #action="scope">
                <el-button @click="openDialog(scope.row, 2)" type="text">编辑</el-button>
                <el-button @click="deleteList(false, scope.row.id)" type="text">删除</el-button>
            </template>
            <template #chakan="scope">
                <el-button type="text" @click="showDetail(scope.row)">查看</el-button>
            </template>
        </common-table>
        <div class="button">
            <el-button type="primary" @click="deleteList(true, null)">批量删除</el-button>
        </div>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>
        <el-dialog title="" :visible.sync="dialogVisible" width="700px" @close="colseModal">
            <common-form :data="formData" :schema="formSchema" :loading="loading" :submit="submit" :isBack="true"
                @nav_back="nav_back">
            </common-form>
        </el-dialog>
        <el-dialog title="公告查看" :visible.sync="dialogShow" width="700px">
            <div style="max-height:800px;overflow: auto;">
                <div v-if="openType == 'IMAGE'">
                    <img :src="content" style="width: 100%;height: 100%;">
                </div>
                <div v-else>
                    <div v-html="content"></div>
                </div>
            </div>
        </el-dialog>
    </d2-container>
</template>
  
<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from "@/components/CommonForm";
export default {
    name: 'deal',
    components: {
        CommonQuery,
        CommonTable,
        CommonForm
    },
    data() {
        return {
            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query: {},
            tableSchema: [ // 表格架构
                {
                    label: '公告ID',
                    field: 'id',
                    width: 80
                },
                {
                    label: '公告类型',
                    field: 'type',
                    type: 'tag',
                    tagMap: {
                        NORMAL: {
                            label: '常规',
                            tagType: 'info'
                        },
                        RATIO: {
                            label: '概率公示',
                            tagType: 'info'
                        },
                        VIOLATION: {
                            label: '违规处理公告',
                            tagType: 'info'
                        },
                        OTHER: {
                            label: '其他',
                            tagType: 'info'
                        },
                    },
                },
                {
                    label: '公告标题',
                    field: 'title',
                },
                {
                    label: '公告内容',
                    slot: 'chakan',
                    width: 280
                },
                {
                    label: '发送状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '未发送',
                            tagType: 'danger'
                        },
                        1: {
                            label: '已发送',
                            tagType: 'success'
                        },
                    },
                    width: '80px',
                },
                {
                    label: '创建时间',
                    field: 'createAt',
                },
                {
                    label: '修改时间',
                    field: 'updateAt',
                },
                {
                    label: '发布时间',
                    field: 'publishTime',
                },
                {
                    label: '操作',
                    slot: 'action',
                    headerSlot: 'action-header',
                    width: '240px',
                    fixed: 'right'
                },
            ],
            tableData: [],
            querySchema: [ // 搜索组件架构
                {
                    type: 'select',
                    label: '公告类型',
                    placeholder: '',
                    field: 'type',
                    options: [
                        {
                            label: '常规',
                            value: 'NORMAL'
                        },
                        {
                            label: '概率公示',
                            value: 'RATIO'
                        },
                        {
                            label: '违规处理公告',
                            value: 'VIOLATION'
                        },
                        {
                            label: '其他',
                            value: 'OTHER'
                        },
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '公告ID：',
                    placeholder: '请输入公告ID',
                    field: 'id'
                },
                {
                    type: 'input',
                    label: '公告标题:',
                    placeholder: '请输入公告标题',
                    field: 'title'
                },
            ],
            loading: false,
            query: {
            },
            dialogVisible: false,
            loading: false,
            formSchema: [
                {
                    type: 'select',
                    label: '公告类型',
                    placeholder: '',
                    field: 'type',
                    options: [{
                        label: '全部',
                        value: ''
                    },
                    {
                        label: '常规',
                        value: 'NORMAL'
                    },
                    {
                        label: '概率公示',
                        value: 'RATIO'
                    },
                    {
                        label: '违规处理公告',
                        value: 'VIOLATION'
                    },
                    {
                        label: '其他',
                        value: 'OTHER'
                    },
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '公告标题:',
                    placeholder: '请输入公告标题',
                    field: 'title'
                },
                {
                    label: "内容类型",
                    field: "contentType",
                    type: "radio",
                    options: [
                        {
                            label: "图片",
                            value: 'IMAGE',
                        },
                        {
                            label: "文本",
                            value: 'TEXT',
                        },
                    ],
                },
                {
                    type: 'img',
                    placeholder: '请选择图片',
                    label: "",
                    field: 'content',
                    rules: [{
                        required: true,
                        message: '请选择图片',
                        trigger: 'change'
                    }],
                    show: {
                        relationField: 'contentType',
                        value: "IMAGE"
                    },
                },
                {
                    type: 'textarea',
                    placeholder: '请输入文本',
                    label: "",
                    field: 'contentText',
                    rules: [{
                        required: true,
                        message: '请输入文本',
                        trigger: 'change'
                    }],
                    show: {
                        relationField: 'contentType',
                        value: "TEXT"
                    },
                },
                {
                    label: "发布形式",
                    field: "isTiming",
                    type: "radio",
                    options: [
                        {
                            label: "立即发布",
                            value: 0,
                        },
                        {
                            label: "定时发布",
                            value: 1,
                        },
                    ],
                },
                {
                    type: 'datetime',
                    label: '发布时间：',
                    field: 'publishTime',
                    placeholder: '请输入发布时间',
                    pickerOptions: {
                        disabledDate: (time) => {
                            return time.getTime() < Date.now() - 86400000
                        }
                    },
                    show: {
                        relationField: 'isTiming',
                        value: [1]
                    },
                },
                {
                    type: "action",
                },
            ],
            formData: {
                status: 0,
                contentType: 'IMAGE',
                isTiming: 0,
                type: "NORMAL",
                content: ""
            },
            multipleSelection: [],
            content: "",
            openType: "",
            dialogShow: false
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        /**
     *修改弹窗
     */
        openDialog(item, type) {
            this.type = type;
            this.dialogVisible = true;
            if (type == 2) {
                this.formData = item;
                if (this.formData.contentType == 'TEXT') {
                    this.formData.contentText = this.formData.content
                }
            }
        },
        // 过滤查询
        onQueryChange(data) {
            this.query = data
            this.getList()
        },
        onRefresh(data) {
            this.query = data
            this.getList()
        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        async getList() {
            const res = await this.$api.noticeList(this.query);
            if (res.status.code === 0) {
                this.page.totalCount = res.result.totalCount
                this.tableData = res.result.list;
            }
        },
        nav_back() {
            this.$router.go(-1)
        },
        colseModal() {
            this.dialogVisible = false;
            this.formData = {
                status: 0,
                contentType: 'IMAGE',
                isTiming: 1,
                type: "NORMAL",
                content: ""
            };
        },
        /**
     *修改动物掉落权重
     */
        async submit() {
            if (this.formData.contentType == 'TEXT') {
                this.formData.content = this.formData.contentText
            }
            if (this.type == 1) {
                const res = await this.$api.noticeAdd({
                    ...this.formData,
                });
                if (res.status.code === 0) {
                    this.dialogVisible = false;
                    this.formData = {
                        status: 0,
                        contentType: 'IMAGE',
                        isTiming: 1,
                        type: "NORMAL",

                    };
                    this.getList();
                    this.$message({
                        type: "success",
                        message: "新增成功",
                    });
                }
            } else if (this.type == 2) {
                const res = await this.$api.noticeUpdate({
                    ...this.formData,
                });
                if (res.status.code === 0) {
                    this.dialogVisible = false;
                    this.formData = {
                        status: 0,
                        contentType: 'IMAGE',
                        isTiming: 1,
                        type: "NORMAL",
                        content: ""
                    };
                    this.getList();
                    this.$message({
                        type: "success",
                        message: "修改成功",
                    });
                }
            }
        },
        /**
    * 二次弹窗确认 动物掉落权重批量删除 二次确认
    * @param {boolean} isBatch 是否批量
    * @param {string} id 批量删除id
    */
        async deleteList(isBatch, id) {
            let idd = [id];
            const str = [];
            if (isBatch) {
                this.multipleSelection.forEach((item) => {
                    str.push(item.id);
                });
            }
            console.log(str);
            const res = await this.$api.noticeBatchDelete({
                idList: isBatch ? JSON.stringify(str) : JSON.stringify(idd),
            });
            if (res.status.code === 0) {
                this.getList();
                this.$message({
                    type: "success",
                    message: "删除成功",
                });
            }
        },
        /**
         * 查看详情
         */
        showDetail(item) {
            this.content = item.content
            this.openType = item.contentType
            this.dialogShow = true

        },
    }
}
</script>
  
<style lang="scss" scoped>
.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
}
</style>
  