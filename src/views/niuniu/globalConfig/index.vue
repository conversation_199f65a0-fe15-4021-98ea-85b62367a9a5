<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #action-header>
        <el-button type="primary" size="mini" @click="openModal(1, null)">添加配置</el-button>
      </template>
      <template #action="scope">
        <el-button type="text" @click="openModal(2, scope.row)">修改</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isaddDialog" width="35%" @close="colseModal()">
      <el-form :model="form">
        <el-form-item label="配置名称" :label-width="formLabelWidth">
          <el-input v-model="form.name" placeholder="请输入配置名称"></el-input>
        </el-form-item>
        <el-form-item label="配置的值" :label-width="formLabelWidth">
          <el-input v-model="form.value" placeholder="请输入配置的值"></el-input>
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth">
          <el-input v-model="form.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="colseModal()">取 消</el-button>
        <el-button type="primary" @click="submitConfig()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
  configList,
  configUpdate,
  configAdd
} from '@/api/gameLobby'
export default {
  name: 'deal',
  components: {
    CommonQuery,
    CommonTable
  },
  data() {
    return {
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query: {},
      tableSchema: [ // 表格架构
        {
          label: '配置名称',
          field: 'name',
        },
        {
          label: '配置的值',
          field: 'value',
        },
        {
          label: '备注',
          field: 'remark',
          showOverflowTooltip: true,
          width: '140px',
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '240px',
          fixed: 'right'
        },
      ],
      tableData: [],
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '备注搜索：',
          placeholder: '请输入备注搜索：',
          field: 'remark'
        },
      ],
      loading: false,
      query: {
        remark: ''
      },
      title: "添加配置",
      isaddDialog: false,
      form: {
        name: '',
        value: '',
        remark: ''
      },
      formLabelWidth: "120px"
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    toTemplatePage(item = {}) {
      // this.$store.commit('SAVE_INFO', item)
      // mapActions('save_stateInfo', item)
      if (item.businessType && item.businessType != 0) {
        this.$router.push({
          name: 'NoticeEdit',
          query: {
            templateId: item.id,
            businessType: item.businessType
          }
        })
      } else {
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      }

    },
    nav_add() {
      this.$router.push({
        name: 'batchListingAdd',
      })
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data

      this.getList()
    },
    onRefresh(data) {
      this.query = data
      this.getList()
    },
    // 分页改变
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页改变
    currentChangeSize(value) {
      this.page.pageSize = value
      this.getList()
    },
    //列表查询
    getList() {
      const params = {
        remark: this.query.remark,
        ...this.page
      }
      configList(params).then(res => {
        this.tableData = res.result.list
        this.page.totalCount = res.result.totalCount
        this.page.pageSize = res.result.pageSize
      })
    },
    openModal(type, item) {
      if (type == 2) {
        this.title = "修改配置"
        this.form.name = item.name
        this.form.value = item.value
        this.form.remark = item.remark
      } else {
        this.title = "添加配置"
      }
      this.isaddDialog = true
    },
    /**
     * 提交配置
     */
    submitConfig() {
      if (this.form.name == '') {
        this.$message.error("配置名称不能为空")
        return
      }
      if (this.form.value == '') {
        this.$message.error("配置值不能为空")
        return
      }
      if (this.form.remark == '') {
        this.$message.error("备注不能为空")
        return
      }
      if (this.form.name) {
        this.updateConfig()
      } else {
        this.addConfig()
      }
    },
    /**
     * 新增配置
     */
    addConfig() {
      const params = {
        ...this.form
      }
      configAdd(params).then(res => {
        if (res.status.code == 0) {
          this.$message.success("添加成功")
          this.isaddDialog = false
          this.getList()
        }
      })
    },
    /**
     * 修改配置
     */
    updateConfig() {
      const params = {
        ...this.form
      }
      configUpdate(params).then(res => {
        if (res.status.code == 0) {
          this.$message.success("修改成功")
          this.isaddDialog = false
          this.getList()
        }
      })

    },
    /**
     * 关闭弹窗
     */
    colseModal() {
      this.isaddDialog = false
      this.form = {
        name: '',
        value: '',
        remark: ''
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.oneOver {
  display: inline-block;
  /*超出部分隐藏*/
  white-space: nowrap;
  overflow: hidden;
  /*不换行*/
  text-overflow: ellipsis;
  width: 120px;
  cursor: pointer;
  font-size: 12px;
  /*超出部分文字以...显示*/
}
</style>
