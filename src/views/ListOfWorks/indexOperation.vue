<template>
	<d2-container class="page">
		<div class="main">
			<div class="header">
				<div class="form">
					<div class="item" v-for="(item, index) in filterCondition" :key="index">
						<span>{{ item.label }}</span>
						<el-select  v-model="search[item.key]" v-if="item.type === 'select'"
							:placeholder="`请选择${item.label}`" clearable>
							<el-option v-for="item in item.options" :key="item.value" :label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
						<div v-else-if="item.type === 'inputR'"
							style="display:flex;justify-content: space-between;align-items: center;width:320px;">
							<el-input v-model="search[item.key]" placeholder="请输入最低价"></el-input>
							<div style="margin-left:10px;">至</div>
							<el-input v-model="search[item.key2]" :placeholder="`请输入最高价`"></el-input>
						</div>
						<div v-else-if="item.type === 'datetimerange'"
							style="display:flex;justify-content: space-between;align-items: center;width:290px;">
							<el-date-picker v-model="search[item.key]" type="datetimerange"
								value-format="yyyy-MM-dd HH:mm:ss.SSS" range-separator="至" start-placeholder="开始日期"
								end-placeholder="结束日期">
							</el-date-picker>
						</div>
						<el-input v-model="search[item.key]" v-else :placeholder="`请输入${item.label}`"></el-input>
					</div>
				</div>
				<div class="action">
					<div class="right">
						<el-button type="primary" @click="showStatusVisible = true" :disabled="!idList.length">审核状态
						</el-button>
						<el-button type="primary" @click="publishStatusVisible = true" :disabled="!idList.length">是否发布
						</el-button>
						<el-button type="primary" @click="rackUpAndDownStatusVisible = true" :disabled="!idList.length">
							上下架
						</el-button>
						<el-button type="primary" @click="recommendStatusVisible = true" :disabled="!idList.length">推荐级别
						</el-button>
						<el-button type="primary" @click="riskStatusVisible = true" :disabled="!idList.length">提示风险
						</el-button>
						<el-button type="primary" @click="destroy_open()" :disabled="!idList.length">销毁作品
						</el-button>
						<el-button type="primary" @click="showAstricUp = true" :disabled="!idList.length">限制寄售作品
						</el-button>
						<el-button type="primary" @click="export_shop()">导出商品
						</el-button>
						<el-button type="primary" @click="editPrice(true)" :disabled="!idList.length">批量修改
						</el-button>
					</div>
					<div class="left">
						<el-button type="primary" @click="getWorks('search')">搜索</el-button>
						<el-button type="info" @click="reset()">重置</el-button>
					</div>
				</div>
			</div>
			<div class="form">
				<el-table border :data="tableData" style="width: 100%" @selection-change="handleSelectionChange"
					v-loading="loading">
					<el-table-column type="selection" width="18">
					</el-table-column>
					<el-table-column v-for="(item, index) in columnList" :key="index" :width="item.width || 'auto'"
						:prop="item.prop" :label="item.label" align="center">
						<template slot-scope="scope">
							<template v-if="item.prop === 'cover'">
								<el-image style="width: 30px; height: 30px" :src="scope.row[item.prop]"
									:preview-src-list="[scope.row[item.prop]]">
								</el-image>
								<div>
									<!-- <el-button type="text" size="mini" @click="getPredict(scope.row)">
										查找相似图片
									</el-button> -->
								</div>
							</template>
							<template v-else-if="item.prop === 'status'">
								<span v-if="scope.row[item.prop] == 0">未发布</span>
								<span v-if="scope.row[item.prop] == 1">已发布</span>
							</template>
							<template v-else-if="item.prop === 'member'">
								<div>
									<span v-if="scope.row[item.prop] == '4'">主力</span>
									<span v-if="scope.row[item.prop] == '0.1'">小户</span>
									<span v-if="scope.row[item.prop] == '0.2'">中户</span>
									<span v-if="scope.row[item.prop] == '0.3'">大户</span>
								</div>
							</template>
							<span v-else-if="item.prop === 'saleVisibility'">
								<el-tag type="danger" v-if="scope.row[item.prop] == 0">下架</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop] == 1">上架</el-tag>
							</span>
							<span v-else-if="item.prop === 'original'">
								<el-tag type="success" v-if="scope.row[item.prop] == 0">否</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop] == 1">是</el-tag>
							</span>
              <span v-else-if="item.prop === 'onSaleStatus'">
              	<el-tag type="success" v-if="scope.row[item.prop] == 0">是</el-tag>
              	<el-tag type="success" v-if="scope.row[item.prop] == 1">否</el-tag>
              </span>
							<span v-else> {{ keyToLabel(item.prop, scope.row[item.prop]) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="80">
						<template slot-scope="scope">
							<el-button @click="whitelist(scope.row)" type="text" >白名单列表</el-button>
							<el-button @click="editPrice(false, scope.row)" type="text" >编辑</el-button>
							<!-- <el-button @click="favoriteList(scope.row)" type="text" >关注列表</el-button> -->
							<!--            <el-button @click="deleteClick(scope.row)" type="text"  class="danger">删除</el-button>-->
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>
		<common-pagination ref="commonPagination" :page.sync="page" @change="getWorks">
		</common-pagination>
		<el-dialog title="白名单列表" :visible.sync="whitelistVisible">
			<el-table :data="whitelistData">
				<el-table-column :prop="item.prop" :label="item.label" width="150"
					v-for="(item, index) in whitelistColumn" :key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<el-dialog title="关注列表" :visible.sync="favoriteListVisible">
			<el-table :data="favoriteListData">
				<el-table-column :prop="item.prop" :label="item.label" width="150"
					v-for="(item, index) in favoriteListColumn" :key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<el-dialog title="审核状态" :visible.sync="showStatusVisible" width="40%">
			<el-radio-group v-model="showsStatus">
				<el-radio label="0">未审核</el-radio>
				<el-radio label="1">人审通过</el-radio>
				<el-radio label="2">人审不通过</el-radio>
				<el-radio label="3">机审通过</el-radio>
				<el-radio label="4">机审不通过</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="showStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="approvalStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="是否发布" :visible.sync="publishStatusVisible" width="40%">
			<el-radio-group v-model="publishStatus">
				<el-radio label="0">草稿</el-radio>
				<el-radio label="1">发布</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="publishStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="publishStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="上下架状态" :visible.sync="rackUpAndDownStatusVisible" width="40%">
			<el-radio-group v-model="rackUpAndDownStatus">
				<el-radio label="0">下架</el-radio>
				<el-radio label="1">上架</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="rackUpAndDownStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="rackUpAndDownStatusFun()">确 定</el-button>
			</span>
		</el-dialog>

		<el-dialog title="提醒" :visible.sync="rackUpAndDownStatusVisibletip" width="40%">
			<span>以下作品持有者为大户，确定是否上架？ TID:</span>
			<span v-for="(item, index) in notfour" :key="index">{{
				item.tid + ' ,'
			}}</span>
			<span slot="footer">
				<el-button @click="rackUpAndDownStatusVisibletip = false">取 消</el-button>
				<el-button type="primary" @click="rackUp()">确 定</el-button>
			</span>
		</el-dialog>

		<el-dialog title="推荐级别" :visible.sync="recommendStatusVisible" width="40%">
			推荐级别：
			<el-input placeholder="请输入推荐级别" v-model="recommendStatus" type="digit" style="width: 50%"
				@input="handleInput">
			</el-input>
			<span slot="footer">
				<el-button @click="recommendStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="recommendStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="提示风险" :visible.sync="riskStatusVisible" width="40%">
			<el-radio-group v-model="riskStatus">
				<el-radio label="0">无风险</el-radio>
				<el-radio label="1">有风险</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="riskStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="riskStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="限制寄售作品" :visible.sync="showAstricUp" width="40%">
			<el-radio-group v-model="astricUpStatus">
				<el-radio label="0">禁止寄售</el-radio>
				<el-radio label="1">正常寄售</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="showAstricUp = false">取 消</el-button>
				<el-button type="primary" @click="submitAstricUp()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="相似图片" :visible.sync="predictDialog" width="1000px">
			<div>
				<div>原图</div>
				<div style="
		        display: inline-block;
		        width: 100px;
		        height: 100px;
		        margin-right: 10px;
		        margin-bottom: 10px;
		        margin-top: 10px;
		      ">
					<img :src="artworkImg" alt="" style="width: 100%; height: 100%" />
				</div>
				<div>百度图片</div>
				<div v-for="(item, index) in baidu_result" :key="index" style="
		        display: inline-block;
		        width: 200px;
		        height: 100%;
		        margin-right: 10px;
		        margin-bottom: 10px;
		      ">
					<el-image style="width: 100%; height: 100%" :src="item" :preview-src-list="[item]" fit="fill ">
					</el-image>
				</div>
			</div>
			<div>谷歌图片</div>
			<div v-for="(item, index) in google_result" :key="index" style="
		      display: inline-block;
		      width: 200px;
		      height: 100%;
		      margin-right: 10px;
		      margin-bottom: 10px;
		    ">
				<el-image :preview-src-list="[item]" style="width: 100%; height: 100%" :src="item" fit="fill ">
				</el-image>
			</div>
		</el-dialog>
	</d2-container>
</template>

<script>
import footerBar from '@/views/configurationDict/window/footer'
import common from '@/mixins/common'
import { downloadBlob } from '@/utils/helper'
export default {
	name: 'indexOperation',
	mixins: [common],
	components: {
		footerBar
	},
	data() {
		return {
			notfour: [],
			itemList: [],
			rackUpAndDownStatusVisibletip: false,
			filterCondition: [
				{
					label: '作品id',
					key: 'tid',
					type: 'input'
				},
				{
					label: '系列id',
					key: 'ctid',
					type: 'input'
				},
				{
					label: '持有者昵称',
					key: 'ownerNickname',
					type: 'input'
				},
				{
					label: '持有者con add',
					key: 'ownerContractAddress',
					type: 'input'
				},
				{
					label: '持有者手机号',
					key: 'ownerPhone',
					type: 'input'
				},
				{
					label: '作品名称',
					key: 'name',
					type: 'input'
				},
				{
					label: '持有者身份',
					key: 'member',
					type: 'select',
					options: [{
						value: '0.3',
						label: '大户'
					}, {
						value: '0.2',
						label: '中户'
					}, {
						value: '0.1',
						label: '小户'
					}, {
						value: '4',
						label: '主力'
					}]
				},
				{
					label: '是否一级市场卖出',
					key: 'sellOut',
					type: 'select',
					options: [{
						value: '0',
						label: '未卖出'
					}, {
						value: '1',
						label: '已卖出'
					}]
				},
				{
					label: '是否上架',
					key: 'saleVisibility',
					type: 'select',
					options: [{
						value: '0',
						label: '下架'
					}, {
						value: '1',
						label: '上架'
					}]
				},
				{
					label: '是否交易过',
					key: 'isTrade',
					type: 'select',
					options: [{
						value: '1',
						label: '是'
					}, {
						value: '0',
						label: '否'
					}]
				},
				{
					label: '购入价格',
					key: 'minBuyPrice',
					key2: 'maxBuyPrice',
					type: 'inputR'
				},
				{
					label: '当前价格',
					key: 'minPrice',
					key2: 'maxPrice',
					type: 'inputR'
				},
				{
					label: '订单时间',
					key: 'buyTime',
					type: 'datetimerange'
				},
				{
					label: '排序类型',
					key: 'sortBy',
					type: 'select',
					options: [{
						value: '3',
						label: '购入时间排序'
					}, {
						value: '4',
						label: '购入金额排序'
					}, {
						value: '5',
						label: '在售金额排序'
					}]
				},
				{
					label: '排序顺序',
					key: 'flagDesc',
					type: 'select',
					options: [{
						value: '0',
						label: '升序'
					}, {
						value: '1',
						label: '降序'
					}]
				},
				{
					label: '只看PGC',
					key: 'isPgc',
					type: 'select',
					options: [{
						value: '1',
						label: '只看PGC'
					}, {
						value: '',
						label: '全部'
					}]
				},
        {
        	label: '是否限制寄售',
        	key: 'onSaleStatus',
        	type: 'select',
        	options: [{
        		value: '0',
        		label: '是'
        	}, {
        		value: '1',
        		label: '否'
        	}]
        },

			],
			tableData: [],
			columnList: [{
				label: 'ID',
				prop: 'id',
				width: '36'
			},
			{
				label: '作品id',
				prop: 'tid',
				width: '68'
			},
			{
				label: '系列ID',
				prop: 'ctid',
				width: '64'
			},
			{
				label: '当前价格',
				prop: 'price',
				width: '40'
			},
			{
				label: '购入价格',
				prop: 'buyPrice',
				width: '40',
				align: 'center'
			},
			{
				label: '作品名称',
				prop: 'name',
				width: '100'
			},
			{
				label: '作品封面',
				prop: 'cover',
				width: '40'
			},
			{
				label: '最近交易时间',
				prop: 'buyTime',
				width: '70'
			},
			{
				label: '持有者名称',
				prop: 'ownerNickname',
				width: '50'
			},
			{
				label: '持有者con add',
				prop: 'ownerContractAddress',
				width: '55'
			},
			{
				label: '持有者手机号',
				prop: 'ownerPhone',
				width: '50'
			},
			{
				label: '持有者身份',
				prop: 'member',
				width: '40'
			},
			{
				label: '是否卖出',
				prop: 'original',
				width: '50'
			},
			{
				label: '上架状态',
				prop: 'saleVisibility',
				width: '50'
			},
      {
      	label: '是否限制寄售',
      	prop: 'onSaleStatus',
      	width: '50'
      },
			],
			search: {
				publisherName: '',
				ctid: '',
				flagDesc: '1',
				sortBy: '3',
				isTrade: '',
				ownerNickname: '',
				isPgc: '1',
        onSaleStatus:'1'
			},
			whitelistVisible: false,
			whitelistData: [],
			whitelistColumn: [{
				label: '用户ID',
				prop: 'uid'
			},
			{
				label: '用户名',
				prop: 'nickname'
			},
			{
				label: '添加时间',
				prop: 'updateAt'
			}
			],
			favoriteListVisible: false,
			favoriteListData: [],
			favoriteListColumn: [{
				label: '用户ID',
				prop: 'uid'
			},
			{
				label: '用户名',
				prop: 'username'
			},
			{
				label: '关注时间',
				prop: 'updatedAt'
			}
			],
			idList: [],
			tidList: [],
			showStatusVisible: false,
			showsStatus: '0',
			publishStatusVisible: false,
			publishStatus: '0',
			rackUpAndDownStatusVisible: false,
			rackUpAndDownStatus: '0',
			recommendStatusVisible: false,
			recommendStatus: 0,
			riskStatusVisible: false,
			riskStatus: '0',
			predictDialog: false,
			loading: false,
			google_result: [],
			baidu_result: [],
			artworkImg: '',
			showAstricUp: false,
			astricUpStatus: '',
		}
	},
	created() {
		// 从用户列表跳转 自动添加筛选条件 铸造这为该用户
		this.search.ctid = this.$route.query.ctid
		this.search.publisherName = this.$route.query.publisherName
		if (this.$route.query.nickname) {
			this.search.ownerNickname = this.$route.query.nickname
		}
		this.getWorks()
	},
	methods: {
		// 在 Input 值改变时触发 只能输入正整数（包括0）
		handleInput(val) {
			let value = val.replace(/\D/g, '') // 只能输入数字
			value = value.replace(/^0+(\d)/, '$1') // 第一 0开头，0后面为数字，则过滤掉，取后面的数字
			value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
			this.recommendStatus = value
		},
		/**
		 *  获取列表
		 * @returns {Promise<void>}
		 */
		async getWorks(init) {
			if (init) {
				this.initPage()
			}
			let member;
			member = this.search.member ? parseInt(this.search.member) : this.search.member
			if (this.search.buyTime) {
				this.search.minBuyTime = this.search.buyTime[0]
				this.search.maxBuyTime = this.search.buyTime[1]
			}
			console.log('this.search.flagDesc,this.search.sortBy', this.search)
			if (this.search.flagDesc && !this.search.sortBy) {
				this.$message.error('请先选择排序类型后搜索')
				return false
			}


			const obj = this.search
			Object.keys(obj).forEach(item => {
				if (!obj[item]) delete obj[item]
			})
			console.log("obj-------------", obj)
			const {
				result: {
					list,
					totalCount
				}
			} = await this.$api.goodsList({
				...obj,
				...this.page,
				member
			})
			list.forEach((item) => {
				if (this.search.member == null || this.search.member == '') {
					if (item.member == 0) {
						let lastNum = this.getLastDigit(item.uid);
						if (lastNum == 0 || lastNum == 1) {
							item.member = '0.3'
						} else if (lastNum > 1 && lastNum < 5) {
							item.member = '0.2'
						} else if (lastNum > 4) {
							item.member = '0.1'
						}
					}
				} else {
					if (this.search.member > 0.4) {

					} else {
						item.member = this.search.member
					}
				}
			})
			this.tableData = list
			this.page.totalCount = totalCount
		},
		getLastDigit(num) {
			return num % 10; // 通过对数字进行求余运算得到最后一位尾号
		},
		/**
		 * 白名单列表
		 * @param row
		 * @returns {Promise<void>}
		 */
		async whitelist(row) {
			this.whitelistVisible = true
			const {
				tid,
				ctid,
				mold
			} = row
			let obj = {
				tid
			}
			if (mold === 2) {
				obj = {
					ctid
				}
			}
			const {
				result: {
					list
				}
			} = await this.$api.allowList(obj)
			this.whitelistData = list
		},
		/**
		 * 关注列表
		 */
		async favoriteList(row) {
			this.favoriteListVisible = true
			const {
				result: {
					list
				}
			} = await this.$api.favoriteList({
				tid: row.tid
			})
			this.favoriteListData = list
		},
		/**
		 * 审核状态
		 */
		async approvalStatusFun() {
			const {
				idList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.batchUpdateAuditState({
				idListStr: JSON.stringify(idList),
				shows: this.showsStatus
			})
			if (code === 0) {
				this.$message.success(msg)
				await this.getWorks()
				this.showStatusVisible = false
			} else {
				this.$message.error(msg)
			}
		},
		/**
		 * 发布状态
		 */
		async publishStatusFun() {
			const {
				idList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.batchUpdatePublishState({
				idListStr: JSON.stringify(idList),
				status: this.publishStatus
			})
			if (code === 0) {
				this.$message.success(msg)
				await this.getWorks()
				this.publishStatusVisible = false
			} else {
				this.$message.error(msg)
			}
		},
		/**
		 * 上下架
		 */

		async rackUp(e) {
			let fours = this.itemList.map(item => item.id)
			console.log(this.idList, '123');
			const {
				idList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.batchUpdateRackStatus({
				idListStr: JSON.stringify(e ? e : fours),
				status: this.rackUpAndDownStatus
			})
			if (code === 0) {

				if (!e) {
					this.$message.success(msg)

					setTimeout(() => {
						this.getWorks()
					}, 1000)

					this.rackUpAndDownStatusVisibletip = false
					this.rackUpAndDownStatusVisible = false
				}


			} else {
				// this.$message.error(msg)
			}


		},


		async rackUpAndDownStatusFun() {
			if (this.rackUpAndDownStatus == 1) {

				let four = this.itemList.filter(item => item.member === 4)  // 主力
				let notfour = this.itemList.filter(item => item.member !== 4)  // 非主力
				let fourid = four.map(item => item.id)
				console.log(four);
				let idlist = four.map(item => item.id)  // 主力id []
				console.log(idlist, 333);
				if (notfour.length > 0) {
					this.rackUpAndDownStatusVisibletip = true
				}

				if (four.length != 0 && notfour.length == 0) {
					// this.rackUp(idlist)
					const {
						status: {
							code,
							msg
						}
					} = await this.$api.batchUpdateRackStatus({
						idListStr: JSON.stringify(fourid),
						status: this.rackUpAndDownStatus
					})
					if (code === 0) {

						this.$message.success(msg)
						if (notfour.length == 0) {
							setTimeout(() => {
								this.getWorks()
							}, 1000)
						}


						this.rackUpAndDownStatusVisibletip = false
						this.rackUpAndDownStatusVisible = false


					} else {
						// this.$message.error(msg)
					}
				}

			} else {
				console.log(this.idList, '123');
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdateRackStatus({
					idListStr: JSON.stringify(idList),
					status: this.rackUpAndDownStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					setTimeout(() => {
						this.getWorks()
					}, 1000)
					this.rackUpAndDownStatusVisible = false
				} else {
					// this.$message.error(msg)
				}
			}


		},
		/**
		 * 推荐级别
		 */
		async recommendStatusFun() {
			const {
				idList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.batchUpdateRecommendLevel({
				idListStr: JSON.stringify(idList),
				recommend: this.recommendStatus
			})
			if (code === 0) {
				this.$message.success(msg)
				setTimeout(() => {
					this.getWorks()
				}, 1000)
				this.recommendStatusVisible = false
			} else {
				this.$message.error(msg)
			}
		},

		/**
		 * 提示风险
		 */
		async riskStatusFun() {
			const {
				idList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.batchUpdateRiskState({
				idListStr: JSON.stringify(idList),
				status: this.riskStatus
			})
			if (code === 0) {
				this.$message.success(msg)
				setTimeout(() => {
					this.getWorks()
				}, 1000)
				this.riskStatusVisible = false
			} else {
				this.$message.error(msg)
			}
		},
		async destroy_open() {
			this.$confirm('是否确认销毁选中的作品', '作品销毁', {
				confirmButtonText: '确认销毁作品',
				cancelButtonText: '取消',
				type: 'error'
			}).then(() => {
				this.destroy()
			}).catch(() => {

			});
		},
		async destroy() {
			const {
				tidList
			} = this
			const {
				status: {
					code,
					msg
				}
			} = await this.$api.destroyByTids({
				tidStr: JSON.stringify(tidList)
			})
			if (code === 0) {
				this.$message.success(msg)
				setTimeout(() => {
					this.getWorks()
				}, 1000)
			} else {
				this.$message.error(msg)
			}
		},
		/**
		 * 字典
		 * @param key
		 * @param value
		 * @returns {*}
		 */
		keyToLabel(key, value) {
			const showsStatus = {
				0: '未审核',
				1: '人审通过',
				2: '人审不通过',
				3: '机审通过',
				4: '机审不通过'
			}
			const listingStatus = {
				0: '下架',
				1: '上架'
			}
			if (key === 'shows') {
				value = showsStatus[value]
			} else if (key === 'listingStatus') {
				value = listingStatus[value]
			} else {
				const obj = this.filterCondition.find(item => {
					return item.key === key
				})
				const res = obj?.options?.find(item => {
					return item.value === value + ''
				})
				value = res?.label || value
			}
			return value
		},
		/**
		 * 选中
		 */
		handleSelectionChange(val) {
			this.itemList = val
			console.log(this.itemList);
			this.notfour = val.filter(item => item.member != 4)
			this.idList = val.map(item => item.id)
			this.tidList = val.map(item => item.tid)
			console.log(this.idList)
		},
		/**
		 * 重置
		 */
		reset() {
			this.search = {}
			this.page.pageNum = 1
			this.getWorks()
		},
		// 查询相似图片
		async getPredict(val) {
			this.loading = true
			const {
				result
			} = await this.$api.getPredict({
				pic: val.cover,
				token: 'a556554e98429fc8a36792054d9b546b'
			}).catch(() => {
				this.loading = false
			})
			this.predictDialog = true
			this.loading = false
			this.baidu_result = result.baidu_result
			this.google_result = result.google_result
			this.artworkImg = result.img_url
		},
		async submitAstricUp() {
			const {
				idList
			} = this
			let res = await this.$api.batchUpdateOnSaleStatus({
				idListStr: JSON.stringify(idList),
				status: this.astricUpStatus
			});
			if (res.status.code == 0) {
				this.$message.success("操作成功")
				setTimeout(() => {
					this.getWorks()
				}, 1000)
				this.showAstricUp = false
			} else {
				this.$message.error(res.status.msg)
				this.showAstricUp = false
			}
		},
		async export_shop() {
			let member;
			member = this.search.member ? parseInt(this.search.member) : this.search.member
			// if (this.page.totalCount > 60000) {
			// 	this.$message.error('导出文件过大,不能超过60000条')
			// } else {

			// }
			this.loading = true
			if (this.search.buyTime) {
				this.search.minBuyTime = this.search.buyTime[0]
				this.search.maxBuyTime = this.search.buyTime[1]
			}
			const res = await this.$api.goodsListExport({
				...this.search,
				member
			})
			if (res.type === 'application/json') {
				this.loading = false
				// blob 转 JSON
				const enc = new TextDecoder('utf-8')
				res.arrayBuffer().then(buffer => {
					const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
					this.$message.error(data.status?.msg)
				})
			} else {
				this.loading = false
         console.log(res)
				downloadBlob(res, '商品')
				this.$message.success('导出成功')
				this.getWorks()
			}
		},
		editPrice(isBatch, item) {
			let idList = JSON.stringify(this.idList)
			this.$prompt('请输入需要改价的价格', `${isBatch ? '批量改价' : '改价'}`, {
				confirmButtonText: '确定',
				cancelButtonText: '取消'
			}).then(async ({ value }) => {
				if (item?.price == value && !isBatch) {
					this.$message.error('修改的价格与当前价格一致，无需修改')
				} else {
					const { status } = await this.$api.goodsPriceUpdate({
						price: value,
						idListJson: isBatch ? idList : `[${item.id}]`
					})
					if (status.code === 0) {
						this.$message.success('修改成功')
						setTimeout(() => {
							this.getWorks()
						}, 1000)
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.main {
	.header {
		.form {
			display: flex;
			justify-content: flex-start;
			flex-wrap: wrap;
			flex: 1;

			.place-holder {
				width: 18%;
				overflow: hidden;
				margin-left: 15px;
			}

			.item {
				margin-left: 15px;
				display: flex;
				margin-bottom: 20px;
				justify-content: space-between;
				align-items: center;
				min-width: 18%;

				.el-select,
				.el-input {
					flex: 1;
					margin-left: 20px;
				}

				span {
					min-width: 80px;
					margin-right: -10px;
          font-size:14px;
				}
			}
		}

		.action {
			display: flex;
			justify-content: space-between;
			height: 100%;
			flex-wrap: wrap;

			.el-button {
				margin-bottom: 20px;
			}
		}
	}
}

.el-table__cell {
	padding: 0px !important;
}
</style>
