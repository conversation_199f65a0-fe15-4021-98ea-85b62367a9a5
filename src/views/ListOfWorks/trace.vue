<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item label="操作类型">
				<el-select  v-model="formInline.operatorType" placeholder="操作类型">
					<el-option label="上架" value="SALE_UP"></el-option>
					<el-option label="下架" value="SALE_DOWN"></el-option>
					<el-option label="标记风险" value="FLAG_RISK"></el-option>
					<el-option label="标记无风险" value="FLAG_NO_RISK"></el-option>
					<el-option label="作品发布" value="FLAG_PUBLISH"></el-option>
					<el-option label="作品未发布" value="FLAG_NOT_PUBLISH"></el-option>
					<el-option label="标记推荐级别" value="RECOMMEND_LEVEL"></el-option>
					<el-option label="人审通过" value="PERSON_VERIFY_PASS"></el-option>
					<el-option label="人审不通过" value="PERSON_VERIFY_BLOCK"></el-option>
					
					<el-option label="订单改变作品状态" value="ORDER"></el-option>
					<el-option label="修改价格" value="UPDATE_PRICE"></el-option>
					<el-option label="修改寄售状态" value="UPDATE_NOT_SALE_SIGN"></el-option>
					<el-option label="禁止上架" value="UPDATE_ON_SALE_STATUS_FORBID"></el-option>
					<el-option label="允许上架" value="UPDATE_ON_SALE_STATUS_ALLOW"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="平台">
				<el-select  v-model="formInline.platform" placeholder="平台">
					<el-option label="C端" value="C"></el-option>
					<el-option label="B端" value="B"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="操作人id">
				<el-input v-model="formInline.operatorUserId" placeholder="请输入操作人id" clearable></el-input>
			</el-form-item>
			<el-form-item label="商品tid">
				<el-input v-model="formInline.tid" placeholder="请输入商品tid" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getList(1)">查询</el-button>
				<el-button type="primary" @click="clear()">清除</el-button>
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%">
			<el-table-column fixed prop="id" label="id" align="center"></el-table-column>
			<el-table-column prop="operatorUserId" label="操作人id" align="center"></el-table-column>
			<el-table-column prop="operatorUserName" label="操作人" align="center"></el-table-column>
			<el-table-column prop="itemTid" label="商品tid" align="center"></el-table-column>
			<el-table-column prop="platform" label="平台" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.platform == 'C'">C端</el-tag>
					<el-tag v-if="scope.row.platform == 'B'">B端</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="operatorType" label="操作类型" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.operatorType == 'SALE_UP'">上架</el-tag>
					<el-tag v-if="scope.row.operatorType == 'SALE_DOWN'">下架</el-tag>
					<el-tag v-if="scope.row.operatorType == 'FLAG_RISK'">标记风险</el-tag>
					<el-tag v-if="scope.row.operatorType == 'FLAG_NO_RISK'">标记无风险</el-tag>
					<el-tag v-if="scope.row.operatorType == 'FLAG_PUBLISH'">作品发布</el-tag>
					<el-tag v-if="scope.row.operatorType == 'FLAG_NOT_PUBLISH'">作品未发布</el-tag>
					<el-tag v-if="scope.row.operatorType == 'RECOMMEND_LEVEL'">标记推荐级别
					</el-tag>
					<el-tag v-if="scope.row.operatorType == 'PERSON_VERIFY_PASS'">人审通过</el-tag>
					<el-tag v-if="scope.row.operatorType == 'PERSON_VERIFY_BLOCK'">人审不通过</el-tag>
					
					<el-tag v-if="scope.row.operatorType == 'ORDER'">订单改变作品状态</el-tag>
					<el-tag v-if="scope.row.operatorType == 'UPDATE_PRICE'">修改价格</el-tag>
					<el-tag v-if="scope.row.operatorType == 'UPDATE_NOT_SALE_SIGN'">修改寄售状态</el-tag>
					<el-tag v-if="scope.row.operatorType == 'UPDATE_ON_SALE_STATUS_FORBID'">禁止上架</el-tag>
					<el-tag v-if="scope.row.operatorType == 'UPDATE_ON_SALE_STATUS_ALLOW'">允许上架</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="operationDesc" label="操作描述" align="center"></el-table-column>
			<el-table-column prop="operationExt" label="操作信息扩展字段" align="center" show-overflow-tooltip>
			</el-table-column>
			<el-table-column prop="updateAt" label="记录修改时间" align="center"></el-table-column>
		</el-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
			</el-pagination>
		</div>
	</d2-container>
</template>

<script>
	export default {
		name: 'trace',
		data() {
			return {
				tableData: [],
				total: 1,
				formInline: {
					operatorType: '',
					operatorUserId: '',
					platform: '',
					tid: ''
				}
			}
		},
		mounted() {
			this.getList(1)
		},
		methods: {
			// 查询列表
			async getList(page) {
				const res = await this.$api.traceItem({
					pageNum: page,
					pageSize: 15,
					operatorType: this.formInline.operatorType,
					operatorUserId: this.formInline.operatorUserId,
					platform: this.formInline.platform,
					tid: this.formInline.tid
				})
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
			xuanze(val) {
				this.getList(val)
			},
			clear() {
				this.formInline.operatorType = ''
				this.formInline.operatorUserId = ''
				this.formInline.platform = ''
				this.formInline.tid = ''
			}
		}
	}
</script>

<style>
</style>
