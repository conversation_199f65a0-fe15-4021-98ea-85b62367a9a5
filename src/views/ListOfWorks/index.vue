<template>
	<d2-container class="page">
		<div class="main">
			<div class="header">
				<div class="form">
					<div class="item" v-for="(item,index) in filterCondition" :key="index">
						<span>{{ item.label }}</span>
						<el-select  v-model="search[item.key]" v-if="item.type === 'select'"
							:placeownerNickname="`请选择${item.label}`" clearable>
							<el-option v-for="item in item.options" :key="item.value" :label="item.label"
								:value="item.value">
							</el-option>
						</el-select>
						<el-input v-model="search[item.key]" v-else :placeownerNickname="`请输入${item.label}`"></el-input>
					</div>
					<!--          &lt;!&ndash;        占位&ndash;&gt;-->
					<!--          <div class="place-ownerNickname"></div>-->
				</div>
				<div class="action">
					<div class="right">
						<el-button type="primary" @click="showStatusVisible = true" :disabled="!idList.length">审核状态
						</el-button>
					</div>
					<div class="left">
						<el-button type="primary" @click="getWorks('search')">搜索</el-button>
						<el-button type="info" @click="reset()">重置</el-button>
					</div>
				</div>
			</div>
			<div class="form">
				<el-table border :data="tableData" style="width: 100%" @selection-change="handleSelectionChange"
					v-loading="loading">
					<el-table-column type="selection" width="40">
					</el-table-column>
					<el-table-column v-for="(item, index) in columnList" :key="index" :width="item.width || 'auto'"
						:prop="item.prop" :label="item.label">
						<template slot-scope="scope">
							<template v-if="item.prop === 'cover'">
								<el-image style="width: 30px; height: 30px" :src="scope.row[item.prop]"
									:preview-src-list="[scope.row[item.prop]]">
								</el-image>
								<div>
									<!-- <el-button type="text" size="mini" @click="getPredict(scope.row)">
										查找相似图片
									</el-button> -->
								</div>
							</template>
							<template v-else-if="item.prop === 'status'">
								<span v-if="scope.row[item.prop]==0">未发布</span>
								<span v-if="scope.row[item.prop]==1">已发布</span>
							</template>
							<template v-else-if="item.prop === 'joinLeapPlan'">
								<span v-if="scope.row[item.prop]==0">否</span>
								<span v-if="scope.row[item.prop]==1">是</span>
							</template>
							<template v-else-if="item.prop === 'notSaleSign'">
								<el-tag type="success" v-if="scope.row[item.prop]==0">正常流通</el-tag>
								<el-tag type="danger" v-if="scope.row[item.prop]==1">仅供收藏</el-tag>
								<el-tag type="warning" v-if="scope.row[item.prop]==2">仅供转增</el-tag>
							</template>
							<span v-else-if="item.prop === 'saleVisibility'">
								<el-tag type="danger" v-if="scope.row[item.prop]==0">下架</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop]==1">上架</el-tag>
							</span>
							<span v-else-if="item.prop === 'original'">
								<el-tag type="success" v-if="scope.row[item.prop]==0">一级市场</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop]==1">二级市场</el-tag>
							</span>
							<span v-else-if="item.prop === 'isFreeGas'">
								<el-tag type="success" v-if="scope.row[item.prop]==0">不免</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop]==1">一级市场免</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop]==2">二级市场免</el-tag>
								<el-tag type="success" v-if="scope.row[item.prop]==3">一二级市场都免</el-tag>
							</span>
							<span v-else-if="item.prop === 'onSaleStatus'">
								<el-tag type="success" v-if="scope.row[item.prop]==1">是</el-tag>
								<el-tag type="danger" v-if="scope.row[item.prop]==0">否</el-tag>
							</span>
							<span v-else> {{ keyToLabel(item.prop, scope.row[item.prop]) }}</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="80">
						<template slot-scope="scope">
							<el-button @click="whitelist(scope.row)" type="text" >白名单列表</el-button>
							<!-- <el-button @click="favoriteList(scope.row)" type="text" >关注列表</el-button> -->
							<!--            <el-button @click="deleteClick(scope.row)" type="text"  class="danger">删除</el-button>-->
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>
		<common-pagination ref="commonPagination" :page.sync="page" @change="getWorks">
		</common-pagination>
		<el-dialog title="白名单列表" :visible.sync="whitelistVisible">
			<el-table :data="whitelistData">
				<el-table-column :prop="item.prop" :label="item.label" width="150"
					v-for="(item,index) in whitelistColumn" :key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<el-dialog title="关注列表" :visible.sync="favoriteListVisible">
			<el-table :data="favoriteListData">
				<el-table-column :prop="item.prop" :label="item.label" width="150"
					v-for="(item,index) in favoriteListColumn" :key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<el-dialog title="审核状态" :visible.sync="showStatusVisible" width="40%">
			<el-radio-group v-model="showsStatus">
				<el-radio label="0">未审核</el-radio>
				<el-radio label="1">人审通过</el-radio>
				<el-radio label="2">人审不通过</el-radio>
				<el-radio label="3">机审通过</el-radio>
				<el-radio label="4">机审不通过</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="showStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="approvalStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="是否发布" :visible.sync="publishStatusVisible" width="40%">
			<el-radio-group v-model="publishStatus">
				<el-radio label="0">草稿</el-radio>
				<el-radio label="1">发布</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="publishStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="publishStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="上下架状态" :visible.sync="rackUpAndDownStatusVisible" width="40%">
			<el-radio-group v-model="rackUpAndDownStatus">
				<el-radio label="0">下架</el-radio>
				<el-radio label="1">上架</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="rackUpAndDownStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="rackUpAndDownStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="推荐级别" :visible.sync="recommendStatusVisible" width="40%">
			推荐级别：
			<el-input placeownerNickname="请输入推荐级别" v-model="recommendStatus" type="digit" style="width: 50%"
				@input="handleInput">
			</el-input>
			<span slot="footer">
				<el-button @click="recommendStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="recommendStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="提示风险" :visible.sync="riskStatusVisible" width="40%">
			<el-radio-group v-model="riskStatus">
				<el-radio label="0">无风险</el-radio>
				<el-radio label="1">有风险</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="riskStatusVisible = false">取 消</el-button>
				<el-button type="primary" @click="riskStatusFun()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="限制寄售作品" :visible.sync="showAstricUp" width="40%">
			<el-radio-group v-model="astricUpStatus">
				<el-radio label="0">禁止寄售</el-radio>
				<el-radio label="1">正常寄售</el-radio>
			</el-radio-group>
			<span slot="footer">
				<el-button @click="showAstricUp = false">取 消</el-button>
				<el-button type="primary" @click="submitAstricUp()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="相似图片" :visible.sync="predictDialog" width="1000px">
			<div>
				<div>原图</div>
				<div style="
		        display: inline-block;
		        width: 100px;
		        height: 100px;
		        margin-right: 10px;
		        margin-bottom: 10px;
		        margin-top: 10px;
		      ">
					<img :src="artworkImg" alt="" style="width: 100%; height: 100%" />
				</div>
				<div>百度图片</div>
				<div v-for="(item, index) in baidu_result" :key="index" style="
		        display: inline-block;
		        width: 200px;
		        height: 100%;
		        margin-right: 10px;
		        margin-bottom: 10px;
		      ">
					<el-image style="width: 100%; height: 100%" :src="item" :preview-src-list="[item]" fit="fill ">
					</el-image>
				</div>
			</div>
			<div>谷歌图片</div>
			<div v-for="(item, index) in google_result" :key="index" style="
		      display: inline-block;
		      width: 200px;
		      height: 100%;
		      margin-right: 10px;
		      margin-bottom: 10px;
		    ">
				<el-image :preview-src-list="[item]" style="width: 100%; height: 100%" :src="item" fit="fill ">
				</el-image>
			</div>
		</el-dialog>
	</d2-container>
</template>

<script>
	import footerBar from '@/views/configurationDict/window/footer'
	import common from '@/mixins/common'
	import { downloadBlob } from '@/utils/helper'
	export default {
		name: 'ListOfWorks',
		mixins: [common],
		components: {
			footerBar
		},
		data() {
			return {
				filterCondition: [{
						label: '排序顺序',
						key: 'flagDesc',
						type: 'select',
						options: [{
							value: '0',
							label: '升序'
						}, {
							value: '1',
							label: '降序'
						}]
					},
					{
						label: '排序类型',
						key: 'sortBy',
						type: 'select',
						options: [{
							value: '1',
							label: '权重'
						}, {
							value: '2',
							label: '发布时间'
						}, {
							value: '3',
							label: '购买时间'
						}]
					},
					{
						label: '持有者昵称',
						key: 'ownerNickname',
						type: 'input'
					},
					{
						label: '是否母版',
						key: 'ifMasterMask',
						type: 'select',
						options: [{
								value: '0',
								label: '子版'
							},
							{
								value: '1',
								label: '母版'
							}
						]
					},
					{
						label: '是否多版',
						key: 'ifMultiVersion',
						type: 'select',
						options: [{
							value: '0',
							label: '独版'
						}, {
							value: '1',
							label: '多版'
						}]
					},
					{
						label: '作品名称',
						key: 'name',
						type: 'input'
					},
					{
						label: '作品属性',
						key: 'isReal',
						type: 'select',
						options: [{
							value: '0',
							label: '非实物作品'
						}, {
							value: '1',
							label: '实物作品'
						}]
					},
					{
						label: '发布者昵称',
						key: 'publisherName',
						type: 'input'
					},
					{
						label: '风险提示',
						key: 'risk',
						type: 'select',
						options: [{
							value: '0',
							label: '无风险'
						}, {
							value: '1',
							label: '有风险'
						}]
					},
					{
						label: '藏品是否上架',
						key: 'sale',
						type: 'select',
						options: [{
							value: '0',
							label: '下架'
						}, {
							value: '1',
							label: '上架'
						}]
					},
					{
						label: '是否一级市场卖出',
						key: 'sellOut',
						type: 'select',
						options: [{
							value: '0',
							label: '未卖出'
						}, {
							value: '1',
							label: '已卖出'
						}]
					},
					{
						label: '版号',
						key: 'serial',
						type: 'input'
					},
					{
						label: '作品id',
						key: 'tid',
						type: 'input'
					},
					{
						label: '系列id',
						key: 'ctid',
						type: 'input'
					},
					{
						label: '作品类型',
						key: 'type',
						type: 'select',
						options: [{
								value: '0',
								label: '正常作品'
							},
							{
								value: '1',
								label: '爷爷'
							},
							{
								value: '2',
								label: '父亲'
							},
							{
								value: '3',
								label: '儿子'
							},
							{
								value: '11',
								label: '视频'
							},
							{
								value: '12',
								label: '音频'
							},
							{
								value: '13',
								label: '3D动态模型'
							},
							{
								value: '100',
								label: '领取'
							},
							{
								value: '110',
								label: '房子'
							},
							{
								value: '120',
								label: 'A虎'
							},
							{
								value: '121',
								label: 'B虎'
							},
							{
								value: '122',
								label: 'C虎'
							},
							{
								value: '123',
								label: 'D虎'
							},
							{
								value: '124',
								label: 'E虎'
							},
							{
								value: '125',
								label: '合成虎'
							},
							{
								value: '126',
								label: '给钱虎'
							},
							{
								value: '130',
								label: '伯德平民'
							},
							{
								value: '131',
								label: '伯德骑士'
							},
							{
								value: '132',
								label: '伯德大臣'
							},
							{
								value: '133',
								label: '伯德皇后'
							},
							{
								value: '134',
								label: '伯德国王'
							},
							{
								value: '140',
								label: '海盗'
							}
						]
					},
					{
						label: '创作品是否上架',
						key: 'visibility',
						type: 'select',
						options: [{
							value: '0',
							label: '下架'
						}, {
							value: '1',
							label: '上架'
						}]
					},
					{
						label: '是否加入飞跃计划',
						key: 'joinLeapPlan',
						type: 'select',
						options: [{
							value: '0',
							label: '否'
						}, {
							value: '1',
							label: '是'
						}]
					}
				],
				tableData: [],
				columnList: [{
						label: 'ID',
						prop: 'id',
						width: '80'
					},
					{
						label: '作品id',
						prop: 'tid',
						width: '150'
					},
					{
						label: '系列ID',
						prop: 'ctid',
						width: '160'
					},
					{
						label: '上架状态',
						prop: 'saleVisibility',
						width: '100'
					},
					{
						label: '版号',
						prop: 'serial',
						width: '80'
					},
					{
						label: '价格',
						prop: 'price',
						width: '80'
					},
					{
						label: '推荐级别',
						prop: 'recommend',
						width: '80'
					},
					{
						label: '作品名称',
						prop: 'name',
						width: '100'
					},
					{
						label: '作品封面图',
						prop: 'cover',
						width: '100'
					},
					{
						label: '作品属性',
						prop: 'isReal',
						width: '80'
					},
					{
						label: '发布者',
						prop: 'publisher',
						width: '80'
					},
					{
						label: '持有者',
						prop: 'ownerNickname',
						width: '80'
					},
					{
						label: '是否卖出',
						prop: 'original',
						width: '80'
					},
					{
						label: '最近购买时间',
						prop: 'buyTime',
						width: '160'
					},
					{
						label: '铸造时间',
						prop: 'createdAt',
						width: '160'
					},
					{
						label: '发售时间',
						prop: 'saleTime',
						width: '160'
					},
					{
						label: '交易是否免gas费',
						prop: 'isFreeGas',
						width: '160'
					},
					{
						label: '是否可以上架',
						prop: 'onSaleStatus',
					},
					{
						label: '版税比例',
						prop: 'copyrightFeeRadio',
					},
					{
						label: '风险提示',
						prop: 'risk',
						width: '80'
					},
					{
						label: '审核状态',
						prop: 'shows',
						width: '80'
					},
					// {
					//   label: '藏品上架',
					//   prop: 'sale',
					//   width: '80'
					// },
					{
						label: '作品类型',
						prop: 'type',
						width: '80'
					},
					{
						label: '作品状态',
						prop: 'notSaleSign',
						width: '80'
					},
					{
						label: '发布状态',
						prop: 'status',
						width: '100'
					},
					{
						label: '是否为飞跃计划',
						prop: 'joinLeapPlan',
						width: '100'
					}
				],
				search: {
					publisherName: '',
					ctid: '',
					flagDesc:'1',
					sortBy:'3'
				},
				whitelistVisible: false,
				whitelistData: [],
				whitelistColumn: [{
						label: '用户ID',
						prop: 'uid'
					},
					{
						label: '用户名',
						prop: 'nickname'
					},
					{
						label: '添加时间',
						prop: 'updateAt'
					}
				],
				favoriteListVisible: false,
				favoriteListData: [],
				favoriteListColumn: [{
						label: '用户ID',
						prop: 'uid'
					},
					{
						label: '用户名',
						prop: 'username'
					},
					{
						label: '关注时间',
						prop: 'updatedAt'
					}
				],
				idList: [],
				tidList: [],
				showStatusVisible: false,
				showsStatus: '0',
				publishStatusVisible: false,
				publishStatus: '0',
				rackUpAndDownStatusVisible: false,
				rackUpAndDownStatus: '0',
				recommendStatusVisible: false,
				recommendStatus: 0,
				riskStatusVisible: false,
				riskStatus: '0',
				predictDialog: false,
				loading: false,
				google_result: [],
				baidu_result: [],
				artworkImg: '',
				showAstricUp: false,
				astricUpStatus: '',
			}
		},
		created() {
			// 从用户列表跳转 自动添加筛选条件 铸造这为该用户
			this.search.ctid = this.$route.query.ctid
			this.search.publisherName = this.$route.query.publisherName
			this.getWorks()
		},
		methods: {
			// 在 Input 值改变时触发 只能输入正整数（包括0）
			handleInput(val) {
				let value = val.replace(/\D/g, '') // 只能输入数字
				value = value.replace(/^0+(\d)/, '$1') // 第一 0开头，0后面为数字，则过滤掉，取后面的数字
				value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
				this.recommendStatus = value
			},
			/**
			 *  获取列表
			 * @returns {Promise<void>}
			 */
			async getWorks(init) {
				if (init) {
					this.initPage()
				}
				console.log(this.page)
				const obj = this.search
				Object.keys(obj).forEach(item => {
					if (!obj[item]) delete obj[item]
				})
				const {
					result: {
						list,
						totalCount
					}
				} = await this.$api.goodsList({
					...obj,
					...this.page
				})
				this.tableData = list
				this.page.totalCount = totalCount
			},
			/**
			 * 白名单列表
			 * @param row
			 * @returns {Promise<void>}
			 */
			async whitelist(row) {
				this.whitelistVisible = true
				const {
					tid,
					ctid,
					mold
				} = row
				let obj = {
					tid
				}
				if (mold === 2) {
					obj = {
						ctid
					}
				}
				const {
					result: {
						list
					}
				} = await this.$api.allowList(obj)
				this.whitelistData = list
			},
			/**
			 * 关注列表
			 */
			async favoriteList(row) {
				this.favoriteListVisible = true
				const {
					result: {
						list
					}
				} = await this.$api.favoriteList({
					tid: row.tid
				})
				this.favoriteListData = list
			},
			/**
			 * 审核状态
			 */
			async approvalStatusFun() {
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdateAuditState({
					idListStr: JSON.stringify(idList),
					shows: this.showsStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
					this.showStatusVisible = false
				} else {
					this.$message.error(msg)
				}
			},
			/**
			 * 发布状态
			 */
			async publishStatusFun() {
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdatePublishState({
					idListStr: JSON.stringify(idList),
					status: this.publishStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
					this.publishStatusVisible = false
				} else {
					this.$message.error(msg)
				}
			},
			/**
			 * 上下架
			 */
			async rackUpAndDownStatusFun() {
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdateRackStatus({
					idListStr: JSON.stringify(idList),
					status: this.rackUpAndDownStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
					this.rackUpAndDownStatusVisible = false
				}
			},
			/**
			 * 推荐级别
			 */
			async recommendStatusFun() {
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdateRecommendLevel({
					idListStr: JSON.stringify(idList),
					recommend: this.recommendStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
					this.recommendStatusVisible = false
				} else {
					this.$message.error(msg)
				}
			},

			/**
			 * 提示风险
			 */
			async riskStatusFun() {
				const {
					idList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.batchUpdateRiskState({
					idListStr: JSON.stringify(idList),
					status: this.riskStatus
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
					this.riskStatusVisible = false
				} else {
					this.$message.error(msg)
				}
			},
			async destroy_open() {
				this.$confirm('是否确认销毁选中的作品', '作品销毁', {
					confirmButtonText: '确认销毁作品',
					cancelButtonText: '取消',
					type: 'error'
				}).then(() => {
					this.destroy()
				}).catch(() => {

				});
			},
			async destroy() {
				const {
					tidList
				} = this
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.destroyByTids({
					tidStr: JSON.stringify(tidList)
				})
				if (code === 0) {
					this.$message.success(msg)
					await this.getWorks()
				} else {
					this.$message.error(msg)
				}
			},
			/**
			 * 字典
			 * @param key
			 * @param value
			 * @returns {*}
			 */
			keyToLabel(key, value) {
				const showsStatus = {
					0: '未审核',
					1: '人审通过',
					2: '人审不通过',
					3: '机审通过',
					4: '机审不通过'
				}
				const listingStatus = {
					0: '下架',
					1: '上架'
				}
				if (key === 'shows') {
					value = showsStatus[value]
				} else if (key === 'listingStatus') {
					value = listingStatus[value]
				} else {
					const obj = this.filterCondition.find(item => {
						return item.key === key
					})
					const res = obj?.options?.find(item => {
						return item.value === value + ''
					})
					value = res?.label || value
				}
				return value
			},
			/**
			 * 选中
			 */
			handleSelectionChange(val) {
				this.idList = val.map(item => item.id)
				this.tidList = val.map(item => item.tid)
				console.log(this.idList)
			},
			/**
			 * 重置
			 */
			reset() {
				this.search = {}
				this.page.pageNum = 1
				this.getWorks()
			},
			// 查询相似图片
			async getPredict(val) {
				this.loading = true
				const {
					result
				} = await this.$api.getPredict({
					pic: val.cover,
					token: 'a556554e98429fc8a36792054d9b546b'
				}).catch(() => {
					this.loading = false
				})
				this.predictDialog = true
				this.loading = false
				this.baidu_result = result.baidu_result
				this.google_result = result.google_result
				this.artworkImg = result.img_url
			},
			async submitAstricUp() {
				const {
					idList
				} = this
				let res = await this.$api.batchUpdateOnSaleStatus({
					idListStr: JSON.stringify(idList),
					status: this.astricUpStatus
				});
				if (res.status.code == 0) {
					this.$message.success("操作成功")
					this.showAstricUp = false
				} else {
					this.$message.error(res.status.msg)
					this.showAstricUp = false
				}
			},
			async export_shop() {
				if (this.page.totalCount > 60000) {
					this.$message.error('导出文件过大,不能超过60000条')
				} else {
					this.loading = true
					const res = await this.$api.goodsListExport({
						...this.search
					})
					if (res.type === 'application/json') {
						this.loading = false
					  // blob 转 JSON
					  const enc = new TextDecoder('utf-8')
					  res.arrayBuffer().then(buffer => {
					    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
					    this.$message.error(data.status?.msg)
					  })
					} else {
						this.loading = false
					  downloadBlob(res, '商品')
					  this.$message.success('导出成功')
					  this.getWorks()
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main {
		.header {
			.form {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				flex: 1;

				.place-ownerNickname {
					width: 18%;
					overflow: hidden;
					margin-left: 15px;
				}

				.item {
					margin-left: 15px;
					display: flex;
					margin-bottom: 20px;
					justify-content: space-between;
					align-items: center;
					width: 18%;

					.el-select,
					.el-input {
						flex: 1;
						margin-left: 20px;
					}

					span {
						width: 80px;
						margin-right: -10px;
					}
				}
			}

			.action {
				display: flex;
				justify-content: space-between;
				height: 100%;
				flex-wrap: wrap;

				.el-button {
					margin-bottom: 20px;
				}
			}
		}
	}

	.el-table__cell {
		padding: 0px !important;
	}
</style>
