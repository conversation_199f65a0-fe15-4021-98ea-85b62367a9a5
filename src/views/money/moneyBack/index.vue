<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户昵称">
        <el-input
          v-model="formInline.nickname"
          placeholder="请输入用户昵称"
        ></el-input>
      </el-form-item>
      <el-form-item label="户名">
        <el-input
          v-model="formInline.realName"
          placeholder="请输入户名"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input
          v-model="formInline.phone"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select  v-model="formInline.checkStatus" placeholder="审核状态">
          <el-option label="全部" value=""></el-option>
          <el-option label="待审核" value="REFUND_CHECKING"></el-option>
          <el-option label="审核通过" value="REFUND_CHECK_SUCCESS"></el-option>
          <el-option label="审核驳回" value="REFUND_CHECK_FAIL"></el-option>
          <el-option label="打款中" value="REFUND_PAYING"></el-option>
          <el-option label="打款成功" value="REFUND_PAY_SUCCESS"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button type="primary" @click="batchExport()">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      v-loading="loading"
      element-loading-text="正在导出"
    >
      <el-table-column
        fixed
        prop="userId"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nickname"
        label="用户昵称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="realName"
        label="户名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="bankCardNumber"
        label="银行卡号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="bankName"
        label="银行名称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="branchBankName"
        label="开户信息"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="phone"
        label="预留手机号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="refundAmount"
        label="退还金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="refundStartTime"
        label="申请退还时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="checkStatus" label="审核状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.checkStatus == 'INIT'" type="success"
            >待支付</el-tag
          >
          <!-- <el-tag
            v-if="scope.row.checkStatus == 'ORDER_CHECKING'"
            type="success"
            >审核中</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'PAY_CHECK_SUCCESS'"
            type="success"
            >审核通过</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'PAY_CHECK_FAIL'"
            type="success"
            >审核失败</el-tag
          > -->
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_CHECKING'"
            type="success"
            >审核中</el-tag
          >
          <el-tag v-if="scope.row.checkStatus == 'USER_CANCEL'" type="danger"
            >用户取消</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_CHECK_SUCCESS'"
            type="success"
            >审核成功</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_CHECK_FAIL'"
            type="success"
            >审核失败</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_USER_CANCEL'"
            type="success"
            >退款取消</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_PAY_SUCCESS'"
            type="success"
            >打款成功</el-tag
          >
          <el-tag v-if="scope.row.checkStatus == 'REFUND_PAYING'" type="success"
            >打款中</el-tag
          >
          <el-tag v-if="scope.row.checkStatus == 'FREEZE'" type="danger"
            >冻结</el-tag
          >
          <el-tag v-if="scope.row.checkStatus == 'UNFREEZE'" type="danger"
            >解冻</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        align="center"
      ></el-table-column>

      <el-table-column
        fixed="right"
        label="其他操作"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            
            @click="audit_click(scope.row)"
            v-if="scope.row.checkStatus == 'REFUND_CHECKING'"
            >审核</el-button
          >
          <el-button
            type="text"
            
            @click="remit_click(scope.row)"
            v-if="scope.row.checkStatus == 'REFUND_CHECK_SUCCESS'"
            >打款</el-button
          >
          <el-button
            type="text"
            
            @click="reson_click(scope.row)"
            v-if="scope.row.checkStatus == 'REFUND_CHECK_FAIL'"
            >驳回原因</el-button
          >
          <el-button type="text"  @click="remark_click(scope.row)"
            >备注</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="审核操作" :visible.sync="isDialog" width="35%" center>
      <el-form :model="form">
        <p>审核通过后用户全部保证金将按照用户填写的银行账号开启打款审核流程</p>
        <p>审核驳回将终止本次用户退还保证金流程并告知驳回原因。</p>
        <el-form-item>
          <el-radio v-model="form.radio" label="REFUND_CHECK_SUCCESS"
            >审核通过</el-radio
          >
          <el-radio v-model="form.radio" label="REFUND_CHECK_FAIL"
            >审核驳回</el-radio
          >
        </el-form-item>
        <el-form-item v-if="form.radio == 'REFUND_CHECK_FAIL'">
          <el-input
            v-model="form.remark"
            placeholder="请输入100字以内驳回原因"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="audit_submit()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="备注信息"
      :visible.sync="isDialogRemark"
      width="35%"
      center
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="remark"
            placeholder="请输入100字以内备注信息"
            type="textarea"
            maxlength="100"
            show-word-limit
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogRemark = false">取 消</el-button>
        <el-button type="primary" @click="submit_remark()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="驳回原因"
      :visible.sync="isDialogReson"
      width="35%"
      center
    >
      <p v-if="rusultReson == null">无</p>
      <p>{{ rusultReson }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogReson = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="submit_reson()">确 定</el-button> -->
      </div>
    </el-dialog>
    <el-dialog
      title="打款操作"
      :visible.sync="isDialogRemit"
      width="35%"
      center
    >
      <p>确定要打款吗？</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogRemit = false">关 闭</el-button>
        <el-button type="primary" @click="submit_remit1()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
import { downloadBlob } from '@/utils/helper'
export default {
  name: 'moneyBack',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        nickname: undefined,
        realName: undefined,
        phone: undefined,
        createAt: null,
        createAtStart: null,
        createAtEnd: null,
        checkStatus: undefined
      },
      headers: {
        authorization: ''
      },
      form: {
        radio: 'REFUND_CHECK_SUCCESS',
        remark: undefined
      },
      formLabelWidth: '120px',
      isDialog: false,
      refundRecordId: undefined,
      userId: undefined,
      isDialogRemark: false,
      remark: undefined,
      isDialogReson: false,
      rusultReson: '',
      isDialogRemit: false,
      loading: false
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete(1)
  },
  methods: {
    // 导出
    async batchExport () {
      if (this.total > 60000) {
        this.$message.error('导出文件过大,不能超过60000条')
      } else {
        this.loading = true
        if (this.formInline.createAt) {
          this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
          this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
        } else {
          this.formInline.createAtStart = null
          this.formInline.createAtEnd = null
        }
        const res = await this.$api.refundExport({
          nickname: this.formInline.nickname,
          realName: this.formInline.realName,
          phoneNum: this.formInline.phone,
          checkStatus: this.formInline.checkStatus,
          startTime: this.formInline.createAtStart,
          endTime: this.formInline.createAtEnd
        })
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
          this.loading = false
          this.getSelete()
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '保证金退还审核' + Date.now() + '.xlsx')
          this.$message.success('导出成功')
          this.loading = false
          this.getSelete()
        }
      }
    },
    // 查询
    async getSelete (page) {
      if (this.formInline.createAt) {
        this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
        this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
      } else {
        this.formInline.createAtStart = null
        this.formInline.createAtEnd = null
      }
      const res = await this.$api.getRefundList({
        pageNum: page,
        pageSize: 15,
        nickname: this.formInline.nickname,
        realName: this.formInline.realName,
        phoneNum: this.formInline.phone,
        checkStatus: this.formInline.checkStatus,
        startTime: this.formInline.createAtStart,
        endTime: this.formInline.createAtEnd
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    clear () {
      this.formInline.nickname = undefined
      this.formInline.realName = undefined
      this.formInline.phone = undefined
      this.formInline.createAt = undefined
      this.formInline.checkStatus = undefined
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    audit_click (val) {
      this.isDialog = true
      this.refundRecordId = val.refundRecordId
    },
    async audit_submit () {
      await this.$api.refundCheck({
        id: this.refundRecordId,
        remark: this.form.remark,
        status: this.form.radio
      })
      this.getSelete(1)
      this.isDialog = false
      this.form = {
        remark: undefined,
        status: undefined
      }
      this.$message.success('审核成功')
    },
    // 备注
    remark_click (val) {
      this.isDialogRemark = true
      this.refundRecordId = val.refundRecordId
      this.remark = val.remark
    },
    async submit_remark () {
      await this.$api.refundRemark({
        refundRecordId: this.refundRecordId,
        remark: this.remark
      })
      this.getSelete(1)
      this.isDialogRemark = false
      this.$message.success('备注修改成功')
    },
    // 打款
    remit_click (val) {
      this.refundRecordId = val.refundRecordId
      this.isDialogRemit = true
    },
    async submit_remit1 () {
      await this.$api.refundpayment({
        refundRecordId: this.refundRecordId
      })
      this.isDialogRemit = false
      this.$message.success('打款成功')
    },
    // 驳回原因
    reson_click (val) {
      this.refundRecordId = val.refundRecordId
      this.isDialogReson = true
      this.submit_reason()
    },
    async submit_reason () {
      const res = await this.$api.refundReson({
        refundRecordId: this.refundRecordId
      })
      if (res.result.reason === '') {
        this.rusultReson = '无'
      } else {
        this.rusultReson = res.result.reason
      }
    }
  }
}
</script>
