<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户昵称">
        <el-input
          v-model="formInline.nickname"
          placeholder="请输入用户昵称"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户地址">
        <el-input
          v-model="formInline.contractAddress"
          placeholder="请输入用户地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"


    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userNickname"
        label="用户昵称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="contactAddress"
        label="用户地址"
        align="center"
      ></el-table-column>

      <!-- <el-table-column label="追加次数" align="center" width="80">
        <template slot-scope="scope">
          <el-button type="text"  @click="addTo_money(scope.row)">{{
            scope.row.depositTimes
          }}</el-button>
        </template>
      </el-table-column> -->

      <el-table-column
        prop="currentDepositAmount"
        label="当前保证金额"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="firstPayTime"
        label="首次支付时间"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="lastPayTime"
        label="最近支付时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        align="center"
      ></el-table-column>
      <el-table-column
        fixed="right"
        label="保证金变更"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text"  @click="add_money(scope.row)"
            >增加</el-button
          >
          <el-button type="text"  @click="reduce_money(scope.row)"
            >减少</el-button
          >
          <el-button
            type="text"
            
            @click="freeze_money(scope.row)"
            v-if="scope.row.status !== 'FREEZE'"
            >冻结</el-button
          >
          <el-button
            type="text"
            
            @click="unfreeze_money(scope.row)"
            v-if="scope.row.status == 'FREEZE'"
            >解冻</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="其他操作"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text"  @click="remark_money(scope.row)"
            >备注</el-button
          >
          <el-button type="text"  @click="change_money(scope.row)"
            >变更记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" width="35%" center>
      <el-form :model="form">
        <h2>用户{{ userName }}当前保证金额：{{ diaMoney }}元</h2>
        <p v-if="title == '用户保证金增加'">
          保证金增加后，客户端将显示用户本金额+增加金额的全部金额，本操作设计用户资产变动，请谨慎操作
        </p>
        <p v-if="title == '用户保证金减少'">
          保证金减少后，客户端将显示用户本金额-减少金额的剩余金额，本操作设计用户资产变动，请谨慎操作
        </p>
        <p v-if="title == '用户保证金冻结'">
          用户保证金冻结后，个人主页会显示保证金冻结状态，冻结期间，用户无法进行退还保证金，正在退还的保证金会中止退还流程。
        </p>
        <el-form-item v-if="title == '用户保证金增加'">
          <el-input
            v-model="form.addAmount"
            placeholder="请输入增加金额"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="title == '用户保证金减少'">
          <el-input
            v-model="form.addAmount"
            placeholder="请输入减少金额"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="title == '用户保证金减少'">
          <el-input
            v-model="form.reason"
            placeholder="请输入100字以内保证金减少原因"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item v-if="title == '用户保证金冻结'">
          <el-input
            v-model="form.reason"
            placeholder="请输入100字以内保证金冻结原因"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="备注信息"
      :visible.sync="isDialogRemark"
      width="35%"
      center
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="remark"
            placeholder="请输入1000字以内备注信息"
            type="textarea"
            maxlength="1000"
            show-word-limit
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogRemark = false">取 消</el-button>
        <el-button type="primary" @click="submit_remark()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户保证金追加记录"
      :visible.sync="isDialogAddTo"
      width="60%"
      center
      :before-close="addTo_close"
    >
      <el-table
        :data="addToData"
        ref="multipleTable"
        @selection-change="addToSelectionChange"
        tooltip-effect="dark"
        show-header
        border
        style="width: 100%"
      >
        <el-table-column
          prop="payTimes"
          label="支付次数"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payTime"
          label="支付时间"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payAmount"
          label="支付金额"
          align="center"
        ></el-table-column>
        <el-table-column prop="checkResult" label="审核结果" align="center">
          <template scope="scope">
            <el-tag v-if="scope.row.checkResult == 'INIT'" type="success"
              >待支付</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'ORDER_CHECKING'"
              type="success"
              >审核中</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'PAY_CHECK_SUCCESS'"
              type="success"
              >审核通过</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'PAY_CHECK_FAIL'"
              type="success"
              >审核失败</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECKING'"
              type="success"
              >审核中</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECK_SUCCESS'"
              type="success"
              >审核成功</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECK_FAIL'"
              type="danger"
              >审核失败</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_USER_CANCEL'"
              type="danger"
              >退款取消</el-tag
            >
            <el-tag v-if="scope.row.checkResult == 'FREEZE'" type="danger"
              >冻结</el-tag
            >
            <el-tag v-if="scope.row.checkResult == 'UNFREEZE'" type="danger"
              >解冻</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <div
        class=""
        style="
          display: flex;
          justify-content: center;
          background-color: #ffffff;
        "
      >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total_addTo"
          :page-size="15"
          style="padding: 20px; background-color: #ffffff"
          @current-change="xuanzeAddTo"
          @size-change="xuanzeAddTo"
        >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addTo_close()">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户保证金变更记录"
      :visible.sync="isDialogChange"
      width="60%"
      center
      :before-close="change_close"
    >
      <el-table
        :data="changeData"
        ref="multipleTable"
        tooltip-effect="dark"
        show-header
        border
        style="width: 100%"
      >
        <el-table-column prop="type" label="变更属性" align="center">
          <template scope="scope">
            <el-tag v-if="scope.row.type == 'USER_DEPOSIT'" type="success"
              >用户充值保证金</el-tag
            >
            <el-tag v-if="scope.row.type == 'USER_REFUND'" type="danger"
              >用户退保</el-tag
            >
            <el-tag v-if="scope.row.type == 'ADMIN_SUBTRACT'" type="danger"
              >平台扣减</el-tag
            >
            <el-tag v-if="scope.row.type == 'ADMIN_ADD'" type="success"
              >平台增加</el-tag
            >
            <el-tag v-if="scope.row.type == 'FREEZE'" type="danger"
              >平台冻结</el-tag
            >
            <el-tag v-if="scope.row.type == 'UNFREEZE'" type="success"
              >平台解冻</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="changeTime"
          label="变更时间"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="changeAmount"
          label="变更金额"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="reason"
          label="变更原因"
          align="center"
        ></el-table-column>
      </el-table>
      <div
        class=""
        style="
          display: flex;
          justify-content: center;
          background-color: #ffffff;
        "
      >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total_change"
          :page-size="15"
          style="padding: 20px; background-color: #ffffff"
          @current-change="xuanzeChange"
          @size-change="xuanzeChange"
        >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="change_close()">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户保证金解冻"
      :visible.sync="isDialogUnfree"
      width="35%"
      center
    >
      <p>确定要解冻用户账户保证金吗</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogUnfree = false">关 闭</el-button>
        <el-button type="primary" @click="submit_unfree()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
  name: 'moneyList',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        nickname: undefined,
        contractAddress: undefined,
        createAt: null,
        createAtStart: null,
        createAtEnd: null
      },
      headers: {
        authorization: ''
      },
      form: {
        addAmount: undefined,
        reason: undefined
      },
      formLabelWidth: '120px',
      isDialog: false,
      title: 'add',
      userId: undefined,
      currentDepositAmount: undefined,
      isDialogRemark: false,
      remark: '',
      isDialogAddTo: false,
      isDialogChange: false,
      addToData: [],
      total_addTo: 1,
      pageNumAddTo: 1,
      changeData: [],
      pageNumChange: 1,
      total_change: 1,
      isDialogUnfree: false,
      userName: '',
      diaMoney: ''
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete(1)
  },
  methods: {
    // 查询
    async getSelete (page) {
      if (this.formInline.createAt) {
        this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
        this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
      } else {
        this.formInline.createAtStart = null
        this.formInline.createAtEnd = null
      }
      const res = await this.$api.getMoenyList({
        pageNum: page,
        pageSize: 15,
        nickname: this.formInline.nickname,
        contractAddress: this.formInline.contractAddres,
        startTime: this.formInline.createAtStart,
        endTime: this.formInline.createAtEnd
      })
      this.tableData = res.result.list
      console.log(this.tableData)
      this.total = res.result.totalCount
    },
  clear () {
    this.formInline.nickname = undefined
    this.formInline.contractAddress = undefined
    this.formInline.createAt = undefined
  },
  xuanze (val) {
    this.getSelete(val)
  },
  handleSelectionChange (val) {
    this.multipleSelection = val
  },
  addToSelectionChange () {},
  add_money (val) {
    this.isDialog = true
    this.title = '用户保证金增加'
    this.userId = val.id
    this.currentDepositAmount = val.currentDepositAmount
    this.userName = val.userNickname
    this.diaMoney = val.currentDepositAmount
  },
  reduce_money (val) {
    this.isDialog = true
    this.title = '用户保证金减少'
    this.userId = val.id
    this.currentDepositAmount = val.currentDepositAmount
    this.userName = val.userNickname
    this.diaMoney = val.currentDepositAmount
  },
  freeze_money (val) {
    this.isDialog = true
    this.title = '用户保证金冻结'
    this.userId = val.id
    this.currentDepositAmount = val.currentDepositAmount
    this.userName = val.userNickname
    this.diaMoney = val.currentDepositAmount
  },
  // 解冻
  unfreeze_money (val) {
    this.isDialogUnfree = true
    this.userId = val.id
  },
  async submit_unfree () {
    await this.$api.unfreeze_money({
      userId: this.userId
    })
    this.getSelete(1)
    this.isDialogUnfree = false
    this.$message.success('保证金解冻成功')
  },
  async submit () {
    if (this.title === '用户保证金增加') {
      await this.$api.add_money({
        userId: this.userId,
        addAmount: this.form.addAmount
      })
      this.getSelete(1)
      this.isDialog = false
      this.form = {
        addAmount: undefined,
        reason: undefined
      }
      this.$message.success('保证金增加成功')
    } else if (this.title === '用户保证金减少') {
      await this.$api.reduce_money({
        userId: this.userId,
        reduceAmount: this.form.addAmount,
        reason: this.form.reason
      })
      this.getSelete(1)
      this.isDialog = false
      this.form = {
        addAmount: undefined,
        reason: undefined
      }
      this.$message.success('保证金减少成功')
    } else {
      await this.$api.freeze_money({
        userId: this.userId,
        reason: this.form.reason
      })
      this.getSelete(1)
      this.isDialog = false
      this.form = {
        addAmount: undefined,
        reason: undefined
      }
      this.$message.success('保证金冻结成功')
    }
  },
  remark_money (val) {
    this.isDialogRemark = true
    this.userId = val.id
    this.remark = val.remark
  },
  async submit_remark () {
    await this.$api.remark_money({
      userId: this.userId,
      remark: this.remark
    })
    this.getSelete(1)
    this.isDialogRemark = false
    this.$message.success('备注修改成功')
  },
  // 追加记录
  async addTo_money (val) {
    if (val) {
      this.userId = val.id
    }
    this.isDialogAddTo = true
    const res = await this.$api.addTo_money({
      pageNum: this.pageNumAddTo,
      pageSize: 10,
      userId: this.userId
    })
    this.addToData = res.result.list
    this.total_addTo = res.result.totalCount
  },
  xuanzeAddTo (val) {
    this.pageNumAddTo = val
    this.addTo_money()
  },
  addTo_close () {
    this.pageNumAddTo = 1
    this.total_addTo = 1
    this.isDialogAddTo = false
    this.addToData = []
  },
  // 变更记录
  change_close () {
    this.isDialogChange = false
    this.changeData = []
    this.pageNumChange = 1
    this.total_change = 1
  },
  xuanzeChange (val) {
    this.pageNumChange = val
    this.change_money()
  },
  async change_money (val) {
    if (val) {
      this.userId = val.id
    }
    this.isDialogChange = true
    const res = await this.$api.change_money({
      pageNum: this.pageNumChange,
      pageSize: 10,
      uid: this.userId
    })
    this.changeData = res.result.list
    this.total_change = res.result.totalCount
  }
  }
}
</script>
