<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户昵称">
        <el-input
          v-model="formInline.nickname"
          placeholder="请输入用户昵称"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户地址">
        <el-input
          v-model="formInline.contractAddress"
          placeholder="请输入用户地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="depositNo"
        label="订单号"
        align="center"
      ></el-table-column>

      <!-- <el-table-column
        prop="contactAddress"
        label="订单名称"
        align="center"
      ></el-table-column> -->

      <el-table-column
        prop="nickname"
        label="用户昵称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="contractAddress"
        label="用户地址"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="payAmount"
        label="支付金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="payTime"
        label="支付时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="payMethod" label="付款方式" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.payMethod == 'BALANCE'">余额支付</el-tag>
          <el-tag v-if="scope.row.payMethod == 'WECHAT'">微信</el-tag>
          <el-tag v-if="scope.row.payMethod == 'ALIPAY'">支付宝</el-tag>
          <el-tag v-if="scope.row.payMethod == 'BANK_CARD'">银行卡</el-tag>
          <el-tag v-if="scope.row.payMethod == 'APPLY_PAY'">苹果</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="outTradeNo"
        label="商户订单号"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="订单状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == 'INIT'" type="success"
            >待支付</el-tag
          >
          <el-tag v-if="scope.row.status == 'ORDER_CHECKING'" type="success"
            >审核中</el-tag
          >
          <el-tag v-if="scope.row.status == 'PAY_CHECK_SUCCESS'" type="success"
            >审核通过</el-tag
          >
          <el-tag v-if="scope.row.status == 'PAY_CHECK_FAIL'" type="success"
            >审核失败</el-tag
          >
          <el-tag v-if="scope.row.status == 'REFUND_CHECKING'" type="success"
            >审核中</el-tag
          >
          <el-tag
            v-if="scope.row.status == 'REFUND_CHECK_SUCCESS'"
            type="success"
            >审核成功</el-tag
          >
          <el-tag v-if="scope.row.status == 'REFUND_CHECK_FAIL'" type="success"
            >审核失败</el-tag
          >
          <el-tag v-if="scope.row.status == 'REFUND_USER_CANCEL'" type="success"
            >退款取消</el-tag
          >
          <el-tag v-if="scope.row.status == 'FREEZE'" type="danger"
            >冻结</el-tag
          >
          <el-tag v-if="scope.row.status == 'UNFREEZE'" type="danger"
            >解冻</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="title" :visible.sync="isDialog" width="35%" center>
      <el-form :model="form">
        <h2>用户XXXX当前保证金额：7000元</h2>
        <el-form-item>
          <el-input
            v-model="form.addAmount"
            placeholder="请输入减少金额"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <!-- <el-button type="primary" @click="submit()">确 定</el-button> -->
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
  name: 'moenyOrder',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        nickname: undefined,
        contractAddress: undefined,
        createAt: null,
        createAtStart: null,
        createAtEnd: null
      },
      headers: {
        authorization: ''
      },
      form: {},
      formLabelWidth: '120px',
      isDialog: false
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete(1)
  },
  methods: {
    // 查询
    async getSelete (page) {
      if (this.formInline.createAt) {
        this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
        this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
      } else {
        this.formInline.createAtStart = null
        this.formInline.createAtEnd = null
      }
      const res = await this.$api.getOrderList({
        pageNum: page,
        pageSize: 15,
        nickname: this.formInline.nickname,
        contractAddress: this.formInline.contractAddress,
        startTime: this.formInline.createAtStart,
        endTime: this.formInline.createAtEnd
      })
      this.tableData = res.result.list
      console.log(this.tableData)
      this.total = res.result.totalCount
    },
    clear () {
      this.formInline.nickname = undefined
      this.formInline.contractAddress = undefined
      this.formInline.createAt = undefined
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    }
  }
}
</script>
