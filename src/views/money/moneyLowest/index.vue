<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="保证金全局配置">
        <el-form
          :model="numberValidateForm"
          label-width="200px"
          class="demo-ruleForm"
        >
          <el-form-item label="保证金最低金额限制">
            <el-input
              type="age"
              v-model.number="numberValidateForm.result"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item> 当前保证金为：{{ result }} </el-form-item>
          <el-form-item>
            <el-button @click="resetForm()">取消返回</el-button>

            <el-button type="primary" @click="submitForm()">提交</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
  name: 'moneyLowest',
  data () {
    return {
      tableData: [],
      total: 1,
      numberValidateForm: {
        result: ''
      },
      headers: {
        authorization: ''
      },
      formLabelWidth: '120px',
      isDialog: false,
      result: ''
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete()
  },
  methods: {
    // 查询
    async getSelete (page) {
      const res = await this.$api.getMoenyLow({})
      this.result = res.result
    }
  },
  async submitForm () {
    await this.$api.configureEdit({
      securityDepositAmount: this.numberValidateForm.result
    })
    this.getSelete()
    this.$message.success('最低保证金编辑成功')
  },
  resetForm () {}
}
</script>
