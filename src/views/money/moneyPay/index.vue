<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="订单号">
        <el-input
          v-model="formInline.depositNo"
          placeholder="请输入订单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户昵称">
        <el-input
          v-model="formInline.nickname"
          placeholder="请输入用户昵称"
        ></el-input>
      </el-form-item>

      <el-form-item label="审核状态">
        <el-select  v-model="formInline.status" placeholder="审核状态">
          <el-option label="全部" value=""></el-option>
          <el-option label="待审核" value="ORDER_CHECKING"></el-option>
          <el-option label="审核通过" value="PAY_CHECK_SUCCESS"></el-option>
          <el-option label="审核驳回" value="PAY_CHECK_FAIL"></el-option>
          <!-- <el-option label="打款成功" value="0"></el-option>
          <el-option label="打款失败" value="0"></el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item label="支付时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button type="primary" @click="exportForm()">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="depositNo"
        label="订单号"
        align="center"
      ></el-table-column>

      <!-- <el-table-column
        prop="realName"
        label="订单名称"
        align="center"
      ></el-table-column> -->
      <!-- <el-table-column prop="depositAttribute" label="申请属性" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="addTo_money(scope.row)">
            <span v-if="scope.row.depositAttribute == '0'">
             首次支付
            </span>
            <span v-if="scope.row.depositAttribute !== '0'">
              {{ scope.row.depositAttribute }}次追加支付
            </span>
          </el-button>
        </template>
      </el-table-column> -->

      <el-table-column
        prop="nickname"
        label="用户昵称"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="changeAmount"
        label="本次支付金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="currentAmount"
        label="当前保证金额"
        align="center"
      ></el-table-column>
      <el-table-column prop="payMethod" label="付款方式" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.payMethod == 'BALANCE'">余额支付</el-tag>
          <el-tag v-if="scope.row.payMethod == 'WECHAT'">微信</el-tag>
          <el-tag v-if="scope.row.payMethod == 'ALIPAY'">支付宝</el-tag>
          <el-tag v-if="scope.row.payMethod == 'BANK_CARD'">银行卡</el-tag>
          <el-tag v-if="scope.row.payMethod == 'APPLY_PAY'">苹果</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="payTime"
        label="支付时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="checkStatus" label="审核状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.checkStatus == 'INIT'" type="success"
            >待支付</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'ORDER_CHECKING'"
            type="success"
            >审核中</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'PAY_CHECK_SUCCESS'"
            type="success"
            >审核通过</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'PAY_CHECK_FAIL'"
            type="success"
            >审核失败</el-tag
          >
          <el-tag
            v-if="scope.row.checkStatus == 'REFUND_USER_CANCEL'"
            type="success"
            >退款取消</el-tag
          >
          <el-tag v-if="scope.row.checkStatus == 'FREEZE'" type="danger"
            >冻结</el-tag
          >
          <el-tag v-if="scope.row.stcheckStatusatus == 'UNFREEZE'" type="danger"
            >解冻</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="checkResultDesc"
        label="审核结果说明"
        align="center"
      ></el-table-column>

      <el-table-column
        fixed="right"
        label="其他操作"
        width="150"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            
            @click="audit_click(scope.row)"
            :disabled="scope.row.checkStatus !== 'ORDER_CHECKING'"
            >审核</el-button
          >
          <!-- <el-button type="text"  @click="remark_click(scope.row)"
            >备注</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="审核操作" :visible.sync="isDialog" width="35%" center>
      <el-form :model="form">
        <p>
          审核通过后用户本次支付的保证金将正式生效，客户端显示该用已缴纳保证金标识。
        </p>
        <el-form-item>
          <el-radio v-model="form.radio" label="PAY_CHECK_SUCCESS"
            >审核通过</el-radio
          >
          <el-radio v-model="form.radio" label="PAY_CHECK_FAIL"
            >审核驳回</el-radio
          >
        </el-form-item>
        <el-form-item v-if="form.radio == 'PAY_CHECK_FAIL'">
          <el-input
            v-model="form.remark"
            placeholder="请输入100字以内驳回原因"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="audit_submit()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="备注信息"
      :visible.sync="isDialogRemark"
      width="35%"
      center
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="remark"
            placeholder="请输入100字以内备注信息"
            type="textarea"
            maxlength="100"
            show-word-limit
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogRemark = false">取 消</el-button>
        <el-button type="primary" @click="submit_remark()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="用户保证金申请记录"
      :visible.sync="isDialogAddTo"
      width="60%"
      center
      :before-close="addTo_close"
    >
      <el-table
        :data="addToData"
        ref="multipleTable"
        tooltip-effect="dark"
        show-header
        border
        style="width: 100%"
      >
        <el-table-column
          prop="payTimes"
          label="支付次数"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payTime"
          label="支付时间"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="payAmount"
          label="支付金额"
          align="center"
        ></el-table-column>
        <el-table-column prop="checkResult" label="审核结果" align="center">
          <template scope="scope">
            <el-tag v-if="scope.row.checkResult == 'INIT'" type="success"
              >待支付</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'ORDER_CHECKING'"
              type="success"
              >审核中</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'PAY_CHECK_SUCCESS'"
              type="success"
              >审核通过</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'PAY_CHECK_FAIL'"
              type="success"
              >审核失败</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECKING'"
              type="success"
              >审核中</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECK_SUCCESS'"
              type="success"
              >审核成功</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_CHECK_FAIL'"
              type="danger"
              >审核失败</el-tag
            >
            <el-tag
              v-if="scope.row.checkResult == 'REFUND_USER_CANCEL'"
              type="danger"
              >退款取消</el-tag
            >
            <el-tag v-if="scope.row.checkResult == 'FREEZE'" type="danger"
              >冻结</el-tag
            >
            <el-tag v-if="scope.row.checkResult == 'UNFREEZE'" type="danger"
              >解冻</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <div
        class=""
        style="
          display: flex;
          justify-content: center;
          background-color: #ffffff;
        "
      >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total_addTo"
          :page-size="15"
          style="padding: 20px; background-color: #ffffff"
          @current-change="xuanzeAddTo"
          @size-change="xuanzeAddTo"
        >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addTo_close()">关 闭</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
import { downloadBlob } from '@/utils/helper'
export default {
  name: 'moneyPay',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        nickname: undefined,
        depositNo: undefined,
        createAt: null,
        createAtStart: null,
        createAtEnd: null,
        status: undefined
      },
      headers: {
        authorization: ''
      },
      form: {
        radio: 'PAY_CHECK_SUCCESS',
        remark: undefined
      },
      formLabelWidth: '120px',
      isDialog: false,
      refundRecordId: undefined,
      userId: undefined,
      isDialogRemark: false,
      remark: undefined,
      isDialogAddTo: false,
      addToData: [],
      total_addTo: 1
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete(1)
  },
  methods: {
    // 导出
    async exportForm () {
      if (this.formInline.createAt) {
        this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
        this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
      } else {
        this.formInline.createAtStart = null
        this.formInline.createAtEnd = null
      }
      const res = await this.$api.checkListExport({
        nickname: this.formInline.nickname,
        depositNo: this.formInline.depositNo,
        status: this.formInline.status,
        startTime: this.formInline.createAtStart,
        endTime: this.formInline.createAtEnd
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        await this.getOrderList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '订单' + Date.now() + '.csv')
        this.$message.success('导出成功')
        await this.getOrderList()
      }
    },
    // 查询
    async getSelete (page) {
      if (this.formInline.createAt) {
        this.formInline.createAtStart = this.formInline.createAt[0] + '.000'
        this.formInline.createAtEnd = this.formInline.createAt[1] + '.000'
      } else {
        this.formInline.createAtStart = null
        this.formInline.createAtEnd = null
      }
      const res = await this.$api.getPayList({
        pageNum: page,
        pageSize: 15,
        nickname: this.formInline.nickname,
        depositNo: this.formInline.depositNo,
        status: this.formInline.status,
        startTime: this.formInline.createAtStart,
        endTime: this.formInline.createAtEnd
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    clear () {
      this.formInline.nickname = undefined
      this.formInline.depositNo = undefined
      this.formInline.createAt = undefined
      this.formInline.status = undefined
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    audit_click (val) {
      this.isDialog = true
      this.refundRecordId = val.id
    },
    async audit_submit () {
      await this.$api.fundCheck({
        id: this.refundRecordId,
        remark: this.form.remark,
        status: this.form.radio
      })
      this.getSelete(1)
      this.isDialog = false
      this.form = {
        remark: undefined,
        status: undefined
      }
      this.$message.success('审核成功')
    },
    // 备注
    remark_click (val) {
      this.isDialogRemark = true
      this.refundRecordId = val.refundRecordId
      this.remark = val.remark
    },
    async submit_remark () {
      await this.$api.refundRemark({
        refundRecordId: this.refundRecordId,
        remark: this.remark
      })
      this.getSelete(1)
      this.isDialogRemark = false
      this.$message.success('备注修改成功')
    },
    // 申请属性
    // apply_money(){},
    // 追加记录
    async addTo_money (val) {
      if (val) {
        this.userId = val.id
      }
      this.isDialogAddTo = true
      const res = await this.$api.addTo_money({
        pageNum: this.pageNumAddTo,
        pageSize: 10,
        userId: this.userId
      })
      this.addToData = res.result.list
      this.total_addTo = res.result.totalCount
    },
    xuanzeAddTo (val) {
      this.pageNumAddTo = val
      this.addTo_money()
    },
    addTo_close () {
      this.pageNumAddTo = 1
      this.total_addTo = 1
      this.isDialogAddTo = false
      this.addToData = []
    }
  }
}
</script>
