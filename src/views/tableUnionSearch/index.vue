<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #exportMessage="scope" >
         <div class="message_div">
            <el-checkbox style="margin: 10px;" v-for="item in list" :key="item.index"  v-model="item.checked" :label="item.name" border  @change='change'></el-checkbox>
          </div>
      </template>

      <template #merge0 >
        <div class="flex_div" >
          <el-button @click="downloadTemplate('USER_INFO_EXPORT')" type="primary" size="mini">
            下载模版</el-button>
          <file-uploader v-if="!isDetail" :value.sync="formData.userInfoImportUrl" style="width:200px;margin-left:10px"
            :text="formData.type?`上传${formData.type}模版`:'上传模板'"></file-uploader>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import CommonForm from '@/components/CommonForm'
  import CommonTable from '@/components/CommonTable'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    downloadBlob
  } from '@/utils/helper'

  export default {
    name: 'casting',
    components: {
      CommonForm,
      CommonTable,
      FileUploader
    },
    data() {
      return {
        isChoose:true,
        form: {
          List: []
        },
        list: [{
            name: '手机号',
            checked: false,
            disabled: false,
            type:'phone',
          },
          {
            name: '区块链地址',
            checked: false,
            disabled: false,
            type:'conAddress',
          },
          {
            name: '用户ID',
            checked: false,
            disabled: false,
            type:'uid',
          },
          {
            name: '身份证姓名',
            checked: false,
            disabled: false,
            type:'cardName',
          },
          {
            name: '注册时间',
            checked: false,
            disabled: false,
            type:'regiestTime',
          },
          {
            name: '最近活跃时间',
            checked: false,
            disabled: false,
            type:'lastActiveTime',
          },
          {
            name: '持有藏品数目',
            checked: false,
            disabled: false,
            type:'hodeCollectionNum',
          },
          {
            name: '持有楼层区的藏品数目',
            checked: false,
            disabled: false,
            type:'hodeFloorNum',
          },
          {
            name: '当前仓位',
            checked: false,
            disabled: false,
            type:'nowPosition',
          },
          {
            name: '净买入',
            checked: false,
            disabled: false,
            type:'netPurchase',
          },
        ],
        templateUrl: '', // 盲盒内作品模板地址
        templateUrl1: '', // 盲盒内作品模板地址
        isDetail: false, // 详情
        activityNo: null, // 活动编号
        formData: {
          phone: '',
          isCreate: 1,
          userInfoImportUrl:'',
          type:'手机号'
        },
        formSchema: [{
            slot: 'exportMessage',
            label: '想要导出的信息：',
          },
          {
            label: '要导入的信息：',
            field: 'type',
            type: 'radio',
            options: [{
                label: '手机号',
                value: "手机号"
              }, {
                label: '区块链地址',
                value: "区块链地址"
              },
              {
                label: '用户ID',
                value: "用户ID"
              },
            ],
            rules: [{
              required: true,
              message: '请选择导入信息',
              trigger: 'change'
            }],
          },
          {
            slot: 'msg_info',
          },
          {
            slot: 'merge0',
            label: '',
            field: 'userInfoImportUrl',

          },
          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ],
        creationNum: 'x',
        collectionNum: 'x',
        exportInfoRuleDTO:{
          cardName:false,
          conAddress:false,
          hodeCollectionNum:false,
          hodeFloorNum:false,
          lastActiveTime:false,
          netPurchase:false,
          nowPosition:false,
          phone:false,
          regiestTime:false,
          uid:false,
        },

        loading: false
      }
    },
    mounted() {

    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async downloadTemplate(templateTag) {
        const {
          status,
          result
        } = await this.$api.userCenterDownLoadTemplate({
          templateTag
        })
        if (status.code === 0) {
          window.open(result.emailsTemplateUrl, '_blank')
          this.$message.success(status.msg)
        }
      },

      async submit() {
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          // beforeClose: (action, instance, done) => {
          // 	if (action === 'confirm') {
          // 		instance.confirmButtonLoading = true;
          // 		instance.confirmButtonText = '铸造中...';
          // 		this.sub()
          // 	} else {
          // 		done();
          // 	}
          // }
        }).then(async () => {
          this.sub()
        })
      },
      async sub() {
        // console.table(this.list)

        let chooseList=[]
        this.list.forEach((item)=>{
          if(item.checked){
            chooseList.push({[item.type]:true})
          }
        })
        console.table(chooseList)
        console.table(this.formData)

        let importType;
        if(this.formData.type == "手机号"){
          importType=0
        }else if(this.formData.type == "区块链地址"){
          importType=1
        }else if(this.formData.type == "用户ID"){
          importType=2
        }

        // console.table(chooseList)

        const combinedObject = chooseList.reduce((accumulator, current) => ({
          ...accumulator,
          ...current
        }), {});

        console.log(combinedObject);

        let resObject= JSON.stringify(combinedObject)
        const param = {
          exportInfoRuleDTOJson:resObject,
          importType,
          importUrl:this.formData.userInfoImportUrl,


        }
        console.log(param)


        const res= await this.$api.userInfoExport(param)
        if (res.type === 'application/json') {
        	// blob 转 JSON
        	const enc = new TextDecoder('utf-8')
        	res.arrayBuffer().then(buffer => {
        		const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
        		this.$message.error(data.status?.msg)
        	})
        } else {
        	downloadBlob(res, '导出信息' + Date.now() + '.csv')
        	this.$message.success('导出成功')
        	this.loading = false
        }
      },

      change(e) {
        // this.getEmail()
        console.log(e)
        this.isChoose=true
        if(!e){
          this.isChoose=false
        }
      },



    }
  }
</script>

<style lang="scss" scoped>
  .page {
    padding-top: 80px;
  }

  .flex_div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 700px;
    height: 50px;
    margin-top: 10px;
  }

  .message_div{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 600px;
  }

  .uploader {
    margin-top: 0 !important;
  }
</style>
