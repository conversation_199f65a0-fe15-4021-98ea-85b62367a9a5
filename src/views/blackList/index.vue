<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px">
      <el-form-item label="名单类型">
        <el-select v-model="formInline.subType" placeholder="名单类型">
          <el-option label="购买黑名单" value="BUY_ITEM"></el-option>
          <el-option label="购买转赠黑名单" value="GET_ITEM"></el-option>
          <el-option label="禁止提现名单" value="WITHDRAW"></el-option>
          <el-option label="提现需审名单" value="WITHDRAW_CHECK"></el-option>
          <el-option label="禁止余额支付" value="BALANCE_PAY"></el-option>
          <el-option label="作品需审名单" value="ITEM_VERIFY"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="formInline.isDeleted" placeholder="状态" clearable>
          <el-option label="不启用" :value="1"></el-option>
          <el-option label="启用" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户id">
        <el-input v-model="formInline.userId" placeholder="请输入用户id" clearable type="number"></el-input>
      </el-form-item>
      <el-form-item label="用户地址">
        <el-input v-model="formInline.contractAddress" placeholder="请输入用户地址" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="formInline.phone" placeholder="请输入手机号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button type="primary" @click="add_click()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%">
      <el-table-column fixed prop="userId" label="用户id" align="center"></el-table-column>
      <el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
      <el-table-column prop="contractAddress" label="用户地址" align="center"></el-table-column>
      <el-table-column prop="isDeleted" label="状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isDeleted == 1" type="danger">不启用</el-tag>
          <el-tag v-if="scope.row.isDeleted == 0" type="success">启用</el-tag>
          <el-button type="text" style="margin-left:10px" @click="submit_updata(scope.row, 1)"
            v-if="scope.row.isDeleted == 0">不启用</el-button>
          <el-button type="text" style="margin-left:10px" @click="submit_updata(scope.row, 0)"
            v-if="scope.row.isDeleted == 1">启用</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createAdminUserName" label="创建的管理员用户名" align="center"></el-table-column>
      <el-table-column prop="updateAdminUserName" label="修改的管理员用户名" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="updateAt" label="修改时间" align="center"></el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" center width="500px">
      <el-form :model="form">
        <el-form-item label="名单类型" :label-width="formLabelWidth" required v-if="this.title == '新增'">
          <el-select v-model="form.subType" placeholder="名单类型">
            <el-option label="购买黑名单" value="BUY_ITEM"></el-option>
            <el-option label="购买转赠黑名单" value="GET_ITEM"></el-option>
            <el-option label="禁止提现名单" value="WITHDRAW"></el-option>
            <el-option label="提现需审名单" value="WITHDRAW_CHECK"></el-option>
            <el-option label="禁止余额支付" value="BALANCE_PAY"></el-option>
            <el-option label="作品需审名单" value="ITEM_VERIFY"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户地址:" :label-width="formLabelWidth" required v-if="this.title == '新增'">
          <el-input v-model="form.contractAddress" placeholder="请输入用户地址" clearable style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="备注:" :label-width="formLabelWidth" v-if="this.title == '新增'">
          <el-input v-model="form.remark" placeholder="请输入备注" clearable style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="状态" v-if="this.title === '修改'">
          <el-select v-model="form.isDeleted" placeholder="状态" style="width: 80%">
            <el-option label="是" :value="1"></el-option>
            <el-option label="否" :value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="click_submit">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'withdrawBlack',
  data() {
    return {
      tableData: [],
      total: 1,
      formInline: {
        isDeleted: '',
        userId: '',
        contractAddress: '',
        phone: '',
        type: 'BLACK',
        subType: 'WITHDRAW_CHECK'
      },
      isDialog: false,
      formLabelWidth: '120px',
      form: {
        type: 'BLACK',
        subType: 'WITHDRAW_CHECK',
        remark: '',
        userId: '',
      },
      title: '',
      userId: ''
    }
  },
  mounted() {
    this.getList(1)
  },
  methods: {
    // 查询列表
    async getList(page) {
      const res = await this.$api.blackWhiteUser({
        pageNum: page,
        pageSize: 15,
        ...this.formInline
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze(val) {
      this.getList(val)
    },
    clear() {
      this.formInline.userId = ''
      this.formInline.isDeleted = ''
      this.formInline.contractAddress = ''
      this.formInline.phone = ''
    },
    // 点击新增
    add_click() {
      this.isDialog = true
      this.title = '新增'
    },
    // 点击修改
    audit_click(val) {
      this.isDialog = true
      this.title = '修改'
      this.userId = val.userId
      this.form.isDeleted = val.isDeleted
    },
    // 新增编辑确定
    async click_submit() {
      if (this.title === '新增') {
        delete this.form.userId
        let res = await this.$api.blackWhiteUserAdd({
          ...this.form
        })
        if (res.status.code == 0) {
          this.getList(1)
          this.isDialog = false
          this.form = {
            remark: '',
            userId: '',
            isDeleted: '',
            type: 'BLACK',
            subType: 'WITHDRAW_CHECK',
          }
          this.$message.success('新增成功')
        }
      }
    },
    async submit_updata(item, isDeleted) {
      let res = await this.$api.blackWhiteUserEdit({
        isDeleted,
        userId: item.userId,
        type: 'BLACK',
        subType: this.formInline.subType,
      })
      if (res.status.code == 0) {
        this.getList()
        this.$message.success('修改成功')
      }
    }
  }
}
</script>

<style></style>
