<!-- App.vue -->
<template>
    <div class="wrapper">
        <div class="container">
            <!-- 左侧宣传区域 -->
            <div class="promo-section">
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250408/aa3daf33b859f4e88624325309fa0c2d_1492x2300.png"
                    alt="Promo Image" class="promo-image" />
                <!-- <h1>注册即可领取最高 5,000 USDT 奖励</h1>
          <p>限时新用户福利，越多级取 5,000+ USDT</p>
          <div class="telegram-link">
            <span>加入 UPTX Telegram 社区</span>
            <p>获取最新平台动态，与用户分享你的任务内容。</p>
          </div> -->
            </div>

            <!-- 右侧登录表单 -->
            <div class="login-section">
                <span class="titles">{{ $t("register.title") }}</span>
                <div class="input-group">
                    <label>{{ $t("login.mode") }}</label>
                    <input maxlength="30" v-model="emailOrPhone" :placeholder="$t('login.plea')" />

                </div>
                <!-- <div class="input-group">
                    <label>验证码</label>
                    <input type="text" v-model="password" placeholder="" />
                </div> -->
                <div class="input-group captcha-input-wrapper">
                    <label>{{ $t("login.code") }}</label>
                    <div class="input-with-button">
                        <input maxlength="6" type="text" v-model="captcha" :placeholder="$t('login.copeplz')" />
                        <div class="send-btn" :disabled="isSending" @click="sendCaptcha">
                            {{ isSending ? `${countdown}${$t("login.retry")}` : $t("login.send") }}
                        </div>
                    </div>
                </div>

                <div class="policy-group">
                    <!-- <div class="show-box" />

                    <input type="checkbox" v-model="agreePolicy" /> -->
                    <img v-if="agreePolicy" @click="agreePolicy = !agreePolicy"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250408/f45e10997929645cf1f55a6fe7e9d28e_64x64.png" />
                    <div class="check" v-else @click="agreePolicy = !agreePolicy"></div>
                    <span>{{ $t("register.argee") }} <a
                            href="https://res.pinkwallet.com/pdf/Terms_of_Use_Pinkwallet_2025.pdf">{{
                                $t("register.term1") }}</a> {{ $t('register.and') }}
                        <a href="https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf">{{
                            $t("register.term2") }}</a></span>

                </div>
                <button class="login-btn" @click="handleLogin" :disabled="!agreePolicy">{{ $t("register.titles")
                }}</button>

                <div class="alternative-login">
                    <!-- <p>或通过以下方式登录</p> -->
                    <div class="title-container-card">
                        <span class="title">{{ $t("login.other") }}</span>
                    </div>
                    <div class="login-options">
                        <button class="google-btn">
                            <img src="https://www.google.com/favicon.ico" alt="Google" /> Google
                        </button>
                        <button class="apple-btn">
                            <img src="https://www.apple.com/favicon.ico" alt="Apple" /> Apple
                        </button>
                    </div>
                </div>
                <p class="register-link">{{ $t("register.has") }} <span @click="nav_to('login')">{{ $t("register.login")
                }} </span></p>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onUnmounted } from 'vue';
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'
const router = useRouter();
const { locale, t } = useI18n();
// 响应式状态（替代 data）
const emailOrPhone = ref('');
const password = ref('');
const captchaType = ref('image');
const agreePolicy = ref(false);
const captcha = ref('');
// 倒计时相关
const isSending = ref(false);
const countdown = ref(60);
let timer = null;

// 获取 router 实例

const sendCaptcha = () => {
    if (!emailOrPhone.value) return
    console.log(isSending.value);

    if (isSending.value) return;

    isSending.value = true;
    countdown.value = 60;

    timer = setInterval(() => {
        countdown.value -= 1;
        if (countdown.value <= 0) {
            clearInterval(timer);
            isSending.value = false;
        }
    }, 1000);

    console.log('验证码已发送');
};

// 方法（替代 methods）
const handleLogin = () => {
    if (!agreePolicy.value) {
        alert('请同意用户协议和隐私政策');
        return;
    }
    console.log('登录信息:', {
        emailOrPhone: emailOrPhone.value,
        password: password.value,
        captchaType: captchaType.value,
    });
    // 这里可以添加登录逻辑
};

const nav_to = (e) => {
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    });
};
// 组件销毁时清除定时器
onUnmounted(() => {
    if (timer) clearInterval(timer);
});
</script>

<style lang="scss" scoped>
/* 外层包装，确保页面不沾满屏幕 */
.wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 40px;

}

/* 主容器 */
.container {
    gap: 100px;
    // padding: 200px 0;
    display: flex;
    font-family: MiSans;
    align-items: center;

    /* 左侧宣传区域 */
    .promo-section {
        height: 575px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 24px;
        cursor: pointer;
        padding: 40px;
        transition: all 0.8s ease-in-out;

        &:hover {
            scale: 1.05;
        }

        .promo-image {
            width: 373px;
            height: 575px;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        p {
            font-size: 16px;
            color: #aaa;
            margin-bottom: 20px;
        }

        .telegram-link {
            text-align: left;

            span {
                font-size: 16px;
                color: #fff;
            }

            p {
                font-size: 14px;
                color: #aaa;
            }
        }
    }

    /* 右侧登录表单 */
    .login-section {
        padding: 40px;
        border-radius: 24px;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.05);
        background-color: #161616;
        color: #fff;
        border: 1px solid #252629;
        transition: all 0.8s ease-in-out;
        min-width: 630px;
        height: 575px;

        &:hover {
            scale: 1.05;
        }

        .titles {
            font-family: MiSans;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            // text-transform: capitalize;
            margin-bottom: 40px;
            color: #fff;
            display: block;
        }

        // .captcha-input-wrapper {
        //     display: flex;
        //     align-items: center;
        //     gap: 10px;

        //     input {
        //         flex: 1;
        //     }


        // }

        .input-group,
        .captcha-group {
            margin-bottom: 30px;
            text-align: left;
            position: relative;

            .send-btn {
                position: absolute;
                right: 0;
                bottom: 7px;
                // padding: 10px 20px;
                // background-color: #FF95B2;
                border: none;
                border-radius: 10px;
                color: rgba(255, 255, 255, .6);
                font-size: 14px;
                cursor: pointer;
                transition: background-color 0.3s;



                // &:disabled {
                //     // background-color: #666;
                //     color: red;
                //     cursor: not-allowed;
                // }
            }

            label {
                display: block;
                font-family: MiSans;
                font-weight: 400;
                font-size: 14px;
                line-height: 137%;
                letter-spacing: 0px;
                text-transform: capitalize;
                color: rgba(255, 255, 255, .6);
                margin-bottom: 5px;
            }

            input,
            select {
                width: 100%;
                background: #222;
                padding: 12px;

                border: none;
                border-radius: 10px;
                color: #fff;
                font-size: 14px;
                outline: none; // 去除选中状态边框
                // text-transform: capitalize;

            }
        }

        .policy-group {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            position: relative;

            .check {
                border: 2px solid #ccc;
                width: 12px;
                height: 12px;
                margin-right: 10px;
                border-radius: 50%;
            }

            img {
                width: 16px;
                height: 16px;
                margin-right: 10px;

            }

            .show-box {
                position: absolute;
                top: 4px;
                left: 4px;
                width: 16px;
                height: 16px;
                border-radius: 2px;
                /* 这里是对勾颜色，可以自定义，和勾选框背景色色差较大 */
                background: #FF95B2;
            }

            .show-box:before {
                content: '';
                position: absolute;
                top: 2px;
                left: 4px;
                width: 5px;
                height: 8px;
                border: solid white;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }

            input {
                z-index: 999;
                margin-right: 10px;

                &:checked {
                    background: FF95B2;
                }
            }

            span {
                font-size: 14px;
                color: #aaa;

                a {
                    color: #FF95B2;
                    text-decoration: none;
                }
            }
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #FF95B2;
            border: none;
            outline: none; // 去除选中状态边框
            border-radius: 12px;
            font-size: 16px;
            color: #000;
            cursor: pointer;
            font-family: HarmonyOS Sans;

            &:disabled {
                background-color: #666;
                cursor: not-allowed;
            }
        }

        .alternative-login {
            margin-top: 40px;
            text-align: center;

            .title-container-card {
                background-color: rgba(255, 255, 255, 0.2);
                height: 1px;
                display: flex;
                align-items: center;
                justify-content: center;

                .title {
                    display: block;
                    font-weight: bold;
                    background-color: #161616;
                    z-index: 9999;
                    position: relative;
                    padding: 0 32px;

                    font-family: MiSans-bold;
                    font-weight: 700;
                    font-size: 14px;

                    line-height: 100%;
                    letter-spacing: 0px;
                    text-align: center;
                    text-transform: capitalize;
                    color: #fff;
                }
            }

            p {
                font-size: 14px;
                color: #aaa;
                margin-bottom: 10px;
            }

            .login-options {
                margin-top: 30px;
                display: flex;
                justify-content: center;
                gap: 10px;

                .google-btn,
                .apple-btn {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    outline: none; // 去除选中状态边框
                    padding: 15px 20px;
                    border-radius: 9999px;
                    color: #fff;
                    font-size: 14px;
                    border: 1px solid #38393d;
                    cursor: pointer;

                    img {
                        width: 20px;
                        height: 20px;
                        margin-right: 10px;
                    }
                }
            }
        }

        .register-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #aaa;

            span {
                color: #FF95B2;
                cursor: pointer;
                text-decoration: none;
            }
        }
    }
}
</style>