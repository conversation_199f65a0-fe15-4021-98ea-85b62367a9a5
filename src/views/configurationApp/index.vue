<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline"
             style="background-color:#FFFFFF;padding:20px;">
      <el-form-item>
        <!-- <el-button type="primary" @click="openDialog(1,'')">添加配置</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
              show-header border style="width: 100%;" >
      <el-table-column fixed prop="id" label="id" align="center"></el-table-column>
      <!-- <el-table-column prop="pic" label="商品图片" align="center">
        <template scope="scope">
          <div style="width:100%;">
            <el-image style="width: 100px; height: 100px" :src="scope.row.pic"
              :preview-src-list="scope.row.srcList">
            </el-image>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column prop="type" label="类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.type=='1'" type="success">IOS</el-tag>
          <el-tag v-if="scope.row.type=='2'" type="info">白色Android(已废弃)</el-tag>
          <el-tag v-if="scope.row.type=='3'" type="success">黑色Android 衍生bit</el-tag>
					 <el-tag v-if="scope.row.type=='4'" >灵境游戏Android</el-tag>
            <el-tag v-if="scope.row.type=='7'" >暴躁龙Android</el-tag>
            <el-tag v-if="scope.row.type=='8'" >黑色Android 藏品</el-tag>
            <el-tag v-if="scope.row.type=='9'" >OWO Android</el-tag>
            <el-tag v-if="scope.row.type=='10'" >暴躁龙 IOS</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="minVersion" label="最小版本" align="center"></el-table-column>
      <el-table-column prop="currentVersion" label="最新版本" align="center"></el-table-column>

      <el-table-column prop="downloadUrl" label="下载链接" align="center"></el-table-column>
      <el-table-column prop="versionDesc" label="更新描述" align="center">
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="removeOpen(scope.row)">更改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display:flex;justify-content: center;background-color:#FFFFFF;">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
                     style="padding:20px;background-color:#FFFFFF;" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="更改配置" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="id" :label-width="formLabelWidth">
          <el-input v-model="form.id" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="类型" :label-width="formLabelWidth">
          <el-input v-model="form.type" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="最低更新版本" :label-width="formLabelWidth">
          <el-input v-model="form.minVersion" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="当前版本" :label-width="formLabelWidth">
          <el-input v-model="form.currentVersion" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="更新描述" :label-width="formLabelWidth">
          <el-input v-model="form.versionDesc"  type="textarea" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="下载链接" :label-width="formLabelWidth">
          <el-input v-model="form.downloadUrl" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>

export default {
  name: 'configurationApp',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        status: ''
      },
      headers: {
        authorization: ''
      },
      form: {
        id: '', // id
        type: '', // 类型
        minVersion: '', // 最低更新版本
        currentVersion: '', // 当前版本
        versionDesc: '', // 说明
        downloadUrl: '' // 下载链接
      },
      formLabelWidth: '120px',
      isDialog: false
    }
  },
  mounted () {
    this.getSelete(1)
  },
  methods: {
    // 查询
    async getSelete (page) {
      const res = await this.$api.getAllDTOList({
        pageNum: page,
        pageSize: 15
      })
      this.tableData = res.result
      this.total = res.result.totalCount
    },
    selete () {
      console.log(this.formInline)
    },

    // 弹窗确定
    add () {
      this.saveOrUpdate(this.form)
    },
    // 更新
    async saveOrUpdate (e) {
      console.log(e)
      await this.$api.AppsaveOrUpdate({
        id: e.id,
        type: e.type,
        minVersion: e.minVersion,
        currentVersion: e.currentVersion,
        versionDesc: e.versionDesc,
        downloadUrl: e.downloadUrl
      })
      this.$message({
        type: 'success',
        message: '更改成功!'
      })
      this.isDialog = false
      this.getSelete(1)
    },
    // 弹窗取消
    adds () {
      this.isaddDialog = false
      this.isDialog = false
      this.form = {
        name: '',
        code: '',
        desc: ''
      }
    },
    // 更改
    removeOpen (item) {
      console.log(item)
      this.form.id = item.id
      this.form.type = item.type
      this.form.minVersion = item.minVersion
      this.form.currentVersion = item.currentVersion
      this.form.versionDesc = item.versionDesc
      this.form.downloadUrl = item.downloadUrl
      this.isDialog = true
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(this.multipleSelection)
    }
  }
}
</script>
