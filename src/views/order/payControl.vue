<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="支付类型">
        <el-select  v-model="formInline.method" placeholder="支付类型">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单号">
        <el-input
          v-model="formInline.orderNum"
          placeholder="请输入订单号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="支付时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"


    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="outTradeNo"
        label="订单编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orderType"
        label="订单类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="cashFee"
        label="支付金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="totalFee"
        label="订单金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="isOk"
        label="是否成功回调"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="method"
        label="支付类型"
        align="center"
      ></el-table-column>
      <!-- <el-table-column prop="method" label="支付类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.method == '1'">是</el-tag>
          <el-tag v-if="scope.row.method == '0'">否</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="timeEnd"
        label="支付时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="resultCode"
        label="业务结果"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            
            @click="again_click(scope.row)"
            :disabled="scope.row.isOk == '回调成功'"
            >重新回调</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'payControl',
  data () {
    return {
      formInline: {
        method: '',
        orderNum: '',
        createAt: undefined,
        timeEndStart: undefined,
        timeEndEnd: undefined
      },
      options: [
        {
          value: '1',
          label: '余额'
        },
        {
          value: '2',
          label: '微信'
        },
        {
          value: '3',
          label: '支付宝'
        },
        {
          value: '4',
          label: '云闪付'
        },
        {
          value: '5',
          label: '银行卡'
        },
        {
          value: '6',
          label: '赠送'
        },
        {
          value: '7',
          label: '五虎赠送'
        },
        {
          value: '8',
          label: '流水'
        },
        {
          value: '9',
          label: '苹果'
        },
        {
          value: '10',
          label: '余额支付'
        },
        {
          value: '11',
          label: '连连的支付宝'
        },
        {
          value: '16',
          label: '空投'
        },
        {
          value: '17',
          label: '数字人民币支付'
        },
        {
          value: '20',
          label: '合成'
        }
      ],
      tableData: []
    }
  },
  mounted () {
    this.getList(1)
  },
  methods: {
    // 查询列表
    async getList (page) {
      if (this.formInline.createAt) {
        this.formInline.timeEndStart = this.formInline.createAt[0]
        this.formInline.timeEndEnd = this.formInline.createAt[1]
      } else {
        this.formInline.timeEndStart = undefined
        this.formInline.timeEndEnd = undefined
      }
      const res = await this.$api.getListNotify({
        pageNum: page,
        pageSize: 15,
        method: this.formInline.method,
        orderNum: this.formInline.orderNum,
        timeEndStart: this.formInline.timeEndStart,
        timeEndEnd: this.formInline.timeEndEnd
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    clear () {
      this.formInline.method = ''
      this.formInline.orderNum = ''
      this.formInline.createAt = ''
    },
    // 重新回调支付回调
    again_click (val) {
      this.$confirm('此操作将重新回调, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.adminNotify(val.orderNum)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async adminNotify (val) {
      let res = await this.$api.adminNotify({
        outTradeNo: val
      })
     
      if(res.status.code==0){
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
         this.getList(1)
      }
    }
  }
}
</script>

<style>
</style>
