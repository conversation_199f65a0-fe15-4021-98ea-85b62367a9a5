<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item label="订单号">
				<el-input v-model="formInline.orderNo" placeholder="请输入订单号"></el-input>
			</el-form-item>
			<el-form-item label="作品名称">
				<el-input v-model="formInline.title" placeholder="请输入作品名称"></el-input>
			</el-form-item>
			<el-form-item label="作品tid">
				<el-input v-model="formInline.tid" placeholder="请输入作品tid"></el-input>
			</el-form-item>
			<el-form-item label="买方昵称">
				<el-input v-model="formInline.buyerNickname" placeholder="请输入买方昵称"></el-input>
			</el-form-item>
			<el-form-item label="买方id">
				<el-input v-model="formInline.buyerId" placeholder="请输入买方id"></el-input>
			</el-form-item>
			<el-form-item label="卖方昵称">
				<el-input v-model="formInline.sellerNickname" placeholder="请输入卖方昵称"></el-input>
			</el-form-item>
			<el-form-item label="卖方id">
				<el-input v-model="formInline.sellerId" placeholder="请输入卖方id"></el-input>
			</el-form-item>
			<el-form-item label="商户订单号">
				<el-input v-model="formInline.outTradeNo" placeholder="请输入商户订单号"></el-input>
			</el-form-item>
			<el-form-item label="订单状态">
				<el-select  v-model="formInline.status" :disabled="statusDisabled" placeholder="订单状态">
					<el-option label="全部" :value="null"></el-option>
					<el-option label="未付款" value="0"></el-option>
					<el-option label="已付款" value="1"></el-option>
					<!-- <el-option label="已发货" value="2"></el-option>
					<el-option label="已签收" value="3"></el-option>
					<el-option label="退货申请" value="4"></el-option>
					<el-option label="退货中" value="5"></el-option>
					<el-option label="已退货" value="6"></el-option> -->
					<el-option label="用户取消交易" value="7"></el-option>
					<el-option label="交易成功" value="8"></el-option>
				  <el-option label="已退款" value="9"></el-option>
					<el-option label="交易自动取消" value="10"></el-option>
					<el-option label="管理员取消交易" value="11"></el-option>
				  <el-option label="抢单失败取消" value="12"></el-option>
					<el-option label="黑洞回收" value="14"></el-option>
					<el-option label="数币支付待确认" value="20"></el-option>
				  <el-option label="抢单中" value="21"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="订单支付场景">
				<el-select  v-model="formInline.paymentScene" placeholder="订单支付场景">
					<el-option label="全部" :value="null"></el-option>
					<el-option label="H5" value="1"></el-option>
					<el-option label="PC" value="2"></el-option>
					<el-option label="IOS" value="3"></el-option>
					<el-option label="Android" value="4"></el-option>
					<el-option label="H5的PC" value="5"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="订单支付方式付款方式">
				<el-select  v-model="formInline.payMethod" placeholder="订单状态">
					<el-option label="全部" :value="null"></el-option>
					<el-option label="余额" value="1"></el-option>
					<el-option label="微信" value="2"></el-option>
					<el-option label="支付宝" value="3"></el-option>
					<el-option label="云闪付" value="4"></el-option>
					<el-option label="银行卡支付-宝付" value="5"></el-option>
          <el-option label="银行卡支付-易宝" value="13"></el-option>
					<el-option label="赠送" value="6"></el-option>
					<el-option label="五虎赠送" value="7"></el-option>
					<el-option label="流水" value="8"></el-option>
					<el-option label="苹果" value="9"></el-option>
					<el-option label="余额支付" value="10"></el-option>
					<el-option label="连连支付" value="11"></el-option>
					<el-option label="银行卡支付-连连" value="12"></el-option>
					<el-option label="黑洞恢复" value="15"></el-option>
					<el-option label="空投" value="16"></el-option>
					<el-option label="数字人民币" value="17"></el-option>
					<el-option label="合成" value="20"></el-option>
          <el-option label="域名到期" value="21"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="订单类型">
				<el-select  v-model="formInline.mold" placeholder="审核状态">
					<el-option label="全部" :value="null"></el-option>
					<el-option label="作品" value="1"></el-option>
					<el-option label="盲盒" value="2"></el-option>
					<el-option label="拍卖" value="3"></el-option>
					<el-option label="燃料" value="4"></el-option>
					<el-option label="燃料石订单" value="5"></el-option>
					<el-option label="飞跃计划订单" value="6"></el-option>
					<el-option label="飞跃计划燃料订单" value="7"></el-option>
          <el-option label="域名订单" value="8"></el-option>
          <el-option label="域名续费订单" value="9"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="创建时间">
				<el-date-picker v-model="createAt" type="datetimerange" range-separator="至" start-placeholder="开始日期"
					end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
				</el-date-picker>
			</el-form-item>
			<el-form-item label="铸造者用户名">
				<el-input v-model="formInline.creatorUsername" placeholder="请输入铸造者用户名"></el-input>
			</el-form-item>
			<el-form-item label="铸造者昵称">
				<el-input v-model="formInline.creatorNickname" placeholder="请输入铸造者昵称"></el-input>
			</el-form-item>
      <el-form-item label="飞跃站内订单">
      	<el-select  v-model="formInline.joinLeapPlan" placeholder="飞跃站内订单">
      		<el-option label="全部" :value="null"></el-option>
      		<el-option label="是" value="1"></el-option>
      		<el-option label="否" value="0"></el-option>
      	</el-select>
      </el-form-item>
      <el-form-item label="交易市场">
      	<el-select  v-model="formInline.original" placeholder="交易市场">
      		<el-option label="全部" :value="null"></el-option>
      		<el-option label="一级市场" value="0"></el-option>
      		<el-option label="二级市场" value="1"></el-option>
      	</el-select>
      </el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getquery()">查询</el-button>
				<el-button type="primary" @click="orderexport()">清除</el-button>
				<el-button type="primary" @click="batchCsv()">导出csv</el-button>
				<!-- <el-button type="primary" @click="batchExport()">导出Excel</el-button> -->
				<el-button type="primary" @click="hint()">黑洞订单恢复</el-button>
			</el-form-item>
			<el-form-item label="输入订单ID(多个,号隔开)" style="width:500px;">
				<el-input v-model="orderStr" placeholder="请输入订单编号No" :rows="1" clearable type="textarea"
					style="width:320px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="hint(true)">输入订单编号No黑洞恢复(开发者使用)
				</el-button>
			</el-form-item>
      <el-form-item>
      	<el-button type="primary" @click="isCaiwu=true">订单导出财务专用</el-button>
      </el-form-item>
		</el-form>

		<el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
			show-header border style="width: 100%"  v-loading="loading"
			:element-loading-text="loadingText">
			<el-table-column fixed prop="id" label="id" type="selection" align="center" ></el-table-column>
			<el-table-column prop="id" label="订单id" align="center"></el-table-column>
			<el-table-column prop="orderNo" label="订单号" align="center" width="180px"></el-table-column>
			<el-table-column prop="tid" label=" 作品tid" align="center" width="310px"></el-table-column>
			<el-table-column prop="ctid" label=" 系列tid" align="center" width="290px"></el-table-column>
			<el-table-column prop="title" label="作品标题" align="center" width="280px"></el-table-column>
			<el-table-column prop="cover" label="作品封面" align="center" width="80px">
				<template scope="scope">
					<div style="width: 100%" @click="ddd(scope.row.cover)">
						<el-image style="width: 20px; height: 20px;cursor: pointer;" :src="scope.row.cover">
						</el-image>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="buyer.nickname" label="购买者昵称" align="center" width="200px"></el-table-column>
			<el-table-column prop="mold" label="订单类型" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.mold === 1" type="success">作品</el-tag>
					<el-tag v-if="scope.row.mold === 2" type="success">盲盒</el-tag>
					<el-tag v-if="scope.row.mold === 3" type="success">拍卖</el-tag>
					<el-tag v-if="scope.row.mold === 4" type="success">燃料</el-tag>
					<el-tag v-if="scope.row.mold === 5" type="success">燃料石订单</el-tag>
					<el-tag v-if="scope.row.mold === 6" type="success">飞跃计划订单</el-tag>
					<el-tag v-if="scope.row.mold === 7" type="success">飞跃计划燃料订单</el-tag>
          <el-tag v-if="scope.row.mold === 8" type="success">域名订单</el-tag>
          <el-tag v-if="scope.row.mold === 9" type="success">域名续费订单</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="payMethod" label="支付方式" align="center" width="180px">
				<template scope="scope">
					<el-tag v-if="scope.row.payMethod == '1'" type="success">余额</el-tag>
					<el-tag v-if="scope.row.payMethod == '2'" type="success">微信</el-tag>
					<el-tag v-if="scope.row.payMethod == '3'" type="success">支付宝</el-tag>
					<el-tag v-if="scope.row.payMethod == '4'" type="success">云闪付</el-tag>
					<el-tag v-if="scope.row.payMethod == '5'" type="success">银行卡支付-宝付</el-tag>
					<el-tag v-if="scope.row.payMethod == '6'" type="success">赠送</el-tag>
					<el-tag v-if="scope.row.payMethod == '7'" type="success">五虎赠送</el-tag>
					<el-tag v-if="scope.row.payMethod == '8'" type="success">流水</el-tag>
					<el-tag v-if="scope.row.payMethod == '9'" type="success">苹果</el-tag>
					<el-tag v-if="scope.row.payMethod == '10'" type="success">余额支付</el-tag>
					<el-tag v-if="scope.row.payMethod == '11'" type="success">连连的支付宝</el-tag>
					<el-tag v-if="scope.row.payMethod == '15'" type="success">黑洞恢复</el-tag>
					<el-tag v-if="scope.row.payMethod == '16'" type="success">空投</el-tag>
					<el-tag v-if="scope.row.payMethod == '17'" type="success">数币支付</el-tag>
					<el-tag v-if="scope.row.payMethod == '20'" type="success">合成</el-tag>
					<el-tag v-if="scope.row.payMethod == '12'" type="success">银行卡支付-连连支付</el-tag>
          <el-tag v-if="scope.row.payMethod == '21'" type="success">域名到期</el-tag>
          <el-tag v-if="scope.row.payMethod == '13'" type="success">银行卡支付-易宝</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="price" label="交易价格" align="center"></el-table-column>
      <el-table-column prop="joinLeapPlan" label="是否为飞跃计划" align="center" width="120px"></el-table-column>
      <el-table-column prop="systemFeeAmount" label="扣点金额" align="center"></el-table-column>
      <el-table-column prop="gasFeeAmount" label="GAS费" align="center"></el-table-column>
      <el-table-column prop="copyrightFeeAmount" label="作者拆账金额" align="center"></el-table-column>
      <el-table-column prop="sellerAmount" label="卖出者拆账金额" align="center"></el-table-column>
      <el-table-column prop="platformAmount" label="分成" align="center"></el-table-column>
      <el-table-column prop="gasAmount" label="燃料" align="center"></el-table-column>
      <el-table-column prop="leapPlanAmount" label="飞跃" align="center"></el-table-column>
      <el-table-column prop="isOriginal" label="交易市场" align="center"></el-table-column>
			<el-table-column prop="status" label="订单状态" align="center" width="120px">
				<template scope="scope">
					<el-tag v-if="scope.row.status == '0'" type="success">未付款</el-tag>
					<el-tag v-if="scope.row.status == '1'" type="success">已付款</el-tag>
					<!-- <el-tag v-if="scope.row.status == '2'" type="success">已发货</el-tag> -->
					<!-- <el-tag v-if="scope.row.status == '3'" type="success">已签收</el-tag> -->
					<!-- <el-tag v-if="scope.row.status == '4'" type="success">退货申请</el-tag> -->
					<!-- <el-tag v-if="scope.row.status == '5'" type="success">退货中</el-tag> -->
					<!-- <el-tag v-if="scope.row.status == '6'" type="success">已退货</el-tag> -->
					<el-tag v-if="scope.row.status == '7'" type="success">用户取消交易</el-tag>
					<el-tag v-if="scope.row.status == '8'" type="success">交易成功</el-tag>
					<el-tag v-if="scope.row.status == '9'" type="success">已退款</el-tag>
					<el-tag v-if="scope.row.status == '10'" type="success">交易自动取消</el-tag>
					<el-tag v-if="scope.row.status == '11'" type="success">管理员取消交易</el-tag>
					<el-tag v-if="scope.row.status == '12'" type="success">抢单失败取消</el-tag>
					<el-tag v-if="scope.row.status == '14'" type="success">黑洞回收</el-tag>
					<el-tag v-if="scope.row.status == '20'" type="success">数币支付待确认</el-tag>
					<el-tag v-if="scope.row.status == '21'" type="success">抢单中</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="paymentScene" label="订单支付场景" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.paymentScene == '1'" type="success">H5</el-tag>
					<el-tag v-if="scope.row.paymentScene == '2'" type="success">PC</el-tag>
					<el-tag v-if="scope.row.paymentScene == '3'" type="success">IOS</el-tag>
					<el-tag v-if="scope.row.paymentScene == '4'" type="success">Android</el-tag>
					<el-tag v-if="scope.row.paymentScene == '5'" type="success">H5的PC</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="outTradeNo" label="商户订单号" align="center" width="200px"></el-table-column>
			<el-table-column prop="createAt" label="订单创建时间" align="center" width="200px"></el-table-column>
			<el-table-column prop="creator.nickname" label="铸造者昵称" align="center" width="200px"></el-table-column>
			<!-- <el-table-column prop="creator.username" label="铸造者用户名" align="center" width="200px"></el-table-column> -->
			<el-table-column prop="creator.id" label="铸造者id" align="center"></el-table-column>
			<el-table-column prop="buyer.username" label="购买者用户名" align="center" width="200px"></el-table-column>
			<el-table-column prop="buyer.id" label="购买者id" align="center"></el-table-column>
			<el-table-column prop="seller.nickname" label="卖出者昵称" align="center" width="200px"></el-table-column>
			<el-table-column prop="seller.username" label="卖出者用户名" align="center" width="200px"></el-table-column>
			<el-table-column prop="seller.id" label="卖出者id" align="center"></el-table-column>
			<el-table-column prop="isReal" label="是否实物作品" align="center">
				<template v-slot="scope">
					<el-tag v-if="scope.row.isReal === 0">非实物</el-tag>
					<el-tag v-if="scope.row.isReal === 1">是实物</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="orderName" label="实物收货人" align="center"></el-table-column>
			<el-table-column prop="orderPhone" label="实物收货电话" align="center"></el-table-column>
			<el-table-column prop="orderAddress" label="实物收货地址" align="center"></el-table-column>
			<el-table-column fixed="right" label="操作" width="150" align="center">
				<template slot-scope="scope">
					<div class="action" v-if="
              scope.row.status + '' === '20' &&
              scope.row.payMethod + '' === '17'
            ">
						<el-button type="primary" @click="credited(scope.row)">
							已核查到账
						</el-button>
						<el-button type="danger" @click="closeOrder(scope.row)">
							未到账关闭订单</el-button>
					</div>
					<!-- <el-button type="text" >只能看看</el-button> -->
				</template>
			</el-table-column>
		</el-table>
		<div
			style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
			<span class="money_total">本页总计交易金额为:￥{{moneyTotal}}</span>
			<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
				:page-size="pageSize" :current-page="current_page" :page-sizes="[20, 50, 100, 200,500,1000]"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
			</el-pagination>
		</div>
		<el-dialog title="批量审核" :visible.sync="isAuthority" width="50%">
			<div style="
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
        ">
				<el-radio v-model="radio1" label="ACCOUNT_CANCEL_SUCCESS" border :change="pupop()">通过</el-radio>
				<el-radio v-model="radio1" label="ACCOUNT_CANCEL_FAIL" border :change="pupop()">拒绝</el-radio>
			</div>
			<div style="margin-top: 30px" v-if="isPupops">
				<el-input type="textarea" :rows="2" placeholder="请输入拒绝原因" v-model="textarea1">
				</el-input>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isAuthority = false">取 消</el-button>
				<el-button type="primary" @click="Submit()">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="备注信息" :visible.sync="isDelete" width="50%">
			<div style="margin-top: 30px">
				<el-input type="textarea" maxlength="1000" show-word-limit :rows="5" placeholder="请输入1000字以内备注信息"
					v-model="remark">
				</el-input>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDelete = false">取 消</el-button>
				<el-button type="primary" @click="to_remark()">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="" :visible.sync="isimgDelete" width="1000px">
			<div style="width: 100%; display: flex">
				<el-image style="width: 500px" :src="imgurl"></el-image>
			</div>
		</el-dialog>
		<el-dialog title="驳回原因" :visible.sync="isDialogReson" width="35%" center>
			<p v-if="reason == null">无</p>
			<p>{{ reason }}</p>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDialogReson = false">关 闭</el-button>
				<!-- <el-button type="primary" @click="submit_reson()">确 定</el-button> -->
			</div>
		</el-dialog>
		<el-dialog title="确认订单（您正在确认数币订单的到账情况）" :visible.sync="dialogVisible" width="30%">
			<div class="content">
				<div class="item">
					<div class="title">附件上传 ：</div>
					<el-upload :limit="1" :action="action" :headers="token" :multiple="false" :file-list="fileList"
						:on-success="handlePicSuccess" :on-change="handleIntroduceUploadHide"
						:on-remove="handleIntroduceRemove" :before-upload="beforeAvatarUpload">
						<el-button  type="primary">点击上传</el-button>
						<div slot="tip" class="el-upload__tip">
							只能上传jpg/png文件，且不超过500kb
						</div>
					</el-upload>
				</div>
				<div class="item">
					<div class="title">到账备注：</div>
					<el-input type="textarea" v-model="form.payRemark"></el-input>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit()">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="提示" :visible.sync="cancelDialogVisible" width="30%">
			<span>是否关闭订单</span>
			<span slot="footer" class="dialog-footer">
				<el-button @click="cancelDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="confirmToClose()">确 定</el-button>
			</span>
		</el-dialog>
    <el-dialog
      title="订单导出财务专用"
      :visible.sync="isCaiwu"
      width="400px"
     >
      <div>
           <el-date-picker style="width:100%"
                v-model="caiwuValue"
                type="month"
                value-format="yyyy-MM"
                placeholder="选择月">
              </el-date-picker>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isCaiwu = false">取 消</el-button>
        <el-button type="primary" @click="caiwuSubmit">确 定</el-button>
      </span>
    </el-dialog>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'

	export default {
		name: 'order',
		data() {
			return {
				fileList: [],
				action: process.env.VUE_APP_BASE_URL +
					'osscenter/adminApi/missWebSign/uploadImage',
				token: {
					AdminAuthorization: localStorage.getItem('usertoken')
				}, // 设置上传的请求头部
				form: {},
				dialogVisible: false,
				cancelDialogVisible: false,
				tableData: [],
				srcList: [],
				total: 1,
				current_page: 1,
				radio1: 'ACCOUNT_CANCEL_SUCCESS', // 1 同意  2 拒绝
				textarea: '手动录入', // 文本
				textarea1: '', // 文本
				options: [{
					value: '手动录入',
					label: '4'
				}],
				isPupop: false, // 拒绝文本控制
				isPupops: false,
				state: 0,
				formInline: {
					title: null,
					orderNo: null, // 订单号
					tid: null, // 作品tid
					buyerNickname: null, // 买方昵称
					buyerId: null, // 买方用户id
					sellerNickname: null, // 卖方昵称
					sellerId: null, // 卖方用户id
					status: null, // 订单状态0未付款 1已付款 2已发货 3已签收 4退货申请 5退货中 6已退货 7取消交易 8交易成功
					paymentScene: null, // 订单支付场景 1-H5 2-PC 3-IOS 4-Android 5-H5的PC
					payMethod: null, // 订单支付方式付款方式:1余额,2微信,3支付宝,4云闪付,5银行卡,6赠送,7五虎赠送,8流水,9苹果10余额支付 20 合成
					mold: null, // 订单类型 1-作品 4-燃料
					outTradeNo: null, // 商户订单号
					createTimeStart: null,
					createTimeEnd: null,
					creatorUsername:null,//创作者用户名
					creatorNickname:null,//创作者昵称
          joinLeapPlan:null,
          original:null
				},
				isAuthority: false,
				isDelete: false,
				idlist: [],
				deleteid: '',
				isimgDelete: false,
				imgurl: '',
				scrollTop: 0,
				goodsSynopsis: '',
				goodsDesc: '',
				remark: '', // 备注信息
				recordId: null, // id
				isDialogReson: false, // 驳回原因
				reason: '', // 驳回原因
				loading: false,
				pageSize: 20,
				loadingText: "",
				moneyTotal: 0,
				orderStr: "",
        isCaiwu:false,
        caiwuValue:'',
        statusDisabled:false
			}
		},
		computed: {
			/**
			 * 搜索栏创建时间
			 */
			createAt: {
				get() {
					const {
						createTimeStart,
						createTimeEnd
					} = this.formInline
					if (createTimeStart && createTimeEnd) {
						return [createTimeStart, createTimeEnd]
					} else {
						return []
					}
				},
				set(val) {
					if (val) {
						Object.assign(this.formInline, {
							createTimeStart: val?. [0] + '.000',
							createTimeEnd: val?. [1] + '.000'
						})
					} else {
						Object.assign(this.formInline, {
							createTimeStart: null,
							createTimeEnd: null
						})
					}
				}
			}
		},
		mounted() {
			if (this.$route.query.type) {
				if(this.$route.query.type=='buy'){
					this.formInline.buyerId = this.$route.query.uid
				}
				if(this.$route.query.type=='sale'){
					this.formInline.sellerId = this.$route.query.uid
				}
			}
      let username =   localStorage.getItem('username')
      if(username == '大大熊'){
        this.formInline.status = '14'
        this.statusDisabled = true
      }
			this.getSelete(1)
			this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
				this.scrollTop = res.target.scrollTop
				console.log(res.target.scrollTop)
			})
		},

		activated() {
			this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
		},
		methods: {
			credited(row) {
				this.form.orderNo = row.orderNo
				this.dialogVisible = true
			},
			async submit() {
				console.log(this.form)
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.rmbComplete(this.form)
				if (!code) {
					this.$message.success(msg)
					this.getquery()
					this.dialogVisible = false
				} else {
					this.$message.error(msg)
				}
			},
			async closeOrder(row) {
				this.cancelDialogVisible = true
				this.form.orderNo = row.orderNo
			},
			async confirmToClose() {
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.rmbCancel({
					orderNo: this.form.orderNo
				})
				if (!code) {
					this.getquery()
					this.cancelDialogVisible = false
					this.$message.success(msg)
				} else {
					this.$message.error(msg)
				}
			},
			// 图片上传
			handlePicSuccess(res, file) {
				this.form.payAnnexFileUrl = res.result.url
			},
			handleIntroduceUploadHide(file, fileList) {
				this.hideUpload_introduce = fileList.length >= this.limitCount
			},
			beforeAvatarUpload(file) {
				if (
					file.type !== 'image/jpeg' &&
					file.type !== 'image/png' &&
					file.type !== 'image/gif'
				) {
					this.$message.error('只能上传jpg/png/GIF格式文件')
					return false
				}
			},
			// 图片移除
			handleIntroduceRemove(file, fileList) {
				this.hideUpload_introduce = fileList.length >= this.limitCount
				this.form.icon = ''
			},
			// 大图
			ddd(e) {
				this.isimgDelete = true
				this.imgurl = e
				console.log(e)
			},
			// 查询
			getquery() {
				this.getSelete(1)
			},
			// 拒绝理由下拉框
			change() {
				console.log(this.textarea)
				if (this.textarea === '4') {
					console.log('我是手动输入')
					this.isPupops = true
				} else {
					this.isPupops = false
				}
			},
			// 导出
			async batchCsv() {
				this.loadingText = "正在导出"
				this.loading = true
				const res = await this.$api.orderlistYyCsvExport(this.formInline)
				if (res.retCode === 500) {
					this.$message.error(res.retMsg)
					this.loading = false
					this.getList()
				} else if (res.type === 'application/json') {
					// blob 转 JSON
          this.loading = false
					const enc = new TextDecoder('utf-8')
					res.arrayBuffer().then((buffer) => {
						const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
						this.$message.error(data.status?.msg)
					})
				} else {
					downloadBlob(res, '订单' + Date.now() + '.csv')
					this.$message.success('导出成功')
					this.loading = false
					this.getList()
				}
			},
			// // 导出Excel
			// // 导出结果
			// async batchExport() {
			// 	const res = await this.$api.orderlistExportExcel(this.formInline)
			// 	if (res.type === 'application/json') {
			// 		// blob 转 JSON
			// 		const enc = new TextDecoder('utf-8')
			// 		res.arrayBuffer().then(buffer => {
			// 			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
			// 			this.$message.error(data.status?.msg)
			// 		})
			// 	} else {
			// 		downloadBlob(res, '订单列表')
			// 		this.$message.success('导出成功')
			// 		this.getList()
			// 	}
			// },
			hint(type) {
				this.$confirm('黑洞订单恢复, 是否确认?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					this.recoveryOrder(type)
				}).catch(() => {

				});
			  },
			// 黑洞订单恢复
			async recoveryOrder(type) {
				if (type) {
					const res = await this.$api.unDestroy({
						orderNoStr: JSON.stringify(this.orderStr.split(","))
					})
					if (res.status.code === 0) {
						this.$message.success('恢复成功')
					} else {
						this.$message.error(res.status.msg)
					}
				} else {
					const str = []
					this.multipleSelection.forEach((item) => {
						str.push(item.orderNo)
					})
					const res = await this.$api.unDestroy({
						orderNoStr: JSON.stringify(str)
					})
					if (res.status.code === 0) {
						this.$message.success('恢复成功')
					} else {
						this.$message.error(res.status.msg)
					}
				}

			},
			// async batchExport () {
			//   this.loading = true
			//   const res = await this.$api.orderlistExportExcel(this.formInline)
			//   if (res.retCode === 500 ) {
			//     this.$message.error(res.retMsg)
			//     this.loading = false
			//     this.getList()
			//   } else if (res.type === 'application/json') {
			//     // blob 转 JSON
			//     const enc = new TextDecoder('utf-8')
			//     res.arrayBuffer().then((buffer) => {
			//       const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
			//       this.$message.error(data.status?.msg)
			//     })
			//   } else {
			//     downloadBlob(res, '订单' + Date.now() + '.xlsx')
			//     this.$message.success('导出成功')
			//     this.loading = false
			//     this.getList()
			//   }
			// },
			async getSelete(page) {
				this.loadingText = "正在加载"
				this.loading = true
				this.moneyTotal = 0
				this.current_page = page
				const res = await this.$api.orderlist({
					pageNum: page,
					pageSize: this.pageSize,
					...this.formInline
				})
				if (res.status.code === 0) {
					this.loading = false
					if (res.result == null) {
						this.$message.error('无搜索结果')
					} else {
						res.result.list.forEach((item) => {
							console.log(Number(item.price))
							this.moneyTotal = this.moneyTotal + Number(item.price)
						})
						this.tableData = res.result.list
						console.log(this.tableData)
						this.total = res.result.totalCount
					}
				} else if (res.status.code === 1002) {
					await this.$router.push({
						name: 'login'
					})
				} else {
					this.$message.error(res.status.msg)
				}
			},
			// 审核
			async check() {
				this.recordId = parseInt(this.recordId)
				if (this.textarea1 === '' || this.textarea1 == null) {
					this.textarea1 = null
				}
				const res = await this.$api.check({
					recordId: this.recordId,
					checkStatus: this.radio1,
					reason: this.textarea1 // 原因
				})
				console.log(res)
				if (res.status.code === 0) {
					this.isAuthority = false
					this.$message.success(res.status.msg)
					this.textarea1 = ''
					await this.getSelete(1)
				} else {
					this.$message.error(res.status.msg)
				}
			},
			// 驳回原因
			async rejectReason() {
				this.recordId = parseInt(this.recordId)
				const res = await this.$api.rejectReason({
					recordId: this.recordId
				})
				if (res.status.code === 0) {
					this.isDialogReson = true
					this.reason = res.result.reason
				} else {
					this.$message.error(res.status.msg)
				}
			},
			// 批量审核
			async getpeopleVerify(e) {
				console.log(e)
				e = e.toString()
				const res = await this.$api.peopleVerify({
					ids: e,
					peopleVerifyStatus: this.radio1,
					rejectExplain: this.textarea1
				})
				if (res.status.code === 0) {
					this.isAuthority = false
					this.$message.success(res.status.msg)
					this.textarea1 = ''
					await this.getSelete(1)
				} else {
					this.$message.error(res.status.msg)
				}
			},
			to_isDelete(e) {
				console.log(e)
				this.remark = ''
				this.recordId = e.id
				this.isDelete = true
			},
			to_isDialogReson(e) {
				this.recordId = e.id

				this.rejectReason()
			},
			// 备注
			async to_remark() {
				console.log()
				const res = await this.$api.remark({
					recordId: this.recordId,
					remark: this.remark // 备注内容
				})
				if (res.status.code === 0) {
					this.getSelete(1)
					this.isDelete = false
					this.$message.success('备注添加成功')
				} else {
					this.$message.error(res.status.msg)
				}
			},
			selete() {
				console.log(this.formInline)
			},
			// 分页
			xuanze(val) {
				this.getSelete(val)

				this.$refs.multipleTable.bodyWrapper.scrollTop = 0
			},
			// 分页
			xuanzeSize(val) {
				this.pageSize = val
				this.getSelete(1)
				this.$refs.multipleTable.bodyWrapper.scrollTop = 0
			},
			// 批量选择
			handleSelectionChange(val) {
				console.log(val)
				this.multipleSelection = val
			},
			// 清除
			orderexport() {
				this.formInline = {
					orderNo: null, // 订单号
					tid: null, // 作品tid
					buyerNickname: null, // 买方昵称
					buyerId: null, // 买方用户id
					sellerNickname: null, // 卖方昵称
					sellerId: null, // 卖方用户id
					status: null, // 订单状态0未付款 1已付款 2已发货 3已签收 4退货申请 5退货中 6已退货 7取消交易 8交易成功
					paymentScene: null, // 订单支付场景 1-H5 2-PC 3-IOS 4-Android 5-H5的PC
					payMethod: null, // 订单支付方式付款方式:1余额,2微信,3支付宝,4云闪付,5银行卡,6赠送,7五虎赠送,8流水,9苹果10余额支付 20 合成
					mold: null // 订单类型 1-作品 4-燃料
				}
				this.getSelete(1)
			},
			// 批量审核
			batch_audit() {
				console.log(this.multipleSelection)
				if (this.multipleSelection.length >= 1) {
					this.state = 1
					this.isAuthority = true
				} else {
					this.$message.error('请选择作品')
				}
			},
			// 拒绝原因弹出框
			pupop() {
				if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
					this.isPupops = false
					this.isPupop = false
				} else {
					this.isPupop = true
					this.isPupops = true
				}
			},
			// 跳转详情
			nav_details(item) {
				console.log(item)
				// this.$refs.multipleTable.bodyWrapper.addEventListener('scroll',(res) =>{
				// this.scrollTop = res.target.scrollTop
				// console.log(res.target.scrollTop)
				// })
				this.$router.push({
					name: 'works_details',
					query: {
						id: item.tid
					}
				})
			},
			// 单个作品终审
			to_examine(item) {
				console.log(item)
				this.state = 0
				this.idlist = []
				this.idlist.push(item.id)
				this.recordId = item.id
				this.isAuthority = true
			},
     async caiwuSubmit(){
        if(this.caiwuValue==""){
          this.$message.error("请选择月份")
        }else{
          this.isCaiwu=false
          this.loadingText = "正在导出"
          this.loading = true
          const res = await this.$api.orderlistExportCsvt({
                month:this.caiwuValue
          })
          if (res.retCode === 500) {
          	this.$message.error(res.retMsg)
          	this.loading = false
          	this.getList()
          } else if (res.type === 'application/json') {
          	// blob 转 JSON
            this.loading = false
          	const enc = new TextDecoder('utf-8')
          	res.arrayBuffer().then((buffer) => {
          		const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          		this.$message.error(data.status?.msg)
          	})
          } else {
          	downloadBlob(res, '财务订单' + Date.now() + '.csv')
          	this.$message.success('导出成功')
            this.caiwuValue=''

          	this.loading = false
          	this.getList()
          }
        }
      }
		}
	}
</script>
<style lang="scss" scoped>
	.action {
		.el-button {
			width: 130px;
			margin: 10px 0;
		}
	}

	.content {
		.item {
			display: flex;
			margin-bottom: 20px;

			.title {
				width: 100px;
			}
		}
	}

	.tag {
		margin: 0px 15px 15px 0px;
		cursor: pointer !important;
	}

	.money_total {
		position: absolute;
		left: 20px;
		font-size: 14px;
		color: #F56C6C;
	}
	::v-deep.el-table .el-table__cell {
		padding:0px !important;
	}
  ::v-deep.el-textarea textarea{
    height:60px !important;
  }
</style>
