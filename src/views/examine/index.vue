<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="作品名称">
        <el-input
          v-model="formInline.goodsName"
          placeholder="请输入作品名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="作品ID">
        <el-input
          v-model="formInline.tid"
          placeholder="请输入作品ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="作者名称">
        <el-input
          v-model="formInline.authorName"
          placeholder="请输入作者名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="作品属性">
        <el-select  v-model="formInline.realNature" placeholder="作品属性">
          <el-option label="实物作品" value="1"></el-option>
          <el-option label="非实物作品" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作品类型">
        <el-select  v-model="formInline.goodsType" placeholder="作品类型">
          <el-option label="图片" value="0"></el-option>
          <el-option label="音频" value="12"></el-option>
          <el-option label="视频" value="11"></el-option>
          <el-option label="3D静态" value="13"></el-option>
          <el-option label="3D动态" value="14"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select  v-model="formInline.verifyType" placeholder="审核状态">
          <el-option label="全部" value=""></el-option>
          <el-option label="机审通过" value="MACHINEPASS"></el-option>
          <el-option label="机审驳回" value="MACHINEREJECT"></el-option>
          <el-option label="人审通过" value="PEOPLEPASS"></el-option>
          <el-option label="人审驳回" value="PEOPLEREJECT"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否为首个作品">
        <el-select 
          v-model="formInline.authorFirstGoods"
          placeholder="是否为首个作品"
          clearable
        >
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
			<el-form-item label="是否为飞跃计划">
			  <el-select 
			    v-model="formInline.joinLeapPlan"
			    placeholder="是否为飞跃计划"
			    clearable
			  >
				<el-option label="全部" value=""></el-option>
			   <el-option label="是" value="1"></el-option>
			   <el-option label="否" value="0"></el-option>
			  </el-select>
			</el-form-item>
      <el-form-item label="铸造时间">
        <el-date-picker
          v-model="formInline.castingDate"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getquery()">查询</el-button>
        <!-- <el-button type="primary" @click="orderexport()">导出</el-button> -->
        <el-button type="primary" @click="batch_audit()">批量审核</el-button>
        <!-- <el-button type="primary" @click="reset_password()">重置密码</el-button> -->
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      v-loading="loading"
	   :element-loading-text="loadingText"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
      ></el-table-column>

      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column
        prop="tid"
        label="作品id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="goodsName"
        label="作品名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="goodsCover"
        label="作品封面图"
        align="center"
        width="150"
      >
        <template scope="scope">
          <div style="width: 100%" @click="ddd(scope.row)">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.goodsCover"
            >
            </el-image>
          </div>
          <div>
            <el-button type="text"  @click="getPredict(scope.row)">
              查找相似图片
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="goodsCover" label="创作历程" align="center">
        <template scope="scope">
          <div
            style="width: 100%"
            @click="showCreativeProcessDialog(scope.row)"
          >
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.creativeProcessList[0]"
              v-if="scope.row.creativeProcessList.length > 0"
            >
            </el-image>
            <span v-else> / </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="serialNum"
        label="版本数量"
        align="center"
      ></el-table-column>
      <el-table-column prop="realNature" label="作品属性" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.realNature == 0" type="success"
          >非实物作品
          </el-tag>
          <el-tag v-if="scope.row.realNature == 1" type="danger"
          >实物作品
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="goodsType" label="手机号" align="center"></el-table-column> -->
      <el-table-column prop="goodsType" label="作品类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.goodsType == 0" type="success">图片</el-tag>
          <el-tag v-if="scope.row.goodsType == 12" type="danger">音频</el-tag>
          <el-tag v-if="scope.row.goodsType == 11" type="success">视频</el-tag>
          <el-tag v-if="scope.row.goodsType == 13" type="danger">3D静态</el-tag>
          <el-tag v-if="scope.row.goodsType == 14" type="danger">3D动态</el-tag>
        </template>
      </el-table-column>
	  <el-table-column prop="goodsType" label="是否为飞跃计划" align="center">
	    <template scope="scope">
	      <el-tag v-if="scope.row.joinLeapPlan == 1" type="success">是</el-tag>
	      <el-tag v-if="scope.row.joinLeapPlan == 0" type="error">否</el-tag>
	    </template>
	  </el-table-column>
      <el-table-column
        prop="authorName"
        label="作者名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="price"
        label="商品价格"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="castingDate"
        label="铸造时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="verifyStatus" label="审核状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.verifyStatus === 'MACHINEPASS'" type="success"
          >机审通过
          </el-tag>
          <el-tag
            v-if="scope.row.verifyStatus === 'MACHINEREJECT'"
            type="danger"
          >机审驳回
          </el-tag>
          <el-tag v-if="scope.row.verifyStatus === 'PEOPLEPASS'" type="success"
          >人审通过
          </el-tag>
          <el-tag v-if="scope.row.verifyStatus === 'PEOPLEREJECT'" type="danger"
          >人审驳回
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="authorFirstGoods"
        label="是否为首个作品"
        align="center"
      >
        <template scope="scope">
          <el-tag v-if="scope.row.authorFirstGoods == '1'" type="success"
          >是
          </el-tag>
          <el-tag v-if="scope.row.authorFirstGoods == '0'" type="danger"
          >否
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="verifyResultExplain"
        label="审核结果说明"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="nav_details(scope.row)"
          >查看
          </el-button>
          <el-button
            v-if="
              scope.row.verifyStatus == 'MACHINEPASS' ||
              scope.row.verifyStatus == 'MACHINEREJECT'
            "
            type="text"
            
            @click="to_examine(scope.row)"
          >终审
          </el-button>
          <!-- <el-button type="text"  @click="delete_verifyTask(scope.row)">删除审核任务</el-button> -->
          <!--          <el-button type="text"  @click="reset_password(scope.row)">重置密码</el-button>
          <el-button type="text"  @click="modify_information(scope.row)">修改信息</el-button>
          <el-button type="text"  @click="open(scope.row)">权限管理</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
       background
           layout="total, sizes, prev, pager, next, jumper"
           :total="total"
           :page-size="pageSize"
           :current-page="current_page"
       :page-sizes="[20, 50, 100, 200,500,1000]"
           style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanzeSize"
      >
      </el-pagination>
    </div>
    <el-dialog title="批量审核" :visible.sync="isAuthority" width="50%">
      <div
        style="
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
        "
      >
        <el-radio v-model="radio1" label="PEOPLEPASS" border :change="pupop()"
        >通过
        </el-radio>
        <el-radio v-model="radio1" label="PEOPLEREJECT" border :change="pupop()"
        >拒绝
        </el-radio>
      </div>
      <el-select 
        v-if="isPupop"
        style="margin: 0 auto"
        v-model="textarea"
        placeholder="作品类型"
        @change="change"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.value"
          :value="item.label"
        >
        </el-option>
      </el-select>
      <div style="margin-top: 30px" v-if="isPupops">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入拒绝原因"
          v-model="textarea1"
        >
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAuthority = false">取 消</el-button>
        <el-button type="primary" @click="Submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="isDelete" width="50%">
      <div id="">是否删除当前审核任务?</div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDelete = false">取 消</el-button>
        <el-button type="primary" @click="determine()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="isimgDelete" width="1000px">
      <div style="width: 100%; display: flex">
        <el-image style="width: 500px" :src="imgurl"></el-image>
        <div style="margin-left: 20px">
          <p>作品简介：{{ goodsSynopsis }}</p>
          <p>作品描述：{{ goodsDesc }}</p>
          <p>艺术家介绍：{{ artistIntro }}</p>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="创作历程"
      :visible.sync="creativeProcessDialog"
      width="1000px"
      @closed="closedCreativeProcess"
      class="creativeProcessDialog"
    >
      <div class="tips">
        展示属性：{{ rowData['creativeProcessOpen'] ? '' : '不' }}公开展示
      </div>
      <div class="content">
        <el-image
          fit="cover"
          :src="item"
          v-for="(item, index) in rowData['creativeProcessList']"
          :key="index"
        ></el-image>
        <!--        <div style="margin-left: 20px">-->
        <!--          <p>作品简介：{{ goodsSynopsis }}</p>-->
        <!--          <p>作品描述：{{ goodsDesc }}</p>-->
        <!--        </div>-->
      </div>
    </el-dialog>
    <el-dialog title="相似图片" :visible.sync="predictDialog" width="1000px">
      <div>
        <div>原图</div>
        <div
          style="
            display: inline-block;
            width: 100px;
            height: 100px;
            margin-right: 10px;
            margin-bottom: 10px;
            margin-top: 10px;
          "
        >
          <img :src="artworkImg" alt="" style="width: 100%; height: 100%"/>
        </div>
        <div>百度图片</div>
        <div
          v-for="(item, index) in baidu_result"
          :key="index"
          style="
            display: inline-block;
            width: 200px;
            height: 100%;
            margin-right: 10px;
            margin-bottom: 10px;
          "
        >
          <el-image
            style="width: 100%; height: 100%"
            :src="item"
            :preview-src-list="[item]"
            fit="fill "
          ></el-image>
        </div>
      </div>
      <div>谷歌图片</div>
      <div
        v-for="(item, index) in google_result"
        :key="index"
        style="
          display: inline-block;
          width: 200px;
          height: 100%;
          margin-right: 10px;
          margin-bottom: 10px;
        "
      >
        <el-image
          :preview-src-list="[item]"
          style="width: 100%; height: 100%"
          :src="item"
          fit="fill "
        ></el-image>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'examine',
  data () {
    return {
      tableData: [],
      srcList: [],
      total: 1,
      current_page: 1,
      radio1: 'PEOPLEPASS', // 1 同意  2 拒绝
      textarea: '需要向客服提供原创证明', // 文本
      textarea1: '', // 文本
      options: [
        {
          value: '需要向客服提供原创证明',
          label: '需要向客服提供原创证明'
        },
        {
          value: '作品涉及色情，涉政或血腥暴力等因素',
          label: '作品涉及色情，涉政或血腥暴力等因素'
        },
        {
          value: '作品疑似搬运',
          label: '作品疑似搬运'
        },
        {
          value: '手动录入',
          label: '4'
        }
      ],
      isPupop: false, // 拒绝文本控制
      isPupops: false,
      state: 0,
      formInline: {
        verifyType: '',
        authorFirstGoods: '',
        tid: '',
        goodsName: '',
        realNature: '',
        goodsType: '',
        authorName: '',
        startCastingDate: null,
        endCastingDate: null,
        castingDate: null,
		joinLeapPlan:""
      },
      headers: {
        authorization: ''
      },
      isAuthority: false,
      isDelete: false,
      form: {
        code: '',
        desc: ''
      },
      formLabelWidth: '120px',
      tags: [],
      allTags: [],
      userId: '',
      // type: 'MACHINEPASS',
      multipleSelection: [],
      idlist: [],
      deleteid: '',
      isimgDelete: false,
      imgurl: '',
      scrollTop: 0,
      goodsSynopsis: '',
      goodsDesc: '',
      artistIntro: '',
      creativeProcessDialog: false,
      rowData: {},
      predictDialog: false,
      loading: false,
      google_result: [],
      baidu_result: [],
      artworkImg: '',
	  pageSize:20
    }
  },
  mounted () {
    this.getSelete()
    this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
      this.scrollTop = res.target.scrollTop
    })
  },

  activated () {
    console.log(this.scrollTop, '我回来了')
    this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
    console.log(this.$refs.multipleTable.bodyWrapper.scrollTop)
  },
  methods: {
    closedCreativeProcess () {
      this.rowData = {}
    },
    // 创作历程详情
    showCreativeProcessDialog (val) {
      this.creativeProcessDialog = true
      this.rowData = val
    },
    // 大图
    ddd (e) {
      this.isimgDelete = true
      this.imgurl = e.goodsCover
      this.goodsSynopsis = e.goodsSynopsis
      this.goodsDesc = e.goodsDesc
      this.artistIntro = e.artistIntro
    },
    // 查询
    getquery () {
      this.current_page = 1
      this.getSelete()
    },
    // 拒绝理由下拉框
    change () {
      console.log(this.textarea)
      if (this.textarea === '4') {
        console.log('我是手动输入')
        this.isPupops = true
      } else {
        this.isPupops = false
      }
    },

    async getSelete () {
		this.loadingText="正在加载"
		this.loading = true
      if (this.formInline.castingDate) {
        this.formInline.startCastingDate = this.formInline.castingDate[0]
        this.formInline.endCastingDate = this.formInline.castingDate[1]
      } else {
        this.formInline.startCastingDate = null
        this.formInline.endCastingDate = null
      }
      const res = await this.$api.listPageVerify({
        pageNum: this.current_page,
        pageSize: this.pageSize,
        verifyType: this.formInline.verifyType,
        authorFirstGoods: this.formInline.authorFirstGoods,
        tid: this.formInline.tid,
        goodsName: this.formInline.goodsName,
        realNature: this.formInline.realNature,
        goodsType: this.formInline.goodsType,
        authorName: this.formInline.authorName,
        startCastingDate: this.formInline.startCastingDate,
        endCastingDate: this.formInline.endCastingDate,
		joinLeapPlan:this.formInline.joinLeapPlan
      })
      if (res.status.code === 0) {
        if (res.result == null) {
          this.$message.error('无搜索结果')
        } else {
			this.loading = false
          this.tableData = res.result.list
          console.log(this.tableData)
          this.total = res.result.totalCount
        }
      } else if (res.status.code === 1002) {
		  	this.loading = false
        await this.$router.push({
          name: 'login'
        })
      } else {
		  	this.loading = false
        this.$message.error(res.status.msg)
      }
    },
    // 批量审核
    async getpeopleVerify (e) {
      console.log(e)
      e = e.toString()
      let reason = this.textarea1
      if (this.radio1 === 'PEOPLEPASS') {
        reason = ''
      }
      const res = await this.$api.peopleVerify({
        ids: e,
        peopleVerifyStatus: this.radio1,
        rejectExplain: reason
      })
      if (res.status.code === 0) {
        this.isAuthority = false
        this.$message.success(res.status.msg)
        this.textarea1 = ''
        await this.getSelete()
		
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 删除审核任务
    async deleteVerifyTask (e) {
      console.log(e)
      const res = await this.$api.deleteVerifyTask({
        id: e
      })
      if (res.status.code === 0) {
        await this.getSelete()
        this.$message.success(res.status.msg)
      } else {
        this.$message.error(res.status.msg)
      } 
    },

    selete () {
      console.log(this.formInline)
    },
    submit () {
      this.$message.success('成功')
    },
    // 分页
	xuanze (val) {
		console.log(val)
		this.current_page=val
		this.getSelete()
		this.$refs.multipleTable.bodyWrapper.scrollTop = 0
	},
    // 分页
    xuanzeSize (val) {
      this.pageSize=val
	  this.current_page=1
      this.getSelete()
      this.$refs.multipleTable.bodyWrapper.scrollTop = 0
    },
    // 删除审核任务
    delete_verifyTask (e) {
      this.isDelete = true
      this.deleteid = e.id
    },
    // 删除审核任务弹出框  确定
    determine () {
      this.deleteVerifyTask(this.deleteid)
      this.isDelete = false
    },
    // 批量选择
    handleSelectionChange (val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 导出
    orderexport () {
    },
    // 批量审核
    batch_audit () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length >= 1) {
        this.state = 1
        this.isAuthority = true
      } else {
        this.$message.error('请选择作品')
      }
    },
    // 拒绝原因弹出框
    pupop () {
      console.log(this.isPupop)
      if (this.radio1 === 'PEOPLEPASS') {
        this.isPupops = false
        this.isPupop = false
      } else {
        this.isPupop = true
        if (this.textarea === '4') {
          this.isPupops = true
        } else {
          this.isPupops = false
        }
      }
    },
    // 提交
    Submit () {
      // console.log(this.textarea)
      if (this.state === 1) {
        this.idlist = []
        this.multipleSelection.forEach((item) => {
          this.idlist.push(item.id)
        })
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete()
            }
          } else {
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      } else {
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete()
            }
          } else {
            // console.log(this.options[this.textarea].value)
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      }
    },
    // 跳转详情
    nav_details (item) {
      console.log(item)
      console.log('aaaaaaaaa', this.scrollTop)
      // this.$refs.multipleTable.bodyWrapper.addEventListener('scroll',(res) =>{
      // this.scrollTop = res.target.scrollTop
      // console.log(res.target.scrollTop)
      // })
      this.$router.push({
        name: 'works_details',
        query: {
          id: item.tid
        }
      })
    },
    // 单个作品终审
    to_examine (item) {
      this.state = 0
      this.idlist = []
      this.idlist.push(item.id)
      this.isAuthority = true
    },
    beforeAvatarUpload (file) {
    },
    // 查询相似图片
    async getPredict (val) {
      this.loading = true
      const { result } = await this.$api.getPredict({
        pic: val.goodsCover,
        token: 'a556554e98429fc8a36792054d9b546b'
      }).catch(() => {
        this.loading = false
      })
      this.predictDialog = true
      this.loading = false
      this.baidu_result = result.baidu_result
      this.google_result = result.google_result
      this.artworkImg = result.img_url
    }
  }
  // beforeRouteEnter(to, form, next) {
  //
  //    next()
  //  },
  // beforeRouteLeave(to, form, next) {

  //    next()
  //  },
}
</script>
<style lang="scss" scoped>
.tag {
  margin: 0px 15px 15px 0px;
  cursor: pointer !important;
}

.creativeProcessDialog {
  ::v-deep {
    .el-dialog__body {
      padding-top: 0;
    }
  }

  .tips {
    margin-bottom: 10px;
  }

  .content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 32%);
    grid-column-gap: 20px;
    grid-row-gap: 20px;

    .el-image {
      width: 100%;
    }
  }
}
</style>
