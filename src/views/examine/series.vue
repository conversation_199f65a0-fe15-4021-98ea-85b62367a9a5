<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户名称">
        <el-input
          v-model="formInline.userNickname"
          placeholder="请输入用户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="系列名称">
        <el-input
          v-model="formInline.seriesName"
          placeholder="请输入系列名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input
          v-model="formInline.email"
          placeholder="请输入邮箱"
        ></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select  v-model="formInline.verifyType" placeholder="审核状态">
          <el-option label="全部" value=""></el-option>
          <el-option label="机审通过" value="MACHINEPASS"></el-option>
          <el-option label="机审驳回" value="MACHINEREJECT"></el-option>
          <el-option label="人审通过" value="PEOPLEPASS"></el-option>
          <el-option label="人审驳回" value="PEOPLEREJECT"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="编辑时间">
        <el-date-picker
          v-model="formInline.castingDate"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getquery()">查询</el-button>
        <!-- <el-button type="primary" @click="orderexport()">导出</el-button> -->
        <el-button type="primary" @click="batch_audit()">批量审核</el-button>
        <!-- <el-button type="primary" @click="reset_password()">重置密码</el-button> -->
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="id"
        label="系列审核任务id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="seriesId"
        label="系列id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userNickname"
        label="用户昵称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="seriesName"
        label="系列名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="seriesCoverImage"
        label="作品封面图"
        align="center"
      >
        <template scope="scope">
          <div style="width: 100%" @click="ddd(scope.row.seriesCoverImage)">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.seriesCoverImage"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="seriesEditAt"
        label="系列编辑时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="verifyStatus" label="审核状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.verifyStatus == 'MACHINEPASS'" type="success"
            >机审通过</el-tag
          >
          <el-tag v-if="scope.row.verifyStatus == 'MACHINEREJECT'" type="danger"
            >机审驳回</el-tag
          >
          <el-tag v-if="scope.row.verifyStatus == 'PEOPLEPASS'" type="success"
            >人审通过</el-tag
          >
          <el-tag v-if="scope.row.verifyStatus == 'PEOPLEREJECT'" type="danger"
            >人审驳回</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="verifyResultExplain"
        label="审核结果说明"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="nav_details(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="
              scope.row.verifyStatus == 'MACHINEPASS' ||
              scope.row.verifyStatus == 'MACHINEREJECT'
            "
            type="text"
            
            @click="to_examine(scope.row)"
            >终审</el-button
          >
          <!-- <el-button type="text"  @click="delete_verifyTask(scope.row)">删除审核任务</el-button> -->
          <!--          <el-button type="text"  @click="reset_password(scope.row)">重置密码</el-button>
          <el-button type="text"  @click="modify_information(scope.row)">修改信息</el-button>
          <el-button type="text"  @click="open(scope.row)">权限管理</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="4"
        :current-page="current_page"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="批量审核" :visible.sync="isAuthority" width="50%">
      <div
        style="
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
        "
      >
        <el-radio v-model="radio1" label="PEOPLEPASS" border :change="pupop()"
          >通过</el-radio
        >
        <el-radio v-model="radio1" label="PEOPLEREJECT" border :change="pupop()"
          >拒绝</el-radio
        >
      </div>
      <el-select 
        v-if="isPupop"
        style="margin: 0 auto"
        v-model="textarea"
        placeholder="作品类型"
        @change="change"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.value"
          :value="item.label"
        >
        </el-option>
      </el-select>
      <div style="margin-top: 30px" v-if="isPupops">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入拒绝原因"
          v-model="textarea1"
        >
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAuthority = false">取 消</el-button>
        <el-button type="primary" @click="Submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="isDelete" width="50%">
      <div id="">是否删除当前审核任务?</div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDelete = false">取 消</el-button>
        <el-button type="primary" @click="determine()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="isimgDelete" width="50%">
      <div style="width: 100%">
        <el-image style="width: 500px" :src="imgurl"> </el-image>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'series',
  data () {
    return {
      tableData: [],
      srcList: [],
      total: 1,
      current_page: 1,
      radio1: 'PEOPLEPASS', // 1 同意  2 拒绝
      textarea: '需要向客服提供原创证明', // 文本
      textarea1: '', // 文本
      options: [
        {
          value: '需要向客服提供原创证明',
          label: '需要向客服提供原创证明'
        },
        {
          value: '作品涉及色情，涉政或血腥暴力等因素',
          label: '作品涉及色情，涉政或血腥暴力等因素'
        },
        {
          value: '作品疑似搬运',
          label: '作品疑似搬运'
        },
        {
          value: '手动录入',
          label: '4'
        }
      ],
      isPupop: false, // 拒绝文本控制
      isPupops: false,
      state: 0,
      formInline: {
        verifyType: '', // 类型
        userNickname: '', // 用户名称
        seriesName: '', // 系列名称
        email: '', // 邮箱
        startCastingDate: null,
        endCastingDate: null,
        castingDate: null
      },
      headers: {
        authorization: ''
      },
      isAuthority: false,
      isDelete: false,
      form: {
        code: '',
        desc: ''
      },
      formLabelWidth: '120px',
      tags: [],
      allTags: [],
      userId: '',
      // type: 'MACHINEPASS',
      multipleSelection: [],
      idlist: [],
      deleteid: '',
      isimgDelete: false,
      imgurl: '',
      scrollTop: 0
    }
  },
  mounted () {
    this.getSelete(1)
    this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
      this.scrollTop = res.target.scrollTop
      console.log(res.target.scrollTop)
    })
    // this.listPageEmails(1)
  },

  activated () {
    console.log(this.scrollTop, '我回来了')
    this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
    console.log(this.$refs.multipleTable.bodyWrapper.scrollTop)
  },
  methods: {
    // 大图
    ddd (e) {
      this.isimgDelete = true
      this.imgurl = e
    },
    // 查询
    getquery () {
      this.getSelete(1)
    },
    // 拒绝理由下拉框
    change () {
      console.log(this.textarea)
      if (this.textarea === '4') {
        console.log('我是手动输入')
        this.isPupops = true
      } else {
        this.isPupops = false
      }
    },

    async getSelete (page) {
      this.current_page = page
      if (this.formInline.castingDate) {
        this.formInline.startCastingDate = this.formInline.castingDate[0]
        this.formInline.endCastingDate = this.formInline.castingDate[1]
      } else {
        this.formInline.startCastingDate = null
        this.formInline.endCastingDate = null
      }
      const res = await this.$api.listPageSeriesVerify({
        pageNum: page,
        pageSize: 4,
        verifyType: this.formInline.verifyType,
        userNickname: this.formInline.userNickname,
        seriesName: this.formInline.seriesName,
        email: this.formInline.email,
        startCastingDate: this.formInline.startCastingDate,
        endCastingDate: this.formInline.endCastingDate
      })
      if (res.result == null) {
        this.$message.error('无搜索结果')
      } else {
        this.tableData = res.result.list
        console.log(this.tableData)
        this.total = res.result.totalCount
      }
    },
    // 批量审核
    async getpeopleVerify (e) {
      console.log(e)
      e = e.toString()
      const res = await this.$api.seriesPeopleVerify({
        ids: e,
        peopleVerifyStatus: this.radio1,
        rejectExplain: this.textarea1
      })
      this.isAuthority = false
      this.$message.success(res.status.msg)
      this.textarea1 = ''
      this.getSelete(1)
    },
    // 删除审核任务
    async deleteVerifyTask (e) {
      console.log(e)
      const res = await this.$api.deleteVerifyTask({
        id: e
      })
      this.getSelete(1)
      this.$message.success(res.status.msg)
    },
    // 获取邮件
    async listPageEmails (page) {
      const res = await this.$api.listPageEmails({
        pageNum: page,
        pageSize: 4
      })
      this.getSelete(1)
      this.$message.success(res.status.msg)
    },

    selete () {
      console.log(this.formInline)
    },
    submit () {
      this.$message.success('成功')
    },
    // 分页
    xuanze (val) {
      this.getSelete(val)

      this.$refs.multipleTable.bodyWrapper.scrollTop = 0
    },
    // 删除审核任务
    delete_verifyTask (e) {
      this.isDelete = true
      this.deleteid = e.id
    },
    // 删除审核任务弹出框  确定
    determine () {
      this.deleteVerifyTask(this.deleteid)
      this.isDelete = false
    },
    // 批量选择
    handleSelectionChange (val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 导出
    orderexport () {},
    // 批量审核
    batch_audit () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length >= 1) {
        this.state = 1
        this.isAuthority = true
      } else {
        this.$message.error('请选择作品')
      }
    },
    // 拒绝原因弹出框
    pupop () {
      console.log(this.isPupop)
      if (this.radio1 === 'PEOPLEPASS') {
        this.isPupops = false
        this.isPupop = false
      } else {
        this.isPupop = true
        if (this.textarea === '4') {
          this.isPupops = true
        } else {
          this.isPupops = false
        }
      }
    },
    // 提交
    Submit () {
      // console.log(this.textarea)
      if (this.state === 1) {
        this.idlist = []
        this.multipleSelection.forEach((item) => {
          this.idlist.push(item.id)
        })
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete(1)
            }
          } else {
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      } else {
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete(1)
            }
          } else {
            // console.log(this.options[this.textarea].value)
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      }
    },
    // 跳转详情
    nav_details (item) {
      this.$router.push({
        name: 'works_details',
        query: {
          id: item.seriesId,
          type: 1
        }
      })
    },
    // 单个作品终审
    to_examine (item) {
      this.state = 0
      this.idlist = []
      this.idlist.push(item.id)
      this.isAuthority = true
    },
    beforeAvatarUpload (file) {}
  }
}
</script>
<style>
.tag {
  margin: 0px 15px 15px 0px;
  cursor: pointer !important;
}
</style>
