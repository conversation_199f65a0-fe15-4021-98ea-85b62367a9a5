<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="评论信息">
        <el-input
          v-model="formInline.commentContent"
          placeholder="请输入评论信息"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="tid">
        <el-input
          v-model="formInline.tid"
          placeholder="请输入 tid"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="用户昵称">
        <el-input
          v-model="formInline.userNickName"
          placeholder="请输入用户昵称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="审核结果:">
        <el-select 
          v-model="formInline.commentVerifyStatus"
          placeholder="请选择审核结果"
          clearable
        >
          <el-option
            v-for="item in commentVerifyStatusList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评论时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button
          type="primary"
          @click="batchDel()"
          :disabled="!multipleSelection.length"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
      ></el-table-column>
      <el-table-column
        fixed
        prop="id"
        label="编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tid"
        label="tid"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userNickName"
        label="用户昵称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userContractAddress"
        label="用户地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="commentContent"
        label="评论内容"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="commentLikeNum"
        label="点赞数"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="commentDate"
        label="评论时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="commentVerifyStatus"
        label="审核结果"
        align="center"
      >
        <template scope="scope">
          <el-tag v-if="scope.row.commentVerifyStatus == '0'">等待审核</el-tag>
          <el-tag v-if="scope.row.commentVerifyStatus == '1'">审核通过</el-tag>
          <el-tag v-if="scope.row.commentVerifyStatus == '2'">审核拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            
            @click="reject_click(scope.row)"
            v-if="scope.row.commentVerifyStatus == '1'"
            >驳回</el-button
          >
          <el-button
            type="text"
            
            @click="del_click(scope.row)"
            style="color: red"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        :current-page="current_page"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'workReview',
  data () {
    return {
      formInline: {
        commentContent: '',
        userNickName: '',
        commentVerifyStatus: '',
        createAt: null,
        commentEndEditAt: null,
        commentStartEditAt: null
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '一天内',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三天',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      total: 1,
      commentVerifyStatusList: [],
      tableData: [],
      multipleSelection: [],
      idListStr: [],
      current_page: 1
    }
  },
  mounted () {
    this.getCommentVerifyStatusList()
    this.getList()
  },
  methods: {
    // 批量选择
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    // 批量删除
    batchDel () {
      this.$confirm('此操作将进行批量删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.batchDelSubmit()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async batchDelSubmit () {
      this.multipleSelection.forEach((item) => {
        this.idListStr.push(item.id)
      })
      const res = await this.$api.commentRemoveBatch({
        commentIdStr: this.idListStr.join(',')
      })
      if (res.status.code === 0) {
        this.getList(1)
        this.$message.success('批量删除成功')
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 获取审核结果
    async getCommentVerifyStatusList () {
      const res = await this.$api.getDictDataByDictType({
        dictType: 'COMMENT_VERIFY'
      })
      if (res.status.code === 0) {
        this.commentVerifyStatusList = res.result.dictDataListVOS
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    clear () {
      this.formInline.commentContent = ''
      this.formInline.userNickName = ''
      this.formInline.commentVerifyStatus = ''
      this.formInline.createAt = undefined
    },
    // 查询列表
    async getList (page) {
      if (this.formInline.createAt) {
        this.formInline.commentStartEditAt = this.formInline.createAt[0]
        this.formInline.commentEndEditAt = this.formInline.createAt[1]
      } else {
        this.formInline.commentStartEditAt = null
        this.formInline.commentEndEditAt = null
      }
      const res = await this.$api.listPageCommentVerify({
        pageNum: page,
        pageSize: 15,
        commentContent: this.formInline.commentContent,
        userNickName: this.formInline.userNickName,
        commentVerifyStatus: this.formInline.commentVerifyStatus,
        commentStartEditAt: this.formInline.commentStartEditAt,
        commentEndEditAt: this.formInline.commentEndEditAt,
        tid: this.formInline.tid
      })
      if (res.status.code === 0) {
        if (res.result == null) {
          this.tableData = []
        } else {
          this.tableData = res.result.list
          this.total = res.result.totalCount
          this.current_page = page
        }
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击驳回   commentVerify
    reject_click (val) {
      this.$confirm('评论驳回后,该用户评论将从客户端移除不再展示', '确认驳回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.commentVerify(val)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async commentVerify (val) {
      const res = await this.$api.commentVerify({
        commentId: val.id,
        commentVerifyStatus: 2
      })
      if (res.status.code === 0) {
        this.getList(1)
        this.$message({
          type: 'success',
          message: '驳回成功!'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 删除
    del_click (val) {
      this.$confirm(
        '评论删除后，该用户评论将从后台及客户端同步删除，审核中状态的评论也将进行删除，中止审核，同时无法恢复历史记录。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.remove(val.id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async remove (code) {
      const res = await this.$api.commentRemove({
        commentId: code
      })
      if (res.status.code === 0) {
        this.getList(1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    }
  }
}
</script>

<style>
</style>
