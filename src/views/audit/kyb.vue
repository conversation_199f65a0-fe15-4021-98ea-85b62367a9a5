<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showCreation="false" @onCreation="nav_task" :showExport="true" @onExport="onExport" :showRefresh="false"
      :showSubmit='true' :showReset='true'></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>
<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    getSeniorAuthList,
    getSeniorAuthExport,
  } from '@/api/pink'
  export default {
    name: 'riskControl',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm,
      FileUploader
    },
    data() {
      return {
        white: '',
        goodsBlindBoxRequestType: 'CTID',
        verifiedDialog: false,
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        tableSchema: [ // 表格架构
          // {
          //   label: '申请人地址',
          //   field: 'addresses',
          //   width: '200px'
          // },
          {
            label: '申请人id',
            field: 'applicantId',
            width: '200px'
          },
          {
            label: '申请人出生国家',
            field: 'country',
            width: '200px'
          },
          {
            label: '申请人出生国家',
            field: 'countryOfBirth',
            width: '200px'
          },
          {
            label: '申请人原始语言的名字',
            field: 'firstName',
            width: '200px'
          },
          {
            label: '申请人原语言的姓氏',
            field: 'lastName',
            width: '200px'
          },
          {
            label: '申请人原语言的中间名',
            field: 'middleName',
            width: '200px'
          },
          {
            label: '申请人原籍国',
            field: 'nationality',
            width: '200px'
          },
          {
            label: '手机',
            field: 'phone',
            width: '200px'
          },
          {
            label: '申请人出生地',
            field: 'placeOfBirth',
            width: '200px'
          },
          {
            label: '申请人出生地',
            field: 'stateOfBirth',
            width: '200px'
          },
          {
            label: '申请人纳税的国家',
            field: 'taxResidenceCountry',
            width: '200px'
          },
          {
            label: '纳税人识别号',
            field: 'tin',
            width: '200px'
          },
          {
            label: '用户uid',
            field: 'uid',
            width: '200px'
          },
          {
            label: '平台用户邮箱',
            field: 'userEmail',
            width: '200px'
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '140px',
            fixed: 'right'
          }],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '审核时间：',
            field: 'approvalTimeStart',
            field2: 'approvalTimeEnd',
          },
          {
            type: 'input',
            label: '申请人id:',
            field: 'applicantId',
          },
          {
            type: 'input',
            label: '申请人出生国家:',
            field: 'country',
          },
          {
            type: 'input',
            label: '申请人出生地:',
            field: 'countryOfBirth',
          },
          {
            type: 'input',
            label: '申请人原始语言的名字:',
            field: 'firstName',
          },
          {
            type: 'input',
            label: '申请人原语言的姓氏:',
            field: 'lastName',
          },
          {
            type: 'input',
            label: '申请人原语言的中间名:',
            field: 'middleName',
          },
          {
            type: 'input',
            label: '申请人原籍国:',
            field: 'nationality',
          },
          {
            type: 'input',
            label: '平台用户邮箱:',
            field: 'pinkWalletUseEmail',
          },
          {
            type: 'input',
            label: '用户uid:',
            field: 'uid',
          },
        ],

        query: {

        },
        loading: false,
        dialogVisible: false,
        details: false,
        whiteDetails: false,
        failMessage: '',
        passStatus: 1,
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }
      },
      nav_task() {
        this.details = false
        this.dialogVisible = true
      },
      async downloadTemplate(isSold) {
        console.log(isSold)
        let type = ''
        //一级
        if (isSold == 0) {
          type = "DUTY_ORDER_CREATE1"

        } else if (isSold == 1) {
          type = "DUTY_ORDER_CREATE2"
        }
        const {
          status,
          result
        } = await this.$api.downLoadTemplateExcel({
          type
        })
        if (status.code === 0) {
          window.open(result.fileUrl, '_blank')
          this.$message.success(status.msg)
        }
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        console.log(this.query)
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList(isInit) {
        this.loading = true
        const params = {
          ...this.query,
          ...this.page,
          approvalTimeStart: this.convertToTimestamp(this.query.approvalTimeStart),
          approvalTimeEnd: this.convertToTimestamp(this.query.approvalTimeEnd),
        }
        console.log(params)
        getSeniorAuthList(params).then(res => {
          this.loading = false
          this.tableData = res.result.data
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        }).catch(err => {
          this.loading = false
        })
      },
      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      // 导出
      async onExport() {
        this.loadingText = "正在导出";
        this.loading = true;
        const res = await this.$api.getSeniorAuthExport({
          ...this.query,
          ...this.page,
        });
        console.log(res);
        if (res.retCode === 500) {
          this.$message.error(res.retMsg);
          this.loading = false;
        } else if (res.type === "application/json") {
          // blob 转 JSON
          this.loading = false;
          const enc = new TextDecoder("utf-8");
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {};
            this.$message.error(data.status?.msg);
          });
        } else {
          downloadBlob(res, "高级认证导出" + Date.now() + ".csv");
          this.$message.success("导出成功");
          this.loading = false;
        }
      },
      navTotalProfit(row) {
        this.$router.push({
          name: 'totalProfit',
          query: {
            uid: row.uid
          }
        })
      },
      openAudit(row) {
        this.id = row.id
      },
      passAction() {
        this.$api.passAction({
          passStatus: this.passStatus,
          failMessage: this.failMessage,
          id: this.id
        }).then(res => {
          if (res.code == 0) {
            this.$message.success("操作成功")
            this.getList()
          }
        })
      },
      // 将日期时间字符串转换为时间戳（秒）
      convertToTimestamp(dateTimeStr) {
        // 创建 Date 对象
        const date = new Date(dateTimeStr);
        // 获取时间戳（毫秒）并转换为秒
        return Math.floor(date.getTime() / 1000);
      },
      // 将时间戳转换为年月日时分秒格式
      convertTimestampToDateTime(timestamp) {
        if (!timestamp) return '';
        // 创建 Date 对象（时间戳是秒，需要转换为毫秒）
        const date = new Date(timestamp * 1000);
        // 格式化年月日时分秒
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },

      // 使用示例：
      // const dateTime = this.convertTimestampToDateTime(1753113600);
      // 返回：2025-06-21 00:00:00
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }

  .flex_div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 700px;
    height: 50px;
    margin-top: 10px;
  }

  .flex {
    display: flex;
  }

  .uploader {
    margin-top: 0 !important;
  }
</style>