<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showCreation="false" @onCreation="nav_task" :showExport="true" @onExport="onExport" :showRefresh="false"
      :showSubmit='true' :showReset='true'></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #applyTime="scope">
        {{ convertTimestampToDateTime(scope.row.applyTime) }}
      </template>
      <template #approvalTime="scope">
        {{ convertTimestampToDateTime(scope.row.approvalTime) }}
      </template>
      <template #action="scope">
        <el-button type="text" @click="openAudit(scope.row)">审核</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <el-dialog title="提示" :visible.sync="verifiedDialog" width="30%">
      <el-radio v-model="passStatus" :label="1">通过</el-radio>
      <el-radio v-model="passStatus" :label="0">不通过</el-radio>
      <div style="margin-top: 20px;" v-if="passStatus == 0">
        <el-input v-model="failMessage" placeholder="请输入拒绝原因
        "></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="verifiedDialog = false">取 消</el-button>
        <el-button type="primary" @click="passAction()">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>
<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    getPrimaryAuthList,
    getPrimaryAuthExport,
    getPrimaryAuthExamine
  } from '@/api/pink'
  export default {
    name: 'riskControl',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm,
      FileUploader
    },
    data() {
      return {
        white: '',
        goodsBlindBoxRequestType: 'CTID',
        verifiedDialog: false,
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        tableSchema: [ // 表格架构

          {
            label: '申请时间',
            width: '200px',
            slot: 'applyTime',
          },
          {
            label: '审核状态',
            field: 'approvalStatus',
            width: '200px',
            type: 'tag',
            tagMap: {
              INIT: {
                label: "待审核",
                tagType: "default",
              },
              PASS: {
                label: "通过",
                tagType: "success",
              },
              NO_PASS: {
                label: "拒绝",
                tagType: "error",
              },
            },
          },
          {
            label: '审核时间',
            width: '200px',
            slot: 'approvalTime',
          },
          {
            type: 'img',
            label: '身份证反面',
            field: 'documentBackUrl',
             width: '200px'
          },
          {
            type: 'img',
            label: '身份证正面',
            field: 'documentFrontUrl',
             width: '200px'
          },
          {
            type: 'img',
            label: '身份证手持',
            field: 'documentHoldUrl',
             width: '200px'
          },
          {
            label: '证件类型',
            field: 'documentType',
          },
          {
            label: '邮箱',
            field: 'email',
            width: '200px'
          },
          {
            label: '申请id',
            field: 'id',
          },
          {
            label: '证件号',
            field: 'idNumber',
            width: '200px'
          },
          {
            label: '实名信息',
            field: 'name',
          },
          {
            label: '国籍',
            field: 'nationality',
          },
          {
            label: '拒绝原因',
            field: 'rejectReason',
            width: '200px'
          },
          {
            label: '用户id',
            field: 'uid',
            width: '200px'
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '140px',
            fixed: 'right'
          }],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '审核时间：',
            field: 'approvalTimeStart',
            field2: 'approvalTimeEnd',
          },
          {
            type: 'datetimerange',
            label: '申请时间',
            field: 'applyTimeStart',
            field2: 'applyTimeEnd',
          },
          {
            type: 'input',
            label: '申请人id:',
            field: 'id',
          },
          {
            type: 'input',
            label: '用户uid:',
            field: 'uid',
          },
          {
            type: 'input',
            label: '平台用户邮箱:',
            field: 'pinkWalletUseEmail',
          },
          {
            type: 'input',
            label: '实名信息:',
            field: 'realName',
          },
          {
            type: 'select',
            label: '审核状态:',
            field: 'approvalStatus',
            options: [{
              label: '待审核',
              value: 'INIT'
            }, {
              label: '通过',
              value: 'PASS'
            }, {
              label: '拒绝',
              value: 'NO_PASS'
            }],
          }
        ],
        formSchema: [{
          type: 'search',
          label: '系列名：',
          placeholder: '请输入系列名',
          field: 'ctid',
          rules: [{
            required: true,
            message: '请输入系列名',
            trigger: 'blur'
          }],
        },
        {
          label: '创建一级/二级订单：',
          field: 'isSold',
          type: 'radio',
          options: [{
            label: '一级',
            value: 0
          }, {
            label: '二级',
            value: 1
          },],
          rules: [{
            required: true,
            message: '请选择订单信息',
            trigger: 'change'
          }],
        },
        {
          slot: 'merge0',
          label: '表格：',
          field: 'importUrl',
        },
        {
          type: 'input',
          label: '多久内投完：',
          field: 'operateTimeSecond',
          show: {
            relationField: 'isSold',
            value: [1]
          },
          placeholder: '请输入多久内投完,单位(秒)',
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          },
          rules: [{
            required: true,
            message: '请输入多久内投完,单位(秒)',
            trigger: 'change'
          }],
        },
        {
          label: '订单时间：',
          field: 'isTiming',
          type: 'radio',
          options: [{
            label: '立即',
            value: 0
          }, {
            label: '定时',
            value: 1
          },],
          rules: [{
            required: true,
            message: '请选择订单时间',
            trigger: 'change'
          }],
        },
        {
          type: 'datetime',
          label: '执行时间：',
          field: 'startTime',
          show: {
            relationField: 'isTiming',
            value: [1]
          },
          placeholder: '请输入执行时间',
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          },
        },
        {
          label: '订单状态：',
          field: 'type',
          type: 'radio',
          options: [{
            label: '支付中',
            value: 2
          },
          {
            label: '直接空投不扣钱',
            value: 3
          }
          ],
          rules: [{
            required: true,
            message: '请选择订单状态',
            trigger: 'change'
          }],
        },
        {
          type: 'radio',
          label: '支付方式：',
          field: 'payMethod',
          options: [{
            label: '易宝钱包',
            value: 5
          }],
          rules: [{
            required: true,
          }],
          show: {
            relationField: 'type',
            value: [2]
          },
        },

        {
          type: 'action',
          exclude: ['reset']
        }
        ],
        formData: {
          isSold: 0,
          type: '',
          importUrl: '',
          dutyType: "CREATE_ORDER",
          ctid: '',
          isTiming: '',
          startTime: '',
          operateTimeSecond: '',
          payMethod: '',

        },
        query: {

        },
        loading: false,
        dialogVisible: false,
        details: false,
        whiteDetails: false,
        failMessage: '',
        passStatus: 1,
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }
      },
      nav_task() {
        this.details = false
        this.dialogVisible = true
      },
      async downloadTemplate(isSold) {
        console.log(isSold)
        let type = ''
        //一级
        if (isSold == 0) {
          type = "DUTY_ORDER_CREATE1"

        } else if (isSold == 1) {
          type = "DUTY_ORDER_CREATE2"
        }
        const {
          status,
          result
        } = await this.$api.downLoadTemplateExcel({
          type
        })
        if (status.code === 0) {
          window.open(result.fileUrl, '_blank')
          this.$message.success(status.msg)
        }
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        console.log(this.query)
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList(isInit) {
        this.loading = true
        const params = {
          ...this.query,
          ...this.page,
          approvalTimeStart: this.convertToTimestamp(this.query.approvalTimeStart),
          approvalTimeEnd: this.convertToTimestamp(this.query.approvalTimeEnd),
          applyTimeStart: this.convertToTimestamp(this.query.applyTimeStart),
          applyTimeEnd: this.convertToTimestamp(this.query.applyTimeEnd),
        }
        console.log(params)
        getPrimaryAuthList(params).then(res => {
          this.loading = false
          this.tableData = res.result.data
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        }).catch(err => {
          this.loading = false
        })
      },
      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      // 导出
      async onExport() {
        this.loadingText = "正在导出";
        this.loading = true;
        const res = await this.$api.getPrimaryAuthExport({
          ...this.query,
          ...this.page,
        });
        console.log(res);
        if (res.retCode === 500) {
          this.$message.error(res.retMsg);
          this.loading = false;
        } else if (res.type === "application/json") {
          // blob 转 JSON
          this.loading = false;
          const enc = new TextDecoder("utf-8");
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {};
            this.$message.error(data.status?.msg);
          });
        } else {
          downloadBlob(res, "初级认证导出" + Date.now() + ".csv");
          this.$message.success("导出成功");
          this.loading = false;
        }
      },
      navTotalProfit(row) {
        this.$router.push({
          name: 'totalProfit',
          query: {
            uid: row.uid
          }
        })
      },
      openAudit(row) {
        this.id = row.id
        this.verifiedDialog = true
      },
      passAction() {
        this.$api.getPrimaryAuthExamine({
          passStatus: this.passStatus,
          failMessage: this.failMessage ? this.passStatus == 0 : '',
          id: this.id
        }).then(res => {
          if (res.code == 0) {
            this.$message.success("操作成功")
            this.getList()
          }
        })
      },
      // 将日期时间字符串转换为时间戳（秒）
      convertToTimestamp(dateTimeStr) {
        // 创建 Date 对象
        const date = new Date(dateTimeStr);
        // 获取时间戳（毫秒）并转换为秒
        return Math.floor(date.getTime() / 1000);
      },
       // 将时间戳转换为年月日时分秒格式
       convertTimestampToDateTime(timestamp) {
        if (!timestamp) return '';
        // 创建 Date 对象（时间戳是秒，需要转换为毫秒）
        const date = new Date(timestamp * 1000);
        // 格式化年月日时分秒
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }

  .flex_div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 700px;
    height: 50px;
    margin-top: 10px;
  }

  .flex {
    display: flex;
  }

  .uploader {
    margin-top: 0 !important;
  }
</style>