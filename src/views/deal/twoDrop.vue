<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange" :showCreation="true" @onCreation="nav_task" :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
       <template #failNum="scope">
            <p  :style="{'color':scope.row.failNum>0?'#c72929':'#000'}">
              {{scope.row.status=='INIT'?'/':scope.row.failNum}}
              <el-button v-if="scope.row.failNum>0" style="color:#c72929" @click="nav_error(scope.row.airDropId)" type="text" size="mini">
                查看</el-button>
            </p>
       </template>
      <template #action="scope">
        <el-button @click="delDrop(scope.row.airDropId)" type="text" v-if="scope.row.status=='INIT'">删除任务</el-button>
        <el-button type="text" size="mini" v-if="scope.row.status=='INIT'" @click="seleteDrop(scope.row.airDropId)">查看任务</el-button>
         <el-button type="text" size="mini" v-if="scope.row.status=='PART_DONE'||scope.row.status=='ALL_DONE'||scope.row.status=='STOP'" @click="onExport(scope.row.airDropId)">查看结果</el-button>
         <el-button type="text" size="mini" v-if="scope.row.status=='INIT'||scope.row.status=='DOING'" @click="sotpAirDrop(scope.row.airDropId)">终止空投</el-button>
      </template>
    </common-table>
    <el-dialog title="" :visible.sync="dialogVisible" width="800px" center @close="closeDialog" destroy-on-close :close-on-click-modal="false">
      <CommonForm :schema="formSchema" :isEdit="!details" :data="formData" :submit="submit" label-width="150px" :isBack="true" @nav_back="closeDialog">
            <template #token_view>
              <div class="flex_div">
                  <el-button v-if="!details" @click="downloadTemplate('AIR_DROP_COLLECTION_TID_IMPORT')" type="text" size="mini">
                    下载模版</el-button>
                  <file-uploader v-if="!details"  :value.sync="formData.tidImportUrl"
                    style="width:200px;margin-left:10px" text="上传模版"></file-uploader>
                  <el-button v-if="details" @click="downloadExcel(formData.tidImportUrl)" type="text" size="mini">
                    下载</el-button>
              </div>
            </template>
            <template #jieshou>
              <div class="flex_div">
                <el-button  v-if="!details"  @click="downloadTemplate('AIR_DROP_COLLECTION_USER_IMPORT')" type="text" size="mini">
                  下载模版</el-button>
                <file-uploader v-if="!details"  :value.sync="formData.userImportUrl"
                  style="width:200px;margin-left:10px" text="上传模版"></file-uploader>
                  <el-button v-if="details" @click="downloadExcel(formData.userImportUrl)" type="text" size="mini">
                    下载</el-button>
              </div>
            </template>
            <template #howLong>
                <el-row justify="start">
                  <el-col :span="4">
                    <div class="grid-content bg-purple flex">
                      <el-input placeholder="" v-model="formData.operateTimeSecond"></el-input><span class="danwei">s</span>
                    </div>
                  </el-col>
                </el-row>
            </template>
            <template #remake_msg>
                <div style="color:rgb(255, 29, 29)">请说明哪位策划让投的、是投给谁的</div>
            </template>

      </CommonForm>
    </el-dialog>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    airDropProjectListCollection,
    airDropResultExportCollection,
    airDropInfo,
    airDropDelete,
    airDropProjectAddCollection
  } from '@/api/hanxin'
  export default {
    name: 'upRoll',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm,
      FileUploader
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        tableSchema: [ // 表格架构
          {
            label: '任务创建时间',
            field: 'createAt',
            width: '100px'
          },
          {
            label: '藏品名',
            field: 'csName',
            width: '120px'
          },
          {
            label: '任务状态',
            field: 'status',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              ALL_DONE: {
                label: '已完成',
                tagType: 'success'
              },
              PART_DONE: {
                label: '有失败的',
                tagType: 'danger'
              },
              STOP:{
                label: '终止',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          {
            label: '空投数量',
            field: 'goodsNum',
            width: '100px',
          },
          {
            label: '失败数量',
            width: '100px',
            slot:'failNum'
          },
          {
            label: '任务开始时间',
            field: 'startTime',
            width: '120px'
          },
          {
            label: '任务结束时间',
            field: 'endTime',
            width: '120px'
          },
          {
            label: '备注',
            field: 'airDropName',
            width: '110px'
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '200px',
          },
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'search',
            label: '藏品名',
            placeholder: '请输入藏品名',
            field: 'ctid'
          },
          {
            type: 'select',
            label: '任务状态：',
            placeholder: '',
            field: 'status',
            options: [{
                label: '准备中',
        				value: 'INIT'
              },
              {
                label: '进行中',
                value: 'DOING'
              },
              {
                label: '已完成',
                value: 'ALL_DONE'
              },
              {
                label: '有失败的',
                value: 'PART_DONE'
              },
            ],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'input',
            label: '备注',
            placeholder: '请输入备注',
            field: 'airDropName'
          },
        ],
        formSchema: [
          {
            type:'search',
            label: '藏品名：',
            field: 'ctid',
            // rules: [{ required: true, message: '请输入藏品名', trigger: 'blur' }]
          },
         {
           label: '藏品从哪来：',
           field: 'fromType',
           type: 'radio',
           options: [{ label: '非指定自取', value: 1 }, { label: '指定Token', value: 2 }]
         },
         {
           label: '',
           slot: 'token_view',
           show: {
             relationField: 'fromType',
             value: '2'
           },
         },
         {
           label: '接受者列表：',
           slot: 'jieshou',
         },
         {
           label: '开始时间：',
           field: 'isTiming',
           type: 'radio',
           options: [{ label: '立即开始', value: 2 },{ label: '定时开始', value: 1 }]
         },

         {
           type: 'datetime',
           label: '开始时间：',
           field: 'startTime',
           rules: [{ required: true, message: '请输入开始时间', trigger: 'blur' }],
           show: {
             relationField: 'isTiming',
             value: '1'
           },
         },
         {
           label: '多快投完：',
           slot: 'howLong',
           rules: [{ required: true, message: '请输入多快投完', trigger: 'blur' }],
         },
         {
           type: 'input',
           label: '备注：',
           field: 'airDropName',
           rules: [{ required: true, message: '请输入备注', trigger: 'blur' }],
         },
         {
           label:"",
           slot: 'remake_msg',
         },
         {
           type: 'action',
           exclude:['reset']
         }
        ],
        formData:{
            fromType:1,
            isTiming:2
        },
        query: {
          // dutyStatus:'DOING'
        },
        loading:false,
        dialogVisible:false,
        details:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }
      },
      nav_task(){
        this.details=false
        this.dialogVisible=true
      },
      async downloadTemplate(templateTag) {
        const {
          status,
          result
        } = await this.$api.userCenterDownLoadTemplate({
          templateTag
        })
        if (status.code === 0) {
          window.open(result.emailsTemplateUrl, '_blank')
          this.$message.success(status.msg)
        }
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        this.loading=true
        let ctid = "";
        if (this.query.ctid) {
          ctid = this.query.ctid.split("(")[1].split(")")[0]
        }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          itemTyp: 'COLLECTION',
          ctid
        }
        let dataList = []
        airDropProjectListCollection(params).then(res => {
          this.tableData = res.result.list
          this.loading=false
          this.page.totalCount = res.result.totalCount
        })
      },
      // 发表/下架
      delDrop(id) {
        this.$confirm(`确定删除该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          airDropDelete({
            airDropId: id,
           itemTyp: 'COLLECTION',
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      seleteDrop(id) {
        this.details=true
        airDropInfo({
          airDropId: id,
          itemTyp: 'COLLECTION',
        }).then((res) => {
          if (res.status.code == 0) {
             this.formData = res.result
             this.dialogVisible=true
          }
        })
      },
      nav_error(airDropId){
        this.$router.push({
            name:'twoDropError',
            query:{
                airDropId
            }
        })
      },
      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      shuaxin() {
        this.$message.success("刷新成功")
        this.getList()
      },
      async onExport(airDropId) {
      	this.loadingText = "正在导出"
      	this.loading = true
        const params = {
          itemTyp: 'COLLECTION',
          airDropId
        }
      	const res = await this.$api.airDropResultExportCollection(params)
      	if (res.retCode === 500 || res.retCode === 9999) {
      		this.$message.error(res.retMsg)
      		this.loading = false
      		this.getList()
      	} else if (res.type === 'application/json') {
      		// blob 转 JSON
          this.loading = false
      		const enc = new TextDecoder('utf-8')
      		res.arrayBuffer().then((buffer) => {
      			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      			this.$message.error(data.status?.msg)
      		})
      	} else {
      		downloadBlob(res, '空投结果' + Date.now() + '.csv')
      		this.$message.success('空投结果导出成功')
      		this.loading = false
      		this.getList()
      	}
      },
      closeDialog(){
         this.dialogVisible=false
        this.formData = {
            fromType:1,
            isTiming:1
        }
      },
      submit(){
           let ctid = "";
          if(this.formData.fromType==1){
            if (this.formData.ctid) {
              ctid = this.formData.ctid.split("(")[1].split(")")[0]
            }else{
               this.$message.error('请输入藏品名')
               return false
            }
          }
          const params = {
            ...this.formData,
            itemTyp: 'COLLECTION',
            ctid
          }
          console.log(params)
          airDropProjectAddCollection(params).then((res) => {
            if (res.status.code == 0) {
              this.getList()
              this.dialogVisible=false
              this.closeDialog()
            }
          })
      },
      async downloadExcel (res) {
        window.open(res)
        this.$message.success('下载成功')
      },
      // 执行空投
      async sotpAirDrop (airDropId) {
        this.$confirm(`确定终止任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          const { status } = await this.$api.airDropCollectionStop({
            airDropId,
            itemTyp: 'COLLECTION',
          })
          if (status.code === 0) {
            this.$message.success(status.msg)
            this.getList()
          }
        })

      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
  .flex_div{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width:700px;
    height:50px;
    margin-top:10px;
  }
  .flex{
    display: flex;
  }
</style>
