<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
     <!-- <template #csName="scope">
        <el-input placeholder="请输入系列名称/系列ID" style="width:300px" @keyup.enter.native="search" v-model="formData.csName"></el-input>
        <el-button type="primary" style="margin-left: 10px;" @click="search">搜索</el-button>
        <div class="search">
          <span>搜索结果</span>
          <div v-if="searchList!=null">
            <el-tag type="" style="margin-right: 10px;cursor: pointer;" v-for="(item,index) in searchList"
              @click="clickName(item)">{{item.name}}</el-tag>
          </div>
          <div v-else style="color:rgb(255, 29, 29)">
            没有搜索结果，请重新输入关键字后重试
          </div>
        </div>
      </template> -->
      <template #maxSellAmount="scope">
        <el-input placeholder="请输入最高卖出总价" style="width:300px" @change="changeMax" v-model="formData.maxSellAmount">
        </el-input>
        <div style="color:rgb(255, 29, 29)" v-if="isError">
          当前最高总卖出去价与预期价格相差过大，请检查
        </div>
      </template>
      <template #floorPrice="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.floorPriceMinus"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.floorPricePlus"></el-input>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #sale="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minOnSaleWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxOnSaleWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #success="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minSuccessWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxSuccessWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #error="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minErrorWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxErrorWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #update="scope">
        <el-row justify="start">
          <el-col :span="3">
            <div class="grid-content bg-purple">
              <file-uploader :value.sync="formData.itemImportUrl"></file-uploader>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="grid-content bg-purple">
              <el-button type="primary" width="100%" @click="downLoad">下载模板</el-button>
            </div>
          </el-col>
        </el-row>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    setSellAdd
  } from '@/api/mallCenter'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'taskAdd',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          ctid: '',
          csName: '',
          floorPrice: '',
          expectSellNum: '',
          startTime: '',
          minSellPrice: '',
          maxSellAmount: '',
          minOnSaleWaitingTime: 120,
          maxOnSaleWaitingTime: 600,
          minErrorWaitingTime: 15,
          maxErrorWaitingTime: 30,
          floorPricePlus: 0,
          floorPriceMinus: -1,
          minSuccessWaitingTime:120,
          maxSuccessWaitingTime: 600,
        },
        formSchema: [{
            type: 'search',
            label: '藏品名称：',
            placeholder: '请输入藏品名称',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入藏品名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '预期卖出数量：',
            placeholder: '请输入预期卖出数量',
            field: 'expectSellNum',
            rules: [{
              required: true,
              message: '请输入预期卖出数量',
              trigger: 'blur'
            }]
          },
          {
            label: '相比地板价价格：',
            placeholder: '请输入相比地板价价格',
            slot: 'floorPrice',
            rules: [{
              required: true,
              message: '请输入相比地板价价格',
              trigger: 'blur'
            }]
          },

          {
            type: 'datetimerange',
            label: '时间范围：',
            placeholder: '请选择时间范围',
            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择时间范围',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '最低卖出价：',
            placeholder: '请输入最低卖出价',
            field: 'minSellPrice',
            rules: [{
              required: true,
              message: '请输入最低卖出价',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '最高卖出总价：',
            placeholder: '请输入最高卖出总价',
            field: 'maxSellAmount',
            slot: 'maxSellAmount',
            rules: [{
              required: true,
              message: '请输入最高卖出总价',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '卖出试探时长：',
            placeholder: '请输入卖出试探时长',
            slot: 'sale',
            rules: [{
              required: true,
              message: '请输入卖出试探时长',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '成功间隔时长：',
            placeholder: '请输入成功间隔时长',
            slot: 'success',
            rules: [{
              required: true,
              message: '请输入成功间隔时长',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '失败间隔时长：',
            placeholder: '请输入失败间隔时长',
            slot: 'error',
            rules: [{
              required: true,
              message: '请输入失败间隔时长',
              trigger: 'blur'
            }]
          },
          {
            type: 'action'
          },

        ],
        searchList: [],
        isError: ''
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        // if (this.isError) {
        //   this.$message.error('当前最高总卖出去价与预期价格相差过大，请检查')
        // } else {
          let startTime, endTime,ctid
          if(this.formData.ctid){
             ctid = this.formData.ctid.split("(")[1].split(")")[0]
          }
          startTime = this.formData.startTime[0]
          endTime = this.formData.startTime[1]
          let extra=JSON.stringify(this.formData)
          const data = {
            dutyType:'SET_SELL',
            startTime,
            endTime,
            extra,
            ctid
          }
          console.log(data)
        // }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.setSellAdd(data);
          if(res.status.code==0){
            this.routerBack()
          }
        })
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        letctid
        if(this.formData.ctid){
           ctid = this.formData.ctid.split("(")[1].split(")")[0]
        }
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
