<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #maxSellAmount="scope">
        <el-input placeholder="请输入最高卖出总价" style="width:300px" @change="changeMax" v-model="formData.maxSellAmount">
        </el-input>
        <div style="color:rgb(255, 29, 29)" v-if="isError">
          当前最高总卖出去价与预期价格相差过大，请检查
        </div>
      </template>
      <template #floorPrice="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.floorPriceMinus"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.floorPricePlus"></el-input>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #error="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minTradeWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxTradeWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #sleepTime="scope">
        <div class="flex">
          <el-time-picker v-model="formData.sleepTimeStart" range-separator="至"
            value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
          </el-time-picker>
          <span style="padding:0px 20px;">至</span>
          <el-time-picker v-model="formData.sleepTimeEnd" range-separator="至"
            value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
          </el-time-picker>
        </div>
      </template>
      <template #StartGapTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minStartGapTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxStartGapTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #LockTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minLockTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxLockTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #PriceRange="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minPriceRange"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxPriceRange"></el-input>
            </div>
          </el-col>
        </el-row>
      </template>
      <!-- <template #update="scope">
        <el-row justify="start">
          <el-col :span="3">
            <div class="grid-content bg-purple">
              <file-uploader :value.sync="formData.itemImportUrl"></file-uploader>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="grid-content bg-purple">
              <el-button type="primary" width="100%" @click="downLoad">下载模板</el-button>
            </div>
          </el-col>
        </el-row>
      </template> -->
      <template #floorPrice_radio>
        <div style="color:rgb(255, 29, 29)">
                      注：交易价格、相比地板价二者填一个，两个都填默认取交易价格
                      </div>
      </template>

    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'volAdd',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          ctid: '',
          csName: '',
          floorPrice: '',
          expectTradeNum: '',
          minTradePrice:'',
          startTime: '',
          minTradeWaitingTime: 4,
          maxTradeWaitingTime: 20,
          floorPricePlus: 0,
          floorPriceMinus: -1,
          isSleep:0,
          isTiming:0,
          copyNum:0
        },
        formSchema: [{
            type: 'search',
            label: '系列名称：',
            placeholder: '请输入系列名称',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入系列名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '预期交易笔数：',
            placeholder: '请输入预期交易笔数',
            field: 'expectTradeNum',
            rules: [{
              required: true,
              message: '请输入预期交易笔数',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '设定最低价：',
            placeholder: '请输入设定最低价',
            field: 'minTradePrice',
            rules: [{
              required: true,
              message: '请输入设定最低价',
              trigger: 'blur'
            }]
          },
          {
            label: '间隔多久上架交易一单：',
            slot:'StartGapTime',
            rules: [{
              required: true,
              message: '请输入间隔多久上架交易一单',
              trigger: 'blur'
            }]
          },
          {
            label: '每次交易前锁单多久：',
            slot:'LockTime',
            rules: [{
              required: true,
              message: '请输入每次交易前锁单多久',
              trigger: 'blur'
            }]
          },
          {
            label: '交易价格：',
            slot:'PriceRange',
            rules: [{
              required: true,
              message: '请输入交易价格',
              trigger: 'blur'
            }]
          },
          {
            label: '相比地板价价格（不论0/4）：',
            placeholder: '请输入相比地板价价格',
            slot: 'floorPrice',
            rules: [{
              required: true,
              message: '请输入相比地板价价格',
              trigger: 'blur'
            }]
          },
          {
            label: '',
            slot: 'floorPrice_radio',
          },
          {
            label: '开始时间：',
            field: 'isTiming',
            type: 'radio',
            options: [{
              label: '立即开始',
              value: 0
            }, {
              label: '定时',
              value: 1
            }]
          },
          {
            type: 'datetimerange',
            label: '时间范围：',
            placeholder: '请选择时间范围',
            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择时间范围',
              trigger: 'blur'
            }],
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            show: {
              relationField: 'isTiming',
              value: [1]
            },
          },
          // {
          //   type: 'number-input',
          //   label: '间隔多久交易一单：',
          //   placeholder: '请输入间隔多久交易一单',
          //   slot: 'error',
          //   rules: [{
          //     required: true,
          //     message: '请输入间隔多久交易一单',
          //     trigger: 'blur'
          //   }]
          // },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
          {
            type: 'number-input',
            label: '任务复制几次：',
            placeholder: '请输入任务复制几次',
            field: 'copyNum',
            rules: [{
              required: true,
              message: '请输入任务复制几次',
              trigger: 'blur'
            }]
          },
          // {
          //   type: 'input',
          //   label: '上传卖货地址：',
          //   placeholder: '请输入上传卖货地址',
          //   slot: 'update',
          //   rules: [{
          //     required: true,
          //     message: '请输入上传卖货地址',
          //     trigger: 'blur'
          //   }]
          // },
          {
            type: 'action'
          },

        ],
        searchList: [],
        isError: ''
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        // if (this.isError) {
        //   this.$message.error('当前最高总卖出去价与预期价格相差过大，请检查')
        // } else {

          let ctid = this.formData.ctid
          if(ctid){
            ctid=ctid.split("(")[1].split(")")[0]
          }

          let startTime, endTime
          startTime = this.formData.startTime[0]
          endTime = this.formData.startTime[1]
          let extra=JSON.stringify(this.formData)
          const data = {
            itemImportUrl:this.formData.itemImportUrl,
            ctid,
            dutyType:'AUTO_TRADE',
            startTime,
            endTime,
            extra,
            isTiming:this.formData.isTiming,
          }
          console.log(data)
        // }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.tradeAdd(data);
          if(res.status.code==0){
            this.routerBack()
          }
        })
      },
      async search() {
        let res = await this.$api.searchPgc({
          name: this.formData.csName
        });
        if (res.status.code == 0) {
          this.searchList = res.result.list
        }
      },
      clickName(item) {
        this.formData.csName = item.name
        this.formData.ctid = item.ctid
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid: this.formData.ctid,
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
    justify-content: flex-start;

    .shuru {
      width: 500px;
      height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      padding: 40px 0px;
    }

    .yulan {
      width: 100px;
      height: 812px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 40px 0px;
    }

    .preview {
      min-height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      background-image: url('https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20230320/4933c07760529e700a4d7b085a9583a5_1520x2006.jpg');
      background-repeat: no-repeat;
      background-size: 100%;
      background-color: #dbdbdb;
      padding: 140px 30px 100px 30px;

      .title {
        font-size: 28px;
        font-weight: 600;
        line-height: 50px;
      }

      .body {
        margin-top: 40px;
        // min-height:300px;
        background-color: rgb(220, 220, 220, 0.6);
        border-radius: 4px;
        // box-shadow: 0px 0px 20px 6px rgb(255, 255, 255);
        border: 1px solid #bebebe;
        padding: 30px;
        font-size: 18px;


        .text {
          margin-bottom: 20px;
        }

        .footer {
          text-align: right;
          margin-top: 40px;
          font-size: 20px;

          >div {
            margin-bottom: 6px;
          }
        }
      }

      .code {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 40px 0px;

        img {
          width: 200px;
          height: auto;
        }
      }

      .body .text:first-child {
        font-weight: 600;
      }

    }
  }

  .el-textarea {
    textarea {
      height: 200px !important;
    }
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
