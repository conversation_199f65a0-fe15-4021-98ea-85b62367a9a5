<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_task()">新建锁单</el-button>
      </template>
      <template #action="scope">
        <el-button @click="setSellStop(scope.row.dutyId)" v-if="scope.row.dutyStatus=='DOING'||scope.row.dutyStatus=='INIT'" type="text">终止任务</el-button>
      </template>
      <template #onSalePriceRadio="scope">
        {{scope.row.minStartGapTime}}s-{{scope.row.maxStartGapTime}}s
      </template>
      <template #lockTime="scope">
        {{scope.row.minLockTime}}min-{{scope.row.maxLockTime}}min
      </template>
      <template #NextWaitingTime="scope">
        {{scope.row.minNextWaitingTime}}s-{{scope.row.maxNextWaitingTime}}s
      </template>
      <template #BuyWaitingTime="scope">
        {{scope.row.minBuyWaitingTime}}s-{{scope.row.maxBuyWaitingTime}}s
      </template>
      <template #sleepTimeStart="scope">
        <div>
           {{scope.row.sleepTimeStart?scope.row.sleepTimeStart:'-'}}
        </div>
      </template>
       <template #sleepTimeEnd="scope">
         <div>
            {{scope.row.sleepTimeEnd?scope.row.sleepTimeEnd:'-'}}
         </div>
       </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'

  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    tradeList,
    tradeStop
  } from '@/api/hanxin'
  export default {
    name: 'autoLockOrder',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [// 表格架构
          {
            label: '任务ID',
            field: 'dutyId',
            width: '100px'
          },
          {
            label: '系列名',
            field: 'csName',
            width: '200px'
          },
          {
            label: '锁子数',
            field: 'dutyNum',
            width: '120px'
          },
          {
            label: '间隔时长',
            slot: 'onSalePriceRadio',
            width: '120px'
          },
          {
            label: '间隔多久锁一单',
            slot: 'NextWaitingTime',
            width: '120px'
          },
          {
            label: '展示时长',
            slot: 'lockTime',
            width: '110px'
          },
          {
            label: '已锁次数',
            field: 'currentTradeNum',
            width: '110px'
          },
          {
            label: '休息时间',
            slot: 'sleepTimeStart',
            width: '110px',
            align:"center"
          },
          {
            label: '起床时间',
            slot: 'sleepTimeEnd',
            width: '130px',
            align:"center"
          },
          {
            label: '任务开始时间',
            field: 'startTime',
            width: '150px'
          },
          {
            label: '任务结束时间',
            field: 'endTime',
            width: '150px'
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              STOPPING: {
                label: '终止中',
                tagType: 'danger'
              },
              STOP: {
                label: '已终止',
                tagType: 'danger'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '200px',
          },
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'input',
            label: '系列名',
            placeholder: '请输入系列名',
            field: 'dutyName'
          },
          {
            type: 'select',
            label: '任务类型：',
            placeholder: '',
            field: 'dutyStatus',
            options: [{
                label: '准备中',
        				value: 'INIT'
              },
              {
                label: '进行中',
                value: 'DOING'
              },
         		   {
                label: '终止中',
                value: 'STOPPING'
              },
              {
             	  label: '已终止',
                value: 'STOP'
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '执行失败',
                value: 'FAIL'
              },
            ],
            rules: [{
              required: true,
            }]
          }
        ],
        query: {
          dutyStatus:'DOING'
        },
        loading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_task() {
        this.$router.push({
          name: 'autoLockOrderAdd',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        this.loading=true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'LOCK_TRADE',
        }
        let dataList = []
        tradeList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.lockTradeExtra,
              ctid: item.ctid,
              startTime: item.startTime,
              endTime: item.endTime,
              dutyId: item.dutyId,
              createAt: item.createAt,
              dutyStatus: item.dutyStatus,
              remark: item.remark,
            })
          })
          this.tableData = dataList
          this.tableData.forEach((item) => {
            // if(item.sleepTimeStart==null){
            //   item.sleepTimeStart="-"
            //   item.sleepTimeEnd="-"
            // }
            item = item.lockTradeExtra
            console.error(item)
            this.$forceUpdate();
          })
          this.loading=false
          console.error(this.tableData)
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      setSellStop(id) {
        this.$confirm(`确定终止该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          tradeStop({
            dutyId: id,
            dutyType: 'LOCK_TRADE'
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      openCopy(id) {
        this.$prompt('请输入你要复制的份数', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^((([1-9]\d+)|([1-9]))$)/,
          inputErrorMessage: '请输入整数',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              setTimeout(() => {
                done();
                setTimeout(() => {
                  instance.confirmButtonLoading = false;
                }, 300);
              }, 1000);
            } else {
              done();
            }
          }
        }).then(({
          value
        }) => {
          this.sellCopy(id, value)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      sellCopy(dutyId, copyNum) {
        const loading = this.$loading({
          lock: true,
          text: '复制中，请耐心等待',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        setSellCopy({
          dutyId,
          dutyType: 'SET_SELL',
          copyNum
        }).then((res) => {
          if (res.status.code == 0) {
            loading.close();
            this.$message.success(res.status.msg)
            this.getList()
          } else {
            loading.close();
          }
        })
      },
      shuaxin() {
        this.$message.success("刷新成功")
        this.getList()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
