<template>
	<d2-container class="page">
		<common-table :table-schema="tableSchema" :table-data="tableData" >
		</common-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
			</common-pagination>
		</div>
	</d2-container>
</template>

<script>
	import CommonTable from '@/components/CommonTable'
		import CommonPagination from '@/components/CommonPagination'
	export default {
		name: 'payBroadcast',
		components: {
			CommonTable,
			CommonPagination
		},
		props: {},
		data() {
			return {
				dialogVisible: false, // 弹窗
				selected: '', // 选择的活动类型
				page: {
					totalCount: 0,
					pageSize: 10
				}, // 分页数据
				querySchema: [ // 搜索组件架构
					{
						type: 'input',
						label: '藏品名：',
						placeholder: '藏品名',
						field: 'csName'
					},
					{
						type: 'select',
						label: '任务类型：',
						placeholder: '',
						field: 'status',
						options: [{
								label: '准备开始',
								value: 0
							},
							{
								label: '执行中',
								value: 1
							},
							{
								label: '已结束',
								value: 2
							},
						],
						rules: [{
							required: true,
						}]
					}
				],
				tableSchema: [ // 表格架构
					{
						label: '用户地址',
						field: 'toContractAddress',
					},
          {
          	label: '手机号',
          	field: 'toPhone',
          },
          {
          	label: 'tid',
          	field: 'tid',
          },
          {
          	label: '失败原因',
          	field: 'failReason',
          },
          {
          	label: '价格',
          	field: 'price',
          },
          {
          	label: '是否播报',
          	field: 'sendImMsg',
          	type: 'tag',
          	tagMap: {
          		0: {
          			label: '否',
          			tagType: 'info'
          		},
          		1: {
          			label: '1',
          			tagType: 'success'
          		},
          	},
          },
				],
				tableData: [{}],
				whitelistData: [],
				whitelistVisible: false,
				whitelistColumn: [{
						label: '用户地址',
						prop: 'contractAddress'
					},
					{
						label: '购买数量',
						prop: 'num'
					},
				],
				searchWhitelist: "",
				keyword:"",
				type:1,
				page: {
					totalCount: 0,
					pageSize: 10,
					pageNum: 1
				}, // 分页数据
        query:{},
        airDropId:''
			}
		},
		mounted() {
     console.log(this.$route.query.airDropId)
     this.airDropId = this.$route.query.airDropId
			this.getList()
		},
		methods: {
			// 过滤查询
			onQueryChange(data) {
				this.query = data
        this.page.pageNum=1
				this.getList()
			},
			// 分页改变
			currentChange(value) {
				this.page.pageNum = value
				this.getList()
			},
			// 获取列表
			async getList(isInit) {
				const params = {
					...this.query,
					...this.page,
					pageNum: isInit ? 1 : this.page.pageNum,
          itemType:'COLLECTION',
          airDropId:this.airDropId
				}
				const {
					status,
					result
				} = await this.$api.airDropResultListCollection(params)
				if (status.code === 0) {
					this.tableData = result.list
					this.page.totalCount = result.totalCount
					this.page.pageSize = result.pageSize
					this.page.pageCount = result.pageCount
				}
			},
      nav_add(){
        this.$router.push({
          name:'payBroadcastAdd'
        })
      },
      onRefresh(data){
        this.query = data
        this.getList()
      },
			async endTask(item){
				const res = await this.$api.imOrderConfigListEnd({
					id: item.id,
				})
        if(res.status.code==0){
          this.$message.success('终止成功');
          this.getList()
        }
			},
		}
	}
</script>

<style lang="scss" scoped>

</style>
