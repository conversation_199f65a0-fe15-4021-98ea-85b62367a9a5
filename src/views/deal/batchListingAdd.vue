<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
       <template #one_msg="scope">
         <div class="h3">第一阶段</div>
       </template>
      <template #success="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input type="number" placeholder="" v-model="formData.minPrice" ></el-input><span class="danwei"></span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input type="number" v-model="formData.maxPrice"></el-input><span class="danwei"></span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #msg>
          <div style="color:rgb(255, 29, 29)">
           提示：机票系列请勿使用批量上架功能,因打乱持有者上架所以查询作品时候排序了
          </div>
      </template>
      <template #two_view="scope">
        <div class="left_view">
          <common-form :data="formDataTwo" :schema="formSchemaTwo" label-width="300px">
            <template #two_msg="scope">
              <div class="h3">第二阶段</div>
            </template>
            <template #success="scope">
              <el-row justify="start">
                <el-col :span="2">
                  <div class="grid-content bg-purple flex">
                    <el-input type="number" placeholder="" v-model="formDataTwo.minPrice" ></el-input><span class="danwei"></span>
                  </div>
                </el-col>
                <el-col :span="1">
                  <div class="grid-content bg-purple" style="text-align:center;">-</div>
                </el-col>
                <el-col :span="2">
                  <div class="grid-content bg-purple flex">
                    <el-input type="number" v-model="formDataTwo.maxPrice"></el-input><span class="danwei"></span>
                  </div>
                </el-col>
              </el-row>
            </template>
             </common-form>
        </div>
      </template>
      <template #autoSuodan="scope">
        <div class="div_left">
        <common-form :data="autoSuodanFormData" :schema="autoSuodanFormSchema" label-width="200px">
          <template #autoSuodanStartGapTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #autoSuodanLockTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minLockTime"></el-input><span class="danwei">min</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxLockTime"></el-input><span class="danwei">min</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #autoSuodanNextWaitingTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minNextWaitingTime"></el-input><span
                    class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxNextWaitingTime"></el-input><span
                    class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #sleepTime="scope">
            <div class="flex">
                <el-time-picker
                    v-model="autoSuodanFormData.sleepTimeStart"
                    range-separator="至"
                    value-format="HH:mm:ss"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
                  </el-time-picker>
                 <span style="padding:0px 20px;">至</span>
                  <el-time-picker
                      v-model="autoSuodanFormData.sleepTimeEnd"
                      range-separator="至"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
            </div>
          </template>
        </common-form>
        </div>
      </template>
      <template #autoPay="scope">
         <div class="div_left">
        <common-form :data="autoPayFormData" :schema="autoPayFormSchema" label-width="200px">
          <template #autoPayTradeWaitingTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoPayFormData.minStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoPayFormData.maxStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #sleepTime="scope">
            <div class="flex">
                <el-time-picker
                    v-model="autoPayFormData.sleepTimeStart"
                    range-separator="至"
                    value-format="HH:mm:ss"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
                  </el-time-picker>
                 <span style="padding:0px 20px;">至</span>
                  <el-time-picker
                      v-model="autoPayFormData.sleepTimeEnd"
                      range-separator="至"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
            </div>
          </template>
          <template #autoPayMsg>
                        <div style="color:rgb(255, 29, 29)">
                         注：每次交易前默认锁单多久系统默认展示4分钟
                        </div>
                    </template>
        </common-form>
         </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'batchListingAdd',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          ctid: '',
          dutyName: '',
          startTimeData:'',
          priceSortType:'1',
          goodsNum:"",
          isTwo:0,
          isAddLock:0,
          isAddAutoTrade:0
        },
        formSchema: [
          {
              type: 'search',
              label: '系列名称：',
              placeholder: '请输入藏品名称',
              field: 'ctid',
              rules: [{
                required: true,
                message: '请输入藏品名称',
                trigger: 'blur'
              }]
            },
          {
            slot:"one_msg",
            label: '',
          },
          {
            type: 'datetimerange',
            label: '时间范围：',
            placeholder: '请选择时间范围',
            field: 'startTimeData',
            rules: [{
              required: true,
              message: '请选择时间范围',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '寄售价格范围：',
            placeholder: '请输入寄售价格范围',
            slot: 'success',
            rules: [{
              required: true,
              message: '请输入寄售价格范围',
              trigger: 'blur'
            }]
          },
          {
            label: '排序：',
            field: 'priceSortType',
            type: 'radio',
            options: [{
              label: '价格从高往低',
              value: "1"
            }, {
              label: '价格从低往高',
              value: "2"
            }],
            rules: [{
              required: true,
              message: '请选择排序',
              trigger: 'blur'
            }]
          },

          {
            type: 'input',
            label: '挂售数量：',
            placeholder: '请输入挂售数量',
            field: 'goodsNum',
            rules: [{
              required: true,
              message: '请输入挂售数量',
              trigger: 'blur'
            }]
          },
          {
            label: '是否需要启用第二阶段：',
            field: 'isTwo',
            type: 'radio',
            options: [{
              label: '启用',
              value: 1
            }, {
              label: '不启用',
              value: 0
            }],
            rules: [{
              required: true,
              message: '请选择排序',
              trigger: 'blur'
            }]
          },
          {
            slot:"two_view",
            label: '',
            show: {
              relationField: 'isTwo',
              value: [1]
            },
          },
          {
            label: '是否配合锁单：',
            field: 'isAddLock',
            type: 'radio',
            options: [{
              label: '是(展开)',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            label: '',
            slot: 'autoSuodan',
            show: {
              relationField: 'isAddLock',
              value: [1]
            },
          },
          {
            label: '是否配合自动交易：',
            field: 'isAddAutoTrade',
            type: 'radio',
            options: [{
              label: '是(展开)',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            label: '',
            slot: 'autoPay',
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            slot:"msg",
            label: '',
          },
          {
            type: 'action'
          },
        ],
        searchList: [],
        isError: '',
        formSchemaTwo: [
           {
             slot:"two_msg",
             label: '',
           },
           // {
           //     type: 'search',
           //     label: '系列名称：',
           //     placeholder: '请输入藏品名称',
           //     field: 'ctid',
           //     rules: [{
           //       required: true,
           //       message: '请输入藏品名称',
           //       trigger: 'blur'
           //     }]
           //   },
          {
            type: 'datetimerange',
            label: '时间范围：',
            placeholder: '请选择时间范围',
            field: 'startTimeData',
            rules: [{
              required: true,
              message: '请选择时间范围',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '寄售价格范围：',
            placeholder: '请输入寄售价格范围',
            slot: 'success',
            rules: [{
              required: true,
              message: '请输入寄售价格范围',
              trigger: 'blur'
            }]
          },
          {
            label: '排序：',
            field: 'priceSortType',
            type: 'radio',
            options: [{
              label: '价格从高往低',
              value: "1"
            }, {
              label: '价格从低往高',
              value: "2"
            }],
            rules: [{
              required: true,
              message: '请选择排序',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '挂售数量：',
            placeholder: '请输入挂售数量',
            field: 'goodsNum',
            rules: [{
              required: true,
              message: '请输入挂售数量',
              trigger: 'blur'
            }]
          },
          {
            slot:"msg",
            label: '',
          },
        ],
        formDataTwo: {
          ctid: '',
          dutyName: '',
          startTimeData:'',
          priceSortType:'1',
          goodsNum:"",
          isTwo:0
        },
        autoSuodanFormSchema: [{
            type: 'number-input',
            label: '锁几单：',
            placeholder: '请输入锁几单',
            field: 'dutyNum',
            rules: [{
              required: true,
              message: '请输入锁几单',
              trigger: 'blur'
            }]
          },
          {
            label: '出发间隔：',
            placeholder: '请输入出发间隔',
            slot: 'autoSuodanStartGapTime',
            rules: [{
              required: true,
              message: '请输入出发间隔',
              trigger: 'blur'
            }]
          },
          {
            label: '每锁一次锁多久：',
            placeholder: '请输入每锁一次锁多久',
            slot: 'autoSuodanLockTime',
            rules: [{
              required: true,
              message: '请输入每锁一次锁多久',
              trigger: 'blur'
            }]
          },
          {
            label: '锁单间隔多久：',
            placeholder: '请输入锁单间隔多久',
            slot: 'autoSuodanNextWaitingTime',
            rules: [{
              required: true,
              message: '请输入锁单间隔多久',
              trigger: 'blur'
            }]
          },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
        ],
        autoSuodanFormData:{
           isSleep:0
        },
        autoPayFormSchema: [
          {
            label: '间隔多久上架交易一单：',
            placeholder: '请输入间隔多久上架交易一单',
            slot: 'autoPayTradeWaitingTime',
            rules: [{
              required: true,
              message: '请输入间隔多久上架交易一单',
              trigger: 'blur'
            }]
          },
          {
            label: '',
            slot: 'autoPayMsg',
          },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
        ],
        autoPayFormData:{
          isSleep:0
        }
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        if(this.formData.minPrice<=0){
             this.$message.error('寄售价格范围不得小于1')
        }else if(this.formData.goodsNum<=0){
             this.$message.error('挂售数量必须大于0')
        }else{
          let startTime, endTime,ctid
            if(this.formData.ctid){
               ctid = this.formData.ctid.split("(")[1].split(")")[0]
            }
            this.formData.startTime = this.formData.startTimeData[0]
            this.formData.endTime = this.formData.startTimeData[1]
            this.formDataTwo.startTime = this.formDataTwo.startTimeData[0]
            this.formDataTwo.endTime = this.formDataTwo.startTimeData[1]
            let lockTradeExtra = this.autoSuodanFormData
            let autoTradeExtra = this.autoPayFormData
            let req={
              extra1:this.formData,
              extra2:this.formDataTwo,
              lockTradeExtra,
              autoTradeExtra,
              isAddLock:this.formData.isAddLock,
              isAdd2:this.formData.isTwo,
              isAddAutoTrade:this.formData.isAddAutoTrade
            }
            console.error(req)
            let extra=JSON.stringify({
              extra1:this.formData,
              extra2:this.formDataTwo,
              lockTradeExtra,
              autoTradeExtra,
              isAddLock:this.formData.isAddLock,
              isAdd2:this.formData.isTwo,
              isAddAutoTrade:this.formData.isAddAutoTrade
            })
            // let extra=JSON.stringify({
            //   extra1:this.formData,
            //   extra2:this.formDataTwo,
            //   ...lockTradeExtra,
            //   ...autoTradeExtra
            // })
            const data = {
              dutyType:'ON_SALE',
              extra,
              ctid
            }
            console.log(data)
          this.$confirm('是否确认提交保存？', '确认提交保存', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            let res = await this.$api.tradeAdd(data);
            if(res.status.code==0){
              this.routerBack()
            }
          })
        }

      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
  .h3{
    font-size:22px;
    margin-left:-120px;
    font-weight:600;
    font-family:"黑体";
  }
  .left_view{
     margin-left:-300px;
  }
</style>
