<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange" :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_add()">创建批量上架</el-button>
      </template>
      <template #jishouPrice="scope">
        {{scope.row.minPrice}}-{{scope.row.maxPrice}}
      </template>
      <template #action="scope">
      	<el-popconfirm style="margin-left: 10px;" v-if="scope.row.dutyStatus=='DOING'||scope.row.dutyStatus=='INIT'" title="确定要终止吗？" @confirm="tradeStop(scope.row.dutyId)">
      		<el-button slot="reference" type="text">终止任务</el-button>
      	</el-popconfirm>
        <el-button type="text" @click="onExport(scope.row.csName,scope.row.dutyId)">导出明细</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    tradeList,
    tradeStop,
  } from '@/api/hanxin'
  export default {
    name: 'deal',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'dutyId',
            width:80
          },
          {
            label: '创建时间',
            field: 'createAt',
          },
          {
            label: '系列名',
            field: 'csName',
          },
          {
            label: '系列id',
            field: 'ctid',
            width:280
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              STOPPING: {
                label: '终止中',
                tagType: 'danger'
              },
              STOP: {
                label: '已终止',
                tagType: 'danger'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          {
            label: '开始时间',
            field: 'startTime',
          },
          {
            label: '结束时间',
            field: 'endTime',
          },
          {
            label: '排序方式',
            field: 'priceSortType',
            type: 'tag',
            tagMap: {
              "1": {
                label: '价格从高往低',
                tagType: 'info'
              },
              "2": {
                label: '价格从低往高',
                tagType: 'info'
              },
            },
          },
          {
            label: '寄售价格范围',
            slot:'jishouPrice'
          },
          {
            label: '上架数量',
            field: 'goodsNum',
          },
          {
            label: '备注',
            field: 'remark',
            showOverflowTooltip:true,
             width: '140px',
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '240px',
            fixed: 'right'
          },
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
        	{
        		type: 'search',
        		label: '系列名/系列ID：',
        		placeholder: '请输入系列名/系列ID：',
        		field: 'ctid'
        	},
        	{
        		type: 'select',
        		label: '任务状态：',
        		placeholder: '',
        		field: 'dutyStatus',
        		options: [{
        				label: '准备中',
        				value: 'INIT'
        			},
        			{
        				label: '进行中',
        				value: 'DOING'
        			},
        			{
        				label: '终止中',
        				value: 'STOPPING'
        			},
              {
              	label: '已终止',
              	value: 'STOP'
              },
              {
              	label: '已完成',
              	value: 'DONE'
              },
              {
              	label: '执行失败',
              	value: 'FAIL'
              },
        		],
        		rules: [{
        			required: true,
        		}]
        	}
        ],
        loading:false,
        query:{
           dutyStatus:'DOING'
        }
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_add() {
        this.$router.push({
          name: 'batchListingAdd',
        })
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      onRefresh(data){
        this.query = data
        this.getList()
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        let ctid="";
        if(this.query.ctid){
          ctid=this.query.ctid.split("(")[1].split(")")[0]
        }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'ON_SALE',
          ctid
        }
        let dataList = []
        tradeList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.onSaleExtra,
              ctid: item.ctid,
              startTime: item.startTime,
              endTime: item.endTime,
              dutyId: item.dutyId,
              createAt:item.createAt,
              dutyStatus:item.dutyStatus,
              remark:item.remark
            })
          })
          this.tableData = dataList
          console.log(this.tableData)
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      tradeStop(id) {
        // this.$confirm(`确定终止该任务吗？`, '', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {

        // })
        tradeStop({
          dutyId: id,
          dutyType: 'ON_SALE'
        }).then((res) => {
          if (res.status.code == 0) {
            this.$message.success(res.status.msg)
            this.getList()
          }
        })
      },
      async onExport(title,id) {
      	this.loadingText = "正在导出"
      	this.loading = true
        const params = {
          dutyId :id,
          dutyType:'ON_SALE'
        }
      	const res = await this.$api.tradeRecordExport(params)
      	if (res.retCode === 500) {
      		this.$message.error(res.retMsg)
      		this.loading = false
      		this.getList()
      	} else if (res.type === 'application/json') {
      		// blob 转 JSON
      		const enc = new TextDecoder('utf-8')
      		res.arrayBuffer().then((buffer) => {
      			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      			this.$message.error(data.status?.msg)
      		})
      	} else {
      		downloadBlob(res, title + Date.now() + '.csv')
      		this.$message.success('明细导出成功')
      		this.loading = false
      		this.getList()
      	}
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
