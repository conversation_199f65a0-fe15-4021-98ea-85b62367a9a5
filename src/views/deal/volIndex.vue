<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange" :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_task()">创建自动成交</el-button>
      </template>
      <template #action="scope">
        <el-button @click="setSellStop(scope.row.dutyId)" type="text">终止任务</el-button>
      </template>
      <template #jindu="scope">
        <el-progress :text-inside="true" :stroke-width="16" :percentage="scope.row.tradeNumRatio"></el-progress>
        <!-- <el-button @click="setSellStop()" type="text">终止任务</el-button> -->
      </template>
      <template #floorPrice="scope">
        {{scope.row.floorPriceMinus}},{{scope.row.floorPricePlus}}
      </template>
      <template #remark="scope">
        <div class="oneOver" @click="openText(scope.row.remark)">{{scope.row.remark}}</div>
      </template>
      <template #TradeWaitingTime="scope">
        {{scope.row.minTradeWaitingTime}}s-{{scope.row.maxTradeWaitingTime}}s
      </template>
      <template #StartGapTime="scope">
        {{scope.row.minStartGapTime}}s-{{scope.row.maxStartGapTime}}s
      </template>
      <template #LockTime="scope">
        {{scope.row.minLockTime}}s-{{scope.row.maxLockTime}}s
      </template>
      <template #PriceRange="scope">
        {{scope.row.minPriceRange}}-{{scope.row.maxPriceRange}}
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    tradeList,
    tradeStop
  } from '@/api/hanxin'
  export default {
    name: 'volIndex',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'dutyId',
            width: '120px'
          },
          {
            label: '创建时间',
            field: 'createAt',
            width: '120px'
          },
          {
            label: '系列名',
            field: 'csName',
            width: '200px'
          },
          {
            label: '预期交易笔数',
            field: 'expectTradeNum',
            width: '120px'
          },
          {
            label: '已交易笔数',
            field: 'currentTradeNum',
            width: '110px'
          },
          {
            label: '进度',
            field: 'tradeNumRatio',
            slot: 'jindu',
            width: '130px'
          },
          {
            label: '交易平均价格',
            field: 'avgSellPrice',
            width: '120px'
          },
          {
            label: '设定价格范围',
            slot: 'floorPrice',
            width: '120px'
          },
          {
            label: '备注',
            slot: 'remark',
            width: '150px'
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              STOPPING: {
                label: '终止中',
                tagType: 'danger'
              },
              STOP: {
                label: '已终止',
                tagType: 'danger'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          // {
          //   label: '交易间隔时长',
          //   slot: 'TradeWaitingTime',
          //   width: '120px'
          // },
          {
            label: '设定价格范围',
            slot: 'floorPrice',
            width: '120px'
          },
          {
            label: '间隔多久上架交易一单',
            slot: 'StartGapTime',
            width: '120px'
          },
          {
            label: '每次交易前锁单多久',
            slot: 'LockTime',
            width: '120px'
          },
          {
            label: '交易价格',
            slot: 'PriceRange',
            width: '120px'
          },
          // {
          //   label: '任务复制几次',
          //   field: 'copyNum',
          //   width: '120px'
          // },
          {
            label: '开始时间',
            field: 'startTime',
            width: '160px'
          },
          {
            label: '结束时间',
            field: 'endTime',
            width: '160px'
          },
          {
            label: '设定最低价',
            field: 'minTradePrice',
            width: '100px'
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '140px',
          }
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
        	{
        		type: 'search',
        		label: '系列名/系列ID：',
        		placeholder: '请输入系列名/系列ID',
        		field: 'ctid'
        	},
        	{
        		type: 'select',
        		label: '任务类型：',
        		placeholder: '',
        		field: 'dutyStatus',
        		options: [{
                label: '准备中',
        				value: 'INIT'
              },
              {
                label: '进行中',
                value: 'DOING'
              },
         		   {
                label: '终止中',
                value: 'STOPPING'
              },
              {
             	  label: '已终止',
                value: 'STOP'
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '执行失败',
                value: 'FAIL'
              },
        		],
        		rules: [{
        			required: true,
        		}]
        	}
        ],
        query:{
          dutyStatus:'DOING'
        }
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_task() {
        this.$router.push({
          name: 'volAdd',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        let ctid="";
        if(this.query.ctid){
          ctid=this.query.ctid.split("(")[1].split(")")[0]
        }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'AUTO_TRADE',
          ctid
        }
        let dataList = []
        tradeList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.autoTradeExtra,
              ctid: item.ctid,
              startTime: item.startTime,
              endTime: item.endTime,
              dutyId: item.dutyId,
              createAt: item.createAt,
              dutyStatus: item.dutyStatus,
              remark: item.remark
            })
          })
          this.tableData = dataList
          this.tableData.forEach((item) => {
            item = item.setSellExtra
            this.$forceUpdate();
          })
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      setSellStop(id) {
        this.$confirm(`确定终止该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          tradeStop({
            dutyId: id,
            dutyType: 'AUTO_TRADE'
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      onRefresh(data){
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
