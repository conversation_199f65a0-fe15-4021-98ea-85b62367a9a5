<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #StartGapTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minStartGapTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxStartGapTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #FloorPriceMinus="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minFloorPriceMinus"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxFloorPriceMinus"></el-input>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #maxBuyPriceRadio="scope">
        <el-row justify="start">
          <el-col :span="3">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="请输入买入最高价-百分比" v-model="formData.maxBuyPriceRadio"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">%</div>
          </el-col>
          <el-col :span="5">
            <div class="grid-content bg-purple flex" style="color:rgb(255, 29, 29);">
              （建议不超过2.5）
            </div>
          </el-col>
        </el-row>
      </template>
      <template #onSalePriceRadio="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="请输入滚动比例" v-model="formData.onSalePriceRadio"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">%</div>
          </el-col>
        </el-row>
      </template>
      <template #UnSaleWaitingTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minUnSaleWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxUnSaleWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #NextWaitingTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minNextWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxNextWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #BuyWaitingTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.minBuyWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="formData.maxBuyWaitingTime"></el-input><span class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #autoSuodan="scope">
        <div class="div_left">
        <common-form :data="autoSuodanFormData" :schema="autoSuodanFormSchema" label-width="200px">
          <template #autoSuodanStartGapTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #autoSuodanLockTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minLockTime"></el-input><span class="danwei">min</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxLockTime"></el-input><span class="danwei">min</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #autoSuodanNextWaitingTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.minNextWaitingTime"></el-input><span
                    class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoSuodanFormData.maxNextWaitingTime"></el-input><span
                    class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #sleepTime="scope">
            <div class="flex">
                <el-time-picker
                    v-model="autoSuodanFormData.sleepTimeStart"
                    range-separator="至"
                    value-format="HH:mm:ss"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
                  </el-time-picker>
                 <span style="padding:0px 20px;">至</span>
                  <el-time-picker
                      v-model="autoSuodanFormData.sleepTimeEnd"
                      range-separator="至"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
            </div>
          </template>
        </common-form>
        </div>
      </template>
      <template #autoPay="scope">
         <div class="div_left">
        <common-form :data="autoPayFormData" :schema="autoPayFormSchema" label-width="200px">
          <template #autoPayTradeWaitingTime="scope">
            <el-row justify="start">
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoPayFormData.minStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
              <el-col :span="1">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple flex">
                  <el-input placeholder="" v-model="autoPayFormData.maxStartGapTime"></el-input><span class="danwei">s</span>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #sleepTime="scope">
            <div class="flex">
                <el-time-picker
                    v-model="autoPayFormData.sleepTimeStart"
                    range-separator="至"
                    value-format="HH:mm:ss"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    placeholder="选择时间范围">
                  </el-time-picker>
                 <span style="padding:0px 20px;">至</span>
                  <el-time-picker
                      v-model="autoPayFormData.sleepTimeEnd"
                      range-separator="至"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围">
                    </el-time-picker>
            </div>
          </template>
          <template #autoPayMsg>
              <div style="color:rgb(255, 29, 29)">
               注：每次交易前默认锁单多久系统默认展示4分钟
相比地板价取向上滚动的值
              </div>
          </template>
        </common-form>
         </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  export default {
    name: 'upRollAdd',
    components: {
      CommonForm,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
         isTiming:0,
         isAddLock:0,
         isAddAutoTrade:0,
         dutyNum:2,//挂几单
         minStartGapTime:31,// 出发间隔-最小值(秒
         maxStartGapTime:62,// 出发间隔-最大值(秒)
         minFloorPriceMinus:6,
         maxFloorPriceMinus:12,
         maxBuyPriceRadio:2,
         onSalePriceRadio:2,
         minUnSaleWaitingTime:303,
         maxUnSaleWaitingTime:606,
         minNextWaitingTime:122,
         maxNextWaitingTime:244,
         minBuyWaitingTime:62,
         maxBuyWaitingTime:123
        },
        formSchema: [{
            type: 'search',
            label: ' 系列：',
            placeholder: '请输入系列',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入系列',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '挂几单：',
            placeholder: '请输入挂几单',
            field: 'dutyNum',
            rules: [{
              required: true,
              message: '请输入挂几单',
              trigger: 'blur'
            }]
          },
          {
            label: '出发间隔：',
            placeholder: '请输入出发间隔',
            slot: 'StartGapTime',
            rules: [{
              required: true,
              message: '请输入出发间隔',
              trigger: 'blur'
            }]
          },

          {
            type: 'datetimerange',
            label: '比地板价（0）低几块：',
            placeholder: '请选择比地板价（0）低几块',
            slot: 'FloorPriceMinus',
            rules: [{
              required: true,
              message: '请选择比地板价（0）低几块',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '挂出最低价-底线：',
            placeholder: '请输入挂出最低价-底线',
            field: 'minOnSalePrice',
            rules: [{
              required: true,
              message: '请输入挂出最低价-底线',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '买入最高价-百分比：',
            placeholder: '请输入买入最高价-百分比',
            slot:'maxBuyPriceRadio',
            // field: 'maxBuyPriceRadio',
            rules: [{
              required: true,
              message: '请输入买入最高价-百分比',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '滚动比例：',
            placeholder: '请输入滚动比例',
            slot:'onSalePriceRadio',
            // field: 'onSalePriceRadio',
            rules: [{
              required: true,
              message: '请输入卖出试探时长',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '尝试时间：',
            placeholder: '请输入尝试时间',
            slot: 'UnSaleWaitingTime',
            rules: [{
              required: true,
              message: '请输入尝试时间',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '休息时间：',
            placeholder: '请输入休息时间',
            slot: 'NextWaitingTime',
            rules: [{
              required: true,
              message: '请输入休息时间',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '卖出后多久买入0的：',
            placeholder: '请输入卖出后多久买入0的',
            slot: 'BuyWaitingTime',
            rules: [{
              required: true,
              message: '请输入卖出后多久买入0的',
              trigger: 'blur'
            }]
          },
          {
            label: '开始时间：',
            field: 'isTiming',
            type: 'radio',
            options: [{
              label: '立即开始',
              value: 0
            }, {
              label: '定时',
              value: 1
            }]
          },
          {
            type: 'datetime',
            label: '开始时间：',
            field: 'startTime',
            placeholder: '请输入开始时间',
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            show: {
              relationField: 'isTiming',
              value: [1]
            },
          },
          {
            label: '是否配合锁单：',
            field: 'isAddLock',
            type: 'radio',
            options: [{
              label: '是(展开)',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            label: '',
            slot: 'autoSuodan',
            show: {
              relationField: 'isAddLock',
              value: [1]
            },
          },
          {
            label: '是否配合自动交易：',
            field: 'isAddAutoTrade',
            type: 'radio',
            options: [{
              label: '是(展开)',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            label: '',
            slot: 'autoPay',
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            type: 'action'
          },
        ],
        searchList: [],
        isError: '',
        autoSuodanFormSchema: [{
            type: 'number-input',
            label: '锁几单：',
            placeholder: '请输入锁几单',
            field: 'dutyNum',
            rules: [{
              required: true,
              message: '请输入锁几单',
              trigger: 'blur'
            }]
          },
          {
            label: '出发间隔：',
            placeholder: '请输入出发间隔',
            slot: 'autoSuodanStartGapTime',
            rules: [{
              required: true,
              message: '请输入出发间隔',
              trigger: 'blur'
            }]
          },
          {
            label: '每锁一次锁多久：',
            placeholder: '请输入每锁一次锁多久',
            slot: 'autoSuodanLockTime',
            rules: [{
              required: true,
              message: '请输入每锁一次锁多久',
              trigger: 'blur'
            }]
          },
          {
            label: '锁单间隔多久：',
            placeholder: '请输入锁单间隔多久',
            slot: 'autoSuodanNextWaitingTime',
            rules: [{
              required: true,
              message: '请输入锁单间隔多久',
              trigger: 'blur'
            }]
          },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
        ],
        autoSuodanFormData:{
           isSleep:0
        },
        autoPayFormSchema: [
          {
            label: '间隔多久上架交易一单：',
            placeholder: '请输入间隔多久上架交易一单',
            slot: 'autoPayTradeWaitingTime',
            rules: [{
              required: true,
              message: '请输入间隔多久上架交易一单',
              trigger: 'blur'
            }]
          },
         {
           label: '',
           slot: 'autoPayMsg',
         },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
        ],
        autoPayFormData:{
          isSleep:0
        }
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      // 富文本中的内容
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        let startTime, ctid
        if (this.formData.ctid) {
          ctid = this.formData.ctid.split("(")[1].split(")")[0]
        }
        let extraData = {
          ...this.formData,
          lockTradeExtra:{
            ...this.autoSuodanFormData,
          },
          autoTradeExtra:{
            ...this.autoPayFormData
          }
        }
        console.log(extraData)
        let extra = JSON.stringify(extraData)
        const data = {
          dutyType: 'UP_TRADE',
          isTiming:this.formData.isTiming,
          extra,
          startTime:this.formData.startTime,
          ctid,
        }
        console.log(data)
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.tradeAdd(data);
          if (res.status.code == 0) {
            this.routerBack()
          }
        })
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        letctid
        if (this.formData.ctid) {
          ctid = this.formData.ctid.split("(")[1].split(")")[0]
        }
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
  .div_left{
    margin-left:-180px !important;
  }
</style>
