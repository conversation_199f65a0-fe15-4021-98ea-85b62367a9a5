<template>
  <d2-container class="page">
    <common-form :data="autoSuodanFormData" :schema="autoSuodanFormSchema" :submit="submit" label-width="200px">
      <template #autoSuodanStartGapTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.minStartGapTime"></el-input><span
                class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.maxStartGapTime"></el-input><span
                class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #autoSuodanLockTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.minLockTime"></el-input><span class="danwei">min</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.maxLockTime"></el-input><span class="danwei">min</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #autoSuodanNextWaitingTime="scope">
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.minNextWaitingTime"></el-input><span
                class="danwei">s</span>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple flex">
              <el-input placeholder="" v-model="autoSuodanFormData.maxNextWaitingTime"></el-input><span
                class="danwei">s</span>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #sleepTime="scope">
        <div class="flex">
          <el-time-picker v-model="autoSuodanFormData.sleepTimeStart" range-separator="至"
            value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
          </el-time-picker>
          <span style="padding:0px 20px;">至</span>
          <el-time-picker v-model="autoSuodanFormData.sleepTimeEnd" range-separator="至"
            value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
          </el-time-picker>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  export default {
    name: 'autoLockOrderAdd',
    components: {
      CommonForm,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        autoSuodanFormSchema: [{
            type: 'search',
            label: ' 系列：',
            placeholder: '请输入系列',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入系列',
              trigger: 'blur'
            }]
          }, {
            type: 'number-input',
            label: '几个锁子军：',
            placeholder: '请输入几个锁子军',
            field: 'dutyNum',
            rules: [{
              required: true,
              message: '请输入几个锁子军',
              trigger: 'blur'
            }]
          },
          {
            label: '出发间隔时长：',
            placeholder: '请输入出发间隔时长',
            slot: 'autoSuodanStartGapTime',
            rules: [{
              required: true,
              message: '请输入出发间隔时长',
              trigger: 'blur'
            }]
          },
          {
            label: '间隔多久锁一单：',
            placeholder: '请输入间隔多久锁一单',
            slot: 'autoSuodanNextWaitingTime',
            rules: [{
              required: true,
              message: '请输入间隔多久锁一单',
              trigger: 'blur'
            }]
          },
          {
            label: '每次展示时间多久：',
            placeholder: '请输入每次展示时间多久',
            slot: 'autoSuodanLockTime',
            rules: [{
              required: true,
              message: '请输入每次展示时间多久',
              trigger: 'blur'
            }]
          },
          {
            label: '开始时间：',
            field: 'isTiming',
            type: 'radio',
            options: [{
              label: '立即开始',
              value: 0
            }, {
              label: '定时',
              value: 1
            }]
          },
          {
            type: 'datetimerange',
            label: '开始时间：',
            field: 'startTime',
            placeholder: '请输入开始时间',
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            show: {
              relationField: 'isTiming',
              value: [1]
            },
          },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
          {
            type: 'action'
          },
        ],
        autoSuodanFormData: {
          isSleep: 0,
          isTiming:0
        },
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      // 富文本中的内容
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
        let startTime,endTime, ctid
        if (this.autoSuodanFormData.ctid) {
          ctid = this.autoSuodanFormData.ctid.split("(")[1].split(")")[0]
        }
        if(this.autoSuodanFormData.isTiming==1){
          startTime =  this.autoSuodanFormData.startTime[0];
          endTime =  this.autoSuodanFormData.startTime[1];
        }
        let extra = JSON.stringify(this.autoSuodanFormData)
        const data = {
          dutyType: 'LOCK_TRADE',
          isTiming: this.autoSuodanFormData.isTiming,
          extra,
          startTime,
          endTime,
          ctid,
        }
        console.log(data)
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.tradeAdd(data);
          if (res.status.code == 0) {
            this.routerBack()
          }
        })
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }

  .div_left {
    margin-left: -180px !important;
  }
</style>
