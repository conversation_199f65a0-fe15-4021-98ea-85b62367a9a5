<template>
  <d2-container class="page" ref="returnTop">
    <!-- <el-button type="primary" size="mini" style="margin-bottom:20px" @click="shuaxin()">刷新</el-button> -->
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showRefresh="true"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_task()">新建任务</el-button>
      </template>
      <template #action="scope">
        <el-button @click="setSellStop(scope.row.dutyId)" type="text">终止任务</el-button>
        <el-button type="text" size="mini" @click="onExport('向上滚动交易明细',scope.row.dutyId)">导出明细</el-button>
      </template>
      <template #floorPriceMinus="scope">
        {{scope.row.minFloorPriceMinus}}-{{scope.row.maxFloorPriceMinus}}
      </template>
      <template #UnSaleWaitingTime="scope">
        {{scope.row.minUnSaleWaitingTime}}s-{{scope.row.maxUnSaleWaitingTime}}s
      </template>
      <template #NextWaitingTime="scope">
        {{scope.row.minNextWaitingTime}}s-{{scope.row.maxNextWaitingTime}}s
      </template>
      <template #BuyWaitingTime="scope">
        {{scope.row.minBuyWaitingTime}}s-{{scope.row.maxBuyWaitingTime}}s
      </template>
      <template #startGapTime="scope">
        {{scope.row.minStartGapTime}}s-{{scope.row.maxStartGapTime}}s
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    tradeList,
    tradeStop,
  } from '@/api/hanxin'
  export default {
    name: 'upRoll',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        tableSchema: [ // 表格架构
          {
            label: '任务ID',
            field: 'dutyId',
            width: '100px'
          },
          {
            label: '系列名称',
            field: 'csName',
            width: '120px'
          },
          {
            label: '挂几单',
            field: 'dutyNum',
            width: '100px'
          },
          {
            label: '滚动比例',
            field: 'onSalePriceRadio',
            width: '120px'
          },
          {
            label: '出发间隔',
            slot: 'startGapTime',
            width: '120px'
          },
          {
            label: '低几块',
            slot: 'floorPriceMinus',
            width: '110px'
          },
          {
            label: '底线价',
            field: 'minOnSalePrice',
            width: '110px'
          },
          {
            label: '尝试时间',
            slot: 'UnSaleWaitingTime',
            width: '130px'
          },
          {
            label: '休息时间',
            slot: 'NextWaitingTime',
            width: '130px'
          },
          {
            label: '卖出多久买入0',
            slot: 'BuyWaitingTime',
            width: '120px'
          },
          {
            label: '任务开始时间',
            field: 'startTime',
            width: '150px'
          },
          {
            label: '已滚动单数',
            field: 'currentTradeNum',
            width: '80px'
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              STOPPING: {
                label: '终止中',
                tagType: 'danger'
              },
              STOP: {
                label: '已终止',
                tagType: 'danger'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          {
            label: '备注',
            field: 'remark',
            align:"center",
             width: '180px',
            showOverflowTooltip:true
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '200px',
          },
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'input',
            label: '系列名',
            placeholder: '请输入系列名',
            field: 'csName'
          },
          {
            type: 'select',
            label: '任务类型：',
            placeholder: '',
            field: 'dutyStatus',
            options: [{
                label: '准备中',
        				value: 'INIT'
              },
              {
                label: '进行中',
                value: 'DOING'
              },
         		   {
                label: '终止中',
                value: 'STOPPING'
              },
              {
             	  label: '已终止',
                value: 'STOP'
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '执行失败',
                value: 'FAIL'
              },
            ],
            rules: [{
              required: true,
            }]
          }
        ],
        query: {
          dutyStatus:'DOING'
        },
        loading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_task() {
        this.$router.push({
          name: 'upRollAdd',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        this.loading=true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'UP_TRADE',
        }
        let dataList = []
        tradeList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.upTradeExtra,
              ctid: item.ctid,
              startTime: item.startTime,
              endTime: item.endTime,
              dutyId: item.dutyId,
              createAt: item.createAt,
              dutyStatus: item.dutyStatus,
              remark: item.remark,
            })
          })
          this.tableData = dataList
          this.tableData.forEach((item) => {
            item = item.upTradeExtra
            console.error(item)
            this.$forceUpdate();
          })
          this.loading=false
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      setSellStop(id) {
        this.$confirm(`确定终止该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          tradeStop({
            dutyId: id,
            dutyType: 'UP_TRADE'
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      openCopy(id) {
        this.$prompt('请输入你要复制的份数', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^((([1-9]\d+)|([1-9]))$)/,
          inputErrorMessage: '请输入整数',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '执行中...';
              setTimeout(() => {
                done();
                setTimeout(() => {
                  instance.confirmButtonLoading = false;
                }, 300);
              }, 1000);
            } else {
              done();
            }
          }
        }).then(({
          value
        }) => {
          this.sellCopy(id, value)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      sellCopy(dutyId, copyNum) {
        const loading = this.$loading({
          lock: true,
          text: '复制中，请耐心等待',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        setSellCopy({
          dutyId,
          dutyType: 'UP_TRADE',
          copyNum
        }).then((res) => {
          if (res.status.code == 0) {
            loading.close();
            this.$message.success(res.status.msg)
            this.getList()
          } else {
            loading.close();
          }
        })
      },
      shuaxin() {
        this.$message.success("刷新成功")
        this.getList()
      },
      async onExport(title,id) {
      	this.loadingText = "正在导出"
      	this.loading = true
        const params = {
          dutyId :id,
          dutyType:'UP_TRADE'
        }
      	const res = await this.$api.tradeRecordExport(params)
      	if (res.retCode === 500 || res.retCode === 9999) {
      		this.$message.error(res.retMsg)
      		this.loading = false
      		this.getList()
      	} else if (res.type === 'application/json') {
      		// blob 转 JSON
          this.loading = false
      		const enc = new TextDecoder('utf-8')
      		res.arrayBuffer().then((buffer) => {
      			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      			this.$message.error(data.status?.msg)
      		})
      	} else {
      		downloadBlob(res, title + Date.now() + '.csv')
      		this.$message.success('明细导出成功')
      		this.loading = false
      		this.getList()
      	}
      },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
