<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="商品id">
        <el-input
          v-model="formInline.goodId"
          placeholder="请输入商品id"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="商品tid">
        <el-input
          v-model="formInline.tid"
          placeholder="请输入商品tid"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="batch_click()">批量上链</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="goodId"
        label="商品id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tid"
        label="商品tid"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contractAddress"
        label="合约地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="transactionHash"
        label="交易hash"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="retryCont"
        label="重试次数"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="response"
        label="返回信息"
        align="center"
      ></el-table-column>
      <el-table-column prop="type" label="部署失败类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.type == '0'">部署合约</el-tag>
          <el-tag v-if="scope.row.type == '1'">激活合约</el-tag>
          <el-tag v-if="scope.row.type == '2'">mint</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="typeStr"
        label="部署失败类型str"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateAt"
        label="更新时间"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >重新上链</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="10"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" center>
      <el-form :model="form" ref="form">
        <el-form-item
          v-if="title == '批量上链'"
          label="日期:"
          :label-width="formLabelWidth"
          required
        >
          <el-date-picker
            v-model="form.createAt"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="是否进入队列:"
          :label-width="formLabelWidth"
          required
          v-if="title == '重新上链'"
        >
          <el-radio-group v-model="form.toQueue">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="类型:"
          :label-width="formLabelWidth"
          required
          v-if="title == '重新上链'"
        >
          <el-option
            v-for="item in manaulTypeList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          >
          </el-option>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="debounceMethods(dialog_submit)"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'contract',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        goodId: '',
        tid: '',
        createAt: null,
        startDate: null,
        endDate: null
      },
      isDialog: false,
      title: '',
      form: {
        toQueue: false,
        createAt: null,
        startDate: null,
        endDate: null
      },
      formLabelWidth: '120px',
      tid: '',
      manaulTypeList: []
    }
  },
  mounted () {
    this.getManaulType()
    this.getList(1)
  },
  methods: {
    // 字典查询
    async getManaulType () {
      const res = await this.$api.getDictDataByDictType({
        dictType: 'CHAIN_DEPLOY_TYPE'
      })
      this.manaulTypeList = res.result.dictDataListVOS
    },
    clear () {
      this.formInline.goodId = ''
      this.formInline.tid = ''
      this.formInline.createAt = undefined
    },
    // 查询
    async getList (page) {
      if (this.formInline.createAt) {
        this.formInline.startDate = this.formInline.createAt[0]
        this.formInline.endDate = this.formInline.createAt[1]
      } else {
        this.formInline.startDate = null
        this.formInline.endDate = null
      }
      const res = await this.$api.contractList({
        pageNum: page,
        pageSize: 10,
        goodId: this.formInline.goodId,
        tid: this.formInline.tid,
        startDate: this.formInline.startDate,
        endDate: this.formInline.endDate
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    // 重新上链
    audit_click (val) {
      this.isDialog = true
      this.tid = val.tid
      this.title = '重新上链'
    },
    // 批量上链
    batch_click () {
      this.isDialog = true
      this.title = '批量上链'
    },
    // 弹框确定
    async dialog_submit () {
      if (this.title === '重新上链') {
        await this.$api.manaulContract({
          tid: this.tid,
          toQueue: this.form.toQueue
        })
        this.isDialog = false
        this.form = {
          createAt: null
        }
        this.$message.success('重新上链成功')
      } else {
        if (this.form.createAt) {
          this.form.startDate = this.form.createAt[0]
          this.form.endDate = this.form.createAt[1]
        } else {
          this.form.startDate = null
          this.form.endDate = null
        }
        await this.$api.historylContract({
          startDate: this.form.secTitle,
          endDate: this.form.title
        })
        this.isDialog = false
        this.form = {
          createAt: null
        }
        this.$message.success('编辑成功')
      }
    }
  }
}
</script>

<style></style>
