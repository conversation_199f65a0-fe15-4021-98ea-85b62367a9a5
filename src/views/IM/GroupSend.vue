<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <el-button type="primary" style="margin: 0 0 20px 0;" @click="SenddialogVisible = true">创建新的群发</el-button>
    <common-table :table-data="tableData" :showIndex="false" :table-schema="tableSchema" :loading="loading">
      <template #ids="scope">
        <!-- {{ (scope.row.groupAccountInfo).forEach(item => item.groupAccount).join(',')  }} -->
        <!-- <span v-for="(item, index) in scope.row.groupAccountInfo" :key="index">{{ item.groupAccount + '/n' }}</span> -->
        <div>
          <div v-for="(item, index) in scope.row.groupAccountInfo" :key="index">
            {{ item.groupAccount }}
          </div>
        </div>
      </template>
      <template #groupName="scope">
        <div>
          <div v-for="(item, index) in scope.row.groupAccountInfo" :key="index">
            {{ item.groupName }}
          </div>
        </div>
      </template>
      <template #groupType="scope">
        <!-- <span v-for="(type, index) in groupTypes" :key="index">
          <span v-if="isSendTypeIncluded(scope.row.groupType, type.value)">{{ type.label }}</span>
        </span> -->
        <!-- getGroupLabels -->
        {{ scope.row.groupType == '' ? '特定群' : (scope.row.groupType) }}
      </template>
      <template #sendStatus="scope">
        <span v-if="scope.row.sendStatus === 0">未执行</span>
        <span v-if="scope.row.sendStatus === 1">已执行</span>
        <span v-if="scope.row.sendStatus === 2">执行中</span>
        <span v-if="scope.row.sendStatus === 3">手动停止</span>
        <span v-if="scope.row.sendStatus === 4">已撤销</span>
        <!-- 显示按钮 -->
        <el-button style="margin-left: 10px;" v-if="scope.row.sendStatus === 0 || scope.row.sendStatus === 2"
          @click="handleStop(scope.row)" type="text">
          终止
        </el-button>

        <el-button style="margin-left: 10px;" v-if="scope.row.sendStatus === 1" @click="handleRevoke(scope.row)"
          type="text">
          撤回
        </el-button>

        <!-- <el-button type="primary" size="mini" @click="SenddialogVisible = true">{{ scope.row.nick }}</el-button> -->
      </template>
      <template #nick="scope">
        <el-button @click="reportNameDetail(scope.row)" type="text">{{ scope.row.nickname }}</el-button>
      </template>
      <template #message="scope">
        <div v-if="scope.row.messageType === 'text'">
          {{ scope.row.message }}
          <!-- <template v-if="scope.row.compliance === 0">
            <div :class="{ 'blue': scope.row.withdraw == 1 }">{{ scope.row.chatContent.MsgContent.Text }}</div>
          </template>
<template v-else>
            <div v-html="keyWordHighlight(scope.row.chatContent.MsgContent.Text, scope.row.badWord)"></div>
          </template> -->
        </div>
        <el-image v-else-if="scope.row.messageType === 'image'" style="width: 100px; height: 100px"
          :src="scope.row.message" :preview-src-list="[scope.row.message]">
        </el-image>

        <a v-else-if="scope.row.messageType === 'video'" target="_blank" :href="scope.row.message">查看视频</a>

      </template>
      <template #all="scope">
        <el-button type="text" @click="openChatList(scope.row)">查看</el-button>
      </template>

      <!-- <template #pic="scope">
          <template v-if="scope.row.pic">
            <video v-if="scope.row.chatContentType === 2" class="home-mint" autoplay loop muted preload="auto">
              <source :src="scope.row.pic" type="video/mp4">
            </video>
            <el-image style="width:60px;"  v-else :src="scope.row.pic" :preview-src-list="[scope.row.pic]"></el-image>
          </template>
        </template> -->
      <template #action="scope">
        <el-button @click="openActionDialogVisible(scope.row)" type="text">操作</el-button>
        <el-button @click="openSpeakDialogVisible(scope.row)" type="text">伯德发言</el-button>
        <el-button @click="open(scope.row)" type="text">水军发言</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <!--群成员信息-->
    <el-dialog title="群成员信息" :visible.sync="dialogVisible">
      <common-form label-width="200px" :is-edit="false" :schema="infoSchema" :data="data"></common-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog width="500px" title="用户操作" :visible.sync="actionDialogVisible">
      <div style="margin-bottom: 20px;">选择确认后，在客户端将以伯德护卫机器人身份对该用户进行请出群聊或全群拉黑。</div>
      <el-radio-group v-model="actionSelected">
        <div v-for="item in actionList" :key="item.value" style="display: inline-block; margin-right: 16px;">
          <template v-if="curInfo.memberType === 0">
            <el-radio v-if="[11, 16].includes(item.value)" :label="item.value">{{ item.label }}</el-radio>
          </template>
          <el-radio v-else :label="item.value">{{ item.label }}</el-radio>
        </div>
      </el-radio-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="actionDialogVisible = false">关闭</el-button>
        <el-button @click="operateChatRecord" type="primary">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog width="500px" title="发言" :visible.sync="speakDialogVisible">
      <ul style="margin-top: 0; margin-bottom: 20px;">
        <li>输入文案并提交发送后，在客户端该群中将以伯德护卫机器人身份进行发言，文字和图片一次性提交将以两条信息进行发送。</li>
        <li>针对某条聊天记录进行发言时，机器人在前端将自动@该用户。</li>
      </ul>
      <common-form label-width="120px" :schema="speakSchema" :data="speakData"></common-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="speakDialogVisible = false">关闭</el-button>
        <el-button @click="chatRecords" type="primary">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="infoDialogVisible" width="600px">
      <el-form ref="form" label-width="200px" v-loading="loading">
        <el-form-item label="昵称:">
          {{ infoDialogData.nickname }}
        </el-form-item>
        <el-form-item label="contractAddress:">
          {{ infoDialogData.contractAddress }}
        </el-form-item>
        <el-form-item label="仓位:">
          {{ infoDialogData.goodsAmount }}
        </el-form-item>
        <el-form-item label="净买入:">
          {{ infoDialogData.buySubSellAmount }}
        </el-form-item>
        <el-form-item label="最近购买时间:">
          {{ infoDialogData.lastBuyOrderTime }}
        </el-form-item>
        <el-form-item label="注册时间:">
          {{ infoDialogData.registerTime }}
        </el-form-item>
        <el-form-item label="查看他的藏品:">
          <el-button type="text" @click="nav_shop()">{{ infoDialogData.collections }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="水军发言" :visible.sync="isJoinQun" width="80%">
      <div class="content_dialog">
        <div class="left_div">
          <el-input type="textarea" autosize placeholder="请输入发言内容" v-model="textarea_speak">
          </el-input>
        </div>
        <div class="right_div">
          <el-form :inline="true" :model="formScreen" class="demo-form-inline" size="mini">
            <el-form-item label="昵称">
              <el-input v-model="formScreen.nickname" placeholder="请输入昵称"></el-input>
            </el-form-item>
            <!-- <el-form-item label="用户ID">
                    <el-input v-model="formScreen.id" placeholder="请输入用户ID"></el-input>
                </el-form-item>
                <el-form-item label="邮箱">
                    <el-input v-model="formScreen.email" placeholder="请输入邮箱" clearable></el-input>
                </el-form-item> -->
            <el-form-item label="contractAddress">
              <el-input v-model="formScreen.contractAddress" placeholder="请输入contractAddress" clearable>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getUserList">查询</el-button>
              <el-button type="success" @click="submitJoinInGroup()">水军一键发言</el-button>
            </el-form-item>
          </el-form>
          <common-table :table-schema="userColumn" :table-data="userData" :multipleSelection.sync="multipleSelection"
            :showSelection="true">
          </common-table>
        </div>
      </div>

    </el-dialog>


    <!-- 弹窗内容 -->
    <el-dialog title="发送消息" :visible.sync="SenddialogVisible" width="60%">
      <!-- 消息类型选择 -->
      <div style="display: flex;flex-direction: column;">
        <span>消息类型</span>
        <el-radio-group v-model="selectedType">
          <el-radio :label="'text'">文本</el-radio>
          <!-- <el-radio :label="'audio'">语音</el-radio> -->
          <el-radio :label="'image'">图片</el-radio>
        </el-radio-group>
      </div>

      <!-- 根据选择类型显示对应内容 -->
      <div v-if="selectedType === 'text'" style="margin-top: 20px;">
        <el-input v-model="textContent" type="textarea" autosize placeholder="请输入文本内容"></el-input>
      </div>
      <div v-else-if="selectedType === 'audio'" style="margin-top: 20px;">
        <div class="view_yu " @click="audio_play">
          <div class="yuyin " :class="{ 'active': isAudioPlpy }" v-if="audioNum">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div v-if="audioNum">{{ audioNum }}s 点击播放</div>
          <div v-else>请先开始路录音</div>
        </div>
        <el-button @click="audio_start()">开始录音</el-button>
        <el-button @click="audio_stop()">结束录音</el-button>
        <el-button @click="audio_destroy()">重录</el-button>
      </div>
      <div v-else-if="selectedType === 'image'" style="margin-top: 20px;">
        <!-- <el-upload action="https://jsonplaceholder.typicode.com/posts/" list-type="picture">
          <el-button type="primary">上传</el-button>
        </el-upload> -->
        <el-upload ref="uploadRef" :action="action" :headers="token" :on-success="handlePicSuccess"
          :class="{ hide: hideUpload_introduce }" :on-change="handleIntroduceUploadHide"
          :on-remove="handleIntroduceRemove" :file-list="fileListImg" class="avatar-uploader">
          <img v-if="images" :src="images" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <el-button @click="reUpload">重传</el-button>
      </div>

      <!-- 发送群选择 -->
      <div style="margin-top: 20px;">
        <div style="margin:  0 0 10px 0;">发送群:</div>
        <el-radio-group v-model="selectedGroups">
          <el-radio :label="0">藏家群</el-radio>
          <el-radio :label="1">DAO群</el-radio>
          <el-radio :label="2">萌新群</el-radio>
          <el-radio :label="3">萌新DAO群</el-radio>
          <el-radio :label="4">直播群</el-radio>
          <el-radio :label="5">师门群</el-radio>
          <el-radio :label="6">讨论群</el-radio>
          <el-radio label="特定群">特定群</el-radio>
        </el-radio-group>
      </div>
      <div v-if="selectedGroups == '特定群'" style="margin-top: 20px;">
        <!-- <el-input v-model="specificGroup" placeholder="输入群名，点击选中"></el-input> -->
        <!-- 多选输入框 -->
        <div class="autocomplete-multi-select">
          <div v-for="(item, index) in selectedItems" :key="index" class="selected-item" style="margin-bottom: 20px;">
            <el-tag closable @close="removeItem(index)">
              {{ item.value }}
            </el-tag>
          </div>
          <el-autocomplete style="width:100%;" v-model="specificGroup" :fetch-suggestions="fetchSuggestions"
            placeholder="请输入内容" @select="handleSelect" clearable />
        </div>
      </div>

      <!-- 发送时间选择 -->
      <div style="margin-top: 20px;display: flex;flex-direction: column;">
        <span>发送时间</span>

        <el-radio-group v-model="sendTime">
          <el-radio :label="'immediate'">立即</el-radio>
          <el-radio :label="'scheduled'">定时</el-radio>
        </el-radio-group>
        <el-date-picker v-if="sendTime === 'scheduled'" v-model="scheduledTime" type="datetime" placeholder="选择定时时间"
          style="margin-top: 10px;"></el-date-picker>
      </div>

      <!-- 弹窗底部 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="SenddialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSend">确认</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import Recorder from 'js-audio-recorder';
import CommonForm from '@/components/CommonForm'
import {
  uploadExcelToOss
} from '@/api/ossCenter'
export default {
  name: 'ChatRecord',
  components: {
    CommonTable,
    CommonForm,
    CommonQuery
  },
  data() {
    return {
      hideUpload_introduce: false,
      fileListImg: [],
      limitCount: 1, images: '',
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/upload',
      token: { AdminAuthorization: localStorage.getItem('usertoken') },
      file: "",
      audioType: false,
      audioUrl: '',
      audioSize: "",
      isGG: false,
      state: [],
      selectedItems: [], // 存储已选择的选项
      suggestions: [], // 自动补全的建议列表
      groupTypes: [
        { value: "0", label: "藏家群" },
        { value: "1", label: "DAO群" },
        { value: "2", label: "萌新群" },
        { value: "3", label: "萌新DAO群" },
        { value: "4", label: "直播群" },
        { value: "5", label: "师门群" },
        { value: "6", label: "讨论群" },
      ],
      isAudioPlpy: false,
      audioNum: "",
      SenddialogVisible: false, // 控制弹窗显示
      selectedType: 'text', // 消息类型选择
      textContent: '', // 文本内容
      selectedGroups: "", // 选中的群
      specificGroup: '', // 特定群名称
      sendTime: 'immediate', // 发送时间选择
      scheduledTime: '', // 定时时间
      curInfo: {}, // 当前操作信息
      tableData: [],
      query: {
        // memberNameCard: "",
        // contractAddress: "",
        // nickName: "",
        // groupAccount: ""
      },
      querySchema: [
        {
          type: 'datetimerange',
          label: '发言时间',
          field: 'sendStartTime',
          field2: 'sendEndTime',
          placeholder: '请选择发言时间'
        },
        {
          type: 'select',
          label: '触达群组',
          field: 'groupType',
          placeholder: '请选择群组',
          options: [
            { value: "0", label: "藏家群" },
            { value: "1", label: "DAO群" },
            { value: "2", label: "萌新群" },
            { value: "3", label: "萌新DAO群" },
            { value: "4", label: "直播群" },
            { value: "5", label: "师门群" },
            { value: "6", label: "讨论群" },
            { value: "", label: '特定群' }
          ]
        },
        {
          type: 'Thesearch',
          label: '触达指定群',
          placeholder: '请输入群聊名称/群ID',
          field: 'messageGroupAccount',
          rules: [{
            required: true,
            message: '请输入系列名称/系列ID',
            trigger: 'blur'
          }]
        },
        {
          type: 'select',
          label: '发送状态',
          field: 'status',
          placeholder: '请选择状态',
          options: [
            { label: '未执行', value: 0 },
            { label: '已执行', value: 1 },
            { label: '执行中', value: 2 },
            { label: '手动停止', value: 3 },
            { label: '已撤销', value: 4 },
          ]
        },
        // {
        //   type: 'input',
        //   label: '群id',
        //   placeholder: '请输入群id',
        //   field: 'groupAccount'
        // },
        // {
        //   type: 'input',
        //   label: '群名称',
        //   placeholder: '请输入群名称',
        //   field: 'groupName'
        // },
        // {
        //   type: 'input',
        //   label: '用户昵称',
        //   placeholder: '请输入用户昵称',
        //   field: 'nickName'
        // },
        // {
        //   type: 'input',
        //   label: ' 用户con add',
        //   placeholder: '请输入 用户con add',
        //   field: 'contractAddress'
        // },
        // {
        //   type: 'input',
        //   label: '用户手机号',
        //   placeholder: '请输入用户手机号',
        //   field: 'phone'
        // },
        // {
        //   type: 'input',
        //   label: '群成员昵称',
        //   placeholder: '请输入群成员昵称',
        //   field: 'memberNameCard'
        // },

        // {
        //   type: 'select',
        //   label: '成员属性',
        //   field: 'memberType',
        //   placeholder: '请选择成员属性',
        //   options: [
        //     { label: '全部', value: -1 },
        //     { label: '群主', value: 0 },
        //     { label: '管理员', value: 1 },
        //     { label: '普通用户', value: 2 },
        //     { label: '机器人', value: 3 }
        //   ]
        // },
        // {
        //   type: 'select',
        //   label: '发言规范',
        //   field: 'compliance',
        //   placeholder: '请选择状态',
        //   options: [
        //     { label: '全部', value: -1 },
        //     { label: '合规', value: 0 },
        //     { label: '文本违规', value: 1 },
        //     { label: '图片违规', value: 2 }
        //   ]
        // },
      ],
      tableSchema: [{
        label: '群发时间',
        field: 'sendTime'
      },
      {
        label: '群名称',
        slot: 'groupName'
      },
      {
        label: '群id',
        slot: 'ids'
      },
      {
        label: '消息内容',
        slot: 'message',
      },
      {
        label: '触达群',
        field: 'groupType',
        slot: 'groupType'

      },
      {
        label: '发送状态',
        field: 'sendStatus',
        slot: 'sendStatus'
      },
        // {
        //   label: '发言内容',
        //   slot: 'chatContent1'
        // },
        // {
        //   label: '查看他的所有发言',
        //   slot: 'all'
        // },
        // {
        //   label: '群id',
        //   field: 'groupAccount',
        //   width: '220px'
        // },
        // {
        //   label: '操作',
        //   slot: 'action'
        // }
      ],
      dialogVisible: false,
      actionDialogVisible: false,
      speakDialogVisible: false,
      data: {}, // 成员信息
      speakData: {
        photos: ''
      }, // 成员信息
      actionSelected: 11, // 操作值
      actionList: [ // 操作列表
        {
          label: '本群禁言',
          value: 11
        },
        {
          label: '移出本群',
          value: 12
        },
        {
          label: '撤回该发言',
          value: 16
        },
        {
          label: '全平台拉黑',
          value: 13
        },
        {
          label: '取消禁止发言',
          value: 14
        },
        {
          label: '取消全平台拉黑',
          value: 15
        }
      ],
      infoSchema: [{
        label: '账户昵称：',
        field: 'nickname'
      },
      {
        label: 'contract_address：',
        field: 'contractAddress'
      },
      {
        label: '作品：',
        field: 'originGoodsNum'
      },
      {
        label: '藏品：',
        field: 'collectGoodsNum'
      },
      {
        label: '管理的DAO群：',
        field: 'daoGroupNum'
      },
      {
        label: '管理的藏家群：',
        field: 'groupNum'
      },
      {
        label: '加入的群：',
        field: 'joinGroupNum'
      }
      ],
      speakSchema: [{
        label: '发言文案信息：',
        field: 'content',
        type: 'textarea'
      },
      {
        label: '图片：',
        field: 'photos',
        type: 'img'
      }
      ],
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      },
      loading: true,
      infoDialogVisible: false,
      infoDialogData: {},
      isJoinQun: false,
      userColumn: [{
        label: '用户id',
        field: 'memberId'
      },
      {
        label: '用户昵称',
        field: 'nickname'
      },
      {
        label: '用户地址',
        field: 'contractAddress'
      },
      {
        label: '用户邮箱',
        field: 'email'
      }
      ],
      keyword: "",
      formScreen: {
        email: '',
        nickname: '',
        id: '',
        contractAddress: ''
      },
      multipleSelection: [],
      userData: "",
      groupId: "",
      textarea_speak: "",
      bobao: ""
    }
  },
  computed: {},
  mounted() {

    if (this.$route.query.memberName) {
      this.query.nickName = this.$route.query.memberName
    }
    if (this.$route.query.groupAccount) {
      this.query.groupAccount = this.$route.query.groupAccount
    }
    console.log(this.query)
    this.getList()
  },
  methods: {
    // 图片移除
    handleIntroduceRemove(file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.images = ''
    },
    handlePicSuccess(res, file) {
      console.log(res, '上传');
      this.images = res.result.url
    },
    handleIntroduceUploadHide(file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    // 模拟异步获取建议列表
    async fetchSuggestions(queryString, callback) {
      if (queryString) {
        // const data = ["Option1", "Option2", "Option3", "Option4"].filter((item) =>
        //   item.toLowerCase().includes(queryString.toLowerCase())
        // );

        const res = await this.$api.searchGroup({
          groupName: queryString
        })
        const arr = []
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              arr.push({
                value: `${item.groupName}(${item.groupMasterName})`,
                id: item.groupAccount

              })
            })
          }
        }
        callback(arr);
      } else {
        callback([]);
      }
    },
    // 处理选择后的逻辑
    handleSelect(item) {
      console.log(item);
      if (!this.selectedItems.includes(item)) {
        this.selectedItems.push(item);
      }
      this.specificGroup = ""; // 清空输入框
    },
    // 移除已选择的选项
    removeItem(index) {
      this.selectedItems.splice(index, 1);
    },
    // 终止按钮的点击事件处理
    async handleStop(e) {
      let res = await this.$api.stopGroupMessage({ taskId: e.taskId })
      if (res.status.code == 0) {
        this.$message.success('操作成功')
        this.getList()
      }
      // 终止逻辑
      console.log("终止操作");
    },

    // 撤回按钮的点击事件处理
    async handleRevoke(e) {
      let res = await this.$api.groupMessageRecall({ taskId: e.taskId })
      if (res.status.code == 0) {
        this.$message.success('操作成功')
        this.getList()
      }
      // 撤回逻辑
      console.log("撤回操作");
    },
    // 获取所有选中的群类型标签
    getGroupLabels(sendType) {
      return this.groupTypes
        .filter(type => this.isSendTypeIncluded(sendType, type.value)) // 过滤出被选中的群类型
        .map(type => type.label) // 获取标签文本
        .join(', '); // 用逗号连接选中的群类型标签
    },
    // 判断 sendType 是否包含某个值
    isSendTypeIncluded(sendType, value) {
      // if (!sendType) return false;
      const types = sendType.includes(',') ? sendType.split(',') : [sendType]; // 如果有逗号，则分割，否则将其作为单一值
      return types.includes(value); // 判断 value 是否在 types 数组中
    },
    audio_start() {
      this.recorder = new Recorder({
        sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
        sampleRate: 48000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        numChannels: 1, // 声道，支持 1 或 2， 默认是1
        // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
      });
      Recorder.getPermission().then(() => {
        this.recorder.start().then(() => {
          this.audioType = true
          this.$message.success('已开启录音')
        }, (error) => {
          // 出错了
          console.log(`${error.name} : ${error.message}`);
          this.$notify.error({
            title: '错误',
            message: error.message
          });
        });
      }, (error) => {
        this.$notify.error({
          title: '错误',
          message: '请授权浏览器录音权限'
        });
        console.log(`${error.name} : ${error.message}`);
      });
    },
    audio_stop() {
      if (this.audioType) {
        this.$message.success('已停止录音')
        this.audioNum = this.recorder.duration.toFixed(0)
        this.recorder.pause()
        this.isGG = true
        console.log(this.audioSize)
      } else {
        this.$notify.error({
          title: '错误',
          message: '请开始录音后操作'
        });
      }
    },
    audio_play() {
      if (this.audioType) {
        this.isAudioPlpy = !this.isAudioPlpy
        if (this.isAudioPlpy) {
          this.recorder.play();
          this.isGG = false
          setTimeout(() => {
            this.isAudioPlpy = false
          }, this.audioNum * 1000)
        } else {
          this.recorder.pause();
        }
      } else {
        this.$notify.error({
          title: '错误',
          message: '请开始录音后操作'
        });
      }
    },
    audio_destroy() {
      if (this.audioType) {
        this.recorder.destroy().then(() => {
          this.audioNum = ""
          this.file = ""
        });
      } else {
        this.$notify.error({
          title: '错误',
          message: '请开始录音后操作'
        });
      }
    },
    reUpload() {
      // this.$message("重新上传图片...");
      // 重传逻辑
      this.fileListImg = []; // 清空文件列表
      this.images = null; // 清空预览图片

      // 打开上传文件对话框
      this.$refs.uploadRef.$el.click();
    },
    async uploadAudio() {
      if (this.isGG) {
        this.audioSize = this.recorder.getWAVBlob().size
        var wavBlob = this.recorder.getWAVBlob();
        // 创建一个formData对象
        var formData = new FormData()
        // 此处获取到blob对象后需要设置fileName满足当前项目上传需求，其它项目可直接传把blob作为file塞入formData
        const newbolb = new Blob([wavBlob], {
          type: 'audio/wav'
        })
        //获取当时时间戳作为文件名
        const fileOfBlob = new File([newbolb], new Date().getTime() + '.wav')
        formData.append('file', fileOfBlob)
        const {
          result
        } = await uploadExcelToOss(formData)
        this.audioUrl = result.url
        this.$message.success('上传成功')
        this.isGG = true
        // this.submitPgcSystemMsg()
      } else {
        this.$message.error('请开始录音后结束录音再一键发言')
      }
    },
    async confirmSend() {
      if (this.selectedType == 'audio') {
        await this.uploadAudio()
      }
      const payload = {
        ...(this.selectedGroups != '特定群' ? { groupType: this.selectedGroups } : {}),
        // groupType: (this.selectedGroups == '特定群' ? '' : this.selectedGroups),
        message: this.selectedType == 'text' ? this.textContent : this.selectedType == 'audio' ? this.audioUrl : this.images,
        messageType: this.selectedType,
        // messageGroupAccount: this.selectedType = '特定群' ? '' : this.selectedGroups.join(','),
        ...(this.selectedGroups == '特定群' ? { messageGroupAccount: this.selectedItems.map(item => item.id).join(",") } : {}),

        // specificGroup: this.specificGroup || null,
        ...(this.sendTime == 'scheduled' ? { startTime: this.formatTime(this.scheduledTime) } : {}),
        timing: this.sendTime === 'scheduled' ? '1' : '0'
      };
      let res = await this.$api.addGroupMessage(payload);
      if (res.status.code === 0) {
        this.$message.success("发送成功");
        this.getList()
        this.SenddialogVisible = false; // 关闭弹窗
      }
      console.log("发送消息数据：", payload);
      console.log(this.selectedItems, '群');

    },
    formatTime(e) {
      const date = new Date(e); // 创建 Date 对象
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始，补零
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data
      this.getList(true)
    },
    async getList(isInit) {
      this.loading = true
      // this.query.messageGroupAccount = '@TGS#_@TGS#cFKJSIIM62CO(随便取一个名字吧藏家群2群)' // 去除右边括号以及括号里面内容
      if (this.query.messageGroupAccount) {
        this.query.messageGroupAccount = this.query.messageGroupAccount.replace(/\(.*?\)$/, "")
      }
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const {
        status,
        result
      } = await this.$api.searchGroupMessage(params)
      if (status.code === 0) {
        this.loading = false

        this.tableData = result.list
        this.tableData.forEach(item => {
          if (item.groupAccountInfo) {
            item.groupAccountInfo = JSON.parse(item.groupAccountInfo)
          }
        })
        // .map(item => {
        //   item.chatContent = JSON.parse(item.chatContent)[0]
        //   return item
        // })
        // console.log(this.tableData)
        this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize
        // this.page.pageCount = result.pageCount
      }
      console.log(this.page)
    },
    async getMemberInfo(memberId) {
      const {
        result
      } = await this.$api.chatRecordMemberInfo({
        memberId
      })
      this.data = result
      this.dialogVisible = true
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    currentChangeSize(value) {
      this.page.pageSize = value
      this.getList()
    },
    // 发言
    async chatRecords() {
      console.log(this.speakData)
      const {
        groupAccount: groupId,
        memberId
      } = this.curInfo
      const data = {
        ...this.speakData,
        groupId,
        memberId
      }
      const {
        status
      } = await this.$api.chatRecords(data)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.speakDialogVisible = false
      }
    },
    // 操作列表
    async operateChatRecord() {
      const {
        groupAccount: groupId,
        memberId,
        attribute,
        chatId: chatRecordId
      } = this.curInfo
      const data = {
        attribute,
        chatRecordId,
        groupId,
        memberId,
        operate: this.actionSelected
      }
      const {
        status
      } = await this.$api.operateChatRecord(data)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.actionDialogVisible = false
      }
    },
    openActionDialogVisible(item) {
      this.actionDialogVisible = true
      this.curInfo = item
    },
    openSpeakDialogVisible(item) {
      this.speakDialogVisible = true
      this.curInfo = item
    },
    keyWordHighlight(rawText, keyWord) {
      if (!rawText || !keyWord) return ''
      return keyWord.split(',').reduce(
        (pre, cur) => pre.replaceAll(cur, `<span style="color: red;">${cur}</span>`),
        rawText
      )
    },
    openChatList(item) {
      this.query.nickName = item.nickname
      // this.query.memberId=item.memberId
      console.log(this.query)
      this.page.pageNum = 1
      this.getList(true)
    },
    /**
     * 举报者信息查看
     * @param row
     */
    async reportNameDetail(row) {
      this.infoDialogData = {}
      this.infoDialogVisible = true
      this.loading = true
      const {
        result
      } = await this.$api.ownerInfo({
        groupId: row.groupAccount,
        nickname: row.nickname
      })
      this.loading = false
      this.infoDialogData = result
    },
    nav_shop() {
      this.infoDialogVisible = false
      this.$router.push({
        name: 'indexOperation',
        query: {
          nickname: this.infoDialogData.nickname
        }
      })
    },
    async submitJoinInGroup() {
      this.numLength = this.multipleSelection.length
      console.log(this.multipleSelection)
      if (this.numLength === 0) {
        this.$message.error('请选择你要发言的人')
      } else if (this.numLength > 1) {
        this.$message.error('暂不支持多人同时发言，请勿选择多个水军')
      } else {
        const res = await this.$api.groupSystemMsg({
          groupId: this.groupId,
          content: this.textarea_speak,
          speakMemberId: this.multipleSelection[0].memberId,
          speakType: 1
        })
        if (res.status.code === 0) {
          this.$message.success('发言成功')
          this.getList()
        } else {
          this.$message.error(res.status.msg)
        }
      }
    },
    async getUserList() {
      const res = await this.$api.groupMemberInfo({
        groupId: this.groupId,
        member: 4,
        pageNum: 1,
        pageSize: 50,
        nickname: this.formScreen.nickname,
        // email: this.formScreen.email,
        address: this.formScreen.contractAddress,
        // id: this.formScreen.id,
      })
      this.userData = res.result.list
    },
    open(item) {
      this.groupId = item.groupAccount
      this.getUserList()
      this.isJoinQun = true
    },
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.yuyin {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: space-between;
  width: 2em;
  margin-right: 20px;

  span {
    width: 0.3em;
    height: 1em;
    background-color: #145358;
  }

  span:nth-of-type(1) {
    transform: scaleY(1);
  }

  span:nth-of-type(2) {
    transform: scaleY(1.25);
  }

  span:nth-of-type(3) {
    transform: scaleY(1.5);
  }

  span:nth-of-type(4) {
    transform: scaleY(2);
  }

  &.active {
    span:nth-of-type(1) {
      animation: grow 1s -0.15s ease-in-out infinite;
    }

    span:nth-of-type(2) {
      animation: grow 1s -0.3s ease-in-out infinite;
    }

    span:nth-of-type(3) {
      animation: grow 1s -0.45s ease-in-out infinite;
    }

    span:nth-of-type(4) {
      animation: grow 1s -0.65s ease-in-out infinite;
    }
  }

  @keyframes grow {

    0%,
    100% {
      transform: scaleY(1);
    }

    50% {
      transform: scaleY(2);
    }
  }
}

.view_yu {
  background-color: #f4f4f4;
  margin: 10px 0;
  border-radius: 6px;
  height: 45px;
  width: 260px;
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
  align-items: center;
  cursor: pointer;
}

.blue {
  color: #409EFF;
}

.content_dialog {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;

  .left_div {
    width: 500px;
    margin-right: 20px;
    border-radius: 6px;
    border: 2px solid #ccc;
    min-height: 226px;
    padding: 10px;
  }

  .right_div {
    width: 700px;
  }
}

::v-deep .success-row {
  background-color: #ff9999;
}
</style>