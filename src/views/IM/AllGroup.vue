<template>
    <d2-container class="page">
        <div class="header">
            <el-form size="mini" :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
                <el-form-item :label="item.label" v-for="(item, index) in formList" :key="index">
                    <el-select v-model="formInline[item.prop]" v-if="item.type === 'select'">
                        <el-option :label="v.label" :value="v.value" v-for="(v, i) in item.options"
                            :key="i"></el-option>
                    </el-select>
                    <el-date-picker v-else-if="item.type === 'dateRange'" v-model="formInline[item.prop]"
                        value-format="yyyy-MM-dd HH:mm:ss" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                    <el-input v-model="formInline[item.prop]" v-else></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="medium" type="" @click="onSearch">查询</el-button>
                    <el-button size="medium" @click="onReset">重置</el-button>
                    <!-- <el-button size="medium" @click="jinyanFunc">批量禁言</el-button>
                    <el-button size="medium" type="primary" @click="jiechuFunc">批量解除禁言</el-button>
                    <el-button size="medium" type="primary" @click="isPgc = true">全PGC群发言</el-button>
                    <el-button size="medium" type="primary" @click="removeFunc">批量解散群聊</el-button> -->
                </el-form-item>
            </el-form>
        </div>
        <div class="table">
            <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="40">
                </el-table-column>
                <!-- </el-table-column> -->
                <el-table-column v-for="(item, index) in tableList" :prop="item.prop" :label="item.label" align="center"
                    :key="index" :width="item.width || 100">
                    <template slot-scope="scope">
                        <!-- 群头像 -->
                        <template v-if="item.prop === 'groupMasterAvatar'">
                            <el-image style="width: 50px; height: 50px" :src="scope.row[item.prop]"
                                :preview-src-list="[scope.row[item.prop]]">
                            </el-image>
                        </template>

                        <!-- 管理员 -->
                        <template v-if="item.prop === 'groupChatManagers'">
                            <div v-if="!!scope.row.groupChatManagers">
                                <!-- 昵称+ 头像 -->


                                <div v-for="(item, index) in scope.row[item.prop].slice(0, 3)" :key="index"
                                    style="display: flex;align-items: center;justify-content: space-around;">
                                    <span v-if="item.nickName">{{ item.nickName }}</span>
                                    <span v-if="item.avatar">
                                        <img :src="item.avatar"
                                            style="width: 30px;height: 30px;border-radius: 50%;margin-right:5px;" />
                                    </span>
                                </div>
                                <el-button type="text" v-if="scope.row[item.prop].length > 3"
                                    @click="viewAll(scope.row[item.prop])">查看</el-button>
                            </div>
                            <!-- <el-button type="text" @click="showGroupMaster(scope.row)">{{scope.row[item.prop]}}</el-button> -->
                        </template>

                        <!-- 群成员 -->
                        <template v-if="item.prop === 'groupChatMembers'">
                            <div v-if="!!scope.row.groupChatMembers">
                                <!-- 昵称+ 头像 -->
                                <span v-for="(item, index) in scope.row[item.prop].slice(0, 3)" :key="index"
                                    style="display: flex;align-items: center;justify-content: space-around;">
                                    <span v-if="item.nickName">{{ item.nickName }}</span>
                                    <span v-if="item.avatar">
                                        <img :src="item.avatar"
                                            style="width: 30px;height: 30px;border-radius: 50%;margin-right:5px;" />
                                    </span>
                                </span>
                                <el-button type="text" v-if="scope.row[item.prop].length > 3"
                                    @click="viewAll(scope.row[item.prop])">查看</el-button>
                            </div>
                        </template>
                        <!-- 群主-->
                        <template v-else-if="item.prop === 'groupMasterName'">
                            <div style="display: flex;align-items: center;justify-content: space-around;">
                                {{ scope.row[item.prop] }}
                                <el-image style="width: 50px; height: 50px" :src="scope.row.groupMasterAvatar"
                                    :preview-src-list="[scope.row.groupMasterAvatar]">
                                </el-image>
                            </div>

                        </template>
                        <!-- 群属性-->
                        <template v-else-if="item.prop === 'groupType'">
                            <el-tag v-if="scope.row[item.prop] == '0'">藏家群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '1'">DAO群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '2'">萌新群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '3'">萌新DAO群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '4'">直播群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '5'">师门群</el-tag>
                            <el-tag v-if="scope.row[item.prop] == '6'">讨论群</el-tag>
                        </template>
                        <!-- DAO成员／群成员 -->
                        <template v-else-if="item.prop === 'member'">
                            <el-button type="text" @click="memberList(scope.row)">
                                {{ scope.row[item.prop] }}
                            </el-button>
                        </template>
                        <!-- 群公告 -->
                        <template v-else-if="item.prop === 'noticeRecords'">
                            <el-button type="text" @click="groupNoticeRecord(scope.row)">查看</el-button>
                        </template>
                        <!-- 群聊记录 -->
                        <template v-else-if="item.prop === 'record'">
                            <el-button type="text" @click="chatHistory(scope.row)">查看</el-button>
                        </template>
                        <!-- 群聊状态 -->
                        <template v-else-if="item.prop === 'status'">
                            <el-tag>{{ statusDict[scope.row[item.prop]] }}</el-tag>
                        </template>
                        <!-- 未入群用户-->
                        <template v-else-if="item.prop === 'notInGroupNum'">
                            <el-button type="text" @click="notInGroupList(scope.row)">{{
                                scope.row[item.prop]
                            }}
                            </el-button>
                        </template>
                        <span v-else>{{ scope.row[item.prop] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="scope">
                        <el-button type="text" @click="onAction(scope.row)">操作</el-button>
                        <el-button type="text" @click="sendMsg(scope.row)">发言</el-button>
                        <el-button type="text" @click="edit(scope.row)">修改类型</el-button>
                        <el-button type="text" @click="editWeight(scope.row)">推荐权重</el-button>

                        <!-- <el-button type="text" @click="uidOpen(scope.row)">一键加群</el-button> -->
                        <!-- <el-button type="text" @click="openJoin(scope.row)"
                            v-show="scope.row.type == '3' || scope.row.type == '2' || scope.row.type == '5' || scope.row.type == '6'">加群</el-button> -->
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="footer">
            <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
                <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
                    :page-size="pageSize" :current-page="pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
                    style="padding: 20px; background-color: #ffffff" @current-change="handleCurrentChange"
                    @size-change="xuanzeSize">
                </el-pagination>
            </div>
        </div>
        <!--    用户信息-->
        <el-dialog title="" :visible.sync="infoDialogVisible" width="30%">
            <el-form ref="form" label-width="200px">
                <el-form-item :label="item.label" v-for="(item, index) in infoDialogList" :key="index">
                    {{ infoDialogData[item.prop] }}
                </el-form-item>
            </el-form>
        </el-dialog>
        <!--    操作-->
        <el-dialog title="群聊操作" :visible.sync="actionDialogVisible" width="30%">
            <div class="dialog-action">
                <div>选择确认后，在客户端将以伯德护卫机器人身份对群进行全员禁言（包含群主或管理）以及对该群进行解散。</div>
                <el-radio-group v-model="operate">
                    <el-radio :label="0">禁言群</el-radio>
                    <el-radio :label="1">解散群</el-radio>
                    <el-radio :label="3">解除群禁言</el-radio>
                </el-radio-group>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="actionDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="actionConfirm1">确 定</el-button>
            </span>
        </el-dialog>
        <!--    发言-->
        <el-dialog title="" :visible.sync="msgDialogVisible" width="30%">
            <div class="msg">
                <div>
                    输入文案并提交发送后，在客户端该群中将以伯德护卫机器人身份进行发言，文字和图片一次性提交将以两条信息进行发送。
                </div>
                <el-input type="textarea" rows="4" v-model="msgInfo.content"></el-input>
                <el-upload :action="action" :headers="token" list-type="picture-card" :file-list="fileList"
                    :on-success="handleSuccess" :on-remove="handleRemove">
                    <i class="el-icon-plus"></i>
                    <div slot="tip" class="el-upload__tip">
                        <div>只能上传JPG/PNG/GIF格式文件</div>
                    </div>
                </el-upload>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="msgDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="confirmSendMsg">发 送</el-button>
            </span>
        </el-dialog>

        <el-dialog title="加入群聊" :visible.sync="isJoinQun" width="80%">
            <el-form :inline="true" :model="formScreen" class="demo-form-inline" size="mini">
                <el-form-item label="昵称">
                    <el-input v-model="formScreen.nickname" placeholder="请输入昵称"></el-input>
                </el-form-item>
                <el-form-item label="用户ID">
                    <el-input v-model="formScreen.id" placeholder="请输入用户ID"></el-input>
                </el-form-item>
                <el-form-item label="邮箱">
                    <el-input v-model="formScreen.email" placeholder="请输入邮箱" clearable></el-input>
                </el-form-item>
                <el-form-item label="contractAddress">
                    <el-input v-model="formScreen.contractAddress" placeholder="请输入contractAddress" clearable>
                    </el-input>
                </el-form-item>
                <el-form-item label="用户类型">
                    <el-select v-model="formScreen.customerType" multiple placeholder="用户类型">
                        <el-option label="0" value="0"></el-option>
                        <el-option label="4.1" value="1"></el-option>
                        <el-option label="4.2" value="2"></el-option>
                        <el-option label="4.3" value="3"></el-option>
                        <el-option label="4.4" value="4"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getUserList">查询</el-button>
                    <el-button type="primary" @click="submitJoinInGroup()">一键加群</el-button>
                </el-form-item>
            </el-form>
            <common-table :table-schema="userColumn" :table-data="userData" :multipleSelection.sync="multipleSelection"
                :showSelection="true">
            </common-table>
            <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
                <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="totalScreen"
                    :page-size="pageSizeScreen" :current-page="pageScreen" :page-sizes="[30, 50, 100, 200, 500, 1000]"
                    style="padding: 20px; background-color: #ffffff" @current-change="xuanzeScreen"
                    @size-change="xuanzeSizeScreen">
                </el-pagination>
            </div>
        </el-dialog>
        <el-dialog title="全PGC群发言" :visible.sync="isPgc" width="450px">
            <el-form :model="formScreen" class="demo-form-inline" size="mini">
                <el-form-item label="发言类型">
                    <el-radio v-model="contentType" label="1" border>发送文字</el-radio>
                    <el-radio v-model="contentType" label="2" border>发送语音</el-radio>
                </el-form-item>
                <el-form-item v-if="contentType == 1">
                    <el-input type="textarea" autosize placeholder="请输入发言内容" v-model="textareaText">
                    </el-input>
                </el-form-item>
                <el-form-item v-if="contentType == 2">
                    <div class="view_yu " @click="audio_play">
                        <div class="yuyin " :class="{ 'active': isAudioPlpy }" v-if="audioNum">
                            <span></span>
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div v-if="audioNum">{{ audioNum }}s 点击播放</div>
                        <div v-else>请先开始路录音</div>
                    </div>
                    <el-button @click="audio_start()">开始录音</el-button>
                    <el-button @click="audio_stop()">结束录音</el-button>
                    <el-button @click="audio_destroy()">重录</el-button>
                    <!-- <el-button @click="audio_play()">播放录音</el-button> -->
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="isPgc = false">取消</el-button>
                    <el-button type="primary" @click="submitPgc()">一键发言</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog title="uid批量加群" :visible.sync="isUidOpen" width="450px">
            <el-form class="demo-form-inline" size="mini">
                <el-form-item>
                    <el-input type="textarea" autosize placeholder="请输入uid ,号隔开" v-model="invitedUserIdStr">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="isUidOpen = false">取消</el-button>
                    <el-button type="primary" @click="uidJoinInGroup()">一键确认加群</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 群类型 Dialog -->
        <el-dialog title="设置群类型" :visible.sync="groupTypeShow" width="30%" @close="handleClose('groupTypeShow')">
            <el-form :model="groupTypeForm">
                <el-form-item label="群类型">
                    <el-select v-model="groupTypeForm.groupType" placeholder="请选择群类型">
                        <el-option v-for="option in groupTypeOptions" :key="option.value" :label="option.label"
                            :value="option.value">
                            {{ option.label }}
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose('groupTypeShow')">取消</el-button>
                <el-button type="primary" @click="handleConfirm('groupTypeForm')">确认</el-button>
            </span>
        </el-dialog>

        <!-- 权重 Dialog -->
        <el-dialog title="设置权重" :visible.sync="weightShow" width="30%" @close="handleClose('weightShow')">
            <el-form :model="weightForm">
                <el-form-item label="权重值">
                    <el-input v-model="weightForm.weight" placeholder="请输入权重值"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose('weightShow')">取消</el-button>
                <el-button type="primary" @click="handleConfirm('weightForm')">确认</el-button>
            </span>
        </el-dialog>


        <!-- 成员信息 -->
        <el-dialog title="全部成员信息" :visible.sync="dialogVisible" width="50%">
            <div>
                <div v-for="(member, index) in fullMembers" :key="index"
                    style="display: flex;align-items: center;margin-bottom: 10px;">
                    <span v-if="member.nickName" style="margin-right: 10px;">{{ member.nickName }}</span>
                    <span v-if="member.avatar">
                        <img :src="member.avatar"
                            style="width: 40px;height: 40px;border-radius: 50%;margin-right:5px;" />
                    </span>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import Recorder from 'js-audio-recorder';
import {
    mapActions
} from 'vuex'
import {
    uploadExcelToOss
} from '@/api/ossCenter'
export default {
    name: 'ChatList',
    data() {
        return {
            dialogVisible: false, // 控制弹窗显示
            fullMembers: [], // 存储全部成员信息
            groupTypeOptions: [
                { value: "0", label: "藏家群" },
                { value: "1", label: "DAO群" },
                { value: "2", label: "萌新群" },
                { value: "3", label: "萌新DAO群" },
                { value: "4", label: "直播群" },
                { value: "5", label: "师门群" },
                { value: "6", label: "讨论群" },
            ],
            formInline: {},
            formList: [
                {
                    label: '群聊名称：',
                    prop: 'groupName',
                    type: 'input'
                },
                // {
                //     label: '群聊状态：',
                //     prop: 'status',
                //     type: 'select',
                //     options: [{
                //         label: '正常',
                //         value: '1'
                //     },
                //     {
                //         label: '禁言',
                //         value: '2'
                //     },
                //     {
                //         label: '解散',
                //         value: '3'
                //     },
                //     {
                //         label: '被群主禁言',
                //         value: '5'
                //     }
                //     ]
                // },
                {
                    label: '排序规则：',
                    prop: 'sorRule',
                    type: 'select',
                    options: [{
                        label: '升序',
                        value: 1
                    },
                    {
                        label: '降序',
                        value: 0
                    },
                    ]
                },
                // {
                //     label: '群聊属性：',
                //     prop: 'groupType',
                //     type: 'select',
                //     options: [{
                //         label: '全部',
                //         value: ''
                //     },
                //     {
                //         label: '藏家群',
                //         value: '0'
                //     },
                //     {
                //         label: 'DAO群',
                //         value: '1'
                //     },
                //     {
                //         label: '萌新群',
                //         value: '2'
                //     },
                //     {
                //         label: '萌新DAO群',
                //         value: '3'
                //     },
                //     {
                //         label: '直播群',
                //         value: '4'
                //     },
                //     {
                //         label: '师门群',
                //         value: '5'
                //     },
                //     {
                //         label: '讨论群',
                //         value: '6'
                //     }
                //     ]
                // },
                {
                    label: '群主昵称：',
                    prop: 'groupMasterName',
                    type: 'input'
                },
                // {
                //     label: '创建时间：',
                //     prop: 'dateRange',
                //     type: 'dateRange',
                // },
                {
                    label: '群排序方式：',
                    prop: 'sortParam',
                    type: 'select',
                    options: [{
                        label: '按照最近24小时发言人数',
                        value: 0
                    },
                    {
                        label: '照最近24发言数',
                        value: 1
                    },
                    {
                        label: '按照群成员人数',
                        value: 2
                    },
                    {
                        label: '推荐权重',
                        value: 3
                    },

                    ]
                }
            ],
            tableData: [],
            tableList: [
                {
                    prop: 'groupName',
                    label: '群名称',
                    width: '200'
                },
                {
                    prop: 'groupType',
                    label: '群属性',
                    width: '100'
                },
                {
                    prop: 'groupMasterName',
                    label: '群主',
                    width: '200'
                },
                {
                    prop: 'groupChatManagers',
                    label: '管理员',
                    width: '200'
                },
                {
                    prop: 'groupMemberNum',
                    label: '群成员数',
                    width: '150'
                },
                {
                    prop: 'groupChatMembers',
                    label: '群成员',
                    width: '200'
                },
                {
                    prop: 'speakNumLimitLast24Hour',
                    label: '近24小时发言数',
                    width: '80'
                },
                {
                    prop: 'speakPeopleNumLimitLast24Hour',
                    label: '近24小时发言人数',
                    width: '80'
                },

                // {
                //     prop: 'groupMasterAvatar',
                //     label: '群头像',
                //     width: '130'
                // },


                // {
                //     prop: 'noticeRecords',
                //     label: '群公告',
                //     width: '80'
                // },
                {
                    prop: 'record',
                    label: '聊天记录',
                    width: '80'
                },
                {
                    prop: 'pushWeight',
                    label: '推荐权重',
                    width: '80'
                },
                // {
                //     prop: 'createAt',
                //     label: '创建时间',
                //     width: '130'
                // },
                // {
                //     prop: 'status',
                //     label: '群聊状态',
                //     width: '80'
                // },
                // {
                //     prop: 'groupId',
                //     label: '群聊ID',
                //     width: '200'
                // },
            ],
            pageSize: 20,
            pageNum: 1,
            total: 0,
            statusDict: {
                0: '未知',
                1: '正常',
                2: '禁言',
                3: '解散',
                5: '被群主禁言'
            },
            infoDialogVisible: false,
            infoDialogList: [{
                label: '昵称：',
                prop: 'nickname'
            },
            {
                label: 'contract_address：',
                prop: 'contractAddress'
            },
            {
                label: '作品：',
                prop: 'goodsNum'
            },
            {
                label: '藏品：',
                prop: 'collections'
            },
            {
                label: '管理的藏家群／DAO群：',
                prop: 'manageGroup'
            },
            {
                label: '加入的群：',
                prop: 'joinGroup'
            }
            ],
            infoDialogData: {},
            actionDialogVisible: false,
            operate: 0,
            groupId: '',
            msgDialogVisible: false,
            msgInfo: {},
            fileList: [],
            action: process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/uploadImage',
            token: {
                AdminAuthorization: localStorage.getItem('usertoken')
            }, // 设置上传的请求头部
            isJoinQun: false,
            userColumn: [{
                label: '用户id',
                field: 'id'
            },
            {
                label: '用户昵称',
                field: 'nickname'
            },
            {
                label: '用户地址',
                field: 'contractAddress'
            },
            {
                label: '用户邮箱',
                field: 'email'
            }
            ],
            keyword: "",
            formScreen: {
                email: '',
                nickname: '',
                id: '',
                contractAddress: '',
                customerType: ''
            },
            multipleSelection: [],
            userData: "",
            groupId: "",
            formInline: {
                sortParam: 1,
                sorRule: 1
            },
            groupIdList: [],
            isPgc: false,
            contentType: "1",
            textareaText: "",
            audioNum: "",
            isAudioPlpy: false,
            file: "",
            audioType: false,
            audioUrl: '',
            audioSize: "",
            isGG: false,
            totalScreen: 1,
            pageScreen: 1,
            pageSizeScreen: 30,
            isUidOpen: false,
            invitedUserIdStr: "",
            groupTypeForm: {
                groupType: "", // 群类型值
            },
            weightForm: {
                weight: "", // 权重值
            },
            groupTypeShow: false, // 控制群类型弹窗显示
            weightShow: false, // 控制权重弹窗显示
            newgroupId: ''
        }
    },
    mounted() {
        this.getList()
    },
    components: {
        CommonTable,
    },
    watch: {
        msgDialogVisible(val) {
            if (!val) {
                this.groupId = ''
                this.msgInfo = {
                    content: null,
                    photos: null
                }
            }
        }
    },
    methods: {
        viewAll(members) {
            this.fullMembers = members; // 将成员信息赋值给 fullMembers
            this.dialogVisible = true; // 打开弹窗
        },
        handleClose(dialog) {
            this[dialog] = false; // 关闭弹窗
        },
        async handleConfirm(formName) {
            let data
            if (formName == 'weightForm') {
                data = {
                    groupAccount: this.newgroupId,
                    groupPushWeight: this.weightForm.weight - 0
                }
            } else {
                data = {
                    groupAccount: this.newgroupId,
                    groupType: this.groupTypeForm.groupType - 0
                }
            }
            let res = await this.$api.groupChatUpdateType(data);
            if (res.status.code == 0) {
                this.$message.success('更新成功')
                this.getList()
            }
            console.log("确认数据：", this[formName]);
            this[formName === "groupTypeForm" ? "groupTypeShow" : "weightShow"] = false;
        },
        // 类型
        edit(e) {
            this.groupTypeForm.groupType = e.groupType
            this.newgroupId = e.groupAccount
            this.groupTypeShow = true
        },
        // 权重
        editWeight(e) {
            this.newgroupId = e.groupAccount

            this.weightForm.weight = e.pushWeight
            this.weightShow = true

        },
        ...mapActions('d2admin/page', ['close']),
        /**
         * 获取列表
         * @method
         */
        async getList() {
            const {
                dateRange
            } = this.formInline
            if (dateRange) {
                this.formInline.beginTime = dateRange[0] + '.000'
                this.formInline.endTime = dateRange[1] + '.000'
            }
            const {
                result: {
                    list,
                    totalCount
                }
            } = await this.$api.groupList({
                ...this.formInline,
                pageNum: this.pageNum,
                pageSize: this.pageSize
            })
            this.tableData = list
            this.total = totalCount
        },
        /**
         * 搜索
         * @method
         */
        onSearch() {
            this.pageNum = 1
            this.getList()
        },
        /**
         * 重置
         * @method
         */
        onReset() {
            this.formInline = {}
            this.pageNum = 1
            this.getList()
        },
        /**
         * 当前页发生变化时会触发该事件
         * @method
         * @param val {Number} 当前页码
         */
        handleCurrentChange(val) {
            this.pageNum = val
            this.getList()
        },
        xuanzeSize(val) {
            this.pageSize = val
            this.getList()
        },

        /**
         *群主信息
         * @method
         * @param row
         */
        async ownerInfo(row) {
            this.infoDialogData = {}
            this.infoDialogVisible = true
            const {
                result
            } = await this.$api.ownerInfo({
                groupId: row.groupId
            })
            this.infoDialogData = result
        },
        /**
         * 点击操作
         * @method
         */
        onAction(row) {
            this.groupId = row.groupAccount
            this.actionDialogVisible = true
        },
        // 操作后点击确定
        async actionConfirm1() {
            const {
                groupId,
                operate
            } = this
            await this.$api.groupOperate({
                groupId,
                operate
            })
            this.$message.success('操作成功')
            this.actionDialogVisible = false
            this.groupId = ''
            await this.getList()
        },
        /**
         * 群成员列表
         * @param row {Object} 当前行数据
         */
        memberList(row) {
            const {
                fullPath
            } = this.$route
            console.log(this.$route)
            this.close({
                tagName: 'MemberList'
            })
            this.$router.push({
                name: 'MemberList',
                query: {
                    groupId: row.groupId
                }
            })
        },
        /**
         * 发言记录
         * @param row {Object} 当前行数据
         */
        chatHistory(row) {
            this.$router.push({
                name: 'ChatRecord',
                query: {
                    groupAccount: row.groupAccount
                }
            })
        },
        /**
         * 未入群用户
         * @param row {Object} 当前行数据
         */
        notInGroupList(row) {
            this.$router.push({
                name: 'NotInGroupList',
                query: {
                    groupId: row.groupId
                }
            })
        },
        /**
         * 未入群用户
         * @param row {Object} 当前行数据
         */
        groupNoticeRecord(row) {
            this.$router.push({
                name: 'GroupNoticeRecord',
                query: {
                    groupId: row.groupId
                }
            })
        },
        /**
         * 发言
         * @param row
         */
        sendMsg(row) {
            this.groupId = row.groupAccount
            this.msgDialogVisible = true
        },
        /**
         * 上传图片成功回调
         * @param res {Object} 图片信息
         * @param file {Object} 图片信息
         */
        handleSuccess(res, file) {
            if (res.status.code === 0) {
                this.msgInfo.photos = res.result.url
                this.fileList = [file]
            } else {
                this.$message.error(res.status.msg)
                this.fileList = []
            }
        },
        /**
         * 删除图片
         * @method
         */
        handleRemove() {
            delete this.msgInfo.photos
        },
        /**
         * 发送消息
         * @method
         */
        async confirmSendMsg() {
            const {
                groupId,
                msgInfo: {
                    content,
                    photos
                }
            } = this
            await this.$api.groupSystemMsg({
                groupId: groupId,
                content,
                photos
            })
            this.msgDialogVisible = false
            this.msgInfo = {}
            this.fileList = []
            this.$message.success('发送成功')
        },
        // 查询列表
        async getUserList() {
            let customerType = []
            customerType = JSON.stringify(this.formScreen.customerType)
            const res = await this.$api.getUserList({
                pageNum: this.pageScreen,
                pageSize: this.pageSizeScreen,
                nickname: this.formScreen.nickname,
                email: this.formScreen.email,
                contractAddress: this.formScreen.contractAddress,
                id: this.formScreen.id,
                customerType
            })
            this.userData = res.result.list
            this.totalScreen = res.result.totalCount
        },
        openJoin(item) {
            this.isJoinQun = true,
                this.groupId = item.groupId
        },
        async submitJoinInGroup() {
            this.numLength = this.multipleSelection.length
            if (this.numLength === 0) {
                this.$message.error('请选择你要加群的人')
            } else {
                let invitedUserIdStr = []
                this.multipleSelection.forEach((item) => {
                    invitedUserIdStr.push(item.id)
                })
                console.log(invitedUserIdStr)
                const res = await this.$api.joinToGroup({
                    groupId: this.groupId,
                    invitedUserIdStr: JSON.stringify(invitedUserIdStr)
                })
                if (res.status.code === 0) {
                    this.$message.success('加入成功')
                } else {
                    this.$message.error(res.status.msg)
                }
            }
        },
        handleSelectionChange(val) {
            // this.idList = val.map(item => item.id)
            // this.tidList = val.map(item => item.tid)
            this.groupIdList = val
        },
        jinyanFunc() {
            this.groupIdList.forEach((item, index) => {
                console.log(item, index)
                setTimeout(() => {
                    this.actionConfirm(item.groupId, 0)
                }, 500)
            })
        },
        jiechuFunc() {
            this.groupIdList.forEach((item, index) => {
                console.log(item, index)
                setTimeout(() => {
                    this.actionConfirm(item.groupId, 3)
                }, 500)
            })
        },
        removeFunc() {
            this.groupIdList.forEach((item, index) => {
                console.log(item, index)
                setTimeout(() => {
                    this.actionConfirm(item.groupId, 1)
                }, 500)
            })
        },
        async actionConfirm(groupId, operate) {
            await this.$api.groupOperate({
                groupId,
                operate
            })
            if (operate == 0) {
                this.$message.success('禁言成功')
            } else if (operate == 1) {
                this.$message.success('解散群成功')
            } else {
                this.$message.success('解除禁言成功')
            }
            this.actionDialogVisible = false
            this.groupId = ''
            // await this.getList()
        },
        audio_start() {
            this.recorder = new Recorder({
                sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
                sampleRate: 48000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
                numChannels: 1, // 声道，支持 1 或 2， 默认是1
                // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
            });
            Recorder.getPermission().then(() => {
                this.recorder.start().then(() => {
                    this.audioType = true
                    this.$message.success('已开启录音')
                }, (error) => {
                    // 出错了
                    console.log(`${error.name} : ${error.message}`);
                    this.$notify.error({
                        title: '错误',
                        message: error.message
                    });
                });
            }, (error) => {
                this.$notify.error({
                    title: '错误',
                    message: '请授权浏览器录音权限'
                });
                console.log(`${error.name} : ${error.message}`);
            });
        },
        audio_stop() {
            if (this.audioType) {
                this.$message.success('已停止录音')
                this.audioNum = this.recorder.duration.toFixed(0)
                this.recorder.pause()
                this.isGG = true
                console.log(this.audioSize)
            } else {
                this.$notify.error({
                    title: '错误',
                    message: '请开始录音后操作'
                });
            }
        },
        audio_play() {
            if (this.audioType) {
                this.isAudioPlpy = !this.isAudioPlpy
                if (this.isAudioPlpy) {
                    this.recorder.play();
                    this.isGG = false
                    setTimeout(() => {
                        this.isAudioPlpy = false
                    }, this.audioNum * 1000)
                } else {
                    this.recorder.pause();
                }
            } else {
                this.$notify.error({
                    title: '错误',
                    message: '请开始录音后操作'
                });
            }
        },
        audio_destroy() {
            if (this.audioType) {
                this.recorder.destroy().then(() => {
                    this.audioNum = ""
                    this.file = ""
                });
            } else {
                this.$notify.error({
                    title: '错误',
                    message: '请开始录音后操作'
                });
            }
        },
        submitPgc() {
            if (this.contentType == '2') {
                this.uploadAudio()
            } else {
                this.audioSize = ""
                this.audioNum = ""
                if (this.textareaText) {
                    this.submitPgcSystemMsg()
                } else {
                    this.$message.error('发言内容不能为空')
                }
            }
        },
        async uploadAudio() {
            if (this.isGG) {
                this.audioSize = this.recorder.getWAVBlob().size
                var wavBlob = this.recorder.getWAVBlob();
                // 创建一个formData对象
                var formData = new FormData()
                // 此处获取到blob对象后需要设置fileName满足当前项目上传需求，其它项目可直接传把blob作为file塞入formData
                const newbolb = new Blob([wavBlob], {
                    type: 'audio/wav'
                })
                //获取当时时间戳作为文件名
                const fileOfBlob = new File([newbolb], new Date().getTime() + '.wav')
                formData.append('file', fileOfBlob)
                const {
                    result
                } = await uploadExcelToOss(formData)
                this.audioUrl = result.url
                this.$message.success('上传成功')
                this.isGG = true
                this.submitPgcSystemMsg()
            } else {
                this.$message.error('请开始录音后结束录音再一键发言')
            }
        },
        async submitPgcSystemMsg() {
            const loading = this.$loading({
                lock: true,
                text: '发言中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            let res = await this.$api.pgcSystemMsg({
                contentType: this.contentType,
                content: this.contentType == '2' ? this.audioUrl : this.textareaText,
                size: this.audioSize,
                second: this.audioNum,
            })
            if (res.status.code === 0) {
                loading.close();
                this.$message.success('发言成功')
                this.isPgc = false
                if (this.contentType == '2') {
                    this.audio_destroy()
                }
            } else {
                loading.close();
            }
        },
        xuanzeScreen(val) {
            this.pageScreen = val
            this.getUserList()
        },
        // 分页
        xuanzeSizeScreen(val) {
            this.pageSizeScreen = val
            this.getUserList()
        },
        // uid批量加群
        async uidJoinInGroup() {
            if (this.invitedUserIdStr == "") {
                this.$message.error('请输入uid')
            } else {
                let userIdStr = []
                this.invitedUserIdStr.split(",").forEach((item) => {
                    userIdStr.push(Number(item))
                })
                const res = await this.$api.joinToGroup({
                    groupId: this.groupId,
                    invitedUserIdStr: JSON.stringify(userIdStr)
                })
                if (res.status.code === 0) {
                    this.$message.success('批量加入成功')
                    this.invitedUserIdStr = ""
                    this.isUidOpen = false
                }
            }
        },
        uidOpen(item) {
            this.groupId = item.groupId
            this.isUidOpen = true
        }
    }
}
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    justify-content: space-between;

    .demo-form-inline {
        flex: 1;
    }

    .action {
        width: 400px;
    }
}

.dialog-action {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.table {
    margin-bottom: 100px;
}

.footer {
    z-index: 10;
    background: white;
    width: calc(100% - 280px);
    padding: 10px 0;
    position: fixed;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.msg {
    .el-textarea {
        margin: 20px 0;
    }
}

.el-table th.el-table__cell>.cell {
    padding: 10px !important;
}

.yuyin {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    width: 2em;
    margin-right: 20px;

    span {
        width: 0.3em;
        height: 1em;
        background-color: #145358;
    }

    span:nth-of-type(1) {
        transform: scaleY(1);
    }

    span:nth-of-type(2) {
        transform: scaleY(1.25);
    }

    span:nth-of-type(3) {
        transform: scaleY(1.5);
    }

    span:nth-of-type(4) {
        transform: scaleY(2);
    }

    &.active {
        span:nth-of-type(1) {
            animation: grow 1s -0.15s ease-in-out infinite;
        }

        span:nth-of-type(2) {
            animation: grow 1s -0.3s ease-in-out infinite;
        }

        span:nth-of-type(3) {
            animation: grow 1s -0.45s ease-in-out infinite;
        }

        span:nth-of-type(4) {
            animation: grow 1s -0.65s ease-in-out infinite;
        }
    }

    @keyframes grow {

        0%,
        100% {
            transform: scaleY(1);
        }

        50% {
            transform: scaleY(2);
        }
    }
}

.view_yu {
    background-color: #f4f4f4;
    margin: 10px 0;
    border-radius: 6px;
    height: 45px;
    width: 260px;
    display: flex;
    justify-content: flex-start;
    padding-left: 20px;
    align-items: center;
    cursor: pointer;
}
</style>