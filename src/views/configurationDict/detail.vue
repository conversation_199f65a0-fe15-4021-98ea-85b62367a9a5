<template>
  <d2-container class="page">
    <div class="header">
      <div class="form">
      </div>
      <div class="action">
        <el-button type="primary" @click="editDictDialog = true">新增</el-button>
      </div>
    </div>
    <div class="form">
      <el-table
        :data="tableData"
        style="width: 100%">
        <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :width="item.width || 'auto'"
          :prop="item.prop"
          :label="item.label">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="150">
          <template slot-scope="scope">
<!--            <el-button @click="handleClick(scope.row)" type="text" >查看</el-button>-->
            <el-button @click="editClick(scope.row)" type="text" >编辑</el-button>
            <el-button @click="deleteClick(scope.row)" type="text"  class="danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <footer-bar  :total="totalCount" @current-change="currentChange"></footer-bar>
    <el-dialog :title="`${edit.id ? '编辑':'添加'}字典`" :visible.sync="editDictDialog" class="editDictDialog">
      <el-form :model="edit" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="字典类型" prop="dictType">
          <el-input v-model="edit.dictType" autocomplete="off" disabled></el-input>
        </el-form-item>
        <el-form-item label="字典标签" prop="dictLabel">
          <el-input v-model="edit.dictLabel" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="字典键值" prop="dictValue">
          <el-input v-model="edit.dictValue" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="权重">
          <el-input v-model="edit.weight" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="edit.remark" autocomplete="off" type="textarea"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDictDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmEdit()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>

</template>
<script>
import footerBar from '@/views/configurationDict/window/footer'
export default {
  name: 'configurationDictDetail',
  components: {
    footerBar
  },
  data () {
    return {
      search: {
        dictName: '',
        dictType: ''
      },
      edit: {
        dictLabel: '',
        dictValue: '',
        weight: '',
        dictType: '',
        remark: ''
      },
      tableData: [],
      columnList: [
        {
          label: 'ID',
          prop: 'id',
          width: '80'
        },
        {
          label: '字典类型',
          prop: 'dictType',
          width: '250'
        },
        {
          label: '字典标签',
          prop: 'dictLabel',
          width: '250'
        },
        {
          label: '字典键值',
          prop: 'dictValue',
          width: '250'
        },
        {
          label: '备注',
          prop: 'remark'
        },
        {
          label: '权重',
          prop: 'weight',
          width: '100'
        }
      ],
      editDictDialog: false,
      rules: {
        dictLabel: [
          { required: true, message: '请输入字典标签', trigger: 'blur' }
        ],
        dictType: [
          { required: true, message: '请输入字典类型', trigger: 'blur' }
        ],
        dictValue: [
          { required: true, message: '请输入字典键值', trigger: 'blur' }
        ]
      },
      totalCount: 0
    }
  },
  watch: {
    editDictDialog (val) {
      if (!val) {
        this.edit = {}
        this.resetForm()
      } else {
        this.edit.dictType = this.$route.query.dictType
      }
    }
  },
  mounted () {
    this.getDictList()
  },
  methods: {
    currentChange (val) {
      this.getDictList(val)
    },
    resetSearch () {
      this.search = {}
      this.getDictList()
    },
    async getDictList (pageNum = 1) {
      const dictType = this.$route.query.dictType
      const { result: { list, totalCount } } = await this.$api.getDictData({
        dictType,
        pageNum,
        pageSize: 15
      })
      this.tableData = list
      this.totalCount = totalCount
    },
    async saveDict () {
      await this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const {
            status: {
              code,
              msg
            }
          } = await this.$api.saveDictData(this.edit)
          this.edit = {}
          if (!code) {
            this.$message.success(msg)
            await this.getDictList()
            this.editDictDialog = false
          } else {
            this.$message.error(msg)
          }
        } else {
          return false
        }
      })
    },
    resetForm () {
      this.$refs.ruleForm.resetFields()
    },
    async deleteClick (val) {
      this.$confirm('此操作将永久删除该字典, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const { id } = val
          const { status: { code, msg } } = await this.$api.deleteDictData({
            id
          })
          code === 0 ? this.$message.success(msg) : this.$message.error(msg)
          await this.getDictList()
        }).catch(() => {})
    },
    handleClick (row) {
      console.log(row)
    },
    editClick (row) {
      this.editDictDialog = true
      this.edit = row
    },
    confirmEdit () {
      this.saveDict()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ebeef5;
}
.item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.header,
.form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .item {
    .el-input {
      width: 200px;
      margin-left: 20px;
    }
  }
}
.form {
  .danger {
    color: #F56C6C;
  }
}
.editDictDialog {
  .item {
    margin-bottom: 30px;
    span{
      min-width: 80px;
    }
    .el-input {
      width: 80%;
      margin-left: 20px;
    }
  }
}

</style>
