<template>
  <d2-container class="page">
    <div class="header">
      <div class="form">
        <div class="item">
          <span>字典名称</span>
          <el-input v-model="search.dictName" placeholder="请输入字典名称"></el-input>
        </div>
        <div class="item">
          <span>字典类型</span>
          <el-input v-model="search.dictType" placeholder="请输入字典类型"></el-input>
        </div>
      </div>
      <div class="action">
        <el-button type="primary" @click="getDictList('search')">搜索</el-button>
        <el-button type="info" @click="resetSearch()">重置</el-button>
        <el-button type="info" @click="editDictDialog = true">新增</el-button>
      </div>
    </div>
    <div class="form">
      <el-table
        :data="tableData"
        height="68vh"
        style="width: 100%">
        <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :width="item.width || 'auto'"
          :prop="item.prop"
          :label="item.label">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button @click="detailClick(scope.row)" type="text" >查看</el-button>
            <el-button @click="editClick(scope.row)" type="text" >编辑</el-button>
<!--            <el-button @click="deleteClick(scope.row)" type="text"  class="danger">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <footer-bar :total="totalCount" @current-change="currentChange" :current="currentPage"></footer-bar>
    <el-dialog :title="`${edit.id ? '编辑':'添加'}字典`" :visible.sync="editDictDialog" class="editDictDialog">
      <el-form :model="edit" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="edit.dictName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="字典类型" prop="dictType">
          <el-input v-model="edit.dictType" autocomplete="off" :disabled="edit.id"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="edit.remark" autocomplete="off" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDictDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmEdit()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>

</template>
<script>
import footerBar from '@/views/configurationDict/window/footer'
export default {
  name: 'configurationDict',
  components: {
    footerBar
  },
  data () {
    return {
      search: {
        dictName: '',
        dictType: ''
      },
      edit: {
        dictName: '',
        dictType: '',
        remark: ''
      },
      tableData: [],
      columnList: [
        {
          label: 'ID',
          prop: 'id',
          width: '80'
        },
        {
          label: '字典名称',
          prop: 'dictName',
          width: '250'
        },
        {
          label: '字典类型',
          prop: 'dictType',
          width: '250'
        },
        {
          label: '备注',
          prop: 'remark'
        },
        {
          label: '创建时间',
          prop: 'createAt',
          width: '200'
        }
      ],
      editDictDialog: false,
      rules: {
        dictName: [
          {
            required: true,
            message: '请输入字典名称',
            trigger: 'blur'
          }
        ],
        dictType: [
          {
            required: true,
            message: '请输入字典类型',
            trigger: 'blur'
          }
        ]
      },
      totalCount: 0,
      currentPage: 1
    }
  },
  watch: {
    editDictDialog (val) {
      if (!val) {
        this.edit = {}
        this.resetForm()
      }
    }
  },
  mounted () {
    this.getDictList()
  },
  methods: {
    currentChange (val) {
      this.currentPage = val
      this.getDictList()
    },
    resetSearch () {
      this.search = {}
      this.currentPage = 1
      this.getDictList()
    },
    async getDictList (type) {
      if (type === 'search') {
        this.currentPage = 1
      }
      const {
        dictName,
        dictType
      } = this.search
      const { result: { list, totalCount } } = await this.$api.getDict({
        dictName,
        dictType,
        pageNum: this.currentPage,
        pageSize: 15
      })
      this.totalCount = totalCount
      this.tableData = list
    },
    async saveDict () {
      await this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const {
            status: {
              code,
              msg
            }
          } = await this.$api.saveDict(this.edit)
          this.edit = {}
          if (!code) {
            this.$message.success(msg)
            await this.getDictList()
            this.editDictDialog = false
          } else {
            this.$message.error(msg)
          }
        } else {
          return false
        }
      })
    },
    resetForm () {
      this.$refs.ruleForm.resetFields()
    },
    async deleteClick (val) {
      this.$confirm('此操作将永久删除该字典, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const { id } = val
          const { status: { code, msg } } = await this.$api.deleteDict({
            id
          })
          code === 0 ? this.$message.success(msg) : this.$message.error(msg)
          await this.getDictList()
        }).catch(() => {
        })
    },
    detailClick (row) {
      this.$router.push({
        path: '/configurationDict/detail',
        query: {
          dictType: row.dictType
        }
      })
    },
    editClick (row) {
      this.editDictDialog = true
      this.edit = row
    },
    confirmEdit () {
      this.saveDict()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  border-bottom: 1px solid #ebeef5;
}

.item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.header,
.form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;

  .item {
    .el-input {
      width: 200px;
      margin-left: 20px;
    }
  }
}

.form {
  .danger {
    color: #F56C6C;
  }
}

.editDictDialog {
  .item {
    margin-bottom: 30px;

    span {
      min-width: 80px;

      .required {
        color: #F56C6C;
        font-size: 15px;
      }
    }

    .el-input {
      width: 80%;
      margin-left: 20px;
    }
  }
}

</style>
