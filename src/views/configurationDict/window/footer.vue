<template>
  <div class="box">
    <el-pagination
      :current-page.sync="currentPage"
      @current-change="currentChange"
      background
      layout="prev, pager, next"
      :total="total"
      :page-size="size">
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: 'footer',
  props: {
    total: {
      type: [Number, String],
      default: 1
    },
    current: {
      type: [Number, String],
      default: 1
    },
    size: {
      type: [Number, String],
      default: 15
    }
  },
  watch: {
    current (val) {
      this.currentPage = val
    }
  },
  data () {
    return {
      currentPage: this.current
    }
  },
  methods: {
    currentChange (val) {
      this.$emit('current-change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  background: white;
  width: 100%;
  position: fixed;
  bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  .el-pagination {
    margin-right: 300px;
  }
}
</style>
