<template>
    <div class="faq-container">
        <h2 class="faq-title">{{ $t('faq.title') }}</h2>
        <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
            <div class="faq-question" @click="toggle(index)">
                <span>{{ item.question }}</span>
                <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250408/e2a0b5bd9d08075ac354f3d5e69a4452_200x200.png"
                    alt="arrow" />
            </div>

            <transition name="faq-slide"> 
                <div class="faq-answer" v-show="activeIndex === index">
                    {{ item.answer }}
                </div>
            </transition>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const activeIndex = ref(null)

const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}

const faqListZh = computed(() => [
    {
        question: t("faq.registerQuestion"),
        answer: t("faq.registerAnswer")
    },
    {
        question: t("faq.forgotPasswordQuestion"),
        answer: t("faq.forgotPasswordAnswer")
    },
    {
        question: t("faq.kycFailureQuestion"),
        answer: t("faq.kycFailureAnswer")
    },
    {
        question: t("faq.bindPaymentQuestion"),
        answer: t("faq.bindPaymentAnswer")
    },
    {
        question: t("faq.internationalTransferQuestion"),
        answer: t("faq.internationalTransferAnswer")
    },
    {
        question: t("faq.swapFeeQuestion"),
        answer: t("faq.swapFeeAnswer")
    },
    {
        question: t("faq.transactionHistoryQuestion"),
        answer: t("faq.transactionHistoryAnswer")
    },
    {
        question: t("faq.withdrawalDelayQuestion"),
        answer: t("faq.withdrawalDelayAnswer")
    },
    {
        question: t("faq.referralProgramQuestion"),
        answer: t("faq.referralProgramAnswer")
    },
    {
        question: t("faq.multiDeviceQuestion"),
        answer: t("faq.multiDeviceAnswer")
    },
    {
        question: t("faq.enable2FAQuestion"),
        answer: t("faq.enable2FAAnswer")
    },
    {
        question: t("faq.investmentRiskQuestion"),
        answer: t("faq.investmentRiskAnswer")
    },
    {
        question: t("faq.accountRestrictedQuestion"),
        answer: t("faq.accountRestrictedAnswer")
    },
    {
        question: t("faq.contact24hSupportQuestion"),
        answer: t("faq.contact24hSupportAnswer")
    },
    {
        question: t("faq.dataEncryptionQuestion"),
        answer: t("faq.dataEncryptionAnswer")
    },
    {
        question: t("faq.cryptoSupportQuestion"),
        answer: t("faq.cryptoSupportAnswer")
    },
    {
        question: t("faq.marketAlertsQuestion"),
        answer: t("faq.marketAlertsAnswer")
    },
    {
        question: t("faq.freezeAccountQuestion"),
        answer: t("faq.freezeAccountAnswer")
    },
    {
        question: t("faq.kybDifferenceQuestion"),
        answer: t("faq.kybDifferenceAnswer")
    },
    {
        question: t("faq.complianceQuestion"),
        answer: t("faq.complianceAnswer")
    },

])
</script>


<style scoped lang="scss">
.faq-container {
    max-width: 600px;
    margin: 80px auto;
    padding: 20px;
    min-height: 600px;
}

.faq-title {
    font-family: MiSans;
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 24px;
}

.faq-item {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    padding: 16px 0;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;

    span {
        font-family: medium;
    }
}



.arrow-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.arrow-icon.rotated {
    transform: rotate(180deg);
}

.faq-answer {
    font-family: MiSans-thin;
    overflow: hidden;
    padding-top: 10px;
    color: rgba(255, 255, 255, .7);
    font-size: 14px;
    line-height: 1.6;
}

/* 动效过渡 */
.faq-slide-enter-active,
.faq-slide-leave-active {
    transition: all 0.3s ease;
}

.faq-slide-enter-from,
.faq-slide-leave-to {
    opacity: 0;
    max-height: 0;
}

.faq-slide-enter-to,
.faq-slide-leave-from {
    opacity: 1;
    max-height: 200px;
}
</style>