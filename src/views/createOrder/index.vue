<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showCreation="false" @onCreation="nav_task" :showRefresh="true" :showSubmit='true'
      :showReset='true'></common-query>
    <div style="margin-bottom:15px;">
      <el-button @click="nav_task" type="primary" size="mini">创建新任务</el-button>
    </div>

    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #action="scope">
        <el-button @click="onExport(scope.row.importUrl)" type="text">下载</el-button>
        <!-- <el-popconfirm style="margin-left: 10px;" title="确定要删除吗？" @confirm="deleteItem(scope.row)">
      		<el-button slot="reference" type="text">删除</el-button>
      	</el-popconfirm> -->
      </template>
      <template #action2="scope">
        <el-button v-if="scope.row.dutyStatus == 'DOING' || scope.row.dutyStatus == 'INIT' " @click="stopItem(scope.row)" type="text">终止</el-button>
        <el-button v-if="scope.row.dutyStatus == 'STOP' || scope.row.dutyStatus == 'FAIL' " @click="onExoportFail(scope.row)" type="text">导出失败记录</el-button>
      </template>
    </common-table>

    <el-dialog title="" :visible.sync="dialogVisible" width="800px" center @close="closeDialog" destroy-on-close
      :close-on-click-modal="false">
      <CommonForm :schema="formSchema" :isEdit="!details" :data="formData" :submit="submit" label-width="180px"
        :isBack="true" @nav_back="closeDialog">

        <template #merge0>
          <div class="flex_div">
            <el-button @click="downloadTemplate(formData.isSold)" type="primary" size="mini">
              下载模版</el-button>
            <file-uploader :value.sync="formData.importUrl" style="width:200px;margin-left:10px"
              text="上传表格"></file-uploader>
          </div>
        </template>
        <!-- <template #remark>
          <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
          <span></span>
        </template> -->
      </CommonForm>
    </el-dialog>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    createDutyStop,
    createDutyAdd,
    createDutyList,
    createDutyDelete
  } from '@/api/hanxin'
  export default {
    name: 'createOrder',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm,
      FileUploader
    },
    data() {
      return {
        white: '',
        goodsBlindBoxRequestType: 'CTID',
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        tableSchema: [ // 表格架构
          {
            label: '任务创建时间',
            field: 'createAt',
            width: '170px'
          },
          {
            label: '任务状态',
            field: 'dutyStatus',
            type: 'tag',
            tagMap: {

              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
              },
              STOPPING: {
                label: '终止中',
                tagType: 'danger'
              },
              STOP: {
                label: '已终止',
                tagType: 'danger'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '80px',
          },
          {
            label: '系列名',
            field: 'csName',
            width: '170px'
          },
          {
            label: '一级/二级',
            field: 'isSold',
            type: 'tag',
            width: '200px',
            tagMap: {
              0: {
                label: '一级',
                tagType: 'info'
              },
              1: {
                label: '二级',
              },
            },
            width: '80px',
          },
          {
            label: '表格',
            slot: 'action',
            field: 'importUrl',
            // headerSlot: 'action-header',
            width: '140px',
          },
          {
            label: '订单执行时间',
            field: 'startTime',
            width: '200px'
          },
          {
            label: '订单状态',
            field: 'type',
            type: 'tag',
            width: '200px',
            tagMap: {
              1: {
                label: '已支付',
                tagType: 'success'
              },
              2: {
                label: '支付中',

              },
              3: {
                label: '直接空投不扣钱',
                tageType:'info'
              }

            },
            width: '100px',
          },
          {
            label:'备注',
            field:'adminRemark',
            width:'200px'
          },
          {
            label: '操作人',
            field: 'createAdminUser',
            width: '80px'
          },
          // {
          //   label: '优先购开始时间',
          //   field: 'startTime',
          //   width: '150px'
          // },
          // {
          //   label: '优先购结束时间',
          //   field: 'endTime',
          //   width: '150px'
          // },

          // {
          //   label: '优先购白名单',
          //   slot: 'action',
          //   field: 'whitelistImportUrl',
          //   // headerSlot: 'action-header',
          //   width: '140px',
          // },
          {
            label: '操作',
            slot: 'action2',
            width: '140px'
          }
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'search',
            label: '系列id：',
            field: 'ctid',
          },
          {
            type: 'select',
            label: '任务状态：',
            field: 'dutyStatus',
            placeholder: '请选择活动状态',
            options: [
              {
                label: '准备中',
                value: 'INIT'
              },
              {
                label: '进行中',
                value: "DOING"
              },
              {
                label: '终止中',
                value: 'STOPPING'
              },
              {
                label: '已终止',
                value: "STOP"
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '执行失败',
                value: 'FAIL'
              },
            ]
          }
        ],
        formSchema: [{
            type: 'search',
            label: '系列名：',
            placeholder: '请输入系列名',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入系列名',
              trigger: 'blur'
            }],
          },
          {
            label: '创建一级/二级订单：',
            field: 'isSold',
            type: 'radio',
            options: [{
              label: '一级',
              value: 0
            }, {
              label: '二级',
              value: 1
            }, ],
            rules: [{
              required: true,
              message: '请选择订单信息',
              trigger: 'change'
            }],
          },
          {
            slot: 'merge0',
            label: '表格：',
            field: 'importUrl',
          },
          {
            label: '订单时间：',
            field: 'isTiming',
            type: 'radio',
            options: [{
              label: '立即',
              value: 0
            }, {
              label: '定时',
              value: 1
            }, ],
            rules: [{
              required: true,
              message: '请选择订单时间',
              trigger: 'change'
            }],
          },
          {
            type: 'datetime',
            label: '执行时间：',
            field: 'startTime',
            show: {
              relationField: 'isTiming',
              value: [1]
            },
            placeholder: '请输入执行时间',
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
          },
          {
            label: '订单状态：',
            field: 'type',
            type: 'radio',
            options: [{
                label: '已支付',
                value: 1
              }, {
                label: '支付中',
                value: 2
              },
              {
                label: '直接空投不扣钱',
                value: 3
              }
            ],
            rules: [{
              required: true,
              message: '请选择订单状态',
              trigger: 'change'
            }],
          },
          {
            label:'备注',
            type: 'input',
            field:'adminRemark',
            placeholder:'哪位策划给哪个团队使用的',
            rules: [{
              required: true,
              message: '请输入备注',
              trigger: 'blur'
            }],
          },
          // {
          //   type: 'input',
          //   label: '优先购售价：',
          //   field: 'salePrice',
          //   placeholder: '请输入优先购售价',
          //   rules: [{
          //     required: true,
          //     message: '请输入优先购售价',
          //     trigger: 'blur'
          //   }],
          // },
          {
            type: 'action',
            exclude: ['reset']
          }
        ],
        formData: {
          isSold: 0,
          type: '',
          importUrl: '',
          dutyType: "CREATE_ORDER",
          ctid: '',
          isTiming: '',
          startTime: '',

          // saleNum: '',
          // salePrice: '',
          // whitelistImportUrl: '',
          // startTime: [],
          // dutyStatus:''
        },
        query: {
          // dutyStatus:'DOING'
        },
        loading: false,
        dialogVisible: false,
        details: false,
        whiteDetails: false,
      }
    },
    mounted() {
      this.getList()
    },
    methods: {

      toTemplatePage(item = {}) {
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }
      },
      nav_task() {
        this.details = false
        this.dialogVisible = true
      },
      async downloadTemplate(isSold) {
        console.log(isSold)
        let type = ''
        //一级
        if (isSold == 0) {
          type = "DUTY_ORDER_CREATE1"

        } else if (isSold == 1) {
          type = "DUTY_ORDER_CREATE2"
        }
        const {
          status,
          result
        } = await this.$api.userCenterDownLoadTemplate({
          templateTag:type
        })
       if (status.code === 0) {
         window.open(result.emailsTemplateUrl, '_blank')
         this.$message.success(status.msg)
       }
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList(isInit) {
        let ctid = "";
        if (this.query.ctid) {
          ctid = this.query.ctid.split("(")[1].split(")")[0]
        }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          dutyType: 'CREATE_ORDER',
          ctid
        }
        let dataList = []
        createDutyList(params).then(res => {
          const data = res.result.list
          data.forEach((item) => {
            dataList.push({
              ...item.createOrderExtra,
              dutyId: item.dutyId,
              ctid: item.ctid,
              startTime: item.startTime,
              createAt: item.createAt,
              dutyStatus: item.dutyStatus,
              csName: item.createOrderExtra.csName,
              isSold: item.createOrderExtra.isSold,
              type: item.createOrderExtra.type,
              importUrl: item.createOrderExtra.importUrl,
              adminRemark:item.createOrderExtra.adminRemark,
              createAdminUser: item.createAdminUser,
            })
          })
          this.tableData = dataList
          this.tableData.forEach((item) => {
            // item = item.setSellExtra
            this.$forceUpdate();
          })
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },


      onRefresh(data) {
        this.query = data
        this.getList()
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      onExport(item) {
        console.log(item)
        window.open(item)

      },
      closeDialog() {
        this.dialogVisible = false
        this.formData = {
          fromType: 2,
          isTiming: 2
        }
      },
      submit() {
        console.log(this.formData)
        // if(this.formData.types2 == 2){
        //   this.$confirm(`确定要直接空投不扣钱？`, '', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        //   }).then(async () => {
        //     // dutyDelete({
        //     //   dutyId: item.dutyId,
        //     //   dutyType: "SERIES_WHITELIST"
        //     // }).then((res) => {
        //     //   if (res.status.code == 0) {
        //     //     this.$message.success(res.status.msg)
        //     //     this.getList()
        //     //     this.dialogVisible = false
        //     //     this.closeDialog()
        //     //   }
        //     // })
        //     // if (res.status.code === 0) {
        //     //   this.$message.success(status.msg)
        //     //   this.getList()
        //     // }
        //     return
        //   })


        // }
        let ctid = "";
        let startTime, endTime
        // startTime = this.formData.startTime[0]
        // endTime = this.formData.startTime[1]
        if (this.formData.ctid) {
          ctid = this.formData.ctid.split("(")[1].split(")")[0]
        } else {
          this.$message.error('请输入藏品名')
          return false
        }
        let extra = {
          isSold: this.formData.isSold,
          type: this.formData.type,
          importUrl: this.formData.importUrl,
          adminRemark: this.formData.adminRemark
        }
        extra = JSON.stringify(extra)
        const params = {
          dutyType: "CREATE_ORDER",
          ctid,
          isTiming: this.formData.isTiming,
          startTime: this.formData.startTime,
          extra,
        }
        console.log(params)
        createDutyAdd(params).then((res) => {
          if (res.status.code == 0) {
            this.$message.success('成功')
            this.getList()
            this.dialogVisible = false
            this.closeDialog()
          }
        })
      },
      async downloadExcel(res) {
        window.open(res)
        this.$message.success('下载成功')
      },


      // 终止
      stopItem(item) {
        console.log(item)
        this.$confirm(`确定要终止订单吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          createDutyStop({
            dutyId: item.dutyId,
            dutyType: "CREATE_ORDER"
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
              this.dialogVisible = false
              this.closeDialog()
            }
          })
          if (res.status.code === 0) {
            this.$message.success(status.msg)
            this.getList()
          }
        })
      },

      //导出失败记录
      async onExoportFail(item) {
        console.log(item)
        const res = await this.$api.createFailImport({
          dutyId: item.dutyId,
          dutyType: "CREATE_ORDER"
        })
        if (res.type === 'application/json') {

          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          this.loading = false
          downloadBlob(res, '订单失败记录')
          this.$message.success('导出成功')
          this.getWorks()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }

  .flex_div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 700px;
    height: 50px;
    margin-top: 10px;
  }

  .flex {
    display: flex;
  }

  .uploader {
    margin-top: 0 !important;
  }
</style>
