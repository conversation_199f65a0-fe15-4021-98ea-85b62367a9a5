<template>
  <d2-container class="page">
      <el-form class="demo-form-inline" style="background-color: #ffffff; padding: 10px">
        <el-form-item>
          <el-button type="primary" @click="addTag">新建标签</el-button>
        </el-form-item>
      </el-form>

      <common-table :table-schema="tableSchema" :table-data="tableData">

        <template #action="scope">
        	<el-button @click="editNewTag(scope.row)" type="primary">编辑</el-button>
        </template>
        
      </common-table>

      <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      	<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
      	</common-pagination>
      </div>

      <el-dialog title="新建标签" :visible.sync="addTagVisible">
      	<commonForm :submit="submit" :data="formData" :schema="formSchema" label-width="300px"></commonForm>
      </el-dialog>

      <el-dialog title="编辑标签" :visible.sync="editTagVisible" :close='editClose'>
      	<commonForm :submit="editSubmit" :data="editformData" :schema="editformSchema" label-width="300px" ></commonForm>
      </el-dialog>


  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import commonForm from '@/components/CommonForm'
  import CommonPagination from '@/components/CommonPagination'
export default{
  name: 'tagManage',
  components: {
  	CommonQuery,
  	CommonTable,
  	commonForm,
  	CommonPagination
  },
  props: {},
  data(){
    return{
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      page: {
      	totalCount: 0,
      	pageSize: 10
      },

      tableSchema: [ // 表格架构
      	{
      		label: '标签名称',
      		field: 'name'
      	},
      	{
      		label: '标签图片',
          type:'img',
      		field: 'img',
      	},
      	{
      		label: '排序',
      		field: 'weight',
      	},
        {
        	label: '操作',
        	slot: 'action',

        	width: '140px',
        	fixed: 'right'
        }

      ],
      tableData: [{}],
      page: {
      	totalCount: 0,
      	pageSize: 10,
      	pageNum: 1
      }, // 分页数据
      query: {

      },
      addTagVisible: false,
      //新增标签表单
      formSchema: [
        {
          type:'input',
          label:'标签名称',
          placeholder: '请输入标签名称',
          field:'name',
          rules: [{
          	required: true,
          	message: '请输入标签名称',
          	trigger: 'blur'
          }]
        },

        {
          type:"img",
        	label: '标签图片',
        	field: 'image',
        	width: '300px',
          align:'center'
        },

        {
          type:'number-input',
          label:'排序',
          field:'weight',

        },
        {
          type:'action'
        }


      ],
      //新增标签
      formData: {
        name:'',
        image:'',
        weight:1
      },


      editTagVisible:false,

      //编辑标签表单
      editformSchema: [
        {
          type:'input',
          label:'标签名称',
          placeholder: '请输入标签名称',
          field:'name',
          rules: [{
          	required: true,
          	message: '请输入标签名称',
          	trigger: 'blur'
          }]
        },

        {
          type:"img",
        	label: '标签图片',
        	field: 'image',
        	width: '300px',
          align:'center'
        },

        {
          type:'number-input',
          label:'排序',
          field:'weight',

        },
        {
          type:'action'
        }


      ],
      //编辑标签
      editformData: {
        id:'',
        name:'',
        image:'',
        weight:1
      },

    }
  },
  mounted() {
    this.getList()
  },
  methods:{
    // 过滤查询
    onQueryChange(data) {
    	this.query = data
    	this.getList(true)
    },
    async getList(isInit){
      let ctid;
      if (this.query.ctid) {
      	ctid = this.query.ctid.split("(")[1].split(")")[0]
      }
      const params = {
      	...this.query,
      	...this.page,
      	pageNum: isInit ? 1 : this.page.pageNum,
      	ctid,
      	dutyType: 'BASE_GOODS_COUNT_CHANGE'
      }
      console.log('列表数据',params)
      const {
      	status,
      	result
      } = await this.$api.tagList(params)
      console.log('获取列表数据',result)
      if (status.code === 0) {
      	let dataList = []
      	const data = result.list
      	data.forEach((item) => {
      		dataList.push({
            id:item.id,
            img:item.image,
            name:item.name,
            weight:item.weight
      		})
      	})
      	this.tableData = dataList
      	this.page.totalCount = result.totalCount
      	this.page.pageSize = result.pageSize
      	this.page.pageCount = result.pageCount
      }


    },
    //新增
    async submit() {
    	// let ctid;
     //  console.log(this.formData)
    	// if (this.formData.ctid) {
    	// 	ctid = this.formData.ctid.split("(")[1].split(")")[0]
    	// }
      if(this.formData.weight){
        
      }
    	let extra = JSON.stringify(this.formData)
    	const data = {
    		...this.formData,
    	}
      console.log(data)
    	this.$confirm('是否确认提交保存？', '确认提交保存', {
    		confirmButtonText: '确定',
    		cancelButtonText: '取消',
    		type: 'warning'
    	}).then(async () => {
    		const {
    			status
    		} = await this.$api.addTag(data)
        console.log(status)
    		if (status.code === 0) {
    			this.addTagVisible = false
    			this.getList()
    			this.formData = {
    				name:'',
    				image:'',
    				weight:1
    			}
    		}
    	})
    },
    addTag(){
      this.addTagVisible=true
    },
    //编辑标签
    editNewTag(param){
      console.log('打开编辑标签-----',param)
      this.editformData={
        id:param.id,
        name:param.name,
        image:param.img,
        weight:param.weight
      }

      console.log('-----------',this.editformData)
      this.editTagVisible=true
    },
    //编辑提交
    async editSubmit(){
      let extra = JSON.stringify(this.formData)
      const data = {
      	...this.editformData,
      }
      console.log('-----------编辑提交',data)
      this.$confirm('是否确认提交保存？', '确认提交保存', {
      	confirmButtonText: '确定',
      	cancelButtonText: '取消',
      	type: 'warning'
      }).then(async () => {
      	const {
      		status
      	} = await this.$api.tagUpdate(data)
        console.log(status)
      	if (status.code === 0) {
      		this.editTagVisible = false
      		this.getList()

      	}
      })
    },
    editClose(){
      this.editformData = {
        id:'',
      	name:'',
      	image:'',
      	weight:1
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
