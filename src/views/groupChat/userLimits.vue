<template>
  <d2-container class="page">
    <common-query
      :query-schema="querySchema"
      :data="query"
      :showCreation="false"
      @onSubmit="onSubmit"
      @onReset="onReset"
    >

    </common-query>
    <common-table
      :table-schema="tableSchema"
      :showIndex="false"
      :table-data="tableData"
    >
      <template #active="scope">
        <el-button
          type="text"
          @click="set(scope.row)"
        >设置</el-button>
      </template>
    </common-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.totalCount"
        :page-size="page.pageSize"
        :current-page="page.pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange"
        @size-change="currentChangeSize"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="修改优先级"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >

      <div class="box">

        <div class="riod">
          <div>群主权限:</div>
          <div>
            <el-radio-group
              v-model="setForm.groupLeaderAuthor"
              @change="change"
            >
              <el-radio :label="1">开通</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </div>
        </div>

        <div
          class="inp"
          v-show="setForm.groupLeaderAuthor==1"
        >
          <div>群主最多创建群数:</div>
          <div>
            <el-input
              v-model="setForm.createMax"
              placeholder="请输入"
            ></el-input>
          </div>
        </div>
        <div class="inp">
          <div>最多管理群数:</div>
          <div>
            <el-input
              v-model="setForm.maxManageNumber"
              placeholder="请输入"
            ></el-input>
          </div>
        </div>
        <div class="inp">
          <div>最多加入群数:</div>
          <div>
            <el-input
              v-model="setForm.maxJoinNumber"
              placeholder="请输入"
            ></el-input>

          </div>
        </div>
      </div>

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submit"
        >确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonQuery from '@/components/CommonQuery_h'

export default {
  components: {
    CommonTable,
    CommonQuery
  },
  data () {
    return {
      page: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0
      },
      setForm: {
        createMax: '',
        groupLeaderAuthor: '', // 群主权限
        maxManageNumber: '', // 管理群数
        maxJoinNumber: ''// 最多加入群聊
      },
      tableData: [],
      tableSchema: [ // 表格架构

        {
          label: '昵称',
          field: 'name',
          width: '150px'
        },
        {
          label: 'con add',
          field: 'conAdd',
          width: '200px'

        },
        {
          label: '群主权限',
          field: 'groupLeaderAuthor',
          width: '100px',
          type: 'tag',
          tagMap: {
            0: {
              label: '无'
              //   tagType: 'success'
            },
            1: {
              label: '群主权限'
            }
          }
        },
        {
          label: '管理员',
          field: 'manageGroups',
          width: '100px'
        },
        {
          label: '最多管理群数',
          field: 'maxManageNumber',
          width: '100px'
        },
        {
          label: '最多加入群数',
          field: 'maxJoinNumber',
          width: '100px'
        },
        {
          label: '设置',
          slot: 'active',
          width: '100px'
        }
      ],
      query: {
        dutyStatus: 'DOING'
      },
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '昵称',
          field: 'name'
        },
        {
          type: 'input',
          label: 'con add',
          field: 'conAdd'
        },
        {
          type: 'select',
          label: '群主权限',
          field: 'groupLeaderAuthor',
          options: [{
            label: '无',
            value: '0'
          }, {
            label: '群主权限',
            value: '1'
          }]
        },
        {
          type: 'select',
          label: '管理员',
          field: 'manageGroups',
          options: [{
            label: '是',
            value: '0'
          }, {
            label: '否',
            value: '1'
          }]
        }
      ],
      dialogVisible: false,
      uid: '',
      searchVal: {}
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getList () {
      const { result, status } = await this.$api.userAuthorList({
        conAdd: this.searchVal.conAdd || '',
        groupLeaderAuthor: this.searchVal.groupLeaderAuthor || '',
        manageGroups: this.searchVal.manageGroups || '',
        name: this.searchVal.name || '',
        pageNum: this.page.pageNum,
        pageSize: 20
      })
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
      }
    },

    async submit () {
      const { status, result } = await this.$api.userAuthorEdit({
        ...this.setForm,
        uid: this.uid
      })
      console.log(status, 'status')
      if (status.code === 0) {
        this.$message({
          type: 'success',
          message: '设置成功'
        })
        this.getList()
      }
    },
    set (row) {
      this.dialogVisible = true
      this.uid = row.uid
      this.setForm.groupLeaderAuthor = row.groupLeaderAuthor * 1
      this.setForm.maxManageNumber = row.maxManageNumber
      this.setForm.maxJoinNumber = row.maxJoinNumber
      this.setForm.createMax = row.createMax
    },
    onSubmit (val) {
      this.searchVal = val
      this.getList()
    },
    onReset () {
      this.searchVal = null
      this.getList()
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    currentChangeSize (val) {
      this.page.pageSize = val
      this.getList()
    },
    change (e) {
      this.setForm.groupLeaderAuthor = e
      if (e === 0) {
        this.setForm.createMax = 0
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
.box {
  > div {
    > div:nth-child(1) {
      width: 150px;
      height: 40px;
      line-height: 40px;
      margin-right: 30px;
      text-align: right;
    }
    display: flex;
    align-items: center;
  }
  .inp {
    margin-bottom: 10px;
  }
}
</style>
