<template>
  <d2-container class="page">
    <div style="margin-bottom: 20px;">
      <el-button
        type="primary"
        
        @click="onReset"
      >刷新</el-button>
    </div>
    <common-table
      :table-schema="tableSchema"
      :showIndex="false"
      :table-data="tableData"
    >
      <template #action="scope">
        <el-button
          type="text"
          @click="upDate(scope.row)"
        >修改</el-button>
      </template>
    </common-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.totalCount"
        :page-size="page.pageSize"
        :current-page="page.pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange"
        @size-change="currentChangeSize"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="修改优先级"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-input
        placeholder="输入优先级"
        v-model="chas"
      ></el-input>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submit"
        >确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'

export default {
  components: {
    CommonTable
  },
  data () {
    return {
      page: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0
      },
      tableData: [],
      tableSchema: [ // 表格架构

        {
          label: '群聊名称',
          field: 'groupName',
          width: '150px'
        },
        {
          label: '群主',
          field: 'currentOwner',
          width: '200px'

        },
        {
          type: 'img',
          label: '群头像',
          field: 'groupIcon',
          width: '100px'
        },
        {
          label: '经典优先级',
          field: 'order',
          width: '100px'
        },
        {
          label: '修改优先级',
          slot: 'action',
          width: '100px'
        }
      ],
      dialogVisible: false,
      chas: '',
      recordId: ''
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getList () {
      const { result, status } = await this.$api.userGroupsPush({
        ...this.page,
      })
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
      }
    },
    upDate (row) {
      this.dialogVisible = true
      this.recordId = row.groupAccount
      this.chas = row.order
    },
    async submit () {
      const { status } = await this.$api.userGroupsPushedit({
        groupAccount: this.recordId,
        order: this.chas * 1
      })
      if (status.code === 0) {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      }
    },
    onReset () {
      console.log(11)
      this.getList()
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    currentChangeSize (val) {
      this.page.pageSize = val
      this.getList()
    }
  }
}
</script>

  <style lang="scss" scoped>
</style>
