<template>
	<d2-container class="page">
		<el-tabs type="border-card">
			<el-tab-pane label="全局配置">
				<el-form :model="numberValidateForm" label-width="200px" class="demo-ruleForm">
					<el-form-item label="伯德寿命字段前端展示：">
						<el-radio v-model="isShow" label="1">寿命显示</el-radio>
						<el-radio v-model="isShow" label="0">寿命隐藏</el-radio>
					</el-form-item>
					<el-form-item label="伯德寿命初始化：">
						<el-button type="warning"  @click="open2(1)">初始化伯德寿命

						</el-button>
						<div>伯德寿命初始化后，全站所有伯德寿命将从初始化时间开始重新倒计时365天计算，若用户使用过延年药水，则继续叠加药水效果状态。

						</div>
					</el-form-item>
					<el-form-item label="伯德寿命归零：">
						<el-button type="warning"  @click="open2(2)">归零伯德寿命

						</el-button>
						<div>伯德寿命归零后，全站所有伯德寿命将全部变更为已过期状态，若用户使用过延年药水，则同样变为已过期。</div>
					</el-form-item>
					<el-form-item>
						<el-button>取消返回</el-button>
						<el-button type="primary" @click="submit()">提交</el-button>
					</el-form-item>
				</el-form>
			</el-tab-pane>
		</el-tabs>
	</d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
	name: 'moneyLowest',
	data() {
		return {
			tableData: [],
			total: 1,
			numberValidateForm: {
				result: ''
			},
			headers: {
				authorization: ''
			},
			formLabelWidth: '120px',
			isDialog: false,
			result: '',
			isShow: '0',
			tips: ""
		}
	},
	mounted() {
		this.get()
	},
	methods: {
		// 查询
		async get() {
			const {
				result,
				status
			} = await this.$api.getValueByName({
				name: 'is_show_bode_life'
			})
			if (status.code === 0) {
				this.isShow = result.value
				this.tips = result.tips
			} else {
				this.$message.error(status.msg);
			}
		},
		open2(type) {
			this.$confirm('提交保存后，全站伯德寿命将重新开始计算，操作不可逆?', '确认初始化伯德寿命', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			}).then(() => {
				if (type === 1) {
					this.bodeLifeInit()
				} else if (type === 2) {
					this.bodeLifeToZero()
				}
			}).catch(() => {

			});
		},
		async submit() {
			const {
				result,
				status
			} = await this.$api.saveOrUpdate({
				name: 'is_show_bode_life',
				value: this.isShow,
				tips: this.tips
			})
			if (status.code === 0) {
				this.get()
				this.$message.success('操作成功');
			} else {
				this.$message.error(status.msg);
			}
		},

		async bodeLifeToZero() {
			const {
				result,
				status
			} = await this.$api.bodeLifeToZero({
			})
			if (status.code === 0) {
				this.get()
				this.$message.success('操作成功');
			} else {
				this.$message.error(status.msg);
			}
		},
		async bodeLifeInit() {
			const {
				result,
				status
			} = await this.$api.bodeLifeInit({
			})
			if (status.code === 0) {
				this.get()
				this.$message.success('操作成功');
			} else {
				this.$message.error(status.msg);
			}
		},

	},
}
</script>
