<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="全局配置">
        <common-table
          :table-schema="tableSchema"
          :table-data="tableData"
          :multipleSelection.sync="multipleSelection"
          :showSelection="true"
        >
          <template #action-header>
            <el-button type="primary" size="mini" @click="open()"
              >批量寿命调整</el-button
            >
          </template>
          <template #action="scope">
            <el-button @click="openUpdata(scope.row)" type="text"
              >调整寿命</el-button
            >
          </template>
        </common-table>
      </el-tab-pane>
    </el-tabs>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <common-pagination
        ref="commonPagination"
        :page.sync="page"
        @change="getSelete"
      >
      </common-pagination>
    </div>
    <el-dialog
      title="调整伯德寿命"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <el-form v-model="form" label-position="left">
        <el-form-item label="伯德nft名称" label-width="160px">
          {{ form.ownerContractAddress }}
        </el-form-item>
        <el-form-item
          :label="`寿命天数剩余${form.restDay}天`"
          label-width="160px"
        >
          <el-radio v-model="num" label="1">寿命增加</el-radio>
          <el-radio v-model="num" label="-1">寿命减少</el-radio>
        </el-form-item>
        <el-input
          v-model="changeLifeDay"
          placeholder=" 请输入对应寿命变更正整数天数"
        ></el-input>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="批量调整伯德寿命"
      :visible.sync="dialogFormVisible1"
      width="600px"
    >
      <el-form v-model="form" label-position="left">
        <div>将所选的{{ numLength }}条伯德数据寿命进行批量修改</div>
        <el-form-item :label="`寿命天数`" label-width="160px">
          <el-radio v-model="num" label="1">寿命增加</el-radio>
          <el-radio v-model="num" label="-1">寿命减少</el-radio>
        </el-form-item>
        <el-input
          v-model="changeLifeDay"
          placeholder=" 请输入对应寿命变更正整数天数"
        ></el-input>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible1 = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from "@/libs/util.js";
import CommonTable from "@/components/CommonTable";
import CommonPagination from "@/components/CommonPagination";
export default {
  name: "moneyLowest",
  components: {
    CommonTable,
    CommonPagination,
  },
  data() {
    return {
      tableData: [],
      total: 1,
      numberValidateForm: {
        result: "",
      },
      headers: {
        authorization: "",
      },
      formLabelWidth: "120px",
      isDialog: false,
      result: "",
      tableSchema: [
        // 表格架构
        {
          label: "作品名称",
          field: "title",
          width: "200px",
        },
        {
          label: "tokenId",
          field: "tid",
          width: "200px",
        },
        {
          label: "作品图片",
          field: "cover",
          type: "img",
          width: "130px",
        },
        {
          label: "持有人",
          field: "ownerContractAddress",
        },
        {
          label: "持有价格",
          field: "price",
        },
        {
          label: "寄售状态",
          field: "saleStr",
          type: "tag",
          tagMap: {
            寄售中: {
              label: "寄售中",
              tagType: "default",
            },
            未寄售: {
              label: "未寄售",
              tagType: "success",
            },
          },
          width: "80px",
        },
        {
          label: "寿命期限",
          field: "lifeTimeStr",
          width: "200px",
        },
        {
          label: "寿命剩余天数",
          field: "restDay",
        },
        {
          label: "延年药水使用信息",
          field: "0",
        },
        {
          label: "操作",
          slot: "action",
          headerSlot: "action-header",
          width: "140px",
          fixed: "right",
        },
      ],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1,
      }, // 分页数据
      dialogFormVisible: false,
      dialogFormVisible1: false,
      form: {},
      num: "1",
      multipleSelection: [],
      changeLifeDay: "",
      numLength: 0,
    };
  },
  mounted() {
    console.log(util.cookies.get("token"));
    this.getSelete();
  },
  methods: {
    // 查询
    async getSelete() {
      const res = await this.$api.getGoodsUserList({
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
      });
      this.tableData = res.result.list;
      this.page.totalCount = res.result.totalCount;
    },
    async openUpdata(item) {
      this.dialogFormVisible = true;
      this.form = item;
    },
    async open() {
      this.numLength = this.multipleSelection.length;
      if (this.numLength === 0) {
        this.$message.error("请选择你要调整的伯德");
      } else {
        this.dialogFormVisible1 = true;
      }
    },
    resetForm() {},
    // 查询
    async submit() {
      console.log(this.multipleSelection);
      const str = [];
      this.multipleSelection.forEach((item) => {
        str.push(item.lingJingUserId);
      });
      if (this.num == 1) {
        this.changeLifeDay = Number(this.changeLifeDay);
      } else {
        this.changeLifeDay = Number(-this.changeLifeDay);
      }
      const res = await this.$api.batchUpdateEndTime({
        lingJingUserIdStr: JSON.stringify(str),
        changeLifeDay: this.changeLifeDay,
      });
      this.dialogFormVisible = false;
      this.dialogFormVisible1 = false;
      this.$message.success("调整成功");
      this.getSelete();
    },
  },
};
</script>
