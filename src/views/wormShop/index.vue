<template>
  <d2-container class="page">
    <el-form>
      <el-form-item>
        <el-button type="primary" @click="open('add','')">新增商品</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%">
      <el-table-column type="selection"></el-table-column>
      <el-table-column fixed prop="id" label="宝石兑换商品ID" align="center"></el-table-column>
      <el-table-column prop="itemCover" label="图片" align="center">
        <template scope="scope">
          <el-image style="width: 38px; height: 38px" fit="cover" :src="scope.row.itemCover"
            :preview-src-list="[scope.row.itemCover]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="itemName" label="商品名称" align="center"></el-table-column>
      <el-table-column prop="remainNum" label="剩余数量（库存）" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="totalNum" label="总量" align="center"></el-table-column>
      <el-table-column prop="isDeleted" label="是否显示" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isDeleted == '0'" type="success">显示</el-tag>
          <el-tag v-if="scope.row.isDeleted == '1'" type="danger">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="itemType" label="实物/数藏" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.itemType == 'NFT'" type="success">藏品</el-tag>
          <el-tag v-if="scope.row.itemType == 'REAL'" type="danger">实物</el-tag>
          <el-tag v-if="scope.row.itemType == 'FREE_FEE_TIMES'">免佣券</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="walletType" label="兑换宝石类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.walletType == 'GOLDEN_WORM'" type="danger">钻石</el-tag>
          <el-tag v-if="scope.row.walletType == 'SILVERY_WORM'" type="info">蓝宝石</el-tag>
          <el-tag v-if="scope.row.walletType == 'COPPER_WORM'" type="warning">红宝石</el-tag>
          <el-tag v-if="scope.row.walletType == 'COMMON_WORM'" type="success">紫宝石</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="weight" label="权重" align="center"></el-table-column>
      <el-table-column prop="price" label="宝石数量" align="center"></el-table-column>

      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="open('edit',scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total" :page-size="pageSize"
        :current-page="page" :page-sizes="[20, 50, 100, 200,500,1000]" style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
    <el-dialog title="" :visible.sync="dialogVisible" center @close="closeDialog" destroy-on-close width="700px"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <CommonForm :schema="formSchema" :data="formData" :submit="present" label-width="150px">
      </CommonForm>
    </el-dialog>
  </d2-container>
</template>

<script>
  import {
    downloadBlob
  } from '@/utils/helper'
  import CommonForm from '@/components/CommonForm'
  export default {
    name: 'worm-shop-list',
    data() {
      return {
        total: 1,
        tableData: [],
        formLabelWidth: '140px',
        edit_id: '',
        page: 1,
        pageSize: 20,
        dialogVisible: false,

        formSchema: [{
            type: 'img',
            label: '上传图片：',
            placeholder: '请先上传图片',
            field: 'form_itemCover',
            limit: 1,
            rules: [{
              required: true,
              message: '请先上传图片',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '商品名称：',
            field: 'form_itemName',
            rules: [{
              required: true,
              message: '请输入商品名称：',
              trigger: 'blur'
            }]
          },
          // {
          // 	type: 'input',
          // 	label: '库存：：',
          // 	field: 'releasePrice',
          // 	rules: [{
          // 		required: true,
          // 		message: '请输入库存',
          // 		trigger: 'blur'
          // 	}]
          // },
          {
            type: 'number-input',
            label: '库存：',
            field: 'form_totalNum',
            rules: [{
              required: true,
              message: '请输入库存',
              trigger: 'blur'
            }]
            // rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
          },
          {
            type: 'number-input',
            label: '兑换需要的宝石数量：',
            field: 'form_price',
            rules: [{
              required: true,
              message: '请输入兑换需要的宝石数量：',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '实物/数藏：',
            field: 'form_itemType',
            options: [{
              label: '数藏',
              value: 'NFT'
            }, {
              label: '实物',
              value: 'REAL'
            }, {
              label: '免佣券',
              value: 'FREE_FEE_TIMES'
            }],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'radio',
            label: '是否隐藏：',
            field: 'form_isDeleted',
            options: [{
              label: '显示',
              value: 0
            }, {
              label: '隐藏',
              value: 1
            }],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'radio',
            label: '宝石类型：',
            field: 'form_walletType',
            options: [{
              label: '紫宝石',
              value: 'COMMON_WORM'
            }, {
              label: '红宝石',
              value: 'COPPER_WORM'
            }, {
              label: '蓝宝石',
              value: 'SILVERY_WORM'
            }, {
              label: '钻石',
              value: 'GOLDEN_WORM'
            }],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'number-input',
            label: '权重排序：',
            placeholder: '请输入权重排序',
            field: 'form_weight',
            rules: [{
              required: true,
              message: '请输入权重排序',
              trigger: 'blur'
            }]
          },
          {
            type: 'action',
            exclude: ['reset', 'back']
          }
        ],
        formData: {
          form_walletType : 'COMMON_WORM',
          form_isDeleted : 0,
          form_itemType : 'NFT',
          form_weight : 0,
          form_itemName : "",
          form_price : "",
          form_totalNum : "",
          form_itemCover : ""
        }, // 表格数据
        openTable: {},
        oldRemainNum: ''
      }
    },
    components: {
      CommonForm
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 查询列表
      async getList() {
        const res = await this.$api.exchangeItemList({
          pageNum: this.page,
          pageSize: this.pageSize,
        })
        this.tableData = res.result.list
        this.total = res.result.totalCount
      },
      xuanze(val) {
        this.page = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.pageSize = val
        this.getList()
      },
      async addShop() {
        let res = await this.$api.exchangeItemAdd({
          walletType: this.formData.form_walletType,
          isDeleted: this.formData.form_isDeleted,
          itemType: this.formData.form_itemType,
          weight: this.formData.form_weight,
          itemName: this.formData.form_itemName,
          price: this.formData.form_price,
          totalNum: this.formData.form_totalNum,
          itemCover: this.formData.form_itemCover
        })
        if (res.status.code == 0) {
          this.$message.success('添加成功')
          this.closeDialog()
          this.getList()
        } else {
          this.dialogVisible = false
          this.$message.error(res.msg)
        }
      },
      async updateShop() {
        let res = await this.$api.exchangeItemUpdate({
          walletType: this.formData.form_walletType,
          isDeleted: this.formData.form_isDeleted,
          itemType: this.formData.form_itemType,
          weight: this.formData.form_weight,
          itemName: this.formData.form_itemName,
          price: this.formData.form_price,
          totalNum: this.formData.form_totalNum,
          itemCover: this.formData.form_itemCover,
          id: this.edit_id,
          oldRemainNum: this.oldRemainNum
        })
        if (res.status.code == 0) {
          this.closeDialog()
          this.$message.success('修改成功')
          this.getList()
        } else {
          this.dialogVisible = false
          this.$message.error(res.msg)
        }
      },

      open(type, item) {
        if (type == 'add') {
          this.dialogVisible = true
        } else if (type == 'edit') {
          this.dialogVisible = true
          this.edit_id = item.id
          this.oldRemainNum = item.remainNum
          console.log(item)
          this.formData.form_walletType = item.walletType
          this.formData.form_isDeleted = item.isDeleted
          this.formData.form_itemType = item.itemType
          this.formData.form_weight = item.weight
          this.formData.form_itemName = item.itemName
          this.formData.form_price = item.price
          this.formData.form_totalNum = item.totalNum
          this.formData.form_itemCover = item.itemCover
        }
      },
      closeDialog() {
        this.formData.form_walletType = 'COMMON_WORM',
        this.formData.form_isDeleted = 0,
        this.formData.form_itemType = 'NFT',
        this.formData.form_weight = 0
        this.formData.form_itemName = ""
        this.formData.form_price = ""
        this.formData.form_totalNum = ""
        this.formData.form_itemCover = ""
        this.edit_id = ''
        this.dialogVisible = false
      },
      present() {
        if (this.edit_id) {
          this.updateShop()
        } else {
          this.addShop()
        }
      }
    }
  }
</script>

<style>
  /* .el-table__cell {
		padding:0px !important;
	} */
  .justify {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .el-form-item {
    margin-bottom: 10px;
  }

  /* .demo-form-inline{
		padding:10px !important;
	} */
</style>
