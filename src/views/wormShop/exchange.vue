<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item>
				<el-button type="primary" @click="batchExport()">导出</el-button>
        <el-button type="primary" @click="getList()">刷新</el-button>
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
			v-loading="loading">
			<el-table-column fixed prop="id" label="id" align="center" width="80"></el-table-column>
			<el-table-column prop="createAt" label="兑换时间" align="center" ></el-table-column>
			<el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>

			<el-table-column prop="itemName" label="兑换商品" align="center"  ></el-table-column>
      <el-table-column prop="cover" label="商品图片" align="center" width="80px">
      	<template scope="scope">
      		<div style="width: 100%">
      			<el-image style="width: 40p; height: 40px;cursor: pointer;" :preview-src-list="[scope.row.itemCover]" :src="scope.row.itemCover">
      			</el-image>
      		</div>
      	</template>
      </el-table-column>
			<el-table-column prop="walletType" label="消耗类型" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.walletType == 'GOLDEN_WORM'" type="danger">钻石</el-tag>
					<el-tag v-if="scope.row.walletType == 'SILVERY_WORM'" type="info">蓝宝石</el-tag>
					<el-tag v-if="scope.row.walletType == 'COPPER_WORM'" type="warning">红宝石</el-tag>
					<el-tag v-if="scope.row.walletType == 'COMMON_WORM'" type="success">紫宝石</el-tag>
				</template>
			</el-table-column>
      <el-table-column prop="consigneeName" label="收件人" align="center"></el-table-column>
      <el-table-column prop="consigneePhone" label="收货人手机号" align="center"></el-table-column>
			<el-table-column prop="address" label="用户地址" align="center">
			  <template scope="scope" >
			    <div v-if="scope.row.address">
			      {{scope.row.address.split("|")[0]}}
			      {{scope.row.address.split("|")[1]}}
			    </div>
			    <div v-else>{{scope.row.address}}</div>
			  </template>
			</el-table-column>
      <el-table-column prop="userRemark" label="用户备注" align="center">
        <template scope="scope" >
          <div style="color:rgb(195 72 11 / 90%);">
           {{scope.row.userRemark}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="oldAmount" label="消耗前余额" align="center"></el-table-column>
			<el-table-column prop="newAmount" label="消耗后余额" align="center"></el-table-column>
			<el-table-column prop="remark" label="备注" align="left" show-overflow-tooltip width="200px">
				<template scope="scope">
					<div style="width:300px;cursor: pointer;" @click="openText(scope.row.remark)">{{scope.row.remark}}
					</div>
					<el-button type="text"  @click="remark_click(scope.row)">编辑</el-button>
				</template>
			</el-table-column>
		</el-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
			</el-pagination>
		</div>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'

	export default {
		name: 'exchange',
		data() {
			return {
				loading: false,
				formInline: {
					orderId: '',
					uid: '',
					createAt: null,
					startTime: '',
					endTime: '',
					type: '',
					nickname: '',
					username: ''
				},
				total: 1,
				tableData: []
			}
		},
		mounted() {
			if (this.$route.query.uid) {
				this.formInline.uid = this.$route.query.uid
			}
			this.getList(1)
		},
		methods: {
			// 导出
			async batchExport() {
				try {
					const res = await this.$api.exchangeExport()
					if (res.type === 'application/json') {
						// blob 转 JSON
						const enc = new TextDecoder('utf-8')
						res.arrayBuffer().then(buffer => {
							const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
							this.$message.error(data.status?.msg)
						})
					} else {
						downloadBlob(res, '兑换记录')
						this.$message.success('导出成功')
						this.getList()
					}
				} finally {
					this.loading = false
				}
			},
			// 查询列表
			async getList(page) {
				const res = await this.$api.exchangeList({
					pageNum: page,
					pageSize: 15,
				})
				this.loading = false
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
			xuanze(val) {
				this.getList(val)
			},
			clear() {
				this.formInline.uid = ''
				this.formInline.type = ''
				this.formInline.orderId = ''
				this.formInline.createAt = undefined
			},
			openText(text) {
				console.log(text)
				this.$alert(text, '备注详情', {
					confirmButtonText: '确定',
					callback: action => {

					}
				});
			},
			// 备注
			remark_click(val) {
				this.edit_id = val.id
				this.$prompt('备注信息', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				}).then(({ value }) => {
				  this.remark=value
				  this.submit_remark()
				}).catch(() => {
				  this.$message({
				    type: 'info',
				    message: '取消输入'
				  });
				});
			},
			async submit_remark() {
				await this.$api.exchangeRemark({
					id: this.edit_id,
					remark: this.remark
				})
				this.getList(1)
				this.isDialogRemark = false
				this.$message.success('备注修改成功')
				this.remark = ''
			},
		}
	}
</script>

<style>
</style>
