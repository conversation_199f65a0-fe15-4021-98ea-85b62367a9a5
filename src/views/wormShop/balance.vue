<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item label="用户昵称">
				<el-input v-model="formInline.nickname" placeholder="请输入用户昵称" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户id">
				<el-input v-model="formInline.uid" placeholder="请输入用户id" clearable type="number"></el-input>
			</el-form-item>
			<el-form-item label="con add">
				<el-input v-model="formInline.contractAddress" placeholder="请输入con add" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户名">
				<el-input v-model="formInline.username" placeholder="请输入用户名" clearable></el-input>
			</el-form-item>
			<el-form-item label="手机号">
				<el-input v-model="formInline.mobphone" placeholder="请输入手机号" clearable style="width: 320px"
					maxlength="11" show-word-limit></el-input>
			</el-form-item>
			<el-form-item label="邮箱">
				<el-input v-model="formInline.email" placeholder="请输入邮箱" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getList(1)">查询</el-button>
				<el-button type="primary" @click="downLoad()">模板下载</el-button>
				<label type="primary" for="importFile" class="import el-button el-button--primary">批量修改余额</label>
				<input type="file" id="importFile" @change="importTemplate($event)" v-show="false">
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" border style="width: 100%" v-loading="loading">
			<el-table-column fixed prop="uid" label="用户id" align="center"></el-table-column>
			<el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
			<el-table-column prop="goldenWorm" label="钻石数量" align="center"></el-table-column>
			<el-table-column prop="silveryWorm" label="蓝宝石数量" align="center"></el-table-column>
			<el-table-column prop="copperWorm" label="红宝石数量" align="center"></el-table-column>
			<el-table-column prop="commonWorm" label="紫宝石数量" align="center"></el-table-column>
			<el-table-column prop="updatedAt" label="操作" align="center" width="100px">
				<template scope="scope">
					<el-button type="text"  @click="open('edit',scope.row)">编辑</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-dialog title="" :visible.sync="dialogVisible" center @close="closeDialog" destroy-on-close width="700px" :close-on-click-modal="false" :close-on-press-escape="false">
			<CommonForm :schema="formSchema" :data="formData" :submit="present" label-width="150px">
			</CommonForm>
		</el-dialog>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
			</el-pagination>
		</div>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'
	import {
		uploadExcelToOss
	} from '@/api/ossCenter'
	import CommonForm from '@/components/CommonForm'
	export default {
		name: 'exchange',
		data() {
			return {
				loading: false,
				formInline: {},
				total: 1,
				tableData: [],
				dialogVisible: false,
				formSchema: [{
						type: 'input',
						label: '用户ID：',
						field: 'uid',
						disabled: true,
						rules: [{
							required: true,
							message: '请输入用户名：',
							trigger: 'blur'
						}]
					},
					{
						type: 'input',
						label: '用户昵称：',
						field: 'nickname',
						disabled: true,
						rules: [{
							required: true,
							message: '请输入昵称：',
							trigger: 'blur'
						}]
					},
					{
						type: 'number-input',
						label: '钻石：',
						field: 'newGoldenWorm',
						rules: [{
							required: true,
							message: '请输入钻石',
							trigger: 'blur'
						}]
						// rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
					},
					{
						type: 'number-input',
						label: '蓝宝石：',
						field: 'newSilveryWorm',
						rules: [{
							required: true,
							message: '请输入蓝宝石',
							trigger: 'blur'
						}]
						// rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
					},
					{
						type: 'number-input',
						label: '红宝石：',
						field: 'newCopperWorm',
						rules: [{
							required: true,
							message: '请输入红宝石',
							trigger: 'blur'
						}]
						// rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
					},
					{
						type: 'number-input',
						label: '紫宝石：',
						field: 'newCommonWorm',
						rules: [{
							required: true,
							message: '请输入紫宝石',
							trigger: 'blur'
						}]
						// rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
					},
					{
						type: 'action',
						exclude: ['reset', 'back']
					}
				],
				formData: {
					newCommonWorm: 0,
					newCopperWorm: 0,
					newGoldenWorm: 0,
					newSilveryWorm: 0,
					uid: "",
					nickname: ""
				}, // 表格数据
				old: {
					oldCommonWorm: 0,
					oldCopperWorm: 0,
					oldGoldenWorm: 0,
					oldSilveryWorm: 0
				}
			}
		},
		mounted() {
			this.getList(1)
		},
		components: {
			CommonForm
		},
		methods: {
			// 导出
			async batchExport() {
				this.loading = true
				if (this.formInline.createAt) {
					this.formInline.startTime = this.formInline.createAt[0]
					this.formInline.endTime = this.formInline.createAt[1]
				} else {
					this.formInline.startTime = undefined
					this.formInline.endTime = undefined
				}
				try {
					const res = await this.$api.financeExport({
						orderId: this.formInline.orderId,
						uid: this.formInline.uid,
						startTime: this.formInline.startTime,
						endTime: this.formInline.endTime,
						type: this.formInline.type,
						username: this.formInline.username,
						nickname: this.formInline.nickname,
					})
					if (res.retCode === 500) {
						this.$message.error(res.retMsg)
						this.getList()
					} else if (res.type === 'application/json') {
						// blob 转 JSON
						const enc = new TextDecoder('utf-8')
						res.arrayBuffer().then((buffer) => {
							const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
							this.$message.error(data.status?.msg)
						})
					} else {
						downloadBlob(res, '账务' + Date.now() + '.csv')
						this.$message.success('导出成功')
						this.getList()
					}
				} finally {
					this.loading = false
				}
			},
			// 查询列表
			async getList(page) {
				const res = await this.$api.wormList({
					pageNum: page,
					pageSize: 15,
					...this.formInline
				})
				this.loading = false
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
			xuanze(val) {
				this.getList(val)
			},
			clear() {
				this.formInline.uid = ''
				this.formInline.type = ''
				this.formInline.orderId = ''
				this.formInline.createAt = undefined
			},
			openText(text) {
				console.log(text)
				this.$alert(text, '备注详情', {
					confirmButtonText: '确定',
					callback: action => {

					}
				});
			},
			open(type, item) {
				this.dialogVisible = true
				this.old.oldCommonWorm = item.commonWorm
				this.old.oldCopperWorm = item.copperWorm
				this.old.oldGoldenWorm = item.goldenWorm
				this.old.oldSilveryWorm = item.silveryWorm
				// let array = item
				this.formData.newCommonWorm = item.commonWorm
				this.formData.newCopperWorm = item.copperWorm
				this.formData.newSilveryWorm = item.silveryWorm
				this.formData.newGoldenWorm = item.goldenWorm
				this.formData.uid = item.uid
				this.formData.nickname = item.nickname
				// item=item
			},
			closeDialog() {
				this.dialogVisible = false
			},
			async present() {
				const res = await this.$api.wormUpdate({
					...this.old,
					...this.formData
				})
				if (res.status.code == 0) {
					this.dialogVisible = false
					this.$message.success('修改成功')
					this.getList(1)
				} else {
					this.$message.error(res.status.msg)
				}

			},
			async downLoad() {
				const res = await this.$api.userCenterDownLoadTemplate({
					templateTag: 'BATCH_UPDATE_USER_WORM'
				})
				if (res.status.code == 0) {
					window.open(res.result.emailsTemplateUrl)
				} else {
					this.$message.error(res.status.msg)
				}
			},
			async importTemplate(event) {
				const formData = new FormData()
				const input = event.target
				const file = input.files[0]
				formData.append('file', file)
				this.loading = true
				const {
					result: {
						url
					}
				} = await uploadExcelToOss(formData)
				input.value = ''
				let res = await this.$api.wormBatchImportUpdate({
					importUrl: url
				})
				if (res.status.code == 0) {
					this.loading = false
					this.$message.success('批量修改余额成功')
					this.getList(1)
				} else {
					this.loading = false
					this.$message.error(res.status.msg)
				}
			},
		}
	}
</script>

<style>
	.import{
		/* background-color:#409EFF; */
		   /* display: inline-block;
		    line-height: 1;
		    white-space: nowrap;
		    cursor: pointer;
		    background: #FFF;
		    border: 1px solid #DCDFE6;
		    color: #606266;
		    -webkit-appearance: none;
		    text-align: center;
		    -webkit-box-sizing: border-box;
		    box-sizing: border-box;
		    outline: 0;
		    margin: 0;
		    -webkit-transition: .1s;
		    transition: .1s;
		    font-weight: 500;
		    padding: 12px 20px;
		    font-size: 14px;
		    border-radius: 4px; */
	}
</style>
