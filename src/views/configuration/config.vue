<template>
  <d2-container class="page">
    <el-form :inline="true" class="demo-form-inline" style="background-color: #ffffff; padding: 20px">
      <el-form-item>
        <el-button type="primary" @click="openDialog(1, '')">添加配置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
      show-header border style="width: 100%">
      <el-table-column fixed prop="id" label="id" align="center"></el-table-column>
      <!-- <el-table-column
        prop="dicType"
        label="dicType"
        align="center"
      ></el-table-column> -->
      <el-table-column prop="dicKey" label="dicKey" align="center"></el-table-column>
      <el-table-column prop="dicValue" label="dicValue" align="center">
      </el-table-column>
      <el-table-column prop="remark" label="remark" align="center">
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click=" removeOpen(scope.row)">更改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>

    <!-- 添加配置 -->
    <el-dialog title="添加配置" :visible.sync="isaddDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="dicKey" :label-width="formLabelWidth">
          <el-input v-model="form.dicKey"></el-input>
        </el-form-item>
        <el-form-item label="dicValue" :label-width="formLabelWidth">
          <el-input v-model="form.dicValue"></el-input>
        </el-form-item>
        <el-form-item label="remark" :label-width="formLabelWidth">
          <el-input v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改配置 -->
    <el-dialog title="修改配置" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="dicValue" :label-width="formLabelWidth">
          <el-input v-model="form.dicValue" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="dicKey" :label-width="formLabelWidth">
          <el-input v-model="form.dicKey" autocomplete="off"></el-input>
        </el-form-item>

        <el-form-item label="remark" :label-width="formLabelWidth">
          <el-input v-model="form.remark" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="update()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
  export default {
    name: 'configuration',
    data() {
      return {
        tableData: [],
        total: 1,
        pageNum: 1,
        pageSize: 15,
        form: {
          id: '',
          dicValue: '',
          remark: '',
          dicKey: ''
        },
        formLabelWidth: '120px',
        isDialog: false,
        isaddDialog: false
      }
    },
    mounted() {
      this.search()
    },
    methods: {
      async search() {
        const res = await this.$api.configSearch({
          pageNum: this.pageNum,
          pageSize: this.pageSize
        })
        this.tableData = res.result.list
        this.total = res.result.totalCount
        console.log(res, '查询')
      },

      // // 添加
      openDialog() {
        this.form = {
          id: '',
          dicValue: '',
          remark: '',
          dicKey: ''
        }
        this.isaddDialog = true
      },
      // 弹窗确定
      async add() {
        const res = await this.$api.configadd({
          dicKey: this.form.dicKey,
          dicValue: this.form.dicValue,
          remark: this.form.remark
        })
        if (res.status.code === 0) {
          this.$message({
            type: 'success',
            message: '添加成功'
          })
          this.search()
        }
      },
      async update() { // 修改配置
        const res = await this.$api.configUpdates({
          dicKey: this.form.dicKey,
          id: this.form.id,
          dicValue: this.form.dicValue,
          remark: this.form.remark
        })
        if (res.status.code === 0) {
          this.$message({
            type: 'success',
            message: '修改成功'
          })
          this.search()
        }
      },

      // 弹窗取消
      adds() {
        this.isaddDialog = false
        this.isDialog = false
        this.form = {
          id: '',
          dicValue: '',
          remark: '',
          dicKey: ''
        }
      },
      //  更改
      removeOpen(item) {
        console.log(item, '更改')
        this.form.id = item.id
        this.form.dicValue = item.dicValue
        this.form.remark = item.remark
        this.form.dicKey = item.dicKey
        this.isDialog = true
      },
      currentChange(value) { // 分页
        this.pageNum = value
        this.search()
      },
      currentChangeSize(value) {
        this.pageSize = value
        this.search()
      }

    }
  }
</script>
