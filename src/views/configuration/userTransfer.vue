<template>
  <d2-container class="page" ref="returnTop">
    <!-- <el-button type="primary" size="mini" style="margin-bottom:20px" @click="shuaxin()">刷新</el-button> -->
    <common-query :query-schema="querySchema" :data="query"  @onSubmit="onQueryChange"
      :showRefresh="false"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="dialogVisible = true">创建</el-button>
      </template>
      <template #action="scope">
        <el-button @click="tableExport1(scope.row.taskId)" type="text">导出错误信息-余额转移</el-button>
        <el-button @click="tableExport2(scope.row.taskId)" type="text">导出错误信息-包括藏品转移</el-button>
        <el-button @click="tableExport3(scope.row.taskId)" type="text">导出转仓人当前持有的藏品数据-余额信息</el-button>
        <el-button @click="tableExport4(scope.row.taskId)" type="text">导出转仓人当前持有的藏品数据-包当前持有藏品</el-button>
      </template>
      <template #fromUserInfoList="scope">
         <el-button @click="openTable(scope.row)" type="text">查看</el-button>
       <!-- <el-descriptions class="margin-top" title="" column="1"  border v-for="(item,index) in scope.row.fromUserInfoList">
              <el-descriptions-item >
                <template slot="label">
                  用户名{{index+1}}
                </template>
                {{item.userName}}
              </el-descriptions-item>
              <el-descriptions-item >
                <template slot="label">
                 用户conAdd
                </template>
                {{item.conAddr}}
              </el-descriptions-item>
          </el-descriptions> -->
      </template>
    </common-table>
    <el-dialog title="创建任务" :visible.sync="dialogVisible" width="800px" center  destroy-on-close
      :close-on-click-modal="false">
      <CommonForm :schema="formSchema" :data="formData" :submit="submitAdd" label-width="220px"
        :isBack="true" >
      </CommonForm>
    </el-dialog>
    <el-dialog title="转出人信息" :visible.sync="dialogTable"  width="600px">
        <common-table :table-schema="dialogTableSchema" :showIndex="false" :table-data="dialogTableData"></common-table>
    </el-dialog>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import {
      downloadBlob
    } from '@/utils/helper'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    collectionTransferList,
    collectionTransferAdd,
    exportFailBalanceExcel,
    exportFailGoodsExcel,
    exportUserHoldBalanceExcel,
    exportUserHoldGoodsExcel
  } from '@/api/hanxin'
  export default {
    name: 'deal',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: '任务Id',
            field: 'taskId',
            width: '50px'
          },
          {
            label: '任务名称',
            field: 'taskName',
            width: '50px'
          },
          {
            label: '创建时间',
            field: 'createTime',
            width: '100px'
          },
          {
            label: '结束时间',
            field: 'endTime',
            width: '100px'
          },
          {
            label: '任务状态',
            field: 'taskStatus',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '准备中',
                tagType: 'info'
              },
              DOING: {
                label: '进行中',
                tagType: 'success'
              },
              DONE: {
                label: '已完成',
                tagType: 'success'
              },
              FAIL: {
                label: '执行失败',
                tagType: 'danger'
              }
            },
            width: '60px',
          },
          {
            label: '转出人信息',
            slot: 'fromUserInfoList',
             width: '80px',
          },
          {
            label: '转入人conadd',
            field: 'toUserInfoConAddr',
            width: '130px'
          },
          {
            label: '转入人用户名',
            field: 'toUserInfoUserName',
            width: '120px'
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '200px',
          },
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'input',
            label: '任务名称：',
            placeholder: '请输入任务名称',
            field: 'taskName'
          },
          {
            type: 'select',
            label: '任务类型：',
            placeholder: '',
            field: 'taskStatus',
            options: [{
                label: '准备中',
                value: 'INIT'
              },
              {
                label: '进行中',
                value: 'DOING'
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '执行失败',
                value: 'FAIL'
              },
            ],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'select',
            label: '排序：',
            placeholder: '',
            field: 'sortRule',
            options: [{
                label: '创建时间升序',
                value: '0'
              },
              {
                label: '创建时间降序',
                value: '1'
              },
            ],
            rules: [{
              required: true,
            }]
          }
        ],
        query: {
          taskStatus: '',
          sortRule:'1'
        },
         dialogVisible:false,
         formSchema: [
           {
             type: 'input',
             label: '任务名称：',
             placeholder: '请输入任务名称',
             field: 'taskName',
             rules: [{
               required: true,
               message: '请输入任务名称',
               trigger: 'blur'
             }],
           },
           {
             type: 'textarea',
             label: '转出藏品的账号conadd：',
             placeholder: '请输入转出藏品的账号conadd',
             field: 'fromConAddress',
             rules: [{
               required: true,
               message: '请输入转出藏品的账号',
               trigger: 'blur'
             }],
           },
           {
             type: 'input',
             label: '转入藏品的账号conadd：',
             placeholder: '请输入转入藏品的账号conadd',
             field: 'toConAddress',
             rules: [{
               required: true,
               message: '请输入转入藏品的账号conadd',
               trigger: 'blur'
             }],
           },
           {
             type: 'radio',
             label: '是否一同将余额转入转入账号：',
             field: 'transBalance',
             options: [
               {
                 label: '是',
                 value: 'Y'
               },
               {
                 label: '否',
                 value: 'N'
               }
             ]
           },
           {
             type: 'action',
             exclude: ['reset']
           }
         ],
         formData: {
            transBalance:'Y',
            toConAddress:'',
            fromConAddress:'',
            taskName:''
         },
         dialogTable:false,
         dialogTableSchema:[
           {
             label: '用户名',
             field: 'userName',
           },
           {
             label: '用户conadd',
             field: 'conAddr',
           },
         ],
         dialogTableData:[]
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
        }

        collectionTransferList(params).then(res => {
          const data = res.result.list
          let dataList = []
          data.forEach((item) => {
            dataList.push({
              ...item,
              toUserInfoConAddr:item.toUserInfo.conAddr,
              toUserInfoUserName:item.toUserInfo.userName,
            })
          })
          this.tableData = dataList
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
     async submitAdd(){
      let fromConAddress =  JSON.stringify(this.formData.fromConAddress.split("\n"))
       let params ={
         ...this.formData,
         fromConAddress
       }
       collectionTransferAdd(params).then(res => {
          this.page.pageNum = 1
          this.dialogVisible = false
          this.formData = {
            transBalance:1,
            toConAddress:'',
            fromConAddress:'',
            taskName:''
          }
          this.getList(true)
       })
      },
      openTable(item){
        this.dialogTable = true
        this.dialogTableData = item.fromUserInfoList
        console.log(this.dialogTableData)
      },
      //余额转移
      async tableExport1(taskId) {
       const params = {
          taskId
       }
        const res = await this.$api.exportFailBalanceExcel(params)
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '导出余额转移')
          this.$message.success('导出成功')
        }
      },
     //藏品转移
     async tableExport2(taskId) {
      const params = {
        taskId
      }
       const res = await this.$api.exportFailGoodsExcel(params)
       if (res.type === 'application/json') {
         // blob 转 JSON
         const enc = new TextDecoder('utf-8')
         res.arrayBuffer().then(buffer => {
           const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
           this.$message.error(data.status?.msg)
         })
       } else {
         downloadBlob(res, '导出藏品转移')
         this.$message.success('导出成功')
       }
     },
     // 导出转仓人当前持有的藏品数据-余额信息
    async tableExport3(taskId) {
     const params = {
       taskId
     }
      const res = await this.$api.exportUserHoldBalanceExcel(params)
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '导出转仓人当前持有的藏品数据-余额信息')
        this.$message.success('导出成功')
      }
    },
    // 导出转仓人当前持有的藏品数据-包当前持有藏品
    async tableExport4(taskId) {
     const params = {
        taskId
     }
      const res = await this.$api.exportUserHoldGoodsExcel(params)
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '导出转仓人当前持有的藏品数据-包当前持有藏品')
        this.$message.success('导出成功')
      }
    },
    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
