<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px">
      <el-form-item>
        <el-input v-model="tips" placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item>
         <el-button type="primary" size="mini" @click="getSelete(1)">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="openDialog(1, '')">添加配置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
      show-header border style="width: 100%">
      <el-table-column fixed prop="id" label="id" align="center" width="100px"></el-table-column>
      <el-table-column prop="name" label="名字" align="center"  width="350px"></el-table-column>
      <el-table-column prop="value" label="值" align="center" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="tips" label="说明" align="center" :show-overflow-tooltip="true">
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="removeOpen(scope.row)">更改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="添加配置" :visible.sync="isaddDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="name" :label-width="formLabelWidth">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="values" :label-width="formLabelWidth">
          <el-input v-model="form.code"></el-input>
        </el-form-item>
        <el-form-item label="tips" :label-width="formLabelWidth">
          <el-input v-model="form.desc"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="更改配置" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="name" :label-width="formLabelWidth">
          <el-input v-model="form.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="values" :label-width="formLabelWidth">
          <el-input v-model="form.code" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="tips" :label-width="formLabelWidth">
          <el-input v-model="form.desc" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
  export default {
    name: 'configuration',
    data() {
      return {
        tableData: [],
        total: 1,
        formInline: {
          status: ''
        },
        headers: {
          authorization: ''
        },
        form: {
          name: '',
          code: '',
          desc: ''
        },
        formLabelWidth: '120px',
        isDialog: false,
        isaddDialog: false,
        submitType: '',
        tips:""
      }
    },
    mounted() {
      this.getSelete(1)
    },
    methods: {
      async getSelete(page) {
        const res = await this.$api.getPageOptionsVo({
          pageNum: page,
          pageSize: 15,
          tips:this.tips
        })
        this.tableData = res.result.list
        this.total = res.result.totalCount
      },
      selete() {
        console.log(this.formInline)
      },
      async saveOrUpdate() {
        await this.$api.saveOrUpdate({
          name: this.form.name,
          value: this.form.code,
          tips: this.form.desc
        })
        this.getSelete(1)
        this.isDialog = false
        this.isaddDialog = false
        this.form = {
          name: '',
          code: '',
          desc: ''
        }
      },
      // 添加
      openDialog() {
        this.form = {
          name: '',
          code: '',
          desc: ''
        }
        this.isaddDialog = true
        this.submitType = 'add'
      },
      // 弹窗确定
      add() {
        if (this.isDialog === true) {
          this.saveOrUpdate()
        } else {
          this.saveOrUpdate()
        }
      },
      // 弹窗取消
      adds() {
        this.isaddDialog = false
        this.isDialog = false
        this.form = {
          name: '',
          code: '',
          desc: ''
        }
      },
      // 更改
      removeOpen(item) {
        console.log(item)
        this.form.name = item.name
        this.form.code = item.value
        this.form.desc = item.tips
        this.isDialog = true
      },
      async remove(code) {
        await this.$api.removeAdminPermission({
          permissionCode: code
        })
        this.getSelete(1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      },
      xuanze(val) {
        this.getSelete(val)
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
        console.log(this.multipleSelection)
      },
      orderexport() {},
      nav_details(item) {
        console.log(item)
        this.$router.push({
          name: 'orderdetails',
          query: {
            orderNo: item.orderNo
          }
        })
      },
      handleAvatarSuccess(res, file) {
        this.$notify({
          title: '上传成功',
          message: '物流单号批量上传成功',
          type: 'success'
        })
        this.getSelete(1)
      },
      beforeAvatarUpload(file) {}
    }
  }
</script>
