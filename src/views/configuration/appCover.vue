<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="APP开屏页封面修改">
        <common-form :isHD="true" :data="formData" :schema="formSchema" :loading="loading" :labelPosition="'right'"
          :submit="open2">
        </common-form>
      </el-tab-pane>
    </el-tabs>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
import CommonForm from "@/components/CommonForm";
export default {
  name: 'moneyLowest',
  data() {
    return {
      formSchema: [
        {
          type: "number-input",
          label: "跳转倒计时",
          field: "duration",
          rules: [
            {
              required: true,
              message: "请输入跳转倒计时",
              trigger: "blur",
            },
          ],
        },
        {
          type: "number-input",
          label: "封面图点击跳转地址",
          field: "url",
        },
        {
          type: "img",
          label: "APP启动封面图",
          field: "advertisement",
          rules: [
            {
              required: true,
              message: "请上传APP启动封面图",
              trigger: "blur",
            },
          ],
        },
        {
          type: "action",
        },
      ],
      formData: {},
      tips: "",
      name: ""

    }
  },
  mounted() {
    this.get()
  },
  components: {
    CommonForm
  },
  methods: {
    // 查询
    async get() {
      const {
        result,
        status
      } = await this.$api.getValueByName({
        name: 'advertisement'
      })
      if (status.code === 0) {
        this.tips = result.tips
        this.name = result.name
        this.formData = JSON.parse(result.value)
      } else {
        this.$message.error(status.msg);
      }
    },
    open2() {
      this.$confirm('提交保存后，APP开屏页封面修改将生效', 'APP开屏页封面修改将生效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.submit()
      }).catch(() => {

      });
    },
    async submit() {
      const {
        result,
        status
      } = await this.$api.saveOrUpdate({
        name: this.name,
        value: JSON.stringify(this.formData),
        tips: this.tips
      })
      if (status.code === 0) {
        this.get()
        this.$message.success('操作成功');
      } else {
        this.$message.error(status.msg);
      }
    },
  },
}
</script>
