<template>
    <div v-if="visible" class="modal-mask">
        <div class="modal-wrapper" ref="posterRef">
            <!-- Header -->
            <div class="modal-header">
                <div class="left">
                    <span class="brand">分享海报</span>
                </div>
                <img class="close" @click="emit('update:visible', false)"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379224833215782912.png" alt="">

            </div>

            <!-- Poster Card -->
            <div class="poster-card">

                <div class="inner">
                    <div class="poster-text">
                        <img class="logos" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379525576384667648.png" alt="">
                        <!-- <div class="title1">PinkWallet.com</div> -->
                        <div class="title2" v-if="mode == 1">终极全球金融资产钱包</div>
                        <div class="title2" v-if="mode == 2">成为PinkWallet经销商</div>
                    </div>
                    <img class="poster-image" v-if="mode == 1"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379524335474663424.png" mode="widthFix" />

                    <div class="feature-grid" v-if="mode == 2">
                        <div class="item" v-for="(item, index) in features" :key="index">
                            <img :src="item.icon" mode="widthFix" :style="{ width: item.w + 'px' }" class="icon" />
                            <div class="text">{{ item.text }}</div>
                        </div>
                    </div>

                    <div class="bg" v-if="mode == 2"></div>

                    <div class="poster-footer">
                        <div class="logo-box">
                            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379524475493113856.png" class="logo" />
                            <span class="brand">PinkWallet</span>
                        </div>
                        <img class="qr" :src="qrImage" alt="二维码" v-if="qrImage" />
                    </div>
                </div>

            </div>

            <!-- Bottom Buttons -->
            <div class="bottom-buttons">
                <button class="btn light" @click="downloadPoster">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382349451443134464.png" alt="">
                    下载</button>
                <button class="btn pink" @click="copyLink">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379527598043389952.png" alt="">
                    复制链接</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import QRCode from 'qrcode'
import html2canvas from 'html2canvas'
import { Toast } from 'vant'

const props = defineProps({
    link: {
        type: String,
        required: true
    },
    visible: {
        type: Boolean,
        default: false
    },
    mode: {
        type: String,
        default: 1
    }
})

const features = [
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379533547802025984.png",
        text: '佣金日结',
        w: 39
    },
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379533841885650944.png",
        text: '7*24小时响应',
        w: 21
    },
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379533989940387840.png",
        text: '股市和加密多种返佣',
        w: 22

    },
    {
        icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379534102825885696.png",
        text: '社区孵化裂变',
        w: 31
    }
]

const emit = defineEmits(['update:visible'])

const qrImage = ref('')
const posterRef = ref(null)

watch(
    () => props.visible,
    async (val) => {
        if (val) {
            qrImage.value = await QRCode.toDataURL(props.link, {
                width: 60,
                margin: 1
            })
        }
    }
)

const copyLink = () => {
    navigator.clipboard.writeText(props.link)
    Toast('已复制链接')
}

const downloadPoster = async () => {
    if (!posterRef.value) return

    // 使用 html2canvas 生成截图
    const canvas = await html2canvas(posterRef.value, {
        backgroundColor: null, // 保持透明
        useCORS: true // 允许加载远程图片
    })

    const imgData = canvas.toDataURL('image/png')
    const link = document.createElement('a')
    link.href = imgData
    link.download = `pinkwallet_poster_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
}
</script>

<style scoped lang="scss">
.feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3.6px;
    padding: 14px 20px 13px 20px;
    z-index: 999;

    .item {
        background: linear-gradient(#1b1b1b, #1a1a1a);
        border-radius: 12px;
        width: 150px;
        height: 86px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border: 1px solid #FFFFFF33;
        text-align: center;
        // padding: 20px 21px;
        color: #fff;
        font-family: MiSans;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;

        .icon {
            // width: 32px;
            // height: 32px;
            margin-bottom: 10px;
            filter: brightness(1.2) saturate(1.5); // 粉色图标可增强
        }

        .text {
            margin-top: 12px;
            font-weight: 400;
        }
    }
}

.modal-mask {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    // z-index: 999;
}

.modal-wrapper {
    position: fixed;
    bottom:0;
    background: #FFFFFF;
    border-radius: 20px;
    width: 100vw;
    height: px2vw(660px*2);
    text-align: center;
    color: #000;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 18px 24px 10px;
    border-bottom: 0.5px solid rgba(255, 255, 255, .1);
    font-weight: bold;
    font-size: 16px;

    .left {
        display: flex;
        align-items: center;

        .logo {
            width: 26px;
            height: 26px;
            margin-right: 14px;
        }

        .brand {
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            color: #000;
        }

    }


    .close {
        cursor: pointer;
        width: 24px;
        height: 24px;
    }
}

.poster-card {
    padding: 17.5px 24px 15px 24px;


    .inner {
        background: #0A0A0A;
        border-radius: 12px;
        position: relative;

        .bg {
            position: absolute;
            bottom: 19px;
            height: 86px;
            width: 100%;
            background: linear-gradient(90deg, rgba(137, 78, 93, 0.2) 0%, #EF88A3 29.65%, #EF88A3 50.25%, #EF88A3 72.55%, rgba(137, 78, 93, 0.2) 100%);
            filter: blur(60px);
            z-index: 1;

        }

        .poster-text {
            padding: 23px 0 0 20px;
            text-align: left;

            .logos {
                width: 123px;
                height: 16px;
            }

            .title1 {
                color: #fff;
                font-weight: bold;
            }

            .title2 {
                margin-top: 7px;
                font-family: MiSans;
                font-weight: 600;
                font-size: 17.74px;
                line-height: 100%;
                color: #FFFFFF;
            }
        }

        .poster-image {
            margin: 18px 0 -10px 0;
            width: 225px;
            // margin: 12px 0;
            // width: 100%;
            // border-radius: 8px;
            // z-index: 1;
        }

        .poster-footer {
            position: relative; // 👈 必须有
            z-index: 2 !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #2D2D2D;

            border-radius: 0 0 12px 12px;
            padding: 13px 20px;

            .logo-box {
                display: flex;
                align-items: center;

                .logo {
                    width: 30px;
                    height: 30px;
                    margin-right: 9px;
                }

                .brand {
                    color: #fff;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 18.67px;
                    line-height: 100%;

                }
            }

            .qr {
                width: 46px;
                height: 47px;
            }
        }
    }

}

.bottom-buttons {
    display: flex;
    gap: 10px;
    margin: 0 24px 31px 24px;
    justify-content: center;

    .btn {
        flex: 1;
        // padding: 10px 0;
        border-radius: 12px;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        width: 168px;
        height: 52px;

        &:focus,
        &:active {
            outline: none;
            border: none;
            box-shadow: none;
        }

        img {
            width: 27px;
            height: 27px;
        }

        &.light {
            background: #000;
            color: #fff;
        }

        &.pink {
            background: #EF88A3;
            color: #fff;
        }
    }
}
</style>