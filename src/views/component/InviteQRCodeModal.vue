<template>
    <div v-if="localVisible" class="modal-mask">
        <div class="modal-wrapper">
            <div class="modal-header">
                <div class="left">
                    <!-- <img class="logo" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379519159477035008.png" alt="logo" /> -->
                    <span class="brand">分享邀请码</span>
                </div>
                <img class="close" @click="closeModal"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379224833215782912.png" alt="">
            </div>

            <div class="modal-content">
                <div class="invite-code-label">邀请码</div>
                <div class="invite-code">{{ inviteCode || '--' }}</div>

                <div class="qrcode-wrapper">
                    <img v-if="qrImage" :src="qrImage" alt="二维码" />
                </div>

                <div class="footer-text">立即报名，一起赚币。</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import QRCode from 'qrcode'

// 接收 props
const props = defineProps({
    inviteCode: {
        type: String,
        required: true
    },
    visible: {
        type: Boolean,
        default: false
    }
})

// 向父组件发出事件
const emit = defineEmits(['update:visible'])

const localVisible = ref(props.visible)
const qrImage = ref('')

const generateQRCode = async () => {
    // https://pinkwallet.com/h5
    const qrUrl = process.env.VUE_APP_URL + `/pages/login/register?code=${props.inviteCode}`
    qrImage.value = await QRCode.toDataURL(qrUrl, {
        width: 200,
        margin: 2
    })
}

watch(() => props.visible, (val) => {
    localVisible.value = val
    if (val) generateQRCode()
})

const closeModal = () => {
    emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.modal-mask {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-wrapper {
    position: fixed;
    bottom:0;
    background: #FFFFFF;
    border-radius: 20px;
    width: 100vw;
    height: px2vw(660px*2);
    text-align: center;
    color: #000;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 18px 24px 10px;
    border-bottom: 0.5px solid rgba(255, 255, 255, .1);

    .left {
        display: flex;
        align-items: center;

        .logo {
            width: 26px;
            height: 26px;
            margin-right: 14px;
        }

        .brand {
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            color: #000;
        }

    }


    .close {
        cursor: pointer;
        width: 24px;
        height: 24px;
    }
}

.modal-content {
    margin-top: 38px;

    .invite-code-label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: rgba(0, 0, 0, .5);
    }

    .invite-code {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: #000;
        margin: 4px 0 6px 0;
    }

    .qrcode-wrapper {
        padding: 9px 12px;
        background: rgba(0, 0, 0, .1);
        display: inline-block;
        border-radius: 20px;
        margin-bottom: 12px;

        img {
            width: 171px;
            height: 176px;
        }
    }

    .footer-text {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #000;
        margin-bottom: 92px;
    }
}
</style>