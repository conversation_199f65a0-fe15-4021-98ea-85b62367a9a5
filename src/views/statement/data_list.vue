<template>
  <d2-container class="page" ref="returnTop">
    <div style="display: flex;justify-content: flex-start;">
      <div class="title">24小时内被浏览最多(价格页)</div>
      <el-form :inline="true"  class="demo-form-inline"
        style="background-color: #ffffff; padding: 20px">
      <el-form-item  label="按点开UV次数排序">
        <el-select  placeholder="请选择排序方式" clearable v-model="sortOrder" @change="change">
          <el-option label="从高到低" value="desc"></el-option>
          <el-option label="从低到高" value="asc"></el-option>
        </el-select>
      </el-form-item>
      </el-form>
    </div>
    <common-table :loading="loading" :table-schema="tableSchema" :table-data="tableData">
      <template #seleUser="scope">
        <el-button @click="whitelist(scope.row)" type="text">查看用户</el-button>
      </template>
      <template #action="scope">
        <el-button @click="nav_noticeLink(scope.row)" type="text">查看公告</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page1.totalCount"
        :page-size="page1.pageSize" :current-page="page1.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
     <div class="title">24小时内被搜索最多(公告页)</div>
    <common-table :loading="loading1" :table-schema="tableSchema1" :table-data="tableData1">
        <template #seleUser="scope">
        	<el-button @click="whitelist(scope.row)" type="text">查看用户</el-button>
        </template>
    		<template #action="scope">
    			<el-button @click="nav_noticeLink(scope.row)" type="text">查看公告</el-button>
    		</template>
    	</common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
    	<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page1.totalCount"
    	 :page-size="page1.pageSize" :current-page="page1.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
    		style="padding: 20px; background-color: #ffffff" @current-change="xuanze1" @size-change="xuanzeSize1">
    	</el-pagination>
    </div>
    <div ref="lineChartPay"
      :style="`width: ${width}px; height:400px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
    <div ref="lineChartUser"
      :style="`width: ${width}px; height:400px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
  </d2-container>
</template>

<script>
  import * as echarts from 'echarts'
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    listCountNoticeSearchRecord
  } from '@/api/hongyan'
  import {
    listOrderUser,
    lastDealOrder
  } from '@/api/hanxin'
  import {
    getLastLoginUser
  } from '@/api/appUserCenter'
  import {
    listShowUserRecord
  } from '@/api/mallCenter'

  export default {
    name: 'data_list',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page1: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        page2: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        },
        query1: {
          lastDay: 3,
          isSeeMe: false
        },
        tableSchema: [ // 表格架构x
          {
            label: '藏品名',
            field: 'userName'
          },
          {
            label: '点开UV数',
            field: 'uv',
          },
          {
            label: '总点开次数',
            field: 'pv',
          },
          {
            label: '藏品当前地板价',
            field: 'goodsFloorPrice',
          },
          {
            label: '24h前地板价',
            field: 'beforeGoodsFloorPrice',
          },
          {
            label: '操作',
            slot: 'action',
            width: '140px',
            fixed: 'right'
          },
        ],
        tableSchema1: [ // 表格架构x
          {
            label: '公告关键词',
            field: 'searchTerm'
          },
          {
            label: '搜索次数',
            field: 'searchNum',
          },
          {
            label: '公告链接',
            field: 'noticeLink',
            width:'500px'
          },
        ],
        tableData: [], // 表格数据
        tableData1: [], // 表格数据
        querySchema1: [ // 搜索组件架构
          {
            type: 'select',
            label: '时间：',
            field: 'lastDay',
            options: [{
              'label': '近24h换手',
              'value': 1,
            }, {
              'label': '近3日换手',
              'value': 3,
            }, {
              'label': '近7日换手',
              'value': 7,
            }]
          },
          {
            type: 'select',
            label: '是否只看自己：',
            field: 'isSeeMe',
            options: [{
                label: '否',
                value: false
              },
              {
                label: '是',
                value: true
              },
            ]
          },
        ],
        whitelistData: [],
        whitelistVisible: false,
        whitelistColumn: [{
            label: '用户名',
            prop: 'username'
          },
          {
            label: 'num',
            prop: 'payedNum'
          },
          {
            label: '类型',
            prop: 'member',
            slot: 'member'
          },
        ],
        zoom: 1,
        lineChartPay: null,
        lineChartUser: null,
        width: "",
        loading:false,
        loading1:false,
        sortOrder:null,
        sortField:1
      }
    },
    mounted() {
      this.zoom = 1 / document.body.style.zoom
      window.addEventListener("resize", () => {
        this.zoom = 1 / document.body.style.zoom
      })
      this.initLineChart()
      this.getList()
      this.getList1()
      this.getPay()
      this.getUser()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange1(data) {
        this.query = data
        this.getList(true)
      },
      xuanze(val) {
        this.page1.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page1.pageSize = val
        this.getList()
      },
      xuanze1(val) {
        this.page1.pageNum = val
        this.getList1()
      },
      // 分页
      xuanzeSize1(val) {
        this.page1.pageSize = val
        this.getList1()
      },
      getList() {
         this.loading=true
         if(this.sortOrder!=null){
            this.sortField=2
         }
         if(this.sortOrder==""){
           this.sortOrder=null
         }
        const params = {
          ...this.page1,
          pageNum: this.page1.pageNum,
          sortOrder:this.sortOrder,
          sortField:this.sortField
        }
        listShowUserRecord(params).then(res => {
          this.loading=false
          const data = res.result
          this.tableData = data.list
          this.page1.totalCount = data.totalCount
          this.page1.pageSize = data.pageSize
          this.page1.pageCount = data.pageCount
        })
      },
      getList1() {
        this.loading1=true
        const params = {
          ...this.page2,
          lastDay:1,
          pageNum: this.page2.pageNum
        }
        listCountNoticeSearchRecord(params).then(res => {
          this.loading1=false
          const data = res.result
          this.tableData1 = data.list
          this.page2.totalCount = data.totalCount
          this.page2.pageSize = data.pageSize
          this.page2.pageCount = data.pageCount
        })
      },
      // 发表/下架
      statusToggle(id, status) {
        const changeText = status === 0 ? '发表' : '下架'
        this.$confirm(`确定${changeText}该条公告吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          noticePublish({
            id,
            status: status === 0 ? 1 : 0
          }).then((res) => {
            this.$message.success(res.status.msg)
            this.getList()
          })
        })
      },
      /**
       *
       *
       *
       * @param row
       * @returns {Promise<void>}
       */
      async whitelist(row, type) {
        this.userId = row.userId
        this.whitelistVisible = true
        const {
          result: {
            list
          }
        } = await this.$api.listOrderUser({
          userId: row.userId,
          pageSize: 1000,
          pageNum: 1,
        })
        this.whitelistData = list
      },
      nav_noticeLink(item) {
        window.open(item.noticeLink, "name",
          "height=900, width=414, top=0, left=2, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=no, status=no"
          );
      },
      getPay() {
        lastDealOrder({}).then(res => {
          const result = res.result
          this.lineChartPay.setOption({
            xAxis: {
              data: result.lastDealOrderLists.map(item => item.days)
            },
            series: [{
              name: '交易数',
              type: 'line',
              stack: 'Total',
              data: result.lastDealOrderLists.map(item => item.dealOrderNum)
            }],
          })
        })
      },
      getUser() {
        getLastLoginUser({}).then(res => {
          const result = res.result
          this.lineChartUser.setOption({
            xAxis: {
              data: result.lastDealOrderLists.map(item => item.days)
            },
            series: [{
              name: '人数',
              type: 'line',
              stack: 'Total',
              data: result.lastDealOrderLists.map(item => item.loginUserNum)
            }],
          })
        })
      },
      initLineChart() {
        this.lineChartPay = echarts.init(this.$refs.lineChartPay)
        this.lineChartUser = echarts.init(this.$refs.lineChartUser)
        this.lineChartPay.setOption({
          title: {
            text: '近10日交易人数折线图'
          },
          legend: {
            data: ['10日交易人数']
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: []
        })
        this.lineChartUser.setOption({
          title: {
            text: '近10日登陆人数折线图'
          },
          legend: {
            data: ['人数']
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: []
        })
      },
      change(){
        this.getList()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .title{
    margin:20px 0px;
    font-weight:600;
  }
</style>
