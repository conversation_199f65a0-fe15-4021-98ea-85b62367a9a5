<template>
  <d2-container class="page" ref="returnTop">
    <div class="shenfen">
      <el-radio-group v-model="userType" size="medium" @input="input">
          <el-radio label="common" border size="medium">散户</el-radio>
          <el-radio  label="primary" border size="medium">主力</el-radio>
          <el-radio  label="all" border size="medium">全部</el-radio>
        </el-radio-group>
    </div>
    <div ref="lineChartUser"
      :style="`width: ${width}px; height:500px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
  </d2-container>
</template>

<script>
  import * as echarts from 'echarts'
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    qifeiChart,
    getRealMarketPrice
  } from '@/api/waliangge'
  export default {
    name: 'data_list',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page1: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        page2: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        },
        query1: {
          lastDay: 3,
          isSeeMe: false
        },
        zoom: 1,
        lineChartPay: null,
        lineChartUser: null,
        width: "",
        loading: false,
        loading1: false,
        sortOrder: null,
        sortField: 1,
        marketPrice: '',
        text: "持仓成本（含）",
        userType:"common",
        interval:""
      }
    },
    mounted() {
      this.zoom = 1 / document.body.style.zoom
      window.addEventListener("resize", () => {
        this.zoom = 1 / document.body.style.zoom
      })
      this.interval = setInterval(()=>{
        this.getMarketPrice()
      },10000)
      this.getMarketPrice()
      this.initLineChart()
      // this.getPay()

    },
    beforeDestroy() {
        // 在组件销毁前清理定时器
        clearInterval(this.interval);
        console.log('组件即将销毁，清理定时器');
      },
      destroyed() {
        // 进一步的清理工作，如果有的话
        console.log('组件已销毁');
      },
    methods: {
      getUser() {
        qifeiChart({
          type: 1,
          userType: this.userType,
          marketPrice: this.marketPrice,
          searchPrice: this.marketPrice,
          size:150
        }).then(res => {
          const result = res.result
          let arrayNum, arrayPrice;
          result.chartInfo.forEach((item, index) => {
            if (index < 16) {
              console.log(index)
              item.price = {
                value: item.price,
                itemStyle: {
                  color: '#a90000'
                }
              }
            }
          })
          console.log(result.chartInfo)
          let marketPrice = this.marketPrice
          this.lineChartUser.setOption({
            xAxis: {
              data: result.chartInfo.map(item => item.price)
            },
            yAxis: {
              data: result.chartInfo.map(item => item.price)
            },
            series: [{
              name: '份数',
              type: 'bar',
              markLine: {
                label: {
                  show: true,
                  formatter(){
                    return `市价${marketPrice}`
                  }
                },
                data: [
                  {
                    xAxis: `${marketPrice}`
                  }
                ],
                lineStyle: {
                  width: 2,
                  color: '#979797'
                }
              },
              data: result.chartInfo.map(item => item.quantity)
            }],
          })
        })
      },
      initLineChart() {
        this.lineChartUser = echarts.init(this.$refs.lineChartUser)
        this.lineChartUser.setOption({
          title: {
            text: this.text
          },
          dataZoom: [
                {
                    type: 'inside', // 类型为滑块型数据区域缩放组件
                    show: true, // 显示数据缩放组件
                    start: 40, // 数据窗口范围的起始百分比
                    end: 60, // 数据窗口范围的结束百分比
                    handleSize: '40%', // 滑块的大小，可以调整
                    xAxisIndex: [0], // 控制x轴的索引，如果是多轴，可以指定控制哪一轴
                    backgroundColor: 'rgba(0,0,0,0)', // 背景颜色，默认透明
                    fillerColor: 'rgba(125,139,176,0.2)', // 填充色，即滑动条的颜色
                    borderColor: '#ccc', // 边框颜色
                    showDetail: false, // 是否显示详细数值信息
                    realtime: false, // 是否实时更新数据视图
                }
             ],
          legend: {
            data: ['价格']
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            data: [],
            name: '价格',
          },
          yAxis: {
            name: '份数',
            data: [],
            type: 'value'
          },
          series: [
            {}
          ]
        })
      },
      getMarketPrice() {
        getRealMarketPrice({

        }).then(res => {
          this.marketPrice = res.result
          console.log(this.marketPrice)
          this.getUser()
        })
      },
      timeToTimestamp(date) {
        var date = new Date(date);
        var Y = date.getFullYear() + '年';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '月';
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '日';
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
        var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        return Y + M + D + h + m
      },
      input(e){
        console.log(e)
        this.userType = e
        this.getMarketPrice()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .title {
    margin: 20px 0px;
    font-weight: 600;
  }
  .shenfen{
    display: flex;
    justify-content: flex-end;
  }
</style>
