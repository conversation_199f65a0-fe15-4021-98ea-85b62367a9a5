<template>
  <d2-container class="page"  v-loading="loading" element-loading-text="数据加载中">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="选择日期">
        <el-date-picker v-model="formInline.day" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getFinance">查询</el-button>
      </el-form-item>
    </el-form>
    <el-descriptions class="margin-top" :colon="false" :labelStyle="label_style" :contentStyle="content_style" :column="3" border>
      <el-descriptions-item label="">
        <!-- {{financeInfo.paOrderNum}} -->
      </el-descriptions-item>
      <el-descriptions-item label="来自宝付总笔数（后台订单）：">
        {{financeInfo.baofooOrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="来自连连总笔（后台订单）：">
        {{financeInfo.llpOrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="平安入账总金额（实际）：">
        <el-input v-model="pinganPrice" type="number" placeholder="请输入平安入账总金额" size="mini"></el-input>
        <!-- {{financeInfo.paOrderAmount}} -->
      </el-descriptions-item>
      <el-descriptions-item label="来自宝付总金额：">
        {{financeInfo.baofooOrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="来自连连总金额：">
        {{financeInfo.llpOrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item>
      </el-descriptions-item>
      <el-descriptions-item label="手续费后金额：">
        {{financeInfo.baofooOrderAmountAfterFee}}
      </el-descriptions-item>
      <el-descriptions-item label="手续费后金额：">
        {{financeInfo.llpOrderAmountAfterFee}}
      </el-descriptions-item>
      <el-descriptions-item label="平安入账是否匹配（宝付+连连）：" :span="1">
        <span style="color:forestgreen;" v-if="(financeInfo.baofooOrderAmountAfterFee+financeInfo.llpOrderAmountAfterFee)-pinganPrice==0">是</span>
        <span style="color:red;" v-else>否</span>
      </el-descriptions-item>
      <el-descriptions-item label="差值为：" :span="2">
        <span style="color:red;" >
            {{(financeInfo.baofooOrderAmountAfterFee+financeInfo.llpOrderAmountAfterFee)-pinganPrice}}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="支付宝实际收款总笔数（实际）：">
         <el-input v-model="zfbNum" type="number" placeholder="请输入支付宝实际收款总笔数" size="mini"></el-input>
        <!-- {{financeInfo.alipayRealOrderNum}} -->
      </el-descriptions-item>
      <el-descriptions-item label="支付宝订单总笔数（后台订单）：">
        {{financeInfo.alipayOrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="用户流水支付宝订单数（后台流水）：">
        {{financeInfo.alipayNotifyNum}}
      </el-descriptions-item>
      <el-descriptions-item label="支付宝实际收款总金额（实际）：">
        <el-input v-model="zfbPriceNum" type="number" placeholder="请输入支付宝实际收款总金额" size="mini"></el-input>
        <!-- {{financeInfo.alipayRealOrderNum}} -->
      </el-descriptions-item>
      <el-descriptions-item label="支付宝订单总金额：">
        {{financeInfo.alipayOrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="用户流水支付宝金额：">
        {{financeInfo.alipayNotifyAmount}}
      </el-descriptions-item>
      <el-descriptions-item>
      </el-descriptions-item>
      <el-descriptions-item label="手续费后金额：">
        {{financeInfo.alipayOrderAmountAfterFee}}
      </el-descriptions-item>
      <el-descriptions-item label="手续费后金额：">
        {{financeInfo.alipayNotifyAmountAfterFee}}
      </el-descriptions-item>
      <el-descriptions-item label="支付宝实际收款是否匹配订单：" :span="1">
        <span style="color:forestgreen;" v-if="financeInfo.alipayOrderAmountAfterFee-zfbPriceNum==0">是</span>
        <span style="color:red;" v-else>否</span>
      </el-descriptions-item>
      <el-descriptions-item label="差值为：" :span="2" >
        <span style="color:red;">
          {{financeInfo.alipayOrderAmountAfterFee-zfbPriceNum}}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="余额不为零用户数：">
        {{financeInfo.balanceAmountNot0Num}}
      </el-descriptions-item>
      <el-descriptions-item label="平安余额不为零用户数：" :span="2">
         <el-input v-model="paBalanceAmountNot0Num" type="number" placeholder="请输入平安余额不为零用户数" size="mini"></el-input>
        <!-- {{financeInfo.paBalanceAmountNot0Num}} -->
      </el-descriptions-item>

      <el-descriptions-item label="用户总余额：" >
        {{financeInfo.balanceAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="平安用户总余额：" :span="2">
         <el-input v-model="paBalanceAmount" type="number" placeholder="请输入平安用户总余额" size="mini"></el-input>
        <!-- {{financeInfo.paBalanceAmount}} -->
      </el-descriptions-item>
      <el-descriptions-item label="用户余额和平安是否一一匹配：" :span="3">
        <span style="color:forestgreen;" v-if="financeInfo.balanceAmountNot0Num==paBalanceAmountNot0Num&&financeInfo.balanceAmount==paBalanceAmount">是</span>
        <span style="color:red;" v-else>否</span>
      </el-descriptions-item>

      <el-descriptions-item label="运营侧总订单数：">
        {{financeInfo.u0OrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="支付侧余额支付 + 支付宝支付 + 宝付支付 + 连连支付 订单数：" :span="2">
        {{financeInfo.payOrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="运营侧总订单金额：">
        {{financeInfo.u0OrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="支付侧总订单金额（从用户流水表来）：" :span="2">
        {{financeInfo.payOrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="运营订单与支付订单是否匹配：" :span="3">
        <span style="color:forestgreen;" v-if="financeInfo.payOrderIsRight==1">是</span>
        <span style="color:red;" v-else>否</span>
      </el-descriptions-item>
      <el-descriptions-item label="4卖给0订单数：">
        {{financeInfo.m4u0OrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="0卖给4订单数：">
        {{financeInfo.m0u4OrderNum}}
      </el-descriptions-item>
      <el-descriptions-item label="4前二日12点总余额：">
        {{financeInfo.u4BalanceAmount0amLast2Day}}
      </el-descriptions-item>
      <el-descriptions-item label="4卖给0订单金额：">
        {{financeInfo.m4u0OrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="0卖给4订单金额：">
        {{financeInfo.m0u4OrderAmount}}
      </el-descriptions-item>
      <el-descriptions-item label="4最近12点理论总余额：">
        {{financeInfo.u4BalanceAmount0amLastDayCalculated}}
      </el-descriptions-item>
      <el-descriptions-item label="4最近12点实际总余额：">
        {{financeInfo.u4BalanceAmount0amLastDay}}
      </el-descriptions-item>
      <el-descriptions-item label="实际总余额与理论值是否匹配：" :span="2">
        <span style="color:forestgreen;" v-if="financeInfo.u4BalanceIsRight==1">是</span>
        <span style="color:red;" v-else>否</span>
      </el-descriptions-item>
    </el-descriptions>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    verification
  } from '@/api/hanxin'
  export default {
    name: 'finance',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        label_style: {
          'width': "300px",
          'text-align': 'center',
        },
        content_style: {
          'width': "200px",
          'text-align': 'center',
        },
        financeInfo: {},
        formInline: {
          day: ""
        },
        pinganPrice:0,
        zfbPriceNum:0,
        zfbNum:0,
        paBalanceAmountNot0Num:0,
        paBalanceAmount:0,
        loading:false
      }
    },
    mounted() {
       const t1 = new Date().valueOf()
      this.formInline.day= this.timestampToTime(t1)
      this.getFinance()
    },
    methods: {
      // 分页
      getFinance() {
        this.loading=true
        if(this.formInline.day==''){
           this.$message.error('请先选择日期后查询')
        }else{
          verification({
            day:this.formInline.day
          }).then(res => {
             this.loading=false
            this.financeInfo = res.result
          })
        }
      },
      timestampToTime(cjsj){
        //时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var date = new Date(cjsj);
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth()+1) : date.getMonth() + 1) + '-';
        var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '';
        var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
        var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
        return  Y + M + D
      }
    }
  }
</script>

<style lang="scss" scoped>
  .label_style {
    width: 100px !important;
  }
</style>
