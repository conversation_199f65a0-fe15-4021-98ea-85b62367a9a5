<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData" :loading="loading">

      <template #seleUser="scope">
        <el-button @click="whitelist(scope.row)" type="text">查看用户</el-button>
      </template>
      <template #action="scope">
        <el-button @click="nav_noticeLink(scope.row)" type="text">查看公告</el-button>
      </template>
    </common-table>

    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
    <el-dialog title="" :visible.sync="whitelistVisible" width="60%">
      <el-table :data="whitelistData" border v-loading="dialogLoading">
        <el-table-column :prop="item.prop" :label="item.label" width="150" v-for="(item,index) in whitelistColumn"
          :key="index">
          <template v-if="item.prop=='member'" scope="scope">
            <span v-if="scope.row.member==1">自己</span>
            <span v-if="scope.row.member==2">他人</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="pageModel.totalCount"
          :page-size="pageModel.pageSize" :current-page="pageModel.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
          style="padding: 20px; background-color: #ffffff" @current-change="xuanzeModel" @size-change="xuanzeSizeModel">
        </el-pagination>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    articleList,
    noticePublish
  } from '@/api/hongyan'
  import {
    listUserOrderAssay,
    listOrderUser
  } from '@/api/hanxin'

  export default {
    name: 'proportion',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        pageModel: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          lastDay: 3,
          isSeeMe: false
        },
        tableSchema: [ // 表格架构x
          {
            label: '藏品',
            field: 'username'
          },
          {
            label: '换手笔数',
            field: 'payedNum',
          },
          {
            label: '换手金额',
            field: 'payAmount',
          },
          {
            label: '平均成交价',
            field: 'averageAmount',
          },
          {
            label: '查看用户',
            slot: 'seleUser',
          },
          {
            label: '持仓比例 = 控盘比',
            field: 'scale',
          },
          {
            label: '操作',
            slot: 'action',
            width: '140px',
            fixed: 'right'
          }
        ],
        tableData: [], // 表格数据
        querySchema: [ // 搜索组件架构
          {
            type: 'select',
            label: '时间：',
            field: 'lastDay',
            options: [{
              'label': '近24h换手',
              'value': 1,
            }, {
              'label': '近3日换手',
              'value': 3,
            }, {
              'label': '近7日换手',
              'value': 7,
            }]
          },
          {
            type: 'select',
            label: '只看他人：',
            field: 'isSeeMe',
            options: [{
                label: '是',
                value: false
              },
              {
                label: '否',
                value: true
              },
            ]
          },
          {
            type: 'input',
            label: '藏品名称：',
            field: 'nickname',
          },
        ],
        whitelistData: [],
        whitelistVisible: false,
        whitelistColumn: [{
            label: '用户名',
            prop: 'username'
          },
          {
            label: '数量',
            prop: 'goodsNnm'
          },
          {
            label: '用户身份',
            prop: 'member',
            slot: 'member'
          },
        ],
        loading:false,
        dialogLoading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      xuanze(val) {
        this.page.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      xuanzeModel(val) {
        this.pageModel.pageNum = val
        this.whitelist()
      },
      // 分页
      xuanzeSizeModel(val) {
        this.pageModel.pageSize = val
        this.whitelist()
      },
      getList() {
        this.loading=true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum
        }
        listUserOrderAssay(params).then(res => {
          this.loading=false
          const data = res.result
          this.tableData = data.list
          this.page.totalCount = data.totalCount
          
          this.page.pageCount = data.pageCount
        })
      },
      // 发表/下架
      statusToggle(id, status) {
        const changeText = status === 0 ? '发表' : '下架'
        this.$confirm(`确定${changeText}该条公告吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          noticePublish({
            id,
            status: status === 0 ? 1 : 0
          }).then((res) => {
            this.$message.success(res.status.msg)
            this.getList()
          })
        })
      },
      /**
       *
       *
       *
       * @param row
       * @returns {Promise<void>}
       */
      async whitelist(row, type) {
        this.dialogLoading=true
        if (row) {
          this.userId = row.userId
        }
        this.whitelistVisible = true
        const res = await this.$api.listOrderUser({
          userId: this.userId,
          pageSize: this.pageModel.pageSize,
          pageNum: this.pageModel.pageNum
        })
        console.log(res)

        this.whitelistData = res.result.list
        this.pageModel.totalCount = res.result.totalCount
        this.pageModel.pageSize = res.result.pageSize
        this.pageModel.pageCount = res.result.pageCount
         this.dialogLoading=false
      },
      nav_noticeLink(item) {
        window.open(item.noticeLink, "name",
          "height=900, width=414, top=0, left=2, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=no, status=no"
        );
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
