<template>
  <d2-container class="page" ref="returnTop">
    <div class="shenfen">
      <el-radio-group v-model="userType" size="medium" @input="input">
          <el-radio label="common" border size="medium">散户</el-radio>
          <el-radio  label="primary" border size="medium">主力</el-radio>
          <el-radio  label="all" border size="medium">全部</el-radio>
        </el-radio-group>
    </div>
    <div ref="lineChartUser"
      :style="`width: ${width}px; height:500px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
  </d2-container>
</template>

<script>
  import * as echarts from 'echarts'
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    getKLine,
  } from '@/api/waliangge'
  export default {
    name: 'data_list',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page1: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        page2: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        },
        query1: {
          lastDay: 3,
          isSeeMe: false
        },
        zoom: 1,
        lineChartPay: null,
        lineChartUser: null,
        width: "",
        loading: false,
        loading1: false,
        sortOrder: null,
        sortField: 1,
        marketPrice: '',
        text: "新下单",
        userType:"common",
        interval:"",
      }
    },
    mounted() {
      this.zoom = 1 / document.body.style.zoom
      window.addEventListener("resize", () => {
        this.zoom = 1 / document.body.style.zoom
      })
      this.initLineChart()
      this.getKlink()
    },
    beforeDestroy() {
        // 在组件销毁前清理定时器
        clearInterval(this.interval);
        console.log('组件即将销毁，清理定时器');
      },
      destroyed() {
        // 进一步的清理工作，如果有的话
        console.log('组件已销毁');
      },
    methods: {
      getKlink() {
        getKLine({
          interval: 1,
          pageNum:1,
          pageSize: 100,
        }).then(res => {
          let kLineData = []
          res.result.list.forEach((item) => {
          	let arr = [item.openPrice, item.closePrice, item.lowPrice, item.highPrice, item
          		.startTime * 60
          	]
          	kLineData.push(arr)
          })
          console.log(kLineData)
          this.lineChartUser.setOption({
            xAxis: {
              type: 'category',
              data: kLineData
            },
            yAxis: {
              data: kLineData
              // min: result.chartInfo[0].price.value, // 根据实际情况设置最小值
              // max: result.chartInfo[30].price.value, // 根据实际情况设置最大值
            },
            series: [{
              name: 'K线图',
              type: 'k',
              data: kLineData
            }],
          })
        })
      },
      initLineChart() {
        this.lineChartUser = echarts.init(this.$refs.lineChartUser)
        this.lineChartUser.setOption({
          title: {
            text: this.text
          },
          tooltip: {
          	trigger: 'item', // 触发类型为'item'，表示鼠标 hover 到具体的 K 线上时显示 tooltip

          	formatter: function(params) {
          		var redDiv =
          			'<span style="display:inline-block;width:6px;height:10px;background-color:#EC4068;margin-right:5px;"></span>';
          		var lvseDiv =
          			'<span style="display:inline-block;width:6px;height:10px;background-color:#6CFF8A;margin-right:5px;"></span>';
          		console.log(params.data[5] * 1000)
          		const now = new Date(params.data[5] * 1000);
          		const year = now.getFullYear();
          		const month = ("0" + (now.getMonth() + 1)).slice(-2); // 月份是从0开始计数的，需要加1
          		const day = ("0" + now.getDate()).slice(-2);
          		const hours = ("0" + now.getHours()).slice(-2);
          		const minutes = ("0" + now.getMinutes()).slice(-2);
          		const seconds = ("0" + now.getSeconds()).slice(-2);
          		// // params 包含了当前被 hover 的数据项的信息
          		var date = `时间：${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 假设第一个字段是日期
          		var open = params.data[1];
          		var close = params.data[2];
          		var highest = params.data[4];
          		var lowest = params.data[3];

          		// 自定义 tooltip 显示的内容
          		return date + '<br />' +
          			redDiv + '起始均价: ' + open + '<br />' +
          			redDiv + '结尾均价: ' + close + '<br />' +
          			lvseDiv + '最小均价: ' + lowest + '<br />' +
          			lvseDiv + '最大均价: ' + highest;
          	},
          	backgroundColor: 'rgba(20, 24, 22, 0.8)', // 自定义背景色
          	textStyle: {
          		color: '#fff', // 自定义 tooltip 文字颜色为深灰色
          		fontSize: 12,
          	}
          },
           dataZoom: [
                  {
                      type: 'slider', // 类型为滑块型数据区域缩放组件
                      show: true, // 显示数据缩放组件
                      start: 25, // 数据窗口范围的起始百分比
                      end: 75, // 数据窗口范围的结束百分比
                      handleSize: '50%', // 滑块的大小，可以调整
                      xAxisIndex: [0], // 控制x轴的索引，如果是多轴，可以指定控制哪一轴
                      backgroundColor: 'rgba(0,0,0,0)', // 背景颜色，默认透明
                      fillerColor: 'rgba(125,139,176,0.2)', // 填充色，即滑动条的颜色
                      borderColor: '#ccc', // 边框颜色
                      showDetail: false, // 是否显示详细数值信息
                      realtime: false, // 是否实时更新数据视图
                  }
              ],
          legend: {
            data: ['份数']
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            data: [],
            name: '价格',
          },
          yAxis: {
            name: '份数',
            data: [],
            type: 'value',
          },
          series: [
            {}
          ],
          markLine: {
                  symbol: ['none', 'none'], // 隐藏两端的标记符号
                  silent: true, // 点击时不响应
                  data: [{
                      yAxis: 15, // 这里是你的中位数值
                      lineStyle: {
                          type: 'solid',
                          width: 1,
                          color: 'red' // 线条颜色
                      },
                      label: {
                          show: true, // 是否显示标签
                          position: 'end', // 标签位置，可选值有'inside', 'left', 'right', 'top', 'bottom', 'end'
                          formatter: '{b}: {c}' // 标签内容，这里可以根据需要自定义
                      }
                  }]
              }
        })
      },
      timeToTimestamp(date) {
        var date = new Date(date);
        var Y = date.getFullYear() + '年';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '月';
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '日';
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
        var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        return Y + M + D + h + m
      },
      input(e){
        console.log(e)
        this.userType = e
        this.getMarketPrice()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .title {
    margin: 20px 0px;
    font-weight: 600;
  }
  .shenfen{
    display: flex;
    justify-content: flex-end;
  }
</style>
