<template>
  <d2-container class="page" ref="returnTop">
    <common-table :table-schema="tableSchema" :table-data="tableData"  :loading="loading">
      <template #action="scope">
        <el-button @click="submit(scope.row)" type="text">不再看</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
    	<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
    	 :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
    		style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
    	</el-pagination>
    </div>
    <el-dialog title="" :visible.sync="whitelistVisible">
      <el-table :data="whitelistData" border>
        <el-table-column :prop="item.prop" :label="item.label" width="150" v-for="(item,index) in whitelistColumn"
          :key="index">
          <template v-if="item.prop=='member'">
            {{item.member==1?'自己':'他人'}}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  // import { mapActions } from 'vuex'
  import {
    articleList,
    noticePublish
  } from '@/api/hongyan'
  import {
    listGoodsCollectionOnly,
listOrderUser
  } from '@/api/mallCenter'
  export default {
    name: 'gzz',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          lastDay: 3,
          isSeeMe: false
        },
        tableSchema: [ // 表格架构x
          {
            label: '藏品名',
            field: 'goodsName'
          },
          {
            label: '最后一笔订单时间',
            field: 'joinTime',
          },
          {
            label: '距离目前时间',
            field: 'gapCurrentTimeHour',
          },
          {
            label: '操作',
            slot: 'action',
            fixed: 'right'
          }
        ],
        tableData: [], // 表格数据
        whitelistData: [],
        whitelistVisible: false,
        whitelistColumn: [{
            label: '用户名',
            prop: 'username'
          },
          {
            label: 'num',
            prop: 'payedNum'
          },
          {
            label: '类型',
            prop: 'member',
            slot: 'member'
          },
        ],
        loading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
     xuanze(val) {
     	this.page.pageNum  = val
     	this.getList()
     },
     // 分页
     xuanzeSize(val) {
     	this.page.pageSize = val
     	this.getList()
     },
      getList() {
        this.loading=true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum
        }
        listGoodsCollectionOnly(params).then(res => {
          const data = res.result
          this.tableData = data.list
          this.page.totalCount = data.totalCount
          
          this.page.pageCount = data.pageCount
          this.loading=false
        })
      },
      async submit(item) {
       const res = await this.$api.goodsCollectionOnlyHide({
       	tid: item.tid,
       })
        this.$message.success('操作成功')
       this.getList()
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
