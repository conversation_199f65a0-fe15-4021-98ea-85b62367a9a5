<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showExport="true"
      @onExport="onExport"></common-query>
    <div style="margin-bottom: 10px;">
      <el-button :class="{'el-button--primary':index==sunIndex}"  @click="check(index,item)"
        v-for="(item,index) in buttonList">{{item.label}}</el-button>
      <el-button :class="{'el-button--primary':isPercentage}"  @click="checkPercentage()"
         >按仓位百分比排序</el-button>
    </div>
    <common-table :table-schema="tableSchema" :table-data="tableData" :loading="loading">
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  // import { mapActions } from 'vuex'
  import {
    goodsAmountList,
  } from '@/api/appUserCenter'
  export default {
    name: 'freightSpace',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          sortField:1,
          dayNum:1,
          sortOrder:'desc',
          member:0,
          isPercentage:false
        },
        tableSchema: [ // 表格架构x
          {
            label: '用户昵称',
            field: 'nickname'
          },
          {
            label: '用户地址',
            field: 'contractAddress',
          },
          {
            label: '当前仓位',
            field: 'lastDayAmount',
          },
          {
            label: '仓位百分比',
            field: 'changeAmountRadio',
          },
          {
            label: '仓位变化',
            field: 'changeAmount',
          },
        ],
        tableData: [], // 表格数据
        querySchema: [ // 搜索组件架构
          {
            type: 'select',
            label: '按仓位：',
            field: 'sortOrder',
            options: [{
              label: '从高到低',
              value: 'desc',
            }, {
              label: '从低到高',
              value: 'asc',
            }]
          },
          {
            type: 'select',
            label: '排序方式：',
            field: 'sortField',
            options: [{
              label: '当前仓位',
              value: 1,
            }, {
              label: '仓位变化',
              value: 2,
            },
            {
              label: '仓位变化百分比',
              value: 3,
            }]
          },
          {
            type: 'inputrange',
            label: '按仓位：',
            field: 'minLastDayAmount',
            field2: 'maxLastDayAmount',
            placeholder: '请输入最低仓位',
            placeholder2: '请输入最高仓位'
          },
          {
            type: 'input',
            label: '仓位快照：',
            field: 'dayNum',
            placeholder: '请输入（前1日-前30日）',
          },
          {
            type: 'select',
            label: '身份：',
            field: 'member',
            options: [{
              label: '全部',
              value: '',
            },{
              label: '大户',
              value: 0,
            }, {
              label: '主力',
              value: 4,
            }]
          },
        ],
        loading: false,
        dialogLoading: false,
        buttonList: [{
          label: '最近1日赚钱最多',
          sortOrder: 'desc',
          dayNum: 1,
          sortField: 2,
        }, {
          label: '最近1日亏钱最多',
          sortOrder: 'asc',
          dayNum: 1,
          sortField: 2
        }, {
          label: '最近3日赚钱最多',
          sortOrder: 'desc',
          dayNum: 3,
          sortField: 2
        }, {
          label: '最近3日亏钱最多',
          sortOrder: 'asc',
          dayNum: 3,
          sortField: 2
        }, {
          label: '最近30日赚钱最多',
          sortOrder: 'desc',
          dayNum: 30,
          sortField: 2
        }, {
          label: '最近30日亏钱最多',
          sortOrder: 'asc',
          dayNum: 30,
          sortField: 2
        }],
        sunIndex: -1
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      xuanze(val) {
        this.page.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      xuanzeModel(val) {
        this.pageModel.pageNum = val
        this.whitelist()
      },
      // 分页
      xuanzeSizeModel(val) {
        this.pageModel.pageSize = val
        this.whitelist()
      },
      getList() {
        this.loading = true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum
        }
        goodsAmountList(params).then(res => {
          this.loading = false
          const data = res.result
          this.tableData = data.list
          this.page.totalCount = data.totalCount
          this.page.pageCount = data.pageCount
        })
      },
      // 发表/下架
      statusToggle(id, status) {
        const changeText = status === 0 ? '发表' : '下架'
        this.$confirm(`确定${changeText}该条公告吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          noticePublish({
            id,
            status: status === 0 ? 1 : 0
          }).then((res) => {
            this.$message.success(res.status.msg)
            this.getList()
          })
        })
      },
      /**
       *
       *
       *
       * @param row
       * @returns {Promise<void>}
       */
      async whitelist(row, type) {
        this.dialogLoading = true
        if (row) {
          this.userId = row.userId
        }
        this.whitelistVisible = true
        const res = await this.$api.listOrderUser({
          userId: this.userId,
          pageSize: this.pageModel.pageSize,
          pageNum: this.pageModel.pageNum
        })
        console.log(res)

        this.whitelistData = res.result.list
        this.pageModel.totalCount = res.result.totalCount
        this.pageModel.pageSize = res.result.pageSize
        this.pageModel.pageCount = res.result.pageCount
        this.dialogLoading = false
      },
      /**
       * 导出订单
       */
      async onExport(data) {
        const res = await this.$api.goodsAmountExport(data)
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '仓位' + Date.now() + '.csv')
          this.$message.success('导出成功')
        }
      },
      check(index, item) {
        let member=this.query.member
        this.sunIndex = index
        this.query = {
          ...item,
           member
        }
        if(this.isPercentage){
         this.query.sortField=3
        }else{
          this.query.sortField=2
        }
        this.getList(true)
      },
      checkPercentage(){
        this.isPercentage=!this.isPercentage
        if(this.isPercentage){
         this.query.sortField=3
        }else{
          this.query.sortField=2
        }
        this.$forceUpdate()
        this.getList(true)
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
