<template>
	<d2-container class="page">
		<el-tabs type="border-card">
			<el-tab-pane label="第三方开发Api">
				<common-table :table-schema="tableSchema" :table-data="tableData" :showSelection="false">
					<template #action-header>
						<el-button type="primary" size="mini" @click="openView()">添加授权客户端</el-button>
					</template>
					<template #action="scope">
						<el-button @click="openView(scope.row)" type="text">修改</el-button>
					</template>
					<template #private-key="scope">
						<el-button type="primary" size="mini" @click="openKey(scope.row)">查看</el-button>
					</template>
				</common-table>
			</el-tab-pane>
		</el-tabs>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getSelete">
			</common-pagination>
		</div>
		<el-dialog title="添加授权客户端" :visible.sync="dialogVisible">
			<el-form label-width="120px">
				<el-form-item label="应用名称:">
					<el-input v-model="form.appName" placeholder="请输入应用名称"></el-input>
				</el-form-item>
				<el-form-item label="应用编码:">
					<el-input v-model="form.appCode" placeholder="请输入应用编码"></el-input>
				</el-form-item>
				<el-form-item label="应用描述:">
					<el-input v-model="form.appDesc" placeholder="请输入应用描述"></el-input>
				</el-form-item>
				<el-form-item label="客户端回调地址:">
					<el-input v-model="form.redirectUrl" placeholder="请输入客户端回调地址"></el-input>
				</el-form-item>
				<el-form-item label="授权范围:">
					<el-input v-model="form.scope" placeholder="请输入授权范围"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible=false">关闭</el-button>
				<el-button type="primary" @click="addItem()">确认</el-button>
			</div>
		</el-dialog>
		<el-dialog
		  title="privateKey 私钥"
		  :visible.sync="privateKeyVisible"
		  width="35%">
		  <span>{{privateKeyTest}}</span>
		  <span slot="footer" class="dialog-footer">
		    <el-button @click="privateKeyVisible = false">取 消</el-button>
		    <el-button type="primary" @click="copy()">一键复制</el-button>
		  </span>
		</el-dialog>
	</d2-container>
</template>

<script>
	import util from '@/libs/util.js'
	import CommonTable from '@/components/CommonTable'
	import CommonPagination from '@/components/CommonPagination'
	export default {
		name: 'moneyLowest',
		components: {
			CommonTable,
			CommonPagination
		},
		data() {
			return {
				tableData: [],
				total: 1,
				numberValidateForm: {
					result: ''
				},
				headers: {
					authorization: ''
				},
				formLabelWidth: '120px',
				isDialog: false,
				result: '',
				tableSchema: [ // 表格架构
					{
						label: '名称',
						field: 'appName',
						width: '200px'
					},
					{
						label: 'clientId',
						field: 'clientId',
						width: '200px'
					},
					{
						label: 'clientSecret',
						field: 'clientSecret',
						width: '200px'
					},
					{
						label: 'privateKey 私钥',
						slot: 'private-key',
					},
					{
						label: 'appCode',
						field: 'appCode',
						width: '200px'
					},
					{
						label: 'appDesc',
						field: 'appDesc',
						width: '200px'
					},
					{
						label: '创建时间',
						field: 'createAt',
						width: '200px'
					},
					{
						label: '修改时间',
						field: 'updateAt',
						width: '200px'
					},
					{
						label: '授权范围',
						field: 'scope',
					},
					{
						label: 'refreshToken过期时间',
						field: 'refreshTokenExpire',
					},
					{
						label: '操作',
						slot: 'action',
						headerSlot: 'action-header',
						width: '140px',
						fixed: 'right'
					}
				],
				page: {
					totalCount: 0,
					pageSize: 10,
					pageNum: 1
				}, // 分页数据
				dialogVisible: false,
				form: {

				},
				num: '1',
				multipleSelection: [],
				changeLifeDay: "",
				numLength: 0,
				type: "",
				privateKeyVisible:false,
				privateKeyTest:""
				}
		},
		mounted() {
			console.log(util.cookies.get('token'))
			this.getSelete()
		},
		methods: {
			// 查询
			async getSelete() {
				const res = await this.$api.clientsList({})
				this.tableData = res.result.list
				this.page.totalCount = res.result.totalCount
			},
			// 添加
			async addItem() {
				if (this.type == 'add') {
					const res = await this.$api.clientsAddClient({
						appCode: this.form.appCode,
						appDesc: this.form.appDesc,
						appName: this.form.appName,
						redirectUrl: this.form.redirectUrl,
						scope: this.form.scope
					})
					this.dialogVisible = false
					this.getSelete()
				} else {
					this.updateItem()
				}
			},
			// 修改
			async updateItem() {
				const res = await this.$api.clientsUpdateClient({
					appCode: this.form.appCode,
					appDesc: this.form.appDesc,
					appName: this.form.appName,
					redirectUrl: this.form.redirectUrl,
					scope: this.form.scope,
					clientId: this.form.id
				})
				this.dialogVisible = false
				this.getSelete()
			},
			openView(item) {
				console.log(item)
				if (item) {
					this.type = "update"
					this.form = item
				} else {
					this.type = "add"
					this.form = []
				}
				this.dialogVisible = true
			},
			async open() {
				this.numLength = this.multipleSelection.length
				if (this.numLength === 0) {
					this.$message.error('请选择你要调整的伯德')
				} else {
					this.dialogFormVisible1 = true
				}
			},
			resetForm() {},
			// 查询
			async submit() {
				console.log(this.multipleSelection)
				const str = [];
				this.multipleSelection.forEach((item) => {
					str.push(item.lingJingUserId)
				})
				if (this.num == 1) {
					this.changeLifeDay = Number(this.changeLifeDay)
				} else {
					this.changeLifeDay = Number(-this.changeLifeDay)
				}
				const res = await this.$api.batchUpdateEndTime({
					lingJingUserIdStr: JSON.stringify(str),
					changeLifeDay: this.changeLifeDay
				})
				this.dialogFormVisible = false
				this.dialogFormVisible1 = false
				this.$message.success('调整成功')
				this.getSelete()
			},
			openKey(item) {
				this.privateKeyVisible=true
				this.privateKeyTest=item.privateKey
			},
			copy() {
				this.privateKeyVisible=false
				let oInput = document.createElement('input');
				// 将想要复制的值
				oInput.value = this.privateKeyTest;
				// 页面底部追加输入框
				document.body.appendChild(oInput);
				// 选中输入框
				oInput.select();
				// 执行浏览器复制命令
				document.execCommand('Copy');
				// 弹出复制成功信息
				this.$message.success('复制成功');
				// 复制后移除输入框
				oInput.remove();
			},
		}
	}
</script>
