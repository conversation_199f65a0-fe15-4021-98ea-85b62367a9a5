<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户id">
        <el-input
          v-model="formInline.appUserId"
          placeholder="请输入用户id"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="操作人id">
        <el-input
          v-model="formInline.operatorUserId"
          placeholder="请输入操作人id"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="操作类型">
        <el-select 
          v-model="formInline.operatorType"
          placeholder="请选择操作类型"
        >
          <el-option
            v-for="item in operatorTypeList"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台">
        <el-select  v-model="formInline.platform" placeholder="平台">
          <el-option label="C端" value="C"></el-option>
          <el-option label="B端" value="B"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="debounceMethods(getList, 1)"
          >查询</el-button
        >
        <el-button type="primary" @click="clear()">清除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      v-loading="loading"
    >
      <el-table-column
        fixed
        prop="appUserId"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="operatorUserId"
        label="操作人id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="operatorUserName"
        label="操作人昵称"
        align="center"
      ></el-table-column>
      operatorUserName
      <el-table-column
        prop="operatorType"
        label="操作类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="operationExt"
        label="操作类型拓展字段"
        align="center"
      ></el-table-column>
      <el-table-column prop="platform" label="平台" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.platform == 'C'">C端</el-tag>
          <el-tag v-if="scope.row.platform == 'B'">B端</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="operationDesc"
        label="操作描述"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateAt"
        label="更新时间"
        align="center"
      ></el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        :current-page="current_page"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'userBalance',
  data () {
    return {
      loading: false,
      formInline: {
        appUserId: '',
        operatorUserId: '',
        operatorType: '',
        platform: ''
      },
      total: 1,
      tableData: [],
      current_page: 1,
      operatorTypeList: []
    }
  },
  mounted () {
    this.getManaulType()
    this.getList(1)
  },
  methods: {
    // 字典查询
    async getManaulType () {
      const res = await this.$api.getDictDataByDictType({
        dictType: 'USER_TRACE_OPERATE_LOG'
      })
      this.operatorTypeList = res.result.dictDataListVOS
    },
    // 查询列表
    async getList (page) {
      const res = await this.$api.userTraceList({
        pageNum: page,
        pageSize: 15,
        appUserId: this.formInline.appUserId,
        operatorUserId: this.formInline.operatorUserId,
        operatorType: this.formInline.operatorType,
        platform: this.formInline.platform
      })
      this.loading = false
      this.tableData = res.result.list
      this.total = res.result.totalCount
      this.current_page = page
    },
    xuanze (val) {
      this.getList(val)
    },
    clear () {
      this.formInline.appUserId = ''
      this.formInline.operatorUserId = ''
      this.formInline.operatorType = ''
      this.formInline.platform = ''
    }
  }
}
</script>

<style>
</style>
