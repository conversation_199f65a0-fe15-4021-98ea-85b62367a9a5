<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
        <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
          <el-input v-if="item.type ==='input'" v-model="formInline[item.prop]" placeholder="请输入用户id"></el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <div class="download">
          <el-button type="primary" @click="downloadTemplate">模板下载</el-button>
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :on-success="bulkGift"
            :limit="1"
            :show-file-list="false"
            :file-list="fileList">
            <el-button type="primary">批量赠送</el-button>
          </el-upload>
        </div>
        <div>
          <el-button @click="onReset">重置</el-button>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="40">
          <template slot-scope="scope">
            <el-button type="text"  @click="viewDetails(scope.row,'giveDialogVisible')">赠送</el-button>
            <el-button type="text"  @click="viewDetails(scope.row,'detailDialogVisible')">明细</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
    <!--    赠送燃料石 -->
    <el-dialog
      title="赠送燃料石"
      :visible.sync="giveDialogVisible"
      width="30%">
      <el-form ref="form" label-width="80px">
        <el-form-item label="赠送数量">
          <el-input v-model="changeGasStone" type="number"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="giveDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="stoneUserGive">确 定</el-button>
      </span>
    </el-dialog>
    <!--    明细 -->
    <el-dialog
      title="明细"
      :visible.sync="detailDialogVisible"
      width="60%">
      <el-table
        :data="detailData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="40">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in detailTableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
        </el-table-column>
      </el-table>
      <div class="dialog-footer">
        <el-pagination
          :current-page.sync="detailPageNum"
          background
          @current-change="handleCurrentChangeDetail"
          :page-size="detailPageSize"
          layout="total, prev, pager, next"
          :total="detailTotal">
        </el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="stoneUserGive">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>

export default {
  name: 'FuelStone',
  data () {
    return {
      action:
        process.env.VUE_APP_BASE_URL + 'jiuzhang/adminApi/stone/batch/missWebSign/give',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      formInline: {},
      formList: [
        {
          label: '用户id：',
          prop: 'uid',
          type: 'input'
        }
      ],
      tableData: [],
      tableList: [
        {
          prop: 'uid',
          label: '用户ID',
          width: '50'
        },
        {
          prop: 'nickName',
          label: 'nickName'
        },
        {
          prop: 'gasStone',
          label: '燃料石数量',
          width: '70'
        },
        {
          prop: 'updateAt',
          label: '记录创建时间'
        },
        {
          prop: 'updateAt',
          label: '记录修改时间'
        }
      ],
      row: {},
      changeGasStone: 0,
      pageSize: 15,
      pageNum: 1,
      total: 0,
      giveDialogVisible: false,
      detailDialogVisible: false,
      fileList: [],
      detailTableList: [
        {
          prop: 'orderId',
          label: '订单id'
        },
        {
          prop: 'changeAmount',
          label: '变更数额',
          width: '50'
        },
        {
          prop: 'oldAmount',
          label: '变更前数额',
          width: '50'
        },
        {
          prop: 'newAmount',
          label: '变更后数额',
          width: '50'
        },
        {
          prop: 'type',
          label: '类型',
          width: '50'
        },
        {
          prop: 'remark',
          label: '备注',
          width: '70'
        },
        {
          prop: 'createAt',
          label: '记录创建时间'
        }
      ],
      detailData: [],
      detailPageSize: 10,
      detailPageNum: 1,
      detailTotal: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 模板下载
     * @return {Promise<void>}
     */
    async downloadTemplate () {
      const { result: { emailsTemplateUrl } } = await this.$api.jiuZhangDownLoadTemplateExcel({
        templateTag: 'BATCH_ADD_GAS_STONE'
      })
      await window.open(emailsTemplateUrl, '_blank')
    },
    /**
     * 查看明细
     * @method
     */
    viewDetails (row, variable = '') {
      this.row = row
      if (variable === 'detailDialogVisible') {
        this.getDetails()
      }
      this[variable] = true
    },
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const {
        pageNum,
        pageSize
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.stoneUserList({
        ...this.formInline,
        pageNum,
        pageSize
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 批量赠送燃料石
     */
    bulkGift (res) {
      const {
        status: {
          code,
          msg
        }
      } = res
      if (!code) {
        this.$message.success(msg)
        this.getList()
      } else {
        this.$message.error(msg)
      }
      this.fileList = []
    },
    /**
     * 单个人赠送燃料石头
     */
    async stoneUserGive () {
      const {
        row: { uid },
        changeGasStone
      } = this
      await this.$api.stoneUserGive({
        uid,
        changeGasStone: Number(changeGasStone)
      })
      this.giveDialogVisible = false
      this.$message.success('赠送成功')
      await this.getList()
    },
    /**
     * 获取明细
     */
    async getDetails () {
      const {
        row: { uid },
        detailPageNum,
        detailPageSize
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.stoneUserDetail({
        uid,
        pageNum: detailPageNum,
        pageSize: detailPageSize
      })
      this.detailTotal = totalCount
      this.detailData = list
      console.log(list)
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件(明细)
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChangeDetail (val) {
      this.detailPageNum = val
      this.getDetails()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;

  .demo-form-inline {
    flex: 1;
  }

  .action {
    display: flex;
    width: 400px;
    justify-content: space-between;

    .download {
      display: flex;

      .el-button {
        margin-left: 10px;
      }
    }

    .el-button {
      height: 40px;
    }
  }
}

.table {
  margin-bottom: 50px;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
