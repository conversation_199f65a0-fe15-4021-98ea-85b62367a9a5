<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item label="昵称">
				<el-input v-model="formInline.nickname" placeholder="请输入昵称" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户id">
				<el-input v-model="formInline.id" placeholder="用户id" clearable></el-input>
			</el-form-item>
			<el-form-item label="邮箱">
				<el-input v-model="formInline.email" placeholder="请输入邮箱" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户等级">
				<el-select  v-model="formInline.level" placeholder="用户等级">
					<el-option label="普通人" value="0"></el-option>
					<el-option label="艺术家" value="1"></el-option>
					<el-option label="藏家" value="10"></el-option>
					<el-option label="艺术家+藏家" value="11"></el-option>
					<el-option label="企业" value="20"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="用户状态">
				<el-select  v-model="formInline.status" placeholder="用户状态">
					<el-option label="禁用" value="0"></el-option>
					<el-option label="正常" value="10"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="会员类别">
				<el-select  v-model="formInline.member" placeholder="会员类别">
					<el-option label="普通用户" value="0"></el-option>
					<el-option label="一级用户" value="4"></el-option>
					<!-- <el-option label="二级用户" value="5"></el-option> -->
				</el-select>
			</el-form-item>
			<el-form-item label="证件号">
				<el-input v-model="formInline.idCardNo" placeholder="请输入证件号" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户名">
				<el-input v-model="formInline.username" placeholder="请输入用户名" clearable></el-input>
			</el-form-item>
			<el-form-item label="手机号">
				<el-input v-model="formInline.mobPhone" placeholder="请输入手机号" clearable></el-input>
			</el-form-item>
			<el-form-item label="contractAddress">
				<el-input v-model="formInline.contractAddress" placeholder="请输入contractAddress" clearable></el-input>
			</el-form-item>
      <el-form-item label="注册时间">
        <el-date-picker v-model="formInline.createAt" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss.SSS">
        </el-date-picker>
      </el-form-item>
			<el-form-item label="用户实名状态">
				<el-select  v-model="formInline.certification" placeholder="用户实名状态">
					<el-option :label="val.dictLabel" :value="+val.dictValue" v-for="(val,i) in certificationDict"
						:key="i"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="
            currentPage = 1;
            getList();
            getAllUserSumBalance();
          ">查询</el-button>
				<el-button type="primary" @click="clear(1)">清除</el-button>
				<el-button type="primary" @click="batchExport()">导出</el-button>
				<el-dropdown trigger="click" @command="downloadTemplate">
					<el-button type="primary" style="margin-left: 10px;">模板下载</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item v-for="item in templateList" :key="item.code" :command="item.code">
							{{item.desc}}
						</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
				<el-dropdown trigger="click">
					<el-button type="primary" style="margin-left: 10px;">导入</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item v-for="item in uploadList" :key="item.code">
							<label :for="`importFile_${item.code}`"
								style="width:100%;height:100%;display: block;">{{item.desc}}</label>
							<input type="file" :id="`importFile_${item.code}`" name="importFile"
								@change="importTemplate($event, item)" v-show="false">
						</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%" >
			<el-table-column fixed prop="id" label="id" align="center" width="80"></el-table-column>
			<el-table-column prop="level" label="用户等级" align="center" width="110">
				<template scope="scope">
					<el-tag v-if="scope.row.level == '0'">普通人</el-tag>
					<el-tag v-if="scope.row.level == '1'">艺术家</el-tag>
					<el-tag v-if="scope.row.level == '10'">藏家</el-tag>
					<el-tag v-if="scope.row.level == '11'">艺术家+藏家</el-tag>
					<el-tag v-if="scope.row.level === 20">企业</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
			<el-table-column prop="username" label="用户名" align="center" width="200px"></el-table-column>
			<el-table-column prop="contractAddress" label="contractAddress" align="center" width="200px"></el-table-column>
			<el-table-column prop="avatar" label="用户头像" align="center" width="110">
				<template scope="scope">
					<div style="width: 100%">
						<el-image :preview-src-list="[scope.row.avatar]" style="width: 60px; height: 60px"
							:src="scope.row.avatar">
						</el-image>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="balance" label="账号余额" align="center"></el-table-column>
			<el-table-column prop="deposit" label="保证金" align="center"></el-table-column>
			<el-table-column prop="mobPhone" label="手机号" align="center"></el-table-column>
			<el-table-column prop="email" label="邮箱" align="center" width="200px"></el-table-column>
			<el-table-column prop="recommend" label="权重" align="center" width="120px"></el-table-column>
			<el-table-column prop="certification" label="实名状态" align="center" width="120px">
				<template scope="scope">
					<el-tag>{{ realNameDict[scope.row.certification] }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="idCardNo" label="证件号" align="center" width="200px"></el-table-column>
			<el-table-column prop="photo1" label="证件正面" align="center" width="110">
				<template scope="scope">
					<div style="width: 100%">
						<el-image :preview-src-list="[scope.row.photo1]" style="width: 100px; height: 100px"
							:src="scope.row.photo1" v-if="scope.row.photo1">
						</el-image>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="photo1" label="证件反面" align="center" width="110">
				<template scope="scope">
					<div style="width: 100%">
						<el-image :preview-src-list="[scope.row.photo2]" style="width: 100px; height: 100px"
							:src="scope.row.photo2" v-if="scope.row.photo2">
						</el-image>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="realName" label="实名姓名" align="center"></el-table-column>
			<el-table-column prop="authTime" label="实名时间" align="center"></el-table-column>
			<el-table-column prop="fuel" label="燃料次数" align="center"></el-table-column>
			<el-table-column prop="desc" label="用户简介" align="center"></el-table-column>
			<el-table-column prop="member" label="会员类别" align="center"></el-table-column>
			<el-table-column prop="status" label="用户状态" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.status == '0'">禁用</el-tag>
					<el-tag v-if="scope.row.status == '10'">正常</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="updateAt" label="更新时间" align="center" width="200px"></el-table-column>
			<el-table-column prop="createAt" label="注册时间" align="center" width="200px"></el-table-column>
			<el-table-column fixed="right" label="操作" width="300px" align="center">
				<template slot-scope="scope">
					<div class="form-action">
						<el-button type="text"  @click="modif_click(scope.row)">
							修改燃料
						</el-button>
						<el-button type="text"  @click="blackAndWhiteList(scope.row)">
							黑白名单
						</el-button>
						<el-button type="text"  @click="listOfWorks(scope.row)">
							作品列表
						</el-button>

						<el-button type="text"  @click="realNameStatus(scope.row)" :disabled="
            scope.row.certification === 0 || scope.row.certification === 8
            ">
							实名状态
						</el-button>
						<el-button type="text"  @click="shoUserLevelDialog(scope.row)">
							企业认证
						</el-button>
						<el-button type="text"  @click="showUserStatusDialog(scope.row)">
							用户状态
						</el-button>
						<el-button type="text"  @click="showEditUserInfoDialog(scope.row)">
							用户信息
						</el-button>
						<el-button type="text"  @click="banlanceModifDialog(scope.row)">
							修改余额
						</el-button>
						<el-button type="text"  @click="openRecommendBox(scope.row)">
							修改权重
						</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff;align-items:center">
      	<span class="money_total">合计金额:￥{{moneyTotal}}</span>
			<el-pagination :current-page.sync="currentPage" background layout="prev, pager, next" :total="total"
				:page-size="15" style="padding: 20px; background-color: #ffffff" @current-change="xuanze"
				@size-change="xuanze">
			</el-pagination>
		</div>
		<el-dialog title="修改燃料" :visible.sync="isDialog" center>
			<el-form :model="form" ref="form">
				<!-- <el-form-item label="修改类型:" :label-width="formLabelWidth" required>
          <el-select  v-model="form.type" placeholder="修改类型">
            <el-option label="平台赠送" value="4"></el-option>
            <el-option label="平台扣减" value="5"></el-option>
          </el-select>
        </el-form-item> -->
				<el-form-item label="改变数额:" :label-width="formLabelWidth" required>
					<el-input v-model="form.changeGas" placeholder="增加传正数，减少传负数" clearable style="width: 80%"
						type="number"></el-input>
				</el-form-item>
				<el-form-item label="备注:" :label-width="formLabelWidth" required>
					<el-input v-model="form.remark" placeholder="请输入备注" clearable style="width: 80%"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDialog = false">取 消</el-button>
				<el-button type="primary" @click="fuel_submit">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="添加黑/白名单" :visible.sync="blackAndWhiteListDialog" center>
			<el-form :model="form" ref="form">
				<el-form-item label="操作:" :label-width="formLabelWidth" required>
					<el-select  v-model="blackAndWhiteListValue" placeholder="请选择">
						<el-option v-for="item in blackAndWhiteListOptions" :key="item.value" :label="item.label"
							:value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="添加原因:" :label-width="formLabelWidth" required>
					<el-input v-model="blackAndWhiteListRemark" placeholder="请输入添加原因" clearable style="width: 80%">
					</el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="blackAndWhiteListDialog = false">取 消</el-button>
				<el-button type="primary" @click="addBlackAndWhite">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="提示" :visible.sync="verifiedDialog" width="30%">
			<el-radio v-model="certification" :label="2">护照实名通过</el-radio>
			<el-radio v-model="certification" :label="8">实名申请驳回</el-radio>
			<span slot="footer" class="dialog-footer">
				<el-button @click="verifiedDialog = false">取 消</el-button>
				<el-button type="primary" @click="certificationAction()">确 定</el-button>
			</span>
		</el-dialog>
		<!--    企业加v-->
		<el-dialog title="企业认证加V去V" :visible.sync="userLevelDialog" center>
			<el-form :model="form" ref="form">
				<el-form-item label="用户等级：" :label-width="formLabelWidth" required>
					<el-radio-group v-model="changeLevelForm.userLevel">
						<el-radio :label="0">普通人</el-radio>
						<el-radio :label="1">艺术家</el-radio>
						<el-radio :label="10">藏家</el-radio>
						<el-radio :label="11">艺术家+藏家</el-radio>
						<el-radio :label="20">企业</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="品牌名：" :label-width="formLabelWidth" required>
					<el-input v-model="changeLevelForm.brandName" placeholder="请输入品牌名" clearable style="width: 80%">
					</el-input>
				</el-form-item>
				<el-form-item label="品牌介绍：" :label-width="formLabelWidth" required>
					<el-input v-model="changeLevelForm.brandIntroduction" placeholder="请输入品牌介绍" type="textarea"
						clearable maxlength="200" show-word-limit style="width: 80%"></el-input>
				</el-form-item>
				<el-form-item label="公司名称：" :label-width="formLabelWidth" required>
					<el-input v-model="changeLevelForm.companyName" placeholder="请输入公司名称" clearable style="width: 80%">
					</el-input>
				</el-form-item>
				<el-form-item label="营业执照：" :label-width="formLabelWidth" required>
					<el-upload :limit="2" :action="action" :headers="token" list-type="picture-card"
						:file-list="businessLicenseFileList" :on-change="handleChange"
						:on-preview="handlePictureCardPreview" :on-success="handleSuccess" :on-remove="handleRemove">
						<i class="el-icon-plus"></i>
					</el-upload>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="userLevelDialog = false">取 消</el-button>
				<el-button type="primary" @click="userLevel">确 定</el-button>
			</div>
		</el-dialog>
		<!--    预览图片-->
		<el-dialog :visible.sync="previewDialogVisible">
			<img width="100%" :src="dialogImageUrl" alt="" />
		</el-dialog>
		<!--    修改用户信息-->
		<el-dialog title="修改用户信息" :visible.sync="editUserInfoDialog" center>
			<el-form :model="form" ref="form">
				<el-form-item label="用户等级：" :label-width="formLabelWidth" required>
					<el-radio-group v-model="userInfoForm.level">
						<el-radio :label="0">普通人</el-radio>
						<el-radio :label="1">艺术家</el-radio>
						<el-radio :label="10">藏家</el-radio>
						<el-radio :label="11">艺术家+藏家</el-radio>
						<el-radio :label="20">企业</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="头像：" :label-width="formLabelWidth" required>
					<el-upload :limit="2" :action="action" :headers="token" list-type="picture-card"
						:file-list="avtarFileList" :on-preview="handlePictureCardPreview"
						:on-success="handleAvatarSuccess" :on-change="handleChange">
						<i class="el-icon-plus"></i>
					</el-upload>
				</el-form-item>
				<el-form-item label="用户昵称：" :label-width="formLabelWidth" required>
					<el-input v-model="userInfoForm.nickname" placeholder="请输入品牌名" clearable style="width: 80%">
					</el-input>
				</el-form-item>
				<el-form-item label="用户简介：" :label-width="formLabelWidth" required>
					<el-input v-model="userInfoForm.desc" placeholder="请输入用户简介" type="textarea" clearable
						style="width: 80%"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="editUserInfoDialog = false">取 消</el-button>
				<el-button type="primary" @click="editUserInfo">确 定</el-button>
			</div>
		</el-dialog>
		<!--    修改用户状态-->
		<el-dialog title="修改用户状态" :visible.sync="userStatusDialog" center>
			<el-form :model="form" ref="form">
				<el-form-item label="用户状态：" :label-width="formLabelWidth" required>
					<el-radio-group v-model="userStatus">
						<el-radio :label="0">禁用</el-radio>
						<el-radio :label="10">正常</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="userStatusDialog = false">取 消</el-button>
				<el-button type="primary" @click="changeUserStatus">确 定</el-button>
			</div>
		</el-dialog>
		<!--    修改用户余额-->
		<el-dialog title="修改用户余额" :visible.sync="banlanceDialog" center :before-close="beforeClose">
			<el-form :model="banlanceForm" ref="form">
				<el-form-item label="修改类型:" :label-width="formLabelWidth">
					<el-radio-group v-model="banlanceForm.type" @change="radioChange">
						<el-radio :label="0">增加</el-radio>
						<el-radio :label="10">减少</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="增加金额" :label-width="formLabelWidth" required v-if="banlanceForm.type == 0">
					<el-input v-model="banlanceForm.addAmount" placeholder="请输入增加金额" clearable style="width: 80%"
						type="number"></el-input>
				</el-form-item>
				<el-form-item label="减少金额" :label-width="formLabelWidth" required v-if="banlanceForm.type == 10">
					<el-input v-model="banlanceForm.subAmount" placeholder="请输入减少金额" clearable style="width: 80%"
						type="number"></el-input>
				</el-form-item>
				<el-form-item label="备注" :label-width="formLabelWidth" required>
					<el-input v-model="banlanceForm.remark" placeholder="请输入备注" clearable style="width: 80%"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="banlanceDialog = false">取 消</el-button>
				<el-button type="primary" @click="banlanceSubmit">确 定</el-button>
			</div>
		</el-dialog>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'
	import {
		userCenterDownLoadTemplate,
		phoneRegister,
		modifyPass,
		modifyUserIcon,
		modifyUserInfo,
		emailRegister,
		emailRegisterForWindy,
		fansCount
	} from '@/api/appUserCenter'
  import {
  	balanceBatchChange0,
    balanceBatchChange4
  } from '@/api/jiuzhang'
	import {
		uploadExcelToOss
	} from '@/api/ossCenter'
	export default {
		name: 'userList',
		data() {
			return {
				action: `${process.env.VUE_APP_BASE_URL}osscenter/adminApi/missWebSign/uploadImage`,
				token: {
					AdminAuthorization: localStorage.getItem('usertoken')
				}, // 设置上传的请求头部
				formInline: {
					nickname: '',
					email: '',
					level: '',
					status: '',
					member: '',
					id: '',
          createAt:''
				},
				total: 1,
				tableData: [],
				userId: '',
				isDialog: false,
				form: {
					changeGas: '',
					type: '',
					remark: ''
				},
				formLabelWidth: '120px',
				blackAndWhiteListDialog: false,
				blackAndWhiteListValue: '1',
				blackAndWhiteListRemark: '',
				blackAndWhiteListOptions: [{
						value: '1',
						label: '购买黑名单'
					},
					{
						value: '2',
						label: '提现黑名单'
					},
					{
						value: '3',
						label: '购买转增黑名单'
					},
					{
						value: '4',
						label: '锁单白名单'
					},
					{
						value: '5',
						label: '作品需审名单'
					},
					{
						value: '6',
						label: '购买验证码白名单'
					},
					{
						value: '7',
						label: '作品免审名单'
					}
				],
				verifiedDialog: false,
				certification: null,
				realNameDict: {
					0: '未实名',
					1: '身份证实名',
					2: '护照实名',
					7: '审核中',
					8: '未通过'
				},
				userLevelDialog: false,
				changeLevelForm: {
					brandIntroduction: '',
					brandName: '',
					businessLicense: '',
					companyName: '',
					userLevel: '',
					nickname: ''
				},
				previewDialogVisible: false,
				dialogImageUrl: '',
				editUserInfoDialog: false,
				userStatusDialog: false,
				userStatus: null,
				userInfoForm: {
					level: '',
					name: '',
					avatar: '',
					desc: ''
				},
				uploadDisabled: false,
				avtarFileList: [],
				businessLicenseFileList: [],
				currentPage: 1,
				banlanceForm: {
					type: 0,
					addAmount: 0,
					subAmount: 0,
					remark: ''
				},
				banlanceDialog: false,
				certificationDict: [],
				// 模板导出列表
				templateList: [{
					code: 'BATCH_MODIFY_USER_PASS_IMPORT',
					desc: '批量修改密码'
				}, {
					code: 'BATCH_MODIFY_USER_INFO',
					desc: '批量用户信息修改'
				}, {
					code: 'BATCH_MODIFY_USER_ICON',
					desc: '批量用户图片修改'
				}, {
					code: 'BATCH_IMPORT_USER_EMAIL_REGISTER',
					desc: '批量导入邮箱注册'
				}, {
					code: 'BATCH_IMPORT_USER_PHONE_REGISTER',
					desc: '批量导入手机号注册'
				},{
					code: 'BATCH_IMPORT_USER_FANS_COUNT',
					desc: '批量修改粉丝数'
				},{
					code: 'BATCH_UPDATE_USER_BALANCE',
					desc: '更改用户余额模板'
				}],
				// 上传功能列表
				uploadList: [{
					func: modifyPass,
					code: 'modifyPass',
					desc: '批量修改密码'
				}, {
					func: modifyUserInfo,
					code: 'modifyUserInfo',
					desc: '批量用户信息修改'
				}, {
					func: modifyUserIcon,
					code: 'modifyUserIcon',
					desc: '批量用户图片修改'
				}, {
					func: emailRegister,
					code: 'emailRegister',
					desc: '批量导入邮箱注册'
				}, {
					func: emailRegisterForWindy,
					code: 'emailRegisterForWindy',
					desc: '批量导入邮箱注册(Wendy专用)'
				}, {
					func: phoneRegister,
					code: 'phoneRegister',
					desc: '批量导入手机号注册'
				},{
					func: fansCount,
					code: 'fansCount',
					desc: '导入批量修改粉丝数'
				},{
					func: balanceBatchChange0,
					code: 'balanceBatchChange0',
					desc: '批量修改用户余额-正常用户'
				},{
					func: balanceBatchChange4,
					code: 'balanceBatchChange4',
					desc: '批量修改用户余额-自己用户'
				}],
        moneyTotal:''
			}
		},
		mounted() {
			if (this.$route.query.uid) {
				this.formInline.id = this.$route.query.uid
			}
			this.getList()
      this.getAllUserSumBalance()
			this.getQueryOptions('APP_ACCOUNT_CERTIFICATION', 'certificationDict')
		},
		watch: {
			userLevelDialog(val) {
				if (!val) {
					this.changeLevelForm = {
						brandIntroduction: '',
						brandName: '',
						businessLicense: '',
						companyName: '',
						userLevel: '',
						nickname: ''
					}
				}
			},
			blackAndWhiteListDialog(val) {
				if (!val) {
					this.blackAndWhiteListValue = '1'
					this.blackAndWhiteListRemark = ''
				}
			},
			editUserInfoDialog(val) {
				if (!val) {
					this.userInfoForm = {
						level: '',
						name: '',
						avatar: '',
						desc: ''
					}
					this.avtarFileList = []
				}
			}
		},
		methods: {
			/**
			 * 下载模板
			 */
			async downloadTemplate(code) {
				const {
					result: {
						emailsTemplateUrl
					}
				} = await userCenterDownLoadTemplate({
					templateTag: code
				})
				window.open(emailsTemplateUrl)
			},
			async importTemplate(event, {
				func,
				code
			}) {
				const formData = new FormData()
				const input = event.target
				const file = input.files[0]
				formData.append('file', file)
				const {
					result: {
						url
					}
				} = await uploadExcelToOss(formData)
				input.value = ''
				await func({
					importUrl: url
				}).then(() => {
					this.$message.success('导入成功')
				})
			},
			/**
			 * 获取字典
			 * @param dictType 字典类型
			 * @param key 赋值的key
			 * @return {Promise<void>}
			 */
			async getQueryOptions(dictType, key) {
				const {
					result: {
						dictDataListVOS
					}
				} = await this.$api.getDictDataByDictType({
					dictType
				})
				this[key] = dictDataListVOS
			},
			// 导出
			async batchExport() {
				if (this.total > 60000) {
					this.$message.error('导出文件过大,不能超过60000条')
				} else {
          let createStartTime,createEndTime=""
          console.log(this.formInline.createAt)
          if(this.formInline.createAt){
            createStartTime=this.formInline.createAt[0]
            createEndTime=this.formInline.createAt[1]
          }else{
            createStartTime=""
            createEndTime=""
          }
					const res = await this.$api.userListExport({
						nickname: this.formInline.nickname,
						email: this.formInline.email,
						level: this.formInline.level,
						member: this.formInline.member,
						status: this.formInline.status,
						contractAddress: this.formInline.contractAddress,
						idCardNo: this.formInline.idCardNo,
						mobPhone: this.formInline.mobPhone,
						username: this.formInline.username,
						certification: this.certification,
            id: this.formInline.id,
            createStartTime,
            createEndTime
					})
					// console.log(res);
					if (res.type === 'application/json') {
						// blob 转 JSON
						const enc = new TextDecoder('utf-8')
						res.arrayBuffer().then((buffer) => {
							const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
							this.$message.error(data.status?.msg)
						})
					} else {
						downloadBlob(res, '用户列表' + Date.now() + '.xlsx')
						this.$message.success('导出成功')
						await this.getList()
					}
				}
			},
			/**
			 * 修改用户信息
			 */
			async editUserInfo() {
				this.userInfoForm.name = this.userInfoForm.nickname
				await this.$api.modifyInfo(this.userInfoForm)
				this.$message.success('修改成功')
				this.editUserInfoDialog = false
				await this.getList()
			},
			/**
			 * 修改用户信息弹窗
			 * @param row 当前行数据
			 */
			showEditUserInfoDialog(row) {
				this.editUserInfoDialog = true
				const {
					level,
					nickname,
					avatar,
					desc,
					id
				} = JSON.parse(
					JSON.stringify(row)
				)
				this.userInfoForm = {
					level,
					nickname,
					avatar,
					desc,
					userId: id
				}
				this.avtarFileList = [{
					name: row.avatar,
					url: row.avatar
				}]
			},
			/**
			 * 修改余额弹窗
			 */
			banlanceModifDialog(val) {
				this.banlanceDialog = true
				this.userId = val.id
			},
			/**
			 * 修改余额弹窗确认
			 */
			async banlanceSubmit() {
				await this.$api.balanceChange({
					addAmount: this.banlanceForm.addAmount || 0,
					remark: this.banlanceForm.remark,
					subAmount: this.banlanceForm.subAmount || 0,
					uid: this.userId
				})
				this.banlanceDialog = false
				this.$message.success('修改成功')
				this.getList(1)
			},
			beforeClose() {
				this.banlanceForm.addAmount = 0
				this.banlanceForm.remark = ''
				this.banlanceForm.subAmount = 0
				this.banlanceForm.type = 0
				this.banlanceDialog = false
			},
			radioChange() {
				this.banlanceForm.addAmount = 0
				this.banlanceForm.remark = ''
				this.banlanceForm.subAmount = 0
			},
			/**
			 * 头像上传成功
			 * @param res 返回的数据
			 */
			handleAvatarSuccess(res) {
				this.userInfoForm.avatar = res.result.url
			},
			/**
			 * 只保留一张
			 * @param file
			 * @param fileList
			 */
			handleChange(file, fileList) {
				if (fileList && fileList.length >= 2) {
					fileList.shift()
				}
			},
			/**
			 * 修改用户状态
			 * @return {Promise<void>}
			 */
			async changeUserStatus() {
				const {
					userId,
					userStatus: status
				} = this
				await this.$api.modifyStatus({
					userId,
					status
				})
				this.userStatusDialog = false
				this.$message.success('修改成功')
				await this.getList()
			},
			/**
			 *  修改用户状态 弹窗
			 */
			showUserStatusDialog(row) {
				this.userStatusDialog = true
				this.userStatus = row.status
				this.userId = row.id
			},
			// 删除图片
			handleRemove() {
				this.changeLevelForm.businessLicense = ''
			},
			// 上传图片成功
			handleSuccess(response) {
				this.changeLevelForm.businessLicense = response.result.url
			},
			// 预览图片
			handlePictureCardPreview(file) {
				this.dialogImageUrl = file.url
				this.previewDialogVisible = true
			},
			/**
			 * 用户等级修改 弹窗
			 */
			shoUserLevelDialog(row) {
				const {
					brandIntroduction,
					brandName,
					businessLicense,
					companyName,
					level,
					nickname
				} = row
				this.userLevelDialog = true
				this.userId = row.id
				this.changeLevelForm.nickname = row.nickname
				this.changeLevelForm.userLevel = row.level
				this.changeLevelForm = {
					brandIntroduction,
					brandName,
					businessLicense,
					companyName,
					userLevel: level,
					nickname
				}
			},
			/**
			 * 用户等级修改 app用户企业认证加v去v
			 */
			async userLevel() {
				await this.$api.changeLevel({
					id: this.userId,
					...this.changeLevelForm
				})
				this.$message.success('修改成功')
				this.userLevelDialog = false
				await this.getList()
			},
			/**
			 * 修改用户实名状态
			 */
			async certificationAction() {
				// 0 未实名,1 身份证实名,2 护照实名,7 审核中,8 未通过
				await this.$api.certification({
					userId: this.userId,
					certification: this.certification
				})
				this.$message.success('修改成功')
				await this.getList()
				this.verifiedDialog = false
			},
			/**
			 *  打开实名修改dialog
			 * @param row 选中的行数据
			 */
			realNameStatus(row) {
				this.certification = row.certification
				this.userId = row.id
				this.verifiedDialog = true
			},
			// 查询列表
			async getList() {
         let createStartTime,createEndTime=""
        console.log(this.formInline.createAt)
        if(this.formInline.createAt){
          createStartTime=this.formInline.createAt[0]
          createEndTime=this.formInline.createAt[1]
        }else{
          createStartTime=""
          createEndTime=""
        }
				const res = await this.$api.getUserList({
					pageNum: this.currentPage,
					pageSize: 15,
					nickname: this.formInline.nickname,
					email: this.formInline.email,
					level: this.formInline.level,
					member: this.formInline.member,
					status: this.formInline.status,
					contractAddress: this.formInline.contractAddress,
					idCardNo: this.formInline.idCardNo,
					mobPhone: this.formInline.mobPhone,
					username: this.formInline.username,
					certification: this.formInline.certification,
					id: this.formInline.id,
          createStartTime,
          createEndTime
				})
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
      // 查询余额
      async getAllUserSumBalance() {
         let createStartTime,createEndTime=""
        console.log(this.formInline.createAt)
        if(this.formInline.createAt){
          createStartTime=this.formInline.createAt[0]
          createEndTime=this.formInline.createAt[1]
        }else{
          createStartTime=""
          createEndTime=""
        }
      	const res = await this.$api.getAllUserSumBalance({
      		pageNum: 1,
      		pageSize: 15,
      		nickname: this.formInline.nickname,
      		email: this.formInline.email,
      		level: this.formInline.level,
      		member: this.formInline.member,
      		status: this.formInline.status,
      		contractAddress: this.formInline.contractAddress,
      		idCardNo: this.formInline.idCardNo,
      		mobPhone: this.formInline.mobPhone,
      		username: this.formInline.username,
      		certification: this.formInline.certification,
      		id: this.formInline.id,
          createStartTime,
          createEndTime
      	})
        this.moneyTotal=res.result.sumBalance
      },

			clear() {
				this.currentPage = 1
				this.formInline = {
					nickname: '',
					email: '',
					level: '',
					member: '',
					status: '',
					contractAddress: '',
					idCardNo: '',
					mobPhone: '',
					username: '',
					certification: ''
				}
				this.getList()
			},
			xuanze(val) {
				this.currentPage = val
				this.getList()
			},
			modif_click(val) {
				this.userId = val.id
				this.isDialog = true
			},
			async fuel_submit() {
				if (this.form.changeGas.indexOf('.') !== -1) {
					this.$message.error('只能输入整数')
					return
				}
				if (this.form.changeGas >= 0) {
					this.form.type = 4
				} else {
					this.form.type = 5
				}
				await this.$api.fuelChange({
					userId: this.userId,
					changeGas: this.form.changeGas,
					type: this.form.type,
					remark: this.form.remark
				})
				await this.getList()
				this.isDialog = false
				this.form = {
					changeGas: '',
					type: '',
					remark: ''
				}
				this.$message.success('修改成功')
			},
			/**
			 * 作品列表
			 */
			listOfWorks(val) {
				this.$router.push({
					name: 'ListOfWorks',
					query: {
						publisherName: val.nickname
					}
				})
			},
			/**
			 * 黑白名单
			 * @param row 行数据
			 */
			blackAndWhiteList(row) {
				this.blackAndWhiteListDialog = true
				this.userId = row.id
			},
			/**
			 * 添加黑白名单
			 */
			async addBlackAndWhite() {
				const {
					userId,
					blackAndWhiteListRemark
				} = this
				if (!blackAndWhiteListRemark.length) {
					this.$message.error('请输入原因')
					return
				}
				const subTypeObj = {
					1: 'BUY_ITEM',
					2: 'WITHDRAW',
					3: 'GET_ITEM',
					4: 'BUY_ITEM_LIMIT',
					5: 'ITEM_VERIFY',
					6: 'BUY_ITEM_CAPTCHA',
					7: 'ITEM_VERIFY'
				}
				const subType = subTypeObj[this.blackAndWhiteListValue]
				await this.$api.blackWhiteUserAdd({
					remark: blackAndWhiteListRemark,
					userId,
					type: subType === 'BUY_ITEM_LIMIT' || 'ITEM_VERIFY' ? 'WHITE' : 'BLACK',
					subType
				})
				this.$message.success('添加成功')
				this.blackAndWhiteListDialog = false
			},
			// 输入权重数字
			openRecommendBox(item) {
				this.$prompt('请输入你要修改的权重', '修改权重', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				}).then(({
					value
				}) => {
					this.updateRecommend(item.id,value)
				}).catch(() => {
					this.$message({
						type: 'info',
						message: '取消输入'
					});
				});
			},
			/**
			 * 修改权重
			 */
			async updateRecommend(userId,value) {
				const res = await this.$api.recommend({
					userId,
					recommend:value
				})
				if(res.status.code===0){
					this.$message.success('修改成功')
				}else{
					this.$message.error(msg)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.cell {
		.el-button {
			margin-left: 0;
		}
	}

	.form-action {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
	}

	::v-deep {
		.el-upload-list__item {
			transition: none;
		}
	}
	::v-deep.el-table--enable-row-transition .el-table__body td.el-table__cell{
		padding:0px !important;
	}
  .money_total {
  	position: absolute;
  	left: 20px;
  	font-size: 14px;
  	color: #F56C6C;
  }
</style>
