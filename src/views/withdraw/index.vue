<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 10px">
			<!-- <el-form-item label="账号类型">
				<el-select  v-model="formInline.accountType" placeholder="账号类型" clearable>
					<el-option label="银行卡" value="1"></el-option>
					<el-option label="微信" value="2"></el-option>
					<el-option label="支付宝" value="3"></el-option>
				</el-select>
			</el-form-item> -->
      <el-form-item label="提现ID">
      	<el-input v-model="formInline.id"  placeholder="请输入提现ID" clearable></el-input>
      </el-form-item>
			<el-form-item label="实名">
				<el-input v-model="formInline.realName"  placeholder="请输入实名" clearable></el-input>
			</el-form-item>
			<el-form-item label="身份证">
				<el-input v-model="formInline.idCardNo"  placeholder="请输入身份证" clearable></el-input>
			</el-form-item>
			<el-form-item label="审核/打款状态">
				<el-select  v-model="formInline.status" placeholder="是否审核" clearable>
					<el-option label="未审核" value="INIT"></el-option>
					<el-option label="审核通过" value="CHECK_SUCCESS"></el-option>
					<el-option label="审核失败" value="CHECK_FAIL"></el-option>
					<el-option label="正在打款" value="PAY_START"></el-option>
					<el-option label="打款成功" value="PAY_SUCCESS"></el-option>
					<el-option label="打款失败" value="PAY_FAIL"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item label="银行卡号">
				<el-input v-model="formInline.bankCardNumber" placeholder="请输入银行卡号" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户昵称">
				<el-input v-model="formInline.nickname" placeholder="请输入用户昵称" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户名">
				<el-input v-model="formInline.username" placeholder="请输入用户名" clearable></el-input>
			</el-form-item>
			<el-form-item label="用户id">
				<el-input v-model="formInline.userId" placeholder="请输入用户id" clearable></el-input>
			</el-form-item>
			<el-form-item label="提现时间">
				<el-date-picker v-model="formInline.createAt" type="datetimerange" range-separator="至"
					start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
				</el-date-picker>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="selete_list()">查询</el-button>
				<el-button type="primary" @click="clear(1)">清除</el-button>
				<el-button type="primary" @click="batchExport()">导出</el-button>
				<el-button type="primary" @click="isBatchDialog=true">批量审核</el-button>
				<el-button type="primary" @click="openRemit()" >批量打款</el-button>
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark" show-header border style="width: 100%"
			v-loading="loading" element-loading-text="正在导出">
			<el-table-column type="selection" >
			</el-table-column>
			<el-table-column fixed prop="id" label="提现ID" align="center" width="80"></el-table-column>
			<el-table-column prop="uid" label="用户id" align="center" ></el-table-column>
			<el-table-column prop="nickname" label="用户昵称" align="center" width="100" ></el-table-column>
			<el-table-column prop="username" label="用户名" align="center" width="120" show-overflow-tooltip></el-table-column>
			<el-table-column prop="realName" label="实名姓名" align="center"></el-table-column>
			<el-table-column prop="idCardNo" label="身份证号码" align="center"  width="100"></el-table-column>
			<el-table-column prop="balance" label="提现金额" align="center">
				<template scope="scope" >
					{{scope.row.balance}}
				</template>
			</el-table-column>
			<el-table-column prop="fee" label="提现手续费" align="center" width="70"></el-table-column>
			<el-table-column prop="isAffirm" label="审核结果" align="center"  width="100">
				<template scope="scope" >
					<el-tag style="margin-top:10px;" size="mini" v-if="scope.row.status=='CHECK_SUCCESS'||scope.row.status=='PAY_SUCCESS'||scope.row.status=='PAY_START'||scope.row.status=='PAY_FAIL'" type="success" >审核通过</el-tag>
					<el-tag style="margin-top:10px;" size="mini" v-if="scope.row.status=='CHECK_FAIL'" type="danger" >审核未通过</el-tag>
					<el-tag style="margin-top:10px;" size="mini" v-if="scope.row.status=='INIT'" type="info" >未审核</el-tag>
					<div class="justify">
						<el-button type="text" @click="audit_click(scope.row)" :disabled="scope.row.status!='INIT'">通过</el-button>
						<el-button type="text" @click="audit_reject(scope.row)" :disabled="scope.row.status!='INIT'">驳回</el-button>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="isAutoPay" label="是否打款" align="center"  width="80">
				<template scope="scope" >
					<div class="">
						<el-button type="text" v-if="scope.row.status=='INIT'||scope.row.status=='CHECK_SUCCESS'||scope.row.status=='CHECK_FAIL'" :disabled="scope.row.status!='CHECK_SUCCESS'" @click="bank_remit_click(scope.row)">打款</el-button>
						<el-button type="text" v-if="scope.row.status=='PAY_START'||scope.row.status=='PAY_SUCCESS'" :disabled="true">已打款</el-button>
						<el-button type="text" v-if="scope.row.status=='PAY_FAIL'" :disabled="true">打款失败</el-button>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="createAt" label="提现时间" align="center" width="100">
				<template scope="scope">
					{{scope.row.createAt}}
				</template>
			</el-table-column>
			<el-table-column prop="startTime" label="打款时间" align="center" width="100">
			</el-table-column>
			<el-table-column prop="endTime" label="打款成功时间" align="center" width="100">
			</el-table-column>
			<el-table-column prop="remark" label="备注" align="center" width="140" show-overflow-tooltip >
				<template scope="scope" >
					<div style="width:100px;cursor: pointer;" @click="openText(scope.row.remark)">{{scope.row.remark}}</div>
					<i class="el-icon-edit" style="cursor: pointer;" @click="remark_click(scope.row)"></i>
				</template>
			</el-table-column>
			<el-table-column prop="bankName" label="银行名称" align="center"></el-table-column>
			<el-table-column prop="bankCardNumber" label="银行卡号" align="center" width="100"></el-table-column>
			<!-- <el-table-column prop="area" label="地区码" align="center"></el-table-column> -->
			<el-table-column prop="province" label="开户行省份" align="center"></el-table-column>
			<el-table-column prop="city" label="开户行城市" align="center"></el-table-column>
			<!-- <el-table-column prop="accountType" label="账号类型" align="center">
				<template scope="scope">
					<el-tag v-if="scope.row.accountType == '1'">银行卡</el-tag>
					<el-tag v-if="scope.row.accountType == '2'">微信</el-tag>
					<el-tag v-if="scope.row.accountType == '3'">支付宝</el-tag>
				</template>
			</el-table-column> -->
			<!-- <el-table-column prop="alipay" label="支付宝账号" align="center"></el-table-column> -->
			<el-table-column prop="remitWay" label="打款方式" align="center" width="110">
				<template scope="scope">
					<el-tag v-if="scope.row.remitWay == '1'">银行卡打款</el-tag>
					<el-tag v-if="scope.row.remitWay == '2'">手动打款</el-tag>
					<el-tag v-if="scope.row.remitWay == '3'">审核拒绝</el-tag>
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="340" align="center">
				<template slot-scope="scope">
					<el-button type="text"  @click="user_click(scope.row)">流水</el-button>
					<el-button type="text"  @click="order_click(scope.row,'sale')">出售记录</el-button>
					<el-button type="text"  @click="order_click(scope.row,'buy')">买入记录</el-button>
					<el-button type="text"  @click="userInfo_click(scope.row)">用户信息</el-button>

					<el-button type="text"  @click="userWithdraw(scope.row)">账号禁止提现</el-button>
					<el-button type="text"  @click="idCard(scope.row)">身份证禁止提现</el-button>
					<el-button type="text"  @click="revocation_audit(scope.row,2)">撤销审核</el-button>
					<el-button type="text"  @click="revocation_audit(scope.row,3)">撤销打款</el-button>
					<!-- <el-button type="text"  @click="audit_click(scope.row)"
						:disabled="scope.row.isAffirm == '1'">审核</el-button>
					<el-button type="text"  @click="remit_click(scope.row)"
						:disabled="scope.row.isAutoPay == '1'">打款</el-button>
					<el-button type="text"  @click="bank_remit_click(scope.row)"
						:disabled="scope.row.isAutoPay == '1'">银行卡打款</el-button>
					<el-button type="text"  @click="remark_click(scope.row)">备注</el-button> -->
					<!-- <el-button type="text"  @click="order_click(scope.row)">订单记录</el-button> -->


				</template>
			</el-table-column>
		</el-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
			 :page-size="pageSize" :current-page="page" :page-sizes="[20, 50, 100, 200,500,1000]"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
			</el-pagination>
		</div>
		<el-dialog title="审核操作" :visible.sync="isDialog" width="35%" center>
			<el-form :model="form">
				<el-form-item>
					<el-radio v-model="form.isPass" label="1">审核通过</el-radio>
					<el-radio v-model="form.isPass" label="0">审核驳回</el-radio>
				</el-form-item>
				<el-form-item v-if="form.isPass == '0'">
					<el-input v-model="form.failReason" placeholder="请输入驳回原因" type="textarea"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDialog = false">取 消</el-button>
				<el-button type="primary" @click="audit_submit()">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="备注信息" :visible.sync="isDialogRemark" width="35%" center>
			<el-form>
				<el-form-item>
					<el-input v-model="remark" placeholder="请输入备注信息" type="textarea"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDialogRemark = false">取 消</el-button>
				<el-button type="primary" @click="submit_remark()">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="批量审核操作(谨慎操作)" :visible.sync="isBatchDialog" width="35%" center>
			<el-form :model="form">
				<el-form-item>
					<el-radio v-model="form.isPass" label="1">审核通过</el-radio>
					<el-radio v-model="form.isPass" label="0">审核驳回</el-radio>
				</el-form-item>
				<el-form-item v-if="form.isPass == '0'">
					<el-input v-model="form.failReason" placeholder="请输入驳回原因" type="textarea"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isBatchDialog = false">取 消</el-button>
				<el-button type="primary" @click="batch_check()">确 定</el-button>
			</div>
		</el-dialog>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'

	export default {
		name: 'withdraw',
		data() {
			return {
				formInline: {
					accountType: 1,
					bankCardNumber: undefined,
					nickname: undefined,
					realName: undefined,
					userId: undefined,
					createAt: undefined,
					startTime: undefined,
					endTime: undefined,
					isAffirm: undefined,
          id:undefined
				},
				total: 1,
				tableData: [],
				formLabelWidth: '120px',
				edit_id: '',
				// 审核操作数据
				isDialog: false,
				form: {
					failReason: '',
					isPass: '1'
				},
				//  备注
				isDialogRemark: false,
				remark: '',
				loading: false,
				multipleSelection:"",
				isBatchDialog:false,
				page:1,
				pageSize:20

			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			// 导出
			async batchExport() {
				if (this.total > 60000) {
					this.$message.error('导出文件过大,不能超过60000条')
				} else {
					this.loading = true
					if (this.formInline.createAt) {
						this.formInline.startTime = this.formInline.createAt[0] + '.000'
						this.formInline.endTime = this.formInline.createAt[1] + '.000'
					} else {
						this.formInline.startTime = undefined
						this.formInline.endTime = undefined
					}
					const res = await this.$api.withdrawExport({
						accountType: this.formInline.accountType,
						isAffirm: this.formInline.isAffirm,
						bankCardNumber: this.formInline.bankCardNumber,
						nickname: this.formInline.nickname,
						realName: this.formInline.realName,
						userId: this.formInline.userId,
						startTime: this.formInline.startTime,
						endTime: this.formInline.endTime,
            status: this.formInline.status,
            id:this.formInline.id
					})
					if (res.retCode === 500) {
						this.$message.error(res.retMsg)
						this.loading = false
						this.getList()
					} else if (res.type === 'application/json') {
						// blob 转 JSON
						const enc = new TextDecoder('utf-8')
						res.arrayBuffer().then((buffer) => {
							const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
							this.$message.error(data.status?.msg)
						})
					} else {
						downloadBlob(res, '提现' + Date.now() + '.xlsx')
						this.$message.success('导出成功')
						this.loading = false
						this.getList()
					}
				}
			},
			// 查询列表
			async getList() {
				if (this.formInline.createAt) {
					this.formInline.startTime = this.formInline.createAt[0] + '.000'
					this.formInline.endTime = this.formInline.createAt[1] + '.000'
				} else {
					this.formInline.startTime = undefined
					this.formInline.endTime = undefined
				}
				const res = await this.$api.getWithdrawList({
					pageNum: this.page,
					pageSize: this.pageSize,
					accountType: this.formInline.accountType,
					isAffirm: this.formInline.isAffirm,
					bankCardNumber: this.formInline.bankCardNumber,
					nickname: this.formInline.nickname,
					username: this.formInline.username,
					realName: this.formInline.realName,
					userId: this.formInline.userId,
					startTime: this.formInline.startTime,
					endTime: this.formInline.endTime,
					idCardNo:this.formInline.idCardNo,
					status: this.formInline.status,
           id:this.formInline.id,
				})
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
			xuanze(val) {
				this.page  = val
				this.getList()
			},
			// 分页
			xuanzeSize(val) {
				this.pageSize = val
				this.getList()
			},
			clear() {
				this.formInline.accountType = undefined
				this.formInline.isAffirm = undefined
				this.formInline.bankCardNumber = undefined
				this.formInline.nickname = undefined
				this.formInline.realName = undefined
				this.formInline.userId = undefined
				this.formInline.createAt = undefined
			},
			// 通过审核
			audit_click(val) {
				this.edit_id = val.id
				this.form.isPas=1
				this.audit_submit()
			},
			async audit_submit() {
				await this.$api.withdrawCheck({
					id: this.edit_id,
					failReason: this.form.failReason,
					isPass: this.form.isPass
				})
				this.getList(1)
				this.isDialog = false
				this.form = {
					failReason: undefined,
					isPass: '1'
				}
				this.$message.success('审核成功')
			},
			//撤销审核
			async revocation_audit(val,type) {
				await this.$api.withdrawCheck({
					id: val.id,
					failReason: "",
					isPass:type
				})
				this.getList(1)
				if(type==2){
					this.$message.success('撤销审核成功')
				}else if(type==3){
					this.$message.success('撤销打款成功')
				}
			},
			// 打款
			remit_click(val) {
				this.edit_id = val.id
				this.$confirm('此操作将进行打款, 是否继续?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					.then(() => {
						this.handPay()
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消打款'
						})
					})
			},
			bank_remit_click(val){
				this.edit_id = val.id
				this.bankCardPay()
				// this.$confirm('此操作将进行银行卡打款, 是否继续?', '提示', {
				// 		confirmButtonText: '确定',
				// 		cancelButtonText: '取消',
				// 		type: 'warning'
				// 	})
				// 	.then(() => {
				// 		this.bankCardPay()
				// 	})
				// 	.catch(() => {
				// 		this.$message({
				// 			type: 'info',
				// 			message: '已取消打款'
				// 		})
				// 	})
			},
			// 银行卡打款
			async bankCardPay() {
				await this.$api.bankCardPay({
					id: this.edit_id
				})
				this.getList(1)
				this.$message({
					type: 'success',
					message: '操作成功!'
				})
			},
			// 线下打款
			async handPay() {
				await this.$api.handPay({
					id: this.edit_id
				})
				this.getList(1)
				this.$message({
					type: 'success',
					message: '操作成功!'
				})
			},
			// 备注
			remark_click(val) {
				this.edit_id = val.id
				this.$prompt('备注信息', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				}).then(({ value }) => {
				  this.remark=value
				  this.submit_remark()
				}).catch(() => {
				  this.$message({
				    type: 'info',
				    message: '取消输入'
				  });
				});
			},

			audit_reject(val){
				this.edit_id = val.id
				console.log(val)
				this.form.failReason = ""
				 this.form.isPass=0
				 this.audit_submit()
				// this.$prompt('请输入驳回原因', '提示', {
				//   confirmButtonText: '确定',
				//   cancelButtonText: '取消',
				// }).then(({ value }) => {
				//   this.form.failReason = value
				//   this.form.isPass=0
				//   this.audit_submit()
				// }).catch(() => {

				// });
			},

			//订单列表
			order_click(item,type) {
				this.$router.push({
					name: 'order',
					query: {
						uid: item.uid,
						type
					}
				})
			},
			//用户流水
			user_click(item) {
				this.$router.push({
					name: 'accounting',
					query: {
						uid: item.uid
					}
				})
			},
			//用户信息
			userInfo_click(item) {
				this.$router.push({
					name: 'userList',
					query: {
				  uid: item.uid
					}
				})
			},
			async submit_remark() {
				await this.$api.withdrawRemark({
					id: this.edit_id,
					remark: this.remark
				})
				this.getList(1)
				this.isDialogRemark = false
				this.$message.success('备注修改成功')
				this.remark = ''
			},
			// 批量选择
			handleSelectionChange(val) {
				console.log(val)
				this.multipleSelection = val
			},
			async batch_check(){
				if (this.multipleSelection.length >= 1) {
					const str = []
					this.multipleSelection.forEach((item) => {
						str.push(item.id)
					})
					const res = await this.$api.withdrawCheck({
						idListStr: JSON.stringify(str),
						failReason: this.form.failReason,
						isPass: this.form.isPass
					})
					if (res.status.code === 0) {
						this.$message.success('批量处理成功')
						this.isBatchDialog=false
						this.getList()
					} else {
						this.isBatchDialog=false
						this.getList()
						this.$message.error(res.status.msg)
					}
				}else{
					this.$message.error("请勾选你要批量的提现")
				}
			},
			openRemit(){
				this.$confirm('是否确认要一键批量打款所选择的用户(谨慎操作)', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				}).then(({ value }) => {
					this.isBatchRemit()
				}).catch(() => {

				});
			},
			async isBatchRemit(){
				if (this.multipleSelection.length >= 1) {
					const str = []
					this.multipleSelection.forEach((item) => {
						str.push(item.id)
					})
					const res = await this.$api.bankCardPay({
						idListStr: JSON.stringify(str),
					})
					if (res.status.code === 0) {
						this.$message.success('批量打款成功')
						this.getList()
					} else {
						this.getList()
						this.$message.error(res.status.msg)
					}
				}else{
					this.$message.error("请勾选你要批量的提现")
				}
			},
			openText(text){
				console.log(text)
				 this.$alert(text, '备注详情', {
				  confirmButtonText: '确定',
				  callback: action => {

				  }
				});
			},
			async userWithdraw(item){
				this.blackWhiteUserAdd(item.uid,"财务操作-用户账号禁用")
				// this.$confirm('账号禁用原因/备注', '提示', {
				//   confirmButtonText: '确定',
				//   cancelButtonText: '取消',
				// }).then(({ value }) => {

				// }).catch(() => {

				// });

			},
			async blackWhiteUserAdd(userId,value){
				await this.$api.blackWhiteUserAdd({
				  remark: value,
				  userId,
				  type: 'BLACK',
				  subType: 'WITHDRAW'
				})
				this.$message.success('用户禁用成功')
			},
			async idCard(item){
				await this.$api.blackWhiteUserAdd({
				  remark: "财务操作-身份证号禁用",
				  value: item.idCardNo,
				  type: 'BLACK',
				  subType: 'ID_CARD_NO_AUTH'
				})
				this.$message.success('身份证禁用实名成功')
			},
			selete_list(){
				this.page=1
				this.getList()
			}
		}
	}
</script>

<style>
	.el-table__cell {
		padding:0px !important;
	}
	.justify{
		display:flex;
		justify-content: space-between;
		align-items: center;
	}
	.el-form-item{
		margin-bottom:10px;
	}
	.demo-form-inline{
		padding:10px !important;
	}
</style>
