<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" style="background-color: #ffffff; padding: 20px">
      <el-form-item label="创建时间">
        <el-date-picker v-model="formInline.createAt" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="流水类型">
        <el-select  v-model="formInline.typeListJson" multiple placeholder="选择流水类型" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="提现" :value="1"></el-option>
          <el-option label="提现驳回" :value="10"></el-option>
          <el-option label="购买NFT" :value="3"></el-option>
          <el-option label="首次销售" :value="2"></el-option>
          <el-option label="二次销售" :value="7"></el-option>
          <el-option label="衍生合伙人佣金" :value="167"></el-option>
          <!-- <el-option label="邀请佣金" :value="11"></el-option> -->
          <el-option label="劳务报酬税" :value="50"></el-option>
          <el-option label="后台增加" :value="9"></el-option>
          <el-option label="大额充值" :value="24"></el-option>

          <el-option :value="168" label="开杠转账"></el-option>
            <el-option :value="180" label="账户转移增加"></el-option>
            <el-option :value="181" label="账户转移减少"></el-option>


          <el-option label="后台减少" :value="14"></el-option>
          <el-option label="五虎被邀请者返gas费" :value="15"></el-option>
          <el-option label="五虎被邀请者返" :value="16"></el-option>
          <el-option label="合成虎返gas费" :value="17"></el-option>
          <el-option label="合成虎返5%平台手续费" :value="18"></el-option>
          <el-option label="充值" :value="20"></el-option>
          <el-option label="购买燃料" :value="30"></el-option>
          <el-option label="扣减转赠佣金" :value="31"></el-option>
          <el-option label="支付保证金" :value="32"></el-option>
          <el-option label="购买燃料石" :value="33"></el-option>
          <el-option label="购买飞跃计划" :value="34"></el-option>
          <el-option label="版税" :value="40"></el-option>
          <el-option label="用户保证金退还" :value="60"></el-option>
          <el-option label="树藏余额购买商品" :value="70"></el-option>
          <el-option label="树藏成功出售商品" :value="71"></el-option>
          <el-option label="树藏卖家超时未发货退款" :value="72"></el-option>
          <el-option label="树藏买家申请取消订单退款" :value="73"></el-option>
          <el-option label="树藏系统关闭订单退款" :value="74"></el-option>
          <el-option label="钱包管理费" :value="80"></el-option>
          <el-option label="司乘奖励" :value="21"></el-option>
          <el-option label="教学奖励" :value="19"></el-option>
           <el-option label="模拟美股买入" :value="140"></el-option>
            <el-option label="模拟美股卖出" :value="141"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单编号">
        <el-input v-model="formInline.orderId" placeholder="请输入订单编号" clearable style="width: 320px" maxlength="25"
          show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="用户id">
        <el-input v-model="formInline.uid" placeholder="请输入用户id" clearable type="number"></el-input>
      </el-form-item>
      <el-form-item label="用户名">
        <el-input v-model="formInline.username" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="昵称">
        <el-input v-model="formInline.nickname" placeholder="请输入昵称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="chaxun()">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="batchExport()">导出当前数据</el-button>
        <!-- <el-button type="primary" @click="openExport()">用户流水快照导出</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
      v-loading="loading">
      <el-table-column fixed prop="id" label="id" align="center" width="80"></el-table-column>
      <el-table-column prop="uid" label="用户id" align="center"></el-table-column>
      <el-table-column prop="nickName" label="用户昵称" align="center"></el-table-column>
      <el-table-column prop="username" label="用户名" align="center" width="160"></el-table-column>
      <el-table-column prop="beginMoney" label="初期余额" align="center">
        <template scope="scope">
          <div>
            {{ scope.row.uid == 1 ? 0 : scope.row.beginMoney }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="enterMoney" label="进入余额" align="center"></el-table-column>
      <el-table-column prop="outMoney" label="消耗余额" align="center"></el-table-column>
      <el-table-column prop="endMoney" label="期末余额" align="center">
        <template scope="scope">
          <div>
            {{ scope.row.uid == 1 ? 0 : scope.row.endMoney }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="账号余额" align="center">
        <template scope="scope">
          <div>
            {{ scope.row.uid == 1 ? 0 : scope.row.balance }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="orderId" label="订单id" align="center"></el-table-column>
      <el-table-column prop="orderNum" label="支付回调订单号" align="center"></el-table-column>
      <el-table-column prop="typeStr" label="类型" align="center" width="180">
        <template scope="scope">
          <el-tag>{{ scope.row.typeStr }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="method" label="支付类型" align="center">
      </el-table-column>
      <!--  <el-table-column
        prop="BigDecimal"
        label="合计金额"
        align="center"
      ></el-table-column> -->
      <el-table-column prop="createdAt" label="创建时间" align="center" width="100px"></el-table-column>
      <el-table-column prop="remark" label="备注" align="left" show-overflow-tooltip width="300px">
        <template scope="scope">
          <div style="width:300px;cursor: pointer;" @click="openText(scope.row.remark)">{{ scope.row.remark }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div
      style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
      <div class="money_total">
        <div class="marginR">消耗金额总计:￥{{ outMoney }}</div>
      </div>
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="请选择用户快照导出时间" :visible.sync="dialogTime" width="20%">
      <el-date-picker style="width:100%;" v-model="timeValue" type="date" value-format="yyyy-MM-dd HH:mm:ss.SSS"
        placeholder="选择日期">
      </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTime = false">取 消</el-button>
        <el-button type="primary" @click="snapshotExport()">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import {
  downloadBlob
} from '@/utils/helper'

export default {
  name: 'accounting',
  data() {
    return {
      loading: false,
      formInline: {
        orderId: '',
        uid: '',
        createAt: null,
        startTime: '',
        endTime: '',
        typeListJson: '',
        nickname: '',
        username: ''
      },
      total: 1,
      tableData: [],
      dialogTime: false,
      timeValue: "",
      outMoney: ""
    }
  },
  mounted() {
    if (this.$route.query.uid) {
      this.formInline.uid = this.$route.query.uid
    }
    this.getList(1)
    this.getListTotal(1)
  },
  methods: {
    // 导出
    async batchExport() {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = undefined
        this.formInline.endTime = undefined
      }
      try {
        const res = await this.$api.financeExport({
          orderId: this.formInline.orderId,
          uid: this.formInline.uid,
          startTime: this.formInline.startTime,
          endTime: this.formInline.endTime,
          type: this.formInline.type,
          username: this.formInline.username,
          nickname: this.formInline.nickname,
          typeListJson: JSON.stringify(this.formInline.typeListJson)
        })
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
          this.getList()
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '账务' + Date.now() + '.csv')
          this.$message.success('导出成功')
          this.getList()
        }
      } finally {
        this.loading = false
      }
    },
    openExport() {
      this.dialogTime = true
    },
    async snapshotExport() {
      if (this.timeValue == '') {
        this.$message.warning('请选择时间')
        return false
      }
      this.loading = true
      this.dialogTime = false
      try {
        const res = await this.$api.snapshotExport({
          snapshotTime: this.timeValue,
        })
        if (res.status.code === 0) {
          this.loading = false
          res.result.fileUrlList.forEach((item) => {
            console.log(item)
            window.open(item, '_blank');
          })
          this.getList()
        } else {
          this.loading = false
          this.$message.error(res.status.msg)
        }
      } finally {
        this.loading = false
      }
    },
    // 查询列表
    async getList(page) {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
      const res = await this.$api.financeList({
        pageNum: page,
        pageSize: 15,
        uid: this.formInline.uid,
        orderId: this.formInline.orderId,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime,
        type: this.formInline.type,
        username: this.formInline.username,
        nickname: this.formInline.nickname,
        typeListJson: JSON.stringify(this.formInline.typeListJson)
      })
      this.loading = false
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    async getListTotal(page) {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
      const res = await this.$api.financeListTotal({
        pageNum: page,
        pageSize: 15,
        uid: this.formInline.uid,
        orderId: this.formInline.orderId,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime,
        type: this.formInline.type,
        username: this.formInline.username,
        nickname: this.formInline.nickname,
        typeListJson: JSON.stringify(this.formInline.typeListJson)
      })
      console.log(res.result)
      this.outMoney = res.result.outMoney
    },
    xuanze(val) {
      this.getList(val)
    },
    clear() {
      this.formInline.uid = ''
      this.formInline.type = ''
      this.formInline.orderId = ''
      this.formInline.createAt = undefined
    },
    chaxun() {
      this.getList(1)
      this.getListTotal(1)
    },
    openText(text) {
      console.log(text)
      this.$alert(text, '备注详情', {
        confirmButtonText: '确定',
        callback: action => {

        }
      });
    },
  }
}
</script>

<style lang="scss">
.money_total {
  font-size: 14px;
  color: #F56C6C;
  display: flex;

  .marginR {
    margin-right: 30px;
  }
}
</style>
