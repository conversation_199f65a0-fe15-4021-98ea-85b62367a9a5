<template>
  <d2-container class="page" style="background-color:#FFFFFF;">
    <div id="container"></div>
  </d2-container>
</template>

<script>
import * as THREE from 'three'
import {
  sRGBEncoding
} from 'three'
// 此处加载gltf模型，故使用GLFTLoader
import {
  GLTFLoader
} from 'three/examples/jsm/loaders/GLTFLoader'
// 其他模型加载器
import {
  OBJLoader,
  MTLLoader
} from 'three-obj-mtl-loader'
import {
  OrbitControls
} from 'three/examples/jsm/controls/OrbitControls.js'
import {
  ColladaLoader
} from 'three/examples/jsm/loaders/ColladaLoader.js'
import {
  RGBELoader
} from 'three/examples/jsm/loaders/RGBELoader.js'

export default {
  data() {
    return {
      container: null, // 挂载点
      // stats: null,//性能插件
      clock: null, // 时钟对象
      controls: null, // 轨道控制器
      camera: null, // 摄像机
      scene: null, // 场景
      renderer: null, // 渲染器
      mixer: null, // 加载模型的动画
      axes: null // 三维线

    }
  },
  mounted() {
    this.init()
    this.animate()
  },
  methods: {
    nav_detalist() {
      console.log('点击了')
    },
    init() {
      var _this = this

      this.container = document.getElementById('container') // 获取挂载点

      this.camera = new THREE.PerspectiveCamera(25, window.innerWidth / window.innerHeight, 1, 1000) // 摄像机
      this.camera.position.set(10, 5, 15) // 调整摄像机位置  x  y  z

      this.scene = new THREE.Scene() // 创建场景
      console.log('scene', this.scene) // 打印

      this.clock = new THREE.Clock() // 时钟对象
      console.log('clock', this.clock)

      this.camera.lookAt(this.scene.position)
      // 设置三维线
      this.axes = new THREE.AxesHelper(5) // 设置一个三维坐标轴，参数位轴的长度  默认参数为 1
      // this.axes.setColors(0x00ffff,0xffff00,0xff00ff);//设置 x y z 轴的颜色
      this.scene.add(this.axes) // 添加到场景对象中

      // RGBELoader  环境贴图
      new RGBELoader()
        .load('https://nftcn.oss-cn-shanghai.aliyuncs.com/file/royal_esplanade_1k.hdr', function (texture) {
          console.log('texture', texture)
          texture.mapping = THREE.EquirectangularReflectionMapping

          _this.scene.background = texture // 场景
          console.log('111111111', _this.scene.background)
          // _this.scene.background = 'https://cdn-lingjing.nftcn.com.cn/h5/static/38/goddess_header.png';//场景
          _this.scene.environment = texture // 环境光

          // render();

          // model

          const loader = new ColladaLoader()
          loader.load('https://nftcn.oss-cn-shanghai.aliyuncs.com/file/untitled.dae', function (collada) {
            console.log('collada', collada) // 查看dae模型参数
            const avatar = collada.scene
            // const animations = collada.animations;//获取dae模型动画参数
            console.log('avatar', avatar)
            avatar.traverse(function (node) {
              if (node.isSkinnedMesh) {
                node.frustumCulled = false
              }
            })
            // 添加骨骼辅助
            // meshHelper = new THREE.SkeletonHelper(avatar);
            // _this.scene.add(meshHelper);

            // avatar.rotation.z += 1;
            avatar.scale.set(1, 1, 1) // 模型比例
            /// /AnimationMixer是场景中特定对象的动画播放器。当场景中的多个对象独立动画时，可以为每个对象使用一个AnimationMixer

            // _this.mixer = new THREE.AnimationMixer(avatar);
            console.log(_this.mixer)
            // _this.mixer.clipAction(animations[0]).play();//clipAction方法生成可以控制执行动画的实例   play调用

            _this.scene.add(avatar) // 添加到场景
          })
        })

      // collada处理dae格式模型

      // 网格辅助线  GridHelper

      const gridHelper = new THREE.GridHelper(1, 2, 0x888888, 0x444444)
      this.scene.add(gridHelper)

      // 环境光源  AmbientLight

      const ambientLight = new THREE.AmbientLight(0xffffff, 1)
      this.scene.add(ambientLight)

      // 点光源  PointLight

      const pointLight = new THREE.PointLight(0xffffff, 1)
      this.scene.add(this.camera)
      this.camera.add(pointLight)

      // 渲染器

      this.renderer = new THREE.WebGLRenderer({
        antialias: true
      })
      this.renderer.outputEncoding = THREE.sRGBEncoding
      this.renderer.setPixelRatio(window.devicePixelRatio) // setPixelRatio  设置设备像素比。通常用于避免HiDPI设备上绘图模糊
      this.renderer.setSize(window.innerWidth, window
        .innerHeight) // setSize  输出canvas的大小调整为(width, height)并考虑设备像素比，且将视口从(0, 0)开始调整到适合大小 将updateStyle设置为false以阻止对canvas的样式做任何改变。
      this.renderer.setClearAlpha(0.8) // 背景透明度
      this.container.appendChild(this.renderer.domElement) // 添加到节点末尾

      // 轨道控制器

      this.controls = new OrbitControls(this.camera, this.renderer.domElement)
      this.controls.screenSpacePanning = true // 定义平移时如何平移相机的位置。如果为 true，则相机在屏幕空间中平移。否则，相机会在与相机向上方向正交的平面中平移。默认为假。
      this.controls.minDistance = 5 // 最小缩放
      this.controls.maxDistance = 40 // 最大距离
      this.controls.target.set(0, 2, 0) // 焦点
      this.controls.update()

      // 性能插件

      // this.stats = new Stats();
      // this.container.appendChild(this.stats.dom);

      //

      window.addEventListener('resize', this.onWindowResize)
    },
    onWindowResize() {
      this.camera.aspect = window.innerWidth / window.innerHeight
      this.camera.updateProjectionMatrix()

      this.renderer.setSize(window.innerWidth, window.innerHeight)
    },

    animate() {
      requestAnimationFrame(this.animate)

      this.render()
      // this.stats.update();
    },
    render() {
      const delta = this.clock.getDelta()

      if (this.mixer !== undefined) {
        // this.mixer.update(delta);//更新

      }

      this.renderer.render(this.scene, this.camera)
    }

  }
}
</script>
<style></style>
