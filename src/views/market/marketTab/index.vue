<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="clear()"
          >清除移动端市场缓存</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'marketTab',
  data () {
    return {
      formInline: {}
    }
  },
  methods: {
    async clear () {
      await this.$api.clearMarketCache()
      this.$message.success('清除成功')
    }
  }
}
</script>

<style>
</style>
