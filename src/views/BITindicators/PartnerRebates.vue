<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :showExport="false" @onExport="houseExport" :data="query"
            @onSubmit="onQueryChange" @onReset="onQueryReset"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #createTime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.createTime) }}</span>
            </template>

            <template #rebatesPercent="scope">
              {{ scope.row.rebatesPercent * 100 + '%' }}
            </template>

            <template #partnerType="scope">
                <el-tag :type="scope.row.partnerType == 2 ? 'success' : 'info'">{{ scope.row.partnerType == 2 ? '超级' : '普通' }}</el-tag>
            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="total" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import { partnerDetail } from '@/api/bit'
import { invite, inviteExport, inviteOpenAmount, inviteOpenCount } from "@/api/bit.js"
import { getBitClose, exportBitClose, getSymbolList } from "@/api/bitconfig"
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "BITcloseHouseRecond",
    components: {
        CommonQuery,
        CommonTable
    },
    props: {},
    data() {
        return {
            coinlist: [],
            querytime: [],
            total: 0,
            listLoading: true,
            tableData: [],
            query: {
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            querySchema: [
                {
                    type: 'input',
                    label: '合伙人con add：',
                    placeholder: '请输入合伙人con add',
                    field: 'conadd'
                },
                {
                    type: 'input',
                    label: '合伙人用户昵称',
                    placeholder: '请输入合伙人用户昵称',
                    field: 'nickname'
                },
                {
                    type: 'input',
                    label: '合伙人用户手机号',
                    placeholder: '请输入合伙人用户手机号',
                    field: 'phone'
                }
            ],
            tableSchema: [ // 表格架构
                {
                    label: '合伙人 con add',
                    field: 'conadd',
                },
                {
                    label: '合伙人昵称',
                    field: 'nickname',
                },
                {
                    label: '合伙人返佣比例',
                    slot: 'rebatesPercent',
                },
                {
                    label: '合伙人等级',
                    slot: 'partnerType',
                    // type: 'tag',
                    // tagMap: {
                    //     2: {
                    //         label: '超级',
                    //         tagType: 'success'
                    //     },
                    //     1: {
                    //         label: '普通',
                    //         tagType: 'info'
                    //     }
                    // },
                },
                {
                    label: '成为合伙人时间',
                    slot: 'createTime',
                },
                {
                    label: '直接好友人数',
                    field: 'directInviteNum',
                },
                {
                    label: '间接好友真开仓人数',
                    field: 'indirectInviteNum',
                },
                {
                    label: '直接好友真开仓人数',
                    field: 'directInviteOpenWithCashNum',
                },
                {
                    label: '间接好友真开仓人数',
                    field: 'indirectInviteOpenWithCashNum',
                },
                {
                    label: '近30天返佣金额',
                    field: 'thirtyRebateAmount',
                },
                {
                    label: '近7日返佣金额',
                    field: 'sevenRebateAmount',
                },
                {
                    label: '旗下好友总开仓金额（不算体验金/万能金）',
                    field: 'totalOpenAmount',
                },
                {
                    label:"好友开仓总金额(带杠杆)",
                    field:'totalOpenAmountWithLevel'
                },
                {
                    label: "累计返佣金额",
                    field: 'totalRebateAmount'
                },
            ],

            tableData: [{}],

            page: {
                // total: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
                userType: 1,
            }
        }
    },
    created() {
        const todayMidnight = this.getTodayMidnight();
        const yesterdayMidnight = this.getYesterdayMidnight();

        this.querytime[0] = this.formatDate(yesterdayMidnight)
        this.querytime[1] = this.formatDate(todayMidnight)
    },
    async mounted() {
        this.getList()
    },
    methods: {
        async gocheck(item) {
            // label: '被邀请人开仓订单数',
            // slot: 'inviteeOrderCount',
            const res = await inviteOpenCount({
                invitedConAdd: item.invitedConAdd
            })
            if (res.status.code === 0) {
                item.inviteeOrderCount = res.result
            }
        },
        async gocheckAmount(item) {
            // label: '被邀请人开仓订单数',
            // slot: 'inviteeOrderCount',
            const res = await inviteOpenAmount({
                invitedConAdd: item.invitedConAdd
            })
            if (res.status.code === 0) {
                item.inviteeOpenTradeAmount = res.result
            }
        },
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                this.coinlist = res.result
                this.query.contractName = res.result[0].contractName
                this.coinlist.push({
                    contractName: 'E-BIT-USDT',
                })
            }
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async houseExport(e) {
            const res = await inviteExport({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '邀新记录' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 过滤查询
        onQueryChange(data) {
            console.log(data, '查询后的数据');
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {
            this.querytime = []

            this.query = {
                userType: '1'
            }
            this.query1 = {
                userType: '1',
            }
            this.listLoading = true
            this.getList()
        },
        // 获取列表
        async getList(isInit) {

            // this.query1.createStart = this.querytime[0] + '.000';
            // this.query1.createEnd = this.querytime[1] + '.000';
            const params = {
                ...this.query1,
                // contractName: this.query.contractName,
                ...this.page,

                pageNum: isInit ? 1 : this.page.pageNum,
            }

            console.log('列表数据', params)
            params.statusListStr = JSON.stringify(params.statusListStr)

            // params.statusListStr = Number(params.statusListStr)

            const {
                status,
                result
            } = await partnerDetail(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []
                const data = result.list
                data.forEach((item) => {
                    item.inviteeOrderCount = null
                    item.inviteeOpenTradeAmount = null
                    dataList.push(item)
                })
                this.tableData = dataList
                this.total = result.totalCount
                // this.page.total = result.totalCount
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        // 获取当天 0 点时间
        getTodayMidnight() {
            const now = new Date();
            // 设置日期为明天
            now.setDate(now.getDate() + 1);
            // 重置小时、分钟、秒、毫秒为 0
            now.setHours(0, 0, 0, 0);
            return now;
        },

        // 获取昨天 0 点时间
        getYesterdayMidnight() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate()); // 减去一天
            yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
            return yesterday;
        },
    }
}
</script>

<style></style>