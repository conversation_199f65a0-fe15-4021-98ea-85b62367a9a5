<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" :data="query"
            @onSubmit="onQueryChange" @onReset="onQueryReset"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #inviteDate="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.inviteDate) }}</span>
            </template>

            <template #invitedFirstOpenDate="scope">
                <span type="text" size="small" v-if="scope.row.invitedFirstOpenDate">{{
                    removeMilliseconds(scope.row.invitedFirstOpenDate) }}</span>
            </template>

            <!-- {
                    label: '被邀请人开仓订单数',
                    slot: 'inviteeOrderCount',
                },
                {
                    label: '被邀请人开杠交易额',
                    slot: 'inviteeOpenTradeAmount',
                }, -->
            <template #inviteeOrderCount="scope">
                <span type="text" size="small" v-if="scope.row.inviteeOrderCount != null">{{ scope.row.inviteeOrderCount
                    }}</span>
                <el-button type="text" size="small" @click="gocheck(scope.row)" v-else>查看</el-button>
            </template>
            <template #inviteeOpenTradeAmount="scope">
                <span type="text" size="small" v-if="scope.row.inviteeOpenTradeAmount != null">{{
                    scope.row.inviteeOpenTradeAmount
                }}</span>
                <el-button type="text" size="small" @click="gocheckAmount(scope.row)" v-else>查看</el-button>
            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="total" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import { invite, inviteExport, inviteOpenAmount, inviteOpenCount } from "@/api/bit.js"
import { getBitClose, exportBitClose, getSymbolList } from "@/api/bitconfig"
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "BITcloseHouseRecond",
    components: {
        CommonQuery,
        CommonTable
    },
    props: {},
    data() {
        return {
            coinlist: [],
            querytime: [],
            total: 0,
            listLoading: true,
            tableData: [],
            query: {
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型

            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '邀请人昵称',
                    placeholder: '请输入邀请人昵称',
                    field: 'inviteNickname'
                },
                {
                    type: 'input',
                    label: '邀请人con add：',
                    placeholder: '请输入邀请人con add',
                    field: 'inviteConAdd'
                },
                {
                    type: 'input',
                    label: '被邀请人昵称',
                    placeholder: '请输入被邀请人昵称',
                    field: 'invitedNickname'
                },
                {
                    type: 'input',
                    label: '被邀请人con add：',
                    placeholder: '请输入被邀请人con add',
                    field: 'invitedConAdd'
                },
                // {
                //     type: 'datetimerange',
                //     label: '被邀请人首次开仓时间：',
                //     placeholder: '请输入被邀请人首次开仓时间',
                //     field: 'createStart',
                //     field2: 'createEnd',
                // },
                {
                    type: 'datetimerange',
                    label: '邀请时间：',
                    placeholder: '请输入邀请时间',
                    field: 'inviteBegin',
                    field2: 'inviteEnd',
                },
                // {
                //     type: 'twoinput',
                //     label: '被邀请人开仓订单数：',
                //     placeholder: '请输入订单数',
                //     field: ['amountStart', 'amountEnd'],
                // },
                // {
                //     type: 'twoinput',
                //     label: '被邀请人交易额：',
                //     placeholder: '请输入交易额',
                //     field: ['amountStart', 'amountEnd'],
                // },
            ],
            tableSchema: [ // 表格架构
                {
                    label: '邀请时间',
                    slot: 'inviteDate',
                },
                {
                    label: '邀请人昵称',
                    field: 'inviteNickName',
                },
                {
                    label: '邀请人 con add',
                    field: 'inviteConAdd',
                },
                {
                    label: '被邀请人昵称',
                    field: 'invitedNickName',
                },
                {
                    label: '被邀请人 con add',
                    field: 'invitedConAdd',
                },
                {
                    label: '被邀请人首次开仓时间',
                    slot: 'invitedFirstOpenDate',
                },
                {
                    label:'同身份证下所有账号是否用真钱开仓过',
                    field:"sameIdCardOpenedWithCash",
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '未开仓',
                            tagType: 'info'
                        },
                        1: {
                            label: '开仓',
                            tagType: 'success'
                        }
                    },
                },
                {
                    label: '被邀请人开仓订单数',
                    slot: 'inviteeOrderCount',
                },
                {
                    label: '被邀请人开杠交易额',
                    slot: 'inviteeOpenTradeAmount',
                },
            ],
            tableData: [{}],

            page: {
                // total: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
                userType: 1,
            }
        }
    },
    created() {
        const todayMidnight = this.getTodayMidnight();
        const yesterdayMidnight = this.getYesterdayMidnight();

        this.querytime[0] = this.formatDate(yesterdayMidnight)
        this.querytime[1] = this.formatDate(todayMidnight)
    },
    async mounted() {
        this.getList()
    },
    methods: {
        async gocheck(item) {
            // label: '被邀请人开仓订单数',
            // slot: 'inviteeOrderCount',
            const res = await inviteOpenCount({
                invitedConAdd: item.invitedConAdd
            })
            if (res.status.code === 0) {
                item.inviteeOrderCount = res.result
            }
        },
        async gocheckAmount(item) {
            // label: '被邀请人开仓订单数',
            // slot: 'inviteeOrderCount',
            const res = await inviteOpenAmount({
                invitedConAdd: item.invitedConAdd
            })
            if (res.status.code === 0) {
                item.inviteeOpenTradeAmount = res.result
            }
        },
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                this.coinlist = res.result
                this.query.contractName = res.result[0].contractName
                this.coinlist.push({
                    contractName: 'E-BIT-USDT',
                })
            }
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async houseExport(e) {
            const res = await inviteExport({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '邀新记录' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 过滤查询
        onQueryChange(data) {
            console.log(data, '查询后的数据');
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {
            this.querytime = []

            this.query = {
                userType: '1'
            }
            this.query1 = {
                userType: '1',
            }
            this.listLoading = true
            this.getList()
        },
        // 获取列表
        async getList(isInit) {

            // this.query1.createStart = this.querytime[0] + '.000';
            // this.query1.createEnd = this.querytime[1] + '.000';
            const params = {
                ...this.query1,
                // contractName: this.query.contractName,
                ...this.page,

                pageNum: isInit ? 1 : this.page.pageNum,
            }

            console.log('列表数据', params)
            params.statusListStr = JSON.stringify(params.statusListStr)

            // params.statusListStr = Number(params.statusListStr)

            const {
                status,
                result
            } = await invite(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []
                const data = result.list
                data.forEach((item) => {
                    item.inviteeOrderCount = null
                    item.inviteeOpenTradeAmount = null
                    dataList.push(item)
                })
                this.tableData = dataList
                this.total = result.totalCount
                // this.page.total = result.totalCount
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        // 获取当天 0 点时间
        getTodayMidnight() {
            const now = new Date();
            // 设置日期为明天
            now.setDate(now.getDate() + 1);
            // 重置小时、分钟、秒、毫秒为 0
            now.setHours(0, 0, 0, 0);
            return now;
        },

        // 获取昨天 0 点时间
        getYesterdayMidnight() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate()); // 减去一天
            yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
            return yesterday;
        },
    }
}
</script>

<style></style>