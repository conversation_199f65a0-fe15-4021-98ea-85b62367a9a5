<template>
  <d2-container class="page">
    <div v-loading="loading">


      <!-- 增加数据按钮 -->
      <el-button type="primary" @click="openAddDialog" style="margin-bottom: 20px;">增加</el-button>

      <common-query :showExport="false" :query-schema="querySchema" @onSubmit="onQueryChange" :data="query"
        @onReset="onQueryReset"></common-query>

      <div style="display: flex;align-items: center">
        <h4 style="margin-right: 20px;"> 策略开关 : </h4>
        <el-switch v-model="Tacticsopen" @change="TrcenterDialogVisible = true" active-text="开" inactive-text="关">
        </el-switch>
      </div>

      <!--盘口铺单  handicapLaysData -->
      <div>
        <h3>盘口铺单</h3>
        <el-table :data="handicapLaysData" border>
          <el-table-column prop="id" label="id" align="center"></el-table-column>
          <el-table-column prop="key" label="key" align="center"></el-table-column>
          <el-table-column prop="value" label="值" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>

          <el-table-column prop="updateAdmin" label="操作人" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" align="center">
            <template slot-scope="scope">
              {{ removeMilliseconds(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-button type="warning" size="mini" @click="openEditDialog(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @current-change="onehandlePageChange" :current-page="handicapLayspage"
          :page-size="handicapLayspageSize" layout="total, prev, pager, next" :total="handicapLaytotal">
        </el-pagination>
      </div>
      <div style="height: 80px;"></div>

      <!-- 远端铺单  farEndLaysData -->
      <div>
        <h3>远端铺单 </h3>

        <el-table :data="farEndLaysData" border>
          <el-table-column prop="id" label="id" align="center"></el-table-column>
          <el-table-column prop="key" label="key" align="center"></el-table-column>
          <el-table-column prop="value" label="值" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>

          <el-table-column prop="updateAdmin" label="操作人" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" align="center">
            <template slot-scope="scope">
              {{ removeMilliseconds(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-button type="warning" size="mini" @click="openEditDialog(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @current-change="tohandlePageChange" :current-page="farEndLayspage"
          :page-size="farEndLayspageSize" layout="total, prev, pager, next" :total="farEndLaytotal">
        </el-pagination>

      </div>
      <div style="height: 80px;"></div>


      <!-- 自成交  selfTradeData -->
      <div>
        <h3>自成交</h3>

        <el-table :data="selfTradeData" border>
          <el-table-column prop="id" label="id" align="center"></el-table-column>
          <el-table-column prop="key" label="key" align="center"></el-table-column>
          <el-table-column prop="value" label="值" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>

          <el-table-column prop="updateAdmin" label="操作人" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" align="center">
            <template slot-scope="scope">
              {{ removeMilliseconds(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-button type="warning" size="mini" @click="openEditDialog(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @current-change="threehandlePageChange" :current-page="selfTradepage"
          :page-size="selfTradepageSize" layout="total, prev, pager, next" :total="selfTradetotal">
        </el-pagination>

      </div>
      <div style="height: 80px;"></div>


      <!-- 资金费率  fundingRateData -->
      <div>
        <h3>资金费率</h3>

        <el-table :data="fundingRateData" border>
          <el-table-column prop="id" label="id" align="center"></el-table-column>
          <el-table-column prop="key" label="key" align="center"></el-table-column>
          <el-table-column prop="value" label="值" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>

          <el-table-column prop="updateAdmin" label="操作人" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" align="center">
            <template slot-scope="scope">
              {{ removeMilliseconds(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-button type="warning" size="mini" @click="openEditDialog(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @current-change="fourhandlePageChange" :current-page="fundingRatepage"
          :page-size="fundingRatepageSize" layout="total, prev, pager, next" :total="fundingRatetotal">
        </el-pagination>
      </div>
      <div style="height: 80px;"></div>


      <!-- 其他类型  otherTypeData -->
      <div>
        <h3>其他类型</h3>

        <el-table :data="otherTypeData" border>
          <el-table-column prop="id" label="id" align="center"></el-table-column>
          <el-table-column prop="key" label="key" align="center"></el-table-column>
          <el-table-column prop="value" label="值" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>

          <el-table-column prop="updateAdmin" label="操作人" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="操作时间" align="center">
            <template slot-scope="scope">
              {{ removeMilliseconds(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-button type="warning" size="mini" @click="openEditDialog(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @current-change="fivehandlePageChange" :current-page="otherTypepage" :total="otherTypetotal"
          :page-size="otherTypepageSize" layout="total, prev, pager, next">
        </el-pagination>
      </div>

      <!-- 手续费配置 -->
      <!-- <div>
      <h3>手续费配置</h3>
      <h4>折扣手续费</h4>
      <el-button type="primary" @click="dialogVisible = true; dialogTitle = '新增'"
        style="margin-bottom: 20px;">新增</el-button>
      <el-table :data="rateTypeData" border>
        <el-table-column prop="name" label="持有系列" align="center"></el-table-column>
        <el-table-column prop="discount" label="手续费折扣" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="endTime" label="到期时间" align="center">
          <template slot-scope="scope">
            {{ removeMilliseconds(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template v-slot="scope">
            <el-button type="warning" size="mini" @click="EditDialog(scope.row)">修改</el-button>
            <el-button type="danger" size="mini" @click="DeleteDialog(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @current-change="sixhandlePageChange" :current-page="ratepageNum" :total="rateTypetotal"
        :page-size="ratepageSize" layout="total, prev, pager, next">
      </el-pagination>
    </div> -->


      <!-- 手续费配置弹窗 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
        <el-form :model="FormData" :rules="rules2" ref="formRef" label-width="100px">
          <!-- 远端搜索的 input -->
          <el-form-item label="系列名" prop="cid">
            <el-autocomplete style="width:340px;" size="mini" v-model="FormData.ctid"
              :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
            </el-autocomplete>
          </el-form-item>

          <!-- 时间选择器 -->
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker style="width:340px;" v-model="FormData.endTime" type="datetime" placeholder="选择结束时间"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <!-- 折扣输入框 -->
          <el-form-item label="折扣" prop="discount">
            <el-input v-model="FormData.discount" style="width:340px;" placeholder="请输入折扣" type="number"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </el-dialog>

      <!-- 修改弹窗 -->
      <el-dialog :visible.sync="editDialogVisible" title="配置修改">
        <el-form :model="editForm" ref="editFormRef" :rules="rules">
          <el-form-item label="key">
            <el-input v-model="editForm.key" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="值">
            <el-input v-model="editForm.value"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="editForm.remark"></el-input>
          </el-form-item>
          <el-form-item label="配置">
            <el-select :popper-append-to-body="false" v-model="editForm.type" placeholder="Select Type"
              style="width: 100%;">
              <el-option v-for="type in options" :key="type" :label="type.label" :value="type.value">
              </el-option>
            </el-select>
          </el-form-item>

        </el-form>
        <div slot="footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit">确定</el-button>
        </div>
      </el-dialog>

      <!-- 增加弹窗 -->
      <el-dialog :visible.sync="addDialogVisible" title="配置添加">
        <el-form :model="addForm" ref="addFormRef" :rules="rules">
          <el-form-item label="键" prop="key">
            <el-input v-model="addForm.key" placeholder="请输入键"></el-input>
          </el-form-item>
          <el-form-item label="值" prop="value">
            <el-input v-model="addForm.value" placeholder="请输入值"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addForm.remark" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="addForm.type" placeholder="Select Type" style="width: 100%;">
              <el-option v-for="type in options" :key="type.id" :label="type.label" :value="type.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd">确定</el-button>
        </div>
      </el-dialog>

      <!-- bit开门关门 -->
      <el-dialog :visible.sync="centerDialogVisible" :show-close="false" :close-on-click-modal="false"
        :close-on-press-escape="false" width="30%" center>
        <h4 style="display: inline-block;">
          BIT指数{{ isopen ? '开门' : '关门' }}请输入
        </h4>
        <h4 :style="{ display: 'inline-block', color: isopen ? 'green' : 'red' }">
          {{ isopen ? 'OPEN' : 'CLOSE' }}
        </h4>

        <el-input v-model="bitopenkw" placeholder="注意大小写" style="margin-top: 20px;"></el-input>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelbit">取 消</el-button>
          <el-button type="primary" @click="makeopen">确 定</el-button>
        </span>
      </el-dialog>


      <!-- b策略开门关门 -->
      <el-dialog :visible.sync="TrcenterDialogVisible" :show-close="false" :close-on-click-modal="false"
        :close-on-press-escape="false" width="30%" center>
        <h4>策略开关</h4>
        <el-select :popper-append-to-body="false" v-model="strategy" placeholder="请选择策略开关">
          <el-option label="开" :value="0"></el-option>
          <el-option label="关" :value="1"></el-option>
        </el-select>
        <span slot="footer" class="dialog-footer">
          <el-button @click="trcancelbit">取 消</el-button>
          <el-button type="primary" @click="trmakeopen">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 二次确认框 -->
      <el-dialog title="确认删除" :visible.sync="confirmDeleteVisible">
        <span>确定要删除这条吗？</span>
        <div slot="footer" class="dialog-footer">
          <el-button @click="confirmDeleteVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteItem">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </d2-container>


</template>

<script>
import { getBitConfig, addBitConfig, getSymbolList, getBitDetail, updateFeeConfig, addFeeConfig, getFeeConfig, deleteFeeConfig } from "@/api/bitconfig"
import CommonQuery from '@/components/CommonQuery_h'

export default {
  components: {
    CommonQuery,
  },
  data() {
    return {
      loading: false,
      coinlist: [],
      query: {
        contractName: "E-BTC-USDT"
      },
      tempDelobj: {},
      confirmDeleteVisible: false,
      rateTypeData: [],
      results: [],
      dialogVisible: false,
      dialogTitle: '新增',
      FormData: {
        ctid: '',
        endTime: '',
        discount: '',
        cid: '',
        name: '',
      },
      rules2: {
        cid: [
          { required: true, message: 'CTID不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        discount: [
          { required: true, message: '折扣不能为空', trigger: 'blur' },
          // { type: 'number', min: 0, max: 1, message: '折扣范围必须在0到1之间', trigger: 'blur' }
        ]
      },
      ratepageNum: 1,
      ratepageSize: 50,
      options: [{
        value: '选项1',
        label: '黄金糕'
      }, {
        value: '选项2',
        label: '双皮奶'
      }, {
        value: '选项3',
        label: '蚵仔煎'
      }, {
        value: '选项4',
        label: '龙须面'
      }, {
        value: '选项5',
        label: '北京烤鸭'
      }],
      value: '',
      strategy: '',
      TrcenterDialogVisible: false,
      Tacticsopen: 1,
      bitopenkw: '',
      isopen: true,
      centerDialogVisible: false,
      bitopen: false,
      options: [
        { label: '盘口铺单', value: 'handicapLays' },
        { label: '远端铺单', value: 'farEndLays' },
        { label: '自成交', value: 'selfTrade' },
        { label: '资金费率', value: 'fundingRate' },
        { label: '其他类型', value: 'otherType' }
      ],
      types: ['handicapLays', 'farEndLays', 'selfTrade', 'fundingRate', 'otherType'],
      // 表格数据
      tables: Array(5).fill({
        data: [
          { name: "Item 1", value: "100", remark: "Remark 1", operator: "Operator 1", operationTime: "2024-08-01" },
          { name: "Item 2", value: "200", remark: "Remark 2", operator: "Operator 2", operationTime: "2024-08-02" },
          // 更多数据...
        ],
        currentPage: 1,
        pageSize: 10,
        total: 20,
      }),

      // 弹窗控制
      editDialogVisible: false,
      addDialogVisible: false,
      // querySchema: ,
      // 编辑表单数据
      editForm: {
        name: "",
        value: "",
        remark: "",
        operator: "",
        operationTime: "",
      },

      // 增加表单数据
      addForm: {
        value: "",
        remark: "",
      },
      page: 1,
      pageSize: 50,
      handicapLaysData: [],
      // { name: "Item 3333", value: "100", remark: "Remark 1", operator: "Operator 1", operationTime: "2024-08-01" },
      //   { name: "Item 2", value: "200", remark: "Remark 2", operator: "Operator 2", operationTime: "2024-08-02" },
      //   // 更多数据...
      // ],
      farEndLaysData: [],
      selfTradeData: [],
      fundingRateData: [],
      otherTypeData: [],

      rules: {
        key: [
          { required: true, message: 'key是必填项', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '值是必填项', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '备注是必填项', trigger: 'blur' }
        ],
        operator: [
          { required: true, message: '操作人是必填项', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '类型是必填项', trigger: 'blur' }
        ]
      },
      handicapLayspageSize: 50,
      handicapLayspage: 1,
      farEndLayspageSize: 50,
      farEndLayspage: 1,

      selfTradepageSize: 50,
      selfTradepage: 1,
      fundingRatepageSize: 50,
      fundingRatepage: 1,

      otherTypepageSize: 50,
      otherTypepage: 1,

      rateTypepageSize: 50,
      rateTypepage: 1,


      handicapLaytotal: 0,
      farEndLaytotal: 0,
      selfTradetotal: 0,
      fundingRatetotal: 0,
      otherTypetotal: 0,
      rateTypetotal: 0
    };
  },

  async mounted() {
    await this.fetchcoin()
    setTimeout(() => {
      this.fetchBitConfig({});
      this.fetchBITopen()
      this.fetchTacticsopen()
    }, 100);

    // this.fetchFeeRate()
    // this.fetchhandicapLays()
    // this.fetchOtherType()

    // this.fetchFarEndLays()
    // this.fetchSelfTrade()
    // this.fetchFundingRate()
    // console.log(this.coinlist, 123);

    // this.query.contractName = 'E-BTC-USDT'
    console.log(this.query.contractName, 1231231231221);



  },
  watch: {
    "query.contractName"(newValue, oldValue) {
      console.log(newValue, '只变了');

      this.fetchBitConfig({});
      this.fetchBITopen()
      this.fetchTacticsopen()
    },
    centerDialogVisible() {
      this.bitopenkw = ''
    },
    TrcenterDialogVisible() {
      // this.
    }
  },
  methods: {
    changeC(e) {
      console.log(e);
      this.query.contractName = e
    },
    async fetchcoin() {
      const res = await getSymbolList({})
      if (res.status.code == 0) {
        // this.coinlist = res.result
        this.coinlist = res.result.map(item => ({
          label: item.contractName,
          value: item.contractName,
        }))
        this.coinlist.push({
          label: 'E-BIT-USDT',
          value: 'E-BIT-USDT',
        })
      }
    },
    DeleteDialog(e) {
      this.confirmDeleteVisible = true
      this.tempDelobj = e
    },
    async deleteItem() {
      let res = await deleteFeeConfig({
        id: this.tempDelobj.id
      })
      if (res.status.code == 0) {
        this.confirmDeleteVisible = false
        this.$message.success(res.status.msg)
        this.fetchFeeRate()
      }
    },
    handleSelect(item) {
      console.log(item, 12312312);
      this.FormData.cid = item.cid
      this.FormData.name = item.name
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'cid': item.ctid,
                'name': item.name,
                'value': `${item.name}(${item.ctid})`,
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    EditDialog(e) {
      this.dialogTitle = '编辑'
      // this.FormData = e
      this.FormData.ctid = e.name
      this.FormData.name = e.name
      this.FormData.endTime = e.endTime
      this.FormData.discount = e.discount
      this.FormData.cid = e.cid

      this.FormData.id = e.id
      this.dialogVisible = true
    },
    async submitForm() {
      console.log(this.FormData, '12312123');
      if (this.dialogTitle == '编辑') {
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            // 提交逻辑
            let data = {
              id: this.FormData.id,
              cid: this.FormData.cid,
              endTime: this.FormData.endTime,
              discount: this.FormData.discount,
              name: this.FormData.name,

            }
            updateFeeConfig(data).then(res => {
              if (res.status.code == 0) {
                this.$message.success(res.status.msg)
                this.fetchFeeRate()
                this.FormData = {
                  cid: '',
                  endTime: '',
                  discount: '',
                }
              }
            })
            this.dialogVisible = false;
          } else {
            console.log('表单验证失败');
            return false;
          }
        });
      } else {
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            // 提交逻辑
            let data = {
              cid: this.FormData.cid,
              endTime: this.FormData.endTime,
              discount: this.FormData.discount,
              name: this.FormData.name,
            }
            addFeeConfig(data).then(res => {
              if (res.status.code == 0) {
                this.$message.success(res.status.msg)
                this.fetchFeeRate()
                this.FormData = {
                  cid: '',
                  endTime: '',
                  discount: '',
                }
              }
            })
            this.dialogVisible = false;
          } else {
            console.log('表单验证失败');
            return false;
          }
        });
      }
    },
    async fetchFeeRate() {
      let data = {
        pageNum: this.ratepageNum,
        pageSize: this.ratepageSize
      }
      const res = await getFeeConfig(data)
      if (res.status.code == 0) {
        this.rateTypeData = res.result.list
        this.rateTypetotal = res.result.totalCount
      }

    },
    removeMilliseconds(dateTimeString) {
      return dateTimeString.split('.')[0];
    },
    switchtactics(e) {
      console.log(e, '开关');
      this.trmakeopen(e)
      // this.TrcenterDialogVisible = true

      // if (e) {
      //   this.Tacticsopen = true
      // } else {
      //   this.Tacticsopen = false
      // }
    },
    cancelbit() {
      this.centerDialogVisible = false;
      this.fetchBITopen()
    },
    trcancelbit() {
      this.TrcenterDialogVisible = false
      this.fetchTacticsopen()
    },
    async fetchBITopen() {
      // await this.fetchcoin()

      let data = {
        type: 'importantType',
        contractName: 'importantType',
        // contractName: this.query.contractName,
        type: this.query.contractName,
        key: 'exchangeOpenStatus'
      }
      let res = await getBitConfig(data);
      if (res.result.pageData.list[0].value == 'OPEN') {
        this.bitopen = true
      } else {
        this.bitopen = false
      }
    },
    async fetchTacticsopen() {
      // await this.fetchcoin()

      let data = {
        contractName: 'strategyIsOpen',
        type: 'strategyIsOpen',
        // type:
        // contractName: this.query.contractName,
        key: 'open'
      }
      let res = await getBitConfig(data);
      if (res.result.pageData.list[0].value == '0') {
        this.Tacticsopen = true
      } else {
        this.Tacticsopen = false
      }
    },

    trmakeopen(e) {
      let data = {
        type: 'strategyIsOpen',
        key: 'open',
        remark: '策略开关 0 开；1 关',
        value: this.strategy,
        contractName: this.query.contractName

      }
      addBitConfig(data).then(res => {
        if (res.status.code == 0) {
          this.$message.success(res.status.msg);
          this.TrcenterDialogVisible = false
          this.fetchTacticsopen()
        } else {
          this.Tacticsopen = !this.Tacticsopen
        }
      })
    },

    makeopen() {
      let data = {
        key: 'exchangeOpenStatus',
        type: 'importantType',
        remark: '比特指数开启状态',
        value: this.bitopenkw
      }
      addBitConfig(data).then(res => {
        if (res.status.code == 0) {
          this.$message.success(res.status.msg);
          this.centerDialogVisible = false
          this.fetchBITopen()
        } else {

        }
      })
    },
    switchchange(e) {
      console.log(e, '开关');
      this.centerDialogVisible = true

      if (e) {
        this.isopen = true
      } else {
        this.isopen = false
      }
    },
    onQueryReset() {
      this.query = {}
      // this.query = {
      //   userType: '1'
      // }
      // this.query1 = {
      //   userType: '1',
      // }
      this.fetchBitConfig({});

    },
    onQueryChange(data) {
      // this.query1 = data
      this.fetchBitConfig(data)
    },

    //HANDICAP_LAYS("handicapLays", 0, "盘口铺单"),

    // FAR_END_LAYS("farEndLays", 1, "远端铺单"),

    // SELF_TRADE("selfTrade", 2, "自成交"),

    // FUNDING_RATE("fundingRate", 3, "资金费率"),

    // OTHER_TYPE("otherType", 4, "其他类型"),
    async fetchhandicapLays() {
      let data = {
        type: 'handicapLays',
        pageNum: this.handicapLayspage,
        pageSize: this.handicapLayspageSize,
        // ...(e.key && { key: e.key }),
        // ...(e.remark && { remark: e.remark })
      }
      let res = await getBitConfig(data);
      this.handicapLaytotal = res.result.pageData.totalCount
      this.handicapLaysData = res.result.pageData.list;
    },
    async fetchFarEndLays(e) {
      let data = {
        type: 'farEndLays',
        pageNum: this.farEndLayspage,
        pageSize: this.farEndLayspageSize,
        ...(e.key && { key: e.key }),
        ...(e.remark && { remark: e.remark })
      };
      let res = await getBitConfig(data);
      this.farEndLaytotal = res.result.pageData.totalCount
      this.farEndLaysData = res.result.pageData.list;
    },

    async fetchSelfTrade(e) {
      let data = {
        type: 'selfTrade',
        pageNum: this.selfTradepage,
        pageSize: this.selfTradepageSize,
        ...(e.key && { key: e.key }),
        ...(e.remark && { remark: e.remark })
      };
      let res = await getBitConfig(data);
      this.selfTradetotal = res.result.pageData.totalCount
      this.selfTradeData = res.result.pageData.list;
    },

    async fetchFundingRate(e) {
      let data = {
        type: 'fundingRate',
        pageNum: this.fundingRatepage,
        pageSize: this.fundingRatepageSize,
        ...(e.key && { key: e.key }),
        ...(e.remark && { remark: e.remark })
      };
      let res = await getBitConfig(data);
      this.fundingRatetotal = res.result.pageData.totalCount

      this.fundingRateData = res.result.pageData.list;
    },

    async fetchOtherType() {
      console.log('进来了12123');
      let data = {
        type: 'otherType',
        pageNum: this.otherTypepage,
        pageSize: this.otherTypepageSize,
        // ...(e.key && { key: e.key }),
        // ...(e.remark && { remark: e.remark })
      };
      let res = await getBitConfig(data);
      this.otherTypetotal = res.result.pageData.totalCount
      this.otherTypeData = res.result.pageData.list;
    },
    async fetchBitConfig(args) {
      // await this.fetchcoin()
      this.loading = true
      let types = ['handicapLays', 'farEndLays', 'selfTrade', 'fundingRate', 'otherType'];

      let promises = types.map(item => {
        let data = {
          type: args.type || item,
          contractName: this.query.contractName,
          pageNum: this.page,
          pageSize: this.pageSize,
          ...(args.key && { key: args.key }),
          ...(args.remark && { remark: args.remark })
        };
        return getBitConfig(data);
      });

      try {
        this.loading = false

        let results = await Promise.all(promises);
        results.forEach(item => {
          const type = item.result.type;
          const pageData = item.result.pageData.list;
          const pageCount = item.result.pageData.totalCount;
          switch (type) {
            case 'handicapLays':
              this.handicapLaysData = pageData;
              this.handicapLaytotal = pageCount
              break;
            case 'farEndLays':
              this.farEndLaysData = pageData;
              this.farEndLaytotal = pageCount
              break;
            case 'selfTrade':
              this.selfTradeData = pageData;
              this.selfTradetotal = pageCount
              break;
            case 'fundingRate':
              this.fundingRateData = pageData;
              this.fundingRatetotal = pageCount
              break;
            case 'otherType':
              this.otherTypeData = pageData;
              this.otherTypetotal = pageCount
              break;
            default:
              console.warn(`Unknown type: ${type}`);
          }
        });
        // 处理结果
        console.log(results);
      } catch (error) {
        // 处理错误
        this.loading = false
        console.error("An error occurred:", error);
      }
    },
    // 打开修改弹窗
    openEditDialog(row) {
      this.editForm = { ...row };
      this.editDialogVisible = true;
    },

    // 打开增加弹窗
    openAddDialog() {
      this.addDialogVisible = true;
    },

    // 提交修改
    submitEdit() {
      console.log("修改的数据", this.editForm);
      // this.editDialogVisible = false;

      this.$refs.editFormRef.validate((valid) => {
        if (valid) {
          // 提交表单逻辑
          console.log("修改的数据", this.editForm);
          this.editForm.contractName = this.query.contractName
          addBitConfig(this.editForm).then(res => {
            if (res.status.code == 0) {
              this.editDialogVisible = false;
              // this.addForm = {}
              this.$message.success(res.status.msg);
              this.fetchBitConfig({});
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      })
    },

    // 提交增加
    submitAdd() {
      this.$refs.addFormRef.validate((valid) => {
        if (valid) {
          // 提交表单逻辑
          console.log("增加的数据", this.addForm);
          this.addForm.contractName = this.query.contractName
          addBitConfig(this.addForm).then(res => {
            if (res.status.code == 0) {
              this.addDialogVisible = false;
              this.addForm = {}
              this.$message.success(res.status.msg);
              this.fetchBitConfig();
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      })




    },
    // 分页处理
    onehandlePageChange(value) {
      this.handicapLayspage = value
      this.fetchhandicapLays({})
      // this.tables[tableIndex].currentPage = newPage;
      // 在这里加载新的表格数据
    },
    tohandlePageChange(value) {
      this.farEndLayspage = value
      this.fetchfarEndLays({})
    },
    threehandlePageChange(value) {
      this.selfTradepage = value
      this.fetchselfTrade({})
    },
    fourhandlePageChange(value) {
      this.fundingRatepage = value
      this.fetchfundingRate({})
    },
    fivehandlePageChange(value) {
      this.otherTypepage = value
      this.fetchOtherType({})
    },
    sixhandlePageChange(value) {
      this.ratepageNum = value
      this.fetchFeeRate({})
    }

  },
  computed: {
    querySchema() {
      return [ // 搜索组件架构
        {
          type: 'select',
          label: '币对：',
          field: 'contractName',
          placeholder: '请选择币对',
          options: this.coinlist
        },
        {
          type: 'input',
          label: 'key：',
          placeholder: '请输入key',
          field: 'key'
        },
        {
          type: 'input',
          label: '备注：',
          placeholder: '请输入备注',
          field: 'remark'
        },
        {
          type: 'select',
          label: '类型：',
          field: 'type',
          placeholder: '请选择类型',
          options: [
            { label: '盘口铺单', value: 'handicapLays' },
            { label: '远端铺单', value: 'farEndLays' },
            { label: '自成交', value: 'selfTrade' },
            { label: '资金费率', value: 'fundingRate' },
            { label: '其他类型', value: 'otherType' }
          ]
        },

      ]
    }
  },

};
</script>

<style scoped>
/* 样式可根据需求进行调整 */
.el-table th {
  height: 60px !important;
  /* 你想要的表头高度 */
  line-height: 60px !important;
  /* 确保文字垂直居中 */
}

::v-deep .el-select-dropdown {
  position: absolute !important;
  top: 50px !important;
  left: 0px !important;
  left: 0px !important;
}

/* ::v-deep .el-select .el-select-dropdown {
  transform: translateX(-5px);
} */
</style>