<template>
    <d2-container class="page">
        <!-- @onExport="onExport" :showExport="true" -->
        <!-- <span style="font-size: 14px;
    color: #606266;">选择币种：</span>
        <el-select v-model="query.contractName" placeholder="请选择币种" clearable style="width: 150px;">
            <el-option v-for="item in coinlist" :key="item.id" :label="item.contractName" :value="item.contractName">
            </el-option>
        </el-select> -->

        <common-query :query-schema="querySchema" @onSubmit="onQueryChange" ref="query" :data="query"
            @onReset="onQueryReset"></common-query>
        <!-- <template >
          <div  style="display:flex;justify-content: space-between;align-items: center;width:320px;">
  
            <el-input v-model="value"   placeholder="请输入最低价"></el-input>
            <div style="margin-left:10px;">至</div>
            <el-input v-model="value" :placeholder="`请输入最高价`"></el-input>
          </div>
        </template> -->
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'
            style="margin-top: 20px;">

        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination @current-change="currentChange" layout="total, sizes, prev, pager, next, jumper"
                :total="page.totalCount" :page-sizes="[20, 50, 100, 200, 500, 1000]" :current-page="page.pageNum"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>

        <!-- <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
          <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
           :page-size="page.pageSize" :current-page="page.totalCount" :page-sizes="[20, 50, 100, 200,500,1000]"
              style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="currentChange">
          </el-pagination>
      </div> -->
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import { downloadBlob } from '@/utils/helper'
import { getBitRisk, getSymbolList } from "@/api/bitconfig"
export default {
    name: "riskBoard",

    components: {
        CommonQuery,
        CommonTable
    },
    props: {},
    data() {

        return {
            value: 1,
            listLoading: true, coinlist: [],
            tableData: [],
            query: {
                userType: '1',
                profitStart: '',
                profitEnd: '',
                contractName: ''
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            // page: {
            //   totalCount: 0,
            //   pageSize: 10
            // }, // 分页数据
            // querySchema:,
            tableSchema: [ // 表格架构
                {
                    label: '用户昵称',
                    field: 'nickName',
                    width: '170px'
                },
                {
                    label: '用户地址',
                    field: 'conAdd',
                    width: '170px'
                },
                {
                    label: '收益(仅看平仓)',
                    field: 'profitWithoutHold',
                    width: '170px'
                },
                {
                    label: '收益(包括浮盈)',
                    field: 'profitWithHold',
                    width: '200px'
                },
                {
                    label: '用户实名',
                    field: 'realName',
                    width: '200px'
                },


                {
                    label: '用户身份证号',
                    field: 'idCardNo',
                    width: '150px'
                },

                {
                    label: '开仓笔数',
                    field: 'openCount',
                    width: '150px'
                },
                {
                    label: '平均每笔开仓收益',
                    field: 'avgProfit',
                    width: '200px'
                },
                {
                    label: '区间内单笔最大收益',
                    field: 'maxProfit',
                    width: '200px'
                },

                {
                    label: '历史以来总收益',
                    field: 'historyTotalProfit',
                    width: '200px'
                },
                {
                    label: '历史以来总手续费',
                    field: 'historyTotalFee',
                    width: '200px'
                },
                // {
                // 	label: '操作',
                // 	slot: 'action',

                // 	width: '140px',
                // 	fixed:'right'
                // }
            ],
            tableData: [{}],

            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
                userType: '1',
                profitStart: '',
                profitEnd: ''
            }
        }
    },
    watch: {
        "query.contractName"(newValue, oldValue) {
            this.getList()
        },
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'select',
                    label: '币对：',
                    field: 'contractName',
                    placeholder: '请选择币对',
                    options: this.coinlist
                },
                {
                    type: 'datetimerange',
                    label: '时间：',
                    placeholder: '请输入时间',
                    field: 'startTime',
                    field2: 'endTime',
                },
                {
                    type: 'input',
                    label: '收益',
                    placeholder: '',
                    field: 'profitStart'
                },
                {
                    type: 'input',
                    label: '-',
                    placeholder: '',
                    field: 'profitEnd'
                },
                {
                    type: 'input',
                    label: '用户昵称：',
                    placeholder: '请输入用户昵称',
                    field: 'nickName'
                },
                {
                    type: 'input',
                    label: '用户地址：',
                    placeholder: '用户地址',
                    field: 'conAdd'
                },


                {
                    type: 'select',
                    label: '用户类型：',
                    field: 'userType',
                    placeholder: '请选择用户类型',
                    options: [{
                        label: '主力',
                        value: "4"
                    },
                    {
                        label: '大户',
                        value: '1'
                    },
                    {
                        label: '中户',
                        value: '2'
                    },
                    {
                        label: '小户',
                        value: '3'
                    }
                    ]
                },
            ]
        }
    },
    async mounted() {
        await this.fetchcoin()
        this.getList()
    },
    methods: {
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                // this.coinlist = res.result
                this.coinlist = res.result.map(item => ({
                    label: item.contractName,
                    value: item.contractName,
                }));
                this.coinlist.push({
                    label: 'E-BIT-USDT',
                    value: 'E-BIT-USDT'
                })
                this.query.contractName = res.result[0].contractName
            }
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        currentChangeSize(value) {
            this.page.pageSize = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {
            this.query = {
                userType: '1'
            }
            this.query1 = {
                userType: '1',
            }
            this.listLoading = true
            this.getList()
        },
        onQueryChange(data) {
            this.listLoading = true
            this.query1 = data
            this.getList(true)
        },
        async onExport(data) {
            console.log(1111)
            this.query1 = data
            console.log(this.query1)
            const res = await this.$api.exportBattleProfit({ ...this.query1 })
            if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then(buffer => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '对战收益')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            //   ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            const params = {
                ...this.query1,
                contractName: this.query.contractName,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
                // offsets: 1
            }
            console.log('列表数据', params)
            const {
                status,
                result
            } = await getBitRisk(params)
            // } = await this.$api.battleProfitList(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                let dataList = []
                this.tableData = []
                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                console.log("this.tableData", this.tableData)
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize

            }
        },
    }
}
</script>

<style></style>