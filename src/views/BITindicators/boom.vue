<template>
    <d2-container class="page">
        <!-- <span style="font-size: 14px;
    color: #606266;">选择币种：</span>
        <el-select v-model="contractName" placeholder="请选择币种" clearable style="width: 150px;">
            <el-option v-for="item in coinlist" :key="item.id" :label="item.contractName" :value="item.contractName">
            </el-option>
        </el-select> -->
        <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange"
            ref="query" :data="query" @onReset="onQueryReset"></common-query>
        <!--  -->
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #action="scope">
                <div class="bg">
                    <span v-if="scope.row.income != null && isCheck">{{ scope.row.income }}</span>
                    <el-button v-else type="text" @click="checkIncome(scope.row)">查看</el-button>
                </div>

            </template>
            <template #toggle="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.createAt) }}</span>
            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>
        <!-- <div
            style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="xuanzeSize">
            </el-pagination>
        </div> -->

    </d2-container>
</template>

<script>
import { getBitBoom, exportBitBoom, getSymbolList } from "@/api/bitconfig"
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "BITboom",
    components: {
        CommonQuery,
        CommonTable
    },
    props: {},
    data() {
        return {
            coinlist: [],
            contractName: '',
            listLoading: true,
            tableData: [],
            query: {
                contractName: ''
                // userType: '1',
                // orderType: '4'
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            page: {
                pageSize: 10
            }, // 分页数据
            // querySchema: ,
            tableSchema: [ // 表格架构
                {
                    label: '爆仓方id',
                    field: 'fromPositionId',
                    width: '170px'
                },
                {
                    label: '爆仓方地址',
                    field: 'fromConAdd',
                    width: '170px'
                },
                {
                    label: '爆仓方昵称',
                    field: 'fromNickName',
                    width: '200px'
                },
                {
                    label: '爆仓数量',
                    field: 'dealVolume',
                    width: '200px'
                },
                {
                    label: '爆仓价格',
                    field: 'reducePrice',
                    width: '200px'
                },
                {
                    label: '爆仓时间',
                    slot: 'toggle',
                    // field: removeMilliseconds(createAt),
                    width: '200px'
                },
                {
                    label: '止盈方id',
                    field: 'toPositionId',
                    width: '200px'
                },
                {
                    label: '止盈方地址',
                    field: 'toConAdd',
                    width: '200px'
                },
                {
                    label: '止盈方昵称',
                    field: 'toNickName',
                    width: '200px'
                },
            ],

            tableData: [{}],

            page: {
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            isCheck: false,
            query1: {
                userType: '1',
                orderType: '4'
            }
        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'select',
                    label: '币对：',
                    field: 'contractName',
                    placeholder: '请选择币对',
                    options: this.coinlist
                },
                {
                    type: 'datetimerange',
                    label: '爆仓时间：',
                    placeholder: '请输入爆仓时间',
                    field: 'startTime',
                    field2: 'endTime',
                },
                {
                    type: 'input',
                    label: '用户爆仓方id：',
                    placeholder: '请输入爆仓方id',
                    field: 'fromPositionId'
                },
                {
                    type: 'input',
                    label: '用户止盈方id：',
                    placeholder: '请输入止盈方id',
                    field: 'toPositionId'
                },
            ]
        }
    },
    async mounted() {
        await this.fetchcoin()
        this.getList()
    },
    watch: {
        "query.contractName"(newValue, oldValue) {
            this.getList()
        },
    },
    methods: {
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                // this.coinlist = res.result
                this.coinlist = res.result.map(item => ({
                    label: item.contractName,
                    value: item.contractName,
                }));
                this.coinlist.push({
                    label: 'E-BIT-USDT',
                    value: 'E-BIT-USDT'
                })
                this.query.contractName = res.result[0].contractName
                console.log(this.query.contractName, 123);

            }

        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async checkIncome(row) {
            console.log(row)
            this.isCheck = true
            // this.getList(row.ctid)
            // const res = await this.$api.qifeiHoldHistory({
            //    orderType:1,
            //   ctid: row.ctid
            // })

        },
        onQueryReset() {
            this.query = {
                userType: '1',
                orderType: '4'
            }
            this.query1 = {
                userType: '1',
                orderType: '4',
            }
            this.listLoading = true
            this.getList()
        },
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        //分页数量切换
        xuanzeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            const params = {
                contractName: this.query.contractName,
                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            delete params.totalCount
            delete params.pageCount

            const {
                status,
                result
            } = await getBitBoom(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []

                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        async houseExport(e) {
            const res = await exportBitBoom({
                ...e,
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '爆仓记录' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
    }
}
</script>

<style></style>