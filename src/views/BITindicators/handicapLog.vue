<template>
  <d2-container class="page">
    <!-- :showExport="true" @onExport="houseExport" -->
    <!-- <common-query  :query-schema="querySchema" @onSubmit="onQueryChange"
      ref="query" :data="query" @onReset="onQueryReset"></common-query> -->
    <span style="font-size: 14px;
    color: #606266;">选择币种：</span>
    <el-select v-model="query.contractName" placeholder="请选择币种" clearable style="width: 150px;">
      <el-option v-for="item in coinlist" :key="item.id" :label="item.contractName" :value="item.contractName">
      </el-option>
    </el-select>

    <el-date-picker style="margin: 0 20px 20px 20px;" clearable format="yyyy-MM-dd HH:mm:ss" v-model="querytime"
      type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
      end-placeholder="结束日期">
    </el-date-picker>
    <el-button type="primary" @click="reset" style="margin-bottom: 20px;">查询</el-button>

    <el-button type="primary" @click="getList()" style="margin-bottom: 20px;">刷新</el-button>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
      <template #buy5="scope">
        <div v-if="scope.row.orderBookJson.BUY && scope.row.orderBookJson.BUY.length >= 5">
          <div>档位金额：{{ scope.row.orderBookJson.BUY[4].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.BUY[4].mainAmount) +
            Number(scope.row.orderBookJson.BUY[4].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.BUY[4].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.BUY[4].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>
      <template #buy4="scope">
        <div v-if="scope.row.orderBookJson.BUY && scope.row.orderBookJson.BUY.length >= 4">
          <div>档位金额：{{ scope.row.orderBookJson.BUY[3].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.BUY[3].mainAmount) +
            Number(scope.row.orderBookJson.BUY[3].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.BUY[3].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.BUY[3].amount) || 0 }}</div>

        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>

      <template #buy3="scope">
        <div v-if="scope.row.orderBookJson.BUY && scope.row.orderBookJson.BUY.length >= 3">
          <div>档位金额：{{ scope.row.orderBookJson.BUY[2].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.BUY[2].mainAmount) +
            Number(scope.row.orderBookJson.BUY[2].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.BUY[2].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.BUY[2].amount) || 0 }}</div>

        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>
      <template #buy2="scope">
        <div v-if="scope.row.orderBookJson.BUY && scope.row.orderBookJson.BUY.length >= 2">
          <div>档位金额：{{ scope.row.orderBookJson.BUY[1].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.BUY[1].mainAmount) +
            Number(scope.row.orderBookJson.BUY[1].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.BUY[1].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.BUY[1].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>
      <template #buy1="scope">
        <div v-if="scope.row.orderBookJson.BUY && scope.row.orderBookJson.BUY.length >= 1">

          <div>档位金额：{{ scope.row.orderBookJson.BUY[0].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.BUY[0].mainAmount) +
            Number(scope.row.orderBookJson.BUY[0].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.BUY[0].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.BUY[0].amount) || 0 }}</div>

        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>

      <!-- <template #showPrice="scope">
        <div style="color: red;">
          ￥{{ scope.row.handicapDetailVOList[5].price }}
        </div>
        <div style="color: red;">买:{{ scope.row.handicapDetailVOList[5].totalNum }}</div>
        <div style="color: red;">卖:{{ scope.row.handicapDetailVOList[5].mainNum }}</div>
      </template> -->

      <template #sell1="scope">
        <div v-if="scope.row.orderBookJson.SELL && scope.row.orderBookJson.SELL.length >= 1">
          <div>档位金额：{{ scope.row.orderBookJson.SELL[0].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.SELL[0].mainAmount) +
            Number(scope.row.orderBookJson.SELL[0].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.SELL[0].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.SELL[0].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>
      <template #sell2="scope">
        <div v-if="scope.row.orderBookJson.SELL && scope.row.orderBookJson.SELL.length >= 2">
          <div>档位金额：{{ scope.row.orderBookJson.SELL[1].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.SELL[1].mainAmount) +
            Number(scope.row.orderBookJson.SELL[1].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.SELL[1].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.SELL[1].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>

      <template #sell3="scope">
        <div v-if="scope.row.orderBookJson.SELL && scope.row.orderBookJson.SELL.length >= 3">
          <div>档位金额：{{ scope.row.orderBookJson.SELL[2].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.SELL[2].mainAmount) +
            Number(scope.row.orderBookJson.SELL[2].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.SELL[2].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.SELL[2].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>

      <template #sell4="scope">
        <div v-if="scope.row.orderBookJson.SELL && scope.row.orderBookJson.SELL.length >= 4">
          <div>档位金额：{{ scope.row.orderBookJson.SELL[3].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.SELL[3].mainAmount) +
            Number(scope.row.orderBookJson.SELL[3].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.SELL[3].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.SELL[3].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>

      <template #sell5="scope">
        <div v-if="scope.row.orderBookJson.SELL && scope.row.orderBookJson.SELL.length >= 5">
          <div>档位金额：{{ scope.row.orderBookJson.SELL[4].price || 0 }}</div>
          <div>数量: {{ Number(scope.row.orderBookJson.SELL[4].mainAmount) +
            Number(scope.row.orderBookJson.SELL[4].amount) }}</div>
          <div>主力: {{ Math.ceil(Number(scope.row.orderBookJson.SELL[4].mainAmount)) || 0 }}</div>
          <div>小户: {{ Math.ceil(scope.row.orderBookJson.SELL[4].amount) || 0 }}</div>
        </div>
        <div v-else>
          <div>档位金额：0</div>
          <div>数量:0</div>
          <div>主力:0</div>
          <div>小户:0</div>
        </div>
      </template>
    </common-table>
    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import { getBitLog, getSymbolList } from "@/api/bitconfig"
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: "BIThandicapLog",
  components: {
    CommonQuery,
    CommonTable
  },
  props: {},
  data() {
    return {
      coinlist: [],
      querytime: [

      ],
      listLoading: true,
      tableData: [],
      query: {
        contractName: '',

        // userType: '1',
        // startTime: ['', ''],
        // endTime: ''
      },
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      page: {
        totalCount: 0,
        pageSize: 10
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'datetimerange',
          label: '时间：',
          placeholder: '请输入下单时间',
          field: 'startTime',
          field2: 'endTime',
        },
      ],
      tableSchema: [ // 表格架构
        {
          label: '创建时间',
          field: 'ctime',
          width: '170px',
          height: '300px'
        },
        {
          label: '成交时间',
          field: 'mtime',
          width: '170px',
          height: '300px'
        },
        {
          label: '买5',
          slot: 'buy5',
          width: '150px'
        },
        {
          label: '买4',
          slot: 'buy4',
          width: '150px'
        },
        {
          label: '买3',
          slot: 'buy3',
          width: '150px'
        },
        {
          label: '买2',
          slot: 'buy2',
          width: '150px'
        },
        {
          label: '买1',
          slot: 'buy1',
          width: '150px'
        },
        {
          label: 'mid',
          slot: ' ',
          width: '200px'
        },
        {
          label: '卖1',
          slot: 'sell1',
          width: '150px'
        },
        {
          label: '卖2',
          slot: 'sell2',
          width: '150px'
        },
        {
          label: '卖3',
          slot: 'sell3',
          width: '150px'
        },
        {
          label: '卖4',
          slot: 'sell4',
          width: '150px'
        },
        {
          label: '卖5',
          slot: 'sell5',
          width: '150px'
        },



      ],
      // tableData: [{}],

      page: {
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query1: {
        userType: '1',
        startTime: '',
        endTime: '',
      }
    }
  },
  created() {
    // const todayMidnight = this.getTodayMidnight();
    // const yesterdayMidnight = this.getYesterdayMidnight();

    // this.querytime[0] = this.formatDate(yesterdayMidnight) + '.000';
    // this.querytime[1] = this.formatDate(todayMidnight) + '.000';
  },
  async mounted() {

    // const { start, end, start2, end2 } = this.getTodayRange();
    // console.log('Today start time:', start2); // 输出今天的开始时间
    // console.log('Today end time:', end2);     // 输出今天的结束时间

    // this.query1.startTime = start
    // this.query1.endTime = end

    // // this.query.startTime[0]=start2
    // // this.query.startTime[1]=end2

    // this.query.startTime = [start2, end2],
    //   console.log("this.query.startTime", this.query.startTime)
    await this.fetchcoin()

    this.getList()
  },
  watch: {
    "query.contractName"(newValue, oldValue) {
      this.getList()
    },
  },
  methods: {
    async fetchcoin() {
      const res = await getSymbolList({})
      if (res.status.code == 0) {
        this.coinlist = res.result
        this.coinlist.push({
          contractName: 'E-BIT-USDT',
        })
        this.query.contractName = res.result[0].contractName
        console.log(this.query.contractName, 123);

      }

    },
    refreshed() {
      this.getList()

    },
    async houseExport(e) {
      // ExportOpenRecords
      // ExportMarketRecords
      const res = await this.$api.ExportMarketRecords({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '盘口记录' + Date.now() + '.xlsx')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    //获取当天时间
    getTodayRange() {
      const now = new Date();
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);

      // 格式化日期时间为指定格式
      const startDateStr = this.formatDate(startOfToday, 'yyyy-MM-dd HH:mm:ss.SSS');
      const endDateStr = this.formatDate(endOfToday, 'yyyy-MM-dd HH:mm:ss.SSS');

      const startDateStr2 = this.formatDate(startOfToday, 'yyyy-MM-dd HH:mm:ss');
      const endDateStr2 = this.formatDate(endOfToday, 'yyyy-MM-dd HH:mm:ss');

      return {
        start: startDateStr,
        end: endDateStr,
        start2: startDateStr2,
        end2: endDateStr2
      };
    },
    formatDate(date, format) {
      const padZero = (num) => num.toString().padStart(2, '0');

      return format.replace('yyyy', date.getFullYear())
        .replace('MM', padZero(date.getMonth() + 1)) // 注意月份是从0开始的
        .replace('dd', padZero(date.getDate()))
        .replace('HH', padZero(date.getHours()))
        .replace('mm', padZero(date.getMinutes()))
        .replace('ss', padZero(date.getSeconds()))
        .replace('SSS', padZero(date.getMilliseconds()));
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.listLoading = true
      this.getList()
    },
    reset() {
      // const todayMidnight = this.getTodayMidnight();
      // const yesterdayMidnight = this.getYesterdayMidnight();

      // this.querytime[0] = this.formatDate(yesterdayMidnight) + '.000';
      // this.querytime[1] = this.formatDate(todayMidnight) + '.000';
      this.getList()
    },
    onQueryReset() {
      this.query = {
        userType: '1',
        startTime: ['', ''],
        endTime: ''
      },
        this.query1 = {
          userType: '1',
          startTime: '',
          endTime: ''
        }
      const { start, end, start2, end2 } = this.getTodayRange();
      this.query1.startTime = start
      this.query1.endTime = end

      this.query.startTime = [start2, end2],

        this.listLoading = true
      this.getList()
      this.query = {
        userType: '1',
        startTime: ['', ''],
        endTime: ''
      }
    },
    onQueryChange(data) {
      this.listLoading = true
      console.log("data-----------------------------", data)
      // this.query = data
      this.query1 = data
      this.getList(true)
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getTodayMidnight() {
      const now = new Date();
      // 设置日期为明天
      now.setDate(now.getDate() + 1);
      // 重置小时、分钟、秒、毫秒为 0
      now.setHours(0, 0, 0, 0);
      return now;
    },

    // 获取昨天 0 点时间
    getYesterdayMidnight() {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate()); // 减去一天
      yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
      return yesterday;
    },
    // 获取列表
    async getList(isInit) {

      // let ctid;
      // if (this.query.ctid) {
      // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
      // }
      if (this.querytime) {
        var atime = this.querytime[0]
        var btime = this.querytime[1]

      }
      const params = {
        // ...this.querytime,
        contractName: this.query.contractName,
        createStart: atime,
        createEnd: btime,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        // offsets: 2
      }
      console.log('列表数据', params)
      const {
        status,
        result
      } = await getBitLog(params)
      console.log('获取列表数据', result)
      if (status.code === 0) {
        this.listLoading = false
        let dataList = []
        this.tableData = []
        const data = result.list
        let obj = {

        }
        data.forEach((item) => {
          let fixedA = item.orderBookJson.replace(/(\d+\.\d+)/g, '"$1"');
          item.orderBookJson = (JSON.parse(fixedA))
          // 创建一个 Date 对象
          let date = new Date(item.ctime);
          // 转换为本地时间格式
          item.ctime = date.toLocaleString();
          let date2 = new Date(item.mtime);
          // 转换为本地时间格式
          item.mtime = date2.toLocaleString();
          dataList.push(item)
        })
        this.tableData = dataList
        // obj.orderBookJson = JSON.parse(item.orderBookJson);
        //   obj.mtime = item.mtime
        //   obj.ctime = item.ctime
        //   obj.id = item.id     
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
        console.log("this.tableData", this.tableData)

        // this.page.pageNum = result.pageCount
        // this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize
      }
    },
  }
}
</script>

<style></style>
