<template>
    <d2-container class="page">
        <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
            <template #titleOne="scope">
                <h3>1. 新用户注册且实名</h3>
            </template>

            <!-- supportLever -->
            <template #supportLever="scope">
                <el-checkbox-group v-model="formData.supportLever">
                    <el-checkbox v-for="(item, index) in leveroptions" :key="index" :label="item.value"
                        :value="item.value">{{
                            item.label }}</el-checkbox>
                </el-checkbox-group>
            </template>

            <!-- supportContract -->
            <template slot="supportContract">
                <el-checkbox-group v-model="formData.supportContract">
                    <el-checkbox v-for="(item, index) in coinlist" :key="index" :label="item.value"
                        :value="item.value">{{
                            item.label }}</el-checkbox>
                </el-checkbox-group>
            </template>

            <template slot="btnSum">
                <el-button type="primary" @click="GOsubmit">查看统计详情</el-button>
            </template>

            <!-- isopen -->
            <template slot="isopen">
                <el-switch v-model="formData.status">
                </el-switch>
            </template>
        </common-form>

        <common-form :submit="submitTotal" :data="formDataTotal" :schema="formSchemaTotal" label-width="300px">
            <!-- titleTwo -->
            <template #titleTwo="scope">
                <h3>2. 新用户首次累计开仓金额</h3>
            </template>
            <template slot="btnSum">
                <el-button type="primary" @click="GOsubmit">查看统计详情</el-button>
            </template>

            <!-- supportLever -->
            <template #supportLever="scope">
                <el-checkbox-group v-model="formDataTotal.supportLever">
                    <el-checkbox v-for="(item, index) in leveroptions" :key="index" :label="item.value"
                        :value="item.value">{{
                            item.label }}</el-checkbox>
                </el-checkbox-group>
            </template>

            <!-- supportContract -->
            <template slot="supportContract">
                <el-checkbox-group v-model="formDataTotal.supportContract">
                    <el-checkbox v-for="(item, index) in coinlist" :key="index" :label="item.value"
                        :value="item.value">{{
                            item.label }}</el-checkbox>
                </el-checkbox-group>
            </template>

            <!-- isopen -->
            <template slot="isopen">
                <el-switch v-model="formDataTotal.status">
                </el-switch>
            </template>
        </common-form>
    </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import { getSymbolList } from "@/api/bitconfig.js"
import { addOrUpdateAutoSend, queryAutoSend } from "@/api/bit.js"
import {
    mapActions
} from 'vuex'
import {
    downloadBlob
} from '@/utils/helper'

export default {
    name: 'AutoWelfare',
    components: {
        CommonForm,
        CommonTable,
        FileUploader
    },
    data() {
        return {
            coinlist: [],
            leveroptions: [
                { label: '无限制', value: "0" },
                { label: '1X', value: "1" },
                { label: '2X', value: "2" },
                { label: '5X', value: "5" },
                { label: '10X', value: "10" },
                { label: '20X', value: "20" },
                { label: '50X', value: "50" },

            ],
            isDetail: false, // 详情
            formData: {
                type: '1',
                supportLever: ["1", "2", "5"],
                supportContract: [0],
                registerType: '1',
                status: true,
                maxPositionTime: 24,
                expireTime: "",
                startTime: [],
                amount: "",


            },
            formDataTotal: {
                supportLever: ["1", "2", "5"],
                supportContract: [0],
                status: true,
                maxPositionTime: 24,
                expireTime: "",
                totalOpenAmount: "",
                amount: "",
                startTime: []
            },
            formSchemaTotal: [
                {
                    slot: 'titleTwo'
                },
                {
                    type: 'input',
                    label: '达到累计开仓金额：',
                    placeholder: '请输入达到累计开仓金额：',
                    field: 'totalOpenAmount',
                    rules: [{
                        required: true,
                        message: '请输入达到累计开仓金额：',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '发放面值：',
                    placeholder: '请输入发放面值',
                    field: 'amount',
                    rules: [{
                        required: true,
                        message: '请输入发放万能金面值',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'datetimerange',
                    label: '活动时间：',
                    placeholder: '请输入活动时间',
                    field: 'startTime',
                    field2: 'endTime',
                    rules: [{
                        required: true,
                        message: '请输入活动时间',
                        trigger: 'blur'
                    },]
                },
                {
                    label: '支持币对（可多选）:',
                    slot: 'supportContract',
                },
                {
                    type: 'radio',
                    label: '支持杠杆（可多选）:',
                    slot: 'supportLever',
                    // multi: true,
                    options: [
                        { label: '无限制', value: "0" },
                        { label: '1X', value: "1" },
                        { label: '2X', value: "2" },
                        { label: '5X', value: "5" },
                        { label: '10X', value: "10" },
                        { label: '20X', value: "20" },
                        { label: '50X', value: "50" },

                    ],
                    rules: [
                        {
                            required: true,
                            message: '请选择杠杆',
                            trigger: 'change',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '最大持仓时长（单位：小时）：',
                    placeholder: '请输入最大持仓时长',
                    field: 'maxPositionTime',
                    rules: [{
                        required: true,
                        message: '请输入最大持仓时长',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'datetime',
                    label: '有效期至：',
                    field: 'expireTime',
                    rules: [{
                        required: true,
                        message: '请选择有效期',
                        trigger: 'change'
                    },]
                },
                {
                    slot: 'isopen',
                    label: '是否开启：',
                },
                {
                    slot: 'btnSum'
                },
                {
                    type: 'action'
                    // exclude: ['reset', 'submit', 'back']
                }
            ],
            formSchema: [
                {
                    slot: 'titleOne'
                },
                {
                    type: 'radio',
                    label: '注册方式：',
                    field: 'registerType',
                    options: [
                        {
                            label: '所有实名',
                            value: '1'
                        },
                        {
                            label: '仅通过邀请码注册',
                            value: '2'
                        }
                    ],
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'input',
                    label: '发放面值：',
                    placeholder: '请输入发放面值',
                    field: 'amount',
                    rules: [{
                        required: true,
                        message: '请输入发放万能金面值',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'datetimerange',
                    label: '活动时间：',
                    placeholder: '请输入活动时间',
                    field: 'startTime',
                    field2: 'endTime',
                    rules: [{
                        required: true,
                        message: '请输入活动时间',
                        trigger: 'blur'
                    },]
                },
                {
                    label: '支持币对（可多选）:',
                    slot: 'supportContract',
                },
                {
                    type: 'radio',
                    label: '支持杠杆（可多选）:',
                    slot: 'supportLever',
                    // multi: true,
                    options: [
                        { label: '无限制', value: "0" },
                        { label: '1X', value: "1" },
                        { label: '2X', value: "2" },
                        { label: '5X', value: "5" },
                        { label: '10X', value: "10" },
                        { label: '20X', value: "20" },
                        { label: '50X', value: "50" },

                    ],
                    rules: [
                        {
                            required: true,
                            message: '请选择杠杆',
                            trigger: 'change',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '最大持仓时长（单位：小时）：',
                    placeholder: '请输入最大持仓时长',
                    field: 'maxPositionTime',
                    rules: [{
                        required: true,
                        message: '请输入最大持仓时长',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'datetime',
                    label: '有效期至：',
                    field: 'expireTime',
                    rules: [{
                        required: true,
                        message: '请选择有效期',
                        trigger: 'change'
                    },]
                },
                {
                    slot: 'isopen',
                    label: '是否开启：',
                },
                {
                    slot: 'btnSum'
                },
            ],
            loading: false
        }
    },
    mounted() {
        // const {
        //     type,
        //     activityType,
        //     dutyId
        // } = this.$route.query
        this.fetchcoin()
        this.fetchqueryAutoSend()
    },
    methods: {
        GOsubmit() {
            this.$router.push({
                name: 'AutoStatisticsDistributionGold',
            })
        },
        async fetchqueryAutoSend() {
            let res = await queryAutoSend()
            if (res.status.code == 0) {
                if (res.result.registerTrailAutoAddDTO && Object.keys(res.result.registerTrailAutoAddDTO).length > 0) {
                    this.formData.amount = res.result.registerTrailAutoAddDTO.amount
                    this.formData.expireTime = res.result.registerTrailAutoAddDTO.expireTime
                    // leverList中数字转字符串
                    this.formData.supportLever = res.result.registerTrailAutoAddDTO.leverList.map(String)
                    // this.formData.supportLever = res.result.registerTrailAutoAddDTO.leverList
                    this.formData.supportContract = res.result.registerTrailAutoAddDTO.contractList
                    this.formData.startTime = [res.result.registerTrailAutoAddDTO.startTime, res.result.registerTrailAutoAddDTO.endTime]
                    // this.formData.startTime[1] = res.result.registerTrailAutoAddDTO.endTime
                    this.formData.maxPositionTime = res.result.registerTrailAutoAddDTO.maxPositionTime
                    this.formData.registerType = res.result.registerTrailAutoAddDTO.registerType.toString()
                    this.formData.status = res.result.registerTrailAutoAddDTO.status == 1 ? true : false
                    if (res.result.registerTrailAutoAddDTO.id) {
                        this.formData.id = res.result.registerTrailAutoAddDTO.id
                    }
                } else {
                    // const now = new Date();
                    // now.setHours(now.getHours() + 32);
                    // this.formData.expireTime = now.toISOString().slice(0, 19).replace("T", " ");
                }

                if (res.result.totalOpenTrailAutoAddDTO && Object.keys(res.result.totalOpenTrailAutoAddDTO).length > 0) {
                    this.formDataTotal.amount = res.result.totalOpenTrailAutoAddDTO.amount
                    this.formDataTotal.expireTime = res.result.totalOpenTrailAutoAddDTO.expireTime
                    // leverList中数字转字符串
                    this.formDataTotal.supportLever = res.result.totalOpenTrailAutoAddDTO.leverList.map(String)
                    // this.formData.supportLever = res.result.registerTrailAutoAddDTO.leverList
                    this.formDataTotal.supportContract = res.result.totalOpenTrailAutoAddDTO.contractList
                    this.formDataTotal.startTime = [res.result.totalOpenTrailAutoAddDTO.startTime, res.result.totalOpenTrailAutoAddDTO.endTime]
                    // this.formDataTotal.startTime[1] = res.result.totalOpenTrailAutoAddDTO.endTime
                    this.formDataTotal.maxPositionTime = res.result.totalOpenTrailAutoAddDTO.maxPositionTime
                    this.formDataTotal.status = res.result.totalOpenTrailAutoAddDTO.status == 1 ? true : false
                    this.formDataTotal.totalOpenAmount = res.result.totalOpenTrailAutoAddDTO.totalOpenAmount
                    if (res.result.totalOpenTrailAutoAddDTO.id) {
                        this.formDataTotal.id = res.result.totalOpenTrailAutoAddDTO.id
                    }

                } else {
                    // const now = new Date();
                    // now.setHours(now.getHours() + 32);
                    // this.formDataTotal.expireTime = now.toISOString().slice(0, 19).replace("T", " ");
                }
            }
        },

        async submit() {
            this.$confirm('是否确认提交保存？', '确认提交保存', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                this.sub()
            })
        },
        submitTotal() {
            this.$confirm('是否确认提交保存？', '确认提交保存', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                this.sub2()
            })
        },
        async sub2() {
            console.log(this.formData, this.formDataTotal);
            // if(!this.formDataTotal.id && this.formData.id){

            // }
            let res = await addOrUpdateAutoSend({
                registerTrailAutoAddDTOStr: JSON.stringify({
                    status: this.formData.status ? 1 : 0,
                    startTime: this.formData.startTime[0],
                    endTime: this.formData.startTime[1],
                    amount: this.formData.amount,
                    expireTime: this.formData.id ? this.formData.expireTime : this.formData.expireTime + '.000',
                    maxPositionTime: this.formData.maxPositionTime,
                    registerType: this.formData.registerType,
                    supportContract: `${this.formData.supportContract}`,
                    supportLever: `${this.formData.supportLever}`,
                    type: 1,
                    ...(this.formData.id ? { id: this.formData.id } : {})
                }),
                totalOpenTrailAutoAddDTOStr: JSON.stringify({
                    status: this.formDataTotal.status ? 1 : 0,
                    startTime: this.formDataTotal.startTime[0],
                    endTime: this.formDataTotal.startTime[1],
                    amount: this.formDataTotal.amount,
                    expireTime: this.formDataTotal.id ? this.formDataTotal.expireTime : this.formDataTotal.expireTime + '.000',
                    maxPositionTime: this.formDataTotal.maxPositionTime,
                    registerType: this.formDataTotal.registerType,
                    supportContract: `${this.formDataTotal.supportContract}`,
                    supportLever: `${this.formDataTotal.supportLever}`,
                    totalOpenAmount: this.formDataTotal.totalOpenAmount,
                    type: 2,
                    ...(this.formDataTotal.id ? { id: this.formDataTotal.id } : {})
                }),
            })

            if (res.status.code == 0) {
                this.$message.success('保存成功')
                this.fetchqueryAutoSend()
            }
            // registerTrailAutoAddDTO
            // totalOpenTrailAutoAddDTO
        },
        async sub() {
            console.table(this.formData)
            const data = {
                ...this.formData,
                photoShow: JSON.parse(localStorage.getItem('img_result')).mediumImageUrl,
                cover: JSON.parse(localStorage.getItem('img_result')).smallImageUrl,
                abstractDesc: this.formData.title,
                content: this.formData.title,
            }
            console.log(data)
            const {
                status
            } = await this.$api.batchCreate(data)
            if (status.code === 0) {
                this.$message.success('铸造成功')
                this.routerBack()
                // instance.confirmButtonLoading = false;
            } else {
                // this.$message.success(status.msg)
                // instance.confirmButtonLoading = false;
            }
        },
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                this.coinlist = res.result.map(item => ({
                    label: item.name,
                    value: item.contractId,
                }))
                this.coinlist.unshift({
                    label: '无限制',
                    value: 0,
                }, {
                    label: 'BIT指数',
                    value: 1,
                },)
                console.log(this.coinlist, '12312312312');

            }
        },
    }
}
</script>

<style lang="scss" scoped>
.page {
    padding-top: 80px;
}
</style>