<template>
  <d2-container class="page">
    <!-- :showExport="true" @onExport="houseExport"  -->
    <span style="font-size: 14px;
    color: #606266;">开仓时间：</span>
    <el-date-picker style="margin: 0 20px 20px 0;" clearable format="yyyy-MM-dd HH:mm:ss" v-model="querytime"
      type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
      end-placeholder="结束日期">
    </el-date-picker>

    <span style="font-size: 14px;
    color: #606266;">选择币种：</span>
    <el-select v-model="query.contractName" placeholder="请选择币种" clearable style="width: 150px;">
      <el-option v-for="item in coinlist" :key="item.id" :label="item.contractName" :value="item.contractName">
      </el-option>
    </el-select>

    <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange"
      :data="query" @onReset="onQueryReset"></common-query>

    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
      <!-- <template #action-header>
      	<el-button @click="nav_add" type="primary" size="mini">创建任务</el-button>
      </template> -->
      <template #openHouse="scope">
        <!-- <el-button v-if="scope.row.closeType == 0" disabled="true" type="primary">未平仓</el-button>
        <el-button v-if="scope.row.closeType == 1" disabled="true" type="success" size="mini"
          style="color: green;background: #7dff13;">主动平仓</el-button>
        <el-button v-if="scope.row.closeType == 2" disabled="true" type="primary" size="mini"
          style="color: #ffffff;background: #710fe0;">强制平仓</el-button>
        <el-button v-if="scope.row.closeType == 3" disabled="true" type="info" size="mini">止盈止损平仓</el-button> -->
        <!-- INIT(0,"init"),  // 初始订单
    NEW(1,"new"), // 委托单
    FILLED(2,"filled"), // 完全成交
    PART_FILLED(3,"part_filled"), // 部分成交
    CANCELED(4,"canceled"), // 撤销
    PENDING_CANCEL(5,"pending_cancel"), // 待撤销
    EXPIRED(6,"expired"); \\ 异常 -->
        <el-button v-if="scope.row.status == 0" disabled="true" type="primary" size="mini">初始订单</el-button>
        <el-button v-if="scope.row.status == 1" disabled="true" type="success" size="mini">委托单</el-button>
        <el-button v-if="scope.row.status == 2" style="color: green;background: #7dff13;" disabled="true" type="info"
          size="mini">完全成交</el-button>
        <el-button v-if="scope.row.status == 3" disabled="true" type="primary" size="mini">部分成交</el-button>
        <el-button v-if="scope.row.status == 4" disabled="true" type="info" size="mini">撤销</el-button>
        <el-button v-if="scope.row.status == 5" disabled="true" type="info" size="mini">待撤销</el-button>

        <el-button v-if="scope.row.status == 6" disabled="true" type="info" size="mini">异常</el-button>
        <!-- <el-button v-if="scope.row.status == 10" disabled="true" type="primary" size="mini">部分成交</el-button> -->

        <!-- <el-button v-if="scope.row.status == 5 && scope.row.closeType == 2" disabled="true" type="primary" size="mini"
          style="color: #ffffff;background: #710fe0;">已强平</el-button>
        <el-button v-if="scope.row.status == 5 && scope.row.closeType == 1" disabled="true" type="info" size="mini">
          已平仓</el-button>
        <el-button v-if="scope.row.status == 5 && scope.row.closeType == 0" disabled="true" type="info" size="mini">
          未平仓</el-button> -->
      </template>

      <template #lever="scope">
        <el-button v-if="scope.row.lever == 2" disabled="true" size="mini"
          style="color: #fff;background: #ff5470;">2倍</el-button>
        <el-button v-if="scope.row.lever == 5" disabled="true" size="mini"
          style="color: #fff;background: #ff6961;">5倍</el-button>
        <el-button v-if="scope.row.lever == 10" disabled="true" size="mini"
          style="color: #fff;background: #f80000;">10倍</el-button>
        <el-button v-if="scope.row.lever == 1 && scope.row.longShort == 1" disabled="true" size="mini"
          style="color: red;background: #ffd2d0;">1倍</el-button>
        <el-button v-if="scope.row.lever == 1 && scope.row.longShort == 2" disabled="true" size="mini"
          style="color: #fff;background: #2a2a2a;">1倍</el-button>
      </template>

      <template #createAt="scope">
        <span>{{ (scope.row.createAt).split('.')[0] }}</span>
      </template>

      <template #tradeTime="scope">
        <span v-if="scope.row.tradeTime">{{ (scope.row.tradeTime).split('.')[0] }}</span>
      </template>

      <template #revokeTime="scope">
        <span v-if="scope.row.revokeTime">{{ (scope.row.revokeTime).split('.')[0] }}</span>
      </template>

    </common-table>

    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination>

  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import { getBitOpen, exportBitOpen, getSymbolList } from "@/api/bitconfig"
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: "BITopenWareHouseRecond",
  components: {
    CommonQuery,
    CommonTable
  },
  props: {},
  data() {
    return {
      coinlist: [],
      querytime: [],
      listLoading: true,
      tableData: [],
      query: {
        userType: '1',
        contractName: '',
      },
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      page: {
        totalCount: 0,
        pageSize: 10
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '开仓ID：',
          placeholder: '请输入开仓ID',
          field: 'orderId'
        },
        // {
        //   type: 'datetimerange',
        //   label: '下单时间：',
        //   placeholder: '请输入下单时间',
        //   field: 'createStart',
        //   field2: 'createEnd',
        //   initTime: true
        // },
        {
          type: 'input',
          label: '用户con add：',
          placeholder: '请输入用户con add',
          field: 'contractAddress'
        },
        {
          type: 'twoinput',
          label: '开仓价格区间：',
          placeholder: '请输入开仓价格',
          field: ['openPriceStart', 'openPriceEnd']
        },
        // {
        //   type: 'input',
        //   label: '开仓价格结束：',
        //   placeholder: '请输入开仓价格结束',
        //   field: 'openPriceEnd'
        // },
        {
          type: 'select',
          label: '排序：',
          field: 'sortType',
          placeholder: '请选择排序类型',
          options: [{
            label: '按时间从近到远',
            value: 1
          },
          {
            label: '按实际成交金额从大到小',
            value: 4
          },
          {
            label: '按实际成交金额从小到大',
            value: 3
          },
          ]
        },
        {
          type: 'select',
          label: '用户类型：',
          field: 'userType',
          placeholder: '请选择用户类型',
          options: [{
            label: '主力',
            value: '4'
          },
          {
            label: '大户',
            value: '1'
          },
          {
            label: '中户',
            value: '2'
          },
          {
            label: '小户',
            value: '3'
          }
          ]
        },
        {
          type: 'select',
          label: '仓位方向：',
          field: 'side',
          placeholder: '请选择仓位方向',
          options: [{
            label: '开多',
            value: 'BUY'
          },
          {
            label: '开空',
            value: 'SELL'
          },
          ]
        },
        {
          type: 'select',
          label: '杠杆：',
          field: 'lever',
          placeholder: '请选择杠杆',
          options: [{
            label: '1倍',
            value: '1'
          },
          {
            label: '2倍',
            value: '2'
          },
          {
            label: '5倍',
            value: '5'
          },
          {
            label: '10倍',
            value: '10'
          },
          {
            label: '20倍',
            value: '20'
          },
          {
            label: '50倍',
            value: '50'
          },
          ]
        },
        {
          type: 'select',
          label: '状态：',
          field: 'statusListStr',
          placeholder: '请选择状态',
          multiple: true,
          //       INIT(0,"init"),  // 初始订单
          // NEW(1,"new"), // 委托单
          // FILLED(2,"filled"), // 完全成交
          // PART_FILLED(3,"part_filled"), // 部分成交
          // CANCELED(4,"canceled"), // 撤销
          // PENDING_CANCEL(5,"pending_cancel"), // 待撤销
          // EXPIRED(6,"expired"); \\ 异常
          options: [{
            label: '初始订单',
            value: 0
          }, {
            label: '委托单',
            value: 1
          },
          {
            label: '完全成交',
            value: 2
          },
          {
            label: '部分成交',
            value: 3
          }, {
            label: '撤销',
            value: 4
          },
          {
            label: '待撤销',
            value: 5
          },
          {
            label: '异常',
            value: 6
          },
          ]
        },
      ],
      tableSchema: [ // 表格架构
        {
          label: '开仓ID',
          field: 'orderId',
          width: '170px'
        },
        {
          label: '下单时间',
          slot: 'createAt',
          width: '170px'
        },
        {
          label: '用户con add',
          field: 'contractAddress',
          width: '200px'
        },
        {
          label: '用户昵称',
          field: 'nickname',
          width: '200px'
        },
        {
          label: '用户类型',
          field: 'userType',
          type: 'tag',
          tagMap: {
            4: {
              label: '主力',
            },
            1: {
              label: '大户',
              tagType: 'info'
            },
            2: {
              label: '中户',
              tagType: 'info'
            },
            3: {
              label: '小户',
              tagType: 'info'
            }
          },
          width: '100px'
        },
        {
          label: '仓位方向',
          field: 'side',
          type: 'tag',
          tagMap: {
            BUY: {
              label: '开多',
            },
            SELL: {
              label: '开空',
              tagType: 'info'
            },

          },
          width: '100px'
        },
        {
          label: '开仓价格',
          field: 'openPrice',
          width: '150px'
        },
        {
          label: '成交均价',
          field: 'avgPrice',
          width: '150px'
        },
        {
          label: '杠杆',
          // slot:'lever',
          field: 'lever',
          // type: 'tag',

          // tagMap: {
          //   1: {
          //     label: '1倍',
          //   },
          //   2: {
          //     label: '2倍',
          //   },
          //   5: {
          //     label: '5倍',
          //   },
          //   10: {
          //     label: '10倍',
          //   },

          // },
          width: '120px'
        },
        {
          label: '委托金额',
          field: 'openMoney',
          width: '120px'
        },
        {
          label: '实际成交金额',
          field: 'dealMoney',
          width: '120px'
        },

        {
          label: '成交支付金额',
          field: 'dealPayMoney',
          width: '120px'
        },
        {
          label: '成交手续费',
          field: 'dealPayFee',
          width: '120px'
        },
        {
          label: '实际支付',
          field: 'payMoney',
          width: '120px'
        },
        {
          label: "体验金/万能金类型",
          field: "trailType",
          type: 'tag',
          tagMap: {
            1: {
              label: '体验金',
            },
            2: {
              label: '万能金',
            },
            0: {
              label: '--',
            },
          },
        },
        {
          label: "体验金/万能金",
          field: "experienceMoney"
        },
        {
          label: '状态',
          field: 'status',
          slot: 'openHouse',
          // type: 'tag',
          // tagMap: {
          // 0: {
          //   label: '委托中',
          // },
          // 1: {
          //   label: '已成交',
          //   tagType: 'success'
          // },
          // 2: {
          //   label: '已撤单',
          //   tagType: 'info'
          // },
          // 3: {
          //   label: '部分成交',
          // },
          // 4: {
          //   label: '平仓中',
          // },
          //       5: {
          //         label: '已平仓',
          //         tagType: 'info'
          //       },
          // 10: {
          //   label: '部分成交',
          // },
          // },
          width: '150px'
        },
        // {
        //   label: '首笔成交时间',
        //   field: 'firstAt',
        //   width: '250px'
        // },
        {
          label: '完全成交时间',
          slot: 'tradeTime',
          width: '250px'
        },
        {
          label: '撤销时间',
          slot: 'revokeTime',
          width: '250px'
        },
        // {
        // 	label: '操作',
        // 	slot: 'action',

        // 	width: '140px',
        // 	fixed:'right'
        // }
      ],

      page: {
        // totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query1: {
        userType: '1',
      }
    }
  },
  created() {
    const todayMidnight = this.getTodayMidnight();
    const yesterdayMidnight = this.getYesterdayMidnight();

    this.querytime[0] = this.formatDate(yesterdayMidnight)
    this.querytime[1] = this.formatDate(todayMidnight)
  },
  async mounted() {
    await this.fetchcoin()
    this.getList()
  },
  methods: {
    async fetchcoin() {
      const res = await getSymbolList({})
      if (res.status.code == 0) {
        this.coinlist = res.result
        this.query.contractName = res.result[0].contractName
        console.log(this.query.contractName, 123);
        this.coinlist.push({
          contractName: 'E-BIT-USDT',
        })
      }

    },
    removeMilliseconds(dateTimeString) {
      console.log(dateTimeString);

      // return dateTimeString.split('.000')[0];
      // return dateTimeString

    },
    async houseExport(e) {
      if (this.querytime) {
        e.createStart = this.querytime[0] + '.000'
        e.createEnd = this.querytime[1] + '.000'
      }
      e.statusListStr = JSON.stringify(e.statusListStr)

      const res = await exportBitOpen({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '当前开仓' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    // 过滤查询
    onQueryChange(data) {
      const filteredObj = {};

      for (const key in data) {
        if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
          filteredObj[key] = data[key];
        }
      }
      this.query1 = filteredObj
      this.listLoading = true
      this.getList(true)
    },
    onQueryReset() {
      this.querytime = []
      this.query = {
        userType: '1'
      }
      this.query1 = {
        userType: '1',
      }
      this.listLoading = true
      this.getList()
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.listLoading = true
      this.getList()
    },
    // 获取列表
    async getList(isInit) {

      console.log(this.query1, '1231231');
      if (this.query1.openPriceStart || this.query1.openPriceEnd) {
        this.query1.openPriceStart = this.query1.openPriceStart ? Number(this.query1.openPriceStart) : ''
        this.query1.openPriceEnd = this.query1.openPriceEnd ? Number(this.query1.openPriceEnd) : ''
      }


      this.query1.createStart = this.querytime[0] + '.000';
      this.query1.createEnd = this.querytime[1] + '.000';
      const params = {
        ...this.query1,
        contractName: this.query.contractName,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
      }
      params.statusListStr = JSON.stringify(params.statusListStr)
      console.log('列表数据', params)
      const {
        status,
        result
      } = await getBitOpen(params)
      console.log('获取列表数据-------', result)
      if (status.code === 0) {
        this.listLoading = false
        let dataList = []
        this.tableData = []
        const data = result.list
        data.forEach((item) => {
          dataList.push(item)
        })
        this.tableData = dataList
        // this.page.pageNum = result.pageCount
        // this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 获取当天 0 点时间
    getTodayMidnight() {
      const now = new Date();
      // 设置日期为明天
      now.setDate(now.getDate() + 1);
      // 重置小时、分钟、秒、毫秒为 0
      now.setHours(0, 0, 0, 0);
      return now;
    },
    // 获取昨天 0 点时间
    getYesterdayMidnight() {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate()); // 减去一天
      yesterday.setHours(0, 0, 0, 0); // 设置时间为 0 点
      return yesterday;
    },
  }
}
</script>

<style></style>
