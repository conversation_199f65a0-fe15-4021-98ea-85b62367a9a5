<template>
    <d2-container class="page">
        <!--  -->
        <common-query :query-schema="querySchema" :showExport="true" @onExport="houseExport" @onSubmit="onQueryChange"
            ref="query" :data="query" @onReset="onQueryReset"></common-query>
        <div class="search-box">
            <el-button type="primary" @click="addForm">新增</el-button>
        </div>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #ctime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.ctime) }}</span>
            </template>
            <template #mtime="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.mtime) }}</span>
            </template>
            <template #action="scope">
                <!-- style="display: flex;justify-content: flex-start;" -->
                <div>
                    <el-button @click="openeditForm(scope.row)" type="text" size="mini">{{ scope.row.status == 1 ? '禁用'
                        : '启用'
                        }}</el-button>
                         <el-button @click="openeditForm2(scope.row)" type="text" size="mini">{{ scope.row.showStatus == 1 ? '不展示' : '展示'
                        }}</el-button>
                    <el-button @click="editForm(scope.row)" type="warning" size="mini">编辑</el-button>

                   
                </div>

            </template>
            <template #icon="scope">
                <img class="avatar2" :src="scope.row.icon" alt="">
            </template>
        </common-table>
        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

        <!-- 新增修改 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <template #icon="scope">
                    <el-upload :action="action" :headers="token" :on-success="handlePicSuccess"
                        :class="{ hide: hideUpload_introduce }" :on-change="handleIntroduceUploadHide"
                        :on-remove="handleIntroduceRemove" :file-list="fileListImg" class="avatar-uploader">
                        <img v-if="formData.icon" :src="formData.icon" class="avatar">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </template>
                <!-- <template #isDeleted="scope">
                    <el-switch v-model="formData.isDeleted" active-color="#13ce66" inactive-color="#ff4949"
                        active-text="是" inactive-text="否" active-value="1" inactive-value="0"></el-switch>
                </template> -->
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import { getSymbolList, searchSymbol, addSymbol, enableSymbol, showSymbol } from "@/api/bitconfig"
import CommonQuery from '@/components/CommonQuery_h'
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    components: {
        CommonQuery,
        CommonTable,
        CommonForm
    },
    data() {
        return {
            limitCount: 1,

            fileListImg: [],
            hideUpload_introduce: false,
            action:
                process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/upload',
            token: { AdminAuthorization: localStorage.getItem('usertoken') },
            pageNum: 1,
            pageSize: 20,
            tableData: [{}],
            dialogTitle: '新增',
            dialogVisible: false,
            formData: {
                status: 1
            },
            // formSchema: 
            page: {
                totalCount: 0,
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            query1: {
                status: 1,
            },
            listLoading: false,
            tableData: [],
            query: {
                createStart: '',
                createEnd: '',
            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            querySchema: [ // 搜索组件架构
                {
                    type: 'select',
                    label: '启用状态：',
                    field: 'status',
                    placeholder: '请选择启用状态',
                    options: [
                        {
                            label: '启用',
                            value: 1
                        },
                        {
                            label: '禁用',
                            value: 0
                        },
                    ]
                },

            ],
            tableSchema: [ // 表格架构
                { label: "合约id", field: "contractId", width: "50px" },
                { label: "基础货币", field: "base", width: "60px" },
                { label: "结算货币", field: "quote", width: "60px" },
                {
                    label: "启动状态", field: "status",
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '禁用',
                            tagType: 'error'
                        },
                        1: {
                            label: '启用',
                            tagType: 'success'
                        },

                    },
                    width: "60px"
                },
                {
                    label: "展示状态", field: "showStatus",
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '禁用',
                            tagType: 'error'
                        },
                        1: {
                            label: '启用',
                            tagType: 'success'
                        },

                    },
                    width: "60px"
                },
                { label: "开仓挂单手续费", field: "openMakerFee", width: "60px" },
                { label: "开仓吃单手续费", field: "openTakerFee", width: "60px" },
                { label: "平仓挂单手续费", field: "closeMakerFee", width: "60px" },
                { label: "平仓吃单手续费", field: "closeTakerFee", width: "60px" },
                // { label: "最小挂单手续费", field: "minMakerFee", width: "60px" },
                // { label: "最小吃单手续费", field: "minTakerFee", width: "60px" },
                { label: "创建时间", slot: "ctime", width: "120px" },
                { label: "修改时间", slot: "mtime", width: "120px" },
                { label: "铺单账户Id", field: "robotUid", width: "60px" },
                { label: "图标", slot: "icon", width: "60px" },
                { label: '操作', slot: 'action', width: '60px',}
            ],
        }
    },

    mounted() {
        // this.getDetail()
        this.getList()

    },
    methods: {
        openeditForm(item) {
            this.$confirm('确认改变币对的启动状态？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true
            }).then(async () => {
                let res = await enableSymbol({
                    id: item.contractId,
                    status: item.status == 1 ? 0 : 1
                })
                if (res.status.code == 0) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }

            }).catch(() => {
                // this.$message({
                //     type: 'info',
                //     message: '已取消删除'
                // });
            });

        },

        openeditForm2(item) {
            this.$confirm('确认改变币对的展示状态？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true
            }).then(async () => {
                let res = await showSymbol({
                    id: item.contractId,
                    status: item.showStatus == 1 ? 0 : 1
                })
                if (res.status.code == 0) {
                    this.getList()
                    this.$message({
                        type: 'success',
                        message: '操作成功!'
                    });
                }

            }).catch(() => {
                // this.$message({
                //     type: 'info',
                //     message: '已取消删除'
                // });
            });

        },
        // 图片移除
        handleIntroduceRemove(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
            this.formData.attachment = ''
        },
        handlePicSuccess(res, file) {
            console.log(res, '上传');
            this.formData.icon = res.result.url
        },
        handleIntroduceUploadHide(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
        },
        async submit() {
            if (this.dialogTitle == '新增') {
                this.formData.closeMakerFee = this.formData.closeTakerFee - 0
                // closeTakerFee
                this.formData.closeTakerFee = this.formData.closeTakerFee - 0

                this.formData.openMakerFee = this.formData.openTakerFee - 0

                // this.formData.minMakerFee = this.formData.minTakerFee - 0
                // this.formData.minTakerFee = this.formData.minTakerFee - 0
                this.formData.openTakerFee = this.formData.openTakerFee - 0
                this.formData.status = this.formData.status - 0
                let data = {
                    ...this.formData
                }
                await addSymbol(data).then(res => {
                    if (res.status.code == 0) {
                        this.$message.success('添加成功')
                        this.dialogVisible = false
                        this.getList()
                        this.formData = {}
                    }
                })
            } else {
                let data = {
                    id: this.formData.contractId,
                    ...this.formData
                }
                let res = await addSymbol(data)
                if (res.status.code == 0) {
                    this.$message.success(res.status.msg)
                    this.dialogVisible = false
                    this.getList()
                    this.formData = {}
                }
            }
            console.log(this.formData, 'tijiao');

        },
        addForm() {
            this.dialogVisible = true
            this.dialogTitle = '新增'
            this.formData = {}
            this.formData.status = "1"
            this.$nextTick(() => {
                this.$refs.form.resetFields()
            })
        },
        editForm(item) {
            this.dialogVisible = true
            this.dialogTitle = '编辑'
            this.formData = item
            this.formData.status = '' + (item.status)
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async houseExport(e) {
            // ExportOpenRecords
            // ExportMarketRecords
            // ExportDealHistory
            const res = await exportBitDetail({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '开仓明细' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {

            this.listLoading = true
            this.getList()
        },
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            this.getList(true)
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            //   ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            // return
            const params = {
                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            const {
                status,
                result
            } = await searchSymbol(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                let dataList = []
                this.tableData = []
                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                console.log("this.tableData", this.tableData)
                this.page.totalCount = result.totalCount
                this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize

            }
        },
        getDetail() {
            let data = {
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }
            getBitDetail(data).then(res => {
            })
        }
    },
    computed: {
        formSchema() {
            return [
                {
                    type: 'input',
                    label: '基础货币：',
                    placeholder: '请输入基础货币',
                    disabled: this.dialogTitle == '编辑',
                    field: 'base',
                    rules: [
                        {
                            required: true,
                            message: '请输入用户id',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '结算货币：',
                    disabled: this.dialogTitle == '编辑',
                    placeholder: '请输入结算货币',
                    field: 'quote',
                    rules: [
                        {
                            required: true,
                            message: '请输入结算货币',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '开仓taker手续费：',
                    placeholder: '请输入开仓taker手续费',
                    field: 'openTakerFee',
                    rules: [
                        {
                            required: true,
                            message: '请输入开仓taker手续费',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '开仓maker手续费：',
                    placeholder: '请输入开仓maker手续费',
                    field: 'openMakerFee',
                    rules: [
                        {
                            required: true,
                            message: '请输入开仓maker手续费',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '平仓taker手续费：',
                    placeholder: '请输入平仓taker手续费',
                    field: 'closeTakerFee',
                    rules: [
                        {
                            required: true,
                            message: '请输入平仓taker手续费',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '平仓maker手续费：',
                    placeholder: '请输入平仓maker手续费',
                    field: 'closeMakerFee',
                    rules: [
                        {
                            required: true,
                            message: '请输入平仓maker手续费',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '公共账户id',
                    placeholder: '请输入公共账户id',
                    disabled: this.dialogTitle == '编辑',

                    field: 'commonUid',
                    rules: [
                        {
                            required: true,
                            message: '请输入公共账户id',
                            trigger: 'blur',
                        },
                    ]
                },
                {
                    type: 'input',
                    label: '公共账户转账预警金额',
                    placeholder: '请输入公共账户转账预警金额',
                    field: 'commonTransferWarnMoney',
                    rules: [
                        {
                            required: true,
                            message: '请输入公共账户转账预警金额',
                            trigger: 'blur',
                        },
                    ]
                },
                {
                    type: 'input',
                    label: '铺单账户Id：',
                    placeholder: '请输入铺单账户Id',
                    disabled: this.dialogTitle == '编辑',
                    field: 'robotUid',
                    rules: [
                        // {
                        //     required: true,
                        //     message: '请选择状态',
                        //     trigger: 'change',
                        // },
                    ],
                },

                {
                    type: 'input',
                    label: '铺单账户转账告警金额',
                    // robotTransferWarnMoney
                    placeholder: '请输入铺单账户转账告警金额',
                    field: 'robotTransferWarnMoney',
                    rules: [
                        {
                            required: true,
                            message: '请输入铺单账户转账告警金额',
                            trigger: 'blur',
                        },
                    ],
                },


                // zeroChangePercent
                {
                    type: 'input',
                    label: '和昨日零点相比变化百分比',
                    placeholder: '请输入和昨日零点相比变化百分比',
                    field: 'zeroChangePercent',
                    rules: [
                        {
                            required: true,
                            message: '请输入和昨日零点相比变化百分比',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    // zeroChangeAmount
                    type: 'input',
                    label: '和昨日零点相比变化值',
                    placeholder: '请输入和昨日零点相比变化值',
                    field: 'zeroChangeAmount',
                    rules: [
                        {
                            required: true,
                            message: '请输入和昨日零点相比变化值',
                            trigger: 'blur',
                        },
                    ],
                },
                // {
                //     type: 'input',
                //     label: '最小maker手续费：',
                //     placeholder: '请输入最小maker手续费',
                //     field: 'minMakerFee',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入最小maker手续费',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // {
                //     type: 'input',
                //     label: '最小taker手续费：',
                //     placeholder: '请输入最小taker手续费',
                //     field: 'minTakerFee',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入最小taker手续费',
                //             trigger: 'blur',
                //         },
                //     ],
                // },





                // {
                //     type: 'radio',
                //     label: '状态：',
                //     field: 'status',
                //     options: [
                //         { label: '启用', value: "1" },
                //         { label: '禁用', value: "0" },
                //     ],
                //     rules: [
                //         {
                //             required: true,
                //             message: '请选择状态',
                //             trigger: 'change',
                //         },
                //     ],
                // },
                // commonUid

                // commonTransferWarnMoney

                {
                    type: 'upload',
                    label: '图标：',
                    slot: 'icon',
                    rules: [],
                },
                {
                    type: 'action'
                },
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.avatar2 {
    width: 78px;
    height: 78px;
    display: block;
}

// .page {
//     width: 100%;
// }

.search-box {
    margin-bottom: 20px;
}
</style>