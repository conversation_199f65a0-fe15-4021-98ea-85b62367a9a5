<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :showExport="false" @onExport="houseExport" @onSubmit="onQueryChange"
            ref="query" :data="query" @onReset="onQueryReset">
        </common-query>
        <el-button type="primary" size="small" @click="add" style="margin-bottom: 20px;">新增发放</el-button>

        <!-- :showExport="true" @onExport="houseExport" -->
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading='listLoading'>
            <template #createAt="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.createAt) }}</span>
            </template>


            <!-- supportLever -->
            <template #supportLever="scope">
                <span v-if="scope.row.supportLever" v-for="item in scope.row.supportLever">{{ item == 0 ? '无限制' :
                    (item + 'X')
                    }}</span>
            </template>

            <!-- supportContract -->
            <template #supportContract="scope">
                <span v-if="scope.row.supportContract" v-for="item in scope.row.supportContract">{{ item }}</span>

            </template>
        </common-table>

        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

        <!-- 窗口 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">

                <!-- supportLever -->
                <template #supportLever="scope">
                    <el-checkbox-group v-model="formData.supportLever">
                        <el-checkbox v-for="(item, index) in leveroptions" :key="index" :label="item.value"
                            :value="item.value">{{
                                item.label }}</el-checkbox>
                    </el-checkbox-group>
                </template>

                <!-- supportContract -->
                <template slot="supportContract">
                    <el-checkbox-group v-model="formData.supportContract" @change="change">
                        <el-checkbox v-for="(item, index) in coinlist" :key="index" :label="item.value"
                            :value="item.value">{{
                                item.label }}</el-checkbox>
                    </el-checkbox-group>
                </template>
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import { getBitHold, exportBitHold, getExperienceList, addExperience } from "@/api/bit"
import { getBitConfigList, getBitConfigListAdd, getSymbolList } from "@/api/bitconfig.js"

import CommonQuery from '@/components/CommonQuery_h'
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: "MagnumGold",
    components: {
        CommonQuery,
        CommonForm,
        CommonTable
    },
    props: {},
    data() {
        return {
            coinlist: [],
            leveroptions: [
                { label: '无限制', value: "0" },
                { label: '1X', value: "1" },
                { label: '2X', value: "2" },
                { label: '5X', value: "5" },
                { label: '10X', value: "10" },
                { label: '20X', value: "20" },
                { label: '50X', value: "50" },

            ],
            formSchema: [
                {
                    type: 'textarea',
                    label: '发放人群：',
                    placeholder: '请输入发放人群',
                    field: 'userInfo',
                    rules: [{
                        required: true,
                        message: '请输入发放人群',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'input',
                    label: '面值：',
                    placeholder: '请输入面值',
                    field: 'amount',
                    rules: [{
                        required: true,
                        message: '请输入面值',
                        trigger: 'blur'
                    },]
                },
                {
                    label: '支持币对（可多选）:',
                    slot: 'supportContract',
                },
                {
                    type: 'radio',
                    label: '支持杠杆（可多选）:',
                    slot: 'supportLever',
                    // multi: true,
                    options: [
                        { label: '无限制', value: "0" },
                        { label: '1X', value: "1" },
                        { label: '2X', value: "2" },
                        { label: '5X', value: "5" },
                        { label: '10X', value: "10" },
                        { label: '20X', value: "20" },
                        { label: '50X', value: "50" },

                    ],
                    rules: [
                        {
                            required: true,
                            message: '请选择杠杆',
                            trigger: 'change',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '最大持仓时长（单位：小时）：',
                    placeholder: '请输入最大持仓时长',
                    field: 'maxPositionTime',
                    rules: [{
                        required: true,
                        message: '请输入最大持仓时长',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'textarea',
                    label: '备注：',
                    placeholder: '请输入备注',
                    field: 'remark',
                    rules: [{
                        required: true,
                        message: '请输入备注',
                        trigger: 'blur'
                    },]
                },
                {
                    type: 'datetime',
                    label: '有效期至：',
                    field: 'expireTime',
                    rules: [{
                        required: true,
                        message: '请选择有效期',
                        trigger: 'change'
                    },]
                },
                {
                    type: 'action'
                }
            ],
            formData: {
                supportLever: ["0"],
                supportContract: [0]
            },
            dialogVisible: false,
            dialogTitle: "新增发放",
            listLoading: true,
            tableData: [],
            query: {

            },
            dialogVisible: false, // 弹窗
            selected: '', // 选择的活动类型
            page: {
                pageSize: 10
            }, // 分页数据
            querySchema: [ // 搜索组件架构

                {
                    type: 'datetimerange',
                    label: '发放日期：',
                    placeholder: '请输入发放日期',
                    field: 'sendDateStart',
                    field2: 'sendDateEnd',
                },
                {
                    type: 'twoinput',
                    label: '面值：',
                    placeholder: '请输入面值',
                    field: ['amountStart', 'amountEnd'],
                },
                {
                    label: "接受用户昵称",
                    type: 'input',
                    field: 'nickName',
                    placeholder: '请输入接受用户昵称',
                },
                {
                    type: 'input',
                    label: '接收用户地址：',
                    placeholder: '请输入接收用户地址',
                    field: 'conadd'
                },


                {
                    type: 'select',
                    label: '状态：',
                    field: 'status',
                    placeholder: '请选择状态',
                    options: [
                        {
                            label: '使用已平仓',
                            value: '4'
                        },
                        {
                            label: '已过期',
                            value: '3'
                        },
                        {
                            label: '已使用',
                            value: '2'
                        },
                        {
                            label: '未使用',
                            value: '1'
                        },
                    ]
                },


            ],
            tableSchema: [
                {
                    label: '发布日期',
                    slot: 'createAt',
                },
                {
                    label: '面值',
                    field: 'amount',
                },
                {
                    label: '接收用户昵称',
                    field: 'nickName',
                },
                {
                    label: '接收用户地址',
                    field: 'conadd',
                },
                {
                    label: '支持币对',
                    slot: 'supportContract',
                },
                {
                    label: '支持杠杆',
                    slot: 'supportLever',
                },
                {
                    label: '支持持仓时长',
                    field: 'maxPositionTime',
                },
                {
                    label: '有效期',
                    field: 'expireTime',
                },
                {
                    label: '使用状态',
                    field: 'status',
                    type: 'tag',
                    tagMap: {
                        4: {
                            label: '已使用已平仓',
                        },
                        1: {
                            label: '未使用',
                            tagType: 'info'
                        },
                        2: {
                            label: '已使用',
                            tagType: 'info'
                        },
                        3: {
                            label: '已过期',
                            tagType: 'info'
                        }
                    },
                },
                {
                    label: '用户收益',
                    field: 'userProfit',
                },
                {
                    label: '发放备注',
                    field: 'remark',
                },
                {
                    label: '发放人',
                    field: 'createAdminName',
                }
            ]
            ,
            // tableSchema: [ // 表格架构
            //     {
            //         label: '活动名称',
            //         field: 'title',
            //     },
            //     {
            //         label: '发放人群',
            //         field: 'userInfo',
            //     },


            //     {
            //         label: '体验金金额',
            //         field: 'amount',
            //     },
            //     {
            //         label: '备注',
            //         field: 'remark',
            //     },
            //     {
            //         label: '状态',
            //         field: 'status',
            //         type: 'tag',
            //         tagMap: [{
            //             label: '初始',
            //             value: 0
            //         }, {
            //             label: '执行中',
            //             value: 1
            //         },
            //         {
            //             label: '完成',
            //             value: 2
            //         },
            //         {
            //             label: '部分完成',
            //             value: 3
            //         }
            //         ],
            //     },
            //     {
            //         label: '发放时间',
            //         slot: 'ctime',
            //     },

            // ],

            tableData: [{}],

            page: {
                pageSize: 20,
                pageNum: 1
            }, // 分页数据
            isCheck: false,
            query1: {
                // status: '1'
            }
        }
    },
    mounted() {
        this.getList()
        this.fetchcoin()
    },
    methods: {
        change(e) {
            console.log(e, this.formData, '12312312312312312');

        },
        async fetchcoin() {
            const res = await getSymbolList({})
            if (res.status.code == 0) {
                this.coinlist = res.result.map(item => ({
                    label: item.name,
                    value: item.contractId,
                }))
                this.coinlist.unshift({
                    label: '无限制',
                    value: 0,
                }, {
                    label: 'BIT指数',
                    value: 1,
                },)
                console.log(this.coinlist, '12312312312');

            }
        },
        async submit() {
            this.formData.amount = Number(this.formData.amount)
            this.formData.userInfo = this.formData.userInfo.replace(/\n+/g, ',');

            let params = {
                ...this.formData,
                supportContract: `${this.formData.supportContract}`,
                supportLever: `${this.formData.supportLever}`,
                type: 2
            }
            const res = await getBitConfigListAdd(params)
            if (res.status.code == 0 && res.result.length == 0) {
                this.$message.success('操作成功')
                this.dialogVisible = false
                this.getList()
            } else {
                // res.reslut为数组，循环这个数组并且$message出来
                // this.$message.error(res.reslut)
                this.$message.error(res.result.join(", "));
            }

        },
        add() {
            this.dialogVisible = true
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.')[0];
        },
        async checkIncome(row) {
            console.log(row)
            this.isCheck = true
            // this.getList(row.ctid)
            // const res = await this.$api.qifeiHoldHistory({
            //    orderType:1,
            //   ctid: row.ctid
            // })

        },
        onQueryReset() {
            this.query = {
                userType: '1',
                orderType: '4'
            }
            this.query1 = {
                userType: '1',
                orderType: '4',
                status: '1'
            }
            this.listLoading = true
            this.getList()
        },
        // 过滤查询
        onQueryChange(data) {
            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }

            this.query1 = filteredObj
            this.listLoading = true
            this.getList(true)
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        //分页数量切换
        xuanzeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        // 获取列表
        async getList(isInit) {
            // let ctid;
            // if (this.query.ctid) {
            // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
            // }
            const params = {

                ...this.query1,
                ...this.page,
                pageNum: isInit ? 1 : this.page.pageNum,
            }
            console.log('列表数据', params)
            delete params.totalCount
            const {
                status,
                result
            } = await getBitConfigList(params)
            console.log('获取列表数据', result)
            if (status.code === 0) {
                this.listLoading = false
                this.tableData = []
                let dataList = []

                const data = result.list
                data.forEach((item) => {
                    dataList.push(item)
                })
                this.tableData = dataList
                this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                this.page.pageCount = result.pageCount
                // this.page.pageNum = result.pageCount
                // this.page.totalCount = result.totalCount
                // this.page.pageSize = result.pageSize
                // this.page.pageCount = result.pageCount
            }
        },
        async houseExport(e) {
            const res = await exportBitHold({
                ...e
            })
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, '当前持仓' + Date.now() + '.csv')
                this.$message.success('导出成功')
                this.getList()
            }
        },
    }
}
</script>

<style></style>