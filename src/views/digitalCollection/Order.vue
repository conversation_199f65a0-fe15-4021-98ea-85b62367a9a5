<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema"  @onSubmit="onQueryChange" @onExport="onExport"
                  show-export></common-query>
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        label="序号"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column
        prop="orderNo"
        label="订单编号">
      </el-table-column>
      <el-table-column
        prop="orderType"
        label="订单类型">
        <template v-slot="scope">
          {{ orderTypeMap[scope.row.orderType] }}
        </template>
      </el-table-column>
      <el-table-column
        prop="goodsTitle"
        label="商品名称">
      </el-table-column>
      <el-table-column
        label="商品信息">
        <template v-slot="scope">
          <el-button @click="openDialog('goods', scope.row)" type="text">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="totalAmount"
        label="订单价格">
      </el-table-column>
      <el-table-column
        prop="payType"
        label="支付方式">
        <template v-slot="scope">
          {{ payTypeMap[scope.row.payType] }}
        </template>
      </el-table-column>
      <el-table-column
        label="卖家信息">
        <template v-slot="scope">
          <el-button @click="openDialog('seller', scope.row)" type="text">{{ scope.row.sellerName }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="收货信息">
        <template v-slot="scope">
          <el-button @click="openDialog('receiver', scope.row)" type="text">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderStatus"
        label="订单状态">
        <template v-slot="scope">
          {{ orderStatusMap[scope.row.orderStatus] }}
        </template>
      </el-table-column>
      <el-table-column
        label="用户协商信息">
        <template v-slot="scope">
          <el-button @click="openListDialog('consult', scope.row)" type="text">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="createAt"
        label="下单时间">
      </el-table-column>
      <el-table-column
        prop="payTime"
        label="支付时间">
      </el-table-column>
      <el-table-column
        prop="deliverVoucher"
        label="发货凭证">
        <template v-slot="scope">
          <el-button
            @click="openDeliverVoucher(scope.row)"
            :disabled="!scope.row.deliverVoucher"
            type="text">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="deliverVoucher"
        label="操作历程">
        <template v-slot="scope">
          <el-button
            @click="openListDialog('history', scope.row)"
            type="text">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="100">
        <template v-slot="scope">
          <el-button :disabled="!(scope.row.orderStatus === 20 || scope.row.orderStatus === 30)"
                     @click="closeOrder(scope.row.orderNo)" type="text">关闭订单
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <div class="dialog-goods-info">
        <el-image
          v-if="curModalInfo.photo"
          style="width: 180px;"
          :src="curModalInfo.photo"
          :preview-src-list="[curModalInfo.photo]">
        </el-image>
        <el-form :model="curModalInfo" size="mini">
          <el-form-item v-for="item in infoSchema" :key="item.field" :label="item.label">
            <span v-if="item.type === 'tag'">
              <el-tag v-if="curModalInfo[item.field] === 1">非预售</el-tag>
              <el-tag v-if="curModalInfo[item.field] === 2">预售</el-tag>
            </span>
            <span v-else>{{ curModalInfo[item.field] }}</span>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="发货凭证" :visible.sync="infoDialogVisible">
      <div class="dialog-goods-info">
        <el-image
          v-for="(item, index) in deliverVoucherList"
          :key="index"
          style="width: 180px;"
          :src="item"
          :preview-src-list="[item]">
        </el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="infoDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="dialogTitle" :visible.sync="infoListDialogVisible">
      <div class="dialog-goods-list-info">
        <template v-if="infoList.length > 0">
          <div v-for="(formItem, index) in infoList" :key="index" class="dialog-goods-list-container">
            <el-form size="mini">
              <el-form-item v-for="item in infoSchema" :key="item.field" :label="item.label">
                <span>{{ formItem[item.field] }}</span>
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template v-else>
          <p>暂无记录</p>
        </template>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="infoListDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import { downloadBlob } from '@/utils/helper'

export default {
  name: 'DigitalCollectionOrder',
  components: {
    CommonQuery
  },
  data () {
    return {
      tableData: [],
      query: {},
      querySchema: [
        {
          type: 'input',
          label: '商品名称',
          placeholder: '请输入商品名称',
          field: 'goodsName'
        },
        {
          type: 'input',
          label: '订单号',
          placeholder: '清输入订单号',
          field: 'orderNo'
        },
        {
          type: 'input',
          label: '卖方昵称',
          placeholder: '请输入卖方昵称',
          field: 'sellerNickname'
        },
        {
          type: 'select',
          label: '订单状态',
          field: 'orderStatus',
          placeholder: '请选择订单状态',
          options: []
        },
        {
          type: 'select',
          label: '订单类型',
          field: 'orderType',
          placeholder: '请选择订单类型',
          options: []
        },
        {
          type: 'datetimerange',
          label: '发布时间',
          field: 'createOrderBeginTime',
          field2: 'createOrderEndTime',
          placeholder: '请选择发布时间'
        }
      ],
      orderStatusMap: {},
      payTypeMap: {},
      orderTypeMap: {},
      dialogVisible: false,
      infoDialogVisible: false,
      infoListDialogVisible: false,
      curModalInfo: {},
      infoSchema: [],
      dialogTitle: '',
      deliverVoucherList: [],
      infoList: [],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }
    }
  },
  computed: {},
  mounted () {
    this.setOptions()
    this.getOrderList()
  },
  methods: {
    /**
     * 导出订单
     */
    async onExport (data) {
      const res = await this.$api.adminOrderListExport(data)
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '订单' + Date.now() + '.csv')
        this.$message.success('导出成功')
      }
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getOrderList(true)
    },
    async getGoodsInfo (id) {
      const {
        result,
        status
      } = await this.$api.getGoodsDetail({ tid: id })
      console.log(result)
      if (status.code === 0) {
        this.curModalInfo = result
        // this.dialogVisible = true
      }
    },
    async closeOrder (orderNo) {
      this.$prompt('请提前与买卖双方协商沟通且达成一致后，关闭该订单，订单关闭后，买家所付款项将退回到买家账户余额，订单状态变更为已关闭，请谨慎操作。',
        '关闭订单',
        {
          confirmButtonText: '确定关闭',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入关闭原因'
        }).then(async ({ value }) => {
        const { status } = await this.$api.digitalCollectionOrderClose({
          orderNo,
          closeReason: value
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
        } else {
          this.$message.error(status.msg)
        }
      })
    },
    async getUserNegotiationInfo (orderNo) {
      const {
        status,
        result
      } = await this.$api.digitalCollectionUserNegotiationInfo({ orderNo })
      if (status.code === 0) {
        this.infoList = result.zsbAdminOrderCancelRecordListVOList
      }
    },
    async getHistoryInfo (orderNo) {
      const {
        status,
        result
      } = await this.$api.digitalCollectionHistoryInfo({ orderNo })
      console.log(result)
      if (status.code === 0) {
        this.infoList = result.zsbAdminOrderCourseListVOS
      }
    },
    async getOrderList (isInit) {
      const {
        status,
        result
      } = await this.$api.digitalCollectionOrderList({
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      })
      console.log(result)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    openDeliverVoucher (row) {
      console.log(row.deliverVoucher)
      this.deliverVoucherList = row.deliverVoucher
      this.infoDialogVisible = true
    },
    openDialog (type, row) {
      console.log(row)
      const goodsSchema = [
        {
          label: '商品名称：',
          field: 'title'
        },
        {
          label: '商品价格：',
          field: 'price'
        },
        {
          label: '发行平台：',
          field: 'platformName'
        },
        {
          label: '信息介绍：',
          field: 'content'
        },
        {
          label: '商品属性：',
          field: 'saleType',
          type: 'tag'
        },
        {
          label: '发行时间：',
          field: 'releaseTime'
        },
        {
          label: 'ID',
          field: 'releaseId'
        },
        {
          label: '发行方',
          field: 'releaseName'
        },
        {
          label: '技术支持',
          field: 'techSupport'
        },
        {
          label: '发行数量',
          field: 'num'
        },
        {
          label: '发行编号',
          field: 'releaseNo'
        },
        {
          label: '其他信息',
          field: 'note'
        }
      ]
      const receiverSchema = [
        {
          label: '收货人昵称：',
          field: 'receiverName'
        },
        {
          label: '收货人手机号：',
          field: 'receiverPhone'
        },
        {
          label: '收货人地址：',
          field: 'receiverAddress'
        },
        {
          label: '收货人平台名称：',
          field: 'receiverPlatform'
        }
      ]
      const sellerSchema = [
        {
          label: '卖方昵称：',
          field: 'sellerName'
        },
        {
          label: '卖方手机号：',
          field: 'sellerPhone'
        }
      ]
      switch (type) {
        case 'goods':
          this.getGoodsInfo(row.goodsTid)
          this.infoSchema = goodsSchema
          this.dialogTitle = '商品信息'
          break
        case 'seller':
          this.curModalInfo = row
          this.infoSchema = sellerSchema
          this.dialogTitle = '卖方信息'
          break
        case 'receiver':
          this.curModalInfo = row
          this.infoSchema = receiverSchema
          this.dialogTitle = '收货人信息'
          break
        default:
          break
      }
      this.dialogVisible = true
    },
    openListDialog (type, row) {
      console.log(row)
      const historySchema = [
        {
          label: '订单编号：',
          field: 'orderNo'
        },
        {
          label: '操作时间：',
          field: 'createAt'
        },
        {
          label: '操作描述：',
          field: 'operateDesc'
        },
        {
          label: '前置状态描述：',
          field: 'preStatusDesc'
        },
        {
          label: '后置状态描述：',
          field: 'currentStatusDesc'
        },
        {
          label: '备注：',
          field: 'remark'
        }
      ]
      const consultSchema = [
        {
          label: '买家申请取消时间：',
          field: 'applyCancelTime'
        },
        {
          label: '取消原因：',
          field: 'applyCancelReason'
        },
        {
          label: '受理状态：',
          field: 'acceptStatusDesc'
        },
        {
          label: '受理时间：',
          field: 'acceptTime'
        },
        {
          label: '订单编号：',
          field: 'orderNo'
        }
      ]
      switch (type) {
        case 'consult':
          this.getUserNegotiationInfo(row.orderNo)
          this.infoSchema = consultSchema
          this.dialogTitle = '协商信息'
          break
        case 'history':
          this.getHistoryInfo(row.orderNo)
          this.infoSchema = historySchema
          this.dialogTitle = '操作历程'
          break
        default:
          break
      }
      this.infoListDialogVisible = true
    },
    async getQueryOptions (dictType) {
      const {
        status,
        result
      } = await this.$api.getDictDataByDictType({ dictType })
      if (status.code === 0) {
        return result.dictDataListVOS.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        return []
      }
    },
    generateMap (raw) {
      return raw.reduce((acc, item) => {
        acc[item.value] = item.label
        return acc
      }, {})
    },
    async setOptions () {
      const orderTypeOptions = await this.getQueryOptions('SC_ORDER_TYPE')
      const orderStatusOptions = await this.getQueryOptions('SC_ORDER_STATUS')
      const payTypeOptions = await this.getQueryOptions('SC_ORDER_PAY_TYPE')
      this.orderStatusMap = this.generateMap(orderStatusOptions)
      this.payTypeMap = this.generateMap(payTypeOptions)
      this.orderTypeMap = this.generateMap(orderTypeOptions)
      console.log(this.orderStatusMap)
      this.querySchema.forEach(item => {
        if (item.field === 'orderType') {
          item.options = orderTypeOptions
        } else if (item.field === 'orderStatus') {
          item.options = orderStatusOptions
        }
      })
    },
    currentChange (value) {
      console.log(value)
      this.page.pageNum = value
      this.getOrderList()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-goods-info {
  display: flex;
  align-items: center;

  .el-form {
    margin-left: 20px;
    flex: 1;
  }
}

.dialog-goods-list-info {
  display: flex;
  flex-direction: column;

  .dialog-goods-list-container {
    padding-top: 20px;

    &:not(:last-child) {
      border-bottom: 1px solid #e6e6e6;
    }
  }

  //.el-form {
  //  margin-left: 20px;
  //  flex: 1;
  //}
}
</style>
