<script>
import CommonQuery from '@/components/CommonQuery'
export default {
  name: 'DigitalCollectionPublish',
  components: {
    CommonQuery
  },
  data () {
    return {
      tableData: [],
      query: {},
      querySchema: [
        {
          type: 'input',
          label: '商品名称',
          placeholder: '请输入商品名称',
          field: 'title'
        },
        {
          type: 'input',
          label: '发布用户',
          placeholder: '请输入发布用户名称',
          field: 'username'
        },
        {
          type: 'select',
          label: '发行平台',
          field: 'platform',
          placeholder: '请选择发行平台',
          options: []
        },
        {
          type: 'select',
          label: '审核状态',
          field: 'audit',
          placeholder: '请选择审核状态',
          options: []
        },
        {
          type: 'select',
          label: '商品状态',
          field: 'saleStatus',
          placeholder: '请选择商品状态',
          options: []
        },
        {
          type: 'select',
          label: '商品属性',
          field: 'saleType',
          placeholder: '请选择商品属性',
          options: []
        },
        {
          type: 'datetimerange',
          label: '发布时间',
          field: 'createTimeStart',
          field2: 'createTimeEnd',
          placeholder: '请选择发布时间',
          options: []
        }
      ],
      tableSchema: [
        {
          label: '商品名称',
          field: 'title'
        },
        {
          label: '商品图片',
          field: 'photo',
          type: 'img'
        },
        {
          label: '商品价格',
          field: 'price'
        },
        {
          label: '发行平台',
          field: 'platformName'
        },
        {
          label: '信息介绍',
          field: 'content'
        },
        {
          label: '发布用户',
          field: 'username'
        },
        {
          label: '审核状态',
          field: 'shows',
          type: 'audit'
        },
        {
          label: '商品状态',
          field: 'sale',
          type: 'status'
        },
        {
          label: '商品属性',
          field: 'saleType',
          type: 'attr'
        },
        {
          label: '发布时间/编辑时间',
          field: 'createAt'
        }
      ],
      dialogVisible: false,
      auditDialogVisible: false,
      curModalInfo: {},
      auditData: {
        peopleVerifyStatus: 'PEOPLEPASS',
        rejectExplain: '',
        tid: ''
      },
      infoSchema: [
        { label: '商品名称：', field: 'title' },
        { label: '商品图片：', field: 'photo', type: 'img' },
        { label: '发售价格：', field: 'price' },
        { label: '发行平台：', field: 'platformName' },
        { label: '信息介绍：', field: 'content' },
        { label: '发布用户：', field: 'nickName' },
        { label: '用户地址：', field: '' },
        { label: '商品属性：', field: 'saleType', type: 'tag' },
        { label: '发布时间：', field: 'createAt' },
        { label: '编辑时间：', field: 'updateAt' },
        { label: '发行时间：', field: 'releaseTime' },
        { label: 'ID：', field: 'releaseId' },
        { label: '发行方：', field: 'releaseName' },
        { label: '技术支持：', field: 'techSupport' },
        { label: '发行数量：', field: 'num' },
        { label: '发行编号：', field: 'releaseNo' },
        { label: '其他信息：', field: 'note' }
      ],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }
    }
  },
  computed: {
  },
  mounted () {
    this.setOptions()
    this.getList()
  },
  methods: {
    // toAddPage (row) {
    //   this.$router.push({
    //     name: 'DigitalCollectionSettingAddPlatform',
    //     query: {
    //       ...row,
    //       id: row.id
    //     }
    //   })
    // },
    async getDetail (tid) {
      const { status, result } = await this.$api.digitalCollectionGoodsDetail({ tid })
      if (status.code === 0) {
        this.curModalInfo = result
        this.dialogVisible = true
      }
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.digitalCollectionGoodsPublish(params)
      console.log(result)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    openDialog (type, row) {
      this.dialogVisible = true
    },
    async getQueryOptions (dictType) {
      const { status, result } = await this.$api.getDictDataByDictType({ dictType })
      if (status.code === 0) {
        return result.dictDataListVOS.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        return []
      }
    },
    generateMap (raw) {
      return raw.reduce((acc, item) => {
        acc[item.value] = item.label
        return acc
      }, {})
    },
    async setOptions () {
      const audit = await this.getQueryOptions('SC_GOODS_LIST_ADUIT')
      const status = await this.getQueryOptions('SC_GOODS_LIST_SALE')
      const attr = await this.getQueryOptions('SC_GOODS_LIST_SALE_TYPE')
      const { result } = await this.$api.digitalCollectionPlatformList({ pageSize: 1000 })
      console.log(attr)
      this.querySchema.forEach(item => {
        switch (item.field) {
          case 'saleType':
            item.options = attr
            break
          case 'saleStatus':
            item.options = status
            break
          case 'audit':
            item.options = audit
            break
          case 'platform':
            item.options = result.list.map(item => ({
              label: item.name,
              value: item.id
            }))
            break
          default:
            break
        }
      })
    },
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    toggleItem (tid, sale) {
      const title = sale === 1 ? '确认下架' : '确认上架'
      const text = sale === 1 ? '是否确认下架' : '是否确认上架'
      this.$confirm(text, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionGoodsUpAndDown({ tid, saleStatus: sale === 1 ? 0 : 1 })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    deleteItem (tid) {
      this.$confirm('删除后该作品将在用户商品列表不可见', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionGoodsDelete({ tid })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    async audit () {
      console.log(this.auditData)
      const { status } = await this.$api.digitalCollectionGoodsAudit(this.auditData)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
        this.auditDialogVisible = false
      }
    },
    openAuditDialog (tid) {
      this.auditData.tid = tid
      this.auditDialogVisible = true
    },
    clearReason () {
      this.auditData.rejectExplain = ''
    }
  }
}
</script>

<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        label="序号"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column
        v-for="(item) in tableSchema"
        :key="item.field"
        :label="item.label"
        :prop="item.field"
        :width="item.width">
        <template v-if="item.type" v-slot="scope">
          <span v-if="item.type === 'img'">
            <img :src="scope.row[item.field]" style="width: 100px;" alt="">
          </span>
          <span v-if="item.type === 'status'">
            <el-tag v-if="scope.row[item.field] === 0">禁售</el-tag>
            <el-tag v-if="scope.row[item.field] === 1">销售</el-tag>
            <el-tag v-if="scope.row[item.field] === 2">已售出</el-tag>
            <el-tag type="danger" v-if="scope.row[item.field] === 3">已删除</el-tag>
            <el-tag type="info" v-if="scope.row[item.field] === 4">已成单</el-tag>
          </span>
          <span v-if="item.type === 'audit'">
            <el-tag v-if="scope.row[item.field] === 0">审核中</el-tag>
            <el-tag type="success" v-if="scope.row[item.field] === 3">审核通过</el-tag>
            <el-tag type="danger" v-if="scope.row[item.field] === 4">审核失败</el-tag>
          </span>
          <span v-if="item.type === 'attr'">
            <el-tag v-if="scope.row[item.field] === 1">非预售</el-tag>
            <el-tag v-if="scope.row[item.field] === 2">预售</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="100">
        <template v-slot="scope">
          <el-button @click="getDetail(scope.row.tid)" type="text">查看</el-button>
          <el-button
            v-if="scope.row.shows === 3 && (scope.row.sale === 0 || scope.row.sale === 1)"
            @click="toggleItem(scope.row.tid, scope.row.sale)"
            type="text"
          >{{ scope.row.sale === 0 ? '上架' : scope.row.sale === 1 ? '下架' : '' }}
          </el-button>
          <el-button v-if="scope.row.sale === 0 || scope.row.sale === 1" @click="deleteItem(scope.row.tid)" type="text">删除</el-button>
          <el-button v-if="scope.row.sale === 0 || scope.row.sale === 1" @click="openAuditDialog(scope.row.tid)" type="text">审核</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>

    <el-dialog title="商品详情" :visible.sync="dialogVisible">
      <el-form size="mini">
        <el-form-item v-for="item in infoSchema" :key="item.field" :label="item.label">
          <span v-if="item.type === 'img'"><img width="200" :src="curModalInfo[item.field]" alt=""></span>
          <span v-else-if="item.type === 'tag'">
            <el-tag v-if="curModalInfo[item.field] === 1">非预售</el-tag>
            <el-tag v-if="curModalInfo[item.field] === 2">预售</el-tag>
          </span>
          <span v-else>{{ curModalInfo[item.field] }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="商品审核" :visible.sync="auditDialogVisible" width="400px">
      <el-radio-group v-model="auditData.peopleVerifyStatus" @change="clearReason">
        <el-radio label="PEOPLEPASS">审核通过</el-radio>
        <el-radio label="PEOPLEREJECT">审核驳回</el-radio>
      </el-radio-group>
      <el-input
        v-if="auditData.peopleVerifyStatus === 'PEOPLEREJECT'"
        style="margin-top: 20px; width: 300px;"
        type="textarea"
        :rows="2"
        placeholder="请输入驳回原因"
        v-model="auditData.rejectExplain">
      </el-input>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="audit">提交</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<style lang="scss" scoped>

</style>
