<script>
import CommonQuery from '@/components/CommonQuery'
export default {
  name: 'DigitalCollectionSettingPlatform',
  components: {
    CommonQuery
  },
  data () {
    return {
      tableData: [],
      query: {},
      querySchema: [
        {
          type: 'input',
          label: '平台名称',
          placeholder: '请输入商品名称',
          field: 'name'
        },
        {
          type: 'select',
          label: '首字母归属',
          field: 'firstLetter',
          placeholder: '请选择首字母归属',
          options: []
        },
        {
          type: 'select',
          label: '显示状态',
          field: 'showStatus',
          placeholder: '请选择显示状态',
          options: []
        },
        {
          type: 'datetimerange',
          label: '发布时间',
          field: 'creatBeginTime',
          field2: 'createEndTime',
          placeholder: '请选择发布时间'
        }
      ],
      tableSchema: [
        {
          label: '平台名称',
          field: 'name'
        },
        {
          label: '首字母归属',
          field: 'firstLetter'
        },
        {
          label: '主页地址',
          field: 'homepageUrl'
        },
        {
          label: '平台logo',
          field: 'icon',
          type: 'img'
        },
        {
          label: '创建时间',
          field: 'createAt'
        },
        {
          label: '显示状态',
          field: 'showStatus',
          type: 'status'
        },
        {
          label: '同类内权重',
          field: 'weight'
        },
        {
          label: '平台备注',
          field: 'remark'
        }
      ],
      orderStatusMap: {},
      payTypeMap: {},
      dialogVisible: false,
      infoDialogVisible: false,
      infoListDialogVisible: false,
      curModalInfo: {},
      infoSchema: [],
      dialogTitle: '',
      deliverVoucherList: [],
      infoList: [],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }
    }
  },
  computed: {
  },
  mounted () {
    this.setOptions()
    this.getList()
  },
  methods: {
    toAddPage (row) {
      this.$router.push({
        name: 'DigitalCollectionSettingAddPlatform',
        query: {
          ...row,
          id: row.id
        }
      })
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.digitalCollectionPlatformList(params)
      console.log(result)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    async getQueryOptions (dictType) {
      const { status, result } = await this.$api.getDictDataByDictType({ dictType })
      if (status.code === 0) {
        return result.dictDataListVOS.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        return []
      }
    },
    generateMap (raw) {
      return raw.reduce((acc, item) => {
        acc[item.value] = item.label
        return acc
      }, {})
    },
    async setOptions () {
      const firstLetter = await this.getQueryOptions('SC_PLATFORM_FIRST_LETTER')
      const showStatus = await this.getQueryOptions('SC_PLATFORM_SHOW_STATUS')
      this.querySchema.forEach(item => {
        if (item.field === 'firstLetter') {
          item.options = firstLetter
        } else if (item.field === 'showStatus') {
          item.options = showStatus
        }
      })
    },
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    toggleItem (platFormId, showStatus) {
      console.log(platFormId)
      const title = showStatus === 1 ? '确认隐藏' : '确认显示'
      const text = showStatus === 1 ? '平台隐藏后，用户无法看到该平台商品，同时无法发布该平台商品及创建该平台地址' : '平台显示后，用户可继续看到该平台商品，同时可发布该平台商品及创建该平台地址'
      this.$confirm(text, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionPlatformHide({ platFormId, showsStatus: showStatus === 1 ? 0 : 1 })
        if (status.code === 0) {
          this.getList()
        }
      })
    },
    deleteItem (platFormId) {
      this.$confirm('平台隐藏后，用户无法看到该平台商品，同时无法发布该平台商品及创建该平台地址，同时无法恢复历史记录', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionPlatformDelete({ platFormId })
        if (status.code === 0) {
          this.getList()
        }
      })
    }
  }
}
</script>

<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        label="序号"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column
        v-for="(item) in tableSchema"
        :key="item.field"
        :label="item.label"
        :prop="item.field"
        :width="item.width">
        <template v-if="item.type" v-slot="scope">
          <span v-if="item.type === 'img'">
            <img :src="scope.row[item.field]" style="width: 100px;" alt="">
          </span>
          <span v-if="item.type === 'status'">
            <el-tag v-if="scope.row[item.field] === 1">显示</el-tag>
            <el-tag v-if="scope.row[item.field] === 0">隐藏</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="120">
        <template #header>
          <el-button size="mini" type="primary" @click="toAddPage">添加平台</el-button>
        </template>
        <template v-slot="scope">
          <el-button @click="toAddPage(scope.row)" type="text">编辑</el-button>
          <el-button @click="toggleItem(scope.row.id, scope.row.showStatus)" type="text">
            {{ scope.row.showStatus === 1 ? '隐藏' : '显示' }}
          </el-button>
          <el-button @click="deleteItem(scope.row.id)" type="text">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>
  </d2-container>
</template>

<style lang="scss" scoped>

</style>
