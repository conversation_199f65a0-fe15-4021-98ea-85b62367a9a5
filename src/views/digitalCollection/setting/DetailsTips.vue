<template>
  <d2-container class="page">
    <div class="content">
      <el-form>
        <el-form-item label="显示设置：" required label-width="160px">
          <el-radio-group v-model="show">
            <el-radio :label="0">隐藏</el-radio>
            <el-radio :label="1">显示</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="商品详情提示信息：" required label-width="160px">
          <el-input type="textarea" v-model="info"
                    :autosize="{ minRows: 6}">
          </el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button type="primary" @click="setConfig()">提交</el-button>
      </div>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'DigitalCollectionSettingDetailsTips',
  data () {
    return {
      show: null,
      info: ''
    }
  },
  created () {
    this.getConfig()
  },
  methods: {
    async getConfig () {
      const { result } = await this.$api.getConfig({ type: 1 })
      this.show = JSON.parse(result).show
      this.info = JSON.parse(result).info
      console.log(result)
    },
    async setConfig () {
      const configStr = JSON.stringify({
        show: this.show,
        info: this.info
      })
      await this.$api.setConfig({
        type: 1,
        configStr
      })
      this.getConfig()
      this.$message.success('设置成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  margin-top: 60px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .action {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .el-form {
    width: 600px;
  }

  .el-textarea {
    width: 400px;
  }

  .el-radio-group {
    line-height: 0;
  }
}

</style>
