<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item label="平台名称:" required>
        <el-input
          v-model="form.name"
          placeholder="请输入平台名称"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="平台首字母归属:" required>
        <el-select  v-model="form.firstLetter" placeholder="请选择活动区域">
          <el-option v-for="item in firstLetter" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="平台logo:" required>
        <el-upload
          :action="action"
          :headers="token"
          list-type="picture-card"
          :on-success="handlePicSuccess"
          :on-change="handleIntroduceUploadHide"
          :on-remove="handleIntroduceRemove"
          :before-upload="beforeUpload"
          :limit="1"
          :class="{ hide: uploadHide }"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="同类权重:" required>
        <el-input
          v-model="form.weight"
          type="number"
          placeholder="请输入同类权重"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="链接类型:" required>
        <el-radio-group v-model="form.showStatus" >
          <el-radio :label="1">显示</el-radio>
          <el-radio :label="0">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="平台备注:">
        <el-input type="textarea" v-model="form.remark" style="width: 500px"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(routerBack)"
        >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit)"
        >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'DigitalCollectionSettingAddPlatform',
  data () {
    return {
      action: `${process.env.VUE_APP_BASE_URL}osscenter/adminApi/missWebSign/uploadImage`,
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      form: {
        icon: '',
        showStatus: 1
      },
      uploadHide: false,
      firstLetter: []
    }
  },
  mounted () {
    this.form = {
      ...this.form,
      ...this.$route.query
    }
    this.setOptions()
  },
  methods: {
    routerBack () {
      this.$router.back()
    },
    async getQueryOptions (dictType) {
      const { status, result } = await this.$api.getDictDataByDictType({ dictType })
      if (status.code === 0) {
        return result.dictDataListVOS.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        return []
      }
    },
    async setOptions () {
      this.firstLetter = await this.getQueryOptions('SC_PLATFORM_FIRST_LETTER')
    },

    handlePicSuccess (res, file) {
      this.form.icon = res.result.url
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      console.log(file)
      this.uploadHide = fileList.length >= 1
      this.form.icon = ''
    },
    handleIntroduceUploadHide (file, fileList) {
      this.uploadHide = fileList.length >= 1
    },
    beforeUpload (file) {
      console.log(file)
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    async submit () {
      this.$confirm('提交保存后，该入口将显示在客户端，确认保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (this.form.id) {
          const { status } = await this.$api.digitalCollectionPlatformUpdate(
            { ...this.form, platFormId: this.form.id })
          if (status.code === 0) {
            this.routerBack()
          }
        } else {
          const { status } = await this.$api.digitalCollectionPlatformAdd(this.form)
          if (status.code === 0) {
            this.routerBack()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>
