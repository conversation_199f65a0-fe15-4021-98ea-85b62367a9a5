<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData">
      <template #action-header>
        <el-button @click="toTemplatePage()" type="primary" size="mini">创建模版</el-button>
      </template>
      <template #used="scope">
        <el-button @click="getTemplateUsedRecord(scope.row.id)" type="text" size="mini">{{ scope.row.useNum }}</el-button>
      </template>
      <template #order="scope">
        <el-button @click="getTemplateOrderRecord(scope.row.id)" type="text" size="mini">{{ scope.row.orderNum }}</el-button>
      </template>
      <template #action="scope">
        <el-button @click="toTemplatePage(scope.row, 'detail')" type="text">查看</el-button>
        <el-button @click="toTemplatePage(scope.row)" type="text">编辑</el-button>
        <el-button @click="statusToggle(scope.row.id, scope.row.showStatus)" type="text">
          {{ scope.row.showStatus === 0 ? '显示' : '隐藏' }}
        </el-button>
        <el-button @click="deleteItem(scope.row.id)" type="text">删除</el-button>
      </template>
    </common-table>

    <el-pagination
      style="float: right; margin-top: 20px;"
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>
    <el-dialog
      :title="dialogTableTitle"
      :visible.sync="dialogVisible"
      width="46%">
      <common-table :table-schema="dialogTableSchema" :table-data="dialogTableData"></common-table>
      <el-pagination
        @current-change="currentDialogChange"
        layout="prev, pager, next"
        :total="dialogPage.totalCount">
      </el-pagination>
      <span slot="footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
export default {
  name: 'DigitalCollectionTemplatePublish',
  components: {
    CommonQuery,
    CommonTable
  },
  props: {},
  data () {
    return {
      dialogVisible: false, // 弹窗
      page: {
        totalCount: 0,
        pageSize: 10
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '商品模版名称：',
          placeholder: '请输入商品模版名称',
          field: 'goodsName'
        },
        {
          type: 'select',
          label: '商品属性：',
          placeholder: '请选择商品属性',
          field: 'saleType',
          options: []
        },
        {
          type: 'select',
          label: '发行平台：',
          field: 'platform',
          placeholder: '请选择发行平台',
          options: []
        },
        {
          type: 'select',
          label: '审核状态：',
          field: 'shows',
          placeholder: '请选择审核状态',
          options: []
        },
        {
          type: 'datetimerange',
          label: '创建时间',
          field: 'startTime',
          field2: 'endTime'
        }
      ],
      tableSchema: [ // 表格架构
        {
          label: '商品模板名称',
          field: 'title'
        },
        {
          label: '商品图片',
          field: 'photo',
          type: 'img',
          width: '130px'
        },
        {
          label: '发行平台',
          field: 'platformName'
        },
        {
          label: '信息介绍',
          field: 'content'
        },
        {
          label: '审核状态',
          field: 'shows',
          type: 'tag',
          tagMap: {
            0: { label: '审核中' },
            3: { label: '审核通过', tagType: 'success' },
            4: { label: '审核不通过', tagType: 'danger' }
          },
          width: '100px'
        },
        {
          label: '商品属性',
          field: 'saleType',
          type: 'tag',
          tagMap: {
            1: '非预售',
            2: '预售'
          },
          width: '80px'
        },
        {
          label: '显示状态',
          field: 'showStatus',
          type: 'tag',
          tagMap: {
            1: { label: '显示', tagType: 'success' },
            0: { label: '隐藏', tagType: 'danger' }
          },
          width: '80px'
        },
        {
          label: '使用次数',
          field: 'useNum',
          slot: 'used',
          width: '80px'
        },
        {
          label: '模版成单',
          field: 'orderNum',
          slot: 'order',
          width: '80px'
        },
        {
          label: '创建时间/编辑时间',
          field: 'updatedAt',
          width: '180px'
        },
        {
          label: '权重',
          field: 'weight',
          width: '80px'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '140px'
        }
      ],
      tableData: [], // 表格数据
      dialogType: '', // 弹窗类型 as 'used' | 'order'
      currentTemplateId: '', // 当前模版id
      dialogTableData: [], // 弹窗表格数据
      dialogPage: {
        totalCount: 0,
        pageSize: 5
      } // 分页数据
    }
  },
  computed: {
    dialogTableSchema () {
      return this.dialogType === 'order'
        ? [
          {
            label: '下单时间',
            field: 'buyTime',
            width: '160px'
          },
          {
            label: '买家昵称',
            field: 'buyer'
          },
          {
            label: '卖家昵称',
            field: 'seller'
          },
          {
            label: '价格',
            field: 'price'
          },
          {
            label: '使用时间',
            field: 'useTime',
            width: '160px'
          },
          {
            label: '订单状态',
            field: 'orderStatus',
            type: 'tag',
            width: '120px',
            tagMap: {
              10: { label: '订单已创建' },
              20: { label: '待发货', tagType: 'warning' },
              30: { label: '已发货', tagType: 'warning' },
              40: { label: '已收货', tagType: 'success' },
              50: { label: '已取消', tagType: 'danger' },
              55: { label: '申请取消中', tagType: 'danger' },
              60: { label: '已取消', tagType: 'info' }
            }
          }
        ]
        : [
          {
            label: '用户昵称',
            field: 'nickname'
          },
          {
            label: '使用时间',
            field: 'useTime'
          },
          {
            label: '商品状态',
            field: 'goodsStatus',
            type: 'tag',
            tagMap: {
              0: { label: '已下架', tagType: 'danger' },
              1: { label: '销售中' },
              2: { label: '已售出', tagType: 'warning' },
              3: { label: '已删除', tagType: 'info' }
            }
          }
        ]
    },
    dialogTableTitle () {
      return this.dialogType === 'order'
        ? `模版成单信息（${this.dialogTableData.length}单）`
        : `模版使用信息（${this.dialogTableData.length}次）`
    }
  },
  mounted () {
    this.getList()
    this.setOptions()
  },
  methods: {
    // 过滤查询
    onQueryChange (data) {
      this.query = data
      this.getList(true)
    },
    // 分页改变
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // dialog 分页改变
    currentDialogChange (value) {
      this.dialogPage.pageNum = value
      if (this.dialogType === 'order') {
        this.getTemplateOrderRecord(this.currentTemplateId)
      } else {
        this.getTemplateUsedRecord(this.currentTemplateId)
      }
    },
    // 跳转到模版页面
    toTemplatePage (item = {}, showType) {
      this.$router.push({
        name: 'DigitalCollectionTemplatePublishEdit',
        query: { templateId: item.id, showType }
      })
    },
    // 获取列表
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.digitalCollectionTemplatePublishList(params)
      if (status.code === 0) {
        this.tableData = result.list.map(item => ({
          ...item,
          updatedAt: item.updatedAt.split('.')[0]
        }))
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    // 获取模版使用记录
    async getTemplateUsedRecord (templateId) {
      this.dialogVisible = true
      this.dialogType = 'used'
      this.currentTemplateId = templateId
      const { result } = await this.$api.digitalCollectionTemplatePublishUsedRecord({ ...this.dialogPage, templateId })
      this.dialogTableData = result.list.map(item => ({
        ...item,
        useTime: item.useTime.split('.')[0]
      }))
      this.dialogPage.totalCount = result.totalCount
      this.dialogPage.pageSize = result.pageSize
      this.dialogPage.pageCount = result.pageCount
    },
    // 获取模版成单记录
    async getTemplateOrderRecord (templateId) {
      this.dialogVisible = true
      this.dialogType = 'order'
      this.currentTemplateId = templateId
      const { result } = await this.$api.digitalCollectionTemplatePublishOrderRecord({ ...this.dialogPage, templateId })
      this.dialogTableData = result.list.map(item => ({
        ...item,
        buyTime: item.buyTime.split('.')[0],
        useTime: item.useTime.split('.')[0]
      }))
      this.dialogPage.totalCount = result.totalCount
      this.dialogPage.pageSize = result.pageSize
      this.dialogPage.pageCount = result.pageCount
    },
    async getQueryOptions (dictType) { // 获取查询条件
      const { status, result } = await this.$api.getDictDataByDictType({ dictType })
      if (status.code === 0) {
        return result.dictDataListVOS.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        return []
      }
    },
    async setOptions () { // 设置查询条件
      const audit = await this.getQueryOptions('SC_GOODS_LIST_ADUIT')
      const attr = await this.getQueryOptions('SC_GOODS_LIST_SALE_TYPE')
      const { result } = await this.$api.digitalCollectionPlatformList({ pageSize: 1000 })
      this.querySchema.forEach(item => {
        switch (item.field) {
          case 'saleType':
            item.options = attr
            break
          case 'shows':
            item.options = audit
            break
          case 'platform':
            item.options = result.list.map(item => ({
              label: item.name,
              value: item.id
            }))
            break
          default:
            break
        }
      })
    },
    // 显示/隐藏切换
    statusToggle (templateId, show) {
      const title = show === 0 ? '模版显示' : '模版隐藏'
      const text = show === 0 ? '模板开启显示后，用户可继续使用该模板' : '模板隐藏后，用户将无法看到该模板，已经使用该模板发布的用户不受影响'
      this.$confirm(text, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionTemplatePublishStatusToggle({
          templateId, show: show === 1 ? 0 : 1
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    // 删除数据
    async deleteItem (templateId) {
      this.$confirm('模板删除后，用户将无法再使用该模板发布商品，已经使用该模板发布的用户不受影响', '模板删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionTemplatePublishDelete({ templateId })
        this.$message.success(status.msg)
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
