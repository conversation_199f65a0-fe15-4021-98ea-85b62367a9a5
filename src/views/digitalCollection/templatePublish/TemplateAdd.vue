<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px"></common-form>
  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import { mapActions } from 'vuex'

export default {
  name: 'DigitalCollectionTemplatePublishEdit',
  components: {
    CommonForm
  },
  data () {
    return {
      isDetail: false, // 详情
      templateId: null, // 活动编号
      formData: {
        photo: []
      },
      formSchema: [
        {
          type: 'input',
          label: '商品名称：',
          placeholder: '请输入商品名称',
          field: 'title',
          rules: [{ required: true, message: '请输入商品名称', trigger: 'blur' }]
        },
        {
          type: 'img',
          label: '商品图片：',
          placeholder: '请选择商品图片',
          field: 'photo',
          limit: 5,
          rules: [{ required: true, message: '请选择商品图片', trigger: 'change' }]
        },
        {
          type: 'select',
          label: '商品平台：',
          placeholder: '请选择商品平台',
          field: 'platform',
          options: [],
          rules: [{ required: true, message: '请选择商品平台', trigger: 'blur' }]
        },
        {
          type: 'textarea',
          field: 'content',
          label: '商品介绍：',
          maxlength: 300,
          rules: [{ required: true, message: '请输入商品介绍', trigger: 'blur' }]
        },
        {
          type: 'select',
          label: '商品属性：',
          placeholder: '请选择商品属性',
          field: 'saleType',
          options: [
            { label: '非预售', value: 1 },
            { label: '预售', value: 2 }
          ],
          rules: [{ required: true, message: '请选择商品属性', trigger: 'blur' }]
        },
        {
          type: 'date',
          label: '预售时间：',
          placeholder: '请选择预售时间',
          field: 'preSaleTime',
          rules: [{ required: true, message: '请选择预售时间', trigger: 'blur' }],
          show: {
            relationField: 'saleType',
            value: [2]
          },
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() || time.getTime() > (Date.now() + 86400000 * 30)
            }
          }
        },
        {
          type: 'date',
          label: '发行时间：',
          placeholder: '请选择发行时间',
          field: 'releaseTime'
        },
        {
          type: 'input',
          label: '商品 ID：',
          placeholder: '请输入商品 ID',
          field: 'releaseId'
        },
        {
          type: 'input',
          label: '发行方：',
          placeholder: '请输入发行方',
          field: 'releaseName'
        },
        {
          type: 'input',
          label: '技术支持：',
          placeholder: '请输入技术支持',
          field: 'techSupport'
        },
        {
          type: 'number-input',
          label: '发行数量：',
          placeholder: '请输入发行数量',
          field: 'num'
        },
        {
          type: 'input',
          label: '发行编号：',
          placeholder: '请输入发行编号',
          field: 'releaseNo'
        },
        {
          type: 'input',
          label: '其他信息：',
          placeholder: '请输入其他信息',
          field: 'note'
        },
        {
          type: 'number-input',
          label: '权重排序：',
          placeholder: '请输入权重排序',
          field: 'weight',
          rules: [{ required: true, message: '请输入权重排序', trigger: 'blur' }]
        },
        {
          type: 'action'
        }
      ]
    }
  },
  mounted () {
    const { showType, templateId } = this.$route.query
    this.isDetail = showType === 'detail'
    this.templateId = templateId
    templateId && this.getDetail()
    this.getPlatformList()
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    routerBack () {
      const { fullPath } = this.$route
      this.close({ tagName: fullPath })
      this.$router.back()
    },
    async getDetail () {
      const { status, result } = await this.$api.digitalCollectionTemplatePublishDetail({ templateId: this.templateId })
      if (status.code === 0) {
        this.formData = {
          ...result,
          photo: result.photo.split(',')
        }
      }
      console.log(this.formData)
    },
    async getPlatformList () { // 获取平台列表
      const { result } = await this.$api.digitalCollectionPlatformList({ pageSize: 1000 })
      this.formSchema.find(item => item.field === 'platform').options = result.list.map(item => ({ label: item.name, value: item.id }))
    },
    async submit () {
      console.log(this.formData)
      const data = {
        ...this.formData,
        photo: this.formData.photo.join(),
        releaseTime: this.formData.releaseTime ? `${this.formData.releaseTime}` : undefined,
        preSaleTime: this.formData.preSaleTime ? `${this.formData.preSaleTime}` : undefined
      }
      data.templateId = this.templateId
      console.log(data)
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        if (this.templateId) {
          const { status } = await this.$api.digitalCollectionTemplatePublishEdit(data)
          if (status.code === 0) {
            this.routerBack()
          }
        } else {
          const { status } = await this.$api.digitalCollectionTemplatePublishAdd(data)
          if (status.code === 0) {
            this.routerBack()
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding-top: 80px;
}
</style>
