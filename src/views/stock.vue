<template>
    <div class="container">
        <div class="img_title">
            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374776145693204480.png" alt="">
        </div>
        <div class="title-container-card">
            <span class="title">{{ t('stock.title') }}</span>
        </div>
        <div class="stock_search_bar">
            <div class="stock_search_input">
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377300822835683328.png" alt="" srcset="">
                <input type="text" :placeholder="t('stock.search_placeholder')">
            </div>
            <div class="stock_search_btn">
                {{ t('stock.search_btn') }}
            </div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('stock.search_result') }}</span>
        </div>
        <div class="stock_info_container">
            <div class="stock_info_header">
                <div class="stock_info_left">
                    <div class="stock_name">
                        {{ t('stock.stock_info') }}
                    </div>
                    <div class="stock_price_row">
                        <span class="stock_price">102.640</span>
                        <div class="stock_price_change">
                            <span class="stock_price_change_num green">+1.825</span>
                            <span class="stock_price_change_percent green">+1.75%</span>
                        </div>
                    </div>
                    <div class="stock_time_tabs">
                        <span v-for="(tab, idx) in timeTabs" :key="tab.value"
                            :class="['_tab', {active: currentTab === tab.value}]" @click="selectTab(tab.value)">{{
                            t(`stock.time_tabs_${tab.value}`) }}</span>
                    </div>
                </div>
            </div>
            <div class="stock_chart_container">
                <div ref="stockChartRef" class="stock_chart"></div>
            </div>
        </div>

        <div class="title-container-card mt_300">
            <span class="title">{{ t('stock.hot_stocks') }}</span>
        </div>
        <div class="hv_list">
            <ul class="header">
                <li class="icon">
                    <p>{{ t('stock.name') }}</p>
                </li>
                <li class="parities">{{ t('stock.price') }}</li>
                <li class="service_charge">{{ t('stock.change') }}</li>
            </ul>
            <ul class="table mb_206">
                <li v-for="(item,index) in compareList" :key="index"
                    :class="['cursor_pointer',{'active': currentIndex === index}]" @click="checkStock(index)">
                    <div class="icon">
                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377297668236730368.png" alt="" srcset="">
                        <span>1SOL/USDT</span>
                    </div>
                    <div class="parities">
                        {{item.rate}}
                    </div>
                    <div class="service_charge green">
                        +69.27%
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch } from 'vue'
    import { useI18n } from "vue-i18n";
    import * as echarts from 'echarts'
    const { locale, t } = useI18n();
    const activeIndex = ref(0);
    const currentIndex = ref(0);
    const compareList = [
        { name: 'WISE', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1288', fee: '6.44USD' },
        { name: 'BMO', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1256', fee: '37.89USD' },
        { name: 'CITIBANK', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1256', fee: '50.00USD' },
        { name: 'CIBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1252', fee: '65.00USD' },
        { name: 'OCBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1251', fee: '65.00USD' },
        { name: 'RBC', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1254', fee: '115.00USD' },
        { name: 'OFX', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/ima ge/1374815832294121472.png', rate: '0.1252', fee: '100.00USD' },
        { name: 'TD', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1253', fee: '230.00USD' },
        { name: 'PayPal', logo: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374815832294121472.png', rate: '0.1205', fee: '38.99USD' }
    ]
    const advantages = computed(() => [
        {
            img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376617913472606208.png',
            title: t("realTimeER.advantage1_title"),
            desc: t("realTimeER.advantage1_desc")
        },
        {
            img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376617992510070784.png',
            title: t("realTimeER.advantage2_title"),
            desc: t("realTimeER.advantage2_desc")
        },
        {
            img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376618091109769216.png',
            title: t("realTimeER.advantage3_title"),
            desc: t("realTimeER.advantage3_desc")
        },
        {
            img: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1376618144067051520.png',
            title: t("realTimeER.advantage4_title"),
            desc: t("realTimeER.advantage4_desc")
        }
    ])

    const handleClick = (index) => {
        activeIndex.value = index
    }
    const checkStock = (index) => {
        currentIndex.value = index
    }
    watchEffect(() => {
        // features.value.forEach((item) => {
        //     item.title = t(item.titleKey); // 监听语言变化，自动更新 title
        // });
        console.log("语言切换为：", locale.value);
        localStorage.setItem('lang', locale.value)

    });

    const stockChartRef = ref(null)
    let chartInstance = null

    const stockChartData = ref({
        xData: [
            '05:45', '06:10', '06:35', '07:00', '07:25', '07:50', '08:15', '08:40', '14:25', '14:50', '15:15', '15:40', '16:05', '16:30', '16:55', '17:20', '17:45', '18:10', '18:35', '19:00', '19:25', '19:50', '20:15', '20:40'
        ],
        yData: [102, 98, 105, 95, 110, 90, 115, 100, 120, 85, 125, 80, 130, 75, 135, 140, 120, 145, 110, 150, 100, 155, 95, 160]
    })
    
    const renderChart = () => {
        if (!stockChartRef.value) return
        if (!chartInstance) {
            chartInstance = echarts.init(stockChartRef.value)
        }
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: '#222',
                borderColor: '#222',
                borderWidth: 1,
                borderRadius: 16,
                textStyle: { color: 'rgba(255,255,255,0.5)', fontSize: 14 },
                formatter: function (params) {
                    // params[0] 是当前点
                    const time = params[0].axisValue;
                    const value = params[0].data;
                    return `日期：${time}<br/>收盘价：${value}`;
                },
                extraCssText: 'box-shadow:none;padding:8px 14px;line-height:1.6;',
                axisPointer: {
                    lineStyle: {
                        color: 'rgba(255, 130, 163, 1)',
                        width: 1,
                        type: 'dashed'
                    }
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: stockChartData.value.xData,
                axisLine: { show: false },
                axisLabel: { color: 'rgba(255,255,255,0.5)' }
            },
            yAxis: {
                type: 'value',
                position: 'right',
                axisLine: { lineStyle: { color: '#fff' } },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } },
                axisLabel: { color: 'rgba(255,255,255,0.5)' }
            },
            series: [
                {
                    data: stockChartData.value.yData,
                    type: 'line',
                    smooth: false,
                    showSymbol: false,
                    lineStyle: { color: '#EF88A3', width: 2 },
                    itemStyle: { color: '#EF88A3' },
                    emphasis: {
                        focus: 'series',
                        itemStyle: {
                            color: 'rgba(255, 130, 163, 1)',
                            borderColor: 'rgba(79, 48, 56, 1)',
                            borderWidth: 10
                        },
                        symbolSize: 14
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(239,136,163,0.3)' },
                            { offset: 1, color: 'rgba(239,136,163,0)' }
                        ])
                    }
                }
            ]
        }
        chartInstance.setOption(option)
    }

    onMounted(() => {
        renderChart()
        window.addEventListener('resize', () => {
            chartInstance && chartInstance.resize()
        })
    })

    const timeTabs = [
        { label: t('stock.time_tabs_24h'), value: '24h' },
        { label: t('stock.time_tabs_7d'), value: '7d' },
        { label: t('stock.time_tabs_1m'), value: '1m' },
        { label: t('stock.time_tabs_3m'), value: '3m' },
        { label: t('stock.time_tabs_1y'), value: '1y' }
    ]
    const currentTab = ref('24h')
    const selectTab = (val) => {
        currentTab.value = val
        // 这里可以触发图表数据切换
    }

    // tools 下拉菜单
    const showTools = ref(false)
    const toolsList = [
        { label: t('stock.tools.export_img'), value: 'export_img' },
        { label: t('stock.tools.download_data'), value: 'download_data' },
        { label: t('stock.tools.custom_indicator'), value: 'custom_indicator' }
    ]
    const toggleTools = () => {
        showTools.value = !showTools.value
    }
    const selectTool = (val) => {
        showTools.value = false
        // 这里可以根据 val 做不同操作
    }
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        margin-bottom: 79px;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans';

        .img_title {
            img {
                width: 271px;
                height: 271px;
            }
        }

        .cart_div {
            width: 1194px;
            background-color: #222222;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 0 auto;
            border-radius: 10px;
            padding: 28px;
            margin-bottom: px(207);

            .input_black {
                display: flex;
                justify-content: space-between;
                justify-items: flex-end;

                .input_div {
                    p {
                        font-size: px(24);
                        text-align: left;
                        color: rgba(255, 255, 255, 0.6);
                        margin: 0px;
                        margin-bottom: 15px;
                        font-weight: 400;
                    }

                    .black {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border: 1px solid #fff;
                        border-radius: 15px;
                        width: 503px;
                        padding: 13px 12px 13px 17px;

                        .left_name {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            font-size: 17px;
                            font-weight: 600;

                            .tx {
                                width: 33px;
                                height: 33px;
                                margin-right: 14px;
                                margin-left: 0px;
                            }

                            img {
                                width: 18px;
                                height: 18px;
                                margin-left: 12px;
                            }
                        }

                        .input {
                            width: 100%;
                            height: 100%;

                            input {
                                width: 100%;
                                height: 100%;
                                border: none;
                                background-color: transparent;
                                font-size: 17px;
                                font-weight: 600;
                                color: #fff;
                                text-align: right;
                            }
                        }
                    }

                }

                .icon {
                    display: flex;
                    align-items: flex-end;
                    padding-bottom: 14px;

                    img {
                        width: px(54);
                        height: px(54);
                    }
                }
            }

            .button_text {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: px(31);

                >div {
                    font-size: px(24);
                    font-weight: 600;
                    color: #fff;
                }

                button {
                    width: px(180);
                    height: px(59);
                    border-radius: px(29);
                    color: #000000;
                    font-size: px(20);
                    background-color: #FF95B2;
                    outline: none;
                }
            }
        }

        .hv_list {
            width: 1194px;
            margin: 0 auto;

            .header {
                display: flex;
                justify-content: space-between;
                margin-bottom: px(40);
                padding: 0 px(25);

                li {
                    font-size: px(24);
                    color: #EF88A3;

                    &.icon {
                        width: 384px;
                        text-align: left;

                        p {
                            margin: 0;
                            margin-left: px(107);
                        }
                    }

                    &.parities {
                        text-align: center;
                        width: 450px;
                    }

                    &.service_charge {
                        text-align: center;
                        width: 359px;
                    }
                }
            }
        }

        .table {
            width: 1194px;

            li {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #212121;
                height: px(100);
                border-radius: px(16);
                border: 1px solid #4B4B4B;
                margin-bottom: px(15);
                padding: px(25);
                font-size: px(20);

                &.active {
                    background-color: rgba(239, 136, 163, 0.1);
                    border: 1px solid rgba(239, 136, 163, 1);
                }

                .icon {
                    width: 384px;
                    text-align: left;
                    height: px(70);
                    line-height: px(70);
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;

                    span {
                        font-size: px(24);
                        color: #fff;
                    }

                    img {
                        width: px(44);
                        margin-left: px(107);
                        margin-right: px(10);
                    }
                }

                .parities {
                    text-align: center;
                    width: 450px;
                    border-right: 1px solid rgba(255, 255, 255, 0.2);
                    border-left: 1px solid rgba(255, 255, 255, 0.2);
                    flex: 1;
                    height: px(70);
                    line-height: px(70);
                    font-size: px(24);
                    color: #fff;

                    &.active {
                        border-right: 1px solid rgba(239, 136, 163, 0.2);
                        border-left: 1px solid rgba(239, 136, 163, 0.2)
                    }
                }

                .service_charge {
                    text-align: center;
                    width: 359px;
                    height: px(70);
                    line-height: px(70);
                    font-size: px(24);

                    &.red {
                        color: #EF88A3;
                    }

                    &.green {
                        color: #04A431;
                    }
                }
            }
        }

        .stock_search_bar {
            width: 1194px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: px(100);
            border-radius: px(16);
            border: 1px solid rgba(255, 255, 255, 1);
            padding: px(20) px(25);

            .stock_search_input {
                display: flex;
                align-items: center;
                justify-content: center;
                width: px(1000);
                height: px(60);
                border-radius: px(16);

                img {
                    width: px(28);
                    height: px(28);
                    margin-right: px(10);
                }

                input {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background-color: transparent;
                    color: #fff;
                    font-size: px(24);
                }

                input::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .stock_search_btn {
                width: px(180);
                height: px(60);
                border-radius: px(30);
                background-color: #EF88A3;
                color: #222;
                font-size: px(24);
                font-weight: 600;
                text-align: center;
                line-height: px(60);
                cursor: pointer;

            }
        }
    }

    .stock_chart_container {
        width: 1194px;
        margin: 40px auto 0 auto;
        border-radius: 16px;
        padding: 24px 0;
    }

    .stock_chart {
        width: 100%;
        height: 340px;
        min-height: 240px;
        background: transparent;
    }

    .stock_info_container {}

    .stock_info_header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 1194px;
        margin: 0 auto px(24) auto;

        .stock_info_left {
            .stock_name {
                color: #fff;
                font-size: px(24);
                font-weight: 500;
                margin-bottom: px(12);
                text-align: left;
            }

            .stock_price_row {
                display: flex;
                align-items: baseline;
                margin-bottom: px(12);

                .stock_price {
                    color: #fff;
                    font-size: px(48);
                    font-weight: bold;
                    margin-right: px(24);
                }

                .stock_price_change {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .stock_price_change_num,
                    .stock_price_change_percent {
                        font-size: px(24);
                        font-weight: 600;

                        &.green {
                            color: #04A431;
                        }

                        &.red {
                            color: #EF88A3;
                        }
                    }
                }
            }

            .stock_time_tabs {
                display: flex;
                gap: px(12);

                ._tab {
                    padding: px(2) px(16);
                    border-radius: px(12);
                    background: none;
                    color: #fff;
                    font-size: px(24);
                    cursor: pointer;
                    border: none;
                    transition: background 0.2s, color 0.2s;

                    &.active {
                        background: #EF88A3;
                        color: #222;
                    }
                }
            }
        }

        .stock_info_tools {
            position: relative;

            .tools_dropdown {
                display: flex;
                align-items: center;
                gap: px(4);
                background: #232026;
                color: #fff;
                border-radius: px(8);
                padding: px(8) px(16);
                cursor: pointer;
                font-size: px(24);
                user-select: none;
                border: 1px solid rgba(255, 255, 255, 0.1);

                svg {
                    margin-left: px(4);
                    transition: transform 0.2s;
                }
            }

            .tools_menu {
                position: absolute;
                top: px(40);
                right: 0;
                background: #232026;
                border-radius: px(8);
                box-shadow: 0 px(2) px(8) rgba(0, 0, 0, 0.15);
                min-width: px(120);
                z-index: 10;

                .tools_menu_item {
                    padding: px(10) px(20);
                    color: #fff;
                    cursor: pointer;
                    font-size: px(15);

                    &:hover {
                        background: #EF88A3;
                        color: #222;
                    }
                }
            }
        }
    }
</style>