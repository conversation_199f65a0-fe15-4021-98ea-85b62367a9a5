<template>
  <d2-container class="page" style="background-color: #ffffff">
    <el-descriptions
      title="个人信息"
      :column="1"
      border
      size="medium  "
      style="width: 1000px; margin: 0px auto"
    >
      <el-descriptions-item label="ID">{{ list.id }}</el-descriptions-item>
      <el-descriptions-item label="用户名">{{
        list.username
      }}</el-descriptions-item>
      <el-descriptions-item label="真实姓名">{{
        list.realName
      }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{
        list.mobphone
      }}</el-descriptions-item>ß
      <el-descriptions-item label="邮箱">{{ list.email }}</el-descriptions-item>
      <el-descriptions-item label="是否在职">
        <el-tag v-if="list.employStatus == 'NORMAL'" type="info">正常</el-tag>
        <el-tag v-if="list.employStatus == 'DIMISSION'" type="danger"
          >离职</el-tag
        >
        <el-button
          type="success"
          style="margin-left: 60px"
          v-if="list.orderStatus == 0"
          @click="check(1)"
          >通过
        </el-button>
        <el-button
          type="danger"
          style="margin-left: 20px"
          v-if="list.orderStatus == 0"
          @click="check(4)"
          >拒绝
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="是否删除">
        <el-tag v-if="list.deleted == 0" type="info">未删除</el-tag>
        <el-tag v-if="list.deleted == 1" type="danger">已删除</el-tag>
        <el-button
          type="success"
          style="margin-left: 60px"
          v-if="list.orderStatus == 0"
          @click="check(1)"
          >通过
        </el-button>
        <el-button
          type="danger"
          style="margin-left: 20px"
          v-if="list.orderStatus == 0"
          @click="check(4)"
          >拒绝
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="备注">暂无</el-descriptions-item>
      <el-descriptions-item label="修改密码"
        ><span id="biao1"></span>

        <el-button type="text"  @click="nav_modify"
          >去修改</el-button
        >
        <!-- <el-button type="text"  v-if="scope.row.orderStatus==0">审核</el-button> -->
      </el-descriptions-item>
      <el-descriptions-item label="修改个人信息"
        ><span id="biao1"></span>
        <el-button type="text"  @click="modify_information"
          >去修改</el-button
        >
        <!-- <el-button type="text"  v-if="scope.row.orderStatus==0">审核</el-button> -->
      </el-descriptions-item>
    </el-descriptions>
  </d2-container>
</template>

<script>
export default {
  name: 'user_details',
  data () {
    return {
      list: [],
      dialogVisible: false,
      logisticsList: [],
      formInline: {
        logistics: '',
        logisticsNo: ''
      }
    }
  },
  mounted () {
    this.get(this.$route.query.type, this.$route.query.id)
    // this.getLogistics()
  },
  methods: {
    nav_modify () {
      this.$router.push({
        name: 'modify'
      })
    },
    async get (e, i) {
      console.log(e, i)
      if (e === 'user') {
        const res = await this.$api.showAdminUserInfo()
        this.list = res.result
      } else if (e === 'item') {
        console.log('别人的')
      }
    },
    // 修改用户信息
    modify_information (e) {
      console.log(e.id)
      this.$router.push({
        name: 'information',
        query: {
          id: this.list.id
        }
      })
    },

    copyUrl2 () {
      // 其实逻辑是执行了把复制目标赋值给创建的input的value，使用选中value执行复制 复制完然后移除input的原理
      var Url2 = document.getElementById('biao1').innerText // 获取span框的文本内容
      console.log(Url2)
      var input = document.createElement('input') // 创建input标签，只有input标签选中可以
      input.setAttribute('id', 'copyInput') // 给input一个id属性
      input.setAttribute('value', Url2) // 给input一个value属性，属性值是变量span的文本
      document.getElementsByTagName('body')[0].appendChild(input) // body里面添加input实体
      document.getElementById('copyInput').select() // 使用js去通过id找到并执行input实体的全部选中
      document.execCommand('Copy') // 原生copy方法执行浏览器复制命令
      document.getElementById('copyInput').remove() // 为避免下次页面添加copy方法 所以这里copy完之后要移除input实体
      this.$message({
        type: 'success',
        message: '复制成功'
      })
    }
  }
}
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
