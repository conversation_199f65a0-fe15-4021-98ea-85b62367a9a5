<template>
  <d2-container class="page" style="background-color:#FFFFFF;">
    <el-descriptions v-if="!show" title="作品详情" :column="1" border size="medium  " style="width:1000px;margin:0px auto;">
      <el-descriptions-item label="作品ID">{{ list.worksId }}</el-descriptions-item>
      <el-descriptions-item label="作品tid">{{ list.worksTid }}</el-descriptions-item>
      <el-descriptions-item label="作品名称">{{ list.worksName }}</el-descriptions-item>
      <el-descriptions-item label="发布者">{{ list.publisher }}</el-descriptions-item>
      <el-descriptions-item label="版本数量">{{ list.worksVersionNum }}</el-descriptions-item>
      <el-descriptions-item label="作品封面图">
        <div style="width:100%;" @click='ddd()'>
          <el-image style="width: 100px; height: 100px" :src="list.worksTypeVO.worksCover">
          </el-image>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="创作历程">
        <div  @click='showCreativeProcessDialog()'>
          <div class="tips">展示属性：{{list['creativeProcessOpen'] ? '' : '不' }}公开展示</div>
          <el-image fit="cover" :src="item" v-for="(item,index) in list['creativeProcessList']" :key="index" style="width: 16%;margin-right: 20px"></el-image>
        </div>
      </el-descriptions-item>
      creativeProcessList
      <el-descriptions-item label="作品音视频">
        <div style="width:100%;">
          <!-- <video
             ref="videoPlay"
             controls
             width="200"
             autoplay
          >
           您的浏览器暂不支持播放该视频，请升级至最新版浏览器。
           <source :src="list.worksTypeVO.worksDetail" />
          </video> -->
          <video class="video-js" controls preload="auto" :src="list.worksTypeVO.worksDetail">

          </video>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="作品类型">
        <el-tag v-if="list.worksTypeVO.worksType==0" type="info">图片</el-tag>
        <el-tag v-if="list.worksTypeVO.worksType==12" type="danger">音频</el-tag>
        <el-tag v-if="list.worksTypeVO.worksType==11" type="danger">视频</el-tag>
        <el-tag v-if="list.worksTypeVO.worksType==13" type="danger">3D静态</el-tag>
        <el-tag v-if="list.worksTypeVO.worksType==14" type="danger">3D动态</el-tag>
        <el-button v-if="list.worksTypeVO.worksType==13 || list.worksTypeVO.worksType==14 " type="text"
                    @click="nav_3DpreviewObj">预览
        </el-button>
      </el-descriptions-item>
      <el-descriptions-item label="作品属性">
        <el-tag v-if="list.materialObject.worksProperty==0" type="info">非实物作品</el-tag>
        <el-tag v-if="list.materialObject.worksProperty==1" type="info">实物作品</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="作品规格">{{ list.worksTypeVO.worksModels }}</el-descriptions-item>
      <el-descriptions-item label="是否装潢">
        <el-tag v-if="list.materialObject.worksModels==0" type="info">未装潢</el-tag>
        <el-tag v-if="list.materialObject.worksModels==1" type="info">已装潢</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="作品合影">
        <div style="width:100%;">
          <el-image style="width: 100px; height: 100px" :src="list.materialObject.groupPhoto">
          </el-image>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="关于实物的其他描述">{{ list.materialObject.otherDesc }}</el-descriptions-item>
      <el-descriptions-item label="作品标签">
        <el-tag v-for="(item,index) in list.worksTags" type="info" :key="index">{{ item }}</el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="价格">{{ list.worksPrice }}</el-descriptions-item>
      <el-descriptions-item label="铸造时间">{{ list.castTime }}</el-descriptions-item>
      <el-descriptions-item label="作品简介">{{ list.worksIntro }}</el-descriptions-item>
      <el-descriptions-item label="作品描述">{{ list.worksDesc }}</el-descriptions-item>
      <el-descriptions-item label="艺术家介绍">{{ list.artistIntro }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions v-else title="系列详情" :column="1" border size="medium  " style="width:1000px;margin:0px auto;">
      <el-descriptions-item label="系列ID">{{ list.seriesId }}</el-descriptions-item>
      <el-descriptions-item label="系列名称(修改后)">{{ list.name }}</el-descriptions-item>
      <el-descriptions-item label="系列名称(修改前)">{{ list.lastEdit.name }}</el-descriptions-item>
      <el-descriptions-item label="创建者">{{ list.nickname }}</el-descriptions-item>
      <el-descriptions-item label="作品封面图(修改后)">
        <div style="width:100%;" @click='ccc(1)'>
          <el-image style="width: 100px; height: 100px" :src="list.cover">
          </el-image>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="作品封面图(修改前)">
        <div style="width:100%;" @click='ccc(2)'>
          <el-image style="width: 100px; height: 100px" :src="list.lastEdit.cover">
          </el-image>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="系列简介(修改后)">{{ list.content }}</el-descriptions-item>
      <el-descriptions-item label="系列简介(修改前)">{{ list.lastEdit.content }}</el-descriptions-item>
      <el-descriptions-item label="系列创建时间">{{ list.createAt }}</el-descriptions-item>
      <el-descriptions-item label="系列编辑时间">{{ list.lastEditTime }}</el-descriptions-item>
      <el-descriptions-item label="用户邮箱">{{ list.email }}</el-descriptions-item>
      <el-descriptions-item label="系列类型">
        <el-tag v-if="list.mold==0" type="info">默认系列</el-tag>
        <el-tag v-if="list.mold==1" type="info">普通系列</el-tag>
        <el-tag v-if="list.mold==2" type="info">实物系列</el-tag>
        <el-tag v-if="list.mold==3" type="info">盲盒系列</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="自定义标签">{{ list.selfTags }}</el-descriptions-item>
      <el-descriptions-item label="系列标签">{{ list.tags }}</el-descriptions-item>
      <el-descriptions-item label="是否包含实物">
        <el-tag v-if="list.isReal==0" type="info">不包含实物</el-tag>
        <el-tag v-if="list.isReal==1" type="info">包含实物</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="发售形式(盲盒)">
        <el-tag v-if="list.saleType==1" type="info">即时发售</el-tag>
        <el-tag v-if="list.saleType==2" type="info">定时发售</el-tag>
        <el-tag v-if="list.saleType==3" type="info">手动发售</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="预售时间">{{ list.salePreTime }}</el-descriptions-item>
      <el-descriptions-item label="发售时间">{{ list.saleTime }}</el-descriptions-item>
    </el-descriptions>
    <el-dialog v-if="!show" title="" :visible.sync="isimgDelete" width="50%">
      <div style="width:100%;">
        <el-image style="width: 500px;" :src="list.worksTypeVO.worksCover">
        </el-image>
      </div>
    </el-dialog>
    <el-dialog v-else title="" :visible.sync="isimgDelete" width="50%">
      <div style="width:100%;">
        <el-image style="width: 500px;" :src="src">
        </el-image>
      </div>
    </el-dialog>
    <el-dialog title="创作历程" :visible.sync="creativeProcessDialog" width="1000px"  class="creativeProcessDialog">
      <div class="tips">展示属性：{{list['creativeProcessOpen'] ? '' : '不' }}公开展示</div>
      <div class="content">
        <el-image fit="cover" :src="item" v-for="(item,index) in list['creativeProcessList']" :key="index"></el-image>
        <!--        <div style="margin-left: 20px">-->
        <!--          <p>作品简介：{{ goodsSynopsis }}</p>-->
        <!--          <p>作品描述：{{ goodsDesc }}</p>-->
        <!--        </div>-->
      </div>
    </el-dialog>

  </d2-container>
</template>

<script>
export default {
  name: 'works_details',
  data () {
    return {
      list: [],
      dialogVisible: false,
      logisticsList: [],
      formInline: {
        logistics: '',
        logisticsNo: ''
      },
      isimgDelete: false,
      show: false, // true--系列  false--作品
      src: '',
      creativeProcessDialog: false
    }
  },
  mounted () {
    if (this.$route.query.type + '' === '1') {
      this.getSeriesDetails(this.$route.query.id)
      this.show = true
    } else {
      this.get(this.$route.query.type, this.$route.query.id)
    }

    // this.getLogistics()
  },
  methods: {
    showCreativeProcessDialog () {
      this.creativeProcessDialog = true
    },
    // 大图
    ddd () {
      this.isimgDelete = true
    },
    ccc (e) {
      if (e + '' === '1') {
        this.src = this.list.cover
      } else {
        this.src = this.list.lastEdit.cover
      }
      this.isimgDelete = true
    },
    nav_modify () {
      this.$router.push({
        name: 'modify'
      })
    },
    // 作品详情
    async get (e, i) {
      console.log(e, i)
      const res = await this.$api.goodsDetail({
        tokenId: i
      })
      if (res.status.code + '' === '0') {
        this.list = res.result
        this.list.worksTags = this.list.worksTags.split(',')
      } else if (res.status.code === 1002) {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 系列详情
    async getSeriesDetails (i) {
      const res = await this.$api.getSeriesDetails({
        seriesId: i
      })
      if (res.status.code === 0) {
        this.list = res.result
        // this.list.worksTypeVO.worksCover = this.list.avatar
        console.log(this.list)
      } else if (res.status.code === 1002) {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 跳转3D预览
    nav_3DpreviewObj () {
      if (this.list.worksTypeVO.fileFormat === 'obj') {
        this.$router.push({
          name: '3DpreviewObj',
          query: {
            address: this.list.worksTypeVO.archiveName
          }
        })
      } else if (this.list.worksTypeVO.fileFormat === 'dae') {
        this.$router.push({
          name: '3Dpreview',
          query: {
            address: this.list.worksTypeVO.archiveName
          }
        })
      }
    },

    copyUrl2 () {
      // 其实逻辑是执行了把复制目标赋值给创建的input的value，使用选中value执行复制 复制完然后移除input的原理
      var Url2 = document.getElementById('biao1').innerText // 获取span框的文本内容
      console.log(Url2)
      var input = document.createElement('input') // 创建input标签，只有input标签选中可以
      input.setAttribute('id', 'copyInput') // 给input一个id属性
      input.setAttribute('value', Url2) // 给input一个value属性，属性值是变量span的文本
      document.getElementsByTagName('body')[0].appendChild(input) // body里面添加input实体
      document.getElementById('copyInput').select() // 使用js去通过id找到并执行input实体的全部选中
      document.execCommand('Copy') // 原生copy方法执行浏览器复制命令
      document.getElementById('copyInput').remove() // 为避免下次页面添加copy方法 所以这里copy完之后要移除input实体
      this.$message({
        type: 'success',
        message: '复制成功'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.creativeProcessDialog {
  ::v-deep {
    .el-dialog__body {
      padding-top: 0;
      height: 65vh;
      overflow-y: auto;
    }
  }
  .tips {
    margin-bottom: 10px;
  }
  .content {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 48%);
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    .el-image {
      width: 100%;
    }
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
