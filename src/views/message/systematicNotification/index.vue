<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="消息标题">
        <el-input
          v-model="formInline.name"
          placeholder="请输入消息标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="消息盒子：">
        <el-select 
          v-model="formInline.boxId"
          placeholder="请选择消息盒子"
          clearable
        >
          <el-option
            v-for="item in boxList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="发布状态">
        <el-select 
          v-model="formInline.pushStatus"
          placeholder="发布状态"
          clearable
        >
          <el-option label="未发布" value="0"></el-option>
          <el-option label="发布中" value="1"></el-option>
          <el-option label="已发布" value="2"></el-option>
          <el-option label="发布失败" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="add_message()"
        >创建消息推送任务
        </el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="事件名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="事件描述"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="boxName"
        label="消息盒子名称"
        align="center"
      ></el-table-column>
      <!-- <el-table-column prop="pushTarget" label="推送目标" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.pushTarget == '1'">全部用户</el-tag>
          <el-tag v-if="scope.row.pushTarget == '2'">部分用户</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="pushStatus" label="发布状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.pushStatus == '0'">未发布</el-tag>
          <el-tag v-if="scope.row.pushStatus == '1'">发布中</el-tag>
          <el-tag v-if="scope.row.pushStatus == '2'">已发布</el-tag>
          <el-tag v-if="scope.row.pushStatus == '3'">发布失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="pushType" label="推送类型" align="center">
        <template scope="scope">
          <el-tag> {{ pushTypeDict[scope.row.pushType] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="scheduleTime"
        label="发布时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="title"
        label="消息标题"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="content"
        label="消息内容"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="linkType" label="跳转链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
          <el-tag v-if="scope.row.linkType == '30'">自定义跳转详情</el-tag>
        </template>
      </el-table-column
      >
      <el-table-column
        prop="link"
        label="h5网页跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updatedAt"
        label="更新时间"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="look_click(scope.row)"
          >查看
          </el-button
          >
          <el-button
            type="text"
            
            @click="audit_click(scope.row)"
            :disabled="editDisable(scope.row.pushType,scope.row.pushStatus)"
          >修改
          </el-button
          >
          <el-button
            type="text"
            
            @click="del_click(scope.row)"
            style="color: red"
            :disabled="scope.row.pushStatus == 1"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="isDialog"
      width="60%"
      center
      @closed="diaClose"
    >
      <el-form :model="form">
        <el-form-item label="事件名称:" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.name"
            placeholder="请输入事件名称"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="事件描述:" :label-width="formLabelWidth">
          <el-input
            v-model="form.description"
            placeholder="请输入事件描述"
            clearable
            style="width: 80%"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="消息盒子:" :label-width="formLabelWidth" required>
          <el-select  v-model="form.boxId" placeholder="请选择消息盒子">
            <el-option
              v-for="item in boxList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消息标题:" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.title"
            placeholder="请输入标题（带有参数标题，参数使用${参数名}符号标识!!!）"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="消息内容:" :label-width="formLabelWidth">
          <el-input
            v-model="form.content"
            placeholder="请输入消息内容（带有参数内容，参数使用${参数名}符号标识!!!）"
            clearable
            style="width: 80%"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="消息图片:" :label-width="formLabelWidth">
          <el-upload
            class="avatar-uploader"
            :headers="token"
            :action="action"
            :show-file-list="false"
            :on-success="handlePicSuccess"
          >
            <img v-if="form.verbPic" :src="form.verbPic" class="avatar"/>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <!-- <el-input
            v-model="form.verbPic"
            placeholder="请输入消息图片"
            clearable
            style="width: 80%"
          ></el-input> -->
        </el-form-item>
        <el-form-item label="推送目标:" :label-width="formLabelWidth" required>
          <el-radio-group v-model="form.pushTarget">
            <el-radio :label="1">全部用户</el-radio>
            <el-radio :label="2">部分用户</el-radio>
            <el-radio :label="3">自定义用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="自定义用户操作:"
          :label-width="formLabelWidth"
          v-if="form.pushTarget == 3"
        >
          <el-button  type="primary" @click="download()"
          >下载模板
          </el-button
          >
          <el-upload
            class="upload-demo"
            :action="excel_action"
            :before-remove="beforeRemove"
            :file-list="fileList"
            :headers="token"
            multiple
            :limit="1"
            :on-success="handleAvatarSuccess"
          >
            <el-button  type="primary" @click="upload()"
            >上传消息推送任务用户模板
            </el-button
            >
          </el-upload>
        </el-form-item>
        <el-form-item
          label="用户平台地址:"
          :label-width="formLabelWidth"
          v-if="form.pushTarget == 2"
          required
        >
          <el-input
            v-model="form.userContractAddressStr"
            placeholder="请输入用户平台地址(以逗号拼接)"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="触发类型:" :label-width="formLabelWidth" required>
          <el-radio-group v-model="form.triggerType">
            <el-radio :label="1">立即执行</el-radio>
            <el-radio :label="2">自动执行（需要指定执行时间）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="发布时间:"
          :label-width="formLabelWidth"
          v-if="form.triggerType == 2"
          required
        >
          <!-- <el-input
            v-model="form.scheduleTime"
            placeholder="请输入发布时间"
            clearable
            style="width: 80%"
          ></el-input> -->
          <el-date-picker
            v-model="form.scheduleTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="跳转链接类型:" :label-width="formLabelWidth">
          <el-radio-group v-model="form.linkType" @change="radioChange">
            <el-radio :label="10">自定义协议</el-radio>
            <el-radio :label="20">网页协议</el-radio>
            <el-radio :label="30">自定义跳转详情</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="跳转路径h5以及网页跳转:"
          :label-width="formLabelWidth"
          v-if="form.linkType == 10 || form.linkType == 20"
          required
        >
          <el-input
            v-model="form.link"
            placeholder="请输入跳转路径h5以及网页跳转"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="IOS 安卓原生跳转路径："
          :label-width="formLabelWidth"
          v-if="form.linkType == 10"
          required
        >
          <el-input
            v-model="form.nativeLink"
            placeholder="请输入IOS 安卓原生跳转路径"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="自定义跳转页面标题:"
          :label-width="formLabelWidth"
          v-if="form.linkType == 30"
          required
        >
          <el-input
            v-model="form.customDetailTitle"
            placeholder="请输入自定义跳转页面标题"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="发布人平台地址:"
          :label-width="formLabelWidth"
          v-if="form.linkType == 30"
        >
          <el-input
            v-model="form.publishContractAddress"
            placeholder="请输入发布人平台地址"
            clearable
            style="width: 80%"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="自定义跳转页面详情内容:"
          :label-width="formLabelWidth"
          v-if="form.linkType == 30"
          required
        >
          <el-input
            v-model="form.customDetail"
            placeholder="请输入自定义跳转页面详情内容"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="addBox_submit()">确 定</el-button> -->
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>

export default {
  name: 'systematicNotification',
  data () {
    return {
      platform: null,
      formInline: {
        name: '',
        boxId: '',
        pushStatus: '',
        createAt: null,
        scheduleTimeBegin: null,
        scheduleTimeEnd: null
      },
      form: {
        name: '',
        description: '',
        boxId: '',
        title: '',
        content: '',
        pushTarget: '',
        userContractAddressStr: '',
        triggerType: 1,
        scheduleTime: '',
        linkType: '',
        link: '',
        nativeLink: '',
        customDetailTitle: '',
        publishContractAddress: '',
        customDetail: '',
        verbPic: ''
      },
      boxList: [],
      tableData: [],
      total: 1,
      isDialog: false,
      id: '',
      formLabelWidth: '180px',
      title: '',
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      excel_action:
        process.env.VUE_APP_BASE_URL +
        'hongyan/adminApi/msgJob/missWebSign/importMsgJobUserExcel',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      fileList: [],
      pushTypeDict: {
        1: '仅站内',
        2: '仅站外',
        3: '全部推送'
      }
    }
  },
  mounted () {
    this.platform = Number.parseInt(this.$route.query.platform) || ''
    this.getListMsgBox()
    this.getList(1)
    // console.log("111", FarmerApi.axios.defaults.baseURL,FarmerApi.defautUrl);
  },
  methods: {
    /**
     * 禁用修改按钮
     */
    // pushType=1的显示修改按钮
    // pushType=2或者3且pushStatus=0显示修改按钮

    editDisable (type, status) {
      return type === 2
      // if (type === 1) {
      //   return false
      // } else {
      //   return !((type === 2 || type === 3) && status === 0)
      // }
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.verbPic = res.result.url
    },
    // 获取全部消息盒子
    async getListMsgBox () {
      const res = await this.$api.getListMsgBox({
        platform: this.platform
      })
      if (res.status.code === 0) {
        this.boxList = res.result
      } else if (res.status.code === 1002) {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    clear () {
      this.formInline.name = ''
      this.formInline.boxId = ''
      this.formInline.createAt = undefined
      this.formInline.pushStatus = ''
    },
    // 查询
    async getList (page) {
      if (this.formInline.createAt) {
        this.formInline.scheduleTimeBegin = this.formInline.createAt[0]
        this.formInline.scheduleTimeEnd = this.formInline.createAt[1]
      } else {
        this.formInline.scheduleTimeBegin = null
        this.formInline.scheduleTimeEnd = null
      }
      const res = await this.$api.getMessageList({
        pageNum: page,
        pageSize: 15,
        name: this.formInline.name,
        boxId: this.formInline.boxId,
        pushStatus: this.formInline.pushStatus,
        scheduleTimeBegin: this.formInline.scheduleTimeBegin,
        scheduleTimeEnd: this.formInline.scheduleTimeEnd,
        platform: this.platform
      })
      if (res.status.code === 0) {
        this.tableData = res.result.list
        this.total = res.result.totalCount
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    xuanze (val) {
      this.getList(val)
    },
    // 下载模板
    async download () {
      const res = await this.$api.downLoadTemplate({
        templateTag: 'MSG_JOB_USER_EXCEL_IMPORT'
      })
      if (res.status.code === 0) {
        window.location.href = res.result.emailsTemplateUrl
        this.$message.success('下载成功')
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    //
    add_message () {
      // this.title = "新增";
      // this.isDialog = true;
      this.$router.push({
        name: 'addMsgPush',
        query: {
          platform: this.platform
        }
      })
    },
    handleAvatarSuccess (res, file) {
      this.form.userContractAddressStr = res.result.userContractAddressStr
    },
    // 点击修改
    async audit_click (val) {
      // this.isDialog = true;
      // this.title = "修改";
      // this.id = val.id;
      // const res = await this.$api.detailMsgJob({
      //   jobId: val.id,
      // });
      // if (res.status.code == 0) {
      //   this.form = res.result;
      // } else {
      //   this.$message.error(res.status.msg);
      // }
      this.$router.push({
        name: 'addMsgPush',
        query: {
          edit_id: val.id,
          platform: this.platform
        }
      })
    },
    // 点击查看
    async look_click (val) {
      await this.$router.push({
        name: 'addMsgPush',
        query: {
          edit_id: val.id,
          look: true,
          platform: this.platform
        }
      })
    },
    // 新增确定
    async addBox_submit () {
      if (this.form.pushTarget !== 1) {
        this.form.pushTarget = 2
      }
      if (this.title === '新增') {
        const res = await this.$api.addMsgJob({
          name: this.form.name,
          description: this.form.description,
          boxId: this.form.boxId,
          title: this.form.title,
          content: this.form.content,
          pushTarget: this.form.pushTarget,
          userContractAddressStr: this.form.userContractAddressStr,
          triggerType: this.form.triggerType,
          scheduleTime: this.form.scheduleTime,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          customDetailTitle: this.form.customDetailTitle,
          publishContractAddress: this.form.publishContractAddress,
          customDetail: this.form.customDetail,
          verbPic: this.form.verbPic,
          platform: this.platform
        })
        if (res.status.code === 0) {
          this.getList(1)
          this.isDialog = false
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.status.msg)
        }
      } else {
        const res = await this.$api.updateMsgJob({
          jobId: this.id,
          name: this.form.name,
          description: this.form.description,
          boxId: this.form.boxId,
          title: this.form.title,
          content: this.form.content,
          pushTarget: this.form.pushTarget,
          userContractAddressStr: this.form.userContractAddressStr,
          triggerType: this.form.triggerType,
          scheduleTime: this.form.scheduleTime,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          customDetailTitle: this.form.customDetailTitle,
          publishContractAddress: this.form.publishContractAddress,
          customDetail: this.form.customDetail,
          verbPic: this.form.verbPic,
          platform: this.platform
        })
        if (res.status.code === 0) {
          this.getList(1)
          this.isDialog = false
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.status.msg)
        }
      }
    },
    // 弹框关闭
    diaClose () {
      this.form = {
        name: '',
        description: '',
        boxId: '',
        title: '',
        content: '',
        pushTarget: '',
        userContractAddressStr: '',
        triggerType: 1,
        scheduleTime: '',
        linkType: '',
        link: '',
        nativeLink: '',
        customDetailTitle: '',
        publishContractAddress: '',
        customDetail: '',
        verbPic: ''
      }
    },
    radioChange () {
      this.form.link = ''
      this.form.nativeLink = ''
      this.form.customDetailTitle = ''
      this.form.publishContractAddress = ''
      this.form.customDetail = ''
    },
    // 删除
    del_click (val) {
      this.$confirm('此操作将永久删除已推送的消息, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.remove(val.id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async remove (code) {
      const res = await this.$api.deleteMsgJob({
        jobId: code,
        platform: this.platform
      })
      if (res.status.code === 0) {
        this.getList(1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    }
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
