<template>
  <d2-container class="page">
    <div class="content">
      <div class="addLeft">
        <el-form :model="form" label-position="right" label-width="200px">
          <el-form-item
            label="事件名称:"
            :label-width="formLabelWidth"
            required
          >
            <el-input
              v-model="form.name"
              placeholder="请输入事件名称"
              clearable
              :disabled="look"	
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item label="事件描述:" :label-width="formLabelWidth">
            <el-input
              v-model="form.description"
              placeholder="请输入事件描述"
              clearable
              :disabled="look"
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="消息盒子:"
            :label-width="formLabelWidth"
            required
          >
            <el-select  v-model="form.boxId" placeholder="请选择消息盒子" :disabled="look">
              <el-option
                v-for="item in boxList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="消息标题:"
            :label-width="formLabelWidth"
            required
          >
            <el-input
              :disabled="look"
              v-model="form.title"
              placeholder="请输入标题"
              clearable
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item label="消息内容:" :label-width="formLabelWidth">
            <el-input
              :disabled="look"
              v-model="form.content"
              placeholder="请输入消息内容"
              clearable
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item label="消息图片:" :label-width="formLabelWidth">
            <!-- <el-upload
              class="avatar-uploader"
              :headers="token"
              :action="action"
              :show-file-list="false"
              :on-success="handlePicSuccess"
            >
              <img v-if="form.verbPic" :src="form.verbPic" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload> -->
            <el-upload
              :disabled="look"
              :action="action"
              :headers="token"
              list-type="picture-card"
              :on-success="handlePicSuccess"
              :class="{ hide: hideUpload_introduce }"
              :on-change="handleIntroduceUploadHide"
              :on-remove="handleIntroduceRemove"
              :file-list="fileListImg"
              :before-upload="beforeAvatarUpload"
            >
              <!-- <img
                v-if="form.verbPic"
                :src="form.verbPic"
                class="avatar"
                style="
                  width: 148px;
                  height: 148px;
                  margin-left: -1px;
                  margin-top: -1px;
                "
              /> -->
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt=""/>
            </el-dialog>
          </el-form-item>
          <el-form-item
            label="推送类型:"
            :label-width="formLabelWidth"
            required
          >
            <el-radio-group v-model="form.pushType" :disabled="(jobId && form.pushStatus)|| platform === 2 || look">
              <el-radio :label="1">仅站内</el-radio>
              <el-radio :label="2">仅站外</el-radio>
              <el-radio :label="3">全部推送</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="推送对象:"
            :label-width="formLabelWidth"
            required
          >
            <el-radio-group v-model="form.pushTarget" :disabled="(jobId && form.pushStatus) || look">
              <el-radio :label="1">全部用户</el-radio>
              <el-radio :label="2">部分用户</el-radio>
              <el-radio :label="3">自定义用户</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="推送设备:"
            :label-width="formLabelWidth"
            required
            v-if="form.pushType !== 1"
          >
            <el-radio-group v-model="form.deviceType" :disabled="(jobId && form.pushStatus)|| platform === 2 || look">
              <el-radio :label="1">安卓</el-radio>
              <el-radio :label="2">iOS</el-radio>
              <el-radio :label="3">全部设备</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="自定义用户操作:"
            :label-width="formLabelWidth"
            v-if="form.pushTarget === 3"
          >
            <el-button  type="primary" @click="download()"
            >下载模板
            </el-button
            >
            <el-upload
              :disabled="(jobId && form.pushStatus)  || look"
              class="upload-demo"
              :action="excel_action"
              :before-remove="beforeRemove"
              :file-list="fileList"
              :headers="token"
              multiple
              :limit="1"
              :on-success="handleAvatarSuccess"
              :on-error="handleAvatarError"
            >
              <el-button  type="primary"
              >上传消息推送任务用户模板
              </el-button
              >
            </el-upload>
          </el-form-item>
          <el-form-item
            label="用户平台地址:"
            :label-width="formLabelWidth"
            v-if="form.pushTarget + '' === '2'"
            required
          >
            <el-input
              :disabled="(jobId && form.pushStatus)  || look"
              v-model="form.userContractAddressStr"
              placeholder="请输入用户平台地址(以逗号拼接)"
              clearable
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="触发类型:"
            :label-width="formLabelWidth"
            required
          >
            <el-radio-group v-model="form.triggerType"
                            :disabled="((jobId && form.triggerType === 1) && form.pushStatus)|| look">
              <el-radio :label="1">立即执行</el-radio>
              <el-radio :label="2">自动执行（需要指定执行时间）</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="发布时间:"
            :label-width="formLabelWidth"
            v-if="form.triggerType == 2"
            required
          >
            <!-- <el-input
            v-model="form.scheduleTime"
            placeholder="请输入发布时间"
            clearable
            style="width: 500px"
          ></el-input> -->
            <el-date-picker
              v-model="form.scheduleTime"
              type="datetime"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :disabled="look"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="跳转链接类型:" :label-width="formLabelWidth">
            <el-radio-group v-model="form.linkType" @change="radioChange" :disabled="look">
              <el-radio :label="10">自定义链接</el-radio>
              <el-radio :label="20">网页链接</el-radio>
              <el-radio :label="30">自定义详情</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="跳转路径h5以及网页跳转:"
            :label-width="formLabelWidth"
            v-if="form.linkType == 10 || form.linkType == 20"
            required
          >
            <el-input
              :disabled="look"
              v-model="form.link"
              placeholder="请输入跳转路径h5以及网页跳转"
              clearable
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item
            :disabled="look"
            label="IOS 安卓原生跳转路径："
            :label-width="formLabelWidth"
            v-if="form.linkType == 10"
          >
            <el-input
              v-model="form.nativeLink"
              placeholder="原生路径不存在无需填写，系统自动使用'H5以及网页跳转路径'"
              clearable
              style="width: 500px"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="自定义跳转页面标题:"
            :label-width="formLabelWidth"
            v-if="form.linkType == 30"
            required
          >
            <el-input
              v-model="form.customDetailTitle"
              placeholder="请输入自定义跳转页面标题"
              clearable
              style="width: 500px"
              :disabled="look"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="发布人平台地址:"
            :label-width="formLabelWidth"
            v-if="form.linkType == 30"
          >
            <el-input
              v-model="form.publishContractAddress"
              placeholder="请输入发布人平台地址"
              clearable
              style="width: 80%"
              :disabled="look"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item
            label="自定义跳转页面详情内容:"
            :label-width="formLabelWidth"
            v-if="form.linkType == 30"
            required
          >
            <!-- <el-input
              v-model="form.customDetail"
              placeholder="请输入自定义跳转页面详情内容"
              clearable
              style="width: 500px"
            ></el-input> -->
            <!-- <el-button type="primary" @click="addRich">添加富文本</el-button> -->
            <editor-bar
              :catchData="catchData"
              :content="editorContent"
            ></editor-bar>
          </el-form-item>
          <el-form-item v-if="!look">
            <div class="tips"> 修改推送任务的推送类型为“全部推送”时，修改后的消息内容只对站内消息有效!!!</div>
            <el-button
              type="primary"
              @click="debounceMethods(submit_click)"
            >提交
            </el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="addRight" v-if="form.linkType == 30">
        <div v-html="richTxt"></div>
        <!-- {{ richTxt }} -->
      </div>
    </div>
  </d2-container>
</template>

<script>
import EditorBar from '@/components/editoritem/editoritem'

export default {
  name: 'addMsgPush',
  data () {
    return {
      platform: null,
      hideUpload_introduce: false,
      limitCount: 1,
      dialogImageUrl: '',
      dialogVisible: false,
      boxList: [],
      form: {
        name: '',
        description: '',
        boxId: 2,
        title: '',
        content: '',
        pushTarget: 1,
        userContractAddressStr: '',
        triggerType: 1,
        scheduleTime: '',
        linkType: '',
        link: '',
        nativeLink: '',
        customDetailTitle: '',
        publishContractAddress: '',
        customDetail: '',
        verbPic: '',
        pushType: 3,
        deviceType: 3
      },
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      excel_action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadExcel',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      fileList: [],
      formLabelWidth: '180px',
      richTxt: '',
      jobId: '',
      detail: '',
      fileListImg: [],
      look: false
    }
  },
  components: { EditorBar },
  watch: {
    'form.pushType' (val) {
      if (val + '' === '1') {
        this.form.deviceType = ''
      }
    },
    'form.pushTarget' (val) {
      if (val && val !== -1) {
        this.form.deviceType = ''
      }
    },
    'form.deviceType' (val) {
      if (val && val !== -1) {
        this.form.pushTarget = ''
      }
    }
  },
  mounted () {
    this.platform = Number.parseInt(this.$route.query.platform) || ''
    this.jobId = this.$route.query.edit_id
    this.look = this.$route.query.look
    this.getListMsgBox()
    if (this.$route.query.edit_id) {
      this.detailMsgJob()
    }
	if(this.$route.query.link){
		this.form.title=this.$route.query.title
		this.form.content=this.$route.query.subTitle
		this.form.linkType=10
		this.form.link=this.$route.query.link
		this.form.verbPic=this.$route.query.thumb 
		this.form.scheduleTime=this.timestampToTime(Date.parse(this.$route.query.publishTime)/1000+10)
		this.form.triggerType=2
		this.fileListImg = [
		  {
		    name: '',
		    url: this.$route.query.thumb
		  }
		]
		this.form.name=this.$route.query.title
	}
	console.log(this.$route.query)
  },
  methods: {
    // 监听富文本的输入
    catchData (e) {
      console.log('1e=====?>', e)
      this.richTxt = e
      this.form.customDetail = e
    },
    // 富文本中的内容
    editorContent (e) {
      console.log('2e=====?>', e)
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.verbPic = res.result.url
    },
    handleIntroduceUploadHide (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    beforeAvatarUpload (file) {
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.form.verbPic = ''
    },
    // 获取全部消息盒子
    async getListMsgBox () {
      const res = await this.$api.getListMsgBox({
        platform: this.platform
      })
      if (res.status.code + '' === '0') {
        this.boxList = res.result
      } else if (res.status.code + '' === '1002') {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 下载模板
    async download () {
      const res = await this.$api.downLoadTemplate({
        templateTag: 'MSG_JOB_USER_EXCEL_IMPORT'
      })
      if (res.status.code + '' === '0') {
        window.location.href = res.result.emailsTemplateUrl
        this.$message.success('下载成功')
      } else if (res.status.code + '' === '1002') {
        await this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    radioChange () {
      // this.form.link = ''
      this.form.nativeLink = ''
      this.form.customDetailTitle = ''
      this.form.publishContractAddress = ''
      this.form.customDetail = ''
    },
    async handleAvatarSuccess (res, file) {
      const {
        status: {
          code,
          msg
        },
        result: {
          url
        }
      } = res
      const { result: { userContractAddressStr } } = await this.$api.importMsgJobUserExcel({
        excelUrl: url
      })
      this.form.userContractAddressStr = userContractAddressStr
    },
    handleAvatarError (res, file) {
      console.log(res, file)
    },
    // 编辑详情
    async detailMsgJob () {
      const res = await this.$api.detailMsgJob({
        jobId: this.jobId,
        platform: this.platform
      })
      if (res.status.code + '' === '0') {
        this.form = res.result
        console.log(res.result)
        this.richTxt = this.form.customDetail
        this.detail = this.form.customDetail

        if (res.result.verbPic !== '') {
          this.hideUpload_introduce = true
          this.fileListImg = [
            {
              name: '',
              url: res.result.verbPic
            }
          ]
        }
      } else {
        this.$message.error(res.status.msg)
      }
    },
	timestampToTime(timestamp) {
	  // 时间戳为10位需*1000，时间戳为13位不需乘1000
	  var date = new Date(timestamp * 1000);
	  var Y = date.getFullYear() + "-";
	  var M =
	    (date.getMonth() + 1 < 10
	      ? "0" + (date.getMonth() + 1)
	      : date.getMonth() + 1) + "-";
	  var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
	  var h = date.getHours() + ":";
	  var m = date.getMinutes() + ":";
	  var s = date.getSeconds();
	  return Y + M + D + h + m + s;
	},
    // 新增编辑确定
    async submit_click () {
      // if (this.form.pushTarget !== 1) {
      //   this.form.pushTarget = 2
      // }
      if (!this.$route.query.edit_id) {
        const res = await this.$api.addMsgJob({
          name: this.form.name,
          description: this.form.description,
          boxId: this.form.boxId,
          title: this.form.title,
          content: this.form.content,
          pushTarget: this.form.pushTarget,
          userContractAddressStr: this.form.userContractAddressStr,
          triggerType: this.form.triggerType,
          scheduleTime: this.form.scheduleTime,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          customDetailTitle: this.form.customDetailTitle,
          publishContractAddress: this.form.publishContractAddress,
          customDetail: this.form.customDetail,
          verbPic: this.form.verbPic,
          platform: this.platform,
          pushType: this.form.pushType,
          deviceType: this.form.deviceType
        })
        if (res.status.code + '' === '0') {
          this.$message.success('新增成功')
          await this.$router.push({
            name: 'systematicNotification',
            query: {
              platform: this.platform
            }
          })
        }
      } else {
        const res = await this.$api.updateMsgJob({
          jobId: this.jobId,
          name: this.form.name,
          description: this.form.description,
          boxId: this.form.boxId,
          title: this.form.title,
          content: this.form.content,
          pushTarget: this.form.pushTarget,
          userContractAddressStr: this.form.userContractAddressStr,
          triggerType: this.form.triggerType,
          scheduleTime: this.form.scheduleTime,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          customDetailTitle: this.form.customDetailTitle,
          publishContractAddress: this.form.publishContractAddress,
          customDetail: this.form.customDetail,
          verbPic: this.form.verbPic,
          platform: this.platform,
          pushType: this.form.pushType,
          deviceType: this.form.deviceType
        })
        if (res.status.code + '' === '0') {
          this.$message.success('修改成功')
          await this.$router.push({
            name: 'systematicNotification',
            query: {
              platform: this.platform
            }
          })
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;

  .addRight {
    margin-left: 30px;
    width: 250px;
    min-height: 500px;
    margin-top: 600px;
    // border: 1px solid #999;
    padding: 80px 40px;
    background: url("../../../assets/img/ios.png") no-repeat;
    background-size: 100% 100%;
  }
}

::v-deep .hide .el-upload--picture-card {
  display: none;
}

.tips {
  color: #ff9900;
  font-size: 16px;
}
</style>
