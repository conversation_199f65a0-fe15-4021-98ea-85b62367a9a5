<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="模板编号">
        <el-input
          v-model="formInline.templateNo"
          placeholder="请输入模板编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="消息盒子：">
        <el-select  v-model="formInline.boxId" placeholder="请选择消息盒子">
          <el-option
            v-for="item in boxList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="事件模板名称">
        <el-input
          v-model="formInline.msgEventTemplateName"
          placeholder="请输入事件模板名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select  v-model="formInline.onLine" placeholder="状态">
          <el-option :label="val.dictLabel" :value="+val.dictValue" v-for="(val,i) in onLineDict" :key="i"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="add_box()"
        >新增新增消息事件模板
        </el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="templateNo"
        label="模板编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="boxName"
        label="消息盒子名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="事件名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="事件描述"
        align="center"
      ></el-table-column>
      <!-- <el-table-column
        prop="weight"
        label="权重"
        align="center"
        width="80"
      ></el-table-column> -->
      <el-table-column prop="pushType" label="推送类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.pushType == '1'">站内信</el-tag>
          <el-tag v-if="scope.row.pushType == '2'">app push</el-tag>
          <el-tag v-if="scope.row.pushType == '3'">全部推送</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        label="消息标题"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="content"
        label="消息内容模板"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="linkType" label="跳转链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType === 10">自定义协议</el-tag>
          <el-tag v-else-if="scope.row.linkType === 20">网页跳转</el-tag>
        </template>
      </el-table-column
      >
      <el-table-column
        prop="link"
        label="h5网页跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column prop="isOnline" label="状态" align="center">
        <template scope="scope">
          <el-tag>{{ scope.row.isOnline ? '上线' : '下线' }}</el-tag>
        </template>
      </el-table-column
      >
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)">修改</el-button>
          <el-button type="text"  @click="msgEventTemplateOnline(scope.row)">
            {{ !scope.row.isOnline ? '上线' : '下线' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" width="60%" center :before-close="beforeClose">
      <el-form :model="form">
        <el-form-item label="消息盒子：" :label-width="formLabelWidth" required>
          <el-select  v-model="form.boxId" placeholder="请选择消息盒子">
            <el-option
              v-for="item in boxList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="事件名称：" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.name"
            placeholder="请输入事件名称"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="事件描述：" :label-width="formLabelWidth">
          <el-input
            v-model="form.description"
            placeholder="请输入事件描述"
            clearable
            style="width: 80%"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="权重：" :label-width="formLabelWidth">
          <el-input
            v-model="form.weight"
            placeholder="请输入权重"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="消息标题：" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.title"
            placeholder="请输入标题（带有参数标题，参数使用${参数名}符号标识!!!）"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="消息内容模板：" :label-width="formLabelWidth">
          <el-input
            v-model="form.content"
            placeholder="请输入消息内容模板（带有参数内容，参数使用${参数名}符号标识!!!）"
            clearable
            style="width: 80%"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="跳转链接类型：" :label-width="formLabelWidth">
          <el-radio-group v-model="form.linkType">
            <el-radio :label="10" @click="linkTypeChange">自定义协议</el-radio>
            <el-radio :label="20">网页协议</el-radio>
          </el-radio-group>
          <i class="el-icon-close" @click="clearLinkType"></i>
        </el-form-item>
        <el-form-item
          label="推送类型:"
          :label-width="formLabelWidth"
          required
        >
          <el-radio-group v-model="form.pushType" :disabled="platform === 2">
            <el-radio :label="1">仅站内</el-radio>
            <el-radio :label="2">仅站外</el-radio>
            <el-radio :label="3">全部推送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="h5网页跳转路径：" :label-width="formLabelWidth"
                      v-if="form.linkType">
          <el-input
            v-model="form.link"
            placeholder="请输入h5网页跳转路径"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="IOS 安卓原生跳转路径："
          :label-width="formLabelWidth"
          v-if="form.linkType + '' === '10'"
        >
          <el-input
            v-model="form.nativeLink"
            placeholder="请输入IOS 安卓原生跳转路径"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="beforeClose">取 消</el-button>
        <el-button type="primary" @click="debounceMethods(addBox_submit)"
        >确 定
        </el-button
        >
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'communique',
  data () {
    return {
      platform: null,
      tableData: [],
      total: 1,
      formInline: {
        templateNo: '',
        msgEventTemplateName: '',
        boxId: ''
      },
      isDialog: false,
      form: {
        name: '',
        description: '',
        boxId: '',
        title: '',
        content: '',
        linkType: null,
        link: '',
        nativeLink: '',
        pushType: 1
        // weight: "",
      },
      id: '',
      formLabelWidth: '150px',
      boxList: [],
      title: '',
      templateList: [],
      onLineDict: []
    }
  },
  mounted () {
    this.platform = Number.parseInt(this.$route.query.platform) || ''
    this.getList(1)
    this.getListMsgBox()
    this.getQueryOptions('MSG_ONLINE', 'onLineDict')
  },
  methods: {
    /**
     * 获取字典
     * @param dictType 字典类型
     * @param key 赋值的key
     * @return {Promise<void>}
     */
    async getQueryOptions (dictType, key) {
      const {
        result: { dictDataListVOS }
      } = await this.$api.getDictDataByDictType({ dictType })
      this[key] = dictDataListVOS
    },
    /**
     * 取消linkType
     */
    clearLinkType () {
      this.form.linkType = null
      this.form.link = ''
      this.form.nativeLink = ''
    },
    /**
     * 关闭dialog 前重置from
     */
    beforeClose () {
      this.isDialog = false
      this.form = {
        name: '',
        description: '',
        boxId: '',
        title: '',
        content: '',
        linkType: null,
        link: '',
        nativeLink: '',
        pushType: 3
        // weight: "",
      }
    },
    clear () {
      this.formInline.templateNo = ''
      this.formInline.msgEventTemplateName = ''
      this.formInline.boxId = ''
      this.formInline.onLine = ''
    },
    // 查询
    async getList (page) {
      const res = await this.$api.getListPageMsgEventTemplate({
        pageNum: page,
        pageSize: 15,
        templateNo: this.formInline.templateNo,
        boxId: this.formInline.boxId,
        msgEventTemplateName: this.formInline.msgEventTemplateName,
        platform: this.platform,
        onLine: this.formInline.onLine
      })
      if (res.status.code + '' === '0') {
        this.tableData = res.result.list
        this.total = res.result.totalCount
      } else if (res.status.code + '' === '1002') {
        await this.$router.push({
          name: 'login'
        })
      }
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击新增消息盒子
    add_box () {
      this.isDialog = true
      this.title = '新增'
      this.getListMsgBox()
    },
    // 点击编辑盒子
    audit_click (val) {
      this.getListMsgBox()
      this.title = '修改'
      this.id = val.id
      //   this.form.name = val.name;
      //   this.form.description = val.description;
      //   this.form.weight = val.weight;
      this.getMsgEventTemplateDetail(val.id)
    },
    /**
     * 模板消息上下线
     * @return {Promise<void>}
     */
    msgEventTemplateOnline (row) {
      this.$confirm(`确定要${!row.isOnline ? '上线' : '下线'}该模板消息吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await this.$api.msgEventTemplateOnline({
          id: row.id,
          onLine: row.isOnline ? 0 : 1,
          platform: this.platform
        })
        this.$message.success('操作成功!')
        await this.getList(1)
      }).catch(() => {
      })
    },
    // 新增确定
    async addBox_submit () {
      if (this.title === '新增') {
        const res = await this.$api.addMsgEventTemplate({
          boxId: this.form.boxId,
          name: this.form.name,
          description: this.form.description,
          // weight: this.form.weight,
          title: this.form.title,
          content: this.form.content,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          platform: this.platform,
          pushType: this.form.pushType
        })
        if (res.status.code + '' === '0') {
          await this.getList(1)
          this.isDialog = false
          this.form = {
            boxId: '',
            name: '',
            description: '',
            // weight: "",
            title: '',
            content: '',
            linkType: 10,
            link: '',
            nativeLink: ''
          }
          this.$message.success('新增成功')
        }
      } else {
        const res = await this.$api.updateMsgEventTemplate({
          id: this.id,
          boxId: this.form.boxId,
          name: this.form.name,
          description: this.form.description,
          // weight: this.form.weight,
          title: this.form.title,
          content: this.form.content,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          platform: this.platform,
          pushType: this.form.pushType
        })
        if (res.status.code + '' === '0') {
          await this.getList(1)
          this.isDialog = false
          this.form = {
            boxId: '',
            name: '',
            description: '',
            // weight: "",
            title: '',
            content: '',
            linkType: 10,
            link: '',
            nativeLink: ''
          }
          this.$message.success('修改成功')
        }
      }
    },
    // 获取全部消息盒子
    async getListMsgBox () {
      const res = await this.$api.getListMsgBox({
        platform: this.platform
      })
      if (res.status.code === 0) {
        this.boxList = res.result
      } else if (res.status.code + '' === '1002') {
        await this.$router.push({
          name: 'login'
        })
      }
    },
    // 获取单个消息事件模板信息
    async getMsgEventTemplateDetail (val) {
      const res = await this.$api.getMsgEventTemplateDetail({
        id: val,
        platform: this.platform
      })
      if (res.status.code + '' === '0') {
        this.templateList = res.result
        this.form = res.result
        this.isDialog = true
        this.form.linkType = res.result.linkType
      } else if (res.status.code + '' === '1002') {
        await this.$router.push({
          name: 'login'
        })
      }
    }
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-radio-group {
  line-height: 40px;
}

.el-icon-close {
  font-weight: bold;
  line-height: 40px;
  font-size: 20px;
  margin-left: 30px;
}
</style>
