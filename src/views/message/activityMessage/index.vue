<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="消息盒子编号">
        <el-input
          v-model="formInline.boxNo"
          placeholder="请输入消息盒子编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="消息盒子名称">
        <el-input
          v-model="formInline.boxName"
          placeholder="请输入消息盒子名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSelete(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="add_box()">新增消息盒子</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="boxNo"
        label="盒子编号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="盒子名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="类型描述"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="weight"
        label="权重"
        align="center"
      ></el-table-column>
      <el-table-column prop="iconUrl" label="盒子图标" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.iconUrl"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" center>
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="盒子名称：" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.name"
            placeholder="请输入盒子名称"
            clearable
            style="width: 80%"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="类型描述：" :label-width="formLabelWidth">
          <el-input
            v-model="form.description"
            placeholder="请输入类型描述"
            clearable
            style="width: 80%"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="权重：" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.weight"
            placeholder="请输入权重"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="盒子图标：" :label-width="formLabelWidth">
          <!-- <el-input
            v-model="form.iconUrl"
            placeholder="请输入盒子图标"
            clearable
            style="width: 80%"
          ></el-input> -->
          <el-upload
            class="avatar-uploader"
            :headers="token"
            :action="action"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
          >
            <img v-if="form.iconUrl" :src="form.iconUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="debounceMethods(addBox_submit)"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'activityMessage',
  data () {
    return {
      platform: null,
      tableData: [],
      total: 1,
      formInline: {
        boxNo: '',
        boxName: ''
      },
      isDialog: false,
      form: {
        name: '',
        description: '',
        weight: '',
        iconUrl: ''
      },
      id: '',
      formLabelWidth: '120px',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      title: '',
      rules: {
        name: [{ required: true, message: '请填写盒子名称', trigger: 'blur' }],
        weight: [{ required: true, message: '请填写权重', trigger: 'blur' }]
      }
    }
  },
  mounted () {
    this.platform = Number.parseInt(this.$route.query.platform) || ''
    this.getSelete(1)
  },
  methods: {
    clear () {
      this.formInline.boxNo = ''
      this.formInline.boxName = ''
    },
    // 查询
    async getSelete (page) {
      const res = await this.$api.getListPageMsgBox({
        pageNum: page,
        pageSize: 15,
        boxNo: this.formInline.boxNo,
        boxName: this.formInline.boxName,
        platform: this.platform
      })
      if (res.status.code === 0) {
        this.tableData = res.result.list
        this.total = res.result.totalCount
      } else if (res.status.code === 1002) {
        this.$router.push({
          name: 'login'
        })
      } else {
        this.$message.error(res.status.msg)
      }
    },
    xuanze (val) {
      this.getSelete(val)
    },
    // 点击新增消息盒子
    add_box () {
      this.isDialog = true
      this.title = '新增'
    },
    // 点击编辑盒子
    audit_click (val) {
      this.isDialog = true
      this.title = '修改'
      this.id = val.id
      this.form.name = val.name
      this.form.description = val.description
      this.form.weight = val.weight
      this.form.iconUrl = val.iconUrl
    },
    // 新增确定
    async addBox_submit () {
      if (this.title === '新增') {
        const res = await this.$api.addMsgBox({
          name: this.form.name,
          description: this.form.description,
          weight: this.form.weight,
          iconUrl: this.form.iconUrl,
          platform: this.platform
        })
        if (res.status.code === 0) {
          this.getSelete(1)
          this.isDialog = false
          this.form = {
            name: '',
            description: '',
            weight: '',
            iconUrl: ''
          }
          this.$message.success('新增成功')
        } else {
          this.$message.error(res.status.msg)
        }
      } else {
        const res = await this.$api.updateMsgBox({
          id: this.id,
          name: this.form.name,
          description: this.form.description,
          weight: this.form.weight,
          iconUrl: this.form.iconUrl,
          platform: this.platform
        })
        if (res.status.code === 0) {
          this.getSelete(1)
          this.isDialog = false
          this.form = {
            name: '',
            description: '',
            weight: '',
            iconUrl: ''
          }
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.status.msg)
        }
      }
    },
    handleAvatarSuccess (res, file) {
      this.form.iconUrl = res.result.url
    }
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
