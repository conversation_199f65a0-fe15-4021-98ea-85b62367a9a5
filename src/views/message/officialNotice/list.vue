<template>
  <d2-container class="page">
    <div class="header">
      <el-form size="mini" :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
        <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
          <el-select  v-model="formInline[item.prop]" v-if="item.type === 'select'">
            <el-option :label="v.label" :value="v.value" v-for="(v,i) in item.options" :key="i"></el-option>
          </el-select>
          <el-date-picker
            v-else-if="item.type === 'dateRange'"
            v-model="formInline[item.prop]"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
          <el-input v-model="formInline[item.prop]" v-else></el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column v-for="item in tableList" :key="item.prop" :prop="item.prop" :label="item.label"></el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
import { getAdminLoginLogs } from '@/api/adminUser'
export default {
  name: 'OfficialNoticeList',
  data () {
    return {
      formInline: {},
      formList: [
        {
          label: '作者ID:',
          prop: 'autherId',
          type: 'input'
        },
        {
          label: '作者昵称:',
          prop: 'autherName',
          type: 'input'
        },
        {
          label: '文章内容:',
          prop: 'seoKeywords',
          type: 'input'
        },
        {
          label: 'seo 关键字:',
          prop: 'seoTitle',
          type: 'input'
        },
        {
          label: '副标题:',
          prop: 'subTitle',
          type: 'input'
        },
        {
          label: '标题:',
          prop: 'title'
        }
      ],
      tableData: [],
      tableList: [
        {
          prop: 'userName',
          label: '用户名',
          width: '200'
        },
        {
          prop: 'userId',
          label: '用户id',
          width: '100'
        },
        {
          prop: 'loginWay',
          label: '登录方式',
          width: '200'
        },
        {
          prop: 'ip',
          label: '登录ip',
          width: '100'
        },
        {
          prop: 'createAt',
          label: '登陆时间',
          width: '100'
        },
        {
          prop: 'isLogin',
          label: '是否登录成功',
          width: '100'
        },
        {
          prop: 'failedReason',
          label: '登陆失败原因',
          width: '150'
        }
      ],
      pageSize: 15,
      pageNum: 1,
      total: 0
    }
  },
  created () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const { dateRange } = this.formInline
      const req = {
        ...this.formInline,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      if (dateRange) {
        req.beginTime = dateRange[0] + '.000'
        req.endTime = dateRange[1] + '.000'
      }
      delete req.dateRange
      const {
        result: {
          list,
          totalCount
        }
      } = await getAdminLoginLogs(req)
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.getList()
    },
    /**
     * 重置
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;

  .demo-form-inline {
    flex: 1;
  }

  .action {
    width: 200px;
  }
}

.dialog-action {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.table {
  margin-bottom: 50px;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.msg {
  .el-textarea {
    margin: 20px 0;
  }
}
</style>
