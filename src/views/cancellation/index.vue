<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" style="background-color: #ffffff; padding: 20px">
      <el-form-item label="用户昵称">
        <el-input v-model="formInline.nickname" placeholder="请输入用户昵称"></el-input>
      </el-form-item>
      <el-form-item label="用户手机号">
        <el-input v-model="formInline.mobPhone" placeholder="请输入用户手机号"></el-input>
      </el-form-item>
      <el-form-item label="联系地址">
        <el-input v-model="formInline.contractAddress" placeholder="请输入联系地址"></el-input>
      </el-form-item>
      <el-form-item label="邮箱地址">
        <el-input v-model="formInline.emailAddress" placeholder="请输入邮箱地址"></el-input>
      </el-form-item>
      <el-form-item label="用户状态">
        <el-select  v-model="formInline.userStatus" placeholder="用户状态">
          <el-option label="全部" :value='null'></el-option>
          <el-option label="正常" value="10"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select  v-model="formInline.checkStatus" placeholder="审核状态">
          <el-option label="全部" :value='null'></el-option>
          <el-option label="账户注销审核中" value="ACCOUNT_CANCEL_CHECKING"></el-option>
          <el-option label="账户注销审核成功" value="ACCOUNT_CANCEL_SUCCESS"></el-option>
          <el-option label="账户注销审核失败" value="ACCOUNT_CANCEL_FAIL"></el-option>
          <el-option label="用户中止注销" value="ACCOUNT_CANCEL_USER_STOP"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="formInline.usercastingDate" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间">
        <el-date-picker v-model="formInline.castingDate" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getquery()">查询</el-button>
        <el-button type="primary" @click="orderexport()">清除</el-button>
        <!-- <el-button type="primary" @click="batch_audit()">批量审核</el-button> -->
      </el-form-item>
    </el-form>

    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
      show-header border style="width: 100%">
      <el-table-column fixed prop="id" label="id" type="selection" align="center"></el-table-column>

      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column prop="uid" label="用户id" align="center"></el-table-column>
      <el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
      <el-table-column prop="username" label="用户名" align="center"></el-table-column>
      <el-table-column prop="contactAddress" label="联系地址" align="center"></el-table-column>
      <el-table-column prop="emailAddress" label="邮箱地址" align="center"></el-table-column>
      <el-table-column prop="mobPhone" label="手机号" align="center"></el-table-column>
      <el-table-column prop="avatar" label="用户头像" align="center">
        <template scope="scope">
          <div style="width:100%;" @click='ddd(scope.row.avatar)'>
            <el-image style="width: 100px; height: 100px" :src="scope.row.avatar">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="recommend" label="推荐等级" align="center"></el-table-column>
      <el-table-column prop="userStatus" label=" 用户状态" align="center"></el-table-column>
      <el-table-column prop="userStatus" label="用户状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.userStatus == '0'" type="danger">禁用</el-tag>
          <el-tag v-if="scope.row.userStatus == '10'" type="success">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="balance" label=" 账户余额" align="center"></el-table-column>
      <el-table-column prop="fuel" label="燃料次数" align="center"></el-table-column>
      <el-table-column prop="deposit" label="保证金" align="center"></el-table-column>
      <el-table-column prop="userCreatedAt" label="用户创建时间" align="center"></el-table-column>
      <el-table-column prop="commitAt" label="提交时间" align="center"></el-table-column>
      <el-table-column prop="updatedAt" label="更新时间" align="center"></el-table-column>
      <el-table-column prop="checkTime" label="审核时间" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="checkStatus" label="审核状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_CHECKING'" type="danger">账户注销审核中</el-tag>
          <el-tag v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_SUCCESS'" type="success">账户注销审核成功</el-tag>
          <el-tag v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_FAIL'" type="danger">账户注销审核失败</el-tag>
          <el-tag v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_USER_STOP'" type="danger">用户中止注销</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rejectReason" label="驳回原因" align="center"></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="to_isDelete(scope.row)">备注</el-button>
          <el-button type="text"  v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_FAIL'"
            @click="to_isDialogReson(scope.row)">驳回原因
          </el-button>
          <el-button type="text"  v-if="scope.row.checkStatus == 'ACCOUNT_CANCEL_CHECKING'"
            @click="to_examine(scope.row)">终审
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="4" :current-page="current_page"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="批量审核" :visible.sync="isAuthority" width="50%">
      <div style="display: flex;justify-content: space-around; margin-bottom: 30px;">
        <el-radio v-model="radio1" label="ACCOUNT_CANCEL_SUCCESS" border :change="pupop()">通过</el-radio>
        <el-radio v-model="radio1" label="ACCOUNT_CANCEL_FAIL" border :change="pupop()">拒绝</el-radio>
      </div>
      <div style="margin-top: 30px" v-if="isPupops">
        <el-input type="textarea" :rows="2" placeholder="请输入拒绝原因" v-model="textarea1">
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAuthority = false">取 消</el-button>
        <el-button type="primary" @click="Submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="备注信息" :visible.sync="isDelete" width="50%">
      <div style="margin-top: 30px">
        <el-input type="textarea" maxlength="1000" show-word-limit :rows="5" placeholder="请输入1000字以内备注信息"
          v-model="remark">
        </el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDelete = false">取 消</el-button>
        <el-button type="primary" @click="to_remark()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="" :visible.sync="isimgDelete" width="1000px">
      <div style="width: 100%; display: flex">
        <el-image style="width: 500px" :src="imgurl"></el-image>
      </div>
    </el-dialog>
    <el-dialog title="驳回原因" :visible.sync="isDialogReson" width="35%" center>
      <p v-if="reason == null">无</p>
      <p>{{ reason }}</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialogReson = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="submit_reson()">确 定</el-button> -->
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'cancellation',
  data() {
    return {
      tableData: [],
      srcList: [],
      total: 1,
      current_page: 1,
      radio1: 'ACCOUNT_CANCEL_SUCCESS', // 1 同意  2 拒绝
      textarea: '手动录入', // 文本
      textarea1: '', // 文本
      options: [{
        value: '手动录入',
        label: '4'
      }],
      isPupop: false, // 拒绝文本控制
      isPupops: false,
      state: 0,
      formInline: {
        checkStatus: null, // 审核状态：ACCOUNT_CANCEL_CHECKING：账户注销审核中；ACCOUNT_CANCEL_SUCCESS：账户注销审核成功；ACCOUNT_CANCEL_FAIL：账户注销审核失败；ACCOUNT_CANCEL_USER_STOP：用户中止注销
        nickname: null, // 用户名称
        contractAddress: null, // 联系地址
        emailAddress: null, // 邮箱地址
        userStatus: null, // 用户状态
        userstartCastingDate: null, // 用户创建开始时间
        userendCastingDate: null, // 用户创建结束时间
        usercastingDate: null, // 用户组件获取时间
        startCastingDate: null, // 开始时间
        endCastingDate: null, // 结束时间
        castingDate: null // 组件获取时间
      },
      headers: {
        authorization: ''
      },
      isAuthority: false,
      isDelete: false,
      form: {
        code: '',
        desc: ''
      },
      formLabelWidth: '120px',
      tags: [],
      allTags: [],
      userId: '',
      // type: 'MACHINEPASS',
      multipleSelection: [],
      idlist: [],
      deleteid: '',
      isimgDelete: false,
      imgurl: '',
      scrollTop: 0,
      goodsSynopsis: '',
      goodsDesc: '',
      remark: '', // 备注信息
      recordId: null, // id
      isDialogReson: false, // 驳回原因
      reason: ''// 驳回原因
    }
  },
  mounted() {
    this.getSelete()
    this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
      this.scrollTop = res.target.scrollTop
      console.log(res.target.scrollTop)
    })
  },

  activated() {
    console.log(this.scrollTop, '我回来了')
    this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
    console.log(this.$refs.multipleTable.bodyWrapper.scrollTop)
  },
  methods: {
    // 大图
    ddd(e) {
      this.isimgDelete = true
      this.imgurl = e
      console.log(e)
    },
    // 查询
    getquery() {
      this.current_page = 1
      this.getSelete()
    },
    // 拒绝理由下拉框
    change() {
      console.log(this.textarea)
      if (this.textarea === '4') {
        console.log('我是手动输入')
        this.isPupops = true
      } else {
        this.isPupops = false
      }
    },

    async getSelete() {
      if (this.formInline.castingDate) {
        this.formInline.startCastingDate = this.formInline.castingDate[0] + '.000'
        this.formInline.endCastingDate = this.formInline.castingDate[1] + '.000'
      } else {
        this.formInline.startCastingDate = null
        this.formInline.endCastingDate = null
      }
      if (this.formInline.usercastingDate) {
        this.formInline.userstartCastingDate = this.formInline.usercastingDate[0] + '.000'
        this.formInline.userendCastingDate = this.formInline.usercastingDate[1] + '.000'
      } else {
        this.formInline.userstartCastingDate = null
        this.formInline.userendCastingDate = null
      }
      if (this.formInline.checkStatus === '') {
        this.formInline.checkStatus = null
      }
      const res = await this.$api.cancelAccountRecordList({
        pageNum: this.current_page,
        pageSize: 4,
        nickname: this.formInline.nickname, // 用户昵称
        contractAddress: this.formInline.contractAddress, // 联系地址
        emailAddress: this.formInline.emailAddress, // 邮箱地址
        userStatus: this.formInline.userStatus, // 用户状态
        checkStatus: this.formInline
          .checkStatus, // 审核状态：ACCOUNT_CANCEL_CHECKING：账户注销审核中；ACCOUNT_CANCEL_SUCCESS：账户注销审核成功；ACCOUNT_CANCEL_FAIL：账户注销审核失败；ACCOUNT_CANCEL_USER_STOP：用户中止注销
        accountStartTime: this.formInline.userstartCastingDate, // 用户创建开始时间
        accountEndTime: this.formInline.userendCastingDate, // 用户创建结束时间
        commitStartTime: this.formInline.startCastingDate, // 提交开始时间
        commitEndTime: this.formInline.endCastingDate, // 提交结束时间
        mobPhone: this.formInline.mobPhone
      })
      if (res.result == null) {
        this.$message.error('无搜索结果')
      } else {
        this.tableData = res.result.list
        console.log(this.tableData)
        this.total = res.result.totalCount
      }
    },
    // 审核
    async check() {
      this.recordId = parseInt(this.recordId)
      if (this.textarea1 === '' || this.textarea1 === null) {
        this.textarea1 = null
      }
      const res = await this.$api.check({
        recordId: this.recordId,
        checkStatus: this.radio1,
        reason: this.textarea1 // 原因
      })
      this.isAuthority = false
      this.$message.success(res.status.msg)
      this.textarea1 = ''
      this.getSelete()
    },
    // 驳回原因
    async rejectReason() {
      this.recordId = parseInt(this.recordId)
      const res = await this.$api.rejectReason({
        recordId: this.recordId
      })
      this.isDialogReson = true
      this.reason = res.result.reason
    },
    // 批量审核
    async getpeopleVerify(e) {
      console.log(e)
      e = e.toString()
      const res = await this.$api.peopleVerify({
        ids: e,
        peopleVerifyStatus: this.radio1,
        rejectExplain: this.textarea1
      })
      this.isAuthority = false
      this.$message.success(res.status.msg)
      this.textarea1 = ''
      this.getSelete()
    },
    to_isDelete(e) {
      console.log(e)
      this.remark = ''
      this.recordId = e.id
      this.isDelete = true
    },
    to_isDialogReson(e) {
      this.recordId = e.id

      this.rejectReason()
    },
    // 备注
    async to_remark() {
      console.log()
      await this.$api.remark({
        recordId: this.recordId,
        remark: this.remark // 备注内容
      })
      this.getSelete()
      this.isDelete = false
      this.$message.success('备注添加成功')
    },
    selete() {
      console.log(this.formInline)
    },
    submit() {
      this.$message.success('成功')
    },
    // 分页
    xuanze(val) {
      this.current_page = val
      this.getSelete()
      this.$refs.multipleTable.bodyWrapper.scrollTop = 0
    },
    // 批量选择
    handleSelectionChange(val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 清除
    orderexport() {
      this.formInline = {
        checkStatus: null, // 审核状态：ACCOUNT_CANCEL_CHECKING：账户注销审核中；ACCOUNT_CANCEL_SUCCESS：账户注销审核成功；ACCOUNT_CANCEL_FAIL：账户注销审核失败；ACCOUNT_CANCEL_USER_STOP：用户中止注销
        nickname: null, // 用户名称
        contractAddress: null, // 联系地址
        emailAddress: null, // 邮箱地址
        userStatus: null, // 用户状态
        userstartCastingDate: null, // 用户创建开始时间
        userendCastingDate: null, // 用户创建结束时间
        usercastingDate: null, // 用户组件获取时间
        startCastingDate: null, // 开始时间
        endCastingDate: null, // 结束时间
        castingDate: null // 组件获取时间
      }
      this.current_page = 1
      this.getSelete()
    },
    // 批量审核
    batch_audit() {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length >= 1) {
        this.state = 1
        this.isAuthority = true
      } else {
        this.$message.error('请选择作品')
      }
    },
    // 拒绝原因弹出框.
    pupop() {
      console.log(this.isPupop)
      if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
        this.isPupops = false
        this.isPupop = false
      } else {
        this.isPupop = true
        this.isPupops = true
      }
    },
    // 提交
    Submit() {
      console.log('22')

      this.idlist = []
      this.multipleSelection.forEach((item) => {
        this.idlist.push(item.id)
      })
      if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
        // 直接掉
        console.log('我掉了')
        this.check(this.idlist)
      } else if (this.radio1 === 'ACCOUNT_CANCEL_FAIL') {
        if (this.textarea1.length < 1) {
          this.$message.error('请填写拒绝原因')
        } else {
          this.isAuthority = false
          this.isPupops = false
          // 提交
          console.log('我掉了')
          this.check(this.idlist)
          this.getSelete()
        }
      } else {
        if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
          // 直接掉
          console.log('我掉了')
          this.check(this.idlist)
        } else if (this.radio1 === 'ACCOUNT_CANCEL_FAIL') {
          if (this.textarea1.length < 1) {
            this.$message.error('请填写拒绝原因')
          } else {
            this.isAuthority = false
            this.isPupops = false
            // 提交
            console.log('我掉了')
            this.check(this.idlist)
            this.getSelete()
          }
        }
      }
    },
    // 跳转详情
    nav_details(item) {
      console.log(item)
      console.log('aaaaaaaaa', this.scrollTop)
      this.$router.push({
        name: 'works_details',
        query: {
          id: item.tid
        }
      })
    },
    // 单个作品终审
    to_examine(item) {
      console.log(item)
      this.state = 0
      this.idlist = []
      this.idlist.push(item.id)
      this.recordId = item.id
      this.isAuthority = true
    },
    beforeAvatarUpload(file) {
    }
  }
}
</script>
<style>
.tag {
  margin: 0px 15px 15px 0px;
  cursor: pointer !important;
}
</style>
