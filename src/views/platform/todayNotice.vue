<template>
  <d2-container class="page">
    <el-form class="demo-form-inline" style="background-color: #ffffff; padding: 10px">
      <el-form-item>
        <el-button type="primary" @click="getList()">刷新</el-button>
        <el-button type="primary" @click="openClick()">{{ showSend ? '隐藏已发送' : '展示已发送' }}</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
      v-loading="loading" element-loading-text="正在加载">
      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column prop="processStatus" label="运营状态" align="center" width="160">
        <template slot-scope="scope">
          <el-select v-model="scope.row.processStatus" clearable placeholder="请选择"
            @change="changeStatus(scope.row.processStatus, scope.row)">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="publishTime" label="时间" align="center" width="170">
        <template slot-scope="scope">
          <el-button type="primary" style="margin-top:10px;" @click="timing(scope.row)"
            v-if="scope.row.sendType == 0">定时
          </el-button>
          <el-time-picker v-if="scope.row.sendType == 1 || scope.row.sendType == 2" v-model="scope.row.publishTime"
            :disabled="scope.row.sendStatus == 1" value-format="HH:mm:ss" :picker-options="{
            }" placeholder="选择时间" @change="changeTime(scope.row.publishTime, scope.row)">
          </el-time-picker>
          <el-button type="primary" style="margin-top:10px;" @click="direct_send(scope.row)"
            v-if="scope.row.sendStatus == 0">直接发送</el-button>
          <el-button type="success" style="margin-top:10px;" v-if="scope.row.sendStatus == 1">已发送</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="templateType" label="类型" align="center" width="140">
        <template slot-scope="scope">
          <el-select v-model="scope.row.templateType" clearable placeholder="请选择"
            @change="changeType(scope.row.templateType, scope.row)">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="imageTitle" label="标题" align="center" width="240">
        <template slot-scope="scope">
          <el-input type="textarea" autosize placeholder="请输入内容" maxlength="30" v-model="scope.row.imageTitle">
          </el-input>
          <el-button type="text" @click="setData(scope.row, false, 1)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="imageContent" label="内容" align="center" width="340">
        <template slot-scope="scope">
          <el-input type="textarea" autosize placeholder="请输入内容" v-model="scope.row.imageContent">
          </el-input>
          <el-button type="text" @click="setData(scope.row, false, 2)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="articleCtidAndCsNameModuleListStr" label="相关藏品" align="center" width="460">
        <template slot-scope="scope">
          <el-card class="box-card" style="width:440px;">
            <div class="" v-for="(item, index) in scope.row.articleCtidAndCsNameModuleListStr">
              <el-autocomplete style="width:340px;margin-bottom:10px;" size="mini" v-model="item.nameCtid"
                :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
              </el-autocomplete>
              <el-button type="text" style="margin-left:10px;" size="mini"
                @click="delCollectionItemnameCtid(scope.row, index)">删除</el-button>
            </div>
            <el-button type="primary" style="width:100%;" plain size="mini"
              @click="addCollectionItemnameCtid(scope.row)">添加相关藏品</el-button>
          </el-card>
          <el-button type="text" @click="update_collection(scope.row)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="yulan" label="预览" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="setData(scope.row, true)">预览</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" width="170">
        <template slot-scope="scope">
          <el-input type="textarea" class="red_input" autosize placeholder="请输入内容" v-model="scope.row.remark">
          </el-input>
          <el-button type="text" @click="update_remark(scope.row.remark, scope.row)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="imageQrCode" label="二维码" align="center" width="160">
        <template slot-scope="scope">
          <ImgUploader :value.sync="scope.row.imageQrCode" :limit="1"></ImgUploader>
          <el-button type="text" @click="uploading_pic(scope.row.imageQrCode, scope.row)">上传</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tapType" label="类型" align="center" width="120">
        <template slot-scope="scope">
          <el-radio-group v-model="scope.row.tapType" size="mini" @change="changeTapType(scope.row.tapType, scope.row)">
            <el-radio :label="0" border style="margin-bottom:10px;margin-left:0px;width:100px;">藏品公告</el-radio>
            <el-radio :label="1" border style="margin-bottom:10px;margin-left:0px;width:100px;">bit指数公告</el-radio>
            <el-radio :label="2" border style="margin-bottom:10px;margin-left:0px;width:100px;">美股公告</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click="nav_details(scope.row)">编辑</el-button>
          <el-button type="text" @click="nav_topping(scope.row)">置顶</el-button>
          <el-button type="text" @click="nav_delete(scope.row)">删除</el-button>
          <el-button type="text"
            v-if="(scope.row.templateType == 3 || scope.row.templateType == 10 || scope.row.templateType == 12 || scope.row.templateType == 5 || scope.row.templateType == 14 || scope.row.templateType == 15 || scope.row.templateType == 21 || scope.row.templateType == 20)"
            @click="openModel(scope.row, scope.$index)" :disabled="scope.row.isSet == 1">配置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="width_footer">
      <el-button type="primary" @click="add()" style="width: 100%;">添加今日公告</el-button>
    </div>
    <div class="flex">
      <div ref="preview">
        <div class="preview_details">
          <div class="tips">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png">
          </div>
          <div class="head_text">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20241030/3b11d88245184e64bd4b91327f8f1e11_524x31.png">
            <div class="title_div">
              <div class="title">
                <div v-for="(item, index) in formDataShuru.imageTitle">
                  {{ item }}
                </div>
              </div>
              <div class="bode">
                <img
                  src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png">
              </div>
            </div>
          </div>
          <!-- <div class="bigverse">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/667ae9be41db64bcb3f155df69642549_400x60.png"
              alt="" srcset="">
          </div> -->
          <div class="bigverse_top">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/523c2b56ae43a1e6093d2de7700de496_400x48.png"
              alt="" srcset="">
          </div>
          <div class="bigverse_body">
            <div class="neirong">
              <div class="title">
                亲爱的Bigverse用户:
                <br>
              </div>
              <div v-for="(item, index) in formDataShuru.imageContent"
                :style="{ 'margin-top': item == '' ? '0px' : '10px' }">
                <div v-if="item == ''">
                  <br>
                </div>
                <div v-else>
                  {{ item }}
                </div>
              </div>
            </div>
            <div class="code" v-if="formDataShuru.imageQrCode != ''">
              <img :src="formDataShuru.imageQrCode" alt="">
            </div>
            <div class="official_msg">
              <div class="title">官方风险提示:</div>
              <div>
                Bigverse平台发售的数字藏品仅具备收藏、欣赏价值，不具有投资、保值、增值、收益功能，平台对藏品价格不构成任何指导意义，请谨慎购买及参与活动，严防炒作。
              </div>
            </div>
            <div class="footer">
              <div v-for="(item, index) in formDataShuru.footer">{{ item, }}</div>
            </div>
          </div>
          <div class="bigverse_bottom">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/a15b6394a0ef2fdd85c1c8ec26d25d95_400x67.png"
              alt="" srcset="">
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="配置-开放寄售" :visible.sync="isOpenUp" width="700px">
      <div class="marginLeft-150">
        <common-form :submit="submit" :data="formData" :isBack="true" @nav_back="nav_back" :isChangeSelect="true"
          :schema="formSchema" submitText="确 定 配 置" label-width="300px">
          <template #fzshm="scope">
            <el-autocomplete style="width:340px;margin-bottom:10px;" size="mini"
              v-model="formData.getMarketTabLeftJoinByTabId" :fetch-suggestions="querySearchAsyncTabLeft"
              placeholder="请输入楼层名称" @select="MarketTabLeftJoinByTabId">
            </el-autocomplete>
            <div style="color:rgb(255, 29, 29);">请先选择好楼层再查询</div>
          </template>
          <template #floorPrice="scope">
            <el-row justify="start">
              <el-col :span="6">
                <div class="grid-content bg-purple">
                  <el-input placeholder="" v-model="formData.floorPriceMinus"></el-input>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="6">
                <div class="grid-content bg-purple">
                  <el-input placeholder="" v-model="formData.floorPricePlus"></el-input>
                </div>
              </el-col>
            </el-row>
          </template>
          <template #error="scope">
            <el-row justify="start">
              <el-col :span="6">
                <div class="grid-content bg-purple" style="display:flex;">
                  <el-input placeholder="" v-model="formData.minStartGapTime"></el-input>
                  <span style="margin-left:10px;">秒</span>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="6">
                <div class="grid-content bg-purple" style="display:flex ;">
                  <el-input placeholder="" v-model="formData.maxStartGapTime"></el-input>
                  <span style="margin-left:10px;">秒</span>
                </div>
              </el-col>
            </el-row>
          </template>
        </common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-限额调整" :visible.sync="isLimitPrice" width="700px">
      <div class="marginLeft-150">
        <common-form :submit="submitLimitPrice" :isBack="true" @nav_back="nav_back" :data="limitPriceData"
          :schema="limitPriceSchema" submitText="确 定 配 置" label-width="300px">
          <template #floorPrice="scope">
            <el-row justify="start">
              <el-col :span="6">
                <div class="grid-content bg-purple">
                  <el-input placeholder="" v-model="limitPriceData.minPriceRange"></el-input>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="grid-content bg-purple" style="text-align:center;">-</div>
              </el-col>
              <el-col :span="6">
                <div class="grid-content bg-purple">
                  <el-input placeholder="" v-model="limitPriceData.maxPriceRange"></el-input>
                </div>
              </el-col>
            </el-row>
          </template>
        </common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-空投" :visible.sync="isAirDrop" width="700px">
      <div class="marginLeft-150">
        <common-form :submit="airDropSubmit" :isBack="true" @nav_back="nav_back" :data="airDropData"
          :schema="airDropSchema" submitText="确 定 配 置" label-width="300px"></common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-置换" :visible.sync="isReplace" width="700px">
      <div class="marginLeft-150">
        <common-form :submit="replaceSubmit" :isBack="true" @nav_back="nav_back" :data="replaceData"
          :schema="replaceSchema" submitText="确 定 配 置" label-width="300px"></common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-展示位变更" :visible.sync="isBiangen" width="900px">
      <div class="">
        <common-form :submit="biangenSubmit" :isBack="true" @nav_back="nav_back" :data="biangenData"
          :schema="biangenSchema" submitText="确 定 配 置" label-width="300px"></common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-开放竞价" :visible.sync="isJingPrice" width="700px">
      <div class="">
        <common-form :submit="jingPriceSubmit" :isBack="true" @nav_back="nav_back" :data="jingPriceData"
          :schema="jingPriceSchema" submitText="确 定 配 置" label-width="200px"></common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-开放转售" :visible.sync="isZhuanShou" width="700px">
      <div class="">
        <common-form :submit="zhuanShouSubmit" :isBack="true" @nav_back="nav_back" :data="zhuanShouData"
          :schema="zhuanShouSchema" submitText="确 定 配 置" label-width="200px"></common-form>
      </div>
    </el-dialog>
    <el-dialog title="配置-委托购买" :visible.sync="isWeiTuo" width="700px">
      <div class="">
        <common-form :submit="weiTuoSubmit" :isBack="true" @nav_back="nav_back" :data="weiTuoData"
          :schema="weiTuoSchema" submitText="确 定 配 置" label-width="200px"></common-form>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import ImgUploader from '@/components/ImgUploader'
import html2canvas from 'html2canvas';
import CommonForm from '@/components/CommonForm'
import {
  uploadImgToOss
} from '@/api/ossCenter'
import {
  articleTimeList,
  articleTimeCreate,
  articleTimeUpdate,
  articleTimeTopping,
  articleTimeDeleted
} from '@/api/hongyan'

export default {
  name: 'todayNotice',
  data() {
    return {
      loading: false,
      tableData: [],
      lading: false,
      statusList: [{
        value: 1,
        label: '待修改'
      }, {
        value: 2,
        label: '文案待确认'
      }, {
        value: 3,
        label: '文案已确认，等待配置'
      }, {
        value: 4,
        label: '已配置，等待复核'
      }, {
        value: 5,
        label: '已复核'
      }],
      typeList: [{
        value: 10,
        label: '限额调整',
        list: {
          title: '藏品《》寄售限价调整',
          content: `藏品《XXX》限额将于XX:XX进行调整为，调整后藏品最低限额¥XX，最高限额为¥XX。敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/10.png'
        }
      }, {
        value: 3,
        label: '开放寄售',
        list: {
          title: '藏品《》开放寄售',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/3.png'
        }
      }, {
        value: 20,
        label: '开放转售',
        list: {
          title: '藏品《XXX》开放转售功能',
          content: `藏品《XXX》将于XX:XX开启转售功能。转售最低限额为¥XX， 转售最高限额为¥XX。敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/20.png'
        }
      }, {
        value: 15,
        label: '竞价',
        list: {
          title: '藏品《》开启竞价',
          content: `藏品《》将于XX年XX月XX日XX:XX开启竞价

          敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/15.png'
        }
      }, {
        value: 21,
        label: '委托购买',
        list: {
          title: '藏品《XXX》开放委托购买',
          content: `藏品《XXX》将于XX:XX开放委托购买功能并将于XX:XX关闭委托购买功能。敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/21.png'
        }
      }, {
        value: 2,
        label: '合成',
        list: {
          title: 'XX系列藏品《》合成公告',
          content: `藏品《》即将开始合成！

合成材料:《》*

合成时间:
敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/2.png'
        }
      }, {
        value: 11,
        label: '分解',
        list: {
          title: '藏品《》即将开始分解',
          content: `藏品《》即将开始分解！

分解规则:每持有一份《》，可分解为X份《》。

分解时间:XXXX - XXXX

(本次分解为限量分解，限量XX次，先到先得！)

敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/11.png'
        }
      }, {
        value: 5,
        label: '空投',
        list: {
          title: `持有《》空投《》`,
          content: `每持有X份《XXX》，即可获得X份《XXX》的空投！

扫描时间:XXX-XXX  扫描会持续一段时间，请保持您的藏品处于下架状态，否则将无法享受专属兑换权益。为避免影响您的权益，扫描期间内请勿交易。空投藏品预计将在扫描结束后2小时内到达您的藏品室，请注意查收！

敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/5.png'
        }
      }, {
        value: 14,
        label: '展示位变更',
        list: {
          title: '展示位变更',
          content: `藏品《XXX》将从【xx区】移至【xx区】。

                    敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/14.png'
        }
      }, {
        value: 17,
        label: '分区调整',
        list: {
          title: '分区调整',
          content: `今日将新增分区【XXXX】，敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/17.png'
        }
      }, {
        value: 12,
        label: '置换',
        list: {
          title: '《》将置换为《》',
          content: `《》将按照 1:X X 下架置换为《》。

《》共铸造XX份，剩余将用于后续活动，并于XX小时内开放寄售，届时最高寄售限额为 ¥ 。

下架置换时间为:XXXX

为避免影响您的权益，置换时间开始后请勿交易。置换藏品预计将在置换开始后6小时内到达您的藏品室，请注意查收！

敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/12.png'
        }
      }, {
        value: 16,
        label: '冰封活动',
        list: {
          title: '藏品《XXX》冰封活动活动来袭！',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/16.png'
        }
      }, {
        value: 18,
        label: '突袭公告',
        list: {
          title: '藏品《XXX》突袭活动来袭！',
          content: `藏品《XXX》即将开始突袭合成！突袭合成材料:XXXX 突袭时间:XXXXXX-XXXXXX，本次突袭限量XXX份，先到先得！敬请各位用户知悉！谢谢！
          `,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/18.png'
        }
      }, {
        value: 19,
        label: '膨胀公告',
        list: {
          title: '藏品《XXX》膨胀活动来袭！',
          content: `每消耗XXX份《XXX》可膨胀为XXX份《XXX》 膨胀时间:XXXXXX-XXXXX 敬请各位用户知悉！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/19.png'
        }
      }, {
        value: 13,
        label: '提醒',
        list: {
          title: '《》XX提醒',
          content: `藏品《》即将于 XX : XX  合成/上新  ！

（活动重复一遍）

请准备参与活动的用户注意不要错过时间！谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/13.png'
        }
      }, {
        value: 4,
        label: '上新',
        list: {
          title: '《》上新预告',
          content: `每持有1份《》可专属兑换X份《》（权益可叠加）。

兑换材料:XX

扫描时间:XX
 （ 扫描会持续一段时间，为防止数据异常影响权益，请避免在扫描期间进行交易，请保持您的藏品处于下架状态，否则将无法享受专属兑换权益）

兑换时间:XX

敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/4.png'
        }
      }, {
        value: 8,
        label: '活动公告',
        list: {
          title: '',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/8.png'
        }
      },
      {
        value: 9,
        label: '运营公告',
        list: {
          title: '',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/new2/9.png'
        }
      },
      ],
      flagchange: 0,
      formDataShuru: [],
      temporaryList: [],
      showSend: false,
      isOpenUp: false, //配置开放寄售
      isLimitPrice: false, //配置限额调整
      isAirDrop: false, //配置-空投
      isReplace: false, //配置-置换
      isJingPrice: false, //配置 - 竞价
      isZhuanShou: false,
      isWeiTuo: false,
      formSchema: [{
        type: 'search',
        label: '藏品:',
        placeholder: '请输入藏品',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入藏品',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '开寄售时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入开寄售时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'input',
        label: '开寄售限价:',
        placeholder: '请输入开寄售限价',
        field: 'onSaleMaxPrice',
        rules: [{
          required: true,
          message: '请输入开寄售限价',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '楼层选择:',
        field: 'isBv',
        options: [{
          label: 'bigverse',
          value: 1
        }, {
          label: 'kverse',
          value: 6
        }],
        rules: [{
          required: true,
          message: '请选择楼层选择',
          trigger: 'blur'
        }]
      },
      {
        type: 'select',
        label: '进入的楼层',
        field: 'marketTabId',
        placeholder: '请输入进入的楼层',
        options: [],
        rules: [{
          required: true,
          message: '请输入进入的楼层',
          trigger: 'blur'
        }]
      },
      {
        label: '放在谁的后面',
        slot: 'fzshm',
      },
      {
        type: 'input',
        label: '楼层权重:',
        placeholder: '请输入楼层权重',
        field: 'marketTabWeight',
        rules: [{
          required: true,
          message: '请输入楼层权重',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '是否配合自动交易:',
        field: 'isAddAutoTrade',
        options: [{
          label: '是',
          value: '1'
        }, {
          label: '否',
          value: '0'
        }],
        rules: [{
          required: true,
          message: '请选择是否配合自动交易',
          trigger: 'blur'
        }]
      },
      // {
      //   type: 'number-input',
      //   label: '断档最大值：',
      //   placeholder: '请输入断档最大值',
      //   field: 'maxGapPrice',
      //   rules: [{
      //     required: true,
      //     message: '请输入断档最大值',
      //     trigger: 'blur'
      //   }],
      //   show: {
      //     relationField: 'isAddAutoTrade',
      //     value: "1"
      //   },
      // },
      {
        label: '相比地板价价格：',
        placeholder: '请输入相比地板价价格',
        slot: 'floorPrice',
        rules: [{
          required: true,
          message: '请输入相比地板价价格',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'number-input',
        label: '间隔多久交易一单：',
        placeholder: '请输入间隔多久交易一单',
        slot: 'error',
        rules: [{
          required: true,
          message: '请输入间隔多久交易一单',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'number-input',
        label: '满几单后停止：',
        placeholder: '请输入满几单后停止',
        field: 'tradeLimitNum',
        rules: [{
          required: true,
          message: '请输入满几单后停止',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      formData: {
        ctid: '',
        startTime: '',
        onSaleMaxPrice: '',
        marketTabId: '',
        marketTabWeight: '',
        getMarketTabLeftJoinByTabId: '',
        isBv: 1,
        floorPricePlus: 0,
        floorPriceMinus: 0,
        minStartGapTime: 4,
        maxStartGapTime: 20,
        isAddAutoTrade: '0',
        tradeLimitNum: 0
      },
      limitPriceSchema: [{
        type: 'search',
        label: '藏品:',
        placeholder: '请输入藏品',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入藏品',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '调限额时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入调限额时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '最高限价:',
        placeholder: '请输入最高限价',
        field: 'onSaleMaxPrice',
      },
      {
        type: 'number-input',
        label: '最低限价:',
        placeholder: '请输入最低限价',
        field: 'onSaleMinPrice',
      },
      {
        label: '是否配合自动交易：',
        field: 'isAddAutoTrade',
        type: 'radio',
        options: [{
          label: '是(展开)',
          value: '1'
        }, {
          label: '否',
          value: '0'
        }]
      },
      {
        label: '交易价格：',
        placeholder: '请输入交易价格',
        slot: 'floorPrice',
        rules: [{
          required: true,
          message: '请输入交易价格',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'number-input',
        label: '满几单后停止：',
        placeholder: '请输入满几单后停止',
        field: 'tradeLimitNum',
        rules: [{
          required: true,
          message: '请输入满几单后停止',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'datetime',
        label: '结束时间：',
        field: 'endTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入结束时间',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isAddAutoTrade',
          value: "1"
        },
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      limitPriceData: {
        ctid: '',
        startTime: '',
        onSaleMaxPrice: '',
        onSaleMinPrice: '',
        isAddAutoTrade: '0'
      },
      airDropSchema: [{
        type: 'search',
        label: '持有的系列id:',
        placeholder: '请输入持有的系列id',
        field: 'holdCtid',
        rules: [{
          required: true,
          message: '请输入持有的系列id',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '持有几个才可以空投:',
        placeholder: '请输入持有几个才可以空投',
        field: 'holdNum',
        rules: [{
          required: true,
          message: '请输入持有几个才可以空投',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetimerange',
        label: '扫描持仓时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入扫描持仓时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'search',
        label: '满足持仓投什么:',
        placeholder: '请输入满足持仓投什么',
        field: 'rewardCtid',
        rules: [{
          required: true,
          message: '请输入满足持仓投什么',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '满足持仓投几个:',
        placeholder: '请输入满足持仓投几个',
        field: 'rewardNum',
        rules: [{
          required: true,
          message: '请输入满足持仓投几个',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '持有多组是否可叠加:',
        field: 'isRepeatReward',
        options: [{
          label: '可叠加',
          value: 1
        }, {
          label: '不可叠加',
          value: 0
        }],
        rules: [{
          required: true,
          message: '请输入持有多组是否可叠加',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '空投用户范围:',
        field: 'airDropRange',
        options: [{
          label: '仅下架的享有空投',
          value: 1
        }, {
          label: '全部都有',
          value: 2
        }],
        rules: [{
          required: true,
          message: '请输入持有多组是否可叠加',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '扫描后状态:',
        field: 'afterOperateType',
        options: [{
          label: '正常流通',
          value: 'NOT_SALE_SIGN0'
        }, {
          label: '仅供收藏',
          value: 'NO_CHANGE'
        }]
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      airDropData: {
        holdCtid: '',
        holdNum: '',
        startTime: '',
        rewardCtid: '',
        rewardNum: '',
        isRepeatReward: 0,
        airDropRange: 1,
        afterOperateType: 'NO_CHANGE'
      },
      replaceSchema: [{
        type: 'search',
        label: '持有什么藏品给置换:',
        placeholder: '请输入持有什么藏品给置换',
        field: 'holdCtid',
        rules: [{
          required: true,
          message: '请输入持有什么藏品给置换',
          trigger: 'blur'
        }]
      },
      {
        type: 'search',
        label: '持有什么置换成什么:',
        placeholder: '请输入持有什么置换成什么',
        field: 'rewardCtid',
        rules: [{
          required: true,
          message: '请输入持有什么置换成什么',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '置换比例是1比几:',
        placeholder: '请输入置换时间',
        field: 'rewardNum',
        rules: [{
          required: true,
          message: '请输入置换时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '置换时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入置换时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      replaceData: {
        ctid: '',
        startTime: '',
        onSaleMaxPrice: ''
      },
      version: '',
      noticeId: '',
      noticeIndex: '',
      marketName: '',
      isBiangen: false,
      biangenSchema: [{
        type: 'search',
        label: '藏品名:',
        placeholder: '请输入持有什么藏品给置换',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入持有什么藏品给置换',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '楼层选择:',
        field: 'isBv',
        options: [{
          label: 'bigverse',
          value: 1
        }, {
          label: 'kverse',
          value: 6
        }],
        rules: [{
          required: true,
          message: '请选择楼层选择',
          trigger: 'blur'
        }]
      },
      {
        type: 'select',
        label: '添加到哪个楼层:',
        placeholder: '请输入添加到哪个楼层',
        field: 'marketTabId',
        options: [],
        rules: [{
          required: true,
          message: '请输入添加到哪个楼层',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '新加入后的权重:',
        placeholder: '请输入新加入后的权重',
        field: 'marketTabWeight',
        rules: [{
          required: true,
          message: '请输入新加入后的权重',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '如已在其他楼层是否从旧楼层移出:',
        field: 'removeOld',
        options: [{
          label: '移出',
          value: 0
        }, {
          label: '新旧都保留',
          value: 1
        }],
        rules: [{
          required: true,
          message: '请输入如已在其他楼层是否从旧楼层移出',
          trigger: 'blur'
        }]
      },
      {
        type: 'radio',
        label: '调整时间:',
        field: 'isTiming',
        options: [{
          label: '立即',
          value: 0
        }, {
          label: '定时',
          value: 1
        }],
        rules: [{
          required: true,
          message: '请选择调整时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        show: {
          relationField: 'isTiming',
          value: "1"
        },
        rules: [{
          required: true,
          message: '请输入时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      biangenData: {
        ctid: '',
        startTime: '',
        isTiming: 0,
        removeOld: 0,
        isBv: 1,
        marketTabId: ''
      },
      resultsTabLeft: [],
      jingPriceSchema: [{
        type: 'search',
        label: '藏品名:',
        placeholder: '请输入持有什么藏品给置换',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入持有什么藏品给置换',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '开竞价时间:',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入开竞价时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '关闭竞价时间:',
        field: 'endTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        }
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      jingPriceData: {
        ctid: '',
        startTime: '',
        endTime: ''
      },
      zhuanShouSchema: [{
        type: 'search',
        label: '藏品名:',
        placeholder: '请输入持有什么藏品给置换',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入持有什么藏品给置换',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '开放转售时间:',
        field: 'openForResaleTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入开放转售时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'number-input',
        label: '转售最低限额:',
        placeholder: '请输入转售最低限额',
        field: 'openForResaleMinPrice',
      },
      {
        type: 'number-input',
        label: '转售最高限额:',
        placeholder: '请输入转售最高限额',
        field: 'openForResaleMaxPrice',
        rules: [{
          required: true,
          message: '请输入转售最高限额',
          trigger: 'blur'
        }]
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      zhuanShouData: {
        ctid: '',
        openForResaleTime: '',
        openForResaleMinPrice: '',
        openForResaleMaxPrice: ''
      },
      weiTuoSchema: [{
        type: 'search',
        label: '藏品名:',
        placeholder: '请输入持有什么藏品给置换',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入持有什么藏品给置换',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '委托购买时间:',
        field: 'EntrustedPurchaseTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入委托购买时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetime',
        label: '关闭委托购买时间:',
        field: 'EntrustedPurchaseStopTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        }
      },
      {
        type: 'action',
        exclude: ['reset']
      }
      ],
      weiTuoData: {
        ctid: '',
        EntrustedPurchaseTime: '',
        EntrustedPurchaseStopTime: '',
      },
      moduleId: 1
    }
  },
  components: {
    ImgUploader,
    CommonForm
  },
  watch: {
    'formData.isBv': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        // isBV发生变化时执行的操作
        console.log('寄售');
        this.formData.marketTabId = ""
        this.moduleId = newVal
        this.getFloor()
        // 在这里可以进行你想要的操作，比如调用函数、发起网络请求等
      }
    },
    'biangenData.isBv': function (newVal, oldVal) {
      if (newVal !== oldVal) {
        // isBV发生变化时执行的操作
        console.log('变更');
        this.biangenData.marketTabId = ""
        this.moduleId = newVal
        this.getFloor()
        // 在这里可以进行你想要的操作，比如调用函数、发起网络请求等
      }
    }
  },
  mounted() {
    this.getList()
    this.getFloor()
    const appDiv = document.getElementById('app');
    // appDiv.style.transform = 'scale(1)';
    // appDiv.style.width = '100%';
    // appDiv.style.height = '100%';
    // appDiv.style.backgroundColor = 'yellow';
  },
  // computed: {
  //   formDataShuru1() {
  //     const data = {
  //       ...this.formDataShuru,
  //     }
  //     // data.textarea = data.body.split('\n')
  //     // data.footer = data.remark.split('\n')
  //     // data.title = data.title.split('\n')
  //     return data
  //   }
  // },
  methods: {
    //选择状态
    changeStatus(e, item) {
      this.temporaryList = item
      let params = {
        processStatus: e
      }
      console.log(item)
      this.edit(params)
    },
    //选择类型
    changeType(e, item) {
      console.log(e, item)
      this.$confirm('是否确认使用该模板，标题和内容将会被覆盖？', '确认使用模板覆盖', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.typeList.forEach((itemm) => {
          console.log("传过来的值" + e)
          console.error("匹配的值" + itemm.value)
          if (itemm.value == e) {
            item.imageTitle = itemm.list.title
            item.imageContent = itemm.list.content
            item.thumb = itemm.list.cover
            console.log(itemm.list)
          }
        })
        console.log(item.imageTitle, item.imageContent)
        this.setData(item, false)
      }).catch(() => {
        this.getList()
      });

    },
    changeTime(e, item) {
      const t1 = new Date().valueOf()

      let str = this.timestampToTime(t1) + e
      console.log(str)
      this.temporaryList = item
      let params = {
        publishTime: str,
        sendType: 1,
      }
      console.log(item)
      this.edit(params)
    },
    direct_send(item) {
      if (item.templateType == 1 && item.imageQrCode == '') {
        this.$confirm('白名单没有二维码，确定直接发送？', '提醒', {
          confirmButtonText: '确定直接发送',
          cancelButtonText: '返回编辑',
          type: 'warning'
        }).then(async () => {
          this.temporaryList = item
          let params = {
            sendType: 2,
          }
          this.edit(params)
        }).catch(() => {

        });
      } else {
        this.temporaryList = item
        let params = {
          sendType: 2,
        }
        this.edit(params)
      }
    },

    //上传二维码
    uploading_pic(e, item) {
      if (e == '') {
        this.$message.error('请选择二维码')
      } else {
        this.temporaryList = item
        this.setData(item, false, 3)
      }
    },
    update_collection(item) {
      console.log(item)
      this.temporaryList = item
      if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
        this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
          console.log(item.nameCtid)
          if (item.nameCtid) {
            item.csName = item.nameCtid.split('(')[0]
            item.ctid = item.nameCtid.split('(')[1].split(')')[0]
          }
        })
      }
      let params = {
        articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr)
      }
      console.error(params)
      this.edit(params)
    },
    update_remark(e, item) {
      if (e == '') {
        this.$message.error('请输入备注')
      } else {
        this.temporaryList = item
        let params = {
          remark: e
        }
        this.edit(params)
      }
    },
    refresh() {
      this.formDataShuru = this.tableData[0]
      console.log(this.formDataShuru)
    },
    //将操作的值赋值予 生成图的dom
    setData(item, type, flag) {
      this.temporaryList = item
      const data = {
        ...item,
      }
      if (data.imageTitle.includes('\n')) {
        data.imageTitle = data.imageTitle.split('\n')
      } else {
        data.imageTitle = [data.imageTitle]
      }
      if (data.imageContent.includes('\n')) {
        data.imageContent = data.imageContent.split('\n')
      } else {
        data.imageContent = [data.imageContent]
      }
      data.footer = ['Bigverse', item.createdTime]
      this.formDataShuru = data
      console.log(data)
      if (flag == 2) {
        this.getCtid(this.temporaryList.imageContent)
      }
      setTimeout(() => {
        this.open(type)
      }, 500)
    },
    async getCtid(str) {
      if (str) {
        let res = await this.$api.searchByCsNameStr({
          str
        });
        if (res.status.code == 0) {
          if (res.result.ctidAndCsNameList != null) {
            res.result.ctidAndCsNameList.forEach((item) => {
              item.nameCtid = `${item.csName}(${item.ctid})`
            })
          }
          this.temporaryList.articleCtidAndCsNameModuleListStr = res.result.ctidAndCsNameList
        }
      }
    },
    async open(type) {
      this.loading = true
      const dom = this.$refs.preview // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, //支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2, // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {
        this.updateImg(canvas.toDataURL('image/jpeg', 1), type)
      }).catch(err => {

      })
    },
    async updateImg(url, type) {
      console.log(url)
      const formData = new FormData()
      let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.loading = false
      if (type) {
        console.log(1)
        window.open(
          `https://cdn-lingjing.nftcn.com.cn/${result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]}`,
          "name",
          "height=820, width=414, top=0, left=2, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=no, status=no"
        );
      } else {
        if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
          this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
            console.log(item.nameCtid)
            if (item.nameCtid) {
              item.csName = item.nameCtid.split('(')[0]
              item.ctid = item.nameCtid.split('(')[1].split(')')[0]
            }
          })
        }
        //请求接口 修改数据
        let params = {
          content: result.mediumImageUrl,
          imageQrCode: this.temporaryList.imageQrCode,
          imageTitle: this.temporaryList.imageTitle,
          templateType: this.temporaryList.templateType,
          imageContent: this.temporaryList.imageContent,
          content: `<p><img src=\"https://cdn-lingjing.nftcn.com.cn/${result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]}\" style=\"width:100%;\" contenteditable=\"false\"/></p>`,
          thumb: this.temporaryList.thumb,
          articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr),
          platformType: this.temporaryList.platformType,
          tapType: this.temporaryList.tapType,
        }
        this.edit(params)
      }
    },
    async getList() {
      const {
        result
      } = await articleTimeList({
        businessType: 1
      })
      let list = [];
      result.adminNftcnTimeArticleListVOS.forEach((item) => {
        if (!this.showSend) {
          if (item.sendStatus == 0) {
            if (item.articleCtidAndCsNameModuleList) {
              item.articleCtidAndCsNameModuleList.forEach((itemm) => {
                itemm.nameCtid = `${itemm.csName}(${itemm.ctid})`
              })
              item.articleCtidAndCsNameModuleListStr = item.articleCtidAndCsNameModuleList
            } else {
              item.articleCtidAndCsNameModuleListStr = []
            }
            list.push(item)
          }
        } else {
          if (item.articleCtidAndCsNameModuleList) {
            item.articleCtidAndCsNameModuleList.forEach((itemm) => {
              itemm.nameCtid = `${itemm.csName}(${itemm.ctid})`
            })
            item.articleCtidAndCsNameModuleListStr = item.articleCtidAndCsNameModuleList
          } else {
            item.articleCtidAndCsNameModuleListStr = []
          }
          list.push(item)
        }
      })
      this.tableData = list
    },
    async add() {
      let createList = {
        thumb: this.typeList[0].list.cover,
        businessType: 1,
        templateType: 10,
        remark: '',
        content: '<p><img src=\"https://cdn-lingjing.nftcn.com.cn/image/20241008/9c5281b3f581db68f759038147a73d1c_828x1604.jpg\" style=\"width:100%;\" contenteditable=\"false\"/></p>',
        imageQrCode: '',
        imageTitle: this.typeList[0].list.title,
        imageContent: this.typeList[0].list.content
      }
      const res = await articleTimeCreate(createList)
      console.log(res)
      if (res.status.code == 0) {
        this.$message.success('新增成功')
        this.getList()
      }
    },
    async edit(data) {
      if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
        this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
          console.log(item.nameCtid)
          if (item.nameCtid) {
            item.csName = item.nameCtid.split('(')[0]
            item.ctid = item.nameCtid.split('(')[1].split(')')[0]
          }
        })
      }
      let req = {
        id: this.temporaryList.id,
        businessType: 1,
        version: this.temporaryList.version,
        articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr),
        tapType: this.temporaryList.tapType,
        ...data
      }
      console.log(req)
      const res = await articleTimeUpdate(req)
      if (res.status.code == 0) {
        if (data.publishTime && this.temporaryList.processStatus != 5) {
          this.$message.warning('时间定时成功，请前往运营状态确认')
          this.getList()
        } else if (data.sendType) {
          this.tableData = []
          this.getList()
        } else {
          this.getList()
          this.$message.success('修改成功')
        }
      } else if (res.status.code == 90889) {
        this.$alert('当前不是最新版本，请保存您的修改内容后刷新', '提醒', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      } else {
        this.getList()
      }
    },
    async nav_delete(item) {
      const res = await articleTimeDeleted({
        id: item.id,
        version: item.version
      })
      if (res.status.code == 0) {
        this.tableData = []
        this.$message.success('删除成功')
        this.getList()
      }
    },
    async nav_topping(item) {
      const res = await articleTimeTopping({
        id: item.id,
        version: item.version
      })
      if (res.status.code == 0) {
        this.tableData = []
        this.$message.success('置顶成功')
        this.getList()
      }
    },
    nav_details(item) {
      this.$router.push({
        name: 'NoticeEdit',
        query: {
          templateId: item.id,
          businessType: item.businessType,
          platformType: item.platformType
        }
      })
    },
    timing(item) {
      if (item.templateType == 1 && item.imageQrCode == '') {
        this.$confirm('白名单没有二维码，确定定时发送？', '提醒', {
          confirmButtonText: '确定定时',
          cancelButtonText: '返回编辑',
          type: 'warning'
        }).then(async () => {
          item.sendType = 1
          item.publishTime = ''
        }).catch(() => {

        });
      } else {
        item.sendType = 1
        item.publishTime = ''
      }
    },
    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    timestampToTime(cjsj) {
      //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      var date = new Date(cjsj);
      var Y = date.getFullYear() + '-';
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
      var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
      var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return Y + M + D
    },
    timeToTimestamp(date) {
      var date = new Date(date);
      var Y = date.getFullYear() + '年';
      var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '月';
      var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + '日';
      var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
      var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return Y + M + D + h + m
    },
    timeToTimestamp2(date) {
      var date = new Date(date);
      var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
      var s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return h + m
    },
    //添加相关藏品
    addCollectionItemnameCtid(item) {
      item.articleCtidAndCsNameModuleListStr.push({
        nameCtid: ''
      })
    },
    //删除相关藏品
    delCollectionItemnameCtid(item, index) {
      item.articleCtidAndCsNameModuleListStr.splice(index, 1)
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    //楼层搜索
    async querySearchAsyncTabLeft(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchTabLeftJoin(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.resultsTabLeft
        console.error(results)
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    async searchTabLeftJoin(str) {
      this.resultsTabLeft = []
      let marketTabId = this.formData.marketTabId.split("/")[0]
      if (str) {
        let res = await this.$api.getMarketTabLeftJoinByTabId({
          marketTabId,
          csname: str
        });
        if (res.status.code == 0) {
          if (res.result != null) {
            res.result.forEach((item) => {
              this.resultsTabLeft.push({
                'value': `${item.name}(${item.ctid})`,
                'weight': item.weight
              })
            })
            console.log(this.resultsTabLeft)
          }
        }
      }
    },
    openClick() {
      this.showSend = !this.showSend
      this.getList()
      console.log(1)
    },
    async getFloor() {
      this.formSchema[4].options = []
      this.biangenSchema[2].options = []
      console.log(this.moduleId)
      let res = await this.$api.java_market_tabList({
        moduleId: this.moduleId
      });
      res.result.list.forEach((item) => {
        this.formSchema[4].options.push({
          'label': item.title,
          'value': `${item.marketTabId}/${item.title}`
        })
        this.biangenSchema[2].options.push({
          'label': item.title,
          'value': `${item.marketTabId}/${item.title}`
        })
      })
      console.log(this.biangenSchema[2].options)
      console.log('================')
      console.log(this.formSchema[4].options)
    },
    openModel(item, index) {
      this.noticeId = item.id
      this.version = item.version
      this.noticeIndex = index
      if (item.templateType == 3) {
        this.isOpenUp = true
      } else if (item.templateType == 10) {
        this.isLimitPrice = true
      } else if (item.templateType == 5) {
        this.isAirDrop = true
      } else if (item.templateType == 12) {
        this.isReplace = true
      } else if (item.templateType == 14) {
        this.isBiangen = true
      } else if (item.templateType == 15) {
        this.isJingPrice = true
      } else if (item.templateType == 20) {
        this.isZhuanShou = true
      } else if (item.templateType == 21) {
        this.isWeiTuo = true
      }
    },
    async articleTime_set(params) { },
    async submit() {
      let name = this.formData.ctid.split('(')[0]
      let ctid = this.formData.ctid.split('(')[1].split(')')[0]
      let marketTabId = this.formData.marketTabId.split('/')[0]
      let marketTabName = this.formData.marketTabId.split('/')[1]
      let startTime = this.timeToTimestamp(this.formData.startTime)
      let openSaleExtra = JSON.stringify({
        ...this.formData,
        ctid,
        marketTabId
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        openSaleExtra
      }
      console.log(params)

      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `藏品《${name}》开放寄售`
        imageContent = `${marketTabName}藏品《${name}》将于${startTime}开放寄售。

《${name}》铸造数量:${res.result.openSale.goodsCount}
《${name}》流通数量:${res.result.openSale.activeNum}
《${name}》销毁数量:${res.result.openSale.destroyNum}

以上藏品暂时为限价藏品，最高寄售限价为¥${this.formData.onSaleMaxPrice}。

敬请各位用户知悉！ 谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isOpenUp = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async submitLimitPrice() {
      let name = this.limitPriceData.ctid.split('(')[0]
      let ctid = this.limitPriceData.ctid.split('(')[1].split(')')[0]
      let startTime = this.timeToTimestamp(this.limitPriceData.startTime)
      let {
        onSaleMaxPrice,
        onSaleMinPrice
      } = this.limitPriceData
      let limitPriceExtra = JSON.stringify({
        ...this.limitPriceData,
        ctid
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        limitPriceExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {

        if (this.limitPriceData.isAddAutoTrade == 1) {
          // this.$api.tradeAdd({
          //   ctid,
          //   onSaleMaxPrice,
          //   onSaleMinPrice
          // })
          let data = {
            ctid,
            dutyType: "AUTO_TRADE",
            isTiming: 1,
            startTime:this.limitPriceData.startTime,
            endTime:this.limitPriceData.endTime,
            extra: {
              priceSortType: "1",
              tradeLimitNum: this.limitPriceData.tradeLimitNum,
              minPriceRange: this.limitPriceData.minPriceRange,
              maxPriceRange: this.limitPriceData.maxPriceRange,
              minLockTime: "30",
              maxLockTime: "60",
              isSleep: 0,
              copyNum: 0,
            },
          }
          data.extra = JSON.stringify(data.extra)
          let res2 = await this.$api.autoDealTask(data)
          if (res2.status.code == 0) {
            setTimeout(() => {
              this.$message.success('自成交任务创建成功')
            }, 500)
          } else {
            return
          }
        }

        let imageTitle, imageContent
        imageTitle = `藏品《${name}》寄售限价调整`
        if (onSaleMaxPrice && onSaleMinPrice) {
          imageContent = `藏品《${name}》限额将于${startTime}进行调整为，调整后藏品最低限额¥${onSaleMinPrice}，最高限额为¥${onSaleMaxPrice}。敬请各位用户知悉！谢谢！`
        } else if (onSaleMaxPrice) {
          imageContent = `藏品《${name}》最高限额将于${startTime}调整为¥${onSaleMaxPrice}。敬请各位用户知悉！谢谢！`
        } else if (onSaleMinPrice) {
          imageContent = `藏品《${name}》最低限额将于${startTime}调整为¥${onSaleMinPrice}。敬请各位用户知悉！谢谢！`
        }
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isLimitPrice = false
        this.limitPriceData = {
          ctid: '',
          startTime: '',
          onSaleMaxPrice: '',
          onSaleMinPrice: ''
        }
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async airDropSubmit() {
      let startTime, endTime, showStartTime, showEndTime;
      let holdName = this.airDropData.holdCtid.split('(')[0]
      let rewardName = this.airDropData.rewardCtid.split('(')[0]
      let holdCtid = this.airDropData.holdCtid.split('(')[1].split(')')[0]
      let rewardCtid = this.airDropData.rewardCtid.split('(')[1].split(')')[0]
      showStartTime = this.timeToTimestamp(this.airDropData.startTime[0])
      showEndTime = this.timeToTimestamp2(this.airDropData.startTime[1])
      startTime = this.airDropData.startTime[0]
      endTime = this.airDropData.startTime[1]
      let {
        holdNum,
        rewardNum
      } = this.airDropData
      let airDropExtra = JSON.stringify({
        ...this.airDropData,
        holdCtid,
        rewardCtid,
        startTime,
        endTime,
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        airDropExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `持有《${holdName}》空投《${rewardName}》`
        imageContent = `每持有${holdNum}份《${holdName}》，即可获得${rewardNum}份《${rewardName}》的空投！

扫描时间:${showStartTime}-${showEndTime}  扫描会持续一段时间，请保持您的藏品处于下架状态，否则将无法享受专属兑换权益。为避免影响您的权益，扫描期间内请勿交易。空投藏品预计将在扫描结束后2小时内到达您的藏品室，请注意查收！

敬请各位用户知悉！谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isAirDrop = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async replaceSubmit() {
      let holdName = this.replaceData.holdCtid.split('(')[0]
      let rewardName = this.replaceData.rewardCtid.split('(')[0]
      let holdCtid = this.replaceData.holdCtid.split('(')[1].split(')')[0]
      let rewardCtid = this.replaceData.rewardCtid.split('(')[1].split(')')[0]
      let startTime = this.timeToTimestamp(this.replaceData.startTime)
      let {
        rewardNum
      } = this.replaceData
      let replaceExtra = JSON.stringify({
        ...this.replaceData,
        holdCtid,
        rewardCtid
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        replaceExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `《${holdName}》将置换为《${rewardName}》`
        imageContent = `《${holdName}》将按照 1:${rewardNum} 下架置换为《${rewardName}》。

《${rewardName}》共铸造XX份，剩余将用于后续活动，并于XX小时内开放寄售，届时最高寄售限额为 ¥ 。

下架置换时间为:${startTime}

为避免影响您的权益，置换时间开始后请勿交易。置换藏品预计将在置换开始后6小时内到达您的藏品室，请注意查收！

敬请各位用户知悉！谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isReplace = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async biangenSubmit() {
      console.log(this.biangenData)
      let name = this.biangenData.ctid.split('(')[0]
      let ctid = this.biangenData.ctid.split('(')[1].split(')')[0]
      let rewardName = this.biangenData.marketTabId.split('/')[1]
      let marketTabId = this.biangenData.marketTabId.split('/')[0]
      let startTime = this.timeToTimestamp(this.biangenData.startTime)
      let showPlaceChangeExtra = JSON.stringify({
        ...this.biangenData,
        ctid,
        marketTabId
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        showPlaceChangeExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let ress = await this.$api.getBelongMarketTab({
          ctid
        });
        console.error(ress.result.marketTabName)
        let imageTitle, imageContent
        imageTitle = `《${name}》展示位变更`
        imageContent = `藏品《${name}》将从【${ress.result.marketTabName}】移至【${rewardName}】。

敬请各位用户知悉！谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isBiangen = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async jingPriceSubmit() {
      console.log(this.jingPriceData)
      let name = this.jingPriceData.ctid.split('(')[0]
      let ctid = this.jingPriceData.ctid.split('(')[1].split(')')[0]
      let startTime = this.timeToTimestamp(this.jingPriceData.startTime)
      let endTime = null;
      if (this.jingPriceData.endTime) {
        endTime = this.timeToTimestamp(this.jingPriceData.endTime)
      }

      let openBiddingExtra = JSON.stringify({
        ...this.jingPriceData,
        ctid,
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        openBiddingExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `藏品《${name}》开启竞价`
        imageContent = `藏品《${name}》将于${startTime}开启竞价，${endTime ? `并将于${endTime}关闭竞价。` : ''}

敬请各位用户知悉！谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isJingPrice = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async zhuanShouSubmit() {
      console.log(this.zhuanShouData)
      let name = this.zhuanShouData.ctid.split('(')[0]
      let ctid = this.zhuanShouData.ctid.split('(')[1].split(')')[0]
      let openForResaleTime = this.timeToTimestamp(this.zhuanShouData.openForResaleTime)
      let {
        openForResaleMinPrice,
        openForResaleMaxPrice
      } = this.zhuanShouData
      let openForResaleExtra = JSON.stringify({
        ...this.zhuanShouData,
        ctid,
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        openForResaleExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `藏品《${name}》开放转售功能`
        imageContent =
          `藏品《${name}》将于${openForResaleTime}开启转售功能。${openForResaleMinPrice ? `转售最低限额为¥${openForResaleMinPrice}，` : ''} 转售最高限额为¥${openForResaleMaxPrice}。敬请各位用户知悉！谢谢！`

        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isZhuanShou = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    async weiTuoSubmit() {
      console.log(this.weiTuoData)
      let name = this.weiTuoData.ctid.split('(')[0]
      let ctid = this.weiTuoData.ctid.split('(')[1].split(')')[0]
      let EntrustedPurchaseTime = this.timeToTimestamp(this.weiTuoData.EntrustedPurchaseTime)
      let EntrustedPurchaseStopTimeShow;
      if (this.weiTuoData.EntrustedPurchaseStopTime) {
        EntrustedPurchaseStopTimeShow = this.timeToTimestamp(this.weiTuoData.EntrustedPurchaseStopTime)
      }
      let {
        EntrustedPurchaseStopTime
      } = this.weiTuoData
      let entrustedPurchaseExtra = JSON.stringify({
        ...this.weiTuoData,
        ctid,
      })
      let params = {
        id: this.noticeId,
        version: this.version,
        entrustedPurchaseExtra
      }
      console.log(params)
      let res = await this.$api.articleTimeSet(params);
      if (res.status.code == 0) {
        let imageTitle, imageContent
        imageTitle = `藏品《${name}》开放委托购买`
        imageContent =
          `藏品《${name}》将于${EntrustedPurchaseTime}开放委托购买功能${EntrustedPurchaseStopTimeShow ? `并将于${EntrustedPurchaseStopTimeShow}关闭委托购买功能` : ''}。敬请各位用户知悉！谢谢！`
        this.tableData[this.noticeIndex].imageTitle = imageTitle
        this.tableData[this.noticeIndex].imageContent = imageContent
        this.tableData[this.noticeIndex].version = this.version + 1
        console.log(this.tableData[this.noticeIndex])
        this.isWeiTuo = false
        this.setData(this.tableData[this.noticeIndex], false, 2)
      }
    },
    nav_back() {
      this.isAirDrop = false
      this.isReplace = false
      this.isLimitPrice = false
      this.isOpenUp = false
      this.isBiangen = false
      this.isJingPrice = false
      this.isZhuanShou = false
      this.isWeiTuo = false
    },
    MarketTabLeftJoinByTabId(e) {
      console.log(e.weight)
      if (e.weight > 1) {
        this.formData.marketTabWeight = e.weight - 1
      } else {
        this.formData.marketTabWeight = e.weight
      }

    },
    changeTapType(e, item) {
      this.temporaryList = item
      let params = {
        tapType: e
      }
      this.edit(params)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-textarea textarea {
  height: 140px !important;
}

::v-deep.el-input--prefix .el-input__inner {
  width: 140px !important;
}

::v-deep.el-date-editor.el-input {
  width: 140px !important;
}

@font-face {
  font-family: 'fonts';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
}

@font-face {
  font-family: 'fonts_title';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newFont.OTF');
}

.flex {
  display: flex;
  justify-content: flex-start;
  margin-top: -100000px;

  .shuru {
    width: 500px;
    height: 812px;
    width: 414px;
    padding: 40px 0px;
    border: 1px solid #ccc;
  }

  .yulan {
    width: 100px;
    height: 812px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .preview_details {
    width: 414px;
    background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
    background-size: 100%;
    font-family: 'fonts';
    padding-bottom: 16px;

    .tips {
      line-height: 0px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .head_text {
      width: 389px;
      margin: 30px auto 0px auto;

      img {
        width: 201px;
        height: auto
      }

      .title_div {
        background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241021/538a071da3d57a3d07c551a3d0284ad4_1015x521.png');
        background-repeat: no-repeat;
        width: 389px;
        height: 200px;
        background-size: 100%;
        margin-top: 7px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .bode {
          position: absolute;
          right: -10px;
          bottom: 31px;

          img {
            width: 49px;
            height: 41px;
          }
        }

        .title {
          width: 210px;
          text-align: center;
          font-size: 21px;
          min-height: 78px;
          color: #fff;
          font-family: 'fonts_title';
          margin-bottom: 20px;
        }
      }
    }

    .bigverse {
      img {
        width: 274px;
        height: 42px
      }
    }

    .bigverse_top {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_bottom {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_body {
      width: 414px;
      // min-height: 421px;
      margin: 0 auto;
      background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230612/bb4106b2df078e4b75df39922f6f09f3_400x244.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 1px 42px 0px 42px;
      color: #fff;
      letter-spacing: 2px;

      .neirong {
        .title {
          font-size: 18px;
          margin-bottom: 30px;
        }

        >div {
          margin-top: 10px;
          line-height: 22px;
          font-size: 16px;
        }
      }

      .official_msg {
        margin-top: 30px;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0px;

        .title {
          margin-bottom: 10px;
        }

        color:#20AFAC;
      }

      .footer {
        margin-top: 40px;
        text-align: right;
        font-size: 18px;
        letter-spacing: 2px;
      }
    }

    .code {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 40px 0px;

      img {
        width: 100%;
        height: auto;
      }
    }
  }
}

.width_footer {
  margin-top: 10px;
}

.noSpacing {
  letter-spacing: 0px;
}

::v-deep.red_input textarea {
  color: rgb(255, 29, 29) !important;
}

.el-radio {
  margin-right: 0px;
}

.marginLeft-150 {
  margin-left: -100px;
}

.dialog-footer {
  text-align: center;
}
</style>
