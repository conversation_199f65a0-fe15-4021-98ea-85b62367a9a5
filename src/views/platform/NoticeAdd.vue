<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #subTitle="scope">
        <el-input placeholder="请输入二级标题" style="width:300px" @change="change" v-model="formData.subTitle"></el-input>
      </template>
      <template #strCtid="scope">
        <el-card class="box-card" style="width:440px;">
          <div class="" v-for="(item, index) in formData.articleCtidAndCsNameModuleListStr">
            <el-autocomplete style="width:340px;margin-bottom:10px;" v-model="item.nameCtid"
              :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect"></el-autocomplete>
            <el-button type="text" style="margin-left:10px;" size="mini"
              @click="delCollectionItemnameCtid(item, index)">删除</el-button>
          </div>
          <el-button type="primary" style="width:100%;" plain @click="addCollectionItemnameCtid()">添加相关藏品</el-button>
          <!-- <el-button  type="primary" style="margin-top:10px;" size="mini" @click="selete_list()">查看数据接口</el-button> -->
        </el-card>
      </template>
      <template v-slot:detail>
        <span style="color:rgb(201 53 53);font-size:12px">温馨提示：修改链接类型跳转详情内容会被清空哦</span>
        <!-- <Editor :catchData="catchData" :content="formData.content"></Editor> -->
        <div class="phoneBoxFlex">
          <Editor :catchData="catchData" :content="formData.content"></Editor>


          <div class="addRight">
            <div class="box_content">
              <div class="addRight_head">
                <div>
                  <img class="imgOne"
                    src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png"
                    alt="" srcset="">
                </div>
                <div style="padding: 0px 0px 0px 0px;">
                  <div>
                    <img class="imgTwo"
                      src="https://cdn-lingjing.nftcn.com.cn/image/20230606/f10a9ed24154e94377789225007ccd82_400x43.png"
                      alt="" srcset="">
                  </div>
                  <div class="contentDiv">
                    <div class="title">
                      <div>
                         {{formData.title}}
                      </div>
                    </div>
                    <div>
                      <img class="imgBd"
                        src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png"
                        alt="" srcset="">
                    </div>
                  </div>

                </div>
              </div>
              <div class="addRight_box">
                <div class="addRight_content" v-html="richTxt"></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import {
    mapActions
  } from 'vuex'
  import {
    noticeEdit,
    noticeAdd
  } from '@/api/hongyan'

  export default {
    name: 'platformPublish',
    components: {
      CommonForm,
      Editor
    },
    data() {
      return {
        richTxt:'',
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          flagBold: 0,
          flagHeadline: 0,
          flagPicture: 1,
          flagRecommend: 0,
          flagRoll: 0,
          flagSlideShow: 0,
          flagSpecialRecommend: 0,
          isAccessOpenPlatform: 1,
          status: 1,
          thumb: '',
          content: '',
          isPush: 1,
          linkType: 'WEB_LINK',
          type: '',
          sort: 0,
          articleCtidAndCsNameModuleListStr: [],
          tapType:0
        },
        formSchema: [
          // {
          //   type: 'input',
          //   label: '平台编号：',
          //   placeholder: '请输入平台编号',
          //   field: 'cid',
          //   rules: [{ required: true, message: '请输入平台编号', trigger: 'blur' }]
          // },
          {
            type: 'img',
            label: '图片：',
            placeholder: '请选择图片',
            field: 'thumb',
            limit: 1,
            rules: [{
              required: true,
              message: '请选择图片',
              trigger: 'change'
            }]
          },
          {
            type: 'input',
            label: '标题：',
            placeholder: '请输入平台标题',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入平台标题',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '二级标题：',
            placeholder: '请输入平台二级标题',
            field: 'subTitle',
            slot: 'subTitle',
            rules: [{
              required: true,
              message: '请输入二级标题',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '链接类型：',
            field: 'linkType',
            options: [{
                label: '无链接',
                value: 'NO_LINK'
              },
              {
                label: '站内链接',
                value: 'INNER_LINK'
              },
              {
                label: '网页链接',
                value: 'WEB_LINK'
              },
              {
                label: '跳转详情',
                value: 'DETAIL_LINK'
              },
            ],
          },
          {
            type: 'input',
            label: '跳转路径h5以及网页跳转:',
            placeholder: '请输入跳转路径h5以及网页跳转:',
            field: 'h5Link',
            rules: [{
              required: true,
              message: '请输入跳转路径h5以及网页跳转:',
              trigger: 'blur'
            }],
            show: {
              relationField: 'linkType',
              value: ['INNER_LINK', 'WEB_LINK']
            },
          },
          {
            type: 'input',
            label: 'IOS 安卓原生跳转路径：',
            placeholder: '请输入IOS 安卓原生跳转路径：',
            field: 'appLink',
            rules: [{
              required: true,
              message: '请输入IOS 安卓原生跳转路径：',
              trigger: 'blur'
            }],
            show: {
              relationField: 'linkType',
              value: 'INNER_LINK'
            },
          },
          {
            label: '详情：',
            slot: 'detail',
            // rules: [{
            // 	required: true,
            // 	message: '请输入详情',
            // 	trigger: 'change'
            // }],
            show: {
              relationField: 'linkType',
              value: 'DETAIL_LINK'
            },
          },
          {
            label: '图文模式：',
            slot: 'image',
            // rules: [{
            // 	required: true,
            // 	message: '请输入详情',
            // 	trigger: 'change'
            // }],
            show: {
              relationField: 'linkType',
              value: 'IMAGE_LINK'
            },
          },
          {
            type: 'select',
            label: '状态：',
            placeholder: '请选择公告状态',
            field: 'status',
            options: [{
                label: '下架',
                value: 0
              },
              {
                label: '发布',
                value: 1
              }
            ],
            rules: [{
              required: true,
              message: '请选择公告状态',
              trigger: 'blur'
            }]
          },
          // {
          //   type: 'radio',
          //   label: '是否置顶：',
          //   field: 'sort',
          //   options: [{
          //       label: '置顶',
          //       value: 1
          //     },
          //     {
          //       label: '不置顶',
          //       value: 0
          //     }
          //   ]
          // },
          {
            type: 'input',
            label: '排序：',
            placeholder: '请输入排序',
            field: 'sort',
            rules: [{
              required: true,
              message: '请输入排序',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '同步到金刚区：',
            field: 'type',
            options: [{
                label: '暂无同步',
                value: ''
              }, {
                label: '上新公告',
                value: 'NEW_GOODS'
              },
              {
                label: '合成公告',
                value: 'MERGE'
              },
              {
                label: '白名单公告',
                value: 'WHITE_USER_LIST'
              },
            ],
          },
          {
            type: 'radio',
            label: '类型：',
            field: 'tapType',
            options: [{
              label: '藏品公告',
              value: 0
            }, {
              label: 'bit指数公告',
              value: 1
            },
            {
              label: '美股公告',
              value: 2
            },
            ],
          },
          // {
          //   type: 'radio',
          //   label: '是否加粗：',
          //   field: 'flagBold',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '头条：',
          //   field: 'flagHeadline',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '详情是否图片',
          //   field: 'flagPicture',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '推荐',
          //   field: 'flagRecommend',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '是否滚动：',
          //   field: 'flagRoll',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '幻灯：',
          //   field: 'flagSlideShow',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          // {
          //   type: 'radio',
          //   label: '特别推荐：',
          //   field: 'flagSpecialRecommend',
          //   options: [
          //     {
          //       label: '是',
          //       value: 1
          //     },
          //     {
          //       label: '不是',
          //       value: 0
          //     }
          //   ]
          // },
          {
            type: 'radio',
            label: '是否接入开放平台：',
            field: 'isAccessOpenPlatform',
            options: [{
                label: '是',
                value: 1
              },
              {
                label: '不是',
                value: 0
              }
            ]
          },
          {
            type: 'datetime',
            label: '发布时间：',
            field: 'publishTime',
            rules: [{
              required: true,
              message: '请输入发布时间',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '是否push推送：',
            field: 'isPush',
            options: [{
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          {
            label: '相关藏品：',
            slot: 'strCtid',
          },
          {
            type: 'action'
          }
        ],
        results: []
      }
    },
    mounted() {
      const {
        templateId,
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        // noticeEdit({
        //   this.templateId
        // })
        // const { status, result } = await this.$api.digitalCollectionTemplatePublishDetail({ templateId: this.templateId })
        // if (status.code === 0) {
        //   this.formData = {
        //     ...result
        //   }
        // }
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        if (data.articleCtidAndCsNameModuleList != null) {
          data.articleCtidAndCsNameModuleList.forEach((item) => {
            item.nameCtid = `${item.csName}(${item.ctid})`
          })
          data.articleCtidAndCsNameModuleListStr = data.articleCtidAndCsNameModuleList
        } else {
          data.articleCtidAndCsNameModuleListStr = []
        }

        this.formData = {
          ...data
        }
        this.richTxt=data.content
      },
      async submit() {
        this.formData.publishTime = this.formData.publishTime
        if (this.formData.articleCtidAndCsNameModuleListStr) {
          this.formData.articleCtidAndCsNameModuleListStr.forEach((item) => {
            if (item.nameCtid) {
              item.csName = item.nameCtid.split('(')[0]
              item.ctid = item.nameCtid.split('(')[1].split(')')[0]
            }
          })
        }
        const data = {
          ...this.formData,
          articleCtidAndCsNameModuleListStr: JSON.stringify(this.formData.articleCtidAndCsNameModuleListStr)
        }
        console.log(data)
        data.templateId = this.templateId
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.formData.publishTime = this.formData.publishTime
          if (this.templateId) {
            let res = await this.$api.noticeEdit(data);
            if (data.isPush === 1) {
              const mainOrigin = process.env.VUE_APP_MAIN_ORIGIN
              data.link = `${mainOrigin}/h5/#/pagesA/project/official/detail?id=${res.result.id}`
              this.nav_push(data)
            } else {
              this.routerBack()
            }
          } else {
            let res = await this.$api.noticeAdd(data);
            if (data.isPush === 1) {
              const mainOrigin = process.env.VUE_APP_MAIN_ORIGIN
              data.link = `${mainOrigin}/h5/#/pagesA/project/official/detail?id=${res.result.id}`
              this.nav_push(data)
            } else {
              this.routerBack()
            }
          }
        })
      },
      // 同步到push
      nav_push(data) {
        this.$confirm('公告已经发布成功？', '是否前往push一键推送', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.$router.push({
            name: 'addMsgPush',
            query: data
          })
        })
      },
      change(e) {
        console.log(e)
        this.getCtid(e)
      },
      async getCtid(str) {
        let res = await this.$api.searchByCsNameStr({
          str
        });
        console.log(res)
        if (res.status.code == 0) {
          if (res.result.ctidAndCsNameList != null) {
            res.result.ctidAndCsNameList.forEach((item) => {
              item.nameCtid = `${item.csName}(${item.ctid})`
            })
          }
          if (res.result.ctidAndCsNameList == null) {
            this.formData.articleCtidAndCsNameModuleListStr = []
          } else {
            this.formData.articleCtidAndCsNameModuleListStr = res.result.ctidAndCsNameList
          }
        } else {

        }
      },
      //添加相关藏品
      addCollectionItemnameCtid() {
        this.formData.articleCtidAndCsNameModuleListStr.push({
          nameCtid: ''
        })
        console.log(this.formData)
      },
      //删除相关藏品
      delCollectionItemnameCtid(item, index) {
        this.formData.articleCtidAndCsNameModuleListStr.splice(index, 1)
      },
      async querySearchAsync(queryString, cb) {
        var restaurants = this.restaurants;
        this.searchNew(queryString)
        let results = []
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
          results = this.results
          cb(results);
        }, 1000);
      },
      handleSelect(item) {
        console.log(item);
      },
      async searchNew(str) {
        this.results = []
        if (str) {
          let res = await this.$api.searchPgc({
            name: str
          });
          if (res.status.code == 0) {
            if (res.result.list != null) {
              res.result.list.forEach((item) => {
                this.results.push({
                  'value': `${item.name}(${item.ctid})`,
                })
              })
              console.log(this.results)
            }
          }
        }
      },
      //调试查看数据
      selete_list() {
        console.log(this.formData)
        // this.formData.articleCtidAndCsNameModuleListStr.forEach((item)=>{
        //     item.csName=item.nameCtid.split('(')[0]
        //     item.ctid=item.nameCtid.split('(')[1].split(')')[0]
        // })
        // console.table(JSON.stringify(this.formData.articleCtidAndCsNameModuleListStr))
      }
    }
  }
</script>

<style lang="scss" scoped>
  @font-face {
  	font-family: 'fonts';
  	src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
  }
  .page {
    padding-top: 80px;

    .phoneBoxFlex {
      display: flex;
      position: relative;


    }

    .addRight {
      overflow-wrap: break-word;
      margin-left: 30px;
      position: absolute;
      height: 860px;
      background: url("../../assets/img/ios.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 800px;
      top: 0px;
      text-align: left;
      padding: 30px 40px;

      // display: flex;
      // align-items: center;
      // justify-content: center;
      .box_content {
        width: 414px;
        height: 828px;
        overflow: auto;
        -ms-overflow-style: none;
        /* IE 和 Edge */
        scrollbar-width: none;
        /* Firefox */
        overflow-y: scroll;
        /* 保持内容可滚动 */
        background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
        border-radius: 40px;

        .addRight_head {
          width: 95%;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          // margin-top: 7px;
          // position: relative;

          margin: 0px auto 0px auto;
          border-radius: 20px 40px 40px 40px;
          color: #fff;


          .imgOne {
            width: 100%;
            height: auto;
          }

          .imgTwo {
            width: 201px;
            margin-left: 20px;
            margin-top: 30px;
          }

          .contentDiv {
            background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241021/538a071da3d57a3d07c551a3d0284ad4_1015x521.png');
            background-repeat: no-repeat;
            width: 389px;
            height: 200px;
            background-size: 100%;
            margin-top: 7px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            .title {
             width: 210px;
             text-align: center;
             font-size: 21px;
             min-height: 78px;
             color: #fff;
             font-family: 'fonts_title';
             margin-bottom:20px;
             display: flex;
             justify-content: center;
             align-items: center;
            }

            .imgBd{
               width: 49px;
               height: 41px;
                position: absolute;
                right: -10px;
                bottom: 31px;
            }
          }




        }

        .addRight_box {
          padding: 0px 14px 20px 14px;
          position: relative;
          margin-top: 0px;
          color: #fff;

          .addRight_content {
            padding: 0px 0px 20px 0px;
              p{
                font-size:16px;
                margin:0px !important;
                font-family:'fonts';
                line-height:1.5;
              }
          }
        }
      }
    }
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {

    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .el-textarea {
    textarea {
      height: 200px !important;
    }
  }
</style>
