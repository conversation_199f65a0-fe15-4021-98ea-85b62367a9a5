<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="open('add','')">新增公告</el-button>
      </template>
      <template #scanCount="scope">
        <el-button type="text" @click="openScanCount(scope.row)">{{ scope.row.scanCount }}</el-button>
      </template>
      <template #url="scope">
          {{`https://www.nftcn.com/bzl/#/pagesA/project/official/detail?id=${scope.row.id}`}}
      </template>
      <template #action="scope">
        <el-button @click="open('edit',scope.row)" type="text">编辑</el-button>
        <el-button @click="statusToggle(scope.row.id, scope.row.status)" type="text">
          {{ scope.row.status === 0 ? '发表' : '下架' }}
        </el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, prev, pager, next, jumper" :total="page.totalCount" :current-page="page"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange">
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="800px" center @close="closeDialog" destroy-on-close
      :close-on-click-modal="false">
      <CommonForm :schema="formSchema"  :data="formData" :submit="submit" label-width="180px"
        :isBack="true" @nav_back="closeDialog">
      </CommonForm>
    </el-dialog>
    <!-- 	<el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
// import { mapActions } from 'vuex'
import {
  articleList,
  noticePublish,
  updateScanCount
} from '@/api/hongyan'

export default {
  name: 'platform',
  components: {
    CommonQuery,
    CommonTable,
    CommonForm
  },
  data() {
    return {
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }, // 分页数据
      query: {
        autherName: '',
        autherId: '',
        seoKeywords: '',
        seoTitle: '',
        subTitle: '',
        title: '',
        isAccessOpenPlatform: '',
        tapType:''
      },
      dialogVisible:false,
      title:"新增公告",
      isEdit:false,
      tableSchema: [ // 表格架构
        {
          label: 'id',
          field: 'id',
          width: 40,
          align: 'center'
        },
        {
          label: '作者',
          field: 'authorName',
          width: '120px',
          align: 'center'
        },
        {
          label: '标题',
          field: 'title',
          width: '200px',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          label: '图片',
          field: 'thumb',
          type: 'img',
          width: '120px',
          align: 'center'
        },
        {
          label: '海报',
          field: 'content',
          type: 'img',
          width: '120px',
          align: 'center'
        },
        {
          label: '公告链接',
          slot: 'url',
          width: '200px',
        },
        {
          label: '状态',
          field: 'status',
          type: 'tag',
          align: 'center',
          tagMap: {
            0: {
              label: '草稿',
              tagType: 'danger'
            },
            1: {
              label: '已发布',
              tagType: 'success'
            }
          },
          width: '90px'
        },
        {
          label: '发布时间',
          field: 'publishTime',
          width: '200px',
          align: 'center',
          align: 'center'
        },
        {
          label: '阅读次数',
          width: '80px',
          align: 'center',
          slot: 'scanCount'
        },
        {
          label: '创建时间',
          field: 'createdAt',
          width: '160px',
          align: 'center'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '140px',
          fixed: 'right'
        }
      ],
      tableData: [], // 表格数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '作者昵称：',
          placeholder: '请输入作者昵称',
          field: 'autherName'
        },
        {
          type: 'input',
          label: '标题：',
          placeholder: '请输入标题',
          field: 'title'
        },
        {
          type: 'datetimerange',
          label: '发布时间：',
          field: 'publishTimeStart',
          field2: 'publishTimeEnd',
        },
      ],
      formSchema: [
        {
          type: 'input',
          label: '公告标题：',
          placeholder: '请输入公告标题',
          field: 'title',
          rules: [{
            required: true,
            message: '请输入公告标题',
            trigger: 'blur'
          }],
        },
        {
          type: 'img',
          label: '公告配图(686*328)：',
          placeholder: '请输入公告配图',
          field: 'thumb',
          isHD:true,
          limit:1,
          rules: [{
            required: true,
            message: '请输入公告标题',
            trigger: 'blur'
          }],
        },
        {
          type: 'img',
          label: '公告海报(不能大于3MB)：',
          placeholder: '请输入公告海报',
          field: 'content',
          isHD:true,
          limit:1,
          rules: [{
            required: true,
            message: '请输入公告海报',
            trigger: 'blur'
          }],
        },
        {
          type: 'datetime',
          label: '发送时间：',
          field: 'publishTime',
          placeholder: '请输入发送时间',
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          },
        },
        {
          type: 'action',
          exclude: ['reset']
        }
      ],
      formData: {
          status: 1,
          thumb: "",
          content: "",
          linkType: "IMAGE_URL",
          tapType: 0,
          title: "",
          publishTime: "",
          subTitle:"",
          sort:0,
          platformType:2,
          isAccessOpenPlatform:0,
          articleId:""
      },
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    toTemplatePage(item = {}) {
      // this.$store.commit('SAVE_INFO', item)
      // mapActions('save_stateInfo', item)
      if (item.businessType && item.businessType != 0) {
        this.$router.push({
          name: 'NoticeEdit',
          query: {
            templateId: item.id,
            businessType: item.businessType,
            platformType:item.platformType
          }
        })
      } else {
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      }

    },
    scrollEvent(e) {
      console.log(e.y) // 获取目标元素的滚动高度
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data
      this.getList(true)
    },
    // 分页改变
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    getList() {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: this.page.pageNum,
        platformType:2
      }
      articleList(params).then(res => {
        const data = res.result
        this.tableData = data.list
        this.page.totalCount = data.totalCount
        this.page.pageSize = data.pageSize
        this.page.pageCount = data.pageCount
      })
    },
    // 发表/下架
    statusToggle(id, status) {
      const changeText = status === 0 ? '发表' : '下架'
      this.$confirm(`确定${changeText}该条公告吗？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        noticePublish({
          id,
          status: status === 0 ? 1 : 0
        }).then((res) => {
          this.$message.success(res.status.msg)
          this.getList()
        })
      })
    },
    openScanCount(item) {
      this.$prompt('请输入你要修改的阅览量', '提示', {
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({
        value
      }) => {
        this.scanCount(value, item.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    scanCount(scanCount, id) {
      updateScanCount({
        scanCount,
        id
      }).then(res => {
        if (res.status.code == 0) {
          this.$message.success('修改成功')
          this.getList()
        }
      })
    },
    async submit(){
       let res;
      if(this.isEdit){
        res = await this.$api.articleEdit({
            ...this.formData,
            subTitle:this.formData.title,

        });
      }else{
       res = await this.$api.noticeAdd({
            ...this.formData,
            subTitle:this.formData.title
        });
      }
      if(res.status.code == 0){
        this.dialogVisible = false
        this.formData=  {
          status: 1,
          thumb: "",
          content: "",
          linkType: "IMAGE_URL",
          tapType: 0,
          title: "",
          publishTime: "",
          subTitle:"",
          sort:0,
          platformType:2,
          isAccessOpenPlatform:0
      }
       this.getList()
       this.$message.success('成功')
      }
    },
    open(type,item){
      if(type=='add'){
        this.title = '新增公告'
        this.dialogVisible = true
        this.isEdit=false
        this.formData=  {
            status: 1,
            thumb: "",
            content: "",
            linkType: "IMAGE_URL",
            tapType: 0,
            title: "",
            publishTime: "",
            subTitle:"",
            sort:0,
            platformType:2,
            isAccessOpenPlatform:0
        }
      }else{
         this.articleId = item.id
         this.title = '修改公告'
         this.dialogVisible = true
         this.isEdit=true
         this.formData = item
      }
    },
    closeDialog(){
       this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped></style>
