<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submitConfig" :data="formData" :schema="formSchema" label-width="300px">
      <template #title="scope">
        <el-input type="textarea" placeholder="请输入标题" style="width:300px" maxlength="30" v-model="formDataShuru.imageTitle"></el-input>
      </template>
      <template #subTitle="scope">
        <el-input type="textarea" placeholder="请输入二级标题" style="width:300px" @change="change"
          v-model="formDataShuru.imageContent"></el-input>
      </template>
      <template #strCtid="scope">
        <el-card class="box-card" style="width:440px;">
          <div class="" v-for="(item, index) in formData.articleCtidAndCsNameModuleListStr">
            <el-autocomplete style="width:340px;margin-bottom:10px;" v-model="item.nameCtid"
              :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect"></el-autocomplete>
            <el-button type="text" style="margin-left:10px;" size="mini"
              @click="delCollectionItemnameCtid(item, index)">删除</el-button>
          </div>
          <el-button type="primary" style="width:100%;" plain @click="addCollectionItemnameCtid()">添加相关藏品</el-button>
          <!-- <el-button  type="primary" style="margin-top:10px;" size="mini" @click="selete_list()">查看数据接口</el-button> -->
        </el-card>
      </template>
      <template v-slot:detail>
        <Editor :catchData="catchData" :content="formData.content"></Editor>
        <div class="addRight">
          <div class="box_content">
            <div class="addRight_head">
              <div>
                <img class="imgOne"
                  src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png"
                  alt="" srcset="">
              </div>
              <div style="padding: 0px 0px 0px 0px;">
                <div>
                  <img class="imgTwo"
                    src="https://cdn-lingjing.nftcn.com.cn/image/20230606/f10a9ed24154e94377789225007ccd82_400x43.png"
                    alt="" srcset="">
                </div>
                <div class="contentDiv">
                  <div class="title">
                    <div v-for="(item, index) in formDataShuru1.imageTitle">
                      {{ item }}
                    </div>
                  </div>
                  <div>
                    <img class="imgBd"
                      src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png"
                      alt="" srcset="">
                  </div>
                </div>

              </div>
            </div>
            <div class="addRight_box">
              <div class="addRight_content" v-html="richTxt"></div>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:image>
        <div class="flex" style="width:1200px">
          <div class="shuru">
            <common-form :submit="submit" :data="formDataShuru" :schema="formSchemaShuru" label-width="80px">
            </common-form>
          </div>
          <div class="yulan">
            <el-button type="primary">预览</el-button>
          </div>
          <div>
            <div class="preview_details" ref="preview">
              <div class="tips">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png">
              </div>
              <div class="head_text">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20241030/3b11d88245184e64bd4b91327f8f1e11_524x31.png">
                <div class="title_div">
                  <div class="title">
                    <div v-for="(item, index) in formDataShuru1.imageTitle">
                      {{ item }}
                    </div>
                  </div>
                  <div class="bode">
                    <img
                      src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png">
                  </div>
                </div>
              </div>
             <!-- <div class="bigverse">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/667ae9be41db64bcb3f155df69642549_400x60.png"
                  alt="" srcset="">
              </div> -->
              <div class="bigverse_top">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/523c2b56ae43a1e6093d2de7700de496_400x48.png"
                  alt="" srcset="">
              </div>
              <div class="bigverse_body">
                <div class="neirong">
                  <div class="title">
                    亲爱的Bigverse用户：
                    <br>
                  </div>
                  <div v-for="(item, index) in formDataShuru1.imageContent"
                    :style="{ 'margin-top': item == '' ? '0px' : '10px' }">
                    <div v-if="item == ''">
                      <br>
                    </div>
                    <div v-else>
                      {{ item }}
                    </div>
                  </div>
                </div>
                <div class="code" v-if="formDataShuru1.imageQrCode != ''">
                  <img :src="formDataShuru1.imageQrCode" alt="">
                </div>
                <div class="official_msg">
                  <div class="title">官方风险提示:</div>
                  <div>
                    Bigverse平台发售的数字藏品仅具备收藏、欣赏价值，不具有投资、保值、增值、收益功能，平台对藏品价格不构成任何指导意义，请谨慎购买及参与活动，严防炒作。
                  </div>
                </div>
                <div class="footer">
                  <div v-for="(item, index) in formDataShuru1.footer" :class="{ 'noSpacing': index == 0 }">{{ item, }}
                  </div>
                </div>
              </div>
              <div class="bigverse_bottom">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/a15b6394a0ef2fdd85c1c8ec26d25d95_400x67.png"
                  alt="" srcset="">
              </div>
              <!-- <div class="title" >
                {{item}}
              </div>
              <div class="body">
                <div class="text">
                  亲爱的<span class="noSpacing">Bigverse</span>用户：
                  <br>
                  <br>
                </div>
                <div class="text" v-for="(item,index) in formDataShuru1.imageContent">
                  <div v-if="item==''">
                    </br>
                  </div>
                  <div v-else>
                    {{item}}
                  </div>
                </div>
                <div class="code" v-if="formDataShuru1.imageQrCode!=''">
                  <img :src="formDataShuru1.imageQrCode" alt="">
                </div>
                <div class="footer">
                  <div v-for="(item,index) in formDataShuru1.footer" :class="{'noSpacing':index==0}">{{item,}}</div>
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
import Editor from '@/components/editoritem/editoritem'
import html2canvas from 'html2canvas';
import CommonForm from '@/components/CommonForm'
import {
  mapActions
} from 'vuex'
import {
  noticeEdit,
  noticeAdd,
  getArticleDetai
} from '@/api/hongyan'
import {
  uploadImgToOss
} from '@/api/ossCenter'
export default {
  name: 'NoticeEdit',
  components: {
    CommonForm,
    Editor
  },
  data() {
    return {
      isDetail: false, // 详情
      templateId: null, // 活动编号
      formData: {
        flagBold: 0,
        flagHeadline: 0,
        flagPicture: 1,
        flagRecommend: 0,
        flagRoll: 0,
        flagSlideShow: 0,
        flagSpecialRecommend: 0,
        isAccessOpenPlatform: 1,
        status: 1,
        thumb: '',
        content: '',
        isPush: 1,
        linkType: 'IMAGE_LINK',
        type: '',
        sort: 6,
        articleCtidAndCsNameModuleListStr: [],
        platformType: '1',
        tapType:0
      },
      formSchema: [
        // {
        //   type: 'input',
        //   label: '平台编号：',
        //   placeholder: '请输入平台编号',
        //   field: 'cid',
        //   rules: [{ required: true, message: '请输入平台编号', trigger: 'blur' }]
        // },
        {
          type: 'img',
          label: '图片：',
          placeholder: '请选择图片',
          field: 'thumb',
          limit: 1,
          rules: [{
            required: true,
            message: '请选择图片',
            trigger: 'change'
          }]
        },
        {
          type: 'textarea',
          label: '标题：',
          slot: 'title',
          placeholder: '请输入平台标题',
          field: 'title',
          rules: [{
            required: true,
            message: '请输入平台标题',
            trigger: 'blur'
          }]
        },
        {
          type: 'input',
          label: '二级标题：',
          placeholder: '请输入平台二级标题',
          field: 'subTitle',
          slot: 'subTitle',
          show: {
            relationField: 'linkType',
            value: 'IMAGE_LINK'
          },
        },
        {
          type: 'radio',
          label: '链接类型：',
          field: 'linkType',
          options: [{
            label: '无连接',
            value: 'NO_LINK'
          },
          {
            label: '站内链接',
            value: 'INNER_LINK'
          },
          {
            label: '网页链接',
            value: 'WEB_LINK'
          },
          {
            label: '图文详情',
            value: 'IMAGE_LINK'
          },
          {
            label: '跳转详情',
            value: 'DETAIL_LINK'
          },
          ],
        },
        {
          type: 'input',
          label: '跳转路径h5以及网页跳转:',
          placeholder: '请输入跳转路径h5以及网页跳转:',
          field: 'h5Link',
          rules: [{
            required: true,
            message: '请输入跳转路径h5以及网页跳转:',
            trigger: 'blur'
          }],
          show: {
            relationField: 'linkType',
            value: ['INNER_LINK', 'WEB_LINK']
          },
        },
        {
          type: 'input',
          label: 'IOS 安卓原生跳转路径：',
          placeholder: '请输入IOS 安卓原生跳转路径：',
          field: 'appLink',
          rules: [{
            required: true,
            message: '请输入IOS 安卓原生跳转路径：',
            trigger: 'blur'
          }],
          show: {
            relationField: 'linkType',
            value: 'INNER_LINK'
          },
        },
        {
          label: '详情：',
          slot: 'detail',
          // rules: [{
          // 	required: true,
          // 	message: '请输入详情',
          // 	trigger: 'change'
          // }],
          show: {
            relationField: 'linkType',
            value: 'DETAIL_LINK'
          },
        },
        {
          label: '图文模式：',
          slot: 'image',
          // rules: [{
          // 	required: true,
          // 	message: '请输入详情',
          // 	trigger: 'change'
          // }],
          show: {
            relationField: 'linkType',
            value: 'IMAGE_LINK'
          },
        },
        {
          type: 'select',
          label: '状态：',
          placeholder: '请选择公告状态',
          field: 'status',
          options: [{
            label: '下架',
            value: 0
          },
          {
            label: '发布',
            value: 1
          }
          ],
          rules: [{
            required: true,
            message: '请选择公告状态',
            trigger: 'blur'
          }]
        },
        {
          type: 'number-input',
          label: '排序：',
          placeholder: '请输入排序',
          field: 'sort',
          rules: [{
            required: true,
            message: '请输入排序',
            trigger: 'blur'
          }]
        },
        {
          type: 'radio',
          label: '同步到金刚区：',
          field: 'type',
          options: [{
            label: '暂无同步',
            value: ''
          }, {
            label: '上新公告',
            value: 'NEW_GOODS'
          },
          {
            label: '合成公告',
            value: 'MERGE'
          },
          {
            label: '白名单公告',
            value: 'WHITE_USER_LIST'
          },
          ],
        },
        {
          type: 'radio',
          label: '类型：',
          field: 'tapType',
          options: [{
            label: '藏品公告',
            value: 0
          }, {
            label: 'bit指数公告',
            value: 1
          },
          {
            label: '美股公告',
            value: 2
          },
          ],
        },
        // {
        //   type: 'radio',
        //   label: '是否加粗：',
        //   field: 'flagBold',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '头条：',
        //   field: 'flagHeadline',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '详情是否图片',
        //   field: 'flagPicture',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '推荐',
        //   field: 'flagRecommend',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '是否滚动：',
        //   field: 'flagRoll',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '幻灯：',
        //   field: 'flagSlideShow',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        // {
        //   type: 'radio',
        //   label: '特别推荐：',
        //   field: 'flagSpecialRecommend',
        //   options: [
        //     {
        //       label: '是',
        //       value: 1
        //     },
        //     {
        //       label: '不是',
        //       value: 0
        //     }
        //   ]
        // },
        {
          type: 'radio',
          label: '是否接入开放平台：',
          field: 'isAccessOpenPlatform',
          options: [{
            label: '是',
            value: 1
          },
          {
            label: '不是',
            value: 0
          }
          ]
        },
        {
          type: 'datetime',
          label: '发布时间：',
          field: 'publishTime',
        },
        {
          type: 'radio',
          label: '是否push推送：',
          field: 'isPush',
          options: [{
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
          ]
        },
        {
          type: 'input',
          label: '相关藏品：',
          slot: 'strCtid',
          field: 'articleCtidAndCsNameModuleListStr',
        },
        // {
        //   type: 'radio',
        //   label: '平台：',
        //   field: 'platformType',
        //   options: [{
        //     label: 'BV',
        //     value: 'bv'
        //   },
        //   {
        //     label: 'KV',
        //     value: 'kv'
        //   }, {
        //     label: '衍界',
        //     value: 'yj'
        //   }]
        // },
        {
          type: 'action'
        }
      ],
      formDataShuru: {
        imageTitle: "",
        imageQrCode: "",
        imageContent: "",
      },
      formSchemaShuru: [{
        type: 'textarea',
        label: '标题：',
        placeholder: '请输入标题',
        field: 'imageTitle',
        maxlength:'30',
        rules: [{
          required: true,
          message: '请输入标题',
          trigger: 'blur'
        }]
      },
      {
        type: 'textarea',
        label: '内容：',
        placeholder: '请输入内容',
        field: 'imageContent',
        rows: 10,
        rules: [{
          required: true,
          message: '请输入内容',
          trigger: 'blur'
        }]
      },
      {
        type: 'img',
        label: '二维码：',
        placeholder: '请选择二维码',
        field: 'imageQrCode',
      },
      ],
      pppp: "",
      businessType: 0,
      resultUrl: '',
      fullscreenLoading: false
    }
  },
  mounted() {
    const {
      templateId,
      businessType,
      platformType
    } = this.$route.query
    this.templateId = templateId
    this.platformType = platformType
    this.businessType = businessType
    templateId && this.getDetail()
    if (this.templateId) {
      this.formData.publishTime = this.formData.publishTime
    }
  },
  computed: {
    formDataShuru1() {
      console.log(this.formDataShuru)
      const data = {
        ...this.formDataShuru,
      }
      data.imageContent = data.imageContent.split('\n')
      data.imageQrCode = data.imageQrCode.split('\n')
      data.imageTitle = data.imageTitle.split('\n')
      console.log(data.imageContent)
      return data
    }
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    // 监听富文本的输入
    catchData(e) {
      console.log('1e=====?>', e)
      this.richTxt = e
      this.formData.content = e
    },
    // 富文本中的内容
    editorContent(e) {
      console.log('2e=====?>', e)
      return '<p>123</p>'
    },
    routerBack() {
      const {
        fullPath
      } = this.$route
      this.close({
        tagName: fullPath
      })
      this.$router.back()
    },
    async getDetail() {
      const {
        status,
        result
      } = await this.$api.getArticleDetail({
        id: this.templateId
      })
      if (status.code === 0) {
        this.formData = {
          ...result,
          articleCtidAndCsNameModuleListStr: [],
          platformType: this.platformType
        }
         this.richTxt=result.content
        this.$forceUpdate()
        if (result.articleCtidAndCsNameModuleList != null) {
          result.articleCtidAndCsNameModuleList.forEach((item) => {
            item.nameCtid = `${item.csName}(${item.ctid})`
          })
          this.formData.articleCtidAndCsNameModuleListStr = result.articleCtidAndCsNameModuleList
        }
      }
      if (this.formData.sendType == 0) {
        this.formData.publishTime = ''
      }
      this.formDataShuru = {
        imageTitle: this.formData.imageTitle,
        imageQrCode: this.formData.imageQrCode,
        imageContent: this.formData.imageContent,
        footer: ['Bigverse', this.formData.createdTime]
      }
    },
    async submit() {
      if (this.formData.articleCtidAndCsNameModuleListStr) {
        this.formData.articleCtidAndCsNameModuleListStr.forEach((item) => {
          console.log(item.nameCtid)
          if (item.nameCtid) {
            item.csName = item.nameCtid.split('(')[0]
            item.ctid = item.nameCtid.split('(')[1].split(')')[0]
          }
        })
      }
      const data = {
        ...this.formData,
        articleCtidAndCsNameModuleListStr: JSON.stringify(this.formData.articleCtidAndCsNameModuleListStr),
      }
      console.log(data)
      data.templateId = this.templateId
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.formData.publishTime = this.formData.publishTime
        if (this.templateId) {
          let res = await this.$api.noticeEdit(data);
          if (res.status.code == 0) {
            this.routerBack()
          }
        } else {
          let res = await this.$api.noticeAdd(data);
          if (res.status.code == 0) {
            this.routerBack()
          }
        }
      })
    },
    // 同步到push
    nav_push(data) {
      this.$confirm('公告已经发布成功？', '是否前往push一键推送', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.$router.push({
          name: 'addMsgPush',
          query: data
        })
      })
    },
    change(e) {
      console.log(e)
      this.getCtid(e)
    },
    async getCtid(str) {
      let res = await this.$api.searchByCsNameStr({
        str
      });
      console.log(res)
      if (res.status.code == 0) {
        if (res.result.ctidAndCsNameList != null) {
          res.result.ctidAndCsNameList.forEach((item) => {
            item.nameCtid = `${item.csName}(${item.ctid})`
          })
        }
        this.formData.articleCtidAndCsNameModuleListStr = res.result.ctidAndCsNameList
      } else {

      }
    },
    //添加相关藏品
    addCollectionItemnameCtid(item) {
      this.formData.articleCtidAndCsNameModuleListStr.push({ nameCtid: '' })
    },
    //删除相关藏品
    delCollectionItemnameCtid(item, index) {
      this.formData.articleCtidAndCsNameModuleListStr.splice(index, 1)
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    //调试查看数据
    selete_list() {
      this.formData.articleCtidAndCsNameModuleListStr.forEach((item) => {
        item.csName = item.nameCtid.split('(')[0]
        item.ctid = item.nameCtid.split('(')[1].split(')')[0]
      })
      console.table(JSON.stringify(this.formData.articleCtidAndCsNameModuleListStr))
    },
    open() {
      const loading = this.$loading({
        lock: true,
        text: '图片生成中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const dom = this.$refs.preview // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, //支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2, // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {

        this.updateImg(canvas.toDataURL('image/jpeg', 1))
      }).catch(err => {

      })
    },
    async updateImg(url) {
      const formData = new FormData()
      let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.resultUrl = result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]
      this.formData.publishTime = this.formData.publishTime
      this.formData.imageTitle = this.formDataShuru.imageTitle
      this.formData.imageContent = this.formDataShuru.imageContent
      this.formData.imageQrCode = this.formDataShuru.imageQrCode
      this.formData.content =
        `<p><img src=\"https://cdn-lingjing.nftcn.com.cn/${this.resultUrl}\" style=\"width:100%;\" contenteditable=\"false\"/></p>`,
        this.formData.title = this.formDataShuru.imageTitle
      this.formData.subTitle = this.formDataShuru.imageContent
      this.formData.businessType = this.businessType
      if (this.formData.publishTime) {
        this.formData.sendType = 1
      }
      const loading = this.$loading({});
      loading.close();
      this.submit()
    },
    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    submitConfig() {
      if (this.formData.linkType == 'IMAGE_LINK') {
        this.open()
      } else {
        this.formData.imageTitle = this.formDataShuru.imageTitle
        this.formData.title = this.formDataShuru.imageTitle
        if (this.formData.publishTime) {
          this.formData.sendType = 1
        }
        this.submit()
      }
    },
    openFullScreen() {
      this.fullscreenLoading = true;
      setTimeout(() => {
        this.fullscreenLoading = false;
      }, 2000);
    },
  }
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'fonts';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
}

@font-face {
  font-family: 'fonts_title';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newFont.OTF');
}

.page {
  padding-top: 80px;
}

.w-e-toolbar {
  z-index: 2 !important;
}

.w-e-menu {
  z-index: 2 !important;
}

.w-e-text-container {
  z-index: 1 !important;
}

.item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.box-card {
  width: 560px;
}

.flex {
  display: flex;
  justify-content: flex-start;
  // margin-top: -100000px;

  .shuru {
    width: 500px;
    height: 812px;
    width: 414px;
    padding: 40px 0px;
    border: 1px solid #ccc;
  }

  .yulan {
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 40px 0px;
  }

  .preview_details {
    width: 414px;
    background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
    background-size: 100%;
    font-family: 'fonts';
    padding-bottom: 16px;

    .tips {
      line-height: 0px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .head_text {
      width: 389px;
      margin: 30px auto 0px auto;

      img {
        width: 201px;
        height: auto
      }

     .title_div {
       background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241021/538a071da3d57a3d07c551a3d0284ad4_1015x521.png');
       background-repeat: no-repeat;
       width: 389px;
       height: 200px;
       background-size: 100%;
       margin-top: 7px;
       position: relative;
       display: flex;
       align-items: center;
       justify-content: center;

       .bode {
         position: absolute;
         right: -10px;
         bottom: 31px;

         img {
          width: 49px;
          height: 41px;
         }
       }

       .title {
         width: 210px;
         text-align: center;
         font-size: 21px;
         min-height: 78px;
         color: #fff;
         font-family: 'fonts_title';
         margin-bottom:20px;
         display: flex;
         justify-content: center;
         align-items: center;
       }
     }
    }

    .bigverse {
      img {
        width: 274px;
        height: 42px
      }
    }

    .bigverse_top {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_bottom {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_body {
      width: 414px;
      // min-height: 421px;
      margin: 0 auto;
      background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230612/bb4106b2df078e4b75df39922f6f09f3_400x244.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 1px 42px 0px 42px;
      color: #fff;
      letter-spacing: 2px;

      .neirong {
        .title {
          font-size: 18px;
          margin-bottom: 30px;
        }

        >div {
          margin-top: 10px;
          line-height: 22px;
          font-size: 16px;
        }
      }

      .official_msg {
        margin-top: 30px;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0px;

        .title {
          margin-bottom: 10px;
        }

        color:#20AFAC;
      }

      .footer {
        margin-top: 40px;
        text-align: right;
        font-size: 18px;
        letter-spacing: 2px;
      }
    }

    .code {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 40px 0px;

      img {
        width: 100%;
        height: auto;
      }
    }
  }
}

.el-textarea {
  textarea {
    height: 200px !important;
  }
}
 .addRight {
      overflow-wrap: break-word;
      margin-left: 30px;
      position: absolute;
      height: 860px;
      background: url("../../assets/img/ios.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      left: 800px;
      top: 0px;
      text-align: left;
      padding: 30px 40px;

      // display: flex;
      // align-items: center;
      // justify-content: center;
      .box_content {
        width: 414px;
        height: 828px;
        overflow: auto;
        -ms-overflow-style: none;
        /* IE 和 Edge */
        scrollbar-width: none;
        /* Firefox */
        overflow-y: scroll;
        /* 保持内容可滚动 */
        background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
        border-radius: 40px;

        .addRight_head {
          width: 95%;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          // margin-top: 7px;
          // position: relative;

          margin: 0px auto 0px auto;
          border-radius: 20px 40px 40px 40px;
          color: #fff;


          .imgOne {
            width: 100%;
            height: auto;
          }

          .imgTwo {
            width: 201px;
            margin-left: 20px;
            margin-top: 30px;
          }

          .contentDiv {
            background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20241021/538a071da3d57a3d07c551a3d0284ad4_1015x521.png');
            background-repeat: no-repeat;
            width: 389px;
            height: 200px;
            background-size: 100%;
            margin-top: 7px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            .title {
             width: 210px;
             text-align: center;
             font-size: 21px;
             min-height: 78px;
             color: #fff;
             font-family: 'fonts_title';
             margin-bottom:20px;
             display: flex;
             justify-content: center;
             align-items: center;
            }

            .imgBd{
               width: 49px;
               height: 41px;
                position: absolute;
                right: -10px;
                bottom: 31px;
            }
          }




        }

        .addRight_box {
          padding: 0px 14px 20px 14px;
          position: relative;
          margin-top: 0px;
          color: #fff;

          .addRight_content {
            padding: 0px 0px 20px 0px;
              p{
                font-size:16px;
                margin:0px !important;
                font-family:'fonts';
                line-height:1.5;
              }
          }
        }
      }
    }
.noSpacing {
  letter-spacing: 0px;
}
</style>
