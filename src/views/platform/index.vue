<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="toTemplatePage()">新增公告</el-button>
      </template>
      <template #scanCount="scope">
        <el-button type="text" @click="openScanCount(scope.row)">{{ scope.row.scanCount }}</el-button>
      </template>
      <template #action="scope">
        <el-button @click="toTemplatePage(scope.row)" type="text">编辑</el-button>
        <el-button @click="statusToggle(scope.row.id, scope.row.status)" type="text">
          {{ scope.row.status === 0 ? '发表' : '下架' }}
        </el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, prev, pager, next, jumper" :total="page.totalCount" :current-page="page"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange">
      </el-pagination>
    </div>
    <!-- 	<el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
// import { mapActions } from 'vuex'
import {
  articleList,
  noticePublish,
  updateScanCount
} from '@/api/hongyan'

export default {
  name: 'platform',
  components: {
    CommonQuery,
    CommonTable
  },
  data() {
    return {
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }, // 分页数据
      query: {
        autherName: '',
        autherId: '',
        seoKeywords: '',
        seoTitle: '',
        subTitle: '',
        title: '',
        isAccessOpenPlatform: '',
        tapType:''
      },
      tableSchema: [ // 表格架构
        {
          label: 'id',
          field: 'id',
          width: 40,
          align: 'center'
        },
        {
          label: '排序',
          field: 'sort',
          width: 40,
          align: 'center'
        },
        {
          label: '作者',
          field: 'authorName',
          width: '120px',
          align: 'center'
        },
        {
          label: '标题',
          field: 'title',
          width: '200px',
          align: 'center'
        },
        {
          label: '二级标题',
          field: 'subTitle',
          width: '300px',
          align: 'center',
          showOverflowTooltip: true
        },
        {
          label: '图片',
          field: 'thumb',
          type: 'img',
          width: '120px',
          align: 'center'
        },
        {
          label: '状态',
          field: 'status',
          type: 'tag',
          align: 'center',
          tagMap: {
            0: {
              label: '草稿',
              tagType: 'danger'
            },
            1: {
              label: '已发布',
              tagType: 'success'
            }
          },
          width: '90px'
        },
        {
          label: '类型',
          field: 'tapType',
          type: 'tag',
          align: 'center',
          tagMap: {
            0: {
              label: '藏品公告',
              tagType: 'danger'
            },
            1: {
              label: 'bit指数公告',
              tagType: 'success'
            },
            2: {
              label: '美股公告',
              tagType: 'success'
            }
          },
          width: '100px'
        },
        {
          label: '公告类型',
          field: 'businessType',
          type: 'tag',
          align: 'center',
          tagMap: {
            0: {
              label: '普通公告',
              tagType: 'danger'
            },
            1: {
              label: '今日公告',
              tagType: 'success'
            },
            2: {
              label: '明日公告',
              tagType: 'success'
            }
          },
          width: '100px'
        },
        {
          label: '发布时间',
          field: 'publishTime',
          width: '200px',
          align: 'center',
          align: 'center'

        },
        // {
        //   label: '详情图片地址',
        //   field: 'content',
        //   width: '150px'
        // },
        // {
        //   label: '是否加粗',
        //   field: 'flagBold',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'default' },
        //     0: { label: '是', tagType: 'success' }
        //   },
        //   width: '80px'
        // },
        // {
        //   label: '头条',
        //   field: 'flagHeadline',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'default' },
        //     0: { label: '是', tagType: 'success' }
        //   },
        //   width: '80px'
        // },
        // {
        //   label: '详情是否图片',
        //   field: 'flagPicture',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'default' },
        //     0: { label: '是', tagType: 'success' }
        //   },
        //   width: '110px'
        // },
        // {
        //   label: '推荐',
        //   field: 'flagRecommend',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'default' },
        //     0: { label: '是', tagType: 'success' }
        //   },
        //   width: '80px'
        // },
        // {
        //   label: '是否滚动',
        //   field: 'flagRoll',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'default' },
        //     0: { label: '是', tagType: 'success' }
        //   },
        //   width: '80px'
        // },
        // {
        //   label: '幻灯',
        //   field: 'flagSlideShow',
        //   type: 'tag',
        //   tagMap: {
        //     1: { label: '不是', tagType: 'success' },
        //     0: { label: '是', tagType: 'default' }
        //   },
        //   width: '80px'
        // },
        {
          label: '是否为第三方',
          field: 'isAccessOpenPlatform',
          type: 'tag',
          align: 'center',
          tagMap: {
            1: {
              label: '是',
              tagType: 'default'
            },
            0: {
              label: '不是',
              tagType: 'success'
            }
          },
          width: '80px'
        },
        {
          label: '阅读次数',
          width: '80px',
          align: 'center',
          slot: 'scanCount'
        },
        {
          label: '创建时间',
          field: 'createdAt',
          width: '160px',
          align: 'center'
        },

        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '140px',
          fixed: 'right'
        }
      ],
      tableData: [], // 表格数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '作者id：',
          placeholder: '请输入作者id',
          field: 'autherId'
        },
        {
          type: 'input',
          label: '作者昵称：',
          placeholder: '请输入作者昵称',
          field: 'autherName'
        },
        {
          type: 'input',
          label: '标题：',
          placeholder: '请输入标题',
          field: 'title'
        },
        {
          type: 'input',
          label: '副标题：',
          placeholder: '请输入副标题',
          field: 'subTitle'
        },
        {
          type: 'select',
          label: '是否为第三方：',
          field: 'isAccessOpenPlatform',
          placeholder: '全部',
          options: [{
            'label': '是',
            'value': 1,
          }, {
            'label': '不是',
            'value': 0,
          }]
        },
        {
          type: 'select',
          label: '同步到金刚区：',
          field: 'type',
          placeholder: '全部',
          options: [{
            label: '上新公告',
            value: 'NEW_GOODS'
          },
          {
            label: '合成公告',
            value: 'MERGE'
          },
          {
            label: '白名单公告',
            value: 'WHITE_USER_LIST'
          },
          ]
        },
        {
          type: 'datetimerange',
          label: '发布时间：',
          field: 'publishTimeStart',
          field2: 'publishTimeEnd',
        },
        {
          type: 'select',
          label: '类型：',
          field: 'tapType',
          placeholder: '全部',
          options: [{
            'label': '藏品公告',
            'value': 0,
          }, {
            'label': 'bit指数公告',
            'value': 1,
          }, {
            'label': '美股公告',
            'value': 2,
          }],
        },
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    toTemplatePage(item = {}) {
      // this.$store.commit('SAVE_INFO', item)
      // mapActions('save_stateInfo', item)
      if (item.businessType && item.businessType != 0) {
        this.$router.push({
          name: 'NoticeEdit',
          query: {
            templateId: item.id,
            businessType: item.businessType,
          }
        })
      } else {
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      }

    },
    scrollEvent(e) {
      console.log(e.y) // 获取目标元素的滚动高度
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data
      this.getList(true)
    },
    // 分页改变
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    getList() {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: this.page.pageNum
      }
      articleList(params).then(res => {
        const data = res.result
        this.tableData = data.list
        this.page.totalCount = data.totalCount
        this.page.pageSize = data.pageSize
        this.page.pageCount = data.pageCount
      })
    },
    // 发表/下架
    statusToggle(id, status) {
      const changeText = status === 0 ? '发表' : '下架'
      this.$confirm(`确定${changeText}该条公告吗？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        noticePublish({
          id,
          status: status === 0 ? 1 : 0
        }).then((res) => {
          this.$message.success(res.status.msg)
          this.getList()
        })
      })
    },
    openScanCount(item) {
      this.$prompt('请输入你要修改的阅览量', '提示', {
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({
        value
      }) => {
        this.scanCount(value, item.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    scanCount(scanCount, id) {
      updateScanCount({
        scanCount,
        id
      }).then(res => {
        if (res.status.code == 0) {
          this.$message.success('修改成功')
          this.getList()
        }
      })
    },

  }
}
</script>

<style lang="scss" scoped></style>
