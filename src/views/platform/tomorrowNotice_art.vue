<template>
  <d2-container class="page">
    <el-form class="demo-form-inline" style="background-color: #ffffff; padding: 10px">
      <el-form-item>
        <el-button type="primary" @click="getList()">刷新</el-button>

      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
      v-loading="loading" element-loading-text="正在加载">
      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column prop="processStatus" label="运营状态" align="center" width="160">
        <template slot-scope="scope">
          <el-select  v-model="scope.row.processStatus" clearable placeholder="请选择"
            @change="changeStatus(scope.row.processStatus, scope.row)">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="publishTime" label="时间" align="center" width="170">
        <template slot-scope="scope">
          <el-button type="primary" style="margin-top:10px;" @click="timing(scope.row)" v-if="scope.row.sendType == 0">定时
          </el-button>
          <el-time-picker v-if="scope.row.sendType == 1 || scope.row.sendType == 2" v-model="scope.row.publishTime"
            :disabled="scope.row.sendStatus == 1" value-format="HH:mm:ss" :picker-options="{
            }" placeholder="选择时间" @change="changeTime(scope.row.publishTime, scope.row)">
          </el-time-picker>
          <!-- <el-button type="primary" style="margin-top:10px;" @click="direct_send(scope.row)"
            v-if="scope.row.sendStatus==0">直接发送</el-button> -->
          <el-button type="success" style="margin-top:10px;" v-if="scope.row.sendStatus == 1">已发送</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="templateType" label="类型" align="center" width="140">
        <template slot-scope="scope">
          <el-select  v-model="scope.row.templateType" clearable placeholder="请选择"
            @change="changeType(scope.row.templateType, scope.row)">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="imageTitle" label="标题" align="center" width="240">
        <template slot-scope="scope">
          <el-input type="textarea" autosize placeholder="请输入内容" v-model="scope.row.imageTitle">
          </el-input>
          <el-button type="text" @click="setData(scope.row, false, 1)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="imageContent" label="内容" align="center" width="340">
        <template slot-scope="scope">
          <el-input type="textarea" autosize placeholder="请输入内容" v-model="scope.row.imageContent">
          </el-input>
          <el-button type="text" @click="setData(scope.row, false, 2)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="articleCtidAndCsNameModuleListStr" label="相关藏品" align="center" width="460">
        <template slot-scope="scope">
          <el-card class="box-card" style="width:440px;">
            <div class="" v-for="(item, index) in scope.row.articleCtidAndCsNameModuleListStr">
              <el-autocomplete style="width:340px;margin-bottom:10px;" size="mini" v-model="item.nameCtid"
                :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
              </el-autocomplete>
              <el-button type="text" style="margin-left:10px;" size="mini"
                @click="delCollectionItemnameCtid(scope.row, index)">删除</el-button>
            </div>
            <el-button type="primary" style="width:100%;" plain size="mini"
              @click="addCollectionItemnameCtid(scope.row)">添加相关藏品</el-button>
          </el-card>
          <el-button type="text" @click="update_collection(scope.row)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="yulan" label="预览" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="setData(scope.row, true)">预览</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" width="170">
        <template slot-scope="scope">
          <el-input type="textarea" class="red_input" autosize placeholder="请输入内容" v-model="scope.row.remark">
          </el-input>
          <el-button type="text" @click="update_remark(scope.row.remark, scope.row)">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="imageQrCode" label="二维码" align="center" width="160">
        <template slot-scope="scope">
          <ImgUploader :value.sync="scope.row.imageQrCode" :limit="1"></ImgUploader>
          <el-button type="text" @click="uploading_pic(scope.row.imageQrCode, scope.row)">上传</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text"  @click="nav_details(scope.row)">编辑</el-button>
          <el-button type="text"  @click="nav_topping(scope.row)">置顶</el-button>
          <el-button type="text"  @click="nav_delete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="width_footer">
      <el-button type="primary" @click="add()" style="width: 100%;">添加明日公告</el-button>
    </div>
    <div class="flex">
      <div ref="preview">
        <div class="preview_details">
          <div class="tips">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/7d1c1bb6f97f9c82284de8e7575c82e2_400x16.png">
          </div>
          <div class="head_text">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/f10a9ed24154e94377789225007ccd82_400x43.png">
            <div class="title_div">
              <div class="title">
                <div v-for="(item, index) in formDataShuru.imageTitle">
                  {{ item }}
                </div>
              </div>
              <div class="bode">
                <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/6950f1e1b6b5abd1d2620ac4d778e0cd_196x164.png">
              </div>
            </div>
          </div>
          <div class="bigverse">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230606/667ae9be41db64bcb3f155df69642549_400x60.png" alt=""
              srcset="">
          </div>
          <div class="bigverse_top">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/523c2b56ae43a1e6093d2de7700de496_400x48.png" alt=""
              srcset="">
          </div>
          <div class="bigverse_body">
            <div class="neirong">
              <div class="title">
                亲爱的Bigverse用户：
                <br>
              </div>
              <div v-for="(item, index) in formDataShuru.imageContent"
                :style="{ 'margin-top': item == '' ? '0px' : '10px' }">
                <div v-if="item == ''">
                  <br>
                </div>
                <div v-else>
                  {{ item }}
                </div>
              </div>
            </div>
            <div class="code" v-if="formDataShuru.imageQrCode != ''">
              <img :src="formDataShuru.imageQrCode" alt="">
            </div>
            <div class="official_msg">
              <div class="title">官方风险提示:</div>
              <div>
                Bigverse平台发售的数字藏品仅具备收藏、欣赏价值，不具有投资、保值、增值、收益功能，平台对藏品价格不构成任何指导意义，请谨慎购买及参与活动，严防炒作。
              </div>
            </div>
            <div class="footer">
              <div v-for="(item, index) in formDataShuru.footer" :class="{ 'noSpacing': index == 0 }">{{ item, }}</div>
            </div>
          </div>
          <div class="bigverse_bottom">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20230612/a15b6394a0ef2fdd85c1c8ec26d25d95_400x67.png" alt=""
              srcset="">
          </div>
        </div>
      </div>
    </div>
  </d2-container>
</template>

<script>
import ImgUploader from '@/components/ImgUploader'
import html2canvas from 'html2canvas';
import {
  uploadImgToOss
} from '@/api/ossCenter'
import {
  articleTimeList,
  articleTimeCreate,
  articleTimeUpdate,
  articleTimeTopping,
  articleTimeDeleted
} from '@/api/hongyan'

export default {
  name: 'tomorrowNotice',
  data() {
    return {
      loading: false,
      tableData: [],
      lading: false,
      statusList: [{
        value: 1,
        label: '待修改'
      }, {
        value: 2,
        label: '文案待确认'
      }, {
        value: 3,
        label: '文案已确认，等待配置'
      }, {
        value: 4,
        label: '已配置，等待复核'
      }, {
        value: 5,
        label: '已复核'
      }],
      typeList: [{
        value: 1,
        label: '白名单公告',
        list: {
          title: '《》XX白名单公告',
          content: `每持有X份《》可（优先购/空投）X份《》。

        以下为《》（优先购/空投）白名单，请查收！
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/1.jpg'
        }
      }, {
        value: 2,
        label: '合成公告',
        list: {
          title: 'XX系列藏品《》合成公告',
          content: `藏品《》即将开始合成！

        合成材料：《》*

        合成时间：
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/2.jpg'
        }
      }, {
        value: 3,
        label: '寄售公告',
        list: {
          title: '《》开放寄售/寄售限价调整',
          content: `藏品《》将于XX开放寄售，并进入专题区《》。

        《》铸造数量：
        《》流通数量：
        《》销毁数量：

        以上藏品暂时为限价藏品，最高寄售限价为¥。

        （调限额）藏品《》最高寄售限价将于XX调整为¥。
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/3.jpg'
        }
      }, {
        value: 4,
        label: '上新预告',
        list: {
          title: '《》上新预告',
          content: `Bigverse将于XX上新《》。

        每持有1份《》可优先购/空投X份《》（权益可叠加）。

        发行单价：¥/份
        发行量：X份

        《》售出后状态将暂时为仅供收藏，后续会开放寄售。

        优先购/空投白名单扫描时间：
        （扫描会持续一段时间，为防止数据异常影响权益，请避免在扫描期间进行交易）

        优先购/空投时间：XX
        （批量购买功能目前仅支持网页端，请有需要的用户前往nftcn.com体验批量购买功能）
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/4.jpg'
        }
      }, {
        value: 5,
        label: '空投公告',
        list: {
          title: `《》空投到账通知
《》空投公告`,
          content: `《》已按照白名单空投完毕，请查收！
每持有？份《》，将空投？份《》。
持仓扫描时间：
空投到账时间：`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/5.jpg'
        }
      },
      {
        value: 6,
        label: '上新提醒',
        list: {
          title: '《》优先购即将开始',
          content: `藏品《》优先购将于今日XX正式开始！

        每持有1份《》可优先购X份《》

        优先购时间：
        （批量购买功能目前仅支持网页端，前往nftcn.com即可体验批量购买功能）

        请具有优先购资格的用户尽快前往购买。
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/6.jpg'
        }
      }, {
        value: 7,
        label: '合成提醒',
        list: {
          title: 'XX系列藏品《》合成提醒',
          content: `藏品《》合成即将开始！

        合成材料：《》*

        合成时间：

        合成入口：点击【市场】，最上方活动图片展示位，找到藏品《》合成图，点击进入，即可参与合成。
        敬请各位用户知悉！ 谢谢！`,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/7.jpg'
        }
      },
      {
        value: 8,
        label: '活动公告',
        list: {
          title: '',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/8.jpg'
        }
      },
      {
        value: 9,
        label: '运营公告',
        list: {
          title: '',
          content: ``,
          cover: 'https://cdn-lingjing.nftcn.com.cn/h5/noticeCover/9.jpg'
        }
      }
      ],
      flagchange: 0,
      formDataShuru: [],
      temporaryList: []
    }
  },
  components: {
    ImgUploader
  },
  watch: {
    // tableData: {
    //   handler(val) {
    //     console.log(val)
    //   },
    //   deep: true // 深度监听
    // }

  },
  mounted() {
    this.getList()
  },
  // computed: {
  //   formDataShuru1() {
  //     const data = {
  //       ...this.formDataShuru,
  //     }
  //     // data.textarea = data.body.split('\n')
  //     // data.footer = data.remark.split('\n')
  //     // data.title = data.title.split('\n')
  //     return data
  //   }
  // },
  methods: {
    //选择状态
    changeStatus(e, item) {
      this.temporaryList = item
      let params = {
        processStatus: e
      }
      console.log(item)
      this.edit(params)
    },
    //选择类型
    changeType(e, item) {
      console.log(e, item)
      this.$confirm('是否确认使用该模板，标题和内容将会被覆盖？', '确认使用模板覆盖', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        item.imageTitle = this.typeList[e - 1].list.title
        item.imageContent = this.typeList[e - 1].list.content
        item.thumb = this.typeList[e - 1].list.cover
        console.log(item.imageTitle, item.imageContent)
        this.setData(item, false)
      }).catch(() => {
        this.getList()
      });

    },
    changeTime(e, item) {
      let Y = item.createdTime.split('年')[0]
      let M = item.createdTime.split('月')[0].split('年')[1]
      let D = item.createdTime.split('日')[0].split('月')[1]
      let str = Y + '-' + M + '-' + D + ' ' + e
      console.log(str)
      this.temporaryList = item
      let params = {
        publishTime: str,
        sendType: 1,
      }
      console.log(item)
      this.edit(params)
    },
    direct_send(item) {
      if (item.templateType == 1 && item.imageQrCode == '') {
        this.$confirm('白名单没有二维码，确定直接发送？', '提醒', {
          confirmButtonText: '确定直接发送',
          cancelButtonText: '返回编辑',
          type: 'warning'
        }).then(async () => {
          this.temporaryList = item
          let params = {
            sendType: 2,
          }
          this.edit(params)
        }).catch(() => {

        });
      } else {
        this.temporaryList = item
        let params = {
          sendType: 2,
        }
        this.edit(params)
      }
    },

    //上传二维码
    uploading_pic(e, item) {
      if (e == '') {
        this.$message.error('请选择二维码')
      } else {
        this.temporaryList = item
        this.setData(item, false, 3)
      }
    },
    update_remark(e, item) {
      if (e == '') {
        this.$message.error('请输入备注')
      } else {
        this.temporaryList = item
        let params = {
          remark: e
        }
        this.edit(params)
      }
    },
    update_collection(item) {
      console.log(item)
      this.temporaryList = item
      if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
        this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
          console.log(item.nameCtid)
          if (item.nameCtid) {
            item.csName = item.nameCtid.split('(')[0]
            item.ctid = item.nameCtid.split('(')[1].split(')')[0]
          }
        })
      }
      let params = {
        articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr)
      }
      console.error(params)
      this.edit(params)
    },
    refresh() {
      this.formDataShuru = this.tableData[0]
      console.log(this.formDataShuru)
    },
    //将操作的值赋值予 生成图的dom
    setData(item, type) {
      this.temporaryList = item
      const data = {
        ...item,
      }
      if (data.imageTitle.includes('\n')) {
        data.imageTitle = data.imageTitle.split('\n')
      } else {
        data.imageTitle = [data.imageTitle]
      }
      if (data.imageContent.includes('\n')) {
        data.imageContent = data.imageContent.split('\n')
      } else {
        data.imageContent = [data.imageContent]
      }
      data.footer = ['Bigverse', item.createdTime]
      this.formDataShuru = data
      console.log(data)
      this.getCtid(this.temporaryList.imageContent)

      setTimeout(() => {
        this.open(type)
      }, 500)
    },
    async getCtid(str) {
      if (str) {
        let res = await this.$api.searchByCsNameStr({
          str
        });
        if (res.status.code == 0) {
          if (res.result.ctidAndCsNameList != null) {
            res.result.ctidAndCsNameList.forEach((item) => {
              item.nameCtid = `${item.csName}(${item.ctid})`
            })
          }
          this.temporaryList.articleCtidAndCsNameModuleListStr = res.result.ctidAndCsNameList
        }
      }
    },
    async open(type) {
      this.loading = true
      const dom = this.$refs.preview // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, //支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2, // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {
        this.updateImg(canvas.toDataURL('image/jpeg', 1), type)
      }).catch(err => {

      })
    },
    async updateImg(url, type) {
      console.log(url)
      const formData = new FormData()
      let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.loading = false
      if (type) {
        console.log(1)
        window.open(
          `https://cdn-lingjing.nftcn.com.cn/${result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]}`,
          "name",
          "height=820, width=414, top=0, left=2, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=no, status=no"
        );
      } else {
        if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
          this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
            console.log(item.nameCtid)
            if (item.nameCtid) {
              item.csName = item.nameCtid.split('(')[0]
              item.ctid = item.nameCtid.split('(')[1].split(')')[0]
            }
          })
        }
        //请求接口 修改数据
        let params = {
          content: result.mediumImageUrl,
          imageQrCode: this.temporaryList.imageQrCode,
          imageTitle: this.temporaryList.imageTitle,
          templateType: this.temporaryList.templateType,
          imageContent: this.temporaryList.imageContent,
          content: `<p><img src=\"https://cdn-lingjing.nftcn.com.cn/${result.mediumImageUrl.split('https://nftcns.oss-cn-shanghai.aliyuncs.com/')[1]}\" style=\"width:100%;\" contenteditable=\"false\"/></p>`,
          thumb: this.temporaryList.thumb,
          articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr)
        }
        this.edit(params)
      }

    },
    async getList() {
      const {
        result
      } = await articleTimeList({
        businessType: 2,
        platformType: 2
      })
      result.adminNftcnTimeArticleListVOS.forEach((item) => {
        if (item.articleCtidAndCsNameModuleList) {
          item.articleCtidAndCsNameModuleList.forEach((itemm) => {
            itemm.nameCtid = `${itemm.csName}(${itemm.ctid})`
          })
          item.articleCtidAndCsNameModuleListStr = item.articleCtidAndCsNameModuleList
        } else {
          item.articleCtidAndCsNameModuleListStr = []
        }
      })
      this.tableData = result.adminNftcnTimeArticleListVOS
    },
    async add() {
      let createList = {
        thumb: this.typeList[0].list.cover,
        businessType: 2,
        templateType: 1,
        remark: '',
        content: '<p><img src=\"https://cdn-lingjing.nftcn.com.cn/image/20230606/dc585006401082ef6376353f24589133_828x1644.jpg\" style=\"width:100%;\" contenteditable=\"false\"/></p>',
        imageQrCode: '',
        imageTitle: this.typeList[0].list.title,
        imageContent: this.typeList[0].list.content,
        platformType: 2
      }
      const res = await articleTimeCreate(createList)
      console.log(res)
      if (res.status.code == 0) {
        this.$message.success('新增成功')
        this.getList()
      }
    },
    async edit(data) {
      if (this.temporaryList.articleCtidAndCsNameModuleListStr) {
        this.temporaryList.articleCtidAndCsNameModuleListStr.forEach((item) => {
          console.log(item.nameCtid)
          if (item.nameCtid) {
            item.csName = item.nameCtid.split('(')[0]
            item.ctid = item.nameCtid.split('(')[1].split(')')[0]
          }
        })
      }
      let req = {
        id: this.temporaryList.id,
        businessType: 2,
        version: this.temporaryList.version,
        articleCtidAndCsNameModuleListStr: JSON.stringify(this.temporaryList.articleCtidAndCsNameModuleListStr),
        ...data
      }
      console.log(req)
      const res = await articleTimeUpdate(req)
      if (res.status.code == 0) {
        if (data.publishTime && this.temporaryList.processStatus != 5) {
          this.$message.warning('时间定时成功，请前往运营状态确认')
          this.getList()
        } else if (data.sendType) {
          this.tableData = []
          this.getList()
        } else {
          this.getList()
          this.$message.success('修改成功')
        }
      } else {
        this.getList()
      }
    },
    async nav_delete(item) {
      const res = await articleTimeDeleted({
        id: item.id,
        version: item.version
      })
      if (res.status.code == 0) {
        this.tableData = []
        this.$message.success('删除成功')
        this.getList()
      }
    },
    async nav_topping(item) {
      const res = await articleTimeTopping({
        id: item.id,
        version: item.version
      })
      if (res.status.code == 0) {
        this.tableData = []
        this.$message.success('置顶成功')
        this.getList()
      }
    },
    nav_details(item) {
      this.$router.push({
        name: 'NoticeEdit_art',
        query: {
          templateId: item.id,
          businessType: item.businessType,
          platformType: item.platformType

        }
      })
    },
    timing(item) {
      if (item.templateType == 1 && item.imageQrCode == '') {
        this.$confirm('白名单没有二维码，确定定时发送？', '提醒', {
          confirmButtonText: '确定定时',
          cancelButtonText: '返回编辑',
          type: 'warning'
        }).then(async () => {
          item.sendType = 1
          item.publishTime = ''
        }).catch(() => {

        });
      } else {
        item.sendType = 1
        item.publishTime = ''
      }
    },
    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    //添加相关藏品
    addCollectionItemnameCtid(item) {
      item.articleCtidAndCsNameModuleListStr.push({
        nameCtid: ''
      })
    },
    //删除相关藏品
    delCollectionItemnameCtid(item, index) {
      item.articleCtidAndCsNameModuleListStr.splice(index, 1)
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    // getTimeFormat(timeS){
    //   let time = (new Date(timeS).getTime()) / 1000; //除1000 是变成秒级的时间戳 不除就是毫秒级
    //   return time+86400;
    // },
    // timestampToTime(cjsj){
    //   //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    //   var date = new Date(cjsj*1000);
    //   var Y = date.getFullYear() + '-';
    //   var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth()+1) : date.getMonth() + 1) + '-';
    //   var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
    //   var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
    //   var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
    //   var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
    //   return  Y + M + D + h + m + s;
    // }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-textarea textarea {
  height: 140px !important;
}

::v-deep.el-input--prefix .el-input__inner {
  width: 140px !important;
}

::v-deep.el-date-editor.el-input {
  width: 140px !important;
}

@font-face {
  font-family: 'fonts';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/PingFangMedium.ttf');
}

@font-face {
  font-family: 'fonts_title';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/newFont.OTF');
}

.flex {
  display: flex;
  justify-content: flex-start;
  margin-top: -100000px;

  .shuru {
    width: 500px;
    height: 812px;
    width: 414px;
    padding: 40px 0px;
    border: 1px solid #ccc;
  }

  .yulan {
    width: 100px;
    height: 812px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .preview_details {
    width: 414px;
    background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/c5e1b324d9583662ebd46a6c03997603_400x1457.png');
    background-size: 100%;
    font-family: 'fonts';
    padding: 23px 0px;

    .tips {
      line-height: 0px;

      img {
        width: 100%;
        height: auto;
      }
    }

    .head_text {
      width: 323px;
      margin: 30px auto 0px auto;

      img {
        width: 201px;
        height: auto
      }

      .title_div {
        background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230606/1a778f5bdd0dd8827cc36fc054118271_400x198.png');
        background-repeat: no-repeat;
        width: 323px;
        height: 160px;
        background-size: 100%;
        margin-top: 7px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .bode {
          position: absolute;
          right: -20px;
          bottom: 31px;

          img {
            width: 75px;
            height: 63px;
          }
        }

        .title {
          width: 200px;
          text-align: center;
          font-size: 24px;
          height: 78px;
          color: #fff;
          font-family: 'fonts_title'
        }
      }
    }

    .bigverse {
      img {
        width: 274px;
        height: 42px
      }
    }

    .bigverse_top {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_bottom {
      width: 414px;
      margin: 0 auto;
      line-height: 0px;

      img {
        width: 100%;
        height: auto
      }
    }

    .bigverse_body {
      width: 414px;
      // min-height: 421px;
      margin: 0 auto;
      background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20230612/bb4106b2df078e4b75df39922f6f09f3_400x244.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 1px 42px 0px 42px;
      color: #fff;
      letter-spacing: 2px;

      .neirong {
        .title {
          font-size: 18px;
          margin-bottom: 30px;
        }

        >div {
          margin-top: 10px;
          line-height: 22px;
          font-size: 16px;
        }
      }

      .official_msg {
        margin-top: 30px;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0px;

        .title {
          margin-bottom: 10px;
        }

        color:#20AFAC;
      }

      .footer {
        margin-top: 40px;
        text-align: right;
        font-size: 18px;
        letter-spacing: 2px;
      }
    }

    .code {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 40px 0px;

      img {
        width: 100%;
        height: auto;
      }
    }
  }
}

.width_footer {
  margin-top: 10px;
}

.noSpacing {
  letter-spacing: 0px;
}

::v-deep.red_input textarea {
  color: rgb(255, 29, 29) !important;
}
</style>
