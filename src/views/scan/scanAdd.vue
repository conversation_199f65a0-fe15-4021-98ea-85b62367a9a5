<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
     <!-- <template #csName="scope">
        <el-input placeholder="请输入系列名称/系列ID" style="width:300px" @keyup.enter.native="search" v-model="formData.csName">
        </el-input>
        <el-button type="primary" style="margin-left: 10px;" @click="search">搜索</el-button>
        <div class="search">
          <span>搜索结果</span>
          <div v-if="searchList!=null">
            <el-tag type="" style="margin-right: 10px;cursor: pointer;margin-bottom:10px"
              v-for="(item,index) in searchList" @click="clickName(item)">{{item.name}}({{item.ctid}})</el-tag>
          </div>
          <div v-else style="color:rgb(255, 29, 29)">
            没有搜索结果，请重新输入关键字后重试
          </div>
        </div>
        <div class="search" v-if="ctidStr!=''">
          <span>已选择系列</span>
          <div>
            <el-tag type="success" style="margin-right: 10px;cursor: pointer;margin-bottom: 4px;">{{ctidStr}}</el-tag>
          </div>
        </div>
      </template> -->
       <template #msg="scope">
         <p style="color:rgb(255, 29, 29);margin-top:-20px">为保证扫面任务正常执行，系列流通数在5万以上的, 扫描时长必须在20分钟以上, 5万以下的, 必须在10分钟以上, 谢谢配合</p>
       </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    dutyAdd
  } from '@/api/mallCenter'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'scanAdd',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          ctid: '',
          afterOperateType: 'NO_CHANGE',
          endTime: '',
          startTime: ''
        },
        formSchema: [{
            type: 'search',
            label: '系列名称：',
            placeholder: '请输入系列名称/系列ID',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入系列名称/系列ID',
              trigger: 'blur'
            }]
          },
          {
            type: 'datetime',
            label: '扫描开始时间：',
            placeholder: '请选择扫描开始时间',
            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择扫描开始时间',
              trigger: 'blur'
            }]
          },
          {
            type: 'datetime',
            label: '扫描结束时间：',
            placeholder: '请选择扫描结束时间',
            field: 'endTime',
            rules: [{
              required: true,
              message: '请选择扫描结束时间',
              trigger: 'blur'
            }]
          },
          {
            label: '扫描结束后：',
            field: 'afterOperateType',
            type: 'radio',
            options: [{
              label: '仍旧仅供收藏 ',
              value: 'NO_CHANGE'
            }, {
              label: '恢复寄售（慎重选择，整个系列都会正常流通）',
              value: 'NOT_SALE_SIGN0'
            }, {
              label: '销毁（慎重选择，仅置换使用）',
              value: 'DESTROY'
            }],
            rules: [{
              required: true,
              message: '请选择扫描结束后操作',
              trigger: 'blur'
            }]
          },
          {
            slot: 'msg',
          },
          {
            type: 'action'
          },

        ],
        searchList: [],
        isError: '',
        ctidStr: ''
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        let res = await this.$api.dutyInfo({
          dutyId: this.templateId,
          dutyType: 'WHITE_USER_SCAN'
        })
        res.result.startTime = res.result.startTime + '.000'
        res.result.endTime = res.result.endTime + '.000'
        this.formData = {
          ...res.result,
          afterOperateType: res.result.whiteUserScanExtra.afterOperateType,
          csName: res.result.whiteUserScanExtra.csName,
          excelUrl: res.result.whiteUserScanExtra.excelUrl,
        }
        console.log(this.formData.startTime)
      },
      async submit() {
        // if (this.isError) {
        //   this.$message.error('当前最高总卖出去价与预期价格相差过大，请检查')
        // } else {
        let startTime, endTime,ctid
        ctid = this.formData.ctid
        if(ctid){
          ctid=ctid.split("(")[1].split(")")[0]
        }
        startTime = this.formData.startTime
        endTime = this.formData.endTime
        let extra = JSON.stringify({
          'afterOperateType': this.formData.afterOperateType
        })
        const data = {
          itemImportUrl: this.formData.itemImportUrl,
          ctid,
          dutyType: 'WHITE_USER_SCAN',
          startTime,
          endTime,
          extra,
          dutyId: this.templateId ? this.templateId : ''
        }
        console.log(data)
        // }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.templateId) {
            let res = await this.$api.dutyEdit(data);
            if (res.status.code == 0) {
              this.routerBack()
            }
          } else {
            let res = await this.$api.dutyAdd(data);
            if (res.status.code == 0) {
              this.routerBack()
            }
          }

        })
      },
      async search() {
        this.ctidStr = ''
        let res = await this.$api.searchPgc({
          name: this.formData.csName
        });
        if (res.status.code == 0) {
          this.searchList = res.result.list
        }
      },
      clickName(item) {
        this.formData.csName = item.name
        this.formData.ctid = item.ctid
        this.ctidStr = `${item.name}(${item.ctid})`
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'GOODS_DUTY_SET_SELL_CONTRACT_ADDRESS'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeMax() {
        let res = await this.$api.maxSellAmountValidate({
          expectSellNum: this.formData.expectSellNum,
          floorPricePlus: this.formData.floorPricePlus,
          floorPriceMinus: this.formData.floorPriceMinus,
          maxSellAmount: this.formData.maxSellAmount,
          ctid: this.formData.ctid,
        });
        if (res.status.code == 0) {
          this.isError = false
        } else {
          this.isError = true
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
