<template>
	<d2-container class="page" ref="returnTop">
		<common-table :table-schema="tableSchema" :loading="loading" :showIndex="false" :table-data="tableData">
			<template #action-header>
				<el-button type="primary" size="mini" @click="nav_task()">新增任务</el-button>
			</template>
			<template #action="scope">
        <el-button @click="toEdit(scope.row)" :disabled="scope.row.dutyStatus=='DONE' ||scope.row.dutyStatus=='DOING'||scope.row.dutyStatus=='FAIL'" type="text">修改任务</el-button>
				<el-button @click="del(scope.row.dutyId)" :disabled="scope.row.dutyStatus=='DONE'||scope.row.dutyStatus=='DOING'||scope.row.dutyStatus=='FAIL'" type="text">删除任务</el-button>
			</template>
      <template #excelUrl="scope">
        <span v-if="scope.row.excelUrl==null">/</span>
      	<el-button @click="download(scope.row)" type="text" v-else>导出</el-button>
      </template>
		</common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
    	<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
    	 :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
    		style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="currentChangeSize">
    	</el-pagination>
    </div>
	</d2-container>
</template>

<script>
	import CommonQuery from '@/components/CommonQuery'
	import CommonTable from '@/components/CommonTable'
  import {
		dutyDelete,
    dutyList
	} from '@/api/mallCenter'
	export default {
		name: 'scan',
		components: {
			CommonQuery,
			CommonTable
		},
		data() {
			return {
				page: {
					totalCount: 0,
					pageSize: 20,
					pageNum: 1
				}, // 分页数据
				query: {
				},
				tableSchema: [ // 表格架构
					{
						label: '扫描任务ID',
						field: 'dutyId'
					},
					{
						label: '相关系列id',
						field: 'ctid',
            width: '300px'
					},
					{
						label: '系列名称',
						field: 'csName',
						width: '200px'
					},
          {
          	label: '开始时间',
          	field: 'startTime',
          	width: '160px'
          },
          {
          	label: '结束时间',
          	field: 'endTime',
          	width: '160px'
          },
          {
          	label: 'excel',
          	slot: 'excelUrl',
          },
          {
          	label: '任务状态',
          	field: 'dutyStatus',
            type:'tag',
            tagMap: {
             INIT: '准备中',
             DOING : '执行中',
             DONE: '已完成',
             FAIL: '执行失败'
           },
          	width: '110px'
          },
          {
          	label: '操作',
          	slot: 'action',
          	headerSlot: 'action-header',
          	width: '200px',
          	fixed: 'right'
          }
				],
        tableData:[],
        loading:false
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			toEdit(item) {
        console.log(item)
        this.$router.push({
        	name: 'scanAdd',
        	query: {
        		templateId: item.dutyId,
        	}
        })
			},
      nav_task(){
        this.$router.push({
        	name: 'scanAdd',
        })
      },
			scrollEvent(e) {
				console.log(e.y) // 获取目标元素的滚动高度
			},
			// 过滤查询
			onQueryChange(data) {
				this.query = data
				this.getList(true)
			},
			// 分页改变
			currentChange(value) {
				this.page.pageNum = value
				this.getList()
			},
      // 分页改变
      currentChangeSize(value) {
      	this.page.pageSize = value
      	this.getList()
      },
			getList() {
        this.loading=true
				const params = {
					...this.query,
					...this.page,
					pageNum: this.page.pageNum,
          dutyType:'WHITE_USER_SCAN'
				}
        let dataList=[]
				dutyList(params).then(res => {
          this.loading=false
					const data = res.result.list
          data.forEach((item)=>{
            dataList.push({
              ...item.whiteUserScanExtra,
              ctid:item.ctid,
              startTime:item.startTime,
              endTime:item.endTime,
              dutyId:item.dutyId,
              // createAt:item.createAt,
              dutyStatus:item.dutyStatus
            })
          })
					this.tableData = dataList
          this.tableData.forEach((item)=>{
            item=item.setSellExtra
             console.error(item)
            this.$forceUpdate();
          })
					this.page.totalCount = res.result.totalCount
					this.page.pageSize = res.result.pageSize
					this.page.pageCount = res.result.pageCount
				})
			},
			// 发表/下架
			del(id) {
				this.$confirm(`确定删除该任务吗？`, '', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					dutyDelete({
						dutyId:id,
            dutyType:'WHITE_USER_SCAN'
					}).then((res) => {
            if(res.status.code==0){
              this.$message.success(res.status.msg)
              this.getList()
            }
					})
				})
			},
      download(item){
        window.open(item.excelUrl)
      }
		}
	}
</script>

<style lang="scss" scoped>

</style>
