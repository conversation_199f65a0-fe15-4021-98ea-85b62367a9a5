<template>
  <d2-container class="page">
    <div class="box">
      <div class="tit">
        <div></div>
        <el-button type="primary" @click="submitAll">提交执行</el-button>

      </div>
      <div class="tit">
        <div>创建时间</div>
        <div style="color: green;">{{currentTime}}</div>
      </div>
      <!-- <div class="tit">
        <div>修改</div>
        <div>【挂单不随市价发生变换。仅用于防止连环穿仓。三重保险】</div>
      </div> -->
      <div class="tit">
        <div></div>
        <div style="color: red;">【数量为零不挂单，提交执行开始挂单】</div>
      </div>


    </div>



    <div class="list" v-for="(item,index) in list" :key="index">
      <div class="tit">
        <div>阶段{{index+1}}</div>
        <div></div>
      </div>
      <div>
        <div>是否开启定时替换 ：</div>
        <div>
          <el-radio-group v-model="item.replace">
            <el-radio :label="true">开启</el-radio>
            <el-radio :label="false">暂停</el-radio>
          </el-radio-group>
        </div>
      </div>

      <div class="tit">
        <div class="assistantBox">
          <div></div>
          <div class="assistantTable">
            <div class="inp" v-for="(item2,index2) in item.percentExtraDTOList" :key="index2">
              <div></div>
              <div>
                +-<el-input v-model="item2.price" type="number"></el-input>——
                <el-input v-model="item2.percent" type="number"></el-input>%
              </div>
            </div>

            <div style="display: flex;">
              <el-button type="primary" style="margin-top:20px;margin-left:170px;"
                @click="addNumber(index)">添加</el-button>
              <el-button type="primary" style="margin-top:20px;" @click="deletNumber(index)">减少</el-button>
            </div>

            <div class="inp" style="margin-top:20px;margin-right: 150px;">
              <div>切换随机时间:(分钟-分钟)</div>
              <div></div>
            </div>

            <div class="inp" style="margin-top:20px;">
              <div></div>
              <div>
                【
                <el-input v-model="item.startMinute" type="number"></el-input>
                <div>——</div>
                <el-input v-model="item.endMinute" type="number"></el-input>】
              </div>

            </div>

          </div>
        </div>

      </div>


      <!-- 做多价格 -->
      <div class="inp" style="margin-top: 30px;">
        <div>做多价格: </div>
        <div>
          【
          <el-input v-model="item.longPriceStart" type="number"></el-input>——
          <el-input v-model="item.longPriceEnd" type="number"></el-input>】
        </div>

      </div>
      <!-- 做多份数 -->
      <div class="inp">
        <div>做多份数: </div>
        <div>【
          <el-input v-model="item.longQuantity" type="number"></el-input>
          】
        </div>
      </div>
      <!-- 做空价格 -->
      <div class="inp">
        <div>做空价格: </div>
        <div>【
          <el-input v-model="item.shortPriceStart" type="number"></el-input>——
          <el-input v-model="item.shortPriceEnd" type="number"></el-input>
          】
        </div>
      </div>
      <!-- 做空份数  -->
      <div class="inp">
        <div>做空份数 : </div>
        <div>【
          <el-input v-model="item.shortQuantity" type="number"></el-input>
          】
        </div>
      </div>

      <!-- tips -->
      <div class="tips">
        <div></div>
        <div>总挂单量为：
          <span>{{ item.longQuantity*1 + item.shortQuantity*1 ||0}}
          </span>
        </div>
      </div>
      <!-- tips -->
      <div class="tips">
        <div></div>
        <div>可能花费：
          <span>
            {{ (item.longPriceEnd *1 * item.longQuantity *1)|| 0 }} /
            {{ (item.shortPriceEnd *1 * item.shortQuantity *1) ||0}}
          </span>
          元
        </div>
      </div>

    </div>



    <div class="btns">
      <div></div>
      <div>
        <el-button type="danger" @click="delet">取消</el-button>
        <el-button type="success" @click="submit">确定</el-button>
        <el-button type="primary" icon="el-icon-plus" circle @click="add"></el-button>
      </div>
    </div>
  </d2-container>

</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  export default {

    name: "remoteAssistant",
    components: {
      CommonQuery,

    },
    data() {
      return {
        list: [],
        currentTime: '',
        id: null,
        flag: false,
        length: null,
        addNum: 0,
        startMinute: '',
        endMinute: '',
        tableData: [],
        tableNum: 0,
        numberLength: null
      }
    },
    created() {
      this.search()
    },
    methods: {
      addNumber(index) {

        this.list[index].tableNum++

        this.list[index].percentExtraDTOList.push({
          price: "0.00",
          percent: "0",
        })
        console.log(this.list[index])

      },
      deletNumber(index) { // 取消 删除
        // this.list[index].percentExtraDTOList = this.list[index].percentExtraDTOList.splice(0, this.list[index]
        //   .numberLength)
          if (this.list[index] && this.list[index].percentExtraDTOList.length > 1) {
              // 删除数组的最后一个元素
              this.list[index].percentExtraDTOList.pop();
              console.log(this.list[index].percentExtraDTOList)
          }else{
            return
          }

        // if (this.list[index].tableNum === 0) {
        //   console.log("1---", this.numberLength, this.list[index].tableNum)

        //   console.log(this.list[index])
        // } else {
        //   console.log("2---", this.numberLength, this.list[index].tableNum)
        //   this.list[index].tableNum--
        //   this.list[index].percentExtraDTOList = this.list[index].percentExtraDTOList.splice(0, this.list[index]
        //     .numberLength + this.list[index].tableNum)
        //   console.log(this.list[index])
        // }
      },
      async submitAll() {
        const res = await this.$api.operateFurtherAssistant()
        if (res.status.code === 0) {
          this.$message({
            message: '执行成功',
            type: 'success'
          })
          this.search()
        }
      },
      async search() { // 超远期助理查询
        const res = await this.$api.searchFurtherAssistant()

        if (res.status.code === 0) {

          this.list = []
          this.tableData = []
          this.currentTime = res.currentTime

          this.startMinute = res.result.startMinute
          this.endMinute = res.result.endMinute
          // this.id = res.result.list.id
          // this.list = res.result.list
          this.list = res.result.list.map((item, i) => {
            console.log('ITEM',item)
            item.percentExtraDTOList = item.percentExtraDTOList==null?[]:item.percentExtraDTOList
            return {
              ...item,
              ...{
                tableNum:0,
                numberLength:1
              },


            }
          })



          console.log("this.list---", this.list)

          // this.numberLength = this.list[index].percentExtraDTOList.length
          this.length = this.list.length
          this.numberLength = this.tableData.length

        }
      },
      async submit() { // 超远期助理创建或更新
        // console.log(this.list)
        console.log("this.tableData", this.tableData)
        let furtherDetailExtra = this.list
        furtherDetailExtra = JSON.stringify(furtherDetailExtra)

        const res = await this.$api.createOrUpdateFurtherAssistant({
          furtherDetailExtra,
        })
        if (res.status.code === 0) {
          this.$message({
            message: '提交成功',
            type: 'success'
          })
          this.search()
        }
        console.log(res, '超远期助理创建或更新')
      },
      // async delet () {
      //   console.log(this.list[this.list.length-1])
      //   let param = this.list[this.list.length-1]
      //   const res = await this.$api.deleteFurtherAssistant({
      //     id:param.id
      //   })
      //   if (res.status.code === 0) {
      //     this.$message({ message: '删除成功', type: 'success' })
      //     this.search()
      //   }
      // },
      delet() { // 取消 删除阶段
        if (this.addNum === 0) {
          console.log(1111)
          console.log(this.list)
          this.list = this.list.splice(0, this.length)
          console.log(this.list)
        } else {
          console.log(2222)
          this.addNum--
          this.list = this.list.splice(0, this.length + this.addNum)
        }
      },
      check(arr) {
        for (const key in arr) {
          if (arr[key].length <= 0) {
            console.log(key, 'key')
            this.$message({
              message: '请检查是否填写完整',
              type: 'warning'
            })
            return 1
          }
        }
      },
      add() { // 添加新阶段
        if (this.list.length >= 10) {
          return this.$message({
            type: 'info',
            message: '最多可存在10个阶段'
          })
        }
        if (this.check(this.list.length - 1) === 1) {
          return
        }
        this.addNum++

        // let longPrice = `longPrice${this.list.length + 1}`
        // let longQuantity = `longQuantity${this.list.length + 1}`
        // let shortPrice = `shortPrice${this.list.length + 1}`
        // let shortQuantity = `shortQuantity${this.list.length + 1}`
        // console.log("this.list[this.list.length + 2]",this.list.length + 1)
        this.list.push({
          longPrice: '',
          longQuantity: '',
          shortPrice: '',
          shortQuantity: '',
          percentExtraDTOList:[]
        })
        console.log("this.list", this.list)
      },
    }
  }
</script>

<style lang="scss" scoped>
  .box,
  .list {
    width: 80%;
    position: relative;
    margin-top: 30px;

    >div {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      >div:nth-child(1) {
        width: 30%;
        font-size: 14px;
        color: #606266;
        margin-right: 30px;
        text-align: right;
      }
    }
  }

  .assistantBox {
    position: absolute;
    margin-left: 1250px;
    margin-top: 350px;

    margin-bottom: 50px;

    .assistantTable {
      width: 500px;

    }
  }

  .tips {
    font-size: 14px;
    color: red;

    span {
      color: green;
    }
  }

  .tit {
    >div:nth-child(1) {
      font-size: 18px !important;
      font-weight: 700;
    }

    >div:nth-child(2) {
      color: red;
    }
  }

  .inp {
    >div:nth-child(2) {
      display: flex;
      align-items: center;
    }
  }

  .btns {
    width: 80%;
    display: flex;
    align-items: center;

    >div:nth-child(1) {
      width: 30%;
    }

    >div:nth-child(2) {
      display: flex;
      align-items: center;
    }
  }

  .red {
    color: red;
  }

  .el-radio-group {
    line-height: 0;
  }

  .el-input__inner {
    height: 30px;
  }
</style>
