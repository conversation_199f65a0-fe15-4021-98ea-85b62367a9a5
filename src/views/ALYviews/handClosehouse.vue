<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" :showRefresh="true" :showCreation="true"
      @onCreation="dialogVisible=true" @onSubmit="onSubmit" @onReset="onReset" @onRefresh="onRefresh"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action="scope">
          <el-button @click="revoke(scope.row.id)" type="text"
             :disabled="scope.row.enableRevoke!='撤销'">撤销</el-button>
             <el-button type="text" @click="openText(scope.row.orderIdList)">查看订单</el-button>
      </template>
    </common-table>

    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>

    <el-dialog title="新建任务" :visible.sync="dialogVisible" width="30%">
      <div class="dialog">
        <div class="radio">
          <div>平多/平空</div>
          <div>
            <el-radio-group v-model="handOpenExtraDTO.longShort">
              <el-radio :label="1">平多</el-radio>
              <el-radio :label="2">平空</el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 价格 -->
        <div class="inp">
          <div>价格</div>
          <div><el-input v-model="handOpenExtraDTO.price"></el-input> </div>
        </div>
        <!-- 份数 -->
        <div class="inp">
          <div>份数</div>
          <div><el-input v-model="handOpenExtraDTO.quantity"></el-input> </div>
        </div>
        <div class="text">
          <div></div>
          <div>本任务最多收回金额为： <span>
              {{handOpenExtraDTO.price * 1 * handOpenExtraDTO.quantity * 1}}</span>
          </div>
        </div>
        <!-- 时间 -->
        <div class="radio">
          <div>时间</div>
          <div>
            <el-radio-group v-model="handOpenExtraDTO.isTiming">
              <el-radio :label="0">立即</el-radio>
              <el-radio :label="1">定时</el-radio>
            </el-radio-group>
          </div>
          <div v-show="handOpenExtraDTO.isTiming==1">

            <el-date-picker v-model="handOpenExtraDTO.startTime" type="datetime" placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>

          </div>
        </div>

        <!-- 任务开始前是否暂停自动开仓 -->
        <div class="radio">
          <div>任务开始前是否暂停自动开仓</div>
          <div>
            <el-radio-group v-model="handOpenExtraDTO.beginClose">
              <el-radio :label="1">暂停</el-radio>
              <el-radio :label="0">不暂停</el-radio>
            </el-radio-group>
          </div>
        </div>
        <!-- 任务完成后是否恢复自动开仓 -->
        <div class="radio">
          <div>任务完成后是否恢复自动开仓</div>
          <div>
            <el-radio-group v-model="handOpenExtraDTO.endStart">
              <el-radio :label="1">恢复</el-radio>
              <el-radio :label="0">不恢复</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false,flag1=false,flag2=false">取
          消</el-button>
        <el-button type="primary" @click="createHand">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
  import CommonQuery from '@/components/CommonQuery_h'

  export default {
    name: "handClosehouse",
    components: {
      CommonTable,
      CommonQuery
    },
    data() {
      return {
        dialogVisible: false,
        tableData: [],
        searchObj: {},
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        handOpenExtraDTO: {
          beginClose: null, // 开始前暂停 0 不暂停 1 暂停
          endStart: null, // 结束后恢复 0 不恢复 1 恢复
          filledQuantity: null, // 已完成份数
          isTiming: null, // 定时
          longShort: null, // 多空  //1多 0空
          price: null, // 平仓价格
          quantity: null, // 份数
          startTime: null // 开始时间
        },
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'id',
            width: '120px'
          },
          {
            label: '创建时间',
            field: 'createAt',
            width: '150px'
          },
          {
            label: '订单id',
            field: 'orderIdList',
            width: '150px',
            showOverflowTooltip:true
          },
          {
            label: '平多/平空',
            field: 'longShort',
            width: '200px'
          },
          {
            label: '平仓价格',
            field: 'price',
            width: '100px'
          },
          {
            label: '份数',
            field: 'quantity',
            width: '100px'
          },
          {
            label: '已平仓份数',
            field: 'filledQuantity',
            width: '100px'
          },
          {
            label: '状态',
            field: 'status',
            width: '150px'
          },
          {
            label: '开始时间',
            field: 'startTime',
            width: '150px'
          },
          {
            label: '完成时间',
            field: 'completeTime',
            width: '150px'
          },
          {
            label: '撤销时间',
            field: 'revokeTime',
            width: '150px'
          },
          {
            label: '操作',
            slot: 'action',
            width: '140px',
            fixed: 'right'
          }
        ],
        query: {
          dutyStatus: 'DOING'
        },
        querySchema: [ // 搜索组件架构
          {
            type: 'select',
            label: '任务状态：',
            placeholder: '请选择任务状态',
            field: 'status',
            options: [{
                label: '初始',
                value: 'INIT'
              }, {
                label: '执行中',
                value: 'DOING'
              },

              {
                label: '部分完成已撤销',
                value: 'HALF_REVOKE'
              },
              {
                label: '已撤销',
                value: 'REVOKE'
              },
              {
                label: '已完成',
                value: 'DONE'
              },
              {
                label: '失败',
                value: 'FAIL'
              }
            ]

          },
          {
            type: 'select',
            label: '平多/平空：',
            placeholder: '请选择平多/平空',
            field: 'more',
            options: [{
              label: '平多',
              value: '1'
            }, {
              label: '平空',
              value: '2'
            }]
          },
          {
            type: 'datetimerange',
            label: '日期：',
            field: 'start',
            field2: 'end'
          }
        ],
        nowPrice: 0,
        flag1: false,
        flag2: false

      }
    },
    created() {
      this.search()
      this.MarketPrice()
    },
    methods: {
      onSubmit(obj) { // 筛选
        this.searchObj = obj
        this.search()
      },
      onReset() { // 清空
        this.searchObj = {}
        this.search()
      },
      onRefresh() { // 刷新
        this.$router.go()
      },
      async search() { // 查询
        const {
          result,
          status
        } = await this.$api.searchHandTradeDuty({
          dutyType: 'HAND_CLOSE',
          startTime: this.searchObj.start || null,
          endTime: this.searchObj.end || null,
          longShort: this.searchObj.more || null,
          pageNum: this.page.pageNum,
          pageSize: this.page.pageSize,
          status: this.searchObj.status || null
        })
        if (status.code === 0) {
          result.list.forEach((item) => {
            item.orderIdList = item.orderIdList != null ? item.orderIdList.toString() : ""
          })
          this.tableData = result.list
          this.page.totalCount = result.totalCount
          console.log(result.totalCount, 'result.totalCount')
          this.tableData.forEach(item => {
            if (item.longShort === 1) {
              item.longShort = '平多'
            } else {
              item.longShort = '平空'
            }
            if (!item.revokeTime) {
              item.revokeTime = '/'
            }
            if (!item.enableRevoke) {
              item.enableRevoke = '/'
            } else {
              item.enableRevoke = '撤销'
            }
            if (!item.completeTime) {
              item.completeTime = '/'
            }
            if (item.status === 'INIT') {
              item.status = '初始'
            } else if (item.status === 'DOING') {
              item.status = '执行中'
            } else if (item.status === 'DONE') {
              item.status = '完成'
            } else if (item.status === 'REVOKE') {
              item.status = '已撤销'
            } else if (item.status === 'HALF_REVOKE') {
              item.status = '部分执行已撤销'
            } else if (item.status === 'FAIL') {
              item.status = '失败'
            }
          })
          console.log(this.tableData, '查询')
        }
      },
      async createHand() { // 创建
        const that = this
        // 手动平仓
        console.log(this.handOpenExtraDTO.price, this.nowPrice * 0.995)
        if (this.check() === 1) {
          return
        }
        // 价格   <    当前市价 *1.005
        if (this.handOpenExtraDTO.longShort === 1 && this.handOpenExtraDTO.price < this.nowPrice * 1.005) { // 多
          if (!that.flag1) {
            that.flag1 = true
            return this.$message({
              message: '请再次确认平仓方向没有错，当前输入可能有误',
              type: 'warning'
            })
          }
        }
        // 价格   >     当前市价 *0.995
        if (this.handOpenExtraDTO.longShort === 2 && this.handOpenExtraDTO.price > this.nowPrice * 0.995 && !that
          .flag2) { // 空
          if (!that.flag2) {
            that.flag2 = true
            return this.$message({
              message: '请再次确认平仓方向没有错，当前输入可能有误',
              type: 'warning'
            })
          }
        }

        const res = await this.$api.createHandTradeDuty({
          dutyType: 'HAND_CLOSE',
          extra: JSON.stringify(this.handOpenExtraDTO)
        })
        if (res.status.code === 0) {
          this.$message({
            type: 'success',
            message: '创建成功'
          })
          that.flag1 = false
          that.flag2 = false
          that.dialogVisible = false
          this.search()
        }
      },
      async revoke(id) { // 撤销
        const res = await this.$api.revokeHandTradeDuty({
          dutyType: 'HAND_CLOSE',
          id: id
        })
        if (res.status.code === 0) {
          this.$message({
            type: 'success',
            message: '撤销成功'
          })
          this.search()
        }
      },
      check() {
        if (this.handOpenExtraDTO.longShort === null) {
          this.$message({
            message: '请选择多or空',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.price === null) {
          this.$message({
            message: '请输入价格',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.quantity === null) {
          this.$message({
            message: '请输入份数',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.isTiming === null) {
          this.$message({
            message: '请选择时间',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.isTiming === 1 && this.handOpenExtraDTO.startTime === null) {
          this.$message({
            message: '请选择定时时间',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.endStart === null) {
          this.$message({
            message: '请选择结束后是否恢复',
            type: 'warning'
          })
          return 1
        } else if (this.handOpenExtraDTO.beginClose === null) {
          this.$message({
            message: '请选择开始前是否暂停',
            type: 'warning'
          })
          return 1
        }
      },
      async MarketPrice() { // 当前市价
        const res = await this.$api.getRealMarketPrice()
        this.nowPrice = res.result
        console.log(res, '当前市价')
      },
      currentChange(value) { // 分页
        this.page.pageNum = value
        this.search()
      },
      currentChangeSize(value) {
        this.page.pageSize = value
        this.search()
      },
      openText(text){
      	console.log(text)
      	 this.$alert(text, '订单ID', {
      	  confirmButtonText: '确定',
      	  callback: action => {

      	  }
      	});
      },
    }
  }
</script>

<style lang="scss" scoped>
  .dialog {
    font-size: 18px;

    >div {
      width: 100%;
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      >div:nth-child(1) {
        width: 40%;
        text-align: center;
      }

      >div:nth-child(2) {
        flex: 1;
      }
    }

    .text {
      font-size: 14px;
      color: red;

      span {
        color: green;
      }
    }

    .el-radio-group {
      line-height: 0;
    }

    .el-input__inner {
      height: 30px !important;
    }
  }
</style>
