<template>
  <d2-container class="page">
    <common-query :showExport="true" @onExport="houseExport" :query-schema="querySchema" :data="query"
      @onSubmit="onQueryChange" @onReset="onQueryReset"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData" :loading='listLoading'>
      <template #closeHouse="scope">
        <!-- <el-button v-if="scope.row.closeType == 0" disabled="true" type="primary">未平仓</el-button>
          <el-button v-if="scope.row.closeType == 1" disabled="true" type="success" size="mini"
            style="color: green;background: #7dff13;">主动平仓</el-button>
          <el-button v-if="scope.row.closeType == 2" disabled="true" type="primary" size="mini"
            style="color: #ffffff;background: #710fe0;">强制平仓</el-button>
          <el-button v-if="scope.row.closeType == 3" disabled="true" type="info" size="mini">止盈止损平仓</el-button> -->

        <el-button v-if="scope.row.closeStatus == 0" disabled="true" type="primary" size="mini">委托中</el-button>
        <el-button v-if="scope.row.closeStatus == 2" disabled="true" type="info" size="mini">已撤销</el-button>
        <el-button v-if="scope.row.closeStatus == 3" disabled="true" type="primary" size="mini">部分成交</el-button>
        <el-button v-if="scope.row.closeStatus == 4" disabled="true" type="info" size="mini">平仓中</el-button>
        <el-button v-if="scope.row.closeStatus == 6" disabled="true" type="info" size="mini">部分成交已撤销</el-button>
        <el-button v-if="scope.row.closeStatus == 10" disabled="true" type="primary" size="mini">部分成交</el-button>

        <el-button v-if="scope.row.closeStatus == 1 && scope.row.closeType == 2" disabled="true" type="primary"
          size="mini" style="color: #ffffff;background: #710fe0;">已强平</el-button>
        <el-button v-if="scope.row.closeStatus == 1 && scope.row.closeType == 1" disabled="true" type="info"
          size="mini">
          已平仓</el-button>
        <el-button v-if="scope.row.closeStatus == 1 && scope.row.closeType == 0" disabled="true" type="info"
          size="mini">
          未平仓</el-button>
      </template>

      <template #lever="scope">
        <el-button v-if="scope.row.closeLever == 2" disabled="true" size="mini"
          style="color: #fff;background: #ff5470;">2倍</el-button>
        <el-button v-if="scope.row.closeLever == 5" disabled="true" size="mini"
          style="color: #fff;background: #ff6961;">5倍</el-button>
        <el-button v-if="scope.row.closeLever == 10" disabled="true" size="mini"
          style="color: #fff;background: #f80000;">10倍</el-button>
        <el-button v-if="scope.row.closeLever == 1 && scope.row.longShort == 1" disabled="true" size="mini"
          style="color: red;background: #ffd2d0;">1倍</el-button>
        <el-button v-if="scope.row.closeLever == 1 && scope.row.longShort == 2" disabled="true" size="mini"
          style="color: #fff;background: #2a2a2a;">1倍</el-button>
      </template>
    </common-table>

    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination>

  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import {
    downloadBlob
  } from '@/utils/helper'
export default {
  name: "closeHouseRecond",
  components: {
    CommonQuery,
    CommonTable
  },
  props: {},
  data() {
    return {
      listLoading: true,
      tableData: [],
      query: {
        userType: '1'
      },
      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型

      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '平仓ID：',
          placeholder: '请输入平仓ID',
          field: 'orderId'
        },
        {
          type: 'datetimerange',
          label: '平仓时间：',
          placeholder: '请输入平仓时间',
          field: 'createStart',
          field2: 'createEnd',
        },
        {
          type: 'input',
          label: '用户con add：',
          placeholder: '请输入用户con add',
          field: 'contractAddress'
        },
        {
          type: 'select',
          label: '用户类型：',
          field: 'userType',
          placeholder: '请选择用户类型',
          options: [{
            label: '主力',
            value: "4"
          },
          {
            label: '大户',
            value: '1'
          },
          {
            label: '中户',
            value: '2'
          },
          {
            label: '小户',
            value: '3'
          }
          ],
        },
        {
          type: 'select',
          label: '仓位方向：',
          field: 'longShort',
          placeholder: '请选择仓位方向',
          options: [{
            label: '多',
            value: '1'
          },
          {
            label: '空',
            value: '2'
          },
          ]
        },
        {
          type: 'select',
          label: '杠杆：',
          field: 'lever',
          placeholder: '请选择杠杆',
          options: [{
            label: '1倍',
            value: '1'
          },
          {
            label: '2倍',
            value: '2'
          },
          {
            label: '5倍',
            value: '5'
          },
          {
            label: '10倍',
            value: '10'
          },
          ]
        },
        {
          type: 'select',
          label: '状态：',
          field: 'status',
          placeholder: '请选择状态',
          options: [

            {
              label: '委托中',
              value: '0'
            }, {
              label: '已成交',
              value: '1'
            },
            {
              label: '已撤单',
              value: '2'
            },
            // {
            //   label: '下单失败',
            //   value: '3'
            // }, {
            //   label: '平仓中',
            //   value: '4'
            // },
            // {
            //   label: '已平仓',
            //   value: '5'
            // },
            // {
            //   label: '部分成交',
            //   value: '10'
            // },
          ]
        },
      ],
      tableSchema: [ // 表格架构
        {
          label: '平仓ID',
          field: 'closeId',
          width: '170px'
        },
        {
          label: '开仓ID',
          field: 'openId',
          width: '170px'
        },
        {
          label: '平仓时间',
          field: 'createAt',
          width: '200px'
        },
        {
          label: '用户con add',
          field: 'contractAddress',
          width: '200px'
        },
        {
          label: '用户昵称',
          field: 'nickname',
          width: '200px'
        },
        {
          label: '用户类型',
          field: 'userType',
          type: 'tag',
          tagMap: {
            4: {
              label: '主力',

            },
            1: {
              label: '大户',
              tagType: 'info'
            },
            2: {
              label: '中户',
              tagType: 'info'
            },
            3: {
              label: '小户',
              tagType: 'info'
            }
          },
        },
        {
          label: '仓位方向',
          field: 'longShort',
          type: 'tag',
          tagMap: {
            1: {
              label: '多',

            },
            2: {
              label: '空',
              tagType: 'info'
            }
          },
          width: '150px'
        },
        {
          label: '杠杆',
          field: 'closeLever',
          slot: 'lever',
          type: 'tag',
          // tagMap: {
          // 	1: {
          // 		label: '1倍',
          // 	},
          // 	2: {
          // 		label: '2倍',
          // 	},
          //   5: {
          //   	label: '5倍',
          //   },
          //   10: {
          //   	label: '10倍',
          //   },

          // },
          width: '120px'
        },
        {
          label: '份数',
          field: 'closeQuantity',
          width: '120px'
        },
        {
          label: '平仓状态',
          slot: 'closeHouse',
          field: 'closeType',
          width: '150px'
        },
        {
          label: '状态',
          field: 'closeStatus',
          type: 'tag',
          tagMap: {
            0: {
              label: '委托中',
            },
            1: {
              label: '已成交',
              tagType: 'success'
            },
            2: {
              label: '已撤单',
              tagType: 'info'
            },
            3: {
              label: '部分成交',
            },
            4: {
              label: '平仓中',
            },
            5: {
              label: '已平仓',
            },
            10: {
              label: '部分成交',
            },
          },
          width: '80px'
        },
        {
          label: '持仓均价',
          field: 'holdAvgPrice',
          width: '120px'
        },
        {
          label: '平仓均价',
          field: 'closeAvgPrice',
          width: '120px'
        },
        {
          label: '平仓总价(不包含杠杆)',
          field: 'closeTotalPrice',
          width: '150px'
        },
        {
          label: '平仓总价(含杠杆)',
          field: 'closeTotalPriceWithLever',
          width: '150px'
        },
        {
          label: '收益(不算手续费)',
          field: 'income',
          width: '150px'
        },
        {
          label: '收益(算手续费)',
          field: 'incomeWithFee',
          width: '150px'
        },
        {
          label: '开仓实际支付',
          field: 'payMoney',
          width: '150px'
        },
        {
          label: '平仓完成时间',
          field: 'tradeTime',
          width: '150px'
        },

        // {
        // 	label: '操作',
        // 	slot: 'action',

        // 	width: '140px',
        // 	fixed:'right'
        // }
      ],
      tableData: [{}],

      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      query1: {
        userType: '1',
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async houseExport(e) {
      // ExportOpenRecords
      // ExportMarketRecords
      const res = await this.$api.ExportCloseRecords({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '当前平仓' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    // 过滤查询
    onQueryChange(data) {
      this.query1 = data
      this.listLoading = true
      this.getList(true)
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.listLoading = true
      this.getList()
    },
    onQueryReset() {
      this.query = {
        userType: '1'
      }
      this.query1 = {
        userType: '1',
      }
      this.listLoading = true
      this.getList()
    },
    // 获取列表
    async getList(isInit) {

      const params = {
        ...this.query1,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,

      }
      console.log('列表数据', params)
      const {
        status,
        result
      } = await this.$api.qifeiCloseHistory(params)
      console.log('获取列表数据', result)
      if (status.code === 0) {
        this.listLoading = false
        this.tableData = []
        let dataList = []
        const data = result.list
        data.forEach((item) => {
          dataList.push(item)
        })
        this.tableData = dataList
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
        // this.page.pageNum = result.pageCount
        // this.page.totalCount = result.totalCount
        // this.page.pageSize = result.pageSize
        // this.page.pageCount = result.pageCount
      }
    },
  }
}
</script>

<style></style>
