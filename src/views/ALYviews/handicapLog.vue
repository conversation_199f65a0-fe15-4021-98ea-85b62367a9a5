<template>
  <d2-container class="page">
    <common-query :showExport="true" @onExport="houseExport" :query-schema="querySchema" @onSubmit="onQueryChange" ref="query" :data="query"
      @onReset="onQueryReset"></common-query>

    <common-table :table-schema="tableSchema" :table-data="tableData" :loading='listLoading'>
      <template #buy5="scope">
          <div>￥{{scope.row.handicapDetailVOList[0].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[0].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[0].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[0].subNum}}</div>
      </template>
      <template #buy4="scope">
          <div>￥{{scope.row.handicapDetailVOList[1].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[1].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[1].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[1].subNum}}</div>
      </template>
      <template #buy3="scope">
          <div>￥{{scope.row.handicapDetailVOList[2].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[2].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[2].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[2].subNum}}</div>
      </template>
      <template #buy2="scope">
          <div>￥{{scope.row.handicapDetailVOList[3].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[3].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[3].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[3].subNum}}</div>
      </template>
      <template #buy1="scope">
          <div>￥{{scope.row.handicapDetailVOList[4].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[4].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[4].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[4].subNum}}</div>
      </template>
      <template #showPrice="scope" >
          <div style="color: red;">
            ￥{{scope.row.handicapDetailVOList[5].price}}
          </div>
          <div style="color: red;">买:{{scope.row.handicapDetailVOList[5].totalNum}}</div>
          <div style="color: red;">卖:{{scope.row.handicapDetailVOList[5].mainNum}}</div>
      </template>
      <template #sell1="scope">
          <div>￥{{scope.row.handicapDetailVOList[6].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[6].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[6].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[6].subNum}}</div>
      </template>
      <template #sell2="scope">
          <div>￥{{scope.row.handicapDetailVOList[7].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[7].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[7].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[7].subNum}}</div>
      </template>
      <template #sell3="scope">
          <div>￥{{scope.row.handicapDetailVOList[8].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[8].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[8].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[8].subNum}}</div>
      </template>
      <template #sell4="scope">
          <div>￥{{scope.row.handicapDetailVOList[9].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[9].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[9].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[9].subNum}}</div>
      </template>
      <template #sell5="scope">
          <div>￥{{scope.row.handicapDetailVOList[10].price}}</div>
          <div>总:{{scope.row.handicapDetailVOList[10].totalNum}}</div>
          <div>主力:{{scope.row.handicapDetailVOList[10].mainNum}}</div>
          <div>小户:{{scope.row.handicapDetailVOList[10].subNum}}</div>
      </template>
    </common-table>
    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
    </el-pagination>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: "transactionSellRecord",
    components: {
      CommonQuery,
      CommonTable
    },
    props: {},
    data() {
      return {
        listLoading: true,
        tableData: [],
        query: {
          userType: '1',
          startTime:['',''],
          endTime:''
        },
        dialogVisible: false, // 弹窗
        selected: '', // 选择的活动类型
        page: {
          totalCount: 0,
          pageSize: 10
        }, // 分页数据
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '时间：',
            placeholder: '请输入下单时间',
            field: 'startTime',
            field2: 'endTime',
          },

        ],
        tableSchema: [ // 表格架构
          {
            label: '时间',
            field: 'time',
            width: '170px',
            height:'300px'
          },
          {
            label: '买5',
            slot:'buy5',
            width: '150px'
          },
          {
            label: '买4',
            slot:'buy4',
            width: '150px'
          },
          {
            label: '买3',
            slot:'buy3',
            width: '150px'
          },
          {
            label: '买2',
            slot:'buy2',
            width: '150px'
          },
          {
            label: '买1',
            slot:'buy1',
            width: '150px'
          },
          {
            label: '显示市价',
            slot:'showPrice',
            width: '200px'
          },
          {
            label: '卖1',
            slot:'sell1',
            width: '150px'
          },
          {
            label: '卖2',
            slot:'sell2',
            width: '150px'
          },
          {
            label: '卖3',
            slot:'sell3',
            width: '150px'
          },
          {
            label: '卖4',
            slot:'sell4',
            width: '150px'
          },
          {
            label: '卖5',
            slot:'sell5',
            width: '150px'
          },



        ],
        tableData: [{}],

        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query1: {
          userType: '1',
          startTime: '',
          endTime: '',
        }
      }
    },
    mounted() {

      const { start, end,start2, end2 } = this.getTodayRange();
      console.log('Today start time:', start2); // 输出今天的开始时间
      console.log('Today end time:', end2);     // 输出今天的结束时间

      this.query1.startTime=start
      this.query1.endTime=end

      // this.query.startTime[0]=start2
      // this.query.startTime[1]=end2

      this.query.startTime=[start2,end2],
      console.log("this.query.startTime",this.query.startTime)

      this.getList()
    },
    methods: {
      async houseExport(e) {
      // ExportOpenRecords
      // ExportMarketRecords
      const res = await this.$api.ExportMarketRecords({
        ...e
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '盘口记录' + Date.now() + '.xlsx')
        this.$message.success('导出成功')
        this.getList()
      }
    },
      //获取当天时间
      getTodayRange() {
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const endOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate()+1);

        // 格式化日期时间为指定格式
        const startDateStr = this.formatDate(startOfToday, 'yyyy-MM-dd HH:mm:ss.SSS');
        const endDateStr = this.formatDate(endOfToday, 'yyyy-MM-dd HH:mm:ss.SSS');

        const startDateStr2 = this.formatDate(startOfToday, 'yyyy-MM-dd HH:mm:ss');
        const endDateStr2 = this.formatDate(endOfToday, 'yyyy-MM-dd HH:mm:ss');

        return {
          start: startDateStr,
          end: endDateStr,
          start2: startDateStr2,
          end2: endDateStr2
        };
      },
      formatDate(date, format) {
        const padZero = (num) => num.toString().padStart(2, '0');

        return format.replace('yyyy', date.getFullYear())
          .replace('MM', padZero(date.getMonth() + 1)) // 注意月份是从0开始的
          .replace('dd', padZero(date.getDate()))
          .replace('HH', padZero(date.getHours()))
          .replace('mm', padZero(date.getMinutes()))
          .replace('ss', padZero(date.getSeconds()))
          .replace('SSS', padZero(date.getMilliseconds()));
      },
      // 分页切换
      currentChange(value) {
        this.page.pageNum = value
        this.listLoading = true
        this.getList()
      },
      onQueryReset() {
        this.query={
          userType: '1',
          startTime:['',''],
          endTime:''
        },
        this.query1 = {
          userType: '1',
          startTime:'',
          endTime:''
        }
        const { start, end,start2, end2 } = this.getTodayRange();
        this.query1.startTime=start
        this.query1.endTime=end

        this.query.startTime=[start2,end2],

        this.listLoading = true
        this.getList()
        this.query={
          userType: '1',
          startTime:['',''],
          endTime:''
        }
      },
      onQueryChange(data) {
        this.listLoading = true
        console.log("data-----------------------------", data)
        // this.query = data
        this.query1 = data
        this.getList(true)
      },
      // 获取列表
      async getList(isInit) {
        // let ctid;
        // if (this.query.ctid) {
        // 	ctid = this.query.ctid.split("(")[1].split(")")[0]
        // }
        const params = {
          ...this.query1,
          ...this.page,
          pageNum: isInit ? 1 : this.page.pageNum,
          offsets: 2
        }
        console.log('列表数据', params)
        const {
          status,
          result
        } = await this.$api.qifeiHandicapLog(params)
        console.log('获取列表数据', result)
        if (status.code === 0) {
          this.listLoading = false
          let dataList = []
          this.tableData = []
          const data = result.list
          data.forEach((item) => {
            dataList.push(item)
          })
          this.tableData = dataList
          this.page.totalCount = result.totalCount
          // this.page.pageSize = result.pageSize
          this.page.pageCount = result.pageCount
          console.log("this.tableData",this.tableData)

          // this.page.pageNum = result.pageCount
          // this.page.totalCount = result.totalCount
          // this.page.pageSize = result.pageSize
        }
      },
    }
  }
</script>

<style>
</style>
