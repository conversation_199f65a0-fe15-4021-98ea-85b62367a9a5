<template>
  <d2-container class="page">
    <div class="box">
      <!--  -->
      <div>
        <div>状态 ：</div>
        <div>
          <el-radio-group v-model="obj.stopStatus">
            <el-radio :label="'RUN'">开启</el-radio>
            <el-radio :label="'STOP'">暂停</el-radio>
          </el-radio-group>
        </div>
      </div>
      <!-- 分位变化监测频率 -->
      <div>
        <div>
          分位变化监测频率:
        </div>
        <div class="inpFlex">
          <div>【</div>
          <div><el-input
              v-model="obj.fenRateStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              type="number"
              v-model="obj.fenRateEnd"
            ></el-input></div>
          <div>】s/次 </div>
        </div>
      </div>
      <!-- 角位变化监测频率 -->
      <div>
        <div>
          角位变化监测频率:
        </div>
        <div class="inpFlex">
          <div>【</div>
          <div><el-input
              v-model="obj.jiaoRateStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              v-model="obj.jiaoRateEnd"
              type="number"
            ></el-input></div>
          <div>】s/次 </div>
        </div>
      </div>
      <!-- 个位变化监测频率 -->
      <div>
        <div>
          个位变化监测频率:
        </div>
        <div class="inpFlex">
          <div>【</div>
          <div><el-input
              v-model="obj.yuanRateStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              v-model="obj.yuanRateEnd"
              type="number"
            ></el-input></div>
          <div>】s/次 </div>
        </div>
      </div>

      <!-- 分位波动推动平仓幅度 -->
      <div>
        <div>
          分位波动推动平仓幅度:
        </div>
        <div class="inpFlex">
          <div>0.01 * （【</div>
          <div><el-input
              v-model="obj.fenMoneyStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              v-model="obj.fenMoneyEnd"
              type="number"
            ></el-input></div>
          <div>】 </div>
        </div>
      </div>
      <!-- 角位波动推动平仓幅度 -->
      <div>
        <div>
          角位波动推动平仓幅度:
        </div>
        <div class="inpFlex">
          <div>0.01 * （【</div>
          <div><el-input
              v-model="obj.jiaoMoneyStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              v-model="obj.jiaoMoneyEnd"
              type="number"
            ></el-input></div>
          <div>】 </div>
        </div>
      </div>
      <!-- 个位波动推动平仓幅度 -->
      <div>
        <div>
          个位波动推动平仓幅度:
        </div>
        <div class="inpFlex">
          <div>0.01 * （【</div>
          <div><el-input
              v-model="obj.yuanMoneyStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              v-model="obj.yuanMoneyEnd"
              type="number"
            ></el-input></div>
          <div>】 </div>
        </div>
      </div>

      <div>
        <div>
          当价格低于多少时,只向上推动:
        </div>
        <div class="inpFlex">
          <div>【</div>
          <div><el-input
              v-model="obj.flowUpStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              type="number"
              v-model="obj.flowUpEnd"
            ></el-input></div>
          <div>】(每小时随机)</div>
        </div>
      </div>

      <div>
        <div>
          当价格高于多少时,只向下推动:
        </div>
        <div class="inpFlex">
          <div>【</div>
          <div><el-input
              v-model="obj.flowDownStart"
              type="number"
            ></el-input> </div>
          <div>】-【</div>
          <div> <el-input
              type="number"
              v-model="obj.flowDownEnd"
            ></el-input></div>
          <div>】(每小时随机)</div>
        </div>
      </div>

      <div class="btns">
        <div></div>
        <div>
          <el-button
            type="danger"
            @click="close"
          >取消</el-button>
          <el-button
            type="success"
            @click="submit"
          >确定</el-button>
        </div>
      </div>

    </div>
  </d2-container>
</template>

<script>
export default {
  name: "fluctuationAssistant",
  data () {
    return {
      obj: {
        stopStatus: '', // 状态
        fenRateStart: '', // 分位监测开始
        fenRateEnd: '', // 分位监测结束
        jiaoRateStart: '', // 角位监测开始
        jiaoRateEnd: '', // 角位监测结束
        yuanRateStart: '', // 元位监测开始
        yuanRateEnd: '', // 元位监测结束
        fenMoneyStart: '', // 分位平仓开始
        fenMoneyEnd: '', // 分位平仓结束
        jiaoMoneyStart: '', // 角位平仓开始
        jiaoMoneyEnd: '', // 角位平仓结束
        yuanMoneyStart: '', // 元位平仓开始
        yuanMoneyEnd: '', // 元位平仓结束
        flowUpStart:'',//向上推动
        flowUpEnd:'',
        flowDownStart:'',//向下推动
        flowDownEnd:''
      },
      checkObj: {
        stopStatusPla: '请选择状态',
        fenMoneyEndPla: '请输入分位平仓结束数值', // 分位平仓结束
        fenMoneyStartPla: '请输入分位平仓开始数值', // 分位平仓开始
        fenRateStartPla: '请输入分位监测开始数值', // 分位监测开始
        fenRateEndPla: '请输入分位监测结束数值', // 分位监测结束
        jiaoMoneyEndPla: '请输入角位平仓结束数值', // 角位平仓结束
        jiaoMoneyStartPla: '请输入角位平仓开始数值', // 角位平仓开始
        jiaoRateEndPla: '请输入角位监测结束数值', // 角位监测结束
        jiaoRateStartPla: '请输入角位监测开始数值', // 角位监测开始
        yuanMoneyEndPla: '请输入个位平仓结束数值', // 个位平仓结束
        yuanMoneyStartPla: '请输入个位平仓开始数值', // 个位平仓开始
        yuanRateEndPla: '请输入个位监测结束数值', // 个位监测结束
        yuanRateStartPla: '请输入个位监测开始数值',// 个位监测开始
        flowUpStartPla:'',//向上推动
        flowUpEndPla:'',
        flowDownStartPla:'',//向下推动
        flowDownEndPla:''
      },
      flag: false
    }
  },
  created () {
    this.searchFluctuation()
  },
  methods: {
    async searchFluctuation () { // 波动助理查询
      const res = await this.$api.searchFluctuationAssistant()
      if (res.status.code === 0) {
        this.obj = res.result
        if (res.result.stopStatus) {
          this.flag = true
        }
      }
    },
    async submit () { // 波动助理创建或更新
      if (this.check() === 1) {
        return
      }
      const { status } = await this.$api.createOrUpdateFluctuationAssistant({ ...this.obj })
      if (status.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.searchFluctuation()
      }
    },
    close () {
      console.log(this.flag, 'flag')
      if (this.flag) {
        this.searchFluctuation()
      } else {
        for (const key in this.obj) {
          this.obj[key] = ''
        }
      }
    },
    check () {
      for (const key in this.obj) {
        console.log(this.obj.length)
        if (this.obj.length < 0 && key !== 'id') {
          console.log(key, 'key')
          console.log(this.obj, 'this.obj')
          this.$message({
            type: 'warning',
            message: this.checkObj[key + 'Pla']
          })
          return 1
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 80%;
  > div {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    > div:nth-child(1) {
      width: 30%;
      font-size: 14px;
      color: #606266;
      margin-right: 30px;
      text-align: right;
    }
  }
}
.inpFlex {
  display: flex;
  align-items: center;
  .el-input {
    width: 100px;
    height: 30;
  }
  > div {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
}
// 按钮
.btns {
  width: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  > div:nth-child(1) {
    width: 30%;
  }
  > div:nth-child(2) {
    display: flex;
    align-items: center;
  }
}
.el-radio-group {
  line-height: 0;
}
.el-input__inner {
  height: 30px;
}
</style>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type="number"] {
  -moz-appearance: textfield;
}
</style>
