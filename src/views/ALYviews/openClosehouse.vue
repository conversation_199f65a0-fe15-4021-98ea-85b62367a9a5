<template>
  <d2-container class="page">
    <div class="openWarehouse">

      <div class="head">
        <div style="align-items: center;">
          <div>今日平仓金额：</div>
          <div style="color:red">{{ curDayCloseMoney }}</div><span>元</span>
          <div style="margin-left:30px;">(从当日00:00开始计算)。</div>
        </div>
        <div style="align-items: center;">
          <div>今日自动平仓金额：</div>
          <div style="color:red">{{ curDayAutoCloseMoney }}</div><span>元</span>
          <div style="margin-left:30px;">(从当日00:00开始计算)。</div>
        </div>
        <div style="align-items: center;">
          <div>今日波动平仓金额：</div>
          <div style="color:red">{{ curDayFluctuationCloseMoney }}</div><span>元</span>
          <div style="margin-left:30px;">(从当日00:00开始计算)。</div>
        </div>
        <div style="align-items: center;">
          <div>今日手动平仓金额：</div>
          <div style="color:red">{{ curDayHandCloseMoney }}</div><span>元</span>
          <div style="margin-left:30px;">(从当日00:00开始计算)。</div>
        </div>
        <div style="align-items: center;">
          <div>目前底仓(多)：</div>
          <div style="color:red">{{ longHoldNum }}份</div>
        </div>
        <div style="align-items: center;">
          <div>目前底仓(空)：</div>
          <div style="color:red">{{ shortHoldNum }}份</div>
        </div>
        <div v-show="currentTime">
          <div>创建时间：</div>
          <div style="color: green;">{{ currentTime }}</div>
        </div>
        <!--  -->
        <div>
          <div>状态 ：</div>
          <div>
            <el-radio-group v-model="stopStatus">
              <el-radio :label="'RUN'">开启</el-radio>
              <el-radio :label="'STOP'">暂停</el-radio>
            </el-radio-group>
          </div>
        </div>
        <!-- 平仓最高限制（主力-自动当日盈亏）： autoOpenProfitLimit -->
        <div>
          <div>
            <span class="red">*</span>
            平仓最高限制（主力-自动当日盈亏）：
          </div>
          <div class="inp">
            <el-input
              v-model="autoCloseProfitLimit"
              placeholder="请输入平仓最高限制（主力-自动当日盈亏）"
            ></el-input>
          </div>
        </div>
        <!-- 底仓急停线（多）： autoOpenProfitLimit -->
        <div>
          <div>
            <span class="red">*</span>
            底仓急停线(多)：
          </div>
          <div class="inp">
            <el-input
              v-model="autoCloseBottomLongLimit"
              placeholder="请输入底仓急停线(多)"
            ></el-input>
          </div>
        </div>
        <!-- 底仓急停线（空）： autoOpenProfitLimit -->
        <div>
          <div>
            <span class="red">*</span>
            底仓急停线（空）：
          </div>
          <div class="inp">
            <el-input
              v-model="autoCloseBottomShortLimit"
              placeholder="请输入底仓急停线（空）"
            ></el-input>
          </div>
        </div>
      </div>
      <!-- ------------------------------------------------------------------------------------------- -->
      <div
        class="list"
        v-for="(item,index) in list"
        :key="index"
      >
        <div class="tit">
          <div>阶段{{index+1}}</div>
          <div></div>
        </div>
        <!--  -->
        <div>
          <div>状态 ：</div>
          <div>
            <el-radio-group v-model="item.radio">
              <el-radio :label="'RUN'">开启</el-radio>
              <el-radio :label="'STOP'">暂停</el-radio>
            </el-radio-group>
          </div>
        </div>
        <!-- 幅度范围(相比市价)-->
        <div>
          <div>
            幅度范围(相比市价)：
          </div>
          <div class="inpFlex">
            <div>正负 0.01 *（ 0.01 * （ </div>
            <div>
              <el-input v-model="item.amplitudeStart"></el-input>-【
            </div>
            <div>
              <el-input v-model="item.amplitudeEnd"></el-input>
            </div>
            <div>】)
            </div>
          </div>
        </div>
        <!-- 每次平仓份数-->
        <div>
          <div>
            每次平仓份数:
          </div>
          <div class="inpFlex">
            <div>【</div>
            <div><el-input
                v-model="item.registration"
                placeholder="请输入每次平仓份数"
              ></el-input></div>
            <div>】份 </div>
          </div>
        </div>
        <!-- 平仓频率：-->
        <div>
          <div v-if="item.id==1">
            平仓频率:
          </div>
          <div v-else>
            调整频率:
          </div>
          <div class="inpFlex">
            <div>【</div>
            <div><el-input
                v-model="item.rateStart"
                placeholder="请输入平仓频率"
              ></el-input> </div>
            <div>】-【</div>
            <div> <el-input
                v-model="item.rateEnd"
                placeholder="请输入平仓频率"
              ></el-input></div>
            <div>】s/次 </div>
          </div>
        </div>

        <!-- tips -->
        <div class="tips">
          <div> </div>
          <div v-show="item.rateStart&&item.registration">
            24h最多可能收回<span>{{ 24 * 3600 / item.rateStart * item.registration}}</span>
            市价元
          </div>
        </div>
      </div>

      <div class="btns">
        <div></div>
        <div>
          <el-button
            type="danger"
            @click="delet"
          >取消</el-button>
          <el-button
            type="success"
            @click="createAndUpdateAutoTradeDuty"
          >确定</el-button>
          <el-button
            type="primary"
            icon="el-icon-plus"
            circle
            @click="add"
          ></el-button>
        </div>
      </div>
    </div>
  </d2-container>

</template>

<script>
export default {
  name: "openClosehouse",
  data () {
    return {
      currentTime: '', // 创建时间
      radio: 0,
      autoCloseProfitLimit: '', //   平仓最高限制（主力-自动当日盈亏）
      autoCloseBottomShortLimit: '', // 底仓急停线（空）
      autoCloseBottomLongLimit: '', // 底仓急停线（多）
      stopStatus: '', // 状态
      list: [{
        radio: '', // 状态
        amplitudeStart: '0', // 幅度
        amplitudeEnd: '',
        registration: '', // 每分位挂单份数
        rateStart: '', // 平仓频率（开始）
        rateEnd: '', // 平仓频率（结束）
        radioPla: '请选择阶段1状态', // 状态
        registrationPla: '请输入阶段1每分位挂单份数', // 每分位挂单份数
        rateStartPla: '请输入阶段1平仓频率(开始)', // 调整频率
        rateEndPla: '请输入阶段1平仓频率(结束)', // 调整频率
        amplitudeEndPla: '请输入阶段1幅度范围(相比市价)',
        amplitudeStartPla: '请输入阶段1幅度范围(相比市价)'

      }],
      //平仓、底仓
      curDayCloseMoney:'',
      curDayAutoCloseMoney:'',
      curDayHandCloseMoney:'',
      curDayFluctuationCloseMoney:'',
      longHoldNum:'',
      shortHoldNum:'',
      length: null,
      addNum: 0
    }
  },
  mounted () {
    this.tradeDuty()
  },
  methods: {
    async tradeDuty () {
      const res = await this.$api.searchAutoTradeDuty({ dutyType: 'AUTO_CLOSE' })
      if (res.status.code === 0) {
        if (res.result.autoCloseDTOList.length > 0) {
          this.list = []
          this.currentTime = res.currentTime

          this.curDayCloseMoney=res.result.curDayCloseMoney
          this.curDayAutoCloseMoney=res.result.curDayAutoCloseMoney
          this.curDayHandCloseMoney=res.result.curDayHandCloseMoney
          this.curDayFluctuationCloseMoney=res.result.curDayFluctuationCloseMoney

          this.longHoldNum=res.result.longHoldNum
          this.shortHoldNum=res.result.shortHoldNum
        }
        // console.log(res, '查询自动开平任务')

        this.stopStatus = res.result.stopStatus
        this.autoCloseProfitLimit = res.result.autoCloseProfitLimit
        this.autoCloseBottomLongLimit = res.result.autoCloseBottomLongLimit
        this.autoCloseBottomShortLimit = res.result.autoCloseBottomShortLimit
        res.result.autoCloseDTOList.forEach((item, index) => {
          this.list.push({
            id: item.id,
            radio: item.stopStatus,
            rateStart: item.rateStart, // 平仓频率（开始）
            rateEnd: item.rateEnd, // 平仓频率（结束）
            registration: item.quantity,
            amplitudeStart: item.priceStart, // 幅度
            amplitudeEnd: item.priceEnd,
            radioPla: '请选择阶段1状态', // 状态
            amplitudeEndPla: '请输入阶段1幅度范围(相比市价)',
            amplitudeStartPla: '请输入阶段1幅度范围(相比市价)',
            rateStartPla: `请输入阶段${item.id}平仓频率(开始)`, // 调整频率
            rateEndPla: `请输入阶段${item.id}平仓频率(结束)` // 调整频率
          })
        })
        this.length = this.list.length
      }
    },
    async createAndUpdateAutoTradeDuty () {
      if (!this.stopStatus) {
        return this.$message({
          message: '请选择开仓状态',
          type: 'warning'
        })
      }
      if (!this.autoCloseProfitLimit) {
        return this.$message({
          message: '请输入平仓最高限制（主力-自动当日盈亏）',
          type: 'warning'
        })
      }
      if (!this.autoCloseBottomLongLimit) {
        return this.$message({
          message: '请输入底仓急停线（多）',
          type: 'warning'
        })
      }
      if (!this.autoCloseBottomShortLimit) {
        return this.$message({
          message: '请输入底仓急停线（空）',
          type: 'warning'
        })
      }

      const autoCloseDTOList = []
      this.list.forEach((item, index) => {
        autoCloseDTOList.push({
          id: item.id,
          priceStart: item.amplitudeStart,
          priceEnd: item.amplitudeEnd,
          quantity: item.registration,
          rateStart: item.rateStart,
          rateEnd: item.rateEnd,
          stopStatus: item.radio || 'STOP'
        })
      })
      console.log(autoCloseDTOList, 'autoCloseDTOList')
      const res = await this.$api.createAndUpdateAutoTradeDuty({
        dutyType: 'AUTO_CLOSE',
        autoCloseBottomShortLimit: this.autoCloseBottomShortLimit, // 底仓急停线（空）
        autoCloseBottomLongLimit: this.autoCloseBottomLongLimit, // 底仓急停线（多）
        extra: JSON.stringify(autoCloseDTOList),
        stopStatus: this.stopStatus,
        autoCloseProfitLimit: this.autoCloseProfitLimit
      })
      if (res.status.code === 0) {
        this.$message({
          message: '创建成功',
          type: 'success'
        })
        this.tradeDuty()
      }
      console.log(res, '自动开平任务创建和更新')
    },
    delet () { // 取消 删除阶段
      if (this.addNum === 0) {
        this.list = this.list.splice(0, this.length)
      } else {
        this.addNum--
        this.list = this.list.splice(0, this.length + this.addNum)
      }
    },
    add () { // 添加新阶段
      if (this.list.length >= 10) {
        return this.$message({
          type: 'info',
          message: '最多可存在10个阶段'
        })
      }
      if (this.check(this.list.length - 1) === 1) {
        return
      }
      this.addNum++
      this.list.push({
        radio: 'STOP', // 状态
        amplitudeEnd: '',
        amplitudeStart: this.list[this.list.length - 1].amplitudeEnd,
        registration: '', // 每分位挂单份数
        rateStart: '', // 平仓频率（开始）
        rateEnd: '', // 平仓频率（结束）
        radioPla: `请选择阶段${this.list.length + 1}状态`, // 状态
        amplitudeEndPla: `请输入阶段${this.list.length + 1}幅度范围(相比市价)`,
        amplitudeStartPla: `请输入阶段${this.list.length + 1}幅度范围(相比市价)`,
        registrationPla: `请输入阶段${this.list.length + 1}每分位挂单份数`, // 每分位挂单份数
        rateStartPla: `请输入阶段${this.list.length + 1}平仓频率（开始）`, // 平仓频率（开始）
        rateEndPla: `请输入阶段${this.list.length + 1}平仓频率（结束）` // 平仓频率（结束）
      })
    },
    check (index) { // 校验是否完成输入
      for (const key in this.list[index]) {
        if (this.list[index][key].length <= 0 && this.list[index][key + 'Pla']) {
          this.$message({
            message: this.list[index][key + 'Pla'],
            type: 'warning'
          })
          return 1
        }
      }
    }

  }

}
</script>

  <style lang="scss" scoped>
.openWarehouse {
  padding: 50px 0 0 0;
  box-sizing: border-box;
  .head,
  .list {
    width: 80%;
    > div {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      > div:nth-child(1) {
        width: 30%;
        font-size: 14px;
        color: #606266;
        margin-right: 30px;
        text-align: right;
      }
    }
  }

  .list {
    > div:nth-child(1) {
      > div:nth-child(1) {
        width: 50%;
        font-size: 20px;
        font-weight: 700;
        color: #000;
        text-align: center;
        padding-right: 50px;
        box-sizing: border-box;
      }
    }
    .inpFlex {
      display: flex;
      align-items: center;
      .el-input {
        width: 100px;
        height: 30;
      }
      > div {
        display: flex;
        align-items: center;
        margin-right: 10px;
      }
    }
    .tips {
      font-size: 14px;
      color: red;
      span {
        color: green;
      }
    }
  }
}
// 按钮
.btns {
  width: 80%;
  display: flex;
  align-items: center;
  > div:nth-child(1) {
    width: 30%;
  }
  > div:nth-child(2) {
    display: flex;
    align-items: center;
  }
}
.inp {
  width: 500px;
}

.red {
  color: red;
}
.el-radio-group {
  line-height: 0;
}
.el-input__inner {
  height: 30px;
}
</style>
