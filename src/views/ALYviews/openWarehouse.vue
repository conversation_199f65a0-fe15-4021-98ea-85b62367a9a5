<template>
  <d2-container class="page">
    <div class="openWarehouse">

      <div class="head">
        <!-- <div  style="align-items: center;">
          <div>今日自动开仓花费：</div>
          <div style="color: red;">{{ curDayAutoOpenMoney }}</div><span>元</span>
          <div style="color: red;margin-left:30px;">(统计时间为当日00:00至目前)</div>
        </div> -->
        <div style="align-items: center;">
          <div>今日开仓金额：</div>
          <div style="color: red;">{{ curDayOpenMoney }}</div><span>元</span>
          <div style="color: red;margin-left:30px;">(统计时间为当日00:00至目前)</div>
        </div>
        <div style="align-items: center;">
          <div>今日自动开仓金额：</div>
          <div style="color: red;">{{ curDayAutoOpenMoney }}</div><span>元</span>
          <div style="color: red;margin-left:30px;">(统计时间为当日00:00至目前)</div>
        </div>
        <div style="align-items: center;">
          <div>今日手动平仓金额：</div>
          <div style="color: red;">{{ curDayHandOpenMoney }}</div><span>元</span>
          <div style="color: red;margin-left:30px;">(统计时间为当日00:00至目前)</div>
        </div>
        <div v-show="currentTime">
          <div>创建时间：</div>
          <div style="color: green;">{{ currentTime }}</div>
        </div>
        <!--  -->
        <div>
          <div>状态 ：</div>
          <div>
            <el-radio-group v-model="stopStatus">
              <el-radio :label="'RUN'">开启</el-radio>
              <el-radio :label="'STOP'">暂停</el-radio>
            </el-radio-group>
          </div>
        </div>
        <!-- 开仓总预算（主力-自动当日盈亏）： autoOpenProfitLimit -->
        <div>
          <div>
            <span class="red">*</span>
            开仓总预算（主力-自动当日盈亏）：
          </div>
          <div class="inp">
            <el-input v-model="autoOpenProfitLimit" placeholder="请输入开仓总预算（主力-自动当日盈亏）"></el-input>
          </div>
        </div>
        <div>
          <div>是否开启自动急停 ：</div>
          <div>
            <el-radio-group v-model="autoOpenImmediateStopStatus">
              <el-radio :label="'RUN'">开启</el-radio>
              <el-radio :label="'STOP'">关闭</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <!-- ------------------------------------------------------------------------------------------- -->
      <div class="list" v-for="(item,index) in list" :key="item.id">
        <div class="tit">
          <div>阶段{{index+1}}</div>
          <div></div>
        </div>
        <!--  -->
        <div>
          <div>状态 ：</div>
          <div>
            <el-radio-group v-model="item.radio">
              <el-radio :label="'RUN'">开启</el-radio>
              <el-radio :label="'STOP'">暂停</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div>
          <div>
            档位：
          </div>
          <div class="inpFlex">
            <div><el-input type="number" v-model="item.lever" placeholder="请输入档位"></el-input></div>
          </div>
        </div>
        <!-- 幅度范围(相比市价)-->
        <div>
          <div>
            幅度范围(相比市价)：
          </div>
          <div class="inpFlex">
            <div>正负 0.01 *（ 0.01 * （ </div>
            <div>
              <el-input v-model="item.amplitudeStart"></el-input>-【
            </div>
            <div>
              <el-input v-model="item.amplitudeEnd"></el-input>
            </div>
            <div>】)
            </div>
          </div>
        </div>
        <!-- 每分位挂单份数：-->

        <div class="flex_view">
          <div> </div>
          <div class="centenr">
              <div class="li" v-for="(itemm,indexx) in item.rateList">
                <div class="right_del" @click="delRate(item,indexx)">
                  <img src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240716/ea4a0d90e685dbcc37aa2be076e4882a_64x64.png" alt="" srcset="" />
                </div>
                <div>
                  <div class="title">
                    {{indexx+1}}.每分位挂单份数：
                  </div>
                  <div class="inpFlex">
                    <div>【</div>
                    <div><el-input v-model="itemm.quantity" placeholder="请输入每分位挂单份数："></el-input></div>
                    <div>】份</div>
                  </div>
                </div>
                <!-- 调整频率：-->
                <div>
                  <div class="title">
                   {{indexx+1}}.调整频率：
                  </div>
                  <div class="inpFlex">
                    <div>【</div>
                    <div><el-input v-model="itemm.rate" placeholder="请输入调整频率"></el-input></div>
                    <div>】s/次</div>
                  </div>
                </div>
              </div>
              <div class="li button">
                <el-button type="primary" icon="el-icon-plus" circle @click="addRate(item)"></el-button>
              </div>
          </div>
        </div>
        <!-- tips -->
        <!-- <div class="tips">
          <div> </div>
          <div>本阶段挂单量为<span>
              {{ (item.amplitudeEnd - item.amplitudeStart) * 2 * item.quantity}}</span>
          </div>
        </div> -->
        <!-- tips -->
        <div class="tips">
          <div> </div>
          <div v-show="item.rate&&item.amplitudeEnd&&item.quantity">
            本阶段可能在<span>{{item.rate||0}}</span>
            秒内花费<span>{{(item.amplitudeEnd - item.amplitudeStart) *  item.quantity }}</span>
            市价元
          </div>
        </div>
      </div>

      <div class="btns">
        <div></div>
        <div>
          <el-button type="danger" @click="delet">取消</el-button>
          <el-button type="success" @click="createAndUpdateAutoTradeDuty">确定</el-button>
          <el-button type="primary" icon="el-icon-plus" circle @click="add"></el-button>
        </div>
      </div>
    </div>
  </d2-container>

</template>

<script>
  export default {
    name: "openWarehouse",
    data() {
      return {
        curDayAutoOpenMoney: '', //今日字段开仓花费
        curDayHandOpenMoney: '',
        curDayOpenMoney: '',
        currentTime: '', // 创建时间
        radio: 0,
        autoOpenProfitLimit: '', // 开仓总预算（主力-自动当日盈亏）
        stopStatus: '', // 状态
        list: [{
          radio: '', // 状态
          amplitudeStart: 0, // 幅度范围(相比市价)
          amplitudeEnd: '',
          radioPla: '请选择阶段1状态', // 状态
          amplitudeEndPla: '请输入阶段1幅度范围(相比市价)',
          rateList: [{
            quantity: '', // 每分位挂单份数
            rate: '', // 调整频率
          }],
          quantityPla: '请输入阶段1每分位挂单份数', // 每分位挂单份数
          ratePla: '请输入阶段1调整频率', // 调整频率
          lever: ''
        }],
        length: null,
        addNum: 0,
        autoOpenImmediateStopStatus: 'RUN'
      }
    },
    mounted() {
      this.tradeDuty()
    },
    methods: {
      async tradeDuty() {
        const res = await this.$api.searchAutoTradeDuty({
          dutyType: 'AUTO_OPEN'
        })
        if (res.status.code === 0) {
          if (res.result.autoOpenDTOList.length > 0) {
            this.list = []
            this.currentTime = res.currentTime
            this.curDayAutoOpenMoney = res.result.curDayAutoOpenMoney
            this.curDayOpenMoney = res.result.curDayOpenMoney
            this.curDayHandOpenMoney = res.result.curDayHandOpenMoney
          }
          this.stopStatus = res.result.stopStatus
          this.autoOpenProfitLimit = res.result.autoOpenProfitLimit
          res.result.autoOpenDTOList.forEach((item, index) => {
            this.list.push({
              id: item.id,
              radio: item.stopStatus,
              amplitudeEnd: item.priceEnd,
              amplitudeStart: item.priceStart,
              radioPla: '请选择阶段1状态', // 状态
              amplitudeEndPla: '请输入阶段1幅度范围(相比市价)',
              quantityPla: '请输入阶段1每分位挂单份数', // 每分位挂单份数
              ratePla: '请输入阶段1调整频率' ,// 调整频率
              rateList:item.rateList?item.rateList:[],
              lever:item.lever
            })
          })
          this.length = this.list.length
        }
        console.log(res, '查询自动开平任务')
      },
      async createAndUpdateAutoTradeDuty() {
        if (!this.stopStatus) {
          return this.$message({
            message: '请选择开仓状态',
            type: 'warning'
          })
        }
        if (!this.autoOpenProfitLimit) {
          return this.$message({
            message: '请输入开仓总预算',
            type: 'warning'
          })
        }
        const autoOpenDTOList = []
        this.list.forEach((item, index) => {
          autoOpenDTOList.push({
            id: item.id,
            priceStart: item.amplitudeStart,
            priceEnd: item.amplitudeEnd,
            stopStatus: item.radio,
            rateList:item.rateList,
            lever:item.lever
          })
        })
        console.log(autoOpenDTOList, 'autoOpenDTOList')
        const res = await this.$api.createAndUpdateAutoTradeDuty({
          dutyType: 'AUTO_OPEN',
          extra: JSON.stringify(autoOpenDTOList),
          stopStatus: this.stopStatus,
          autoOpenProfitLimit: this.autoOpenProfitLimit,
          autoOpenImmediateStopStatus:this.autoOpenImmediateStopStatus
        })
        if (res.status.code === 0) {
          this.$message({
            message: '创建成功',
            type: 'success'
          })
          this.tradeDuty()
        }
        console.log(res, '自动开平任务创建和更新')
      },
      delet() { // 取消 删除阶段
        if (this.addNum === 0) {
          this.list = this.list.splice(0, this.length)
        } else {
          this.addNum--
          this.list = this.list.splice(0, this.length + this.addNum)
        }
      },
      add() { // 添加新阶段
        if (this.list.length >= 10) {
          return this.$message({
            type: 'info',
            message: '最多可存在10个阶段'
          })
        }
        if (this.check(this.list.length - 1) === 1) {
          return
        }
        this.addNum++
        this.list.push({
          // id: this.list.length + 1,
          radio: 'STOP', // 状态
          amplitudeStart: this.list[this.list.length - 1].amplitudeEnd, // 幅度范围(相比市价) 最小范围
          amplitudeEnd: '', // 幅度范围(相比市价) 最大范围
          radioPla: `请选择阶段${this.list.length + 1}状态`, // 状态
          amplitudeEndPla: `请输入阶段${this.list.length + 1}幅度范围(相比市价)`,
          quantityPla: `请输入阶段${this.list.length + 1}每分位挂单份数`, // 每分位挂单份数
          ratePla: `请输入阶段${this.list.length + 1}调整频率`, // 调整频率
          lever:'',
          rateList:[]
        })
      },
      addRate(item) { // 添加阶段
       item.rateList.push({
          quantity: '', // 每分位挂单份数
          rate: '', // 调整频率
          quantityPla: `请输入阶段${this.list.length + 1}每分位挂单份数`, // 每分位挂单份数
          ratePla: `请输入阶段${this.list.length + 1}调整频率` // 调整频率
        })
      },
      delRate(item,index) { // 添加阶段
      console.log(index)
        item.rateList.splice(index,1)
      },
      check(index) { // 校验是否完成输入
        for (const key in this.list[index]) {
          if (this.list[index][key].length <= 0 && this.list[index][key + 'Pla']) {
            this.$message({
              message: this.list[index][key + 'Pla'],
              type: 'warning'
            })
            return 1
          }
          // else if (this.list[index].amplitudeEnd - this.list[index].amplitudeStart <= 0) {
          //   this.$message({
          //     message: '幅度范围不能小于0',
          //     type: 'warning'
          //   })
          //   return 1
          // }
        }
      }

    }

  }
</script>

<style lang="scss" scoped>
  .openWarehouse {
    padding: 50px 0 0 0;
    box-sizing: border-box;

    .head,
    .list {
      width: 80%;

      >div {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        >div:nth-child(1) {
          width: 30%;
          font-size: 14px;
          color: #606266;
          margin-right: 30px;
          text-align: right;
        }
      }
    }

    .list {
      >div:nth-child(1) {
        >div:nth-child(1) {
          width: 50%;
          font-size: 20px;
          font-weight: 700;
          color: #000;
          text-align: center;
          padding-right: 50px;
          box-sizing: border-box;
        }
      }

      .inpFlex {
        display: flex;
        align-items: center;

        .el-input {
          width: 100px;
          height: 30;
        }

        >div {
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
      }

      .tips {
        font-size: 14px;
        color: red;

        span {
          color: green;
        }
      }
    }
  }

  // 按钮
  .btns {
    width: 80%;
    display: flex;
    align-items: center;

    >div:nth-child(1) {
      width: 30%;
    }

    >div:nth-child(2) {
      display: flex;
      align-items: center;
    }
  }

  .inp {
    width: 500px;
  }

  .red {
    color: red;
  }

  .el-radio-group {
    line-height: 0;
  }

  .el-input__inner {
    height: 30px;
  }

  .flex_view {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    .centenr{
      width:1200px;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
    .li {
      border: 1px solid #ccc;
      width: 230px;
      height: 180px;
      margin-right: 10px;
      margin-bottom:10px;
      padding: 10px;
      border-radius: 4px;
      position: relative;
      .right_del{
        position: absolute;
        right:6px;
        top:6px;
        cursor: pointer;
        img{
          width:30px;
          height:30px;
        }
      }
      .title {
        margin: 10px 0px;
      }

      &.button {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
</style>
