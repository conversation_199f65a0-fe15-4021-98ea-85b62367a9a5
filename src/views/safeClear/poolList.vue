<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="订单号">
        <el-input
          v-model="formInline.orderId"
          placeholder="请输入订单号"
          clearable
          style="width: 220px"
          maxlength="25"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="用户id">
        <el-input
          v-model="formInline.uid"
          placeholder="请输入用户id"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="需求状态">
        <el-select  v-model="formInline.status" clearable placeholder="需求状态">
          <el-option label="待处理" value="0"></el-option>
          <el-option label="已发送消息但还未执行" value="1"></el-option>
          <el-option label="已处理" value="2"></el-option>
          <el-option label="处理中" value="3"></el-option>
          <el-option label="子账户错误" value="4"></el-option>
          <el-option label="转账异常" value="5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="debounceMethods(getList, 1)"
          >查询</el-button
        >
        <el-button type="primary" @click="clear(1)">清除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
      v-loading="loading"
      size="mini"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="uid"
        label="用户id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="realName"
        label="用户名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tranAmount"
        label="交易金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="balance"
        label="余额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orderId"
        label="订单号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctNo"
        label="收款人账户"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctName"
        label="收款人账户户名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctBankNode"
        label="收款人开户行行号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctBankName"
        label="收款人开户行名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="type" label="交易类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.type == '1'">清分</el-tag>
          <el-tag v-if="scope.row.type == '2'">转账</el-tag>
          <el-tag v-if="scope.row.type == '3'">余额支付</el-tag>
          <el-tag v-if="scope.row.type == '4'">提现</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="status" label="需求状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == '1'">已发送消息但还未执行</el-tag>
          <el-tag v-if="scope.row.status == '2'">已处理</el-tag>
          <el-tag v-if="scope.row.status == '3'">处理中</el-tag>
          <el-tag type="danger" v-if="scope.row.status == '4'"
            >子账户错误</el-tag
          >
          <el-tag type="danger" v-if="scope.row.status == '5'">转账异常</el-tag>
          <el-tag type="danger" v-if="scope.row.status == '0'">待处理</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="changeType" label="出入账" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.changeType == '1'">出账</el-tag>
          <el-tag v-if="scope.row.changeType == '2'">入账</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="sameAddress" label="是否同城" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.sameAddress == '1'">异地</el-tag>
          <el-tag v-if="scope.row.sameAddress == '0'">同城</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateAt"
        label="更新时间"
        align="center"
      ></el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'poolList',
  data () {
    return {
      loading: false,
      formInline: {
        orderId: '',
        uid: '',
        status: '',
        createAt: null,
        startTime: '',
        endTime: ''
      },
      total: 1,
      tableData: []
    }
  },
  mounted () {
    this.getList(1)
  },
  methods: {
    // 查询列表
    async getList (page) {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
      const res = await this.$api.poolList({
        pageNum: page,
        pageSize: 15,
        uid: this.formInline.uid,
        orderId: this.formInline.orderId,
        status: this.formInline.status,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime
      })
      this.loading = false
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    clear () {
      this.formInline.uid = ''
      this.formInline.orderId = ''
      this.formInline.status = ''
      this.formInline.createAt = undefined
    }
  }
}
</script>

<style></style>
