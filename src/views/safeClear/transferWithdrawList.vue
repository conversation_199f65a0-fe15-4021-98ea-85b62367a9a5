<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="订单号">
        <el-input
          v-model="formInline.orderId"
          placeholder="请输入订单号"
          clearable
          style="width: 220px"
          maxlength="25"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="用户id">
        <el-input
          v-model="formInline.uid"
          placeholder="请输入用户id"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="提现状态">
        <el-select  v-model="formInline.status" clearable placeholder="提现状态">
          <el-option label="待处理" value="0"></el-option>
          <el-option label="交易中" value="10"></el-option>
          <el-option label="交易成功" value="20"></el-option>
          <el-option label="失败" value="30"></el-option>
          <el-option label="异常" value="99"></el-option>
          <el-option label="已滞留" value="40"></el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="debounceMethods(getList, 1)"
          >查询</el-button
        >
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button
          type="primary"
          @click="retry()"
          :disabled="!multipleSelection.length"
          >批量重新提现</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"


      v-loading="loading"
      size="mini"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
        :selectable="selectable"
      ></el-table-column>
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="uid"
        label="用户id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="thirdVoucher"
        label="转账凭证号"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="orderId"
        label="订单号"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="frontLogNo"
        label="银行业务流水号"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="ccyCode"
        label="货币类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="outAcctName"
        label="付款人账户名称"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="outAcctNo"
        label="付款人账户"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctNo"
        label="收款人账户"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctBankNode"
        label="收款人开户行行号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctBankName"
        label="收款人开户行名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inAcctName"
        label="收款人账户户名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tranAmount"
        label="交易金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="recordDesc"
        label="提现描述"
        align="center"
        width="300"
      ></el-table-column>
      <el-table-column
        prop="feelOne"
        label="手续费"
        align="center"
      ></el-table-column>
      <el-table-column prop="unionFlag" label="行内跨行标志" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.unionFlag == '1'">行内转账</el-tag>
          <el-tag v-if="scope.row.unionFlag == '0'">跨行转账</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="sameAddress" label="是否同城" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.sameAddress == '0'">同城</el-tag>
          <el-tag v-if="scope.row.sameAddress == '1'">异地</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="status" label="提现状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == '0'">待处理</el-tag>
          <el-tag v-if="scope.row.status == '10'">交易中</el-tag>
          <el-tag v-if="scope.row.status == '20'">交易成功</el-tag>
          <el-tag v-if="scope.row.status == '40'">已滞留</el-tag>
          <el-tag type="danger" v-if="scope.row.status == '30'">失败</el-tag>
          <el-tag type="danger" v-if="scope.row.status == '99'">异常</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="withdrawalAt"
        label="提现时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateAt"
        label="更新时间"
        align="center"
      ></el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
       :page-size="pageSize" :current-page="page" :page-sizes="[20, 50, 100, 200,500,1000]"
      	style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'transferWithdrawList',
  data () {
    return {
      loading: false,
      formInline: {
        orderId: '',
        uid: '',
        status: '',
        createAt: null,
        startTime: '',
        endTime: ''
      },
      total: 1,
      tableData: [],
      multipleSelection: [],
      idListStr: [],
      pageSize:20,
      page:1
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    selectable (row, index) {
      return row.status !== 40&&row.status !== 20  // 通过某个值来进行判断，规定那一行的选择框被禁用
    },
    // 批量选择
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    // 查询列表
    async getList () {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
      const res = await this.$api.transferWithdrawList({
        pageNum: this.page,
        pageSize: this.pageSize,
        uid: this.formInline.uid,
        orderId: this.formInline.orderId,
        status: this.formInline.status,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime
      })
      this.loading = false
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.page=val
      this.getList()
    },
    xuanzeSize(val) {
       this.page=1
      this.pageSize=val
      this.getList()
    },
    clear () {
      this.formInline.uid = ''
      this.formInline.orderId = ''
      this.formInline.status = ''
      this.formInline.createAt = undefined
    },
    // 重新提现
    retry () {
      console.log(this.multipleSelection)
      this.$confirm('此操作将重新提现, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.retrySubmit()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async retrySubmit () {
      this.multipleSelection.forEach((item) => {
        this.idListStr.push(item.orderId)
      })
      await this.$api.withdrawalRetry({
        orderIdJson: JSON.stringify(this.idListStr)
      })
		this.idListStr=[]
      this.getList()
      this.$message.success('重新提现成功')
    }
  }
}
</script>

<style></style>
