<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="订单号">
        <el-input
          v-model="formInline.orderId"
          placeholder="请输入订单号"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="用户id">
        <el-input
          v-model="formInline.uid"
          placeholder="请输入用户id"
          clearable
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="转账状态">
        <el-select  v-model="formInline.status" clearable placeholder="转账状态">
          <el-option label="默认状态，提现使用" value="0"></el-option>
          <el-option label="默认状态，记账使用" value="1"></el-option>
          <el-option label="交易中" value="10"></el-option>
          <el-option label="交易成功" value="20"></el-option>
          <el-option label="失败" value="30"></el-option>
          <el-option label="异常" value="99"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="formInline.createAt"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="debounceMethods(getList, 1)"
          >查询</el-button
        >
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button
          type="primary"
          @click="retry()"
          :disabled="!multipleSelection.length"
          >批量重新转账</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"


      v-loading="loading"
      @selection-change="handleSelectionChange"
      size="mini"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
        :selectable="selectable"
      ></el-table-column>
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="uid"
        label="用户id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="thirdVoucher"
        label="交易流水号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="frontLogNo"
        label="银行流水好"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="cstInnerFlowNo"
        label="支付订单号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="mainAccount"
        label="智能账号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="outSubAccount"
        label="付款清分台账编码"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="outSubAccountName"
        label="付款清分台账编码别名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="outSubAccBalance"
        label="付款清分台账编码余额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="ccyCode"
        label="币种"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tranAmount"
        label="交易金额"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inSubAccNo"
        label="收款清分台账编码"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inSubAccName"
        label="收款清分台账编码别名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inSubAccBalance"
        label="收款清分台账编码余额"
        align="center"
      ></el-table-column>
      <el-table-column prop="type" label="交易类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.type === 1">清分</el-tag>
          <el-tag v-if="scope.row.type === 2">转账</el-tag>
          <el-tag v-if="scope.row.type === 3">余额支付</el-tag>
          <el-tag v-if="scope.row.type === 4">提现清分</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="status" label="转账状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == '0'">默认状态，提现使用</el-tag>
          <el-tag v-if="scope.row.status == '1'">默认状态，记账使用</el-tag>
          <el-tag v-if="scope.row.status == '10'">交易中</el-tag>
          <el-tag v-if="scope.row.status == '20'">交易成功</el-tag>
          <el-tag type="danger" v-if="scope.row.status == '30'">失败</el-tag>
          <el-tag type="danger" v-if="scope.row.status === '99'">异常</el-tag>
        </template></el-table-column
      >
      <el-table-column prop="changeType" label="出入账" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.changeType == '1'">出账</el-tag>
          <el-tag v-if="scope.row.changeType == '2'">入账</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="createAt"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="updateAt"
        label="更新时间"
        align="center"
      ></el-table-column>
      <!-- <el-table-column fixed="right" label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="balanceQuery(scope.row)"
            >余额查询</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'accountList',
  data () {
    return {
      loading: false,
      formInline: {
        orderId: '',
        uid: '',
        status: '',
        createAt: null,
        startTime: '',
        endTime: ''
      },
      total: 1,
      tableData: [],
      multipleSelection: [],
      idListStr: []
    }
  },
  mounted () {
    this.getList(1)
  },
  methods: {
    selectable (row, index) {
      return row.status !== 20 // 通过某个值来进行判断，规定那一行的选择框被禁用
    },
    // 批量选择
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    // 查询列表
    async getList (page) {
      this.loading = true
      if (this.formInline.createAt) {
        this.formInline.startTime = this.formInline.createAt[0]
        this.formInline.endTime = this.formInline.createAt[1]
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
      const res = await this.$api.transferInfoList({
        pageNum: page,
        pageSize: 15,
        uid: this.formInline.uid,
        orderId: this.formInline.orderId,
        status: this.formInline.status,
        startTime: this.formInline.startTime,
        endTime: this.formInline.endTime
      })
      this.loading = false
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    clear () {
      this.formInline.uid = ''
      this.formInline.orderId = ''
      this.formInline.status = ''
      this.formInline.createAt = undefined
    },
    // 重新转账
    retry () {
      console.log(this.multipleSelection)
      this.$confirm('此操作将重新转账, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.retrySubmit()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async retrySubmit () {
      this.multipleSelection.forEach((item) => {
        this.idListStr.push(item.id)
      })
      await this.$api.transferRetry({
        idJson: JSON.stringify(this.idListStr)
      })
      this.getList(1)
      this.$message.success('重新转账成功')
    }
  }
}
</script>

<style></style>
