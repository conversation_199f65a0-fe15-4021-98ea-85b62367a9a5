<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
			<el-form-item label="账户号">
				<el-input v-model="formInline.subAccount" placeholder="请输入账户号" clearable type="number"></el-input>
			</el-form-item>
			
			<el-form-item label="用户id">
				<el-input v-model=" formInline.uid" placeholder="请输入用户id" clearable type="text"></el-input>
			</el-form-item>
			<el-form-item label="创建时间">
				<el-date-picker v-model="formInline.createAt" type="datetimerange" range-separator="至"
					start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
				</el-date-picker>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="debounceMethods(getList, 1)">查询
				</el-button>
				<el-button type="primary" @click="clear(1)">清除</el-button>
				<el-button type="primary" @click="batchFlatBill()" :disabled="!multipleSelection.length">批量平账
				</el-button>
			</el-form-item>
			<el-form-item label="输入用户id(多个,号隔开)" style="width:500px;"> 
				<el-input v-model="uidListStr" placeholder="请输入用户id" :rows="1"  clearable type="textarea" style="width:320px;"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handFlatBill()">输入uid平账(开发者使用)
				</el-button>
			</el-form-item>
			
		</el-form>
		<el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
			 v-loading="loading" @selection-change="handleSelectionChange" size='mini'>
			<el-table-column fixed prop="id" label="id" type="selection" align="center"></el-table-column>
			<el-table-column fixed prop="id" label="id" align="center" width="80"></el-table-column>
			<el-table-column prop="uid" label="用户id" align="center"></el-table-column>
			<el-table-column prop="nickName" label="用户昵称" align="center"></el-table-column>
			<el-table-column prop="nftcnBalance" label="nftcn账户余额" align="center"></el-table-column>
			<el-table-column prop="accountName" label="用户子账户名称" align="center"></el-table-column>
			<el-table-column prop="accountNo" label="平安银行子账户" align="center"></el-table-column>
			<el-table-column prop="balance" label="子账户余额" align="center"></el-table-column>
			<el-table-column prop="balanceAvailable" label="子账户可提现余额" align="center"></el-table-column>
			<el-table-column prop="balanceFreeze" label="子账户冻结余额" align="center"></el-table-column>
			<el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
			<el-table-column prop="updateAt" label="更新时间" align="center"></el-table-column>
			<el-table-column prop="status" label="子账户状态" align="center">
				<template scope="scope">
					<el-tag type="danger" v-if="scope.row.status == '0'">删除</el-tag>
					<el-tag v-if="scope.row.status == '1'">正常</el-tag>
					<el-tag type="danger" v-if="scope.row.status == '2'">创建失败</el-tag>
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="170" align="center">
				<template slot-scope="scope">
					<el-button type="text"  @click="balanceQuery(scope.row)">余额查询
					</el-button>
					<el-button type="text"  @click="balanceModif(scope.row)">余额更正
					</el-button>
					<el-button type="text"  @click="flatBill(scope.row)">平账
					</el-button>
					<el-button type="text"  @click="changeBalanceDialog(scope.row)">修改平安账号余额
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
			</el-pagination>
		</div>
		<el-dialog title="余额" :visible.sync="isDialog" center>
			<el-form>
				<el-form-item label="账户余额:" :label-width="formLabelWidth">
					{{ balance }}
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="isDialog = false">关闭</el-button>
			</div>
		</el-dialog>
		<el-dialog title="余额更正" :visible.sync="updataDialog" center>
			<el-form>
				<el-form-item label="子账户余额:" :label-width="formLabelWidth">
					<el-input v-model="form.balance" placeholder="请输入子账户余额" clearable style="width: 80%" type="number">
					</el-input>
				</el-form-item>
				<el-form-item label="子账户可提现余额:" :label-width="formLabelWidth">
					<el-input v-model="form.balanceAvailable" placeholder="请输入子账户可提现余额" clearable style="width: 80%"
						type="number"></el-input>
				</el-form-item>
				<el-form-item label="子账户冻结余额:" :label-width="formLabelWidth">
					<el-input v-model="form.balanceFreeze" placeholder="请输入子账户冻结余额" clearable style="width: 80%"
						type="number"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="updataDialog = false">取 消</el-button>
				<el-button type="primary" @click="debounceMethods(updata_submit)">确 定
				</el-button>
			</div>
		</el-dialog>
		<!--    修改用户平安余额-->
		<el-dialog title="修改用户平安余额" :visible.sync="dialogVisible" width="30%">
			<div class="item">
				操作类型：
				<el-select  v-model="changeType" placeholder="请选择">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
					</el-option>
				</el-select>
			</div>

			<div class="item" style="margin-top: 30px">
				修改数值：
				<el-input v-model="changeValue" placeholder="请输入内容" style="width: 60%;"></el-input>
			</div>

			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="changeBalance">确 定</el-button>
			</span>
		</el-dialog>

	</d2-container>
</template>

<script>
	export default {
		name: 'safeAccountList',
		data() {
			return {
				changeValue: null,
				dialogVisible: false,
				changeType: '1',
				options: [{
					value: '1',
					label: '增加'
				}, {
					value: '2',
					label: '减少'
				}],
				uid: '',
				loading: false,
				formInline: {
					subAccount: '',
					uid: '',
					createAt: null,
					startTime: '',
					endTime: '',
					uids:""
				},
				total: 1,
				tableData: [],
				isDialog: false,
				balance: '',
				formLabelWidth: '130px',
				updataDialog: false,
				form: {
					balance: '',
					balanceAvailable: '',
					balanceFreeze: ''
				},
				subAccount: '',
				multipleSelection: [],
				idListStr: [],
				uidListStr:[]
			}
		},
		mounted() {
			this.getList(1)
		},
		methods: {
			changeBalanceDialog(row) {
				this.dialogVisible = true
				this.uid = row.uid
			},
			async changeBalance() {
				const obj = {
					uid: this.uid
				}
				if (this.changeType === '1') {
					obj.addAmount = this.changeValue
					obj.subAmount = 0
				} else {
					obj.subAmount = this.changeValue
					obj.addAmount = 0
				}
				await this.$api.changeBalance(obj)
				this.$message.success('修改成功')
				this.dialogVisible = false
				await this.getList(1)
			},
			// 批量选择
			handleSelectionChange(val) {
				this.multipleSelection = val
			},
			// 查询列表
			async getList(page) {
				this.loading = true
				if (this.formInline.createAt) {
					this.formInline.startTime = this.formInline.createAt[0]
					this.formInline.endTime = this.formInline.createAt[1]
				} else {
					this.formInline.startTime = ''
					this.formInline.endTime = ''
				}
				const res = await this.$api.safeAccountList({
					pageNum: page,
					pageSize: 15,
					uid: this.formInline.uid,
					subAccount: this.formInline.subAccount,
					startTime: this.formInline.startTime,
					endTime: this.formInline.endTime
				})
				this.loading = false
				this.tableData = res.result.list
				this.total = res.result.totalCount
			},
			xuanze(val) {
				this.getList(val)
			},
			clear() {
				this.formInline.uid = ''
				this.formInline.subAccount = ''
				this.formInline.createAt = undefined
			},
			// 平账
			flatBill(val) {
				this.$confirm('此操作将进行平账, 是否继续?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					.then(() => {
						this.flatBillSubmit(val.uid)
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消'
						})
					})
			},
			async flatBillSubmit(val) {
				await this.$api.autoCharge({
					uid: val
				})
				this.getList(1)
				this.$message.success('平账成功')
			},
			// 批量平账
			batchFlatBill() {
				this.$confirm('此操作将进行批量平账, 是否继续?', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					.then(() => {
						this.batchFlatBillSubmit()
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消'
						})
					})
			},
			async batchFlatBillSubmit() {
        this.idListStr=[]
				this.multipleSelection.forEach((item) => {
					this.idListStr.push(item.uid)
				})
				console.log(this.idListStr)
				await this.$api.batchAutoCharge({
					uidList: JSON.stringify(this.idListStr)
				})
				this.getList(1)
				this.$message.success('批量平账成功')
			},
			
			// 手动输入平账
			handFlatBill() {
				this.$confirm(`此操作将对${this.uidListStr}进行批量平账, 是否继续?`, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					.then(() => {
						this.handFlatBillSubmit()
					})
					.catch(() => {
						this.$message({
							type: 'info',
							message: '已取消'
						})
					})
			},
			async handFlatBillSubmit() {
				let str=[]
				this.uidListStr.split(",").forEach((item)=>{
					str.push(Number(item))
				})
				console.error(str)
				await this.$api.batchAutoCharge({
					uidList: JSON.stringify(str)
				})
				this.getList(1)
				this.$message.success('手动批量平账成功')
			},
			// 余额查询
			async balanceQuery(val) {
				const res = await this.$api.pinanBalance({
					subAccount: val.accountNo,
					uid: val.uid
				})
				this.balance = res.result.balance
				this.isDialog = true
			},
			// 余额更正
			balanceModif(val) {
				this.updataDialog = true
				this.form.balance = val.balance
				this.form.balanceAvailable = val.balanceAvailable
				this.form.balanceFreeze = val.balanceFreeze
				this.subAccount = val.accountNo
			},
			async updata_submit() {
				await this.$api.updateBalance({
					balance: this.form.balance,
					balanceAvailable: this.form.balanceAvailable,
					balanceFreeze: this.form.balanceFreeze,
					subAccount: this.subAccount
				})
				this.getList(1)
				this.updataDialog = false
				this.$message.success('修改成功')
			}
		}
	}
</script>

<style>
</style>
