<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="openDialog(1, '')">+ 新建</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        fixed
        prop="gameName"
        label="游戏空间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="time"
        label="时间段"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="planCountLimit"
        label="预约次数"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="playDuration"
        label="单次时长"
        align="center"
      ></el-table-column>
      <el-table-column prop="isActive" label="状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isActive" type="success">已启用</el-tag>
          <el-tag v-else type="danger">未启用</el-tag>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="removeOpen(scope.row)"
            >更改</el-button
          >
          <el-button type="text"  @click="remove(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="新增游戏空间" :visible.sync="isaddDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-select  v-model="form.id" placeholder="请选择">
            <el-option
              v-for="item in form.name"
              :key="item.id"
              :label="item.gameName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" :label-width="formLabelWidth">
          <el-date-picker
            v-model="form.trialStartTime"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" :label-width="formLabelWidth">
          <el-date-picker
            v-model="form.trialEndTime"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约次数" :label-width="formLabelWidth">
          <el-input v-model="form.planCountLimit"></el-input>
        </el-form-item>
        <el-form-item label="时长" :label-width="formLabelWidth">
          <el-input v-model="form.playDuration"></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-switch
            v-model="form.isActive"
            active-color="#13ce66"
            inactive-color="#ff4949"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="开始时间" :label-width="formLabelWidth">
          <el-date-picker
            v-model="form.trialStartTime"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" :label-width="formLabelWidth">
          <el-date-picker
            v-model="form.trialEndTime"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约次数" :label-width="formLabelWidth">
          <el-input v-model="form.planCountLimit"></el-input>
        </el-form-item>
        <el-form-item label="单次时长" :label-width="formLabelWidth">
          <el-input v-model="form.playDuration"></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-switch
            v-model="form.isActive"
            active-color="#13ce66"
            inactive-color="#ff4949"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="查看" :visible.sync="isshow" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-input v-model="form.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="开始时间" :label-width="formLabelWidth">
          <el-input v-model="form.trialStartTime" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="结束时间" :label-width="formLabelWidth">
          <el-input v-model="form.trialEndTime" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="预约次数" :label-width="formLabelWidth">
          <el-input v-model="form.planCountLimit" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="单次时长" :label-width="formLabelWidth">
          <el-input v-model="form.playDuration" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="状态" :label-width="formLabelWidth">
          <el-switch
            v-model="form.isActive"
            disabled
            active-color="#13ce66"
            inactive-color="#ff4949"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="isshow = false">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'duration',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        status: ''
      },
      form: {
        name: [],
        id: '',

        planCountLimit: '', // 次数
        trialStartTime: '', // 开始时间
        trialEndTime: '', // 结束时间
        playDuration: '', // 时长
        isActive: false // 状态
      },
      formLabelWidth: '120px',
      isDialog: false,
      isaddDialog: false,
      isshow: false, // 查看
      inputVisible: false,
      inputValue: '',
      id: ''
    }
  },
  mounted () {
    this.getSelete(1)
  },
  methods: {
    // 获取下拉框游戏列表
    async getGameList () {
      const res = await this.$api.getGameList({
        pageNum: 1,
        pageSize: 100000
      })
      this.isaddDialog = true
      this.form.name = res.result.list
    },
    // 列表
    async getSelete (page) {
      const res = await this.$api.getPlanConfigList({
        pageNum: page,
        pageSize: 15
      })
      this.tableData = res.result.list
      this.tableData.forEach((item) => {
        item.time = item.trialStartTime + item.trialEndTime
      })
      this.total = res.result.totalCount
    },
    // 更改
    async updatePlanConfig () {
      await this.$api.updatePlanConfig({
        id: this.id,
        gameId: 1, // 游戏id
        trialStartTime: this.form.trialStartTime, // 开始时间
        trialEndTime: this.form.trialEndTime, // 结束时间
        // rangeLimit:this.form.rangeLimit,//人数限制
        userLimit: this.form.planCountLimit, // 用户次数
        playDuration: this.form.playDuration, // 时长
        isActive: this.form.isActive // 是否激活
      })
      this.getSelete(1)
      this.isDialog = false
      this.isaddDialog = false
      this.form = {
        name: '',
        planCountLimit: '', // 次数
        trialStartTime: '', // 开始时间
        trialEndTime: '', // 结束时间
        playDuration: '', // 时长
        isActive: false // 状态
      }
    },
    // 添加
    async addPlanConfig () {
      await this.$api.addPlanConfig({
        gameId: this.form.id,
        trialStartTime: this.form.trialStartTime, // 开始
        trialEndTime: this.form.trialEndTime, // 结束
        playDuration: this.form.playDuration, // 人数
        planCountLimit: this.form.planCountLimit, // 次数
        isActive: this.form.isActive // 状态
      })
      this.getSelete(1)
      this.isDialog = false
      this.isaddDialog = false
      this.isshow = false
      this.form = {
        name: '',
        planCountLimit: '', // 次数
        trialStartTime: '', // 开始时间
        trialEndTime: '', // 结束时间
        playDuration: '', // 时长
        isActive: false // 状态
      }
      this.id = ''
      this.dynamicTags = []
      this.$message.success('添加游戏空间成功')
    },
    // 添加
    openDialog (index, item) {
      (this.form = {
        name: '',
        planCountLimit: '', // 次数
        trialStartTime: '', // 开始时间
        trialEndTime: '', // 结束时间
        playDuration: '', // 时长
        isActive: false // 状态
      })
      this.getGameList()

      console.log('添加')
    },
    // 弹窗确定
    add () {
      if (this.isDialog) {
        this.updatePlanConfig()
      } else {
        this.addPlanConfig()
      }
    },
    // 弹窗取消
    adds () {
      this.isaddDialog = false
      this.isDialog = false
      this.isshow = false
      this.form = {
        name: '',
        planCountLimit: '', // 次数
        trialStartTime: '', // 开始时间
        trialEndTime: '', // 结束时间
        playDuration: '', // 时长
        isActive: false // 状态
      }
    },
    // 更改
    removeOpen (item) {
      console.log(item)
      this.id = item.id
      this.form.name = item.gameName
      this.form.planCountLimit = item.planCountLimit
      this.form.trialStartTime = item.trialStartTime
      this.form.trialEndTime = item.trialEndTime
      this.form.playDuration = item.playDuration
      this.form.isActive = item.isActive
      this.isDialog = true
    },
    // 查看
    remove (item) {
      console.log(item)
      this.form.name = item.gameName
      this.form.planCountLimit = item.planCountLimit
      this.form.trialStartTime = item.trialStartTime
      this.form.trialEndTime = item.trialEndTime
      this.form.playDuration = item.playDuration
      this.form.isActive = item.isActive
      this.isshow = true
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },
    orderexport () {},
    nav_details (item) {
      console.log(item)
      this.$router.push({
        name: 'orderdetails',
        query: {
          orderNo: item.orderNo
        }
      })
    },
    handleClose (tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },

    showInput () {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm () {
      const inputValue = this.inputValue
      if (inputValue) {
        this.dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>
