<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户id">
        <el-input
          v-model="formInline.contractAddress"
          placeholder="请输入用户id"
        ></el-input>
      </el-form-item>
      <el-form-item label="token ID">
        <el-input
          v-model="formInline.tokenId"
          placeholder="请输入token ID"
        ></el-input>
      </el-form-item>
      <el-form-item label="游戏空间">
        <el-select 
          v-model="formInline.gameId"
          placeholder="请选择游戏空间"
          @change="Selectgame"
        >
          <el-option
            v-for="item in gamelist"
            :key="item.id"
            :label="item.gameName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="游戏模式">
        <el-select 
          v-model="formInline.gamePatternId"
          placeholder="请选择游戏模式"
          :disabled="disabled ? true : false"
        >
          <el-option
            v-for="item in gamemoldlist"
            :key="item.id"
            :label="item.gameName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getquery()">查询</el-button>
        <el-button type="primary" @click="batch_audit()">批量审核</el-button>
        <el-button type="primary" @click="orderexport()">清除</el-button>
        <el-button type="primary" @click="getquery()">查询</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        type="selection"
        align="center"
      ></el-table-column>

      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column
        prop="contractAddress"
        label="用户id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tokenId"
        label="作品id"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="gameNameList"
        label="游戏空间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="gamePatternNameList"
        label="游戏模式"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="nav_details(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="
              scope.row.verifyStatus == 'MACHINEPASS' ||
              scope.row.verifyStatus == 'MACHINEREJECT'
            "
            type="text"
            
            @click="to_examine(scope.row)"
            >终审</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="4"
        :current-page="current_page"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="批量审核" :visible.sync="isAuthority" width="50%">
      <div
        style="
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
        "
      >
        <el-radio v-model="radio1" label="PEOPLEPASS" border @change="pupop()"
          >通过</el-radio
        >
        <el-radio v-model="radio1" label="PEOPLEREJECT" border @change="pupop()"
          >拒绝</el-radio
        >
      </div>
      <el-select 
        v-if="isPupop"
        style="margin: 0 auto"
        v-model="textarea"
        placeholder="作品类型"
        @change="change"
      >
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAuthority = false">取 消</el-button>
        <el-button type="primary" @click="Submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="删除" :visible.sync="isDelete" width="50%">
      <div id="">是否删除当前审核任务?</div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isDelete = false">取 消</el-button>
        <el-button type="primary" @click="determine()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'Administration',
  data () {
    return {
      tableData: [],
      srcList: [],
      total: 1,
      current_page: 1,
      radio1: 'PEOPLEPASS', // 1 同意  2 拒绝
      textarea: '需要向客服提供原创证明', // 文本
      textarea1: '', // 文本
      isPupop: false, // 拒绝文本控制
      isPupops: false,
      state: 0,
      formInline: {
        contractAddress: null, // 铸造者id
        tokenId: null, // 作品的tid
        gameId: null, // 游戏id
        gamePatternId: null // 游戏模式id
      },
      isAuthority: false,
      isDelete: false,
      formLabelWidth: '120px',
      userId: '',
      multipleSelection: [],
      idlist: [],
      deleteid: '',
      gamelist: [], // 游戏列表
      gamemoldlist: [], // 游戏列表
      disabled: true
    }
  },
  mounted () {
    this.getSelete(1)
    this.getGameList()
  },

  methods: {
    // 查询
    getquery () {
      this.getSelete(1)
    },
    // 拒绝理由下拉框
    change () {
      console.log(this.textarea)
      if (this.textarea === '4') {
        console.log('我是手动输入')
        this.isPupops = true
      } else {
        this.isPupops = false
      }
    },
    // 获取下拉框游戏列表
    async getGameList () {
      const res = await this.$api.getGameList({
        pageNum: 1,
        pageSize: 100000
      })
      this.isaddDialog = true
      console.log(res)
      this.gamelist = res.result.list
    },
    async getSelete (page) {
      this.current_page = page
      const res = await this.$api.getGoodsPatternList({
        pageNum: page,
        pageSize: 4,
        contractAddress: this.formInline.contractAddress, // 铸造者id
        tokenId: this.formInline.tokenId, // 作品tid
        gameId: this.formInline.gameId, // 游戏id
        gamePatternId: this.formInline.gamePatternId // 游戏模式
      })
      if (res.result == null) {
        this.$message.error('无搜索结果')
      } else {
        this.tableData = res.result.list
        this.tableData.forEach((item) => {
          item.gameNameList = item.gameNameList.toString()
          item.gamePatternNameList = item.gamePatternNameList.toString()
        })
        console.log(this.tableData)
        this.total = res.result.totalCount
      }
    },
    // 批量审核
    async getpeopleVerify (e) {
      console.log(e)
      e = e.toString()
      const res = await this.$api.peopleVerify({
        ids: e,
        peopleVerifyStatus: this.radio1,
        rejectExplain: this.textarea1
      })
      this.isAuthority = false
      this.$message.success(res.status.msg)
      this.textarea1 = ''
      this.getSelete(1)
    },
    // 删除审核任务
    async deleteVerifyTask (e) {
      console.log(e)
      const res = await this.$api.deleteVerifyTask({
        id: e
      })
      this.getSelete(1)
      this.$message.success(res.status.msg)
    },

    // 选择游戏
    Selectgame (e) {
      console.log(e)
      this.disabled = false
      this.gamelist.forEach((item) => {
        if (item.id === e) {
          this.gamemoldlist.push(item.patternNameList)
        }
      })
    },
    // 分页
    xuanze (val) {
      this.getSelete(val)
    },
    // 删除审核任务
    delete_verifyTask (e) {
      this.isDelete = true
      this.deleteid = e.id
    },
    // 删除审核任务弹出框  确定
    determine () {
      this.deleteVerifyTask(this.deleteid)
      this.isDelete = false
    },
    // 批量选择
    handleSelectionChange (val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 批量审核
    batch_audit () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length >= 1) {
        this.state = 1
        this.isAuthority = true
      } else {
        this.$message.error('请选择作品')
      }
    },
    // 拒绝原因弹出框
    pupop () {
      console.log(this.isPupop)
      if (this.radio1 === 'PEOPLEPASS') {
        this.isPupops = false
        this.isPupop = false
      } else {
        this.isPupop = true
        if (this.textarea === '4') {
          this.isPupops = true
        } else {
          this.isPupops = false
        }
      }
    },
    // 提交
    Submit () {
      // console.log(this.textarea)
      if (this.state === 1) {
        this.idlist = []
        this.multipleSelection.forEach((item) => {
          this.idlist.push(item.id)
        })
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete(1)
            }
          } else {
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      } else {
        if (this.radio1 === 'PEOPLEPASS') {
          // 直接掉
          console.log('我掉了')
          this.getpeopleVerify(this.idlist)
        } else if (this.radio1 === 'PEOPLEREJECT') {
          if (this.textarea === '4') {
            if (this.textarea1.length < 1) {
              this.$message.error('请填写拒绝原因')
            } else {
              this.isAuthority = false
              this.isPupops = false
              // 提交
              console.log('我掉了')
              this.getpeopleVerify(this.idlist)
              this.getSelete(1)
            }
          } else {
            // console.log(this.options[this.textarea].value)
            console.log(this.textarea)
            this.textarea1 = this.textarea
            this.getpeopleVerify(this.idlist)
          }
        }
      }
    },
    // 清除
    orderexport () {
      this.formInline = {
        contractAddress: null, // 铸造者id
        tokenId: null, // 作品的tid
        gameId: null, // 游戏id
        gamePatternId: null // 游戏模式id
      }
      this.disabled = true
      this.getSelete(1)
    },
    // 跳转详情
    nav_details (item) {
      console.log(item)
    },
    // 单个作品终审
    to_examine (item) {
      this.state = 0
      this.idlist = []
      this.idlist.push(item.id)
      this.isAuthority = true
    }
  }
}
</script>
<style>
.tag {
  margin: 0px 15px 15px 0px;
  cursor: pointer !important;
}
</style>
