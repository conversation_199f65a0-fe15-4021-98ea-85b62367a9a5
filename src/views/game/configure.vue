<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="openDialog(1, '')"
          >新建游戏空间</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
      ></el-table-column>
      <el-table-column
        fixed
        prop="gameName"
        label="游戏空间"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="patternNameList"
        label="模式"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="onlineLimit"
        label="同时在线人数"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="removeOpen(scope.row)"
            >更改</el-button
          >
          <el-button type="text"  @click="remove(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="新增游戏空间" :visible.sync="isaddDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-input v-model="form.name" :disabled="true"></el-input>
        </el-form-item>

        <el-form-item label="模式" :label-width="formLabelWidth">
          <el-tag
            :key="tag"
            v-for="tag in dynamicTags"
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-if="inputVisible"
            v-model="inputValue"
            ref="saveTagInput"
            
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="同时在线人数" :label-width="formLabelWidth">
          <el-input v-model="form.desc" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="查看" :visible.sync="isshow" width="35%">
      <el-form :model="form">
        <el-form-item label="游戏空间" :label-width="formLabelWidth">
          <el-input v-model="form.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="模式" :label-width="formLabelWidth">
          <el-input v-model="form.code" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="同时在线人数" :label-width="formLabelWidth">
          <el-input v-model="form.desc" :disabled="true"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="add()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
  name: 'configure',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        status: ''
      },
      headers: {
        authorization: ''
      },
      form: {
        name: '',
        code: '',
        desc: ''
      },
      formLabelWidth: '120px',
      isDialog: false,
      isaddDialog: false,
      submitType: '',
      isshow: false, // 查看
      dynamicTags: [], // 标签
      inputVisible: false,
      inputValue: '',
      id: ''
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete(1)
  },
  methods: {
    async getSelete (page) {
      const res = await this.$api.getGameList({
        pageNum: page,
        pageSize: 15
      })
      this.tableData = res.result.list
      this.tableData.forEach((item) => {
        item.patternNameList = item.patternNameList.toString()
      })
      console.log(this.tableData)
      this.total = res.result.totalCount
    },
    // 添加
    async saveOrUpdate () {
      await this.$api.addGame({
        gameName: this.form.name
      })
      this.getSelete(1)
      this.isDialog = false
      this.isaddDialog = false
      this.form = {
        name: '',
        code: '',
        desc: ''
      }
    },
    // 更改
    async updateGame () {
      await this.$api.updateGame({
        gameId: this.id,
        onlineLimit: this.form.desc, // 人数
        gamePatternNameList: this.dynamicTags // 标签
      })
      this.getSelete(1)
      this.isDialog = false
      this.isaddDialog = false
      this.isshow = false
      this.form = {
        name: '',
        code: '',
        desc: ''
      }
      this.id = ''
      this.dynamicTags = []
      this.$message.success('修改游戏空间成功')
    },
    // 添加
    openDialog (index, item) {
      this.form = {
        name: '',
        code: '',
        desc: ''
      }
      this.isaddDialog = true
    },
    // 弹窗确定
    add () {
      if (this.isDialog) {
        this.updateGame()
      } else {
        this.saveOrUpdate()
      }
    },
    // 弹窗取消
    adds () {
      this.isaddDialog = false
      this.isDialog = false
      this.isshow = false
      this.form = {
        name: '',
        code: '',
        desc: ''
      }
    },
    // 更改
    removeOpen (item) {
      console.log(item)
      this.id = item.id
      this.form.name = item.gameName
      if (item.patternNameList === '' || item.patternNameList === null) {
        this.dynamicTags = []
      } else {
        this.dynamicTags = item.patternNameList.split(',')
      }

      console.log(this.dynamicTags)
      this.form.desc = item.onlineLimit
      this.isDialog = true
    },
    // 查看
    remove (item) {
      console.log(item)
      this.form.name = item.gameName
      this.form.code = item.patternNameList
      this.form.desc = item.onlineLimit
      this.isshow = true
    },
    xuanze (val) {
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },
    orderexport () {},
    nav_details (item) {
      console.log(item)
      this.$router.push({
        name: 'orderdetails',
        query: {
          orderNo: item.orderNo
        }
      })
    },
    handleClose (tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },

    showInput () {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm () {
      const inputValue = this.inputValue
      if (inputValue) {
        this.dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>
