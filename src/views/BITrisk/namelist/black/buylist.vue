<template>
    <d2-container class="page">
        <!--  -->
        <common-query :query-schema="querySchema" @onSubmit="onQueryChange" ref="query" :data="query"
            @onReset="onQueryReset"></common-query>
        <!-- :showExport="true" @onExport="houseExport" -->

        <div style="margin-bottom: 10px;">
            <el-button class="btn" :disabled="multipleSelection.length == 0" type="primary">导出</el-button>
            <el-button class="btn" type="primary">导入</el-button>
            <el-button class="btn" type="primary">导入模板下载</el-button>
            <el-button class="btn" type="danger" :disabled="multipleSelection.length == 0" @click="ban()">{{ '禁用' }}
            </el-button>
            <el-button class="btn" type="primary" :disabled="multipleSelection.length == 0" @click="open()">{{ '启用' }}
            </el-button>
            <el-button class="btn" type="primary" @click="handleAdd()">添加</el-button>

        </div>
        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :showSelection="true"
            :multipleSelection.sync="multipleSelection" :loading='listLoading'>
            <template #createAt="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.createAt) }}</span>
            </template>
            <template #updateAt="scope">
                <span type="text" size="small">{{ removeMilliseconds(scope.row.updateAt) }}</span>
            </template>
            <template #action="scope">
                <el-button class="btn" type="warning" @click="handleEdit(scope.row)">编辑</el-button>
                <!-- <el-button class="btn" :type="scope.row.isDeleted == 1 ? 'danger' : 'primary'"
                    @click="handleStatus(scope.row)">{{ scope.row.isDeleted == 1 ? '禁用' : '启用' }}
                </el-button> -->
            </template>
        </common-table>
        <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
            layout="prev, pager, next" :total="page.totalCount" :page-size="page.pageSize" :current-page="page.pageNum">
        </el-pagination>

        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <template #attachment="scope">
                    <el-upload :action="action" :headers="token" :on-success="handlePicSuccess"
                        :class="{ hide: hideUpload_introduce }" :on-change="handleIntroduceUploadHide"
                        :on-remove="handleIntroduceRemove" :file-list="fileListImg" :before-upload="beforeAvatarUpload">
                        <i class="el-icon-plus"></i> 点击上传
                    </el-upload>
                </template>
                <!-- <template #isDeleted="scope">
                    <el-switch v-model="formData.isDeleted" active-color="#13ce66" inactive-color="#ff4949"
                        active-text="是" inactive-text="否" active-value="1" inactive-value="0"></el-switch>
                </template> -->
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import { getBlackList, updateBlackListStatus, addBlackList, updateBlackList } from '@/api/bit';
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'

export default {
    components: {
        CommonQuery,
        CommonTable,
        CommonForm
    },
    data() {
        return {
            limitCount: 1,

            fileListImg: [],
            hideUpload_introduce: false,

            action:
                process.env.VUE_APP_BASE_URL +
                'osscenter/adminApi/missWebSign/upload',
            token: { AdminAuthorization: localStorage.getItem('usertoken') },

            formData: {
                userId: '',
                idCardNo: '',
                source: '',
                isDeleted: "1",
                remark: '',
                attachment: '',
            },
            dialogTitle: '新增',
            dialogVisible: false,
            multipleSelection: [],
            query1: {},
            page: {
                pageSize: 20,
                pageNum: 1
            },
            tableData: [],
            query: {
            },

            querySchema: [ // 搜索组件架构
                {
                    type: 'input',
                    label: '用户id：',
                    placeholder: '请输入用户id',
                    field: 'userId'
                },
                {
                    type: 'input',
                    label: '用户昵称：',
                    placeholder: '请输入用户昵称',
                    field: 'nickname'
                },
                {
                    type: 'input',
                    label: '用户姓名：',
                    placeholder: '请输入姓名',
                    field: 'name'
                },
                // 身份证号码
                {
                    type: 'input',
                    label: '身份证号码：',
                    placeholder: '请输入身份证号码',
                    field: 'idCardNo'
                },
                // contractAddress
                {
                    type: 'input',
                    label: '用户地址：',
                    placeholder: '请输入用户地址',
                    field: 'contractAddress'
                },
                // 来源
                {
                    // type: 'select',
                    // label: '来源：',
                    // placeholder: '请选择来源',
                    // field: 'source',
                    // options: [
                    //     {
                    //         label: '微信',
                    //         value: 'wechat'
                    //     },
                    //     {
                    //         label: '支付宝',
                    //         value: 'alipay'
                    //     },
                    //     {
                    //        label: '百度钱包',
                    //     }
                    // ]
                    type: 'input',
                    label: '来源：',
                    placeholder: '请输入来源',
                    field: 'source'
                },
                // 名单状态 启用 禁用
                {
                    type: 'select',
                    label: '名单状态',
                    field: 'isDeleted',
                    options: [
                        {
                            label: '启用',
                            value: 1
                        },
                        {
                            label: '禁用',
                            value: 0
                        }
                    ]
                },
                // 备注
                {
                    type: 'input',
                    label: '备注',
                    placeholder: '请输入备注',
                    field: 'remark'
                }
            ],
            tableSchema: [ // 表格架构
                // 用户id	用户昵称	contractAddress	姓名	身份证号码	来源	备注	附件	名单状态	创建日期	创建的管理员用户名	修改日期	修改的管理员用户名
                {
                    label: '用户id',
                    field: 'userId'
                },
                {
                    label: '用户昵称',
                    field: 'nickname'
                },
                {
                    label: '用户地址',
                    field: 'contractAddress'
                },
                {
                    label: '姓名',
                    field: 'realName'
                },
                {
                    label: '身份证号码',
                    field: 'idCardNo'
                },
                // 来源
                {
                    label: '来源',
                    field: 'source'
                },
                // 备注
                {
                    label: '备注',
                    field: 'remark'
                },
                // 附件	名单状态	创建日期	创建的管理员用户名	修改日期	修改的管理员用户名
                {
                    label: '附件',
                    field: 'attachment'
                },
                {
                    label: '名单状态',
                    field: 'isDeleted',
                    type: 'tag',
                    tagMap: {
                        0: {
                            label: '禁用',
                            tagType: 'danger'
                        },
                        1: {
                            label: '启用',
                            tagType: 'default'
                        },
                    },
                },
                {
                    label: '创建日期',
                    slot: 'createAt'
                },
                {
                    label: '创建的管理员用户名',
                    field: 'createAdminUserName'
                },
                {
                    label: '修改日期',
                    slot: 'updateAt'
                },
                {
                    label: '修改的管理员用户名',
                    field: 'updateAdminUserName'
                },

                {
                    label: '操作',
                    slot: 'action',
                    width: '140px',
                    fixed: 'right'
                }
            ],

        }
    },
    computed: {
        formSchema() {
            return [
                {
                    type: 'input',
                    label: '用户id：',
                    placeholder: '请输入用户id',
                    field: 'userId',
                    disabled: this.dialogTitle == '编辑',
                    rules: [
                        {
                            required: true,
                            message: '请输入用户id',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '身份证号码：',
                    placeholder: '请输入身份证号码',
                    field: 'idCardNo',
                    rules: [
                        {
                            required: true,
                            message: '请输入身份证号码',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '来源：',
                    placeholder: '请输入来源',
                    field: 'source',
                    rules: [
                        {
                            required: true,
                            message: '请输入来源',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'radio',
                    label: '状态：',
                    field: 'isDeleted',
                    options: [
                        { label: '启用', value: "1" },
                        { label: '禁用', value: "0" },
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请选择状态',
                            trigger: 'change',
                        },
                    ],
                },
                {
                    type: 'textarea',
                    label: '备注：',
                    placeholder: '请输入备注',
                    field: 'remark',
                    rules: [],
                },
                {
                    type: 'upload',
                    label: '附件：',
                    slot: 'attachment',
                    rules: [],
                },
                {
                    type: 'action'
                },
            ]
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        formatDataToList(data) {
            return data.map(item => {
                const key = Object.keys(item)[0];
                const value = item[key];
                return { key: String(key), value: String(value) };
            });
        },
        async ban() {
            const List = this.multipleSelection.map(item => ({ [item.userId]: item.isDeleted }));
            console.log(List);
            let data = {
                subType: 'BUY_ITEM',
                type: 'BLACK',
                isDeleted: 0,
                listStr: JSON.stringify(this.formatDataToList(List))

            }
            let res = await updateBlackListStatus(data)
            if (res.status.code == 0) {
                this.$message({
                    message: res.status.msg,
                    type: 'success'
                })
                this.getList()
            }
        },
        async open() {
            const List = this.multipleSelection.map(item => ({ [item.userId]: item.isDeleted }));
            let data = {
                subType: 'BUY_ITEM',
                type: 'BLACK',
                isDeleted: 1,
                listStr: JSON.stringify(this.formatDataToList(List))

            }
            let res = await updateBlackListStatus(data)
            if (res.status.code == 0) {
                this.$message({
                    message: res.status.msg,
                    type: 'success'
                })
                this.getList()
            }
        },
        handleIntroduceUploadHide(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
        },
        beforeAvatarUpload(file) {
            // if (
            //     file.type !== 'image/jpeg' &&
            //     file.type !== 'image/png' &&
            //     file.type !== 'image/gif'
            // ) {
            //     this.$message.error('只能上传jpg/png/GIF格式文件')
            //     return false
            // }
        },
        // 图片移除
        handleIntroduceRemove(file, fileList) {
            this.hideUpload_introduce = fileList.length >= this.limitCount
            this.formData.attachment = ''
        },
        handlePicSuccess(res, file) {
            console.log(res, '上传');
            this.formData.attachment = res.result.url
        },
        async submit() {
            if (this.dialogTitle == '新增') {
                let data = {
                    userId: this.formData.userId,
                    contractAddress: this.formData.contractAddress,
                    remark: this.formData.remark,
                    subType: 'BUY_ITEM',
                    type: 'BLACK',
                    ...this.formData

                }
                await addBlackList(data).then(res => {
                    if (res.status.code == 0) {

                        this.$message.success('添加成功')
                        this.dialogVisible = false
                        this.getList()
                        this.formData = {}
                    }
                })
            } else {
                let data = {
                    idStr: this.formData.idStr,
                    userId: this.formData.userId,
                    contractAddress: this.formData.contractAddress,
                    remark: this.formData.remark,
                    subType: 'BUY_ITEM',
                    type: 'BLACK',
                    ...this.formData
                }
                let res = await updateBlackList(data)
                if (res.status.code == 0) {
                    this.$message.success(res.status.msg)
                    this.dialogVisible = false
                    this.getList()
                    this.formData = {}
                }
            }
            console.log(this.formData, 'tijiao');

        },
        handleAdd() {
            this.dialogTitle = '新增'
            this.dialogVisible = true
            this.formData = {}
            this.formData.isDeleted = "1"

        },
        async handleStatus(e) {

        },
        handleEdit(e) {
            this.formData.attachment = ''
            this.formData = e
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.formData.isDeleted = e.isDeleted.toString()
            console.log(e, this.formData.isDeleted, 'row行');
        },
        // 分页切换
        currentChange(value) {
            this.page.pageNum = value
            this.listLoading = true
            this.getList()
        },
        onQueryReset() {
            this.query = {

            }

            this.listLoading = true
            this.getList()
        },
        async houseExport(e) {
            // const res = await exportBitDetail({
            //     ...e
            // })
            // if (res.retCode === 500) {
            //     this.$message.error(res.retMsg)
            //     this.getList()
            // } else if (res.type === 'application/json') {
            //     // blob 转 JSON
            //     const enc = new TextDecoder('utf-8')
            //     res.arrayBuffer().then((buffer) => {
            //         const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            //         this.$message.error(data.status?.msg)
            //     })
            // } else {
            //     downloadBlob(res, '开仓明细' + Date.now() + '.csv')
            //     this.$message.success('导出成功')
            //     this.getList()
            // }
        },
        onQueryChange(data) {
            console.log(data, 123);

            const filteredObj = {};

            for (const key in data) {
                if (data[key] !== "" && data[key] !== null && data[key] !== undefined && !(Array.isArray(data[key]) && data[key].length === 0)) {
                    filteredObj[key] = data[key];
                }
            }
            this.listLoading = true
            this.query1 = filteredObj
            this.getList(true)
        },
        getList() {
            this.listLoading = true
            let data = {
                ...this.query1,
                ...this.page,
                ...this.query,
                subType: 'BUY_ITEM',
                type: 'BLACK'
            }
            getBlackList(data).then(res => {
                console.log(res);
                this.listLoading = false
                this.page.totalCount = res.result.totalCount
                this.tableData = res.result.list
            })
        },
        removeMilliseconds(dateTimeString) {
            return dateTimeString.split('.000')[0];
        },
    }
}
</script>

<style></style>