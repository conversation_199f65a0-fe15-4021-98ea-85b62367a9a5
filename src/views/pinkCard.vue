<template>
    <div class="container">
        <div class="card_top">
            <div class="card_top_left">
                <div class="card_title">{{ $t('pink_card_page.title') }}</div>
                <div class="card_subtitle">{{ $t('pink_card_page.subtitle') }}</div>
                <div class="card_btn_group">
                    <button class="card_btn active">{{ $t('pink_card_page.apply_now') }}</button>
                    <button class="card_btn border_left_right">{{ $t('pink_card_page.check_card') }}</button>
                    <button class="card_btn">{{ $t('pink_card_page.overdraft') }}</button>
                </div>
            </div>
            <div class="card_top_right">
                <img class="card_img" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377651086037966848.png" alt="Pink Card" />
            </div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ $t('pink_card_page.highlights') }}</span>
        </div>
        <div class="advantage_grid">
            <div class="advantage_row">
                <div class="advantage_text">
                    <div class="advantage_title">{{ $t('pink_card_page.feature1_title') }}</div>
                    <div class="advantage_desc">{{ $t('pink_card_page.feature1_desc') }}</div>
                </div>
                <div class="advantage_img">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377665341122502656.png" alt="即刻开卡" />
                </div>
            </div>
            <div class="advantage_row reverse">
                <div class="advantage_text">
                    <div class="advantage_title">{{ $t('pink_card_page.feature2_title') }}</div>
                    <div class="advantage_desc">{{ $t('pink_card_page.feature2_desc') }}</div>
                </div>
                <div class="advantage_img">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377665341122502656.png" alt="多币种支持" />
                </div>
            </div>
            <div class="advantage_row">
                <div class="advantage_text">
                    <div class="advantage_title">{{ $t('pink_card_page.feature3_title') }}</div>
                    <div class="advantage_desc">{{ $t('pink_card_page.feature3_desc') }}</div>
                </div>
                <div class="advantage_img">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377665341122502656.png" alt="低手续费" />
                </div>
            </div>
            <div class="advantage_row reverse">
                <div class="advantage_text">
                    <div class="advantage_title">{{ $t('pink_card_page.feature4_title') }}</div>
                    <div class="advantage_desc">{{ $t('pink_card_page.feature4_desc') }}</div>
                </div>
                <div class="advantage_img">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377665474467815424.png" alt="全球通用" />
                </div>
            </div>
            <div class="advantage_row">
                <div class="advantage_text">
                    <div class="advantage_title">{{ $t('pink_card_page.feature5_title') }}</div>
                    <div class="advantage_desc">{{ $t('pink_card_page.feature5_desc') }}</div>
                </div>
                <div class="advantage_img">
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377665516289220608.png" alt="极致安全" />
                </div>
            </div>
        </div>
        
        <div class="apply_section">
            <div class="apply_title">{{ $t('pink_card_page.apply_title') }}</div>
            <div class="apply_desc">{{ $t('pink_card_page.apply_desc') }}</div>
            <div class="apply_highlight">{{ $t('pink_card_page.apply_highlight', { brand: 'PinkWallet' }) }}</div>
            <button class="apply_btn">{{ $t('pink_card_page.apply_btn') }}</button>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch  } from 'vue'
    import { useI18n } from "vue-i18n";
    const { locale, t } = useI18n();
    
    watchEffect(() => {
        console.log("语言切换为：", locale.value);
        localStorage.setItem('lang', locale.value)
    });

    const applyCard = () => {
        // 申请卡片逻辑
    }

    const checkCard = () => {
        // 检查卡片逻辑
    }
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";
    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans';
    }
    .advantage_grid {
        display: flex;
        flex-direction: column;
        gap: 120px;
        margin: 100px 0 0 0;
    }
    .advantage_row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-height: 260px;
        .advantage_text {
            max-width: 480px;
            text-align: left;
            .advantage_title {
                font-size: px(24);
                font-weight: 700;
                color: #fff;
                margin-bottom: px(18);
            }
    .advantage_desc {
                color: rgba(255,255,255,0.5);
                font-size: px(16);
                line-height: 1.8;
            }
        }
        .advantage_img img {
            width: px(334);
            height: auto;
            display: block;
        }
        &.reverse {
            flex-direction: row-reverse;
            .advantage_text {
                text-align: right;
            }
        }
    }
    .card_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: px(180);
        .card_top_left {
            flex: 1;
            min-width: 320px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            margin-top: 0;
            .card_title {
                font-size: px(52);
                font-weight: 700;
                color: #fff;
                margin-bottom: px(50);
                font-family: 'MiSans-bold', ' ', sans-serif;
            }
            .card_subtitle {
                color: #ef88a3;
                font-size: px(20);
                margin-bottom: px(80);
                font-weight: 500;
            }
            .card_btn_group {
                display: flex;
                gap: 18px;
                height:73px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding:px(12);
                border-radius: px(12);
                .card_btn {
                    color: #fff;
                    padding: 10px 28px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s;
                    outline: none;
                    background: transparent;
                    width: 144px;
                    &.border_left_right{
                        position: relative;
                        margin:0 20px;
                    }
                    &.border_left_right::before{
                        content: '';
                        position: absolute;
                        left: -20px;
                        top: 0;
                        width: 1px;
                        height: 100%;
                        background: rgba(255, 255, 255, 0.2);
                    }
                    &.border_left_right::after{
                        content: '';
                        position: absolute;
                        right: -20px;
                        top: 0;
                        width: 1px;
                        height: 100%;
                        background: rgba(255, 255, 255, 0.2);
                    }
                    &.active, &:hover {
                        background: #222;
                        border-color: #222;
                    }
                }
            }
        }
        .card_top_right {
            flex: none;
            margin-left: 60px;
            .card_img {
                width: 406px;
                height: 256px;
                border-radius: 16px;
                box-shadow: 0px 0px 9.3px 3px #EF88A366;
                object-fit: cover;
                background: #222;
                border: 1.5px solid #EF88A3;
            }
        }
    }
    .apply_section {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        text-align: center;
        margin-top: px(337);
        margin-bottom: px(380);
        .apply_title {
            font-size: px(26);
            font-weight: 700;
            color: #fff;
            margin-bottom: px(35);
        }
        .apply_desc {
            color: rgba(255,255,255,0.7);
            font-size: px(18);
            margin-bottom: px(35);
        }
        .apply_highlight {
            color: #fff;
            font-size: px(36);
            margin-bottom: px(81);
            .highlight {
                color: #ef88a3;
                font-weight: 700;
            }
        }
        .apply_btn {
            width: px(180);
            height: px(52);
            background: #ef88a3;
            color: #181818;
            border-radius: px(30);
            font-size: px(20);
            font-weight: 600;
            border: none;
            cursor: pointer;
            outline: none;
        }
    }
    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        transition: all 0.8s;
        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            position: relative;
            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;
            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }
            &::before {
                left: -100px;
            }
            &::after {
                right: -100px;
            }
        }
    }
</style>