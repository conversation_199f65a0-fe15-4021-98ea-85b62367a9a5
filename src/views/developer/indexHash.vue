<template>
  <d2-container class="page" style="background-color:#FFFFFF;">
    <div class="content">
         <div class="textarea" >
           <div class="left">
             <p>请输入你要生成的tid</p>
             <el-input type="textarea" :rows="10" placeholder="请输入tid" v-model="tidStr">
            </el-input>
           </div>
          <div class="right">
            <p>生成Hash结果</p>
            <el-input type="textarea" readonly :rows="10" placeholder="生成结果" v-model="tidHash">
           </el-input>
          </div>

         </div>
      <el-button type="primary" @click="submit()">确认生成</el-button>
    </div>
  </d2-container>
</template>

<script>
  export default {
    name: 'developerHash',
    data() {
      return {
        tidStr: '',
        tidHash: ''
      }
    },
    methods: {
      async submit() {
        let data = this.tidStr.split("\n")
        let res = await this.$api.tidHashList({
          tids:data.toString()
        })
          if (res.status.code == 0) {
            let array=[]
            res.result.forEach(item => {
              array.push({tid:item.tid,hash:item.hash})
            });
            let result = array.map(item => `tid:${item.tid} hash值:${item.hash}`);
            this.tidHash = result.join('\n');
            this.$message.success('生成成功')
          }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .content {
    padding: 20px;
  }
  .textarea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .left{
      width: 39%;
    }
    .right{
      width: 59%;
    }
  }
</style>
