<template>
  <div class="content">
    <el-row :gutter="20" type="flex" align="middle">
      <el-col :span="4">
        <h4>根据traceId查询对应日志: </h4>
      </el-col>
      <el-col :span="4">
        <el-input v-model.trim="traceId" placeholder="traceId" />
      </el-col>
      <el-col :span="16">
        <el-button type="primary" @click="search">查询</el-button>
      </el-col>
    </el-row>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%" :before-close="handleClose"
      :close-on-click-modal="false">
      <el-input type="text" v-if="type == 'uid'" style="margin-bottom:20px;" placeholder="请输入系列id" v-model="ctid">
      </el-input>
      <div class="textarea" v-if="type == 'tid'">
        <el-input type="textarea" :rows="10" placeholder="请输入tid 例如:
  121xxxxx312
  131xxxxx221
  141xxxxx331" v-model="tidStr">
        </el-input>
      </div>
      <div class="textarea" v-if="type == 'uid'">
        <el-input type="text" :rows="10" placeholder="请输入用户id" v-model="uid">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">取 消</el-button>
        <el-button type="primary" @click="submit()">确 定</el-button>
      </span>
    </el-dialog>
    <el-button @click="refreshCdn()">刷新cdn</el-button>
    <el-button type="warning" @click="open('tid')">ES批量同步-tid</el-button>
    <el-button type="danger" @click="open('uid')">ES批量同步-用户id</el-button>
  </div>
</template>

<script>
  export default {
    name: 'DevTools',
    data() {
      return {
        traceId: '',
        dialogVisible: false,
        title: '',
        type: '',
        ctid: '',
        tidStr: '',
        uid: ''
      }
    },
    methods: {
      /**
       * 查询
       */
      search() {
        const {
          traceId
        } = this
        if (traceId) {
          window.open(
            `${process.env.VUE_APP_BASE_URL}adminuser/adminApi/sls/missWebSign/traceIdSearch?traceId=${traceId}`
          )
        } else {
          this.$message.error('traceId不可为空')
        }
      },

      //修复飞跃计划
      async leapPlanWebsite() {
        const res = await this.$api.initLeapPlanWebsite({})
        if (res.status.code !== 0) {
          this.$message.error(res.status.msg)
        }
      },
      //修复飞跃计划
      async mall() {
        const res = await this.$api.marketTabToGoods({})
        if (res.status.code !== 0) {
          this.$message.error(res.status.msg)
        }
      },
      // 刷新cdn
      async refreshCdn() {
        const res = await this.$api.refreshCdnCaches({})
      },
      open(type) {
        this.type = type
        if (type == 'tid') {
          this.dialogVisible = true
          this.title = 'ES批量同步-tid'
        } else if (type == 'uid') {
          this.dialogVisible = true
          this.title = 'ES批量同步-用户id'
        }
      },
      handleClose() {
        this.dialogVisible = false
        this.uid = ""
        this.tidStr = ""
        this.ctid = ""
      },
      async submit() {
        let data;
        if(this.type == 'tid'){
          data = {
            tidStr:JSON.stringify(this.tidStr.split("\n"))
          }
        }
        if(this.type == 'uid'){
         data = {
           ctid:this.ctid,
           uid:this.uid
         }
        }
        console.log(data)
        if(this.type == 'tid'){
          let res = await this.$api.batchTid(data)
          if (res.status.code == 0) {
             this.$message.success('操作成功')
             this.handleClose()
          }
        }else{
          let res = await this.$api.batchCtidUser(data)
          if (res.status.code == 0) {
            this.$message.success('操作成功')
            this.handleClose()
          }
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .content {
    padding: 20px;
  }
</style>
