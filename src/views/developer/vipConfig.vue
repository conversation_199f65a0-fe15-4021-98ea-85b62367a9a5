<template>
  <d2-container class="page">
    <div class="padding">
      <common-table :table-schema="tablesShema" :table-data="tableData" :showIndex="false">
        <template #action-header>
          <el-button @click="addItem()" type="primary" size="mini">新增</el-button>
        </template>
        <template #search="scope">
          <el-autocomplete style="width:340px;margin-bottom:10px;" v-model=" scope.row.ctid"
            :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
          </el-autocomplete>
        </template>
        <template #SchemaSeries="scope">
          <div class="border" v-for="(item,index) in scope.row.condList">
            <el-form ref="form" :model="item" label-width="80px" size="mini">
              <el-form-item label="address">
                <el-input v-model="item.cond" placeholder="请输入address"></el-input>
              </el-form-item>
              <el-form-item label="概率">
                <el-row>
                  <el-col :span="12">
                    <el-input v-model="item.percent" placeholder="请输入整数"></el-input>
                  </el-col>
                  <el-col style="text-align: left;margin-left:20rpx;" :span="12">%</el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </div>
          <el-button type="primary" style="margin-top:10px;" size="mini"
            @click="addItemAddress(scope.row)">添加address</el-button>
        </template>
        <template #action="scope">
          <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, tableData)">
            <el-button slot="reference" type="text">删除</el-button>
          </el-popconfirm>
        </template>
      </common-table>
      <div class="footer">
        <el-button type="primary" style="width:300px" round @click="submitConfig">提交</el-button>
      </div>
    </div>
  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
  export default {
    name: 'vipConfig',
    components: {
      CommonTable
    },
    data() {
      return {
        tableData: [{
          ctid: "",
          condList: [{
            cond: '',
            percent: 0
          }]
        }],
        tablesShema: [ // 收藏表格架构
          {
            label: '系列ID',
            field: 'ctid',
            slot: 'search',
          },
          {
            label: '用户地址/概率',
            slot: 'SchemaSeries',
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header'
          }
        ],
      }
    },
    created() {
      this.getInfo()
    },
    methods: {
      addItem() {
        this.tableData.push({
          ctid: "",
          condList: [{
            cond: '',
            percent: 0
          }]
        })
      },
      addItemAddress(item) {
        console.log(item.condList)
        item.condList.push({
          cond: '',
          percent: 0
        })
      },
      deleteItem(index, data) {
        data.splice(index, 1)
      },
      async querySearchAsync(queryString, cb) {
        var restaurants = this.restaurants;
        this.searchNew(queryString)
        let results = []
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
          results = this.results
          cb(results);
        }, 1000);
      },
      handleSelect(item) {
        console.log(item);
      },
      async searchNew(str) {
        this.results = []
        if (str) {
          let res = await this.$api.searchPgc({
            name: str
          });
          if (res.status.code == 0) {
            if (res.result.list != null) {
              res.result.list.forEach((item) => {
                this.results.push({
                  'value': `${item.name}(${item.ctid})`,
                })
              })
              console.log(this.results)
            }
          }
        }
      },
      async getInfo() {
        let res = await this.$api.rankingConfigInfo();
        if (res.status.code == 0) {
          let list = JSON.parse(res.result.configJson)
          list.forEach((item)=>{
            item.ctid = item.ctidStr
          })
          console.log(list)
          this.tableData = list
        }
      },
      async submitConfig() {
       let data = JSON.parse(JSON.stringify(this.tableData))
       data.forEach((item)=>{
         item.ctidStr = item.ctid
          if(item.ctid){
             item.ctid = item.ctid.split('(')[1].split(')')[0]
          }
        })
        console.log(data)
        let res = await this.$api.rankingConfigUpdate({
          configJson:JSON.stringify(data)
        });
        if (res.status.code == 0) {
          this.$message.success('操作成功')
        } else {

        }
      }
  }
  }
</script>
<style lang="scss" scoped>
  .padding {
    padding: 30px 200px;
  }

  .border {
    border-bottom: 1px solid #ccc;
    padding-top: 20px;
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
  }
</style>
