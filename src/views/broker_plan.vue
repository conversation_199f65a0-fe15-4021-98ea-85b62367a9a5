<template>
    <div class="partner-program">
        <!-- 顶部介绍 -->
        <section class="hero">
            <div class="hero-left">
                <div class="title">加入PinkWallet合伙人计划</div>

                <div class="gobe" @click="postshow = true">
                    前往邀请返佣
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377952497078460416.png" alt="">
                </div>
                <div class="desc">
                    如果您是内容创作者，社群社区长、kOL，加入Pinkwallet代理商，助您收入攀升，让您的影响力增值变现
                </div>
                <div class="gopar">
                    <view class="join-btn" @click="visible = true">成为合伙人</view>
                    <!-- <div > -->
                    <img class="share" @click="postshow = true"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382338764171993088.png" alt="">
                    <!-- </div> -->
                </div>
            </div>
            <div class="hero-right">
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377745403372986368.png" alt="hero-img" />
            </div>
        </section>

        <!-- 合伙人权益 -->
        <section class="benefits">
            <div class="title-container-card">
                <span class="title">加入PinkWallet合伙人计划可得</span>
            </div>
            <div class="cards">
                <div v-for="(item, i) in benefits" :key="i" class="card">
                    <img :src="item.icon" class="icon" />
                    <span class="text">{{ item.text }}</span>
                </div>
            </div>
        </section>

        <!-- swiper 合伙人如何运营 -->
        <section class="swiper-section">
            <div class="title-container-card">
                <span class="title">合伙人如何运作</span>
            </div>
            <div class="swiper" v-for="(item, index) in swiperList" :key="index">
                <div class="view_grid" v-if="currentIndex == index" :class="[`slide_${slideDirection}`]">
                    <div class="view_container">
                        <div class="right_view">
                            <img :src="item.img" :style="{ width: item.w + 'px', height: item.h + 'px' }" alt="" />
                        </div>
                        <div class="dots">
                            <div v-for="(item, i) in swiperList" :key="i"
                                :class="['dot', { active: currentIndex == i }]"></div>
                        </div>
                        <div class="left_view">
                            <div class="text">
                                <span class="title">{{ item.title }}</span>
                                <span class="desc">{{ item.desc }}</span>
                            </div>
                            <div class="carousel-controls">
                                <div class="arrows">
                                    <div class="arrow-btn" @click="prevPage(index)">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382342405196963840.png" />
                                    </div>
                                    <div class="arrow-btn rotate" @click="nextPage(index)">
                                        <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382342405196963840.png" />
                                    </div>
                                </div>

                            </div>
                        </div>

                    </div>
                    <!-- <div class="view_bottom right mt_48" @click="nextPage(1)">
                        <div class="right_icon">
                            <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377356567308492800.png" alt="" srcset="">
                        </div>
                    </div> -->
                </div>
            </div>
        </section>

        <!-- 品类展示 -->
        <section class="categories">
            <div class="title-container-card">
                <span class="title">返佣交易品类丰富</span>
            </div>
            <div class="cards2">
                <div v-for="(item, i) in categories" :key="i" class="card">
                    <img :src="item.icon" class="icon" />
                    <p class="text">{{ item.text }}</p>
                </div>
            </div>
        </section>

        <!-- 合规牌照 -->
        <section class="compliance">
            <div class="title-container-card">
                <span class="title">合规持牌运营</span>
            </div>
            <div class="cards-compliance">
                <div v-for="(item, i) in licenses" :key="i" class="card-compliance">
                    <div class="img">
                        <img :src="item.icon" class="icon" mode="heightFixed" />
                        <img class="checked" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377973040368410624.png" alt="">
                    </div>
                    <p class="text">{{ item.text }}</p>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="faq">
            <div class="title-container-card">
                <span class="title">FAQ</span>
            </div>

            <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
                <div class="faq-question" @click="toggle(index)">
                    <span>{{ item.question }}</span>
                    <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382307585527996416.png" alt="arrow" />
                </div>

                <transition name="faq-slide">
                    <div class="faq-answer" v-show="activeIndex === index">
                        {{ item.answer }}
                    </div>
                </transition>
            </div>

            <!-- <div class="faq-list">
                <div v-for="(item, index) in faqList" :key="index" class="faq-item">
                    <div class="question">{{ item.q }}</div>
                </div>
            </div> -->
        </section>


        <!-- 底部按钮 -->
        <footer class="footer">
            <div class="inner">
                <span>申请成为PinkWallet合伙人</span>
                <div class="btns">
                    <div class="login-btn" @click="visible = true">立即申请</div>
                    <div class="contact-btn" @click="opens('https://t.me/+U5S8KFdz7_AzMDM0')">联系专员</div>
                </div>
            </div>
        </footer>
    </div>

    <div v-show="visible" class="modal-mask">
        <transition name="fade-zoom">
            <div class="modal-wrapper">
                <div class="modal-container">
                    <!-- Header -->
                    <div class="modal-header">
                        <div class="title">申请成为合伙人</div>

                        <img class="close-btn" @click="visible = false"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1379224833215782912.png" alt="">
                    </div>
                    <!-- {{ userInfo }} -->
                    <!-- Form -->
                    <div class="modal-body">

                        <div class="sub-title">您的PinkWallet账户 <br> (代理佣金将发放至您的PinkWallet账户)</div>
                        <div class="email-box">
                            <div class="email">
                                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382352512802709504.png" alt="">
                                {{ userInfo.email || '--' }}
                            </div>
                            <div class="uid">UID: {{ uid }}</div>
                        </div>

                        <div class="form-group">
                            <div class="required label">您的联系方式</div>
                            <div class="form-row">
                                <input type="text" placeholder="请输入您的名字" v-model="form.name" />
                                <!-- <input type="text" disabled :value="userInfo.email" /> -->
                            </div>

                            <!-- <div class="form-row">
                                <input type="text" class="full" placeholder="请输入您的Telegram" v-model="form.telegram" />
                            </div> -->
                        </div>


                        <div class="form-group">
                            <div class="required label">Telegram</div>
                            <div class="form-row">
                                <input type="text" class="full" placeholder="请输入您的Telegram" v-model="form.telegram" />
                                <!-- <div></div> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="required label">您的居住地</div>
                            <div class="form-row">
                                <input type="text" placeholder="请输入您的居住地" v-model="form.address" />
                                <!-- <input type="text" placeholder="BD邀请码(选填)" v-model="form.BDcode" /> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="label">BD邀请码</div>
                            <div class="form-row">
                                <!-- <input type="text" placeholder="请输入您的居住地" v-model="form.address" /> -->
                                <input type="text" placeholder="BD邀请码(选填)" v-model="form.BDcode" />
                            </div>
                        </div>

                        <div class="form-group" style="margin-bottom: 0;">
                            <div class="required label">影响力证明</div>

                            <div class="proof" v-if="hasX">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    X
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写个人主页链接" v-model="formPl.Xlink" />
                                    <input type="text" placeholder="请填写粉丝数量" v-model="formPl.Xfans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasYoutube">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    Youtube
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写个人主页链接" v-model="formPl.YoutubeLink" />
                                    <input type="text" placeholder="请填写粉丝数量" v-model="formPl.YoutubeFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasFacebook">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    Facebook
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写个人主页链接" v-model="formPl.FacebookLink" />
                                    <input type="text" placeholder="请填写粉丝数量" v-model="formPl.FacebookFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasTikTok">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    TikTok
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写个人主页链接" v-model="formPl.TikTokLink" />
                                    <input type="text" placeholder="请填写粉丝数量" v-model="formPl.TikTokFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasTelegram">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    Telegram
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写社群或频道链接" v-model="formPl.TelegramLink" />
                                    <input type="text" placeholder="请填写个人Telegram链接"
                                        v-model="formPl.TelegramPersonalLink" />
                                </div>
                                <div class="form-row">
                                    <input type="text" class="full" placeholder="请填写社群人数/频道订阅人数"
                                        v-model="formPl.TelegramChannelNum" />
                                    <!-- <div></div> -->
                                </div>
                            </div>

                            <div class="proof" v-if="hasOthers">
                                <div class="labelX">
                                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382355230086750208.png" alt="">
                                    Others
                                </div>
                                <div class="form-row">
                                    <input type="text" placeholder="请填写其他平台名称" v-model="formPl.OthersName" />
                                    <input type="text" placeholder="请填写账户或个人主页链接" v-model="formPl.OthersLink" />
                                </div>
                                <div class="form-row">
                                    <input type="text" class="full" placeholder="请填写粉丝数量/群成员人数"
                                        v-model="formPl.OthersFans" />
                                    <div></div>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label v-for="(item, i) in platforms" :key="i">
                                    <input type="checkbox" v-model="form.selectedPlatforms" :value="item" />
                                    {{ item }}
                                </label>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="modal-footer">
                            <button class="submit-btn" :disabled="!form.name" @click="submitForm">提交</button>
                        </div>
                    </div>


                </div>
            </div>
        </transition>
    </div>


    <SharePosterModal v-model:visible="postshow" mode="2"
        :link="`${url}/pages/login/register?code=${inviteCodes.inviteCode}`" />
</template>

<script setup>
import { judgePlatform } from '@/utils/platform';
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
const {
    platform,
    token,
    // activityNo,
} = route.query;
import { ref, computed, reactive, watch, onUnmounted } from 'vue'
import { getInviteCode, submitAgentApply } from '@/api/pinkexchange'
import SharePosterModal from "./component/SharePosterModal.vue"
import { GetUserInfo } from "@/api/pinkwallet"
import { Toast } from 'vant'

const url = process.env.VUE_APP_URL
const visible = ref(false);

// import { useI18n } from "vue-i18n";
const postshow = ref(false)
const inviteCodes = ref({})

watch(visible, (val) => {
    if (val) {
        document.body.style.overflow = 'hidden'
        document.body.style.touchAction = 'none' // iOS 禁止滑动
    } else {
        document.body.style.overflow = ''
        document.body.style.touchAction = ''
    }
})

// 避免组件销毁后仍然禁止滚动
onUnmounted(() => {
    document.body.style.overflow = ''
    document.body.style.touchAction = ''
})

// const { t } = useI18n();
const activeIndex = ref(null)
const currentSwiperIndex = ref(0);
const currentIndex = ref(0);
const slideDirection = ref('right');
const userInfo = ref({})
const uid = localStorage.getItem("uid") || ''

const form = reactive({
    name: '',
    telegram: '',
    influence: [],
    selectedPlatforms: [],
    platformData: {} // 存储每个平台的三个字段
});

const formPl = reactive({})

const opens = (e) => {
    window.open(e)
}

const platforms = ['X', 'Youtube', 'Facebook', 'TikTok', 'Telegram', 'Others'];

const hasX = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'x')
})

const hasYoutube = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'youtube')
})

const hasFacebook = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'facebook')
})

const hasTikTok = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'tiktok')
})

const hasTelegram = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'telegram')
})

const hasOthers = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'others')
})

const submitForm = async () => {

    let data = {
        name: form.name,
        // phone: form.telegram,
        email: userInfo.email,
        otherContracts: "",
        address: form.address,
        bdCode: form.BDcode,
        influenceProof: JSON.stringify(formPl),
    }
    let res = await submitAgentApply(data)
    if (res.code == 200) {
        visible.value = false;
        Toast('提交成功')
    }
    console.log('提交信息：', form.value);
    // 提交逻辑
};


const fetchGetUserInfo = async () => {
    let res = await GetUserInfo()
    if (res.code == 200) {
        userInfo.value = res.result
    }
}

const getInviteCodes = async () => {
    let res = await getInviteCode()
    if (res.code == 200) {
        inviteCodes.value = res.result
    }
}

const handleClick = (index) => {
    activeIndex.value = index
}
const prevPage = (index) => {
    console.log(index, swiperList.length);

    if (index <= 0) {
        currentIndex.value = swiperList.length - 1;
        return
    }

    if (index > currentIndex.value) {
        slideDirection.value = 'right';
    } else {
        slideDirection.value = 'left';
    }


    currentIndex.value--;
}

const nextPage = (index) => {
    console.log(index, swiperList.length);
    if (index >= swiperList.length - 1) {
        currentIndex.value = 0;
        return
    }

    if (index > currentIndex.value) {
        slideDirection.value = 'right';
    } else {
        slideDirection.value = 'left';
    }
    currentIndex.value++;

}

const swiperList = [
    {
        title: "申请成为 PinkWallet合伙人",
        desc: "欢迎拥有社群资源人，申请成为合伙人",
        img: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378070623594045440.png",
        w: 267,
        h: 156
    },
    {
        title: "资质审批",
        desc: "我们将审核您的申请，并在24小时内答复，审核通过后您可登陆后台，获取专属推荐链接",
        img: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378070928142458880.png",
        w: 267,
        h: 156
    },
    {
        title: "邀请新用户",
        desc: "分享您的专属推荐链接到您的社区或其他渠道",
        img: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378071309429858304.png",
        w: 267,
        h: 156
    },
    {
        title: "获得佣金",
        desc: "新用户通过专属推荐链接注册并交易您可获得佣金，次日到账",
        img: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378071424118906880.png",
        w: 267,
        h: 156
    }
]

const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}



const faqListZh = computed(() => [
    {
        question: "什么是PinkWallet邀请返佣计划？",
        answer: "PinkWallet邀请返佣计划让您推荐好友加入PinkWallet平台，并从好友的交易手续费中赚得奖励。 作为邀请人，您最多可以获得受邀好友支付的净交易手续费的 20%作为奖励， 当您邀请的好友交易额达到一定门槛，即可一键申请 合伙人 计划，享45%起的无限层级返佣。"
    },
    {
        question: "谁可以加入PinkWallet合伙人计划？",
        answer: `1.推特 KOL、Telegram 社区管理员、Youtube 或 TikTok 视频博主、加密货币社区领袖、媒体内容创作者及其他愿意推

广PinkWallet的影响力人物（社交媒体账号拥有 500 名以上粉丝或社区拥有 100 名以上成员优先）。

2.在过去三个月 你已为PinkWallet带来超过100名注册用户。请直接联系我们进行返佣比例调整。

3.广告商、空投平台、SEO服务商、加密市场网站、加密工具网站、行业媒体网站等区块链网站。

4.营销鬼才、推广机构组织等。`
    },
    {
        question: "如何通过PinkWallet合伙人计划赚取佣金奖励？",
        answer: `成功成为PinkWallet合伙人后，您可以使用自己的推荐链接邀请好友在PinkWallet进行交易，获得被邀请人交易费用最高55%的佣金返还。您还可以创建具有不同费用折扣的特殊推荐链接，以提高邀请效率。`
    },
    {
        question: "如果我加入该计划，我可以享受多久的会员佣金？",
        answer: "只要达到对应的门槛要求，你可以获得受邀人提供的终身返佣。终身返佣——没有上限和时间限制。邀请的用户越多交易得越多，获得的返佣越高。"
    },
    {
        question: "我在PinkWallet如何获得更高等级返佣？",
        answer: "我们每月会根据合伙人所带来的交易量进行评估，如果能够到达指定等级最低交易量的门槛，那么将会获得该等级的返佣比例。"
    }]);

const benefits = [
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377745577424019456.png", text: '每周返点高额佣金' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377745683565076480.png", text: '次日返佣，终生收益' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377745790314307584.png", text: '支持加密和股市返佣' }
]

const swiperData = [
    { title: '申请成为PinkWallet合伙人', img: '' },
    { title: '完成身份认证', img: '' },
    { title: '开始邀请好友赚佣金', img: '' }
]

const categories = [
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382344347201658880.png", text: '永续合约交易' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382344520912953344.png", text: '美股证券交易' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382344778225115136.png", text: '港股证券交易' }
]

const licenses = [
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377746683847860224.png", text: '获加拿大 FINTRAC MSB & 美国 FinCEN MSB 牌照' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377746807646937088.png", text: '已成功取得欧盟 VASP 牌照' },
    { icon: "https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377746807646937088.png", text: '同步推进欧盟 CASP 牌照申请' }
]

const faqList = [
    { q: '什么是PinkWallet合伙人计划？', a: '这是一个奖励推荐计划...' },
    { q: '谁可以加入PinkWallet合伙人计划？', a: '任何有影响力的个人或社群...' },
    { q: '如何通过PinkWallet合伙人计划赚取佣金？', a: '通过邀请链接邀请用户注册交易即可返佣。' },
    { q: '如果我把人拉进计划，可以享受多久的会员佣金？', a: '返佣持续周期可长期有效...' },
    { q: '我在PinkWallet如何获得更高等级返佣？', a: '邀请更多交易用户，提高自身等级。' }
]
judgePlatform(platform, token).then((res) => {
    // info()
    fetchGetUserInfo()
    getInviteCodes()
    // FetchgetMyRebate()

});
</script>

<style lang="scss" scoped>
.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .modal-wrapper {
        width: 100vw;
        position: relative;
        position: fixed;
        bottom: 0;
        background: #FFFFFF;
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        height: px2vw(660px*2);
        text-align: center;
        color: #000;
        padding-bottom: 100px;

        .modal-container {
            display: flex;
            flex-direction: column;

            .modal-header {
                // padding: 22px 18px 48px 20px;
                padding: 22px 18px 12.5px 20px;

                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 0.5px solid rgba(255, 255, 255, .1);

                .title {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 20px;
                    letter-spacing: 0px;
                    color: #000;
                }


                .close-btn {
                    cursor: pointer;
                    width: 24px;
                    height: 24px;
                }
            }

            .modal-body {
                padding: 10.5px 18px 0 20px;
                max-height: 600px;
                overflow-y: auto;

                .sub-title {
                    text-align: left;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    color: #8A8A8A;
                    // margin-top: 6px;
                }

                .email-box {
                    background: #FBFBFB;
                    border: 1px solid #E2E2E2;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 100%;
                    color: rgba(0, 0, 0, .5);
                    border-radius: 12px;
                    margin-top: 16.5px;
                    padding: 9px 22px 8px 12px;
                    display: flex;
                    justify-content: space-between;

                    .uid {

                        display: flex;
                        align-items: center;
                    }

                    .email {
                        display: flex;
                        align-items: center;
                        margin-right: 4px;

                        img {
                            width: 34px;
                            height: 34px;
                        }
                    }
                }

                .form-group {
                    // margin-bottom: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;

                    .proof {
                        .labelX {
                            text-align: left;
                            margin: 20px 0 10px 0;
                            font-size: 14px;
                            // margin-bottom: 8px;
                            // display: inline-block;
                            color: #000;
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 16px;
                            line-height: 20px;
                            display: flex;
                            align-items: center;

                            img {
                                margin-right: 8px;
                                width: 12px;
                                height: 12px;
                            }
                        }
                    }


                    .label {
                        text-align: left;
                        margin: 20px 0 12px 0;
                        font-size: 14px;
                        // margin-bottom: 8px;
                        // display: inline-block;
                        color: #000;
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 16px;
                        line-height: 20px;
                    }

                    .required::before {
                        content: "* ";
                        color: #EF88A3;
                    }

                    .form-row {
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        // margin-bottom: 14px;

                        div {
                            flex: 1;
                            padding: 16px 22px;

                        }

                        input {
                            flex: 1;
                            background: #FBFBFB;

                            border: 1px solid #E2E2E2;

                            font-family: MiSans;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 100%;
                            color: #000;
                            border-radius: 12px;
                            padding: 16px 22px;

                            &:focus {
                                outline: none;
                                // border: none;
                                box-shadow: none;
                            }
                        }

                        input[disabled] {
                            opacity: 0.5;
                        }
                    }

                    .full {
                        margin-top: 8px;
                        flex: 1;
                        background: #212121;
                        border: 1px solid #3E3E3E;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 100%;
                        color: #000;
                        border-radius: 12px;
                        padding: 16px 22px;

                        // margin-left: 14px;
                        &:focus {
                            outline: none;
                            // border: none;
                            box-shadow: none;
                        }
                    }

                    .checkbox-group {
                        margin-top: 20px;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 24px;
                        // margin-top: 17px;
                        cursor: pointer;

                        label {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            font-size: 14px;
                            cursor: pointer;

                            input {
                                accent-color: #000;
                            }
                        }
                    }
                }
            }

            .modal-footer {
                text-align: right;
                margin: 12px 18px 48px 0;
                display: flex;
                justify-content: flex-end;

                .submit-btn {
                    width: 130px;
                    height: 49px;
                    border-radius: 12px;
                    background: #EF88A3;
                    color: #000;
                    border: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: MiSans;
                    font-weight: 500;
                    font-size: 14px;


                    &:not(:disabled) {
                        opacity: 1;
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    &:focus,
                    &:active {
                        outline: none;
                        border: none;
                        box-shadow: none;
                    }
                }
            }

            .fade-zoom-enter-active,
            .fade-zoom-leave-active {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .fade-zoom-enter-from,
            .fade-zoom-leave-to {
                opacity: 0;
                transform: scale(0.95);
            }

            .fade-zoom-enter-to,
            .fade-zoom-leave-from {
                opacity: 1;
                transform: scale(1);
            }
        }

    }

}

.partner-program {
    .section-title {
        font-size: 22px;
        text-align: center;
        margin: 40px 0 20px;
    }

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        // opacity: 0;
        // margin-bottom: 120px;
        // padding-top: -60px;

        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-weight: bold;
            color: white;
            position: relative;
            // padding: 0 16px;
            width: px2vw(228px*2);
            white-space: wrap;
            font-family: MiSans-bold;
            font-weight: 700;
            font-size: px2vw(20px*2);
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #000;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: px2vw(20px*2); // 横线宽度
                height: px2vw(2px); // 横线高度
                background-color: rgba(0, 0, 0, .2)
            }

            &::before {
                left: px2vw(-100px);
            }

            &::after {
                right: px2vw(-100px);
            }
        }
    }

    .hero {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: px2vw(44px*2);
        ;
        // max-width: 1280px;
        margin: 100px auto 0 auto;

        .hero-left {
            display: flex;
            flex-direction: column;
            align-items: center;

            .title {
                width: px2vw(218px*2);
                white-space: wrap;
                font-family: MiSans;
                font-weight: 700;
                font-size: px2vw(28px*2);

                line-height: 100%;
                color: #000;
                text-align: left;
            }

            .desc {
                width: px2vw(296px*2);
                white-space: wrap;
                font-family: MiSans;
                font-weight: 400;
                font-size: px2vw(14px*2);
                line-height: 137%;
                color: #000;
                // margin: 24px 0 10px 0;
                opacity: 0.5;
                text-align: left;
            }

            .gobe {
                margin: 12px 0 8px 0;
                cursor: pointer;
                font-family: MiSans;
                font-weight: 500;
                font-size: px2vw(14px*2);
                line-height: 150%;
                text-align: left;
                color: #EF88A3;
                display: flex;
                align-items: center;
                gap: 8px;

                img {
                    width: px2vw(24px*2);
                    height: px2vw(19px*2);
                }
            }

            .gopar {
                margin-top: px2vw(16px*2);
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 12px;

                .join-btn {
                    cursor: pointer;
                    width: px2vw(117px*2);
                    height: px2vw(39px*2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #EF88A3;
                    color: #fff;
                    border: none;
                    border-radius: 12px;
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: px2vw(14px*2);

                }

                .share {
                    width: px2vw(39px*2);
                    height: px2vw(39px*2);
                    border-radius: 8px;
                    background: #FBFBFB;

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    // img {
                    //     width: 24px;
                    //     height: 24px;
                    // }
                }
            }

        }


        .hero-right {
            img {
                width: px2vw(257px*2);
                ;
                height: px2vw(242px*2);
                ;
            }
        }
    }

    .benefits {
        max-width: 1280px;
        margin: 110px auto 0 auto;
    }

    .cards {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        margin: px2vw(28px*2) px2vw(16px*2) 0 px2vw(16px*2);
        align-items: center;
        // flex-wrap: wrap;
        gap: 9px;

        .card {
            cursor: pointer;
            // min-width: 421px;
            width: 100%;
            min-height: px2vw(114px*2);
            border-radius: 8px;
            background: #FBFBFB;

            padding: 0;
            // padding: 20px;
            flex: 1;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;

            .icon {
                width: px2vw(30px*2);
                height: px2vw(30px*2);
            }

            .text {
                font-family: MiSans;
                font-weight: 600;
                font-size: px2vw(14px*2);
                ;
                line-height: 150%;
                text-align: center;
                color: #000;
                margin-top: px2vw(9px*2);
                ;
                ;
            }
        }
    }

    .swiper-section {
        // margin: 60px 0;
        max-width: 1280px;
        margin: 140px auto 0 auto;

        .swiper {
            margin-top: 47px;

            .view_grid {
                cursor: pointer;
                // width: 884px;
                // max-width: 884px;
                // background-color: #222;
                // border-radius: px;
                // padding: px(50) px(48);
                margin: 0 auto;
                // border: 1px solid #4B4B4B;
                position: relative;
                overflow: hidden;
                transition: all 0.5s ease-in-out;
                opacity: 0;

                &.slide_right {
                    animation: slideRight 0.5s forwards;
                }

                &.slide_left {
                    animation: slideLeft 0.5s forwards;
                }

                .view_container {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    flex-direction: column;



                    .dots {
                        margin-top: 32px;
                        display: flex;
                        gap: 10px;

                        .dot {
                            height: 10px;
                            border-radius: 12px;
                            background: rgba(0, 0, 0, .5);
                            transition: all 0.3s ease;
                            width: 10px;
                        }

                        .dot.active {
                            background: #000;
                            width: 54px;
                        }
                    }

                    .left_view {
                        // width: 300px;
                        // background-color: #222;
                        // border-radius: 16px;
                        // padding: px(14) 0;
                        margin-top: 30px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        flex-direction: column;

                        .text {
                            text-align: center;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;

                            .title {
                                width: 430px;
                                white-space: wrap;
                                font-family: MiSans;
                                font-weight: 700;
                                font-size: px2vw(20px*2);
                                line-height: 100%;
                                color: #000;
                            }

                            .desc {
                                text-align: center;
                                width: 300px;
                                white-space: wrap;
                                margin-top: 14px;
                                font-family: MiSans;
                                font-weight: 400;
                                font-size: px2vw(12px*2);
                                ;
                                line-height: 137%;
                                color: rgba(0, 0, 0, .7);
                            }
                        }

                        .carousel-controls {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-top: 28px;

                            .arrows {
                                display: flex;
                                gap: 20px;
                                align-items: flex-end;

                                .arrow-btn {
                                    cursor: pointer;
                                    transition: background 0.3s;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 52px;
                                    height: 52px;
                                    border-radius: 50%;
                                    background: #FF95B112;
                                    // border: 1.13px solid #363636;

                                    img {
                                        width: 16px;
                                        height: 11px;
                                    }
                                }

                                .rotate {
                                    transform: rotate(180deg);
                                }

                                .arrow-btn:hover {
                                    background: rgba(255, 255, 255, 0.1);
                                }
                            }



                        }



                    }

                    .right_view {
                        // width: 324px;
                        rotate: 1.95 deg;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                }

            }

            @keyframes slideRight {
                0% {
                    transform: translateX(100%);
                    opacity: 0;
                }

                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideLeft {
                0% {
                    transform: translateX(-100%);
                    opacity: 0;
                }

                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        }

        .my-swiper {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;

            .slide-content {
                text-align: center;

                .slide-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                }

                .slide-img {
                    width: 100%;
                    max-width: 300px;
                    border-radius: 12px;
                }
            }
        }
    }

    .categories {
        margin-top: px2vw(80px*2);
        padding: px2vw(50px*2) 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        // height: 495px;

        background: #FF95B2;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;
                width: px2vw(228px*2);
                white-space: wrap;
                font-family: MiSans-bold;
                font-weight: 700;
                font-size: px2vw(20px*2);
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: px2vw(20px*2); // 横线宽度
                    height: px2vw(2px); // 横线高度
                    background-color: rgba(255, 255, 255, .2)
                }

                &::before {
                    left: px2vw(-100px);
                }

                &::after {
                    right: px2vw(-100px);
                }
            }
        }

        // .card {
        //     background: #0A0A0A;
        //     border: 1.13px solid #0A0A0A
        // }


        .cards2 {
            display: flex;
            width: 100%;
            justify-content: space-between;
            margin-top: px2vw(18px*2);
            padding: 0 px2vw(16px*2);
            align-items: center;
            // flex-wrap: wrap;
            gap: 4px;

            .card {
                cursor: pointer;
                // min-width: 421px;
                width: 100%;
                min-height: px2vw(134px*2);
                border-radius: 1ch;
                background: #FBFBFB;
                flex: 1;
                padding: 0;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;

                .icon {
                    width: px2vw(30px*2);
                    height: px2vw(30px*2);
                }

                .text {
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: px2vw(14px*2);
                    ;
                    line-height: 150%;
                    text-align: center;
                    color: #000;
                    margin-top: px2vw(9px*2);
                    ;
                    ;
                }
            }
        }

    }

    .compliance {
        // max-width: 1213px;
        // margin: 183px auto 0 auto;
        margin-top: px2vw(80px*2);

        .cards-compliance {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-top: 48px;

            .card-compliance {
                width: 421px;
                height: 167px;
                border-radius: 18.13px;
                display: flex;
                align-items: center;
                flex-direction: column;
                text-align: center;
                position: relative;

                .img {
                    position: relative;

                    .checked {
                        position: absolute;
                        width: 26px;
                        height: 26px;
                        right: -8px;
                        bottom: 4px;
                    }
                }

                .icon {
                    height: 59px;
                }

                .text {
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: px2vw(24px);
                    line-height: 150%;
                    text-align: center;
                    color: #000;
                    margin-top: 3px;
                }

                // 强制第三个元素换行独占
                &:nth-child(3) {
                    flex-basis: 100%;
                    display: flex;
                    justify-content: center;
                }
            }
        }

    }

    .faq {
        margin: px2vw(80px*2) auto 0 auto;
        // max-width: 1280px;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-weight: bold;
                color: #000;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: px2vw(20px*2);
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: px2vw(20px*2); // 横线宽度
                    height: px2vw(2px); // 横线高度
                    background-color: rgba(0, 0, 0, .2)
                }

                &::before {
                    left: px2vw(-100px);
                }

                &::after {
                    right: px2vw(-100px);
                }
            }
        }

        .faq-item {
            border-bottom: 1px solid rgba(0, 0, 0, .05);
            padding: 40px 0 32px 0;
            margin: 0 px2vw(16px*2);

        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;

            span {

                font-family: MiSans;
                font-weight: 600;
                font-size: px2vw(16px*2);
                line-height: px2vw(21px*2);
                color: #000;
            }
        }



        .arrow-icon {
            width: px2vw(30px*2);
            ;
            height: px2vw(30px*2);
            ;
            transition: transform 0.3s ease;
        }

        .arrow-icon.rotated {
            transform: rotate(180deg);
        }

        .faq-answer {
            overflow: hidden;
            padding-top: 24px;
            font-family: MiSans;
            font-weight: 400;
            // line-height: 100%;
            text-align: left;
            font-size: px2vw(14px*2);
            line-height: px2vw(21px*2);
            color: rgba(0, 0, 0, .5);


        }

        /* 动效过渡 */
        .faq-slide-enter-active,
        .faq-slide-leave-active {
            transition: all 0.3s ease;
        }

        .faq-slide-enter-from,
        .faq-slide-leave-to {
            opacity: 0;
            max-height: 0;
        }

        .faq-slide-enter-to,
        .faq-slide-leave-from {
            opacity: 1;
            max-height: 200px;
        }

        // .faq-list {
        //     margin-top: 8px;
        //     .faq-item {
        //         background-color: #111;
        //         border-radius: 10px;
        //         padding: 16px;
        //         margin-bottom: 12px;

        //         .question {
        //             font-size: 14px;
        //             color: #fff;
        //         }
        //     }
        // }
    }

    .footer {
        margin-top: px2vw(80px*2);
        text-align: center;
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382308354167758848.png");
        background-size: 100% 100%;
        height: px2vw(200px*2);
        color: #000;

        .inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 21px;
        }

        span {
            font-family: MiSans;
            font-weight: 700;
            line-height: px2vw(29px*2);

            font-size: px2vw(22px*2);
            text-align: center;
            text-transform: capitalize;
        }

        .btns {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }

        .contact-btn {
            border: 1px solid #EF88A3;
            color: #EF88A3;
            font-family: MiSans;
            font-weight: 500;
            font-size: px2vw(14px*2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: px2vw(117px*2);
            height: px2vw(39px*2);
            border-radius: 8px;
            cursor: pointer;
        }

        .login-btn {

            background-color: #EF88A3;
            border: none;
            // padding: 12px 30px;
            // border-radius: 20px;
            font-family: MiSans;
            font-weight: 500;
            font-size: px2vw(14px*2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: px2vw(117px*2);
            height: px2vw(39px*2);
            border-radius: 8px;
            color: #fff;
            cursor: pointer;
        }
    }
}
</style>