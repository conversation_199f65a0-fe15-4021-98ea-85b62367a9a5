<template>
  <d2-container class="page">
    <el-form
      :inline="false"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="旧密码">
        <el-input
          v-model="formInline.oldPassword"
          show-password
          placeholder="请输入旧密码"
        ></el-input>
      </el-form-item>
      <el-form-item label="新密码">
        <el-input
          v-model="formInline.newPassword"
          show-password
          placeholder="请输入新密码"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getupdateUserPassWord"
          >修改</el-button
        >
        <!-- <el-button type="primary" @click="orderexport()">导出</el-button> -->
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'modify',
  data () {
    return {
      formInline: {
        oldPassword: '',
        newPassword: ''
      }
    }
  },
  methods: {
    async getupdateUserPassWord (page) {
      await this.$api.updateUserPassWord({
        oldPassword: this.formInline.oldPassword,
        newPassword: this.formInline.newPassword
      })
      this.$notify({
        title: '成功',
        message: '修改密码成功',
        type: 'success'
      })
    }
  }
}
</script>
