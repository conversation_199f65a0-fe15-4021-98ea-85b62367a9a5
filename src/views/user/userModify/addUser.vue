<template>
  <d2-container class="page">
    <el-form
      :inline="false"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item label="用户名称">
        <el-input
          v-model="formInline.userName"
          placeholder="请输入用户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="真实姓名">
        <el-input
          v-model="formInline.realName"
          placeholder="请输入真实姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input
          v-model="formInline.email"
          placeholder="请输入邮箱"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input
          v-model="formInline.mobPhone"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getregistryAdminUser">添加</el-button>
        <!-- <el-button type="primary" @click="orderexport()">导出</el-button> -->
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'adduser',
  data () {
    return {
      formInline: {
        userName: '',
        realName: '',
        email: '',
        mobPhone: ''
      }
    }
  },
  mounted () {},
  methods: {
    async getregistryAdminUser (page) {
      await this.$api.registryAdminUser({
        userName: this.formInline.userName,
        realName: this.formInline.realName,
        email: this.formInline.email,
        mobPhone: this.formInline.mobPhone
      })
      this.$notify({
        title: '成功',
        message: '添加用户成功',
        type: 'success'
      })
      this.formInline = {
        userName: '',
        realName: '',
        email: '',
        mobPhone: ''
      }
    }
  }
}
</script>
