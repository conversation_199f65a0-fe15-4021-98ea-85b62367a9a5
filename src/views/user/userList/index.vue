<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px">
      <el-form-item label="真实姓名">
        <el-input v-model="formInline.name" placeholder="请输入真实姓名"></el-input>
      </el-form-item>
      <el-form-item label="在职状态">
        <el-select  v-model="formInline.employStatus" placeholder="订单状态">
          <el-option label="正常" value="NORMAL"></el-option>
          <el-option label="离职" value="DIMISSION"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button type="primary" @click="reset()">重置</el-button>
        <el-button type="primary" @click="orderexport()">导出</el-button>
        <el-button type="primary" @click="nav_addUser()">新增用户</el-button>
        <!-- <el-button type="primary" @click="reset_password()">重置密码</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
      show-header border style="width: 100%">
      <el-table-column fixed prop="id" label="id" type="selection" align="center"></el-table-column>

      <el-table-column prop="id" label="id" align="center"></el-table-column>
      <el-table-column prop="username" label="用户名" align="center"></el-table-column>
      <!--      <el-table-column prop="pic" label="商品图片" align="center">-->
      <!--        <template scope="scope">-->
      <!--          <div style="width:100%;">-->
      <!--            <el-image style="width: 100px; height: 100px" :src="scope.row.pic"-->
      <!--                      :preview-src-list="scope.row.srcList">-->
      <!--            </el-image>-->
      <!--          </div>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column prop="realName" label="真实姓名" align="center"></el-table-column>
      <el-table-column prop="email" label="邮箱" align="center"></el-table-column>
      <el-table-column prop="mobphone" label="手机号" align="center"></el-table-column>
      <el-table-column prop="deleted" label="是否删除" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.deleted == 0" type="success">未删除</el-tag>
          <el-tag v-if="scope.row.deleted == 1" type="danger">已删除</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="employStatus" label="是否在职" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.employStatus == 'NORMAL'" type="success">正常
          </el-tag>
          <el-tag v-if="scope.row.employStatus == 'DIMISSION'" type="danger">离职
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updateAt" label="修改时间" align="center"></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="nav_details(scope.row)">查看
          </el-button>
          <el-button type="text"  @click="reset_password(scope.row)">重置密码
          </el-button>
          <el-button type="text"  @click="open(scope.row)">权限管理
          </el-button>
          <el-button type="text" v-if="scope.row.employStatus == 'NORMAL'" @click="dismiss(scope.row)">离职关闭账号</el-button>
          <el-button type="text" v-if="scope.row.employStatus == 'DIMISSION'"  @click="openUuser(scope.row)">入职开启账号</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="10"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="权限管理" :visible.sync="isAuthority" width="50%">
      <div>
        <el-input placeholder="请输入权限code" v-model="permissionCode">
          <template slot="prepend">搜索</template>
        </el-input>
        <p>用户当前权限</p>
        <div>
          <el-tag class="tag" v-for="tag in userCurrentPermissions" :key="tag.permissionDesc" closable :type="tag.type"
            @close="tagClose(tag)">
            {{ tag.permissionDesc }}({{ tag.permissionCode }})
          </el-tag>
        </div>
      </div>
      <div>
        <p>可添加的权限</p>
        <div>
          <template v-for="tag in permissionsCanBeAdded">
            <el-tag class="tag" :key="tag.permissionDesc" @click="checkTag(tag)" v-if="tag.deleted === 0">
              {{ tag.permissionDesc }}({{ tag.permissionCode }})
            </el-tag>
          </template>
        </div>
        <!--        <div-->
        <!--          class=""-->
        <!--          style="-->
        <!--            display: flex;-->
        <!--            justify-content: center;-->
        <!--            background-color: #ffffff;-->
        <!--          "-->
        <!--        >-->
        <!--          <el-pagination-->
        <!--            background-->
        <!--            layout="prev, pager, next"-->
        <!--            :total="power"-->
        <!--            :page-size="10"-->
        <!--            style="padding: 20px; background-color: #ffffff"-->
        <!--            @current-change="powerxuanze"-->
        <!--            @size-change="powerxuanze"-->
        <!--          >-->
        <!--          </el-pagination>-->
        <!--        </div>-->
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAuthority = false">取 消</el-button>
        <el-button type="primary" @click="isAuthority = false">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
  export default {
    name: 'user_list',
    data() {
      return {
        permissionCode: '',
        tableData: [],
        srcList: [],
        total: 1,
        formInline: {
          orderNo: '',
          name: '',
          phone: '',
          zfbNo: '',
          logisticsNo: '',
          date: '',
          status: '',
          employStatus:'NORMAL'
        },
        headers: {
          authorization: ''
        },
        isAuthority: false,
        form: {
          code: '',
          desc: ''
        },
        formLabelWidth: '120px',
        tags: [],
        userCurrentPermissions: [],
        allTags: [],
        permissionsCanBeAdded: [],
        userId: '',
        power: 1, // 权限分页
        currentPageNum: 0
      }
    },
    watch: {
      isAuthority(val) {
        if (!val) {
          this.permissionCode = ''
          this.permissionsCanBeAdded = this.userCurrentPermissions = []
        }
      },
      permissionCode(val) {
        console.log(val)
        this.permissionCode = val.toUpperCase()
        if (val) {
          this.userCurrentPermissions = this.tags.filter(item => item.permissionCode.includes(val))
          this.permissionsCanBeAdded = this.allTags.filter(
            item => !this.userCurrentPermissions.some(tag => tag.permissionCode === item.permissionCode) && item
            .permissionCode.includes(val)
          )
        } else {
          this.userCurrentPermissions = this.tags
          this.permissionsCanBeAdded = this.allTags.filter(
            item => !this.tags.some(tag => tag.permissionCode === item.permissionCode)
          )
        }
      }
    },
    mounted() {
      this.currentPageNum = 1
      this.getSelete()
    },
    methods: {
      handleClick(row) {
        console.log(row)
      },
      search() {
        this.currentPageNum = 1
        this.getSelete()
      },
      reset() {
        this.currentPageNum = 1
        this.formInline = {
          orderNo: '',
          name: '',
          phone: '',
          zfbNo: '',
          logisticsNo: '',
          date: '',
          status: '',
          employStatus:''
        }
        this.getSelete()
      },
      async getSelete() {
        const res = await this.$api.listPageAdminUser({
          pageNum: this.currentPageNum,
          realName: this.formInline.name,
          pageSize: 10,
          employStatus:this.formInline.employStatus
        })
        this.tableData = res.result.list
        // this.tableData.forEach(item => {
        // item.srcList = [item.pic]
        // })
        console.log(this.tableData)
        this.total = res.result.totalCount
      },
      async getreser(e) {
        const res = await this.$api.resetAdminUserPassWord({
          userId: e
        })
        this.$message({
          type: 'success',
          message: '重置成功!'
        })
        this.total = res.result.totalCount
      },
      async userPermission(uid) {
        const res = await this.$api.showAdminUserPermission({
          userId: uid
        })
        if (res.status.code === 0) {
          if (this.permissionCode) {
            this.userCurrentPermissions = res.result.filter(item => item.permissionCode.includes(this.permissionCode))
            this.permissionsCanBeAdded = this.allTags.filter(
              item => !this.userCurrentPermissions.some(tag => tag.permissionCode === item.permissionCode) && item
              .permissionCode.includes(this.permissionCode)
            )
          } else {
            this.userCurrentPermissions = this.tags = res.result
            this.permissionsCanBeAdded = this.allTags.filter(
              item => !this.userCurrentPermissions.some(tag => tag.permissionCode === item.permissionCode)
            )
          }
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async adminPermission() {
        const res = await this.$api.listPageAdminPermission({
          pageSize: 1000,
          pageNum: 1
        })
        if (res.status.code === 0) {
          this.allTags = res.result.list
          this.permissionsCanBeAdded = this.allTags.filter(
            item => !this.tags.some(tag => tag.permissionCode === item.permissionCode)
          )
          this.power = res.result.totalCount
        } else {
          this.$message.error(res.status.msg)
        }
      },
      selete() {
        console.log(this.formInline)
      },
      open(item) {
        this.isAuthority = true
        this.userId = item.id
        this.userPermission(item.id)
        this.adminPermission(1)
      },
      submit() {
        this.$message.error('成功')
      },
      xuanze(val) {
        this.currentPageNum = val
        this.getSelete()
      },
      // 权限分页
      powerxuanze(val) {
        this.adminPermission(val)
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
        console.log(this.multipleSelection)
      },
      orderexport() {},
      // 新增用户
      nav_addUser() {
        this.$router.push({
          name: 'adduser'
        })
      },
      // 重置密码
      reset_password(e) {
        console.log(e)
        this.$confirm('此操作将重置该用户密码, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            this.getreser(e.id)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消重置'
            })
          })
      },

      nav_details(item) {
        console.log(item)
        this.$router.push({
          name: 'user_details',
          query: {
            type: 'item',
            id: item.id
          }
        })
      },
      checkTag(item) {
        console.log(item)
        this.addAdminUserPermission(item.permissionCode)
      },
      async addAdminUserPermission(code) {
        let res =  await this.$api.addAdminUserPermission({
          userId: this.userId,
          permissionCode: code
        })
        if(res.status.code == 0){
          this.$message.success('权限添加成功')
          this.userPermission(this.userId)
        }
      },
      async removeAdminUserPermission(code) {
        let res = await this.$api.removeAdminUserPermission({
          userId: this.userId,
          permissionCode: code
        })
        if(res.status.code == 0){
          await this.userPermission(this.userId)
        }
      },
      tagClose(item) {
        this.removeAdminUserPermission(item.permissionCode)
      },
      async dismiss(val) {
        this.$confirm('此操作将关闭该用户账号, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(async () => {
            await this.$api.dismissClose({
              userId: val.id,
              employStatus: 'DIMISSION'
            })
            await this.getSelete()
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消重置'
            })
          })
      },
      async openUuser(val) {
        this.$confirm('此操作将开启该用户账号, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(async () => {
            await this.$api.dismissClose({
              userId: val.id,
              employStatus: 'NORMAL'
            })
            await this.getSelete()
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消重置'
            })
          })
      }
    }
  }
</script>
<style>
  .tag {
    margin: 0px 15px 15px 0px;
    cursor: pointer !important;
  }
</style>
