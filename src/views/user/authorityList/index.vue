<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" style="background-color:#FFFFFF;padding:20px;">
      <el-form-item label="权限码">
        <el-input v-model="formInline.code" placeholder="请输入权限码">
        </el-input>
        </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">搜索</el-button>
        <el-button type="primary" @click="reset()">重置</el-button>
        <el-button type="primary" @click="openDialog(1,'')">添加权限</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" @selection-change="handleSelectionChange" tooltip-effect="dark"
      show-header border style="width: 100%;" >
      <el-table-column fixed prop="id" label="id" align="center"></el-table-column>
      <el-table-column prop="permissionDesc" label="权限描述" align="center"></el-table-column>
      <el-table-column prop="permissionCode" label="权限码" align="center"></el-table-column>
      <el-table-column prop="deleted" label="是否删除" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.deleted==0" type="success">未删除</el-tag>
          <el-tag v-if="scope.row.deleted==1" type="danger">已删除</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="updateAt" label="修改时间" align="center"></el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="removeOpen(scope.row)">删除权限</el-button>
          <el-button style="margin-left: 0;" type="text"  @click="openDialog(2,scope.row)">修改权限</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display:flex;justify-content: center;background-color:#FFFFFF;">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding:20px;background-color:#FFFFFF;" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
    <el-dialog title="添加权限" :visible.sync="isDialog" width="35%">
      <el-form :model="form">
        <el-form-item label="权限码" :label-width="formLabelWidth">
          <el-input v-model="form.code"  :disabled="true?submitType=='update':submitType=='add'" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="权限描述" :label-width="formLabelWidth">
          <el-input v-model="form.desc" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" v-if="submitType=='add'" @click="add()">确 定</el-button>
         <el-button type="primary" v-if="submitType=='update'" @click="updateAdmin()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
export default {
  name: 'authority_list',
  data () {
    return {
      tableData: [],
      total: 1,
      formInline: {
        status: ''
      },
      headers: {
        authorization: ''
      },
      form: {
        code: '',
        desc: ''
      },
      formLabelWidth: '120px',
      isDialog: false,
      submitType: '',
      pageNum:1
    }
  },
  mounted () {
    console.log(util.cookies.get('token'))
    this.getSelete()
  },
  methods: {
    search () {
      this.pageNum = 1
      this.getSelete()
    },
    reset () {
      this.formInline.code = ''
      this.pageNum = 1
      this.getSelete()
    },
    async getSelete () {
      const res = await this.$api.listPageAdminPermission({
        permissionCode: this.formInline.code,
        pageNum: this.pageNum,
        pageSize: 15
      })
      if (res.status.code === 0) {
        this.tableData = res.result.list
        console.log(this.tableData)
        this.total = res.result.totalCount
      } else {
        this.$message.error(res.status.msg)
      }
    },
    selete () {
      console.log(this.formInline)
    },
    async add () {
      await this.$api.createAdminPermission({
        permissionCode: this.form.code,
        permissionDesc: this.form.desc
      })
      this.getSelete()
      this.isDialog = false
      this.form =  {
        code: '',
        desc: ''
      }
    },
    openDialog (index, item) {
      this.isDialog = true
      if (index === 1) {
        this.submitType = 'add'
        console.log('添加')
      } else {
        this.form.code = item.permissionCode
        this.form.desc = item.permissionDesc
        this.isDialog = true
        this.submitType = 'update'
        console.log('修改')
      }
    },
    async updateAdmin (permissionCode, permissionDesc) {
      await this.$api.updateAdminPermission({
        permissionCode: this.form.code,
        permissionDesc: this.form.desc
      })
      this.getSelete()
      this.isDialog = false
     this.form =  {
       code: '',
       desc: ''
     }
    },
    removeOpen (item) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.remove(item.permissionCode)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async remove (code) {
      await this.$api.removeAdminPermission({
        permissionCode: code
      })
      this.getSelete()
      this.$message({
        type: 'success',
        message: '删除成功!'
      })
    },
    xuanze (val) {
      this.pageNum = val
      this.getSelete(val)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },
    orderexport () {

    },
    nav_details (item) {
      console.log(item)
      this.$router.push({
        name: 'orderdetails',
        query: {
          orderNo: item.orderNo
        }
      })
    }
  }
}
</script>
