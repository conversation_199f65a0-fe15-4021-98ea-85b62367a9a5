<template>
  <d2-container class="page" v-loading="loading">
    <common-query :query-schema="querySchema" :data="query" :showCreation="false" @onSubmit="onSubmit"
      @onReset="onReset">
    </common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="open()">新增小组</el-button>
      </template>
      <template #action="scope">

        <el-button type="text" size="mini" @click="Enexport(scope.row)">导出各组员数据</el-button>

        <el-button type="text" size="mini" @click="open(scope.row)">修改</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <el-dialog :title="`${isEdit ? '修改' : '新增'}小组`" :visible.sync="dialogVisible" width="40%"
      :before-close="handleClose">
      <common-form :submit="submitAdd" :data="formData" :schema="formSchema" label-width="120px">
        <template #list="scope">
          <div class="marginLeft-150">
            <el-row :gutter="24" v-for="(item, index) in peersList">
              <el-col :span="3">
                组员{{ index + 1 }}
              </el-col>
              <el-col :span="18">
                <el-form label-position="right" size="mini" label-width="120px" :model="item">
                  <el-form-item label="名称:">
                    <el-input v-model="item.peerName" class="width300"></el-input>
                  </el-form-item>
                  <el-form-item label="钱包地址:">
                    <el-input v-model="item.peerAddress" class="width300"></el-input>
                  </el-form-item>
                  <el-form-item label="图片提成比例:">
                    <el-input type="number-input" v-model="item.commissionRatio"
                      class="width100"></el-input>&nbsp;&nbsp;%
                  </el-form-item>
                  <el-form-item label="开杠提成比例:">
                    <el-input type="number-input" v-model="item.commissionRatioOpenBar"
                      class="width100"></el-input>&nbsp;&nbsp;%
                  </el-form-item>
                </el-form>
              </el-col>
              <el-col :span="3" class="close">
                <img @click="delItem(index, item)"
                  src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240829/3abcfc5cf533e6b5307b5282a7c7a436_64x64.png"
                  alt="" srcset="" />
              </el-col>
            </el-row>
            <div class="add">
              <img @click="addItem"
                src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240829/bb23f4319467640bd49930533c70fe91_64x64.png"
                alt="" srcset="" />
            </div>
            <div style="text-align: center;
    color: brown;">
              最多可添加50个成员
            </div>
          </div>
        </template>
      </common-form>
    </el-dialog>

  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonQuery from '@/components/CommonQuery_h'
import CommonForm from '@/components/CommonForm'
import {
  downloadBlob
} from '@/utils/helper'
export default {
  components: {
    CommonTable,
    CommonQuery,
    CommonForm
  },
  data() {
    return {
      loading:false,
      dialogVisible: false,
      page: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0
      },
      tableData: [],
      tableSchema: [ // 表格架构
        {
          label: 'id',
          field: 'id',
          width: '120px'
        },
        {
          label: '小组名称',
          field: 'groupName',
          width: '150px'
        },
        {
          label: '创建时间',
          field: 'createTime',
          width: '150px'
        },
        {
          label: '组长',
          field: 'leaderName',
          width: '150px'
        },
        {
          label: '组员人数',
          field: 'groupCount',
          width: '150px'
        },
        {
          label: '组员',
          field: 'groupPeers',
          width: '100px'
        },
        {
          label: '小组当月累计交易额（图片）',
          field: 'imageAmount',
          width: '100px'
        },
        {
          label: '小组当月累计交易额（开杠）',
          field: 'openBarAmount',
          width: '100px'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '100px',
        }
      ],
      query: {
        startTime: [],
        // endTime: '',
      },
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '组名',
          field: 'groupName'
        },
        {
          type: 'input',
          label: '组长地址',
          field: 'leaderAddress'
        },
        {
          type: 'input',
          label: '组长名字',
          field: 'leaderName'
        },
        {
          type: 'datetimerange',
          label: '时间段：',
          placeholder: '请输入时间段',
          field: 'startTime',
          // field2: 'endTime',
        },
      ],
      searchVal: {},
      options: [],
      leave: '',
      ntfId: '',
      formData: {

      },
      formSchema: [{
        type: 'input',
        label: '小组名称：',
        placeholder: '请输入小组名称',
        field: 'groupName',
        rules: [{
          required: true,
          message: '请输入小组名称',
          trigger: 'blur'
        }]
      }, {
        type: 'input',
        label: '组长名称：',
        placeholder: '请输入组长名称',
        field: 'leaderName',
        rules: [{
          required: true,
          message: '请输入组长名称',
          trigger: 'blur'
        }]
      }, {
        type: 'input',
        label: '组长钱包地址：',
        placeholder: '请输入组长钱包地址',
        field: 'leaderAddress',
        rules: [{
          required: true,
          message: '请输入组长钱包地址',
          trigger: 'blur'
        }]
      },
      {
        slot: 'list',
      },
      {
        type: 'action',
        exclude: ['back', 'reset']
      },
      ],
      peersList: [{
        commissionRatio: 0.5,
        commissionRatioOpenBar: 10,
        peerAddress: "",
        id: "",
        peerName: ""
      }],
      removeIds: [],
      isEdit: false
    }
  },
  mounted() {
    const { formattedFirstDay, currentDate } = this.getFormattedDates();
    this.query.startTime = [formattedFirstDay, currentDate]
    // this.query.startTime = formattedFirstDay;
    // this.query.endTime = currentDate;
    this.searchVal.startTime = formattedFirstDay
    this.searchVal.endTime = currentDate
    console.log(this.query.startTime, '时间');
    this.getList()

  },
  methods: {
    getFormattedDates() {
      const now = new Date();

      // 当前时间（本地时间格式）
      const currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;

      // 当前月的1号00:00:00（本地时间格式）
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
      const formattedFirstDay = `${firstDayOfMonth.getFullYear()}-${String(firstDayOfMonth.getMonth() + 1).padStart(2, '0')}-01 00:00:00`;

      return { formattedFirstDay, currentDate };
    },
    async Enexport(e) {


      // const res = await this.$api.ExportCloseRecords({
      //   ...e
      // })
      // if (res.retCode === 500) {
      //   this.$message.error(res.retMsg)
      //   this.getList()
      // } else if (res.type === 'application/json') {
      //   // blob 转 JSON
      //   const enc = new TextDecoder('utf-8')
      //   res.arrayBuffer().then((buffer) => {
      //     const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      //     this.$message.error(data.status?.msg)
      //   })
      // } else {
      //   downloadBlob(res, '当前平仓' + Date.now() + '.csv')
      //   this.$message.success('导出成功')
      //   this.getList()
      // }

      const res = await this.$api.brokerExport({
        startTime: this.query.startTime[0],
        endTime: this.query.startTime[1],
        groupId: e.id,
        trade: 1
      })


      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '各组员数据' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }

      // if (res.type === 'application/json') {
      //   // blob 转 JSON
      //   const enc = new TextDecoder('utf-8')
      //   res.arrayBuffer().then(buffer => {
      //     const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      //     this.$message.error(data.status?.msg)
      //   })
      // } else {
      //   downloadBlob(res, '各组员数据')
      //   this.$message.success('导出成功')
      // }
    },
    async getList() {
      this.loading = true
      console.log(this.searchVal, '条件');
      if (this.searchVal.undefined) {
        this.searchVal.endTime = this.searchVal.undefined
        delete this.searchVal.undefined

      }
      const {
        result,
        status
      } = await this.$api.brokerGroupList({
        ...this.searchVal,
        pageNum: this.page.pageNum,
        pageSize: 20
      })
      if (status.code === 0) {
        this.loading = false
        this.tableData = result.list
        this.page.totalCount = result.totalCount
      } else {
        this.loading = false
      }
    },
    async rove(type, nftId) {
      const {
        result,
        status
      } = await this.$api.approve({
        approveType: type, // 审批类型 0拒绝 1通过
        nftId: nftId // "bigverse 唯一地址"
      })
      if (status.code === 0) {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
        this.getList()
      }
    },
    async levelClick(nftId) { // 等级弹窗
      this.dialogVisible = true
      this.ntfId = nftId
    },
    async leaveList() { // 获取等级
      const {
        result,
        status
      } = await this.$api.levelConfig({
        pageNum: 1,
        pageSize: 10
      })
      if (status.code === 0) {
        result.list.forEach(item => {
          this.querySchema[1].options.push({
            value: item.levelOrder,
            label: item.levelName
          })
          this.options.push({
            value: item.levelOrder,
            label: item.levelName
          })

        })
      }
    },
    async tiaozheng() { // 确认调整等级
      const {
        result,
        status
      } = await this.$api.adjustingLevels({
        level: this.leave,
        nftId: this.ntfId
      })
      if (status.code === 0) {
        this.$message({
          message: '已调整',
          type: 'success'
        })
        this.dialogVisible = false
        this.getList()
      }
    },
    onSubmit(val) {
      console.log(val, '查询');
      this.searchVal = val
      this.getList()
    },
    onReset() {
      this.searchVal = {}
    },
    // 分页切换
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    currentChangeSize(val) {
      this.page.pageSize = val
      this.getList()
    },
    addItem() {
      this.peersList.push({
        commissionRatio: 0.5,
        commissionRatioOpenBar: 10,
        peerAddress: "",
        id: "",
        peerName: ""
      })
    },
    /**
     * 删除帮我写一个删除，当item.id有值就需要记录下来
     */
    delItem(index, item) {
      if (item.id) {
        this.removeIds.push(item.id)
      }
      this.peersList.splice(index, 1)
    },
    handleClose() {
      this.dialogVisible = false
    },
    async submitAdd() {
      let errorNum = 0
      let peersListNew = JSON.parse(JSON.stringify(this.peersList))
      if (peersListNew) {
        peersListNew.forEach((item, index) => {
          if (item.commissionRatio < 2 && item.commissionRatioOpenBar < 21) {
            item.commissionRatio = item.commissionRatio / 100
            item.commissionRatioOpenBar = item.commissionRatioOpenBar / 100
          } else {
            setTimeout(() => {
              this.$message.error(`第${index + 1}个组员比例填写错误`)
            }, 10);
            errorNum++
          }
        })
        if (errorNum > 0) {
          return false
        }
      }
      let data = {
        ...this.formData,
        peersJson: JSON.stringify(peersListNew),
        operType: this.isEdit ? 1 : 0,
        removePeersIdsJson: JSON.stringify(this.removeIds),
        peers: null
      }
      console.log(data)
      let res = await this.$api.brokerGroupAddEdit(data)
      if (res.status.code == 0) {
        this.dialogVisible = false
        this.defaultData()
        this.$message({
          message: '操作成功',
          type: 'success'
        })
      }
    },
    async open(item) {
      if (item) {
        this.isEdit = true
        let res = await this.$api.brokerGroupInfo({
          id: item.id
        })
        if (res.status.code == 0) {
          if (res.result.peers) {
            res.result.peers.forEach((item) => {
              item.commissionRatio = item.commissionRatio * 100
              item.commissionRatioOpenBar = item.commissionRatioOpenBar * 100
            })
          }
          console.log(res.result)
          this.formData = res.result
          this.peersList = res.result.peers
        }
      } else {
        this.defaultData()
        this.isEdit = false
      }
      this.dialogVisible = true
    },
    defaultData() {
      this.formData = []
      this.peersList = [{
        commissionRatio: 0.5,
        commissionRatioOpenBar: 10,
        peerAddress: "",
        id: "",
        peerName: ""
      }],
        this.removeIds = []
    }
  }
}
</script>

<style lang="scss" scoped>
.close {
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  height: 170px;

  img {
    width: 36px;
    cursor: pointer;
  }
}

.marginLeft-150 {
  margin-left: -100px;
}

.add {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 50px;
    cursor: pointer;
  }
}

.width300 {
  width: 300px;
}

.width100 {
  width: 100px;
}
</style>
