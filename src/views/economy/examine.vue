<template>
  <d2-container class="page">
    <common-query
      :query-schema="querySchema"
      :data="query"
      :showCreation="false"
      @onSubmit="onSubmit"
      @onReset="onReset"
    >
    </common-query>
    <common-table
      :table-schema="tableSchema"
      :showIndex="false"
      :table-data="tableData"
    >
    </common-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.totalCount"
        :page-size="page.pageSize"
        :current-page="page.pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange"
        @size-change="currentChangeSize"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonQuery from '@/components/CommonQuery_h'
export default {
  components: {
    CommonTable,
    CommonQuery
  },
  data () {
    return {
      page: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0
      },
      tableData: [],
      tableSchema: [ // 表格架构

        {
          label: '用户昵称',
          field: 'name',
          width: '150px'
        },
        {
          label: '用户con add',
          field: 'conAdd',
          width: '200px'

        },
        {
          label: '自动考核时间',
          field: 'assessmentTime',
          width: '100px'
        },
        {
          label: '考核后等级',
          field: 'checkedLevel',
          width: '100px'
        },
        {
          label: '上周周期的等级',
          field: 'currentCycle',
          width: '100px'
        },
        {
          label: '等级上升/变化',
          field: 'levelUpOrDown',
          width: '180px',
          type: 'tag',
          tagMap: {
            0: {
              label: '不变'
            },
            1: {
              label: '上升'

            },
            '-1': {
              label: '下降',
              tagType: 'danger'
            }
          }
        },
        {
          label: '周期内消费额',
          field: 'consumptionThisWeek',
          width: '150px'
        }

      ],
      query: {
        dutyStatus: 'DOING'
      },
      querySchema: [ // 搜索组件架构
        {
          type: 'datetimerange',
          label: '自动考核时间',
          field: 'automaticAssessmentStartTime',
          field2: 'automaticAssessmentEndTime'
        },
        {
          type: 'input',
          label: '用户昵称',
          placeholder: '请选择用户昵称',
          field: 'userName'
        },
        {
          type: 'input',
          label: '用户con add',
          placeholder: '请输入用户con add',
          field: 'conAdd'
        },
        {
          type: 'select',
          label: '等级变化类型',
          placeholder: '请选择等级变化类型',
          field: 'levelChangeType',
          options: [{
            label: '上升',
            value: '1'
          }, {
            label: '下降',
            value: '-1'
          }, {
            label: '不变',
            value: '0'
          }]
        },
        {
          type: 'select',
          label: '考核后等级',
          field: 'assessmentLevel',
          options: [{
            label: 'LV1',
            value: '1'
          }, {
            label: 'LV2',
            value: '2'
          }, {
            label: 'LV3',
            value: '3'
          }, {
            label: 'LV4',
            value: '4'
          }, {
            label: 'LV5',
            value: '5'
          }]
        }
      ],
      searchVal: {}
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getList () {
      const { result, status } = await this.$api.automaticAssessment({
        ...this.searchVal,
        pageNum:this.page.pageNum,
        pageSize:this.page.pageSize
      })
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        console.log( this.page.totalCount)
      }
    },
    onSubmit (val) {
      this.searchVal = val
      this.getList()
    },
    onReset () {
      this.searchVal = {}
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    currentChangeSize (val) {
      this.page.pageSize = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
