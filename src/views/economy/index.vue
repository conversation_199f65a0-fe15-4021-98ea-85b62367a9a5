<template>
  <d2-container class="page">
    <common-query
      :query-schema="querySchema"
      :data="query"
      :showCreation="false"
      @onSubmit="onSubmit"
      @onReset="onReset"
    >
    </common-query>
    <common-table
      :table-schema="tableSchema"
      :showIndex="false"
      :table-data="tableData"
    >
      <template #action="scope">

        <el-button
          type="text"
          @click="rove(1,scope.row.nftId)"
        >
          <!-- {{  scope.row.enableRevoke }} -->
          通过</el-button>
        <el-button
          type="text"
          @click="rove(0,scope.row.nftId)"
        >
          <!-- {{  scope.row.enableRevoke }} -->
          拒绝</el-button>
        <el-button
          type="text"
          @click="levelClick(scope.row.nftId)"
        >调整等级</el-button>
      </template>
    </common-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.totalCount"
        :page-size="page.pageSize"
        :current-page="page.pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange"
        @size-change="currentChangeSize"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="等级调整"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-select 
        v-model="leave"
        placeholder="请选择等级"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="tiaozheng"
        >确 定</el-button>
      </span>
    </el-dialog>

  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonQuery from '@/components/CommonQuery_h'
export default {
  components: {
    CommonTable,
    CommonQuery
  },
  data () {
    return {
      dialogVisible: false,
      page: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0
      },
      tableData: [],
      tableSchema: [ // 表格架构
        {
          label: 'id',
          field: 'id',
          width: '120px'
        },
        {
          label: '申请日期',
          field: 'applicationTime',
          width: '150px'
        },
        {
          label: '经纪商类型',
          field: 'brokerType',
          width: '200px',
          type: 'tag',
          tagMap: {
            0: {
              label: 'KOL'
              //   tagType: 'success'
            },
            1: {
              label: '数藏社区长'
              //   tagType: 'success'
            },
            2: {
              label: '职业数藏玩家'
            },
            3: {
              label: '专业数藏团队'
            }
          }
        },
        {
          label: '申请登记',
          field: 'approveLevel',
          width: '100px'
        },
        {
          label: '称呼',
          field: 'callName',
          width: '100px'
        },
        {
          label: '联系方式',
          field: 'wchatOrPhone',
          width: '100px'
        },
        {
          label: 'con add',
          field: 'nftId',
          width: '180px'
        },
        {
          label: 'ta填写的信息',
          field: 'qaJson',
          width: '300px'
        },
        {
          label: '状态',
          field: 'status',
          width: '150px',
          type: 'tag',
          tagMap: {
            0: {
              label: '待审核'
              //   tagType: 'success'
            },
            1: {
              label: '已通过',
              tagType: 'success'

            },
            2: {
              label: '已拒绝',
              tagType: 'danger'
            }
          }
        },
        {
          label: '当前等级',
          field: 'nowLevel',
          width: '150px'
        },
        {
          label: '操作',
          slot: 'action',
          width: '100px'
        }
      ],
      query: {
        dutyStatus: 'DOING'
      },
      querySchema: [ // 搜索组件架构
        {
          type: 'select',
          label: '类型',
          placeholder: '请选择类型',
          field: 'brokerType',
          options: [{
            label: 'KOL',
            value: '0'
          },{
            label: '数藏社区长',
            value: '1'
          }, {
            label: '职业数藏玩家',
            value: '2'
          },

          {
            label: '专业数藏团队',
            value: '3'
          }]
        },
        {
          type: 'select',
          label: '等级：',
          placeholder: '请选择等级',
          field: 'level',
          options: []
        },
        {
          type: 'input',
          label: '称呼',
          field: 'callName'
        },
        {
          type: 'input',
          label: 'con add',
          field: 'nftId'
        },
        {
          type: 'select',
          label: '状态',
          field: 'status',
          options: [{
            label: '待审核',
            value: '0'
          }, {
            label: '已通过',
            value: '1'
          }, {
            label: '已拒绝',
            value: '2'
          }]
        }
      ],
      searchVal: {},
      options: [],
      leave: '',
      ntfId: ''
    }
  },
  mounted () {
    this.getList()
    this.leaveList()
  },
  methods: {
    async getList () {
      const { result, status } = await this.$api.settlementList({
        ...this.searchVal,
        pageNum: this.page.pageNum,
        pageSize: 20
      })
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
      }
    },
    async rove (type, nftId) {
      const { result, status } = await this.$api.approve({
        approveType: type, // 审批类型 0拒绝 1通过
        nftId: nftId // "bigverse 唯一地址"
      })
      if (status.code === 0) {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
        this.getList()
      }
    },
    async levelClick (nftId) { // 等级弹窗
      this.dialogVisible = true
      this.ntfId = nftId
    },
    async leaveList () { // 获取等级
      const { result, status } = await this.$api.levelConfig({
        pageNum: 1,
        pageSize: 10
      })
      if (status.code === 0) {
        result.list.forEach(item => {
          this.querySchema[1].options.push({
            value: item.levelOrder,
            label: item.levelName
          })
          this.options.push({
            value: item.levelOrder,
            label: item.levelName
          })

        })
      }
    },
    async tiaozheng () { // 确认调整等级
      const { result, status } = await this.$api.adjustingLevels({
        level: this.leave,
        nftId: this.ntfId
      })
      if (status.code === 0) {
        this.$message({
          message: '已调整',
          type: 'success'
        })
        this.dialogVisible = false
        this.getList()
      }
    },
    onSubmit (val) {
      this.searchVal = val
      this.getList()
    },
    onReset () {
      this.searchVal = {}
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    currentChangeSize (val) {
      this.page.pageSize = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
