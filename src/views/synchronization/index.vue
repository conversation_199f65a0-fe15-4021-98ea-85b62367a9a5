<template>
  <d2-container class="page">
    <el-form :inline="true" class="demo-form-inline"
             style="background-color:#FFFFFF;padding:20px;">
      <el-form-item>
        用户elasticsearch 数据全量同步
        <el-button style="display: inline-block;" type="primary" @click="isaddDialog=true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        商品elasticsearch 数据全量同步
        <el-button style="display: inline-block;" type="primary" @click="isDialog = true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        系列elasticsearch 数据全量同步
        <el-button style="display: inline-block;" type="primary" @click="isCollectionDialog = true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        系列mysql 数据初始化
        <el-button style="display: inline-block;" type="primary" @click="ismysqlDialog = true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        用户业务含义count值刷新DB及Es接口
        <el-button style="display: inline-block;" type="primary" @click="iscountDialog = true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        系列封面同步
        <el-button style="display: inline-block;" type="primary" @click="iscoverDialog = true">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        树藏平台作品elasticsearch数据全量同步
        <el-button style="display: inline-block;" type="primary" @click="digitalCollectionDataSync">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        月球主题活动数据同步到商品es中
        <el-button style="display: inline-block;" type="primary" @click="moonDataSync">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
       初始化群数据
        <el-button style="display: inline-block;" type="primary" @click="initGroup">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        初始化批量导入账号
        <el-button style="display: inline-block;" type="primary" @click="initBatchImportAccount">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        同步db订单数据到es中
        <el-button style="display: inline-block;" type="primary" @click="asyncDBOrderToES">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
        域名标签筛选项更新
        <el-button style="display: inline-block;" type="primary" @click="asyncDomain">同步</el-button>
      </el-form-item>
      <el-form-item style="display:block;">
       市场页推荐数据-同步到商品ES
        <el-button style="display: inline-block;" type="primary" @click="asyncMall">同步</el-button>
      </el-form-item>
    </el-form>
    <el-dialog title="用户elasticsearch 数据全量同步" :visible.sync="isaddDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="userDataToEs()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="商品elasticsearch 数据全量同步" :visible.sync="isDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="goodsEs()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="系列elasticsearch 数据全量同步" :visible.sync="isCollectionDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="seriesEs()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="系列mysql 数据初始化" :visible.sync="ismysqlDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="operate()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="用户业务含义count值刷新DB及Es接口" :visible.sync="iscountDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="initSyncNftcnUserBusinessCountData()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="系列封面同步" :visible.sync="iscoverDialog" width="35%">
      <div class="">
        确定要全量同步吗？
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adds()">取 消</el-button>
        <el-button type="primary" @click="cover()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import util from '@/libs/util.js'
import { asyncDBOrderToES } from '@/api'

export default {
  name: 'synchronization',
  data () {
    return {
      isDialog: false, // 商品
      isaddDialog: false, // 用户
      isCollectionDialog: false, // 系列
      ismysqlDialog: false, // mysql
      iscountDialog: false, // 粉丝
      submitType: '',
      iscoverDialog: false// 系列封面
    }
  },
  mounted () {
  },
  methods: {
    // 商品
    async goodsEs () {
      try {
        await this.$api.goodsEs()
      } finally {
        this.isDialog = false
      }
    },
    // 用户
    async userDataToEs () {
      try {
        await this.$api.userDataToEs()
      } finally {
        this.isaddDialog = false
      }
    },
    // 系列
    async seriesEs () {
      try {
        await this.$api.seriesEs()
      } finally {
        this.isCollectionDialog = false
      }
    },
    // mysql
    async operate () {
      try {
        await this.$api.operate()
      } finally {
        this.ismysqlDialog = false
      }
    },
    // mysql
    async initSyncNftcnUserBusinessCountData () {
      try {
        await this.$api.initSyncNftcnUserBusinessCountData()
      } finally {
        this.ismysqlDialog = false
      }
    },
    // 系列封面
    async cover () {
      try {
        await this.$api.cover()
      } finally {
        this.ismysqlDialog = false
      }
    },
    // 弹窗取消
    adds () {
      this.isaddDialog = false
      this.isDialog = false
      this.isCollectionDialog = false
      this.ismysqlDialog = false
      this.iscountDialog = false
      this.iscoverDialog = false
    },
    digitalCollectionDataSync () {
      this.$confirm('确定要全量同步吗？', '树藏平台作品elasticsearch数据全量同步', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.digitalCollectionDataSync()
        if (status.code === 0) {
        } else {
          this.$message.error(status.msg)
        }
      })
    },
    moonDataSync () {
      this.$confirm('确定要全量同步吗？', '月球主题活动数据同步到商品es中', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.moonDataSync()
        if (status.code === 0) {
        } else {
          this.$message.error(status.msg)
        }
      })
    },
    asyncDBOrderToES () {
      this.$confirm('确定要全量同步吗？', '同步db订单数据到es中', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.asyncDBOrderToES()
        if (status.code === 0) {
        } else {
          this.$message.error(status.msg)
        }
      })
    },
    /**
     * 初始化群数据
     */
    initGroup () {
      this.$confirm('确定要初始化群数据吗？', '初始化群数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { msg } = await this.$api.initGroup()
        this.$message.success(msg)
      })
    },
    /**
     * 初始化群数据
     */
    initBatchImportAccount () {
      this.$confirm('确定要初始化批量导入账号吗？', '初始化批量导入账号', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { msg } = await this.$api.initBatchImportAccount()
        this.$message.success(msg)
      })
    },
    asyncDomain () {
      this.$confirm('确定要域名标签筛选项更新吗？', '域名标签筛选项更新', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { msg } = await this.$api.domainNameTags()
        this.$message.success('成功')
      })
    },
    asyncMall () {
      this.$confirm('确定要市场页推荐数据-同步到商品ES吗？', '市场页推荐数据-同步到商品ES', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
       const res = await this.$api.marketTabToGoods({})
       if(res.status.code!==0){
       	this.$message.error(res.status.msg)
       }
      })
    },
    handleClick (row) {
      console.log(row)
    }
  }
}
</script>
