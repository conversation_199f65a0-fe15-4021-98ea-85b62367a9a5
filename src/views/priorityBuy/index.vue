<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
      :showCreation="false" @onCreation="nav_task" :showRefresh="true" :showSubmit='true'
      :showReset='false'></common-query>
    <div style="margin-bottom:15px;">
      <el-button @click="nav_task" type="primary" size="mini">新增优先购</el-button>
    </div>

    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
      <template #dutyType="scope">
        {{ scope.row.dutyType == 'CO_SALE' ? '公售'  : '优先购'}}
      </template>
      <template #action="scope">
        <el-button @click="onExport(scope.row.whitelistImportUrl)" type="text">查看</el-button>
        <!-- <el-popconfirm style="margin-left: 10px;" title="确定要删除吗？" @confirm="deleteItem(scope.row)">
          <el-button slot="reference" type="text">删除</el-button>
        </el-popconfirm> -->
      </template>

      <template #action2="scope">

        <el-button @click="deleteItem(scope.row)" type="text">删除</el-button>
      </template>

    </common-table>

    <el-dialog title="优先购白名单" :visible.sync="whiteDetails" center>
      <el-form>
        <el-form-item label="白名单:" :label-width="formLabelWidth">
          {{ white }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="whiteDetails = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="" :visible.sync="dialogVisible" width="800px" center @close="closeDialog" destroy-on-close
      :close-on-click-modal="false">
      <el-radio-group v-model="formData.type" style="margin-left: 150px;margin-bottom: 15px;">
        <el-radio :label="'SERIES_WHITELIST'">优先购任务</el-radio>
        <el-radio :label="'CO_SALE'">公售</el-radio>
      </el-radio-group>
      <CommonForm v-if="formData.type == 'SERIES_WHITELIST'" :schema="formSchema" :isEdit="!details" :data="formData"
        :submit="submit" label-width="150px" :isBack="true" @nav_back="closeDialog">


        <!-- <template #orderMinMax="scope">
          <el-row justify="start">
            <el-col :span="6">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="请输入最小订单频率" v-model="formData.minSecond"></el-input><span class="danwei">秒</span>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="grid-content bg-purple" style="text-align:center;">-</div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="请输入最大订单频率" v-model="formData.maxSecond"></el-input><span class="danwei">秒</span>
              </div>
            </el-col>
          </el-row>
        </template> -->
        <template #merge0>
          <div class="flex_div">
            <el-button @click="downloadTemplate('DUTY_SERIES_WHITELIST')" type="primary" size="mini">
              下载模版</el-button>
            <file-uploader v-if="!isDetail" :value.sync="formData.whitelistImportUrl"
              style="width:200px;margin-left:10px" text="上传模版"></file-uploader>
          </div>
        </template>
      </CommonForm>

      <CommonForm v-else :schema="PublicSaleSchema" :isEdit="!details" :data="formData2" :submit="submit2"
        label-width="150px" :isBack="true" @nav_back="closeDialog">
        <template #button="scope">
          <el-button @click="dialogVisible = false">返回</el-button>
          <el-button type="primary" @click="submit2">提交</el-button>

        </template>

        <!-- <template #orderMinMax="scope">
          <el-row justify="start">
            <el-col :span="6">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="请输入最小订单频率" v-model="formData.minSecond"></el-input><span class="danwei">秒</span>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="grid-content bg-purple" style="text-align:center;">-</div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="请输入最大订单频率" v-model="formData.maxSecond"></el-input><span class="danwei">秒</span>
              </div>
            </el-col>
          </el-row>
        </template> -->
      </CommonForm>
    </el-dialog>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
import FileUploader from '@/components/FileUploader'
import {
  downloadBlob
} from '@/utils/helper'
import {
  noticePublish
} from '@/api/hongyan'
// import {
//   dutyStop,
//   dutyAdd,
//   dutyList,
//   dutyDelete
// } from '@/api/npCollection'
export default {
  name: 'priorityBuy',
  components: {
    CommonQuery,
    CommonTable,
    CommonForm,
    FileUploader
  },
  data() {
    return {
      formData2: {
        csName: '', // 公售系列名
        coSalePrice: '', // 公售价格
        coSaleNum: '', // 公售数量
        coSaleMaxNum: '', // 每个账号最多购买数量
        coSaleRealNameMaxNum: '', // 每个实名账号最多购买数量
        type: 'CO_SALE'

      },
      white: '',
      goodsBlindBoxRequestType: 'CTID',
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      }, // 分页数据
      tableSchema: [ // 表格架构
        {
          label: '优先购id',
          field: 'dutyId',
          width: '170px'
        },
        {
          label:'类型',
          slot:'dutyType',
          width:'170px'
        },
        {
          label: '优先购藏品',
          field: 'dutyName',
          width: '170px'
        },
        {
          label: '藏品总份数',
          field: 'saleNum',
          width: '170px'
        },
        {
          label: '优先购/公售发售份数',
          field: 'saleNum',
          width: '200px'
        },
        {
          label: '优先购/公售售价',
          field: 'salePrice',
          width: '100px'
        },

        {
          label: '优先购开始时间',
          field: 'startTime',
          width: '150px'
        },
        {
          label: '优先购结束时间',
          field: 'endTime',
          width: '150px'
        },
        {
          label: '任务状态',
          field: 'dutyStatus',
          type: 'tag',
          tagMap: {
            INIT: {
              label: '准备中',
              tagType: 'info'
            },
            DOING: {
              label: '进行中',
              tagType: 'success'
            },
            STOPPING: {
              label: '终止中',
              tagType: 'danger'
            },
            STOP: {
              label: '已终止',
              tagType: 'danger'
            },
            DONE: {
              label: '已完成',
              tagType: 'success'
            },
            FAIL: {
              label: '执行失败',
              tagType: 'danger'
            }
          },
          width: '80px',
        },
        {
          label: '优先购白名单',
          slot: 'action',
          field: 'whitelistImportUrl',
          // headerSlot: 'action-header',
          width: '140px',
        },
        {
          label: '操作',
          slot: 'action2',

          width: '140px'
        }
      ],
      tableData: [],
      querySchema: [ // 搜索组件架构
        {
          type: 'search',
          label: '优先购藏品：',
          field: 'ctid',

        },
      ],
      // 公售列表
      PublicSaleSchema: [
        {
          field: 'ctid',
          type: 'search',
          label: '系列名',
          placeholder: '请输入系列名',
          rules: [
            {
              required: true,
              message: '请输入系列名',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'input',
          label: '公售价格：',
          placeholder: '请输入公售价格',
          field: 'coSalePrice',
          rules: [
            {
              required: true,
              message: '请输入公售价格',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'input',
          label: '公售数量：',
          placeholder: '请输入公售数量',
          field: 'coSaleNum',
          rules: [
            {
              required: true,
              message: '请输入公售数量',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'datetimerange',
          label: '公售时段：',
          field: 'startTime',
          placeholder: '请输入公售时段',
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          },
          rules: [
            {
              required: true,
              message: '请选择公售时间',
              trigger: 'change',
            },
          ],
        },
        {
          type: 'input',
          label: '每个账号最多买几个：',
          placeholder: '请输入每个账号最多购买数量',
          field: 'coSaleMaxNum',
          rules: [], // 非必填，无需验证规则
        },
        {
          type: 'input',
          label: '每个实名最多买几个：',
          placeholder: '请输入每个实名最多购买数量',
          field: 'coSaleRealNameMaxNum',
          rules: [], // 非必填，无需验证规则
        },
        {
          slot: 'button'
        }
      ],
      formSchema: [
        {
          type: 'search',
          label: '优先购藏品：',
          placeholder: '请输入优先购藏品名',
          field: 'ctid',
          rules: [{
            required: true,
            message: '请输入优先购藏品名',
            trigger: 'blur'
          }],
        },
        {
          type: 'input',
          label: '优先购发售份数：',
          placeholder: '请输入发售份数',
          field: 'saleNum',
          rules: [{
            required: true,
            message: '请输入发售份数',
            trigger: 'blur'
          }],
        },
        {
          type: 'input',
          label: '优先购售价：',
          field: 'salePrice',
          placeholder: '请输入优先购售价',
          rules: [{
            required: true,
            message: '请输入优先购售价',
            trigger: 'blur'
          }],
        },
        {
          type: 'datetimerange',
          label: '优先购时段：',
          field: 'startTime',
          placeholder: '请输入优先购时段',
          pickerOptions: {
            disabledDate: (time) => {
              return time.getTime() < Date.now() - 86400000
            }
          },
        },
        {
          slot: 'merge0',
          label: '优先购白名单：',
          field: 'whitelistImportUrl',

        },
        {
          type: 'action',
          exclude: ['reset']
        }
      ],
      formData: {
        saleNum: '',
        salePrice: '',
        whitelistImportUrl: '',
        startTime: [],
        type: 'SERIES_WHITELIST'
      },
      query: {
        // dutyStatus:'DOING'
      },
      loading: false,
      dialogVisible: false,
      details: false,
      whiteDetails: false,
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    toTemplatePage(item = {}) {
      if (item.businessType && item.businessType != 0) {
        this.$router.push({
          name: 'NoticeEdit',
          query: {
            templateId: item.id,
            businessType: item.businessType
          }
        })
      } else {
        localStorage.setItem('noticeInfo', JSON.stringify(item))
        this.$router.push({
          name: 'platformPublish',
          query: {
            templateId: item.id
          }
        })
      }
    },
    nav_task() {
      this.details = false
      this.dialogVisible = true
    },
    async downloadTemplate() {
      const res = await this.$api.userCenterDownLoadTemplate({
        templateTag: "DUTY_SERIES_WHITELIST"
      })
      console.log(res)
      if (res.status.code == 0) {
        window.open(res.result.emailsTemplateUrl)
        this.$message.success(res.status.msg)
        // this.dialogVisible = false
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data
      this.getList(true)
    },
    // 分页改变
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页改变
    currentChangeSize(value) {
      this.page.pageSize = value
      this.getList()
    },
    async getList() {
      let ctid = "";
      if (this.query.ctid) {
        ctid = this.query.ctid.split("(")[1].split(")")[0]
      }
      const params = {
        ...this.query,
        ...this.page,
        pageNum: this.page.pageNum,
        dutyType: 'SERIES_WHITELIST',
        ctid
      }
      let dataList = []
      const {
        status,
        result
      } = await this.$api.dutyList(params)
      console.log(status, result)
      if (status.code === 0) {
        const data = result.list
        data.forEach((item) => {
          dataList.push({
            ...item.seriesWhitelistExtra,
            ctid: item.ctid,
            startTime: item.startTime,
            endTime: item.endTime,
            dutyId: item.dutyId,
            dutyName: item.dutyName,
            dutyStatus: item.dutyStatus,
            dutyType:item.dutyType,
            remark: item.remark,
            csName: item.csName,
            saleNum: item.seriesWhitelistExtra ? item.seriesWhitelistExtra.saleNum : item.coSaleExtra.coSaleNum,
            salePrice: item.seriesWhitelistExtra ? item.seriesWhitelistExtra.salePrice : item.coSaleExtra.coSalePrice,
            whitelistImportUrl: item.seriesWhitelistExtra ? item.seriesWhitelistExtra.whitelistImportUrl : '--',
          })
        })
        this.tableData = dataList
        this.tableData.forEach((item) => {
          item = item.setSellExtra
          this.$forceUpdate();
        })
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount


      }

    },

    nav_error(airDropId) {
      this.$router.push({
        name: 'twoDropError',
        query: {
          airDropId
        }
      })
    },
    onRefresh(data) {
      this.query = data
      this.getList()
    },
    openText(text) {
      console.log(text)
      this.$alert(text, '备注详情', {
        confirmButtonText: '确定',
        callback: action => {

        }
      });
    },
    shuaxin() {
      this.$message.success("刷新成功")
      this.getList()
    },
    onExport(item) {
      console.log(item)
      window.open(item)
    },
    closeDialog() {
      this.dialogVisible = false
      this.formData = {
        fromType: 2,
        isTiming: 2
      }
    },
    async submit2() {
      let startTime, endTime
      let ctid = "";

      startTime = this.formData2.startTime[0]
      endTime = this.formData2.startTime[1]
      if (this.formData2.ctid) {
        ctid = this.formData2.ctid.split("(")[1].split(")")[0]
      } else {
        this.$message.error('请输入藏品名')
        return false
      }
      let extra = {
        ...this.formData2,
      }
      extra = JSON.stringify(extra)
      const params = {
        ...this.formData2,
        dutyType: 'CO_SALE',
        ctid,
        extra,
        startTime,
        endTime
      }
      const {
        status,
        result
      } = await this.$api.dutyAdd(params)
      if (status.code === 0) {
        this.$message.success('新增公售成功')
        this.getList()
        this.dialogVisible = false
        this.closeDialog()
      }
    },

    async submit() {
      console.log(this.formData)
      let ctid = "";
      let startTime, endTime
      startTime = this.formData.startTime[0]
      endTime = this.formData.startTime[1]
      if (this.formData.ctid) {
        ctid = this.formData.ctid.split("(")[1].split(")")[0]
      } else {
        this.$message.error('请输入藏品名')
        return false
      }
      let extra = {
        ...this.formData,
      }
      extra = JSON.stringify(extra)
      const params = {
        ...this.formData,
        dutyType: 'SERIES_WHITELIST',
        ctid,
        extra,
        startTime,
        endTime
      }
      console.log(params)
      const {
        status,
        result
      } = await this.$api.dutyAdd(params)
      if (status.code === 0) {
        this.$message.success('新增优先购成功')
        this.getList()
        this.dialogVisible = false
        this.closeDialog()
      }

    },
    async downloadExcel(res) {
      window.open(res)
      this.$message.success('下载成功')
    },
    // 执行空投

    open_data(item) {
      this.whiteDetails = true
      console.log(item)
      this.white = item.whitelistImportUrl
    },
    // 删除数据
    async deleteItem(item) {
      console.log(item)
      this.$confirm(`确定删除该优先购藏品吗？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {

        const res = await this.$api.dutyDelete({
          dutyId: item.dutyId,
          dutyType: "SERIES_WHITELIST"
        })
        console.log(res)
        if (res.status.code == 0) {
          this.$message.success(res.status.msg)
          this.getList()
          this.dialogVisible = false
          this.closeDialog()
        }




        //   dutyDelete({
        //     dutyId: item.dutyId,
        //     dutyType: "SERIES_WHITELIST"
        //   }).then((res) => {
        //     if (res.status.code == 0) {
        //       this.$message.success(res.status.msg)
        //       this.getList()
        //       this.dialogVisible = false
        //       this.closeDialog()
        //     }
        //   })
        //   if (res.status.code === 0) {
        //     this.$message.success(status.msg)
        //     this.getList()
        //   }
      })

    }
  }
}
</script>

<style lang="scss" scoped>
.oneOver {
  display: inline-block;
  /*超出部分隐藏*/
  white-space: nowrap;
  overflow: hidden;
  /*不换行*/
  text-overflow: ellipsis;
  width: 120px;
  cursor: pointer;
  font-size: 12px;
  /*超出部分文字以...显示*/
}

.flex_div {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 700px;
  height: 50px;
  margin-top: 10px;
}

.flex {
  display: flex;
}

.uploader {
  margin-top: 0 !important;
}

::v-deep .el-form-item__label {
  width: 160px !important;
}
</style>
