<template>
	<d2-container class="page">
		<common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
		<!-- <div style="margin-bottom:15px;" >
			<el-button @click="nav_add" type="primary" size="mini">创建任务</el-button>
		</div> -->
		<common-table :table-schema="tableSchema" :table-data="tableData" >
			<template #action-header>
				<el-button @click="nav_add" type="primary" size="mini">创建任务</el-button>
			</template>
			<template #button="scope">
				<el-button type="text" @click="whitelist(scope.row,1)">查看优先购名单</el-button>
			</template>
			<template #nav_shop="scope">
				<el-button type="text" @click="nav_shop(scope.row)">查看</el-button>
			</template>
			<template #button1="scope">
				<el-button type="text" @click="whitelist(scope.row,2)">查看优先抢名单</el-button>
			</template>
			<template #action="scope">
				<el-button @click="nav_update(scope.row)" type="text">编辑</el-button>
				<el-popconfirm style="margin-left: 10px;" title="确定要删除吗？" @confirm="deleteItem(scope.row)">
					<el-button slot="reference" type="text">删除</el-button>
				</el-popconfirm>
			</template>
		</common-table>
		<el-dialog title="白名单列表" :visible.sync="whitelistVisible">
			<el-input style="margin-bottom:20px;" placeholder="请输入用户地址contractAddress进行搜索" :clearable="true" prefix-icon="el-icon-search"
				v-model="keyword">
				<el-button slot="append" icon="el-icon-search" @click="sousuo()" ></el-button>
			</el-input>
			</el-input>
			<el-table :data="whitelistData" border>
				<el-table-column :prop="item.prop" :label="item.label" width="150"
					v-for="(item,index) in whitelistColumn" :key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
			</common-pagination>
		</div>
		<!-- <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
	</d2-container>
</template>

<script>
	import CommonQuery from '@/components/CommonQuery'
	import CommonTable from '@/components/CommonTable'
		import CommonPagination from '@/components/CommonPagination'
	export default {
		name: 'ActivityManage',
		components: {
			CommonQuery,
			CommonTable,
			CommonPagination
		},
		props: {},
		data() {
			return {
				dialogVisible: false, // 弹窗
				selected: '', // 选择的活动类型
				page: {
					totalCount: 0,
					pageSize: 10
				}, // 分页数据
				querySchema: [ // 搜索组件架构
					{
						type: 'input',
						label: '新品名称：',
						placeholder: '新品名称',
						field: 'dutyName'
					},
					{
						type: 'input',
						label: '任务编号：',
						placeholder: '请输入任务编号',
						field: 'dutyCode'
					},
				],
				tableSchema: [ // 表格架构
					{
						label: '任务名称',
						field: 'dutyName',
						width: '170px'
					},
					{
						label: '任务编号',
						field: 'dutyCode',
						width: '170px'
					},
					{
						label: '系列id',
						field: 'ctid',
						width: '200px'
					},
					{
						label: '系列名称',
						field: 'csName',
						width: '100px'
					},
					{
						label: '系列内作品',
						slot: 'nav_shop',
						width: '120px'
					},
					{
						label: '优先购名单',
						slot: 'button',
						width: '150px'
					},
					{
						label: '当前状态',
						field: 'isCanEdit',
						type: 'tag',
						tagMap: {
							1: {
								label: '是',
								tagType: 'success'
							},
							0: {
								label: '否',
								tagType: 'danger'
							}
						},
						width: '80px'
					},
					{
						label: '优先购开始时间',
						field: 'preSaleStartTime1',
						width: '190px'
					},
					{
						label: '优先购结束时间',
						field: 'preSaleEndTime1',
						width: '190px'
					},
					{
						label: '优先抢名单',
						slot: 'button1',
						width: '170px'
					},
					{
						label: '优先抢开始时间',
						field: 'preSaleStartTime2',
						width: '190px'
					},
					{

						label: '优先抢结束时间',
						field: 'preSaleEndTime2',
						width: '190px'
					},
					{
						label: '是否下架',
						field: 'isAutoUnSale',
						type: 'tag',
						tagMap: {
							1: {
								label: '是',
								tagType: 'success'
							},
							0: {
								label: '否',
								tagType: 'danger'
							}
						},
						width: '80px'
					},
					{
						label: '公售开始时间',
						field: 'saleTime',
						width: '180px'
					},
					{
						label: '下架时间',
						field: 'unSaleTime',
						width: '170px'
					},
					{
						label: '操作',
						slot: 'action',
						headerSlot: 'action-header',
						width: '140px',
						fixed:'right'
					}
				],
				tableData: [{}],
				whitelistData: [],
				whitelistVisible: false,
				whitelistColumn: [{
						label: '用户地址',
						prop: 'contractAddress'
					},
					{
						label: '购买数量',
						prop: 'num'
					},
				],
				searchWhitelist: "",
				keyword:"",
				type:1,
				page: {
					totalCount: 0,
					pageSize: 10,
					pageNum: 1
				}, // 分页数据
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			// 过滤查询
			onQueryChange(data) {
				this.query = data
				this.getList(true)
			},
			// 分页改变
			currentChange(value) {
				this.page.pageNum = value
				this.getList()
			},
			// 创建活动
			toFormPage(item = {}, activityType) {
				this.dialogVisible = false
				console.log(item)
				const typeMap = {
					INVITE_NEW: 'ActivityPullNew',
					REBUILD: 'ActivityRebuild',
					GET_REWARD: 'ActivityPullNew',
					MERGE: 'ActivityPullNew'
				}
				this.$router.push({
					name: item.type ? typeMap[item.type] : typeMap[this.selected],
					query: {
						type: item.type || this.selected,
						activityNo: item.activityNo,
						activityType
					}
				})
			},
			// 获取列表
			async getList(isInit) {
				const params = {
					...this.query,
					...this.page,
					dutyType:'PRE_SALE_AND_UN_SALE',
					pageNum: isInit ? 1 : this.page.pageNum,
					pageSize:20
				}
				const {
					status,
					result
				} = await this.$api.dutyList(params)
				if (status.code === 0) {
					this.tableData = result.list
					this.page.totalCount = result.totalCount
					this.page.pageSize = result.pageSize
					this.page.pageCount = result.pageCount
				}
			},
			async clearRedis(item) {
				const {
					status
				} = await this.$api.clearRedis({
					activityNo: item.activityNo,
					activityType: item.type
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
				}
			},
			// 活动上下线切换
			activityStatusToggle(activityNo, onlineStatus) {
				const title = onlineStatus === 1 ? '下线' : '上线'
				const text = onlineStatus === 1 ? '确认下线吗？' : '确认上线吗？'
				this.$confirm(text, title, {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(async () => {
					const {
						status
					} = await this.$api.activityStatusToggle({
						activityNo,
						onlineStatus: onlineStatus === 0 || onlineStatus === 2 ? 1 : 2
					})
					if (status.code === 0) {
						this.$message.success(status.msg)
						this.getList()
					}
				})
			},
			// 删除数据
			async deleteItem(item) {
				if(item.isCanEdit===0){
					this.$message.error("不可删除！！！！")
				}else{
					const {
						status
					} = await this.$api.dutyDelete({
						dutyId:item.dutyId,
						dutyType:item.dutyType
					})
					if (status.code === 0) {
						this.$message.success(status.msg)
						this.getList()
					}
				}
			},
			nav_add() {
				this.$router.push({
					name: 'timing-addupdate'
				})
			},
			nav_update(item) {
				this.$router.push({
					name: 'timing-addupdate',
					query: {
						dutyId: item.dutyId
					}
				})
				
			},
			/**
			 * 白名单列表
			 * @param row
			 * @returns {Promise<void>}
			 */
			async whitelist(row, type) {
				this.dutyId=row.dutyId
				this.type=type
				this.whitelistVisible = true
				const {
					result: {
						list
					}
				} = await this.$api.whiteUserList({
					dutyId: row.dutyId,
					type: type,
					pageSize: 1000,
					pageNum: 1,
				})
				this.whitelistData = list
			},
			async sousuo(){
				const {
					result: {
						list
					}
				} = await this.$api.whiteUserList({
					dutyId: this.dutyId,
					type: this.type,
					pageSize: 1000,
					pageNum: 1,
					contractAddress:this.keyword
				})
				this.whitelistData = list
			},
			nav_shop(item){
				this.$router.push({
					name: 'ListOfWorks',
					query: {
						ctid: item.ctid
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>

</style>
