<template>
	<d2-container class="page">
		<common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
			<template #preSale1>
				<el-row>
					<el-col :span="20">
						<common-table :is-edit="!isDetail" :table-schema="preSale1TableSchema"
							:table-data="preSale1TableData">
							<template v-if="!isDetail" #action-header>
								<file-uploader v-if="!isDetail" @success="importTemplate" :value.sync="templateUrl"
									style="margin-bottom: 10px;" text="上传模版"></file-uploader>
								<el-button @click="downloadTemplate('MALL_CENTER_IMPORT_GOODS_DUTY_WHITE_USER_LIST')"
									type="primary" size="mini">
									下载模版</el-button>
							</template>
							<template v-if="!isDetail" #action="scope">
								<el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, preSale1TableData)">
									<el-button slot="reference" type="text">删除</el-button>
								</el-popconfirm>
							</template>
						</common-table>
					</el-col>
				</el-row>
			</template>
			<template #datetime-msg>
				<div style="color:#F56C6C
">
					<div>注：如无公售请填写下架时间后5分钟</div>
				</div>
			</template>
			<template #preSale1msg>
				<div style="color:#F56C6C">
					<div>注：可重复多次导入，每次导入覆盖之前的，以最后一次导入数据为准，脚本执行中不可修改</div>
					<div>1、导入模板信息，用户地址可以重复：用户地址、购买次数</div>
					<div>2、导入名单后优先购时间必填开始时间必须大于结束时间，如有优先抢，优先购结束时间必须小于优先抢开始时间</div>
				</div>
			</template>
			
			<template #preSale2>
				<el-row>
					<el-col :span="20">
						<common-table :is-edit="!isDetail" :table-schema="preSale2TableSchema"
							:table-data="preSale2TableData">
							<template v-if="!isDetail" #action-header>
								<file-uploader v-if="!isDetail" @success="importTemplate1" :value.sync="templateUrl1"
									style="margin-bottom: 10px;" text="上传模版"></file-uploader>
								<el-button @click="downloadTemplate('MALL_CENTER_IMPORT_GOODS_DUTY_WHITE_USER_LIST')"
									type="primary" size="mini">
									下载模版</el-button>
							</template>
							<template v-if="!isDetail" #action="scope">
								<el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, preSale2TableData)">
									<el-button slot="reference" type="text">删除</el-button>
								</el-popconfirm>
							</template>
						</common-table>
					</el-col>
				</el-row>
			</template>
			<template #preSale2msg>
				<div style="color:#F56C6C
">
					<div>注：可重复多次导入，每次导入覆盖之前的，以最后一次导入数据为准，脚本执行中不可修改</div>
					<div>1、导入模板信息，用户地址可以重复：用户地址、抢购次数</div>
					<div>2、选择是展开优先抢名单导入和优先抢时间输入框</div>
					<div>3、导入名单后优先抢时间必填开始时间必须大于结束时间，如有公售，优先抢结束时间必须小于公售开始时间</div>
				</div>
			</template>
			<template #unSalemsg>
				<div style="color:#F56C6C
">
					<div>注：</div>
					<div>1、选择“是”展开下架时间输入框</div>
					<div>2、下架可以优先购设置完后直接选择</div>
					<div>3、下架时间可以早于公售时间，这样可在公售前下架，下架后公售时间到了也不会上架。如需公售，可把下架时间设置在公售后。</div>
				</div>
			</template>
		</common-form>
	</d2-container>
</template>

<script>
	import CommonForm from '@/components/CommonForm'
	import CommonTable from '@/components/CommonTable'
	import FileUploader from '@/components/FileUploader'
	import {
		mapActions
	} from 'vuex'
	import {
		downloadBlob
	} from '@/utils/helper'

	export default {
		name: 'timing-addupdate',
		components: {
			CommonForm,
			CommonTable,
			FileUploader
		},
		data() {
			const validatepreSale1 = (rule, value, callback) => {
				if (this.preSale1TableData.length <= 0) {
					callback(new Error('请添加数据'))
				} else {
					this.preSale1TableData.forEach(item => {
						if (!item.contractAddress) {
							callback(new Error('请输入contractAddress'))
						} else if (!item.num) {
							callback(new Error('请填写购买数量'))
						}
					})
					callback()
				}
			}
			const validatepreSale2 = (rule, value, callback) => {
				if (this.preSale2TableData.length <= 0) {
					callback(new Error('请添加数据'))
				} else {
					this.preSale2TableData.forEach(item => {
						if (!item.contractAddress) {
							callback(new Error('请输入contractAddress'))
						} else if (!item.num) {
							callback(new Error('请填写购买数量'))
						}
					})
					callback()
				}
			}
			return {
				templateUrl: '', // 盲盒内作品模板地址
				templateUrl1: '', // 盲盒内作品模板地址
				isDetail: false, // 详情
				activityNo: null, // 活动编号
				preSale1TableSchema: [ // 优先购表格结构
					{
						label: '用户地址',
						field: 'contractAddress'
					},
					{
						label: '购买个数',
						field: 'num'
					},
					{
						label: '操作',
						slot: 'action',
						width: '120px',
						headerSlot: 'action-header'
					}
				],
				preSale1TableData: [], // 优先购数据
				preSale2TableSchema: [ // 优先抢表格结构
					{
						label: '用户地址',
						field: 'contractAddress'
					},
					{
						label: '购买个数',
						field: 'num'
					},
					{
						label: '操作',
						slot: 'action',
						width: '120px',
						headerSlot: 'action-header'
					}
				],
				preSale2TableData: [], // 优先抢数据
				formData: {
					dutyName: '',
					ctid: '',
					preSaleStartTime1: '',
					preSaleEndTime1: '',
					isOpenPreSale2: 0,
					preSaleStartTime2: '',
					preSaleEndTime2: '',
					saleTime: '',
					unSaleTime: '',
					isAutoUnSale: 0,
					dutyType:'PRE_SALE_AND_UN_SALE'
				},
				formSchema: [{
						type: 'input',
						label: '新品名称：',
						placeholder: '请输入新品名称',
						field: 'dutyName',
						maxlength: 20,
						rules: [{
							required: true,
							message: '请输入新品名称',
							trigger: 'blur'
						}, ]
					},
					{
						type: 'input',
						label: '系列id：',
						placeholder: '请输入系列id',
						field: 'ctid',
						rules: [{
							required: true,
							message: '请输入系列id',
							trigger: 'blur'
						}]
					},
					{
						type: 'datetime',
						label: '优先购开始时间：',
						placeholder: '请输入优先购开始时间',
						field: 'preSaleStartTime1',
						rules: [{
							required: true,
							message: '请输入优先购开始时间',
							trigger: 'blur'
						}]
					},
					{
						type: 'datetime',
						label: '优先购结束时间：',
						placeholder: '请输入优先购结束时间',
						field: 'preSaleEndTime1',
						rules: [{
							required: true,
							message: '请输入优先购结束时间',
							trigger: 'blur'
						}]
					},
					{
						type: 'datetime',
						label: '公售时间：',
						placeholder: '请输入公售时间',
						field: 'saleTime',
						rules: [{
							required: true,
							message: '请输入公售时间',
							trigger: 'blur'
						}]
					},
					{
						slot: 'datetime-msg',
						label: '',
					},
					{
						slot: 'preSale1',
						label: '优先购：',
						field: 'preSale1TableData',
						rules: [{
							required: true,
							validator: validatepreSale1,
							trigger: 'blur'
						}]
					},
					{
						slot: 'preSale1msg',
						label: '',
					},
					{
						type: 'radio',
						label: '是否需要优先抢：',
						placeholder: '是否需要优先抢',
						field: 'isOpenPreSale2',
						options: [{
								label: '是',
								value: 1
							},
							{
								label: '否',
								value: 0
							},
						],
						rules: [{
							required: true,
						}]
					},
					{
						type: 'datetime',
						label: '优先抢开始时间：',
						placeholder: '请输入优先抢开始时间',
						field: 'preSaleStartTime2',
						show: {
							relationField: 'isOpenPreSale2',
							value: [1]
						},
						rules: [{
							required: true,
							message: '请输入优先抢开始时间',
							trigger: 'blur'
						}]
					},
					{
						type: 'datetime',
						label: '优先抢结束时间：',
						placeholder: '请输入优先抢结束时间',
						field: 'preSaleEndTime2',
						show: {
							relationField: 'isOpenPreSale2',
							value: [1]
						},
						rules: [{
							required: true,
							message: '请输入优先抢结束时间',
							trigger: 'blur'
						}]
					},
					{
						slot: 'preSale2',
						label: '优先抢：',
						field: 'preSale2TableData',
						show: {
							relationField: 'isOpenPreSale2',
							value: [1]
						},
						rules: [{
							required: true,
							validator: validatepreSale2,
							trigger: 'blur'
						}]
					},{
						slot: 'preSale2msg',
						label: '',
						show: {
							relationField: 'isOpenPreSale2',
							value: [1]
						},
					},
					{
						type: 'radio',
						label: '是否需要下架时间：',
						placeholder: '是否需要下架时间',
						field: 'isAutoUnSale',
						options: [{
								label: '是',
								value: 1
							},
							{
								label: '否',
								value: 0
							},
						],
						rules: [{
							required: true,
						}]
					},
					{
						type: 'datetime',
						label: '下架时间：',
						placeholder: '请输入下架时间',
						field: 'unSaleTime',
						show: {
							relationField: 'isAutoUnSale',
							value: [1]
						},
						rules: [{
							required: true,
							message: '请输入下架时间',
							trigger: 'blur'
						}]
					},
					{
						slot: 'unSalemsg',
						label: '',
						show: {
							relationField: 'isAutoUnSale',
							value: [1]
						},
					},
					{
						type: 'action'
						// exclude: ['reset', 'submit', 'back']
					}
				]
			}
		},
		mounted() {
			const {
				type,
				activityType,
				dutyId
			} = this.$route.query
			this.isDetail = dutyId === 'detail'
			this.dutyId = dutyId
			dutyId && this.getDetail()
		},
		methods: {
			...mapActions('d2admin/page', ['close']),
			// 优先购导入模版
			async importTemplate(data) {
				const {
					status,
					result
				} = await this.$api.whiteUserImport({
					importUrl: data.result?.url
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
					this.preSale1TableData = result.list
				}
			},
			// 优先抢导入模版
			async importTemplate1(data) {
				const {
					status,
					result
				} = await this.$api.whiteUserImport({
					importUrl: data.result?.url
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
					this.preSale2TableData = result.list
				}
			},
			routerBack() {
				const {
					fullPath
				} = this.$route
				this.close({
					tagName: fullPath
				})
				this.$router.back()
			},
			async getDetail() {
				const {
					status,
					result
				} = await this.$api.dutyInfo({
					dutyId: this.dutyId,
					dutyType:'PRE_SALE_AND_UN_SALE'
				})
				if (status.code === 0) {
					const {
						preSaleWhiteUserList1,
						preSaleWhiteUserList2,
						...rest
					} = result
					this.formData = {
						...rest,
					}
					this.preSale1TableData = preSaleWhiteUserList1
					this.preSale2TableData = preSaleWhiteUserList2
				}
				console.table(this.formData)
			},
			// 添加可熔炼藏品
			addCollectionItem() {
				this.collectionTableData.push({
					picCoverSmelting: '',
					smeltingCollectionExcelUrl: ''
				})
			},
			// 添加盲盒结果
			addResultItem() {
				this.tableData.push({
					prizePicture: ''
				})
			},
			deleteItem(index, data) {
				data.splice(index, 1)
			},
			async downloadTemplate(templateTag) {
				const {
					status,
					result
				} = await this.$api.downLoadTemplateExcel({
					templateTag
				})
				if (status.code === 0) {
					window.open(result.emailsTemplateUrl, '_blank')
					this.$message.success(status.msg)
				}
			},
			async downloadExcel(materialsUnqNo) {
				const res = await this.$api.activityDownloadExcel({
					materialsUnqNo
				})
				if (res) {
					downloadBlob(res, '作品')
					this.$message.success('下载成功')
				}
			},
			async submit() {
				console.table(this.formData)
				const data = {
					dutyId: this.dutyId,
					...this.formData,
					preSaleWhiteUserList1: JSON.stringify(this.preSale1TableData),
					preSaleWhiteUserList2: JSON.stringify(this.preSale2TableData),
					dutyType:'PRE_SALE_AND_UN_SALE'
				}
				console.log(data)
				this.$confirm('是否确认提交保存？', '确认提交保存', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(async () => {
					if (this.dutyId) {
						const {
							status
						} = await this.$api.dutyEdit(data)
						if (status.code === 0) {
							this.routerBack()
						}
					} else {
						const {
							status
						} = await this.$api.dutyAdd(data)
						if (status.code === 0) {
							this.routerBack()
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		padding-top: 80px;
	}
</style>
