<template>
	<d2-container class="page">
		<common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
		<!-- <div style="margin-bottom:15px;" >
			<el-button @click="nav_add" type="primary" size="mini">创建任务</el-button>
		</div> -->
		<common-table :table-schema="tableSchema" :table-data="tableData">
			<template #action-header>
				<el-button @click="nav_add" type="primary" size="mini">创建批量任务</el-button>
			</template>
			<template #duty-type="scope">
				{{ scope.row }}
			</template>
			<template #action="scope">
				<el-button @click="nav_update(scope.row)" type="text">编辑</el-button>
				<el-popconfirm style="margin-left: 10px;" title="确定要删除吗？" @confirm="deleteItem(scope.row)">
					<el-button slot="reference" type="text">删除</el-button>
				</el-popconfirm>
				<el-popconfirm style="margin-left: 10px;" title="确定要终止任务吗？" v-if="scope.row.dutyStatus == 'DOING'"
					@confirm="stop(scope.row)">
					<el-button slot="reference" type="text">终止任务</el-button>
				</el-popconfirm>
			</template>
		</common-table>
		<el-dialog title="白名单列表" :visible.sync="whitelistVisible">
			<el-input style="margin-bottom:20px;" placeholder="请输入用户地址contractAddress进行搜索" :clearable="true"
				prefix-icon="el-icon-search" v-model="keyword">
				<el-button slot="append" icon="el-icon-search" @click="sousuo()"></el-button>
			</el-input>
			</el-input>
			<el-table :data="whitelistData" border>
				<el-table-column :prop="item.prop" :label="item.label" width="150" v-for="(item, index) in whitelistColumn"
					:key="index">
				</el-table-column>
			</el-table>
		</el-dialog>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
			</common-pagination>
		</div>
		<!-- <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
	</d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonPagination from '@/components/CommonPagination'
export default {
	name: 'ActivityManage',
	components: {
		CommonQuery,
		CommonTable,
		CommonPagination
	},
	props: {},
	data() {
		return {
			dialogVisible: false, // 弹窗
			selected: '', // 选择的活动类型
			page: {
				totalCount: 0,
				pageSize: 10
			}, // 分页数据
			querySchema: [ // 搜索组件架构
				{
					type: 'search',
					label: '系列名称：',
					placeholder: '系列名称',
					field: 'ctid'
				},
				{
					type: 'input',
					label: '任务名称：',
					placeholder: '任务名称',
					field: 'dutyName'
				},
				{
					type: 'input',
					label: '任务编号：',
					placeholder: '请输入任务编号',
					field: 'dutyCode'
				},
				{
					type: 'select',
					label: '任务类型：',
					placeholder: '',
					field: 'dutyType',
					options: [{
						label: '批量销毁',
						value: 'DESTROY'
					},
					{
						label: '批量变更寄售价格',
						value: 'CHANGE_ON_SALE_PRICE'
					},
					{
						label: '批量变更寄售状态',
						value: 'CHANGE_NOT_SALE_SIGN'
					},
					],
					rules: [{
						required: true,
					}]
				}
			],
			tableSchema: [ // 表格架构
				{
					label: '任务编号',
					field: 'dutyCode'
				},
				{
					label: '任务名称',
					field: 'dutyName',
				},
				{
					label: '系列名称',
					field: 'csName',
				},
				{
					label: '任务类型',
					field: 'dutyType',
					type: 'tag',
					tagMap: {
						DESTROY: {
							label: '批量销毁',
							tagType: 'success'
						},
						CHANGE_ON_SALE_PRICE: {
							label: '寄售价格',
							tagType: 'success'
						},
						CHANGE_NOT_SALE_SIGN: {
							label: '寄售状态',
							tagType: 'success'
						}
					},
				},
				{
					label: '任务状态',
					field: 'dutyStatus',
					type: 'tag',
					tagMap: {
						INIT: {
							label: '准备中',
							tagType: 'info'
						},
						DOING: {
							label: '进行中',
							tagType: 'success'
						},
						STOPPING: {
							label: '终止中',
							tagType: 'danger'
						},
						STOP: {
							label: '已终止',
							tagType: 'danger'
						},
						DONE: {
							label: '已完成',
							tagType: 'success'
						},
						FAIL: {
							label: '执行失败',
							tagType: 'danger'
						}
					},
					width: '80px',
				},
				{
					label: '执行时间',
					field: 'operateTime',
				},
				{
					label: '备注',
					field: 'remark',
				},
				{
					label: '操作',
					slot: 'action',
					headerSlot: 'action-header',
					width: '140px',
					fixed: 'right'
				}
			],
			tableData: [{}],
			whitelistData: [],
			whitelistVisible: false,
			whitelistColumn: [{
				label: '用户地址',
				prop: 'contractAddress'
			},
			{
				label: '购买数量',
				prop: 'num'
			},
			],
			searchWhitelist: "",
			keyword: "",
			type: 1,
			page: {
				totalCount: 0,
				pageSize: 10,
				pageNum: 1
			}, // 分页数据
			query: {

			}
		}
	},
	mounted() {
		this.getList()
	},
	methods: {
		// 过滤查询
		onQueryChange(data) {
			this.query = data
			this.getList(true)
		},
		// 分页改变
		currentChange(value) {
			this.page.pageNum = value
			this.getList()
		},
		// 创建活动
		toFormPage(item = {}, activityType) {
			this.dialogVisible = false
			console.log(item)
			const typeMap = {
				INVITE_NEW: 'ActivityPullNew',
				REBUILD: 'ActivityRebuild',
				GET_REWARD: 'ActivityPullNew',
				MERGE: 'ActivityPullNew'
			}
			this.$router.push({
				name: item.type ? typeMap[item.type] : typeMap[this.selected],
				query: {
					type: item.type || this.selected,
					activityNo: item.activityNo,
					activityType
				}
			})
		},
		// 获取列表
		async getList(isInit) {
			let ctid;
			if (this.query.ctid) {
				ctid = this.query.ctid.split("(")[1].split(")")[0]
			}
			const params = {
				...this.query,
				...this.page,
				pageNum: isInit ? 1 : this.page.pageNum,
				ctid
			}
			const {
				status,
				result
			} = await this.$api.dutyList(params)
			if (status.code === 0) {
				this.tableData = result.list
				this.page.totalCount = result.totalCount
				this.page.pageSize = result.pageSize
				this.page.pageCount = result.pageCount
			}
		},
		async clearRedis(item) {
			const {
				status
			} = await this.$api.clearRedis({
				activityNo: item.activityNo,
				activityType: item.type
			})
			if (status.code === 0) {
				this.$message.success(status.msg)
			}
		},
		// 活动上下线切换
		activityStatusToggle(activityNo, onlineStatus) {
			const title = onlineStatus === 1 ? '下线' : '上线'
			const text = onlineStatus === 1 ? '确认下线吗？' : '确认上线吗？'
			this.$confirm(text, title, {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				const {
					status
				} = await this.$api.activityStatusToggle({
					activityNo,
					onlineStatus: onlineStatus === 0 || onlineStatus === 2 ? 1 : 2
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
					this.getList()
				}
			})
		},
		// 删除数据
		async deleteItem(item) {
			if (item.isCanEdit === 0) {
				this.$message.error("不可删除！！！！")
			} else {
				const {
					status
				} = await this.$api.dutyDelete({
					dutyId: item.dutyId,
					dutyType: item.dutyType
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
					this.getList()
				}
			}
		},
		nav_add() {
			this.$router.push({
				name: 'batchAddupdate'
			})
		},
		nav_update(item) {
			this.$router.push({
				name: 'batchAddupdate',
				query: {
					dutyId: item.dutyId,
					type: 'details',
					dutyType: item.dutyType
				}
			})

		},
		/**
		 * 白名单列表
		 * @param row
		 * @returns {Promise<void>}
		 */
		async whitelist(row, type) {
			this.dutyId = row.dutyId
			this.type = type
			this.whitelistVisible = true
			const {
				result: {
					list
				}
			} = await this.$api.whiteUserList({
				dutyId: row.dutyId,
				type: type,
				pageSize: 1000,
				pageNum: 1,
			})
			this.whitelistData = list
		},
		async sousuo() {
			const {
				result: {
					list
				}
			} = await this.$api.whiteUserList({
				dutyId: this.dutyId,
				type: this.type,
				pageSize: 1000,
				pageNum: 1,
				contractAddress: this.keyword
			})
			this.whitelistData = list
		},
		nav_shop(item) {
			this.$router.push({
				name: 'ListOfWorks',
				query: {
					ctid: item.ctid
				}
			})
		},
		// 删除数据
		async stop(item) {
			const {
				status
			} = await this.$api.dutyStop({
				dutyId: item.dutyId,
				dutyType: item.dutyType
			})
			if (status.code === 0) {
				this.$message.success(status.msg)
				this.getList()
			}
		},
	}
}
</script>

<style lang="scss" scoped></style>
