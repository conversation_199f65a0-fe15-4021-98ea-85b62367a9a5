<template>
  <d2-container
    class="page"
    ref="returnTop"
  >
    <common-query
      :query-schema="querySchema"
      :data="query"
      :showCreation="true"
      @onCreation="onCreation"
      @onSubmit="onSubmit"
      @onReset="onReset"
    ></common-query>
    <common-table
      :table-schema="tableSchema"
      :showIndex="false"
      :table-data="tableData"
    >
      <template #action="scope">
        <el-button
          type="text"
          @click="update(scope.row)"
        >修改</el-button>
      </template>
    </common-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;"
    >
      <el-pagination
        background
        :total="totalCount"
        :page-size="pageSize"
        :current-page="pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange"
        @size-change="xuanzeSize"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="修改"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <!-- <div class="box"> -->
      <commonForm
        :submit="submit"
        :data="formData"
        :schema="formSchema"
        label-width="300px"
      ></commonForm>

    </el-dialog>

  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonQuery from '@/components/CommonQuery_h'
import commonForm from '@/components/CommonForm'
export default {
  components: {
    CommonTable,
    CommonQuery,
    commonForm
  },
  data () {
    return {
      tableData: [],
      totalCount: 0,
      dialogVisible: false,
      tableSchema: [ // 表格架构
        {
          label: 'id',
          field: 'id',
          width: '120px'
        },
        {
          label: '标题',
          field: 'title',
          width: '150px'
        },
        {
          label: '配图',
          field: 'iconUrl',
          type: 'img',
          width: '100px'
        },
        {
          label: '显示的时间',
          field: 'showTime',
          width: '100px'
        },
        {
          label: '权重',
          field: 'weight',
          width: '100px'
        },
        {
          label: '状态',
          field: 'showStatus',
          width: '100px',
          type: 'tag',
          tagMap: {
            0: {
              label: '隐藏',
              tagType: 'info'
            },
            1: {
              label: '显示',
              tagType: 'success'

            }
          }
        }, {
          label: '跳转链接',
          field: 'linkUrl',
          width: '100px'
        },

        {
          label: '操作',
          slot: 'action',
          width: '120px'
        }
      ],
      query: {
        dutyStatus: 'DOING'
      },
      querySchema: [ // 搜索组件架构
        {
          type: 'select',
          label: '状态：',
          placeholder: '请选择状态',
          field: 'status',
          options: [{
            label: '显示中',
            value: '1'
          }, {
            label: '隐藏中',
            value: '0'
          }
          ]
        },
        {
          type: 'input',
          label: '标题',
          placeholder: '请输入标题',
          field: 'title'
        }
      ],
      searchShow: '',
      searchTit: '',
      options: [{
        value: '1',
        label: '显示中'
      }, {
        value: '0',
        label: '隐藏中'
      }],
      formSchema: [{
        type: 'input',
        label: '标题名称：',
        placeholder: '请输入标题名称',
        field: 'title',
        rules: [{
          required: true,
          message: '请输入标题名称',
          trigger: 'blur'
        }]
      },
      {
        type: 'input',
        label: '权重：',
        placeholder: '请输入权重',
        field: 'weight',
        rules: [{
          required: true,
          message: '请输入权重',
          trigger: 'blur'
        }]
      },
      {
        type: 'input',
        label: '跳转链接：',
        placeholder: '请输入跳转链接',
        field: 'linkUrl',
        rules: [{
          required: true,
          message: '请输入跳转链接',
          trigger: 'blur'
        }]
      },
      {
        type: 'select',
        label: '状态',
        field: 'showStatus',
        options: [{
          label: '显示中',
          value: '1'
        }, {
          label: '隐藏中',
          value: '0'
        }]
      },
      {
        type: 'datetime',
        label: '显示的时间',
        field: 'showTime',
        placeholder: '请选择显示的时间',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请选择显示的时间',
          trigger: 'blur'
        }]
      },
      {
        type: 'img',
        label: '配图:',
        field: 'iconUrl'
      },
      {
        type: 'action'
      }
      ],
      type: '',
      pageNum: 1,
      pageSize: 8,
      formData: {
        title: '',
        weight: '',
        showStatus: '',
        showTime: '',
        iconUrl: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    async getList () { // 资讯列表
      const { result, status } = await this.$api.consultationList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        showStatus: this.searchShow || null,
        title: this.searchTit || null
      })
      if (status.code === 0) {
        this.tableData = result.list
        this.totalCount = result.totalCount
      }
    },
    async edit () { // 修改
      const { result, status } = await this.$api.editConfig({
        iconUrl: this.formData.iconUrl,
        showStatus: this.formData.showStatus,
        showTime: this.formData.showTime,
        title: this.formData.title,
        weight: this.formData.weight,
        id: this.formData.id,
        linkUrl: this.formData.linkUrl
      })
      if (status.code === 0) {
        this.$message({ type: 'success', message: '修改成功' })
        this.getList()
      }
      console.log(status, result, '修改')
    },
    update (row) { // 修改弹出
      this.dialogVisible = true
      this.type = 'update'
      this.formData = {
        id: row.id,
        title: row.title,
        weight: row.weight,
        showStatus: row.showStatus,
        showTime: row.showTime,
        iconUrl: row.iconUrl,
        linkUrl: row.linkUrl
      }
    },
    submit () { // 提交
      if (this.type === 'update') {
        this.edit()
      } else if (this.type === 'add') {
        this.add()
      }
    },
    async add () { // 新增
      const { status } = await this.$api.editConfigAdd({
        iconUrl: this.formData.iconUrl,
        showStatus: this.formData.showStatus,
        showTime: this.formData.showTime,
        title: this.formData.title,
        weight: this.formData.weight,
        linkUrl: this.formData.linkUrl
      })
      if (status.code === 0) {
        this.$message({ type: 'success', message: '添加成功' })
        this.getList()
      }
    },
    onCreation () { // 新增弹出
      this.dialogVisible = true
      this.type = 'add'
      this.formData = {
        title: '',
        weight: '',
        showStatus: '',
        showTime: '',
        linkUrl: ''
      }
    },

    onSubmit (e) { // 筛选
      this.searchShow = e.status
      this.searchTit = e.title
      this.getList()
    },
    onReset () { // 清除
      this.searchShow = ''
      this.searchTit = ''
      this.getList()
    },
    currentChange (value) {
      this.pageNum = value
      this.getList()
    },
    xuanzeSize (value) {
      this.pageSize = value
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  > div {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    > div:nth-child(1) {
      width: 30%;
      text-align: right;
      margin-right: 30px;
      font-size: 18px;
    }
  }
}
.dialog-footer {
  margin-left: 70%;
}
::v-deep .el-input--suffix .el-input__inner {
  width: 212px !important;
}
</style>
