<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" :loading="loading" label-width="300px">
      <template #basic-title>
        <div class="title">
          <span>基础配置</span>
        </div>
      </template>
      <template #ctid>
        <el-autocomplete style="width:340px;" v-model="formData.ctid" :fetch-suggestions="querySearchAsync"
          placeholder="请输入系列名/系列ID" @select="handleSelect" >
        </el-autocomplete>
      </template>
      <template #duty-type>
        <el-select  v-model="formData.dutyType" :disabled='isDetail' placeholder="请选择" @change="change">
          <el-option label="批量销毁" value="DESTROY">
          </el-option>
          <el-option label="批量变更寄售价格" value="CHANGE_ON_SALE_PRICE">
          </el-option>
          <el-option label="批量变更寄售状态" value="CHANGE_NOT_SALE_SIGN">
          </el-option>
        </el-select>
      </template>
      <template #works-title>
        <div class="title">
          <span>销毁作品选择</span>
        </div>
      </template>
      <template #form_destroy>
        <div class="div_left">
          <common-form :data="formDataDestroy" :schema="formSchemaDestroy" label-width="140px">
            <template #tid>
              <div class="flex_div">
                <el-button @click="downloadTemplate('MALL_CENTER_IMPORT_GOODS_DUTY_GOODS_LIST')" type="primary"
                  size="mini">
                  下载模版</el-button>
                <file-uploader id="ddd" :value.sync="formDataDestroy.tidImportUrl" style="width:200px;margin-left:10px"
                  text="上传模版"></file-uploader>

              </div>
            </template>
            <template #inDestroy>
              <div class="flex_div">
                <el-button @click="downloadTemplate('MALL_CENTER_IMPORT_GOODS_DUTY_GOODS_CONADDRESS_NUM')" type="primary"
                  size="mini">
                  下载模版</el-button>
                <file-uploader id="eee" :value.sync="formDataDestroy.conAddressAndNumUrl" style="width:200px;margin-left:10px"
                  text="上传模版"></file-uploader>
              </div>
            </template>

          </common-form>
        </div>
      </template>
      <template #form_sale_sign v-if="formData.dutyType=='CHANGE_NOT_SALE_SIGN'&&formData.notSaleSign==0&&!this.dutyId">
        <div class="div_left_2">
          <common-form :data="formDataSaleSign" :schema="formSchemaSaleSign" label-width="280px">
            <template #floorPrice="scope">
              <el-row justify="start">
                <el-col :span="2">
                  <div class="grid-content bg-purple">
                    <el-input placeholder="" v-model="formDataSaleSign.floorPriceMinus"></el-input>
                  </div>
                </el-col>
                <el-col :span="1">
                  <div class="grid-content bg-purple" style="text-align:center;">-</div>
                </el-col>
                <el-col :span="2">
                  <div class="grid-content bg-purple">
                    <el-input placeholder="" v-model="formDataSaleSign.floorPricePlus"></el-input>
                  </div>
                </el-col>
              </el-row>
            </template>
              <template #StartGapTime="scope">
                <el-row justify="start">
                  <el-col :span="2">
                    <div class="grid-content bg-purple flex">
                      <el-input placeholder="" v-model="formDataSaleSign.minStartGapTime"></el-input><span class="danwei">s</span>
                    </div>
                  </el-col>
                  <el-col :span="1">
                    <div class="grid-content bg-purple" style="text-align:center;">-</div>
                  </el-col>
                  <el-col :span="2">
                    <div class="grid-content bg-purple flex">
                      <el-input placeholder="" v-model="formDataSaleSign.maxStartGapTime"></el-input><span class="danwei">s</span>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template #LockTime="scope">
                <el-row justify="start">
                  <el-col :span="2">
                    <div class="grid-content bg-purple flex">
                      <el-input placeholder="" v-model="formDataSaleSign.minLockTime"></el-input><span class="danwei">s</span>
                    </div>
                  </el-col>
                  <el-col :span="1">
                    <div class="grid-content bg-purple" style="text-align:center;">-</div>
                  </el-col>
                  <el-col :span="2">
                    <div class="grid-content bg-purple flex">
                      <el-input placeholder="" v-model="formDataSaleSign.maxLockTime"></el-input><span class="danwei">s</span>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template #sleepTime="scope">
                <div class="flex">
                  <el-time-picker v-model="formDataSaleSign.sleepTimeStart" range-separator="至"
                    value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
                  </el-time-picker>
                  <span style="padding:0px 20px;">至</span>
                  <el-time-picker v-model="formDataSaleSign.sleepTimeEnd" range-separator="至"
                    value-format="HH:mm:ss" start-placeholder="开始时间" end-placeholder="结束时间" placeholder="选择时间范围">
                  </el-time-picker>
                </div>
              </template>
              <template #duandang>
                <div style="color:rgb(255, 29, 29)">
                  注：断档最大值只生效与相比地板价价格（不论大中小、主力）
                </div>
              </template>
              <template #msg>
                <div style="color:rgb(255, 29, 29)">
                  注意：请谨慎填写，提交后如要修改请联系BV客服
                </div>
              </template>
          </common-form>
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import CommonQuery from '@/components/CommonQuery_h'
import {
  mapActions
} from 'vuex'
import {
  downloadBlob
} from '@/utils/helper'

export default {
  name: 'batchAddupdate',
  components: {
    CommonForm,
    CommonTable,
    FileUploader,
    CommonQuery
  },
  data() {
    const validatepreSale1 = (rule, value, callback) => {
      console.log(111)
      if (this.formDataDestroy.length <= 0) {
        callback(new Error('请添加数据'))
      } else {
        this.goodsList.forEach(item => {
          if (!item.contractAddress) {
            callback(new Error('请输入contractAddress'))
          } else if (!item.num) {
            callback(new Error('请填写购买数量'))
          }
        })
        callback()
      }
    }
    return {
      templateUrl: '', // 盲盒内作品模板地址
      templateUrl1: '', // 盲盒内作品模板地址
      isDetail: false, // 详情
      activityNo: null, // 活动编号
      preSale1TableSchema: [ // 优先购表格结构
        {
          label: '系列id',
          field: 'ctid'
        },
        {
          label: 'tokenid',
          field: 'tid'
        },
        {
          label: '作品名称',
          field: 'title'
        },
        {
          label: '操作',
          slot: 'action',
          width: '120px',
          headerSlot: 'action-header'
        }
      ],
      goodsList: [], // 优先购数据
      preSale2TableSchema: [ // 优先抢表格结构
        {
          label: '系列id',
          field: 'ctid'
        },
        {
          label: 'tokenid',
          field: 'tid'
        },
        {
          label: '作品名称',
          field: 'name'
        }
      ],
      preSale2TableData: [], // 优先抢数据
      formData: {
        dutyType: 'DESTROY',
        operateTime: '',
        member: null,
        ctid: '',
        csName: '',
        importType: '',
        notSaleSign: -1,
        onSaleMaxPrice: '',
        isTiming: 0,
        isCtid: "1"
      },
      formSchema: [{
        type: 'action',
        slot: 'basic-title'
      },
      {
        type: 'select',
        label: '任务类型：',
        placeholder: '',
        field: 'dutyType',
        slot: 'duty-type',
        options: [{
          label: '批量销毁',
          value: 'DESTROY'
        },
        {
          label: '批量变更寄售价格',
          value: 'CHANGE_ON_SALE_PRICE'
        },
        {
          label: '批量变更寄售状态',
          value: 'CHANGE_NOT_SALE_SIGN'
        },
        ],
        rules: [{
          required: true,
        }]
      },
      {
        type: 'input',
        label: '任务名称：',
        placeholder: '请输入任务名称',
        field: 'dutyName',
        maxlength: 20,
        // rules: [{
        //   required: true,
        //   message: '请输入任务名称',
        //   trigger: 'blur'
        // },]
      },
      {
        type: 'radio',
        label: '是否定时：',
        field: 'isTiming',
        options: [{
          label: '定时执行',
          value: 1
        },
        {
          label: '立即执行',
          value: 0
        },
        ],
        rules: [{
          required: true,
        }],
      },
      {
        type: 'datetime',
        label: '执行时间：',
        placeholder: '请输入执行时间',
        field: 'operateTime',
        rules: [{
          required: true,
          message: '请输入执行时间',
          trigger: 'blur'
        }],
        show: {
          relationField: 'isTiming',
          value: [1]
        },
      },
      {
        type: 'input',
        label: '系列id：',
        placeholder: '请输入系列id',
        slot: 'ctid',
        maxlength: 40,
        rules: [{
          required: true,
          message: '请输入系列id',
          trigger: 'blur'
        },],
        show: {
          relationField: 'isCtid',
          value: ['1']
        },
      },
      {
        type: 'action',
        slot: 'works-title',
        show: {
          relationField: 'dutyType',
          value: 'DESTROY'
        },
      },
      {
        slot: 'form_destroy',
        show: {
          relationField: 'dutyType',
          value: 'DESTROY'
        },
      },
      {
        type: 'input',
        label: '寄售价格：',
        placeholder: '请输入寄售价格',
        field: 'onSaleMaxPrice',
        maxlength: 20,
        rules: [{
          required: true,
          message: '请输入寄售价格',
          trigger: 'blur'
        },],
        show: {
          relationField: 'dutyType',
          value: 'CHANGE_ON_SALE_PRICE'
        },
      },
      {
        type: 'radio',
        label: '选择寄售状态：',
        field: 'notSaleSign',
        options: [{
          label: '不变更',
          value: -1
        }, {
          label: '正常流通',
          value: 0
        },
        {
          label: '仅供收藏',
          value: 1
        },
        {
          label: '仅供转增',
          value: 2
        },
        ],
        show: {
          relationField: 'dutyType',
          value: ['CHANGE_NOT_SALE_SIGN', 'CHANGE_ON_SALE_PRICE']
        },
      },
      {
        slot: 'form_sale_sign',
        show: {
          relationField: 'dutyType',
          value: 'CHANGE_NOT_SALE_SIGN'
        }
      },
      {
        type: 'action'
        // exclude: ['reset', 'submit', 'back']
      }
      ],
      query: "",
      formSchemaDestroy: [ // 搜索组件架构
        {
          type: 'radio',
          label: '选择',
          field: 'original',
          options: [{
            label: '一级作品',
            value: "0"
          },
          {
            label: '主力二级藏品',
            value: "1"
          },
          {
            label: '所有系列',
            value: "2"
          },
          {
            label: '指定token',
            value: "3"
          },
          {
            label: '散户二级藏品',
            value: "4"
          },
          ],
          rules: [{
            required: true,
          }],
        }, {
          type: 'input',
          label: '销毁数量',
          field: 'destroyNum',
          rules: [{
            required: true,
            message: '请输入销毁数量',
          }],
          show: {
            relationField: 'original',
            value: ['0', '1', '2']
          },
        }, {
          type: 'input',
          label: '销毁耗时(秒/整数)',
          field: 'destroyTime',
          rules: [{
            required: true,
            message: '请输入销毁耗时',
          }],
          show: {
            relationField: 'original',
            value: ['0', '1', '2']
          },
        }, {
          slot: "tid",
          label: '指定token导入',
          show: {
            relationField: 'original',
            value: ['3']
          },
        },{
          slot: "inDestroy",
          label: '导入销毁',
          show: {
            relationField: 'original',
            value: ['4']
          },
        }
      ],
      formDataDestroy: {
        destroyTime: "",
        destroyNum: "",
        csName: "",
        original: "0",
        itemType: "CTID"
      },
      formSchemaSaleSign: [ // 搜索组件架构
        {
          label: '开寄售后是否交易：',
          field: 'isAddAutoTrade',
          type: 'radio',
          options: [{
            label: '是',
            value: 1
          }, {
            label: '否',
            value: 0
          }]
        },
          {
            type: 'number-input',
            label: '断档最大值：',
            placeholder: '请输入断档最大值',
            field: 'maxGapPrice',
            rules: [{
              required: true,
              message: '请输入断档最大值',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '间隔多久上架交易一单：',
            slot:'StartGapTime',
            rules: [{
              required: true,
              message: '请输入间隔多久上架交易一单',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '',
            slot: 'duandang',
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '每次交易前锁单多久：',
            slot:'LockTime',
            rules: [{
              required: true,
              message: '请输入每次交易前锁单多久',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '相比地板价价格（不论大中小、主力）：',
            placeholder: '请输入相比地板价价格',
            slot: 'floorPrice',
            rules: [{
              required: true,
              message: '请输入相比地板价价格',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '',
            slot: 'floorPrice_radio',
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            type: 'datetimerange',
            label: '开始时间：',
            placeholder: '请选择开始时间',
            field: 'startTimeView',
            rules: [{
              required: true,
              message: '请选择开始时间',
              trigger: 'blur'
            }],
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '深夜是否休息：',
            field: 'isSleep',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            type: 'timerange',
            label: '休息时段：',
            placeholder: '请输入休息时段',
            slot: 'sleepTime',
            rules: [{
              required: true,
              message: '请输入休息时段',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isSleep',
              value: [1]
            },
          },
          {
            type: 'number-input',
            label: '任务复制几次：',
            placeholder: '请输入任务复制几次',
            field: 'copyNum',
            rules: [{
              required: true,
              message: '请输入任务复制几次',
              trigger: 'blur'
            }],
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
          {
            label: '',
            slot: 'msg',
            show: {
              relationField: 'isAddAutoTrade',
              value: [1]
            },
          },
      ],
      formDataSaleSign: {
          floorPriceMinus:0,
          floorPricePlus:0,
          minLockTime:30,
          maxLockTime:120,
          isSleep:0,
          copyNum:0,
          sleepTimeStart:"",
          sleepTimeEnd:"",
          isAddAutoTrade:0
      },
      loading: false
    }
  },
  watch: {
    formDataDestroy: {
      // 执行方法
      handler(newValue) {
        if (newValue.original == "3") {
          this.formData.isCtid = "0"
        } else {
          this.formData.isCtid = "1"
        }
      },
      deep: true, // 深度监听
    },
  },
  mounted() {
    const {
      type,
      activityType,
      dutyId,
      dutyType
    } = this.$route.query
    this.isDetail = type === 'details'
    this.dutyId = dutyId
    this.dutyType = dutyType
    dutyId && this.getDetail()
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    // 优先购导入模版
    async importTemplate(data) {
      const {
        status,
        result
      } = await this.$api.goodsImport({
        dutyType: this.formData.dutyType,
        importUrl: data.result?.url
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.goodsList = result.list
      }
    },
    // 优先抢导入模版
    async importTemplate1(data) {
      const {
        status,
        result
      } = await this.$api.whiteUserImport({
        importUrl: data.result?.url
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.preSale2TableData = result.list
      }
    },
    routerBack() {
      const {
        fullPath
      } = this.$route
      this.close({
        tagName: fullPath
      })
      this.$router.back()
    },
    async getDetail() {
      const {
        status,
        result
      } = await this.$api.dutyInfo({
        dutyId: this.dutyId,
        dutyType: this.dutyType
      })
      if (status.code === 0) {
        const {
          goodsList,
          preSaleWhiteUserList2,
          ...rest
        } = result
        this.formData = {
          ...rest,
        }
        this.queryData = {
          ...rest
        }
        if (this.dutyType == 'DESTROY') {
          if (this.formData.ctid) {
            this.formData.ctid = `${this.formData.destroyExtra.csName}(${this.formData.ctid})`
          }
        } else {
          if (this.formData.ctid) {
            this.formData.ctid = `${this.formData.csName}(${this.formData.ctid})`
          }
        }
        if (this.dutyType == 'DESTROY') {
          if (this.formData.destroyExtra.itemType == "TID") {
            this.formData.destroyExtra.original = '3'
          } else {
            if (this.formData.destroyExtra.original == null) {

              this.formData.destroyExtra.original = "2"
            } else {
              this.formData.destroyExtra.original = `${this.formData.destroyExtra.original}`
            }
          }
          this.formDataDestroy = this.formData.destroyExtra
        } else {
          this.formData.isCtid = "1"
        }
      }
      if(this.formData.isTiming==null){
          this.formData.isTiming=0
      }
      console.table(this.formData)
    },
    // 添加可熔炼藏品
    addCollectionItem() {
      this.collectionTableData.push({
        picCoverSmelting: '',
        smeltingCollectionExcelUrl: ''
      })
    },
    // 添加盲盒结果
    addResultItem() {
      this.tableData.push({
        prizePicture: ''
      })
    },
    deleteItem(index, data) {
      data.splice(index, 1)
    },
    async downloadTemplate(templateTag) {
      const {
        status,
        result
      } = await this.$api.downLoadTemplateExcel({
        templateTag
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    async downloadExcel(materialsUnqNo) {
      const res = await this.$api.activityDownloadExcel({
        materialsUnqNo
      })
      if (res) {
        downloadBlob(res, '作品')
        this.$message.success('下载成功')
      }
    },
    async submit() {
      let ctid, original,dutyName;
      if (this.formDataDestroy.original == '2') {
        original = null
      } else {
        original = this.formDataDestroy.original
      }
      if (this.formDataDestroy.original == '3') {
        this.formDataDestroy.itemType = "TID"
      }else if(this.formDataDestroy.original == '4'){
         this.formDataDestroy.itemType = "conAddAndNum"
      } else {
        this.formDataDestroy.itemType = "CTID"
      }
      console.table(this.formData)
      if (this.formData.ctid) {
        ctid = this.formData.ctid.split('(')[1].split(')')[0]
        dutyName = this.formData.ctid.split('(')[0]
      }
      if (this.formData.destroyExtra) {
        this.formData.destroyExtra = JSON.stringify(this.formData.destroyExtra)
      }
      let startTime, endTime
      if(this.formData.dutyType=='CHANGE_NOT_SALE_SIGN'&&this.formData.notSaleSign==0&&this.formDataSaleSign.isAddAutoTrade == 1){
        if(this.formDataSaleSign.startTimeView){
          startTime = this.formDataSaleSign.startTimeView[0]
          endTime = this.formDataSaleSign.startTimeView[1]
        }else{
            this.$message.error('请选择开始时间')
           return false
        }
        this.formDataSaleSign={
           ...this.formDataSaleSign,
           startTime,
           endTime
        }
      }
      const extraJson = {
        ...this.formDataDestroy,
        autoTradeExtra:this.formDataSaleSign,
        original
      }
      console.log(extraJson)
      const data = {
        dutyId: this.dutyId,
        ...this.formData,
        ...this.query,
        ...this.formDataDestroy,
        ctid,
        dutyName,
        goodsList: JSON.stringify(this.goodsList),
        extra: JSON.stringify(extraJson),
      }
      console.log(data)
      this.$confirm('是否确认提交保存？', '确认提交保存', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true
        if (this.dutyId) {
          const {
            status
          } = await this.$api.dutyEdit(data)
          if (status.code === 0) {
            this.loading = false
            this.routerBack()
          } else {
            this.loading = false
          }
        } else {
          const {
            status
          } = await this.$api.dutyAdd(data)
          if (status.code === 0) {
            this.routerBack()
          } else {
            this.loading = false
          }
        }
      })
    },
    onQueryChange(data) {
      console.log(data)
      if (data.ctid) {
        data.ctid = data.ctid.split('(')[1].split(')')[0]
      }
      this.query = data
      this.getWorks(data)
    },
    async getWorks(data) {
      let res = await this.$api.goodsList({
        ...data,
        pageSize: 10000,
        pageNum: 1
      });
      console.log(res)
      if (res.status.code == 0) {
        this.preSale2TableData = res.result.list
      } else {
        this.$message.error(res.status.msg)
      }
    },
    change(e) {
      this.formData.isCtid = "1"
      this.formDataDestroy.original = "0"
      if (e != 'DESTROY') {
        this.formData.importType = ''
      }
      console.log(e)
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item);
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
              })
            })
            console.log(this.results)
          }
        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding-top: 80px;
}

::v-deep .div_left {
  margin-left: -100px !important;
}
::v-deep .div_left_2{
  margin-left: -290px !important;
}
.flex{
  display: flex;
   justify-content: flex-start;
}
.title {
  width: 200px;
  margin-left: -100px;

  span {
    border-left: 8px solid #00aaff;
    font-weight: 700;
    font-size: 20px;
    padding-left: 10px;
    text-align: right;
  }
}

.flex_div {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 700px;
  height: 50px;
}

.uploader {
  margin-top: 0 !important;
}
</style>
