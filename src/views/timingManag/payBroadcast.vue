<template>
	<d2-container class="page">
		<common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange" :showRefresh="true"></common-query>
		<common-table :table-schema="tableSchema" :table-data="tableData" >
      <template #ratio0="scope">
      	{{scope.row.ratio0}}%
      </template>
      <template #ratio4="scope">
      	{{scope.row.ratio4}}%
      </template>

			<template #action-header>
				<el-button @click="nav_add" type="primary" size="mini">添加任务</el-button>
			</template>
			<template #action="scope">
				<el-popconfirm style="margin-left: 10px;" v-if="scope.row.status==1" title="确定要终止吗？" @confirm="endTask(scope.row)">
					<el-button slot="reference" type="text">终止</el-button>
				</el-popconfirm>
			</template>
		</common-table>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
			</common-pagination>
		</div>
		<!-- <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
	</d2-container>
</template>

<script>
	import CommonQuery from '@/components/CommonQuery_h'
	import CommonTable from '@/components/CommonTable'
		import CommonPagination from '@/components/CommonPagination'
	export default {
		name: 'payBroadcast',
		components: {
			CommonQuery,
			CommonTable,
			CommonPagination
		},
		props: {},
		data() {
			return {
				dialogVisible: false, // 弹窗
				selected: '', // 选择的活动类型
				page: {
					totalCount: 0,
					pageSize: 10
				}, // 分页数据
				querySchema: [ // 搜索组件架构
					{
						type: 'input',
						label: '藏品名：',
						placeholder: '藏品名',
						field: 'csName'
					},
					{
						type: 'select',
						label: '任务类型：',
						placeholder: '',
						field: 'status',
						options: [{
								label: '准备开始',
								value: 0
							},
							{
								label: '执行中',
								value: 1
							},
							{
								label: '已结束',
								value: 2
							},
						],
						rules: [{
							required: true,
						}]
					}
				],
				tableSchema: [ // 表格架构
					{
						label: '藏品名称',
						field: 'csName',
					},
          {
          	label: '4播报比例',
          	field: 'ratio4',
            slot:'ratio4'
          },
          {
          	label: '0播报比例',
          	field: 'ratio0',
            slot:'ratio0'
          },
          {
          	label: '开始时间',
          	field: 'startTime',
          },
          {
          	label: '当前状态',
          	field: 'status',
          	type: 'tag',
          	tagMap: {
          		0: {
          			label: '准备开始',
          			tagType: 'info'
          		},
          		1: {
          			label: '执行中',
          			tagType: 'success'
          		},
          		2: {
          			label: '已结束',
          			tagType: 'error'
          		}
          	},
          },
          {
          	label: '结束时间',
          	field: 'endTime',
          },
					{
						label: '操作',
						slot: 'action',
						headerSlot: 'action-header',
						width: '140px',
						fixed:'right'
					}
				],
				tableData: [{}],
				whitelistData: [],
				whitelistVisible: false,
				whitelistColumn: [{
						label: '用户地址',
						prop: 'contractAddress'
					},
					{
						label: '购买数量',
						prop: 'num'
					},
				],
				searchWhitelist: "",
				keyword:"",
				type:1,
				page: {
					totalCount: 0,
					pageSize: 10,
					pageNum: 1
				}, // 分页数据
        query:{}
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			// 过滤查询
			onQueryChange(data) {
				this.query = data
        this.page.pageNum=1
				this.getList()
			},
			// 分页改变
			currentChange(value) {
				this.page.pageNum = value
				this.getList()
			},
			// 获取列表
			async getList(isInit) {
				const params = {
					...this.query,
					...this.page,
          status:this.query.status===""?null:this.query.status,
					pageNum: isInit ? 1 : this.page.pageNum,
				}
				const {
					status,
					result
				} = await this.$api.imOrderConfigList(params)
				if (status.code === 0) {
					this.tableData = result.list
					this.page.totalCount = result.totalCount
					this.page.pageSize = result.pageSize
					this.page.pageCount = result.pageCount
				}
			},
      nav_add(){
        this.$router.push({
          name:'payBroadcastAdd'
        })
      },
      onRefresh(data){
        this.query = data
        this.getList()
      },
			async endTask(item){
				const res = await this.$api.imOrderConfigListEnd({
					id: item.id,
				})
        if(res.status.code==0){
          this.$message.success('终止成功');
          this.getList()
        }
			},
		}
	}
</script>

<style lang="scss" scoped>

</style>
