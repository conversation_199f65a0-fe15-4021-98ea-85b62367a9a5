<template>
	<d2-container class="page">
		<common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
		<!-- <div style="margin-bottom:15px;" >
			<el-button @click="nav_add" type="primary" size="mini">创建任务</el-button>
		</div> -->
		<common-table :table-schema="tableSchema" :table-data="tableData">
			<template #action-header>
				<el-button @click="nav_add" type="primary" size="mini">新建任务</el-button>
			</template>
			<template #duty-type="scope">
				{{ scope.row }}
			</template>
			<template #action="scope">
				<el-popconfirm v-if="scope.row.dutyStatus == 'INIT' || scope.row.dutyStatus == 'DOING'"
					style="margin-left: 10px;" title="确定要终止吗？" @confirm="stop(scope.row)">
					<el-button slot="reference" type="text">终止任务</el-button>
				</el-popconfirm>
			</template>
		</common-table>
		<el-dialog title="新建任务" :visible.sync="addVisible">
			<commonForm :submit="submit" :data="formData" :schema="formSchema" label-width="300px"></commonForm>
		</el-dialog>
		<div class="" style="display: flex; justify-content: center; background-color: #ffffff">
			<common-pagination ref="commonPagination" :page.sync="page" @change="getList">
			</common-pagination>
		</div>
		<!-- <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange"
			layout="prev, pager, next" :total="page.totalCount">
		</el-pagination> -->
	</d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import commonForm from '@/components/CommonForm'
import CommonPagination from '@/components/CommonPagination'
export default {
	name: 'ActivityManage',
	components: {
		CommonQuery,
		CommonTable,
		commonForm,
		CommonPagination
	},
	props: {},
	data() {
		return {
			dialogVisible: false, // 弹窗
			selected: '', // 选择的活动类型
			page: {
				totalCount: 0,
				pageSize: 10
			}, // 分页数据
			querySchema: [ // 搜索组件架构
				{
					type: 'search',
					label: '系列名称：',
					placeholder: '系列名称',
					field: 'ctid'
				},
			],
			tableSchema: [ // 表格架构
				{
					label: '创建时间',
					field: 'createAt'
				},
				{
					label: 'ctid',
					field: 'ctid',
				},
				{
					label: '系列名',
					field: 'dutyName',
				},
				{
					label: '创建时流通数',
					field: 'createActiveNum',
				},
				{
					label: '是否变更总量',
					field: 'isChangeGoodsCount',
					type: 'tag',
					tagMap: {
						1: {
							label: '总量流通',
							tagType: 'info'
						},
						0: {
							label: '流通',
							tagType: 'success'
						},

					},
				},
				{
					label: '增加/减少',
					field: 'isAdd',
					type: 'tag',
					tagMap: {
						1: {
							label: '增加',
							tagType: 'info'
						},
						0: {
							label: '减少',
							tagType: 'success'
						},

					},
				},
				{
					label: '变化数量',
					field: 'changeNum',
				},
				{
					label: '开始执行时间',
					field: 'startTime',
				},
				{
					label: '执行耗时',
					field: 'changeTime',
				},
				{
					label: '任务状态',
					field: 'dutyStatus',
					type: 'tag',
					tagMap: {
						INIT: {
							label: '准备中',
							tagType: 'info'
						},
						DOING: {
							label: '进行中',
							tagType: 'success'
						},
						STOPPING: {
							label: '终止中',
							tagType: 'danger'
						},
						STOP: {
							label: '已终止',
							tagType: 'danger'
						},
						DONE: {
							label: '已完成',
							tagType: 'success'
						},
						FAIL: {
							label: '执行失败',
							tagType: 'danger'
						}
					},
					width: '80px',
				},
				{
					label: '备注',
					field: 'remark',
				},
				{
					label: '操作',
					slot: 'action',
					headerSlot: 'action-header',
					width: '140px',
					fixed: 'right'
				}
			],
			tableData: [{}],
			page: {
				totalCount: 0,
				pageSize: 10,
				pageNum: 1
			}, // 分页数据
			query: {

			},
			addVisible: false,
			formSchema: [{
				type: 'search',
				label: '藏品名称：',
				placeholder: '请输入藏品名称',
				field: 'ctid',
				rules: [{
					required: true,
					message: '请输入藏品名称',
					trigger: 'blur'
				}]
			},
			{
				label: '流通数增加/减少',
				field: 'isAdd',
				type: 'radio',
				options: [{
					label: '增加',
					value: 1
				}, {
					label: '减少',
					value: 0
				}]
			},
			{
				label: '数量变化点',
				field: 'isChangeGoodsCount',
				type: 'radio',
				options: [{
					label: '总量流通',
					value: 1
				}, {
					label: '流通',
					value: 0
				}]
			},
			{
				type: 'number-input',
				label: '变化数量',
				field: 'changeNum',
				rules: [{
					required: true,
					message: '请输入变化数量',
					trigger: 'blur'
				}],
			},
			{
				type: 'datetime',
				label: '开始执行时间',
				field: 'startTime',
				placeholder: '请选择开始时间',
				pickerOptions: {
					disabledDate: (time) => {
						return time.getTime() < Date.now() - 86400000
					}
				},
				rules: [{
					required: true,
					message: '请选择开始时间',
					trigger: 'blur'
				}],
			},
			{
				type: 'number-input',
				label: '执行耗时',
				field: 'changeTime',
				rules: [{
					required: true,
					message: '请输入执行耗时',
					trigger: 'blur'
				}],
			},
			{
				type: 'action'
			},
			],
			formData: {
				isAdd: 1,
				isChangeGoodsCount: 1,
				changeTime: 1
			}
		}
	},
	mounted() {
		this.getList()
	},
	methods: {
		// 过滤查询
		onQueryChange(data) {
			this.query = data
			this.getList(true)
		},
		// 分页改变
		currentChange(value) {
			this.page.pageNum = value
			this.getList()
		},
		async submit() {
			let ctid;
			if (this.formData.ctid) {
				ctid = this.formData.ctid.split("(")[1].split(")")[0]
			}
			let extra = JSON.stringify(this.formData)
			const data = {
				...this.formData,
				ctid,
				dutyType: 'BASE_GOODS_COUNT_CHANGE',
				extra
			}
			this.$confirm('是否确认提交保存？', '确认提交保存', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				const {
					status
				} = await this.$api.dutyAdd(data)
				if (status.code === 0) {
					this.addVisible = false
					this.getList()
					this.formData = {
						isAdd: 1,
						isChangeGoodsCount: 1,
						changeTime: 1
					}
				}
			})
		},
		// 获取列表
		async getList(isInit) {
			let ctid;
			if (this.query.ctid) {
				ctid = this.query.ctid.split("(")[1].split(")")[0]
			}
			const params = {
				...this.query,
				...this.page,
				pageNum: isInit ? 1 : this.page.pageNum,
				ctid,
				dutyType: 'BASE_GOODS_COUNT_CHANGE'
			}
			const {
				status,
				result
			} = await this.$api.dutyList(params)
			if (status.code === 0) {
				let dataList = []
				const data = result.list
				data.forEach((item) => {
					dataList.push({
						...item.baseGoodsCountChangeExtraVO,
						ctid: item.ctid,
						startTime: item.startTime,
						endTime: item.endTime,
						dutyId: item.dutyId,
						createAt: item.createAt,
						dutyStatus: item.dutyStatus,
						dutyName: item.dutyName,
						remark: item.remark
					})
				})
				this.tableData = dataList
				this.page.totalCount = result.totalCount
				this.page.pageSize = result.pageSize
				this.page.pageCount = result.pageCount
			}
		},
		async clearRedis(item) {
			const {
				status
			} = await this.$api.clearRedis({
				activityNo: item.activityNo,
				activityType: item.type
			})
			if (status.code === 0) {
				this.$message.success(status.msg)
			}
		},
		// 删除数据
		async deleteItem(item) {
			if (item.isCanEdit === 0) {
				this.$message.error("不可删除！！！！")
			} else {
				const {
					status
				} = await this.$api.dutyDelete({
					dutyId: item.dutyId,
					dutyType: item.dutyType
				})
				if (status.code === 0) {
					this.$message.success(status.msg)
					this.getList()
				}
			}
		},
		nav_add() {
			this.addVisible = true
		},
		// 删除数据
		async stop(item) {
			const {
				status
			} = await this.$api.dutyStop({
				dutyId: item.dutyId,
				dutyType: 'BASE_GOODS_COUNT_CHANGE'
			})
			if (status.code === 0) {
				this.$message.success(status.msg)
				this.getList()
			}
		},

	}
}
</script>

<style lang="scss" scoped></style>
