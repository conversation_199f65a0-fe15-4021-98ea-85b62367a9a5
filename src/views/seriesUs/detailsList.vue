<template>
  <d2-container class="page">
    <common-table :table-schema="tableSchema"  :showIndex="false" :table-data="tableData">
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
  import {
    holdList
  } from '@/api/mallCenter'
  export default {
    name: 'scan',
    components: {
      CommonTable,
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: '用户昵称',
            field: 'nickname',
          },
          {
            label: '用户实名',
            field: 'realname',
          },
          {
            label: '持有份数',
            field: 'goodsCount',
          },
          {
            label: '持仓均价',
            field: 'avgBuyPrice',
          },
          {
            label: '用户地址',
            field: 'contractAddress',
          },
        ],
        tableData: [],
      }
    },
    mounted() {
      let ctid = this.$route.query.ctid
      this.getList(ctid)
    },
    methods: {
      toEdit(item) {
        console.log(item)
        this.$router.push({
          name: 'scanAdd',
          query: {
            templateId: item.dutyId,
          }
        })
      },
      nav_task() {
        this.$router.push({
          name: 'scanAdd',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList(ctid) {
        const params = {
          ...this.query,
          ...this.page,
          ctid
        }
        holdList(params).then(res => {
          this.tableData = res.result.list
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
     async submit() {
       const data = {
         ...this.formData,
         photoShow: JSON.parse(localStorage.getItem('img_result')).mediumImageUrl,
         photo: JSON.parse(localStorage.getItem('img_result')).url,
       }
       const {
         status
       } = await this.$api.scaleBatchCreate(data)
       if (status.code === 0) {
         this.$message.success('新增系列成功')
         this.isDialog = false
         this.formData = {}
         this.getList()
       } else {
         // this.$message.success(status.msg)
       }
     },
     async showClick(item) {
       const {
         status,
         result
       } = await this.$api.holdCount({
         ctid:item.ctid
       })
       if (status.code === 0) {
         this.show = true
         this.info = result
       }
     },
    }
  }
</script>

<style lang="scss" scoped>

</style>
