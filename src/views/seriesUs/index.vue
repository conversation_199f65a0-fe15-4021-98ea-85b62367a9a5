<template>
  <d2-container class="page">
    <el-button type="primary" size="mini" @click="isDialog = true" style="margin-bottom:20px;">新增系列</el-button>
     <el-button type="primary" size="mini" @click="getList(true),page.pageNum = 1" style="margin-bottom:20px;">刷新</el-button>
    <common-table :table-schema="tableSchema" :loading="loading" :showIndex="false" :table-data="tableData">
      <template #action="scope">
        <el-button @click="toEdit(scope.row)" type="text">修改</el-button>
      </template>
      <template #u0GoodsCount="scope">
        <span v-if="scope.row.show">{{scope.row.u4GoodsCount}}</span>
        <el-button @click="showClick(scope.row)" type="text" v-else>查看</el-button>
      </template>
      <template #u4GoodsCount="scope">
        <span v-if="scope.row.show">{{scope.row.u0GoodsCount}}</span>
        <el-button @click="showClick(scope.row)" type="text" v-else>查看</el-button>
      </template>

      <template #maxTargetPrice="scope">
        <span>{{scope.row.maxTargetPrice}}</span>
        <el-button @click="updataFunction('请输入要修改的求购最高风控线（主',1,scope.row.ctid,scope.row.maxTargetPrice)" type="text">修改</el-button>
      </template>
      <template #minOnSalePrice="scope">
        <span>{{scope.row.minOnSalePrice}}</span>
        <el-button @click="updataFunction('请输入要修改的最低挂售风控线（主）',2,scope.row.ctid,scope.row.minOnSalePrice)" type="text">修改</el-button>
      </template>
      <template #weight="scope">
        <span>{{scope.row.weight}}</span>
        <el-button @click="updataFunction('请输入要修改的权重​',3,scope.row.ctid,scope.row.weight)" type="text">修改</el-button>
      </template>
      <template #status="scope">
        <span style="color:#6ac244;" v-show="scope.row.status == 1">开启交易中</span>
        <span style="color:#EC3F67" v-show="scope.row.status == 0">停止交易中</span>
        <el-button @click="updataStatus(scope.row)" type="text">{{scope.row.status == 0?'开启':'停止'}}</el-button>
      </template>
      <template #nav_list="scope">
        <el-button type="text" @click="nav_details(scope.row)">查看</el-button>
        <el-button @click="batchCsv(scope.row)" type="text">导出</el-button>
      </template>
      <template #action="scope">
      	<el-button @click="openUpdata(scope.row)" type="text">修改附加参数</el-button>
      </template>
      <template #img="scope">
        <el-image style="width: 40px; height: 40px" :src="scope.row.cover"
        	:preview-src-list="[scope.row.cover]">
        </el-image>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
    <el-dialog title="新增系列" :visible.sync="isDialog" center width="700px">
      <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="200px" :loading="addLoading"></common-form>
    </el-dialog>
    <el-dialog title="修改附加参数" :visible.sync="isDialog2" center width="700px">
      <common-form :submit="submitExtra" :data="formDataExtra" :schema="formSchemaExtra" label-width="200px"></common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    holdCount,
    scaleBatchCreate,
    scaleList,
    scaleUpdate,
    holdListExport
  } from '@/api/mallCenter'
  export default {
    name: 'scan',
    components: {
      CommonQuery,
      CommonTable,
      CommonForm
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'ctid',
            width: "120px"
          },
          {
            label: '英文名称',
            field: 'title',
            width: '80px'
          },
          {
            label: '中文名称',
            field: 'titleCn',
            width: '80px'
          },
          {
            slot: 'img',
            label: '图片',
            width: '80px'
          },
          {
            label: '铸造份数',
            field: 'createNum',
            width: '100px'
          },
          {
            label: '求购最高风控线（主）',
            slot: 'maxTargetPrice',
             width: '100px'
          },
          {
            label: '最低挂售风控线（主）',
            slot: 'minOnSalePrice',
             width: '100px'
          },
          {
            label: '状态',
            slot: 'status',
            width: '82px'
          },
          {
            label: '当前二级总份数',
            field: 'activeNum2',
            width: '100px'
          },
          {
            label: '主力份数',
            slot: 'u0GoodsCount',
            width: '80px'
          },
          {
            label: '大户份数',
            slot: 'u4GoodsCount',
            width: '80px'
          },
          {
            label: '查看用户持仓',
            slot: 'nav_list',
           width: '100px'
          },
          {
            label: '主a',
            field: 'emailA',
            width: '100px'
          },
          {
            label: '主b',
            field: 'emailB',
            width: '100px'
          },
          {
            label: '作品描述​',
            field: 'content',
            width: '100px',
            showOverflowTooltip:true
          },
          {
            label: '权重​',
            slot: 'weight',
            width: '70px'
          },
         {
         	label: '操作',
         	slot: 'action',
         	width: '140px',
         }
        ],
        tableData: [],
        loading: false,
        isDialog: false,
        isDialog2: false,
        formSchema: [{
            type: 'input',
            label: '英文名称',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入名称',
              trigger: 'change'
            }]
          },
          {
              type: 'input',
              label: '中文名称',
              field: 'titleCn',
              rules: [{
                required: true,
                message: '请输入中文名称',
                trigger: 'change'
              }]
            },
          {
            type: 'img',
            label: '图标',
            field: 'cover',
            multigraph: true,
            rules: [{
              required: true,
              message: '请输入图标',
              trigger: 'change'
            }]
          },
          {
            type:'textarea',
            label: '作品描述',
            field: 'content',
            height:100,
            rules: [{
              required: true,
              message: '请输入作品描述',
              trigger: 'change'
            }]
          },
          {
            type: 'input',
            label: '铸造份数',
            field: 'createNum',
            rules: [{
              required: true,
              message: '请输入铸造份数',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: '求购最高风控线',
            field: 'maxTargetPrice',
            rules: [{
              required: true,
              message: '请输入求购最高风控线',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: '最低挂售风控线​',
            field: 'minOnSalePrice',
            rules: [{
              required: true,
              message: '请输入最低挂售风控线',
              trigger: 'change'
            }]
          },
          {
            type: 'input',
            label: '主力a账户(邮箱)',
            field: 'emailA',
            rules: [{
              required: true,
              message: '请输入主力a账户',
              trigger: 'change'
            }]
          },
          {
            type: 'input',
            label: '主力b账户(邮箱)',
            field: 'emailB',
            rules: [{
              required: true,
              message: '请输入主力b账户',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: '权重',
            field: 'weight',
            rules: [{
              required: true,
              message: '请输入权重',
              trigger: 'change'
            }]
          },
          {
            slot: 'msg',
          },
          {
            type: 'action',
            exclude:['reset','back']
          }
        ],
        formData: {},
        info: {},
        show: false,
        formSchemaExtra: [{
            type: 'number-input',
            label: '最小对冲阈值',
            field: 'hedgeMinPosition',
            rules: [{
              required: true,
              message: '请输入最小对冲阈值',
              trigger: 'change'
            }]
          },
          {
              type: 'number-input',
              label: '卖单档位价差间隔',
              field: 'levelInterval',
              rules: [{
                required: true,
                message: '请输入卖单档位价差间隔',
                trigger: 'change'
              }]
            },
          {
            type: 'number-input',
            label: '挂单撤单最小间隔/ms',
            field: 'rateInterval',
            rules: [{
              required: true,
              message: '请输入挂单撤单最小间隔',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: 'sell1',
            field: 'sell1',
            rules: [{
              required: true,
              message: '请输入sell1',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: 'sellN',
            field: 'sellN',
            rules: [{
              required: true,
              message: '请输入sellN',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: '卖单的档位数量​',
            field: 'sellTimes',
            rules: [{
              required: true,
              message: '请输入卖单的档位数量',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: 'spread',
            field: 'spread',
            rules: [{
              required: true,
              message: '请输入spread',
              trigger: 'change'
            }]
          },
          {
            slot: 'msg',
          },
          {
            type: 'action',
            exclude:['reset','back']
          }
        ],
        formDataExtra: {},
        addLoading:false
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toEdit(item) {
        console.log(item)
        this.$router.push({
          name: 'scanAdd',
          query: {
            templateId: item.dutyId,
          }
        })
      },
      nav_task() {
        this.$router.push({
          name: 'scanAdd',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        this.loading = true
        const params = {
          ...this.query,
          ...this.page,
        }
        scaleList(params).then(res => {
          this.loading = false
          res.result.list.forEach((item)=>{
              item.show = false
          })
          this.tableData = res.result.list
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      async submit() {
        this.addLoading= true
        const data = {
          ...this.formData,
          photoShow: JSON.parse(localStorage.getItem('img_result')).mediumImageUrl,
          photo: JSON.parse(localStorage.getItem('img_result')).url,
        }
        const {
          status
        } = await this.$api.scaleBatchCreate(data)
        if (status.code === 0) {
          this.$message.success('新增系列成功')
          this.isDialog = false
          this.formData = {}
          this.getList()
           this.addLoading= false
        } else {
          this.addLoading= false
          // this.$message.success(status.msg)
          // instance.confirmButtonLoading = false;
        }
      },
      async showClick(item) {
        const {
          status,
          result
        } = await this.$api.holdCount({
          ctid: item.ctid
        })
        if (status.code === 0) {
            item.show = true
            item.u0GoodsCount = result.u0GoodsCount
            item.u4GoodsCount = result.u4GoodsCount
            console.log(item.u4GoodsCount)
        }
      },
      nav_details(item) {
        this.$router.push({
          name: "detailsList",
          query: {
            ctid: item.ctid
          }
        })
      },
      // 导出
      async batchCsv(item) {
        this.loading = true
        const res = await this.$api.holdListExport({
          ctid: item.ctid
        })
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
          this.loading = false
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          this.loading = false
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '用户持仓' + Date.now() + '.csv')
          this.$message.success('用户持仓导出成功')
          this.loading = false
        }
      },
      updataFunction(text,type,ctid,value) {
        this.$prompt(text, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue:value,
        }).then(({value}) => {
          console.log(111)
            this.submitUpdata(ctid,type,value)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      },
      async submitUpdata(ctid,type,value){
          let params={}
          if(type==1){
            params={
                maxTargetPrice:value
            }
          }else if(type==2){
            params={
                minOnSalePrice:value
            }
          }else if(type==3){
            params={
                weight:value
            }
          }
        const res = await this.$api.scaleUpdate({
            ctid,
            ...params
        })
        if(res.status.code==0){
             this.$message.success('修改成功')
             this.getList()
        }
      },
      async updataStatus(item) {
        this.$confirm(`此操作将${item.status==0?'开启':'停止'}该任务`, '是否确认?', '提示', {
                 confirmButtonText: '确定',
                 cancelButtonText: '取消',
                 type: 'warning'
               }).then(async() => {
                  let res = await this.$api.scaleUpdate({
                      ctid:item.ctid,
                      status:item.status==0?1:0
                  });
                  if (res.status.code == 0) {
                      this.$message.success('修改成功')
                      this.getList()
                  }
               }).catch(() => {
                 this.$message({
                   type: 'info',
                   message: '已取消删除'
                 });
               });


      },
      openUpdata(item){
        this.formDataExtra = item
        this.isDialog2 = true
      },
      async submitExtra() {
        const data = {
          ...this.formDataExtra,
        }
        const {
          status
        } = await this.$api.scaleUpdateExtra(data)
        if (status.code === 0) {
          this.$message.success('修改成功')
          this.isDialog2 = false
          this.formDataExtra = {}
        }
      },

    }
  }
</script>

<style lang="scss" scoped>

</style>
