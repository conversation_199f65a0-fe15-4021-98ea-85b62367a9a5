<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <el-table :data="tableData" tooltip-effect="dark" show-header border style="width: 100%" v-loading="loading">
      <el-table-column prop="originName" label="创作者" align="center">
        <template scope="scope">
          {{scope.row.originName==null?'/':scope.row.originName}}
        </template>
      </el-table-column>
      <el-table-column prop="saleName" label="卖出者" align="center">
        <template scope="scope">
          {{scope.row.saleName==null?'/':scope.row.saleName}}
        </template>
      </el-table-column>
      <el-table-column prop="buyName" label="购买者" align="center">
        <template scope="scope">
           {{scope.row.buyName==null?'/':scope.row.buyName}}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" label="实际付款金额" align="center">
        <template scope="scope">
          {{scope.row.orderAmount==null?'/':scope.row.orderAmount}}
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="交易笔数" align="center">
        <template scope="scope">
           {{scope.row.orderNum==null?'/':scope.row.orderNum}}
        </template>
      </el-table-column>
      <el-table-column prop="gasLeapDomainAmount" label="GAS/燃料费/域名续费" align="center">
        <template scope="scope"> 
           {{scope.row.gasLeapDomainAmount==null?'/':scope.row.gasLeapDomainAmount}}
        </template>
      </el-table-column>
      <el-table-column prop="systemFeeAmount" label="佣金" align="center">
        <template scope="scope">
          {{scope.row.systemFeeAmount==null?'/':scope.row.systemFeeAmount}}
        </template>
      </el-table-column>
      <el-table-column prop="copyrightFeeAmount" label="版税收入" align="center">
        <template scope="scope">
           {{scope.row.copyrightFeeAmount==null?'/':scope.row.copyrightFeeAmount}}
        </template>
      </el-table-column>
      <el-table-column prop="withdrawFeeAmount" label="提现手续费" align="center">
        <template scope="scope">
          {{scope.row.withdrawFeeAmount==null?'/':scope.row.withdrawFeeAmount}}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="备注" width="280" align="center">
        <template slot-scope="scope">
          <el-button type="text"  v-if="scope.row.type!=6&scope.row.type!=12" @click="batchExport(scope.row)">详情
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="total" label="合计" align="center"></el-table-column> -->
      <!-- <el-table-column prop="buyMember" label="下载订单明细" align="center" width="70px" ></el-table-column> -->
    </el-table>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    financeDataIncome,
    financeDataUserOrderExportExcel
  } from '@/api/hanxin'
  import {
    listUserOrderAssay,
    listOrderUser
  } from '@/api/hanxin'

  export default {
    name: 'nft_income',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          endTime: "",
          startTime: ""
        },
        tableData: [], // 表格数据
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '选择时间：',
            valueFormat: 'yyyy-MM-dd',
            format: 'yyyy-MM-dd',
            field: 'startTime',
            field2: 'endTime'
          },
        ],
        loading: false
      }
    },
    mounted() {
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        
        this.query = data
        if (this.query.startTime) {
          this.query.startTime = this.query.startTime.split(".000")[0]
          this.query.endTime = this.query.endTime.split(".000")[0]
          this.getList(true)
        }
      },
      xuanze(val) {
        this.page.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      getList() {
        this.tableData = []
        this.loading = true
        const params = {
          ...this.query,
        }
        financeDataIncome(params).then(res => {
          this.loading = false
          const data = res.result
          this.tableData.push({
            originName: "",
            saleName: "用户",
            buyName: '',
            type: 1,
            ...data.m0SystemFee
          }, {
            originName: "",
            saleName: "自营用户 ",
            buyName: '',
            type: 2,
            ...data.m4SystemFee
          }, {
            originName: "用户",
            saleName: "",
            buyName: '',
            type: 3,
            ...data.c0CopyrightFee
          }, {
            originName: "自营用户",
            saleName: "用户",
            buyName: '',
            type: 4,
            ...data.c4m0CopyrightFee
          }, {
            originName: "自营用户",
            saleName: "自营用户",
            buyName: '',
            type: 5,
            ...data.c4m4CopyrightFee
          }, {
            originName: "",
            saleName: "用户提现手续费",
            buyName: '',
            type: 6,
            ...data.withdraw
          }, {
            originName: "",
            saleName: "首次交易域名订单",
            buyName: '',
            type: 7,
            ...data.domain1
          }, {
            originName: "",
            saleName: "二次交易域名订单",
            buyName: '',
            type: 8,
            ...data.domain2
          }, {
            originName: "",
            saleName: "域名续费",
            buyName: '',
            type: 9,
            ...data.domainRenew
          }, {
            originName: "",
            saleName: "燃料",
            buyName: '',
            type: 10,
            ...data.gas
          }, {
            originName: "",
            saleName: "飞跃",
            buyName: '',
            type: 11,
            ...data.leapPlan
          }, {
            originName: "",
            saleName: "合计",
            buyName: '',
            type: 12,
            ...data.total
          })
          console.log(this.tableData)
        })
      },
      // 导出Excel
      // 导出结果
      async batchExport(item) {
        let data;
        if (item.type == 1) {
          data = {
            sellerMember: 0,
          }
        } else if (item.type == 2) {
          data = {
            sellerMember: 4,
          }
        } else if (item.type == 3) {
          data = {
            creatorMember: 0,
          }
        } else if (item.type == 4) {
          data = {
            creatorMember: 4,
            sellerMember: 0,
          }
        } else if (item.type == 5) {
          data = {
            creatorMember: 4,
            sellerMember: 4,
          }
        }
        else if (item.type == 5) {
          data = {
            creatorMember: 4,
            sellerMember: 4,
          }
        }else if (item.type == 7) {
          data = {
            mold: 8,
            original: 0,
          }
        }else if (item.type == 8) {
          data = {
            mold: 8,
            original: 1,
          }
        }else if (item.type == 9) {
          data = {
           mold: 9,
          }
        }else if (item.type == 10) {
          data = {
           mold: 4,
          }
        }else if (item.type == 11) {
          data = {
           mold:7,
          }
        }
        const res = await this.$api.financeDataUserOrderExportExcel({
          ...this.query,
          ...data
        })

        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, `NFTCN收入${item.type}`)
          this.$message.success('导出成功')
          // this.getList()
        }
      },
    }
  }
</script>

<style lang="scss" scoped>

</style>
