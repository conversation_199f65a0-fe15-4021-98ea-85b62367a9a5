<template>
	<d2-container class="page">
		<el-form :inline="true" :model="formInline" class="demo-form-inline"
			style="background-color: #ffffff; padding: 20px">
      <el-form-item label="选择时间">
      	<el-date-picker v-model="createAt" type="datetimerange" range-separator="至" start-placeholder="开始日期"
      		end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss.SSS" :default-time="['00:00:00', '23:59:59']" format="yyyy-MM-dd HH:mm:ss">
      	</el-date-picker>
      </el-form-item>
    <!-- <el-form-item label="用户类型">
      	<el-select  v-model="formInline.customerType" multiple  placeholder="用户类型">
      		<el-option label="小户" value="0.1"></el-option>
      	  <el-option label="中户" value="0.2"></el-option>
      	  <el-option label="大户" value="0.3"></el-option>
      		<el-option label="创作者-灵境" value="1"></el-option>
      		<el-option label="创作者-万重山" value="2"></el-option>
      		<el-option label="主力1" value="3"></el-option>
      		<el-option label="主力2" value="4"></el-option>
      	</el-select>
      </el-form-item> -->
			<el-form-item label="用户id">
				<el-input v-model="formInline.uid" placeholder="请输入用户id"></el-input>
			</el-form-item>
      <el-form-item label="用户昵称">
      	<el-input v-model="formInline.nickname" placeholder="请输入用户昵称"></el-input>
      </el-form-item>
    <el-form-item label="期初金额">
    	<div  style="display:flex;justify-content: space-between;align-items: center;width:320px;">
    	  <el-input v-model="formInline.minBeginAmount"   placeholder="请输入最低金额"></el-input>
    	  <div style="margin:0 10px;">至</div>
    	  <el-input v-model="formInline.maxBeginAmount" :placeholder="`请输入最高金额`"></el-input>
    	</div>
    </el-form-item>
    <el-form-item label="入账金额">
    	<div  style="display:flex;justify-content: space-between;align-items: center;width:320px;">
    	  <el-input v-model="formInline.minEnterAmount"   placeholder="请输入最低金额"></el-input>
    	  <div style="margin:0 10px;">至</div>
    	  <el-input v-model="formInline.maxEnterAmount" :placeholder="`请输入最高金额`"></el-input>
    	</div>
    </el-form-item>
    <el-form-item label="消耗金额">
    	<div  style="display:flex;justify-content: space-between;align-items: center;width:320px;">
    	  <el-input v-model="formInline.minOutAmount"   placeholder="请输入最低金额"></el-input>
    	  <div style="margin:0 10px;">至</div>
    	  <el-input v-model="formInline.maxOutAmount" :placeholder="`请输入最高金额`"></el-input>
    	</div>
    </el-form-item>
    <el-form-item label="期末金额">
    	<div  style="display:flex;justify-content: space-between;align-items: center;width:320px;">
    	  <el-input v-model="formInline.minEndAmount"   placeholder="请输入最低金额"></el-input>
    	  <div style="margin:0 10px;">至</div>
    	  <el-input v-model="formInline.maxEndAmount" :placeholder="`请输入最高金额`"></el-input>
    	</div>
    </el-form-item>
			<el-form-item>
				<el-button type="primary" @click="getquery()">查询</el-button>
        <!-- <el-button type="primary" @click="batchCsv(null)">导出</el-button> -->
        <el-button type="primary" @click="batchCsv(1)">导出当前全部余额</el-button>
        <el-button type="primary" @click="batchCsv(2)">导出当前创作者余额</el-button>
        <el-button type="primary" @click="batchCsv(3)">导出当前藏家余额</el-button>
        <el-button type="primary" @click="batchCsv(4)">导出当前头部藏家余额</el-button>
        <el-button type="primary" @click="dialogTime=true">导出某日余额快照</el-button>
			</el-form-item>
		</el-form>
		<el-table :data="tableData" ref="multipleTable" tooltip-effect="dark"
			show-header border style="width: 100%"  v-loading="loading">
			<!-- <el-table-column prop="orderNo" label="时间" align="center" width="180px"></el-table-column> -->
      <!-- <el-table-column prop="customerType" label="用户类型" align="center" width="150px">
          <template scope="scope">
              <span v-if="scope.row.customerType=='4.1'||scope.row.customerType=='1'">创作者-灵境</span>
              <span v-if="scope.row.customerType=='4.2'||scope.row.customerType=='2'">创作者-万重山</span>
              <span v-if="scope.row.customerType=='4.3'||scope.row.customerType=='3'">主力1</span>
              <span v-if="scope.row.customerType=='4.4'||scope.row.customerType=='4'">主力2</span>
              <span v-if="scope.row.customerType=='0.1'">小户</span>
              <span v-if="scope.row.customerType=='0.2'">中户</span>
              <span v-if="scope.row.customerType=='0.3'">大户</span>
          </template>
      </el-table-column> -->
			<el-table-column prop="uid" label="用户ID" align="center" width="150px">
      </el-table-column>
			<el-table-column prop="nickname" label="用户昵称" align="center" width="200px"></el-table-column>
      <el-table-column prop="beginAmount" label="期初金额" align="center"  width="150px">
        <template scope="scope">
          <div>
            {{scope.row.uid == 1 ? 0 : scope.row.beginAmount}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="enterAmount" label="入账金额" align="center" width="150px"></el-table-column>
      <el-table-column prop="outAmount" label="消耗金额" align="center" width="150px" ></el-table-column>
      <el-table-column prop="endAmount" label="期末金额" align="center" width="150px"> </el-table-column>
		</el-table>
		<div
			style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
			<div class="money_total">
        <div class="marginR">期初金额共:{{totalInfo.beginAmount}}</div>
        <div class="marginR">入账金额共:{{totalInfo.enterAmount}}</div>
        <div class="marginR">消耗金额共:{{totalInfo.outAmount}}</div>
        <div class="marginR">期末余额共:{{totalInfo.endAmount}}</div>
			</div>
			<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
				:page-size="pageSize" :current-page="current_page" :page-sizes="[20, 50, 100, 200,500,1000]"
				style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
			</el-pagination>
		</div>
    <el-dialog title="请选择用户快照导出时间" :visible.sync="dialogTime" width="20%">
       <el-date-picker style="width:100%;"
            v-model="timeValue"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
          </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogTime = false">取 消</el-button>
        <el-button type="primary" @click="batchCsvExportSnapshot()">确 定</el-button>
      </span>
    </el-dialog>
	</d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'

	export default {
		name: 'financeUserView',
		data() {
			return {
				fileList: [],
				action: process.env.VUE_APP_BASE_URL +
					'osscenter/adminApi/missWebSign/uploadImage',
				token: {
					AdminAuthorization: localStorage.getItem('usertoken')
				}, // 设置上传的请求头部
				form: {},
				dialogVisible: false,
				cancelDialogVisible: false,
				tableData: [],
				srcList: [],
				total: 1,
				current_page: 1,
				radio1: 'ACCOUNT_CANCEL_SUCCESS', // 1 同意  2 拒绝
				textarea: '手动录入', // 文本
				textarea1: '', // 文本
				options: [{
					value: '手动录入',
					label: '4'
				}],
				isPupop: false, // 拒绝文本控制
				isPupops: false,
				state: 0,
				formInline: {
            startTime:"",
            endTime:""
				},
				isAuthority: false,
				isDelete: false,
				idlist: [],
				deleteid: '',
				isimgDelete: false,
				imgurl: '',
				scrollTop: 0,
				goodsSynopsis: '',
				goodsDesc: '',
				remark: '', // 备注信息
				recordId: null, // id
				isDialogReson: false, // 驳回原因
				reason: '', // 驳回原因
				loading: false,
				pageSize: 20,
				loadingText: "",
				moneyTotal: 0,
				orderStr: "",
        totalInfo:"",
        customerTypeList:[],
        dialogTime:false,
        timeValue:''

			}
		},
		computed: {
			/**
			 * 搜索栏创建时间
			 */
			createAt: {
				get() {
					const {
						startTime,
						endTime
					} = this.formInline
					if (startTime && endTime) {
						return [startTime, endTime]
					} else {
						return []
					}
				},
				set(val) {
					if (val) {
						Object.assign(this.formInline, {
							startTime: val?. [0],
							endTime: val?. [1]
						})
					} else {
						Object.assign(this.formInline, {
							startTime: null,
							endTime: null
						})
					}
				}
			}
		},
		mounted() {
			if (this.$route.query.type) {
				if(this.$route.query.type=='buy'){
					this.formInline.buyerId = this.$route.query.uid
				}
				if(this.$route.query.type=='sale'){
					this.formInline.sellerId = this.$route.query.uid
				}
			}
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate()<10?`0${date.getDate()}`:date.getDate();
      this.formInline.startTime = `${year}-${month}-${day} 00:00:00.000`
      this.formInline.endTime = `${year}-${month}-${day} 23:59:59.000`
			this.$refs.multipleTable.bodyWrapper.addEventListener('scroll', (res) => {
				this.scrollTop = res.target.scrollTop
				console.log(res.target.scrollTop)
			})
      this.getquery()
		},

		activated() {
			this.$refs.multipleTable.bodyWrapper.scrollTop = this.scrollTop
		},
		methods: {
			credited(row) {
				this.form.orderNo = row.orderNo
				this.dialogVisible = true
			},
			async submit() {
				console.log(this.form)
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.rmbComplete(this.form)
				if (!code) {
					this.$message.success(msg)
					this.getquery()
					this.dialogVisible = false
				} else {
					this.$message.error(msg)
				}
			},
			async closeOrder(row) {
				this.cancelDialogVisible = true
				this.form.orderNo = row.orderNo
			},
			async confirmToClose() {
				const {
					status: {
						code,
						msg
					}
				} = await this.$api.rmbCancel({
					orderNo: this.form.orderNo
				})
				if (!code) {
					this.getquery()
					this.cancelDialogVisible = false
					this.$message.success(msg)
				} else {
					this.$message.error(msg)
				}
			},
			// 图片上传
			handlePicSuccess(res, file) {
				this.form.payAnnexFileUrl = res.result.url
			},
			handleIntroduceUploadHide(file, fileList) {
				this.hideUpload_introduce = fileList.length >= this.limitCount
			},
			beforeAvatarUpload(file) {
				if (
					file.type !== 'image/jpeg' &&
					file.type !== 'image/png' &&
					file.type !== 'image/gif'
				) {
					this.$message.error('只能上传jpg/png/GIF格式文件')
					return false
				}
			},
			// 图片移除
			handleIntroduceRemove(file, fileList) {
				this.hideUpload_introduce = fileList.length >= this.limitCount
				this.form.icon = ''
			},
			// 大图
			ddd(e) {
				this.isimgDelete = true
				this.imgurl = e
				console.log(e)
			},
			// 查询
			getquery() {
				this.getSelete(1)
			},
			// 拒绝理由下拉框
			change() {
				console.log(this.textarea)
				if (this.textarea === '4') {
					console.log('我是手动输入')
					this.isPupops = true
				} else {
					this.isPupops = false
				}
			},
			// 导出
			async batchCsv(type) {
				this.loadingText = "正在导出"
				this.loading = true
				const res = await this.$api.csvExportCurrent({
          type
        })
				if (res.type === 'application/json') {
				  // blob 转 JSON
				  const enc = new TextDecoder('utf-8')
				  res.arrayBuffer().then(buffer => {
				    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
				    this.$message.error(data.status?.msg)
				  })
				} else {
          this.loading = false
          let str;
          if(type==1){
            str="全部余额"
          }else if(type==2){
             str="创作者余额"
          }else if(type==3){
             str="藏家余额"
          }else if(type==4){
             str="头部藏家余额"
          }
           downloadBlob(res, str + Date.now() + '.csv')
				  this.$message.success('导出成功')
				}
			},
      // 导出
      async batchCsvExportSnapshot() {
      	this.loadingText = "正在导出"
      	this.loading = true
      	const res = await this.$api.csvExportSnapshot({
          day:this.timeValue
        })
      	if (res.type === 'application/json') {
      	  // blob 转 JSON
      	  const enc = new TextDecoder('utf-8')
      	  res.arrayBuffer().then(buffer => {
      	    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      	    this.$message.error(data.status?.msg)
      	  })
      	} else {
          this.loading = false
          this.dialogTime=false
          downloadBlob(res, '余额快照导出' + Date.now() + '.csv')
      	  this.$message.success('导出成功')
      	}
      },
			// 导出Excel
			// 导出结果
			hint(type) {
				this.$confirm('黑洞订单恢复, 是否确认?', '提示', {
				  confirmButtonText: '确定',
				  cancelButtonText: '取消',
				  type: 'warning'
				}).then(() => {
					this.recoveryOrder(type)
				}).catch(() => {

				});
			  },
			// 黑洞订单恢复
			async recoveryOrder(type) {
				if (type) {
					const res = await this.$api.unDestroy({
						orderNoStr: JSON.stringify(this.orderStr.split(","))
					})
					if (res.status.code === 0) {
						this.$message.success('恢复成功')
					} else {
						this.$message.error(res.status.msg)
					}
				} else {
					const str = []
					this.multipleSelection.forEach((item) => {
						str.push(item.orderNo)
					})
					const res = await this.$api.unDestroy({
						orderNoStr: JSON.stringify(str)
					})
					if (res.status.code === 0) {
						this.$message.success('恢复成功')
					} else {
						this.$message.error(res.status.msg)
					}
				}

			},
			// async batchExport () {
			//   this.loading = true
			//   const res = await this.$api.orderlistExportExcel(this.formInline)
			//   if (res.retCode === 500 ) {
			//     this.$message.error(res.retMsg)
			//     this.loading = false
			//     this.getList()
			//   } else if (res.type === 'application/json') {
			//     // blob 转 JSON
			//     const enc = new TextDecoder('utf-8')
			//     res.arrayBuffer().then((buffer) => {
			//       const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
			//       this.$message.error(data.status?.msg)
			//     })
			//   } else {
			//     downloadBlob(res, '订单' + Date.now() + '.xlsx')
			//     this.$message.success('导出成功')
			//     this.loading = false
			//     this.getList()
			//   }
			// },
			async getSelete(page) {
       // let customerType = []
       // this.customerTypeList = this.deepClone2(this.formInline.customerType)
       // this.customerTypeList.forEach((item,index,customerTypeList)=>{
       //       customerTypeList[index] = parseInt(customerTypeList[index])
       // })
       // console.error(this.customerTypeList)
       // console.error(this.formInline.customerType)
       // customerType = JSON.stringify(this.customerTypeList)
       // console.log(customerType)
				this.loadingText = "正在加载"
				this.loading = true
				this.moneyTotal = 0
				this.current_page = page
				const res = await this.$api.financeDataUserBalancet({
					pageNum: page,
					pageSize: this.pageSize,
          ...this.formInline,

				})
				if (res.status.code === 0) {
					this.loading = false
          if(page==1){
             this.balanceTotal(page)
          }
					if (res.result == null) {
						this.$message.error('无搜索结果')
					} else {
						// console.error(this.formInline.customerType.length)
						// let array2=[]
						// let array= this.formInline.customerType.sort((a,b)=>{return a-b})
						// array.forEach((item)=>{
						//   if(item<0.4){
						//     array2.push(item)
						//   }
						// })
						// console.log(array)
						// res.result.list.forEach((item) => {
						//   if(this.formInline.customerType==null||this.formInline.customerType==''){
						//     if(item.customerType==0){
						//        item.customerType=(Math.floor(Math.random()*3)+1)/10
						//     }
						//   }else{
						//     console.log(item.customerType)
						//       if(array[0]>0.4){
						//           console.log("非大中小户")
						//           if(item.customerType==0){
						//             let index;
						//             console.log("数组长度",array.length)
						//             index=Math.round(Math.random() * (array.length-1))
						//              item.customerType = array[index]
						//             console.log(index)
						//           }
						//       }else{
						//          console.log("大中小户")
						//         if(item.customerType==0){
						//           let index;
						//           console.log("数组长度",array2.length)
						//           index=Math.round(Math.random() * (array2.length-1))
						//            item.customerType = array2[index]
						//           console.log(index)
						//         }
						//       }
						//   }
						// })
						this.tableData = res.result.list
						this.total = res.result.totalCount
					}
				} else if (res.status.code === 1002) {
					await this.$router.push({
						name: 'login'
					})
				} else {
          this.loading = false
					// this.$message.error(res.status.msg)
				}
			},
			// 审核
			async check() {
				this.recordId = parseInt(this.recordId)
				if (this.textarea1 === '' || this.textarea1 == null) {
					this.textarea1 = null
				}
				const res = await this.$api.check({
					recordId: this.recordId,
					checkStatus: this.radio1,
					reason: this.textarea1 // 原因
				})
				console.log(res)
				if (res.status.code === 0) {
					this.isAuthority = false
					this.$message.success(res.status.msg)
					this.textarea1 = ''
					await this.getSelete(1)
				} else {
					this.$message.error(res.status.msg)
				}
			},
			// 驳回原因
			async rejectReason() {
				this.recordId = parseInt(this.recordId)
				const res = await this.$api.rejectReason({
					recordId: this.recordId
				})
				if (res.status.code === 0) {
					this.isDialogReson = true
					this.reason = res.result.reason
				} else {
					this.$message.error(res.status.msg)
				}
			},
			// 批量审核
			async getpeopleVerify(e) {
				console.log(e)
				e = e.toString()
				const res = await this.$api.peopleVerify({
					ids: e,
					peopleVerifyStatus: this.radio1,
					rejectExplain: this.textarea1
				})
				if (res.status.code === 0) {
					this.isAuthority = false
					this.$message.success(res.status.msg)
					this.textarea1 = ''
					await this.getSelete(1)
				} else {
					this.$message.error(res.status.msg)
				}
			},
			to_isDelete(e) {
				console.log(e)
				this.remark = ''
				this.recordId = e.id
				this.isDelete = true
			},
			to_isDialogReson(e) {
				this.recordId = e.id

				this.rejectReason()
			},
			// 备注
			async to_remark() {
				console.log()
				const res = await this.$api.remark({
					recordId: this.recordId,
					remark: this.remark // 备注内容
				})
				if (res.status.code === 0) {
					this.getSelete(1)
					this.isDelete = false
					this.$message.success('备注添加成功')
				} else {
					this.$message.error(res.status.msg)
				}
			},
			selete() {
				console.log(this.formInline)
			},
			// 分页
			xuanze(val) {
				this.getSelete(val)

				this.$refs.multipleTable.bodyWrapper.scrollTop = 0
			},
			// 分页
			xuanzeSize(val) {
				this.pageSize = val
				this.getSelete(1)
				this.$refs.multipleTable.bodyWrapper.scrollTop = 0
			},
			// 批量选择
			handleSelectionChange(val) {
				console.log(val)
				this.multipleSelection = val
			},
			// 清除
			orderexport() {
				this.formInline = {
					orderNo: null, // 订单号
					tid: null, // 作品tid
					buyerNickname: null, // 买方昵称
					buyerId: null, // 买方用户id
					sellerNickname: null, // 卖方昵称
					sellerId: null, // 卖方用户id
					status: null, // 订单状态0未付款 1已付款 2已发货 3已签收 4退货申请 5退货中 6已退货 7取消交易 8交易成功
					paymentScene: null, // 订单支付场景 1-H5 2-PC 3-IOS 4-Android 5-H5的PC
					payMethod: null, // 订单支付方式付款方式:1余额,2微信,3支付宝,4云闪付,5银行卡,6赠送,7五虎赠送,8流水,9苹果10余额支付 20 合成
					mold: null // 订单类型 1-作品 4-燃料
				}
				this.getSelete(1)
			},
			// 批量审核
			batch_audit() {
				console.log(this.multipleSelection)
				if (this.multipleSelection.length >= 1) {
					this.state = 1
					this.isAuthority = true
				} else {
					this.$message.error('请选择作品')
				}
			},
			// 拒绝原因弹出框
			pupop() {
				if (this.radio1 === 'ACCOUNT_CANCEL_SUCCESS') {
					this.isPupops = false
					this.isPupop = false
				} else {
					this.isPupop = true
					this.isPupops = true
				}
			},
			// 跳转详情
			nav_details(item) {
				console.log(item)
				// this.$refs.multipleTable.bodyWrapper.addEventListener('scroll',(res) =>{
				// this.scrollTop = res.target.scrollTop
				// console.log(res.target.scrollTop)
				// })
				this.$router.push({
					name: 'works_details',
					query: {
						id: item.tid
					}
				})
			},
			// 单个作品终审
			to_examine(item) {
				console.log(item)
				this.state = 0
				this.idlist = []
				this.idlist.push(item.id)
				this.recordId = item.id
				this.isAuthority = true
			},

      async balanceTotal(page) {
        // let customerType = []
        // this.customerTypeList = this.deepClone2(this.formInline.customerType)
        // this.customerTypeList.forEach((item,index,customerTypeList)=>{
        //       customerTypeList[index] = parseInt(customerTypeList[index])
        // })
        // console.error(this.customerTypeList)
        // console.error(this.formInline.customerType)
        // customerType = JSON.stringify(this.customerTypeList)
        // console.log(customerType)
      	const res = await this.$api.financeDataUserBalanceTotal({
      		pageNum: page,
      		pageSize: this.pageSize,
      		...this.formInline,
      	})
      	if (res.status.code === 0) {
            this.totalInfo=res.result
      	}
      },
      deepClone2(obj){
          return JSON.parse(JSON.stringify(obj))
      },
      contains(arr, val) {
          var i = arr.length;
          while (i--) {
             if (arr[i] === val){
                 return true;
             }
          }
          return false;
      },
		}
	}
</script>
<style lang="scss" scoped>
	.action {
		.el-button {
			width: 130px;
			margin: 10px 0;
		}
	}

	.content {
		.item {
			display: flex;
			margin-bottom: 20px;

			.title {
				width: 100px;
			}
		}
	}

	.tag {
		margin: 0px 15px 15px 0px;
		cursor: pointer !important;
	}

	.money_total {
			// position: absolute;
			// left: 20px;
			font-size: 14px;
			color: #F56C6C;
	    display: flex;
	    .marginR{
	      margin-right:30px;
	    }
		}
	::v-deep.el-table .el-table__cell {
		padding:0px !important;
	}
  ::v-deep.el-textarea textarea{
    height:60px !important;
  }
</style>
