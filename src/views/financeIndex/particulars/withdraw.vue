<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" :showExport="true"@onExport="onExport" ></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData" :loading="loading">
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
      <div class="money_total">
      <div class="marginR">提现金额共计:{{totalInfo.withdrawAmount}}</div>
      <div class="marginR">提现笔数:{{totalInfo.withdrawNum}}</div>
      <div class="marginR">提现手续费:{{totalInfo.withdrawFeeAmount}}</div>
      </div>
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanzeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import {
  	downloadBlob
  } from '@/utils/helper'
  import {
    financeDatawithdrawFee,
    financeDataWithdrawFeeExportExcel,
    financeDataWithdrawFeeTotal
  } from '@/api/hanxin'
  export default {
    name: 'particulars_withdraw',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          lastDay: 3,
          isSeeMe: false
        },
        tableSchema: [ // 表格架构x
          {
            label: '提现时间（默认在搜索范围内降序排列）',
            field: 'day',
            width:300
          },
          {
            label: '用户ID',
            field: 'uid',
          },
          {
            label: '提现金额',
            field: 'withdrawAmount',
          },
          {
            label: '提现笔数',
            field: 'withdrawNum',
          },
          {
            label: '提现手续费',
            field: 'withdrawFeeAmount',
          },
        ],
        tableData: [], // 表格数据
        querySchema: [ // 搜索组件架构
         {
           type: 'datetimerange',
           label: '选择时间：',
           valueFormat:'yyyy-MM-dd',
           format:'yyyy-MM-dd',
           field: 'startTime',
           field2:'endTime'
         },
       ],
       totalInfo:"",
       loading:false

      }
    },
    mounted() {
      // this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        if(this.query.startTime){
          this.query.startTime= this.query.startTime.split(".000")[0]
          this.query.endTime= this.query.endTime.split(".000")[0]
          this.getList(true)
        }
      },
      xuanze(val) {
        this.page.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      getList() {
        this.loading=true
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum
        }
        financeDatawithdrawFee(params).then(res => {
          this.loading=false
          const data = res.result
          this.tableData = data.list
          this.page.totalCount = data.totalCount
          this.page.pageCount = data.pageCount
          this.getTotal()
        })
      },
      getTotal() {
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum
        }
        financeDataWithdrawFeeTotal(params).then(res => {
          this.totalInfo = res.result
        })
      },
      async onExport(e) {
        if(e.startTime){
          e.startTime= e.startTime.split(".000")[0]
          e.endTime= e.endTime.split(".000")[0]
        }
      	const res = await this.$api.financeDataWithdrawFeeExportExcel({
          ...e,
        })
      	if (res.type === 'application/json') {
      		// blob 转 JSON
      		const enc = new TextDecoder('utf-8')
      		res.arrayBuffer().then(buffer => {
      			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      			this.$message.error(data.status?.msg)
      		})
      	} else {
      		downloadBlob(res, '提现明细')
      		this.$message.success('导出成功')
      		// this.getList()
      	}
      },
    }
  }
</script>

<style lang="scss" scoped>
.money_total {
		// position: absolute;
		// left: 20px;
		font-size: 14px;
		color: #F56C6C;
    display: flex;
    .marginR{
      margin-right:30px;
    }
	}
</style>
