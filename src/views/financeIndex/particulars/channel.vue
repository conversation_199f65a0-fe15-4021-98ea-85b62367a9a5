<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <el-table :data="tableData"   tooltip-effect="dark"
    	show-header border style="width: 100%"  v-loading="loading">
    	<el-table-column prop="title" label="渠道名称" align="center"  width="300px"></el-table-column>
    	<el-table-column prop="orderNum" label="渠道笔数" align="center"></el-table-column>
    	<el-table-column prop="orderAmount" label="交易金额" align="center"></el-table-column>
      <el-table-column prop="payFeeAmount" label="渠道成本" align="center"  ></el-table-column>
     <!-- <el-table-column fixed="right" label="下载订单明细" width="280" align="center">
         <template slot-scope="scope">
           <el-button type="text"  v-if="scope.row.title!='合计'" @click="batchExport(scope.row)">下载</el-button>
         </template>
       </el-table-column> -->
      <!-- <el-table-column prop="total" label="合计" align="center"></el-table-column> -->
      <!-- <el-table-column prop="buyMember" label="下载订单明细" align="center" width="70px" ></el-table-column> -->
    </el-table>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import {
  	downloadBlob
  } from '@/utils/helper'
  import {
    financeDataPayMethodCosts,
  } from '@/api/hanxin'
  import {
    listUserOrderAssay,
    listOrderUser
  } from '@/api/hanxin'

  export default {
    name: 'particulars_channel',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {
          lastDay: 3,
          isSeeMe: false
        },
        tableData:[], // 表格数据
        querySchema: [ // 搜索组件架构
          {
            type: 'datetimerange',
            label: '选择时间：',
            valueFormat:'yyyy-MM-dd',
            format:'yyyy-MM-dd',
            field: 'startTime',
            field2:'endTime'
          },
        ],
        loading:false
      }
    },
    mounted() {
      // this.getList()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        if(this.query.startTime){
          this.query.startTime= this.query.startTime.split(".000")[0]
          this.query.endTime= this.query.endTime.split(".000")[0]
          this.getList(true)
        }
      },
      xuanze(val) {
        this.page.pageNum = val
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      getList() {
         this.tableData=[]
        this.loading=true
        const params = {
          ...this.query,
        }
        financeDataPayMethodCosts(params).then(res => {
          this.loading=false
          const data = res.result
          this.tableData.push({
            title:"支付宝",
            ...data.alipay
          },{
            title:"连连",
            ...data.llPay
          },{
            title:"宝付",
            ...data.baofoo
          },{
            title:"平安提现",
            ...data.paWithdraw
          },{
            title:"汇总",
            ...data.total
          })
          console.log(this.tableData)
        })
      },
      // 导出Excel
      // 导出结果
      async batchExport(item) {
        let data;
       if(item.title=='用户'){
           data={
             creatorMember:0
           }
       }else if(item.title=='自营用户'){
         data={
           creatorMember:4
         }
       }
      	const res = await this.$api.financeDataCopyrightFeeOrderExportExcel({
          ...this.query,
          ...data
        })
      	if (res.type === 'application/json') {
      		// blob 转 JSON
      		const enc = new TextDecoder('utf-8')
      		res.arrayBuffer().then(buffer => {
      			const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      			this.$message.error(data.status?.msg)
      		})
      	} else {
      		downloadBlob(res, '订单列表')
      		this.$message.success('导出成功')
      	}
      },
    }
  }
</script>

<style lang="scss" scoped>

</style>
