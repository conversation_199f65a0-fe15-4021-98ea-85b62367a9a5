<template>
  <d2-container class="page">
    <div class="flex-box">
      <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
      <div class="margin_top555">
        <el-button type="primary" size="mini" @click="openItemVisibility()">一级上下架</el-button>
      </div>
      <div class="margin_top555" style="margin-left: 15px;">
        <el-button type="primary" size="mini" @click="openBindArr()">批量开启竞价</el-button>
      </div>
      <!-- <div class="margin_top555" style="margin-left: 15px;">
        <el-button type="primary" size="mini"  @click="openBindArr()">导出</el-button>
      </div> -->
    </div>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
      v-loading="loading" :element-loading-text="loadingText" @selection-change="handleSelectionChange">
      <el-table-column fixed prop="id" label="id" type="selection" align="center"></el-table-column>
      <el-table-column prop="name" label="系列名称" align="center" width="155"></el-table-column>
      <el-table-column prop="ctid" label="系列tid" align="center" width="155"></el-table-column>
      <el-table-column prop="createTime" label="铸造时间" align="center" width="155"></el-table-column>
      <el-table-column prop="cover" label="图片" align="center" width="60">
        <template scope="scope">
          <div style="width: 100%" @click="cover_click(scope.row.cover)">
            <el-image style="width: 50px; height: 50px" :src="scope.row.cover">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="visibility" label="是否可见" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.visibility == '1'">上架</el-tag>
          <el-tag v-if="scope.row.visibility == '0'">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="onSaleMinPrice" label="系列最小限价" align="center"></el-table-column>
      <el-table-column prop="onSaleMaxPrice" label="系列最大限价" align="center"></el-table-column>
      <el-table-column prop="floorPrice" label="地板价" align="center"></el-table-column>
      <el-table-column prop="leader" label="负责人" align="center"></el-table-column>
      <el-table-column prop="isExitMarket" label="是否退市" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isExitMarket == '1'">退市</el-tag>
          <el-tag v-if="scope.row.isExitMarket == '0'">/</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="minResalePrice" label="最低转售价" align="center"></el-table-column>
      <el-table-column prop="maxResalePrice" label="最高转售价" align="center"></el-table-column>

      <el-table-column prop="notSaleSign" label="系列状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.notSaleSign == '1'">仅供收藏</el-tag>
          <el-tag v-if="scope.row.notSaleSign == '0'">正常流通</el-tag>
          <el-tag v-if="scope.row.notSaleSign == '2'">仅供转赠</el-tag>
          <el-tag v-if="scope.row.notSaleSign == '3'">仅供转售</el-tag>

        </template>
      </el-table-column>

      <el-table-column prop="biddingStatus" label="是否已开启竞价" align="center" width="155">
        <template scope="scope">
          <el-tag v-if="scope.row.biddingStatus == 1">是</el-tag>
          <el-tag v-if="scope.row.biddingStatus == 0">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="enableExitOrderTarget" label="是否开启退市可批量" align="center" width="155">
        <template scope="scope">
          <el-tag v-if="scope.row.enableExitOrderTarget == 1">是</el-tag>
          <el-tag v-if="scope.row.enableExitOrderTarget == 0 || scope.row.enableExitOrderTarget == null">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="enableOrderTarget" label="是否开启批量下单" align="center" width="155">
        <template scope="scope">
          <el-tag v-if="scope.row.enableOrderTarget == 1">是</el-tag>
          <el-tag v-if="scope.row.enableOrderTarget == 0 || scope.row.enableOrderTarget == null">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="goodsCount" label="总数" align="center"></el-table-column>
      <el-table-column prop="activeNum" label="流通" align="center"></el-table-column>
      <el-table-column prop="firstMarketNum" label="一级剩余" align="center"></el-table-column>
      <el-table-column prop="u4GoodsCount" label="主力持有" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.u4GoodsCount != null">{{ scope.row.u4GoodsCount }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="holdNumMax" label="大户持有" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.holdNumMax != null">{{ scope.row.holdNumMax }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="holdNumMiddle" label="中户持有" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.holdNumMiddle != null">{{ scope.row.holdNumMiddle }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="holdNumMin" label="小户持有" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.holdNumMin != null">{{ scope.row.holdNumMin }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="u4UserCount" label="主力人数" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.u4UserCount != null">{{ scope.row.u4UserCount }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="userNumMax" label="大户人数" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.userNumMax != null">{{ scope.row.userNumMax }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="userNumMiddle" label="中户人数" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.userNumMiddle != null">{{ scope.row.userNumMiddle }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="userNumMin" label="小户人数" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.userNumMin != null">{{ scope.row.userNumMin }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="u4OnSaleGoodsCount" label="主力寄售数" align="center">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.u4OnSaleGoodsCount != null">{{ scope.row.u4OnSaleGoodsCount }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="u0OnSaleGoodsCount" label="非主力寄售数" align="center" width="100">
        <template scope="scope">
          <div class="bg">
            <span v-if="scope.row.u0OnSaleGoodsCount != null">{{ scope.row.u0OnSaleGoodsCount }}</span>
            <el-button v-else type="text" @click="open_data(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="isPgc" label="PGC/UGC" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isPgc == 0">UGC</el-tag>
          <el-tag v-if="scope.row.isPgc == 1">PGC</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="isJwc" label="是否进入吉物仓" align="center" width="120">
        <template scope="scope">
          <el-tag v-if="scope.row.isJwc == 0">否</el-tag>
          <el-tag v-if="scope.row.isJwc == 1">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200" align="center">

        <template slot-scope="scope">
          <!-- <el-button type="text"  @click="openItemVisibility(scope.row)">一级上下架</el-button> -->
          <el-dropdown>
            <el-button type="primary">
              操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button type="text" @click="open_tag(scope.row)">修改标签</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="open3(scope.row)">修改限价</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="set_up_flag(scope.row)">修改状态</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="open_destroy(scope.row)">销毁一级剩余</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="open_allDestroy(scope.row)">销毁全部</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="set_updateLeader(scope.row)" v-if="buttonShow">负责人</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="batchCsv(scope.row)">查看持有明细</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="openBind(1, scope.row)"
                  v-show="scope.row.biddingStatus == 0">开启竞价</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="openBind(0, scope.row)"
                  v-show="scope.row.biddingStatus == 1">关闭竞价</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="exitOrderTarget(1, scope.row)"
                  v-show="scope.row.enableExitOrderTarget == 0 || scope.row.enableExitOrderTarget == null">开启退市可批量</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="exitOrderTarget(0, scope.row)"
                  v-show="scope.row.enableExitOrderTarget == 1">关闭退市可批量</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="orderTarget(1, scope.row)"
                  v-show="scope.row.enableOrderTarget == 0 || scope.row.enableOrderTarget == null">开启批量下单</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="orderTarget(0, scope.row)"
                  v-show="scope.row.enableOrderTarget == 1">关闭批量下单</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="openJwc(scope.row)">是否进入吉物仓</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" @click="LimitOneDay(scope.row)">单日限购数量</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" size="small" @click="setUpLimits(scope.row)">设置涨跌停</el-button>

              </el-dropdown-item>
              <el-dropdown-item>

                <el-button type="text" size="small" @click="ClosedMarket(scope.row)">休市时间</el-button>
              </el-dropdown-item>
              <el-dropdown-item>

                <el-button type="text" size="small" @click="opensetSellStatus(scope.row)">{{ scope.row.entrustViewStatus == 1
                  ? '关闭显示委托' : '开启显示委托' }}</el-button>
              </el-dropdown-item>
              <el-dropdown-item v-if="scope.row.mold == 1">

                <el-button type="text" size="small" @click="openGd(scope.row)">修改挂单时间</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>

          </el-dropdown>
        </template>

      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
      </common-pagination>
    </div>
    <el-dialog title="" :visible.sync="isimgDelete" width="50%">
      <div style="width: 100%">
        <el-image style="width: 500px" :src="imgurl"></el-image>
      </div>
    </el-dialog>
    <el-dialog title="设置系列寄售状态" :visible.sync="isSaleSign" width="50%">
      <el-radio v-model="saleSign" label="0">正常流通</el-radio>
      <el-radio v-model="saleSign" label="1">仅供收藏</el-radio>
      <el-radio v-model="saleSign" label="2">仅供转赠</el-radio>
      <el-radio v-model="saleSign" label="3">仅供转售</el-radio>

      <!-- 价格区间，最低转售价格和最高转售价格 -->
      <el-form :model="saleForm" v-if="saleSign == 3">
        <el-form-item label="最低转售价格">
          <el-input v-model="saleForm.minPrice" style="width: 300px"></el-input>
        </el-form-item>
        <el-form-item label="最高转售价格">
          <el-input v-model="saleForm.maxPrice" style="width: 300px"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="isSaleSign = false">取 消</el-button>
        <el-button type="primary" @click="set_up_submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改系列标签" :visible.sync="isTagSign" width="30%">
      <el-form :model="tagForm">
        <el-form-item label="系列标签">
          <el-select v-model="tagForm.tagValue" placeholder="请选择系列标签">
            <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否为新品">
          <el-radio-group v-model="tagForm.newFlag">
            <el-radio v-model="tagForm.newFlag" :label="1">是</el-radio>
            <el-radio v-model="tagForm.newFlag" :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>


        <el-form-item label='标签'>
          <el-select v-model="tagForm.tagIdListJson" multiple placeholder="请选择">
            <el-option v-for="item in tagForm.tagList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="tag_submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改一级上下架" :visible.sync="dialogUp" width="600px">
      <el-form :model="fodialogUpForm">
        <el-form-item label="状态：">
          <el-radio v-model="fodialogUpForm.visibility" label="1">上架</el-radio>
          <el-radio v-model="fodialogUpForm.visibility" label="0">下架</el-radio>
        </el-form-item>
        <el-form-item label="上下架数量：">
          <el-input v-model="fodialogUpForm.num" type="number" placeholder="请输入数量" style="width: 300px;" />
        </el-form-item>
        <el-form-item label="提示：">
          <span style="color: crimson;">输入数量则按部分数量上下架，不输入则按全部</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogUp = false">取 消</el-button>
        <el-button type="primary" @click="click_submit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="负责人修改" :visible.sync="isLeader" width="50%">
      <el-radio v-model="leader" label="云龙">云龙</el-radio>
      <el-radio v-model="leader" label="xh-new">xh-new</el-radio>
      <el-radio v-model="leader" label="xinzai">xinzai</el-radio>
      <el-radio v-model="leader" label="微笑">微笑</el-radio>
      <el-radio v-model="leader" label="我执">我执</el-radio>
      <el-radio v-model="leader" label="波">波</el-radio>
      <el-radio v-model="leader" label="bk">bk</el-radio>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isLeader = false">取 消</el-button>
        <el-button type="primary" @click="set_updateLeader_submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="是否进入吉物仓" :visible.sync="isJwc" width="500px">
      <el-radio v-model="jwc" :label="1">是(如未开寄售则我的藏品和怀旧区都不显示)</el-radio>
      <el-radio v-model="jwc" :label="0">否</el-radio>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isJwc = false">取 消</el-button>
        <el-button type="primary" @click="set_update_jwc_submit()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 单日限购弹窗 编辑 -->
    <el-dialog title="单日限购数量编辑" :visible.sync="dialogVisible">
      <div>
        <el-form :model="formDataLimit" label-position="right" label-width="110px">
          <!-- 是否启用 -->
          <el-form-item label="是否启用：">
            <el-radio-group v-model="formDataLimit.enable">
              <el-radio v-model="formDataLimit.enable" :label="1">是</el-radio>
              <el-radio v-model="formDataLimit.enable" :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 生效时间 -->
          <el-form-item label="生效时间：" v-if="formDataLimit.enable == 1">
            <el-radio-group :disabled="open" v-model="formDataLimit.isNowStart" @change="onStartTimeTypeChange">
              <el-radio :label="1">立即</el-radio>
              <el-radio :label="0">定时</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-date-picker :disabled="open" style="width: 300px;height: 27px;margin-left: 110px;margin-bottom: 5px"
            v-if="formDataLimit.isNowStart == 0 && formDataLimit.enable == 1" v-model="formDataLimit.startTime"
            type="datetime" placeholder="选择时间区域"></el-date-picker>

          <!-- 结束时间 -->
          <el-form-item label="结束时间：" style="margin-top: 20px;" v-if="formDataLimit.enable == 1">
            <el-date-picker :disabled="open" style="width: 300px;height: 27px;" v-model="formDataLimit.endTime"
              type="datetime" placeholder="选择时间区域"></el-date-picker>
          </el-form-item>

          <!-- 每人限购数量 -->
          <el-form-item label="每人限购数量：" v-if="formDataLimit.enable == 1">
            <el-input v-model="formDataLimit.buyMaxNum" style="width:300px" placeholder="请输入限购数量"></el-input>
          </el-form-item>
        </el-form>
      </div>


      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确认</el-button>
      </div>

    </el-dialog>
    <el-dialog title="单日限购数量新增" :visible.sync="dialogVisibleadd">
      <div>
        <el-form :model="formDataLimit2" label-position="left" label-width="110px">
          <!-- 是否启用 -->
          <el-form-item label="是否启用：">
            <el-radio-group v-model="formDataLimit2.enable">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 生效时间 -->
          <el-form-item label="生效时间：" v-if="formDataLimit2.enable == 1">
            <el-radio-group v-model="formDataLimit2.isNowStart" @change="onStartTimeTypeChange2">
              <el-radio label="1">立即</el-radio>
              <el-radio label="0">定时</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-date-picker style="width: 300px;height: 27px;margin-left: 110px;margin-bottom: 5px;"
            v-if="formDataLimit2.isNowStart == 0 && formDataLimit2.enable == 1" v-model="formDataLimit2.startTime"
            type="datetime" placeholder="选择时间区域" value-format="yyyy-MM-dd HH:mm:ss.SSS"></el-date-picker>

          <!-- 结束时间 -->
          <el-form-item label="结束时间：" style="margin-top: 20px;" v-if="formDataLimit2.enable == 1">
            <el-date-picker style="width: 300px;height: 27px;" v-model="formDataLimit2.endTime" type="datetime"
              placeholder="选择时间区域" value-format="yyyy-MM-dd HH:mm:ss.SSS"></el-date-picker>
          </el-form-item>

          <!-- 每人限购数量 -->
          <el-form-item label="每人限购数量：" v-if="formDataLimit2.enable == 1">
            <el-input v-model="formDataLimit2.buyMaxNum" style="width:300px" placeholder="请输入限购数量"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleadd = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确认</el-button>
      </div>
    </el-dialog>

    <!-- 涨跌停 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisiblesetupLimit">
      <common-form :is-edit="true" :submit="submit" :data="formDatasetupLimit" :schema="formSchema" label-width="300px">
        <template #timing="scope">
          <el-radio-group v-model="formDatasetupLimit.timing" @change="onStartTimeTypeChange2">
            <el-radio label="0">立即</el-radio>
            <el-radio label="1">定时</el-radio>
          </el-radio-group>
        </template>
        <!-- <template #startTime="scope">
          <el-date-picker v-model="formDatasetupLimit.startTime" type="datetime" placeholder="选择日期时间"
            :picker-options="pickerOptions" :disabled="formDatasetupLimit.timing == 1" />
        </template> -->
      </common-form>
    </el-dialog>

    <!-- 休市 -->
    <el-dialog :title="dialogTitleClose" :visible.sync="dialogVisibleClose">
      <common-form :is-edit="true" :submit="submitClose" :data="formDataClose" :schema="formSchemaClose"
        label-width="300px">
      </common-form>
    </el-dialog>
    <el-dialog title="系列挂单时间修改" :visible.sync="dialogVisible1">
      <common-form :submit="submit1" :data="formDataGd" :schema="formSchemaGd" label-width="300px">
        <!-- tradeDay 交易天 -->
        <template #tradeDay="scope">
          每周<el-input style="width: 80px;" v-model="formDataGd.minTradeDay"></el-input> - 每周<el-input
            style="width: 80px;" v-model="formDataGd.maxTradeDay"></el-input>
        </template>
        <!-- error_msg -->
        <template #error_msg="scope">
          <span style="color: red;">请输入阿拉伯数字1-7，前面数字不能大于后面数字</span>
        </template>
        <!-- tradeTime -->
        <template #tradeTime="scope">
          <el-input style="width: 100px;" v-model="formDataGd.minTradeTime"></el-input> - <el-input
            style="width: 100px;" v-model="formDataGd.maxTradeTime"></el-input>
        </template>

        <!-- OnSaleDay -->
        <template #OnSaleDay="scope">
          每周<el-input style="width: 100px;" v-model="formDataGd.minOnSaleDay"></el-input> - 每周<el-input
            style="width: 100px;" v-model="formDataGd.maxOnSaleDay"></el-input>
        </template>

        <!-- OnSaleTime -->
        <template #OnSaleTime="scope">
          <el-input style="width: 100px;" v-model="formDataGd.minOnSaleTime"></el-input> - <el-input
            style="width: 100px;" v-model="formDataGd.maxOnSaleTime"></el-input>
        </template>
      </common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
  import common from '@/mixins/common'
  import CommonForm from '@/components/CommonForm'
  import CommonQuery from '@/components/CommonQuery_h'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'seriesList',
    mixins: [common],
    components: {
      CommonForm,
      CommonQuery
    },
    data() {
      return {
        dialogTitleClose: "",
        CloseLimitObj: {},
        formDataClose: {
          repeatStatus: 0,
          enableStatus: 1,

        },
        dialogVisibleClose: false,
        formDatasetupLimit: {

        },

        dialogTitle: '',
        setupLimitObj: {},
        dialogVisiblesetupLimit: false,
        formDatasetupLimit: {
          timing: '0'
        },
        dialogVisibleadd: false,
        dialogVisible: false,
        open: false,
        formDataLimit2: {
          enable: 0, // 是否启用
          isNowStart: "0", // 生效时间类型（立即或定时）
          startTime: "", // 生效时间区域
          endTime: "", // 结束时间区域
          buyMaxNum: ""
        },
        formDataLimitTemp: {},
        formDataLimit: {
          enable: 0, // 是否启用
          isNowStart: 1, // 生效时间类型（立即或定时）
          startTime: "", // 生效时间区域
          endTime: "", // 结束时间区域
          buyMaxNum: '', // 每人限购数量
        },
        //标签数据列表
        saleForm: {
          minPrice: '',
          maxPrice: ''
        },
        tagManageList: [],
        tableData: [],
        isimgDelete: false,
        imgurl: '',
        formInline: {
          adult: '',
          blindSaleStatus: '',
          buyLimitTimes: '',
          ctIds: '',
          email: '',
          id: '',
          isDeleted: '',
          isReal: '',
          mold: '',
          name: '',
          nickname: '',
          purview: '',
          recommend: '',
          saleType: '',
          selfTags: '',
          visibility: ''
        },
        multipleSelection: [],
        idListStr: [],
        dialogUp: false,
        form: {
          value: ''
        },
        dialogRank: false,
        title: '',
        isSaleSign: false,
        saleSign: '0',
        ctid: '',
        isTagSign: false,
        tagForm: {
          tagValue: '',
          newFlag: 0,
          tagIdListJson: [],
          tagList: []
        },
        tagOptions: [{
          label: '无标签',
          value: 0
        }, {
          label: '蛋x0.5',
          value: 1
        }, {
          label: '蛋x1',
          value: 2
        }, {
          label: '蛋x1.5',
          value: 3
        }, {
          label: '蛋x2',
          value: 4
        }, {
          label: '蛋x2.5',
          value: 5
        }, {
          label: '蛋x3',
          value: 6
        }, {
          label: '鸡x0.5',
          value: 7
        }, {
          label: '鸡x1',
          value: 8
        }, {
          label: '鸡x1.5',
          value: 9
        }, {
          label: '鸡x2',
          value: 10
        }, {
          label: '鸡x2.5',
          value: 11
        }, {
          label: '鸡x3',
          value: 12
        }],
        querySchema: [ // 搜索组件架构
          {
            type: 'input',
            label: '系列名称：',
            placeholder: '请输入系列名称',
            field: 'name'
          },
          {
            type: 'input',
            label: '系列ID：',
            placeholder: '请输入任务编号',
            field: 'ctid'
          },
          {
            type: 'datetimerange',
            label: '铸造时间',
            placeholder: '请输入铸造时间',
            field: 'createTimeStart',
            field2: 'createTimeEnd',
          },
          {
            type: 'select',
            label: '负责人：',
            placeholder: '',
            field: 'leader',
            options: [{
                label: '云龙',
                value: '云龙'
              },
              {
                label: 'xh-new',
                value: 'xh-new'
              },
              {
                label: 'xinzai',
                value: 'xinzai'
              },
              {
                label: '微笑',
                value: '微笑'
              },
              {
                label: '我执',
                value: '我执'
              },
              {
                label: '波',
                value: '波'
              },
              {
                label: 'bk',
                value: 'bk'
              }
            ],
            rules: [{
              required: true
            }]
          },
          {
            type: 'select',
            label: 'PGC/UGC：',
            placeholder: '',
            field: 'isPgc',
            options: [{
                label: 'PGC',
                value: '1'
              },
              {
                label: 'UGC',
                value: '0'
              }
            ],
            rules: [{
              required: true
            }]
          }
        ],
        query: {
          name: null,
          ctid: null,
          isPgc: '1',
          leader: null
        },
        loading: false,
        loadingText: '正在导出',
        dialogUp: false,
        fodialogUpForm: {
          visibility: '0'
        },
        multipleSelection: [],
        isLeader: false,
        leader: '云龙',
        buttonShow: false,
        searchForm: {
          name: null,
          ctid: null,
          isPgc: '1',
          leader: null
        },
        jwc: 1,
        isJwc: false,
        dialogVisible1: false,
        formSchemaGd: [
          {
            type: 'input',
            label: '系列最低限价：',
            placeholder: '请输入系列最低限价',
            field: 'onSaleMinPrice',
            rules: [{
              required: true,
              message: '请输入系列最低限价',
              trigger: 'blur'
            }, ]
          },
          {
            type: 'input',
            label: '系列最高限价：',
            placeholder: '请输入系列最高限价',
            field: 'onSaleMaxPrice',
            rules: [{
              required: true,
              message: '请输入系列最高限价',
              trigger: 'blur'
            }, ]
          },
          {
              label: '是否开启：',
              field: 'tradeTimeLimit',
              type: 'radio',
              options: [{
                label: '是',
                value: 1
              }, {
                label: '否',
                value: 0
              }]
            },
          {
            type: 'radio',
            label: '可挂售时间(天)：',
            slot: 'OnSaleDay',
            rules: [{
              required: true,
            }],
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          {
            slot: 'error_msg',
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          {
            type: 'datetimerange',
            label: '可挂售时间段：',
            slot: 'OnSaleTime',
            rules: [{
              required: true,
              message: '请选择可挂售时间段',
              trigger: 'change'
            }],
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          {
            type: 'radio',
            label: '可交易时间(天)：',
            slot: 'tradeDay',
            rules: [{
              required: true,
            }],
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          {
            slot: 'error_msg',
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          // 可交易时间段
          {
            type: 'datetimerange',
            label: '可交易时间段：',
            slot: 'tradeTime',
            rules: [{
              required: true,
              message: '请选择可交易时间段',
              trigger: 'change'
            }],
            show: {
              relationField: 'tradeTimeLimit',
              value: [1]
            },
          },
          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ],
        formDataGd: {
          minOnSaleDay: 1,
          minTradeDay: 1,
          maxTradeDay: 5,
          minTradeTime: "13:00",
          maxTradeTime: "14:00",
          maxOnSaleDay: 5,
          minOnSaleTime: "14:00",
          maxOnSaleTime: "15:00",
          onSaleMinPrice: null,
          onSaleMaxPrice: null,
          tradeTimeLimit:0
        }
      }
    },
    mounted() {
      this.getList()
      const username = localStorage.getItem('username')
      if (username == '周周' || username == 'daxiong' || username == 'shiqi' || username == '云龙' || username == 'admin' ||
        username == 'chz' || username == 'dingqiang') {
        this.buttonShow = true
      }
    },
    methods: {
      async opensetSellStatus(item) {
        let res = await this.$api.setSellStatus({
          ctid: item.ctid,
          entrustView: item.entrustViewStatus == 1 ? 0 : 1
        })
        if (res.status.code == 0) {
          this.$message.success('操作成功')
          this.getList()
        }
      },

      async submitClose() {
        console.log(this.formDataClose);
        if (this.dialogTitleClose == '新增休市') {
          let {
            status,
            result

            //     let params = {
            //   ctid: this.CloseLimitObj.ctid,
            //   csName: this.CloseLimitObj.title,
            //   extra: JSON.stringify({
            //     repeatStatus: this.formDataClose.repeatStatus,
            //     startTime: this.formDataClose.startTime[0],
            //     endTime: this.formDataClose.startTime[1]
            //   })
            // }

          } = await this.$api.dutyAdd({
            ctid: this.CloseLimitObj.ctid,
            csName: this.CloseLimitObj.title,
            dutyType: 'MARKET_CLOSURE',
            startTime: this.formDataClose.startTime[0],
            endTime: this.formDataClose.startTime[1],
            extra: JSON.stringify({

              repeatStatus: this.formDataClose.repeatStatus,
              startTime: this.formDataClose.startTime[0],
              endTime: this.formDataClose.startTime[1]
            })
          });
          if (status.code == 0) {
            this.$message.success('创建成功')
            this.dialogVisibleClose = false
          }
        } else if (this.dialogTitleClose == '设置休市') {
          let {
            status,
            result
          } = await this.$api.dutyEdit({
            dutyId: this.formDataClose.dutyId,
            ctid: this.CloseLimitObj.ctid,
            csName: this.CloseLimitObj.title,
            dutyType: 'MARKET_CLOSURE',
            startTime: this.formDataClose.startTime[0],
            endTime: this.formDataClose.startTime[1],
            extra: JSON.stringify({

              enableStatus: this.formDataClose.enableStatus,
              repeatStatus: this.formDataClose.repeatStatus,
              startTime: this.formDataClose.startTime[0],
              endTime: this.formDataClose.startTime[1]
            })
          });
          if (status.code == 0) {
            this.$message.success('修改成功')
            this.dialogVisibleClose = false
          }
          // dutyEdit
        }

      },
      async ClosedMarket(e) {
        let res = await this.$api.getLatestUnfinishedTask({
          ctid: e.ctid
        })
        if (res.status.code == 0) {
          if (res.result) {
            this.dialogTitle = '设置涨跌停' 
            this.formDatasetupLimit = res.result
            this.formDatasetupLimit.timing = res.result.timing.toString()

          } else {
            this.dialogTitleClose = '新增休市'
            this.formDataClose = {
              repeatStatus: 0,
              enableStatus: 1,

            }

          }
        }
        this.CloseLimitObj = e
        this.dialogVisibleClose = true
      },
      async submit() {
        if (this.formDatasetupLimit.monitoringTimes < 5) {
          this.$message.error('监测时间不能小于5min')
          return
        }

        this.formDatasetupLimit.ctid = this.setupLimitObj.ctid
        this.formDatasetupLimit.csName = this.setupLimitObj.title
        if (this.dialogTitle == '新增涨跌停') {
          let {
            status,
            result
          } = await this.$api.dutyAdd({
            ctid: this.formDatasetupLimit.ctid,
            dutyType: 'PRICE_INCREASE_MONITORING',
            timing: this.formDatasetupLimit.timing,
            startTime: this.formDatasetupLimit.startTime,
            endTime: this.formDatasetupLimit.endTime,
            extra: JSON.stringify({
              ...this.formDatasetupLimit,
              // increaseRate: this.formDatasetupLimit.increaseRate / 100

            })
          });
          if (status.code == 0) {
            this.$message.success('创建成功')
            this.dialogVisiblesetupLimit = false
          }
        } else if (this.dialogTitle == '编辑涨跌停') {
          let {
            status,
            result
          } = await this.$api.dutyEdit({
            dutyId: this.formDatasetupLimit.dutyId,
            ctid: this.formDatasetupLimit.ctid,
            dutyType: 'PRICE_INCREASE_MONITORING',
            timing: this.formDatasetupLimit.timing,
            startTime: this.formDatasetupLimit.startTime,
            endTime: this.formDatasetupLimit.endTime,
            extra: JSON.stringify({
              ...this.formDatasetupLimit,
              // increaseRate: this.formDatasetupLimit.increaseRate / 100
            })
          });
          if (status.code == 0) {
            this.$message.success('修改成功')
            this.dialogVisiblesetupLimit = false
          }
        }
        console.log(this.formDatasetupLimit);

      },
      setUpLimits(e) {
        console.log(e);

        this.dialogVisiblesetupLimit = true
        this.setupLimitObj = e
        // this.formDatasetupLimit = e
        this.$api.setUpLimit({
          ctid: e.ctid
        }).then(res => {
          if (res.status.code == 0) {
            if (res.result) {
              this.dialogTitle = '设置涨跌停'
              this.formDatasetupLimit = res.result
              this.formDatasetupLimit.timing = res.result.timing.toString()

            } else {
              this.dialogTitle = '新增涨跌停'
              this.formDatasetupLimit = {
                timing: '0',
                startTime: ""
              }
            }
          }
        })
      },
      onStartTimeTypeChange() {
        // 清空生效时间区域
        if (this.formDataLimit.isNowStart == 0) {
          this.formDataLimit.startTime = "";
        }
      },
      onStartTimeTypeChange2() {
        // 清空生效时间区域
        if (this.formDataLimit2.isNowStart == 0) {
          this.formDataLimit.startTime = "";
        }
      },
      async onConfirm() {
        if (this.open) {
          // 编辑
          let {
            status,
            result
          } = await this.$api.dutyEdit({
            dutyId: this.formDataLimit.dutyId,
            ctid: this.formDataLimitTemp.ctid,
            dutyType: 'LIMIT_BUY',
            isTiming: this.formDataLimitTemp.isTiming,
            endTime: this.formDataLimitTemp.endTime,
            isNowStart: this.formDataLimit.isNowStart,
            ...(this.formDataLimit.isNowStart != 1 && {
              startTime: this.formDataLimitTemp.startTime
            }),
            extra: JSON.stringify({
              csName: this.formDataLimitTemp.title,
              ctid: this.formDataLimitTemp.ctid,
              buyMaxNum: this.formDataLimit.buyMaxNum,
              isNowStart: this.formDataLimit.isNowStart,
              enable: this.formDataLimit.enable
            })
          });
          if (status.code == 0) {
            this.$message.success('修改成功')
            this.dialogVisible = false
          }
        } else {
          let {
            status,
            result
          } = await this.$api.dutyAdd({
            ctid: this.formDataLimitTemp.ctid,
            dutyType: 'LIMIT_BUY',
            isTiming: this.formDataLimit2.isTiming,
            endTime: this.formDataLimit2.endTime,
            isNowStart: this.formDataLimit2.isNowStart,
            ...(this.formDataLimit2.isNowStart != 1 && {
              startTime: this.formDataLimit2.startTime
            }),
            extra: JSON.stringify({
              csName: this.formDataLimitTemp.title,
              ctid: this.formDataLimitTemp.ctid,
              buyMaxNum: this.formDataLimit2.buyMaxNum,
              isNowStart: this.formDataLimit2.isNowStart,
              enable: this.formDataLimit2.enable
            })
          });
          if (status.code == 0) {
            this.$message.success('创建成功')
            this.dialogVisibleadd = false
          }
          // 新增
        }
        // 确认保存逻辑
        console.log("表单数据：", this.formDataLimit, this.formDataLimit2);
        this.dialogVisible = false;
      },
      async LimitOneDay(item) {
        this.formDataLimitTemp = item
        // this.loading = true

        let res = await this.$api.dutyLimitList({
          ctid: item.ctid
        })
        // this.formDataLimit = {
        //   enable: true, // 示例数据
        //   startTimeType: 'timed',
        //   startTime: '2024-11-10',
        //   endTime: '2024-11-20',
        //   limitPerPerson: 5
        // };
        if (res.status.code == 0 && res.result) {
          this.dialogVisible = true;
          this.formDataLimit = res.result
          this.open = true
          // this.loading = false

        } else {
          this.dialogVisibleadd = true;
          // this.loading = false

          this.open = false
        }
        console.log(this.formDataLimit);

      },
      openList() {

      },
      // 大图
      cover_click(e) {
        this.isimgDelete = true
        this.imgurl = e
      },
      onQueryChange(data) {
        // this.query = data
        this.searchForm = data
        this.getList(true)
      },
      // 查询列表
      async getList(init) {
        if (init) {
          this.initPage()
        }
        // if (this.query.createTimeStart) {
        //   createTimeStart = this.query.createTimeStart[0]
        //   createTimeEnd = this.query.createTimeStart[1]
        // }
        const res = await this.$api.seriesList({
          pageNum: this.page.pageNum,
          pageSize: this.page.pageSize,
          name: this.searchForm.name,
          ctid: this.searchForm.ctid,
          isPgc: this.searchForm.isPgc,
          ...this.searchForm,
        })
        res.result.list.forEach((item) => {
          item.u4GoodsCount = null
          item.u4UserCount = null
          item.u0OnSaleGoodsCount = null
          // 新增字段
          item.holdNumMax = null
          item.holdNumMiddle = null
          item.holdNumMin = null
          item.userNumMax = null
          item.userNumMiddle = null
          item.userNumMin = null
          item.u4OnSaleGoodsCount = null
        })
        this.tableData = res.result.list
        this.page.totalCount = res.result.totalCount
      },
      clear() {
        this.formInline.adult = ''
        this.formInline.blindSaleStatus = ''
        this.formInline.buyLimitTimes = ''
        this.formInline.ctIds = ''
        this.formInline.email = ''
        this.formInline.id = ''
        this.formInline.isDeleted = ''
        this.formInline.isReal = ''
        this.formInline.mold = ''
        this.formInline.name = ''
        this.formInline.nickname = ''
        this.formInline.purview = ''
        this.formInline.recommend = ''
        this.formInline.saleType = ''
        this.formInline.selfTags = ''
        this.formInline.visibility = ''
      },
      // 点击详情
      async look_click(val) {
        this.$router.push({
          name: 'seriesListDetail',
          query: {
            id: val.id
          }
        })
      },
      // 设置系列内作品上架最高价格
      async set_up_price(ctid, onSaleMaxPrice) {
        const res = await this.$api.updateOnSaleMaxPrice({
          onSaleMaxPrice,
          ctid
        })
        if (res.status.code == 0) {
          this.$message.success('设置成功')
        } else {
          this.$message.error(res.status.msg)
        }
      },
      open3(item) {
        this.$prompt('请输入系列内作品上架最高价格', '修改系列内最高价格', {
          confirmButtonText: '确认修改',
          cancelButtonText: '取消'
        }).then(({
          value
        }) => {
          if (value) {
            this.set_up_price(item.ctid, value)
          } else {
            this.$message.error('请输入价格')
          }
        }).catch(() => {

        })
      },
      open2(item) {
        this.$confirm('是否确认重置修改系列内最高价格', '重置系列内最高价格', {
          confirmButtonText: '确认重置',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.reset_price(item.ctid)
        }).catch(() => {

        })
      },
      // 重置系列内作品上架最高价格
      async reset_price(ctid) {
        console.log(ctid)
        const res = await this.$api.updateOnSaleMaxPrice({
          onSaleMaxPrice: null,
          ctid
        })
        if (res.status.code == 0) {
          this.$message.success('重置成功')
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async openBind(type, row) { // 是否开启竞价
        this.$confirm(`是否确认${type == 1 ? '开启' : '关闭'}竞价`, `${type == 1 ? '开启' : '关闭'}竞价`, {
          confirmButtonText: `确认${type == 1 ? '开启' : '关闭'}`,
          cancelButtonText: '取消',
        }).then(async () => {
          this.loading = true
          this.loadingText = "正在加载"
          let str = []
          if (this.multipleSelection.length >= 1) {
            this.multipleSelection.forEach(item => {
              str.push(item.ctid)
            })
          } else {
            str = [row.ctid]
          }
          const {
            result,
            status
          } = await this.$api.openBidding({
            openBiddingJson: JSON.stringify(str),
            type: type // 0:关 1:开
          })
          if (status.code === 0) {
            this.$message.success(`${type == 1 ? '开启' : '关闭'}竞价`)
            this.getList()
            this.loading = false
          } else {
            this.loading = false
          }
        }).catch(() => {

        })
      },
      // 批量选择
      handleSelectionChange(val) {
        this.multipleSelection = val
        console.log(val, '批量选择')
      },

      // 批量设置推荐级别
      batchRank() {
        this.dialogUp = true
        this.title = '批量设置推荐级别'
      },
      // 批量上下架
      openItemVisibility() {
        if (this.multipleSelection.length === 0) {
          this.$message.error('请选择需要修改的系列')
        } else {
          this.dialogUp = true
        }
      },
      // 批量开启竞价
      openBindArr() {
        if (this.multipleSelection.length === 0) {
          this.$message.error('请选择需要开启的竞价')
        } else {
          this.openBind(1)
        }
      },
      async click_submit() {
        const ctid = []
        if (this.multipleSelection.length == 0) {
          this.$message.error('请选择需要修改的系列')
        } else {
          this.multipleSelection.forEach(item => {
            ctid.push(item.ctid)
          })
          const res = await this.$api.batchUpdateItemVisibility({
            ctidsStr: JSON.stringify(ctid),
            visibility: this.fodialogUpForm.visibility,
            num: this.fodialogUpForm.num
          })
          if (res.status.code == 0) {
            this.$message.success('修改成功')
            this.dialogUp = false
          }
        }
      },
      batchItemVisibility() {
        this.dialogUp = true
        this.title = '批量上下架系列内的创作品'
      },
      set_up_flag(item) {
        this.saleForm.minPrice = item.minResalePrice;
        this.saleForm.maxPrice = item.maxResalePrice
        console.log(item)
        this.ctid = item.ctid
        this.isSaleSign = true
      },
      open_tag(item) {

        this.tagForm.tagValue = item.tag
        this.tagForm.newFlag = item.newFlag
        this.ctid = item.ctid

        this.tagManageListSearch()
        // this.tagSeriesList(this.ctid)

      },
      handleClose() {
        // this.tagForm.tagIdListJson=[]
        // console.log('this.tagForm.tagList----',this.tagForm.tagList)
        this.isTagSign = false
      },
      tagSwitch(item) {
        console.log("item", item)
      },
      //加载用户标签
      async tagSeriesList(id) {


        // let listData=this.tagForm.tagList
        const params = {
          ctid: id,
          dutyType: 'BASE_GOODS_COUNT_CHANGE'
        }
        const {
          status,
          result
        } = await this.$api.querySeriesTag(params)
        console.log('获取query数据-----', params)
        this.tagForm.tagIdListJson = []
        this.tagForm.tagValue = result.tag
        this.tagForm.newFlag = result.newFlag
        result.tagList.forEach(item => {
          console.log("item------", item)
          this.tagForm.tagIdListJson.push(item.id)
        })
        console.log('获取query数据-----', this.tagForm)
        // this.tagForm.tagIdListJson=[5,3]
        // console.log('获取query数据-----',result)
        // if (status.code === 0) {


        //   console.log('获取query数据-----',result)



        //   this.tagForm.tagValue=result.tag
        //   this.tagForm.newFlag=result.newFlag
        //   this.tagForm.tagIdListJson=result.tagList

        //    console.log('-----',this.tagForm)
        //   let tagArr= this.tagForm.tagIdListJson
        //   let tagArr2=this.tagForm.tagList

        //   console.log("tagArr",tagArr,tagArr2)

        //   // const tempArr= tagArr.forEach(item=>{
        //   //    const matched =  tagArr2.find((res)=>res.name === item.name)

        //   // })
        //   // console.log("tempArr",tempArr)


        //   // this.tagForm.tagIdListJson=result.tagList

        //   // this.tagForm.tagIdListJson=result.tagList
        //   // this.tagForm.tagList=result.tagList
        //   // const data = result.tagList
        // 	// this.tagForm.tagOptions = result
        // 	// data.forEach((item) => {
        // 	// 	this.tagForm.tagList.push({
        //  //      id:item.id,
        //  //      name:item.name,
        // 	// 	})
        // 	// })
        //   // console.log('this.tagForm.tagList',this.tagForm.tagList)
        // }
        this.isTagSign = true
      },
      //点击编辑系列标签
      async tagManageListSearch(isInit) {
        let ctid;
        console.log('asdasdkaksld', this.query.ctid)
        // if (this.query.ctid) {
        //   ctid = this.query.ctid.split("(")[1].split(")")[0]
        // }
        const params = {
          ...this.query,
          ...this.page,
          pageNum: isInit ? 1 : this.page.pageNum,
          ctid,
          dutyType: 'BASE_GOODS_COUNT_CHANGE'
        }
        // console.log('列表数据',params)
        const {
          status,
          result
        } = await this.$api.tagList(params)
        console.log('获取列表数据', result)
        if (status.code === 0) {
          this.tagForm.tagList = []
          const data = result.list
          data.forEach((item) => {
            this.tagForm.tagList.push({
              id: item.id,
              img: item.image,
              name: item.name,
              weight: item.weight
            })
          })

          // console.log('123123123213123',this.tagForm)
          this.tagSeriesList(this.ctid)

          // this.tableData = dataList
          this.page.totalCount = result.totalCount
          this.page.pageSize = result.pageSize
          this.page.pageCount = result.pageCount
        }


      },

      async set_up_submit() {
        const res = await this.$api.batchUpdateItemNotSaleSign({
          ctid: this.ctid,
          notSaleSign: this.saleSign,
          minResalePrice: this.saleForm.minPrice,
          maxResalePrice: this.saleForm.maxPrice
        })
        if (res.status.code == 0) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.status.msg)
        }
      },
      set_updateLeader(item) {
        console.log(item)
        this.ctid = item.ctid
        this.isLeader = true
      },
      async set_updateLeader_submit() {
        const res = await this.$api.updateLeader({
          ctid: this.ctid,
          leader: this.leader
        })
        if (res.status.code == 0) {
          this.isLeader = false
          this.$message.success('修改成功')
          this.getList(true)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async tag_submit() {

        let tempArr = JSON.stringify(this.tagForm.tagIdListJson)
        console.log('----------tempArr', tempArr, typeof tempArr)
        console.log('----------this.tagForm', this.tagForm)
        const res = await this.$api.updateCollectionTag({
          ctid: this.ctid,
          tag: this.tagForm.tagValue,
          newFlag: this.tagForm.newFlag,
          tagIdListJson: tempArr
        })
        if (res.status.code == 0) {
          this.$message.success('修改成功')
          this.isTagSign = false
          this.getList()
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async open_destroy(item) {
        this.$confirm('是否确认销毁系列内未产生交易的创作品', '系列内作品销毁', {
          confirmButtonText: '确认销毁创作品',
          cancelButtonText: '取消',
          type: 'error'
        }).then(() => {
          this.destroy(item.ctid)
        }).catch(() => {

        })
      },
      async destroy(ctid) {
        this.loading = true
        this.loadingText = '正在销毁，请耐心等待'
        const res = await this.$api.destroyByCtid({
          ctid
        })
        if (res.status.code == 0) {
          this.$message.success('销毁成功')
          this.loading = false
        } else {
          this.$message.error(res.status.msg)
          this.loading = false
        }
      },
      async open_data(item) {
        // item.u4GoodsCount=1
        // item.u0GoodsCount=2
        // item.u4UserCount=3
        // item.u0UserCount=4
        // item.u0OnSaleGoodsCount=5
        const res = await this.$api.seriesGoodsCount({
          ctid: item.ctid
        })
        if (res.status.code == 0) {
          const u0GoodsCount = res.result.u0GoodsCount // 大户持有数
          const u0UserCount = res.result.u0UserCount // 大户人数

          item.u4GoodsCount = res.result.u4GoodsCount
          item.u4UserCount = res.result.u4UserCount
          item.u0OnSaleGoodsCount = res.result.u0OnSaleGoodsCount
          item.u4OnSaleGoodsCount = res.result.u4OnSaleGoodsCount
          item.holdNumMax = Math.round(u0GoodsCount * 0.1)
          item.holdNumMiddle = Math.round(u0GoodsCount * 0.2)
          item.holdNumMin = Math.round(u0GoodsCount * 0.7)

          item.userNumMax = Math.round(u0UserCount * 0.1)
          item.userNumMiddle = Math.round(u0UserCount * 0.2)
          item.userNumMin = Math.round(u0UserCount * 0.7)
          console.error(item)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      // 导出
      async batchCsv(item) {
        if (item.u0GoodsCount == 0) {
          this.$message.error('查看0持有明细无数据，暂无法导出')
          return false
        }
        this.loading = true
        const res = await this.$api.seriesGoodsCountExport({
          ctid: item.ctid
        })
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
          this.loading = false
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          this.loading = false
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '0持有明细' + Date.now() + '.csv')
          this.$message.success('0持有明细导出成功')
          this.loading = false
        }
      },
      async open_allDestroy(item) {
        this.$confirm('是否确认销毁系列内所有作品', '系列内作品全部销毁', {
          confirmButtonText: '确认全部销毁',
          cancelButtonText: '取消',
          type: 'error'
        }).then(() => {
          this.submitAllDestroy(item)
        }).catch(() => {

        })
      },
      async submitAllDestroy(item) {
        this.loading = true
        this.loadingText = '正在销毁，请耐心等待'
        const formDataDestroy = {
          ctid: item.ctid,
          dutyName: item.name,
          dutyType: 'DESTROY',
          isTiming: 0
        }
        const extraJson = {
          destroyTime: 1,
          destroyNum: item.activeNum,
          itemType: 'CTID',
          isTiming: 0
        }
        const data = {
          ...formDataDestroy,
          extra: JSON.stringify(extraJson)
        }
        const {
          status
        } = await this.$api.dutyAdd(data)
        if (status.code === 0) {
          this.$message.success('销毁成功')
          this.loading = false
          setTimeout(() => {
            this.getList()
          }, 2000)
        } else {
          this.loading = false
        }
      },
      /**
       * 开启批量退市 二次确认弹窗
       *
       */
      async exitOrderTarget(type, item) {
        this.$confirm(`是否确认${type == 1 ? '开启' : '关闭'}批量退市`, `${type == 1 ? '开启' : '关闭'}批量退市`, {
          confirmButtonText: `确认${type == 1 ? '开启' : '关闭'}`,
          cancelButtonText: '取消',
        }).then(async () => {
          this.loading = true
          this.loadingText = "正在加载"
          const {
            status
          } = await this.$api.enableExitOrderTarget({
            ctid: item.ctid,
            enable: type
          })
          if (status.code === 0) {
            this.$message.success(`${type == 1 ? '开启' : '关闭'}批量退市成功`)
            this.loading = false
            setTimeout(() => {
              this.getList()
            }, 2000)
          } else {
            this.loading = false
          }
        }).catch(() => {

        })
      },
      /**
       * 开启批量 二次确认弹窗
       *
       */
      async orderTarget(type, item) {
        this.$confirm(`是否确认${type == 1 ? '开启' : '关闭'}批量下单`, `${type == 1 ? '开启' : '关闭'}批量下单`, {
          confirmButtonText: `确认${type == 1 ? '开启' : '关闭'}`,
          cancelButtonText: '取消',
        }).then(async () => {
          this.loading = true
          this.loadingText = "正在加载"
          const {
            status
          } = await this.$api.enableOrderTarget({
            ctid: item.ctid,
            enable: type
          })
          if (status.code === 0) {
            this.$message.success(`${type == 1 ? '开启' : '关闭'}批量下单成功`)
            this.loading = false
            setTimeout(() => {
              this.getList()
            }, 2000)
          } else {
            this.loading = false
          }
        }).catch(() => {

        })
      },
      openJwc(item) {
        this.ctid = item.ctid
        this.isJwc = true
      },
      openGd(item) {
        this.ctid = item.ctid
        this.tradeTimeInfo()
      },
      async set_update_jwc_submit() {
        const res = await this.$api.updateIsJwc({
          ctid: this.ctid,
          isJwc: this.jwc
        })
        if (res.status.code == 0) {
          this.isJwc = false
          this.$message.success('修改成功')
          this.getList(true)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async submit1() {
        this.formDataGd.onSaleMinPrice = this.formDataGd.onSaleMinPrice - 0
        this.formDataGd.onSaleMaxPrice = this.formDataGd.onSaleMaxPrice - 0
        let res = await this.$api.tradeTimeUpdate({
          ...this.formDataGd,
          ctid: this.ctid
        })
        if (res.status.code == 0) {
          this.dialogVisible1 = false
          this.$message.success('修改成功')
          this.getList()
        }
        // fixedUpdate
      },
      async tradeTimeInfo() {
        const res = await this.$api.tradeTimeInfo({
          ctid: this.ctid
        })
        if (res.status.code == 0) {
            if(res.result.tradeTimeLimit==1){
               this.formDataGd = res.result
            }
             this.dialogVisible1 = true
        } else {
          this.$message.error(res.status.msg)
        }
      },
    },
    computed: {
      formSchema() {
        return [{
            type: 'input',
            label: '涨跌幅度(0-1)：',
            placeholder: '请输入涨跌幅度(0-1)',
            field: 'increaseRate',
            rules: [{
              required: true,
              message: '请输入涨跌幅度(0-1)',
              trigger: 'blur'
            }, ]
          },
          {
            type: 'input',
            label: '监测时间(min)：',
            placeholder: '请输入监测时间',
            field: 'monitoringTimes',
            rules: [{
              required: true,
              message: '请输入监测时间且大于5分钟',
              trigger: 'blur'
            }]
          },
          {
            slot: 'timing'
          },
          {
            type: 'datetime',
            label: '开始时间：',
            disabled: this.formDatasetupLimit.timing == '0' || this.dialogTitle == '设置涨跌停',
            placeholder: '请输入开始时间',
            field: 'startTime',

          },
          {
            type: 'datetime',
            label: '结束时间：',
            disabled: this.dialogTitle == '设置涨跌停',
            placeholder: '请输入结束时间',
            field: 'endTime',
            rules: [{
              required: true,
              message: '请输入结束时间',
              trigger: 'blur'
            }]
          },

          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ]
      },
      formSchemaClose() {
        return [{
            label: '是否启用：',
            field: 'enableStatus',
            type: 'radio',
            options: [{
              label: '不启用',
              value: 0
            }, {
              label: '启用',
              value: 1
            }],
            rules: [{
              required: true,
              message: '请选择是否启用',
              trigger: 'change'
            }],
          },
          {
            label: '是否重复：',
            field: 'repeatStatus',
            disabled: this.dialogTitleClose == '设置休市',
            type: 'radio',
            options: [{
              label: '不重复',
              value: 0
            }, {
              label: '每日重复',
              value: 1
            }],
            rules: [{
              required: true,
              message: '请选择是否重复',
              trigger: 'change'
            }],
          },

          {
            type: 'datetimerange',
            label: '休市时间：',
            placeholder: '请选择休市时间',
            disabled: this.dialogTitleClose == '设置休市',

            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择休市时间',
              trigger: 'blur'
            }],

          },

          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ]
      },
    },
  }
</script>

<style>
  .bg {
    /* background-color:lemonchiffon; */
    flex: 1;
    height: 100%;
    text-align: center;
    height: 64px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .el-table .cell {
    padding: 0px !important;
  }

  .flex-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .margin_top555 {
    margin-top: -10px;
  }

  .operateBtn {
    width: 50px;
    height: 30px;


  }
</style>
