<template>
  <d2-container class="page">
    <div class="content">
      <el-form label-position="right" label-width="200px">
        <el-form-item label="系列id:" :label-width="formLabelWidth">
          {{ information.id }}
        </el-form-item>
        <el-form-item label="系列tid:" :label-width="formLabelWidth">
          {{ information.ctid }}
        </el-form-item>
        <el-form-item label="系列名称:" :label-width="formLabelWidth">
          {{ information.name }}
        </el-form-item>
        <el-form-item label="用户昵称:" :label-width="formLabelWidth">
          {{ information.nickname }}
        </el-form-item>
        <el-form-item label="用户邮箱:" :label-width="formLabelWidth">
          {{ information.email }}
        </el-form-item>
        <el-form-item label="评论次数:" :label-width="formLabelWidth">
          {{ information.commentCount }}
        </el-form-item>
        <el-form-item label="关注次数:" :label-width="formLabelWidth">
          {{ information.favoriteCount }}
        </el-form-item>
        <el-form-item label="点赞次数:" :label-width="formLabelWidth">
          {{ information.fondCount }}
        </el-form-item>
        <el-form-item label="作品数量:" :label-width="formLabelWidth">
          {{ information.goodsCount }}
        </el-form-item>
        <el-form-item label="上次审核通过内容:" :label-width="formLabelWidth">
          {{ information.lastEdit }}
        </el-form-item>
        <el-form-item label="用户头像:" :label-width="formLabelWidth">
          <img
            :src="information.avatar"
            alt=""
            style="width: 100px; height: 100px"
          />
        </el-form-item>
        <el-form-item label="系列简介:" :label-width="formLabelWidth">
          {{ information.content }}
        </el-form-item>
        <el-form-item label="系列封面图:" :label-width="formLabelWidth">
          <img
            :src="information.cover"
            alt=""
            style="width: 100px; height: 100px"
          />
        </el-form-item>
        <el-form-item label="审核说明:" :label-width="formLabelWidth">
          {{ information.adultDesc }}
        </el-form-item>
        <el-form-item label="自定义标签:" :label-width="formLabelWidth">
          {{ information.selfTags }}
        </el-form-item>
        <el-form-item label="系列排序顺序:" :label-width="formLabelWidth">
          {{ information.sort }}
        </el-form-item>
        <el-form-item label="系列类型:" :label-width="formLabelWidth">
          <span v-if="information.mold == '0'">默认类型</span>
          <span v-if="information.mold == '1'">普通系列</span>
          <span v-if="information.mold == '2'">实物系列</span>
          <span v-if="information.mold == '3'">盲盒系列</span>
        </el-form-item>
        <el-form-item label="系列审核:" :label-width="formLabelWidth">
          <span v-if="information.adult == '0'">未审核</span>
          <span v-if="information.adult == '1'">人审成功</span>
          <span v-if="information.adult == '2'">人审失败</span>
          <span v-if="information.adult == '3'">机审成功</span>
          <span v-if="information.adult == '4'">机审失败</span>
        </el-form-item>
        <el-form-item label="盲盒交易状态:" :label-width="formLabelWidth">
          <span v-if="information.blindSaleStatus == '0'">未领取</span>
          <span v-if="information.blindSaleStatus == '1'">已领取</span>
        </el-form-item>
        <el-form-item label="系列作品排序类型:" :label-width="formLabelWidth">
          <span v-if="information.goodsSortType == '1'">最近编辑在前</span>
          <span v-if="information.goodsSortType == '2'">最新创建在前</span>
          <span v-if="information.goodsSortType == '3'">最新创建在后</span>
          <span v-if="information.goodsSortType == '4'">自定义排序</span>
        </el-form-item>
        <el-form-item label="限制购买次数:" :label-width="formLabelWidth">
          <span v-if="information.buyLimitTimes == '0'">不限购</span>
          <span v-if="information.buyLimitTimes == '1'"
            >{{ information.buyLimitTimes }}限购次数</span
          >
        </el-form-item>
        <el-form-item label="是否删除:" :label-width="formLabelWidth">
          <span v-if="information.isDeleted == '0'">不删除</span>
          <span v-if="information.isDeleted == '1'">删除</span>
        </el-form-item>
        <el-form-item label="是否包含实物:" :label-width="formLabelWidth">
          <span v-if="information.isReal == '0'">不包含实物</span>
          <span v-if="information.isReal == '1'">含实物</span>
        </el-form-item>
        <el-form-item label="可见权限:" :label-width="formLabelWidth">
          <span v-if="information.purview == '0'">所有人可见</span>
          <span v-if="information.purview == '1'">仅粉丝可见</span>
          <span v-if="information.purview == '2'">仅自己可见</span>
        </el-form-item>
        <el-form-item label="发售形式:" :label-width="formLabelWidth">
          <span v-if="information.saleType == '1'">即时发售</span>
          <span v-if="information.saleType == '2'">定时发售</span>
          <span v-if="information.saleType == '3'">手动发售</span>
        </el-form-item>
        <el-form-item label="是否可见:" :label-width="formLabelWidth">
          <span v-if="information.visibility == '0'">下架</span>
          <span v-if="information.visibility == '1'">上架</span>
        </el-form-item>
        <el-form-item label="推荐级别:" :label-width="formLabelWidth">
          {{ information.recommend }}
        </el-form-item>
        <el-form-item label="创建时间:" :label-width="formLabelWidth">
          {{ information.createAt }}
        </el-form-item>
        <el-form-item label="上次编辑时间:" :label-width="formLabelWidth">
          {{ information.lastEditTime }}
        </el-form-item>
        <el-form-item label="预售时间:" :label-width="formLabelWidth">
          {{ information.salePreTime }}
        </el-form-item>
        <el-form-item label="发售时间:" :label-width="formLabelWidth">
          {{ information.saleTime }}
        </el-form-item>
        <el-form-item label="更新时间:" :label-width="formLabelWidth">
          {{ information.updateAt }}
        </el-form-item>
      </el-form>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'seriesListDetail',
  data () {
    return {
      id: '',
      information: {},
      formLabelWidth: '180px'
    }
  },
  mounted () {
    this.id = this.$route.query.id
    this.seriesDetail()
  },
  methods: {
    async seriesDetail () {
      const res = await this.$api.seriesDetail({
        id: this.id
      })
      this.information = res.result
    }
  }
}
</script>

<style></style>
