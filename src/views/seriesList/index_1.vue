<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" style="background-color: #ffffff; padding: 20px">
      <el-form-item label="系列审核">
        <el-select  v-model="formInline.adult" placeholder="系列审核">
          <el-option label="未审核" :value="0"></el-option>
          <el-option label="人审成功" :value="1"></el-option>
          <el-option label="人审失败" :value="2"></el-option>
          <el-option label="机审成功" :value="3"></el-option>
          <el-option label="机审失败" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="盲盒交易状态">
        <el-select  v-model="formInline.blindSaleStatus" placeholder="盲盒交易状态">
          <el-option label="未领取" :value="0"></el-option>
          <el-option label="已领取" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="限制购买次数">
        <el-select  v-model="formInline.buyLimitTimes" placeholder="限制购买次数">
          <el-option label="不限购" :value="0"></el-option>
          <el-option label="其他限购次数" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否删除">
        <el-select  v-model="formInline.isDeleted" placeholder="是否删除">
          <el-option label="不删除" :value="0"></el-option>
          <el-option label="删除" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否包含实物">
        <el-select  v-model="formInline.isReal" placeholder="是否包含实物">
          <el-option label="不含实物" :value="0"></el-option>
          <el-option label="含实物" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="系列类型">
        <el-select  v-model="formInline.mold" placeholder="系列类型">
          <el-option label="默认系列" :value="0"></el-option>
          <el-option label="普通系列" :value="1"></el-option>
          <el-option label="实物系列" :value="2"></el-option>
          <el-option label="盲盒系列" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="可见权限">
        <el-select  v-model="formInline.purview" placeholder="可见权限">
          <el-option label="所有人可见" :value="0"></el-option>
          <el-option label="仅粉丝可见" :value="1"></el-option>
          <el-option label="仅自己可见" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推荐级别">
        <el-select  v-model="formInline.recommend" placeholder="推荐级别">
          <el-option label="0" :value="0"></el-option>
          <el-option label="1" :value="1"></el-option>
          <el-option label="2" :value="2"></el-option>
          <el-option label="3" :value="3"></el-option>
          <el-option label="4" :value="4"></el-option>
          <el-option label="5" :value="5"></el-option>
          <el-option label="6" :value="6"></el-option>
          <el-option label="7" :value="7"></el-option>
          <el-option label="8" :value="8"></el-option>
          <el-option label="9" :value="9"></el-option>
          <el-option label="10" :value="10"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发售形式(盲盒)">
        <el-select  v-model="formInline.saleType" placeholder="发售形式(盲盒)">
          <el-option label="即时发售" :value="1"></el-option>
          <el-option label="定时发售" :value="2"></el-option>
          <el-option label="手动发售" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否可见">
        <el-select  v-model="formInline.visibility" placeholder="是否可见">
          <el-option label="下架" :value="0"></el-option>
          <el-option label="上架" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="系列tid">
        <el-select  v-model="formInline.ctIds" multiple filterable allow-create default-first-option
          placeholder="请输入系列tid">
        </el-select>
      </el-form-item>
      <el-form-item label="用户邮箱">
        <el-input v-model="formInline.email" placeholder="请输入用户邮箱" clearable></el-input>
      </el-form-item>
      <!-- 	<el-form-item label="系列id">
				<el-input v-model="formInline.id" placeholder="请输入系列id" clearable></el-input>
			</el-form-item> -->
      <el-form-item label="系列名称">
        <el-input v-model="formInline.name" placeholder="请输入系列名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="用户昵称">
        <el-input v-model="formInline.nickname" placeholder="请输入用户昵称" clearable></el-input>
      </el-form-item>
      <el-form-item label="自定义标签">
        <el-input v-model="formInline.selfTags" placeholder="请输入自定义标签" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="clear(1)">清除</el-button>
        <el-button type="primary" @click="batchUp()" :disabled="!multipleSelection.length">批量上下架
        </el-button>
        <el-button type="primary" @click="batchRank()" :disabled="!multipleSelection.length">批量设置推荐级别
        </el-button>
        <el-button type="primary" @click="batchItemVisibility()" :disabled="!multipleSelection.length">
          批量上下架系列内的创作品
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column fixed prop="id" label="id" type="selection" align="center"></el-table-column>
      <el-table-column fixed prop="id" label="系列id" align="center"></el-table-column>
      <el-table-column prop="ctid" label="系列tid" align="center"></el-table-column>
      <el-table-column prop="visibility" label="是否可见" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.visibility == '1'">上架</el-tag>
          <el-tag v-if="scope.row.visibility == '0'">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="nickname" label="用户昵称" align="center"></el-table-column>
      <el-table-column prop="avatar" label="用户头像" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image style="width: 50px; height: 50px" :src="scope.row.avatar">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="isDeleted" label="是否删除" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.isDeleted == '1'">是</el-tag>
          <el-tag v-if="scope.row.isDeleted == '0'">否</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="email" label="用户邮箱" align="center"></el-table-column>
      <el-table-column prop="name" label="系列名称" align="center"></el-table-column>
      <el-table-column prop="onSaleMaxPrice" label="系列内最高价格" align="center"></el-table-column>
      <el-table-column prop="cover" label="系列封面图" align="center">
        <template scope="scope">
          <div style="width: 100%" @click="cover_click(scope.row.cover)">
            <el-image style="width: 100px; height: 100px" :src="scope.row.cover">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="系列排序顺序" align="center"></el-table-column>
      <el-table-column prop="content" label="系列简介" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="adultDesc" label="审核说明" align="center"></el-table-column>
      <el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="newFlag" label="是否为新品" align="center">
        <template scope="scope">
          <div style="width: 100%">
            {{ scope.row.newFlag == 0 ? '否' : '是' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="tag" label="系列标签" align="center">
        <template scope="scope">
          <div style="width: 100%" v-if="scope.row.tag > 6">
            鸡{{ (scope.row.tag - 6) / 2 }}
          </div>
          <div style="width: 100%" v-else>
            蛋{{ (scope.row.tag) / 2 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="look_click(scope.row)">详情</el-button>
          <el-button type="text"  @click="open3(scope.row)">设置上架最高价</el-button>
          <el-button type="text"  @click="open2(scope.row)">重置价格</el-button>
          <el-button style="color:#F56C6C" type="text"  @click="open_destroy(scope.row)">销毁创作品</el-button>
          <el-button type="text"  @click="set_up_flag(scope.row)">设置系列寄售状态</el-button>
          <el-button type="text"  @click="open_tag(scope.row)">修改系列标签</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <common-pagination ref="commonPagination" :page.sync="page" @change="getList">
      </common-pagination>
    </div>
    <el-dialog title="" :visible.sync="isimgDelete" width="50%">
      <div style="width: 100%">
        <el-image style="width: 500px" :src="imgurl"></el-image>
      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="dialogUp" width="50%">
      <el-form :model="form">
        <el-form-item label="批量设置推荐级别" v-if="this.title === '批量设置推荐级别'">
          <el-input v-model="form.value" placeholder="请输入推荐级别" clearable style="width: 80%"></el-input>
        </el-form-item>
        <el-form-item label="状态" v-else>
          <el-select  v-model="form.value" placeholder="状态" style="width: 80%">
            <el-option label="上架" :value="1"></el-option>
            <el-option label="下架" :value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogUp = false">取 消</el-button>
        <el-button type="primary" @click="click_submit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="设置系列寄售状态" :visible.sync="isSaleSign" width="50%">
      <el-radio v-model="saleSign" label="0">正常流通</el-radio>
      <el-radio v-model="saleSign" label="1">仅供收藏</el-radio>
      <el-radio v-model="saleSign" label="2">仅供转赠</el-radio>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isSaleSign = false">取 消</el-button>
        <el-button type="primary" @click="set_up_submit()">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改系列标签" :visible.sync="isTagSign" width="30%">
      <el-form :model="tagForm">
        <el-form-item label="系列标签">
          <el-select  v-model="tagForm.tagValue" placeholder="请选择系列标签">
            <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否为新品">
          <el-radio-group v-model="tagForm.newFlag">
            <el-radio v-model="tagForm.newFlag" :label="1">是</el-radio>
            <el-radio v-model="tagForm.newFlag" :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isTagSign = false">取 消</el-button>
        <el-button type="primary" @click="tag_submit()">确 定</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
import common from '@/mixins/common'

export default {
  name: 'seriesList',
  mixins: [common],
  data() {
    return {
      tableData: [],
      isimgDelete: false,
      imgurl: '',
      formInline: {
        adult: '',
        blindSaleStatus: '',
        buyLimitTimes: '',
        ctIds: '',
        email: '',
        id: '',
        isDeleted: '',
        isReal: '',
        mold: '',
        name: '',
        nickname: '',
        purview: '',
        recommend: '',
        saleType: '',
        selfTags: '',
        visibility: ''
      },
      multipleSelection: [],
      idListStr: [],
      dialogUp: false,
      form: {
        value: ''
      },
      dialogRank: false,
      title: '',
      isSaleSign: false,
      saleSign: "0",
      ctid: "",
      isTagSign: false,
      tagForm: {
        tagValue: '',
        newFlag: 0
      },
      tagOptions: [{
        label: '无标签',
        value: 0
      }, {
        label: '蛋x0.5',
        value: 1
      }, {
        label: '蛋x1',
        value: 2
      }, {
        label: '蛋x1.5',
        value: 3
      }, {
        label: '蛋x2',
        value: 4
      }, {
        label: '蛋x2.5',
        value: 5
      }, {
        label: '蛋x3',
        value: 6
      }, {
        label: '鸡x0.5',
        value: 7
      }, {
        label: '鸡x1',
        value: 8
      }, {
        label: '鸡x1.5',
        value: 9
      }, {
        label: '鸡x2',
        value: 10
      }, {
        label: '鸡x2.5',
        value: 11
      }, {
        label: '鸡x3',
        value: 12
      }],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 大图
    cover_click(e) {
      this.isimgDelete = true
      this.imgurl = e
    },
    // 查询列表
    async getList(init) {
      if (init) {
        this.initPage()
      }
      const res = await this.$api.seriesList({
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
        adult: this.formInline.adult,
        blindSaleStatus: this.formInline.blindSaleStatus,
        buyLimitTimes: this.formInline.buyLimitTimes,
        ctids: JSON.stringify(this.formInline.ctIds),
        email: this.formInline.email,
        id: this.formInline.id,
        isDeleted: this.formInline.isDeleted,
        isReal: this.formInline.isReal,
        mold: this.formInline.mold,
        name: this.formInline.name,
        nickname: this.formInline.nickname,
        purview: this.formInline.purview,
        recommend: this.formInline.recommend,
        saleType: this.formInline.saleType,
        selfTags: this.formInline.selfTags,
        visibility: this.formInline.visibility
      })
      this.tableData = res.result.list
      this.page.totalCount = res.result.totalCount
    },
    clear() {
      this.formInline.adult = ''
      this.formInline.blindSaleStatus = ''
      this.formInline.buyLimitTimes = ''
      this.formInline.ctIds = ''
      this.formInline.email = ''
      this.formInline.id = ''
      this.formInline.isDeleted = ''
      this.formInline.isReal = ''
      this.formInline.mold = ''
      this.formInline.name = ''
      this.formInline.nickname = ''
      this.formInline.purview = ''
      this.formInline.recommend = ''
      this.formInline.saleType = ''
      this.formInline.selfTags = ''
      this.formInline.visibility = ''
    },
    // 点击详情
    async look_click(val) {
      this.$router.push({
        name: 'seriesListDetail',
        query: {
          id: val.id
        }
      })
    },
    //设置系列内作品上架最高价格
    async set_up_price(ctid, onSaleMaxPrice) {
      const res = await this.$api.updateOnSaleMaxPrice({
        onSaleMaxPrice,
        ctid
      })
      if (res.status.code == 0) {
        this.$message.success('设置成功')
      } else {
        this.$message.error(res.status.msg)
      }
    },
    open3(item) {
      this.$prompt('请输入系列内作品上架最高价格', '修改系列内最高价格', {
        confirmButtonText: '确认修改',
        cancelButtonText: '取消',
      }).then(({
        value
      }) => {
        this.set_up_price(item.ctid, value)
      }).catch(() => {

      });
    },
    open2(item) {
      this.$confirm('是否确认重置修改系列内最高价格', '重置系列内最高价格', {
        confirmButtonText: '确认重置',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reset_price(item.ctid)
      }).catch(() => {

      });
    },
    //重置系列内作品上架最高价格
    async reset_price(ctid) {
      console.log(ctid)
      const res = await this.$api.updateOnSaleMaxPrice({
        onSaleMaxPrice: null,
        ctid
      })
      if (res.status.code == 0) {
        this.$message.success('重置成功')
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 批量选择
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 批量上下架
    batchUp() {
      this.dialogUp = true
      this.title = '批量上下架'
    },
    // 批量设置推荐级别
    batchRank() {
      this.dialogUp = true
      this.title = '批量设置推荐级别'
    },
    async click_submit() {
      const ctIdsArr = []
      let method = ''
      this.multipleSelection.forEach((item) => {
        this.idListStr.push(item.id)
        ctIdsArr.push(item.ctid)
      })
      const obj = {
        idListStr: this.idListStr.join(',')
      }
      if (this.title === '批量上下架') {
        method = 'batchUpdateSeriesVisibility'
        obj.visibility = this.form.value
      } else if (this.title === '批量设置推荐级别') {
        obj.value = this.form.value
        method = 'batchUpdateSeriesRecommend'
      } else if (this.title === '批量上下架系列内的创作品') {
        method = 'batchUpdateItemVisibility'
        obj.visibility = this.form.value
        obj.ctidsStr = JSON.stringify(ctIdsArr)
        delete obj.idListStr
      }
      await this.$api[method](obj)
      this.dialogUp = false
      this.$message.success('设置成功')
    },
    batchItemVisibility() {
      this.dialogUp = true
      this.title = '批量上下架系列内的创作品'
    },
    set_up_flag(item) {
      console.log(item)
      this.ctid = item.ctid
      this.isSaleSign = true
    },
    open_tag(item) {
      console.log(item)
      this.tagForm.tagValue = item.tag
      this.tagForm.newFlag = item.newFlag
      this.ctid = item.id
      this.isTagSign = true
    },
    async set_up_submit() {
      const res = await this.$api.batchUpdateItemNotSaleSign({
        ctid: this.ctid,
        notSaleSign: this.saleSign
      })
      if (res.status.code == 0) {
        this.$message.success('修改成功')
      } else {
        this.$message.error(res.status.msg)
      }
    },
    async tag_submit() {
      const res = await this.$api.updateCollectionTag({
        id: this.ctid,
        tag: this.tagForm.tagValue,
        newFlag: this.tagForm.newFlag,
      })
      if (res.status.code == 0) {
        this.$message.success('修改成功')
        this.isTagSign = false
        this.getList()
      } else {
        this.$message.error(res.status.msg)
      }
    },
    async open_destroy(item) {
      this.$confirm('是否确认销毁系列内未产生交易的创作品', '系列内作品销毁', {
        confirmButtonText: '确认销毁创作品',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.destroy(item.ctid)
      }).catch(() => {

      });
    },
    async destroy(ctid) {
      const res = await this.$api.destroyByCtid({
        ctid
      })
      if (res.status.code == 0) {
        this.$message.success('销毁成功')
      } else {
        this.$message.error(res.status.msg)
      }
    }
  }
}
</script>

<style></style>
