<template>
  <d2-container class="page">
    <div class="content">
      <div class="addLeft">
        <el-form :model="form" label-position="right" label-width="230px">
          <el-form-item label="广告位置:" required>
            市场页banner
          </el-form-item>
          <el-form-item label="广告名称:" required>
            <el-input v-model="form.name" placeholder="请输入20字以内文字" clearable style="width: 500px" maxlength="20"
              show-word-limit :disabled="look"></el-input>
            <div style="color: #999d9c; fontsize: 12px">
              广告名称只是作为辨别多个广告条目之用，并不显示在广告中
            </div>
          </el-form-item>
          <el-form-item label="链接类型:" required>
            <el-radio-group v-model="form.linkType" @change="radioChange" :disabled="look">
              <el-radio :label="0">无连接</el-radio>
              <el-radio v-if="businessLine !== 10" :label="10">站内链接</el-radio>
              <el-radio :label="20">网页链接</el-radio>
              <el-radio v-if="businessLine !== 10" :label="30">自定义详情</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跳转路径h5以及网页跳转:" label-width="220px" v-if="form.linkType === 10 || form.linkType === 20"
            required>
            <el-input :disabled="look" v-model="form.link" placeholder="请输入跳转路径h5以及网页跳转" clearable
              style="width: 500px"></el-input>
            <el-upload v-if="form.linkType === 20 && !look" :action="action" :headers="token" :show-file-list="false"
              :on-success="posterUploadSuccess" :before-upload="beforeAvatarUpload">
              <el-button type="primary">生成海报页面</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="IOS 安卓原生跳转路径：" v-if="form.linkType == 10">
            <el-input :disabled="look" v-model="form.nativeLink" placeholder="原生路径不存在无需填写，系统自动使用'H5以及网页跳转路径'" clearable
              style="width: 500px"></el-input>
          </el-form-item>
          <el-form-item label="自定义跳转页面标题:" v-if="form.linkType == 30" required>
            <el-input :disabled="look" v-model="form.customDetailTitle" placeholder="请输入自定义跳转页面标题" clearable
              style="width: 500px"></el-input>
          </el-form-item>
          <el-form-item label="自定义跳转页面详情内容:" v-if="form.linkType == 30" required>
            <editor-bar :catchData="catchData" :content="editorContent"></editor-bar>
          </el-form-item>
          <el-form-item v-if="businessLine !== 10" label="是否需要登录:" required>
            <el-radio-group v-model="form.needLogin" :disabled="look">
              <el-radio :label="0">不需要</el-radio>
              <el-radio :label="1">需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="类型:" required>
            <el-radio-group v-model="form.tapType" :disabled="look">
              <el-radio :label="0">藏品</el-radio>
              <el-radio :label="1">衍生</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上线计划:" required>
            <el-radio-group v-model="form.onlineType" :disabled="look">
              <el-radio :label="1">立即上线</el-radio>
              <el-radio :label="2">定时上线</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="" v-if="form.onlineType == 2">
            <el-date-picker :disabled="look" v-model="form.onlineBeginTime" type="datetime" placeholder="开始时间"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="" v-if="form.onlineType == 2">
            <el-date-picker :disabled="look" v-model="form.onlineEndTime" type="datetime" placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="广告图片:" required>
            <el-upload :disabled="look" :action="action" :headers="token" list-type="picture-card"
              :on-success="handlePicSuccess" :class="{ hide: hideUpload_introduce }"
              :on-change="handleIntroduceUploadHide" :on-remove="handleIntroduceRemove" :file-list="fileListImg"
              :before-upload="beforeAvatarUpload">
              <i class="el-icon-plus"></i>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="" />
            </el-dialog>
          </el-form-item>
          <el-form-item label="排序:" required>
            <el-input :disabled="look" v-model="form.weight" placeholder="请输入排序" clearable style="width: 500px"
              type="number"></el-input>
          </el-form-item>
          <el-form-item label="广告备注:">
            <el-input v-model="form.remark" placeholder="请输入广告备注" clearable style="width: 500px" type="textarea"
              :autosize="{ minRows: 4, maxRows: 8 }" :disabled="look"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="debounceMethods(return_click)" v-if="!look">取消返回
            </el-button>
            <el-button type="primary" @click="debounceMethods(submit_click)" v-if="!look">提交
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="addRight" v-if="form.linkType == 30">
        <div v-html="richTxt"></div>
        <!-- {{ richTxt }} -->
      </div>
    </div>
  </d2-container>
</template>

<script>
  import EditorBar from '@/components/editoritem/editoritem'

  export default {
    name: 'bannerConfig',
    components: {
      EditorBar
    },
    data() {
      return {
        hideUpload_introduce: false,
        limitCount: 1,
        dialogImageUrl: '',
        dialogVisible: false,
        form: {
          name: '',
          linkType: 0,
          link: '',
          nativeLink: '',
          customDetailTitle: '',
          customDetail: '',
          onlineType: 2,
          onlineBeginTime: '',
          onlineEndTime: '',
          image: '',
          weight: '',
          remark: '',
          needLogin: 0,
          tapType:0
        },
        action: process.env.VUE_APP_BASE_URL + 'osscenter/adminApi/missWebSign/uploadImage',
        token: {
          AdminAuthorization: localStorage.getItem('usertoken')
        }, // 设置上传的请求头部
        fileList: [],
        formLabelWidth: '180px',
        richTxt: '',
        bannerId: '',
        detail: '',
        fileListImg: [],
        look: false,
        businessLine: null
      }
    },
    mounted() {
      this.bannerId = this.$route.query.edit_id
      this.look = this.$route.query.look
      this.businessLine = Number.parseInt(this.$route.query.businessLine) || ''
      if (this.$route.query.edit_id) {
        this.detailMsgJob()
      }
    },
    methods: {
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        this.richTxt = e
        this.form.customDetail = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${file.name}？`)
      },
      /**
       * 海报图片上传
       */
      posterUploadSuccess(res) {
        if (res.status.code !== 0) {
          this.$message.error(res.status.msg)
        } else {
          this.$message.success('上传成功,页面地址已自动填入')
          const mainOrigin = process.env.VUE_APP_MAIN_ORIGIN
          const {
            origin,
            pathname
          } = new URL(res.result.url)
          let src = ''
          switch (origin) {
            case 'https://nftcns.oss-cn-shanghai.aliyuncs.com':
              src = `https://cdn-lingjing.nftcn.com.cn${pathname}`
              break
            case 'https://nftcn.oss-cn-shanghai.aliyuncs.com':
              src = `https://oss.nftcn.com.cn${pathname}`
              break
            default:
              src = res.result.url
          }
          this.form.link = `${mainOrigin}/active/#/poster?src=${encodeURIComponent(src)}`
        }
      },
      // 图片上传
      handlePicSuccess(res, file) {
        if (res.status.code !== 0) {
          this.$message.error(res.status.msg)
        } else {
          this.form.image = res.result.mediumImageUrl
        }
      },
      handleIntroduceUploadHide(file, fileList) {
        this.hideUpload_introduce = fileList.length >= this.limitCount
      },
      beforeAvatarUpload(file) {
        if (
          file.type !== 'image/jpeg' &&
          file.type !== 'image/png' &&
          file.type !== 'image/gif'
        ) {
          this.$message.error('只能上传jpg/png/GIF格式文件')
          return false
        }
      },
      // 图片移除
      handleIntroduceRemove(file, fileList) {
        this.hideUpload_introduce = fileList.length >= this.limitCount
        this.form.image = ''
      },
      radioChange() {
        this.form.link = ''
        this.form.nativeLink = ''
        this.form.customDetailTitle = ''
        this.form.customDetail = ''
      },
      // 编辑详情
      async detailMsgJob() {
        const res = await this.$api.detailBanner({
          bannerId: this.bannerId
        })
        if (res.status.code === 0) {
          this.form = res.result
          this.richTxt = this.form.customDetail
          this.detail = this.form.customDetail

          if (res.result.image !== '') {
            this.hideUpload_introduce = true
            this.fileListImg = [{
              name: '',
              url: res.result.image
            }]
          }
        } else {
          this.$message.error(res.status.msg)
        }
      },
      // 取消返回
      return_click() {
        this.$router.back()
      },
      // 新增编辑确定
      submit_click() {
        this.$confirm(
            '提交保存后,该banner将显示在客户端,确认保存?',
            '确认提交保存', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
          .then(() => {
            this.submitClick()
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      },
      async submitClick() {
        const routerConfig = {
          name: this.businessLine === 10 ? 'DigitalCollectionSettingBanner' : 'bannerConfig',
          query: {
            businessLine: this.businessLine
          }
        }
        if (!this.$route.query.edit_id) {
         let res =  await this.$api.addBanner({
            type: '0',
            name: this.form.name,
            linkType: this.form.linkType,
            link: this.form.link,
            nativeLink: this.form.nativeLink,
            customDetailTitle: this.form.customDetailTitle,
            customDetail: this.form.customDetail,
            onlineType: this.form.onlineType,
            needLogin: this.form.needLogin,
            onlineBeginTime: this.form.onlineBeginTime,
            onlineEndTime: this.form.onlineEndTime,
            image: this.form.image,
            weight: this.form.weight,
            remark: this.form.remark,
            businessLine: this.businessLine,
            tapType:this.form.tapType
          })
          console.log(this.businessLine)
          console.log(this.businessLine === 10)
          if(res.status.code == 0){
            this.$message.success('新增成功')
          }
          await this.$router.push(routerConfig)
        } else {
          let res = await this.$api.updateBanner({
            type: '0',
            bannerId: this.bannerId,
            name: this.form.name,
            linkType: this.form.linkType,
            link: this.form.link,
            nativeLink: this.form.nativeLink,
            customDetailTitle: this.form.customDetailTitle,
            customDetail: this.form.customDetail,
            onlineType: this.form.onlineType,
            needLogin: this.form.needLogin,
            onlineBeginTime: this.form.onlineBeginTime,
            onlineEndTime: this.form.onlineEndTime,
            image: this.form.image,
            weight: this.form.weight,
            remark: this.form.remark,
            tapType:this.form.tapType
          })
          if(res.status.code == 0){
            this.$message.success('修改成功')
          }
          this.$router.push(routerConfig)
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .content {
    display: flex;

    .addRight {
      margin-left: 30px;
      width: 250px;
      min-height: 500px;
      margin-top: 100px;
      padding: 80px 40px;
      background: url('../../../assets/img/ios.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  ::v-deep .hide .el-upload--picture-card {
    display: none;
  }
</style>
