<template>
  <d2-container class="page">
    <el-form :inline="true" :model="formInline" class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px">
      <el-form-item label="广告名称">
        <el-input v-model="formInline.name" placeholder="请输入广告名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="链接类型:">
        <el-select v-model="formInline.linkType" placeholder="请选择链接类型" clearable>
          <el-option v-for="item in linkTypeList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型:">
        <el-select v-model="formInline.tapType" placeholder="请选择类型" clearable>
            <el-option label="藏品" :value="0"></el-option>
            <el-option label="衍生" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker v-model="formInline.createAt" type="datetimerange" :picker-options="pickerOptions"
          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="clear()">清除</el-button>
        <el-button type="primary" @click="add_banner()">添加广告</el-button>
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="全部" name="11"></el-tab-pane>
      <el-tab-pane label="待上线" name="0"></el-tab-pane>
      <el-tab-pane label="上线中" name="1"></el-tab-pane>
      <el-tab-pane label="已下线" name="2"></el-tab-pane>
    </el-tabs>
    <el-table :data="tableData" ref="multipleTable" tooltip-effect="dark" show-header border style="width: 100%">
      <el-table-column fixed prop="id" label="id" align="center" width="80"></el-table-column>
      <el-table-column prop="name" label="广告名称" align="center"></el-table-column>
      <el-table-column prop="linkType" label="跳转链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '0'">无链接</el-tag>
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
          <el-tag v-if="scope.row.linkType == '30'">自定义跳转详情</el-tag>
        </template></el-table-column>
      <el-table-column prop="link" label="跳转路径h5以及网页跳转" align="center"></el-table-column>
      <el-table-column v-if="businessLine !== 10" prop="nativeLink" label="安卓原生跳转路径" align="center"></el-table-column>
      <el-table-column prop="tapType" label="类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.tapType == '0'">藏品</el-tag>
          <el-tag v-if="scope.row.tapType == '1'">衍生</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="image" label="广告图片" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image style="width: 100px; height: 100px" :src="scope.row.image">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createAt" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="onlineBeginTime" label="上线时间" align="center" width="220">
        <template scope="scope">
          <div>开始时间:{{ scope.row.onlineBeginTime }}</div>
          <div>到期时间:{{ scope.row.onlineEndTime }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="weight" label="权重" align="center"></el-table-column>
      <el-table-column prop="bannerStatus" label="状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.bannerStatus == '0'">待上线</el-tag>
          <el-tag v-if="scope.row.bannerStatus == '1'">在线中</el-tag>
          <el-tag v-if="scope.row.bannerStatus == '2'">已下线</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="look_click(scope.row)">查看</el-button>
          <el-button type="text" @click="audit_click(scope.row)" v-if="scope.row.bannerStatus !== 2">编辑</el-button>
          <el-button type="text" @click="online_click(scope.row)" v-if="scope.row.bannerStatus == '0'">上线</el-button>
          <el-button type="text" @click="down_click(scope.row)" v-if="scope.row.bannerStatus == '1'">下线</el-button>
          <el-button type="text" @click="del_click(scope.row)" style="color: red">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="prev, pager, next" :total="total" :page-size="15"
        style="padding: 20px; background-color: #ffffff" @current-change="xuanze" @size-change="xuanze">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  export default {
    name: 'DigitalCollectionSettingBanner',
    data() {
      return {
        activeName: '11',
        formInline: {
          name: '',
          linkType: '',
          createAt: null,
          createAtBegin: null,
          createAtEnd: null,
          type: '0',
          bannerStatus: '',
          tapType:''
        },
        pickerOptions: {
          shortcuts: [{
              text: '一天内',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
                picker.$emit('pick', [start, end])
              }
            },
            {
              text: '最近三天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
                picker.$emit('pick', [start, end])
              }
            },
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              }
            }
          ]
        },
        total: 1,
        linkTypeList: [],
        tableData: [],
        businessLine: null
      }
    },
    mounted() {
      this.businessLine = Number.parseInt(this.$route.query.businessLine) || ''
      this.getLinkTypeList()
      this.getList()
    },
    methods: {
      handleClick() {
        if (this.activeName === '11') {
          this.formInline.bannerStatus = ''
        } else {
          this.formInline.bannerStatus = this.activeName
        }
        this.getList()
      },
      // 获取链接类型
      async getLinkTypeList() {
        const res = await this.$api.getDictDataByDictType({
          dictType: 'LINK_TYPE'
        })
        this.linkTypeList = res.result.dictDataListVOS
      },
      clear() {
        this.formInline.name = ''
        this.formInline.linkType = ''
        this.formInline.createAt = undefined
      },
      // 查询列表
      async getList(page) {
        if (this.formInline.createAt) {
          this.formInline.createAtBegin = this.formInline.createAt[0]
          this.formInline.createAtEnd = this.formInline.createAt[1]
        } else {
          this.formInline.createAtBegin = null
          this.formInline.createAtEnd = null
        }
        const params = {
          pageNum: page,
          pageSize: 15,
          name: this.formInline.name,
          linkType: this.formInline.linkType,
          createAtBegin: this.formInline.createAtBegin,
          createAtEnd: this.formInline.createAtEnd,
          type: this.formInline.type,
          bannerStatus: this.formInline.bannerStatus,
          tapType:this.formInline.tapType
        }
        console.log(this.businessLine)
        if (this.businessLine) {
          params.businessLine = this.businessLine
        }
        const res = await this.$api.getBannerList(params)
        this.tableData = res.result.list
        this.total = res.result.totalCount
      },
      xuanze(val) {
        this.getList(val)
      },
      add_banner() {
        this.$router.push({
          name: 'addBanner',
          query: {
            businessLine: this.businessLine
          }
        })
      },
      // 点击修改
      async audit_click(val) {
        this.$router.push({
          name: 'addBanner',
          query: {
            edit_id: val.id,
            businessLine: this.businessLine
          }
        })
      },
      // 点击查看
      async look_click(val) {
        this.$router.push({
          name: 'addBanner',
          query: {
            edit_id: val.id,
            look: true,
            businessLine: this.businessLine
          }
        })
      },
      // 上线
      online_click(val) {
        this.$confirm(
            '您已设置自动上线时间,点击确定可将本广告立刻上线至banner位。',
            '确认上线', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
          .then(() => {
            this.onlineBanner({
              bannerId: val.id,
              onlineStatus: 1
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      },
      // 下线
      down_click(val) {
        this.$confirm(
            'bannner下线后,用户将无法查看本广告,且无法再次上线',
            '确认下线', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
          .then(() => {
            this.onlineBanner({
              bannerId: val.id,
              onlineStatus: 2
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      },
      async onlineBanner(val) {
        const res = await this.$api.onlineBanner({
          bannerId: val.bannerId,
          onlineStatus: val.onlineStatus
        })
        this.getList(1)
        this.$message({
          type: 'success',
          message: res.status.msg
        })
      },
      // 删除
      del_click(val) {
        this.$confirm(
            'bannner删除后,客户端将自动下架本广告,同时无法恢复历史记录。',
            '确认删除', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
          .then(() => {
            this.remove(val.id)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      },
      async remove(code) {
        await this.$api.deleteBanner({
          bannerId: code
        })
        this.getList(1)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }
    }
  }
</script>

<style>
</style>
