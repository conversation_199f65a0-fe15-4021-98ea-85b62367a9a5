<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-data="tableData" :table-schema="tableSchema">
      <template #action="scope">
        <el-button @click="toggleStatus(scope.row.shortCode, scope.row.isDeleted)" type="text">
          {{ scope.row.isDeleted ? '启用' : '禁用' }}
        </el-button>
      </template>
      <template #action-header>
        <el-button @click="addProject()" type="primary" size="mini">新增</el-button>
      </template>
    </common-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
export default {
  name: 'ShortLink',
  components: {
    CommonTable,
    CommonQuery
  },
  data () {
    return {
      tableData: [], // 表格数据
      query: {},
      querySchema: [
        {
          type: 'input',
          label: 'code 码',
          placeholder: '请输入 code 码',
          field: 'shortCode'
        },
        {
          type: 'input',
          label: '长链接',
          placeholder: '请输入长链接',
          field: 'longLink'
        },
        {
          type: 'select',
          label: '是否禁用',
          field: 'isDeleted',
          placeholder: '请选择状态',
          options: [
            { label: '禁用', value: 1 },
            { label: '未禁用', value: 0 }
          ]
        }
      ],
      tableSchema: [ // 表格架构
        {
          label: 'code 码',
          field: 'shortCode'
        },
        {
          label: '短链接',
          field: 'shortLink'
        },
        {
          label: '长链接',
          field: 'longLink'
        },
        {
          label: '状态',
          field: 'isDeleted',
          type: 'tag',
          tagMap: {
            0: { tagType: 'success', label: '未禁用' },
            1: { tagType: 'danger', label: '禁用' }
          }
        },
        {
          label: '创建时间',
          field: 'createAt'
        },
        {
          label: '更新时间',
          field: 'updateAt'
        },
        {
          label: '创建的管理员用户名',
          field: 'createAdminUser'
        },
        {
          label: '更新的管理员用户名',
          field: 'updateAdminUser'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          fixed: 'right'
        }
      ],
      dialogVisible: false,
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      }
    }
  },
  computed: {
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 过滤查询
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.shortLinkList(params)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 切换启用、禁用
    toggleStatus (shortCode, isDeleted) {
      const title = isDeleted ? '启用' : '禁用'
      this.$confirm(`是否${title}`, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.shortLinkDisabled({ shortCode, isDeleted: isDeleted ? 0 : 1 })
        if (status.code === 0) {
          this.getList()
        }
      })
    },
    // 添加项目
    addProject () {
      this.$prompt('请输入长链接', '添加短链接', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async ({ value }) => {
        const { status } = await this.$api.shortLinkAdd({ longLink: value })
        if (status.code === 0) {
          this.getList()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #409EFF;
}
</style>
