<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action-header>
        <el-button type="primary" size="mini" @click="nav_task()">创建任务</el-button>
        <!-- <el-button type="primary" size="mini" @click="nav_task()">下载模板</el-button> -->
      </template>
      <template #action="scope">
        <el-button @click="exportResult(scope.row,'')" type="text">导出结果</el-button>
        <el-button @click="exportResult(scope.row,'FAIL')" type="text">导出失败结果</el-button>
        <el-button @click="delItem(scope.row)" v-if="scope.row.status == 'INIT'" type="text">删除</el-button>
      </template>
    </common-table>
    <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
        @size-change="currentChangeSize">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'
  // import { mapActions } from 'vuex'
  import {
    noticePublish
  } from '@/api/hongyan'
  import {
    creationCslist,
    tradeStop
  } from '@/api/hanxin'
  export default {
    name: 'stairAirDrop',
    components: {
      CommonQuery,
      CommonTable
    },
    data() {
      return {
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        }, // 分页数据
        query: {},
        tableSchema: [ // 表格架构
          {
            label: 'airDropNo',
            field: 'airDropNo',
          },
          {
            label: '任务名称',
            field: 'airDropName',
          },
          {
            label: '系列名',
            field: 'csName',
          },
          {
            label: '系列id',
            field: 'ctid',
          },
          {
            label: '创建时间',
            field: 'createAt',
          },
          {
            label: '创建人',
            field: 'createAdminUser',
          },
          {
            label: '开始时间',
            field: 'startTime',
          },
          {
            label: '结束时间',
            field: 'endTime',
          },
          {
            label: '任务状态',
            field: 'status',
            type: 'tag',
            tagMap: {
              INIT: '准备中',
              DONE: '执行中',
              ALL_DONE: '已完成',
              PART_DONE: '部分空投完成',
              DISCARD:"已删除"
            },
          },
          {
            label: '空投类型',
            field: 'airDropType',
            type: 'tag',
            tagMap: {
              VOLUNTEER: '志愿者',
              ANCHOR: '主播',
              REMEDIATION_CARD: '补救卡',
              LOTTERY: '抽奖',
              PRIMARY: '主力',
              COMMUNITY_SPECIAL_SHORTS: '社区特殊空头',
            },
          },
          {
            label: '备注',
            field: 'remark',
          },
          {
            label: '操作',
            slot: 'action',
            headerSlot: 'action-header',
            width: '180px',
            fixed: 'right'
          }
        ],
        tableData: [],
        querySchema: [ // 搜索组件架构
          {
            type: 'input',
            label: '系列名称：',
            field: 'csName',
          },
          {
            type: 'select',
            label: '任务状态：',
            field: 'status',
            options: [{
                label: '准备中',
                value: 'INIT'
              },
              {
                label: '执行中',
                value: 'DONE'
              },
              {
                label: '已完成',
                value: 'ALL_DONE'
              },
              {
                label: '部分空投完成',
                value: 'PART_DONE'
              }
            ]
          },
        ],
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      toTemplatePage(item = {}) {
        // this.$store.commit('SAVE_INFO', item)
        // mapActions('save_stateInfo', item)
        if (item.businessType && item.businessType != 0) {
          this.$router.push({
            name: 'NoticeEdit',
            query: {
              templateId: item.id,
              businessType: item.businessType
            }
          })
        } else {
          localStorage.setItem('noticeInfo', JSON.stringify(item))
          this.$router.push({
            name: 'platformPublish',
            query: {
              templateId: item.id
            }
          })
        }

      },
      nav_task() {
        this.$router.push({
          name: 'stairAirDrop_add',
        })
      },
      scrollEvent(e) {
        console.log(e.y) // 获取目标元素的滚动高度
      },
      // 过滤查询
      onQueryChange(data) {
        this.query = data
        this.getList(true)
      },
      // 分页改变
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页改变
      currentChangeSize(value) {
        this.page.pageSize = value
        this.getList()
      },
      getList() {
        const params = {
          ...this.query,
          ...this.page,
          pageNum: this.page.pageNum,
          itemType: 'CREATION_CS'
        }
        creationCslist(params).then(res => {
           this.tableData = res.result.list
          this.page.totalCount = res.result.totalCount
          this.page.pageSize = res.result.pageSize
          this.page.pageCount = res.result.pageCount
        })
      },
      // 发表/下架
      setSellStop(id) {
        this.$confirm(`确定终止该任务吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          tradeStop({
            dutyId: id,
            dutyType: 'AUTO_TRADE'
          }).then((res) => {
            if (res.status.code == 0) {
              this.$message.success(res.status.msg)
              this.getList()
            }
          })
        })
      },
      openText(text) {
        console.log(text)
        this.$alert(text, '备注详情', {
          confirmButtonText: '确定',
          callback: action => {

          }
        });
      },
      // 导出结果
      async exportResult(item,type) {
        const res = await this.$api.recordExport({
          airDropId: item.airDropId,
          status:type
        })
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '空投结果')
          this.$message.success('导出成功')
        }
      },
      async delItem(item) {
        const res = await this.$api.creationCsDiscard({
          airDropId: item.airDropId
        })
        if(res.status.code == 0){
            this.$message.success('删除成功')
            this.getList()
        }

      },

    }
  }
</script>

<style lang="scss" scoped>
  .oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
  }
</style>
