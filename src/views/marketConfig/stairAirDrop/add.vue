<template>
  <d2-container class="page">
    <common-form :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #csName="scope">
        <el-input placeholder="请输入系列名称/系列ID" style="width:300px" @keyup.enter.native="search" v-model="formData.csName"></el-input>
        <el-button type="primary" style="margin-left: 10px;" @click="search">搜索</el-button>
        <div class="search">
          <span>搜索结果</span>
          <div v-if="searchList!=null">
            <el-tag type="" style="margin-right: 10px;cursor: pointer;margin-bottom:10px;" v-for="(item,index) in searchList"
              @click="clickName(item)">{{item.name}}({{item.ctid}})</el-tag>
          </div>
          <div v-else style="color:rgb(255, 29, 29)">
            没有搜索结果，请重新输入关键字后重试
          </div>
        </div>
      </template>
      <template #reminder="scope">
        <div style="color:rgb(255, 29, 29)" v-if="formData.ctid!=''">
          当前系列剩余一级作品数量约为：{{goodsNum}} （下架的作品数量）
        </div>
      </template>
      <template #update="scope">
        <el-row justify="start">
          <el-col :span="3" >
            <div class="grid-content bg-purple">
              <file-uploader :value.sync="formData.userImportUrl" :disabled="!formData.ctid" :condition="condition"  @success="success"></file-uploader>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="grid-content bg-purple">
              <el-button type="primary" width="100%"  @click="downLoad">下载模板</el-button>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #msg_view="scope">
        <div style="color:rgb(255, 29, 29)" >
            导入用户可重复，空投作品数量会叠加，请先选择好空投系列后再上传空投明细
        </div>
      </template>
    </common-form>
  </d2-container>
</template>

<script>
  import Editor from '@/components/editoritem/editoritem'
  import html2canvas from 'html2canvas';
  import CommonForm from '@/components/CommonForm'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    tradeAdd
  } from '@/api/hanxin'
  import {
    downloadBlob
  } from '@/utils/helper'
  export default {
    name: 'stairAirDrop_add',
    components: {
      CommonForm,
      Editor,
      FileUploader
    },
    data() {
      return {
        isDetail: false, // 详情
        templateId: null, // 活动编号
        formData: {
          startTime: '',
          isTiming:0,
          airDropName:'',
          ctid:'',
          csName:'',
          userImportUrl:'',
          airDropType:'VOLUNTEER',
          isTiming:1
        },
        formSchema: [{
            type: 'input',
            label: '任务名称：',
            placeholder: '请输入任务名称',
            field: 'airDropName',
            rules: [{
              required: true,
              message: '请输入任务名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '空投的系列：',
            placeholder: '请输入空投的系列',
            field: 'csName',
            slot: 'csName',
            rules: [{
              required: true,
              message: '请输入空投的系列',
              trigger: 'blur'
            }]
          },
          {
            slot: 'reminder',
          },
          {
          	type: 'radio',
          	label: '空投类型：',
          	field: 'airDropType',
          	options: [{
          			label: '志愿者',
          			value: 'VOLUNTEER'
          		},
          		{
          			label: '主播',
          			value: 'ANCHOR'
          		},
              {
              	label: '补救卡',
              	value: 'REMEDIATION_CARD'
              },
              {
              	label: '抽奖',
              	value: 'LOTTERY'
              },
              {
              	label: '主力',
              	value: 'PRIMARY'
              },
              {
              	label: '社区特殊空头',
              	value: 'COMMUNITY_SPECIAL_SHORTS'
              },
          	],
          	rules: [{
          		required: true,
          	}],
          },
          {
            type: 'input',
            label: '上传空投明细：',
            placeholder: '请输入上传空投明细',
            slot: 'update',
            rules: [{
              required: true,
              message: '请输入上传空投明细',
              trigger: 'blur'
            }],
          },
          {
            slot: 'msg_view',
          },
          {
          	type: 'radio',
          	label: '是否定时：',
          	field: 'isTiming',
          	options: [{
          			label: '定时执行',
          			value: 1
          		},
          		{
          			label: '立即执行',
          			value: 2
          		},
          	],
          	rules: [{
          		required: true,
          	}],
          },
          {
            type: 'datetimerange',
            label: '时间范围：',
            placeholder: '请选择时间范围',
            field: 'startTime',
            rules: [{
              required: true,
              message: '请选择时间范围',
              trigger: 'blur'
            }],
            show: {
            	relationField: 'isTiming',
            	value: [1]
            },
          },
          {
              type: 'input',
              label: '备注：',
              placeholder: '备注不少于10个字(必填)',
              field: 'remark',
              rules: [{
                required: true,
                message: '备注不少于10个字(必填)',
                trigger: 'blur'
              }]
            },
          {
            type: 'action'
          },

        ],
        searchList: [],
        isError: '',
        goodsNum:0,
        condition:true
      }
    },
    mounted() {
      const {
        templateId
      } = this.$route.query
      this.templateId = templateId
      templateId && this.getDetail()
      if (this.templateId) {
        this.formData.publishTime = this.formData.publishTime
      }
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 监听富文本的输入
      catchData(e) {
        console.log('1e=====?>', e)
        // this.richTxt = e
        this.formData.content = e
      },
      // 富文本中的内容
      editorContent(e) {
        console.log('2e=====?>', e)
        return '<p>123</p>'
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const data = JSON.parse(localStorage.getItem('noticeInfo'))
        data.isPush = 0
        console.log(data)
        this.formData = {
          ...data
        }
      },
      async submit() {
          let startTime, endTime
          if(this.formData.isTiming==1){
            startTime = this.formData.startTime[0]
            endTime = this.formData.startTime[1]
          }
          const data = {
            userImportUrl:this.formData.userImportUrl,
            ctid:this.formData.ctid,
            startTime,
            endTime,
            isTiming:this.formData.isTiming,
            airDropName:this.formData.airDropName,
            itemType:'CREATION_CS',
            airDropType:this.formData.airDropType,
            remark:this.formData.remark
          }
          console.log(data)
        // }
        console.log(this.formData.remark)
        if(this.formData.remark.length<10){
          this.$message.error('备注不得少于10个字')
           return false
        }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          let res = await this.$api.creationCsCreate(data);
          if(res.status.code==0){
            this.routerBack()
          }
        })
      },
      async search() {
        let res = await this.$api.searchPgc({
          name: this.formData.csName
        });
        if (res.status.code == 0) {
          this.searchList = res.result.list
        }
      },
      clickName(item) {
        this.formData.csName = item.name
        this.formData.ctid = item.ctid
        this.changeNum()
      },
      async downLoad() {
        const res = await this.$api.userCenterDownLoadTemplate({
          templateTag: 'AIR_DROP_CREATION_USER_IMPORT'
        })
        if (res.status.code == 0) {
          window.open(res.result.emailsTemplateUrl)
        } else {
          this.$message.error(res.status.msg)
        }
      },
      async changeNum() {
        let res = await this.$api.restGoodsNum({
          ctid: this.formData.ctid,
        });
        if (res.status.code == 0) {
            this.goodsNum=res.result.goodsNum
        } else {
           this.$message.error(res.status.msg)
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      Remove(){

      },
      success(){
        if(this.formData.ctid==''){
           this.$message.error('请先选择你要空投的系列')
        }else{
           this.userValidate()
        }
        // console.log("上传成功")
      },
      async userValidate() {
        let res = await this.$api.userValidate({
          ctid: this.formData.ctid,
          importUrl:this.formData.userImportUrl,
          airDropType:this.formData.airDropType
        });
        if (res.status.code == 0) {
          this.$alert(`本次空投之后，一级剩余作品数为：${res.result.goodsNum}`, '提示', {
            confirmButtonText: '确定',
            callback: action => {
            }
          });
            // this.goodsNum=res.result.goodsNum
        }
      },
    }
  }
</script>

<style lang="scss">
  .page {
    padding-top: 80px;
  }

  .w-e-toolbar {
    z-index: 2 !important;
  }

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .item {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .box-card {
    width: 560px;
  }

  .flex {
    display: flex;
    justify-content: flex-start;

    .shuru {
      width: 500px;
      height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      padding: 40px 0px;
    }

    .yulan {
      width: 100px;
      height: 812px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 40px 0px;
    }

    .preview {
      min-height: 812px;
      width: 414px;
      border: 1px solid #ccc;
      background-image: url('https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20230320/4933c07760529e700a4d7b085a9583a5_1520x2006.jpg');
      background-repeat: no-repeat;
      background-size: 100%;
      background-color: #dbdbdb;
      padding: 140px 30px 100px 30px;

      .title {
        font-size: 28px;
        font-weight: 600;
        line-height: 50px;
      }

      .body {
        margin-top: 40px;
        // min-height:300px;
        background-color: rgb(220, 220, 220, 0.6);
        border-radius: 4px;
        // box-shadow: 0px 0px 20px 6px rgb(255, 255, 255);
        border: 1px solid #bebebe;
        padding: 30px;
        font-size: 18px;


        .text {
          margin-bottom: 20px;
        }

        .footer {
          text-align: right;
          margin-top: 40px;
          font-size: 20px;

          >div {
            margin-bottom: 6px;
          }
        }
      }

      .code {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 40px 0px;

        img {
          width: 200px;
          height: auto;
        }
      }

      .body .text:first-child {
        font-weight: 600;
      }

    }
  }

  .el-textarea {
    textarea {
      height: 200px !important;
    }
  }

  .flex {
    display: flex;
  }

  .danwei {
    margin-left: 6px;
  }
</style>
