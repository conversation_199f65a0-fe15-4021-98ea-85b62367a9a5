<template>
  <d2-container class="page">
    <common-table :table-data="tableData" :table-schema="tableSchema">
      <template #action="scope">
        <el-button @click="openDialog(scope.row.id)" type="text">编辑</el-button>
        <el-button @click="toggleStatus(scope.row.id, scope.row.status)" type="text">
          {{ scope.row.status ? '隐藏' : '显示' }}
        </el-button>
        <el-button @click="remove(scope.row.id)" type="text">移出</el-button>
      </template>
      <template #timeRemove="scope" >
        <el-button type="primary" style="margin:10px 0;" size="mini" @click="scope.row.isTime=true" v-if="!scope.row.isTime">定时
        </el-button>
        <br>
         <el-date-picker
        <el-date-picker  v-model="scope.row.endTime" :picker-options="pickerOptions" type="datetime" v-if="scope.row.isTime"
          :disabled="false" value-format="yyyy-MM-dd HH:mm:ss.SSS" size="mini"  placeholder="选择时间" @change="update_endTime(scope.row.endTime,scope.row)">
        </el-date-picker>
        <el-button type="primary" style="margin:10px; 0px" size="mini" v-if="scope.row.isTime" @click="update_endTime('',scope.row)" >取消定时
        </el-button>
      </template>
      <template #action-header>
        <el-button @click="openDialog()" type="primary" size="mini">新增</el-button>
      </template>
      <template #worksData="s">
        <el-button @click="toGoodsGallery(s.row.id)" type="text" size="mini">查看</el-button>
      </template>
    </common-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>

    <el-dialog title="" :visible.sync="dialogVisible" center @close="closeDialog" destroy-on-close>
      <CommonForm :schema="formSchema" :data="formData" :submit="submit" label-width="150px">
       <!-- <template #contractAddress>
          <el-input v-model="formData.leftJoinId" size="mini" @change="getSeriesList(formData.leftJoinId)"></el-input>
        </template>
        <template #addWorks>
         <el-radio-group v-if="seriesList.length > 0" v-model="formData.ctid">
           <el-radio v-for="item in seriesList" :label="item.ctid" :key="item.id">{{ item.name }}</el-radio>
         </el-radio-group>
         <div v-else>暂无数据</div>
        </template> -->
      </CommonForm>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
import FileUploader from '@/components/FileUploader'
export default {
  name: 'IPListEntry',
  components: {
    FileUploader,
    CommonTable,
    CommonForm
  },
  data () {
    return {
      tableData: [], // 表格数据
      tableSchema: [ // 表格架构
        {
          label: '系列名称',
          field: 'name',
          width:'170'
        },
        {
          label: '系列Id',
          field: 'ctid',
          width:'290'
        },
        // {
        //   label: '作品数据',
        //   slot: 'worksData',
        //   width: 80
        // },
        // {
        //   label: '发行价格',
        //   field: 'releasePrice',
        //   width:'80'
        // },
        // {
        //   label: '发行时间',
        //   field: 'releaseTime'
        // },
        {
          type: 'img',
          label: '入口图片',
          field: 'verbPic',
          width:'130',
           align:'center'
        },
        {
          label: '定时移出',
          slot:'timeRemove',
          width:'260',
          align:'center'
        },
        // {
        //   label: '备注信息',
        //   field: 'remark'
        // },
				// {
				//   label: '限价',
				//   field: 'limitPrice'
				// },
        {
          label: '状态',
          type: 'tag',
          field: 'status',
          width: 100,
          tagMap: {
            1: { tagType: 'success', label: '显示' },
            0: { tagType: 'danger', label: '隐藏' }
          }
        },
        {
          label: '排序',
          field: 'weight',
          width: 80
        },
        {
          label: '上线状态',
          type: 'tag',
          field: 'isTiming',
          width: 90,
          tagMap: {
            1: { tagType: 'success', label: '定时上线' },
            0: { tagType: 'danger', label: '立即上线' }
          }
        },
        {
          label: '上线时间',
          field: 'startTime',
           width: 190
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          fixed: 'right',
          width: 150
        }
      ],
      formData: {
        goodsType: 'TID',
        goodsExcelUrl: '',
        verbPic: '',
        ctid: '',
		tabName:''
      }, // 表格数据
      formSchema: [
        {
          type: 'input',
          label: '作品所属楼层：',
          field: 'tabName',
          disabled: true,
          rules: [{ required: true, message: '请输入作品所属楼层', trigger: 'blur' }]
        },
        // {
        //   type: 'input',
        //   label: 'IP 名称：',
        //   field: 'name',
        //   rules: [{ required: true, message: '请输入IP名称', trigger: 'blur' }]
        // },
		// {
		//   type: 'input',
		//   label: '发行价格：',
		//   field: 'releasePrice',
		//   rules: [{ required: true, message: '请输入发行价格', trigger: 'blur' }]
		// },
		// {
		//   type: 'input',
		//   label: '限价：',
		//   field: 'limitPrice',
		//   disabled: true,
		//   // rules: [{ required: true, message: '请输入限价', trigger: 'blur' }]
		// },
		// {
		//   type: 'datetime',
		//   label: '发行时间：',
		//   field: 'releaseTime',
		//   rules: [{ required: true, message: '请输入发行时间', trigger: 'blur' }]
		// },
        // {
        //   type: 'input',
        //   label: 'contract_address：',
        //   placeholder: '请输入 contract_address',
        //   field: 'leftJoinId',
        //   slot: 'contractAddress',
        //   rules: [{ required: true, message: '请输入 contract_address', trigger: 'change' }]
        // },
        {
          type:'search',
          label: '搜索作品名称：',
          field: 'ctid',
          rules: [{ required: true, message: '请添加 IP 作品', trigger: 'change' }]
        },
        // {
        //   type: 'img',
        //   label: '入口图片：',
        //   placeholder: '请选择入口图片',
        //   field: 'verbPic',
        //   limit: 1
        // },
        {
          type: 'number-input',
          label: '权重排序：',
          placeholder: '请输入权重排序',
          field: 'weight',
          rules: [{ required: true, message: '请输入权重排序', trigger: 'blur' }]
        },
        // {
        //   type: 'textarea',
        //   field: 'remark',
        //   label: '广告备注：',
        //   maxlength: 300
        // },
		{
		  label: '上线状态：',
		  field: 'isTiming',
		  type: 'radio',
		  options: [{ label: '定时上线', value: 1 }, { label: '立即上线', value: 0 }]
		},
		{
		  type: 'datetime',
		  label: '上线时间：',
		  field: 'startTime',
		  rules: [{ required: false, message: '请输入开始时间', trigger: 'blur' }],
		},
        {
          type: 'action'
        }
      ],
      dialogVisible: false,
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      },
      seriesList: [],
      id: '',
      itemId: '',
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 86400000
        }
      },
    }
  },
  computed: {
  },
  mounted () {
    this.id = this.$route.query.marketTabId
    this.formData.tabName = this.$route.query.tabName
    this.getList()
  },
  methods: {
    async getList (isInit) {
      const params = {
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        marketTabId: this.id
      }
      const { status, result } = await this.$api.leftJoinIpList(params)
      if (status.code === 0) {
        result.list.forEach((item)=>{
          if(item.endTime){
              item.isTime=true
          }else{
             item.isTime=false
          }
        })
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    async getSeriesList (contractAddress) {
      if (!contractAddress) return
      const { result } = await this.$api.seriesList({ contractAddress, pageSize: 10000 })
      this.seriesList = result.list
      console.log(this.seriesList)
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 切换启用、禁用
    toggleStatus (id, status) {
      const title = status ? '显示' : '隐藏'
      this.$confirm(`是否${title}`, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$api.hideOrShowLeftJoin({ id, status: status ? 0 : 1 })
        if (res.status.code === 0) {
          this.getList()
        }
      })
    },
    openDialog (id) {
	  this.formData.tabName=this.$route.query.tabName
	  console.log(this.formData)
      id && this.getDetail(id)
      this.itemId = id
      this.dialogVisible = true
    },
    closeDialog () {
      console.log(1)
      this.dialogVisible = false
      this.itemId = ''
      this.formData = {
        goodsType: 'TID',
        goodsExcelUrl: '',
        verbPic: '',
        ctid: '',
		tabName:''
      }
    },
    async remove (id) {
      this.$confirm(
        '确认移出',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        await this.$api.delLeftJoin({ id })
        this.$message.success('移出成功')
        await this.getList()
      })
    },
    async getDetail (id) {
      const res = await this.$api.leftJoinDetail({
        tabLeftJoinId: id
      })
      if(res.result.ctid){
         res.result.ctid = `${res.result.name}(${res.result.ctid})`
      }
      this.formData = {
        goodsType: 'TID',
        goodsExcelUrl: '',
        verbPic: '',
        ctid: '',
        tabName: this.$route.query.tabName,
        ...res.result
      }
      // this.getSeriesList(this.formData.leftJoinId)
    },
    toGoodsGallery (id) {
      this.$router.push({
        name: 'GoodGallery',
        query: {
          ipListId: id
        }
      })
    },
    async downloadTemplate () {
      const { status, result } = await this.$api.downLoadTemplateExcel({ templateTag: 'MARKET_TAB_IMPORT_GOODS' })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    async submit () {
			console.log(this.formData)
      if(this.formData.ctid){
        this.formData.ctid=this.formData.ctid.split('(')[1].split(')')[0]
      }
      const data = {
        ...this.formData,
        ctid: this.formData.ctid,
        linkType: 0,
        marketTabType: 8,
        marketTabId: this.id
      }
      if (!this.itemId) {
        let res = await this.$api.insertLeftJoin(data)
        if(res.status.code==0){
           this.$message.success('新增成功')
        }
      } else {
        let res = await this.$api.updateLeftJoin(data)
        if(res.status.code==0){
           this.$message.success('修改成功')
        }
      }
      this.closeDialog()
      this.getList()
    },
    async update_endTime(endTime ,item) {
      let res = await this.$api.updateEndTime({
          endTime,
          id:item.id
      });
      if (res.status.code == 0) {
        if(endTime){
            this.$message.success('定时成功')
            this.getList()
        }else{
           this.$message.success('取消定时成功')
           item.endTime=''
          item.isTime=false

        }
      }
    },
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #409EFF;
}
</style>
