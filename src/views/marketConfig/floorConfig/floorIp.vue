<template>
  <d2-container>
    <el-form
      :inline="true"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button type="primary" @click="add_ip()">添加IP</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="IP名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="verbPic" label="IP图片" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.verbPic"
            >
            </el-image>
          </div> </template
      ></el-table-column>
      <el-table-column prop="linkType" label="链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '0'">无链接</el-tag>
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
        </template></el-table-column
      >
      <!-- <el-table-column
        prop="link"
        label="跳转路径h5以及网页跳转"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column> -->
      <el-table-column
        prop="leftJoinIdAddress"
        label="IP地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="显示状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == 1">显示</el-tag>
          <el-tag v-if="scope.row.status == 0">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="weight"
        label="排序"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 1"
            >隐藏</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 0"
            >显示</el-button
          >
          <el-button
            type="text"
            
            @click="del_click(scope.row)"
            style="color: red"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'floorIp',
  data () {
    return {
      tableData: [],
      total: 1,
      marketTabId: ''
    }
  },
  mounted () {
    this.marketTabId = this.$route.query.marketTabId
    this.getList()
  },
  methods: {
    // 查询列表
    async getList (page) {
      const res = await this.$api.leftJoinIpList({
        // pageNum: page,
        // pageSize: 15,
        marketTabId: this.marketTabId,
        leftJoinId: ''
      })
      this.tableData = res.result.list
    },
    xuanze (val) {
      this.getList(val)
    },
    add_ip () {
      this.$router.push({
        name: 'addFloorIp',
        query: {
          marketTabId: this.marketTabId
        }
      })
    },
    // 点击编辑
    audit_click (val) {
      this.$router.push({
        name: 'addFloorIp',
        query: {
          edit_id: val.id,
          marketTabId: this.marketTabId
        }
      })
    },
    // 隐藏显示
    async hide_click (val) {
      if (val.status === 1) {
        this.status = 0
      } else {
        this.status = 1
      }
      const res = await this.$api.hideOrShowLeftJoin({
        id: val.id,
        status: this.status
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: res.status.msg
      })
    },
    // 删除
    del_click (val) {
      this.$confirm(
        'IP删除后,客户端将自动移除本IP,同时无法恢复历史记录。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.remove(val.id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async remove (code) {
      await this.$api.delLeftJoin({
        id: code
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: '删除成功!'
      })
    }
  }
}
</script>

<style></style>
