<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="clear()"
          >清除移动端市场缓存</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="模块类型"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="content"
        label="模块名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="显示状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == 1">显示</el-tag>
          <el-tag v-if="scope.row.status == 0">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="weight"
        label="排序"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 1"
            >隐藏</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 0"
            >显示</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'module',
  data () {
    return {
      tableData: [],
      total: 1,
      status: ''
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async clear () {
      await this.$api.clearMarketCache()
      this.$message.success('清除成功')
    },
    // 查询列表
    async getList (page) {
      const res = await this.$api.moduleList({
        pageNum: page,
        pageSize: 15
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击编辑
    async audit_click (val) {
      this.$router.push({
        name: 'editModule',
        query: {
          edit_id: val.id,
          form: val
        }
      })
    },
    // 隐藏显示
    async hide_click (val) {
      if (val.status === 1) {
        this.status = 0
      } else {
        this.status = 1
      }
      console.log(val, this.status)
      const res = await this.$api.hideModule({
        id: val.id,
        status: this.status
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: res.status.msg
      })
    }
  }
}
</script>

<style></style>
