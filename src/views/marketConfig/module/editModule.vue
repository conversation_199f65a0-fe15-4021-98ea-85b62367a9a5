<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item
        v-for="(item, index) in form.content"
        :key="index"
        :label-width="formLabelWidth"
        required
      >
        <span slot="label"> 楼层区{{ index + 1 }}名称 </span>
        <el-input
          v-model="form.content[index]"
          placeholder="请输入楼层区1名称"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="排序:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.weight"
          placeholder="请输入排序"
          clearable
          style="width: 500px"
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="广告备注:" :label-width="formLabelWidth">
        <el-input
          v-model="form.remark"
          placeholder="请输入广告备注"
          clearable
          style="width: 500px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(return_click)"
          v-if="!look"
          >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit_click)"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'editModule',
  data () {
    return {
      form: {
        content: ''
      },
      formLabelWidth: '200px',
      edit_id: ''
    }
  },
  mounted () {
    this.edit_id = this.$route.query.edit_id
    this.form = this.$route.query.form
    if (this.form.content) {
      this.form.content = this.form.content.split(',')
    }
  },
  methods: {
    // 取消返回
    return_click () {
      this.$router.back()
    },
    // 新增编辑确定
    submit_click () {
      this.$confirm(
        '提交保存后,该入口将显示在客户端,确认保存？',
        '确认提交保存',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.submitClick()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async submitClick () {
      await this.$api.updateModule({
        id: this.edit_id,
        content: this.form.content.join(','),
        weight: this.form.weight,
        remark: this.form.remark
      })
      this.$message.success('修改成功')
      this.$router.push({
        name: 'module'
      })
    }
  }
}
</script>

<style></style>
