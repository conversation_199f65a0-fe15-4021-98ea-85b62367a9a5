<template>
  <d2-container class="page" ref="returnTop">
    <common-query :query-schema="querySchema" :data="query" :showCreation="true" @onCreation="onCreation"
      @onSubmit="onSubmit" @onReset="onReset"></common-query>
    <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData">
      <template #action="scope">
        <el-button type="text" @click="update(scope.row)">修改</el-button>
        <el-button type="text" @click="del(scope.row)">删除</el-button>
      </template>
    </common-table>
    <div class=""
      style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
      <el-pagination background :total="totalCount" :page-size="pageSize" :current-page="pageNum"
        :page-sizes="[20, 50, 100, 200,500,1000]" style="padding: 20px; background-color: #ffffff"
        @current-change="currentChange" @size-change="xuanzeSize">
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="900px">
      <!-- <div class="box"> -->
      <commonForm :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
        <template #game_type="scope">
          <el-checkbox-group v-model="selectedValues">
                   <el-checkbox
                     v-for="item in options"
                     :key="item.value"
                     :label="item.value"
                     :value="item.value"
                   >
                     {{ item.label }}
                   </el-checkbox>
                 </el-checkbox-group>
        </template>
      </commonForm>
    </el-dialog>

  </d2-container>
</template>

<script>
  import CommonTable from '@/components/CommonTable'
  import CommonQuery from '@/components/CommonQuery_h'
  import commonForm from '@/components/CommonForm'
  export default {
    components: {
      CommonTable,
      CommonQuery,
      commonForm
    },
    data() {
      return {
        tableData: [],
        totalCount: 0,
        dialogVisible: false,
        tableSchema: [ // 表格架构
          {
            label: 'id',
            field: 'id',
            width: '120px'
          },
          {
            label: '标题',
            field: 'title',
            width: '150px'
          },
          {
            label: '配图',
            field: 'iconUrl',
            type: 'img',
            width: '100px'
          },
          {
            label: '显示的时间',
            field: 'showTime',
            width: '100px'
          },
          {
            label: '权重',
            field: 'weight',
            width: '100px'
          },
          {
            label: '状态',
            field: 'showStatus',
            width: '100px',
            type: 'tag',
            tagMap: {
              0: {
                label: '隐藏',
                tagType: 'info'
              },
              1: {
                label: '显示',
                tagType: 'success'

              }
            }
          }, {
            label: '跳转链接',
            field: 'linkUrl',
            width: '100px'
          },

          {
            label: '操作',
            slot: 'action',
            width: '120px'
          }
        ],
        query: {
          dutyStatus: 'DOING'
        },
        querySchema: [ // 搜索组件架构
          {
            type: 'select',
            label: '状态：',
            placeholder: '请选择状态',
            field: 'status',
            options: [{
              label: '显示中',
              value: '1'
            }, {
              label: '隐藏中',
              value: '0'
            }]
          },
          {
            type: 'input',
            label: '标题',
            placeholder: '请输入标题',
            field: 'title'
          }
        ],
        searchShow: '',
        searchTit: '',
        options: [{
          value: '1',
          label: '显示中'
        }, {
          value: '0',
          label: '隐藏中'
        }],
        formSchema: [{
            type: 'input',
            label: '标题名称：',
            placeholder: '请输入标题名称',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入标题名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'select',
            label: '游戏名：',
            placeholder: '请选择游戏名',
            field: 'gameName',
            options: []
          },
          {
            type: 'input',
            label: '权重：',
            placeholder: '请输入权重',
            field: 'weight',
            rules: [{
              required: true,
              message: '请输入权重',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '跳转链接：',
            placeholder: '请输入跳转链接',
            field: 'linkUrl',
            rules: [{
              required: true,
              message: '请输入跳转链接',
              trigger: 'blur'
            }]
          },
          {
            type: 'select',
            label: '状态',
            field: 'showStatus',
            options: [{
              label: '显示中',
              value: '1'
            }, {
              label: '隐藏中',
              value: '0'
            }]
          },
          {
            type: 'datetime',
            label: '显示的时间',
            field: 'showTime',
            placeholder: '请选择显示的时间',
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },
            rules: [{
              required: true,
              message: '请选择显示的时间',
              trigger: 'blur'
            }]
          },
          {
            label: '',
            slot: 'game_type',
          },
          {
            type: 'img',
            label: '配图:',
            field: 'iconUrl',
            limit: 1
          },
          {
            type: 'action'
          }
        ],
        type: '',
        pageNum: 1,
        pageSize: 8,
        formData: {
          title: '',
          weight: '',
          showStatus: '',
          showTime: '',
          iconUrl: '',
        },
        title:"",
        winType:"",
        winType1:"",
        winTypeList:[],
        selectedValues: [],
        options: [
           { label: '赢B宝', value: 'B_BAO' },
           { label: '赢起飞卡', value: 'FLY_CARD' },
           // ... 更多选项
         ],
      }
    },
    created() {
      this.getList()
      this.gameItemList()
    },
    methods: {
      async getList() { // 获取变赚边玩列表
        const {
          result,
          status
        } = await this.$api.gameItemQry({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          showStatus: this.searchShow || null,
          title: this.searchTit || null
        })
        if (status.code === 0) {
          this.tableData = result.list
          this.totalCount = result.totalCount
        }
      },
      async gameItemList() { // 获取游戏列表
        const {
          result,
          status
        } = await this.$api.gameItemList({
          pageNum: 1,
          pageSize: 100,
        })
        if (status.code === 0) {
          console.log(this.formSchema[1])
          result.forEach((item)=>{
            this.formSchema[1].options.push({
                label:item.name,
                value:item.code
            })
          })
          console.log(this.formData)
        }
      },
      async edit() { // 修改
      let winType =  this.selectedValues.toString()
        const {
          result,
          status
        } = await this.$api.gameItemUpd({
          ...this.formData,
          winType
        })
        if (status.code === 0) {
          this.$message({
            type: 'success',
            message: '修改成功'
          })
           this.dialogVisible = false
          this.getList()
        }
        console.log(status, result, '修改')
      },
      update(row) {
         this.title= "修改"// 修改弹出
        this.dialogVisible = true
        this.type = 'update'
        if(row.winType){
          this.selectedValues = row.winType.split(",")
        }
        this.formData = {
          id: row.id,
          title: row.title,
          weight: row.weight,
          showStatus: row.showStatus,
          showTime: row.showTime,
          iconUrl: row.iconUrl,
          linkUrl: row.linkUrl,
          gameName: row.gameName
        }
      },
      submit() { // 提交
        console.log(this.formData)
        if (this.type === 'update') {
          this.edit()
        } else if (this.type === 'add') {
          this.add()
        }
      },
      async add() { // 新增
       let winType =  this.selectedValues.toString()
        const {
          status
        } = await this.$api.gameItemAdd({
          ...this.formData,
          winType
        })
        if (status.code === 0) {
          this.$message({
            type: 'success',
            message: '添加成功'
          })
           this.dialogVisible = false
           this.winType = false
           this.winType1 = false
          this.getList()
        }
      },
      onCreation() { // 新增弹出
      this.title= "新增"
        this.dialogVisible = true
        this.type = 'add'
        this.formData = {
          title: '',
          weight: '',
          showStatus: '',
          showTime: '',
          linkUrl: '',
          iconUrl: null
        }
      },

      onSubmit(e) { // 筛选
        this.searchShow = e.status
        this.searchTit = e.title
        this.getList()
      },
      onReset() { // 清除
        this.searchShow = ''
        this.searchTit = ''
        this.getList()
      },
      currentChange(value) {
        this.pageNum = value
        this.getList()
      },
      xuanzeSize(value) {
        this.pageSize = value
        this.getList()
      },
      async del(item){
        this.$confirm(`确定删除吗？`, '', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          const {
            status
          } = await this.$api.gameItemDel({id:item.id})
          if(status.code==0){
            this.getList()
          }
        })
      },
      handleCheckedCitiesChange(e){
        console.log(e)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .box {
    >div {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      >div:nth-child(1) {
        width: 30%;
        text-align: right;
        margin-right: 30px;
        font-size: 18px;
      }
    }
  }

  .dialog-footer {
    margin-left: 70%;
  }

  ::v-deep .el-input--suffix .el-input__inner {
    width: 212px !important;
  }
</style>
