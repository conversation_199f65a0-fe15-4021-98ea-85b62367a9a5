<template>
  <d2-container class="page">
    <div class="header">
      <el-button type="primary" @click="toAddPage()">添加IP</el-button>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 连接类型-->
            <template v-if="item.prop === 'linkType'">
              <el-tag>{{ linkTypeDict[scope.row[item.prop]] }}</el-tag>
            </template>
            <!-- 价格范围-->
            <template v-else-if="item.prop === 'priceRange'">
              <span>{{ `${scope.row['minPrice']} - ${scope.row['maxPrice']}` }}</span>
            </template>
            <!-- 作品库-->
            <template v-else-if="item.prop === 'goodsCount'">
              <el-button type="text"  @click="toGallery(scope.row)">{{ scope.row[item.prop] }}</el-button>
            </template>
            <!-- 显示状态-->
            <template v-else-if="item.prop === 'status'">
              <el-tag>{{ +scope.row[item.prop] ? '显示' : '隐藏' }}</el-tag>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120">
          <template slot-scope="scope">
            <el-button type="text"  @click="toAddPage(scope.row)">编辑</el-button>
            <el-button type="text"  @click="statusChange(scope.row)">
              {{ +scope.row.status ? '隐藏' : '显示' }}
            </el-button>
            <el-button type="text"  @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'IPGood',
  mounted () {
    this.marketTabId = this.$route.query.marketTabId
    this.getList()
  },
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'name',
          label: 'IP名称',
          width: '200'
        },
        {
          prop: 'linkType',
          label: '链接类型'
        },
        {
          prop: 'leftJoinIdAddress',
          label: 'IP地址',
          width: '250'
        },
        {
          prop: 'priceRange',
          label: '价格范围'
        },
        {
          prop: 'goodsCount',
          label: '作品库'
        },
        {
          prop: 'remark',
          label: '备注信息'
        },
        {
          prop: 'status',
          label: '显示状态'
        },
        {
          prop: 'weight',
          label: '排序'
        }
      ],
      linkTypeDict: {
        0: '无链接',
        10: '自定义协议',
        20: '网页跳转'
      },
      marketTabId: '',
      currentPageNum: 1
    }
  },
  methods: {
    /**
     * 获取列表数据
     * @method
     * @return {Promise<void>}
     */
    async getList () {
      const { result: { list } } = await this.$api.leftJoinIpList({
        pageNum: this.currentPageNum,
        pageSize: 15,
        marketTabId: this.marketTabId,
        leftJoinId: ''
      })
      this.tableData = list
    },
    /**
     * 跳转到添加页
     * @method
     */
    toAddPage (row) {
      this.$router.push({
        name: 'GoodAdd',
        query: {
          tabLeftJoinId: row?.id,
          marketTabId: this.marketTabId,
          tabName: this.$route.query.tabName
        }
      })
    },
    /**
     * 改变状态 显示/隐藏
     * @method
     * @param row {Object} 当前行数据
     */
    async statusChange (row) {
      await this.$api.hideOrShowLeftJoin({
        id: row.id,
        status: row.status ? 0 : 1
      })
      this.$message.success('操作成功')
      await this.getList()
    },
    /**
     * 删除按钮
     * @method
     * @param row {Object} 当前行数据
     */
    handleDelete (row) {
      this.$confirm(
        'IP删除后,客户端将自动移除本IP,同时无法恢复历史记录。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.remove(row.id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /**
     * 请求删除接口
     * @method
     * @param id {String} 当前行数据id
     */
    async remove (id) {
      await this.$api.delLeftJoin({ id })
      this.$message.success('删除成功')
      await this.getList()
    },
    /**
     * 跳转到作品库
     * @method
     * @param row {Object} 当前行数据
     */
    toGallery (row) {
      this.$router.push({
        name: 'GoodGallery',
        query: {
          ipListId: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

</style>
