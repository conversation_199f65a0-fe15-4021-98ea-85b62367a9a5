<template>
  <d2-container class="page">
    <div class="form">
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item :label="item.label" :required="item.required" v-for="(item,index) in formList" :key="index">
          <div v-if="item.type === 'priceRange'" class="priceRange">
            <el-input v-model="form.minPrice" placeholder="起始金额"></el-input>
            <span>-</span>
            <el-input v-model="form.maxPrice" placeholder="最高金额"></el-input>
          </div>
          <el-input v-else v-model="form[item.prop]" :type="item.type"
                    :disabled="item.disabled" :placeholder="item.placeholder"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">填充数据</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table">
      <el-table
        ref="multipleTable"
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
        :row-key="getRowKey"
      >
        <el-table-column
          type="selection"
          reserve-selection
          width="55">
        </el-table-column>
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 50">
          <template slot-scope="scope">
            <!-- 连接类型-->
            <template v-if="item.prop === 'cover'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]"
              >
              </el-image>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <span>已选{{ multipleSelection.length }}条</span>
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
      <div class="action">
        <el-button type="danger" @click="submit('all')">全选提交</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </div>
  </d2-container>
</template>

<script>

export default {
  name: 'GoodAdd',
  mounted () {
    const {
      tabName,
      tabLeftJoinId
    } = this.$route.query
    this.form = {
      ...this.form,
      tabName
    }
    if (tabLeftJoinId) {
      this.getDetail()
    }
  },
  data () {
    return {
      form: {
        leftJoinId: '',
        minPrice: '',
        maxPrice: '',
        remark: ''
      },
      formList: [
        {
          label: 'IP所属楼层：',
          prop: 'tabName',
          type: 'input',
          placeholder: '请输入IP所属楼层',
          disabled: true,
          required: true
        },
        {
          label: 'contract_address：',
          prop: 'leftJoinId',
          type: 'input',
          placeholder: '请输入用户地址',
          required: true
        },
        {
          label: '价格区间：',
          prop: 'priceRange',
          type: 'priceRange',
          placeholder: '请输入价格区间',
          required: true
        },
        {
          label: '排序：',
          prop: 'weight',
          type: 'input',
          placeholder: '请输入权重',
          required: true
        },
        {
          label: '广告备注：',
          prop: 'remark',
          type: 'textarea',
          placeholder: '请输入广告备注'
        }
      ],
      tableData: [],
      tableList: [
        {
          prop: 'title',
          label: '作品名称',
          width: '250'
        },
        {
          prop: 'tid',
          label: 'token ID',
          width: '200'
        },
        {
          prop: 'cover',
          label: '作品图片',
          width: '100'
        },
        {
          prop: 'price',
          label: '作品价格',
          width: '100'
        }
      ],
      multipleSelection: [],
      total: 0,
      pageNum: 1,
      pageSize: 15,
      echoList: [] // 已选择的作品列表
    }
  },
  methods: {
    /**
     * 指定一个key标识这一行的数据
     * @param row 当前行数据
     * @return {any} 返回一个key
     */
    getRowKey (row) {
      return row.tid
    },
    /**
     * 点击填充数据后 搜索
     * @method
     */
    search () {
      this.pageNum = 1
      this.getGoodChooseList()
    },
    /**
     * 获取当前ip可以选择的作品列表
     * @method
     */
    async getGoodChooseList () {
      const {
        leftJoinId: contractAddress,
        minPrice,
        maxPrice
      } = this.form
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.ipGoodsChooseList({
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        contractAddress,
        minPrice,
        maxPrice
      })
      this.tableData = list
      // 回显
      this.echo()
      this.total = totalCount
    },
    /**
     * 当选择项发生变化时会触发该事件
     * @method
     * @param val {Array} 当前选中的项
     */
    handleSelectionChange (val) {
      this.multipleSelection = []
      // 去重以免发生意外的push
      for (const i in val) {
        if (!this.multipleSelection.includes(val[i].tid)) {
          this.multipleSelection.push(val[i].tid)
        }
      }
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getGoodChooseList()
    },
    /**
     * 提交
     * @method
     */
    async submit (type) {
      const {
        marketTabId,
        tabLeftJoinId,
        tabName
      } = this.$route.query
      let tidListStr = this.multipleSelection.join(',')
      if (type === 'all') {
        tidListStr = '#ALL'
      }
      const obj = {
        ...this.form,
        tidListStr,
        marketTabId,
        linkType: 0,
        tabLeftJoinId
      }
      // updateLeftJoin
      delete obj.name
      const method = tabLeftJoinId ? 'updateLeftJoin' : 'insertLeftJoin'
      await this.$api[method](obj)
      this.$message.success(tabLeftJoinId ? '修改成功' : '添加成功')
      !tabLeftJoinId && (this.form = { tabName })
    },
    /**
     * 详情
     * @method
     */
    async getDetail () {
      const { result } = await this.$api.leftJoinDetail({
        tabLeftJoinId: this.$route.query.tabLeftJoinId
      })
      this.form = {
        ...this.form,
        ...result
      }
      await this.getGoodChooseList()
      await this.goodList()
    },
    /**
     * 获取IP当前作品库
     * @method
     */
    async goodList () {
      const { result: { list } } = await this.$api.ipGoodsList({
        ipListId: this.$route.query.tabLeftJoinId
      })
      this.echoList = list
      // 回显
      this.echo()
      this.multipleSelection = list.map(item => item.tid)
    },
    echo () {
      // 回显选中的作品
      this.echoList.forEach(key => {
        this.tableData.forEach(row => {
          if (row.tid === key.tid) {
            this.$refs.multipleTable.toggleRowSelection(row, true)
          }
        })
      })
    }
  }
}
</script>j

<style lang="scss" scoped>
.form {
  margin: 0 auto;
  width: 500px;

  .priceRange {
    display: flex;
    justify-content: space-between;

    span {
      width: 20%;
      text-align: center;
    }
  }

  .el-button {
    width: 100%;
  }
}

.table {
  margin-top: 20px;
  margin-bottom: 100px;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
