<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="作品名称：">
          <el-input v-model="formInline.title" placeholder="作品名称"></el-input>
        </el-form-item>
        <el-form-item label="token ID：">
          <el-input v-model="formInline.tid" placeholder="token id搜索"></el-input>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button type="info" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSubmit">查询</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 作品图片-->
            <template v-if="item.prop === 'cover'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="40">
          <template slot-scope="scope">
            <el-button type="text"  @click="toDetailPage(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'GoodGallery',
  data () {
    return {
      formInline: {
        title: '',
        tid: ''
      },
      tableData: [],
      tableList: [
        {
          prop: 'title',
          label: '作品名称',
          width: '200'
        },
        {
          prop: 'tid',
          label: 'token ID',
          width: '120'
        },
        {
          prop: 'cover',
          label: '作品图片',
          width: '150'
        },
        {
          prop: 'price',
          label: '作品价格'
        }
      ],
      total: 0,
      pageNum: 1,
      pageSize: 15
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取IP当前作品库
     * @method
     */
    async getList () {
      const {
        pageNum,
        pageSize,
        formInline,
        $route: { query: { ipListId } }
      } = this
      const { result: { list, totalCount } } = await this.$api.ipGoodsList({
        ipListId,
        pageNum,
        pageSize,
        ...formInline
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 重置搜索条件
     * @method
     */
    onReset () {
      this.pageNum = 1
      this.formInline = {
        title: '',
        tid: ''
      }
      this.getList()
    },
    /**
     * 提交搜索条件
     * @method
     */
    onSubmit () {
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 跳转到详情页
     * @param row {Object} 行数据
     */
    toDetailPage (row) {
      this.$router.push({
        name: 'GoodDetail',
        query: {
          tid: row.tid
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
