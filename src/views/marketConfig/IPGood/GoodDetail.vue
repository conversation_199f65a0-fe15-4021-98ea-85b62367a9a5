<template>
  <d2-container class="page">
    <div class="table">
     <div class="image">
       <el-image
         style="width: 200px; height: 200px"
         :src="details.cover"
         :preview-src-list="[details.cover]"
       ></el-image>
     </div>
      <el-form ref="form">
        <el-form-item :label="item.label" v-for="(item,index) in formList" :key="index">
          <div class="data">{{details[item.prop]}}</div>
        </el-form-item>
      </el-form>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'GoodDetail',
  data () {
    return {
      details: {},
      formList: [
        {
          label: '作品名称：',
          prop: 'title'
        },
        {
          label: '价格：',
          prop: 'price'
        },
        {
          label: '作品简介：',
          prop: 'introduction'
        },
        {
          label: '创作者：',
          prop: 'publisher'
        },
        {
          label: '持有人：',
          prop: 'holder'
        },
        {
          label: '版号：',
          prop: 'serial'
        },
        {
          label: 'token id：',
          prop: 'tid'
        },
        {
          label: '作品描述：',
          prop: 'content'
        },
        {
          label: '艺术家介绍：',
          prop: 'introduce'
        },
        {
          label: '作品标签：',
          prop: 'tags'
        }
      ]
    }
  },
  mounted () {
    this.getDetail()
  },
  methods: {
    async getDetail () {
      const { result } = await this.$api.getGoodsDetails({
        tid: this.$route.query.tid
      })
      this.details = result
    }
  }
}
</script>

<style lang="scss" scoped>
.table {
  margin:  0 auto;
  width: 600px;
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
