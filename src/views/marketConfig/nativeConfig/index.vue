<template>
	<d2-container class="page">
		<el-tabs type="border-card">
			<el-tab-pane label="原生配置(暂未上线)">
				<el-form label-width="200px" class="demo-ruleForm">
					<el-form-item label="请选择你要配置的：">
						<el-select  v-model="selectItem" placeholder="请选择" @change="change">
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item">
							</el-option>
						</el-select>
					</el-form-item>
					<el-form-item :label="`请输入${item.label}`" v-for="(item,index) in selectItem.param">
						<el-input v-model="item.value" @input="handlerChange(item)" :placeholder="`请输入${item.label}`"
							style="width:300px;" size="medium"></el-input>
					</el-form-item>
					<el-form-item label="原生地址：">
						<div v-if="selectItem.type==1">{{selectItem.value+tokenId}}</div>
						<div v-if="selectItem.type==2">{{selectItem.value+userId}}</div>
						<div v-if="selectItem.type==3">
							{{`${selectItem.value+ctId}&userId=${userId}&isSelf=0&isCreate=1`}}</div>
						<div v-if="selectItem.type==0">{{selectItem.value}}</div>
					</el-form-item>
					<el-form-item>
						<el-button>清空</el-button>
						<el-button type="primary" @click="copy()">一键复制</el-button>
					</el-form-item>
				</el-form>
			</el-tab-pane>
		</el-tabs>
	</d2-container>
</template>

<script>
	import util from '@/libs/util.js'
	export default {
		name: 'native-config',
		data() {
			return {
				options: [{
					value: 'nftcn://ranking',
					label: '榜单',
					param: [],
					type: 0
				}, {
					value: 'nftcn://item?itemTokenId=',
					label: '商品详情',
					param: [{
						label: 'tokenId',
						value: ''
					}],
					type: 1
				}, {
					value: 'nftcn://user?userId=',
					label: '用户详情',
					param: [{
						label: 'userId',
						value: ''
					}],
					type: 2
				}, {
					value: 'nftcn://series?itemTokenId=',
					label: '系列详情',
					param: [{
						label: 'userId',
						value: ''
					}, {
						label: 'ctId',
						value: ''
					}],
					type: 3
				}, {
					value: 'nftcn://box?itemTokenId=',
					label: '盲盒系列详情页面',
					param: [{
						label: 'userId',
						value: ''
					}, {
						label: 'ctId',
						value: ''
					}],
					type: 3
				}],
				selectItem: '',
				ctId: "",
				tokenId: "",
				userId: "",
				type: 0
			}
		},
		mounted() {

		},

		methods: {
			change(e) {
				e.param.forEach((item)=>{
					item.value=""
				})
				this.ctId = ""
				this.tokenId = ""
				this.userId = ""
				this.type=e.type
				if (e.label == 'userId') {
					this.userId = e.value
				} else if (e.label == 'ctId') {
					this.ctId = e.value
				} else if (e.label == 'tokenId') {
					this.tokenId = e.value
				}
				console.log(this.type)
			},
			handlerChange(e) {
				if (e.label == 'userId') {
					this.userId = e.value
					this.type = 2
				} else if (e.label == 'ctId') {
					this.ctId = e.value
					this.type = 3
				} else if (e.label == 'tokenId') {
					this.tokenId = e.value
					this.type = 1
				}
			},
			copy() {
				let content="";
				console.error(this.type)
				if (this.type === 1) {
					content = this.selectItem.value + this.tokenId
				} else if (this.type === 2) {
					content = this.selectItem.value + this.userId
				} else if (this.type === 3) {
					content = `${this.selectItem.value+this.ctId}&userId=${this.userId}&isSelf=0&isCreate=1`
				} else {
					content = this.options[0].value
				}
				console.log(content)
				let oInput = document.createElement('input');
				// 将想要复制的值
				oInput.value = content;
				// 页面底部追加输入框
				document.body.appendChild(oInput);
				// 选中输入框
				oInput.select();
				// 执行浏览器复制命令
				document.execCommand('Copy');
				// 弹出复制成功信息
				this.$message.success('复制成功');
				// 复制后移除输入框
				oInput.remove();
			},
		},
	}
</script>
