<script>
import ImgUploader from '@/components/ImgUploader'
export default {
  name: 'BigVerseManage',
  components: {
    ImgUploader
  },
  data () {
    return {
      tableData: [],
      query: {},
      querySchema: [
        {
          type: 'input',
          label: '商品名称',
          placeholder: '请输入商品名称',
          field: 'title'
        },
        {
          type: 'input',
          label: '发布用户',
          placeholder: '请输入发布用户名称',
          field: 'username'
        },
        {
          type: 'select',
          label: '发行平台',
          field: 'platform',
          placeholder: '请选择发行平台',
          options: []
        },
        {
          type: 'select',
          label: '审核状态',
          field: 'audit',
          placeholder: '请选择审核状态',
          options: []
        },
        {
          type: 'select',
          label: '商品状态',
          field: 'saleStatus',
          placeholder: '请选择商品状态',
          options: []
        },
        {
          type: 'select',
          label: '商品属性',
          field: 'saleType',
          placeholder: '请选择商品属性',
          options: []
        }
      ],
      tableSchema: [
        {
          label: '名称',
          field: 'name'
        },
        {
          label: '封面图',
          field: 'cover',
          type: 'img'
        },
        {
          label: '背景图',
          field: 'image',
          type: 'img'
        },
        {
          label: '二维码',
          field: 'qrCode',
          type: 'img'
        },
        {
          label: '跳转链接',
          field: 'link'
        },
        {
          label: '链接类型',
          field: 'linkType',
          type: 'linkType'
        },
        {
          label: '权重',
          field: 'weight'
        }
      ],
      dialogVisible: false,
      data: {
        cover: '',
        qrCode: '',
        image: ''
      },
      infoSchema: [
        { label: '名称：', field: 'name' },
        { label: '封面图：', field: 'cover', type: 'img' },
        { label: '背景图：', field: 'image', type: 'img' },
        { label: '二维码：', field: 'qrCode', type: 'img' },
        {
          label: '链接类型：',
          field: 'linkType',
          type: 'radio',
          options: [{ label: '无链接', value: 0 }, { label: '网页链接', value: 10 }]
        },
        { label: '跳转链接：', field: 'link', show: { relationField: 'linkType', value: 10 } },
        { label: '权重：', field: 'weight' }
      ],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      },
      isDetail: false,
      curId: '' // 当前编辑的id
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    async getDetail (id) {
      const { status, result } = await this.$api.getBigVerseDetail({ id })
      if (status.code === 0) {
        this.data = result
      }
    },
    async addItem () {
      const apiStr = this.curId ? 'updateBigVerse' : 'addBigVerse'
      const data = {
        ...this.data,
        id: this.curId
      }
      await this.$api[apiStr](data)
      this.getList()
      this.dialogVisible = false
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.getBigVerseList(params)
      console.log(result)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    deleteItem (id) {
      this.$confirm('删除后该项将在列表不可见', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.deleteBigVerse({ id })
        if (status.code === 0) {
          this.getList()
        }
      })
    },
    openDialog (id, isDetail) {
      this.isDetail = isDetail
      this.curId = id
      !id && (this.data = {
        cover: '',
        qrCode: '',
        image: ''
      })
      id && this.getDetail(id)
      this.dialogVisible = true
    },
    closeDialog () {
      this.dialogVisible = false
      this.isDetail = false
    }
  }
}
</script>

<template>
  <d2-container class="page">
<!--    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>-->
    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column
        label="序号"
        type="index"
        width="50">
      </el-table-column>
      <el-table-column
        v-for="(item) in tableSchema"
        :key="item.field"
        :label="item.label"
        :prop="item.field"
        :width="item.width">
        <template v-if="item.type" v-slot="scope">
          <span v-if="item.type === 'img'">
            <img :src="scope.row[item.field]" style="width: 100px;" alt="">
          </span>
          <span v-if="item.type === 'linkType'">
            <el-tag v-if="scope.row[item.field] === 0">无链接</el-tag>
            <el-tag v-if="scope.row[item.field] === 10">网页链接</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="100">
          <template v-slot:header>
            <el-button @click="openDialog()" type="primary" size="mini">新增</el-button>
          </template>
          <template v-slot="scope">
            <el-button @click="openDialog(scope.row.id, true)" type="text">查看</el-button>
            <el-button @click="deleteItem(scope.row.id)" type="text">删除</el-button>
            <el-button @click="openDialog(scope.row.id)" type="text">修改</el-button>
          </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>

    <el-dialog title="bigverse" :visible.sync="dialogVisible">
      <el-form size="mini" label-width="120px">
        <div
          v-for="item in infoSchema"
          :key="item.field"
        >
          <el-form-item
            v-if="!item.show || item.show && data[item.show.relationField] === item.show.value"
            :label="item.label"
          >
              <template v-if="item.type === 'img'">
                <img v-if="isDetail" width="200" :src="data[item.field]" alt="">
                <ImgUploader v-else :value.sync="data[item.field]"></ImgUploader>
              </template>
              <template v-else-if="item.type === 'radio'">
                <el-radio-group :disabled="isDetail" v-model="data[item.field]">
                  <el-radio
                    v-for="item in item.options"
                    :key="item.value"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                </el-radio-group>
              </template>
              <template v-else-if="!item.type || item.type === 'input'">
                <template v-if="isDetail">{{ data[item.field] }}</template>
                <el-input
                  v-else
                  style="width: 200px;"
                  :placeholder="item.placeholder ? item.placeholder : '请输入内容'"
                  v-model="data[item.field]"
                  clearable>
                </el-input>
              </template>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
        <el-button v-if="!isDetail" type="primary" @click="addItem">提交</el-button>
      </div>
    </el-dialog>
  </d2-container>
</template>

<style lang="scss" scoped>

</style>
