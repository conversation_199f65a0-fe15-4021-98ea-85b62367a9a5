<template>
  <d2-container class="page">
    <div class="flex">
      <common-query :query-schema="querySchema" ref="query" :data="query"  @onSubmit="onQueryChange"
        @onExport="onExport" :showSubmit="false" :showReset="false"></common-query>

      <!-- <el-button type="primary"  @click="downloadTemplate()">模板下载</el-button>

        <el-button type="primary"  @click="batchCancelBuy()">批量撤销</el-button> -->
    </div>
    <div class="flex" style="margin-bottom:20px;">
      <el-button type="primary" @click="chaxun" >查询</el-button>
      <el-button type="primary" @click="onReset" >清除</el-button>
      <el-button type="primary" @click="clickExport()" >导出</el-button>
      <el-dropdown trigger="click" @command="functionSubmit">
        <el-button type="primary"  style="margin-left: 10px;">批量操作 <i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item,index) in templateList" :key="item.code" :command="item.code">
            <!-- <div v-if="index==1">
               <input type="file" id="importFile" @change="importTemplate($event)" v-show="false">
               <label type="primary" for="importFile"  >模板导入</label>
             </div> -->
            <span>
              {{item.desc}}
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <common-table :table-data="tableData" :table-schema="tableSchema" :loading="loading" :showSelection="true"
      :showIndex="false" :multipleSelection.sync="multipleSelectionList">
      <template #action="scope">
        <el-button @click="CancelBuy(scope.row)" type="text">撤销</el-button>
        <el-button @click="openQuick(scope.row)" type="text" v-show="scope.row.status == 'INIT'">加速求购</el-button>
      </template>
    </common-table>
    <div class=""
      style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
      <div class="money_total">
        <div class="marginR">本页总计求购剩余份数:{{moneyNum}}份</div>
        <div class="marginR">合计总计求购剩余份数:{{allMoneyNum}}份</div>
      </div>
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="xuanzeSize">
      </el-pagination>
    </div>
    <el-dialog title="添加求购" :visible.sync="isDialog" width="900px" center>
      <common-form :submit="modelSubmit" :data="modelFormData" :schema="modelFormSchema" label-width="300px">
        <template #msg="scope">
          <p style="color:rgb(255, 29, 29);margin-top:-10px;">
            求购出价只能输入整数，否则会进行四舍五入取整，望熟知!</p>
        </template>
      </common-form>
    </el-dialog>
    <el-dialog title="加速求购" :visible.sync="isQuickMode" width="900px" center>
      <common-form :submit="quickModelSubmit" :data="quickModelFormData" :schema="quickModelFormSchema"   label-width="300px">
        <template #time="scope">
          <el-row justify="start">
            <el-col :span="4">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="" v-model="quickModelFormData.minSecond"></el-input><span class="danwei">s</span>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="grid-content bg-purple" style="text-align:center;">-</div>
            </el-col>
            <el-col :span="4">
              <div class="grid-content bg-purple flex">
                <el-input placeholder="" v-model="quickModelFormData.maxSecond"></el-input><span class="danwei">s</span>
              </div>
            </el-col>
          </el-row>
        </template>
      </common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
  import CommonQuery from '@/components/CommonQuery_h'
  import CommonTable from '@/components/CommonTable'
  import CommonForm from '@/components/CommonForm'
  import {
    uploadExcelToOss
  } from '@/api/ossCenter'
  import {
    downloadBlob
  } from '@/utils/helper'
  import FileUploader from '@/components/FileUploader'
  export default {
    name: 'buyList',
    components: {
      CommonTable,
      CommonQuery,
      FileUploader,
      uploadExcelToOss,
      CommonForm
    },
    data() {
      return {
        tableData: [],
        query: {
          status: 'INIT',
          sort: 'DESC',
          wantToBuyTimeBegin: '',
          wantToBuyTimeEnd: '',

        },
        querySchema: [{
            type: 'input',
            label: '系列id',
            placeholder: '请输入系列id',
            field: 'ctId'
          },
          {
            type: 'input',
            label: '系列名称',
            placeholder: '请输入系列名称',
            field: 'ctName'
          },
          {
            type: 'input',
            label: '用户地址',
            placeholder: '请输入用户地址',
            field: 'userContractAddress'
          },
          {
            type: 'select',
            label: '用户类型',
            field: 'userMember',
            placeholder: '请选择用户类型',
            options: [{
                label: '主力',
                value: 4
              },
              {
                label: '大户',
                value: 0
              }
            ]
          },
          {
            type: 'input',
            label: '求购价格',
            placeholder: '请输入求购价格',
            field: 'minTargetPrice'
          },
          {
            label: '状态',
            field: 'status',
            type: 'select',
            options: [{
                label: '求购中',
                value: 'INIT'
              },
              {
                label: '已撤销',
                value: 'CANCEL'
              },
              {
                label: '已完成',
                value: 'COMPLETE'
              },
              {
                label: '等待开始',
                value: 'WAITING'
              },
              {
                label: '账户余额不足',
                value: 'BALANCE_NOT_ENOUGH_WAITING'
              },
              {
                label: '遇到错误中止',
                value: 'ERROR'
              }
            ]
          },
          {
            type: 'datetimerange',
            label: '求购时间',
            placeholder: '请输入求购时间',
            field: 'wantToBuyTimeBegin',
            field2: 'wantToBuyTimeEnd',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss.SSS'
          },
          {
            label: '价格排序',
            field: 'sort',
            type: 'select',
            options: [{
                label: '价格升序',
                value: 'ASC'
              },
              {
                label: '价格降序',
                value: 'DESC'
              }
            ]
          }
        ],
        tableSchema: [{
            label: '系列',
            field: 'seriesName'
          },
          {
            label: '系列id',
            field: 'ctId',
            width: '300px'
          },
          {
            label: '用户地址',
            field: 'userContractAddress'
          },
          {
            label: '用户昵称',
            field: 'userNickName'
          },
          {
            label: '求购价格',
            field: 'targetPrice',
            width: '100px'
          },
          {
            label: '求购份数',
            field: 'targetNum',
            width: '100px'
          },
          {
            label: '求购剩余份数',
            field: 'remainNum',
            width: '120px'
          },
          {
            label: '用户类型',
            field: 'userMember',
            type: 'tag',
            tagMap: {
              4: {
                label: '主力',
                tagType: 'success'
              },
              0: {
                label: '大户'
              }
            },
            width: '100px'
          },
          {
            label: '导入时间',
            field: 'wantToBuyTime'
          },
          {
            label: '状态',
            field: 'status',
            type: 'tag',
            tagMap: {
              INIT: {
                label: '求购中'
              },
              WAITING: {
                label: '等待开始'
              },
              CANCEL: {
                label: '已撤销',
                tagType: 'success'
              },
              COMPLETE: {
                label: '已完成',
                tagType: 'success'
              },
              BALANCE_NOT_ENOUGH_WAITING: {
                label: '账户余额不足'
              },
              ERROR: {
                label: '遇到错误中止'
              }
            }
          },
          {
            label: '操作',
            slot: 'action',
            fixed: 'right',
            width: '100px'
          }
        ],
        dialogVisible: false,
        data: {
          cover: '',
          qrCode: ''
        },
        infoSchema: [{
            label: '名称：',
            field: 'name'
          },
          {
            label: '封面图：',
            field: 'cover',
            type: 'img'
          },
          {
            label: '二维码：',
            field: 'qrCode',
            type: 'img'
          },
          {
            label: '链接类型：',
            field: 'linkType',
            type: 'radio',
            options: [{
              label: '无链接',
              value: 0
            }, {
              label: '网页链接',
              value: 10
            }]
          },
          {
            label: '跳转链接：',
            field: 'link',
            show: {
              relationField: 'linkType',
              value: 10
            }
          },
          {
            label: '权重：',
            field: 'weight'
          }
        ],
        modelFormData: {
          ctid: '',
          targetNum: '',
          userNum: '',
          targetPrice: '',
          isTiming:1
        },
        modelFormSchema: [{
            type: 'search',
            label: '求购系列',
            field: 'ctid',
            rules: [{
              required: true,
              message: '请输入求购系列',
              trigger: 'change'
            }]
          },
          {
            label: '求购总份数',
            field: 'targetNum',
            rules: [{
              required: true,
              message: '请输入求购总份数',
              trigger: 'change'
            }]
          },
          {
            label: '求购的4.3账号数',
            field: 'userNum',
            rules: [{
              required: true,
              message: '请输入求购的4.3账号数',
              trigger: 'change'
            }]
          },
          {
            type: 'number-input',
            label: '求购出价(整数)',
            field: 'targetPrice',
            rules: [{
              required: true,
              message: '请输入求购出价(整数)',
              trigger: 'change'
            }]
          },
         {
         	type: 'radio',
         	label: '是否定时：',
         	field: 'isTiming',
         	options: [{
         			label: '定时执行',
         			value: 1
         		},
         		{
         			label: '不定时执行',
         			value: 0
         		},
         	],
         	rules: [{
         		required: true,
         	}],
         },
         {
           type: 'datetime',
           label: '开始时间：',
           placeholder: '请选择开始时间',
           field: 'startTime',

           show: {
           	relationField: 'isTiming',
           	value: [1]
           },
         },
          {
            label: '',
            slot: 'msg'
          }, {
            type: 'action'
          }
        ],
        page: {
          totalCount: 0,
          pageSize: 20,
          pageNum: 1
        },
        isDetail: false,
        action: `${process.env.VUE_APP_BASE_URL}hanxin/adminApi/airDrop/record/import`,
        token: {
          AdminAuthorization: localStorage.getItem('usertoken')
        },
        multipleSelectionList: [],
        loading: false,
        templateList: [{
          code: '2',
          desc: '添加求购'
        }, {
          code: '3',
          desc: '批量撤销'
        }, {
          code: '4',
          desc: '批量加速求购'
        }],
        moneyNum: 0,
        allMoneyNum: 0,
        isDialog: false,
        quickModelFormData: {
          num: '',
          maxSecond: '',
          minSecond: '',
        },
        quickModelFormSchema: [{
            label: '求购数量',
            field: 'num',
            placeholder:'请输入求购数量',
            rules: [{
              required: true,
              message: '请输入求购数量',
              trigger: 'change'
            }]
          },{
            label: '多久加速一单',
            slot: 'time',
            rules: [{
              required: true,
            }]
          }, {
            type: 'action',
            exclude:['back']
          }
        ],
        isQuickMode:false,
        dutyId:""
      }
    },
    computed: {},
    mounted() {
      this.getList()
      this.orderTargetBuyList_total()
    },
    methods: {
      // 过滤查询
      onQueryChange(data) {
        console.log(this.query)
        console.log(data)
        this.query = data
        this.getList(true)
        this.orderTargetBuyList_total()
      },
      async getList(isInit) {
        const params = {
          ...this.query,
          pageSize: this.page.pageSize,
          pageNum: this.page.pageNum,
          pageNum: isInit ? 1 : this.page.pageNum
        }
        const {
          status,
          result
        } = await this.$api.orderTargetBuyList(params)
        this.moneyNum = 0
        if (status.code === 0) {
          result.list.forEach((item) => {
            if (item.status == 'INIT' || item.status == 'BALANCE_NOT_ENOUGH_WAITING') {
              this.moneyNum = this.moneyNum + Number(item.remainNum)
            }
          })
          this.tableData = result.list
          this.page.totalCount = result.totalCount
          this.page.pageCount = result.pageCount
        }
      },
      // 分页切换
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      // 分页
      xuanzeSize(val) {
        this.page.pageSize = val
        this.getList()
      },
      // 添加、修改项目
      addProject(airDropId) {
        const title = airDropId ? '修改空投' : '新增空投'
        const apiName = airDropId ? 'airDropProjectEditCollection' : 'airDropProjectAddCollection'
        this.$prompt('请输入空投名称', title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(async ({
          value
        }) => {
          const data = {
            airDropName: value
          }
          airDropId && (data.airDropId = airDropId)
          const {
            status
          } = await this.$api[apiName](data)
          if (status.code === 0) {
            this.$message.success(status.msg)
            this.getList()
          }
        })
      },
      // 清空结果
      async clearPreview(airDropId) {
        const {
          status
        } = await this.$api.airDropResultDeleteCollection({
          airDropId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      },
      // 导出结果
      async exportResult(airDropId) {
        const res = await this.$api.airDropResultExportCollection({
          airDropId
        })
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '空投结果')
          this.$message.success('导出成功')
          this.getList()
        }
      },
      // 执行空投
      async executeAirDrop(airDropId) {
        const {
          status
        } = await this.$api.airDropExecuteProjectCollection({
          airDropId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      },
      async downloadTemplate() {
        const {
          status,
          result
        } = await this.$api.downLoadOrderTargetBuyTemplate({})
        if (status.code === 0) {
          window.open(result.wantToBuyTemplateUrl, '_blank')
        }
      },
      // 空投记录导入
      async importTemplate(event) {
        const formData = new FormData()
        const input = event.target
        const file = input.files[0]
        formData.append('file', file)
        this.loading = true
        const {
          result: {
            url
          }
        } = await uploadExcelToOss(formData)
        input.value = ''
        const res = await this.$api.importWantToBuyExcel({
          excelUrl: url
        })
        if (res.status.code == 0) {
          this.loading = false
          this.$message.success('导入成功')
          this.getList()
        } else {
          this.loading = false
          this.$message.error(res.status.msg)
        }
      },
      add(item) {
        console.log(item)
      },
      async CancelBuy(item) {
        const array = []
        array.push({
          dutyId: item.dutyId
        })
        const {
          status
        } = await this.$api.batchCancelWantToBuy({
          dutyIdJson: JSON.stringify(array)
        })
        if (status.code === 0) {
          this.$message.success('撤销成功')
          this.getList()
        }
      },
      async batchCancelBuy() {
        if (this.multipleSelectionList.length >= 1) {
          const str = []
          this.multipleSelectionList.forEach((item) => {
            str.push({
              dutyId: item.dutyId
            })
          })
          const {
            status
          } = await this.$api.batchCancelWantToBuy({
            dutyIdJson: JSON.stringify(str)
          })
          if (status.code === 0) {
            this.$message.success('批量撤销成功')
            this.getList()
          }
        } else {
          this.$message.error('请勾选你要批量的选项')
        }
      },
      // 导出
      async onExport(isInit) {
        this.loadingText = '正在导出'
        this.loading = true
        const params = {
          ...this.query
        }
        const res = await this.$api.orderTargetBuyListExport(params)
        console.log(res, '导出')
        if (res.retCode === 500) {
          this.$message.error(res.retMsg)
          this.loading = false
          this.getList()
        } else if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then((buffer) => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '求购' + Date.now() + '.csv')
          this.$message.success('导出成功')
          this.loading = false
          this.getList()
        }
      },
      functionSubmit(code) {
        console.log(code)
        if (code == 2) {
          this.isDialog = true
        } else if (code == 3) {
          this.batchCancelBuy()
        } else if (code == 4) {
          this.batchSpeedUpBuy()
        }
      },
      chaxun() {
        // this.orderTargetBuyList_total()
        this.$refs.query.onSubmit()
      },
      onReset() {
        // this.$refs.query.onReset()
      },
      clickExport() {
        this.$refs.query.onExport()
      },
      async orderTargetBuyList_total() {
        const params = {
          ...this.query,
          pageSize: this.page.pageSize,
          pageNum: this.page.pageNum
        }
        const {
          status,
          result
        } = await this.$api.orderTargetBuyListTotal(params)
        if (status.code === 0) {
          console.log(result)
          this.allMoneyNum = result.remainNum
        }
      },
      async modelSubmit() {
        let ctid, targetPrice
        if (this.modelFormData.ctid.indexOf('(') != -1) {
          ctid = this.modelFormData.ctid.split('(')[1].split(')')[0]
        } else {
          this.$message.error('请搜索系列选择后重试')
          return false
        }
        targetPrice = Math.round(this.modelFormData.targetPrice)
        const params = {
          ...this.modelFormData,
          ctid,
          targetPrice
        }
        const {
          status,
          result
        } = await this.$api.orderTargetBatchCreate(params)
        if (status.code === 0) {
          this.$message.success('成功')
          this.isDialog = false,
            this.modelFormData = {}
        }
      },
      async quickModelSubmit() {
        const {
          status
        } = await this.$api.quickTargetSingle({
          dutyId: this.dutyId,
          ...this.quickModelFormData
        })
        if (status.code === 0) {
          this.isQuickMode = false
          this.quickModelFormData = {
              num:"",
              minSecond:"",
              maxSecond:""
          }
          this.$message.success('加速求购成功')
          this.getList()
        }
      },
      async batchSpeedUpBuy() {
        if (this.multipleSelectionList.length >= 1) {
          const str = []
          this.multipleSelectionList.forEach((item) => {
            str.push(item.dutyId)
          })
          const {
            status
          } = await this.$api.quickTarget({
            dutyIdJson: JSON.stringify(str)
          })
          if (status.code === 0) {
            this.$message.success('批量加速求购成功')
            this.getList()
          }
        } else {
          this.$message.error('请勾选你要批量的选项')
        }
      },
      openQuick(item){
        this.dutyId = item.dutyId
        this.isQuickMode = true
      }
      // openDialog () {
      //   this.dialogVisible = true
      // },
      // closeDialog () {
      //   this.dialogVisible = false
      //   this.isDetail = false
      // }
    }
  }
</script>

<style lang="scss" scoped>
  a {
    text-decoration: none;
    color: #409eff;
  }

  .flex {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .el-dropdown-menu {
    // left:1915px !important;
  }

  .money_total {
    // position: absolute;
    // left: 20px;
    font-size: 14px;
    color: #f56c6c;
    display: flex;

    .marginR {
      margin-right: 30px;
    }
  }
</style>
