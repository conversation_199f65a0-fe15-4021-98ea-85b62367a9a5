<template>
  <d2-container class="page">
    <div class="buttons">
      <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="clearPreview()"  type="primary">
        清空结果</el-button>
      <el-button v-if="tableData.length > 0" @click="exportResult()"  type="primary">导出结果</el-button>
      <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="isDialog=true"  type="primary">
        添加单个记录
      </el-button>
      <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="executeAirDrop()"
        type="primary">执行空投</el-button>
				<el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)"  @click="downloadTemplate()" type="primary" style="margin-right: 10px;">模板下载</el-button>
				<file-uploader v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @success="importTemplate" :value.sync="templateUrl" :id="airDropId"
					 text="上传导入" ></file-uploader>
    </div>
    <common-table  :table-data="tableData" :table-schema="tableSchema">
      <template #cover="scope">
        <img :src="scope.row.cover" style="width: 100px;" :alt="scope.row.cover">
      </template>
      <template #action="scope">
        <el-button @click="executeAirDropItem(scope.row.airDropRecordId)" type="text">执行空投</el-button>
         <el-button @click="openDel(scope.row.airDropRecordId)" type="text">删除</el-button>
      </template>
    </common-table>
    <el-dialog title="添加单个记录" :visible.sync="isDialog" center>
      <el-form :model="form">
        <el-form-item label="用户contractAddress:" :label-width="formLabelWidth">
          <el-input v-model="form.contractAddress" placeholder="请输入contractAddress" clearable style="width: 80%">
          </el-input>
        </el-form-item>
        <el-form-item label="作品tokenId:" :label-width="formLabelWidth">
          <el-input v-model="form.tokenId" placeholder="作品tokenId" clearable style="width: 80%"></el-input>
        </el-form-item>
		<el-form-item label="空投订单价格:" :label-width="formLabelWidth">
		  <el-input v-model="form.price" placeholder="空投订单价格" clearable style="width: 80%" type="number" min="1"></el-input>
		</el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="click_submit">确 定</el-button>
      </div>
    </el-dialog>
    <el-pagination @current-change="currentChange" layout="prev, pager, next" :total="page.totalCount">
    </el-pagination>
  </d2-container>
</template>

<script>
  import FileUploader from '@/components/FileUploader'
  import CommonTable from '@/components/CommonTable'
  import {
    downloadBlob
  } from '@/utils/helper'

  export default {
    name: 'AirDropWorks',
    components: {
      CommonTable,
	  FileUploader
    },
    data() {
      return {
        status: '', // 项目 status
        action: `${process.env.VUE_APP_BASE_URL}airDrop/missWebSign/item/import`,
        token: {
          AdminAuthorization: localStorage.getItem('usertoken')
        },
        tableData: [], // 表格数据
        query: {}, // 表格查询参数
        tableSchema: [ // 组件数据架构
          {
            label: '作品标题',
            field: 'title'
          },
          {
            label: '作品封面',
            field: 'cover',
            slot: 'cover'
          },
          {
            label: '作品价格',
            field: 'price'
          },
			{
			  label: '空投订单价格',
			  field: 'airDropPrice'
			},
          {
            label: '作品版本号',
            field: 'serial'
          },
          {
            label: '作品 tid',
            field: 'tid'
          },
          {
            label: '作品类型',
            field: 'type',
            slot: 'type',
            type: 'tag',
            tagMap: {
              11: {
                label: '视频'
              },
              12: {
                label: '音频'
              },
              13: {
                label: '3D静态模型'
              },
              14: {
                label: '3D动态模型'
              },
              default: {
                label: '图片'
              }
            }
          },
          {
            label: '作品种类',
            field: 'mold',
            slot: 'mold',
            type: 'tag',
            tagMap: {
              1: {
                label: '普通作品'
              },
              2: {
                label: '盲盒'
              }
            }
          },
		  {
		    label: '是否播报',
		    field: 'sendImMsg',
		    slot: 'sendImMsg',
		    type: 'tag',
		    tagMap: {
		      0: {
		        label: '否'
		      },
		      1: {
		        label: '是',
		  		tagType: 'success'
		      },
		    }
		  },
		  {
		    label: '状态',
		    field: 'status',
		    slot: 'status',
		    type: 'tag',
		    tagMap: {
		      INIT: {
		        label: '创建初始'
		      },
		      DONE: {
		        label: '空投完成',
				tagType: 'success'
		      },
		      FAIL: {
		        label: '空投失败',
				tagType: 'danger'
		      }
		    }
		  },
		  {
		    label: '失败原因',
		    field: 'failReason'
		  },
          {
            label: '来源用户昵称',
            field: 'fromNickname'
          },
          {
            label: '来源用户 id',
            field: 'fromUid'
          },
          {
            label: '空投用户昵称',
            field: 'toNickname'
          },
          {
            label: '空投用户 id',
            field: 'toUid'
          },
          {
            label: '空投用户地址',
            field: 'toContractAddress'
          },
          {
            label: '空投用户邮箱',
            field: 'toEmail'
          },
          {
            label: '空投用户手机号',
            field: 'toPhone'
          },
          {
            label: '创建时间',
            field: 'createAt'
          },
          {
            label: '修改时间',
            field: 'updateAt'
          },
          {
            label: '创建的管理员用户名',
            field: 'createAdminUser'
          },
          {
            label: '修改的管理员用户名',
            field: 'updateAdminUser'
          },
          {
            label: '操作',
            slot: 'action',
            width: 150,
            fixed: 'right'
          }
        ],
        dialogVisible: false,
        data: {
          cover: '',
          qrCode: ''
        },
        infoSchema: [{
            label: '名称：',
            field: 'name'
          },
          {
            label: '封面图：',
            field: 'cover',
            type: 'img'
          },
          {
            label: '二维码：',
            field: 'qrCode',
            type: 'img'
          },
          {
            label: '链接类型：',
            field: 'linkType',
            type: 'radio',
            options: [{
              label: '无链接',
              value: 0
            }, {
              label: '网页链接',
              value: 10
            }]
          },
          {
            label: '跳转链接：',
            field: 'link',
            show: {
              relationField: 'linkType',
              value: 10
            }
          },
          {
            label: '权重：',
            field: 'weight'
          }
        ],
        page: {
          totalCount: 0,
          pageSize: 10,
          pageNum: 1
        },
        isDetail: false,
        isPreview: false,
        airDropId: '', // 当前空投项目 id
        formLabelWidth: '160px',
        isDialog: false,
        form: {
          tokenId: '',
          contractAddress: '',
		  price:''
        }
      }
    },
    computed: {},
    mounted() {
      this.airDropId = Number.parseInt(this.$route.params.airDropId)
      this.isPreview = this.$route.query.type === 'preview'
      this.status = this.$route.query.status
      this.getList()
    },
    methods: {
      async importWorksItem() {

      },
      onQueryChange(data) {
        console.log(data)
        this.query = data
        this.getList(true)
      },
      async preview(item) {
        const {
          status
        } = await this.$api.airDropResultPreviewCollection({
          airDropId: this.$route.params.airDropId
        })
        if (status.code === 0) {
          this.getList(true)
        }
      },
      async getList(isInit) {
        const params = {
          ...this.query,
          ...this.page,
          pageNum: isInit ? 1 : this.page.pageNum,
          airDropId: this.$route.params.airDropId
        }
        const {
          status,
          result
        } = await this.$api.airDropResultListCollection(params)
        if (status.code === 0) {
          this.tableData = result.list
          this.page.totalCount = result.totalCount
          this.page.pageSize = result.pageSize
          this.page.pageCount = result.pageCount
        }
      },
      currentChange(value) {
        this.page.pageNum = value
        this.getList()
      },
      deleteWorksItem(airDropUserId) {
        this.$confirm('删除后该项将在列表不可见', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const {
            status
          } = await this.$api.airDropAssetsDeleteCollection({
            airDropId: this.airDropId,
            airDropUserId
          })
          if (status.code === 0) {
            this.$message.success(status.msg)
            this.getList()
          }
        })
      },
      addWorksItem() {
        this.$prompt('请输入空投作品 TID', '新增空投作品', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(async ({
          value
        }) => {
          const data = {
            tid: value,
            airDropId: this.airDropId
          }
          const {
            status
          } = await this.$api.airDropAssetsAddCollection(data)
          if (status.code === 0) {
            this.$message.success(status.msg)
            this.getList()
          }
        })
      },
      async onUploadSuccess(response) {
        if (response.status.code === 0) {
          this.$message.success(response.status.msg)
        }
      },
      // 清空结果
      async clearPreview() {
        const {
          status
        } = await this.$api.airDropResultDeleteCollection({
          airDropId: this.airDropId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      },
      // 导出结果
      async exportResult() {
        const res = await this.$api.airDropResultExportCollection({
          airDropId: this.airDropId
        })
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '空投结果')
          this.$message.success('导出成功')
        }
      },
      // 执行空投
      async executeAirDrop() {
        const {
          status
        } = await this.$api.airDropExecuteProjectCollection({
          airDropId: this.airDropId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
        }
      },
      // 执行空投
      async executeAirDropItem(airDropRecordId) {
        const {
          status
        } = await this.$api.airDropExecuteCollection({
          airDropRecordId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      },
      async click_submit() {
        const {
          status
        } = await this.$api.airDropRecordAddCollection({
          airDropId: this.airDropId,
          contractAddress: this.form.contractAddress,
          tid: this.form.tokenId,
		  price:this.form.price
        })
        if (status.code === 0) {
			this.isDialog=false
          this.$message.success(status.msg)
          this.getList()
        }else{
			this.$message.error(status.msg)
		}
      },
      openDel(airDropRecordId) {
        console.log(airDropRecordId)
        this.$confirm('是否确认删除该空投记录', '删除空投记录 ', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delAirDropItem(airDropRecordId)
        }).catch(() => {

        });
      },
      async delAirDropItem(airDropRecordId) {
        const {
          status
        } = await this.$api.airDropRecordDeleteCollection({
          airDropId: this.airDropId,
          airDropRecordId
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      },
		async downloadTemplate () {
		  const { status, result } = await this.$api.userCenterDownLoadTemplate({
			templateTag:"AIR_DROP_COLLECTION_RECORD_IMPORT"
		  })
		  if (status.code === 0) {
			window.open(result.emailsTemplateUrl, '_blank')
		  }
		},
		// 空投记录导入
		async importTemplate(data) {
		  console.error(data)
			const {
				status,
				result
			} = await this.$api.airDropImportCollection({
				importUrl: data.result?.url,
				airDropId:data.result?.id
			})
			if (status.code === 0) {
				this.$message.success(status.msg)
        this.getList()
			}
		},
    }
  }
</script>

<style lang="scss" scoped>
  .buttons {
    display: flex;
    justify-content: end;
    margin-bottom: 20px;
	e-button{
		width:100px;
	}
  }

  ::v-deep .uploader{
	  height:40px !important;
	  margin-top:0 !important;
	  .el-upload{
		   .el-button--mini{
			   height:40px !important;
		   }
	  }
  }
</style>
