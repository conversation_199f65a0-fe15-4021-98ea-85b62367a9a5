<template>
  <d2-container class="page">
    <div class="form">
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item label="作品所属列表：">
          <el-input v-model="themeName" disabled></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-radio-group v-model="form['way']">
            <el-radio :label="1">单作品添加</el-radio>
            <el-radio :label="2">批量添加</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.way === 1">
          <el-form-item label="Token ID：" required>
            <el-input v-model="form.tid" placeholder="请输入作品token id"></el-input>
          </el-form-item>
          <el-form-item label="IP图片：">
            <el-upload
              :action="action"
              :headers="token"
              list-type="picture-card"
              :file-list="fileList"
              :on-success="handlePicSuccess"
            >
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">
                <div>只能上传JPG/PNG/GIF格式文件</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="作品名称：">
            <el-input v-model="form.title" placeholder="选填，未填写时自动取token id信息"></el-input>
          </el-form-item>
          <el-form-item label="排序：">
            <el-input v-model="form.weight" placeholder="选填，未填写时自动取作品算法排序"></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="表单上传：" required>
            <el-upload
              :action="excelAction"
              :headers="token"
              :file-list="fileList"
              :on-success="handleExcelSuccess"
            >
              <el-button >点击上传</el-button>
              <div slot="tip" class="el-upload__tip">
                <el-button  type="text" @click="downloadTemp">表单模板</el-button>
              </div>
            </el-upload>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'WorkThemeGoodAdd',
  computed: {
    themeName () {
      return this.$route.query.themeName
    },
    themeId () {
      return this.$route.query.themeId
    }
  },
  data () {
    return {
      form: {
        way: 1
      },
      fileList: [],
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      excelAction:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadExcel',
      token: { AdminAuthorization: localStorage.getItem('usertoken') } // 设置上传的请求头部
    }
  },
  watch: {
    'form.way': function (val) {
      this.form = {
        way: val
      }
      this.fileList = []
    }
  },
  methods: {
    /**
     * 图片上传成功回调
     * @param res 成功返回的数据
     * @param file 图片文件
     */
    handlePicSuccess (res, file) {
      if (res.status.code === 0) {
        this.form.photo = res.result.url
        this.fileList = [file]
      } else {
        this.$message.error(res.status.msg)
        this.fileList = []
      }
    },
    /**
     * 上传excel成功回调
     * @param res 成功返回的数据
     * @param file excel文件
     */
    handleExcelSuccess (res, file) {
      if (res.status.code === 0) {
        this.form.excelUrL = res.result.url
        this.fileList = [file]
      } else {
        this.$message.error(res.status.msg)
        this.fileList = []
      }
    },
    /**
     * 下载表单模板
     * @method
     */
    async downloadTemp () {
      const { result: { emailsTemplateUrl } } = await this.$api.downLoadTemplateExcel({
        templateTag: 'MARKET_THEME_GOODS'
      })
      await window.open(emailsTemplateUrl)
    },
    async onSubmit () {
      const {
        themeId,
        form,
        $message
      } = this
      await this.$api.themeAddGoods({
        ...form,
        themeId
      })
      $message.success('添加成功')
      this.form = {
        way: 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form {
  width: 600px;
  margin: 0 auto;
}
</style>
