<template>
  <d2-container class="page">
    <div class="header">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="作品名称：">
          <el-input v-model="formInline.title" placeholder="作品名称"></el-input>
        </el-form-item>
        <el-form-item label="token ID：">
          <el-input v-model="formInline.tid" placeholder="token ID"></el-input>
        </el-form-item>
        <el-form-item label="显示状态：">
          <el-select  v-model="formInline.status" placeholder="显示状态">
            <el-option label="显示" value="1"></el-option>
            <el-option label="隐藏" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="action">
        <el-button type="primary" @click="addGood" v-if="!(tableData.length && tableData[0].goodsType)">添加作品</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button type="primary" @click="onSearch">查询</el-button>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 作品图片-->
            <template v-if="item.prop === 'photo'">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row[item.prop]"
                :preview-src-list="[scope.row[item.prop]]">
              </el-image>
            </template>
            <template v-else-if="item.prop === 'goodsType'">
              {{ +scope.row[item.prop] ? '规则获取' : '后台创建' }}
            </template>
            <template v-else-if="item.prop === 'status'">
              {{ +scope.row[item.prop] ? '显示' : '隐藏' }}
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="80">
          <template slot-scope="scope">
            <el-button type="text"  @click="toDetailPage(scope.row)">查看</el-button>
            <el-button v-if="!scope.row.goodsType" type="text"  @click="onHide(scope.row)"> {{ +scope.row.status ? '隐藏' : '显示' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'WorkThemeGoodAdd',
  computed: {
    id () {
      return this.$route.query.id
    }
  },
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'title',
          label: '作品名称',
          width: '200'
        },
        {
          prop: 'tid',
          label: 'token ID',
          width: '200'
        },
        {
          prop: 'photo',
          label: '作品图片',
          width: '150'
        },
        {
          prop: 'price',
          label: '作品价格'
        },
        {
          prop: 'status',
          label: '显示状态'
        },
        {
          prop: 'goodsType',
          label: '获取来源'
        },
        {
          prop: 'weight',
          label: '排序'
        }
      ],
      formInline: {
        user: '',
        region: ''
      },
      pageNum: 1,
      pageSize: 15,
      total: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * @method
     */
    async getList () {
      const {
        pageNum,
        pageSize,
        id: themeId
      } = this
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.themeGoodsList({
        themeId,
        ...this.formInline,
        pageNum,
        pageSize
      })
      this.total = totalCount
      this.tableData = list
    },
    /**
     * 跳转到详情页
     * @method
     * @param row {Object} 行数据
     */
    toDetailPage (row) {
      this.$router.push({
        name: 'GoodDetail',
        query: {
          tid: row.tid
        }
      })
    },
    /**
     * 重置搜索条件
     * @method
     */
    onReset () {
      this.formInline = {}
      this.pageNum = 1
      this.getList()
    },
    /**
     * 搜索
     * @method
     */
    onSearch () {
      this.pageNum = 1
      this.getList()
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 添加作品
     */
    addGood () {
      this.$router.push({
        name: 'WorkThemeGoodAdd',
        query: {
          themeId: this.id,
          themeName: this.$route.query.themeName
        }
      })
    },
    /**
     * 隐藏作品
     * @param row {Object} 行数据
     */
    async onHide (row) {
      await this.$api.themeGoodsStatus({
        idListStr: JSON.stringify([row.id]),
        status: row.status ? 0 : 1
      })
      this.$message.success('修改成功')
      await this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
