<template>
  <d2-container class="page">
    <div class="header">
      <el-button  type="primary" @click="toEdit('add')">创建主题页</el-button>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column
          label="编号"
          type="index"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          v-for="(item, index) in tableList"
          :prop="item.prop"
          :label="item.label"
          align="center"
          :key="index"
          :width="item.width || 100">
          <template slot-scope="scope">
            <!-- 作品库-->
            <template v-if="item.prop === 'goodsCount'">
              <el-button type="text"  @click="toGoodList(scope.row)">{{ scope.row[item.prop] }}</el-button>
            </template>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100">
          <template slot-scope="scope">
            <el-button type="text"  @click="toEdit('read',scope.row)">查看</el-button>
            <el-button type="text"  @click="toEdit('edit',scope.row)">编辑</el-button>
            <el-button type="text"  @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        :current-page.sync="pageNum"
        background
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'WorkTheme',
  data () {
    return {
      tableData: [],
      tableList: [
        {
          prop: 'themeName',
          label: '主题页名称',
          width: '150'
        },
        {
          prop: 'goodsCount',
          label: '作品数'
        },
        {
          prop: 'createdAt',
          label: '创建时间'
        },
        {
          prop: 'updatedAt',
          label: '编辑时间'
        },
        {
          prop: 'remark',
          label: '主题页备注',
          width: '200'
        }
      ],
      pageNum: 1,
      pageSize: 15,
      total: 0
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    /**
     * 获取列表数据
     * @method
     * @return {Promise<void>}
     */
    async getList () {
      const {
        result: {
          list,
          totalCount
        }
      } = await this.$api.themeList({
        pageNum: this.pageNum,
        pageSize: this.pageSize
      })
      this.tableData = list
      this.total = totalCount
    },
    /**
     * 当前页发生变化时会触发该事件
     * @method
     * @param val {Number} 当前页码
     */
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },
    /**
     * 跳转编辑页面
     * @param type  类型
     * @param row 行数据
     */
    toEdit (type, row) {
      this.$router.push({
        name: 'EditTheme',
        query: {
          id: row?.id,
          type: type
        }
      })
    },
    /**
     * 删除
     * @param row 行数据
     */
    handleDelete (row) {
      this.$confirm(`确认删除${row.themeName}？`)
        .then(async () => {
          await this.$api.deleteTheme({
            id: row.id
          })
          this.$message.success('删除成功')
          await this.getList()
        })
        .catch(_ => {
        })
    },
    toGoodList (row) {
      const {
        id,
        themeName
      } = row
      this.$router.push({
        name: 'WorkThemeGoodList',
        query: {
          id,
          themeName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.table {
  margin-bottom: 50px;
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
