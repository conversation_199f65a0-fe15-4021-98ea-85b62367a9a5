<template>
  <d2-container class="page">
    <div class="form">
      <el-form ref="form">
        <div v-for="(item,index) in themeList" :key="index">
          <el-form-item :label="`主题页${index + 1}名称：`" required label-width="130px">
            <el-input v-model="item.title" @blur="updateLink(index)" :disabled="type === 'read'">
              <el-button slot="append" icon="el-icon-delete" @click="deleteItem(index)"
                         v-if="type !== 'read'"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item :label="`H5链接：`" required label-width="130px">
            <el-input v-model="item.link" disabled></el-input>
          </el-form-item>
          <el-form-item :label="`APP链接：`" required label-width="130px">
            <el-input v-model="item.nativeLink" disabled></el-input>
          </el-form-item>
        </div>
        <el-form-item label="主题页备注：" label-width="130px">
          <el-input v-model="remark" type="textarea" rows="4" :disabled="type === 'read'"></el-input>
        </el-form-item>
        <el-form-item label="" label-width="130px" v-if="type !== 'read'">
          <el-button type="primary" class="button" @click="addTheme">新增主题页名称及链接</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer" v-if="type !== 'read'">
      <el-button type="primary" class="button" @click="submit">提交</el-button>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'EditTheme',
  computed: {
    type () {
      return this.$route.query.type
    }
  },
  data () {
    return {
      detail: {},
      remark: '',
      themeList: [
        {
          title: '',
          link: '',
          nativeLink: ''
        }
      ],
      id: ''
    }
  },
  async mounted () {
    /**
     *  如果有id 则获取详情
     */
    this.id = this.$route.query.id
    if (this.id) {
      await this.getDetail()
    }
    if (this.type === 'add') {
      const { result: { id } } = await this.$api.getThemeId()
      this.id = id
    }
  },
  methods: {
    /**
     * 获取详情
     * @method
     */
    async getDetail () {
      const { result } = await this.$api.themeDetail({
        id: this.id
      })
      this.themeList = result.infoList
      this.remark = result.remark
    },
    /**
     * 生成链接
     * @method
     * @param {Number} index 索引
     */
    updateLink (index) {
      const { themeList } = this
      const title = themeList[index].title
      if (title) {
        themeList[index].link = `https://www.nftcn.com.cn/h5/#/pagesA/project/mall/selectedTopics?title=${title}&marketSpecialId=${this.id}`
        themeList[index].nativeLink = `nftcn://mall_category?title=${title}&typeStr=${this.id}`
      }
    },
    /**
     * 新增主题页名称及链接
     * @method
     */
    addTheme () {
      this.themeList.push({
        title: '',
        link: '',
        nativeLink: ''
      })
    },
    /**
     * 删除主题页名称及链接
     * @param index {Number} 删除主题页索引
     */
    deleteItem (index) {
      if (this.themeList.length > 1) {
        this.$confirm(`确认删除主题页${index + 1}？`)
          .then(() => {
            this.themeList.splice(index, 1)
          })
          .catch(_ => {
          })
      } else {
        this.$message.error('至少保留一个主题页')
      }
    },
    /**
     * 提交
     * @method
     */
    async submit () {
      const {
        themeList,
        type,
        id
      } = this
      // 去除空数据 条件：主题页1名称
      const removeEmpty = themeList.filter(item => item.title)
      if (removeEmpty.length === 0) {
        this.$message.error('请至少填写一个主题页名称及链接')
        return
      }
      /**
       * 根据是否有ID判断是新增还是修改
       *  新增：请求接口获取id method = 'updateTheme'
       *  修改：直接请求更新接口 method = 'updateTheme'
       */
      const method = type === 'add' ? 'addTheme' : 'updateTheme'
      await this.$api[method]({
        id,
        remark: this.remark,
        themeInfo: JSON.stringify(removeEmpty)
      })
      this.$message.success(type === 'add' ? '新增成功' : '修改成功')
      type === 'add' && await this.$router.push({
        name: 'WorkTheme'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form {
  width: 600px;
  margin: 0 auto 50px;

  .button {
    width: 100%;
  }
}

.footer {
  z-index: 10;
  background: white;
  width: calc(100% - 280px);
  padding: 10px 0;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
