<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item label="入口名称:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.name"
          placeholder="请输入入口名称"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="入口icon:" :label-width="formLabelWidth" required>
        <el-upload
          :action="action"
          :headers="token"
          list-type="picture-card"
          :on-success="handlePicSuccess"
          :class="{ hide: hideUpload_introduce }"
          :on-change="handleIntroduceUploadHide"
          :on-remove="handleIntroduceRemove"
          :file-list="fileListImg"
          :before-upload="beforeAvatarUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item label="链接类型:" :label-width="formLabelWidth" required>
        <el-radio-group v-model="form.linkType" @change="radioChange">
          <el-radio v-if="businessLine !== 10" :label="10">自定义链接</el-radio>
          <el-radio :label="20">网页链接</el-radio>
        </el-radio-group>
        <div style="color: red; fontsize: 12px">只支持不需要登录的链接</div>
      </el-form-item>
      <el-form-item
        label="跳转路径h5以及网页跳转:"
        :label-width="formLabelWidth"
        v-if="form.linkType == 10 || form.linkType == 20"
        required
      >
        <el-input
          v-model="form.link"
          placeholder="请输入跳转路径h5以及网页跳转"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="IOS 安卓原生跳转路径："
        :label-width="formLabelWidth"
        v-if="form.linkType == 10"
      >
        <el-input
          v-model="form.nativeLink"
          placeholder="原生路径不存在无需填写，系统自动使用'H5以及网页跳转路径'"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="权重:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.weight"
          placeholder="请输入权重"
          clearable
          style="width: 500px"
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注:" :label-width="formLabelWidth">
        <el-input
          v-model="form.remark"
          placeholder="请输入备注"
          clearable
          style="width: 500px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(return_click)"
          v-if="!look"
          >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit_click)"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'editKingKong',
  data () {
    return {
      form: {},
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      fileListImg: [],
      kingKongId: '',
      formLabelWidth: '200px',
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload_introduce: false,
      limitCount: 1,
      businessLine: null
    }
  },
  mounted () {
    this.kingKongId = this.$route.query.edit_id
    this.businessLine = Number.parseInt(this.$route.query.businessLine) || ''
    if (this.businessLine === 10) {
      this.form.linkType = 20
    }
    this.detailKingkong()
  },
  methods: {
    radioChange () {
      this.form.link = ''
      this.form.nativeLink = ''
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.icon = res.result.url
    },
    handleIntroduceUploadHide (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    beforeAvatarUpload (file) {
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.form.icon = ''
    },
    // 编辑详情
    async detailKingkong () {
      const res = await this.$api.detailKingkong({
        kingKongId: this.kingKongId
      })
      this.form = res.result
      if (res.result.icon !== '') {
        this.hideUpload_introduce = true
        this.fileListImg = [
          {
            name: '',
            url: res.result.icon
          }
        ]
      }
    },
    // 取消返回
    return_click () {
      this.$router.back()
    },
    // 新增编辑确定
    submit_click () {
      this.$confirm(
        '提交保存后,该入口将显示在客户端,确认保存？',
        '确认提交保存',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.submitClick()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async submitClick () {
      await this.$api.updateKingkong({
        kingKongId: this.kingKongId,
        name: this.form.name,
        linkType: this.form.linkType,
        link: this.form.link,
        nativeLink: this.form.nativeLink,
        icon: this.form.icon,
        weight: this.form.weight,
        remark: this.form.remark
      })
      this.$message.success('修改成功')
      this.$router.push({
        name: 'kingKong',
        query: {
          businessLine: this.businessLine
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>
