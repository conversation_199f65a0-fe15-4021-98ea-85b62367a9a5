<template>
  <d2-container class="page">
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="入口名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="linkType" label="链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
          <el-tag v-if="scope.row.linkType == '30'">自定义跳转详情</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="link"
        label="跳转路径h5以及网页跳转"
        align="center"
      ></el-table-column>
       <el-table-column
         v-if="businessLine !== 10"
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column prop="icon" label="入口图片" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image style="width: 100px; height: 100px" :src="scope.row.icon">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="weight"
        label="权重"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'kingKong',
  data () {
    return {
      tableData: [],
      total: 1,
      businessLine: null
    }
  },
  mounted () {
    this.businessLine = Number.parseInt(this.$route.query.businessLine) || ''
    this.getList()
  },
  methods: {
    // 查询列表
    async getList (page) {
      const res = await this.$api.kingKongList({
        pageNum: page,
        pageSize: 15,
        businessLine: this.businessLine
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击编辑
    async audit_click (val) {
      this.$router.push({
        name: 'editKingKong',
        query: {
          edit_id: val.id,
          businessLine: this.businessLine
        }
      })
    }
  }
}
</script>

<style>
</style>
