<template>
  <d2-container class="page">
    <div class="day-input">
      请输入天数：
      <el-input
        class="input"
        type="number"
        placeholder="请输入图表数据天数"
        v-model.number="day"
        size="mini"
        @change="timeChange"
      ></el-input>
    </div>
    <div ref="lineChartPV" :style="`width: ${width}px; height:400px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
    <div ref="lineChartUV" :style="`width: ${width}px; height:400px;zoom:${zoom};transform:scale(${1/zoom});transform-origin:0 0`"></div>
  </d2-container>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'index',
  data () {
    return {
      lineChartPV: null,
      lineChartUV: null,
      day: 30,
	  zoom:1,
    width:""
    }
  },
  mounted () {
   this.zoom = 1 / document.body.style.zoom
	   window.addEventListener("resize", () => {
		this.zoom = 1 / document.body.style.zoom
	  })
    this.initLineChart()
    this.getLineChart()
  },
  methods: {
    timeChange () {
      this.getLineChart()
    },
    async getLineChart () {
      const { status, result } = await this.$api.getCharts({ limit: this.day })
      if (status.code === 0) {
        this.lineChartPV.setOption({
          xAxis: {
            data: result.websiteStaticsVolumeVOList.map(item => item.time)
          },
          series: [
            {
              name: 'PV',
              type: 'line',
              stack: 'Total',
              data: result.websiteStaticsVolumeVOList.map(item => item.pv)
            }
          ],
        })
        this.lineChartUV.setOption({
          xAxis: {
            data: result.websiteStaticsVolumeVOList.map(item => item.time)
          },
          series: [
            {
              name: 'UV',
              type: 'line',
              stack: 'Total',
              data: result.websiteStaticsVolumeVOList.map(item => item.uv)
            }
          ]
        })
      }
    },
    initLineChart () {
      this.lineChartPV = echarts.init(this.$refs.lineChartPV)
      this.lineChartUV = echarts.init(this.$refs.lineChartUV)
      this.lineChartUV.setOption({
        title: {
          text: 'UV 变化分析图'
        },
        legend: {
          data: ['UV']
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      })
      this.lineChartPV.setOption({
        title: {
          text: 'PV 变化分析图'
        },
        legend: {
          data: ['PV']
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  .day-input {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    width: 300px;
    .input {
      width: 200px;
    }
  }

  //.logo {
  //  width: 120px;
  //}
  //.btn-group {
  //  color: $color-text-placehoder;
  //  font-size: 12px;
  //  line-height: 12px;
  //  margin-top: 0px;
  //  margin-bottom: 20px;
  //  .btn-group__btn {
  //    color: $color-text-sub;
  //    &:hover {
  //      color: $color-text-main;
  //    }
  //    &.btn-group__btn--link {
  //      color: $color-primary;
  //    }
  //  }
  //}
}
</style>
