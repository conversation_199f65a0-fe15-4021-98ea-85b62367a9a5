<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #banner_theme>
        <div class="banner_flex">
          <div class="banner_ul">
            <div class="li" :class="{'active':index==bannerSun}" v-for="(item,index) in bannerList"
              @click="checkendBanner(item,index)">
              <img :src="item.src" alt="" srcset="">
            </div>
          </div>
          <div class="yulan">
            <div class="canvas" ref="canvas" :style="{backgroundImage: `url(${bannerInfo.src})`}">
              <div class="font">
                <div class="title"
                  :style="{marginTop:`${bannerInfo.titleMarginTop}px`,color:`${bannerInfo.titleColor}`}">
                  {{formData.title}}</div>
                <div class="subtitle"
                  :style="{marginTop:`${bannerInfo.subTitleMarginTop}px`,color:`${bannerInfo.subTitleColor}`}">
                  {{formData.subTitle}}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #maxMergeNumEveryUserHide>
        <el-row justify="start">
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.maxEcRoundStart"></el-input>
            </div>
          </el-col>
          <el-col :span="1">
            <div class="grid-content bg-purple" style="text-align:center;">-</div>
          </el-col>
          <el-col :span="2">
            <div class="grid-content bg-purple">
              <el-input placeholder="" v-model="formData.maxEcRoundEnd"></el-input>
            </div>
          </el-col>
          <el-col :span="10">
            <div style="color:#F56C6C;margin-left:10px">
              <!-- <div>选择tid导入，定时开始时间必须是当前时间后5分钟</div> -->
            </div>
          </el-col>
        </el-row>
      </template>
      <template #rebuild-collection-series>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="collectionTableSchemaSeries"
              :table-data="collectionTableDataSeries" :showIndex="false" >
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItemSeries()" type="primary" size="mini">新增</el-button>
              </template>
              <template #SchemaSeries="scope">
                <div class="" >
                  <el-autocomplete style="width:260px;margin:10px 0px;" v-model="scope.row.iceProductCidView"
                    :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
                  </el-autocomplete>
                </div>
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, collectionTableDataSeries)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template>
      <template #iceOption>
          <div class="margin-left-active">
            <div class="li" v-for="(item,index) in formData.iceOptionList">
                <div class="title_pz" >
                  质押期选项{{index+1}}
                  <el-button v-if="index>0" style="margin-left:30px;" type="danger" size="mini" plain @click="deleteItem(index,formData.iceOptionList)">删除该选项</el-button>
                </div>
                  <common-form :is-edit="!isDetail"  :data="item" :schema="formSchemaIceOpt" label-width="300px">
                      <template #pledge24ProduceImgNum="scope">
                            <el-row justify="start">
                              <el-col :span="3">
                                <div class="grid-content bg-purple">
                                  <el-input placeholder="" v-model="item.pledge24ProduceImgNumRoundStart"></el-input>
                                </div>
                              </el-col>
                              <el-col :span="1">
                                <div class="grid-content bg-purple" style="text-align:center;">-</div>
                              </el-col>
                              <el-col :span="3">
                                <div class="grid-content bg-purple">
                                  <el-input placeholder="" v-model="item.pledge24ProduceImgNumRoundEnd"></el-input>
                                </div>
                              </el-col>
                            </el-row>
                      </template>
                      <template #pledge-collection-series>
                        <el-row>
                          <el-col :span="20">
                            <common-table :is-edit="!isDetail" :table-schema="pledgeTableSchema"
                              :table-data="item.IceProduceImgs" :showIndex="false" >
                              <template v-if="!isDetail" #action-header>
                                <el-button @click="addPledgeItemSeries(item.IceProduceImgs)" type="primary" size="mini">新增</el-button>
                              </template>
                              <template #SchemaSeries="scope">
                                <div class="" >
                                  <el-autocomplete style="width:260px;margin:10px 0px;" v-model="scope.row.iceProductCidView"
                                    :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
                                  </el-autocomplete>
                                </div>
                              </template>
                              <template #outputProbability="scope" >
                                <div  v-show="item.outputRule == '0'">
                                   <el-input   type="number" placeholder="" style="width:60px;" v-model="scope.row.outputProbability" ></el-input>%
                                </div>
                              </template>
                              <template #pledge24ProduceImgNum="scope">
                                <el-row justify="start">
                                  <el-col :span="8">
                                    <div class="grid-content bg-purple">
                                      <el-input placeholder="" v-model="scope.row.pledge24ProduceImgNumRoundStart"></el-input>
                                    </div>
                                  </el-col>
                                  <el-col :span="2">
                                    <div class="grid-content bg-purple" style="text-align:center;">-</div>
                                  </el-col>
                                  <el-col :span="8">
                                    <div class="grid-content bg-purple">
                                      <el-input placeholder="" v-model="scope.row.pledge24ProduceImgNumRoundEnd"></el-input>
                                    </div>
                                  </el-col>
                                </el-row>
                              </template>
                              <template v-if="!isDetail" #action="scope">
                                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, item.IceProduceImgs)">
                                  <el-button slot="reference" type="text">删除</el-button>
                                </el-popconfirm>
                              </template>
                            </common-table>
                          </el-col>
                        </el-row>
                      </template>
                  </common-form>
            </div>
          </div>
          <el-button  type="primary" plain @click="addIceItem()">添加质押期选项</el-button>
      </template>
     <!-- <template #reminder>
        <div style="color:#F56C6C">
          <div>使用规则：</div>
          <div>1.若存在小熔炉, 则必须上传用户资格, 小熔炉资格次数必填, 盲盒内作品excel "是否小熔炉作品"一列必须有"是"</div>
          <div>2.有小熔炉资格次数的用户, 在合成作品时, 优先取 小熔炉作品作为奖品, 若小熔炉作品全部发放完了, 则取大熔炉内作品作为奖品</div>
          <div>3.若大小熔炉均需要资格时, 同一个用户的大熔炉资格次数必须>=小熔炉资格次数</div>
          <div>4.举例, A用户有大熔炉资格次数2次, 小熔炉资格次数1次,
            当A用户第1次合成时, 取小熔炉内作品作为奖品, A用户剩余大熔炉次数为1, 小熔炉次数为0,
            当A用户第2次合成时, 取大熔炉内作品作为奖品, A用户剩余大熔炉次数为0, 小熔炉次数为0,
            当A用户第3次合成时, 没有次数, 合成失败</div>
        </div>
      </template>
      <template #activityShip_msg>
        <div style="color:#F56C6C">
          <div>【开始活动之前10分钟】</div>
        </div>
      </template> -->
    </common-form>
  </d2-container>
</template>

<script>
  import CommonForm from '@/components/CommonForm'
  import CommonTable from '@/components/CommonTable'
  import FileUploader from '@/components/FileUploader'
  import html2canvas from 'html2canvas';
  import {
    mapActions
  } from 'vuex'
  import {
    downloadBlob
  } from '@/utils/helper'
  import {
    uploadImgToOss
  } from '@/api/ossCenter'
  export default {
    name: 'icebound',
    components: {
      CommonForm,
      CommonTable,
      FileUploader
    },
    data() {
      const validateCollectionSeries = (rule, value, callback) => {
        if (this.collectionTableDataSeries.length <= 0) {
          callback(new Error('请添加数据'))
        } else {
          this.collectionTableDataSeries.forEach(item => {
            if (!item.iceNum) {
              callback(new Error('请输入每次熔炼需要数量'))
            } else if (!item.iceProductCidView) {
              callback(new Error('请选择系列'))
            }
          })
          callback()
        }
      }
      return {
        isDetail: false, // 详情
        activityNo: null, // 活动编号
        formData: {
          backgroundImage: 'https://cdn-lingjing.nftcn.com.cn/image/20241021/872c88a8ab54b91731898cc8a3a0bf8c_750x1062.png',
          type: 'icebound',
          topMargin: '65',
          color: '#79e0ef',
          borderBg: 'https://cdn-lingjing.nftcn.com.cn/image/20240910/5807ac340c2fb714f87d148cc8ba668d_756x328.png',
          butBg: 'https://cdn-lingjing.nftcn.com.cn/image/20240910/84c76a835053776d36d437a98abc15b8_170x60.png',
          isTiming: 1,
          startTime: ['2023-01-26 20:37:58', '2023-01-27 20:37:58'],
          activityNewShow: 1,
          activityNewBannerImage: "",
          show_set: '0',
          subTitle: "",
          activityNewBannerType: "1",
          iceOptionList:[{
              id:1,
              mainPledgeNum:0,
              outputRule:1,
              IceProduceImgs:[{
                iceProductCidView:'',
                produceAutoSell:1,
                pledgeExpireAutoSell:1,
                pledge24ProduceImgNumRoundStart:"",
                pledge24ProduceImgNumRoundEnd:"",
                outputProbability:""
              }]
          }],
          link:'https://www.nftcn.com/'
        },
        formSchema: [{
            type: 'select',
            label: '活动类型：',
            placeholder: '请选择活动类型',
            field: 'type',
            disabled: true,
            options: [{
                label: '拉新',
                value: 'INVITE_NEW'
              },
              {
                label: '熔炉',
                value: 'REBUILD'
              },
              {
                label: '合成',
                value: 'MERGE'
              },
              {
                label: '抽奖',
                value: 'GET_REWARD'
              },
              {
                label: '冰封',
                value: 'icebound'
              }
            ],
            rules: [{
              required: true,
              message: '请选择活动类型',
              trigger: 'change'
            }]
          },
          {
            type: 'input',
            label: '活动主标题：',
            placeholder: '请输入主标题',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入主标题',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '副标题：',
            placeholder: '请输入副标题',
            field: 'subTitle',
            rules: [{
              required: true,
              message: '请输入副标题',
              trigger: 'blur'
            }]
          },
          {
            type: 'switch',
            label: '更多高级设置：',
            field: 'show_set',
          },
          {
            label: '活动banner：',
            field: 'activityNewBannerType',
            type: 'radio',
            options: [{
              label: '推荐',
              value: "1"
            }, {
              label: '自定义',
              value: "0"
            }]
          },
          {
            slot: 'banner_theme',
            show: {
              relationField: 'activityNewBannerType',
              value: "1"
            },
          },
          {
            type: 'img',
            placeholder: '请选择活动banner',
            field: 'activityNewBannerImage',
            rules: [{
              required: true,
              message: '请选择活动banner',
              trigger: 'change'
            }],
            show: {
              relationField: 'activityNewBannerType',
              value: "0"
            },
          },
          {
            type: 'img',
            label: '活动背景图片：',
            placeholder: '请选择活动背景图片',
            field: 'backgroundImage',
            rules: [{
              required: true,
              message: '请选择活动背景图片',
              trigger: 'change'
            }],
            show: {
              relationField: 'show_set',
              value: '1'
            },
          },
          {
            type: 'img',
            label: '质押选项背景图：',
            placeholder: '质押选项背景图',
            field: 'borderBg',
            rules: [{
              required: true,
              message: '质押选项背景图',
              trigger: 'change'
            }],
            show: {
              relationField: 'show_set',
              value: '1'
            },
          },
          {
            type: 'input',
            label: '活动边框距离顶部距离：',
            placeholder: '请输入活动边框距离顶部距离(默认值为500)',
            field: 'topMargin',
            rules: [{
              required: true,
              message: '请输入活动边框距离顶部距离',
              trigger: 'change'
            }],
            show: {
              relationField: 'show_set',
              value: '1'
            },
          },
          {
            type: 'img',
            label: '封存按钮图片：',
            placeholder: '封存按钮图片',
            field: 'butBg',
            rules: [{
              required: true,
              message: '封存按钮图片',
              trigger: 'change'
            }],
            show: {
              relationField: 'show_set',
              value: '1'
            },
          },
          {
            type: 'input',
            label: '最多质押数量:',
            placeholder: '请输入最多质押数量',
            field: 'maxEcNum',
            // rules: [{ required: true, message: '请输入最多质押数量', trigger: 'blur' }]
          },
          {
            type: 'number-input',
            label: '每人最多质押几次(明说):',
            placeholder: '请输入每人最多质押几次',
            field: 'maxTimes',
            // rules: [{ required: true, message: '请输入最多兑换数量', trigger: 'blur' }]
          },
          {
            type: 'number-input',
            label: '每人最多质押几次:(隐藏)',
            placeholder: '请输入每人最多质押几次',
            slot: 'maxMergeNumEveryUserHide',
            // rules: [{ required: true, message: '请输入最多兑换数量', trigger: 'blur' }]
          },
          {
            slot: 'yunying_msg',
            label: '',
          },
          {
            type: 'textarea',
            label: '质押规则：',
            placeholder: '请输入质押规则',
            field: 'compositionRule',
            rules: [{
              required: true,
              message: '请输入质押规则',
              trigger: 'blur'
            }],
          },
          {
            type: 'colorPicker',
            label: '活动规则字体颜色：',
            placeholder: '请填写活动规则字体颜色',
            field: 'color',
            show: {
              relationField: 'show_set',
              value: '1'
            },
          },
          {
            label: '',
            field: 'isShow',
            slot: 'ddd'
          },
          {
            type: 'datetimerange',
            label: '活动时间：',
            field: 'startTime',
            // rules: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
            pickerOptions: {
              disabledDate: (time) => {
                return time.getTime() < Date.now() - 86400000
              }
            },

          },
          {
            label: '是否立即同步到金刚区“活动”：',
            field: 'activityNewShow',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }]
          },
          {
            slot: 'rebuild-collection-series',
            label: '可冰封材料：',
            rules: [{
              required: true,
              validator: validateCollectionSeries,
              trigger: 'blur'
            }],
          },
          {
            slot:'iceOption',
            label:'',
          },
          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ],
        bannerList: [{
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/1.jpg",
          titleMarginTop: 14,
          titleColor: "#6DE5FE",
          subTitleMarginTop: 20,
          subTitleColor: "#fff"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/2.jpg",
          titleMarginTop: 14,
          titleColor: "#fff",
          subTitleMarginTop: 24,
          subTitleColor: "#fff"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/3.jpg",
          titleMarginTop: 15,
          titleColor: "#2E1C6D",
          subTitleMarginTop: 15,
          subTitleColor: "#2E1C6D"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/4.jpg",
          titleMarginTop: 15,
          titleColor: "#2569B9",
          subTitleMarginTop: 15,
          subTitleColor: "#2569B9"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/5.jpg",
          titleMarginTop: 15,
          titleColor: "#fff",
          subTitleMarginTop: 36,
          subTitleColor: "#fff"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/6.jpg",
          titleMarginTop: 0,
          titleColor: "#fff",
          subTitleMarginTop: 7,
          subTitleColor: "#fff"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/7.jpg",
          titleMarginTop: 8,
          titleColor: "#fff",
          subTitleMarginTop: 20,
          subTitleColor: "#fff"
        }, {
          src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/8.jpg",
          titleMarginTop: 16,
          titleColor: "#fff",
          subTitleMarginTop: 28,
          subTitleColor: "#fff"
        }],
        bannerSun: 0,
        bannerInfo: {
            src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/icebound/1.jpg",
            titleMarginTop: 14,
            titleColor: "#6DE5FE",
            subTitleMarginTop: 20,
            subTitleColor: "#fff"
        },
        bannerUrl: "",
        formSchemaIceOpt:[
          {
            label: '质押时长（天）:',
            field: 'pledgeDay',
            type: 'number-input',
            rules: [{ required: true, message: '请输入质押时长（天）', trigger: 'blur' }]
          },
          {
            label: '质押最大数量',
            field: 'pledgeMaxNum',
            type: 'number-input',
             rules: [{ required: true, message: '质押最大数量', trigger: 'blur' }]
          },
          {
            label: '产出材料：​​​',
            field: 'outputRule',
            type: 'radio',
            options: [{
              label: '和',
              value: 1
            }, {
              label: '或',
              value: 0
            }],
            rules: [{ required: true, message: '请选择产出材料', trigger: 'blur' }]
          },
          {
            slot: 'pledge-collection-series',
            label: '',
            rules: [{
              required: true,
              validator: validateCollectionSeries,
              trigger: 'blur'
            }],
          },
          {
            type: 'number-input',
            label: '主力质押多少份：​​​',
            field: 'mainPledgeNum',
             rules: [{ required: true, message: '请输入主力质押多少份', trigger: 'blur' }]
          },
        ],
        collectionTableSchemaSeries: [ // 收藏表格架构
          {
            label: '系列',
            slot: 'SchemaSeries',
            field:'iceProductCid',
            width: 200,
          },
          {
            label: '数量',
            field: 'iceNum',
            type: 'input',
            width: 100
          },
          {
            label: '操作',
            slot: 'action',
            width: '170px',
            headerSlot: 'action-header',
            width: 100
          }
        ],
        collectionTableDataSeries:[{
          iceProductCidView:'',
          iceNum:''
        }],
        pledgeTableSchema: [ // 收藏表格架构
          {
            label: '系列',
            slot: 'SchemaSeries',
            width: 200,
          },
          {
            label: '概率',
            slot: 'outputProbability',
            width: 100
          },
          {
            label: '产出图到账后自动开寄售：​​​',
            field: 'produceAutoSell',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }],
             width: 140,
          },
          {
            label: '质押图到期后自动开寄售：​​​',
            field: 'pledgeExpireAutoSell',
            type: 'radio',
            options: [{
              label: '是',
              value: 1
            }, {
              label: '否',
              value: 0
            }],
            width: 140,
          },
          {
            label: '数量',
            slot: 'pledge24ProduceImgNum',
            width: 150
          },
          {
            label: '操作',
            slot: 'action',
            width: '170px',
            headerSlot: 'action-header',
            width: 100
          }
        ],
      }
    },
    mounted() {
      const {
        type,
        activityType,
        activityNo
      } = this.$route.query
      this.isDetail = activityType === 'detail'
      this.activityNo = activityNo
      activityNo && this.getDetail()
    },
    watch: {
    },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 导入模版
      async importTemplate(data) {
        const {
          status,
          result
        } = await this.$api.rebuildImportTemplate({
          excelUrl: data.result?.url
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.worksTableData = result
        }
      },
      //导入资格模板
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const {
          status,
          result
        } = await this.$api.activityDetail({
          activityNo: this.activityNo,
          type:'ICE_PLEDGE'
        })
        if (status.code === 0) {
          result.startTime = [result.startTime, result.endTime]
          const {
            smeltingCollection,
            blindBoxPrize,
            goodsBlindBox,
            purchaseSpecNft,
            collectiblesAvailable,
            activityShipVOS,
            inItemJson,
            ...rest
          } = result
          console.log('extraJson',JSON.parse(result.extraJson))
          this.formData = {
            ...rest,
            ...JSON.parse(result.extraJson),
            ...JSON.parse(result.extraJson).baseInfo
          }
          this.formData.iceOptionList = this.formData.options
           this.formData.iceOptionList.forEach((item)=>{
              item.pledge24ProduceImgCidView = `${item.name}(${item.pledge24ProduceImgCid})`
           })
          this.collectionTableDataSeries=this.formData.baseInfo.iceProducts
          delete this.formData.baseInfo;
          delete this.formData.options;
          delete this.formData.iceProducts;
        }
        console.error(this.formData)
      },
      // 添加质押选项
      addIceItem(index) {
        let id = this.formData.iceOptionList.length + 1
        if(id == 11){
           this.$message.error('最大可添加10个')
           return
        }
        this.formData.iceOptionList.push({
          id,
          mainPledgeNum:0,
          outputRule:1,
          IceProduceImgs:[{
            iceProductCidView:'',
            produceAutoSell:1,
            pledgeExpireAutoSell:1,
            pledge24ProduceImgNumRoundStart:"",
            pledge24ProduceImgNumRoundEnd:"",
            outputProbability:""
          }]
        })
      },
      deleteItem(index, data) {
        data.splice(index, 1)
      },
      async downloadTemplate(templateTag) {
        const {
          status,
          result
        } = await this.$api.downLoadTemplateExcel({
          templateTag
        })
        if (status.code === 0) {
          window.open(result.emailsTemplateUrl, '_blank')
          this.$message.success(status.msg)
        }
      },
      async downloadExcel(item) {

        try {
          const res = await this.$api.activityDownloadExcel({
            materialsUnqNo: item.materialsUnqNo,
            activityNo: this.formData.activityNo
          })
          if (res.type === 'application/json') {
            // blob 转 JSON
            const enc = new TextDecoder('utf-8')
            res.arrayBuffer().then(buffer => {
              const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
              this.$message.error(data.status?.msg)
            })
          } else {
            downloadBlob(res, '下载')
            this.$message.success('下载成功')
          }
        } finally {

        }
        // if (res) {
        // 	downloadBlob(res, '作品')
        // 	this.$message.success('下载成功')
        // }
      },
      async submit() {
        if (this.formData.activityNewBannerType == 1) {
          this.addBanner()
        } else {
          this.configSubmit()
        }
      },
      checkendBanner(item, index) {
        this.bannerSun = index
        this.bannerInfo = item
      },
      addBanner() {
        const loading = this.$loading({
          lock: true,
          text: '图片生成中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        const dom = this.$refs.canvas // 需要生成图片内容的
        console.log(dom)
        html2canvas(dom, {
          // width: dom.clientWidth, //dom 原始宽度
          // height: dom.clientHeight,
          // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
          // scrollX: 0,
          useCORS: true, //支持跨域
          backgroundColor: 'transparent',
          scale: 2, // 按比例增加分辨率 (2=双倍)
          dpi: window.devicePixelRatio * 2, // 设备像素比
          // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
        }).then((canvas) => {
          this.updateImg(canvas.toDataURL('image/jpeg', 1))
        }).catch(err => {

        })
      },
      async updateImg(url) {
        const formData = new FormData()
        let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
        formData.append('file', fileOfBlob)
        const {
          result
        } = await uploadImgToOss(formData)
        this.bannerUrl = result.smallImageUrl
        this.formData.activityNewBannerImage = this.bannerUrl
        const loading = this.$loading({});
        loading.close();
        this.configSubmit()
      },
      //最终提交
      configSubmit() {
        // 检查整个数组是否非空
        let data = this.formData.iceOptionList
        if (data && Array.isArray(data) && data.length > 0) {
            // 遍历数组中的每个元素
            let errors = [];
            data.forEach((item, index) => {
                if (
                    item.id === undefined ||
                    item.pledgeDay === undefined ||
                    item.pledgeMaxNum === undefined
                ) {
                    errors.push(index+1);
                }
            });

            if (errors.length === 0) {

            } else {
                console.log(`存在无效的数据项: ${errors}`);
                this.$message.error(`质押期选项第${errors}项,填写错误，请检查后重试`)
                return
            }
        } else {
            console.log('数据数组为空或不是有效的数组');
            this.$message.error(`质押期选项第${errors}项,填写错误，请检查后重试`)
            return
        }
         //
          this.submitAll()
      },
      async submitAll() {
        let startTime, endTime, activityShipCtid = ""
        if (this.formData.startTime[0]) {
          startTime = this.formData.startTime[0]
        }
        if (this.formData.startTime[1]) {
          endTime = this.formData.startTime[1]
        }
        this.formData.iceOptionList.forEach((item)=>{
          item.IceProduceImgs.forEach((itemm)=>{
            if(itemm.iceProductCidView!=undefined){
              itemm.pledge24ProduceImgCid = this.stringCtidToCtid(itemm.iceProductCidView)
            }
          })
        })
        this.collectionTableDataSeries.forEach((item)=>{
           if(item.iceProductCidView!=undefined){
              item.iceProductCid = this.stringCtidToCtid(item.iceProductCidView)
           }
        })
        let baseInfo = {
            maxEcNum:this.formData.maxEcNum,
            maxTimes:this.formData.maxTimes,
            maxEcRoundStart:this.formData.maxEcRoundStart,
            maxEcRoundEnd:this.formData.maxEcRoundEnd,
            iceProducts:this.collectionTableDataSeries
        }
        console.log(this.formData.iceOptionList)
        console.log(this.collectionTableDataSeries)
        let extraJson = {
            ...this.formData,
            options:this.formData.iceOptionList,
            baseInfo
        }
        console.error(extraJson)
        const data = {
          activityNo: this.activityNo,
          ...this.formData,
          extraJson: JSON.stringify(extraJson),
          iceOptionList:JSON.stringify(this.formData.iceOptionList),
          startTime,
          endTime,
          type:'ICE_PLEDGE'
        }
        console.table(this.formData.baseInfo)
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.activityNo) {
            const {
              status
            } = await this.$api.activityEdit(data)
            if (status.code === 0) {
              this.routerBack()
            }
          } else {
            const {
              status
            } = await this.$api.activityAdd(data)
            if (status.code === 0) {
              this.routerBack()
            }
          }
        })
      },

      base64ToFile(urlData, fileName) {
        const arr = urlData.split(',')
        const mime = arr[0].match(/:(.*?);/)[1]
        const bytes = atob(arr[1])
        let n = bytes.length
        const ia = new Uint8Array(n)
        while (n--) {
          ia[n] = bytes.charCodeAt(n)
        }
        return new File([ia], fileName, {
          type: mime
        })
      },
      // ctid剔除方法
      stringCtidToCtid(str){
          if(str!=""){
            return str.split('(')[1].split(')')[0]
          }else{
            return str
          }
      },
      // ctid剔除姓名
      stringCtidToName(str){
        if(str!=""){
          return str.split('(')[0]
        }else{
          return str
        }
      },
      async querySearchAsync(queryString, cb) {
        var restaurants = this.restaurants;
        this.searchNew(queryString)
        let results = []
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
          results = this.results
          cb(results);
        }, 1000);
      },
      handleSelect(item) {
        console.log(item);
      },
      async searchNew(str) {
        this.results = []
        if (str) {
          let res = await this.$api.searchPgc({
            name: str
          });
          if (res.status.code == 0) {
            if (res.result.list != null) {
              res.result.list.forEach((item) => {
                this.results.push({
                  'value': `${item.name}(${item.ctid})`,
                })
              })
              console.log(this.results)
            }
          }
        }
      },
      addCollectionItemSeries(){
        if(this.collectionTableDataSeries.length>3){
           this.$message.error(`可冰封材料最多为4个哦`)
          return
        }
        this.collectionTableDataSeries.push({
          iceProductCidView:"",
          iceNum:""
        })
      },
      addPledgeItemSeries(item){
       item.push({
          iceProductCidView:'',
          produceAutoSell:1,
          pledgeExpireAutoSell:1,
          pledge24ProduceImgNumRoundStart:"",
          pledge24ProduceImgNumRoundEnd:"",
          outputProbability:""
        })
      }
    },
  }
</script>

<style lang="scss" scoped>
  @font-face {
    font-family: 'eryaheiti';
    src: url('https://cdn-lingjing.nftcn.com.cn/h5/ttf/eryaheiti.ttf');
  }
  .page {
    padding-top: 80px;
  }

  .banner_ul {
    width: 600px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .li {
      margin-right: 20px;
      width: 128px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;

      &.active {
        img {
          border: 4px solid #409EFF;
        }
      }

      img {
        width: 120px;
        height: auto;
        border-radius: 8px;
      }
    }
  }

  .banner_flex {
    display: flex;
    justify-content: flex-start;
    width: 1300px;
  }

  .yulan {
    width: 600px;

    .canvas {
      width: 100%;
      height: 270px;
      background-size: 100%;
      background-repeat: no-repeat;

      // display:flex;
      // justify-content: center;
      // align-items: center;
      .font {
        width: 85%;
        color: #fff;
        text-align: center;
        margin: 0 auto 0px auto;
        padding-top: 90px;

        .title {
          font-size: 28px;
          font-family: 'eryaheiti';
        }

        .subtitle {
          font-size: 20px;
          font-family: 'eryaheiti';
        }
      }
    }
  }

  .flex_div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 700px;
    height: 50px;
  }

  .uploader {
    margin-top: 0 !important;
  }

  .danwei {
    margin-left: 6px;
  }
  .title_pz{
    padding-left:200px;
    color:rgb(255, 29, 29);
    font-size:18px;
    margin-bottom:20px;
  }
  .margin-left-active{
    margin-left:-300px;
  }
</style>
