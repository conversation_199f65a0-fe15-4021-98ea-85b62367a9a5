<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-schema="tableSchema" :table-data="tableData" :showIndex="false" :loading="loading">
      <template #action-header>
        <el-button @click="dialogVisible = true" type="primary" size="mini">创建活动</el-button>
      </template>
      <template #online-time="scope">
        {{ scope.row.startTime }} - {{ scope.row.endTime }}
      </template>
      <template #startTime="scope">
        {{ scope.row.startTime }} - {{ scope.row.endTime }}
      </template>
      <template #backgroundImage="scope">
        <el-image style="width: 60px; height: 60px" fit="cover" :src="scope.row.backgroundImage"
          :preview-src-list="[scope.row.backgroundImage]">
        </el-image>
      </template>
      <template #activityNewShow="scope">
        <div>
          <el-tag v-if="scope.row.activityNewShow == 0">未展示</el-tag>
          <el-tag v-if="scope.row.activityNewShow == 1 && currentTime > scope.row.timestamp" type="success">展示中</el-tag>
          <el-tag v-if="scope.row.activityNewShow == 1 && currentTime < scope.row.timestamp" type="success">已定时{{
            scope.row.activityNewStartTime }}</el-tag>
          <el-button v-if="scope.row.activityNewShow == 1 && currentTime > scope.row.timestamp"
            @click="gg_hide(scope.row)" type="text">立即隐藏</el-button>
          <el-button v-if="scope.row.activityNewShow == 0" @click="gg_show(scope.row)" type="text">立即展示</el-button>
          <el-button v-if="scope.row.activityNewShow == 0" @click="open_gg_time(scope.row)" type="text">定时展示</el-button>
          <el-button v-if="scope.row.activityNewShow == 1 && currentTime < scope.row.timestamp"
            @click="gg_hide(scope.row)" type="text">取消展示定时</el-button>
          <!-- <el-button
             @click="hhhhh(scope.row)" >查看状态</el-button> -->
        </div>
      </template>

      <template #action="scope">
        <el-button @click="toFormPage(scope.row, 'detail')"
          v-if="scope.row.onlineStatus !== 2 && scope.row.type != 'THREE_H5'" type="text">查看</el-button>
        <el-button
          v-if="scope.row.onlineStatus !== 2 && scope.row.type != 'THREE_H5' && scope.row.type != 'VOTE' && scope.row.type != 'BZL_ICE_PLEDGE'"
          @click="toFormPage(scope.row)" type="text">编辑</el-button>
        <el-button v-if="scope.row.type == 'REBUILD'" @click="exportExcel(scope.row)" type="text">导出资格</el-button>
        <el-popconfirm style="margin-left: 20px;" title="确定要删除吗？" @confirm="deleteItem(scope.row.activityNo)">
          <el-button slot="reference" type="text">删除</el-button>
        </el-popconfirm>
        <el-button style="margin-left: 10px;" v-if="scope.row.onlineStatus !== 2"
          @click="activityStatusToggle(scope.row.activityNo, scope.row.onlineStatus)" type="text">
          {{ scope.row.onlineStatus === 0 || scope.row.onlineStatus === 2 ? '上线' : '下线' }}
        </el-button>
        <el-button @click="clearRedis(scope.row)" type="text">清空缓存</el-button>
        <el-button @click="cancelTiming(scope.row.activityNo)" type="text"
          v-if="scope.row.isTiming == 1">取消定时</el-button>

        <el-button @click="downloadTemplate('REBUILD_BOX_EXCEL')" type="text"
          v-if="scope.row.goodsBlindBoxRequestType == 'TID' && scope.row.onlineStatus != 1">
          下载模版</el-button>
        <el-button v-if='scope.row.type == "VOTE"' type="text" @click="exportResult(scope.row)">导出</el-button>
        <el-button v-if='scope.row.type == "ICE_PLEDGE"' type="text"
          @click="exportResultICE(scope.row, 0)">导出明细</el-button>
        <el-button v-if='scope.row.type == "ICE_PLEDGE"' type="text"
          @click="exportResultICE(scope.row, 1)">导出质押记录</el-button>
        <el-button @click="openFile(scope.row.activityNo)" type="text"
          v-if="scope.row.goodsBlindBoxRequestType == 'TID' && scope.row.onlineStatus != 1">上传合成出来的作品</el-button>
      </template>
    </common-table>

    <el-pagination style="float: right; margin-top: 20px;" @current-change="currentChange" layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>

    <el-dialog title="创建活动" :visible.sync="dialogVisible" width="260px">
      <el-select v-model="selected">
        <el-option v-for="item in activityOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <span slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button :disabled="!selected" type="primary" @click="toFormPage()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="定时展示开始" :visible.sync="dsTime" width="300px">
      <el-date-picker v-model="time" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择时间">
      </el-date-picker>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dsTime = false">取 消</el-button>
        <el-button type="primary" @click="gg_time()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="上传合成出来的作品" :visible.sync="updateMuban" width="600px">
      <el-form ref="form" label-width="170px">
        <el-form-item label="活动编号:">
          {{ activityNoMuban }}
        </el-form-item>
        <el-form-item label="上传合成出来的作品(TID)">
          <file-uploader :value.sync="templateUrl" ref="uploader" limit="1"
            style="margin-left: 10px;display:inline-block;width:240px;" text="上传合成出来的作品"></file-uploader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="updateMuban = false">取 消</el-button>
        <el-button type="primary" @click="submitMuban()">确 定</el-button>
      </span>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: 'ActivityManage',
  components: {
    CommonQuery,
    CommonTable,
    FileUploader
  },
  props: {},
  data() {
    return {
      platformType: "",

      dialogVisible: false, // 弹窗
      selected: '', // 选择的活动类型
      activityOptions: [
        // { label: '拉新', value: 'INVITE_NEW' },
        {
          label: '分解',
          value: 'resolve'
        },
        {
          label: '合成',
          value: 'REBUILD'
        },
        {
          label: '投票',
          value: 'vote'
        },
        {
          label: '抽奖',
          value: 'lottery'
        },
        {
          label: '其他',
          value: 'qita'
        },
        {
          label: '冰封',
          value: 'icebound'
        }
        // { label: '抽奖', value: 'GET_REWARD' }
      ],
      page: {
        totalCount: 0,
        pageSize: 10
      }, // 分页数据
      querySchema: [ // 搜索组件架构
        {
          type: 'input',
          label: '活动名称：',
          placeholder: '请输入活动名称',
          field: 'title'
        },
        {
          type: 'input',
          label: '活动编号：',
          placeholder: '请输入活动编号',
          field: 'activityNo'
        },
        {
          type: 'select',
          label: '活动类型：',
          field: 'type',
          placeholder: '请选择活动类型',
          options: [{
            label: '拉新',
            value: 'INVITE_NEW'
          },
          {
            label: '合成',
            value: 'REBUILD'
          },
          {
            label: '暴躁龙质押',
            value: 'BZL_ICE_PLEDGE'
          },
          // {
          //   label: '合成',
          //   value: 'MERGE'
          // },
          {
            label: '抽奖',
            value: 'GET_REWARD'
          },
          {
            label: '投票',
            value: 'VOTE'
          },
          {
            label: '冰封',
            value: 'ICE_PLEDGE'
          }
          ]
        },
        {
          type: 'select',
          label: '活动状态：',
          field: 'onlineStatus',
          placeholder: '请选择活动状态',
          options: [{
            label: '待上线',
            value: 0
          },
          {
            label: '在线中',
            value: 1
          },
          {
            label: '已下线',
            value: 2
          }
          ]
        }
      ],
      tableSchema: [ // 表格架构
        {
          label: '活动名称',
          field: 'title',
          width: '140px'
        },
        {
          label: '活动编号',
          field: 'activityNo',
          width: '120px'
        },
        {
          label: '活动类型',
          field: 'type',
          type: 'tag',
          tagMap: {
            INVITE_NEW: '拉新',
            REBUILD: '合成',
            // MERGE: '合成',
            GET_REWARD: '抽奖',
            THREE_H5: '其他',
            VOTE: '投票',
            ICE_PLEDGE: '冰封',
            BZL_ICE_PLEDGE: '暴躁龙质押'

          },
          width: '100px'
        },
        {
          label: '活动图片',
          field: 'backgroundImage',
          width: '110px',
          slot: 'backgroundImage',
        },
        {
          label: '上线时间',
          slot: 'online-time',
          width: '110px'
        },
        {
          label: '创建时间',
          field: 'createAt',
          width: '170px'
        },
        {
          label: '活动地址',
          field: 'link',
          width: '280px',
          width: '300px'
        },
        {
          label: '活动展示区',
          field: 'activityNewShow',
          slot: 'activityNewShow',
          width: '280px',
          width: '300px'
        },
        // {
        //   label: '定时区间',
        //   field: 'startTime',
        //   slot: 'startTime',
        //   width: '170px'
        // },
        {
          label: '状态',
          field: 'onlineStatus',
          type: 'tag',
          tagMap: {
            0: {
              label: '待上线',
              tagType: 'info'
            },
            1: {
              label: '在线中',
              tagType: 'success'
            },
            2: {
              label: '已下线',
              tagType: 'danger'
            }
          },
          width: '80px'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          width: '300px'
        }
      ],
      tableData: [{}],
      dsTime: false,
      time: '',
      currentTime: '',
      templateUrl: '',
      updateMuban: false,
      activityNoMuban: '',
      loading: false
    }
  },
  mounted() {
    // this.timestampToTime()
    const {
      platformType
    } = this.$route.query
    this.platformType = platformType
    this.getList()
    this.currentTime = Date.parse(new Date());
    console.log(this.currentTime)

  },
  methods: {
    async exportExcel(item) {
      const res = await this.$api.synthesisExport({
        activityNo: item.activityNo
      })
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '合成活动配置')
        this.$message.success('导出成功')
      }
    },
    // 导出结果
    async exportResult(item) {
      console.log(item)
      const res = await this.$api.voteExport({
        activityNo: item.activityNo
      })
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '投票活动页面结果')
        this.$message.success('导出成功')
      }
    },
    // 导出结果
    async exportResultICE(item, isAll) {
      console.log(item)
      const res = await this.$api.iceActiveExport({
        activeId: item.activityNo,
        isAll
      })
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        let title = isAll == 1 ? '导出质押记录' : '导出明细'
        downloadBlob(res, title)
        this.$message.success('导出成功')
      }
    },
    // 过滤查询
    onQueryChange(data) {
      this.query = data
      this.getList(true)
    },
    // 分页改变
    currentChange(value) {
      this.page.pageNum = value
      this.getList()
    },
    // 创建活动
    toFormPage(item = {}, activityType) {
      this.dialogVisible = false
      console.log(item)
      const typeMap = {
        INVITE_NEW: 'ActivityPullNew',
        REBUILD: 'ActivityRebuild',
        GET_REWARD: 'ActivityPullNew',
        MERGE: 'ActivityPullNew'
      }
      console.log("item.type", item.type)
      if (item.type == 'BZL_ICE_PLEDGE') {
        console.log(111)
        this.$router.push({
          name: 'bzl_icebound',
          query: {
            type: item.type || this.selected,
            activityNo: item.activityNo,
            activityType
          }
        })
        return
      }
      if (item.type == 'VOTE') {
        this.selected = ""
        this.$router.push({
          name: 'voteIndex',
          query: {
            activityNo: item.activityNo,
            type: activityType,
          }
        })
        return
      }
      if (this.selected == 'qita') {
        this.$router.push({
          name: 'elseIndex',
        })
      } else if (this.selected == 'vote') {
        this.$router.push({
          name: 'voteIndex',
        })
      } else {
        if (item.type == 'ICE_PLEDGE' || this.selected == 'icebound') {
          this.$router.push({
            name: 'icebound',
            query: {
              type: item.type || this.selected,
              activityNo: item.activityNo,
              activityType
            }
          })
        } else {
          if (this.selected == 'resolve' || this.selected == 'lottery') {
            this.$router.push({
              name: 'ActivityRebuild',
              query: {
                type: this.selected,
                activityNo: item.activityNo,
                activityType
              }
            })
          } else {
            if (item.type == 'REBUILD') {
              this.platformType == 2 ? this.selected = 'ActivityRebuild2' : this.selected = 'ActivityRebuild'
            }
            console.log(item.type, this.selected, typeMap[this.selected]);
            if (this.platformType == 2) {
              this.$router.push({
                name: 'ActivityRebuild2',
                query: {
                  type: 'REBUILD',
                  platformType: 2,
                  activityNo: item.activityNo,
                  activityType
                }
              })
            } else {
              this.$router.push({
                name: item.type ? typeMap[item.type] : typeMap[this.selected],
                query: {
                  type: item.type || this.selected,
                  platformType: 1,
                  activityNo: item.activityNo,
                  activityType
                }
              })
            }

          }
        }
      }
    },
    toActive() {
      console.log(this.selected)
    },
    // 获取列表
    async getList(isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        pageSize: 20,
        platformType: this.platformType ? this.platformType : '1'
      }
      const {
        status,
        result
      } = await this.$api.activityList(params)
      if (status.code === 0) {
        result.list.forEach((item) => {
          item.timestamp = new Date(item.activityNewStartTime).getTime()
        })
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
        console.log(this.tableData)
      }
    },
    async clearRedis(item) {
      const {
        status
      } = await this.$api.clearRedis({
        activityNo: item.activityNo,
        activityType: item.type
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
      }
    },
    // 活动上下线切换
    activityStatusToggle(activityNo, onlineStatus) {
      const title = onlineStatus === 1 ? '下线' : '上线'
      const text = onlineStatus === 1 ? '确认下线吗？' : '确认上线吗？'
      this.$confirm(text, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const {
          status
        } = await this.$api.activityStatusToggle({
          activityNo,
          onlineStatus: onlineStatus === 0 || onlineStatus === 2 ? 1 : 2
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    async gg_show(item) {
      const {
        status
      } = await this.$api.updateActivityNewStatusAndTiming({
        activityNo: item.activityNo,
        activityNewShow: 1,
      })
      if (status.code == 0) {
        this.$message.success('展示成功')
        this.getList()
      }
    },
    async gg_hide(item) {
      const {
        status
      } = await this.$api.updateActivityNewStatusAndTiming({
        activityNo: item.activityNo,
        activityNewShow: 0,
      })
      if (status.code == 0) {
        this.$message.success('隐藏成功')
        this.getList()
      }
    },
    open_gg_time(item) {
      this.dsTime = true
      this.gg_timeInfo = item
    },
    async gg_time(item) {
      this.dsTime = false
      let activityNewEndTime, activityNewStartTime
      if (this.time) {
        activityNewStartTime = this.time
      }
      const {
        status
      } = await this.$api.updateActivityNewStatusAndTiming({
        activityNewStartTime,
        activityNo: this.gg_timeInfo.activityNo,
        activityNewShow: 1,
      })
      if (status.code == 0) {
        this.$message.success('隐藏成功')
        this.getList()
      }
    },
    // 删除数据
    async deleteItem(activityNo) {
      const {
        status
      } = await this.$api.activityDelete({
        activityNo
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
      }
    },
    async cancelTiming(activityNo) {
      const {
        status
      } = await this.$api.cancelTiming({
        activityNo
      })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
      }
    },
    hhhhh(item) {
      console.log(item.timestamp)
      if (item.timestamp > this.currentTime) {
        console.log(true)
      }
    },
    async downloadTemplate(templateTag) {
      const {
        status,
        result
      } = await this.$api.downLoadTemplateExcel({
        templateTag
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    openFile(activityNo) {
      this.activityNoMuban = activityNo
      this.updateMuban = true
    },
    async submitMuban() {
      this.loading = true
      const {
        status,
        result
      } = await this.$api.mergeInItemTidImport({
        importUrl: this.templateUrl,
        activityNo: this.activityNoMuban
      })
      if (status.code === 0) {
        this.updateMuban = false
        this.loading = false
        this.templateUrl = ""
        this.$refs.uploader.remove()
        this.$message.success(status.msg)
      } else {
        this.loading = false
      }
    },

    //获取当前时间
    // timestampToTime(){
    //   //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    //   var timestamp = Date.parse(new Date());
    //   var date = new Date(timestamp);
    //   var Y = date.getFullYear() + '-';
    //   var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth()+1) : date.getMonth() + 1) + '-';
    //   var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + ' ';
    //   var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
    //   var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
    //   var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
    //   return  Y + M + D + h + m + s
    // },
  }
}
</script>

<style lang="scss" scoped></style>
