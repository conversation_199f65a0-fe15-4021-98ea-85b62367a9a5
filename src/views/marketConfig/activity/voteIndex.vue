<template>
  <d2-container class="page">
    <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
      <template #banner_theme>
        <div class="banner_flex">
          <div class="banner_ul">
            <div class="li" :class="{ 'active': index == bannerSun }" v-for="(item, index) in bannerList"
              @click="checkendBanner(item, index)">
              <img :src="item.src" alt="" srcset="">
            </div>
          </div>
          <div class="yulan">
            <div class="canvas" ref="canvas" :style="{ backgroundImage: `url(${bannerInfo.src})` }">
              <div class="font">
                <div class="title"
                  :style="{ marginTop: `${bannerInfo.titleMarginTop}px`, color: `${bannerInfo.titleColor}` }">
                  {{ formData.title }}
                </div>
                <div class="subtitle"
                  :style="{ marginTop: `${bannerInfo.subTitleMarginTop}px`, color: `${bannerInfo.subTitleColor}` }">
                  {{ formData.subTitle }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #rebuild-collection>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="collectionTableSchema" :table-data="collectionTableData">
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItem(1)" type="primary" size="mini">新增</el-button>
                <el-button @click="downloadTemplate('REBUILD_ACTIVITY_EXCEL')" type="primary" size="mini">下载模版
                </el-button>
              </template>
              <template #template-upload="scope">
                11
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, collectionTableData)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template>
      <template #dddNum>
        <div style="width:300px;display: flex;">
          <el-input placeholder="请输入内容" v-model="formData.dddNum">
          </el-input>
          <el-button type="primary" style="margin-left:10px" size="mini"
            @click="addCollectionItemSeries(formData.dddNum, 'itemSeries')">确认</el-button>
        </div>
      </template>
      <template #rebuild-collection-series>
        <el-row>
          <el-col :span="20">
            <common-table :is-edit="!isDetail" :table-schema="collectionTableSchemaSeries"
              :table-data="collectionTableDataSeries">
              <template v-if="!isDetail" #action-header>
                <el-button @click="addCollectionItemSeries(-1, 'itemSeries')" type="primary" size="mini">新增</el-button>
              </template>
              <template #SchemaSeries="scope">
                <div class="">

                  <el-input placeholder="请输入内容" style="width:340px;margin-bottom:10px;" v-model="scope.row.title">
                  </el-input>

                </div>
              </template>
              <template #SchemaMainstay="scope">
                <div class="">

                  <el-input placeholder="" style="width:150px;margin-bottom:10px;" v-model="scope.row.second">
                  </el-input>
                  秒
                  <el-input placeholder="" style="width:150px;margin-bottom:10px;" v-model="scope.row.num">
                  </el-input>
                  票
                </div>
                <span style="color: crimson;">若填10秒5票，会在活动开始之后10秒内随机投5票</span>
              </template>


              <template #template-upload="scope">
                <template v-if="!isDetail">
                  <file-uploader :value.sync="scope.row.smeltingCollectionExcelUrl"></file-uploader>
                </template>
                <el-button v-else @click="downloadExcel(scope.row)">下载 Excel</el-button>
              </template>
              <template v-if="!isDetail" #action="scope">
                <el-popconfirm title="确定要删除吗？" @confirm="deleteItem(scope.$index, collectionTableDataSeries)">
                  <el-button slot="reference" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </common-table>
          </el-col>
        </el-row>
      </template>

      <template #inCtidStr="scope">
        <div>
          <el-table :data="tableDataColl" border>
            <el-table-column prop="series" label="系列" width="30">
              <template slot-scope="scope">
                <!-- <el-input v-model="scope.row.series" placeholder="请输入系列名/系列ID" /> -->
                <el-autocomplete style="width:340px;" v-model="scope.row.ctid"
                  :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect">
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column prop="num" label="每次消耗数量" width="30">
              <template slot-scope="scope">
                <el-input v-model="scope.row.num" placeholder="个数" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="20">
              <template slot-scope="scope">
                <el-button type="text" @click="removeRow(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div style="margin-top: 10px; text-align: center;">
            <el-button type="primary" @click="addRow">添加藏品</el-button>
          </div>
        </div>
      </template>






    </common-form>
  </d2-container>
</template>

<script>
import CommonForm from '@/components/CommonForm'
import CommonTable from '@/components/CommonTable'
import FileUploader from '@/components/FileUploader'
import html2canvas from 'html2canvas';
import {
  mapActions
} from 'vuex'
import {
  downloadBlob
} from '@/utils/helper'
import {
  uploadImgToOss
} from '@/api/ossCenter'
export default {
  name: 'ActivityRebuild',
  components: {
    CommonForm,
    CommonTable,
    FileUploader
  },
  data() {

    const validateCollection = (rule, value, callback) => {
      if (this.collectionTableData.length <= 0) {
      } else {

        callback()
      }
    }
    const validateCollectionSeries = (rule, value, callback) => {
      if (this.collectionTableDataSeries.length <= 0) {

      } else {

        callback()
      }
    }


    return {
      tableDataColl: [
        {
          ctid: '',
          num: '',
        },
      ],
      value1: '',
      templateUrl: '', // 盲盒内作品模板地址
      templateUrl2: '',
      isDetail: false, // 详情
      activityNo: null, // 活动编号


      collectionTableSchemaSeries: [ // 收藏表格架构
        {
          label: '名称',
          slot: 'SchemaSeries',
          field: 'name',
          width: 300
        },
        {
          label: '主力  几秒投几票',
          slot: 'SchemaMainstay',
          width: 300,
        },
        {
          label: '操作',
          slot: 'action',
          width: '170px',
          headerSlot: 'action-header'
        }
      ],


      tableData: [], // 盲盒可以开出内容的数据
      collectionTableData: [], // 收藏表格数据
      collectionTableDataSeries: [
        {
          title: '',
          second: '',
          num: ''
        }
      ],
      goodsBlindBoxTableDataSeries: [{
        csList: [{
          nameCtid: "",
          ctid: "",
          name: "",
          eachNum: "",
          isActivityTag: true
        }],
      }],

      formData: {
        smeltingCollectionType: 'CTID',
        goodsBlindBoxRequestType: 'CTID',
        goodsBlindBoxRequestJson: [{
          csList: []
        }],
        dddNum: 1,
        activityNewShow: 1,
        activityNewBannerImage: "",
        blindBoxesTotal: "",
        subTitle: "",
        activityNewBannerType: "1",
        activityShipCtid: "",
        activityShipConfigJson: "",
        activityShipScanType: 1,
        activityShipScanType2: -1, //扫描资格方式 判断,
        votenum: 1,
        startTime: ['2023-01-26 20:37:58', '2023-01-27 20:37:58'],
      },


      formSchema: [{
        type: 'input',
        label: '投票名称：',
        placeholder: '请输入投票名称',
        field: 'title',
        disabled: false,
        rules: [{
          required: true,
          message: '请输入投票名称',
          trigger: 'blur'
        }]
      },

      {
        label: '新增选项：',
        slot: 'dddNum',
      },

      {
        slot: 'rebuild-collection-series',
        field: 'rebuildSerie',
        label: '投票选项：',
        rules: [{
          required: true,
          validator: validateCollectionSeries,
          trigger: 'blur'
        }],
        show: {
          relationField: 'smeltingCollectionType',
          value: ['CTID']
        },
      },
      {
        type: 'img',
        label: '活动banner：',
        placeholder: '请选择活动banner',
        field: 'activityNewBannerImage',
        rules: [{
          required: true,
          message: '请选择活动banner',
          trigger: 'change'
        }]
      },
      {
        type: 'search',
        label: '投票消耗的藏品为：',
        placeholder: '请输入藏品名称',
        slot: 'inCtidStr',
        rules: [{
          required: true,
          message: '请输入藏品名称',
          trigger: 'blur'
        }]
      },
      {
        type: 'datetimerange',
        label: '投票时间为：',
        field: 'startTime',
        pickerOptions: {
          disabledDate: (time) => {
            return time.getTime() < Date.now() - 86400000
          }
        },
        rules: [{
          required: true,
          message: '请输入投票时间',
          trigger: 'blur'
        }]
      },
      {
        label: '每人最多投几票',
        field: 'votenum',
        rules: [{
          required: true,
          message: '请设置每人最多投票数',
          trigger: 'blur'
        }]
      },
      {
        type: 'action'
        // exclude: ['reset', 'submit', 'back']
      }
      ],
      searchList: [],
      csName: '',
      restaurants: [],
      timeout: null,
      results: [],
      aggregate: 0,
      blindBoxesTotal: 0,
      bannerList: [],
      bannerSun: 0,
      bannerInfo: {
        src: "https://cdn-lingjing.nftcn.com.cn/h5/admin/bannerImg/1.png",
        titleMarginTop: 24,
        titleColor: "#D0FFF7",
        subTitleMarginTop: 30,
        subTitleColor: "#83F0FB"
      },
      bannerUrl: "",
      serializableTableSchemaSeries: [],

    }
  },
  mounted() {
    const {
      type,
      activityNo
    } = this.$route.query
    console.log(activityNo)
    if (type == 'detail') {
      this.isDetail = true
    }
    this.activityNo = activityNo
    console.log(this.activityNo)
    if (activityNo) {
      this.getDetail()
    }

  },
  watch: {

  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results);
      }, 1000);
    },
    handleSelect(item) {
      console.log(item, '12312');
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        let res = await this.$api.searchPgc({
          name: str
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                'value': `${item.name}(${item.ctid})`,
                'id': item.ctid
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    addRow() {
      this.tableDataColl.push({
        ctid: '',
        num: '',
      });
    },
    removeRow(index) {
      this.tableDataColl.splice(index, 1);
    },
    async getDetail() {
      console.log(this.activityNo)
      const {
        status,
        result
      } = await this.$api.voteDetail({
        activityNo: this.activityNo
      })
      console.log(result)
      if (status.code === 0) {
        // console.log("this.formData.startTime",this.formData)
        let startTime = [`${result.startTime}.000`, `${result.endTime}.000`]


        this.formData = {
          smeltingCollectionType: 'CTID',
          title: result.title,
          dddNum: result.voteItemList.length,
          activityNewShow: result.voteItemList.length,
          activityNewBannerImage: result.activityNewBannerImage,
          ctid: result.inCtid,
          votenum: result.maxVoteNumEveryUser,
          startTime: startTime
        }
        this.collectionTableDataSeries = []
        console.log("result.voteItemList", result.voteItemList)
        result.voteItemList.forEach((item) => {
          this.collectionTableDataSeries.push(item)
        })
        console.log("collectionTableDataSeries", this.collectionTableDataSeries)
        // this.collectionTableDataSeries.csList=result.voteItemList
        console.log("this.formData", this.formData)
      }

    },

    addCollectionItem() {
      this.collectionTableData.push({
        picCoverSmelting: '',
        smeltingCollectionExcelUrl: ''
      })
    },
    // 添加投票选项
    addCollectionItemSeries(value, type) {
      console.log(value, type)
      //限制投票选项数量，最多10个
      if (value >= 11) {
        this.$notify({
          message: '最多只能新增10个投票选项',
          type: 'error'
        })
        return
      }
      if (type == "itemSeries") {
        // console.log(2222222)
        // console.log(this.collectionTableDataSeries.length)
        if (this.collectionTableDataSeries.length > 9) {
          this.$notify({
            message: '最多只能新增10个投票选项',
            type: 'error'
          })
          return
        }
      }
      if (value == 1) {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries = []
          this.collectionTableDataSeries.push({
            title: "",
            second: '',
            num: ''
          })
        }
      } else if (value > 1) {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries = []
          var i = 0
          for (i; i < value; i++) {
            this.collectionTableDataSeries.push({
              title: "",
              second: '',
              num: ''
            })
          }
        }
      } else {
        if (type == 'itemSeries') {
          this.collectionTableDataSeries.push({
            title: "",
            second: '',
            num: ''
          })
        }
      }
    },

    deleteItem(index, data) {
      data.splice(index, 1)
    },
    async downloadTemplate(templateTag) {
      const {
        status,
        result
      } = await this.$api.downLoadTemplateExcel({
        templateTag
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
        this.$message.success(status.msg)
      }
    },
    async downloadExcel(item) {

      try {
        const res = await this.$api.activityDownloadExcel({
          materialsUnqNo: item.materialsUnqNo,
          activityNo: this.formData.activityNo
        })
        if (res.type === 'application/json') {
          // blob 转 JSON
          const enc = new TextDecoder('utf-8')
          res.arrayBuffer().then(buffer => {
            const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
            this.$message.error(data.status?.msg)
          })
        } else {
          downloadBlob(res, '下载')
          this.$message.success('下载成功')
        }
      } finally {

      }
      // if (res) {
      //  downloadBlob(res, '作品')
      //  this.$message.success('下载成功')
      // }
    },


    async submit() {
      console.log(this.tableDataColl, '12312312312');
      this.tableDataColl.forEach(item => {
        const match = item.ctid.match(/\((.*?)\)/);
        if (match) {
          item.ctid = match[1];
        }
      });
      let ctid;
      if (this.formData.ctid) {
        ctid = this.formData.ctid.split("(")[1].split(")")[0]
      }
      console.log(11111111111111111111)
      console.log(this.formData)


      let voteList = this.collectionTableDataSeries
      console.log("this.voteList", voteList)
      let voteItemListJson = []
      voteList.forEach((item) => {
        console.log(item)
        voteItemListJson.push(item)
      })
      voteItemListJson = JSON.stringify(voteItemListJson)
      console.log("voteItemListJson", voteItemListJson)




      let res = await this.$api.addVote({
        title: this.formData.title,
        startTime: this.formData.startTime[0],
        endTime: this.formData.startTime[1],
        activityNewBannerImage: this.formData.activityNewBannerImage,
        voteItemListJson,
        inCtidStr: JSON.stringify(this.tableDataColl),
        maxVoteNumEveryUser: this.formData.votenum
      });
      console.log("res-------------", res)
      if (res.status.code == 0) {
        this.$message.success("提交成功")
        this.routerBack()
      } else {
        // this.$message.error("系统异常")

      }


      // if (this.formData.activityNewBannerType == 1) {
      //   this.addBanner()
      // } else {
      //   this.configSubmit()
      // }


    },
    async search() {
      let res = await this.$api.searchPgc({
        name: this.csName
      });
      if (res.status.code == 0) {
        this.searchList = res.result.list
      }
    },
    checkendBanner(item, index) {
      this.bannerSun = index
      this.bannerInfo = item
    },
    addBanner() {
      const loading = this.$loading({
        lock: true,
        text: '图片生成中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      const dom = this.$refs.canvas // 需要生成图片内容的
      console.log(dom)
      html2canvas(dom, {
        // width: dom.clientWidth, //dom 原始宽度
        // height: dom.clientHeight,
        // scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
        // scrollX: 0,
        useCORS: true, //支持跨域
        backgroundColor: 'transparent',
        scale: 2, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 2, // 设备像素比
        // scale: 4, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
      }).then((canvas) => {
        this.updateImg(canvas.toDataURL('image/jpeg', 1))
      }).catch(err => {

      })
    },
    async updateImg(url) {
      const formData = new FormData()
      let fileOfBlob = new File([this.base64ToFile(url, 'file')], new Date() + '.jpg')
      formData.append('file', fileOfBlob)
      const {
        result
      } = await uploadImgToOss(formData)
      this.bannerUrl = result.smallImageUrl
      this.formData.activityNewBannerImage = this.bannerUrl
      const loading = this.$loading({});
      loading.close();
      this.configSubmit()
    },
    //最终提交


    base64ToFile(urlData, fileName) {
      const arr = urlData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bytes = atob(arr[1])
      let n = bytes.length
      const ia = new Uint8Array(n)
      while (n--) {
        ia[n] = bytes.charCodeAt(n)
      }
      return new File([ia], fileName, {
        type: mime
      })
    },
    addSerializable(itemList) {
      itemList.push({
        ctid: "",
        name: "",
        eachNum: "",
        nameCtid: "",
        isActivityTag: true
      })
    },
    seleteData() {
      console.table(this.goodsBlindBoxTableDataSeries)
    },
    add_zigeItem() {
      this.zigeTable.push({
        ctid: "",
        num: "",
        nameCtid: ""
      })
    },
    routerBack() {
      const {
        fullPath
      } = this.$route
      this.close({
        tagName: fullPath
      })
      this.$router.back()
    },
  },
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'fonts1';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/admin/fonts/1_title.ttf');
}

@font-face {
  font-family: 'sub_fonts';
  src: url('https://cdn-lingjing.nftcn.com.cn/h5/admin/fonts/1_subtitle.ttf');
}

.page {
  padding-top: 80px;
}

.banner_ul {
  width: 600px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .li {
    margin-right: 20px;
    width: 128px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;

    &.active {
      img {
        border: 4px solid #409EFF;
      }
    }

    img {
      width: 120px;
      height: auto;
      border-radius: 8px;
    }
  }
}

.banner_flex {
  display: flex;
  justify-content: flex-start;
  width: 1300px;
}

.yulan {
  width: 600px;

  .canvas {
    width: 100%;
    height: 270px;
    background-size: 100%;
    background-repeat: no-repeat;

    // display:flex;
    // justify-content: center;
    // align-items: center;
    .font {
      width: 85%;
      color: #fff;
      text-align: center;
      margin: 0 auto 0px auto;
      padding-top: 90px;

      .title {
        font-size: 28px;
        font-family: 'fonts1';
      }

      .subtitle {
        font-size: 20px;
        font-family: 'sub_fonts';
      }
    }
  }
}

.flex_div {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 700px;
  height: 50px;
}

.uploader {
  margin-top: 0 !important;
}

.danwei {
  margin-left: 6px;
}
</style>

