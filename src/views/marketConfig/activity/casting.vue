<template>
  <d2-container class="page">
    <el-tabs type="border-card">
      <el-tab-pane label="铸造作品">
        <common-form :is-edit="!isDetail" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
          <template #mailbox="scope">
            <el-input placeholder="请输入二级标题" style="width:300px" @change="change" v-model="formData.email">
            </el-input>
            <div style="color:rgb(255, 29, 29);font-size:14px" v-if="formData.email!=''">
              该账号已有{{creationNum}}个作品，{{collectionNum}}个藏品
            </div>
          </template>
          <template #creation="scope">
              <el-autocomplete style="width: 340px" v-model="formData.introduce" :fetch-suggestions="querySearchAsync"
                placeholder="请输入系列名/系列ID" @select="handleSelect" >
              </el-autocomplete>
          </template>
        </common-form>
      </el-tab-pane>
      <el-tab-pane label="在已有系列中补充铸造">
        <common-form :submit="submitAppend" :data="formDataAppend" :schema="formSchemaAppend"  :loading="loading" label-width="300px">
        </common-form>
      </el-tab-pane>
    </el-tabs>

  </d2-container>
</template>

<script>
  import CommonForm from '@/components/CommonForm'
  import CommonTable from '@/components/CommonTable'
  import FileUploader from '@/components/FileUploader'
  import {
    mapActions
  } from 'vuex'
  import {
    downloadBlob
  } from '@/utils/helper'

  export default {
    name: 'casting',
    components: {
      CommonForm,
      CommonTable,
      FileUploader
    },
    data() {
      return {
        templateUrl: '', // 盲盒内作品模板地址
        templateUrl1: '', // 盲盒内作品模板地址
        isDetail: false, // 详情
        activityNo: null, // 活动编号
        formData: {
          email: '',
          joinLeapPlan:1,
          // leader:'云龙',
          castingTiming:0,
          price:0,
          ctid:"",
          creation:""
        },
        formSchema: [
          // {
          //   type: 'input',
          //   label: '创作者邮箱：',
          //   placeholder: '请输入创作者邮箱',
          //   field: 'email',
          //   slot: 'mailbox',
          //   rules: [{
          //     required: true,
          //     message: '请输入创作者邮箱',
          //     trigger: 'blur'
          //   }, ]
          // },
          {
            type: 'input',
            label: '名称：',
            placeholder: '请输入名称',
            field: 'title',
            rules: [{
              required: true,
              message: '请输入名称',
              trigger: 'blur'
            }]
          },
          {
            type: 'img',
            label: '作品图片：',
            placeholder: '作品图片',
            field: 'photo',
            multigraph: true,
            rules: [{
              required: true,
              message: '请选择作品图片',
              trigger: 'change'
            }]
          },
          {
            type: 'search_num',
            label: '查询其他系列数量(非必填)：',
            placeholder: '请输入铸造数量',
            field: 'ctid',
          },
          {
            type: 'number-input',
            label: '铸造数量：',
            placeholder: '请输入铸造数量',
            field: 'createNum',
            rules: [{
              required: true,
              message: '请输入铸造数量',
              trigger: 'blur'
            }]
          },
          {
            type: 'input',
            label: '创作者：',
            placeholder: '请输入创作者：',
            slot: 'creation',
            rules: [{
              required: true,
              message: '请输入创作者：',
              trigger: 'blur'
            }]
          },
          {
            type: 'number-input',
            label: '铸造价格：',
            placeholder: '请输入铸造价格',
            field: 'price',
            rules: [{
              required: true,
              message: '请输入铸造价格',
              trigger: 'blur'
            }]
          },
          {
            type: 'radio',
            label: '负责人：',
            field: 'leader',
            options: [{
                label: '云龙',
                value: '云龙'
              },
              {
                  label: 'xh-new',
                  value: 'xh-new'
              },
              {
                label: 'xinzai',
                value: 'xinzai'
              },
              {
                  label: '微笑',
                  value: '微笑'
              },
              {
                  label: '我执',
                  value: '我执'
              },
              {
                  label: '波',
                  value: '波'
              },
              {
                  label: 'bk',
                  value: 'bk'
              }
            ],
            rules: [{
              required: true,
            }]
          },
          {
            type: 'radio',
            label: '铸造：',
            field: 'castingTiming',
            options: [{
                label: '立即铸造',
                value: 0
              },
              {
                label: '一级购买时铸造',
                value: 1
              },
            ],
            rules: [{
              required: true,
            }]
          },
          {
            slot: 'msg_info',
          },
          {
            type: 'action'
            // exclude: ['reset', 'submit', 'back']
          }
        ],
        creationNum: 'x',
        collectionNum: 'x',
        formSchemaAppend: [{
          type: 'search',
          label: '系列ID/系列名：',
          placeholder: '请输入系列ID/系列名',
          field: 'ctid',
          rules: [{
            required: true,
            message: '请输入系列ID/系列名',
            trigger: 'blur'
          }]
        }, {
          type: 'number-input',
          label: '补充多少个token：',
          placeholder: '请输入补充多少个token',
          field: 'createNum',
          rules: [{
            required: true,
            message: '请输入补充多少个token',
            trigger: 'blur'
          }]
        }, {
          type: 'action',
          exclude: ['reset']
        }],
        formDataAppend: {
          ctid: "",
          createNum: "",
        },
        loading:false
      }
    },
    mounted() {
      const {
        type,
        activityType,
        dutyId
      } = this.$route.query
      this.isDetail = dutyId === 'detail'
      this.dutyId = dutyId
      dutyId && this.getDetail()
    },
    // watch:{
    //   'formData.ctid': {
    //     handler(newValue) {
    //       if(newValue.length>33){
    //         // 查询流通数量

    //       }
    //     },
    //     deep: true, // 深度监听
    //   },
    // },
    methods: {
      ...mapActions('d2admin/page', ['close']),
      // 优先购导入模版
      async importTemplate(data) {
        const {
          status,
          result
        } = await this.$api.whiteUserImport({
          importUrl: data.result?.url
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.preSale1TableData = result.list
        }
      },
      // 优先抢导入模版
      async importTemplate1(data) {
        const {
          status,
          result
        } = await this.$api.whiteUserImport({
          importUrl: data.result?.url
        })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.preSale2TableData = result.list
        }
      },
      routerBack() {
        const {
          fullPath
        } = this.$route
        this.close({
          tagName: fullPath
        })
        this.$router.back()
      },
      async getDetail() {
        const {
          status,
          result
        } = await this.$api.dutyInfo({
          dutyId: this.dutyId,
          dutyType: 'PRE_SALE_AND_UN_SALE'
        })
        if (status.code === 0) {
          const {
            preSaleWhiteUserList1,
            preSaleWhiteUserList2,
            ...rest
          } = result
          this.formData = {
            ...rest,
          }
          this.preSale1TableData = preSaleWhiteUserList1
          this.preSale2TableData = preSaleWhiteUserList2
        }
        console.table(this.formData)
      },
      // 添加可熔炼藏品
      addCollectionItem() {
        this.collectionTableData.push({
          picCoverSmelting: '',
          smeltingCollectionExcelUrl: ''
        })
      },
      // 添加盲盒结果
      addResultItem() {
        this.tableData.push({
          prizePicture: ''
        })
      },
      deleteItem(index, data) {
        data.splice(index, 1)
      },
      async downloadTemplate(templateTag) {
        const {
          status,
          result
        } = await this.$api.downLoadTemplateExcel({
          templateTag
        })
        if (status.code === 0) {
          window.open(result.emailsTemplateUrl, '_blank')
          this.$message.success(status.msg)
        }
      },
      async downloadExcel(materialsUnqNo) {
        const res = await this.$api.activityDownloadExcel({
          materialsUnqNo
        })
        if (res) {
          downloadBlob(res, '作品')
          this.$message.success('下载成功')
        }
      },
      async submit() {
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          // beforeClose: (action, instance, done) => {
          // 	if (action === 'confirm') {
          // 		instance.confirmButtonLoading = true;
          // 		instance.confirmButtonText = '铸造中...';
          // 		this.sub()
          // 	} else {
          // 		done();
          // 	}
          // }
        }).then(async () => {
          this.sub()
        })
      },
      async sub() {
        console.table(this.formData)
        const data = {
          ...this.formData,
          photoShow: JSON.parse(localStorage.getItem('img_result')).mediumImageUrl,
          cover: JSON.parse(localStorage.getItem('img_result')).smallImageUrl,
          abstractDesc:this.formData.title,
          content:this.formData.title,
        }
        console.log(data)
        const {
          status
        } = await this.$api.batchCreate(data)
        if (status.code === 0) { 
          this.$message.success('铸造成功')
          this.routerBack()
          // instance.confirmButtonLoading = false;
        } else {
          // this.$message.success(status.msg)
          // instance.confirmButtonLoading = false;
        }
      },
      async getEmail() {
        const res = await this.$api.userGoodsCount({
          email: this.formData.email
        })
        if (res.status.code === 0) {
          this.creationNum = res.result.creationNum
          this.collectionNum = res.result.collectionNum
        } else {
          // this.$message.success(res.status.msg)
          this.creationNum = 'x'
          this.collectionNum = 'x'
          console.log(this.creationNum, this.collectionNum)
        }
      },
      change(e) {
        this.getEmail()
      },
      async submitAppend() {
        if(this.formDataAppend.createNum<1){
          this.$message.error('补充token数量不能小于1')
          return false
        }
        this.$confirm('是否确认提交保存？', '确认提交保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          // beforeClose: (action, instance, done) => {
          // 	if (action === 'confirm') {
          // 		instance.confirmButtonLoading = true;
          // 		instance.confirmButtonText = '铸造中...';
          // 		this.sub()
          // 	} else {
          // 		done();
          // 	}
          // }
        }).then(async () => {
          this.batch_create_append()
        })
      },
      async batch_create_append() {
        this.loading=true
        let ctid;
        if(this.formDataAppend.ctid){
          ctid=this.formDataAppend.ctid.split("(")[1].split(")")[0]
        }
        const data = {
          ...this.formDataAppend,
          ctid
        }
        const {
          status
        } = await this.$api.batchCreateAppend(data)
        if (status.code === 0) {
          this.$message.success('铸造成功')
          this.routerBack()
          this.loading=false
        }else{
          this.loading=false
        }
      },
      async querySearchAsync(queryString, cb) {
        this.searchNew(queryString);
        let results = [];
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
          results = this.results;
          cb(results);
        }, 1000);
      },
      async searchNew(str) {
        this.results = [];
        // if (str) {
          let res = await this.$api.creatorInfos({
            creatorName: str,
          });
          if (res.status.code == 0) {
            if (res.result.list != null) {
              res.result.list.forEach((item) => {
                this.results.push({
                  value: `${item.creatorName} + ${item.synopsis}`,
                  label: `${item.synopsis}`,
                });
              });
              console.log(this.results);
            }
          }
        // }
      },
      handleSelect(e){
        console.log(e.value)
        this.formData.introduce = e.label
        this.formData.creation = e.label
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page {
    padding-top: 80px;
  }
</style>
