<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="updateMarketTabInfo()"
          >专题区编辑</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="specialName"
        label="瓷片标题"
        align="center"
      ></el-table-column>
      <el-table-column prop="specialCover" label="广告图片" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.specialCover"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="linkType" label="链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
          <el-tag v-if="scope.row.linkType == '30'">自定义跳转详情</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="link"
        label="跳转路径h5以及网页跳转"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="weight"
        label="排序"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog title="专题区模块编辑" :visible.sync="isDialog" center>
      <el-form :model="form" ref="form">
        <el-form-item label="模块名称:" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.marketTabName"
            placeholder="请输入模块名称"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="模块副标题:"
          :label-width="formLabelWidth"
          required
        >
          <el-input
            v-model="form.marketTabDesc"
            placeholder="请输入模块副标题"
            clearable
            style="width: 80%"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="debounceMethods(dialog_submit)"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'subject',
  data () {
    return {
      tableData: [],
      total: 1,
      isDialog: false,
      form: {
        marketTabName: '',
        marketTabDesc: ''
      },
      formLabelWidth: '120px'
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 查询列表
    async getList (page) {
      const res = await this.$api.subjectList({
        pageNum: page,
        pageSize: 15
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击编辑
    async audit_click (val) {
      this.$router.push({
        name: 'editSubject',
        query: {
          edit_id: val.id
        }
      })
    },
    // 点击专题区编辑
    updateMarketTabInfo () {
      this.isDialog = true
    },
    async dialog_submit () {
      await this.$api.updateMarketTabInfo({
        marketTabName: this.form.marketTabName,
        marketTabDesc: this.form.marketTabDesc,
        marketTabId: 6
      })
      this.isDialog = false
      this.form = {}
      this.$message.success('编辑成功')
    }
  }
}
</script>

<style></style>
