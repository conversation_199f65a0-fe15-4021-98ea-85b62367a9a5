<template>
  <d2-container class="page" ref="query">
    <div class="flex">
      <common-query :query-schema="querySchema" :data="query" @onSubmit="onQueryChange" @onReset="onReset"
        @onExport="onExport" :showSubmit="true" :showReset="true" :showExport="true"></common-query>
    </div>
    <div class="flex" style="margin-bottom:20px;">
      <!-- <el-button
        type="primary"
        @click="chaxun"
      >查询</el-button>
      <el-button
        type="primary"
        @click="onReset"

      >清除</el-button>
      <el-button
        type="primary"
        @click="clickExport()"

      >导出</el-button>-->
      <el-dropdown trigger="click" @command="functionSubmit">
        <el-button type="primary" style="margin-left: 10px;">批量操作 <i
            class="el-icon-arrow-down el-icon--right"></i></el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in templateList" :key="item.code" :command="item.code">

            <span>
              {{ item.desc }}
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <common-table :table-data="tableData" :table-schema="tableSchema" :loading="loading" :showSelection="true"
      :showIndex="false" :multipleSelection.sync="multipleSelectionList">
      <template #add="scope">
        <el-button type="text" @click="goJum(scope.row)" v-if="scope.row.status == 0">上架</el-button>
      </template>
      <template #action="scope">
        <el-button @click="CancelBuy(scope.row)" v-if="scope.row.status == 0" type="text">撤销</el-button>
      </template>
    </common-table>
    <div class=""
      style="display: flex; justify-content: center; background-color: #ffffff;align-items: center;position: relative;">
      <!-- <div class="money_total">
        <div class="marginR">本页总计求购剩余份数:{{moneyNum}}份</div>
        <div class="marginR">合计总计求购剩余份数:{{allMoneyNum}}份</div>
      </div> -->
      <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
        :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200, 500, 1000]"
        style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="xuanzeSize">
      </el-pagination>
    </div>
    <el-dialog title="添加竞价" :visible.sync="isDialog" width="900px" center>
      <common-form :submit="modelSubmit" :data="modelFormData" :schema="modelFormSchema" label-width="300px">
        <template #msg="scope">
          <p style="color:rgb(255, 29, 29);margin-top:-10px;">
            竞价出价只能输入整数，否则会进行四舍五入取整，望熟知!</p>
        </template>
        <!-- <template #offerPriceSlot="scope">
          <el-input v-model="scope.row" placeholder="请输入内容"  size="mini"></el-input>
          <el-input v-model="scope.row" placeholder="请输入内容"></el-input>
        </template> -->
        <template #offerPriceSlot>
          <el-row justify="start">
            <el-col :span="4">
              <div class="grid-content bg-purple">
                <el-input placeholder="" v-model="modelFormData.offerStartPrice"></el-input>
              </div>
            </el-col>
            <el-col :span="1">
              <div class="grid-content bg-purple" style="text-align:center;">-</div>
            </el-col>
            <el-col :span="4">
              <div class="grid-content bg-purple">
                <el-input placeholder="" v-model="modelFormData.offerEndPrice"></el-input>
              </div>
            </el-col>
            <el-col :span="10">
              <div style="color:#F56C6C;margin-left:10px">
                <!-- <div>选择tid导入，定时开始时间必须是当前时间后5分钟</div> -->
              </div>
            </el-col>
          </el-row>
        </template>



      </common-form>
    </el-dialog>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
import {
  downloadBlob
} from '@/utils/helper'
export default {
  name: 'buyList',
  components: {
    CommonTable,
    CommonQuery,
    CommonForm
  },
  data() {
    return {
      isDialog: false,
      tableData: [],
      query: {
        dutyStatus: 'DOING',
        type: '0',
        sort: '3',
        status: '0'
      },
      searchForm: {
        type: '0',
        sort: '3',
        status: '0'
      },
      querySchema: [{
        type: 'search',
        label: '系列id',
        placeholder: '请输入系列id',
        field: 'ctId'
      },
      {
        type: 'input',
        label: '系列名称',
        placeholder: '请输入系列名称',
        field: 'ctName'
      },
      {
        type: 'input',
        label: '竞价价格',
        placeholder: '请输入竞价价格',
        field: 'price'
      },
      {
        label: '用户类型',
        field: 'type',
        type: 'select',
        options: [{
          label: '主力',
          value: '4'
        },
        {
          label: '大户',
          value: '0'
        }
        ]
      },
      {
        type: 'input',
        label: '用户地址',
        placeholder: '请输入用户地址',
        field: 'userContractAddress'
      },
      {
        label: '竞价状态',
        field: 'status',
        type: 'select',
        options: [{
          label: '竞价中',
          value: '0'
        },
        {
          label: '撤销',
          value: '1'
        }, {
          label: '已完成',
          value: '2'
        }
        ]
      },
      {
        type: 'datetimerange',
        label: '竞价时间',
        placeholder: '请输入竞价时间',
        field: 'wantToBuyTimeBegin',
        field2: 'wantToBuyTimeEnd',
        format: 'yyyy-MM-dd HH:mm:ss.SSS',
        valueFormat: 'yyyy-MM-dd HH:mm:ss.SSS',
      },
      {
        label: '价格排序',
        field: 'sort',
        type: 'select',
        options: [{
          label: '价格从高到低',
          value: '1'
        },
        {
          label: '价格从低到高',
          value: '0'
        },
        {
          label: '时间从近到远',
          value: '3'
        },
        {
          label: '时间从远到近',
          value: '2'
        }
        ]
      }
      ],
      tableSchema: [{
        label: '系列',
        field: 'seriesName'
      },
      {
        label: '系列id',
        field: 'ctid',
        width: '300px'
      },
      {
        label: '用户地址',
        field: 'conAdd'
      },
      {
        label: '用户昵称',
        field: 'userName'
      },
      {
        label: '用户类型',
        field: 'member',
        type: 'tag',
        tagMap: {
          4: {
            label: '主力',
            tagType: 'success'
          },
          0: {
            label: '大户'
          }
        }
      },
      {
        label: '竞价价格',
        field: 'offerPrice'
      },
      {
        label: '竞价份数',
        field: 'number'
      },

      {
        label: '剩余份数',
        field: 'surplusNumber'
      },
      {
        label: '竞价时间',
        field: 'biddingTime'
      },

      {
        label: '状态',
        field: 'status',
        type: 'tag',
        tagMap: {
          0: {
            label: '竞价中',
            tagType: 'success'
          },
          1: {
            label: '撤销',
            tagType: 'info'

          },
          2: {
            label: '已完成',
            tagType: 'success'

          }
        }
      },
      {
        label: '上架',
        slot: 'add',
        fixed: 'right',
        width: '100px'
      },
      {
        label: '操作',
        slot: 'action',
        fixed: 'right',
        width: '100px'
      }
      ],

      modelFormData: {
        ctid: '',
        biddingUserNum: '',
        number: '',
        offerEndPrice: '',
        offerStartPrice: ''
      },
      modelFormSchema: [{
        type: 'search',
        label: '竞价系列id',
        field: 'ctid',
        rules: [{
          required: true,
          message: '请输入竞价系列id',
          trigger: 'change'
        }]
      },
      {
        label: '竞价总份数',
        field: 'number',
        rules: [{
          required: true,
          message: '请输入竞价总份数',
          trigger: 'change'
        }]
      },
      {
        type: 'number-input',
        label: '竞价的4.3账号数',
        field: 'biddingUserNum',
        rules: [{
          required: true,
          message: '请输入竞价的4.3账号数',
          trigger: 'change'
        }]
      },
      {
        type: 'number-input',
        label: '竞价出价(整数)',

        field: 'offerEndPrice',
        field2: 'offerStartPrice',
        slot: 'offerPriceSlot',
        rules: [{
          required: true,
          message: '请输入竞价出价(整数)',
          trigger: 'change'
        }]
      }, {
        label: '',
        slot: 'msg'
      }, {
        type: 'action'
      }
      ],
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      },
      isDetail: false,
      multipleSelectionList: [],
      templateList: [{
        code: '2',
        desc: '添加竞价'
      }, {
        code: '3',
        desc: '批量撤销'
      }, {
        code: '4',
        desc: '批量上架'
      }]
    }
  },
  computed: {},
  mounted() {
    this.tableData = []
    this.page.pageNum = 1
    this.getList()
  },
  methods: {
    async getList() {
      let biddingBeginTime, biddingEndTime;
      if (this.query.wantToBuyTimeBegin) {
        biddingBeginTime = this.query.wantToBuyTimeBegin[0]
        biddingEndTime = this.query.wantToBuyTimeBegin[1]
      }
      const {
        result,
        status
      } = await this.$api.biddingList({
        biddingBeginTime,
        biddingEndTime,
        conAdd: this.searchForm.userContractAddress || null,
        ctid: this.searchForm.ctId || null,
        csName: this.searchForm.ctName || null,
        member: this.searchForm.type, // 用户类型 主力4 大户 0
        offerPrice: this.searchForm.price || null, // 竞价价格
        orderType: this.searchForm.sort || null, // 价格排序 0升序asc 1降序desc
        pageNum: this.page.pageNum || null,
        pageSize: this.page.pageSize,
        seriesName: this.searchForm.ctName || null, // 系列名字
        status: this.searchForm.status || null,// 竞价状态 0竞价中 1撤销 2已完成 3账户余额不足

      })
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
      }
      console.log(result, '列表')
    },
    onQueryChange(e) { // 筛选
      this.searchForm = e
      if (this.searchForm.ctId) {
        this.searchForm.ctId = this.searchForm.ctId.split("(")[1].split(")")[0]
      }
      console.error(this.searchForm)
      this.getList()
    },

    async onExport(e) {
      let biddingBeginTime, biddingEndTime;
      if (this.query.wantToBuyTimeBegin) {
        biddingBeginTime = this.query.wantToBuyTimeBegin[0]
        biddingEndTime = this.query.wantToBuyTimeBegin[1]
      } // 导出
      const res = await this.$api.biddingListExport({
        ...e,
        biddingBeginTime,
        biddingEndTime
      })
      if (res.retCode === 500) {
        this.$message.error(res.retMsg)
        this.getList()
      } else if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then((buffer) => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '竞价' + Date.now() + '.csv')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    async CancelBuy(even) { // 撤销
      const res = await this.$api.revokeBidding({
        biddingId: even.biddingId
      })
      if (res.status.code === 0) {
        this.$message({
          type: 'success',
          message: '撤销成功'
        })
        this.getList()
      }
    },
    async batchCancelBuy() { // 批量撤销
      if (this.multipleSelectionList.length >= 1) {
        const str = []
        this.multipleSelectionList.forEach((item) => {
          str.push(item.biddingId)
        })
        const {
          status
        } = await this.$api.batchRevokeBidding({
          biddingId: JSON.stringify(str)
        })
        if (status.code === 0) {
          this.$message.success('批量撤销成功')
          this.getList()
        }
      } else {
        this.$message.error('请勾选你要批量的选项')
      }
    },
    functionSubmit(code) {
      if (code === '2') {
        // 添加竞价
        this.isDialog = true
      } else if (code === '3') {
        this.batchCancelBuy()
      } else if (code === '4') {
        this.openInput()
      }
    },
    async modelSubmit() { // 添加竞价
      if (this.modelFormData.ctid) {
        this.modelFormData.ctid = this.modelFormData.ctid.split("(")[1].split(")")[0]
      }
      const res = await this.$api.adminAddBidding({
        ...this.modelFormData
      })
      if (res.status.code === 0) {
        this.$message({
          type: 'success',
          message: '添加成功'
        })
        this.isDialog = false
        this.getList()
      }
    },
    async goJum(row) { // 上架
      if (row.surplusNumber == 1) {
        const {
          status
        } = await this.$api.ListingBidding({
          biddingJson: JSON.stringify([row.biddingId]),
          num: 1
        })
        if (status.code === 0) {
          this.$message.success('上架成功')
          this.getList()
        }
      } else {
        this.$prompt('请输入你要上架的份数', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: 1,
        }).then(async ({ value }) => {
          const {
            status
          } = await this.$api.ListingBidding({
            biddingJson: JSON.stringify([row.biddingId]),
            num: value > 0 ? value : -1
          })
          if (status.code === 0) {
            this.$message.success('上架成功')
            this.getList()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      }

    },
    openInput() {
      if (this.multipleSelectionList.length == 0) {
        this.$message.error('请勾选你要批量的选项')
        return false
      }
      this.$prompt('请输入你要批量上架的份数', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: 1,
      }).then(async ({ value }) => {
        this.goIn(value)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    async goIn(value) { // 批量上架
      if (this.multipleSelectionList.length >= 1) {
        const str = []
        this.multipleSelectionList.forEach((item) => {
          str.push(item.biddingId)
        })
        const {
          status,
          result
        } = await this.$api.ListingBidding({
          biddingJson: JSON.stringify(str),
          num: value > 0 ? value : -1
        })
        if (status.code === 0) {
          if (result[0].code == 0) {
            this.$message.success('批量上架成功')
            this.getList()
          } else {
            this.$message.success(result[0].message)
          }
        }
      } else {
        this.$message.error('请勾选你要批量的选项')
      }
    },
    onReset() { },
    currentChange(value) {
      console.log(value, 'value1')
      this.page.pageNum = value
      this.getList()
    },
    xuanzeSize(value) {
      console.log(value, 'value2')

      this.page.pageSize = value
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #409eff;
}

.flex {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.el-dropdown-menu {
  // left:1915px !important;
}

.money_total {
  // position: absolute;
  // left: 20px;
  font-size: 14px;
  color: #f56c6c;
  display: flex;

  .marginR {
    margin-right: 30px;
  }
}
</style>
