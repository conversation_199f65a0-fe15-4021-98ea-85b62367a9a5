<template>
  <d2-container class="page">
    <common-table  :table-data="tableData" :table-schema="tableSchema">
      <template #cover="scope">
        <img :src="scope.row.cover" style="width: 100px;" :alt="scope.row.cover">
      </template>
      <template v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" #action="scope">
        <el-button @click="deleteWorksItem(scope.row.airDropItemId)" type="text">删除</el-button>
      </template>
      <template #action-header>
        <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="addWorksItem" type="primary" size="mini">添加</el-button>
        <el-button @click="downloadTemplate" type="primary" size="mini">模版下载</el-button>
        <el-button type="primary" size="mini" @click="cleanWorksItem">清空作品</el-button>
        <el-upload
          v-if="!['ALL_DONE', 'PART_DONE'].includes(status)"
          style="display: inline-block; margin-left: 10px;"
          :action="action"
          :headers="token"
          :data="{ airDropId }"
          :on-success="onUploadSuccess"
          accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          :show-file-list="false"
        >
          <el-button type="primary" size="mini">导入</el-button>
        </el-upload>
      </template>
    </common-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'

export default {
  name: 'AirDropWorks',
  components: {
    CommonTable
  },
  data () {
    return {
      status: '', // 项目 status
      action: `${process.env.VUE_APP_BASE_URL}hanxin/adminApi/airDrop/missWebSign/item/import`,
      token: { AdminAuthorization: localStorage.getItem('usertoken') },
      tableData: [], // 表格数据
      query: {}, // 表格查询参数
      tableSchema: [ // 组件数据架构
        {
          label: '作品标题',
          field: 'title'
        },
        {
          label: '作品封面',
          field: 'cover',
          slot: 'cover'
        },
        {
          label: '作品 tid',
          field: 'tid'
        },
        {
          label: '作品价格',
          field: 'price'
        },
        {
          label: '作品版本号',
          field: 'serial'
        },
        {
          label: '状态',
          field: 'status',
          slot: 'status',
          type: 'tag',
          tagMap: {
            SUCCESS: { tagType: 'success', label: '成功' },
            FAIL: { tagType: 'danger', label: '失败' }
          }
        },
        {
          label: '失败原因',
          field: 'failReason'
        },
        {
          label: '作品类型',
          field: 'type',
          slot: 'type',
          type: 'tag',
          tagMap: {
            11: { label: '视频' },
            12: { label: '音频' },
            13: { label: '3D静态模型' },
            14: { label: '3D动态模型' },
            default: { label: '图片' }
          }
        },
        {
          label: '作品种类',
          field: 'mold',
          slot: 'mold',
          type: 'tag',
          tagMap: {
            1: { label: '普通作品' },
            2: { label: '盲盒' }
          }
        },
        {
          label: '创建时间',
          field: 'createAt'
        },
        {
          label: '修改时间',
          field: 'updateAt'
        },
        {
          label: '创建的管理员用户名',
          field: 'createAdminUser',
          width: 150
        },
        {
          label: '修改的管理员用户名',
          field: 'updateAdminUser',
          width: 150
        },
        {
          label: '操作',
          slot: 'action',
          width: 240,
          headerSlot: 'action-header',
          fixed: 'right'
        }
      ],
      dialogVisible: false,
      data: {
        cover: '',
        qrCode: ''
      },
      infoSchema: [
        { label: '名称：', field: 'name' },
        { label: '封面图：', field: 'cover', type: 'img' },
        { label: '二维码：', field: 'qrCode', type: 'img' },
        {
          label: '链接类型：',
          field: 'linkType',
          type: 'radio',
          options: [{ label: '无链接', value: 0 }, { label: '网页链接', value: 10 }]
        },
        { label: '跳转链接：', field: 'link', show: { relationField: 'linkType', value: 10 } },
        { label: '权重：', field: 'weight' }
      ],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      },
      isDetail: false,
      airDropId: '', // 当前空投项目 id
      isPreview: false // 是否预览
    }
  },
  computed: {
  },
  mounted () {
    this.airDropId = Number.parseInt(this.$route.params.airDropId)
    this.isPreview = this.$route.query.type === 'preview'
    this.status = this.$route.query.status
    this.getList()
  },
  methods: {
    async downloadTemplate () {
      const { status, result } = await this.$api.airDropItemTemplate()
      if (status.code === 0) {
        window.open(result.templateUrl, '_blank')
      }
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      console.log(this.$route.params.airDropId)
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        airDropId: this.$route.params.airDropId
      }
      const { status, result } = await this.$api.airDropAssetsList(params)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    deleteWorksItem (airDropItemId) {
      this.$confirm('删除后该项将在列表不可见', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.airDropAssetsDelete({ airDropId: this.airDropId, airDropItemId })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    addWorksItem () {
      this.$prompt('请输入空投作品 TID', '新增空投作品', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async ({ value }) => {
        const data = {
          tid: value,
          airDropId: this.airDropId
        }
        const { status } = await this.$api.airDropAssetsAdd(data)
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    async onUploadSuccess (response) {
      if (response.status.code === 0) {
        this.$message.success(response.status.msg)
        this.getList()
      } else {
        this.$message.error(response.status.msg)
      }
    },
    cleanWorksItem () {
      this.$confirm('空投作品记录清空', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.airDropItemClean({ airDropId: this.airDropId})
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
