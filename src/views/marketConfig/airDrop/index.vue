<template>
  <d2-container class="page">
    <common-query :query-schema="querySchema" @onSubmit="onQueryChange"></common-query>
    <common-table :table-data="tableData" :table-schema="tableSchema" >
      <template #users="scope">
        <router-link
          :to="{ name: 'AirDropUsers', params: { airDropId: scope.row.airDropId }, query: { status: scope.row.status } }"
        >查看</router-link>
      </template>
      <template #works="scope">
        <router-link
          :to="{ name: 'AirDropWorks', params: { airDropId: scope.row.airDropId }, query: { status: scope.row.status } }"
        >查看</router-link>
      </template>
      <template #result="scope">
        <router-link
          :to="{ name: 'AirDropPreview', params: { airDropId: scope.row.airDropId }, query: { status: scope.row.status } }"
        >查看</router-link>
<!--        <router-link-->
<!--          :to="{ name: 'AirDropPreview', params: { airDropId: scope.row.airDropId }, query: { type: 'preview', status: scope.row.status } }"-->
<!--          style="margin-left: 10px;"-->
<!--        >预览</router-link>-->
      </template>
      <template #status="scope">
        <el-tag v-if="scope.row.status === 'INIT'">创建初始</el-tag>
        <el-tag v-if="scope.row.status === 'DOING'">空投中</el-tag>
        <el-tag v-if="scope.row.status === 'ALL_DONE'">全部空投完成</el-tag>
        <el-tag v-if="scope.row.status === 'PART_DONE'">部分空投完成</el-tag>
      </template>
			<template #item-type="scope">
			  <el-tag v-if="scope.row.itemType === 'COLLECTION'">藏品</el-tag>
			  <el-tag v-if="scope.row.itemType === 'CREATION'">创作品</el-tag>
			</template>
      <template #isTiming="scope">
        <el-tag v-if="scope.row.isTiming === 0">否</el-tag>
        <el-tag v-if="scope.row.isTiming === 1">是</el-tag>
      </template>
      <template #action="scope">
        <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(scope.row.status)"  @click="addProject(scope.row.airDropId,scope.row)" type="text">修改</el-button>
        <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(scope.row.status)" @click="clearPreview(scope.row.airDropId)" type="text">清空结果</el-button>
        <el-button @click="exportResult(scope.row.airDropId)" type="text">导出结果</el-button>
        <el-button v-if="!['ALL_DONE'].includes(scope.row.status)" @click="executeAirDrop(scope.row.airDropId)" type="text">执行空投</el-button>
      </template>
      <template #action-header>
        <el-button @click="addProject()" type="primary" size="mini">新增</el-button>
      </template>
    </common-table>
   <el-dialog :title="isDialogTitle" :visible.sync="isDialog" center>
     <el-form :model="form">
       <el-form-item label="空投名称:" :label-width="formLabelWidth">
         <el-input v-model="form.airDropName" placeholder="请输入空投名" clearable style="width: 80%">
         </el-input>
       </el-form-item>
       <el-form-item label="是否定时:" :label-width="formLabelWidth">
         <el-radio v-model="form.isTiming" :label="1">是</el-radio>
         <el-radio v-model="form.isTiming" :label="0">否</el-radio>
       </el-form-item>
       <el-form-item label="定时时间:" :label-width="formLabelWidth" v-if="form.isTiming==1">
         <el-date-picker
               v-model="form.startTime"
               type="datetimerange"
               range-separator="至"
               start-placeholder="开始日期"
               value-format="yyyy-MM-dd HH:mm:ss.SSS"
               end-placeholder="结束日期">
             </el-date-picker>
       </el-form-item>
     </el-form>
     <div slot="footer" class="dialog-footer">
       <el-button @click="isDialog = false">取 消</el-button>
       <el-button type="primary" @click="add()">确 定</el-button>
     </div>
   </el-dialog>
   <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
   	<el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
   	 :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[20, 50, 100, 200,500,1000]"
   		style="padding: 20px; background-color: #ffffff" @current-change="currentChange" @size-change="xuanzeSize">
   	</el-pagination>
   </div>
  </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery'
import CommonTable from '@/components/CommonTable'
import { downloadBlob } from '@/utils/helper'
import FileUploader from '@/components/FileUploader'
export default {
  name: 'AirDrop',
  components: {
    CommonTable,
    CommonQuery,
    FileUploader
  },
  data () {
    return {
      tableData: [],
      query: {},
      querySchema: [
        {
          type: 'input',
          label: '空投名称',
          placeholder: '请输入空投名称',
          field: 'airDropName'
        },
        {
          type: 'input',
          label: '空投编号',
          placeholder: '请输入空投编号',
          field: 'airDropNo',
        },
        {
          type: 'select',
          label: '状态',
          field: 'status',
          placeholder: '请选择状态',
          options: [
            { label: '创建初始', value: 'INIT' },
            { label: '空投中', value: 'DOING' },
            { label: '全部空投完成', value: 'ALL_DONE' },
            { label: '部分空投完成', value: 'PART_DONE' }
          ]
        }
      ],
      tableSchema: [
        {
          label: '空投名称',
          field: 'airDropName',
           width: '180px'
        },
        {
          label: '空投编号',
          field: 'airDropNo',
          width: '180px'
        },
		{
		  label: '空投类型',
		  slot:'item-type',
		  field: 'itemType',
		  width:100
		},
        {
          label: '作品',
          slot: 'works',
          width: '50px'
        },
        {
          label: '用户',
          slot: 'users',
          width: '50px'
        },
        {
          label: '空投结果',
          slot: 'result',
          width: '50px'
        },
        {
          label: '创建时间',
          field: 'createAt',
          width: '180px'
        },
        {
          label: '修改时间',
          field: 'updateAt',
          width: '180px'
        },
        {
          label: '定时开始时间',
          field: 'startTime',
          width: '180px'
        },
        {
          label: '定时结束时间',
          field: 'endTime',
          width: '180px'
        },
        {
          label: '创建的管理员用户名',
          field: 'createAdminUser',
           width: '100px'
        },
        {
          label: '修改的管理员用户名',
          field: 'updateAdminUser',
           width: '100px'
        },
        {
          label: '状态',
          field: 'status',
          slot: 'status',
          width: '120px'
        },
        {
          label: '是否开启定时',
          field: 'isTiming',
          slot: 'isTiming',
          width: '120px'
        },
        {
          label: '操作',
          slot: 'action',
          headerSlot: 'action-header',
          fixed: 'right',
          width: '280px'
        }
      ],
      dialogVisible: true,
      data: {
        cover: '',
        qrCode: ''
      },
      infoSchema: [
        { label: '名称：', field: 'name' },
        { label: '封面图：', field: 'cover', type: 'img' },
        { label: '二维码：', field: 'qrCode', type: 'img' },
        {
          label: '链接类型：',
          field: 'linkType',
          type: 'radio',
          options: [{ label: '无链接', value: 0 }, { label: '网页链接', value: 10 }]
        },
        { label: '跳转链接：', field: 'link', show: { relationField: 'linkType', value: 10 } },
        { label: '权重：', field: 'weight' }
      ],
      page: {
        totalCount: 0,
        pageSize: 20,
        pageNum: 1
      },
      isDetail: false,
      action: `${process.env.VUE_APP_BASE_URL}hanxin//adminApi/airDrop/record/import`,
      token: { AdminAuthorization: localStorage.getItem('usertoken') },
      isDialog:false,
      isDialogTitle:"",
      formLabelWidth: '160px',
      form: {
        airDropName: '',
        isTiming: 0,
        startTime:''
      },
      apiName:''
    }
  },
  computed: {
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 过滤查询
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum
      }
      const { status, result } = await this.$api.airDropProjectList(params)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    // 分页切换
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    // 分页
    xuanzeSize(val) {
    	this.page.pageSize = val
    	this.getList()
    },
    // 添加、修改项目
    addProject (airDropId,item) {
      if(item){
        this.form.airDropName=item.airDropName
        this.form.isTiming=item.isTiming
        if(item.startTime&&item.endTime){
          this.form.startTime=[item.startTime,item.endTime]
        }else{
           this.form.startTime=""
        }
      }
      this.airDropId=airDropId
      this.isDialogTitle = airDropId ? '修改空投' : '新增空投'
      this.apiName = airDropId ? 'airDropProjectEdit' : 'airDropProjectAdd'
      this.isDialog=true

    },
    // 清空结果
    async clearPreview (airDropId) {
      const { status } = await this.$api.airDropResultDelete({ airDropId })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
      }
    },
    // 导出结果
    async exportResult (airDropId) {
      const res = await this.$api.airDropResultExport({ airDropId })
      if (res.type === 'application/json') {
        // blob 转 JSON
        const enc = new TextDecoder('utf-8')
        res.arrayBuffer().then(buffer => {
          const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
          this.$message.error(data.status?.msg)
        })
      } else {
        downloadBlob(res, '空投结果')
        this.$message.success('导出成功')
        this.getList()
      }
    },
    // 执行空投
    async executeAirDrop (airDropId) {
      const { status } = await this.$api.airDropExecuteProject({ airDropId })
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
      }
    },
    async downloadTemplate () {
      const { status, result } = await this.$api.userCenterDownLoadTemplate({
        templateTag:"AIR_DROP_RECORD_IMPORT"
      })
      if (status.code === 0) {
        window.open(result.emailsTemplateUrl, '_blank')
      }
    },
    // 空投记录导入
    async importTemplate(data) {
      console.error(data)
    	const {
    		status,
    		result
    	} = await this.$api.airDropImport({
    		importUrl: data.result?.url,
        airDropId:data.result?.id
    	})
    	if (status.code === 0) {
    		this.$message.success(status.msg)
    	}
    },
    async add(){
       let startTime,endTime
      if(this.form.isTiming==1){
        startTime=this.form.startTime[0]
        endTime=this.form.startTime[1]
      }
      console.log(this.form)
      const data = { airDropName: this.form.airDropName,startTime,endTime,isTiming:this.form.isTiming}
      this.airDropId && (data.airDropId = this.airDropId)
      const { status } = await this.$api[this.apiName](data)
      if (status.code === 0) {
        this.$message.success(status.msg)
        this.getList()
         this.isDialog=false
      }
    }
    // openDialog () {
    //   this.dialogVisible = true
    // },
    // closeDialog () {
    //   this.dialogVisible = false
    //   this.isDetail = false
    // }
  }
}
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  color: #409EFF;
}
</style>
