<template>
  <d2-container class="page">
    <common-table  :table-data="tableData" :table-schema="tableSchema">
      <template #cover="scope">
        <img :src="scope.row.cover" style="width: 100px;" :alt="scope.row.cover">
      </template>
      <template #action="scope">
        <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="deleteWorksItem(scope.row.airDropUserId)" type="text">删除</el-button>
      </template>
      <template #action-header>
        <el-button v-if="!['ALL_DONE', 'PART_DONE'].includes(status)" @click="addWorksItem" type="primary" size="mini">添加</el-button>
        <el-button @click="downloadTemplate" type="primary" size="mini">模版下载</el-button>
        <el-button @click="cleanUserItem" type="primary" size="mini">清空用户</el-button>
        <el-upload
          v-if="!['ALL_DONE', 'PART_DONE'].includes(status)"
          style="display: inline-block; margin-left: 10px;"
          :action="action"
          :headers="token"
          :data="{ airDropId }"
          :on-success="onUploadSuccess"
          accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          :show-file-list="false"
        >
          <el-button type="primary" size="mini">导入</el-button>
        </el-upload>
      </template>
    </common-table>

    <el-pagination
      @current-change="currentChange"
      layout="prev, pager, next"
      :total="page.totalCount">
    </el-pagination>
  </d2-container>
</template>

<script>
import CommonTable from '@/components/CommonTable'

export default {
  name: 'AirDropWorks',
  components: {
    CommonTable
  },
  data () {
    return {
      status: '', // 项目 status
      action: `${process.env.VUE_APP_BASE_URL}hanxin/adminApi/airDrop/missWebSign/user/import`,
      token: { AdminAuthorization: localStorage.getItem('usertoken') },
      tableData: [], // 表格数据
      query: {}, // 表格查询参数
      tableSchema: [ // 组件数据架构
        {
          label: '用户昵称',
          field: 'nickname'
        },
        {
          label: '用户邮箱',
          field: 'email'
        },
        {
          label: '手机号',
          field: 'phone'
        },
        {
          label: '状态',
          field: 'status',
          type: 'tag',
          tagMap: {
            SUCCESS: { label: '成功', tagType: 'success' },
            FAIL: { label: '失败', tagType: 'danger' }
          }
        },
        {
          label: '失败原因',
          field: 'failReason'
        },
        {
          label: '创建时间',
          field: 'createAt'
        },
        {
          label: '修改时间',
          field: 'updateAt'
        },
        {
          label: '创建的管理员用户名',
          field: 'createAdminUser'
        },
        {
          label: '修改的管理员用户名',
          field: 'updateAdminUser'
        },
        {
          label: '操作',
          slot: 'action',
          width: 240,
          headerSlot: 'action-header',
          fixed: 'right'
        }
      ],
      dialogVisible: false,
      data: {
        cover: '',
        qrCode: ''
      },
      infoSchema: [
        { label: '名称：', field: 'name' },
        { label: '封面图：', field: 'cover', type: 'img' },
        { label: '二维码：', field: 'qrCode', type: 'img' },
        {
          label: '链接类型：',
          field: 'linkType',
          type: 'radio',
          options: [{ label: '无链接', value: 0 }, { label: '网页链接', value: 10 }]
        },
        { label: '跳转链接：', field: 'link', show: { relationField: 'linkType', value: 10 } },
        { label: '权重：', field: 'weight' }
      ],
      page: {
        totalCount: 0,
        pageSize: 10,
        pageNum: 1
      },
      isDetail: false,
      airDropId: '' // 当前空投项目 id
    }
  },
  computed: {
  },
  mounted () {
    this.airDropId = Number.parseInt(this.$route.params.airDropId)
    this.status = this.$route.query.status
    this.getList()
  },
  methods: {
    async downloadTemplate () {
      const { status, result } = await this.$api.airDropUserTemplate()
      if (status.code === 0) {
        window.open(result.templateUrl, '_blank')
      }
    },
    onQueryChange (data) {
      console.log(data)
      this.query = data
      this.getList(true)
    },
    async getList (isInit) {
      const params = {
        ...this.query,
        ...this.page,
        pageNum: isInit ? 1 : this.page.pageNum,
        airDropId: this.$route.params.airDropId
      }
      const { status, result } = await this.$api.airDropUserList(params)
      if (status.code === 0) {
        this.tableData = result.list
        this.page.totalCount = result.totalCount
        this.page.pageSize = result.pageSize
        this.page.pageCount = result.pageCount
      }
    },
    currentChange (value) {
      this.page.pageNum = value
      this.getList()
    },
    deleteWorksItem (airDropUserId) {
      this.$confirm('删除后该项将在列表不可见', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.airDropUserDelete({ airDropId: this.airDropId, airDropUserId })
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    addWorksItem () {
      this.$prompt('请输入空投用户地址', '新增空投用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async ({ value }) => {
        const data = {
          contractAddress: value,
          airDropId: this.airDropId
        }
        const { status } = await this.$api.airDropUserAdd(data)
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
    async onUploadSuccess (response, file, fileList) {
      if (response.status.code === 0) {
        this.$message.success(response.status.msg)
        this.getList()
      } else {
        this.$message.error(response.status.msg)
      }
    },
    cleanUserItem () {
      this.$confirm('空投用户记录清空', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { status } = await this.$api.airDropUserClean({ airDropId: this.airDropId})
        if (status.code === 0) {
          this.$message.success(status.msg)
          this.getList()
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
