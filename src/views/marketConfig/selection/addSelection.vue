<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item
        label="contract_address:"
        :label-width="formLabelWidth"
        required
      >
        <el-input
          v-model="form.userContractAddress"
          placeholder="请输入用户地址"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="IP名称:" :label-width="formLabelWidth">
        <el-input
          v-model="form.userName"
          placeholder="请输入20字以内文字"
          clearable
          style="width: 500px"
          maxlength="20"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="IP图片:" :label-width="formLabelWidth">
        <el-upload
          :action="action"
          :headers="token"
          list-type="picture-card"
          :on-success="handlePicSuccess"
          :class="{ hide: hideUpload_introduce }"
          :on-change="handleIntroduceUploadHide"
          :on-remove="handleIntroduceRemove"
          :file-list="fileListImg"
          :before-upload="beforeAvatarUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item label="数据关联类型:" :label-width="formLabelWidth">
        <el-radio-group v-model="form.relType">
          <el-radio :label="0">关联当前用户全量商品</el-radio>
          <el-radio :label="1">根据excel导入</el-radio>
          <el-radio :label="null" v-if="this.$route.query.edit_id"
            >不做改变</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="表单数据:"
        :label-width="formLabelWidth"
        required
        v-if="this.form.relType == 1"
      >
        <span v-if="this.$route.query.edit_id">
          {{ form.goodCount }}
          <el-button
            style="margin-left: 20px"
            
            type="primary"
            @click="downLoadGoods"
            >下载表单</el-button
          >
        </span>
        <el-upload
          class="upload-demo"
          :action="excel_action"
          :before-remove="beforeRemove"
          :file-list="fileList"
          :headers="token"
          multiple
          :limit="1"
          :on-success="handleAvatarSuccess"
          :on-error="handleAvatarError"
        >
          <el-button  type="primary">选择上传文件</el-button>
        </el-upload>
        <el-button
          
          type="primary"
          @click="downLoadTemplate"
          v-if="!this.$route.query.edit_id"
          >下载模板</el-button
        >
      </el-form-item>
      <el-form-item
        label="上传类型:"
        :label-width="formLabelWidth"
        v-if="this.$route.query.edit_id && form.relType == 1"
      >
        <el-radio-group v-model="form.uploadType">
          <el-radio :label="0">覆盖新增</el-radio>
          <el-radio :label="1">单独新增</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="IP详情信息:" :label-width="formLabelWidth" required>
        <el-radio-group v-model="form.tipType">
          <el-radio :label="1">模版填写</el-radio>
          <el-radio :label="0">自定义信息</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="信息文案:"
        :label-width="formLabelWidth"
        required
        v-if="form.tipType == 0"
      >
        <el-input
          v-model="form.tipText"
          placeholder="请输入300字以内文字"
          clearable
          style="width: 500px"
          maxlength="300"
          show-word-limit
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="发售数量:"
        v-if="form.tipType !== 0"
        :label-width="formLabelWidth"
        required
      >
        <el-input
          v-model="form.count"
          placeholder="请输入100字以内文字"
          clearable
          style="width: 500px"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item
        label="发售价格:"
        v-if="form.tipType !== 0"
        :label-width="formLabelWidth"
        required
      >
        <el-input
          v-model="form.price"
          placeholder="请输入100字以内文字"
          clearable
          style="width: 500px"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item
        label="发售计划:"
        v-if="form.tipType !== 0"
        :label-width="formLabelWidth"
        required
      >
        <el-input
          v-model="form.plan"
          placeholder="请输入500字以内文字"
          clearable
          style="width: 500px"
          maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="排序:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.weight"
          placeholder="请输入排序"
          clearable
          style="width: 500px"
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="广告备注:" :label-width="formLabelWidth">
        <el-input
          v-model="form.remark"
          placeholder="请输入广告备注"
          clearable
          style="width: 500px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(return_click)"
          v-if="!look"
          >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit_click)"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
	import {
		downloadBlob
	} from '@/utils/helper'
  import {
  	mapActions
  } from 'vuex'
export default {
  name: 'addSelection',
  data () {
    return {
      hideUpload_introduce: false,
      limitCount: 1,
      dialogImageUrl: '',
      dialogVisible: false,
      form: {
        tipType: 1,
        uploadType: 0,
        relType: 0
      },
      excel_action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadExcel',
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      fileList: [],
      fileListImg: [],
      formLabelWidth: '180px',
      edit_id: ''
    }
  },
  mounted () {
    this.edit_id = this.$route.query.edit_id
    if (this.$route.query.edit_id) {
      this.detailMsgJob()
    }
  },
  methods: {
    ...mapActions('d2admin/page', ['close']),
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleAvatarSuccess (res, file, fileList) {
      if (res.status.code !== 0) {
        this.$message.error(res.status.msg)
      }
      this.form.excelUrL = res.result.url
    },
    handleAvatarError (res, file) {
      console.log(res, file)
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.avatar = res.result.smallImageUrl
    },
    handleIntroduceUploadHide (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    beforeAvatarUpload (file) {
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.form.avatar = ''
    },
    // 编辑详情
    async detailMsgJob () {
      const res = await this.$api.hotUserDetail({
        id: this.edit_id
      })
      this.form = res.result
      if (res.result.avatar !== '') {
        this.hideUpload_introduce = true
        this.fileListImg = [
          {
            name: '',
            url: res.result.avatar
          }
        ]
      }
    },
    // 取消返回
    return_click () {
      const { fullPath } = this.$route
       this.close({ tagName: fullPath })
      this.$router.back(-1)
    },
    // 新增/编辑确定
    submit_click () {
      this.$confirm(
        '提交保存后,该IP将显示在精选IP中,确认保存？',
        '确认提交保存',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.submitClick()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async submitClick () {
      if (!this.$route.query.edit_id) {
        await this.$api.insertHotUser({
          excelUrL: this.form.excelUrL,
          userContractAddress: this.form.userContractAddress,
          userName: this.form.userName,
          avatar: this.form.avatar,
          weight: this.form.weight,
          remark: this.form.remark,
          tipType: this.form.tipType,
          count: this.form.count,
          tipText: this.form.tipText,
          price: this.form.price,
          plan: this.form.plan,
          relType: this.form.relType,
          uploadType: 0
        })
        this.$message.success('新增成功')
        this.$router.push({
          name: 'selection'
        })
        const { fullPath } = this.$route
         this.close({ tagName: fullPath })
      } else {
        await this.$api.updateHotUser({
          id: this.form.id,
          excelUrL: this.form.excelUrL,
          userContractAddress: this.form.userContractAddress,
          userName: this.form.userName,
          avatar: this.form.avatar,
          weight: this.form.weight,
          remark: this.form.remark,
          tipType: this.form.tipType,
          count: this.form.count,
          tipText: this.form.tipText,
          price: this.form.price,
          plan: this.form.plan,
          relType: this.form.relType,
          uploadType: this.form.uploadType
        })
         const { fullPath } = this.$route
          this.close({ tagName: fullPath })
        this.$message.success('编辑成功')
        this.$router.push({
          name: 'selection'
        })
      }
    },
    // 下载表单
    async downLoadGoods () {
      const res = await this.$api.downLoadGoods({
        userContractAddress: this.form.userContractAddress
      })
      if (res.type === 'application/json') {
      	// blob 转 JSON
      	const enc = new TextDecoder('utf-8')
      	res.arrayBuffer().then(buffer => {
      		const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
      		this.$message.error(data.status?.msg)
      	})
      } else {
      	downloadBlob(res, '精选IP表单')
      	this.$message.success('导出成功')
      }
    },
    // 下载模板
    async downLoadTemplate () {
      const res = await this.$api.downLoadTemplateExcel({
        templateTag: 'HOT_GOODS_EXCEL_IMPORT'
      })
      window.location.href = res.result.emailsTemplateUrl
      this.$message.success('下载成功')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>
