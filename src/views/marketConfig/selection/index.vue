<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <!-- <el-button type="primary" @click="showHotWords()"
          >搜索关键词管理</el-button
        > -->
        <el-button type="primary" @click="showBounceTips()"
          >界面主弹窗</el-button
        >
        <el-button type="primary" @click="addSelection()">添加精选ip</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="userName"
        label="IP名称"
        align="center"
      ></el-table-column>
      <el-table-column prop="avatar" label="IP图片" align="center">
        <template scope="scope">
          <div style="width: 100%">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.avatar"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="goodCount"
        label="列表数据"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="userContractAddress"
        label="地址"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column prop="status" label="显示状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.status == 1">显示</el-tag>
          <el-tag v-if="scope.row.status == 0">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="weight"
        label="排序"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 1"
            >隐藏</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.status == 0"
            >显示</el-button
          >
          <el-button
            type="text"
            
            @click="del_click(scope.row)"
            style="color: red"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
    <el-dialog :title="title" :visible.sync="isDialog" center>
      <el-form :model="form" ref="form">
        <!-- <el-form-item
          v-if="title == '搜索关键词'"
          label="关键词:"
          :label-width="formLabelWidth"
          required
        >
          <el-input
            v-model="form.hotWords"
            placeholder="请输入关键词, 以,隔开"
            clearable
            style="width: 80%"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="弹框标题:" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.title"
            placeholder="请输入10个字以内文案"
            clearable
            style="width: 80%"
            maxlength="10"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="弹窗副标题:"
          :label-width="formLabelWidth"
          required
        >
          <el-input
            v-model="form.secTitle"
            placeholder="请输入30个字以内文案"
            clearable
            style="width: 80%"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="弹窗正文:" :label-width="formLabelWidth" required>
          <el-input
            v-model="form.content"
            placeholder="请输入500字以内正文"
            clearable
            style="width: 80%"
            maxlength="500"
            show-word-limit
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isDialog = false">取 消</el-button>
        <el-button type="primary" @click="debounceMethods(dialog_submit)" >确 定</el-button
        >
      </div>
    </el-dialog>
  </d2-container>
</template>

<script>
export default {
  name: 'selection',
  data () {
    return {
      tableData: [],
      total: "",
      status: '',
      isDialog: false,
      title: '',
      pageNum:1,
      form: {
        // hotWords: "",
      },
      formLabelWidth: '120px'
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 查询列表
    async getList () {
      const res = await this.$api.hotUserList({
        pageNum: this.pageNum,
        pageSize: 15
      })
      this.tableData = res.result.list
      console.log(res.result.totalCount)
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.pageNum=val
      this.getList()
    },
    addSelection () {
      this.$router.push({
        name: 'addSelection'
      })
    },
    // 点击编辑
    async audit_click (val) {
      this.$router.push({
        name: 'addSelection',
        query: {
          edit_id: val.id,
        }
      })
    },
    // 隐藏显示
    async hide_click (val) {
      if (val.status === 1) {
        this.status = 0
      } else {
        this.status = 1
      }
      const res = await this.$api.hideOrShowHotUser({
        userContractAddress: val.userContractAddress,
        status: this.status
      })
      this.getList()
      this.$message({
        type: 'success',
        message: res.status.msg
      })
    },
    // 删除
    del_click (val) {
      this.$confirm(
        '精选IP删除后，可能会导致其他模块已经设置的指定跳转路径失效,客户端将自动移除本IP,同时无法恢复历史记录，请谨慎操作。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.remove(val.userContractAddress)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async remove (code) {
      await this.$api.delHotUser({
        userContractAddress: code
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: '删除成功!'
      })
    },
    // async showHotWords() {
    //   this.title = "搜索关键词";
    //   const res = await this.$api.showHotWords({
    //     key: "hot_search_key",
    //   });
    //   if (res.status.code == 0) {
    //     this.form.hotWords = res.result.hotWords;
    //     this.isDialog = true;
    //   } else {
    //     this.$message.error(res.status.msg);
    //   }
    // },
    async showBounceTips () {
      this.title = '界面主弹窗'
      const res = await this.$api.showBounceTips({
        kingkongNo: '42680538173939713'
      })
      this.form = res.result
      this.isDialog = true
    },
    // 弹框确定
    async dialog_submit () {
      await this.$api.updateBounceTips({
        content: this.form.content,
        kingkongNo: 42680538173939713,
        secTitle: this.form.secTitle,
        title: this.form.title
      })
      this.isDialog = false
      this.form = {
        content: '',
        secTitle: '',
        title: ''
      }
      this.$message.success('编辑成功')
    }
  }
}
</script>

<style></style>
