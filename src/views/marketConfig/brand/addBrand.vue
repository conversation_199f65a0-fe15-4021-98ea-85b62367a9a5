<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item
        label="列表透出类型:"
        :label-width="formLabelWidth"
        required
      >
        <el-radio-group v-model="form.leftJoinType">
          <el-radio label="USER">IP</el-radio>
          <el-radio label="GOODS">作品</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="楼层名称:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.tabName"
          placeholder="请输入楼层名称"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="楼层副标题:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.tabDesc"
          placeholder="请输入楼层副标题"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="链接类型:" :label-width="formLabelWidth" required>
        <el-radio-group v-model="form.linkType" @change="radioChange">
          <el-radio :label="0">无链接</el-radio>
          <el-radio :label="10">自定义链接</el-radio>
          <el-radio :label="20">网页链接</el-radio>
        </el-radio-group>
        <div style="color: red; fontsize: 12px">只支持不需要登录的链接</div>
      </el-form-item>
      <el-form-item
        label="跳转路径h5以及网页跳转:"
        :label-width="formLabelWidth"
        v-if="form.linkType == 10 || form.linkType == 20"
        required
      >
        <el-input
          v-model="form.link"
          placeholder="请输入跳转路径h5以及网页跳转"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="IOS 安卓原生跳转路径："
        :label-width="formLabelWidth"
        v-if="form.linkType == 10"
      >
        <el-input
          v-model="form.nativeLink"
          placeholder="原生路径不存在无需填写，系统自动使用'H5以及网页跳转路径'"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="权重:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.weight"
          placeholder="请输入权重"
          clearable
          style="width: 500px"
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注:" :label-width="formLabelWidth">
        <el-input
          v-model="form.remark"
          placeholder="请输入备注"
          clearable
          style="width: 500px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(return_click)"
          v-if="!look"
          >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit_click)"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  name: 'addBrand',
  data () {
    return {
      formLabelWidth: '200px',
      form: {
        leftJoinType: 'USER',
        tabName: '',
        tabDesc: '',
        linkType: 10,
        link: '',
        nativeLink: '',
        weight: '',
        remark: ''
      },
      marketTabId: ''
    }
  },
  mounted () {
    this.marketTabId = this.$route.query.edit_id
    if (this.marketTabId) {
      this.tabDetail()
    }
  },
  methods: {
    radioChange () {
      this.form.link = ''
      this.form.nativeLink = ''
    },
    // 编辑详情
    async tabDetail () {
      const res = await this.$api.tabDetail({
        marketTabId: this.marketTabId
      })
      this.form = res.result
    },
    // 取消返回
    return_click () {
      this.$router.back()
    },
    // 新增编辑确定
    submit_click () {
      this.$confirm(
        '提交保存后,将创建本楼层,若想楼层信息展示在客户端还需创建楼层内作品或IP信息,确认保存？',
        '确认提交保存',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.submitClick()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async submitClick () {
      if (!this.$route.query.edit_id) {
        await this.$api.addMarketTab({
          moduleId: 4,
          tabName: this.form.tabName,
          tabDesc: this.form.tabDesc,
          leftJoinType: this.form.leftJoinType,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          weight: this.form.weight,
          remark: this.form.remark
        })
        this.$message.success('新增成功')
        this.$router.push({
          name: 'brand'
        })
      } else {
        await this.$api.updateMarketTab({
          marketTabId: this.marketTabId,
          moduleId: 4,
          tabName: this.form.tabName,
          tabDesc: this.form.tabDesc,
          leftJoinType: this.form.leftJoinType,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          weight: this.form.weight,
          remark: this.form.remark
        })
        this.$message.success('修改成功')
        this.$router.push({
          name: 'brand'
        })
      }
    }
  }
}
</script>

<style>
</style>
