<template>
  <d2-container class="page">
    <el-form
      :inline="true"
      class="demo-form-inline"
      style="background-color: #ffffff; padding: 20px"
    >
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <!-- <el-button type="primary" @click="add_brand()">添加品牌</el-button> -->
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      ref="multipleTable"
      tooltip-effect="dark"
      show-header
      border
      style="width: 100%"
     
      
    >
      <el-table-column
        fixed
        prop="id"
        label="id"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="tabName"
        label="楼层名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="tabDesc"
        label="副楼层标题"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="leftJoinTypeName"
        label="透出类型"
        align="center"
      ></el-table-column>
      <el-table-column label="透出IP/作品" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="look_click(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="leftJoinNum"
        label="透出IP/作品数"
        align="center"
      ></el-table-column>
      <el-table-column prop="linkType" label="链接类型" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.linkType == '0'">无链接</el-tag>
          <el-tag v-if="scope.row.linkType == '10'">自定义协议</el-tag>
          <el-tag v-if="scope.row.linkType == '20'">网页跳转</el-tag>
        </template></el-table-column
      >
      <el-table-column
        prop="link"
        label="跳转路径h5以及网页跳转"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nativeLink"
        label="IOS 安卓原生跳转路径"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注信息"
        align="center"
      ></el-table-column>
      <el-table-column prop="tabStatus" label="显示状态" align="center">
        <template scope="scope">
          <el-tag v-if="scope.row.tabStatus == 1">显示</el-tag>
          <el-tag v-if="scope.row.tabStatus == 0">隐藏</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="weight"
        label="排序"
        align="center"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text"  @click="audit_click(scope.row)"
            >编辑</el-button
          >
          <!-- <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.tabStatus == 1"
            >隐藏</el-button
          >
          <el-button
            type="text"
            
            @click="debounceMethods(hide_click, scope.row)"
            v-if="scope.row.tabStatus == 0"
            >显示</el-button
          >
          <el-button
            type="text"
            
            @click="del_click(scope.row)"
            style="color: red"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <div
      class=""
      style="display: flex; justify-content: center; background-color: #ffffff"
    >
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="15"
        style="padding: 20px; background-color: #ffffff"
        @current-change="xuanze"
        @size-change="xuanze"
      >
      </el-pagination>
    </div>
  </d2-container>
</template>

<script>
export default {
  name: 'brand',
  data () {
    return {
      tableData: [],
      total: 1,
      moduleId: 4,
      tabStatus: ''
    }
  },
  mounted () {
    this.getList(1)
  },
  methods: {
    // 查询列表
    async getList (page) {
      const res = await this.$api.floorList({
        pageNum: page,
        pageSize: 15,
        moduleId: this.moduleId
      })
      this.tableData = res.result.list
      this.total = res.result.totalCount
    },
    xuanze (val) {
      this.getList(val)
    },
    // 点击查看
    look_click (val) {
      console.log(val)
      if (val.leftJoinType === 'USER') {
        this.$router.push({
          name: 'brandIp',
          query: {
            marketTabId: val.id
          }
        })
      } else {
        this.$router.push({
          name: 'floorGoods',
          query: {
            marketTabId: val.id
          }
        })
      }
    },
    // 点击添加
    add_brand () {
      this.$router.push({
        name: 'addBrand'
      })
    },
    // 点击编辑
    async audit_click (val) {
      this.$router.push({
        name: 'addBrand',
        query: {
          edit_id: val.id
        }
      })
    },
    // 隐藏显示
    async hide_click (val) {
      if (val.tabStatus === 1) {
        this.tabStatus = 0
      } else {
        this.tabStatus = 1
      }
      console.log(val, this.tabStatus)
      const res = await this.$api.showMarketTab({
        marketTabId: val.id,
        tabStatus: this.tabStatus
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: res.status.msg
      })
    },
    // 删除
    del_click (val) {
      this.$confirm(
        '楼层删除后，客户端将自动移除本楼层，同时无法恢复历史记录。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.remove(val.id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async remove (code) {
      await this.$api.deleteMarketTab({
        marketTabId: code
      })
      this.getList(1)
      this.$message({
        type: 'success',
        message: '删除成功!'
      })
    }
  }
}
</script>

<style>
</style>
