<template>
  <d2-container class="page">
    <el-form :model="form" label-position="right" label-width="200px">
      <el-form-item label="链接类型:" :label-width="formLabelWidth" required>
        <el-radio-group v-model="form.linkType" @change="radioChange">
          <el-radio :label="0">无链接</el-radio>
          <el-radio :label="10">自定义链接</el-radio>
          <el-radio :label="20">网页链接</el-radio>
          <el-radio :label="40">IP地址</el-radio>
        </el-radio-group>
        <div style="color: red; fontsize: 12px">只支持不需要登录的链接</div>
      </el-form-item>
      <el-form-item
        label="IP名称:"
        :label-width="formLabelWidth"
        v-if="form.linkType == 40"
        required
      >
        <el-input
          v-model="form.name"
          placeholder="请输入IP名称"
          clearable
          style="width: 500px"
          maxlength="20"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item
        label="跳转路径h5以及网页跳转:"
        :label-width="formLabelWidth"
        v-if="form.linkType == 10 || form.linkType == 20"
        required
      >
        <el-input
          v-model="form.link"
          placeholder="请输入跳转路径h5以及网页跳转"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="IOS 安卓原生跳转路径："
        :label-width="formLabelWidth"
        v-if="form.linkType == 10"
      >
        <el-input
          v-model="form.nativeLink"
          placeholder="原生路径不存在无需填写，系统自动使用'H5以及网页跳转路径'"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="图片:" :label-width="formLabelWidth" required>
        <el-upload
          :action="action"
          :headers="token"
          list-type="picture-card"
          :on-success="handlePicSuccess"
          :class="{ hide: hideUpload_introduce }"
          :on-change="handleIntroduceUploadHide"
          :on-remove="handleIntroduceRemove"
          :file-list="fileListImg"
          :before-upload="beforeAvatarUpload"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item
        label="contract_address:"
        :label-width="formLabelWidth"
        required
        v-if="form.linkType == 40"
      >
        <el-input
          v-model="form.leftJoinId"
          placeholder="请输入用户地址"
          clearable
          style="width: 500px"
        ></el-input>
      </el-form-item>
      <el-form-item label="排序:" :label-width="formLabelWidth" required>
        <el-input
          v-model="form.weight"
          placeholder="请输入排序"
          clearable
          style="width: 500px"
          type="number"
        ></el-input>
      </el-form-item>
      <el-form-item label="广告备注:" :label-width="formLabelWidth">
        <el-input
          v-model="form.remark"
          placeholder="请输入广告备注"
          clearable
          style="width: 500px"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="debounceMethods(return_click)"
          v-if="!look"
          >取消返回</el-button
        >
        <el-button type="primary" @click="debounceMethods(submit_click)"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </d2-container>
</template>

<script>
export default {
  data () {
    return {
      form: {
        linkType: 40,
        name: '',
        leftJoinId: '',
        link: '',
        marketTabId: 7,
        marketTabType: 7,
        verbPic: '',
        weight: '',
        nativeLink: '',
        remark: ''
      },
      action:
        process.env.VUE_APP_BASE_URL +
        'osscenter/adminApi/missWebSign/uploadImage',
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      fileListImg: [],
      formLabelWidth: '200px',
      edit_id: '',
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload_introduce: false,
      limitCount: 1
    }
  },
  mounted () {
    this.edit_id = this.$route.query.edit_id
    if (this.$route.query.edit_id) {
      this.leftJoinDetail()
    }
  },
  methods: {
    radioChange () {
      this.form.link = ''
      this.form.nativeLink = ''
    },
    // 图片上传
    handlePicSuccess (res, file) {
      this.form.verbPic = res.result.url
    },
    handleIntroduceUploadHide (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
    },
    beforeAvatarUpload (file) {
      if (
        file.type !== 'image/jpeg' &&
        file.type !== 'image/png' &&
        file.type !== 'image/gif'
      ) {
        this.$message.error('只能上传jpg/png/GIF格式文件')
        return false
      }
    },
    // 图片移除
    handleIntroduceRemove (file, fileList) {
      this.hideUpload_introduce = fileList.length >= this.limitCount
      this.form.verbPic = ''
    },
    // 编辑详情
    async leftJoinDetail () {
      const res = await this.$api.leftJoinDetail({
        tabLeftJoinId: this.edit_id
      })
      if (res.status.code == 0) {
        this.form = res.result

        if (res.result.verbPic !== '') {
          this.hideUpload_introduce = true
          this.fileListImg = [
            {
              name: '',
              url: res.result.verbPic
            }
          ]
        }
      } else {
        this.$message.error(res.status.msg)
      }
    },
    // 取消返回
    return_click () {
      this.$router.back()
    },
    // 新增编辑确定
    submit_click () {
      this.$confirm(
        '提交保存后,该品牌将显示在客户端,确认保存?',
        '确认提交保存',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.submitClick()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    async submitClick () {
      if (!this.$route.query.edit_id) {
        const res = await this.$api.insertLeftJoin({
          name: this.form.name,
          leftJoinId: this.form.leftJoinId,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          marketTabId: 7,
          marketTabType: 7,
          verbPic: this.form.verbPic,
          weight: this.form.weight,
          remark: this.form.remark
        })
        if (res.status.code == 0) {
          this.$message.success('新增成功')
          this.$router.push({
            name: 'brand'
          })
        } else {
          this.$message.error(res.status.msg)
        }
      } else {
        const res = await this.$api.updateLeftJoin({
          id: this.edit_id,
          name: this.form.name,
          leftJoinId: this.form.leftJoinId,
          linkType: this.form.linkType,
          link: this.form.link,
          nativeLink: this.form.nativeLink,
          marketTabId: 7,
          marketTabType: 7,
          verbPic: this.form.verbPic,
          weight: this.form.weight,
          remark: this.form.remark
        })
        if (res.status.code == 0) {
          this.$message.success('修改成功')
          this.$router.push({
            name: 'brand'
          })
        } else {
          this.$message.error(res.status.msg)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .hide .el-upload--picture-card {
  display: none;
}
</style>
