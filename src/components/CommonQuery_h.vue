<script>
/**
 * @description: 选择器组件
 * @author: jay
 * @date: 2022-07-04
 * @update: 2022-07-04
 * @param {array} querySchema 查询架构，参数详情如下：interface querySchemaItem
 * @event onSubmit 查询参数变化时触发
 * @event onReset 重置参数时触发
 * @event onExport 导出列表时触发
 */

// interface querySchemaItem = {
//   type: 'input' ｜ 'select' ｜ 'datetimerange'
//   dataType?: string
//   label: string
//   field: string
//   field2: string // type为 datetimerange 时，field2为结束时间字段名称
//   placeholder?: string
//   options?: Array<{ label: string; value: string | number }>
// }
export default {
  name: 'CommonQuery',
  props: {
    querySchema: {
      type: Array,
      required: true
    },
    showExport: {
      type: Boolean,
      default: false
    },
    showSubmit: {
      type: Boolean,
      default: true
    },
    showReset: {
      type: Boolean,
      default: true
    },
    showRefresh: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    showCreation: {
      type: <PERSON><PERSON>an,
      default: false
    },
    data: { // form 数据
      type: Object,
      required: true
    }
  },
  data() {
    return {
      data: {}
    }
  },
  methods: {
    onSubmit() {
      this.$emit('onSubmit', JSON.parse(JSON.stringify({
        ...this.data,
        ...this.transformTime()
      })))
    },
    onReset() {
      this.data = {}
      this.$emit('onReset')
    },
    onExport() {
      console.log(this.data, '参数')
      this.$emit('onExport', JSON.parse(JSON.stringify({
        ...this.data,
        ...this.transformTime()
      })))
    },
    onRefresh() {
      this.$emit('onRefresh', JSON.parse(JSON.stringify({ ...this.data, ...this.transformTime() })))
    },
    onCreation() {
      this.$emit('onCreation')
    },
    transformTime() {
      const datetimerange = {}
      this.querySchema.forEach(item => {
        console.log(item,12312312312132);
        
        if (item.type === 'datetimerange' && this.data[item.field]) {
          const [start, end] = this.data[item.field]
          datetimerange[item.field] = `${start}.000`
          datetimerange[item.field2] = `${end}.000`
        }
      })
      console.log(datetimerange,12312312312132);
      
      return datetimerange
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants
      this.searchNew(queryString)
      let results = []
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results)
      }, 1000)
    },
    async querySearchAsync2(queryString, cb) {
      var restaurants = this.restaurants
      this.searchNew2(queryString)
      let results = []
      clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        results = this.results
        cb(results)
      }, 1000)
    },
    async searchNew2(str) {
      this.results = []
      if (str) {
        const res = await this.$api.searchGroup({
          groupName: str
        })
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                value: `${item.groupAccount}(${item.groupName})`
              })
            })
            console.log(this.results)
          }
        }
      }
    },
    handleSelect(item) {
      console.log(item)
    },
    async searchNew(str) {
      this.results = []
      if (str) {
        const res = await this.$api.searchPgc({
          name: str
        })
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                value: `${item.name}(${item.ctid})`
              })
            })
            console.log(this.results)
          }
        }
      }
    }
  }
}
</script>

<template>
  <div>
    <el-form :inline="true" :model="data">
      <el-form-item v-for="item in querySchema" :label="item.label" :key="item.field">
        <el-select  v-if="item.type === 'select'" v-model="data[item.field]" :placeholder="item.placeholder || '请选择'"
          clearable size="mini" :multiple="item.multiple">
          <el-option v-for="option in item.options" :key="option.label" :label="option.label" :value="option.value">
          </el-option>
        </el-select>

        <div v-else-if="item.type === 'twoinput'" style="display: flex;">
          <el-input :placeholder="item.placeholder || '请输入'" v-model="data[item.field[0]]" :type="item.dataType"
            clearable size="mini"></el-input>
          <span style="margin: 0 10px;">至</span>
          <el-input :placeholder="item.placeholder || '请输入'" v-model="data[item.field[1]]" :type="item.dataType"
            clearable size="mini"></el-input>
        </div>



        <el-input v-else-if="item.type === 'input'" :placeholder="item.placeholder || '请输入'" v-model="data[item.field]"
          :type="item.dataType" clearable size="mini"></el-input>
        <el-autocomplete style="width:340px;" v-else-if="item.type === 'search'" v-model="data[item.field]"
          :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect" size="mini">
        </el-autocomplete>

        <el-autocomplete style="width:340px;" v-else-if="item.type === 'Thesearch'" v-model="data[item.field]"
          :fetch-suggestions="querySearchAsync2" placeholder="请输入群聊名称/群ID" @select="handleSelect2" size="mini">
        </el-autocomplete>

        <el-date-picker size="mini" clearable v-else-if="item.type === 'datetimerange'"
          :format="item.format ? item.format : 'yyyy-MM-dd HH:mm:ss'"
          :value-format="item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'" v-model="data[item.field]"
          type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
        <div v-else-if="item.type === 'inputrange'" class="inputrange_div">
          <el-input size="mini" v-model="data[item.field]" :placeholder="item.placeholder || '请输入'"></el-input>
          <span>至</span>
          <el-input size="mini" v-model="data[item.field2]" :placeholder="item.placeholder2 || '请输入'"></el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" size="mini" v-if="showSubmit">查询</el-button>
        <el-button type="primary" @click="onReset" size="mini" v-if="showReset">清除</el-button>
        <el-button type="primary" @click="onExport" size="mini" v-if="showExport">导出</el-button>
        <el-button type="primary" @click="onRefresh" size="mini" v-if="showRefresh">刷新</el-button>
        <el-button type="primary" @click="onCreation" size="mini" v-if="showCreation">创建</el-button>

      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.inputrange_div {
  width: 310px;
  display: flex;
  justify-content: flex-start;

  span {
    margin: 0px 20px;
  }
}

</style>
