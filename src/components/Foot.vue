<template>
    <div class="footer">

        <div class="content">
            <div class="tops">
                <div class="left">
                    <!-- <img
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250321/4fa4903b6172115e5f0d1a2bb33da0e6_712x94.png" /> -->
                    <span>{{ $t("footer.contact_email") }}：<EMAIL></span>
                </div>
                <div class="right">
                    <div class="top">
                        <img @click="go_link('https://www.facebook.com/profile.php?id=61573121643985')"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/64d09ec118ef3226df52b7fc05a53cfe_120x120.png" />
                        <img @click="go_link('https://t.me/+U5S8KFdz7_AzMDM0')"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/9a454741efd2cc34e83bd6d56b0e6f8f_120x120.png" />
                        <img @click="go_link('https://x.com/pinkwallet168')"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/4da1fda556a0ae934f6723f017493f8c_120x120.png" />
                        <img @click="go_link('https://discord.gg/Z2PzMEaJ')"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/5611a0aac3eeb31bfb1f923ec84f51a5_120x120.png" />
                        <img @click="go_link('https://youtube.com/@pinkwallet-r1l?si=3CR5N_fuBxLbbBBM')"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/8d21b9ca0a64ebf099189fccd6168eb4_120x120.png" />
                    </div>
                    <div class="bom">{{ $t("footer.copyright") }}</div>
                </div>
            </div>

            <div class="footline"></div>
            <div class="footer_links">
                <div class="link_group" v-for="(section, index) in footerData" :key="index">
                    <span class="section_title">{{ section.title }}</span>
                    <ul>
                        <li v-for="(item, i) in section.items" :key="i">
                            <a class="span" :href="item.url" target="_blank" rel="noopener noreferrer">{{ item.label
                                }}</a>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- <div class="footerbottom">
                <span>Sertus Chambers, Governors Square, Suite # 5-204, 23 Lime Tree Bay Avenue, P.O. Box 2547, Grand
                    Cayman, KY1-1104, Cayman Islands</span>
                <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378020284782174208.png" alt="">
            </div> -->
        </div>

        <div class="footerbottom">
            <span>{{ $t("footer.address") }}</span>
            <!-- <img mode="widthFix"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250325/ea63d027d5001e118dde80bbc6961ebf_4916x841.png"
                alt=""> -->
                <img  mode="widthFix" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378020284782174208.png" alt="">

        </div>
    </div>
</template>

<script setup>
    import { reactive, computed } from "vue";
    import { useI18n } from "vue-i18n";
    const { t } = useI18n();

    const go_link = (url) => {
        window.open(url)
    }
    // const footerData = computed(() => [
    //     {
    //         title: t("bottom.title"),
    //         // items: [
    //         //     t("bottom.about"), t("bottom.cooperation"), t("bottom.career"), t("bottom.contact"),
    //         //     t("bottom.terms"), t("bottom.privacy"), t("bottom.disclaimer"), t("bottom.anti_fraud"), t("bottom.support"), "PinkWallet App"
    //         // ]
    //         items: [

    //             t("bottom.terms"), t("bottom.privacy")
    //         ]
    //     },
    //     {
    //         title: t("bottom.title2"),
    //         items: [
    //             // t("bottom.buy_crypto"), t("bottom.flash_exchange"), t("bottom.forex"), t("bottom.contact_us"),
    //             // t("bottom.business_account"), t("bottom.newbie_guide"), t("bottom.standard_contract"), t("bottom.spot_trading"), t("bottom.smart_following")
    //         ]
    //     },
    //     {
    //         title: t("bottom.title3"),
    //         items: [
    //             // t("bottom.rewards"), t("bottom.broker_application"), t("bottom.benefits_center"),
    //             // t("bottom.institution_services"), t("bottom.vip_services")
    //         ]
    //     },
    //     {
    //         title: t("bottom.title4"),
    //         // items: [
    //         //     t("bottom.regulation"), t("bottom.privacy_terms"), t("bottom.user_agreement"), t("bottom.privacy_statement"), t("bottom.risk_warning")
    //         // ]
    //         items: [
    //           t("bottom.privacy_terms"), t("bottom.user_agreement"), t("bottom.privacy_statement")
    //         ]
    //     }
    // ]);

    const footerData = computed(() => [
        {
            title: t("footer.about"),
            items: [
                { label: t("footer.about_us"), url: "/#/aboutUs" },
                // { label: "就业机会", url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.terms"), url: "https://res.pinkwallet.com/pdf/Terms_of_Use_Pinkwallet_2025.pdf" },
                { label: t("footer.privacy"), url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.disclaimer"), url: "" }
            ]
        },
        {
            title: t("footer.products"),
            items: [
                { label: t("footer.quick_transfer"), url: "/#/Remittance" },
                { label: t("footer.stock"), url: "/#/stock" },
                { label: t("footer.flash_exchange"), url: "/#/flashExchange" },
                // { label: "数字货币价格查询", url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.pink_card"), url: "/#/pinkCard" }
            ]
        },
        {
            title: t("footer.services"),
            items: [
                { label: t("footer.submit_request"), url: "/#/suggest" },
                { label: t("footer.help_center"), url: "/#/help" },
                // { label: "支持中心", url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.user_feedback"), url: "/#/suggest" },
                // { label: "快讯", url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.college"), url: "/#/college" },
                // { label: "邀请好友", url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" }
            ]
        },
        // {
        //     title: t("bottom.title"),
        //     items: [
        //         { label: t("bottom.terms"), url: "https://res.pinkwallet.com/pdf/Terms_of_Use_Pinkwallet_2025.pdf" },
        //         { label: t("bottom.privacy"), url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" }
        //     ]
        // },
        {
            title: t("footer.legal"),
            items: [
                { label: t("footer.privacy_terms"), url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" },
                { label: t("footer.user_agreement"), url: "https://res.pinkwallet.com/pdf/Terms_of_Use_Pinkwallet_2025.pdf" },
                { label: t("footer.privacy_statement"), url: "https://res.pinkwallet.com/pdf/PRIVACY_POLICY_Pinkwallet_2025.pdf" }
            ]
        }
    ]);

</script>

<style lang="scss" scoped>
    .footer {
        // padding: 68px 123px 139px 119px;
        // padding: 32px 117px 15px 108px;
        // padding: 32px 0 15px 0;
        display: flex;
        justify-content: center;
        // height: 754px;
        flex-direction: column;
        background: #FF95B2;
        // background: #fff;

        min-height: 100vh;
        .content {
            padding: 32px 117px 100px 108px;

            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;

            .tops {
                width: 100%;
                display: flex;
                justify-content: space-between;

                .right {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;

                    .top {
                        gap: 22px;
                        display: flex;
                        align-items: center;

                        img {
                            width: 30px;
                            cursor: pointer;
                            height: 30px;

                        }
                    }

                    .bom {
                        margin-top: 13px;
                        font-family: Inter;
                        cursor: pointer;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 100%;
                        letter-spacing: -0.43px;
                        color: #000;
                    }
                }

                .left {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    img {
                        width: 178px;
                        cursor: pointer;
                        height: 23px;
                    }

                    span {
                        margin-top: 20px;
                        cursor: pointer;
                        font-family: Inter;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 100%;
                        letter-spacing: -0.43px;
                        color: #000;
                    }
                }
            }

            .footline {
                width: 100%;
                height: 1px;
                background: #000;
                margin: 26px 0 32px 0;
                // padding: 0 117px 0 108px;

            }

            .footer_links {
                width: 100%;
                display: flex;
                // justify-content: space-between;
                justify-content: space-around;
                flex-wrap: nowrap;
                text-align: left;
                // padding: 10px 0;

                .link_group {
                    // flex: 1;
                    min-width: 268px;
                    // margin-right: 111px;

                    &:nth-of-type(2) {
                        >ul {
                            >li {
                                cursor: none;

                                &:nth-of-type(7) {

                                    span {
                                        color: rgba(0, 0, 0, .3);
                                    }
                                }

                                &:nth-of-type(9) {
                                    color: rgba(0, 0, 0, .3);

                                }

                                &:nth-of-type(8) {
                                    color: rgba(0, 0, 0, .3);

                                }
                            }


                        }
                    }

                    .section_title {
                        font-family: MiSans;
                        font-weight: 700;
                        font-size: 16px;
                        line-height: 24px;
                        letter-spacing: 0px;
                        display: block;
                        padding-bottom: 24px;
                        color: #000;
                        // text-transform: uppercase;
                    }

                    ul {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            // margin-bottom: 5px;
                            margin-bottom: 12px;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            color: rgba(0, 0, 0, .8);

                            a {
                                color: rgba(0, 0, 0, .8);

                            }

                            .span {
                                line-height: 16px;
                                text-decoration: none;
                                font-family: MiSans-normal;
                                font-weight: 400;
                                font-size: 14px;
                                line-height: 16px;
                                letter-spacing: 0px;
                                cursor: pointer;
                                // &:hover {
                                //     text-decoration: underline;
                                // }
                            }
                        }
                    }
                }
            }

            .footerimg {
                width: 100%;
                margin-top: 243px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .footerbottom {
            margin: 58px 100px 59px 90px;
            display: flex;
            flex-direction: column;

            span {
                text-align: left !important;
                font-family: Inter;
                font-weight: 400;
                font-size: 16px;
                line-height: 100%;
                letter-spacing: -0.43px;
                color: rgba(0, 0, 0, .6);
                // margin-left: 15px;
            }

            img {
                margin-top: 30px;
                // width: 100%;
                // height: 210px;
                // width: 1229px;
                width: 100%;
                // max-width: 1229px; // 最大宽度 1229px
                height: auto; // 保持原始比例，避免变形
            }
        }

    }
</style>