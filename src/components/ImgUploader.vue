<script>
/**
 * @description: 图片上传
 * @author: jay
 * @date: 2022-07-08
 * @update: 2022-07-25
 * @param {number} limit 允许上传的图片数量。默认 1，传 0 不限制
 * @param {array | number} value 上传图片地址，limit 为 1 时为 string，其他情况为 array。返回阿里云图片地址
 * @param {string} accept 接受的文件类型，默认为图片类型，可以传入 'image/jpeg,image/png,image/gif'
 */
import { Message } from 'element-ui'
import router from '@/router'

export default {
  props: {
    listType: { // 列表类型
      type: String,
      default: 'picture-card'
    },
    limit: { // 最大上传数量，默认 1，传 0 不限制
      type: Number,
      default: 1
    },
    multigraph: { // 返回一个对象，包含3张图，大图中图小图
      type: Boolean,
      default: false
    },

    value: { // 上传图片地址
      type: [Array, String],
      // required: true
    },
    accept: { // 文件类型
      type: String,
      default: 'image/*'
    },
    isHD: { // 是否是高清图
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      action: `${process.env.VUE_APP_BASE_URL}osscenter/adminApi/missWebSign/uploadImage`,
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      uploadHide: false,
      init: true,
      fileList: []
    }
  },
  mounted() {
  },
  methods: {
    onSuccess(res, file) {
      if (res.status && res.status.code === 0) {
        if (this.multigraph) {
          const result = this.limit === 1 ? res.result?.smallImageUrl : this.fileList.map(item => item.response?.result?.smallImageUrl || item.smallImageUrl)
          localStorage.setItem('img_result', JSON.stringify(res.result))
          this.$emit('update:value', result)
        } else {
          console.log(this.isHD)
          const result = this.limit === 1 ? this.isHD ? res.result?.url : res.result?.smallImageUrl : this.fileList.map(item => item.response?.result?.smallImageUrl || item.smallImageUrl)
          console.log(result)
          this.$emit('update:value', result)
          this.$emit('success')
        }

      } else if (res.status?.code === 1002) {
        Message.error(res?.status?.msg)
        router.push('/login')
      }
    },
    // 图片移除
    onRemove(file, fileList) {
      this.fileList = fileList
      this.uploadHide = fileList.length >= this.limit
      const result = this.limit === 1 ? '' : this.fileList.map(item => item.response?.result?.url || item.url)
      this.$emit('update:value', result)
    },
    onChange(file, fileList) {
      this.fileList = fileList
      this.uploadHide = fileList.length >= this.limit
    },
    beforeUpload(file) {
    },
    initFileList(value) { // 初始化 fileList
      if (this.limit === 1) {
        value && (this.fileList = [{ url: value }])
      } else {
        console.log(value)
        value && (this.fileList = value.map(item => ({ url: item })))
      }
      this.init = false
    }
  },
  watch: {
    value: {
      handler(value) {
        const exist = this.limit === 1 ? value : value.length > 0
        this.init && exist && this.initFileList(value)
      },
      immediate: true
    }
  }
}
</script>

<template>
  <el-upload :action="action" :headers="token" :list-type="listType" :on-success="onSuccess" :on-change="onChange"
    :on-remove="onRemove" :before-upload="beforeUpload" :limit="limit" class="uploader" :accept="accept"
    :class="{ hide: limit !== 0 && (uploadHide || limit === fileList.length) }" :file-list="fileList">
    <i class="el-icon-plus"></i>
  </el-upload>
</template>

<style lang="scss" scoped>
.uploader {
  &::v-deep.hide .el-upload--picture-card {
    display: none;
  }
}
</style>
