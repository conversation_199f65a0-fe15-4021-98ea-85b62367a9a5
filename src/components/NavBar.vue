<template>
  <div class="header">
    <img src="https://cdn-lingjing.nftcn.com.cn/image/20241031/9d3ea4c94d89532f9ffc5fe483e0eeb0_26x50.png" mode="widthFix" class="left" @click="goBack()" v-if="props.haveBack" />
    <img src="https://cdn-lingjing.nftcn.com.cn/image/20220713/91f17b4344265b25b81a0ca4eb366304_64x64.png" mode="widthFix" class="right" @click="openShare()" v-if="props.haveShare" />
  </div>
</template>

<script setup>
/**
 * @description 头部按钮组件（返回、分享）
 * @property {Boolean} haveBack 控制返回按钮显隐
 * @property {Boolean} haveShare 控制分享按钮显隐
 */
import { defineProps, defineEmits } from 'vue';
import { goBack } from '@/utils/jump';

const props = defineProps({
  haveBack: {
    type: Boolean,
    default: true,
  },
  haveShare: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['share']);
const openShare = () => {
  emit('share');
};
</script>

<style lang="scss" scoped>
  .header {
    width: 100%;
    position: fixed;
    top: 0;
    padding: px2vw(28px) px2vw(32px);
    z-index: 500;
    img {
      width: px2vw(34px);
    }
    .right {
      position: fixed;
      right: px2vw(30px);
    }
  }
</style>
