<template>
	<van-popup v-model:show="show" class="popup_body_bottom" position="bottom" border-radius="0">
		<div class="bodyPop_password">
			<div class="title">
				<div class="colse" @click="colse()">
					<img src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240219/dc022946fdd0d661192b699cce40ee76_200x200.png" alt="" srcset="" />
				</div>
				支付密码
			</div>
			<van-password-input
			  :value="value"
			  :focused="showKeyboard"
			  :error-info="errorInfo"
			  @focus="showKeyboard = true"
			   info="请输入余额支付密码,用于支付"
			/>
			<van-number-keyboard 
			  v-model="value"
			  :show="showKeyboard"
			/>
		</div>
	</van-popup>
 
</template>

<script setup>
import { ref,watch,computed,defineProps } from 'vue';
const value = ref('');
const showKeyboard = ref(true);
const errorInfo = ref();
const props = defineProps({
  payPassword: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:payPassword','pay']);
const show = computed({
  get() {
    return props.payPassword;
  },
  set(value) {
    emit('update:payPassword', value);
  },
});
 watch(value, (newVal) => {
      if (newVal.length === 6) {
		 emit('pay',value.value);
		 setTimeout(()=>{
			 value.value=""
		 },500)
      } else {
        errorInfo.value = '';
      }
    });
const openShare = () => {
  emit('share');
};
const colse=()=>{
	show.value=false 
} 
</script>

<style lang="scss" >
	::v-deep .popup_body_bottom {
		height:px2vw(700px);
		width:100%;
		// background-color: #fff;
	}
	.bodyPop_password{
		height:px2vw(860px);
		width:100%;
		background-color: #1E1E1E;
		padding-top:px2vw(20px);
		.title{
			text-align: center;
			position: relative;
			height:px2vw(100px);
			line-height: px2vw(100px);
			color:#fff;
			margin-bottom:px2vw(40px);
			.colse{
				position: absolute;
				right:px2vw(40px);
				top:px2vw(10px);
				img{
					width:px2vw(40px);
					
				}
			}
			
		}
	}
	.van-password-input__item{
		background: #2B2B2B !important;
		border:none !important;
	}
	.van-password-input__security i{
		background:#fff !important;
	}
	.van-number-keyboard{
		background-color: #1E1E1E;
	}
	.van-password-input__info{
		color:#fff;
	}
	.van-key{
		background: #616161;
		color:#fff;
	}
</style>
