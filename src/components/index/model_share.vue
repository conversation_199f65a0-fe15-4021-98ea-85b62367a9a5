<template>
	<view>
		<u-popup class="pinlun_model" v-model="show" mode="bottom" border-radius="14" :closeable="true">
			<u-toast ref="uToast" />
			<view class="model_header">
				分享到
			</view>
			<view class="model_content">
				<view class="type_send">
					<!-- <view class="item" @click="to_msg()">
            <view class="bg">
              <image src="../../static/imgs/public/instation.png" mode="widthFix"></image>
            </view>
            <text>站内好友݋</text>
          </view> -->
					<!-- <view class="item" @click="openMaskCert(1)" v-if="isItem">
            <view class="bg">
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20220610/6abe192a3542297bef7c18e8e352ef50_96x96.png" mode="widthFix"></image>
            </view>
            <text>微信好友</text>
          </view>
          <view class="item" @click="openMaskCert(2)" v-if="isItem">
            <view class="bg">
              <image src="https://cdn-lingjing.nftcn.com.cn/image/20220610/587ae4e37bb0383a4f7e5a7baf62dbb7_96x96.png" mode="widthFix"></image>
            </view>
            <text>朋友圈</text>
          </view> -->
					<!-- <view class="item" @click="to_msg()">
            <view class="bg">
              <image src="../../static/imgs/public/qq.png" mode="widthFix"></image>
            </view>
            <text>QQ好友</text>
          </view>
          <view class="item" @click="to_msg()">
            <view class="bg">
              <image src="../../static/imgs/public/weibo.png" mode="widthFix"></image>
            </view>
            <text>微博</text>
          </view> -->
					<view class="item" @click="copy()">
						<view class="bg">
							<image
								src="https://cdn-lingjing.nftcn.com.cn/image/20220610/2b76898b23d9b3c020010edea81ec274_96x96.png"
								mode="widthFix"></image>
						</view>
						<text>分享链接</text>
					</view>
					<view class="item" @click="openMaskCert()" v-if="isItem">
						<view class="bg">
							<image src="../../static/imgs/public/Share_pic.png" mode="widthFix"></image>
						</view>
						<text>生成海报</text>
					</view>
					<view class="item" @click="nav_report()" v-if="isReport">
						<view class="bg">
							<image src="../../static/imgs/public/report.png" mode="widthFix"></image>
						</view>
						<text>举报</text>
					</view>
					<!-- <view class="item" @click="to_msg()">
            <view class="bg">
              <image src="../../static/imgs/public/quxiaogz.png" mode="widthFix"></image>
            </view>
            <text>取消关注</text>
          </view> -->
					<!-- <view class="item" @click="nav_report()" v-if="isItem">
            <view class="bg">
              <image src="../../static/imgs/public/inform.png" mode="widthFix"></image>
            </view>
            <text>举报</text>
          </view>
          <view class="item" @click="to_msg()">
            <view class="bg">
              <image src="../../static/imgs/public/collect.png" mode="widthFix"></image>
            </view>
            <text>收藏</text>
          </view> -->
				</view>
			</view>
			<!-- <view class="cancel_btn" @click="show=false">取消</view> -->
		</u-popup>
		<!-- 遮罩 -->
		<!-- 	<u-mask :show="isShowMask" z-index="999" @click="closeMask" style="background: linear-gradient(135deg, #2A2A38 0%, #273641 47%, #1E2630 100%);"></u-mask> -->
		<view v-if="isShowMask" @click="closeMask" class="shareMask"></view>
		<!-- 作品分享 -->
		<!-- <div ref="goodsDetail" > -->
		<!--  -->
		<view ref="goodsDetail" class="modal-cert" @click="closeMask">
			<view class="cert_top">
				<view class="time" v-if="isNormal">存证时间:{{existingevidenceList.created_at}}</view>
				<view class="photoShow">
					<image :src="existingevidenceList.cover" mode="aspectFill" class="img"></image>
					<img src="@/static/imgs/public/share_new.png" class="icon">
				</view>
				<view class="price">￥{{existingevidenceList.price}}</view>
				<view class="content_title">
					<view class="type_tip" v-if="isBind">盲盒</view>
					<view class="cert_name">{{existingevidenceList.title}}</view>
				</view>
			</view>
			<view class="user">
				<image :src="existingevidenceList.u_avatar" mode="acseptFit" />
				<view>{{existingevidenceList.c_nickname}}</view>
			</view>
			<view class="line"></view>
			<view class="cert_end">
				<view class="cert-left">
					<view class="left_top">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20220708/46ef91a054e48d9e285f673aafa0ce18_48x48.png"
							mode="aspectFit" />
						NFTCN
					</view>
					<view class="left-tip">扫码探索更多数字藏品</view>
				</view>
				<view class="qrimg-i">
					<canvas id="grcode" canvas-id="grcode"
						:style="{ width: `${size}px`, height: `${size}px` }"></canvas>
				</view>
			</view>
		</view>
		<!-- </div> -->
		<view v-show="isShowMaskCert" class="autoImg">
			<img :src="downImg" mode="scaleToFill" />
		</view>
		<!-- 个人中心分享 -->
		<view v-if="person" ref="personModal" id="personModal" class="modal_person" @click="closeMask">
			<view class="person_top">
				<view class="person_img">
					<image :src="person.avatar" mode="aspectFit" />
				</view>
				<image class="bg_img" :src="person.backImage" mode="widthFill" />
				<view class="person_head">
					<view class="person_content">
						<view class="person_name">{{person.name}}</view>
						<view class="person_des">{{person.intro}}</view>
					</view>
					<view class="person_meng"></view>
				</view>
				<view class="person_tip">{{person.name}}邀请您来参观数字藏品～</view>
				<view class="line"></view>
			</view>
			<view class="person_bottom">
				<view class="mcs-left">
					<view class="left_top">
						<image
							src="https://cdn-lingjing.nftcn.com.cn/image/20220708/46ef91a054e48d9e285f673aafa0ce18_48x48.png"
							mode="aspectFit" />
						NFTCN
					</view>
					<view class="left-tip">扫码探索更多数字藏品</view>
				</view>
				<view class="qrimg-i">
					<canvas id="qrcode" canvas-id="qrcode"
						:style="{ width: `${size}px`, height: `${size}px` }"></canvas>
				</view>
			</view>
		</view>
		<view v-show="isShowMaskPerson" class="person_autoImg">
			<img :src="downImg" mode="scaleToFill" />
			<view class="auto-img-save" @click="downloadImg(downImg)">
				<view class="auto-img-save-icon">
					<u-icon name="download" size="48" color="#1FEDF0"></u-icon>
				</view>
				保存海报
			</view>
		</view>
		<!-- 藏品分享 -->
		<model-works v-show="workList.length" ref="moreWorks" :list="workList" :info="person" :url="url" />
		<view v-show="isShowWorks" class="work_autoImg">
			<img :src="downImg" mode="scaleToFill" />
		</view>

	</view>
</template>


<script>
	import html2canvas from 'html2canvas';
	import uQRCode from 'uqrcodejs';
	import uniCopy from '@/js_sdk/uni-copy.js';
	import modelWorks from './model_works.vue'
	export default {
		components: {
			modelWorks
		},
		props: {
			tid: {
				type: String,
				default: ""
			},
			behavior_content: {
				type: Object,
				default: () => {},
			},
			person: {
				type: Object,
				default: () => {}
			},
			info: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				show: false,
				id: '',
				idType: '',
				isShowMask: false, // 遮罩
				isShowMaskCert: false,
				isShowWorks: false, // 藏品分享
				val: "", // 要生成的二维码值
				size: 66, // 二维码大小
				src: '', // 二维码生成后的图片地址或base64
				url: "",
				copyLink: "", // 复制的链接
				existingevidenceList: "",
				isShowModalBubble: false,
				isItem: true,
				contract_address: "",
				isShowMaskPerson: false,
				isPerson: false, // 是否是个人中心页
				isBind: false, // 是否是盲盒
				isSerise: false, // 是否是系列
				isNormal: false, // 是否是普通系列
				isMoreShare: false, // 是否多作品分享
				downImg: '', // 图片地址
				workList: [], // 作品list
				shareType: '', // 分享类型
				isReport: false //举报按钮
			}
		},
		onLoad(options) {
			this.url = getApp().globalData.url
			// this.contract_address=options.contract_address
		},
		methods: {
			downloadImg(url, name) {
				const element = document.createElement('a')
				element.href = url
				element.style.display = 'none'
				element.download = name || '海报.png'
				document.body.appendChild(element)
				element.click()
				document.body.removeChild(element)
			},
			// 埋点品牌
			eventTracking(acceptId) {
				this.$slsTracker({
					behaviorType: 'share12',
					pageId: 'newbrands',
					eventType: 'click',
					status: 1
				}, {
					accept_id: acceptId
				})
			},
			async open(e, idValue, tab, arr, type) {
				// e - 页面类型, 如果是个人中心分享tab 0-创造 1-藏品, arr: 个人中心藏品列表分享数据, type 分享类型
				this.shareType = type
				this.show = true
				this.id = idValue;
				this.idType = e;
				if (e == 1) { //看别人传人
					this.isPerson = true;
					this.url = getApp().globalData.url +
						"#/pagesA/project/personal/social/otherPeople?contract_address=" + idValue + "&tabCurrent=" +
						tab;
				} else if (e == 2) { //个人中心
					this.workList = arr;
					if (tab === 1 && arr.length) {
						this.isMoreShare = true
						this.isPerson = false;
					} else {
						this.isPerson = true;
						this.isMoreShare = false;
					}
					this.val = this.url = getApp().globalData.url +
						"#/pagesA/project/personal/social/otherPeople?contract_address=" + uni.getStorageSync(
							"contract_address") + "&tabCurrent=" + tab;
				} else if (e == 3) { //详情页
					this.isNormal = true;
					this.isReport = true
					this.url = getApp().globalData.url + "#/pagesA/project/mall/mallDetails?tid=" + idValue
				} else if (e == 4) { //活动页复制地址
					this.isItem = false
					this.url = window.location.href
				} else if (e == 6) { //盲盒分享
					this.isBind = true;
					this.getBlind(idValue);
					this.url = window.location.href
				} else if (e == 7) { //系列分享
					this.isSerise = true;
					this.getSeries(idValue);
					this.url = (window.location.href).replace('isSelf=1', 'isSelf=0')
				} else {
					// e = 5 吉物仓/版权
					this.isItem = false
					this.url = window.location.href
				}
				// 复制的分享链接转换
				this.copyLink = this.url;
				if (this.copyLink.indexOf('?') === -1) {
					this.copyLink = `${this.copyLink}?`;
				}
				// 添加埋点 1-微信号 2-朋友圈 3-分享链接 4-海报
				// this.url = `${this.url}&shared_contract_address=${uni.getStorageSync("contract_address")}&accept_id=3`;
				this.copyLink = await this.getShortLink(
					`${this.copyLink}&shared_contract_address=${uni.getStorageSync("contract_address")}&accept_id=3`
				)
			},
			to_msg() {
				this.$refs.uToast.show({
					title: '功能尚未开放',
					type: 'default',
				})
			},
			nav_report() {
				this.show = false
				this.$Router.push({
					name: "report",
					params: {
						tid: this.tid
					}
				})
			},
			copy() {
				uniCopy({
					content: this.copyLink,
					success: (res) => {
						uni.showToast({
							title: "链接复制成功,分享给朋友吧",
							icon: 'none'
						})
						if (this.shareType === 'brand') {
							this.eventTracking('accept_type3=分享链接')
						}
						switch (this.idType) {
							case 2:
								this.$slsTracker(
									"myhomecreate",
									"myhome_create",
									"",
									"click",
									"share11", {
										accept_id: "accept_type3",
									},
									1
								);
								break;
							case 3:
								this.$slsTracker(
									"workdetails",
									"work_details",
									"",
									"click",
									"share1", {
										// shared_contract_address: localStorage.contract_address,
										item_id: this.behavior_content.item_id,
										item_name: this.behavior_content.item_name,
										collection_id: this.behavior_content.collection_id,
										collection_name: this.behavior_content.collection_name,
										creator_id: this.behavior_content.creator_id,
										creator_name: this.behavior_content.creator_name,
										price: this.behavior_content.price,
										accept_id: 'accept_type3',
									},
									1
								);
								break;
							case 5:
								this.$slsTracker(
									"choiceip",
									"market_choiceip_ip",
									"",
									"click",
									"share3", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 7:
								this.$slsTracker(
									"seriesdetails",
									"series_details",
									"",
									"click",
									"share2", {
										collection_id: this.behavior_content.collection_id,
										collection_name: this.behavior_content.collection_name,
										creator_id: this.behavior_content.creator_id,
										creator_name: this.behavior_content.creator_name,
										price: this.behavior_content.price,
										accept_id: 'accept_type3',
									},
									1
								)
								break
							case 8:
								this.$slsTracker(
									"copyright",
									"market_copyright_work",
									"",
									"click",
									"share4", {
										accept_id: 'accept_type3',
									},
									1
								)
								break
							case 9:
								this.$slsTracker(
									"3dcollection",
									"market_3d_collection",
									"",
									"click",
									"share5", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 10:
								this.$slsTracker(
									"2dcollection",
									"market_2d_collection",
									"",
									"click",
									"share6", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 11:
								this.$slsTracker(
									"virtualhuman",
									"market_virtualhuman",
									"",
									"click",
									"share7", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 12:
								this.$slsTracker(
									"famousmaster",
									"market_choicetopic",
									"",
									"click",
									"share8", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 13:
								this.$slsTracker(
									"flowcreator",
									"market_choicetopic",
									"",
									"click",
									"share9", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							case 14:
								this.$slsTracker(
									"isrealalbum",
									"market_choicetopic",
									"",
									"click",
									"share10", {
										accept_id: 'accept_type3',
									},
									1
								);
								break
							default:
								break
						}
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: 'none',
							duration: 3000,
						})
					}
				});

				this.show = false;
			},
			// 打开查看证书
			async openMaskCert(type) {
				console.log(this.isPerson)
				if (this.isPerson) return this.openPersonShare(type);
				this.isShowMask = true;
				document.body.style['overflow'] = 'hidden';
				if (this.isBind) { //盲盒分享
					// this.getBlind(this.id);
					// uni.showLoading({
					//   mask: true,
					// })
					uni.showToast({
						icon: 'loading',
						mask: true,
						duration: 2000
					})
					setTimeout(() => {
						this.generateImage('goodsDetail')
					}, 2000)
					this.val = window.location.href;
				} else if (this.isSerise) { //系列分享
					uni.showToast({
						icon: 'loading',
						mask: true,
						duration: 2000
					})
					setTimeout(() => {
						this.generateImage('goodsDetail')
					}, 2000)
					// this.getSeries(this.id,type);
					this.val = window.location.href;
				} else if (this.isNormal) { // 作品详情分享
					this.url = getApp().globalData.url
					this.val = this.url + "#/pagesA/project/mall/mallDetails?tid=" + this.tid
					this.getExistingevidence(type)
				} else if (this.isMoreShare) {
					// 多作品分享海报, 内容出现
					// console.log('多作品分享');
					uni.showToast({
						icon: 'loading',
						mask: true,
						duration: 2000
					})
					setTimeout(() => {
						this.generateImage('moreWorks')
					}, 2000)
					// this.isShowWorks = true;
				}
				// 添加埋点
				await this.link(type, 'val');
				const ctx = uni.createCanvasContext('grcode');
				const uqrcode = new uQRCode({
						text: this.val,
						size: this.size
					},
					ctx
				);
				uqrcode.make();
				uqrcode.draw();
				this.show = false
			},

			// 个人主页海报
			async openPersonShare(type) {
				// 添加埋点
				await this.link(type, 'url');
				// this.size = 72;
				this.size = 48;
				this.isShowMask = true;
				this.show = false;
				const ctx = uni.createCanvasContext('qrcode');
				const uqrcode = new uQRCode({
						text: this.url,
						size: this.size
					},
					ctx
				);
				uqrcode.make();
				uqrcode.draw();
				let accept_id = "";
				let behavior_type = "";
				if (type == 1) {
					accept_id = "微信号";
					behavior_type = 'share1'
				} else if (type == 2) {
					accept_id = "朋友圈";
					behavior_type = 'share1'
				}
				if (this.shareType === 'brand') {
					this.eventTracking('accept_type4=海报')
				}
				this.$slsTracker(
					"myhomecreate",
					"myhome_create",
					"",
					"click",
					behavior_type, {
						accept_id: accept_id ? 'accept_type' + accept_id : ''
					},
					1
				);
				uni.showLoading({
					mask: true,
				})
				setTimeout(() => {
					this.generateImage('personModal')
				}, 2000)

				// this.isShowMaskPerson = true;
			},
			/**
			 * 分享链接增加埋点
			 * @param { string } type 分享途径
			 * @param { string } name 分享分享链接参数名
			 */
			async link(type, name) {
				// 1-微信号 2-朋友圈 3-分享链接 4-海报
				let accept_id = '';
				if (type == 1) {
					accept_id = 1;
				} else if (type == 2) {
					accept_id = 2;
				} else {
					accept_id = 4;
				}
				// this[name] = `${this[name]}&shared_contract_address=${uni.getStorageSync("contract_address")}&accept_id=${accept_id}`;
				this[name] = await this.getShortLink(
					`${this[name]}&shared_contract_address=${uni.getStorageSync("contract_address")}&accept_id=${accept_id}`
				);
			},
			// 关闭
			closeMask() {
				this.isShowWorks = false;
				this.isShowMaskCert = false;
				// this.creation[this.currentIndex].isShowBubble = false;
				this.isShowModalBubble = false;
				this.isShowMaskPerson = false;
				this.isShowMask = false;
				document.body.style['overflow'] = '';
			},
			async getExistingevidence(type) {
				let res = await this.$api.java_goodsDetails({
					itemTokenId: this.tid,
				});
				if (res.status.code == 0) {
					const data = res.result;
					this.existingevidenceList = {
						"u_avatar": data.createUser.avatar,
						"price": data.price,
						"title": data.name,
						"cover": data.photoShow,
						"o_nickname": data.ownerUser.name,
						"c_nickname": data.createUser.name,
						"created_at": data.createTime,
						"tid": data.tokenId
					};
					uni.showToast({
						icon: 'loading',
						mask: true,
						duration: 2000
					})
					setTimeout(() => {
						this.generateImage('goodsDetail')
					}, 2000)
					var accept_id = "";
					var behavior_type = "";
					if (type == 1) {
						accept_id = "微信号";
						behavior_type = 'share3'
					} else if (type == 2) {
						accept_id = "朋友圈";
						behavior_type = 'share4'
					}
					// console.log(this.$refs.goodsDetail, 'ceshi');
					this.$slsTracker(
						"workdetails",
						"work_details",
						"",
						"click",
						behavior_type, {
							item_id: this.behavior_content.item_id,
							collection_id: this.behavior_content.collection_id,
							collection_name: this.behavior_content.collection_name,
							creator_id: this.behavior_content.creator_id,
							creator_name: this.behavior_content.creator_name,
							price: this.behavior_content.price,
							accept_id: accept_id ? 'accept_type' + accept_id : '',
						},
						1
					);
					// this.isShowMaskCert = true

				}

			},
			// 长链接转短链接
			async getShortLink(url, name) {
				const {
					status: {
						code,
						msg
					},
					result
				} = await this.$api.java_shortLink({
					longLink: url
				});
				if (code == 0) {
					return result.shortUrl;
				} else {
					uni.showToast({
						title: msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			// 获取系列信息
			async getSeries(id, type) {
				let res = await this.$api.java_seriesDetails({
					seriesId: id
				});
				if (res.status.code == 0) { //请求状态 200成功
					const data = res.result;
					this.existingevidenceList = {
						"u_avatar": data.seriesUser.avatar,
						"price": data.priceRance,
						"title": data.name,
						"cover": data.cover,
						"c_nickname": data.seriesUser.name
					};
				} else {
					uni.showToast({ //提示框
						title: res.status.msg,
						icon: "none",
						duration: 3000
					});
				}
				var accept_id = "";
				if (type == 1) {
					accept_id = "微信号";
				} else if (type == 2) {
					accept_id = "朋友圈";
				}
				this.$slsTracker(
					"seriesdetails",
					"series_details",
					"",
					"click",
					"share2", {
						collection_id: this.behavior_content.collection_id,
						collection_name: this.behavior_content.collection_name,
						creator_id: this.behavior_content.creator_id,
						creator_name: this.behavior_content.creator_name,
						price: this.behavior_content.price,
						accept_id: accept_id ? 'accept_type' + accept_id : ''
					},
					1
				);
			},
			// 获取盲盒信息
			async getBlind(id) {
				let res = await this.$api.java_blindSeriesDetail({
					seriesId: id,
					pageNum: 1,
					pageSize: 15,
				});
				if (res.status.code == 0) {
					const data = res.result;
					this.existingevidenceList = {
						"u_avatar": data.userDetails.avatar,
						"price": data.price,
						"title": data.name,
						"cover": data.cover,
						"c_nickname": data.userDetails.name
					};
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000,
					});
				}
			},
			// qrR(res) {
			//   this.src = res;
			//   console.log(res)
			// },
			openPoster() {
				this.show = false
				this.$emit("poster")
			},
			// 生成图片需要调用的方法
			generateImage(e) {
				uni.showLoading({
					title: '生成海报中',
					mask: true,
				})
				const dom = this.$refs[e].$el // 需要生成图片内容的
				// const dom = document.getElementById(e) // 需要生成图片内容的 dom 节点
				html2canvas(dom, {
					// width: dom.clientWidth, //dom 原始宽度
					// height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 6, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 6, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					this.downImg = canvas.toDataURL('image/jpeg', 1)
					// console.log(canvas.toDataURL('image/png'), '转化之后的信息');
					if (e === 'personModal') {
						this.isShowMaskPerson = true;
					} else if (e === 'goodsDetail') {
						this.isShowMaskCert = true
					} else if (e === 'moreWorks') {
						this.isShowWorks = true;
					}
					uni.hideLoading();
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.$refs.uToast.show({
					//   title: '生成失败',
					//   type: 'error',
					// })
					this.isShowMask = false;
				})
			},
		},
	}
</script>

<style lang="scss">
	::v-deep .uni-scroll-view-content {
		background-color: #1E1E1E;
	}

	.auto-img-save {
		margin-top: 100rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #999999;
		font-size: 24rpx;

		.auto-img-save-icon {
			margin-bottom: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 96rpx;
			height: 96rpx;
			background: rgba(0, 0, 0, 0.1400);
			border-radius: 50%;
		}
	}

	.shareMask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: url('../../static/imgs/public/share_bg.png');
		background-size: cover;
		background-repeat: no-repeat;
		z-index: 1000;
	}

	.pinlun_model {
		.model_header {
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			font-size: 32rpx;
			font-weight: 600;
			color: #F9F9F9;
			font-family: PingFangSC-Semibold, PingFang SC;
			letter-spacing: 4rpx;
		}

		.model_content {
			padding: 46rpx 0;

			.type_send {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				width: 100%;

				.item {
					width: 33%;
					text-align: center;

					.bg {
						width: 100%;
						border-radius: 50%;
						margin-bottom: 28rpx;
						display: flex;
						justify-content: center;
						align-items: center;

						image {
							width: 96rpx;
						}
					}

					text {
						font-size: 24rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #999999;
					}
				}
			}
		}

		.cancel_btn {
			padding-bottom: 50rpx;
			text-align: center;
			color: #F9F9F9;
		}
	}

	.modal-cert {
		width: 600rpx;
		height: auto;
		position: fixed;
		top: -99999px;
		left: -999999px;
		// position: fixed;
		// top: 20%;
		// left: 50%;
		// transform: translate(-50%, -20%);
		// z-index: 1000;
		border-radius: 20rpx;
		overflow: hidden;
		background-image: url('../../static/imgs/personal/de_bg.png');
		background-size: cover;
		background-repeat: no-repeat;
		padding: 30rpx 40rpx 40rpx;

		.cert_top {
			position: relative;

			.time {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #616161;
				line-height: 20rpx;
				transform: scale(0.94);
				text-align: center;
				margin-bottom: 20rpx;
			}

			.photoShow {
				height: 520rpx;
				width: 520rpx;
				position: relative;
				margin-top: 20rpx;

				.img {
					width: 100%;
					height: 520rpx;
					border-radius: 20rpx;
					overflow: hidden;
					background-color: #fff;
				}

				.icon {
					width: 224rpx;
					height: 224rpx;
					position: absolute;
					right: -32rpx;
					bottom: -196rpx;
					// width:200rpx;
					z-index: 100;
				}
			}

			.price {
				margin-top: 50rpx;
				padding-bottom: 60rpx;
				font-size: 44rpx;
				font-family: MiSans-Demibold, MiSans;
				font-weight: 600;
				color: #F9F9F9;
				line-height: 44rpx;
			}

			.content_title {
				font-size: 36rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 50rpx;
				display: flex;
				align-items: center;

				.cert_name {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.type_tip {
					background: #1FEDF0;
					border-radius: 2rpx;
					margin-right: 16rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #121212;
					height: 36rpx;
					width: 70rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					transform: scale(0.9);
				}
			}
		}

		.user {
			padding: 10rpx 0 60rpx;
			display: flex;
			align-items: center;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #616161;
			line-height: 40rpx;

			image {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-right: 12rpx;
			}
		}

		.cert_end {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 30rpx;
			border-top: 2rpx dashed #303030;

			.cert-left {
				width: 400rpx;

				.left_top {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #F9F9F9;
					line-height: 40rx;

					image {
						width: 52rpx;
						height: 52rpx;
						margin-right: 20rpx;
					}
				}

				.left-tip {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #616161;
					line-height: 28rpx;
					margin-top: 20rpx;
				}
			}

			.qrimg-i {
				// width: 140rpx;
				// height: 140rpx;
				background-color: #F9F9F9;
				padding: 2px;
				// display: flex;
				// align-items: center;
				// justify-content: center;
			}
		}
	}

	.autoImg {
		position: fixed;
		width: 600rpx;
		height: 1070rpx;
		border-radius: 20rpx;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1000;

		img {
			width: 600rpx;
			height: 1070rpx;
			border-radius: 20rpx;
		}
	}

	.person_autoImg {
		position: fixed;
		width: 600rpx;
		// border-radius: 20rpx;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1000;

		img {
			width: 600rpx;
			border-radius: 20rpx;
		}
	}

	.work_autoImg {
		position: fixed;
		width: 662rpx;
		max-height: 86%;
		border-radius: 20rpx;
		overflow-y: auto;
		z-index: 1000;
		top: 20%;
		left: 50%;
		transform: translate(-50%, -20%);
		z-index: 1000;

		img {
			width: 662rpx;
			border-radius: 44rpx;
		}
	}

	.modal_person {
		width: 600rpx;
		position: fixed;
		top: -99999px;
		left: -999999px;
		// position: fixed;
		// top: 20%;
		// left: 50%;
		// transform: translate(-50%, -20%);
		// z-index: 1000;
		border-radius: 20rpx;
		overflow: hidden;
		background-image: url('../../static/imgs/personal/hb_bg.png');
		background-size: cover;
		// background-size: 100%;
		background-repeat: no-repeat;

		.person_top {
			height: 658rpx;
			// background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20220708/60e3fd213f1c719ad38cee35698db527_600x746.png');
			// background-size: 100%;
			// background-repeat: no-repeat;
			position: relative;
			padding: 122rpx 40rpx 60rpx;

			.person_img {
				z-index: 600;
				position: absolute;
				top: 64rpx;
				left: 100rpx;
				width: 132rpx;
				height: 132rpx;
				background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20220708/b4dc6320118bfafe95655dc67296f115_124x124.png');
				background-size: 100%;
				background-repeat: no-repeat;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 124rpx;
					height: 124rpx;
					border-radius: 50%;
					overflow: hidden;
				}
			}

			.bg_img {
				width: 520rpx;
				height: 400rpx;
				position: absolute;
				top: 122rpx;
				left: 42rpx;
				border-radius: 20rpx;
			}

			.person_head {
				padding: 98rpx 60rpx;
				height: 400rpx;
				z-index: 500;
				backdrop-filter: blur(14rpx);
				position: relative;
				font-size: 24rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #F9F9F9;
				border-radius: 20rpx;

				//&::before{
				//  content:'';
				//  position:absolute;
				//  top:0;
				//  left:0;
				//  right: 0;
				//  bottom: 0;
				//  background-size:cover;
				//  background: inherit;
				//  border-radius: 20rpx;
				//  filter: blur(4rpx);
				//  z-index: 0;
				//}
				.person_content {
					padding: 0 60rpx;
					position: absolute;
					z-index: 580;
					right: 0;
					left: 0;
				}

				.person_name {
					font-size: 32rpx;
					font-family: Alibaba-PuHuiTi-B, Alibaba-PuHuiTi;
					font-weight: normal;
					color: #FFFFFF;
					line-height: 32rpx;
					margin-bottom: 18rpx;
				}

				.person_des {
					font-size: 24rpx;
					line-height: 36rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}

				.person_meng {
					position: absolute;
					border-radius: 20rpx;
					// background-image: url('../../static/imgs/personal/mengban.png');
					// background-size: 100%;
					// background-repeat: no-repeat;
					background: #000000;
					opacity: 0.3;
					top: 0;
					bottom: 0;
					right: 0;
					left: 0;
					z-index: 550;
				}
			}

			.person_tip {
				margin-top: 20rpx;
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #F9F9F9;
				line-height: 40px;
			}

			.line {
				width: 520rpx;
				border-top: 2rpx dashed #303030;
				margin-top: 40rpx;
			}
		}

		.person_bottom {
			width: 100%;
			padding-top: 38rpx;
			padding-bottom: 40rpx;
			// height: 250rpx;
			// background-image: url('https://cdn-lingjing.nftcn.com.cn/image/20220708/9fe38dc6dcfc8deefe8bd9c9274cad68_600x304.png');
			// background-size: 100%;
			// background-repeat: no-repeat;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.mcs-left {
				width: 400rpx;
				padding-left: 40rpx;
				padding-top: 6rpx;
				padding-bottom: 6rpx;

				.left_top {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #F9F9F9;
					line-height: 40rx;

					image {
						width: 52rpx;
						height: 52rpx;
						margin-right: 20rpx;
					}
				}

				.left-tip {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #616161;
					line-height: 28rpx;
					margin-top: 20rpx;
				}
			}

			.qrimg-i {
				// width: 100rpx;
				// height: 100rpx;
				background-color: #F9F9F9;
				// display: flex;
				// align-items: center;
				// justify-content: center;
				margin-right: 40rpx;
				padding: 2px;
			}
		}
	}
</style>
