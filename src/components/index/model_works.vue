<template>
  <view class="works-box">
    <view
      ref="shareImg"
      class="works-share">
      <view class="bg_top"></view>
      <view class="bg_bot"></view>
      <view :class="{'works-top': true, 'foure': length === 4, 'more': length > 4} ">
        <template v-if="length">
          <!-- :class="{'works-item': true, 'one': length === 1, 'tow': length === 2, 'three': length === 3, 'foure': length === 4, 'more': length > 4 }" -->
          <view
            :class="['works-item', className]"
            v-for="(item, index) in list" :key="index">
            <!-- :class="{'img': true, 'tow': length === 2, 'three': length === 3, 'foure': length === 4, 'more': length > 4 }" -->
            <img
              :class="['img', className]"
              :src="item.coverImage"
              alt=""
            >
            <view :class="['title', className]">
              {{
                className === 'more' || className === 'three' ?
                item.name.length > 6 ? item.name.substring(0, 5) + '...' : item.name
                : className === 'two' ? item.name.length > 10 ? item.name.substring(0, 9) + '...' : item.name : item.name
              }}
            </view>
            <view :class="['price', className]"><text>¥</text>{{item.price}}</view>
          </view>
        </template>
      </view>
      <view v-if="info" class="author">
        <image
          :src="info.avatar"
          class="avater"
        >
        <!-- mode="scaleToFill" -->
        <text class="nick-name">{{info.name}}</text>
      </view>
      <view class="line"></view>
      <view class="cert_end">
        <view class="cert-left">
          <view class="left_top">
            <img
              src="https://cdn-lingjing.nftcn.com.cn/image/20220708/46ef91a054e48d9e285f673aafa0ce18_48x48.png"
              mode="aspectFit"
            />
            NFTCN
          </view>
          <view class="left-tip">扫码探索更多数字藏品</view>
        </view>
        <view class="qrimg-i">
          <canvas id="grcode" canvas-id="grcode" :style="{ width: `${size}px`, height: `${size}px` }"></canvas>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * @description 多作品分享海报
 * @param list 作品列表
 * @param info 当前艺术家列表
 */
import uQRCode from 'uqrcodejs';
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  computed: {
    length() {
      return this.list.length
    },
    className() {
      let name = ''
      switch(this.list.length) {
        case 0: name = ''
        break;
        case 1: name = 'one'
        break;
        case 2: name = 'tow'
        break;
        case 3: name = 'three'
        break;
        case 4: name = 'foure'
        break;
        default: name = 'more'
        break;
      }
      return name
    },
  },
  data() {
    return {
      url: '',
      size: 66, // 二维码大小
    }
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.works-box {
  position: fixed;
  top: -999999px;
  left: -999999px;
  // z-index: 1000;
  // top: 20%;
  // left: 50%;
  // transform: translate(-50%, -20%);
 
}
.works-share {
  position: relative;
  width: 662rpx;
  border-radius: 20rpx;
  background-image: url('../../static/imgs/public/share_center.png');
  background-size: cover;
  background-repeat: repeat;
  padding: 32rpx 32rpx 44rpx;
  .bg_top, .bg_bot {
    position: absolute;
  }
  .bg_top {
    top: 0;
    left: 0;
    height: 360rpx;
    width: 662rpx;
    z-index: 2;
    background-image: url('../../static/imgs/public/share_top.png');
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .bg_bot {
    // border: 1px solid cyan;
    bottom: 0;
    left: 0;
    height: 334rpx;
    width: 662rpx;
    z-index: 2;
    background-image: url('../../static/imgs/public/share_bot.png');
    background-size: 100%;
    background-repeat: no-repeat;
    border-radius: 0 0 20rpx 20rpx;
  }
  .works-top {
    // width: 598rpx;
    position: relative;
    z-index: 22;
    min-height: 562rpx;
    width: 100%;
    background: rgba(38,42,55,0.3000);
    border-radius: 16rpx;
    border: 2rpx solid rgba(255,255,255,0.3000);
    // opacity: 0.3;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 28rpx 24rpx 0;
    .works-item {
      margin-bottom: 28rpx;
      text-align: center;
      .img {
        width: 398rpx;
        height: 398rpx;
        display: inline-block;
        // margin: 0 auto;
        background-color: #fff;
        &.tow {
          width: 265rpx;
          height: 266rpx;
          // width: 100%;
          // height: 100%;
        }
        &.three {
          // width: 265rpx;
          // height: 266rpx;
          width: 172rpx;
          height: 172rpx;
          // border: 1px solid red;
        }
        &.foure {
          width: 266rpx;
          height: 266rpx;
          // width: 100%;
          // height: 100%;
          // border: 1px solid red;
        }
        &.more {
          height: 128rpx;
          width: 100%;
        }
      }
      view {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #F9F9F9;
        text-align: center;
        // line-height: 28rpx;
      }
      .title {
        width: 398rpx;
        margin-top: 28rpx;
        display: inline-block;

        /*超出部分隐藏*/
        white-space: nowrap;
        overflow: hidden;
        /*不换行*/
        text-overflow: ellipsis;
        padding: 4rpx 0;
        /*超出部分文字以...显示*/
        &.tow {
          width: 265rpx;
        }
        &.three {
          width: 168rpx;
        }
        &.foure {
          width: 266rpx;
        }
        &.more {
          margin-top: 20rpx;
          width: 128rpx;
          font-size: 20rpx;
        }

      }
      .price {
        font-weight: bold;
        // border: 1px solid red;
        margin-top: 20rpx;
        font-size: 32rpx;
        width: 398rpx;
        display: inline-block;
        /*超出部分隐藏*/
        white-space: nowrap;
        overflow: hidden;
        /*不换行*/
        text-overflow: ellipsis;
        padding: 4rpx 0;
        text {
          font-size: 24rpx;
        }
        &.tow {
          width: 265rpx;
        }
        &.three {
          width: 168rpx;
        }
        &.foure {
          width: 266rpx;
        }
        &.more {
          margin-top: 12rpx;
          text {
            font-size: 20rpx;
          }
          width: 128rpx;
          font-size: 24rpx;
        }
      }
      &.tow {
        padding: 94rpx 0;
      }
      &.three {
        padding: 140rpx 0;
      }
      &.foure {
        width: calc((100% - 24rpx) / 2);
      }
      &.foure:nth-last-child(1) {
        margin-top: 4rpx;
      }
      &.foure:nth-last-child(2) {
        margin-top: 4rpx;
      }
      &.more {
        width: calc((100% - 46rpx) / 4);
        margin-right: 14rpx;
      }
      &.more:nth-child(4n) {
        margin-right: 0;
      }
    }
    .tow + .tow {
      margin-left: 18rpx;
    }
    .three + .three {
      margin-left: 14rpx;
    }
    &.foure {
      justify-content: space-between;
      flex-wrap: wrap;
    }
    &.more {
      justify-content: flex-start;
      flex-wrap: wrap;
      flex-direction: row;
      align-content: flex-start;
    }

  }
  .author {
    position: relative;
    z-index: 22;
    padding-top: 36rpx;
    display: flex;
    align-items: center;
    // justify-content: center;
    .avater {
      // border: 1px solid red;
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;

    }
    .nick-name {
      color: #616161;
      font-weight: 400;
      margin-left: 12rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
    }
  }
  .line {
    position: relative;
    z-index: 22;
    width: 598rpx;
    border-top: 2rpx dashed #303030;
    margin-top: 40rpx;
  }
  .cert_end {
    position: relative;
    z-index: 22;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 36rpx;
    // border-top: 2rpx dashed #303030;
    .cert-left {
      width: 400rpx;
      padding-top: 8rpx;
      .left_top {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #F9F9F9;
        line-height: 40rx;
        img {
          width: 48rpx;
          height: 48rpx;
          margin-right: 20rpx;
        }
      }
      .left-tip {
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #616161;
        line-height: 28rpx;
        margin-top: 12rpx;
      }
    }
    .qrimg-i {
      // width: 140rpx;
      // height: 140rpx;
      background-color: #F9F9F9;
      padding: 2px;
      // display: flex;
      // align-items: center;
      // justify-content: center;
    }
  }

}
</style>