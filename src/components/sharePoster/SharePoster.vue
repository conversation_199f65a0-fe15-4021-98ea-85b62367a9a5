<template>
  <van-popup
    v-model:show="show"
    position="center"
    :overlay-style="{'backdrop-filter': 'blur(20px)'}"
    style="background-color: rgba(255, 255, 255, 0);"
  >
    <div v-if="imgUrl">
      <img :src="imgUrl" class="poster_img"/>
    </div>
    <div v-else ref="poster">
      <div id="container">
        <img :src="props.poster.img" class="container_img"/>
        <div class="bottom">
          <div class="code">
            <qrcode-vue :value="props.url" :size="82" level="H" />
          </div>
          <div>{{props.poster.text}}</div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
/**
 * @description 分享海报
 * @property {Boolean} codeShow 控制弹窗显隐
 * @property {String} url 分享链接
 * @property {Object} poster 海报信息
 */
import { defineProps, computed, defineEmits, ref, watchEffect } from 'vue';
import QrcodeVue from 'qrcode.vue';
import html2canvas from 'html2canvas';

const props = defineProps({
  codeShow: {
    type: Boolean,
    default: false,
  },
  url: {
    type: String,
    default: window.location.href,
  },
  poster: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['update:codeShow']);
const show = computed({
  get () {
    return props.codeShow;
  },
  set (value) {
    emit('update:codeShow', value);
  },
});
const poster = ref(null);
// 海报图片
const imgUrl = ref('');
watchEffect(
  () => {
    if (poster.value) {
      html2canvas(poster.value, {
        useCORS: true,
        backgroundColor: 'transparent',
        scale: 1.5, // 按比例增加分辨率 (2=双倍)
        dpi: window.devicePixelRatio * 1.5, // 设备像素比
      })
        .then((canvas) => {
          imgUrl.value = canvas.toDataURL('image/jpg', 1);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  { flush: 'post' }
);
</script>

<style lang="scss" scoped>
.poster_img {
  width: px2vw(640px);
  height: auto;
}
#container {
  border-radius: px2vw(16px);
  box-shadow: 0 px2vw(4px) px2vw(8px) 0 #111822;
  position: relative;
  .container_img {
    width: px2vw(640px);
    height: auto;
    border-radius: px2vw(16px);
  }
  .bottom {
    width: 100%;
    position: absolute;
    bottom: px2vw(76px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: px2vw(24px);
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    color: #BEDEFF;
    line-height: px2vw(28px);
    .code {
      background: rgba(0,0,0,0.15);
      border-radius: px2vw(16px);
      width: px2vw(176px);
      height: px2vw(176px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 500;
      margin-bottom: px2vw(22px);
    }
  }
}
</style>
