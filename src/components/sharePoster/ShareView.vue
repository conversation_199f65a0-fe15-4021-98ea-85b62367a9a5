<template>
  <van-popup
    v-model:show="show"
    closeable
    position="bottom"
    style="background-color: #1E1E1E;"
  >
    <div class="model_header">分享到</div>
    <div class="model_content" >
      <div class="type_send">
        <div class="item copy-qb" @click="copy">
          <div class="bg">
            <img :src="props.poster.urlIcon" />
          </div>
          <span>复制链接</span>
        </div>
        <div class="item copy-qb" @click="getPoster()">
          <div class="bg">
            <img :src="props.poster.posterIcon" />
          </div>
          <span>保存海报</span>
        </div>
      </div>
    </div>
  </van-popup>
  <SharePoster v-model:code-show="codeShow" :url="props.finallyPoster" :poster="poster"/>
</template>

<script setup>
/**
 * @description 分享组件
 * @property {Boolean} popupShow 控制弹窗显隐
 * @property {Boolean} finallyPoster 海报分享链接
 * @property {Boolean} finallyCopy 复制分享链接
 * @property {Object} poster 海报信息
 * @property {String} activityId 活动分享埋点id
 */
import { defineProps, computed, defineEmits, ref, getCurrentInstance } from 'vue';
import Clipboard from 'clipboard';
import { Toast } from 'vant';
import SharePoster from './SharePoster';

// 获取埋点方法
const { proxy } = getCurrentInstance();
const { $slsTracker } = proxy;
const props = defineProps({
  popupShow: {
    type: Boolean,
    default: false,
  },
  finallyPoster: {
    type: String,
    default: window.location.href,
  },
  finallyCopy: {
    type: String,
    default: window.location.href,
  },
  poster: {
    type: Object,
    default: () => {},
  },
  activityId: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['update:popupShow']);
const show = computed({
  get () {
    return props.popupShow;
  },
  set (value) {
    emit('update:popupShow', value);
  },
});
// 是否显示二维码海报
const codeShow = ref(false);
// 复制
// const copy = () => {
//   const clipboard = new Clipboard('.copy-qb', {
//     text: () => {
//       return props.finallyCopy;
//     },
//   });
//   clipboard.on('success', () => {
//     Toast('复制成功');
//     clipboard.destroy();
//   });
//   clipboard.on('error', () => {
//     Toast('该浏览器不支持自动复制~');
//     clipboard.destroy();
//   });
//   getDot('accept_type3');
// };


/**
 * 生成海报
 */
const getPoster = () => {
  codeShow.value = true;
  emit('update:popupShow', false);
  getDot('accept_type4');
};

// 埋点方法
const getDot = (type) => {
  // 埋点
  $slsTracker(
    'activityinvitation',
    'activity',
    '',
    'click',
    'share13',
    {
      activity_id: props.activityId, 
      accept_id: type,
    },
    1
  );
};
</script>

<style lang="scss" scoped>
.model_header {
  height: px2vw(100px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 32rpx;
  font-weight: 600;
  color: #F9F9F9;
  font-family: PingFangSC-Semibold, PingFang SC;
  letter-spacing: 4rpx;
}
.model_content {
  padding: px2vw(40px) 0 px2vw(80px);
  .type_send{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: px2vw(30px);
    width: 100%;
    .item{
      width: 38%;
      text-align: center;
      .bg{
        width: 100%;
        border-radius: 50%;
        margin-bottom: px2vw(28px);
        display: flex;
        justify-content: center;
        align-items: center;
        img{
          width: px2vw(96px);
        }
      }
      span{
        font-size: px2vw(24px);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}
</style>
