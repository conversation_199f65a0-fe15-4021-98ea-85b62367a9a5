<template>
  <div class="marquee-outer-wrapper">
    <div class="marquee-inner-wrapper">
      <span
        :class="{
          'first-marquee': index == 0,
          'second-marquee': index == 1,
          'three-marquee': index == 2,
        }"
        v-for="index in [0, 1, 2, 4]"
        :key="index"
      >
        测试测试数据
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.marquee-outer-wrapper {
  overflow: hidden;
  width: 100%;
  height: px2vw(200px);
}

.marquee-inner-wrapper {
  height: 100%;
  line-height: 46px;
  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #b8dce5;
  white-space: nowrap;
  position: relative;
}

/* 需要将两个文字内容一样的span放在最右边 */
.marquee-inner-wrapper > span {
  position: absolute;
  top: 0;
  left: 100%;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

@keyframes first-marquee {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  /* 向左移动 */
  100% {
    -webkit-transform: translate3d(-300%, 0, 0);
    transform: translate3d(-300%, 0, 0);
    display: none;
  }
}

/* 定义第一个span的animation：时长 动画名字 匀速 循环 正常播放 */
.first-marquee {
  -webkit-animation: 20s first-marquee linear infinite normal;
  animation:20s first-marquee linear infinite normal;
}

.second-marquee {
  /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
  -webkit-animation: 20s first-marquee linear 6s infinite normal;
  animation: 20s first-marquee linear 6s infinite normal;
}

.three-marquee {
  /* 因为要在第一个span播完之前就得出现第二个span，所以就延迟12s才播放 */
  -webkit-animation: 20s first-marquee linear 12s infinite normal;
  animation: 20s first-marquee linear 12s infinite normal;
}
</style>
