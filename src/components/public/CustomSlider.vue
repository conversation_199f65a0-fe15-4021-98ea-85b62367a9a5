<template>
	<div class="slider-container">
		<!-- 滑轨 -->
		<div class="slider-track">
			<!-- 激活的区域 :style="{ width: `${(activeIndex / (points.length - 1)) * 100}%` }"-->
			<div class="slider-active-line">
				<div class="slider-points" :style="{marginTop: easy ? '-10rpx' : ''}">
					<div v-for="(point, index) in points" :key="index" class="slider-point"
						:class="{ active: (index == activeIndex && disabledPoints.includes(index)), disabled: disabledPoints.includes(index) }"
						@click="handleClick(index)">
						<div class="point" v-if="!easy"
							:class="{ disabledPoint: !disabledPoints.includes(index) && disabledPoints.length > 0, }">
						</div>
						<div class="pointpary" v-if="easy"
							:class="{ disabledPointeasy: !disabledPoints.includes(index) && disabledPoints.length > 0, }">
						</div>
	<!-- width: 60rpx; -->
						
						<div v-if="!disabledPoints.includes(index) && !disabledPoints.includes(index + 1) && index < points.length - 1 && disabledPoints.length > 0"
						:style="{width:easy ? '120rpx' : '60rpx',top:easy ? '10rpx' : '6rpx'}"	class="connecting-line"></div>
						<text style="margin-top: 14rpx;" :style="{fontSize: easy ? '28rpx' : ''}">{{ point }}</text>
					</div>
				</div>
			</div>
			<!-- 滑块 primary-->
			<div class="slider-handle" v-if="!easy"
				:class="{ primary: disabledPoints.length == 0, active_btn: disabledPoints.length > 0 }"
				:style="{ left: `${positions[activeIndex]}%` }" @touchstart="startDrag" @touchmove="handleDrag"
				@touchend="stopDrag" @mousedown="startDrag" @mousemove="handleDrag" @mouseup="stopDrag"></div>

			<div class="slider-handle" v-if="easy"
				:class="{ pointeasy: disabledPoints.length == 0, pointeasyDisabled: disabledPoints.length > 0 }"
				:style="{ left: `${positionseasy[activeIndex]}%` }" @touchstart="startDrag" @touchmove="handleDrag"
				@touchend="stopDrag" @mousedown="startDrag" @mousemove="handleDrag" @mouseup="stopDrag"></div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		points: {
			type: Array,
			required: true,
			default: () => ["1X", "2X", "5X", "10X", "20X", "50X"], // 默认杠杆点
		},
		disabledPoints: {
			type: Array,
			default: () => [], // 禁用的点索引
		},
		value: {
			type: Number,
			default: 0, // 当前激活的索引
		},
		easy: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			// #ifdef H5
			positions: [-2, 16, 34, 53, 74, 96], // 每个点的位置百分比
			positionseasy: [-1, 17, 36	, 56, 77, 98],
			// #endif

			// #ifndef H5
			positions: [-2, 16, 34, 54, 76, 97], // 每个点的位置百分比
			positionseasy: [-1, 18, 37, 57, 78, 99],
			// #endif
			activeIndex: this.value, // 当前激活索引
			isDragging: false, // 是否正在拖动
		};
	},
	methods: {
		handleClick(index) {
			if (this.disabledPoints.includes(index)) return; // 禁用点不允许点击
			this.activeIndex = index;
			this.$emit("input", index); // 触发 v-model
		},
		startDrag(event) {
			this.isDragging = true;
			this.$el.classList.add("dragging"); // 开始拖动时移除过渡
		},
		handleDrag(event) {
			if (!this.isDragging) return;
			const clientX = event.type.includes("touch") ?
				event.touches[0].clientX :
				event.clientX;

			const track = this.$el.querySelector(".slider-track");
			const {
				left,
				width
			} = track.getBoundingClientRect();
			const position = Math.min(Math.max(clientX - left, 0), width); // 限制拖动范围
			const index = Math.round((position / width) * (this.points.length - 1)); // 计算激活索引
			if (!this.disabledPoints.includes(index)) {
				this.activeIndex = index;
				this.$emit("input", index); // 触发 v-model 更新
			}
		},
		stopDrag() {
			this.isDragging = false;
			this.$el.classList.remove("dragging"); // 停止拖动时添加过渡
		},
	},
	watch: {
		value(newVal) {
			this.activeIndex = newVal; // 外部更新时同步内部状态
		},
	},
};
</script>

<style scoped lang="scss">
.connecting-line {
	position: absolute;
	// top: 50%; /* 居中 */
	left: 70%;
	/* 从当前点右侧开始 */
	height: 2rpx;
	background-color: #FC6751;
	/* 直线颜色 */
	transform: translateY(-50%);
}

.slider-container {
	width: 100%;
	padding: 10rpx 0;
	box-sizing: border-box;
}

.slider-track {
	position: relative;
	height: 2rpx;
	background: #434343;
}

.slider-active-line {
	width: 100%;
	position: absolute;
	height: 100%;
	background-color: #434343;
	;
	/* 激活区域颜色 */
	border-radius: 3rpx;
	transition: width 0.3s ease;
	/* 激活区域滑动过渡 */
}

.slider-container:not(.dragging) .slider-active-line {
	transition: width 0.3s ease;
	/* 激活区域滑动过渡（非拖动状态） */
}

.primary {
	width: 12rpx;
	height: 12rpx;
	background: #FFFFFF;
	border: 2rpx solid #49FFFF;
	border-radius: 50%;
}

.active_btn {
	width: 16rpx;
	height: 16rpx;
	background: #FC6751;
}

.slider-handle {
	position: absolute;
	top: -7rpx;
	border-radius: 50%;
	/* 滑块颜色 */
	z-index: 9;
	cursor: pointer;
	transition: left 0.3s ease;
	/* 滑块滑动过渡 */
}

.slider-container:not(.dragging) .slider-handle {
	transition: left 0.3s ease;
	/* 滑块滑动过渡（非拖动状态） */
}

.slider-points {
	display: flex;
	justify-content: space-between;
	// #ifdef H5
	margin-top: -5.3rpx;
	// #endif

	// #ifndef H5
	margin-top: -6rpx;
	// #endif
	width: 104%;
}

.pointpary {
	width: 22rpx;
	height: 22rpx;
	background: #ABABAB;
	border-radius: 50%;
	
}

.point {
	width: 12rpx;
	height: 12rpx;
	background: #ABABAB;
	border-radius: 50%;
}

.pointeasy {
	width: 20rpx;
	height: 20rpx;
	background: #FFFFFF;
	border: 2rpx solid #49FFFF;
	margin-top: -4rpx;
}

.pointeasyDisabled {
	width: 22rpx;
	height: 22rpx;
	background: #FF6C63;
	border-radius: 50%;
	margin-top: -4rpx;
}

.slider-point {
	text-align: center;
	color: #fff;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-family: PingFang SC;
	font-weight: 400;
	font-size: 20rpx;
	position: relative;
}

.slider-point:first-child {
	margin-left: -12rpx;
}

.slider-point.active {
	color: #FC6751;
	/* 激活点颜色 */
}

.slider-point.disabled {
	color: #ccc;
	/* 禁用点颜色 */
	cursor: not-allowed;
}

.disabledPoint {
	width: 10rpx;
	height: 10rpx;
	background: #2B2B2B !important;
	border: 1rpx solid #FC6751 !important;
}
.disabledPointeasy {
	width: 18rpx;
	height: 18rpx;
	background: #2B2B2B !important;
	border: 2rpx solid #FF6C63 !important;
}
</style>
