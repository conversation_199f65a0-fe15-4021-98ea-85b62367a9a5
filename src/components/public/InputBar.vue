<template>
    <view>
        <u-verification-code :seconds="60" ref="uCode" @change="codeChange" change-text="重新获取 (xs)"
                             @start="startStyle = true" @end="end()"></u-verification-code>
        <view class="item">
            <view class="label" >
                <view class="label-prefix">
                    <slot name="label-prefix"></slot>
                </view>
                <span :class="{'focusStyle':focusStyle }"> {{ item.label }}</span>
            </view>
            <u-field
                v-model="result"
                :placeholder="item.placeholder"
                label-width="0"
                :disabled="item.disabled"
                :border-bottom="false"
                :maxlength="item.maxlength"
                :class="{'input-prefix':item.prefix,'focusStyle':focusStyle  }"
                @focus="focusStyle = true"
                @blur="focusStyle = false"
            >
                <template slot="icon">
                    <span class="prefix" v-if="item.prefix">{{ item.prefix }}</span>
                </template>
                <template slot="right">
                    <span slot="right" class="get-code"  v-if="showCode"
                          :style="{color:startStyle ? 'var(--info-front-color)' : 'var(--active-color)'}"
                          @click="getCode">{{ codeText }}</span>
                    <template v-else-if="item.tooltip">
                        <image style="width: 48rpx" src="/static/imgs/public/warn_icon.png" mode="widthFix"></image>
                        <span class="tooltip">{{ item.tooltip }}</span>
                    </template>
                   <!-- <u-icon v-else-if="item.type === 'select'" name="arrow-down" color="var(--main-front-color)" size="28"></u-icon> -->
                </template>
            </u-field>
            <view class="tips">{{ item.tips }}</view>
        </view>
    </view>
</template>

<script>
export default {
    name: "inputBar",
    model: {
        prop: "value",
        event: "input"
    },
    props: {
        value: {
            type: String,
            default: ""
        },
        item: {
            type: Object,
            default: {}
        },
        showCode: {
            type: Boolean,
            default: false
        },
        startCountdown: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        startCountdown(val) {
            console.log(val)
            if (val) {
                // 模拟向后端请求验证码
                uni.showLoading({
                    title: '正在获取验证码'
                })
                setTimeout(() => {
                    uni.hideLoading();
                    // 通知验证码组件内部开始倒计时
                    if (!this.startStyle) {
                        this.$refs.uCode.start();
                    }
                }, 1000);
            } else {
                // this.$u.toast('倒计时结束后再发送');
            }
        }
    },
    data() {
        return {
            codeText: '',
            startStyle: '',
            focusStyle: ''
        }
    },
    computed: {
        result: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val)
                this.$emit('change', val)
            },
        },
    },
    methods: {
        end(){
            this.startStyle = false
            this.$emit('end')
        },
        codeChange(text) {
            this.codeText = text;
        },
        getCode() {
            this.$emit('getCode')
        }
    }
}
</script>

<style lang="scss" scoped>
.item {
    margin-top: 48rpx;
    .prefix {
        font-size: 24rpx;
        color: var( --main-front-color);
    }
    .label {
        display: flex;
        align-items: center;
        color: var(--main-front-color);
        font-size: 24rpx;
        font-weight: 600;
        margin-bottom: 16rpx;
        .label-prefix {
            margin-right: 10rpx;
        }
    }
    .tips {
        margin: 20rpx 0;
        color: var(--active-color);
        font-size: 20rpx;
        font-weight: 400;
    }
    .tooltip {
        height: 30rpx;
        line-height: 30rpx;
        background-color: var(--tooltip-bg-color);
        font-size: 24rpx;
        color: var(--main-front-color);
        text-align: center;
        padding: 10rpx 20rpx;
        position: absolute;
        z-index: 1;
        top: 90%;
        right: 10rpx;
    }
    .tooltip::after {
        content: "";
        position: absolute;
        top: -40%;
        right: 34rpx;
        border-width: 5px;
        border-style: solid;
        border-color:  transparent transparent  var(--tooltip-bg-color) transparent ;
    }
    ::v-deep {
        .u-field {
            padding-left: 0;
            border-bottom: #4B3E27 2rpx solid;
            &.input-prefix {
                /* background-color: red; */
                .u-label {
                    /* flex: 1 !important; */
                    width: auto;
                }
            }
        }
        .fild-body {
            margin-left: -8rpx;
        }
        .uni-input-input {
            color: var(--main-front-color);
        }
        .uni-input-placeholder {
            font-size: 28rpx;
            font-weight: 400;
            color: #A6A6A6;
        }
    }
    .focusStyle {
        color: #D8B662;
        border-color: #D8B662;
        transition: all 0.1s;
    }

}

</style>
