<template>
	<u-popup v-model="show" mode="center" width="750" height="1624">
		<view class="share-dialog">
			<!-- 返回按钮 -->
			<image class="back-btn"
				src="https://cdn.yanjie.art/image/20241025/563e60262c16b4a0b952a2c8cbc362d7_26x50.png" mode="aspectFit"
				@click="handleClose" />

			<!-- 背景图 -->
			<image class="bg-image" :src="bgImage" mode="aspectFill" />

			<!-- 内容区域 -->
			<view class="content">
				<!-- 存证时间 -->
				<view class="cart_bg" v-if="!isShow" id="inviteNew">
					<view class="user-info">
						<img class="avatar" :src="info.avatar" />
						<view class="user-info-text">
							<text class="username">{{info.nickname}}</text>
							<text class="contractAddress">{{desensitize(info.contractAddress)}}</text>
						</view>

					</view>
					<!-- 收益信息 -->
					<view class="profit-details">
						<view class="detail-item" v-if="isShowIncomeRatio">
							<text class="label">90天收益率</text>
							<text class="value">{{info.incomeRatio>0?`+${info.incomeRatio*100}`:info.incomeRatio*100}}%</text>
						</view>
						<view class="detail-item" v-if="isShowIncome">
							<text class="label">90天收益</text>
							<text class="value">¥{{info.totalIncome>0?`+${info.totalIncome}`:info.totalIncome}}</text>
						</view>
						<view class="detail-item" v-if="isShowFloatingIncome">
							<text class="label">90天浮盈</text>
							<text class="value">¥{{info.floatingIncome>0?`+${info.floatingIncome}`:info.floatingIncome}}</text>
						</view>
						<view class="detail-item" v-if="isShowRealIncome">
							<text class="label">90天实盈</text>
							<text class="value">¥{{info.realIncome>0?`+${info.realIncome}`:info.realIncome}}</text>
						</view>
					</view>
					<!-- 底部信息 -->
					<view class="bottom-info">
						<view class="slogan">
							<img src="https://cdn.yanjie.art/image/20241224/a2da0cd3cf94c25249ce1e8f157b5e63_280x176.png"
								mode="aspectFit" />
							<view class="invite" v-if="isShowInviteCode">
								邀请码: <text class="code">{{info.inviteCode}}</text>
							</view>
						</view>
						<view class="qr-code">
							<view class="img">
								<view class="white">
									<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="info.shareLink" size="130rpx"
										:options="options"></uv-qrcode>
								</view>
							</view>
							<view class="text_1">加入衍界，大开眼界</view>
						</view>
					</view>
				</view>

				<view class="head_bg" id="inviteOld" v-else>
					<view class="height"></view>
					<view class="invite_code">
						<view class="border">
							<img :src="info.avatar" />
						</view>
						<view style="height: 164rpx;"></view>
						<text class="nickname">{{info.nickname}}</text>
						<text class="codes" v-if="isShowInviteCode">
							邀请码：
							<text>{{info.inviteCode}}</text>
						</text>
						<view class="qrcode">
							<view class="white"><uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="info.shareLink"
									size="164rpx" :options="options"></uv-qrcode></view>
						</view>
						<text class="scan">扫描二维码下载注册衍界</text>
					</view>
				</view>
			</view>
			<view class="ul_view" style="margin-bottom: 40rpx;">
				<view class="li" :class="{'active': isShowIncomeRatio}" @click="showIncomeRatio">
					展示收益率
				</view>
				<view class="li" :class="{'active': isShowIncome}" @click="showIncome">
					展示收益
				</view>
				<view class="li" :class="{'active': isShowFloatingIncome}" @click="showFloatingIncome">
					展示浮盈
				</view>
				<view class="li" :class="{'active': isShowRealIncome}" @click="showRealIncome">
					展示实盈
				</view>
			</view>
			<view class="ul_view" style="margin-top: 40rpx;">
				<view class="li" :class="{'active': isShowInviteCode}" @click="showInvite">
					展示邀请码
				</view>
				<view class="hide-view" @tap="checkAgreement">
					<image v-if="isShow"
						src="https://cdn.yanjie.art/image/20241023/98b6f94570306e0b3796dd72613fa8f8_24x26.png"
						mode="widthFix"></image>
					<image v-else src="https://cdn.yanjie.art/image/20241023/63ed1abbf0cd92dddc8e5d78de022152_24x24.png"
						mode="widthFix"></image>
					隐藏所有收益
				</view>
			</view>
			<!-- 底部按钮组 -->
			<view class="action-buttons">
				<view class="btn-group">
					<view class="btn-item" @click="canvas.onClick" v-if="!isShow">
						<image
							src="https://cdn.yanjie.art/image/20241224/724d7b21c7cf045b60f70ccabc761680_200x200.png"
							mode="aspectFit" />
						<text>保存海报</text>
					</view>
					<view class="btn-item" @click="canvas.onClickOld" v-if="isShow">
						<image
							src="https://cdn.yanjie.art/image/20241224/724d7b21c7cf045b60f70ccabc761680_200x200.png"
							mode="aspectFit" />
						<text>保存海报</text>
					</view>
					<!-- #ifdef  APP-PLUS -->
					<view class="btn-item" @click="handleWechat">
						<image
							src="https://cdn.yanjie.art/image/20241224/8d90ad52a916fbe63fc83fca8e3ec07e_100x100.png"
							mode="aspectFit" />
						<text>微信好友</text>
					</view>
					<view class="btn-item" @click="handleMoments">
						<image
							src="https://cdn.yanjie.art/image/20241224/babbd297226c866d9f0bb4daebba9558_100x100.png"
							mode="aspectFit" />
						<text>朋友圈</text>
					</view>
					<!-- #endif -->
					<view class="btn-item" @click="handleCopy">
						<image
							src="https://cdn.yanjie.art/image/20241224/4de2f23aacea6717c674c6176ff51d97_100x100.png"
							mode="aspectFit" />
						<text>复制链接</text>
					</view>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
	import uvQrcode from 'uqrcodejs';
	export default {
		name: 'ShareDialog',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			info: {
				type: Object,
				default: () => ({})
			},
			isShowIncomeRatio: {
				type: Boolean,
				default: true
			},
			isShowIncome: {
				type: Boolean,
				default: true
			},
			isShowFloatingIncome: {
				type: Boolean,
				default: true
			},
			isShowRealIncome: {
				type: Boolean,
				default: true
			},
			isShowInviteCode: {
				type: Boolean,
				default: true
			},
			isShow: {
				type: Boolean,
				default: false
			},
			options: {
				useDynamicSize: false,
				errorCorrectLevel: 'Q',
				// margin: 10,
				areaColor: "#fff",
				// 指定二维码前景，一般可在中间放logo
				// foregroundImageSrc: require('static/image/logo.png')
			},
			postImg: ''

		},
		data() {
			return {
				bgImage: 'https://cdn.yanjie.art/image/20241224/aff4cb1c9146cf1d2b58e8ef0476cd69_750x1624.png',
			}
		},
		watch: {
			isShowIncomeRatio(newVal) {
				this.checkAllFalse();
			},
			isShowIncome(newVal) {
				this.checkAllFalse();
			},
			isShowFloatingIncome(newVal) {
				this.checkAllFalse();
			},
			isShowRealIncome(newVal) {
				this.checkAllFalse();
			}
		},
		methods: {
			formatDate(date) {
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hours = String(date.getHours()).padStart(2, '0')
				const minutes = String(date.getMinutes()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}`
			},
			checkAllFalse() {
				if (!this.isShowIncomeRatio && !this.isShowIncome && !this.isShowFloatingIncome && !this.isShowRealIncome) {
					this.isShow = true;
				} else {
					this.isShow = false;
				}
			},
			handleSave() {
				// #ifdef H5
				this.saveH5Image()
				// #endif
				// #ifdef APP-PLUS
				this.saveAppImage()
				// #endif
			},
			saveH5Image() {
				// H5保存图片逻辑
			},
			saveAppImage() {
				// APP保存图片逻辑 
			},
			handleWechat() {
				this.$emit('wechat')
			},
			handleMoments() {
				this.$emit('moments')
			},
			handleCopy() {
				uni.setClipboardData({
					data: this.info.shareLink,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						})
					}
				})
			},
			handleClose() {
				this.$emit('input', false)
			},
			// 脱敏处理方法 - 中间只保留5个*
			desensitize(str) {
				if (!str) return ''
				if (str.length <= 8) return str
				const firstFive = str.substring(0, 5)
				const lastThree = str.substring(str.length - 3)
				const middle = '*****'
				return firstFive + middle + lastThree
			},
			showIncomeRatio() {
				this.isShowIncomeRatio = !this.isShowIncomeRatio
			},
			showIncome() {
				this.isShowIncome = !this.isShowIncome
			},
			showFloatingIncome() {
				this.isShowFloatingIncome = !this.isShowFloatingIncome
			},
			showRealIncome() {
				this.isShowRealIncome = !this.isShowRealIncome
			},
			checkAgreement() {
				this.isShow = !this.isShow
				if (this.isShow) {
					this.isShowIncomeRatio = false
					this.isShowIncome = false
					this.isShowFloatingIncome = false
					this.isShowRealIncome = false
				} else {
					this.isShowIncomeRatio = true
					this.isShowIncome = true
					this.isShowFloatingIncome = true
					this.isShowRealIncome = true
				}
			},
			showInvite() {
				this.isShowInviteCode = !this.isShowInviteCode
			},
			getUrl(option) {
				console.log(option)
				uni.hideLoading()
				// this.postImg = option.base64
				// #ifdef APP
				this.saveHeadImgFile(option.base64)
				// #endif
				// #ifdef H5
				this.saveImage(option.base64)
				// #endif
				// // #ifdef H5
				// if(uni.getSystemInfoSync().platform == 'ios'){
				// 	this.showPop = true
				// }else{
				// 	this.fileDownload(this.postImg)
				// }
				// // #endif
			},
			saveImage(url) {
				// #ifdef H5
				let image = new Image()
				image.setAttribute("crossOrigin", 'Anonymous')
				image.src = url
				image.onload = function() {
					let canvas = document.createElement('canvas')
					canvas.width = image.width
					canvas.height = image.height
					let context = canvas.getContext('2d')
					context.drawImage(image, 0, 0, image.width, image.height)
					let url = canvas.toDataURL('image/png')
					let a = document.createElement('a')
					let event = new MouseEvent('click')
					a.download = '分享好友'
					a.href = url
					a.dispatchEvent(event)
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				}
				// #endif
				// #ifdef APP-PLUS
				uni.saveImageToPhotosAlbum({
					filePath: url,
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						})
					},
					fail: () => {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						})
					}
				})
				// #endif
			},
			saveHeadImgFile(base64) {
				const bitmap = new plus.nativeObj.Bitmap("test");
				bitmap.loadBase64Data(base64, function() {
					const url = "_doc/" + new Date().getTime() + ".png"; // url为时间戳命名方式
					console.log('saveHeadImgFile', url)
					bitmap.save(url, {
						overwrite: true, // 是否覆盖
						// quality: 'quality'  // 图片清晰度
					}, (i) => {
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success: function() {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
								bitmap.clear()
							}
						});
					}, (e) => {
						uni.showToast({
							title: '图片保存失败',
							icon: 'none'
						})
						bitmap.clear()
					});
				}, (e) => {
					uni.showToast({
						title: '图片保存失败',
						icon: 'none'
					})
					bitmap.clear()
				});
			},
			loaddingTost() {
				uni.showLoading({
					title: "图片生成中",
					mask: true
				})
			},
			getRenderData() {
				// 返回需要传递给 renderjs 的数据
				return {
					isShow: this.isShow,
					// ... 其他需要传递的数据
				}
			},
			// #ifdef H5
			fileDownload(downloadUrl) {
				const a = document.createElement('a');
				a.href = downloadUrl;
				a.download = '邀请好友.jpg';
				a.click();
				uni.showToast({
					title: '图片保存成功',
					icon: 'none'
				})
			},
			// base64转blob方法
			dataURItoBlob(dataURI) {
				const byteString = atob(dataURI.split(',')[1]);
				const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
				const ab = new ArrayBuffer(byteString.length);
				const ia = new Uint8Array(ab);

				for (let i = 0; i < byteString.length; i++) {
					ia[i] = byteString.charCodeAt(i);
				}

				return new Blob([ab], {
					type: mimeString
				});
			}

			// #endif
		}
	}
</script>
<script module="canvas" lang="renderjs">
	import html2canvas from 'html2canvas'
	export default {
		mounted() {},
		methods: {
			// 生成图片需要调用的方法
			generateImage(ownerInstance) {
				let dom = document.getElementById('inviteNew') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;

				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			generateImageOld(ownerInstance) {
				let dom = document.getElementById('inviteOld') // 需要生成图片内容的
				console.log('dom', dom)
				console.log(dom.offsetWidth)
				const box = window.getComputedStyle(dom);
				// DOM 节点计算后宽高
				const width = parseInt(box.width, 10);
				const height = parseInt(box.height, 10);
				// const width = dom.offsetWidth
				// const height = dom.offsetHeight
				const canvas = document.createElement('canvas')
				const scale = window.devicePixelRatio && window.devicePixelRatio > 1 ? window.devicePixelRatio : 1
				console.log(scale)
				canvas.width = width * scale
				canvas.height = height * scale
				// 关闭抗锯齿
				canvas.mozImageSmoothingEnabled = false;
				canvas.webkitImageSmoothingEnabled = false;
				canvas.msImageSmoothingEnabled = false;
				canvas.imageSmoothingEnabled = false;
			
				canvas.getContext('2d').scale(scale, scale)
				console.log(canvas)
				html2canvas(dom, {
					width: dom.clientWidth, //dom 原始宽度
					height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					// canvas,
					useCORS: true, //支持跨域
					backgroundColor: 'transparent',
					scale: 2, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 1, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					// const context = canvas.getContext('2d');
					// let img = Canvas2Image.converToImage(canvas, canvas.width, canvas.height)
					// context.mozImageSmoothingEnabled = false;
					// context.webkitImageSmoothingEnabled = false;
					// context.msImageSmoothingEnabled = false;
					// context.imageSmoothingEnabled = false;
					// this.postImg = canvas.toDataURL('image/jpeg', 1)
					console.log(canvas.toDataURL('image/jpeg', 1))
					//回调数据到serve层面
					ownerInstance.callMethod('getUrl', {
						base64: canvas.toDataURL('image/jpeg', 1)
					})
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.isShowMask = false;
				})
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				console.log(event, ownerInstance)
				this.generateImage(ownerInstance)
				// ownerInstance.callMethod('onViewClick', {
				// 	test: 'test'
				// })
			},
			onClickOld(event, ownerInstance) {
				// 调用 service 层的方法
				console.log(event, ownerInstance)
				this.generateImageOld(ownerInstance)
				// ownerInstance.callMethod('onViewClick', {
				// 	test: 'test'
				// })
			},
			
		},
	}
</script>
<style lang="scss" scoped>
	.share-dialog {
		position: relative;
		width: 750rpx;
		height: 1624rpx;
		background: #241E15;

		.back-btn {
			position: absolute;
			/* #ifdef APP-PLUS */
			top: 100rpx;
			/* #endif */
			/* #ifdef H5 */
				top: 40rpx;
			/* #endif */
			left: 40rpx;
			width: 26rpx;
			height: 50rpx;
			z-index: 10;
			padding: 20rpx;
		}

		.bg-image {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}

		.content {
			position: relative;
			z-index: 2;
			padding-top: 168rpx;

			.cart_bg {
				width: 680rpx;
				height: 870rpx;
				margin: 0 auto;
				background-image: url('https://cdn.yanjie.art/image/20241224/b475265ab4243c9407a4922b10cf2d59_1020x1305.png');
				background-size: 100% 100%;
				background-repeat: no-repeat;
				padding: 146rpx 39rpx;
			}

			.user-info {
				display: flex;
				align-items: center;
				margin-bottom: 27rpx;

				.user-info-text {
					display: flex;
					flex-direction: column;
					margin-left: 20rpx;

					.contractAddress {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.65);
					}

					.username {
						font-size: 24rpx;
						color: #FFFFFF;
					}
				}

				.avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
				}


			}

			.profit-details {
				.detail-item {
					display: flex;
					justify-content: flex-start;
					flex-wrap: wrap;
					margin-top: 24rpx;
					margin-bottom: 28rpx;

					.label {
						font-size: 28rpx;
						color: rgba(255, 255, 255, 0.65);
						margin-right: 20rpx;
					}

					.value {
						font-size: 28rpx;
						color: #D8B662;
					}
				}

				.detail-item:first-child {
					.value {
						width: 100%;
						font-size: 88rpx;
						color: #D8B662;

					}
				}
			}

			.logo {
				text-align: center;
				margin-bottom: 80rpx;

				image {
					width: 200rpx;
					height: 60rpx;
				}
			}

			.bottom-info {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.slogan {
					img {
						width: 186rpx;
						height: 116rpx;
						margin-bottom: 16rpx;
					}

					.invite {
						font-size: 28rpx;
						color: rgba(216, 182, 98, 0.65);
						display: flex;
						justify-content: flex-start;
						align-items: center;
						width: 100%;

						text {
							color: #FFFFFF;
							font-size: 28rpx;
						}
					}
				}

				.qr-code {
					margin-bottom: 20rpx;

					.img {
						display: flex;
						justify-content: center;
						align-items: center;

						image {
							width: 150rpx;
							height: 150rpx;
							background: #fff;
						}

						.white {
							width: 150rpx;
							height: 150rpx;
							background: #fff;
							padding: 10rpx;
						}
					}

					.text_1 {
						font-size: 24rpx;
						color: rgba(216, 182, 98, 0.6);
						margin-top: 20rpx;
					}
				}
			}
		}

		.ul_view {
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 680rpx;
			height: 64rpx;
			position: relative;
			z-index: 10;
			margin: 74rpx auto;

			.li {
				font-size: 24rpx;
				color: rgba(216, 182, 98, 0.65);
				background-color: rgba(36, 30, 21, 1);
				width: 140rpx;
				text-align: center;
				line-height: 64rpx;

				&.active {
					background-color: rgba(216, 182, 98, 1);
					color: #241E15;
				}
			}
		}

		.hide-view {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			color: rgba(216, 182, 98, 0.65);
			position: relative;
			z-index: 10;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 14rpx;
			}
		}

		.action-buttons {
			position: absolute;
			/* #ifdef APP-PLUS */
			bottom: 100rpx;
			/* #endif */
			/* #ifdef H5 */
				bottom: 40rpx;
			/* #endif */
			left: 0;
			right: 0;
			z-index: 3;

			.btn-group {
				display: flex;
				justify-content: space-around;
				align-items: center;
				padding: 0 22rpx;

				.btn-item {
					display: flex;
					align-items: center;
					flex-direction: column;

					image {
						width: 100rpx;
						height: 100rpx;
						margin-bottom: 12rpx;
					}

					text {
						font-size: 24rpx;
						color: rgba(216, 182, 98, 0.8);
					}
				}
			}
		}
	}

	.height {
		height: 220rpx;
	}

	.head_bg {
		background-image: url("https://cdn.yanjie.art/image/20241224/728e94845d9035f73b3e77c69ef3c75c_1020x1305.png");
		background-size: 100% 100%;
		padding-bottom: 40rpx;
		width: 680rpx;
		height: 900rpx;
		margin: 0 auto;
	}

	.invite_code {
		margin: 0 45rpx 0rpx 45rpx;
		height: 620rpx;
		position: relative;
		text-align: center;
		display: flex;
		flex-direction: column;

		.nickname {
			font-weight: 400;
			font-size: 28rpx;
			color: #FFFFFF;
		}

		.qrcode {
			background-image: url("https://cdn.yanjie.art/image/20241108/727dbc3d0cbf918287a41d6ebb5c3ad2_244x244.png");
			background-size: 100% 100%;
			width: 246rpx;
			height: 246rpx;
			margin: 0 auto;
			margin-top: 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.white {
				width: 200rpx;
				height: 200rpx;
				background: #EDEDED;
				display: flex;
				justify-content: center;
				align-items: center;

			}
		}

		.scan {
			margin-top: 30rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #A6A6A6;
		}

		.codes {
			margin-top: 14rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #D8B662;

			>text {
				font-weight: bold;
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}

		text {
			&:nth-of-type(1) {
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}

		.border {
			width: 145rpx;
			height: 145rpx;
			position: absolute;
			left: 235rpx;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			border: 2px solid #D8B662;
			overflow: hidden;

			>img {
				border-radius: 50%;
				width: 145rpx;
				height: 145rpx;
				background-color: #fff;
			}
		}

	}
</style>