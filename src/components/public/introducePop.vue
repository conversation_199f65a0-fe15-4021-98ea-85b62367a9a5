<template>
	<view>
		<u-modal class="model" width="600" v-model="isModel" :show-title="showTitle" :show-confirm-button="false"
			border-radius="0" :title="title" :title-style="titleObject">
			<view class="colse" @click="colse()">
				<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
			</view>
			<view class="introduce">
				{{introduce}}
			</view>
		</u-modal>
		<u-toast ref="uToast" />
	</view>
</template>

<script>
	export default {
		name: 'introducePop',
		data() {
			return {
				titleObject: {
					'color': '#F9F9F9',
					'padding': '40rpx 40rpx 0rpx 40rpx',
					'font-size': '32rpx',
					'font-weight': '600'
				},
			}
		},
		computed: {
			isModel: {
				get() {
					return this.show
				},
				set(val) {
					console.log(val)
				},
			},
		},
		watch: {

		},
		props: {
			show: {
				type: <PERSON><PERSON>an,
				default () {
					return false
				}
			},
			title: {
				type: [String]
			},
			introduce: {
				type: [String]
			},
			showTitle:{
				type: [<PERSON><PERSON><PERSON>],
				default () {
					return true
				}
			}
		},
		methods: {
			colse() {
				console.log("关闭")
				this.$emit('update:show', false)
			}, 
		}

	}
</script>

<style lang="scss" scoped>
	.model::v-deep {}

	::v-deep .u-toast.u-show {
		background-color: rgba(0, 0, 0, 0.9) !important;
		color: #F9F9F9;
		border-radius: 10rpx !important;
		padding: 40rpx 40rpx;
	}

	// .u-modal {
	//   border: 1px solid cyan;
	//   background: var(--dialog-bg-color);
	//   border-radius: 4rpx;
	//   width: 100%;
	//   height: 100%;
	// }
	.model {
		
		.colse {
			image {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
				width: 48rpx;
				height: 48rpx;
			}
		}

		.msg {
			color: #F9F9F9;
			font-size: 26rpx;
			padding: 0rpx 40rpx 28rpx 40rpx;
		}
	}

	.input-box {
		border-bottom: 1rpx solid #282828;
		padding-bottom: 20rpx;
	}

	.introduce {
		padding: 60rpx 40rpx 48rpx 40rpx;
		font-size: 28rpx;
		line-height: 38rpx;
		text-align: center;
		color:#FFFFFF;
	}
</style>
