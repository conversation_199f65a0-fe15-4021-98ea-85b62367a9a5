<template>
	<view>
		<!-- 确认设置该作品为头像 -->
		<u-toast ref="uToast" />
		<u-modal ref="setAvatarPop" v-model="isShowModalSetTx" title="确认设置该作品为头像？" border-radius="0"
			:content-style="bgObject" :title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn space-between">
				<view class="but mb-cancel" @click="isShowModalSetTx=false">取消</view>
				<view class="but mb-confirm" @click="setImage()">确定</view>
			</view>
		</u-modal>
		<!--确认 一键销毁 -->
		<u-modal v-model="isShowModalDestroy" title="确认销毁该作品？" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-content">
				销毁作品后将无法恢复，是否确定删除该作品？
			</view>
			<view class="modal-btn ">
				<view class="but mb-cancel" @click="isShowModalDestroy=false">取消</view>
				<view class="but mb-confirm" @click="openDestroy">确定</view>
			</view>
		</u-modal>

		<!--确认 创作/藏品一键停售 -->
		<u-modal v-model="isShowModalDown" title="确认停售该作品？" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn ">
				<view class="but mb-cancel" @click="isShowModalDown=false">取消</view>
				<view class="but mb-confirm" @click="checkDown()">确定</view>
			</view>
		</u-modal>
		<!--确认 创作/藏品一键寄售 -->
		<u-modal v-model="isShowModalUp" title="确认寄售该作品？" border-radius="0" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="modal-btn ">
				<view class="but mb-cancel" @click="isShowModalUp=false">取消</view>
				<view class="but mb-confirm" @click="checkUp()">确定</view>
			</view>
		</u-modal>
		<u-modal v-model="isWorkSsucceed" border-radius="0" :show-title="false" :content-style="bgObject"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="work_ssucceed">
				<view class="icon">
					<image src="@/static/imgs/public/checked.png" mode="widthFix"></image>
				</view>
				<view class="msg">
					{{workSsucceedText}}
				</view>
			</view>
		</u-modal>
		<resalePop :isShowModalResale.sync="isShowModalResale" :itemId="tid" @closeResale="closeResale">
		</resalePop>
		<!-- 密码支付, 设置密码 -->

		<u-modal class="" v-model="isPasswordText" width="80%" :show-title="false" :show-confirm-button="false"
			border-radius="0">
			<view class="BankVerifyBody">
				<view class="head_title_y">
					<view class="right" @click="isPasswordText=false">
						<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
					</view>
					请先设置支付密码
				</view>
				<view class="msg_y">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="modal-btn" style="padding:30rpx 0rpx 0rpx 0rpx;">
					<view class="but mb-cancel" @click="isPasswordText=false">取消</view>
					<view class="but mb-confirm" @click="SetPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>
		<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
			email="333" :mode=mode @pay="password" v-if="isMoreShow" @createSuccess="createSuccess" />
		<!--  -->
		<ModelShare ref="share" :tid="tid">
		</ModelShare>
		<!-- <popup-bar v-model="isRegistration" title="实名认证" @cancel="isRegistration = false" @confirm="nav_realName()">
		</popup-bar> -->
	</view>
</template>

<script>
	/**
	 * 一键设置头像,销毁作品,作品证书合成组件
	 * @description 弹窗组件
	 * <AUTHOR>
	 * @property {String} tid 作品tid/tokenId
	 * @property {String} mode 模式,set:一键设置头像,destroy: 一键销毁作品, cart:作品证书
	 * @event succeed 支付成功后触发
	 */
	import uQRCode from 'uqrcodejs';
	import payPopup from "@/components/payPopup/index.vue";
	import resalePop from '@/components/public/resalePop';
	import html2canvas from 'html2canvas';
	import ModelShare from "@/components/index/model_share"
	export default {
		name: 'setAvatarPop',
		data() {
			return {
				titleObject: {
					'background-color': '#35333E', 
					'color': '#FFFFFF'
				},
				bgObject: {
					'background-color': '#35333E',
					'color': '#FFFFFF'
				},
				isShowModalSetTx: false,
				isShowModalDestroy: false,
				isShowMaskCert: false,
				isShowMask: false,

				val: "", // 要生成的二维码值
				size: 66, // 二维码大小
				unit: 'px', // 单位
				background: '#ffffff', // 背景色
				foreground: '#000000', // 前景色
				pdground: '#000000', // 角标色
				icon: '', // 二维码图标
				iconsize: 20, // 二维码图标大小
				lv: 3, // 二维码容错级别 ， 一般不用设置，默认就行
				onval: true, // val值变化时自动重新生成二维码
				loadMake: true, // 组件加载完成后自动生成二维码
				src: '', // 二维码生成后的图片地址或base64
				url: "",
				ifShow: true,
				isShowModalDown: false,
				workSsucceedText: "",
				isWorkSsucceed: false,
				isShowModalUp: false,
				isShowModalResale: false,
				resalePrice: false,
				isPasswordImport: false,
				isPasswordText: false,
				mode: "pay",
				downImg: "",
				isRegistration: false,
				passwordTitle: "确认寄售",
				passwordMsg: "请输入余额支付密码，用于寄售",
				operateType:''
			}
		},
		components: {
			resalePop,
			payPopup,
			html2canvas,
			ModelShare
		},
		props: {
			tid: {
				type: String,
				default () {
					return ''
				}
			},
			detailsList: {
				type: Object,
				default () {
					return {
						createUser: {}
					}
				}
			},
			isCreation: {
				// type: Number,
				default () {
					return ''
				}
			},
			isMoreShow: {
				type: Boolean,
				default () {
					return false
				}
			}

		},
		methods: {
			async setImage() {
				let res = await this.$api.java_userEditAvatar({
					tid: this.tid
				});
				if (res.status.code == 0) {
					this.$refs.uToast.show({
						title: "头像设置成功",
						type: 'success',
					})
					this.isShowModalSetTx = false,
						this.$emit('succeed')
				} else {
					this.isShowModalSetTx = false,
						uni.showToast({
							title: res.status.msg,
							icon: "none",
							duration: 3000
						})
				}
			},
			openDestroy(){
				this.operateType = 'destroy'
				this.isShowModalDestroy = false
				if (uni.getStorageSync("isSetTradePassword") == 1) {
					this.mode = "pay"
					this.isPasswordImport = true
					this.passwordTitle="确认销毁"
					this.passwordMsg="请输入余额支付密码，用于销毁"
				} else {
					this.isPasswordText = true
				}
			},
			async destroy(tradePassword) {
				let res = await this.$api.goodsDestroy({
					tid: this.tid,
					tradePassword
				})
				if (res.status.code == 0) {
					uni.showToast({
						title: '作品销毁成功',
						icon: 'none',
						duration: 2000
					});
					this.$emit('destroySucceed')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 2000
					});
				}
			},
			qrR(res) {
				this.src = res;
				console.log(res)
			},
			openPoster() {
				this.show = false
				// this.$emit("poster")
			},
			openPop(mode) {
				if (mode == "set") {
					this.isShowModalSetTx = true
				} else if (mode == "destroy") {
					this.isShowModalDestroy = true
				} else if (mode == 'cert') {
					this.$refs.share.isNormal = true
					this.$refs.share.openMaskCert(3)
				} else if (mode == 'down') {
					this.isShowModalDown = true
				} else if (mode == 'up') {
					// let authStatus = uni.getStorageSync('authStatus')
					// if (!authStatus || authStatus === 30) {
					// 	this.isRegistration = true
					// } else {
						
					// 	// this.isShowModalUp = true
					// }
					this.checkUp()
				}

			},
			openShowModalDown() {
				this.isShowModalDown = true
			},
			checkDown() {
				this.operateType = 'donw'
				this.isShowModalDown = false
				if (this.isCreation === 1) {
					//创作
					console.log(this.isCreation)
					this.creationOffShelfConfirm(this.tid, 0, this.detailsList.price, '')
				} else {
					//藏品
					this.collectionOffShelfConfirm()
				}
			},
			checkUp() {
				this.operateType = 'up'
				this.isShowModalUp = false
				if (uni.getStorageSync("isSetTradePassword") == 1) {
					this.mode = "pay"
					if (this.isCreation === 1) {
						//创作
						this.isPasswordImport = true
						this.passwordTitle="确认寄售"
						this.passwordMsg="请输入余额支付密码，用于寄售"
					} else {
						//藏品
						this.isShowModalResale = true
						// this.collectionPutaway(111111)
					}
				} else {
					this.isPasswordText = true
				}
			},
			// 藏品一键停售请求
			async collectionOffShelfConfirm() {
				let res = await this.$api.unSale({
					tid: this.tid,
				})
				if (res.status.code == 0) {
					this.workSsucceedText = "作品停售成功！"
					this.isWorkSsucceed = true
					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
					this.$emit('downSucceed')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			//创作一键停售请求
			async creationOffShelfConfirm(tid, visibility, price, password) {
				let res = await this.$api.unSale({
					tid,
					price,
					tradePassword: password
				})
				if (res.status.code == 0) {
					this.workSsucceedText = "作品停售成功！"
					this.isWorkSsucceed = true
					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
					this.$emit('downSucceed')
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 2000
					});
				}
			},
			async collectionPutaway(password) { //提交藏品寄售
				let res = await this.$api.visibility({
					tid: this.tid,
					price: this.resalePrice,
					tradePassword: password
				});
				if (res.status.code == 0) {
					this.isWorkSsucceed = true
					this.workSsucceedText = "作品寄售成功！"

					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
					this.$emit('upSucceed', this.resalePrice)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: "none",
						duration: 2000
					});
				}
			},
			async offAndOnShelf(tid, visibility, price, password) {
				let res;
				if (visibility == 0) {
					res = await this.$api.unSale({
						tid,
						price,
						tradePassword: password
					})
				} else {
					res = await this.$api.visibility({
						tid,
						price,
						tradePassword: password
					})
				}
				if (res.status.code == 0) {
					this.workSsucceedText = "作品寄售成功！"
					this.isWorkSsucceed = true
					setTimeout(() => {
						this.isWorkSsucceed = false
					}, 2000)
					this.$emit('upSucceed')
				} else {
					this.passwordImportPay = ""
					this.isPasswordImport = false
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 2000
					});
				}
			},
			closeResale(data) {
				this.isShowModalResale = false
				this.contentList = []
				if (data) {
					this.mode = "pay"
					this.resalePrice = data
					this.isPasswordImport = true
				}
			},
			password(e) {
				console.log(e)
				this.isPasswordImport = false
				if(this.operateType=='destroy'){
					this.destroy(e)
				}else{
					if (this.isCreation == 1) {
						this.offAndOnShelf(this.tid, 1, this.detailsList.price, e)
					} else {
						// this.isShowModalResale = true
						this.collectionPutaway(e)
					}
				}
			},
			createSuccess(psw) {
				uni.setStorageSync("isSetTradePassword", 1)
				this.isSetTradePassword = 1
				this.isPasswordImport = false
				// this.checkUp()
				// this.finishPay(psw)
			},
			nav_paySuccess() {

			},
			SetPayPassword() {
				this.isPasswordText = false
				this.mode = "set"
				this.isPasswordImport = true
			},
			// 生成证书 查询
			async goodsDetails() {
				let res = await this.$api.java_goodsDetails({
					itemTokenId: this.tid,
				});
				if (res.status.code == 0) {
					const data = res.result;
					this.existingevidenceList = {
						"u_avatar": data.createUser.avatar,
						"price": data.price,
						"title": data.name,
						"cover": data.photoShow,
						"o_nickname": data.ownerUser.name,
						"c_nickname": data.createUser.name,
						"created_at": data.createTime,
						"tid": data.tokenId
					};
					uni.showToast({
						icon: 'loading',
						mask: true,
						duration: 2000
					})
					setTimeout(() => {
						this.generateImage('goodsDetail')
					}, 2000)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			// 生成图片需要调用的方法
			generateImage(e) {
				uni.showLoading({
					title: '生成海报中',
					mask: true,
				})
				const dom = document.getElementById(e) // 需要生成图片内容的 dom 节点
				console.log(dom)
				html2canvas(dom, {
					// width: dom.clientWidth, //dom 原始宽度
					// height: dom.clientHeight,
					// scrollY: 0, // html2canvas默认绘制视图内的页面，需要把scrollY，scrollX设置为0
					// scrollX: 0,
					useCORS: true, //支持跨域
					// background: 'transparent',
					scale: 6, // 按比例增加分辨率 (2=双倍)
					dpi: window.devicePixelRatio * 6, // 设备像素比
					// scale: 2, // 设置生成图片的像素比例，默认是1，如果生成的图片模糊的话可以开启该配置项
				}).then((canvas) => {
					this.downImg = canvas.toDataURL('image/png', 1)
					console.log(this.downImg)
					this.isShowMaskCert = true
					uni.hideLoading();
				}).catch(err => {
					// 生成失败 弹出提示弹窗
					// this.$refs.uToast.show({
					//   title: '生成失败',
					//   type: 'error',
					// })
					this.isShowMask = false;
				})
			},
			// 长链接转短链接
			async getShortLink(url, name) {
				const {
					status: {
						code,
						msg
					},
					result
				} = await this.$api.java_shortLink({
					longLink: url
				});
				if (code == 0) {
					this.val = result.shortUrl
					const ctx = uni.createCanvasContext('grcode');
					const uqrcode = new uQRCode({
							text: this.val,
							size: this.size
						},
						ctx
					);
					uqrcode.make();
					uqrcode.draw();
				} else {
					uni.showToast({
						title: msg,
						icon: "none",
						duration: 3000
					});
				}
			},
			//跳转至实名
			nav_realName() {
				this.isRegistration = false
				if (uni.getStorageSync('authStatus') == 30) {
					this.$Router.push({
						name: "authentication"
					})
				} else {
					this.$Router.push({
						name: "realName"
					})
				}
			},
		}

	}
</script>

<style lang="scss" scoped>
	.modal-content {
		padding: 40rpx 40rpx 0rpx 40rpx;
		font-size: 28rpx;
		line-height: 40rpx;
		text-align: center;
	}

	.modal-btn {
		padding: 60rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.but {
			width: 240rpx;
			text-align: center;
			line-height: 80rpx;
			height: 80rpx;
		}

		.mb-cancel {
			color: #666;
			border: 2rpx solid #616161;
			color: var(--message-box-point-color);
			border-radius: 4rpx;
		}

		.mb-confirm {
			color: var(--main-bg-color);
			box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
			background:var(--primary-button-color);
			
		}
	}

	.bg-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #2A2A38 0%, #273641 47%, #1E2630 100%);
		z-index: 1000;
		transition: all 0.3s ease-in-out 0s;
		transform: scale(1, 1);
	}

	.modal-cert {
		width: 600rpx;
		height: auto;
		position: fixed;
		// top: -99999px;
		// left: -999999px;
		// position: fixed;
		top: 20%;
		left: 50%;
		transform: translate(-50%, -20%);
		z-index: 1000;
		border-radius: 20rpx;
		overflow: hidden;
		background-image: url('@/static/imgs/personal/de_bg.png');
		background-size: cover;
		background-repeat: no-repeat;
		padding: 30rpx 40rpx 40rpx;

		.cert_top {
			position: relative;

			.time {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #616161;
				line-height: 20rpx;
				transform: scale(0.94);
				text-align: center;
				margin-bottom: 20rpx;
			}

			.photoShow {
				height: 480rpx;
				width: 520rpx;
				position: relative;
				margin-top: 20rpx;

				.img {
					width: 100%;
					height: 480rpx;
					border-radius: 20rpx;
					overflow: hidden;
					background-color: #fff;
				}

				.icon {
					width: 224rpx;
					height: 224rpx;
					position: absolute;
					right: -32rpx;
					bottom: -196rpx;
					// width:200rpx;
					z-index: 100;
				}
			}

			.price {
				margin-top: 50rpx;
				padding-bottom: 60rpx;
				font-size: 44rpx;
				font-family: MiSans-Demibold, MiSans;
				font-weight: 600;
				color: #F9F9F9;
				line-height: 44rpx;
			}

			.content_title {
				font-size: 36rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 50rpx;
				display: flex;
				align-items: center;

				.cert_name {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.type_tip {
					background: #1FEDF0;
					border-radius: 2rpx;
					margin-right: 16rpx;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #121212;
					height: 36rpx;
					width: 70rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					transform: scale(0.9);
				}
			}
		}

		.user {
			padding: 10rpx 0 60rpx;
			display: flex;
			align-items: center;
			font-size: 28rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #616161;
			line-height: 40rpx;

			image {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				overflow: hidden;
				margin-right: 12rpx;
			}
		}

		.cert_end {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 30rpx;
			border-top: 2rpx dashed #303030;

			.cert-left {
				width: 400rpx;

				.left_top {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #F9F9F9;
					line-height: 40rx;

					image {
						width: 52rpx;
						height: 52rpx;
						margin-right: 20rpx;
					}
				}

				.left-tip {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #616161;
					line-height: 28rpx;
					margin-top: 20rpx;
				}
			}

			.qrimg-i {
				width: 140rpx;
				height: 140rpx;
				background-color: #F9F9F9;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.work_ssucceed {
		padding: 60rpx;

		.icon {
			display: flex;
			justify-content: center;

			image {
				width: 60rpx;
			}
		}

		.msg {
			margin-top: 30rpx;
			text-align: center;
		}
	}

	.BankVerifyBody {
		padding: 42rpx;

		.head_title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
			color: var(--message-box-point-color);

			.right {
				position: absolute;
				right: 40rpx;
				top: 86rpx;

				image {
					width: 50rpx;
				}
			}
		}

		.footer {
			button {
				width: 100%;
				height: 88rpx;
				background-color: #333333;
				color: var(--message-box-point-color);
				line-height: 88rpx;
				border-radius: 44rpx;
				font-size: 32rpx;
			}
		}
	}

	.head_title_y {
		text-align: left;
		font-size: 40rpx;
		font-weight: 600;
		height: 80rpx;
		line-height: 80rpx;
		color: var(--message-box-point-color);

		.right {
			position: absolute;
			right: 40rpx;
			top: 46rpx;

			image {
				width: 40rpx;
			}
		}
	}

	.msg_y {
		color: #999999;
		font-size: 30rpx;
		line-height: 32rpx;
	}

	.footer_y {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		button {
			margin: 0rpx;
			width: 238rpx;
			height: 64rpx;
			width: 240rpx;
			line-height: 64rpx;
			text-align: center;
			background-color: #999999;
			color: var(--message-box-point-color);
			border-radius: 0rpx;
			font-size: 30rpx;

			&.active {
				color: var(--main-bg-color);
				box-shadow: inset 0px 1px 3px 0px rgba(255, 255, 255, 0.5);
				background: var(--primary-button-color);
				color: var(--message-box-point-color);
			}
		}
	}

	.autoImg {
		position: fixed;
		width: 600rpx;
		height: 1070rpx;
		border-radius: 20rpx;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1000;

		img {
			width: 600rpx;
			height: 1070rpx;
			border-radius: 20rpx;
		}
	}
</style>
