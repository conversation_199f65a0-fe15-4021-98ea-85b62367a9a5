<template>
	<view>
		<u-modal class="model" width="670" border-radius="0" v-model="show" :show-title="false"
			:show-confirm-button="false">
			<view style="padding:40rpx 40rpx 0rpx 40rpx;">
				<view class="title_bg">
					<image
						src="https://cdn.yanjie.art/image/20241030/dcd5744034861613f37a7e03899f4994_292x47.png"
						mode="widthFix"></image>
				</view>

				<view class="input-box">
					<text><price-symbol></price-symbol></text>
					<u-input class="modal-resale-input" v-model="resalePrice" placeholder="" type="number" border
						border-color="transparent" :trim="true" :adjust-position="true" :show-confirmbar="true"
						:custom-style="{ 'padding-left': '25rpx' }" @input="changePrice" :clearable="false" />
				</view>
				<view class="resale_pop_msg">
					<view class="li" v-for="(item, index) in contentList" :key="index">
						<view class="key">
							{{ item.key }}
						</view>
						<view class="value" v-html="formatValue(item.value)"></view>
					</view>

					<!-- <view class="li" v-for="(item, index) in contentList2" :key="index" v-if="info.isAutoSeries == 1">
						<view class="key">
							{{ item.key }}
						</view>
						<view class="value" v-html="formatValue(item.value)"></view>
					</view> -->
				</view>
			</view>

			<view style="padding:0rpx 40rpx 40rpx 40rpx;">
				<view class="xieyi">
					<view class="p">
						<view class="xieyi_msg">
							<image class="img" @click="j_isAgree" v-if="isAgree"
								src="https://cdn.yanjie.art/image/20241023/98b6f94570306e0b3796dd72613fa8f8_24x26.png"
								mode="">
							</image>
							<image class="img" @click="j_isAgree" v-else
								src="https://cdn.yanjie.art/image/20241023/63ed1abbf0cd92dddc8e5d78de022152_24x24.png"
								mode="">
							</image>
							<text>
								<text @click="j_isAgree">已阅读并同意上述价格规则及</text>
								<text class="protocol" @click="nav_link('平台用户售卖服务协议', 1)">《平台用户售卖服务协议》</text>
							</text>
						</view>
					</view>
				</view>
				<view class="buttons">
					<view class="img_cancel model_button_cancel_bg" @click="resaleCancel()">
						返回
					</view>
					<view class="img_reasale model_button_reasale_bg" @click="reasaleConfirm()">
						确认
					</view>
				</view>
			</view>

		</u-modal>
		<!--确认 一键销毁 -->
		<u-modal v-model="isShowMsg" border-radius="0" :content-style="bgObject" :show-title="false" width="638"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					恭喜您
				</view>
				<view class="modal-content">
					{{ tip }}
				</view>
				<view class="showModal-btn">
					<view class="img_reasale" @click="confirm()">仍要寄售</view>
					<view class="img_cancel" @click="cancel()">修改价格</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isConfirmWorks" :content-style="bgObject" :title-style="titleObject" border-radius="0"
			width="638" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<image
						src="https://cdn.yanjie.art/image/20241030/9a05a72f925af537e099eeafd9ce4393_252x47.png"
						mode="widthFix"></image>
				</view>
				<view class="modal-content">
					是否以<text style="color: #D8B662;">{{ resalePrice }}元</text> 寄售此作品？
				</view>
				<view class="showModal-btn">
					<view class="img_reasale model_button_cancel_bg" @click="confirmWorks()">仍要寄售</view>
					<view class="img_cancel model_button_reasale_bg" @click="cancelWorks()">修改价格</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import antiShake from "@/common/public.js"
export default {
	name: 'resalePop',
	model: {
		prop: 'isShowModalResale',
		event: 'closeResale',
	},
	data() {
		return {
			resalePrice: '', // 寄售价格
			isAgree: '', // 是否同意
			contentList: [],
			contentList2: [],
			tip: "",
			titleObject: {
			},
			bgObject: {
			},
			isShowMsg: false,
			topTip: "",
			isConfirmWorks: false,
			isSuccess: false,
			info: {}
		}
	},
	computed: {
		show: {
			get() {
				return this.isShowModalResale;
			},
			set(val) { },
		}
	},
	watch: {
		isShowModalResale(val) {
			this.show = val;
		},
		resalePrice(val){
			this.changePrice()
		}
	},
	props: {
		isShowModalResale: {
			type: Boolean,
			default() {
				return false
			}
		},
		itemId: {
			type: [String, Number]
		}
		// contentList: {
		//   type: Array,
		//   default() {
		//     return []
		//   }
		// }
	},
	// mounted() {
	// 	console.log(this.itemId,12312312312);
	// 	if(this.itemId){
	// 		this.details()
	// 	}
	// },
	methods: {
		async details() {
			let res = await this.$api.collectionDetail({
				tid: this.itemId
			});
			if (res.status.code == 0) {
				this.info = res.result
				// this.info.transactionNotes = res.result.transactionNotes.split("\n\n")
				// console.log(this.info);
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		formatValue(value) {
			const regex = /\$\{(\w+)\}/g;
			const color = '#D8B662';
			return value.replace(regex, (match, p1) => `<font color="${color}">${this[p1]}</font>`)
				.replace('元', `<font color="${color}">元</font>`);
		},
		changePrice() {
			// this.antiShakeclick()
			if (this.resalePrice == "") {
				this.contentList = []
			} else {
				if (this.info.isAutoSeries == 0) {
					let servicePrice, royalty, buyPrice
					servicePrice = (this.resalePrice * 0.04).toFixed(2)
					royalty = (this.resalePrice * 0.025).toFixed(2)
					buyPrice = (this.resalePrice - servicePrice - royalty).toFixed(2)
					let list = [
						{
							"key": "服  务  费",
							"value": `成交价的4%, 为${servicePrice}元`
						},
						{
							"key": "版        税",
							"value": `成交价的2.5%, 为${royalty}元`
						},
						{
							"key": "到手可得",
							"value": `${buyPrice}元`
						}
					]
					this.contentList = list
				} else if (this.info.isAutoSeries == 1) {
					if (Number(this.resalePrice) > Number(this.info.price)) {
						if ((Number(this.info?.storeTimeDay) - Number(this.info.freeStoreTimeDay)) > 0) {
							let list = [
								{
									"key": "手  续  费",
									"value": `${((this.resalePrice - this.info.price) * this.info.handsFreeRaite).toFixed(2)}元`
								},
								{
									"key": "寄  存  费",
									"value": `逾期${this.info.storeTimeDay - this.info.freeStoreTimeDay}天，${((this.info.storeTimeDay - this.info.freeStoreTimeDay) * this.resalePrice * this.info.storeFreeRaite).toFixed(2)}元`
								}
							]
							this.contentList = list
						} else {
							let list = [
								{
									"key": "手  续  费",
									"value": `${((this.resalePrice - this.info.price) * this.info.handsFreeRaite).toFixed(2)}元`
								}
							]
							this.contentList = list

						}

					}

				}

			}
		},
		// antiShakeclick: antiShake._debounce(function() {
		// 	this.calculatePrice(this.resalePrice)
		// }, 200),
		async calculatePrice(price) {
			if (!price) return
			let res = await this.$api.calculatePrice({
				price: price,
				tid: this.itemId
			})
			if (res.result != "") {
				this.contentList = res.result.list
				this.tip = res.result.tip,
					this.topTip = res.result.topTip
				if (this.resalePrice === '') {
					uni.showToast({
						title: "金额不能为空",
						icon: "none",
						duration: 2000
					});
				} else if (this.resalePrice > 100000000 || this.resalePrice == 100000000) {
					uni.showToast({
						title: "作品必须在1亿以内",
						icon: "none",
						duration: 2000
					});
				} else if (this.tip != "") {
					this.$emit('update:isShowModalResale', false)
					this.isShowMsg = true
				} else {
					// this.$emit('closeResale', this.resalePrice)
					// this.contentList = ''
					this.$emit('update:isShowModalResale', false)
					this.isConfirmWorks = true
				}
			} else {
				uni.showToast({
					title: res.msg,
					icon: "none",
					duration: 3000
				});
			}
			// console.log(this.contentList)
		},
		j_isAgree() {
			this.isAgree = !this.isAgree
		},
		nav_link(title, index) {
			if (index == 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com.cn/link/#/pages/index/yanjieUserSaleServiceAgreement"
					}
				})
			}
		},
		// 一键寄售取消按钮
		resaleCancel() {
			this.resalePrice = ''
			this.contentList = ''
			this.$emit('closeResale', false)
		},
		// 一键寄售确定按钮
		async reasaleConfirm() {
			if (this.isAgree) {
				if (this.resalePrice === '') {
					uni.showToast({
						title: "金额不能为空",
						icon: "none",
						duration: 2000
					});
				} else if (this.resalePrice < 1) {
					uni.showToast({
						title: `金额不能低于1元`,
						icon: "none",
						duration: 2000
					});
				} else if (this.resalePrice > 100000000 || this.resalePrice == 100000000) {
					uni.showToast({
						title: "作品必须在1亿以内",
						icon: "none",
						duration: 2000
					});
				} else if (this.tip != "") {
					this.isShowMsg = true
				} else {
					this.isConfirmWorks = true
				}
			} else {
				uni.showToast({
					title: "请先勾选协议",
					icon: "none",
					duration: 2000
				});
			}
		},
		cancel() {
			this.isShowMsg = false
			this.$emit('update:isShowModalResale', true)
		},
		confirm() {
			this.isShowMsg = false
			this.$emit('closeResale', this.resalePrice)
		},
		confirmWorks() {
			this.isConfirmWorks = false
			this.$emit('closeResale', this.resalePrice)
			this.resalePrice = ''
			this.contentList = ''
		},
		cancelWorks() {
			this.isConfirmWorks = false
			this.$emit('update:isShowModalResale', true)
		}
	}

}
</script>

<style lang="scss" scoped>
::v-deep .u-model__content {
	background-color: #241E15 !important;
}

.model::v-deep {
	.u-model {
		width: 670rpx;
	}
}

// .u-modal {
//   border: 1px solid cyan;
//   background: var(--dialog-bg-color);
//   border-radius: 4rpx;
//   width: 100%;
//   height: 100%;
// }
.input-box {
	padding-bottom: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx 130rpx;

	text {
		font-size: 34rpx;
		color: #D8B662;
	}
}

.modal-resale-input::v-deep {
	width: 240rpx;
	margin: 0 auto;
	color: #FFF;
	// border: 2rpx solid #1FEDF0;
	border-radius: 0;

	.u-input__input {
		padding: 0 !important;
		text-align: center;
		font-weight: bold;
		color: #FFFFFF;
		font-weight: 600;
		font-size: 34rpx;
		padding: 0rpx 20rpx;
		background-color: #22180E;

	}

	.uni-input-placeholder {
		font-weight: 400 !important;
		// font-size: 50rpx !important;
	}
}

.title {
	text-align: center;
	font-weight: 500;
	font-size: 36rpx;
	padding: 40rpx 40rpx;
	color: #FFFFFF;
}

.xieyi {
	width: 100%;
	// margin: 40rpx 0rpx;
	padding-bottom: 40rpx;

	// border-bottom:1rpx solid #EDEDED;
	.p {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}

		.xieyi_msg {
			display: flex;

			.img {
				margin-top: 4rpx;
				display: inline-block;
			}

			font-size: 24rpx;
			color: #999999;
			line-height: 36rpx;

			.protocol {
				color: #D8B662;
				text-decoration: underline;
			}
		}
	}
}

.buttons {
	display: flex;
	justify-content: space-between;
	align-items: center;

	>view {
		width: 266rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		font-weight: bold;
	}

	.img_cancel {
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;
	}

	.img_reasale {
		font-weight: 400;
		font-size: 24rpx;
		color: #130E07;

	}
}

.modal-btn {
	margin-top: 48rpx;
	// padding: 10rpx 70rpx 50rpx 70rpx;
	// border: 1px solid cyan;

	.mb-cancel,
	.mb-confirm {
		text-align: center;
		width: 270rpx;
		height: 68rpx;
		line-height: 68rpx;
		border-radius: 2rpx;
		border: 2rpx solid #616161;
	}

	.mb-cancel {
		color: var(--active-color);
	}

	.mb-confirm {
		color: var(--new-consignment-color);
		background: rgb(192, 165, 114);
	}
}

.space-between {
	display: flex;
	justify-content: space-between;
	// align-items: center;
}

.resale_pop_msg {
	// padding: 50rpx 0 0;
	// min-height: 210rpx;
	margin: 40rpx 0rpx;

	.li {
		margin-bottom: 16rpx;
		font-size: 28rpx;
		font-weight: 400;
		line-height: 40rpx;
		color: var(--main-front-color);
		display: flex;
		justify-content: flex-start;

		.key {
			color: rgba(255, 255, 255, 0.5);
			width: 160rpx;
			text-align: right;
			// text-align-last: justify;
		}

		.value {
			font-weight: 400;
			font-size: 28rpx;
			// color: #D8B662;
			color: #FFF;
			margin-left: 30rpx;
			width: 460rpx;
		}

		&.active {
			.value {
				color: #1FEDF0;
				margin: 0rpx;
				font-size: 28rpx;
			}
		}
	}

}

.tip {
	background-color: #2B2B2B;
	font-size: 24rpx;
	height: 88rpx;
	color: #999999;
	display: flex;
	align-items: center;
	padding: 0rpx 40rpx;
}

.new-modal-content {
	padding: 35rpx 40rpx;

	.success_img {
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 60rpx;
			height: 60rpx;
		}
	}

	.modal-content {
		padding: 35rpx 0rpx;
		font-size: 28rpx;
		text-align: center;
		font-weight: 400;
		color: #FFFFFF;
	}

	.showModal-btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 30rpx;

		>view {
			width: 266rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			color: #141414;
		}

		// .img_cancel {
		// 	border: 1px solid #141414;
		// }

		// .img_reasale {
		// 	background-color: #D8B662;
		// 	color: #141414;
		// }
		.img_reasale {
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;
		}

		.img_cancel {
			font-weight: 400;
			font-size: 24rpx;
			color: #130E07;
		}
	}
}

.title_bg {
	font-weight: bold;
	color: #FFFFFF;
	font-size: 34rpx;
	position: relative;
	text-align: center;
	margin: 0 auto;
	margin-bottom: 10rpx;

	image {
		width: 292rpx;
	}
}
</style>
