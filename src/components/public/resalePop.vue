<template>
	<view>
		<u-modal class="model" width="670" border-radius="30" v-model="show" :show-title="false" :show-confirm-button="false"
			>
			<view style="padding:40rpx 40rpx 0rpx 40rpx;">
				<view class="title_bg">
					<view class="icon"></view>
					寄售金额
				</view>
				<view class="input-box">
					<view class="label">寄售金额￥</view>
					<u-input class="modal-resale-input" v-model="resalePrice" placeholder="" type="number" border
						border-color="transparent" :trim="true" :adjust-position="true" :show-confirmbar="true"
						:custom-style="{'padding-left': '25rpx'}" @input="changePrice" :clearable="false" />

				</view>
				<view class="resale_pop_msg">
					<view class="li" v-for="(item,index) in contentList" :key="index">
						<view class="key">
							{{item.key}}
						</view>
						<view class="value">{{item.value}}</view>
					</view>
				</view>
			</view>
			<view style="padding:0rpx 40rpx 40rpx 40rpx;">
				<view class="xieyi">
					<view class="p">
						<view class="xieyi_msg">
							<image class="img" @click="j_isAgree" v-if="!isAgree"
								src="../../../src/static/login/jx.png"
								mode="">
							</image>
							<image class="img" @click="j_isAgree" v-else
								src="../../../src/static/login/jxs2x.png"
								mode="">
							</image>
							<text>
								<text @click="j_isAgree">已阅读并同意上述价格规则及</text>
								<text class="protocol" @click="nav_link('平台用户售卖服务协议',1)">《平台用户售卖服务协议》</text>
							</text>
						</view>
					</view>
				</view>
				<view class="buttons">
					<view class="img_cancel" @click="resaleCancel()">
						返回
					</view>
					<view class="img_reasale" @click="reasaleConfirm()">
						确认
					</view>
				</view>
			</view>
		</u-modal>
		<!--确认 一键销毁 -->
		<u-modal v-model="isShowMsg" border-radius="30" :content-style="bgObject" :show-title="false"
			:title-style="titleObject" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					恭喜您
				</view>
				<view class="modal-content">
					{{tip}}
				</view>
				<view class="showModal-btn">
					<view class="img_reasale" @click="confirm()">仍要寄售</view>
					<view class="img_cancel" @click="cancel()">修改价格</view>
				</view>
			</view>
		</u-modal>
		<u-modal v-model="isConfirmWorks"  :content-style="bgObject"
			:title-style="titleObject" border-radius="30" :show-title="false" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					温馨提示
				</view>
				<view class="modal-content">
					是否以{{resalePrice}}元寄售此作品？
				</view>
				<view class="showModal-btn">
					<view class="img_reasale" @click="confirmWorks()">仍要寄售</view>
					<view class="img_cancel" @click="cancelWorks()">修改价格</view>
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import antiShake from "@/common/public.js"
	export default {
		name: 'resalePop',
		model: {
			prop: 'isShowModalResale',
			event: 'closeResale',
		},
		data() {
			return {
				resalePrice: '', // 寄售价格
				isAgree: '', // 是否同意
				contentList: [],
				tip: "",
				titleObject: {
				},
				bgObject: {
				},
				isShowMsg: false,
				topTip: "",
				isConfirmWorks:false,
				isSuccess:false
			}
		},
		computed: {
			show: {
				get() {
					return this.isShowModalResale;
				},
				set(val) {},
			}
		},
		watch: {
			isShowModalResale(val) {
				this.show = val;
			}
		},
		props: {
			isShowModalResale: {
				type: Boolean,
				default () {
					return false
				}
			},
			itemId: {
				type: [String, Number]
			}
			// contentList: {
			//   type: Array,
			//   default() {
			//     return []
			//   }
			// }
		},
		methods: {
			changePrice() {
				// this.antiShakeclick()
				if (this.resalePrice == "") {
					this.contentList = []
				}else{
					let servicePrice,royalty,buyPrice
					servicePrice=(this.resalePrice*0.04).toFixed(2)
					royalty=(this.resalePrice*0.025).toFixed(2)
					buyPrice=(this.resalePrice-servicePrice-royalty).toFixed(2)
					let list=[
					  {
					    "key": "服  务  费",
					    "value": `成交价的4%, 为${servicePrice}元`
					  },
					  {
					    "key": "版        税",
					    "value": `成交价的2.5%, 为${royalty}元`
					  },
					  {
					    "key": "购入价格",
					    "value": `预计寄售可得${buyPrice}元`
					  }
					]
					this.contentList = list
				}
			},
			// antiShakeclick: antiShake._debounce(function() {
			// 	this.calculatePrice(this.resalePrice)
			// }, 200),
			async calculatePrice(price) {
				if (!price) return
				let res = await this.$api.calculatePrice({
					price: price,
					tid: this.itemId
				})
				if (res.result != "") {
					this.contentList = res.result.list
					this.tip = res.result.tip,
					this.topTip = res.result.topTip
					if (this.resalePrice === '') {
						uni.showToast({
							title: "金额不能为空",
							icon: "none",
							duration: 2000
						});
					} else if (this.resalePrice > 100000000 || this.resalePrice == 100000000) {
						uni.showToast({
							title: "作品必须在1亿以内",
							icon: "none",
							duration: 2000
						});
					} else if (this.tip != "") {
						this.$emit('update:isShowModalResale', false)
						this.isShowMsg = true
					} else {
						// this.$emit('closeResale', this.resalePrice)
						// this.contentList = ''
						this.$emit('update:isShowModalResale', false)
						this.isConfirmWorks=true
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					});
				}
				// console.log(this.contentList)
			},
			j_isAgree() {
				this.isAgree = !this.isAgree
			},
			nav_link(title, index) {
				if (index == 1) {
					this.$Router.push({
						name: "generalAgreement",
						params: {
							title: title,
							link: "https://www.nftcn.com.cn/link/#/pages/index/saleAgreement"
						}
					})
				}
			},
			// 一键寄售取消按钮
			resaleCancel() {
				this.resalePrice = ''
				this.contentList = ''
				this.$emit('closeResale', false)
			},
			// 一键寄售确定按钮
			async reasaleConfirm() {
				if (this.isAgree) {
					if (this.resalePrice === '') {
						uni.showToast({
							title: "金额不能为空",
							icon: "none",
							duration: 2000
						});
					} else if (this.resalePrice < 1) {
						uni.showToast({
							title: `金额不能低于1元`,
							icon: "none",
							duration: 2000
						});
					}else if (this.resalePrice > 100000000 || this.resalePrice == 100000000) {
						uni.showToast({
							title: "作品必须在1亿以内",
							icon: "none",
							duration: 2000
						});
					} else if (this.tip != "") {
						this.isShowMsg = true
					} else {
						this.isConfirmWorks = true
					}
				} else {
					uni.showToast({
						title: "请先勾选协议",
						icon: "none",
						duration: 2000
					});
				}
			},
			cancel() {
				this.isShowMsg = false
				this.$emit('update:isShowModalResale', true)
			},
			confirm() {
				this.isShowMsg = false
				this.$emit('closeResale', this.resalePrice)
			},
			confirmWorks(){
				this.isConfirmWorks = false
				this.$emit('closeResale', this.resalePrice)
				this.resalePrice = ''
				this.contentList = ''
			},
			cancelWorks(){
				this.isConfirmWorks = false
				this.$emit('update:isShowModalResale', true)
			}
		}

	}
</script>

<style lang="scss" scoped>
	.model::v-deep {
		.u-model {
			width: 670rpx;
			z-index:999;
		}
	}

	// .u-modal {
	//   border: 1px solid cyan;
	//   background: var(--dialog-bg-color);
	//   border-radius: 4rpx;
	//   width: 100%;
	//   height: 100%;
	// }
	.input-box {
		padding-bottom: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding:40rpx 100rpx 20rpx 100rpx;
		.label {
			color: #fff;
			font-size: 28rpx;
		}
				
		.input {
			width: 240rpx;
		}
	}

	.modal-resale-input::v-deep {
		margin: 0 auto;
		color: #FFF;
		// border: 2rpx solid #1FEDF0;
		width: 240rpx;
		border-radius: 0;
		.u-input__input {
			color: #63EAEE !important;
			font-weight: 600;
			font-size: 34rpx;
			padding: 0rpx 20rpx;
			background-color: #25232D;
			border-radius: 14rpx;
			width: 240rpx;
		}
		
		.uni-input-placeholder {
			font-weight: 400 !important;
			font-size: 32rpx !important;
		}
	}

	.title {
		text-align: center;
		font-weight: 500;
		font-size: 36rpx;
		padding: 40rpx 40rpx;
		color: #FFFFFF;
	}

	.xieyi {
		width: 100%;
		margin: 40rpx 0rpx;
		padding-bottom:40rpx;
		border-bottom:1px solid var(--default-color3);
		.p {
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 30rpx;
				height: 30rpx;
				margin-right: 10rpx;
			}

			.xieyi_msg {
				display: flex;

				.img {
					margin-top: 4rpx;
					display: inline-block;
				}

				font-size: 24rpx;
				color: #999999;
				line-height: 36rpx;

				.protocol {
					color: #63EAEE;
					text-decoration: underline;
				}
			}
		}
	}
	.buttons {
		display: flex;
		justify-content: space-between;
		align-items: center;
	
		>view {
			width: 266rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			font-weight: bold;
			border-radius: 14rpx;
		}
	
		.img_cancel {
			border: 1px solid #fff;
			color:var(--default-color3);
		}
	
		.img_reasale {
			background:var(--primary-button-color);
		}
	}
	.modal-btn {
		margin-top: 48rpx;
		// padding: 10rpx 70rpx 50rpx 70rpx;
		// border: 1px solid cyan;

		.mb-cancel,
		.mb-confirm {
			text-align: center;
			width: 270rpx;
			height: 68rpx;
			line-height: 68rpx;
			border-radius: 2rpx;
			border: 2rpx solid #616161;
		}

		.mb-cancel {
			color: var(--active-color);
		}

		.mb-confirm {
			color: var(--new-consignment-color);
			background: var(--primary-button-color);
		}
	}

	.space-between {
		display: flex;
		justify-content: space-between;
		// align-items: center;
	}

	.resale_pop_msg {
		// padding: 50rpx 0 0;
		// min-height: 210rpx;
		margin: 40rpx 0rpx;

		.li {
			margin-bottom: 16rpx;
			font-size: 28rpx;
			font-weight: 400;
			line-height: 40rpx;
			color: var(--main-front-color);
			display: flex;
			justify-content: flex-start;

			.key {
				color: #A6A6A6;
				width: 160rpx;
				text-align: right;
				// text-align-last: justify;
			}

			.value {
				color: var(--default-color1);
				margin-left: 30rpx;
				width: 460rpx;
			}

			&.active {
				.value {
					color: #1FEDF0;
					margin: 0rpx;
					font-size: 28rpx;
				}
			}
		}

	}

	.tip {
		background-color: #2B2B2B;
		font-size: 24rpx;
		height: 88rpx;
		color: #999999;
		display: flex;
		align-items: center;
		padding: 0rpx 40rpx;
	}

	.new-modal-content {
		padding:35rpx 40rpx;
		.success_img{
			display: flex;
			justify-content: center;
			align-items: center;
			image{
				width:160rpx;
				height:160rpx;
			}
		}
		.modal-content{
			padding:35rpx 0rpx;
			border-bottom:1rpx solid #EDEDED;
			font-size:28rpx;
			color:#fff;
			text-align: center;
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			>view {
				width: 226rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				font-weight: bold;
				border-radius: 14rpx;
				color:#141414;
			}
	
			.img_cancel {
				border: 1px solid #FFF;
				color:var(--default-color3);
			}
			.img_reasale {
				color:#141414;
				background:var(--primary-button-color);
			}
		}
	}
	.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: #fff;
	
		.icon {
			position: absolute;
			left: 0rpx;
			top: 20rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}
</style>
