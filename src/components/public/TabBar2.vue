<template>
	<view class="tabbar_view_bar">
		<view class="flex">
			<view class="tabbar_li" :class="{'cen':item.isCenter}" v-for="(item, index) in tabbar" @click="check(index)">
				<view class="tuchu" v-if="item.isCenter">
					<image :src="item.selectedIconPath" mode="widthFix"></image>
				</view>
				<view class="def" v-else>
					<view class="active" v-show="tabBarCheck == index">
						<view class="img">
							<image :src="item.selectedIconPath" mode="widthFix"></image>
						</view>
						<view class="text">
							{{item.text}}
						</view>
					</view>
					<view class="default" v-show="tabBarCheck != index">
						<view class="img">
							<image :src="item.iconPath" mode="widthFix"></image>
						</view>
						<view class="text">
							{{item.text}}
						</view>
					</view>
				</view>
				<view class="bar" v-show="tabBarCheck == index"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "tabbar",
		props: {
			// 初始激活项索引
			initialActiveIndex: {
				type: Number,
				default: 0
			},
			outside: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				tabbar: [{
						"iconPath": "/static/tabBar2/index.png",
						"selectedIconPath": "/static/tabBar2/index_c.png",
						"pagePath": "/pagesA/project/store/index/index",
						"text": "首页"
					},
					{
						"pagePath": "/pagesA/project/store/shop/index",
						"iconPath": "/static/tabBar2/mall.png",
						"selectedIconPath": "/static/tabBar2/mall_c.png",
						"text": "商城"
					},
					{
						"pagePath": "/pagesA/project/store/user/index",
						"iconPath": "/static/tabBar2/user.png",
						"selectedIconPath": "/static/tabBar2/user_c.png",
						"text": "我的"
					}
				],
				tabBarCheck: this.initialActiveIndex,
			};
		},
		onShow() {
			this.tabBarCheck = this.initialActiveIndex
		},
		mounted() {
		},
		methods: {
			check(index) {
				this.$forceUpdate()
				console.error(this.tabBarCheck)
				console.error(index)
				if (this.tabbar[index].pagePath.includes('/pagesA/')) {
					// 你可以在这里执行其他操作
					uni.navigateTo({
						url: this.tabbar[index].pagePath
					});
				} else {
					uni.switchTab({
						url: this.tabbar[index].pagePath
					});
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.tabbar_view_bar{
		padding: 0rpx 100rpx 0rpx 100rpx;
		position: fixed;
		left: 0;
		bottom: 0rpx;
		width: 100%;
		height: 180rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		z-index:20;
		background: linear-gradient(217.32deg, #FCFCFD -0.01%, rgba(252, 252, 253, 0.6) 100%);
		backdrop-filter: blur(64rpx);
		.flex{
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			height: 180rpx;
		}
		.tabbar_li {
			width:80rpx;
			height:100%;
			position: relative;
			line-height: 180rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			&.cen{
				width: 96rpx;
			}
			.bar{
				background:#6B63E5;
				width:80rpx;
				height:8rpx;
				border-top-left-radius:8rpx;
				border-top-right-radius:8rpx;
				position: absolute;
				left:0rpx;
				bottom:0rpx;
			}
			.tuchu{
				padding-bottom:20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				height:100%;
				width: 96rpx;
				image {
					width: 96rpx;
				}
			}
			.default {
				width:80rpx;
				.img{
					display: flex;
					justify-content: center;
					image {
						width: 44rpx;
					}
				}
				.text {
					color: #121212;
					font-size: 20rpx;
					line-height:36rpx;
					text-align: center;
				}

				
			}

			.active {
				width:80rpx;
				.text {
					color: #6B63E5;
					font-size: 20rpx;
					line-height:36rpx;
					text-align: center;
				}
				.img{
					display: flex;
					justify-content: center;
					image {
						width: 44rpx;
					}
				}
			}
		}

		.index_check {
			width: 105rpx;
			height: 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			background-color: #666472;
			border-radius: 30rpx;
			position: absolute;
			left: -28rpx;
			color: #fff;

			image {
				width: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
</style>