<template>
    <div class="trend-chart" :style="{ width: `${safeWidth}px`, height: `${safeHeight}px` }">
      <canvas ref="chartCanvas" @touchstart="handleTouchStart" @touchmove="handleTouchMove"></canvas>
    </div>
  </template>
  
  <script>
  export default {
    name: 'TrendChart',
    props: {
      data: {
        type: Array,
        required: true,
        default: () => []
      },
      lineColor: {
        type: String,
        default: '#00C853'
      },
      shadowColor: {
        type: String,
        default: 'rgba(0, 200, 83, 0.2)'
      },
      height: {
        type: Number,
        default: 50
      },
      width: {
        type: Number,
        default: 100
      }
    },
    data() {
      return {
        offsetX: 0,
        touchStartX: 0,
        devicePixelRatio: window.devicePixelRatio || 1
      };
    },
    computed: {
      safeWidth() {
        return typeof this.width === 'number' && !isNaN(this.width) ? this.width : 100;
      },
      safeHeight() {
        return typeof this.height === 'number' && !isNaN(this.height) ? this.height : 50;
      },
      safeLineColor() {
        return typeof this.lineColor === 'string' ? this.lineColor : '#00C853';
      },
      safeShadowColor() {
        return typeof this.shadowColor === 'string' ? this.shadowColor : 'rgba(0, 200, 83, 0.2)';
      }
    },
    mounted() {
      this.$nextTick(() => {
        if (this.$refs.chartCanvas) {
          this.initCanvas();
          this.drawChart();
          window.addEventListener('resize', this.handleResize);
        } else {
          console.error('Canvas element not found');
        }
      });
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    watch: {
      data() {
        this.$nextTick(() => {
          if (this.$refs.chartCanvas) {
            this.drawChart();
          }
        });
      },
      width() {
        this.$nextTick(() => {
          if (this.$refs.chartCanvas) {
            this.initCanvas();
            this.drawChart();
          }
        });
      },
      height() {
        this.$nextTick(() => {
          if (this.$refs.chartCanvas) {
            this.initCanvas();
            this.drawChart();
          }
        });
      }
    },
    methods: {
      initCanvas() {
        const canvas = this.$refs.chartCanvas;
        if (!canvas || typeof canvas.getContext !== 'function') {
          console.error('Canvas is not available or getContext is not a function');
          return;
        }
        const ctx = canvas.getContext('2d');
        canvas.width = this.safeWidth * this.devicePixelRatio;
        canvas.height = this.safeHeight * this.devicePixelRatio;
        canvas.style.width = `${this.safeWidth}px`;
        canvas.style.height = `${this.safeHeight}px`;
        ctx.scale(this.devicePixelRatio, this.devicePixelRatio);
      },
      drawChart() {
        const canvas = this.$refs.chartCanvas;
        if (!canvas || typeof canvas.getContext !== 'function') return;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, this.safeWidth, this.safeHeight);
        if (!this.data || this.data.length < 2) return;
        const maxValue = Math.max(...this.data);
        const minValue = Math.min(...this.data);
        const range = maxValue - minValue || 1;
        const stepX = this.safeWidth / (this.data.length - 1);
        const points = [];
        for (let i = 0; i < this.data.length; i++) {
          const x = i * stepX + this.offsetX;
          const y = this.safeHeight - ((this.data[i] - minValue) / range) * this.safeHeight;
          points.push({ x, y });
        }
        ctx.beginPath();
        ctx.moveTo(points[0].x, this.safeHeight);
        for (let i = 0; i < points.length; i++) {
          if (points[i].x >= 0 && points[i].x <= this.safeWidth) {
            ctx.lineTo(points[i].x, points[i].y);
          }
        }
        ctx.lineTo(points[points.length - 1].x, this.safeHeight);
        ctx.closePath();
        ctx.fillStyle = this.safeShadowColor;
        ctx.fill();
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
          if (points[i].x >= 0 && points[i].x <= this.safeWidth) {
            ctx.lineTo(points[i].x, points[i].y);
          }
        }
        ctx.strokeStyle = this.safeLineColor;
        ctx.lineWidth = 2;
        ctx.stroke();
      },
      handleTouchStart(event) {
        const touch = event.touches[0];
        this.touchStartX = touch.clientX;
      },
      handleTouchMove(event) {
        const touch = event.touches[0];
        const deltaX = touch.clientX - this.touchStartX;
        this.offsetX += deltaX;
        const maxOffset = 0;
        const minOffset = -(this.data.length - 1) * (this.safeWidth / (this.data.length - 1)) + this.safeWidth;
        this.offsetX = Math.max(minOffset, Math.min(maxOffset, this.offsetX));
        this.touchStartX = touch.clientX;
        this.drawChart();
      },
      handleResize() {
        this.$nextTick(() => {
          if (this.$refs.chartCanvas) {
            this.initCanvas();
            this.drawChart();
          }
        });
      }
    }
  };
  </script>
  
  <style scoped>
  .trend-chart {
    position: relative;
    overflow: hidden;
  }
  canvas {
    display: block;
    width: 100%;
    height: 100%;
  }
  </style>