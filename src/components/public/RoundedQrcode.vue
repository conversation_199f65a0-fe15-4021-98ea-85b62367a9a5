<template>
    <view class="qrcode-wrapper">
      <canvas :canvas-id="canvasId" :style="canvasStyle"></canvas>
    </view>
  </template>
  
  <script>
  import QRCode from 'qrcode'
  
  export default {
    name: 'RoundedQrcode',
    props: {
      text: {
        type: String,
        default: 'https://example.com'
      },
      size: {
        type: Number,
        default: 200
      },
      borderRadius: {
        type: Number,
        default: 24 // 圆角半径
      },
      canvasId: {
        type: String,
        default: 'qrcodeCanvas'
      }
    },
    computed: {
      canvasStyle() {
        return {
          width: this.size + 'px',
          height: this.size + 'px',
          borderRadius: this.borderRadius + 'px',
          overflow: 'hidden',
          backgroundColor: '#fff'
        }
      }
    },
    mounted() {
      this.renderQr()
    },
    methods: {
      async renderQr() {
        if (!this.text) {
          console.warn('QRCode text is empty')
          return
        }
  
        try {
          const dataUrl = await QRCode.toDataURL(this.text, {
            errorCorrectionLevel: 'H',
            margin: 1,
            scale: 10,
            color: {
              dark: '#000000',
              light: '#ffffff'
            }
          })
  
          const imgInfo = await this.getImageInfo(dataUrl)
          const ctx = uni.createCanvasContext(this.canvasId, this)
  
          ctx.save()
          this.drawRoundedRect(ctx, 0, 0, this.size, this.size, this.borderRadius)
          ctx.clip()
          ctx.drawImage(imgInfo.path, 0, 0, this.size, this.size)
          ctx.restore()
          ctx.draw()
        } catch (err) {
          console.error('二维码绘制失败:', err)
        }
      },
  
      getImageInfo(dataUrl) {
        return new Promise((resolve, reject) => {
          // H5 直接返回 base64
          // App 需要转换成本地路径
          // #ifdef H5
          resolve({ path: dataUrl })
          // #endif
  
          // #ifndef H5
          uni.getImageInfo({
            src: dataUrl,
            success: resolve,
            fail: reject
          })
          // #endif
        })
      },
  
      drawRoundedRect(ctx, x, y, w, h, r) {
        ctx.beginPath()
        ctx.moveTo(x + r, y)
        ctx.lineTo(x + w - r, y)
        ctx.quadraticCurveTo(x + w, y, x + w, y + r)
        ctx.lineTo(x + w, y + h - r)
        ctx.quadraticCurveTo(x + w, y + h, x + w - r, y + h)
        ctx.lineTo(x + r, y + h)
        ctx.quadraticCurveTo(x, y + h, x, y + h - r)
        ctx.lineTo(x, y + r)
        ctx.quadraticCurveTo(x, y, x + r, y)
        ctx.closePath()
      }
    }
  }
  </script>
  
  <style scoped>
  .qrcode-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  </style>
  