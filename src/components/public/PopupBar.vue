<template>
	<u-popup v-model="show" mode="center" border-radius="30" width="582rpx" class="purchase_model " :mask-close-able="false">
		<view class="purchase_body">
			<view class="title">{{ title }}</view>
			<view class="text">{{ content }}</view>
			<view class="btn" :class="{'space-between':between}">
				<button-bar class="btn2 but_view" @click='cancel' v-if="isCancel" :text="cancelText"></button-bar>
				<button-bar class="confirm but_view" @click='confirm' v-if="isConfirm" :text="confirmText"></button-bar>
			</view>
		</view>
	</u-popup>
</template>

<script>
	import ButtonBar from "@/components/public/ButtonBar";
	export default {
		name: "popupBar",
		components: {
			ButtonBar,
		},
		model: {
			prop: "showPopup",
			event: "input"
		},
		computed: {
			show: {
				get() {
					return this.showPopup;
				},
				set(val) {
					console.log(val)
					this.$emit('input', val)
				},
			},
		},
		props: {
			showPopup: {
				type: Boolean,
				default: false
			},
			title: {
				type: String,
				default: ''
			},
			content: {
				type: String,
				default: '为了保障您的创作权益，以及避免交易版权纠纷，请先进行实名认证。'
			},
			confirmText: {
				type: String,
				default: '确认'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			isConfirm: {
				type: Boolean,
				default: true
			},
			isCancel: {
				type: Boolean,
				default: true
			},
			between: {
				type: Boolean,
				default: false
			},
		},
		watch: {
			showPopup(val) {
				this.show = val;
			},
		},
		methods: {
			confirm() {
				this.$emit('update:showPopup', false);
				this.$emit('confirm');
			},
			cancel() {
				this.$emit('cancel');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.purchase_model {
		.purchase_body {
			letter-spacing: 2rpx;
			padding: 40rpx;
			background-color: #241E15;
			color: var(--main-front-color);

			.title {
				font-size: 32rpx;
				line-height: 60rpx;
				font-weight: 600;
				text-align: center;
				margin-bottom: 40rpx;
			}

			.text {
				font-size: 28rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				line-height: 44rpx;
				margin-bottom: 60rpx;
				text-align: center;
			}

			.btn {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.but_view{
					width:40%;
					border-radius:10rpx;
				}
				.btn2 {
					width: 40%;
					height: 80rpx;
					background: var(--dialog-bg-color);
					font-size: 28rpx;
					font-weight: 400;
					color: var(--main-front-color);
					border: #fff solid 2rpx;
					
				}
			}

			.space-between {
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-direction: row-reverse;

				.u-btn {
					width: 45%;
				}

				.btn2 {
					margin-top: 0;
				}
			}
		}

	}
</style>
