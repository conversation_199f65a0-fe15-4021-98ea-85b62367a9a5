<template>
  <view>
    <view class="tabbar_view " :class="{ 'grayscale': initialActiveIndex == 0 }" v-if="bv_platform">
      <view class="left_view" :class="{ 'oldPadd': !showActive }">
        <!-- <view class="index_check" @click="checkPlatform" v-if="showActive">
          <image src="https://cdn-lingjing.nftcn.com.cn/image/20241017/96a7afc054e88e67c98596ca5d779978_24x24.png"
            mode="widthFix"></image>
          {{ bv_platform ? '藏品' : '衍生' }}
        </view> -->
        <view class="tabbar_li" v-for="(item, index) in tabbar" @click="check(index)">
          <view class="active" v-show="tabBarCheck == index">
            <image :src="item.selectedIconPath" mode="heightFix"></image>
          </view>
          <view class="default" v-show="tabBarCheck != index">
            <image :src="item.iconPath" mode="widthFix"></image>
          </view>
        </view>
      </view>
      <!-- 右边我的藏品 -->
      <view class="right_view">
        <view class="active" v-show="tabBarCheck == 9" @click="check(9)">
          <image src="/static/tabBar/property_c.png" mode="widthFix"></image>
        </view>
        <view class="default" v-show="tabBarCheck != 9" @click="check(9)">
          <image src="/static/tabBar/property.png" mode="widthFix"></image>
        </view>
      </view>
    </view>

    <view class="tabbar_view2" :class="{ 'grayscale': initialActiveIndex == 0 }" v-else>
      <!-- 遮罩层 -->


      <transition name="fade">
        <view v-if="helpoptionsShow" class="mask" @click="closeOverlay"></view>
      </transition>


      <view class="left_view2" :class="{ 'oldPadd': !showActive }">
        <view class="index_check2" @click="checkPlatform" v-if="showActive">
          <image src="https://cdn-lingjing.nftcn.com.cn/image/20241218/057d8982808b03f2632aa0a6a1674536_86x86.png"
            mode="widthFix"></image>
        </view>

        <transition name="popup-transition">
          <view class="popup" v-if="helpoptionsShow">
            <view class="popup-item" :class="{ 'activecheck': checkindex == ('E-' + item.title + '-USDT') }"
              v-for="(item, index) in indexList" @click="changeSymbol(item)" :key="index">
              <image :src="item.cover" class="icon" />
              <text>{{ item.title }}</text>
              <image class="checked" v-if="checkindex == ('E-' + item.title + '-USDT')"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241219/a36e47cba961e8f01d150ef192f639e3_56x56.png" />
            </view>
            <!--  <view class="mg" @click="mgshow = !mgshow">
              <text>模拟美股</text>
              <image :class="{ 'rotate-anim': mgshow }" class="arrow"
                :style="{ transform: mgshow ? 'rotate(-90deg)' : 'rotate(360deg)' }"
                src="https://cdn-lingjing.nftcn.com.cn/image/20241219/6d28f66a4155f286b9484d1294d5e366_22x42.png" />
            </view>
            <view class="popup-item" v-for="(item, index) in usList" :key="index" v-show="mgshow" @click="nav_mgus">
              <image :src="item.cover" class="icon" />
              <text>{{ item.title }}</text>
            </view>
          -->
            <view style="position: absolute;bottom: 0;height: 20rpx;width: 100%;"></view>
          </view>
        </transition>

        <view class="tabbar_li2" v-for="(item, index) in tabbar" :style="{ zIndex: index === 1 ? 9 : 0 }"
          @click="check(index)">
          <view class="active2" v-show="tabBarCheck == index">
            <image :src="item.selectedIconPath" mode="heightFix"></image>
          </view>
          <view class="default2" v-show="tabBarCheck != index">
            <image :src="item.iconPath" mode="widthFix"></image>
          </view>
        </view>

      </view>



      <view class="right_view2">
        <view class="active2" v-show="tabBarCheck == 2" @click="check('wallet')">
          <image src="https://cdn-lingjing.nftcn.com.cn/image/20241218/6a38060845d2524c0ae0488f429be07d_230x92.png"
            mode="widthFix"></image>
        </view>
        <view class="default2" v-show="tabBarCheck != 2" @click="check('wallet')">
          <image src="https://cdn-lingjing.nftcn.com.cn/image/20241218/7241366fbe35e63446f0083921fbcf19_86x88.png"
            mode="widthFix"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import store from '@/store'
export default {
  name: "tabbar",
  props: {
    // 初始激活项索引
    initialActiveIndex: {
      type: Number,
      default: 0
    },
    outside: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkindex: '',
      mgshow: false,
      indexLists: [], // 容器
      usLists: [],
      indexList: [],
      usList: [],
      tabbar: [],
      finallList: [],
      helpoptionsShow: false,
      tabBarCheck: this.initialActiveIndex,
      bv_platform: true,
      showActive: false
    };
  },
  onShow() {
    this.tabBarCheck = this.initialActiveIndex
    console.error(this.tabBarCheck);
  },
  mounted() {
    console.log(123);
    
    this.get_config();
    this.get_increaseList()
    this.fetchIndex()
    this.checkindex = uni.getStorageSync('currentIndictor')
  },
  methods: {
    nav_mgus(e) {
      this.$Router.pushTab({
        name: "mgUs"
      })
    },
    changeSymbol(e) {
      if (e.type == 'bit') {
        store.commit("changeOPtionSymbol", 'aaveusdt')
        uni.setStorageSync('currentIndictor', "")
        uni.setStorageSync('IndictorInfo', '')
        this.$emit('changeSymbol', "")
        this.helpoptionsShow = false
        return
      }
      store.commit("changeOPtionSymbol", (e.title + 'USDT').toLocaleLowerCase())
      let currentIndicator = 'E-' + e.title + '-USDT'
      this.helpoptionsShow = false
      this.$emit('changeSymbol', currentIndicator)
      uni.setStorageSync('currentIndictor', currentIndicator)
      // uni.setStorageSync('IndictorInfo', e)
    },
    combine() {
      console.log(this.usList, this.indexList, '数组门');

      // 提取第一个数组中的titleCn和cover，并添加type:'mg'
      this.usList = this.usLists.map(item => ({
        title: item.titleCn,
        cover: item.cover,
        type: 'mg'
      }));

      // 提取第二个数组中的name和icon，并添加type:'coin'
      this.indexList = this.indexLists.map(item => ({
        title: item.name,
        cover: item.icon,
        type: 'coin'
      }));
      this.indexList.unshift({
        title: 'BIT指数',
        cover: "https://cdn-lingjing.nftcn.com.cn/image/20240726/1600e1c1b871583a15880cb4f6abcde4_72x72.png",
        type: 'bit'
      })


      // 合并两个数组
      // const finalArray = [...firstArrayMapped, ...secondArrayMapped];
      // this.finallList = finalArray
      // console.log(finalArray, 'hebing的');
    },
    async get_increaseList() {
      let res = await this.$api.scaleIncreaseList({
        pageNum: 1,
        pageSize: 20
      });
      if (res.status.code == 0) {
        this.usLists = res.result.list
        console.log(this.usList, '美股');

      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
    async fetchIndex() {
      let res = await this.$api.indexs()
      if (res.status.code === 0) {
        this.indexLists = res.result.list
      }
    },
    closeOverlay() {
      this.indexList = []
      this.usList = []
      this.helpoptionsShow = false;
      // this.tabBarCheck = 0
    },
    // active 跳转
    activeGo(index) {
      if (index == 4) {
        this.isShow = !this.isShow

      } else {
        this.isShow = false
      }
    },
    check(index) {
      console.log(this.tabBarCheck, index, 12312312321);

      if (this.tabBarCheck == 1 && index == 1) {

        this.helpoptionsShow = true
        this.tabBarCheck = 1
        this.get_increaseList()
        this.fetchIndex()
        this.combine()
        this.checkindex = uni.getStorageSync('currentIndictor')

      }
      this.$forceUpdate()
      console.error(this.tabBarCheck)
      console.error(index)
      if (index == 3) {
        let token = uni.getStorageSync('token')

        if (!token) {
          this.$Router.push({
            name: 'mainLogin',
          })
          return
        }
        // #ifdef H5
        this.appUrl = getApp().globalData.Imurl
        window.location.href = `${this.appUrl}im/#/pages/index/index?token=${token}`
        // #endif

        // #ifdef APP
        this.appUrl = getApp().globalData.Imurl
        let link = `${this.appUrl}im/#/pages/index/index`
        console.log(link)
        this.$Router.push({
          name: 'webView',
          params: {
            url: link,
            token: token
          }
        })
        // #endif
      }
      if (index == 'wallet') {
        this.$Router.pushTab({
          name: 'personalYs',
        })
      }
      if (index == 9) {
        uni.switchTab({
          url: '/pages/project/personal/index'
        });
      } else {
        if (this.tabBarCheck == index) {
          return
        } else {
          if (this.tabbar[index].pagePath == '/pages/project/im/index') {
            return;
          }
          if (this.tabbar[index].pagePath.includes('/pagesA/')) {
            // 你可以在这里执行其他操作
            uni.navigateTo({
              url: this.tabbar[index].pagePath
            });
          } else {
            console.log(this.tabBarCheck, index, '12312')
            if (this.tabbar[index].pagePath == '/pages/project/actives/contract-BITindex') {
              this.$Router.pushTab({
                name: 'contract-BITindex',
                params: {
                  load: true
                }
              })
            } else {
              uni.switchTab({
                url: this.tabbar[index].pagePath
              });
            }

          }
        }

      }
    },
    //读取配置
    get_config() {
      let currentTabbar = uni.getStorageSync('currentTabbar')
      if (currentTabbar == 'bv') {
        this.bv_platform = true
        this.tabbar = uni.getStorageSync('bv_tabbarList')
        console.log(this.tabbar)
      } else {
        this.bv_platform = false
        this.tabbar = uni.getStorageSync('pay_tabbarList')
      }
      // #ifdef APP
      if (uni.getSystemInfoSync().platform == 'ios') {
        this.get_version()
      } else {
        this.showActive = true
      }
      // #endif
      // #ifdef H5
      this.showActive = true
      // #endif
      // this.tabbar = val
    },
    checkPlatform() {
      this.bv_platform = !this.bv_platform
      if (this.bv_platform) {
        uni.setStorageSync('currentTabbar', 'bv')
      } else {
        uni.setStorageSync('currentTabbar', 'pay')
      }
      if (this.bv_platform) {
        //跳转到bv首页
        this.$Router.pushTab({
          name: "index"
        })
      } else {
        //跳转到交易首页
        this.$Router.pushTab({
          // name: "indexYs"
          name: 'contract-BITindex'
        })
      }
    },
    //ios获取送审版本
    async get_version() {
      let res = await this.$api.java_commonconfigInfo({
        name: 'ios_apple_pay_version',
      });
      if (res.status.code == 0) {
        let curV = uni.getSystemInfoSync().appVersion
        let reqV = res.result.value
        if (curV == reqV) {
          this.showActive = false
        } else {
          this.showActive = true
        }
        console.log(this.showActive)
      } else {
        uni.showToast({
          title: res.status.msg,
          icon: 'none',
          duration: 3000
        });
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.activecheck {
  background: #595765;
  border-radius: 21rpx;
  width: 358rpx;
  height: 63rpx;
  margin-left: -10rpx;
  padding-left: 10rpx;
}

.rotate-anim {
  animation: rotate-animation .3s forwards;
}

@keyframes rotate-animation {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(135deg);
  }

  100% {
    transform: rotate(270deg);
  }
}

img:not(.rotate-anim) {
  animation: rotate-back .3s forwards;
}

@keyframes rotate-back {
  0% {
    transform: rotate(270deg);
  }

  50% {
    transform: rotate(135deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 动画样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
  /* 控制透明度的变化时长 */
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  /* 进入和离开时透明度为0 */
}

.mask {
  transition: .5s;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.74);
  z-index: 1;
  /* 确保蒙版在最上层 */
}

.tabbar_view {
  // pointer-events: none;
  padding: 0rpx 30rpx 0rpx 54rpx;
  position: fixed;
  left: 0;
  bottom: 30rpx;
  width: 100%;
  height: 110rpx;
  z-index: 9;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left_view {
    background-color: rgba(73, 71, 82, 0.9);
    padding: 20rpx 30rpx 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    border-radius: 44rpx;
    position: relative;
    min-width: 300rpx;
    transition: width 1s ease;

    /* 平滑过渡效果 */
    &.oldPadd {
      padding: 20rpx 30rpx 20rpx 30rpx;
    }

    .opt {
      // pointer-events: auto;
      position: absolute;
      top: -130rpx;
      right: -10rpx;
      width: 170rpx;
      height: 132rpx;
      background: #46454F;
      box-shadow: inset 0px 0px 6rpx 1rpx rgba(0, 0, 0, 0.16);
      border-radius: 24rpx 24rpx 24rpx 0px;
      padding: 18rpx 16rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      // align-items: center;
      &-item {
        display: flex;
        align-items: center;

        // justify-content: flex-start;
        image {
          width: 36rpx;
          height: 36rpx;
          margin-right: 6rpx;
        }

        text {
          font-weight: 400;
          font-size: 24rpx;
          color: #FFFFFF;
        }
      }

      &-line {
        height: 1rpx;
        width: 100%;
        background: #53505D;
      }
    }

    .tabbar_li {
      padding: 0rpx 22rpx;

      .default {
        image {
          width: 44rpx;
        }
      }

      .active {
        image {
          height: 60rpx;
        }
      }
    }

    .index_check {
      width: 105rpx;
      height: 50rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24rpx;
      background-color: #666472;
      border-radius: 30rpx;
      position: absolute;
      left: -28rpx;
      color: #fff;

      image {
        width: 24rpx;
        margin-right: 6rpx;
      }
    }
  }

  .right_view {
    background-color: rgba(73, 71, 82, 0.9);
    border-radius: 50rpx;
    height: 90rpx;
    min-width: 90rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 24rpx;

    .default {
      image {
        width: 44rpx;
      }
    }

    .active {
      image {
        width: 120rpx;
      }
    }
  }
}

.tabbar_view2 {
  // pointer-events: none;
  padding: 0rpx 30rpx 0rpx 0rpx;
  position: fixed;
  left: 0;
  bottom: 39rpx;
  width: 100%;
  height: 110rpx;
  z-index: 9;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left_view2 {
    // background-color: rgba(73, 71, 82, 0.9);
    padding: 20rpx 0rpx 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    border-radius: 44rpx;
    position: relative;
    // min-width: 300rpx;
    transition: width 1s ease;

    .popup {
      position: absolute;
      bottom: 70rpx;
      // #ifdef H5
      left: 259rpx;
      // #endif

      // #ifndef H5
      left: 256rpx;
      // #endif
      background-image: url("https://cdn-lingjing.nftcn.com.cn/image/20241218/9065ce07d98ffbd3278967acb4c629d5_776x922.png");
      background-size: 100%;
      width: 388rpx;
      height: 461rpx;
      z-index: 1;
      padding: 27rpx 20rpx 55rpx 20rpx;
      overflow-y: auto;
      overflow-x: hidden;

      image {
        width: 42rpx;
        height: 42rpx;
        margin-right: 15rpx;
        border-radius: 50%;
      }

      font-weight: 500;
      font-size: 30rpx;
      color: #FFFFFF;
      line-height: 7rpx;

      .popup-item {
        display: flex;
        margin-bottom: 25rpx;
        align-items: center;
        position: relative;

        .checked {
          position: absolute;
          width: 28rpx;
          height: 28rpx;
          right: 25rpx
        }
      }

      .mg {
        width: 100%;
        height: 63rpx;
        background: #595765;
        border-radius: 21rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        font-size: 24rpx;
        color: #FFFFFF;
        margin-bottom: 30rpx;
        transition: .5s all;

        .arrow {
          margin-left: 55rpx;
          width: 11rpx;
          height: 21rpx;
        }
      }
    }

    .popup-transition-enter,
    .popup-transition-leave-to

    /* .popup-transition-leave-active in <2.1.8 */
      {
      transform: translateY(100%);
      /* 初始位置往下 */
      bottom: -100rpx;
    }

    .popup-transition-enter-to,
    .popup-transition-leave {
      transform: translateY(0);
      /* 动画结束后的最终位置 */
      bottom: 75rpx;
      /* 设置弹出的目标位置 */
    }

    .popup-transition-enter-active,
    .popup-transition-leave-active {
      transition: transform 0.5s ease, bottom 0.5s ease;
      /* 设置动画时长 */
    }

    &.oldPadd {
      padding: 20rpx 30rpx 20rpx 30rpx;
    }

    .tabbar_li2 {
      padding: 0rpx 30rpx;

      .default2 {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 116rpx;
        height: 88rpx;
        background: #484554;
        border-radius: 44rpx;

        image {
          width: 44rpx;
        }
      }

      .active2 {
        z-index: 99;
        width: 162rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 88rpx;
        background: #484554;
        border-radius: 44rpx;
        // padding: 22rpx 25rpx 20rpx 22rpx;

        image {
          height: 46rpx;
        }
      }
    }

    .index_check2 {
      width: 116rpx;
      height: 88rpx;
      background: #484554;
      border-radius: 44rpx;
      display: flex;
      margin-right: 20rpx;
      justify-content: center;
      align-items: center;
      font-size: 24rpx;
      background-color: #666472;
      // position: absolute;
      // left: -28rpx;
      color: #fff;

      image {
        width: 44rpx;
        height: 37rpx;
        margin-right: 6rpx;
      }
    }
  }

  .right_view2 {
    // background-color: rgba(73, 71, 82, 0.9);
    // border-radius: 50rpx;
    // height: 90rpx;
    // min-width: 90rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rpx 40rpx 0 24rpx;


    .default2 {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 116rpx;
      height: 88rpx;
      background: #484554;
      border-radius: 44rpx;

      image {
        width: 44rpx;
      }
    }

    .active2 {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 88rpx;
      background: #484554;
      border-radius: 44rpx;
      padding: 22rpx 25rpx 20rpx 22rpx;

      image {
        width: 120rpx;

      }
    }
  }
}
</style>
