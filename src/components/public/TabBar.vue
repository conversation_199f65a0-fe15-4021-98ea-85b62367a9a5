<template>
	<view class="view">
		<u-tabbar :list="tabbar" :beforeSwitch="beforeSwitch"    bg-color="#FEF4FF" height="100rpx"
			:border-top="false"  active-color="#121212"
			inactive-color="rgba(216, 182, 98, 0.3)" icon-size="48"></u-tabbar>
	</view>
</template>

<script>
	export default {
		name: "tabbar",
		data() {
			return { 
				tabbar: [{
						iconPath: "/static/newtabBar/index.png",
						selectedIconPath: "/static/newtabBar/index_c.png",
						pagePath: "/pages/project/index/index",
						text: "首页",
					},
					{
						pagePath: "/pages/project/notice/index",
						// pagePath: "/pagesA/project/personal/comingSoon",

						iconPath: "/static/newtabBar/notice.png",
						selectedIconPath: "/static/newtabBar/notice_c.png",
						text: "公告",
					},
					{
						pagePath: "/pages/project/putaway/index",
						iconPath: "/static/newtabBar/putaway.png",
						selectedIconPath: "/static/newtabBar/putaway_c.png",
						text: "寄售区",
					},
					{
						pagePath: "/pages/project/personal/index",
						iconPath: "/static/newtabBar/personal.png",
						selectedIconPath: "/static/newtabBar/personal_c.png",
						text: "我的",
					},
				],
				content: "为了保障您的创作权益，以及避免交易版权纠纷，请先进行实名认证。",
				isShow: false,
				type: "",
			};
		},
		created() {
		},
		onLoad(query) {
			// 
		},
		methods: {
			beforeSwitch(index) {
				uni.navigateTo({
					url: this.tabbar[index].pagePath
				});
				return true;
			},
		},
	};
</script>

<style lang="scss" scoped>
	::v-deep {
		.u-tabbar__content {
			padding-bottom: 20rpx;
		}
		.u-icon__img{
			// filter:grayscale(1);
		}
		.u-tabbar__content__item__text {
			font-size: 20rpx;
			line-height:2rpx;
		}

		.u-tabbar__content__circle__button {
			top: -10rpx;
		}

		.u-tabbar__content__circle__border {
			top: -20rpx;
		}

		.u-badge {
			right: 0rpx !important;
			left: 24rpx !important;
			top: -12rpx !important;
			font-size: 20rpx !important;
			background-color: #F64035 !important;
			display: inline-table;
			min-width: 30rpx;
			line-height: inherit !important;
			height: 30rpx;
			text-align: center;
			border: 2rpx solid #1E1E1E;
			// filter:grayscale(1);
			// background-image: url("https://www.nftcn.com.cn/h5/static/tabBar/collection_c.png") !important;
			// background-size: 100% 100%;
		}
	}
</style>
