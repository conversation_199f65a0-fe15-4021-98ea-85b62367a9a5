<template>
    <u-button hover-class="none" @click="click">{{ text }}</u-button>
</template>

<script>
export default {
    name: "ButtonBar",
    props: {
        text: {
            type: String,
            default: '确认'
        },
    },
    methods: {
        click() {
            this.$emit('click');
        },
    },
}
</script>

<style lang="scss" scoped>
.u-btn{
    width: 100%;
    background-image: var(--primary-button-color);
    border-radius: 0;
    border: node;
    color: #121212;
    font-size: 28rpx;
    font-weight: bold;
}
.u-hairline-border::after {
    border: none;
}
</style>
