<template>
    <text class="price-symbol">{{ symbol }}</text>
  </template>
  
  <script>
  export default {
    name: 'PriceSymbol',
    computed: {
      symbol() {
        let iversion = uni.getStorageSync('iversion')
        let curV = uni.getSystemInfoSync().appVersion
        if(iversion && iversion == curV){
           return ''
        }else{
          return '￥'
        }
        // 这里可以根据你的业务逻辑来判断是否显示符号
        // 例如：可以从 vuex 获取用户配置或其他条件
      }
    },
    methods: {
      shouldShowSymbol() {
        // 在这里添加你的判断逻辑
        // 例如：判断是否为特定页面、特定商品类型等
        return false  // 默认显示符号
      }
    }
  }
  </script>
  
  <style scoped>
 
  </style>