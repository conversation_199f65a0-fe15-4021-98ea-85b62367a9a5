<script>
	/**
	 * @description: 选择器组件
	 * @author: jay
	 * @date: 2022-07-04
	 * @update: 2022-07-04
	 * @param {array} options 选项数组 [{label: string 显示标签, value: string | number 选项值, disabled: boolean 是否禁用 }]
	 * @param {string} placeholder 占位符
	 * @param {boolean} clear 点击选项如已选就清空选项
	 * @param {boolean} showMask 显示遮罩
	 * @param {string | number} selected 选中的值 selected 需加 .sync 修饰符.(空值不要设置为数字 0) <common-select :options="options" :selected.sync="selected" clear></common-select>
	 * @event {function} click 点击标签触发
	 * @event {function} onChange 选项变化
	 */
	export default {
		components: {},
		props: {
			selected: {
				// 选中的值
				type: String | Number | null,
				required: true,
			},
			options: {
				// 可选项, label: 显示标签; value: 选项值; disabled: 是否禁用
				type: Array,
				required: true,
			},
			placeholder: {
				// 没有选择时的提示文字
				type: String,
			},
			clear: {
				// 点击选项如已选就清空选项
				type: Boolean,
				default: false,
			},
			showMask: {
				// 显示遮罩
				type: Boolean,
				default: true,
			},
		},
		data() {
			return {
				selectOptionsVisible: false,
				selectOptionsPosition: {
					top: 0,
					left: 0,
				},
			};
		},
		computed: {
			selectedText() {
				return this.options.find(
					(item) => item.value.toString() === this.selected?.toString()
				).label;
			},
		},
		methods: {
			toggleVisible() {
				this.$emit("click");
				if (this.options.length <= 1) {
					const exist = this.selected === 0 || !!this.selected
					if (!exist) {
						console.log(this.options[0].value)
						this.$emit("update:selected", this.options[0].value);
					} else {
						this.$emit("update:selected", null);
					}
					return
				}
				this.selectOptionsVisible = !this.selectOptionsVisible;
				if (this.selectOptionsVisible) {
					this.$nextTick(() => {
						this.positionContent();
					});
				}
			},
			onChangeSelect(value, disabled) {
				if (disabled) {
					return;
				}
				if (this.selected === value) {
					this.clear && this.$emit("update:selected", null);
					this.$emit('onChange', null)
				} else {
					this.$emit("update:selected", value);
					this.$emit('onChange', value)
				}
				this.toggleVisible();
			},
			positionContent() {
				const query = uni.createSelectorQuery().in(this);
				query
					.select(".common-select-label")
					.boundingClientRect((data) => {
						this.selectOptionsPosition.top = `${data.height + 2}px`;
						this.selectOptionsPosition.left = `-${data.left}px`;
					})
					.exec();
			},
		},
	};
</script>

<template>
	<view class="common-select">
		<view class="common-select-label" :class="selected && 'active'" @click="toggleVisible">
			{{
        selected ? selectedText : placeholder ? placeholder : options[0].label
      }}
			<!-- <u-icon :name="selectOptionsVisible ? 'arrow-up-fill' : 'arrow-down-fill'" size="22"></u-icon> -->
			<image src="@/static/imgs/public/price.png" mode="widthFix" style="width: 14rpx;height: 12rpx;">
			</image>

		</view>
		<view :style="selectOptionsPosition" v-if="selectOptionsVisible" class="common-select-options">
			<view class="common-select-item" v-for="item in options" :key="item.value"
				@click="onChangeSelect(item.value, item.disabled)" :class="{
          'common-select-item__active':
            item.value.toString() === (selected && selected.toString()),
          'common-select-item__disabled': item.disabled,
        }">
				<view>{{ item.label }}</view>

			</view>
		</view>

		<view v-if="showMask && selectOptionsVisible" @click="toggleVisible" class="common-select-mask"></view>
	</view>
</template>

<style lang="scss" scoped>
	.common-select {
		position: relative;
		display: inline-block;
		color: #fff;
		font-size: 24rpx;
		font-weight: 600;

		.common-select-label {
			padding: 16rpx 20rpx;
			display: flex;
			align-items: center;
			font-weight: bold;
			font-size: 24rpx;
			color: #35333E;

			&.active {
				color: #35333E;
			}
		}

		.u-icon {
			margin-left: 10rpx;
		}

		.common-select-options {
			position: absolute;
			top: 0rpx;
			left: 0;
			padding: 16rpx 0;
			font-size: 28rpx;
			color: var(--main-front-color);
			line-height: 28rpx;
			width: 100vw;
			background-color: var(--main-bg-color);
			z-index: 20;

			.common-select-item {
				padding: 24rpx 40rpx;

				&.common-select-item__active {
					color: var(--active-color1);
				}

				&.common-select-item__disabled {
					cursor: not-allowed;
					color: #aaa;
				}
			}
		}

		.common-select-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: #00000066;
			z-index: 1;
			width: 100vw;
			height: 100vh;
		}
	}
</style>