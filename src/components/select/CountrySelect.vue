<template>
  <u-popup v-model="show" mode="bottom" border-radius="14" height="1500rpx">
    <view class="country_select_popup">
      <!-- 头部 -->
      <view class="country_select_header">
        <text class="country_select_title">国家/地区</text>
        <u-icon name="close" class="country_select_close" @click="close" size="40"></u-icon>
      </view>
      <view class="search_box_">
        <view class="search_inner_">
          <image class="search_icon_" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385297482358546432.png" />
          <input @input="onSearch" v-model="keyword" class="search_input_" placeholder="搜索"
            placeholder-style="color:#bdbdbd;font-size:30rpx;" type="text" />
        </view>
      </view>
      <!-- 列表 -->
      <view class="country_select_list">
        <view v-for="item in filteredList" :key="item.value" class="country_select_item" @click="selectCountry(item)">
          <image class="country_flag" :src="item.icon" mode="aspectFill" />
          <text class="country_name">{{ item.label }}</text>
          <view v-if="item.value === value" class="country_dot"></view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
  export default {
    name: 'CountrySelect',
    props: {
      show: Boolean,
      value: [String, Number],
      type: String,
    },
    data() {
      return {
        keyword: '',
        list: [
          {
            label: '美国',
            value: 'US',
            icon: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385643664817610752.png'
          },
          {
            label: '加拿大',
            value: 'CA',
            icon: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385643715954565120.png'
          },
          {
            label: '中国',
            value: 'CN',
            icon: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385643752109465600.png'
          },
          {
            label: '日本',
            value: 'JP',
            icon: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385643796476813312.png'
          }
        ],
        filteredList: [],
      }
    },
    watch: {
      list: {
        immediate: true,
        handler(val) {
          this.filteredList = val || []
        }
      },
      keyword() {
        this.onSearch()
      }
    },
    methods: {
      selectCountry(item) {
        this.$emit('select', item, this.type)
      },
      onSearch() {
        const kw = this.keyword.trim().toLowerCase()
        if (!kw) {
          this.filteredList = this.list
        } else {
          this.filteredList = this.list.filter(item =>
            item.label.toLowerCase().includes(kw)
          )
        }
      },
      close() {
        this.$emit('update:show', false)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .search_box_ {
    padding: 24rpx 24rpx 0 24rpx;
    margin-top: 40rpx;
  }

  .search_inner_ {
    display: flex;
    align-items: center;
    background: #f7f7f7;
    border-radius: 32rpx;
    height: 74rpx;
    padding: 0 24rpx;
  }

  .search_icon_ {
    width: 52rpx;
    height: 52rpx;
    margin-right: 16rpx;
  }

  .search_input_ {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 30rpx;
    color: #111;
    height: 74rpx;
    line-height: 74rpx;
  }

  .country_select_mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.05);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .country_select_popup {
    background: #fff;
    width: 100vw;
    border-radius: 0 0 32rpx 32rpx;
    margin-top: 0;
  }

  .country_select_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 0 32rpx;
  }

  .country_select_title {
    font-size: 32rpx;
    font-weight: 600;
  }

  .country_select_close {
    color: #999;
  }

  .country_select_list {
    margin-top: 32rpx;
  }

  .country_select_item {
    display: flex;
    align-items: center;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    position: relative;
    margin-bottom: 40rpx;
  }

  .country_flag {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    background: #f5f5f5;
  }

  .country_name {
    font-size: 28rpx;
    color: #222;
  }

  .country_dot {
    width: 18rpx;
    height: 18rpx;
    border-radius: 50%;
    background: #F97C9A;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    margin-left: -36rpx;
  }
</style>