<script setup >
import { ref, onMounted } from 'vue';
 const props = defineProps({
   num: {
     type: Number,
     default: 0,
   },
   duration: {
     type: Number,
     default: 1,
   },
 });
let current = ref('0');
 
function numberGrow() {
  let step = parseInt((props.num * 100) / (props.duration * 1000) + '');
  let start = 0;
  let timer = setInterval(() => {
    start += step;
    if (start > props.num) {
      clearInterval(timer);
      start = props.num;
      timer = null;
    }
    if (start === 0) {
      start = props.num;
      clearInterval(timer);
    }
    current.value = start.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
  }, props.duration * 100);
}
 
onMounted(() => {
	setTimeout(()=>{
		 numberGrow();
	},300)
})
</script>
 
<template>
{{ current }}
</template>