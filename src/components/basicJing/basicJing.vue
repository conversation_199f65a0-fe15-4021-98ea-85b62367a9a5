<template>
	<view class="list">
		<view>
			<view v-if="subCurrent==0" class="zore">发起时间:{{item.launchTime}}</view>
			<view v-else class="one">
				<view>竞价购买</view>
				<view>支付时间:{{item.payTime}}</view>
			</view>
		</view>
		<view class="cont">
			<view class="lef">
				<view>
					<image :src="item.icon" mode="aspectFill" v-if="subCurrent==0"></image>
					<image :src="item.cover" mode="aspectFill" v-else></image>
				</view>
				<view v-if="subCurrent==0" class="load">
					<view>{{item.seriesName}}</view>
					<view>已买到 {{item.okNumber}}/{{item.number}}</view>
				</view>
				<view v-else class="yes">
					<view>{{item.title}}</view>
					<view style="display: inline-block;">
						<view>token ID:</view>
						<view class="flex">
							<text v-show="item.show">{{item.tid}}</text>
							<text v-show="!item.show">{{item.tidd}}</text>
							<view class="copy">
								<image  src="@/static/imgs/public/copy.png" mode="widthFix" @click="copy(text)"></image>
							</view>
							<view class="show" >
								<image class="copy-img" v-show="item.show" src="https://cdn-lingjing.nftcn.com.cn/image/20240308/8812274a59ce41c3cc2753903ac889bc_72x72.png" mode="widthFix"
									@click="check(item)">
								</image>
								<image class="copy-img" v-show="!item.show" src="https://cdn-lingjing.nftcn.com.cn/image/20240308/fe895b568b5e4befab21850132e60ad3_72x72.png" mode="widthFix"
									@click="check(item)">
								</image>
							</view>
						</view>
					</view>
					<!-- 	<view>
						<view>哈希值:</view>
						<view>1321456123132151231 <image src="@/static//imgs//public/copy.png" mode="widthFix" @click="copy(text)"></image>
						</view>
					</view> -->
					<view>
						<view>订单号:</view>
						<view>{{item.orderNo}}
							<image src="@/static//imgs//public/copy.png" mode="widthFix" @click="copy(text)"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="rig" v-if="subCurrent==0">
				<view @click="det(item)">撤销</view>
				<view>出价: <text>￥{{item.price}}</text> </view>
			</view>
			<view v-else class="rigYes">
				￥{{item.price}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ['item', 'subCurrent'],
		name: "basicJIng",
		data() {
			return {

			};
		},
		methods: {
			det(item) { //撤销
				this.$emit('remove', item.biddingId)
			},
			copy(text) {
				uni.setClipboardData({
					data: text,
					success() {
						uni.showToast({
							title: '已复制',
							icon: 'none'
						})
					}
				})
			},
			check(item){
				console.log(item.show)
				item.show = !item.show
			}
		}
	}
</script>

<style lang="scss" scoped>
	.list {
		width: 678rpx;
		height: 258rpx;
		background: #25232d;
		border-radius: 30rpx;
		margin: 0 auto 40rpx auto;
		border-radius: 30rpx;
		overflow: hidden;

		.zore {
			width: 100%;
			height: 60rpx;
			line-height: 60rpx;
			background: rgba(29, 27, 37, 0.5);
			border-radius: 30rpx 30rpx 0px 0px;
			font-weight: 400;
			font-size: 18rpx;
			color: #999;
			padding-left: 32rpx;
			box-sizing: border-box;
		}

		.one {
			width: 100%;
			height: 60rpx;
			background: rgba(29, 27, 37, 0.5);
			border-radius: 30rpx 30rpx 0px 0px;
			display: flex;
			align-items: center;
			color: #999;
			font-weight: 400;
			font-size: 18rpx;
			padding-left: 32rpx;
			box-sizing: border-box;

			>view:nth-child(1) {
				width: 80rpx;
				height: 24rpx;
				line-height: 24rpx;
				text-align: center;
				background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
				border-radius: 4rpx;
				margin-right: 20rpx;
				font-weight: bold;
				font-size: 16rpx;
				color: #25232D;
			}
		}

		.cont {
			width: 100%;
			height: 198rpx;
			background: #25232D;
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			padding: 20rpx 36rpx 0 30rpx;
			box-sizing: border-box;
			position: relative;

			.lef {
				display: flex;
				align-items: flex-start;

				>view:nth-child(1) {
					width: 148rpx;
					height: 148rpx;
					background: #F5F5F5;
					border-radius: 12rpx;
					border: 4rpx solid #F5F5F5;
					box-sizing: border-box;
					margin-right: 24rpx;

					>image {
						width: 100%;
						height: 100%;
					}
				}

				.load {
					font-weight: 400;
					font-size: 24rpx;
					color: #999;

					>view:nth-child(1) {
						color: #FFFFFF;
						margin-bottom: 44rpx;
					}
				}

				.yes {

					>view {
						display: flex;
						align-items: center;
						font-size: 20rpx;
						font-weight: 400;
						color: #999;
						margin-bottom: 15rpx;

						>view:nth-child(2) {
							margin-left: 10rpx;
							display: flex;
							align-items: center;

							>image {
								width: 20rpx;
								height: 22rpx;
								margin-left: 10rpx;
							}
						}
					}
					.flex{
						display:flex;
						justify-content: flex-start;
					}
					>view:nth-child(1) {
						font-weight: 400;
						font-size: 24rpx;
						color: #fff;
						margin-bottom: 18rpx;
					}



				}
			}

			.rigYes {
				font-weight: bold;
				font-size: 24rpx;
				color: #63EAEE;
				position: absolute;
				right: 50rpx;
			}

			.rig {
				display: flex;
				flex-direction: column;
				align-items: flex-end;

				>view:nth-child(1) {
					width: 80rpx;
					height: 36rpx;
					line-height: 36rpx;
					text-align: center;
					background: #25232D;
					border-radius: 18rpx;
					border: 1rpx solid #FFFFFF;
					font-weight: 400;
					font-size: 20rpx;
					color: #FFFFFF;
					box-sizing: border-box;
					margin-bottom: 40rpx;
				}

				>view:nth-child(2) {
					font-weight: bold;
					font-size: 24rpx;
					color: #63EAEE;

					>text {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
	.copy{
		image{
			width:22rpx;
			margin-left: 10rpx;
		}
	}
	.show{
		margin-left: 10rpx;
		image{
			width:30rpx;
		}
	}
</style>