<script>
/**
 * @description: 图片上传
 * @author: jay
 * @date: 2022-07-08
 * @update: 2022-07-25
 * @param {number} limit 允许上传的文件数量。默认 1 暂时只支持单个文件上传，等待优化
 * @param {array | number} value 上传图片地址，limit 为 1 时为 string，其他情况为 array。返回阿里云图片地址
 * @param {string} accept 接受的文件类型，默认为图片类型，可以传入 'image/jpeg,image/png,image/gif'
 * @param {string} action 服务器地址
 */

import { Message } from 'element-ui'
import router from '@/router'

export default {
  props: {
    text: {
      type: String,
      default: '点击上传'
    },
    action: {
      type: String,
      default: `${process.env.VUE_APP_BASE_URL}osscenter/adminApi/missWebSign/uploadExcel`
    },
    limit: { // 暂时只支持单个文件上传，等待优化
      type: Number,
      default: 1
    },
    value: {
      type: [Array, String],
      required: true
    },
    id: { // 暂时只支持单个文件上传，等待优化
      type: String,
      default: 1
    },
    accept: { // 文件类型
      type: String
    },
    disabled: { // 暂时只支持单个文件上传，等待优化
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
      token: { AdminAuthorization: localStorage.getItem('usertoken') }, // 设置上传的请求头部
      uploadHide: false,
      fileList: []
    }
  },
  mounted () {
  },
  methods: {
    onSuccess (res, file) {
      res.result.id=this.id
      if (res.status && res.status.code === 0) {
        this.$emit('update:value', res.result?.url)
        this.$emit('success', res)
      } else if (res.status?.code === 1002) {
        Message.error(res?.status?.msg)
        router.push('/login')
      }
    },
    // 图片移除
    onRemove (file, fileList) {
      this.uploadHide = fileList.length >= this.limit
      this.$emit('update:value', '')
    },
    onChange (file, fileList) {
      if(this.condition){
        this.uploadHide = fileList.length >= this.limit
      }else{
         this.$emit('onMsg')
      }
    },
    beforeUpload (file) {
      console.log(file)
    },
    // 图片移除
    remove () {
      this.fileList=[]
    },
  },
  watch: {
    value: {
      handler (value) {
        if (value) {
          if (value.startsWith('https://oss.')) {
            // 阿里云，截取文件名
            const fileName = value.replace(/(.*\/)*([^.]+).*/ig, '$2')
            this.fileList = [{ url: value, name: fileName }]
          } else {
            this.fileList = [{ url: value, name: value }]
          }
        }
      },
      immediate: true
    }
  }
}
</script>

<template>
  <el-upload
    :disabled="disabled"
    :action="action"
    :headers="token"
    :on-success="onSuccess"
    :on-change="onChange"
    :on-remove="onRemove"
    :before-upload="beforeUpload"
    :limit="limit"
    class="uploader"
    :accept="accept"
    :class="{ hide: uploadHide || value }"
    :file-list="fileList"
  >
    <el-button v-if="!uploadHide" size="mini" type="primary">{{ text }}</el-button>
  </el-upload>
</template>

<style lang="scss" scoped>
.uploader {
  //display: inline-block;
  &::v-deep.hide {
    //display: flex;
    //flex-wrap: wrap;
    //width: 100%;
    margin-top: -30px;
    .el-upload-list__item .el-icon-close {
      //display: none;
      top: -6px;
    }
  }

}
</style>
