<template>
	<u-popup class="popup" v-model="show" mode="bottom" closeable close-icon-pos="top-left" close-icon-color="#fff"
		close-icon-size="34">
		<view class="header">
			{{ modeStr.title }}
		</view>
		<view class="message">
			{{ modeStr.message }}
		</view>
		<u-message-input ref="input" mode="box" class="message-box" :focus="false" :maxlength="6" :dot-fill="true"
			:disabled-keyboard="true" inactive-color="var(--message-box-point-color)" @finish="finish">
		</u-message-input>
		<view class="change-mode" :class="[
        page,
        {
          disabled: sendEmailDisable,
        },
      ]" @click="modeStr.clickEvent">
			<text>{{ modeStr.button }}</text>
			<text v-show="sendEmailDisable">({{ second }})</text>
		</view>
		<u-number-keyboard class="keyboard" @backspace="backspace" @change="keyDown" mode="number"></u-number-keyboard>
	</u-popup>
</template>

<script>
	/**
	 * 余额支付,重置,设置支付密码合成组件
	 * @description 弹窗组件+验证码组件+键盘组件合成
	 * <AUTHOR>
	 * @property {Boolean} popupShow 控制弹窗显隐,支持.sync
	 * @property {String} orderNo 支付订单的订单编号,支付时;
	 * @property {String} orderType 订单类型,支付时;
	 * @property {String} mode 模式,pay: 支付,set: 设置密码 ,forget: 重置密码
	 * @event paySuccess 支付成功后触发
	 * @event resetSuccess 支付密码重置成功后触发
	 * @event createSuccess 支付密码设置成功后触发
	 */
	export default {
		name: 'payPopup',
		props: {
			popupShow: {
				type: Boolean,
				required: true,
			},
			orderNo: String,
			orderNoList: String,
			batchBuyNum:Number,
			orderType: String,
			mode: {
				type: String,
				default: 'pay',
			},
			paymentScene: {
				type: Number,
				default: 1,
			},
			title: String,
			message: String,
		},
		data() {
			return {
				password: '', // 此时输入框中的内容
				page: 'pay', // 当前所处的页面
				second: 0, // 读秒
				captcha: '', // 验证码
				passwordFirst: '', // 第一次输入的密码
				passwordSecond: '', // 第二次输入的密码
				phone: '', // 用户手机号
				email: '', // 用户邮箱号
			};
		},
		computed: {
			show: {
				get() {
					return this.popupShow;
				},
				set(value) {
					this.$emit('update:popupShow', value);
				},
			},
			modeStr() {
				switch (this.page) {
					case 'pay':
						return {
							title: this.title || '支付密码',
								message: this.message || '请输入支付密码,用于支付订单',
								button: '忘记密码',
								clickEvent: this.forget,
						};
					case 'email':
						return {
							title: '验证码验证',
								message: '请输入验证码,用于安全校验',
								button: '重新获取',
								clickEvent: this.sendEmail,
						};
					case 'password':
						return {
							title: '设置新的支付密码',
								message: '请重新设置平台余额支付密码,用于订单支付',
						};
					case 'passwordSecond':
						return {
							title: '设置新的支付密码',
								message: '请再次输入余额支付密码,用于二次确认',
						};
				}
			},
			sendEmailDisable() {
				return this.second > 0 && this.page === 'email';
			},
			isReset() {
				return this.mode === 'pay' || this.mode === 'reset';
			},
			/**
			 * 发送信息的类型,有手机号就用手机,没有就用邮箱;
			 */
			messageType() {
				if (this.phone) {
					return {
						api: 'java_sendAliYunSms',
						req: {
							mobPhone: this.phone,
							aliYumSmsType: this.isReset ?
								'RESET_TRADE_PASSWORD' :
								'SET_TRADE_PASSWORD',
						},
						checkApi: 'java_verifyAliYunPhoneCode',
						successMsg: '已发送手机验证码',
						uuidKey: 'smsUUID',
						type: 'phone',
					};
				} else {
					return {
						api: 'java_sendAliYunEmail',
						req: {
							emailAddress: this.email,
							emailType: this.isReset ?
								'RESET_TRADE_PASSWORD' :
								'SET_TRADE_PASSWORD',
						},
						checkApi: 'java_verifyAliYunEmailCode',
						successMsg: '已发送邮箱验证码',
						uuidKey: 'emailUUID',
						type: 'email',
					};
				}
			},
		},
		watch: {
			page() {
				this.password = '';
			},
			show() {
				this.password = '';
				switch (this.mode) {
					case 'pay':
						this.page = 'pay';
						break;
					case 'set':
					case 'reset':
						this.page = 'email';
						this.sendEmail();
						break;
				}
			},
			password(val) {
				const input = this.$refs['input'];
				input.getVal({
					detail: {
						value: val,
					},
				});
			},
		},
		created() {
			this.$api
				.userInfo({
					userId: '',
				})
				.then(({
					status: {
						code,
						msg
					},
					result
				}) => {
					if (code === 0) {
						this.phone = result.phone;
						this.email = result.email;
						uni.setStorageSync("balance",result.balance)
						uni.setStorageSync("isSetTradePassword",result.isSetTradePassword)
					} else {
						uni.showToast({
							title: msg,
							icon: 'none',
						});
					}
				});
		},
		methods: {
			/**
			 * 键盘按下事件
			 */
			keyDown(val) {
				this.password += val;
			},
			/**
			 * 键盘按下后退
			 */
			backspace() {
				const {
					password
				} = this;
				const {
					length
				} = password;
				if (length) this.password = password.substr(0, length - 1);
			},
			/**
			 * 输入完成事件
			 */
			finish(value) {
				switch (this.page) {
					case 'pay':
						this.balancePay(value);
						break;
					case 'email':
						this.captcha = this.password;
						this.page = 'password';
						break;
					case 'password':
						this.passwordFirst = this.password;
						this.page = 'passwordSecond';
						break;
					case 'passwordSecond':
						this.passwordSecond = this.password;
						this.check();
						break;
				}
			},
			/**
			 * 余额支付
			 * @param {String} psw 支付密码
			 */
			async balancePay(psw) {
				this.$emit('pay', psw);
				if (!this.orderNo) return;
				uni.showLoading({
					title: '支付中',
					mask:true
				});
				const res = await this.$api.java_order_pay_call({
					payMethod: 10,
					tradePassword: psw,
					paymentScene: this.paymentScene,
					orderId: this.orderNo,
					orderType: this.orderType,
					isH5: 1,
					orderNoListStr:this.orderNoList,
					isBatchBuy:this.batchBuyNum>1?1:0
				});
				if (res.status.code == 0) {
					uni.hideLoading();
					uni.showLoading({
						title: '支付成功',
						duration: 3000,
					});
					this.show = false;
					this.$emit('paySuccess');
				} else { 
					this.password = '';
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000,
					});
				}
			},
			/**
			 * 点击忘记密码
			 */
			forget() {
				if (this.second > 0) this.page = 'email';
				else this.sendEmail();
			},
			/**
			 * 发送邮箱验证码
			 */
			async sendEmail() {
				if (this.sendEmailDisable > 0 || !this.show) return;
				uni.showLoading({
					title: '正在发送',
					mask: true,
				});
				try {
					const {
						api,
						req,
						successMsg
					} = this.messageType;
					const {
						status: {
							code,
							msg
						},
					} = await this.$api[api](req);
					if (code === 0) {
						this.second = 60;
						this.page = 'email';
						uni.showToast({
							title: successMsg,
							icon: 'none',
						});
						const timer = setInterval(() => {
							if (this.second > 0) {
								this.second--;
							} else {
								clearInterval(timer);
							}
						}, 1000);
					} else {
						uni.showToast({
							title: msg,
							icon: 'none',
							duration: 3000,
						});
					}
				} finally {
					uni.hideLoading();
				}
			},
			/**
			 * 检验验证码是否正确
			 */
			// async verifyCode() {
			//   const {req, checkApi} = this.messageType;
			//   const {status: {code, msg}, result} = this.$api[checkApi]({...req, code: this.captcha});
			//   if (code === 0)
			// },
			/**
			 * 校验
			 */
			check() {
				const {
					passwordFirst,
					passwordSecond,
					mode
				} = this;
				if (passwordFirst === passwordSecond) {
					if (this.isReset) this.resetPassword();
					else this.createPassword();
				} else {
					uni.showToast({
						title: '两次输入的密码不一致！',
						icon: 'error',
						duration: 3000,
					});
					this.password = '';
					this.page = 'password';
				}
			},
			/**
			 * 重置密码
			 */
			async resetPassword() {
				uni.showLoading({
					title: '重置密码',
					mask: true,
				});
				try {
					let res = await this.$api.tradePassReset({
						captcha: this.captcha,
						password: this.passwordFirst,
						verifyWay: this.messageType.type
					});
					if (res.status.code == 0) {
						uni.showToast({
							title: '支付密码重置成功！',
							icon: 'success',
							duration: 3000,
						});
						this.show = false;
						this.$emit('resetSuccess', this.passwordFirst);
					} else {
						uni.showToast({
							title: res.status.msg,
							icon: 'error',
							duration: 3000,
						});
						this.page = 'email';
					}
				} finally {
					uni.hideLoading();
				}
			},
			/**
			 * 创建支付密码
			 */
			async createPassword() {
				uni.showLoading({
					title: '设置密码',
					mask: true,
				});
				try {
					let res = await this.$api.tradePassCreate({
						captcha: this.captcha,
						password: this.passwordFirst,
						verifyWay: this.messageType.type
					});
					if (res.status.code == 0) {
						uni.showToast({
							title: '支付密码设置成功!',
							icon: 'success',
							duration: 3000,
						});
						this.$emit('createSuccess', this.passwordFirst);
					} else {
						this.page = 'email';
						uni.showToast({
							title: res.status.msg,
							icon: 'none',
							duration: 3000,
						});
					}
				} finally {
					uni.hideLoading();
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	::v-deep .uni-toast-ccontent {
		font-size: 26rpx !important;
	}
	.popup {
		::v-deep .u-drawer-content {
			background-color: var(--main-bg-color);
		}

		.header {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			margin-top: 32rpx;
			position: relative;
			color: var(--default-color1);
		}

		.message {
			text-align: center;
			color: var(--default-color1);
			margin-top: 88rpx;
		}

		.message-box {
			margin-top: 40rpx;

			::v-deep .u-box {
				border: 0;
				background-color: rgba(255, 255, 255, 0.3);
			}
		}

		.change-mode {
			text-align: center;
			margin-top: 50rpx;

			&.pay {
				color: #2179D2;
			}

			&.email {
				color:#2179D2;

				&.disabled {
					color: #616161;
				}
			}
		}

		.keyboard {
			margin-top: 60rpx;

			::v-deep .u-keyboard-grids {
				justify-content: space-evenly;

				.u-keyboard-grids-item {
					background-color: #25232D;
					color: #ffffff;
					border-radius: 10rpx;
					flex: none;
					width: 234rpx;
					margin-top: 12rpx;
					&::after {
						content: none;
					}
					&.u-bg-gray {
						background-color:transparent;
						color:#25232D;
					}
				}
			}
		}
	}
</style>
