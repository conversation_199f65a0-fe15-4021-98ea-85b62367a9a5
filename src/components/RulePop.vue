<template>
  <van-popup
    v-model:show="show"
    position="center"
    style="background-color: rgba(255, 255, 255, 0);"
  >
    <div class="content">
      <van-icon name="cross" size="22" color="#fff" class="icon" @click="show = false"/>
      <van-image
        fit="scale-down"
        :src="props.info"
        class="rule_img"
      />
    </div>
  </van-popup>
</template>

<script setup>
/**
 * @description 规则弹窗
 * @property {Boolean} popupShow 控制弹窗显隐
 * @property {String} info 规则图片
 */
import { defineProps, computed, defineEmits } from 'vue';

const props = defineProps({
  popupShow: {
    type: Boolean,
    default: false,
  },
  info: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['update:popupShow']);
const show = computed({
  get () {
    return props.popupShow;
  },
  set (value) {
    emit('update:popupShow', value);
  },
});
</script>

<style lang="scss" scoped>
  .content {
    position: relative;
    max-height: 100vh;
    overflow: scroll;
    .icon {
      position: fixed;
      top: px2vw(60px);
      right: px2vw(48px);
      z-index: 100;
    }
    .rule_img {
      width: 100vw;
    }
  }
</style>
