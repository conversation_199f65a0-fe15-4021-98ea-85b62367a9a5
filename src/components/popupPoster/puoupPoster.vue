<template>
	<view>
		<u-popup v-model="show" width="750rpx" height="2000rpx" mode="center">
			<view class="puoupPoster">
				<view class="box" id="box">
					<view class="tim">存证时间：{{ detailsList.buyTime }}</view>
					<view class="img">
						<image :src="detailsList.cover" mode="aspectFill"></image>
					</view>
					<view class="price"><span>￥</span>{{ detailsList.price }}</view>
					<view class="tit">{{ detailsList.title }}</view>
					<view class="flex">
						<view>
							<view>
								<!-- <image src="@/static/imgs/public/bigverse.png" mode="widthFix" class="bigverse"></image> -->
							</view>
							<view>扫码探索更多数字藏品</view>
						</view>
						<view class="qrCode">
							<view>
								<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="value" size="80rpx"
									:options="options" @complete="complete"></uv-qrcode>
							</view>
							<view></view>
						</view>
					</view>
					<view class="log">
						<image
							src="https://cdn.yanjie.art/image/20240725/6623b439a4b81c5e89b9c61e7756dbdd_200x126.png"
							mode="aspectFill" class="logo"></image>
					</view>
				</view>

				<view class="btns">
					<view v-for="item in btns" :key="item.id" @click="saves(item.id)">
						<view>
							<image :src="item.img" mode="aspectFill"></image>
						</view>
						<view>{{ item.name }}</view>
					</view>
				</view>
				<view class="close" @click="close">
					<image
						src="https://cdn.yanjie.art/image/20240717/f4dd4a53eb0183680b70fe322c7d5b17_160x160.png"
						mode="aspectFill"></image>
				</view>
				<view class="canvas">
					<canvas id="canvasId" canvas-id="canvasId"
						:style="`width: ${width * 2}rpx;height: ${height * 2}rpx;background: #46454F;`"></canvas>
				</view>
			</view>


		</u-popup>
	</view>
</template>

<script>
import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue';
export default {
	name: "puoupPoster",
	props: ['show', 'detailsList', 'value'],
	data() {
		return {
			timer: null,
			btns: [{
				id: 3,
				name: '保存海报',
				img: "https://cdn.yanjie.art/image/20240717/a49021a29b7a773338451a026f1c3976_160x160.png"
			}],
			// {
			// 	id: 0,
			// 	name: '微信',
			// 	// img: ("https://cdn.yanjie.art/image/20240717/9a1e812f1f85ed41314046659904a3e3_160x160.png")
			// }, {
			// 	id: 1,
			// 	name: '朋友圈',
			// 	// img: ("https://cdn.yanjie.art/image/20240717/4c5f549eae10aede5c9b754e05a44b41_160x160.png")
			// }, {
			// 	id: 2,
			// 	name: 'QQ',
			// 	// img: ("https://cdn.yanjie.art/image/20240717/8a72b301f79f16cc11570bf2ee815e4c_160x160.png")
			// },
			ctx: null,
			width: null, //盒子宽
			height: null, //盒子高
			widImg: null, //图片宽
			heiImg: null, //图片高
			versWid: null, //bigvers 宽
			versHei: null, //bigvers 高
			logoWid: null, //logo 宽
			logoHei: null, //logo 高
			arWid: null, //二维码 宽
			arHeight: null, //二维码 高
			flagNum: true, //是否是第一次生成图片
			type: null, //判断是app还是h5
			typeId: null, //判断当前操作
			img: null, //保存生成的图片
			options: {
				useDynamicSize: false,
				errorCorrectLevel: 'Q',
				// margin: 10,
				areaColor: "#fff",
				// 指定二维码前景，一般可在中间放logo
				// foregroundImageSrc: require('static/image/logo.png')
			},
			qrImg: null,
			num: 0,
		};
	},
	mounted() {
		// #ifdef APP
		this.type = 'app'
		// #endif
		// #ifdef H5
		this.type = 'H5'
		// #endif
	},
	methods: {
		complete(event) {
			this.num++
			if (event.success == true && this.num == 1) {
				console.log(event, 'event');
				return this.uvQrcode()
			}
		},
		qrImgCode() {
			let that = this
			if (that.type == 'H5') {
				that.$refs.qrcodes.toTempFilePath({
					success: res => {
						that.qrImg = res.tempFilePath
						console.log('H5临时路径', res.tempFilePath);
					}
				});
			} else if (that.type == 'app') {
				that.$refs.qrcodes.toTempFilePath({
					success: res => {
						that.qrImg = res.tempFilePath
						console.log('app临时路径', res.tempFilePath);
					}
				});
			}
		},
		uvQrcode() {
			let that = this
			that.$nextTick(() => {
				that.$refs.qrcodes.make({
					success: () => {
						console.log('生成成功');
						that.qrImgCode()
					},
					fail: err => {
						console.log(err, '生成失败')
					}
				});
			})
		},
		getsysInfo() { //各个图片的宽高
			let that = this
			const query = uni.createSelectorQuery().in(this);
			query.select("#box")
				.boundingClientRect((data) => {
					// console.log("盒子", data);
					that.width = data.width
					that.height = data.height
				})
				.exec();
			query.select(".img")
				.boundingClientRect((data) => {
					// console.log("大图片", data);
					that.widImg = data.width
					that.heiImg = data.height
				})
				.exec();
			query.select(".bigverse")
				.boundingClientRect((data) => {
					// console.log("bigverse", data);
					that.versWid = data.width
					that.versHei = data.height
				})
				.exec();
			query.select(".logo")
				.boundingClientRect((data) => {
					// console.log("logo", data);
					that.logoWid = data.width
					that.logoHei = data.height
				})
				.exec();
			query.select(".qrCode")
				.boundingClientRect((data) => {
					// console.log("二维码", data);
					that.arWid = data.width
					that.arHei = data.height
				})
				.exec();
		},
		// 
		saves(id) {
			let that = this
			that.typeId = id
			that.getsysInfo()

			const ctx = uni.createCanvasContext('canvasId')
			ctx.width = that.width
			ctx.height = that.height

			if (that.img && that.type == 'app') {
				uni.showLoading({
					title: '正在准备分享'
				})
				return that.share(that.img)
			}
			if (that.img && that.type == 'H5' && that.typeId == 3) {
				uni.showLoading({
					title: '正在准备保存'
				})
				return that.uploadH5(that.img)
			}
			if (that.typeId !== 3 && that.type == 'H5') {
				return uni.showToast({
					title: '请在APP端进行分享',
					icon: 'none',
					duration: 3000
				})
			}
			let timX = (that.width / 3) - 40
			// 背景 
			that.roundRect(ctx, 0, 0, that.width, that.height, 20, '#46454F')
			ctx.draw(true)
			// 字体
			ctx.setFillStyle('#ccc')
			ctx.setFontSize(13)
			ctx.fillText('存证时间：' + that.detailsList.buyTime, timX, 30)
			ctx.setFillStyle('#fff')
			ctx.setFontSize(15)
			ctx.fillText('￥', 20, 60 + that.heiImg + 33)
			ctx.setFontSize(23)
			ctx.fillText(that.detailsList.price, 35, 60 + that.heiImg + 33)
			ctx.setFontSize(15)
			ctx.fillText(that.detailsList.name, 20, 125 + that.heiImg)
			ctx.setFontSize(13)
			ctx.fillText('扫码探索更多数字藏品', 17, 180 + that.heiImg + that
				.versHei)
			setTimeout(() => {
				that.boxImg(ctx)
			}, 2000)
		},
		boxImg(ctx) {
			// 绘制图片
			let that = this
			uni.getImageInfo({
				src: that.detailsList.photo,
				success(res) {
					that.circleImgTwo(ctx, res.path, 20, 50, that.widImg, that.heiImg, 20)
					that.bigverseImg(ctx)
				}
			})
		},

		bigverseImg(ctx) {
			// 绘制bigverse
			let that = this
			uni.getImageInfo({
				src: '/static/imgs/public/add_bank.png',
				success(res) {
					ctx.drawImage(res.path, 15, 155 + that.heiImg, that.versWid, that
						.versHei)
					that.logoImg(ctx)
				}
			})
		},
		logoImg(ctx) {
			// 绘制logo
			let that = this
			uni.getImageInfo({
				src: '/static/imgs/public/add_bank.png',
				success(res) {
					ctx.drawImage(res.path, 150, that.heiImg, that.logoWid, that
						.logoHei)
					ctx.save()
					ctx.restore()
					//二维码
					that.roundRectQR(ctx, that.width - 90, that.height - 90, that.arWid, that.arHei, 20,
						'#fff')

					that.arCode(ctx)
				}
			})
		},
		arCode(ctx) {
			// 绘制二维码
			let that = this
			console.log(that.qrImg, 'qrImg');
			// uni.getImageInfo({
			// 	src: that.qrImg,
			// 	success(res) {
			ctx.drawImage(that.qrImg, that.width - 80, that.height - 80, that.arWid - 20, that.arHei - 20,)
			ctx.save()
			ctx.restore()
			uni.showLoading({
				title: '正在生成海报...'
			})
			ctx.draw(true)
			if (that.flagNum) {
				that.saves(that.typeId)
				that.flagNum = false
			} else {
				that.timer = setTimeout(() => {
					that.savePhone()
				}, 200)
			}

			// }
			// })
		},
		savePhone() { //判断分享还是保存
			let that = this
			// 保存到手机
			clearTimeout(this.timer)
			uni.canvasToTempFilePath({
				x: 0,
				y: 0,
				width: that.width,
				height: that.height,
				destWidth: (that.width - 50) * 2,
				destHeight: (that.height - 50) * 2,
				canvasId: 'canvasId',
				fileType: "png",
				success: function (res) {
					// 在H5平台下，tempFilePath 为 base64
					that.img = res.tempFilePath
					if (that.type == 'app') {
						that.share(res.tempFilePath)
					} else if (that.type == 'H5') {
						that.uploadH5(res.tempFilePath)

					}
				},
				fail() {
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					})
					uni.hideLoading()
				}
			})
		},
		share(tempFilePath) { //分享
			let that = this
			if (that.typeId == 0) {
				// 分享到微信
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					href: that.value,
					title: "Bigverse |" + that.detailsList.name,
					summary: that.detailsList.name,
					imageUrl: tempFilePath,
					success: function (res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function (err) {
						console.log("fail:" + JSON.stringify(err));
					},
					complete() {
						uni.hideLoading()
					}
				});
			} else if (that.typeId == 1) {
				// 分享到朋友圈
				uni.share({
					provider: "weixin",
					scene: "WXSceneTimeline",
					type: 2,
					imageUrl: tempFilePath,
					success: function (res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function (err) {
						console.log("fail:" + JSON.stringify(err));
					},
					complete() {
						uni.hideLoading()
					}
				});

			} else if (that.typeId == 2) {
				// 分享到QQ
				uni.share({
					provider: "qq",
					scene: "WXSceneSession",
					type: 0,
					href: that.value,
					title: "Bigverse |" + that.detailsList.name,
					summary: that.detailsList.name,
					imageUrl: tempFilePath,
					success: function (res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function (err) {
						console.log("fail:" + JSON.stringify(err));
					},
					complete() {
						uni.hideLoading()
					}
				});
			} else {
				// 保存到手机
				that.saveImg(tempFilePath)
			}
		},
		uploadH5(res) { //H5端保存图片到系统相册
			uni.hideLoading()
			let a = document.createElement('a')
			let event = new MouseEvent('click')
			a.download = '海报'
			a.href = res
			a.dispatchEvent(event)
			uni.hideLoading()
			uni.showToast({
				title: '已生成海报',
				icon: 'none'
			})
		},
		saveImg(tempFilePath) { //APP端保存图片到系统相册
			let that = this
			uni.saveImageToPhotosAlbum({
				filePath: tempFilePath,
				success(e) {
					uni.showToast({
						title: '已保存',
						icon: 'none'
					})
				},
				complete() {
					uni.hideLoading()
				}
			})
		},
		circleImgTwo(ctx, img, x, y, w, h, r) { //绘制圆角图片
			ctx.save();
			// 画一个图形
			if (w < 2 * r) r = w / 2;
			if (h < 2 * r) r = h / 2;
			ctx.beginPath();
			ctx.moveTo(x + r, y);
			ctx.arcTo(x + w, y, x + w, y + h, r);
			ctx.arcTo(x + w, y + h, x, y + h, r);
			ctx.arcTo(x, y + h, x, y, r);
			ctx.arcTo(x, y, x + w, y, r);
			ctx.closePath();
			// ctx.strokeStyle = '#FFFFFF'; // 设置绘制圆形边框的颜色
			ctx.stroke();
			ctx.clip();
			ctx.drawImage(img, x, y, w, h);
			ctx.restore();
		},
		roundRect(ctx, x, y, w, h, r, color) { //绘制圆角背景
			// 开始绘制
			ctx.beginPath()
			ctx.setFillStyle(color)
			// 左上角
			ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5)
			// border-top
			ctx.moveTo(x + r, y)
			ctx.lineTo(x + w - r, y)
			ctx.lineTo(x + w, y + r)
			// 右上角
			ctx.arc(x + w - r, y + r, r, Math.PI * 1.5, Math.PI * 2)
			// border-right
			ctx.lineTo(x + w, y + h - r)
			ctx.lineTo(x + w - r, y + h)
			// 右下角
			ctx.arc(x + w - r, y + h - r, r, 0, Math.PI * 0.5)
			// border-bottom
			ctx.lineTo(x + r, y + h)
			ctx.lineTo(x, y + h - r)
			// 左下角
			ctx.arc(x + r, y + h - r, r, Math.PI * 0.5, Math.PI)
			// border-left
			ctx.lineTo(x, y + r)
			ctx.lineTo(x + r, y)
			// 这里是使用 fill 还是 stroke都可以，二选一即可，但是需要与上面对应
			ctx.fill()
			ctx.closePath()
			// 剪切
			ctx.clip()
		},
		roundRectQR(ctx, x, y, w, h, r, color) { //绘制圆角盒子
			ctx.save();
			// 开始绘制
			ctx.beginPath()
			ctx.setFillStyle(color)
			// 左上角
			ctx.arc(x + r, y + r, r, Math.PI, Math.PI * 1.5)
			// border-top
			ctx.moveTo(x + r, y)
			ctx.lineTo(x + w - r, y)
			ctx.lineTo(x + w, y + r)
			// 右上角
			ctx.arc(x + w - r, y + r, r, Math.PI * 1.5, Math.PI * 2)
			// border-right
			ctx.lineTo(x + w, y + h - r)
			ctx.lineTo(x + w - r, y + h)
			// 右下角
			ctx.arc(x + w - r, y + h - r, r, 0, Math.PI * 0.5)
			// border-bottom
			ctx.lineTo(x + r, y + h)
			ctx.lineTo(x, y + h - r)
			// 左下角
			ctx.arc(x + r, y + h - r, r, Math.PI * 0.5, Math.PI)
			// border-left
			ctx.lineTo(x, y + r)
			ctx.lineTo(x + r, y)
			// 这里是使用 fill 还是 stroke都可以，二选一即可，但是需要与上面对应
			ctx.fill()
			ctx.closePath()
			// 剪切
			ctx.clip()
			ctx.restore();
		},
		close() {
			this.$emit('close', false)
		}
	}
}
</script>

<style lang="scss" scoped>
.puoupPoster {
	width: 100%;
	height: 100vh;
	background: #111;
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 55;

	.canvas {
		width: 100%;
		height: 70vh;
		opacity: 0;
		// position: absolute;
		// top: 0;
		// left: 0;
		// z-index: 33;
		// background: #46454F;
	}

	.box {
		width: 600rpx;
		height: 960rpx;
		background: #2B2A2F;
		box-shadow: 0rpx 6rpx 24rpx 1rpx rgba(74, 75, 99, 0.1);
		padding: 30rpx 40rpx 0 40rpx;
		box-sizing: border-box;
		border-radius: 30rpx;
		margin: 121rpx auto 140rpx auto;
		position: relative;

		.tim {
			color: #A6A6A6;
			text-align: center;
			font-weight: 400;
			font-size: 22rpx;
		}

		.img {
			width: 520rpx;
			height: 520rpx;
			background: #F5F5F5;
			border-radius: 24rpx;
			margin: 33rpx 0;
			overflow: hidden;

			>image {
				width: 100%;
				height: 100%;
			}
		}

		.price {
			font-weight: bold;
			font-size: 66rpx;
			color: #FFFFFF;

			>span {
				font-size: 42rpx;
			}
		}

		.tit {
			font-weight: bold;
			font-size: 28rpx;
			color: #FFFFFF;
			margin-top: 33rpx;
			margin-bottom: 30rpx;
		}

		.flex {
			display: flex;
			align-items: center;
			justify-content: space-between;

			>view:nth-child(1) {
				font-weight: 400;
				font-size: 24rpx;
				color: #A6A6A6;

				>view:nth-child(1) {
					width: 160rpx;
					height: 40rpx;
					margin-bottom: 28rpx;

					>image {
						width: 100%;
						height: 100%;
					}
				}
			}

			>view:nth-child(2) {
				width: 120rpx;
				height: 120rpx;
				background: #EDEDED;
				border-radius: 28rpx;
			}

			.qrCode {
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.log {
			width: 120rpx;
			height: 44rpx;
			position: absolute;
			bottom: 150rpx;
			left: 40rpx;

			>image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.btns {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin-bottom: 76rpx;

		>view {
			width: 112rpx;
			text-align: center;

			>view:nth-child(1) {
				width: 80rpx;
				height: 80rpx;
				margin: 0 auto;

				>image {
					width: 100%;
					height: 100%;
				}
			}

			>view:nth-child(2) {
				font-size: 28rpx;
				color: #FFFFFF;
				margin-top: 10rpx;
				text-align: center;
			}
		}
	}

	.close {
		width: 80rpx;
		height: 80rpx;
		margin: 0 auto;

		>image {
			width: 100%;
			height: 100%;
		}
	}
}
</style>