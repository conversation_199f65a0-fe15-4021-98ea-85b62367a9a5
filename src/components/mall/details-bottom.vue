<template>
	<!-- 底部按钮 -->
	<view class="details_bottom">
		<view class="bottomTips " v-if="isTips">
			<view class="tips" v-if="detailsList.status == 8 && timestampNum < 86400">
				<text>距公售开启还剩 </text>
				<view class="date">
					<u-count-down :timestamp="timestampNum" :autoplay="true" bg-color="rgba(31, 237, 240, 0.15)"
						color="#1FEDF0" separator="colon" separator-size="28" separator-color="#1FEDF0" font-size="24"
						@end="endTimestamp">
					</u-count-down>
				</view>
			</view>
			<view class="tips" v-if="detailsList.status == 9 && timestampNum < 86400">
				<text>距您可优先抢购还剩 </text>
				<view class="date">
					<u-count-down :timestamp="timestampNum" :autoplay="true" bg-color="rgba(31, 237, 240, 0.15)"
						color="#1FEDF0" separator="colon" separator-size="28" separator-color="#1FEDF0" font-size="24"
						@end="endTimestamp">
					</u-count-down>
				</view>
			</view>
			<view class="tips" v-if="detailsList.status != 8 && detailsList.status != 9">
				<text>{{ TipsText }} </text>
			</view>
		</view>

		<view class="bottom_btn">
			<view class="left">
				<view class="footer_bottom_price" v-if="!isMoreShow">
					￥{{ detailsList.price }}
				</view>
				<view class="item_icon" @click="more()" v-if="isMoreShow">
					<image src="@/static/imgs/mall/mall_operate.png" mode="widthFix"></image>
					<view class="number" style="margin-top: 12rpx">
						更多
					</view>
				</view>
			</view>

			<u-button border="none" :class="{ 'submit': true, 'disable': !isCanSale, 'Collection': isCollection }"
				:ripple='false' :hair-line='false' @click="creationSubmit()" v-if="isMoreShow">
				{{ buttonText }}
			</u-button>
			<u-button
				:class="{ 'submit': true, 'disable': !isCanSale, 'Collection': isCollection, 'opacity': isOpacity }"
				:disabled="!isCanSale" :ripple='false' :hair-line='false' @click="configSubmit()"
				v-if="detailsList.status != 10 && detailsList.isSeize != 1 && detailsList.status != 7">
				{{ btnText }}
			</u-button>
			<u-button :class="{ 'submit': detailsList.isSeize == 1, 'submit_huise': detailsList.isSeize == 0 }"
				:ripple='false' style="margin-left: 20rpx;" :hair-line='false' @click="robSubmit()"
				v-if="!isMoreShow && detailsList.status == 7">
				抢单
			</u-button>
		</view>
		<u-popup v-model="isMore" mode="bottom" @close="closeMore">
			<view class="mall_more_header">
				更多
				<view class="close" @click="isMore = false">
					<image src="@/static/imgs/mall/mall_colse.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="mall_more_ul">
				<view v-if="detailsList.isCreation == 1">
					<view class="li"
						v-if="detailsList.rickStatus == 1 && detailsList.saleStatus == 2 && detailsList.showStatus != 3"
						@click="openDown()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_dow.png" mode="widthFix"></image>
						</view>
						<text>停售作品</text>
					</view>
					<view class="li" v-if="detailsList.saleStatus != 3 && detailsList.isReal == 0"
						@click="nav_donation()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_send.png" mode="widthFix"></image>
						</view>
						<text>赠送</text>
					</view>
					<view class="li" @click="openCertificate()" v-if="detailsList.showStatus != 1">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_zs.png" mode="widthFix"></image>
						</view>
						<text>作品证书</text>
					</view>
					<view class="li" v-if="detailsList.saleStatus == 2" @click="openDestroy()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_del.png" mode="widthFix"></image>
						</view>
						<text>销毁作品</text>
					</view>
					<view class="li" @click="openSet()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_set.png" mode="widthFix"></image>
						</view>
						<text>设为头像</text>
					</view>
				</view>
				<view v-else>
					<view class="li"
						v-if="detailsList.rickStatus == 1 && detailsList.notSaleSign == 0 && detailsList.showStatus != 3"
						@click="openDown()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_dow.png" mode="widthFix"></image>
						</view>
						<text>停售作品</text>
					</view>
					<view class="li" v-if="detailsList.notSaleSign == 0 && detailsList.maxVersion > 1">
						<view class="icon" @click="isShow = true">
							<image src="@/static/imgs/mall/mall_more_price.png" mode="widthFix">
							</image>
						</view>
						<text @click="isShow = true">
							{{ detailsList.hasExpectPrice == 0 ? '设置预期售价' : '预期售价￥' + detailsList.expectPrice }}
						</text>
						<u-icon size="36" @click="isShowIntroduce = true"
							name="https://cdn-lingjing.nftcn.com.cn/image/20220722/2eaed29230f7d77f7230b4e59f111cee_32x32.png">
						</u-icon>
					</view>
					<!-- <view class="li" v-if="detailsList.notSaleSign==2" @click="nav_donation()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_send.png" mode="widthFix"></image>
						</view>
						<text>赠送</text>
					</view> -->
					<view class="li" @click="openSet()">
						<view class="icon">
							<image src="@/static/imgs/mall/mall_more_set.png" mode="widthFix"></image>
						</view>
						<text>设为头像</text>
					</view>
				</view>
			</view>
		</u-popup>
		<modalPop ref="modalPop" :tid="detailsList.tokenId" :detailsList="detailsList"
			:isCreation="detailsList.isCreation" :isMoreShow="isMoreShow" @downSucceed="downSucceed"
			@upSucceed="upSucceed"></modalPop>
		<setPresellPop :tid="detailsList.tokenId" :show.sync="isShow" :price.sync="expectedPrice" type="1"
			@succeed="expectedSucceed">
		</setPresellPop>
		<introducePop :show.sync="isShowIntroduce" :title="title" :introduce="introduce">
		</introducePop>

		<!-- 转售作品popup -->
		<u-popup v-model="sellPopup" mode="center" border-radius="30" duration='300'>
			<view class="collect_card">
				<view class="collect_card_head">
					<image class="bg1"
						src=https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png
						mode="">
					</image>
					<view class="title">转售作品</view>
				</view>
				<view class="collect_card_content">
					<view class="body">
						<view class="text">
							转售地址
						</view>
						<view class="input">
							<u-input v-model="sellAddress" placeholder="请输入转售地址" type="text" />
						</view>
					</view>
					<view class="body">
						<view class="text">
							转售价格
						</view>
						<view class="input">
							<u-input v-model="sellPrice" placeholder="请输入转售价格" type="number" />
						</view>
					</view>
				</view>
				<view class="btn" @click="openResell()">
					确定
				</view>
			</view>
		</u-popup>

		<!-- 设置支付密码popup -->
		<u-modal v-model="isPassword" border-radius="30" :show-title="false" width="630" :show-confirm-button="false">
			<view class="new-modal-content">
				<view class="title_bg">
					<view class="icon"></view>
					设置密码
				</view>
				<view class="modal-content">
					非常抱歉，您暂未设置支付密码， 请您先设置支付密码或选择其它支付方式。
				</view>
				<view class="showModal-btn">
					<view class="img_cancel" @click="isPassword = false">取消</view>
					<view class="img_reasale" @click="setPayPassword()">去设置</view>
				</view>
			</view>
		</u-modal>

		<pay-popup :popup-show.sync="isPasswordImport" order-type="" :title="passwordTitle" :message="passwordMsg"
			email="333" :mode="mode" @pay="password" @createSuccess="createSuccess" />

	</view>
</template>

<script>
import antiShake from "@/common/public.js";
import modalPop from "@/components/public/modalPop";
import setPresellPop from '@/components/public/setPresellPop.vue';
import introducePop from '@/components/public/introducePop.vue';
import payPopup from "@/components/payPopup/index.vue";

export default {
	data() {
		return {

			sellAddress: '',
			phoneInfo: '',

			sellPrice: '',
			passwordTitle: "确认转售",
			passwordMsg: "请输入余额支付密码，用于转售",
			isPasswordImport: false,
			isPassword: false,//设置交易密码弹唱
			sellPopup: false, // 转售作品popup
			listNum: 0, // 作品点赞
			isMore: false,
			type: 0,
			isShow: false,
			expectedPrice: 0,
			isShowIntroduce: false,
			introduce: "当您针对该藏品设置预期售价后，若该藏品其他版号真实成交价格达到您设定的价格，您将会收到系统推送，您可及时地进行寄售或改价。",
			title: "什么是预期售价",
			timestampNum: 0,
			mode: "pay",


		}
	},
	props: {
		isTips: {
			type: Boolean,
		},
		TipsText: {
			type: String,
			default: '',
		},
		detailsList: {
			type: Object,
			default: "",
		},
		likeCheck: {
			type: Boolean,
			default: false,
		},
		isShowLikeAnimation: {
			// type: Boolean,
		},
		isCanSale: {
			type: Boolean,
		},
		isCollection: {
			type: Boolean,
		},
		isUser: {
			type: Boolean,
		},
		isOpacity: {
			type: Boolean,
		},
		btnText: {
			type: String,
		},

	},
	created() {
		let _this = this
		uni.getSystemInfo({
			success: function (res) {
				_this.phoneInfo = res
				console.log('当前平台？？？？？', res.statusBarHeight)
				console.log("res---------", res)

			}
		});
		this.holdSeriesList()
		// this.userInfo()
	},
	computed: {
		btnTextBut() {
			console.log(this.btnText)
		},
		isMoreShow() {
			console.error(this.detailsList.createUser.userId)
			if (this.detailsList.createUser.userId == uni.getStorageSync("uid") || this.detailsList.ownerUser.userId == uni.getStorageSync("uid")) {
				return true
			} else {
				var data = new Date().valueOf()
				if (this.detailsList.openSaleTime) {
					var timestamp = Date.parse(new Date(this.detailsList.openSaleTime.replace(/-/g, "/")));
					this.timestampNum = parseInt(timestamp / 1000 - data / 1000)
				}
				return false
			}
		},
		buttonText() {
			console.log(this.timestampNum)
			if (this.detailsList.showStatus == 1) {
				this.type = 1
				return "查看作品证书"
			} else if (this.detailsList.showStatus == 2) {
				if (this.detailsList.isCreation == 1) {
					this.type = 2
					return "立即购买"
				} else {
					this.type = 4
					return "其他寄售"
				}
			} else if (this.detailsList.showStatus == 3 && this.detailsList.saleStatus != 3 && this.detailsList
				.rickStatus == 0 && this.detailsList.isCreation == 1) {
				this.type = 3
				return "寄售"
			} else if (this.detailsList.showStatus == 3 && this.detailsList.rickStatus == 0 && this.detailsList
				.isCreation == 0 && this.detailsList.notSaleSign == 0) {
				this.type = 3
				return "寄售"
			} else if (this.detailsList.isCreation == 0 && this.detailsList.notSaleSign == 2) {
				this.type = 5
				return "赠送"
			} else if (this.detailsList.notSaleSign == 3) {
				this.type = 6
				return "转售"
			} else {
				this.type = 4
				return "其他寄售"
			}
		}
	},
	methods: {
		downSucceed() {
			// this.popupList[this.sunIndex].sale = 0
		},
		upSucceed() {
			// this.popupList[this.sunIndex].sale = 1
		},
		destroySucceed() {
			// this.popupList.splice(this.sunIndex, 1);
		},
		createSuccess(psw) {
			uni.setStorageSync("isSetTradePassword", 1)
			this.isSetTradePassword = 1
			this.isPasswordImport = false
			// this.checkUp()
			// this.finishPay(psw)
		},
		password(e) {
			console.log(e)
			this.isPasswordImport = false
			this.payPassword = e
			this.resell()

		},
		//批量转售
		async openResell() {
			console.log(this.detailsList)
			console.log("phoneInfo----", this.phoneInfo)
			let payClient;
			this.isPasswordImport = true
		},
		//转售作品
		openSell() {
			this.isMore = false
			if (uni.getStorageSync('isSetTradePassword') == 1) {
				this.sellPopup = true
			} else {
				this.isPassword = true
			}
		},
		async resell() {
			console.log(this.detailsList)
			console.log("phoneInfo----", this.phoneInfo)
			console.log(this.payPassword)
			let paymentScene;

			if (this.phoneInfo.platform == 'ios') {
				paymentScene = 3
			} else if (this.phoneInfo.platform == "android") {
				paymentScene = 4
			} else if (this.phoneInfo.platform == "h5") {
				paymentScene = 1
			} else {
				paymentScene = 2
			}
			let data = {
				tid: this.detailsList.tokenId,
				contractAddress: this.sellAddress,
				price: this.sellPrice,
				paymentScene,
				tradePassword: this.payPassword
			}
			console.log(data)

			let res = await this.$api.CreateResale(data);
			console.log(res)
			this.sellPopup = false
			this.solePopupBox = false;
			this.sellAddress = ''
			this.sellPrice = ''
			if (res.status.code == 0) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});

			} else if (res.status.code == 9999) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			} else if (res.status.code == 510) {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000
				});
			}
		},
		//关注作品/收藏作品
		async Favorite() {
			console.log(this.detailsList.isCollect);
			let res = await this.$api.java_community_collect({
				tid: this.detailsList.tokenId,
			});
			if (res.status.code == 0) {
				if (this.detailsList.isCollect) {
					this.detailsList.isCollect = false;
					this.detailsList.collectCount--;
					uni.showToast({
						title: ' 取消关注作品成功',
						icon: 'none',
						duration: 3000,
					});
				} else {
					this.detailsList.isCollect = true;
					this.detailsList.collectCount++;
					uni.showToast({
						title: '关注作品成功',
						icon: 'none',
						duration: 3000,
					});
				}
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: 'none',
					duration: 3000,
				});
			}
		},
		addCheckLike() {
			this.listNum++;
			this.tid = this.detailsList.tokenId;
			this.detailsList.fondCount = Number(this.detailsList.fondCount) + 1;
			this.likeCheck = true;
			// this.isShowLikeAnimation = true;
			this.fclick(this.tid, "listNum");
		},
		fclick: antiShake._debounce(function (id, type) {
			this.clickLike(id, type)
		}, 1000),
		async clickLike(tid, type) {
			let res = await this.$api.java_communityLike({
				tid: tid,
				likeNum: this[type],
			});
			if (res.status.code == 0) {
				this[type] = 0
			} else {
				uni.showToast({
					title: res.status.msg,
					icon: "none",
					duration: 3000
				})
			}
		},
		// 立即购买
		configSubmit() {
			this.$emit('submit');
		},
		//抢单
		robSubmit() {
			this.$emit('robSubmit');
		},
		closeMore() {
			this.isMore = false
			this.$emit('more-show', this.isMore);
		},
		more() {
			this.isMore = true
			this.$emit('more-show', this.isMore);
		},
		// 调用设置头像
		openSet() {
			this.isMore = false
			this.$refs.modalPop.openPop('set');
		},
		// 调用查看证书
		openCertificate() {
			this.isMore = false
			this.$refs.modalPop.openPop('cert');
		},
		// 调用销毁作品
		openDestroy() {
			this.isMore = false
			this.$refs.modalPop.openPop('destroy');
		},
		// 跳转【一键转赠】
		nav_donation(item) {
			this.$Router.push({
				name: "donation",
				params: {
					tid: this.detailsList.tokenId
				}
			});
		},
		// 调用停售作品
		openDown() {
			this.isMore = false
			this.$refs.modalPop.openPop('down');
		},
		// 调用寄售售作品
		openUp() {
			this.isMore = false
			this.$refs.modalPop.openPop('up');
			this.$emit('hideWebView')
		},
		creationSubmit() {
			//type 1 证书  2购买  3寄售  4 同系列作品 
			if (this.type == 1) {
				this.openCertificate()
			} else if (this.type == 2) {
				this.configSubmit()
			} else if (this.type == 3) {
				// 寄售
				this.openUp()
			} else if (this.type == 5) {
				this.nav_donation()
			} else if (this.type == 4) {
				console.log("3333")
				this.nav_series_link()
			} else if (this.type == 6) {
				// 转售
				this.openSell()
			}
		},
		// 停售成功回调
		downSucceed() {
			this.detailsList.showStatus = 3
			this.detailsList.rickStatus = 0
			console.log("展示状态:" + this.detailsList.showStatus)
			console.log("上架状态:" + this.detailsList.rickStatus)
			console.log("交易状态:" + this.detailsList.saleStatus)
		},
		// 寄售成功回调
		upSucceed(e) {
			if (this.detailsList.isCreation == 0) {
				this.detailsList.price = e
			}
			if (this.detailsList.saleStatus == 3) {
				this.detailsList.showStatus = 2
			} else {
				this.detailsList.showStatus = 1
			}
			this.detailsList.rickStatus = 1

			console.log("展示状态:" + this.detailsList.showStatus)
			console.log("上架状态:" + this.detailsList.rickStatus)
			console.log("交易状态:" + this.detailsList.saleStatus)
		},
		expectedSucceed(e) {
			console.log(e)
			this.isShow = false
			if (e == 0) {
				this.detailsList.hasExpectPrice = 0
				this.detailsList.expectPrice = 0
			} else {
				this.detailsList.expectPrice = e
				this.detailsList.hasExpectPrice = 1
			}
		},
		endTimestamp() {
			this.$emit("update:isOpacity", false)
			this.$emit("update:isCanSale", false)
			this.$emit("getDetails")
		},
		nav_series_link() {
			this.$Router.push({
				name: "seriesList",
				params: {
					ctid: this.detailsList.seriesId
				}
			})
		},
		setPayPassword() {
			this.isPassword = false
			this.mode = "set"
			this.isPasswordImport = true
		},
	},
	components: {
		modalPop,
		setPresellPop,
		introducePop,
		payPopup
	},
}
</script>

<style lang="scss">
::v-deep .u-countdown-time {
	padding: 6rpx !important;
	// font-weight: 600 !important;
}

::v-deep .u-btn--bold-border {
	border: none !important;
	// font-weight: 600 !important;
}

::v-deep .u-input__input {
	color: #63EAEE !important;
	font-weight: 600;
	padding: 0px 10rpx;
	border-radius: 7rpx;

	&::placeholder {}
}

::v-deep .u-input__placeholder {
	font-size: 14px;
	/* 修改placeholder字体大小 */
	color: #00E4FF !important;

	/* 修改placeholder字体颜色 */
}


.collect_card {
	width: 630rpx;
	height: 506rpx;
	background: #35333E;
	border-radius: 30rpx;
	font-weight: bold;
	font-size: 32rpx;
	color: #000000;
	font-style: normal;
	text-transform: none;


	.collect_card_head {
		width: 100%;
		height: 80rpx;
		// border: 1rpx solid red;
		position: relative;

		.bg1 {
			width: 250rpx;
			height: 8rpx;
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			margin: auto;
		}

		.title {
			text-align: center;
			line-height: 80rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #fff;
			font-style: normal;
			text-transform: none;
		}




	}

	.collect_card_content {
		padding: 10rpx 50rpx 50rpx 50rpx;

		.body {
			display: flex;
			align-items: center;
			padding-top: 30rpx;

			.text {
				
				font-weight: 400;
				font-size: 28rpx;
				color: #fff;

				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.input {
				color: #63EAEE;
				font-weight: 600;
				font-size: 17px;
				background-color: #25232D;

				width: 380rpx;
				height: 90rpx;
				border-radius: 14rpx;
				padding-left: 30rpx;
				padding-top: 10rpx;
				padding-right: 20rpx;
				margin-left: 20rpx;
			}
		}
	}

	.btn {
		width: 520rpx;
		height: 90rpx;
		background: linear-gradient(90deg, #EF91FB 0%, #40F8EC 100%);
		border-radius: 45rpx;
		color: #121212;
		line-height: 90rpx;
		text-align: center;
		margin: auto;
	}
}

.details_bottom {
	position: fixed;
	bottom: 0rpx;

	.bottomTips {
		width: 100%;
		height: 68rpx;
		line-height: 76rpx;
		font-size: 24rpx;
		color: #F9F9F9;
		text-align: center;

		.tips {
			height: 68rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			background-color: #191919;

			.date {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-left: 10rpx;
			}
		}
	}

	.count_down {
		background-color: #2A2F31;
		padding: 20rpx 40rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #F9F9F9;
		line-height: 36rpx;

		.time {
			color: var(--active-color);
			font-weight: Semibold;
			padding: 0 10rpx;
		}
	}

	.bottom_btn {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100vw;
		height: 128rpx;
		background-color: var(--main-bg-color);
		left: 0rpx;
		padding: 20rpx 40rpx;
		padding-bottom: 30rpx;

		.left {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			margin-right: 20rpx;

			.item_icon {
				// margin-right: 40rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				image {
					width: 44rpx;
					height: 44rpx;
				}

				.number {
					font-size: 24rpx;
					transform: scale(0.85);
					color: #F9F9F9;
					margin-top: 10rpx;
					width: 80rpx;
					text-align: center;
				}
			}
		}

		.submit {
			width: 100%;
			height: 80rpx;
			background: linear-gradient(132deg, #F6AAF2 0%, #C0BDF4 31%, #54D5F2 65%, #00FBEF 100%);
			color: #121212;
			font-size: 28rpx;
			font-weight: 600;
			border-radius: 2rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 50rpx;
		}

		.submit_huise {
			width: 100%;
			height: 80rpx;
			background: var(--main-bg-color);
			color: #fff;
			font-size: 28rpx;
			font-weight: 600;
			border-radius: 2rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 50rpx;
			border: 1px solid #fff !important;
		}

		.disable {
			background: #1E1E1E;
			color: #616161;
		}

		.Collection {
			background: linear-gradient(132deg, #F6AAF2 0%, #C0BDF4 31%, #54D5F2 65%, #00FBEF 100%);
			color: #121212;
		}

		.opacity {
			color: #121212;
			background: linear-gradient(132deg, #F6AAF2 0%, #C0BDF4 31%, #54D5F2 65%, #00FBEF 100%) !important;
			opacity: 0.35 !important;
		}
	}
}

.like-btn {
	width: 44rpx;
	height: 44rpx;
	position: relative;
	margin: 0 auto;

	.like,
	.liked {
		width: 44rpx;
		height: 44rpx;
		position: relative;
		background-size: contain;
		background-repeat: no-repeat;
	}
}

::v-deep .u-drawer-bottom {
	background-color: #35333E !important;
}

.mall_more_header {
	height: 100rpx;
	line-height: 100rpx;
	color: #FFFFFF;
	width: 100%;
	text-align: center;
	position: relative;
	font-size: 32rpx;

	.close {
		position: absolute;
		right: 30rpx;
		top: 26rpx;
		z-index: 99;

		image {
			width: 42rpx;
			height: 42rpx;
		}
	}
}

.mall_more_ul {
	padding: 40rpx;

	.li {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		color: #FFFFFF;
		padding-bottom: 40rpx;
		border-bottom: 1rpx solid #282828;
		margin-bottom: 30rpx;
		font-size: 28rpx;
		font-weight: 600;

		.icon {
			margin-right: 20rpx;

			image {
				width: 72rpx;
			}
		}

		text {
			margin-right: 20rpx;
		}
	}

	.li:last-child {
		border-bottom: none;
	}
}

.footer_bottom_price {
	min-width: 200rpx;
	font-size: 44rpx;
	color: #00E4FF;
	font-weight: 600;
}
.title_bg {
		font-size: 34rpx;
		font-weight: 600;
		width: 240rpx;
		position: relative;
		text-align: center;
		margin: 0 auto;
		margin-bottom: 10rpx;
		color: var(--default-color1);

		.icon {
			position: absolute;
			left: 0rpx;
			top: 20rpx;
			width: 240rpx;
			height: 8rpx;
			background-image: url(https://cdn-lingjing.nftcn.com.cn/image/20240305/cd180fe9aa5bb072a52815affe447d27_240x8.png);
			background-size: 100%;
		}
	}

	.webView_class {
		height: 750rpx;
	}
	.new-modal-content {
		padding: 35rpx 40rpx;
		background-color: #34323D;
		.success_img {
			display: flex;
			justify-content: center;
			align-items: center;
	
			image {
				width: 160rpx;
				height: 160rpx;
			}
		}
	
		.modal-content{
			padding:0rpx 0rpx 30rpx 0rpx;
			border-bottom:1rpx solid #53505D;
			font-size:28rpx;
			color:#fff;
			.title{
				font-size:34rpx;
				font-weight:600;
			}
			.msg_view{
				font-size:28rpx;
				line-height:34rpx;
				margin-top:30rpx;
				.active_msg{
					font-size:24rpx;
					color:#63EAEE;
					margin-top:10rpx;
				}
			}
			
		}
		.showModal-btn {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top:30rpx;
			
			>view {
				width: 236rpx;
				height: 80rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx; 
				font-weight: bold;
				border-radius: 14rpx;
				color:rgba(255, 255, 255, 0.5);
			}
			.img_cancel {
				border: 1px solid rgba(255, 255, 255, 0.5);
			}
			.img_reasale {
				color:var(--default-color2);
				background: var(--primary-button-color);
			}
			&.center{
				justify-content: center;
				.img_reasale {
					width:300rpx;
				}
			}
		}
	}
</style>
