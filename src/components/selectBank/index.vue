<template>
	<u-popup v-model="showPop" mode="bottom">
		<view class="modal_head">
			<view class="left">
				<image
					src="https://cdn.yanjie.art/image/********/563e60262c16b4a0b952a2c8cbc362d7_26x50.png"
					mode="widthFix"></image>
			</view>
			选择银行卡
		</view>
		<view class="modal_Body">
			<view class="bank_ul">
				<scroll-view scroll-y="true" class="scroll_view_style">
					<view class="li" v-for="(item, index) in bankList" :key="index" @click="checkBank(item, index)">
						<view class="left_icon">
							<image :src="item.icon" mode="widthFix"></image>
						</view>
						<view class="right_text">
							<text>{{ item.bankName }}（**** {{ item.bankCardNumber }}）</text>
							<view class="img" v-if="isBanCheck == index">
								<image src="https://cdn.yanjie.art/image/********/711832c9b7ea313eecab313092a2f81b_36x38.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="li" @click="nav_bank()">
					<view class="left_icon">
						<image src="https://cdn.yanjie.art/image/********/bc4bf131f00f0600a05fc7b79d662396_36x36.png" mode="widthFix"></image>
					</view>
					<view class="right_text">
						<text>{{ type == 0 ? '添加银行卡' : '去绑定新银行卡并支付' }}</text>
					</view>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
/**
 * 使用 $refs.selectBank.showPop=true 调用
 * @property {Array} bankList 用户银行卡列表;
 * @event select 选择银行卡后触发;
 */
export default {
	props: {
		bankList: {
			type: Array,
			default: []
		},
		type: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			showPop: false,
			isBanCheck: false
		}
	},
	methods: {
		nav_bank() {
			this.$emit("navbank");
			this.showPop = false;
		},
		checkBank(item, index) {
			this.isBanCheck = index;
			// 将选择数据传给父级
			this.$emit("select", item, index);
			this.showPop = false
		},
	}
}
</script>

<style lang="scss" scoped>
::v-deep .u-drawer__scroll-view {
	background-color:#0E0A06 ;
}

.modal_head {
	font-weight: bold;
	font-size: 34rpx;
	color: #FFFFFF;
	text-align: center;
	height: 120rpx;
	line-height: 120rpx;
	border-bottom: 1rpx solid #4B3E27;

	.left {
		position: absolute;
		left: 40rpx;
		top: 20rpx;
		
		image {
			width: 26rpx;
		}
	}
}

.modal_Body {
	padding: 0 24rpx;

	.bank_ul {
		display: inline-block;
		border-radius: 12rpx;
		overflow: hidden;
		width: 100%;

		.scroll_view_style {
			width: 100%;
			max-height: 600rpx;
		}

		.li {
			width: 100%;
			padding: 20rpx 24rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			border-bottom: 1rpx solid  #4B3E27 !important;

			.left_icon {
				margin-right: 10rpx;
				width: 42rpx;

				image {
					width: 42rpx;
					border-radius: 50%;
				}
			}

			.right_text {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 0rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF !important;

				.img {
					image {
						width: 30rpx;
					}
				}
			}
		}

		.li:last-child {}
	}
}
</style>
