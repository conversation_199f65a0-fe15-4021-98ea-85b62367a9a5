<template>
	<view class="charts-box">
		<qiun-data-charts type="mix" :opts="opts" :chartData="chartData" :ontouch="true" />
	</view>
</template>

<script>
	export default {
		props: ['quantityList', 'categories'],
		data() {
			return {
				chartData: {},
				opts: {
					show: false,
					enableScroll: true,
					legendText: '',
					dataLabel: false,
					legend: {
						show: false,
					},
					xAxis: {
						scrollShow: true,
						itemCount: 5,
						fontSize:10,
						fontColor:'#fff',
					},
					yAxis: {
						disabled: true,
						disableGrid: true,
						showTitle: true,
						data: [{
							position: "left",
							title: "折线",
						},
						{
							position: "right",
							title: "柱状图",
						},]
					},
					extra: {
						// mix: {
						// 	column: {
						// 		width: 20
						// 	}
						// }
					}
				}
			};
		},
		mounted() {
			this.getServerData();
		},
		methods: {
			getServerData() {
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
					//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
					let res = {
						categories: this.categories,

						series: [{
								name: "交易量",
								index: 1,
								type: "column",
								color: "#5B7273",
								data: this.quantityList

							},
							{
								name: "交易量",
								type: "line",
								color: '#73C0DE',
								data: this.quantityList

							},

						]
					};
					this.chartData = JSON.parse(JSON.stringify(res));
				}, 500);
			},
		}
	};
</script>

<style scoped>
	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 100%;
		height: 100%;
	}
</style>