<template>
    <u-popup v-model="showPopup" mode="center">
        <view :style="{
            backgroundColor: backgroundColor,
            width: width + 'rpx'
        }" class="container">
            <view class="title">{{ title }}</view>
            <view class="content">{{ content }}</view>
            <u-button hover-class="none" :style="{
                backgroundColor: buttonBackgroundColor,
                color: buttonTextColor,
                fontSize: buttonFontSize + 'rpx'
            }" @click="onButtonClick">
                {{ buttonText }}
            </u-button>
            <view style="height: 20rpx;"></view>
            <u-button hover-class="none" border="none" type="info" :style="{
                backgroundColor: CancelbuttonBackgroundColor,
                color: CancelbuttonTextColor,
                fontSize: CancelbuttonFontSize + 'rpx'
            }" @click="cancelClick">
                {{ CancelbuttonText }}
            </u-button>
        </view>
    </u-popup>
</template>

<script>
export default {
    props: {
        CancelbuttonBackgroundColor: {
            type: String,
            default: '#e6e6e6'
        },
        CancelbuttonFontSize: {
            type: String,
            default: '26'
        },
        CancelbuttonTextColor: {
            type: String,
            default: '#121212'
        },
        CancelbuttonText: {
            type: String,
            default: '取消'
        },
        width: {
            type: String,
            default: '680'
        },
        backgroundColor: {
            type: String,
            default: '#ffffff'
        },
        title: {
            type: String,
            default: '设置密码'
        },
        content: {
            type: String,
            default: '您当前还未设置登录密码和支付密码。'
        },
        buttonText: {
            type: String,
            default: '立即设置'
        },
        buttonBackgroundColor: {
            type: String,
            default: '#007aff'
        },
        buttonTextColor: {
            type: String,
            default: '#ffffff'
        },
        buttonFontSize: {
            type: String,
            default: '26'
        },
        onButtonClick: {
            type: Function,
            default: () => { }
        },
        cancelClick: {
            type: Function,
            default: () => { }
        },
        showPopup: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        closePopup() {
            this.$emit('update:showPopup', false);
        },
    }
}
</script>

<style scoped>
.container {
    padding: 34rpx;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.content {
    font-size: 14px;
    margin-bottom: 20px;
}

button {
    height: 80rpx;
    width: 100%;
    border: none;
    border-radius: 5px;
    text-align: center;
}
</style>