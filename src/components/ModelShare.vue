<template>
  <van-popup
    v-model:show="show"
    closeable
    position="bottom"
    style="background-color: #1E1E1E;"
  >
    <div class="model_header">分享到</div>
    <div class="model_content" >
      <div class="type_send">
        <template  v-if="havePoster">
          <div class="item_poster" @click="getPoster(4)">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220709/9e0294d2e1b4ba08bb123a8909cde775_96x148.png" class="item_bg" />
          </div>
          <div class="item_poster" @click="getPoster(2)">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220709/691cb06396d1083a592211739ad295d2_96x148.png" class="item_bg" />6
          </div>
          <!-- <div class="item_poster" @click="getPoster(5)">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220709/c52647607a79ff27f575766ac8854e83_96x148.png" class="item_bg" />
          </div> -->
          <div class="item_poster" @click="getPoster(1)">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220709/9305ef8d630ff6f6a7121437c8e5c8a1_96x148.png" class="item_bg" />
          </div>
          <div class="item_poster" @click="getPoster(3)">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220709/29a35806d23216181874338e5714282a_96x148.png" class="item_bg" />
          </div>
        </template>
        <div class="item copy-qb" v-if="haveCopy" @click="copy">
          <div class="bg">
            <img src="https://cdn-lingjing.nftcn.com.cn/image/20220610/2b76898b23d9b3c020010edea81ec274_96x96.png" />
          </div>
          <span>复制链接</span>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
/**
 * @description 分享组件
 * @property {Boolean} popupShow 控制弹窗显隐
 * @property {Boolean} havePoster 控制生成海报按钮显隐
 * @property {Boolean} haveCopy 控制是否显示复制链接按钮xxxx
 * @property {Object} info 分享海报的详情
 */
import { defineProps, computed, defineEmits, ref } from 'vue';
import Clipboard from 'clipboard';
import { Toast } from 'vant';
import { getShortLink } from '@/api/shortLink';
import { userInfo } from '@/api/appusercenter.js';

const props = defineProps({
  popupShow: {
    type: Boolean,
    default: false,
  },
  havePoster: {
    type: Boolean,
    default: false,
  },
  haveCopy: {
    type: Boolean,
    default: true,
  },
  info: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(['update:popupShow']);
const show = computed({
  get () {
    return props.popupShow;
  },
  set (value) {
    emit('update:popupShow', value);
  },
});
// 分享链接
const finallyUrl = ref('');
// 获取用户信息
const getUser = async () => {
  await userInfo({
    userId: '',
  }).then(res => {
    localStorage.setItem('uid', res.userId);
    localStorage.setItem('contract_address', res.contractAddress);
  });
};
getUser().then(() => {
  const jumpUrl = `${window.location.href.split('?')[0]}?share_contract_address=${localStorage.getItem('contract_address')}`;
  getShortLink({ longLink: jumpUrl }).then(res => {
    finallyUrl.value = res.shortUrl;
  });
});
// 复制
const copy = () => {
  const clipboard = new Clipboard('.copy-qb', {
    text: () => {
      return finallyUrl.value;
    },
  });
  clipboard.on('success', () => {
    Toast('链接复制成功,分享给朋友吧');
    clipboard.destroy();
  });
  clipboard.on('error', () => {
    Toast('该浏览器不支持自动复制~');
    clipboard.destroy();
  });
};
/**
 * 生成海报xxx
 * @param {String} channel 分享渠道 1-微博 2-小红书 3-抖音 4-微信 5-B站 6-酷站
 */
const getPoster = (channel) => {
  console.log(channel);
};
</script>

<style lang="scss" scoped>
.model_header {
  height: px2vw(100px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 32rpx;
  font-weight: 600;
  color: #F9F9F9;
  font-family: PingFangSC-Semibold, PingFang SC;
  letter-spacing: 4rpx;
}
.model_content {
  padding: px2vw(40px) 0 px2vw(80px);
  .type_send{
    display: flex;
    align-items: center;
    margin-bottom: px2vw(30px);
    width: 100%;
    .item_poster {
      width: 20%;
      text-align: center;
      .item_bg {
        width: px2vw(96px);
        height: px2vw(148px);
      }
    }
    .item{
      width: 33%;
      text-align: center;
      .bg{
        width: 100%;
        border-radius: 50%;
        margin-bottom: px2vw(28px);
        display: flex;
        justify-content: center;
        align-items: center;
        img{
          width: px2vw(96px);
        }
      }
      span{
        font-size: px2vw(24px);
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}
</style>
