<template>
	<view class="container">
		<!-- <button class="button" type="primary" @click="toggle('center')"><text class="button-text">居中</text></button> -->
		<uni-popup ref="popup" background-color="#fff" type="center"  :mask-click="false">
		<!-- <uni-popup ref="popup" background-color="#fff" type="center" :mask-click="false"/> -->
			<view class="popup-content">
				<view class="header">
					<image src="../../static/login/logo.png"></image>
					<text>新版本更新</text>
				</view>
				<view class="content">
					<h4>更新功能</h4>
					<view class="text_view">
						<view v-for="(item,index) in verdata.versionDesc">{{item}}</view>
					</view>
				</view>
				<button class="button" type="default" @click="gouper">
					<text class="button-text">{{uptext}}</text>
				</button>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	/**
	 *  版本获取，强更
	 * 使用方法：
	 * 		<fpop ref="fpop"></fpop>
	 * 
	 * import fpop from '@/components/force-updates/force-updates.vue'
	 * 
	 * components: {
			fpop
		},
	 * 	 * 
	 * openuper(){
				console.log('父组件open')	
				this.$refs.fpop.toggle('center')
			},
	 */
	// 	
	export default {
		data() {
			return {
				verdata: {
					currentVersion: '',
					downloadUrl: '',
					updateStatus: 0,
					versionDesc: '',
				},
				htmlte: '',
				uptext: '立即更新'
			}
		},
		created() {
			// #ifdef APP
				this.cuuur()
			// #endif
		},
		// watch: {
		// 	verdata: {
		// 		deep: true,
		// 		handler(nVal, oVal) {
		// 			console.log("a11111--- change", nVal)
		// 		}
		// 	}
		// },

		mounted() {
			
		},
		methods: {
			toggle() {
				this.$refs.popup.open()
			},
			gouper() {
				if(uni.getSystemInfoSync().platform == 'ios'){
					 plus.runtime.launchApplication({
					      action: `https://apps.apple.com/us/app/nftcn/id1605702361`
					 });
					return false
				}
				let _this = this
				console.log("去下载", _this.verdata.downloadUrl)
				// let url = 'https://cdn-lingjing.nftcn.com.cn/h5/xCase/android/XCase.apk'
				let url = _this.verdata.downloadUrl
				var dtask = plus.downloader.createDownload(url, {},
					function(d, status) {
						console.log(d)
						if (status == 200) {
							plus.runtime.install(plus.io.convertLocalFileSystemURL(d.filename), {}, {}, function(
								error) {
								uni.showToast({
									title: '安装失败',
									mask: false,
									duration: 1500
								});
							})
						} else {
							uni.showToast({
								title: '更新失败',
								mask: false,
								duration: 1500
							});
						}
					});
				try {
					dtask.start(); // 开启下载的任务
					var prg = 0;
					var showLoading = plus.nativeUI.showWaiting("正在下载"); //创建一个showWaiting对象 
					dtask.addEventListener('statechanged', function(
						task,
						status
					) {
						// 给下载任务设置一个监听 并根据状态  做操作
						switch (task.state) {
							case 1:
								_this.uptext = "正在下载"
								// showLoading.setTitle("正在下载");
								break;
							case 2:
								showLoading.setTitle("已连接到服务器");
								break;
							case 3:
								prg = parseInt(
									(parseFloat(task.downloadedSize) /
										parseFloat(task.totalSize)) *
									100
								);
								_this.uptext = prg + "%"
								// showLoading.setTitle("  正在下载" + prg + "%  ");
								break;
							case 4:
								plus.nativeUI.closeWaiting();
								//下载完成
								break;
						}
					});
				} catch (err) {
					plus.nativeUI.closeWaiting();
					uni.showToast({
						title: '更新失败-03',
						mask: false,
						duration: 1500
					});
				}
				// this.$refs.popup.close()
			},
			cuuur() {
				var _this = this
				uni.getSystemInfo({
					success: function(res) {
						let v = res.appVersion
						let type = res.osName === 'ios' ? '1' : '8'
						console.log("当前版本", res.appVersion, res.osName)
						if (res) {
							if (type == '1' || type == '8') {
								_this.getVersion(v, type)
							}
						} else {
							uni.showToast({
								title: '获取当前版本失败',
								icon: 'none',
								duration: 3000
							});
						}
					}
				})
			},
			async getVersion(v, type) {
				console.log(v)
				let res = await this.$api.getAllDTOList({
					type: type,
					userVersion: v
				});
				console.log(res)
				console.log("版本", res.result)
				console.log(res.result.versionDesc.split("\n"))
				res.result.versionDesc=res.result.versionDesc.split("\n")
				this.verdata = res.result
				if (res.status.code == 0) {
					if (res.result.updateStatus == 3) {
						this.$refs.popup.open()
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			isMaskClick(){
				return false
			}

		}
	}
</script>

<style lang="scss">
	.popup-content {
		width: 520rpx;
		min-height: 600rpx;
		background: #fff;
		border-radius: 8rpx;
		position: relative;

		.header {

			height: 150rpx;
			width: 100%;
			background: #333333;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-wrap: wrap;
			padding: 16rpx;

			image {
				width: 340rpx;
				height: 59.65rpx;
			}

			text {
				padding: 5rpx 0 0 18rpx;
				font-size: 32rpx;
				font-weight: 300;
				letter-spacing: 12.8rpx;
				line-height: 42rpx;
				color: rgba(255, 255, 255, 1);
				text-align: center;
				font-family: HarmonyOS Sans SC;

			}
		}

		.content {
			padding: 26rpx 60rpx 80rpx;
			line-height: 50rpx;
			height: 550rpx;
			overflow-y: auto;
			overflow: hidden;
			.text_view{
				height:300rpx;
				overflow:auto;
			}
			>h4 {
				font-weight: bold;
			}
			>view{
				font-size: 24rpx;
				letter-spacing: 0px;
				line-height: 35rpx;
				color:#333333;
				margin-bottom:20rpx;
			}
		}

		.button {
			position: absolute;
			bottom: 30rpx;
			left: 50%;
			transform: translate(-50%, -50%);
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80rpx;
			width: 350rpx;
			background: linear-gradient(180deg, #EF91FB 0%, #40F8EC 100%);
			font-size: 32rpx;
			font-weight: 300;
			letter-spacing: 12.8rpx;
			line-height: 42rpx;
		}
	}
</style>