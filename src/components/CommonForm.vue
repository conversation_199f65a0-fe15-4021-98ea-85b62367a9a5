<script>
/**
 * @description: 表单组件
 * @author: jay
 * @date: 2022-07-10
 * @update: 2022-07-26
 * @param {boolean} showIndex 是否显示序号
 * @param {object} data 表单数据
 * @param {array} schema 表单架构 SchemaItem[]
 * @param {string} labelWidth 表单标签宽度
 * @param {object} rules 表单验证规则
 * @param {boolean} isEdit 是否可编辑
 * @param {function} submit 提交方法
 * @param {function} reset 重置方法
 */

// interface SchemaItem {
//   label: string; // 显示标题
//   field: string; // form 数据对应字段名称, type 为 'action' 时不必传
//   slot?: string; // slot 插槽名称, 自定义插槽时使用。外部使用 v-slot:[slot] 指令渲染。
//   type?: 'input' | 'number-input' | 'select' | 'textarea' | 'radio' | 'action' | 'img' | 'datetime' | 'datetimerange' | 'date'; // 显示类型，不传默认显示 input 框。
//   isValidat?e: boolean; // 是否验证，type 为 'action' 时生效。
//   rules: Array<rules>; // 表单验证规则，具体规则参数见 element 文档
//   options: Array<{ label: string, value: string }>; // select 类型下的选项，type 为 'select' | 'radio' 时生效。
//   show: {
//     relationField: sting, // 关联字段，当关联字段的值等于 show.value 时显示本条 form item。
//     value: Array<number | string>, // 关联字段值，设置当前项关联字段显示的值。
//   }; // 是否显示
//   exclude: Array<'reset' | 'submit' | 'back'>; // 排除按钮显示。type 为 action 时生效。
//   limit: number; // 上传图片数量，type 为 'img' 时生效。默认为 1，为 0 时不限制。
//   pickerOptions: object; // 日期选择器配置，type 为 'datetime' | 'datetimerange' | 'date' 时生效。
// }

import ImgUploader from "@/components/ImgUploader";
import {
  mapActions
} from "vuex";

export default {
  name: "CommonTable",
  components: {
    ImgUploader,
  },
  props: {
    isEdit: {
      // 是否可编辑
      type: Boolean,
      default: true,
    },
    data: {
      // form 数据
      type: Object,
      required: true,
    },
    schema: {
      // form 结构, 参考 SchemaItem
      type: Array, // Array<SchemaItem>
      required: true,
    },
    labelWidth: {
      // label 宽度
      type: String,
    },
    rules: {
      // 表单校验规则
      type: Object,
      default: () => ({}),
    },
    submit: {
      // 提交方法
      type: Function,
    },
    reset: {
      // 重置方法
      type: Function,
    },
    loading: {
      // 是否加载中
      type: Boolean,
      default: false,
    },
    submitText: {
      // 提交方法
      default: "提交",
    },
    isBack: {
      // 提交方法
      type: Boolean,
      default: false,
    },
    labelPosition: {
      // label 位置
      type: String,
      default: "right",
    },
    isHD: { // 是否高清
      type: Boolean,
      default: false
    },
    isSeleteChange: {//是否监听
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {},
  mounted() { },
  methods: {
    ...mapActions("d2admin/page", ["close"]),
    // 验证表单
    async validate(field) {
      try {
        return await this.$refs.form.validate();
      } catch (error) {
        return false;
      }
    },
    // 重置表单
    resetForm(formName) {
      this.$refs.form.resetFields();
    },
    // type 为 action 时的表单提交
    async onSubmit(isValidate = true) {
      const valid = isValidate ? await this.validate() : true;
      !valid && this.$message.error("验证失败，请检查提交数据");
      if (valid) {
        this.submit && this.submit();
      }
    },
    // type 为 action 时的表单重置
    onReset() {
      this.resetForm();
      this.reset && this.reset();
    },
    back() {
      if (this.isBack) {
        this.$emit("nav_back");
      } else {
        const {
          fullPath
        } = this.$route;
        this.close({
          tagName: fullPath
        });
        this.$router.go(-1);
      }
    },
    async querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      this.searchNew(queryString);
      let results = [];
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.results;
        cb(results);
      }, 1000);
    },
    async querySearchAsyncNum(queryString, cb) {
      this.searchNewSeries(queryString);
      let results = [];
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        results = this.resultsNum;
        cb(results);
      }, 1000);
    },

    handleSelect(item) {
      console.log(item);
    },
    async searchNew(str) {
      this.results = [];
      if (str) {
        let res = await this.$api.searchPgc({
          name: str,
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.results.push({
                value: `${item.name}(${item.ctid})`,
              });
            });
            console.log(this.results);
          }
        }
      }
    },
    async searchNewSeries(str) {
      this.resultsNum = [];
      if (str) {
        let res = await this.$api.seriesList({
          name: str,
          pageNum: 1,
          pageSize: 20,
        });
        if (res.status.code == 0) {
          if (res.result.list != null) {
            res.result.list.forEach((item) => {
              this.resultsNum.push({
                value: `${item.name}(流通数：${item.activeNum})`,
              });
            });
            console.log(this.results);
          }
        }
      }
    },
    changeSelect(e) {
      this.$emit("changeSelect", e);
    }
  },
};
</script>

<template>
  <div>
    <el-form :model="data" ref="form" :rules="rules" size="mini" :label-width="labelWidth" v-loading="loading"
      :label-position="labelPosition">
      <div v-for="item in schema" :key="item.field">
        <el-form-item v-if="!item.show ||
      (item.show &&
        item.show.value.includes(data[item.show.relationField]))
      " :prop="item.field" :label="item.label" :rules="item.rules" :label-width="item.labelWidth">
          <template v-if="item.slot">
            <slot :name="item.slot" :item="item"></slot>
          </template>
          <template v-else-if="!item.type ||
      item.type === 'input' ||
      item.type === 'number-input' ||
      item.type === 'textarea'
      ">
            <template v-if="!isEdit">{{ data[item.field] }}</template>
            <template v-else>
              <el-input v-if="item.type === 'number-input'" :style="{ width: item.width ? `${item.width}px` : '300px' }"
                :placeholder="item.placeholder ? item.placeholder : '请输入内容'
      " type="number" v-model.number="data[item.field]" :maxlength="item.maxlength"
                :minlength="item.minlength" :disabled="item.disabled" :show-word-limit="item.showWordLimit" clearable>
              </el-input>
              <el-input v-else :type="item.type === 'input' ? 'text' : item.type"
                :style="{ width: item.width ? `${item.width}px` : '300px' }" :placeholder="item.placeholder ? item.placeholder : '请输入内容'
      " v-model="data[item.field]" :maxlength="item.maxlength" :minlength="item.minlength" :rows="4"
                :disabled="item.disabled" :show-word-limit="item.showWordLimit" clearable>
              </el-input>
            </template>
          </template>
          <template v-else-if="item.type === 'img'">
            <template v-if="!isEdit">
              <template v-if="Array.isArray(data[item.field])">
                <el-image v-for="img in data[item.field]" :key="item.field + img"
                  style="width: 100px; height: 100px; margin-right: 4px" :src="img"
                  :preview-src-list="data[item.field]">
                </el-image>
              </template>
              <template v-else>
                <el-image v-if="data[item.field]" style="width: 100px; height: 100px" :src="data[item.field]"
                  :preview-src-list="[data[item.field]]">
                </el-image>
                <span v-else>---</span>
              </template>
            </template>
            <ImgUploader v-else :value.sync="data[item.field]" :isHD="item.isHD" :multigraph="item.multigraph"
              :limit="item.limit">
            </ImgUploader>
          </template>
          <template v-else-if="item.type === 'radio'">
            <el-radio-group :disabled="!isEdit || item.disabled" v-model="data[item.field]">
              <el-radio v-for="radio in item.options" :key="radio.value" :label="radio.value">{{ radio.label }}
              </el-radio>
            </el-radio-group>
          </template>
          <template v-else-if="item.type === 'select'">
            <el-select  v-model="data[item.field]" :disabled="!isEdit || item.disabled" :placeholder="item.placeholder"
              @change="changeSelect">
              <el-option v-for="select in item.options" :key="select.value" :label="select.label" :value="select.value">
              </el-option>
            </el-select>
          </template>
          <template v-else-if="item.type === 'datetimerange' ||
      item.type === 'datetime' ||
      item.type === 'date'
      ">
            <el-date-picker :disabled="!isEdit || item.disabled" v-model="data[item.field]" :type="item.type"
              :placeholder="item.placeholder" :picker-options="item.pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss.SSS" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </template>
          <template v-else-if="item.type === 'search'">
            <el-autocomplete style="width: 340px" v-model="data[item.field]" :fetch-suggestions="querySearchAsync"
              placeholder="请输入系列名/系列ID" @select="handleSelect" >
            </el-autocomplete>
          </template>
          <template v-else-if="item.type === 'search_num'">
            <el-autocomplete style="width: 340px" v-model="data[item.field]" :fetch-suggestions="querySearchAsyncNum"
              placeholder="请输入系列名/系列ID" @select="handleSelect" >
            </el-autocomplete>
          </template>
          <template v-else-if="item.type === 'switch'">
            <el-switch v-model="data[item.field]" :active-text="`${data[item.field] == 1 ? '已展示' : '已隐藏'
      }更多设置`" active-value="1" inactive-value="0">
            </el-switch>
            <!-- <el-autocomplete style="width:340px;"  v-model="data[item.field]"
                :fetch-suggestions="querySearchAsync" placeholder="请输入系列名/系列ID" @select="handleSelect" >
              </el-autocomplete> -->
          </template>
          <template v-else-if="item.type === 'colorPicker'">
               <el-color-picker v-model="data[item.field]"></el-color-picker>
          </template>
          <template v-else-if="item.type === 'inputNumber'">
            <el-input-number v-model="data[item.field]" :step="1" :min="1" :disabled="!isEdit"></el-input-number>
          </template>
          <template v-else-if="item.type === 'inputNumberRange'">
            <el-input-number v-model="data[item.field]" :step="1" :min="1" :disabled="!isEdit"></el-input-number>
            <span style="margin: 0px 20px">至</span>
            <el-input-number v-model="data[item.field2]" :step="1" :min="1" :disabled="!isEdit"></el-input-number>
          </template>
          <template v-else-if="item.type === 'action' && isEdit">
            <!-- <el-button v-if="!item.exclude ||
      (item.exclude && !item.exclude.includes('back'))
      " @click="back()" type="" >返回</el-button> -->
            <el-button v-if="!item.exclude ||
      (item.exclude && !item.exclude.includes('reset'))
      " @click="onReset()" type="primary" >重置</el-button>
            <el-button v-if="!item.exclude ||
      (item.exclude && !item.exclude.includes('submit'))
      " @click="onSubmit(item.isValidate)" type="primary" >{{ submitText }}</el-button>
          </template>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped></style>
