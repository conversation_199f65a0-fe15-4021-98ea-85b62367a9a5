<script>
/**
 * @description: 选择器组件
 * @author: jay
 * @date: 2022-07-04
 * @update: 2022-07-04
 * @param {array} querySchema 查询架构，参数详情如下：interface querySchemaItem
 * @event onSubmit 查询参数变化时触发
 * @event onReset 重置参数时触发
 * @event onExport 导出列表时触发
 */

// interface querySchemaItem = {
//   type: 'input' ｜ 'select' ｜ 'datetimerange'
//   dataType?: string
//   label: string
//   field: string
//   field2: string // type为 datetimerange 时，field2为结束时间字段名称
//   placeholder?: string
//   options?: Array<{ label: string; value: string | number }>
// }
export default {
  name: 'CommonQuery',
  props: {
    querySchema: {
      type: Array,
      required: true
    },
    showExport: {
      type: Boolean,
      default: false
    },
    showSubmit: {
      type: Boolean,
      default: true
    },
    showReset: {
      type: Boolean,
      default: true
    },
    showRefresh: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
  },
  data() {
    return {
      data: {}
    }
  },
  methods: {
    onSubmit() {
      this.$emit('onSubmit', JSON.parse(JSON.stringify({ ...this.data, ...this.transformTime() })))
    },
    onReset() {
      this.data = {}
      this.$emit('onReset')
    },
    onExport() {
      this.$emit('onExport', JSON.parse(JSON.stringify({ ...this.data, ...this.transformTime() })))
    },
    onRefresh() {
      this.$emit('onRefresh', JSON.parse(JSON.stringify({ ...this.data, ...this.transformTime() })))
    },
    transformTime() {
      const datetimerange = {}
      this.querySchema.forEach(item => {
        if (item.type === 'datetimerange' && this.data[item.field]) {
          const [start, end] = this.data[item.field]
          datetimerange[item.field] = `${start}.000`
          datetimerange[item.field2] = `${end}.000`
        }
      })
      return datetimerange
    }
  }
}
</script>

<template>
  <div>
    <el-form :inline="true" :model="data">
      <el-form-item v-for="item in querySchema" :label="item.label" :key="item.field">
        <el-select  v-if="item.type === 'select'" v-model="data[item.field]" :placeholder="item.placeholder || '请选择'"
          clearable :multiple="item.multiple" size="mini">
          <el-option v-for="option in item.options" :key="option.value" :label="option.label"
            :value="option.value"></el-option>
        </el-select>
        <el-input v-else-if="item.type === 'input'" :placeholder="item.placeholder || '请输入'" v-model="data[item.field]"
          :type="item.dataType" clearable size="mini"></el-input>
        <el-date-picker size="mini" clearable v-else-if="item.type === 'datetimerange'" v-model="data[item.field]"
          type="datetimerange" :format="item.format ? item.format : 'yyyy-MM-dd HH:mm:ss'"
          :value-format="item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        <div v-else-if="item.type === 'inputrange'" class="inputrange_div">
          <el-input size="mini" v-model="data[item.field]" :placeholder="item.placeholder || '请输入'"></el-input>
          <span>至</span>
          <el-input size="mini" v-model="data[item.field2]" :placeholder="item.placeholder2 || '请输入'"></el-input>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" size="mini" v-if="showSubmit">查询</el-button>
        <el-button type="primary" @click="onReset" size="mini" v-if="showReset">清除</el-button>
        <el-button type="primary" @click="onExport" size="mini" v-if="showExport">导出</el-button>
        <el-button type="primary" @click="onRefresh" size="mini" v-if="showRefresh">刷新</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.inputrange_div {
  width: 310px;
  display: flex;
  justify-content: flex-start;

  span {
    margin: 0px 20px;
  }
}

</style>
