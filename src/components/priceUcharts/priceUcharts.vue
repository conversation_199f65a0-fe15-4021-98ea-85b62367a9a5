<template>
	<view class="charts-box">
		<qiun-data-charts type="candle" background='rgba(70, 49, 79, 0.1)' :opts="opts" :chartData="chartData"
			:disableScroll="true" :ontouch="true" :onzoom="true" />
	</view>
</template>

<script>
	export default {
		props: ['series', 'categories', 'currentSmallVal'], //series数据    categories时间 
		data() {
			return {
				chartData: {},
				opts: {
					color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666"],
					rotate: false,
					rotateLock: false,
					padding: [15, 15, 0, 15],
					dataLabel: true,
					enableScroll: false,
					enableMarkLine: false,
					tooltip: {
					    showCategory: true,
						borderRadius:10,
						showCategory:true,
					},
					legend: {
						show: false
					},
					xAxis: {
						scrollShow: true,
						fontSize:10,
						fontColor:'#fff'
					},
					yAxis: {
						data: [{
							position: 'right',
							disabled:false,
							type:'value',
							fontSize:10,
							gridColor:'#fff',
							fontColor:'#fff'
						}],
						background: ' rgba(70, 49, 79, 0.1)',
					},
					

				}
			};
		},
		mounted() {
			this.getServerData();
		},
		watch: {
			currentSmallVal() {
				this.series = []
				this.categories = []
				this.getServerData()
			}
		},
		methods: {
			getServerData() {
				console.log(this.categories, '时间');
				console.log(this.series, '价格');
				//模拟从服务器获取数据时的延时
				setTimeout(() => {
						//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
						let res = {
							categories: this.categories,
							series: [{
								name: "上证指数",
								data: this.series, // 开盘,收盘,最低,最高
							}],
						};
						this.chartData = JSON.parse(JSON.stringify(res));
					},500);
			},
		}
	};
</script>

<style scoped>
	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 100%;
		height: 100;
		/* transform: rotateY(180deg); */

	}
</style>