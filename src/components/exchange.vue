<template>
	<van-popup v-model:show="show" class="popup_body_bottom" position="bottom" border-radius="18">
		<div class="bodyPop_exchange">
			<div class="title">
				<div class="colse" @click="closePopup">
					<img src="https://nftcns.oss-cn-shanghai.aliyuncs.com/image/20240219/dc022946fdd0d661192b699cce40ee76_200x200.png" alt="" srcset="" />
				</div>
				<div class="coin">
					<img src="@/asstes/gameHome/B_BAO.png" alt="" srcset="" />
				</div>
				兑换B宝
			</div>
			<div class="input">
				<input type="number" v-model="batchNum"/>
				<img class="add" @click="btn_add()" src="@/asstes/gameHome/btn_add.png" alt="" srcset="" />
				<img class="sub" @click="btn_sub()" src="@/asstes/gameHome/btn_sub.png" alt="" srcset="" />
				<div class="max" @click="btn_max()">全部</div>
				<div class="min" @click="btn_min()">最少</div>
			</div>
			<div class="tips">
				<p class="t1">1张</p>
				<img class="card" src="@/asstes/gameHome/FLY_CARD.png" alt="" srcset="" />
				<p class="t2">可以兑换1个</p>
				<img class="coin" src="@/asstes/gameHome/B_BAO.png" alt="" srcset="" />
			</div>
			<div class="myCard">
				<p class="t1">我的</p>
				<img class="card" src="@/asstes/gameHome/FLY_CARD.png" alt="" srcset="" />
				<p class="t2" >{{ myCoinNum }}</p>
			</div>
			<div class="btn">
				<div class="btn_confirm">确认兑换</div>
			</div>
			
		</div>
	</van-popup>
	<!-- <payPassword v-model:pay-password="isPayPassword" @pay="paySubmit"></payPassword> -->
</template>

<script setup>
// import payPassword from '@/components/payPassword.vue'
// const isPayPassword = ref(false);
import { getFlyCardCount } from '@/api/waliangge';
import { flyCardExchange } from '@/api/gameHome';
import { Toast } from 'vant';
import { goLogin, goAuthentication } from '@/utils/jump';
import { ref,onMounted,computed,defineProps,defineEmits} from 'vue';
const emit = defineEmits(['update:showBoard']);
const props = defineProps({
  showBoard: {
    type: Boolean,
    default: false,
  },
});
const batchNum = ref('1')
const myCoinNum = ref('0')

const show = computed({
  get() {
	console.log(props.showBoard)
    return props.showBoard;
  },
  set(value) {
    emit('update:showBoard', value);
  },
});
const closePopup = () => {  
	console.log("触发")
  emit('update:showBoard', false); // 发送一个事件给父组件，告诉它更新showBoard为false  
}; 
const btn_add=()=>{
	if(batchNum.value<myCoinNum.value){
		batchNum.value++
	}
} 
const btn_sub=()=>{
	if(batchNum.value>1){
		batchNum.value--
	}
}
const btn_max=()=>{
	batchNum.value = myCoinNum.value;
} 
const btn_min=()=>{
	batchNum.value = 1;
}  
const flyCardCount = () => {
       
	getFlyCardCount({}).then((res) => {
            console.log(res)
			myCoinNum.value = res.count;
		}).catch((err) => {
			console.log(err)
			if (err.code === 1002) {
				Toast(err.msg);
				setTimeout(() => {
					sessionStorage.clear();
					goLogin();
				}, 2000)
			} else {
				Toast(err.msg);
			}
		});
	};
	const clickExchange = () => {
		flyCardExchange({}).then((res) => {
            console.log(res)
            document.querySelector('.balance_text').textContent = res;
		}).catch((err) => {
			console.log(err)
            if (err.code === 1002) {
				Toast(err.msg);
				setTimeout(() => {
					sessionStorage.clear();
					goLogin();
				}, 2000)
			} else {
				Toast(err.msg);
			}
		});
	};
	onMounted(() => {
        // flyCardCount();
    });
</script>

<style lang="scss" >
	::v-deep .popup_body_bottom {
		height:px2vw(600px);
		width:100%;
		// background-color: #fff;
	}
	.bodyPop_exchange{
		height:px2vw(600px);
		width:100%;
		background-color: #35333E;
		// padding-top:px2vw(20px);
		.title{
			text-align: center;
			position: relative;
			height:px2vw(100px);
			line-height: px2vw(100px);
			color:#fff;
			// margin-bottom:px2vw(40px);
			font-weight: bold;
			.colse{
				position: absolute;
				right:px2vw(40px);
				top:px2vw(10px);
				img{
					width:px2vw(40px);
				}
			}
			.coin{
				position: absolute;
				left:px2vw(270px);
				top:px2vw(10px);
				img{
					width:px2vw(40px);
				}
			}
			
		}
	}
	.input{
		width:100%;
		display: flex;
  		justify-content: center;
		align-items: center; /* 垂直居中 */
		input{
			margin: 0 auto;
			width:25%;
			height:px2vw(80px);
			line-height: px2vw(80px);
			text-align: center;
			background-color:#46454F;
			border:1px solid #46454F;
			border-radius:px2vw(50px);
			color:#63EAEE;
			font-size:px2vw(40px);
			font-weight: bold;
		}
		.add{
			position: absolute;
			right: 100px;
			width:px2vw(55px); 
		}
		.sub{
			position: absolute;
			left: 100px;
			width:px2vw(55px); 
		}
		.max{
			position: absolute;
			right: px2vw(30px);
			width: px2vw(150px);;
			height: px2vw(70px);
			border-radius: 60px;
			mix-blend-mode: normal;
			background: linear-gradient(101.31deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
			text-align: center;
			font-family: PingFangSC-Medium, PingFang SC;
			letter-spacing: px2vw(2px);
			font-size: px2vw(25px);
			line-height: px2vw(70px);
			color: #0a0a0a;
			font-weight: bold;
		}
		.min{
			position: absolute;
			left: px2vw(30px);
			width: px2vw(150px);;
			height: px2vw(70px);
			border-radius: 60px;
			border: #ffffff 1px solid;
			mix-blend-mode: normal;
			text-align: center;
			font-family: PingFangSC-Medium, PingFang SC;
			letter-spacing: px2vw(2px);
			font-size: px2vw(25px);
			line-height: px2vw(70px);
			color: #ffffff;
			font-weight: bold;
		}
	  }
	  .tips{
		width:100%;
		margin-top: 15px;
		display: flex;
  		justify-content: center;
		align-items: center; /* 垂直居中 */
		.coin{
			// position: absolute;
			// right: 100px;
			width:px2vw(55px); 
		}
		.card{
			// position: absolute;
			// left: 100px;
			width:px2vw(60px); 
		}
		p{
			color: white;
			opacity: 0.5;

		}
	  }
	  .myCard{
		width:100%;
		margin-top: 15px;
		display: flex;
  		justify-content: center;
		align-items: center; /* 垂直居中 */
		.card{
			// position: absolute;
			// left: 100px;
			width:px2vw(60px); 
		}
		.t1{
			color: white;
			font-weight: bold;
		}
		.t2{
			color: #63EAEE;
			font-weight: bold;
		}
	  }
	  .btn{
		width:100%;
		margin-top: 40px;
		display: flex;
  		justify-content: center;
		align-items: center; /* 垂直居中 */
		.btn_confirm{
			position: absolute;
			width: px2vw(500px);;
			height: px2vw(100px);
			border-radius: 60px;
			mix-blend-mode: normal;
			background: linear-gradient(101.31deg, rgba(239, 145, 251, 1) 0%, rgba(64, 248, 236, 1) 100%);
			text-align: center;
			font-family: PingFangSC-Medium, PingFang SC;
			letter-spacing: px2vw(2px);
			font-size: px2vw(30px);
			line-height: px2vw(100px);
			color: #0a0a0a;
			font-weight: bold;
		}
	  }
	  
</style>
