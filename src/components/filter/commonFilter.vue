<script>
	/**
	 * @description: 过滤组件
	 * @author: jay
	 * @date: 2022-07-04
	 * @update: 2022-07-04
	 * @param {array} options 选项数组
	 * filterOptions: [
	 *  {
	 *    title: '作品状态', // 标题
	 *    field: 'value',  // 绑定 selected 字段名
	 *    clear: true,     // 是否可清除
	 *    options: [{
	 *      label: 'test', // 选项标签
	 *      value: 1        // 选项值
	 *    }]
	 *  },
	 *  {
	 *    title: '价格区间',
	 *    field: 'value2',
	 *    range: true,  // 是否显示价格区间
	 *    options: [  // 价格区间 value 需为一个数组
	 *    {label: '0-500', value: [0, 500]},
	 *    {label: '501-2000', value: [501, 2000]},
	 *    {label: '2000以上', value: [2000, '']}
	 *    ]
	 *  },
	 *],
	 * @param {string} title 标题
	 * @param {boolean} showMask 显示遮罩
	 * @param {object} selected 选中一些的值
	 * @param {function} confirm 确认回调
	 * @param {function} reset 重置回调
	 * @event {function} click 点击标签触发
	 */
	import commonFilterItem from "@/components/filter/commonFilterItem";
	export default {
		components: {
			commonFilterItem,
		},
		props: {
			selected: {
				// 选中的值
				type: Object,
				required: true,
			},
			options: {
				type: Array,
				required: true,
			},
			confirm: {
				type: Function,
			},
			reset: {
				type: Function,
			},
			title: {
				type: String,
				default: "筛选",
			},
			showMask: {
				// 显示遮罩
				type: Boolean,
				default: true,
			},
			labelHighlight: {
				// 是否高亮显示标签
				type: Boolean
			},
			domain: {
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				active: false, // 是否高亮标题
				optionsVisible: false,
				filterContainerPosition: {
					top: 0,
					left: 0,
				},
			};
		},
		computed: {},
		methods: {
			toggleHighlight() {
				// console.error(this.selected)
				this.active = Object.values(this.selected).some(item => {
					console.log(item)
					if (Array.isArray(item)) {
						return item.some(i => i)
					} else {
						return item || item === 0
					}
				})
				console.log(this.active)
			},
			toggleVisible() {
				this.$emit("click");
				this.optionsVisible = !this.optionsVisible;
				if (this.optionsVisible) {
					this.$nextTick(() => {
						this.positionContent();
					});
				}
			},
			onReset() {
				this.reset && this.reset();
				this.toggleVisible();
				this.active = false
				console.log(this.options)
			},
			onConfirm() {
				this.confirm && this.confirm(true);
				this.toggleVisible();
				this.toggleHighlight()
			},
			item_click(e) {
				this.$emit('homingResult', e)
				if (this.domain) {
					switch (e) {
						case 'number':
							this.options[1] = {
								title: '特征', //  标题
								field: "subTag", // 绑定 selected 字段名
								clear: true, // 是否可清除
								options: [{
									label: '三数字', // 选项标签
									value: 3 // 选项值
								}, {
									label: '四数字', // 选项标签
									value: 4 // 选项值
								}, {
									label: '五数字', // 选项标签
									value: 5 // 选项值
								}, {
									label: '多数字', // 选项标签
									value: 'more' // 选项值
								}, {
									label: '生日', // 选项标签
									value: 'birth' // 选项值
								}]
							}
							break;
						case 'alphabet':
							this.options[1] = {
								title: '特征', //  标题
								field: "subTag", // 绑定 selected 字段名
								clear: true, // 是否可清除
								options: [{
									label: '三字母', // 选项标签
									value: 3 // 选项值
								}, {
									label: '四字母', // 选项标签
									value: 4 // 选项值
								}, {
									label: '五字母', // 选项标签
									value: 5 // 选项值
								}, {
									label: '多字母', // 选项标签
									value: 'more' // 选项值
								}]
							}
							break;
						case 'chinese':
							this.options[1] = {
								title: '特征', //  标题
								field: "subTag", // 绑定 selected 字段名
								clear: true, // 是否可清除
								options: [{
									label: '一位汉字', // 选项标签
									value: 1 // 选项值
								}, {
									label: '两位汉字', // 选项标签
									value: 2 // 选项值
								}, {
									label: '三位汉字', // 选项标签
									value: 3 // 选项值
								}, {
									label: '多位汉字', // 选项标签
									value: 'more' // 选项值
								}]
							}
							break;
						case 'repeat':
							this.options[1] = {
								title: '特征', //  标题
								field: "subTag", // 绑定 selected 字段名
								clear: true, // 是否可清除
								options: [{
									label: '三叠', // 选项标签
									value: 3 // 选项值
								}, {
									label: '四叠', // 选项标签
									value: 4 // 选项值
								}, {
									label: '五叠', // 选项标签
									value: 5 // 选项值
								}, {
									label: '多叠', // 选项标签
									value: 'more' // 选项值
								}, {
									label: 'ABBA', // 选项标签
									value: 'abba' // 选项值
								}, {
									label: 'AABB', // 选项标签
									value: 'aabb' // 选项值
								}, {
									label: 'ABAB', // 选项标签
									value: 'abab' // 选项值
								}]
							}
							break;
						case 'mixed':
							this.options[1] = {
								title: '特征', //  标题
								field: "subTag", // 绑定 selected 字段名
								clear: true, // 是否可清除
								options: [{
									label: '三杂', // 选项标签
									value: 3 // 选项值
								}, {
									label: '四杂', // 选项标签
									value: 4 // 选项值
								}, {
									label: '五杂', // 选项标签
									value: 5 // 选项值
								}, {
									label: '多杂', // 选项标签
									value: 'more' // 选项值
								}]
							}
							break;
						default:
					}
				}
			},
			positionContent() {
				const query = uni.createSelectorQuery().in(this);
				query
					.select(".common-filter")
					.boundingClientRect((data) => {
						this.filterContainerPosition.top = `${data.height}px`;
						this.filterContainerPosition.left = `-${data.left}px`;
					})
					.exec();
			},
		},
	};
</script>

<template>
	<view class="common-filter">
		<view class="common-filter-label" :class="{ active: labelHighlight || active }" @click="toggleVisible">
			{{ title }}
			<!-- 	<image v-if="labelHighlight || active" class="common-filter-icon"
				src="../../static/imgs/public/filter-active.png" mode="widthFix"></image> -->
			<!-- v-else -->
			<image class="common-filter-icon" src="@/static/imgs/public/select.png" mode="widthFix">
			</image>
		</view>
		<view v-if="optionsVisible" :style="filterContainerPosition" class="common-filter-container">
			<common-filter-item v-for="item in options" :key="item.title" :title="item.title" :options="item.options"
				:selected.sync="selected[item.field]" :clear="item.clear" :range="item.range"
				@item_click="item_click"></common-filter-item>
			<view class="common-filter-buttons">
				<button @click="onReset" class="common-button common-filter-reset">
					重置
				</button>
				<button @click="onConfirm" class="common-button common-filter-confirm">
					确认
				</button>
			</view>
		</view>
		<view v-if="optionsVisible" @click="toggleVisible" class="common-filter-mask"></view>
	</view>
</template>

<style lang="scss" scoped>
	.common-filter {
		font-family: PingFangSC-Medium, PingFang SC;
		position: relative;
		display: inline-block;
		font-size: 24rpx;
		line-height: 24rpx;
		color: var(--info-front-color);

		.common-filter-label {
			display: flex;
			align-items: center;
			padding: 16rpx 20rpx;
			font-weight: bold;
			font-size: 24rpx;
			color: #35333E;

			&.active {
				color: var(--active-color);
			}

			.common-filter-icon {
				margin-left: 8rpx;
				width: 34rpx;
			}
		}

		.common-filter-container {
			position: absolute;
			top: 60rpx;
			color: var(--main-front-color);
			background-color: var(--main-bg-color);
			width: 100vw;
			z-index: 20;
		}

		.common-filter-buttons {
			display: flex;
			justify-content: space-between;
			margin-top: 32rpx;
			padding: 32rpx;
			border-top: 2rpx solid #1C1C1C;

			.common-button {
				padding: 24rpx 80rpx;
				border: 2rpx solid #999;
				border-radius: 44rpx;
				font-size: 32rpx;
				line-height: 32rpx;
				color: var(--main-front-color);
				background-color: var(--main-bg-color);
			}

			.common-filter-confirm {
				font-weight: 500;
				color: #1E1E1E;
				border: none;
				width: 430rpx;
				background: var(--primary-button-color);
				color: var(--main-bg-color);
			}
		}

		.common-filter-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: #00000066;
			z-index: 1;
			width: 100vw;
			height: 100vh;
		}
	}
</style>