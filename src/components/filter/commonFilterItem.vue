<script>
	/**
	 * @description:
	 */
	import CommonSelect from '@/components/select/commonSelect'
	export default {
		components: {
			CommonSelect
		},
		props: {
			selected: { // 选中的值
				type: String | Number,
				required: true
			},
			title: {
				type: String,
				required: true
			},
			options: {
				type: Array,
				required: true
			},
			clear: { // 点击选项如已选就清空选项
				type: Boolean,
				default: false
			},
			range: { // 价格区间
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				value: '',
				rangeMin: '',
				rangeMax: '',
			}
		},
		computed: {

		},
		methods: {
			buttonActive(item) {
				// console.log(this.selected)
				if (this.range && this.selected) {
					return this.selected[0] === item.value[0]
				} else {
					return false
				}
			},
			onClick(value) {
				console.log(value)
				if (this.selected === value) {
					console.log(this.selected)
					this.clear && this.$emit('update:selected', this.range ? ['', ''] : '')
				} else {
					this.$emit('update:selected', value)
					this.$emit('item_click', value)
				}
				if (this.range) {
					this.rangeMin = value[0]
					this.rangeMax = value[1]
				}
			},
		},
		watch: {
			rangeMin(value) {
				this.$emit('update:selected', [value, this.selected[1]])
			},
			rangeMax(value) {
				this.$emit('update:selected', [this.selected[0], value])
			}
		}
	}
</script>

<template>
	<view class="filter-item">
		<view class="filter-item-title">
			{{ title }}
		</view>
		<view v-if="range" class="filter-range">
			<input v-model="rangeMin" placeholder="最低价"
				placeholder-style="color:#616161;fontSize:24rpx;textAlign:center;" class="filter-range-input"
				:class="(rangeMin || rangeMin === 0) && 'filter-range-input__active'" type="number">
			-
			<input v-model="rangeMax" placeholder="最高价"
				placeholder-style="color:#616161;fontSize:24rpx;textAlign:center;" class="filter-range-input"
				:class="(rangeMax || rangeMax === 0) && 'filter-range-input__active'" type="number">
		</view>
		<view class="filter-options">
			<button v-for="(item, index) in options" :key="index" @click="onClick(item.value)"
				class="filter-options-button"
				:class="(selected === item.value || buttonActive(item)) && 'filter-options-button__active'">{{ item.label }}</button>
		</view>
	</view>
</template>

<style lang="scss" scoped>
	.filter-item {
		padding: 36rpx 40rpx 12rpx 40rpx;
		font-size: 28rpx;
		color: var(--main-front-color);

		.filter-item-title {
			margin-bottom: 28rpx;
			line-height: 32rpx;
		}

		.filter-range {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			.filter-range-input {
				padding: 8rpx 20rpx;
				border: 2rpx solid var(--secondary-front-color);
				border-radius: 50rpx;
				text-align: center;

				&:first-child {
					margin-right: 16rpx;
				}

				&:last-child {
					margin-left: 16rpx;
				}

				&.filter-range-input__active {
					border-color: var(--active-color);
					color: var(--active-color);
				}
			}
		}

		.filter-options {
			display: flex;
			flex-wrap: wrap;

			.filter-options-button {
				margin: 0;
				padding: 12rpx 20rpx;
				border: 2rpx solid rgb(20, 61, 62);
				border-radius: 28rpx;
				min-width: 204rpx;
				color: #fff;
				background-color: transparent;
				line-height: 32rpx;
				font-size: 24rpx;
				margin-bottom: 24rpx;
				border: 1rpx solid #fff;

				&.filter-options-button__active {
					color: #1fedf0;
					border-color: #1fedf0;
					background-color: rgb(20, 61, 62);
				}

				&:not(:first-child).filter-options-button {
					margin-left: 24rpx;
				}

				&:nth-child(4n).filter-options-button {
					margin-left: 0rpx;
				}

				&:nth-child(7n).filter-options-button {
					margin-left: 0rpx;
				}
			}
		}
	}
</style>