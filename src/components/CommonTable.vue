<script>
/**
 * @description: 表格组件
 * @author: jay
 * @date: 2022-07-10
 * @update: 2022-07-26
 * @param {boolean} showIndex 是否显示序号
 * @param {array} tableData 表格数据
 * @param {array} tableSchema 表格架构 tableSchemaItem[]
 * @param {string} height 表格高度
 * @param {boolean} isEdit 是否可编辑
 * @param {boolean} loading 是否加载中
 */

// interface SchemaItem {
//   label: string; // 显示标题
//   field: string; // prop 名称
//   slot?: string; // slot 插槽名称, 自定义插槽时使用。外部使用 v-slot:[slot] 指令渲染。
//   headerSlot?: string; // header slot 自定义表头插槽名称
//   type?: 'tag'; // 显示类型
//   width?: string; // 列宽度
//   fixed?: string; // 列固定
//   tagMap?: { // 标签映射 type 为 'tag' 时定义显示映射的结果，key：对应映射的值，value：标签映射对象。
//    [key: string | number | 'default']: {
//      tagType?: 'success' ｜ 'danger' ｜ 'warning' ｜ 'info', // 标签类型
//      label: string // 标签显示文本
//    } | string;
//   }
//   edit: {  // 编辑配置，暂时不可用，请无视
//     disabled: boolean, // 是否禁用编辑
//     placeholder: string, // 编辑框占位符
//     type: 'input', // 编辑类型
//     isVisible: boolean,  // 是否可见
//     editButton: boolean  // 是否显示编辑按钮
//   }
// }
import ImgUploader from '@/components/ImgUploader'
export default {
  name: 'CommonTable',
  components: {
    ImgUploader
  },
  props: {
    showIndex: { // 是否显示序号
      type: Boolean,
      default: true
    },
    showSelection: { // 是否显示多选框
      type: Boolean,
      default: false
    },
    tableData: { // 表格数据
      type: Array,
      required: true
    },
    multipleSelection: { // 多选框选中数据
      type: Array,
      default: () => []
    },
    selectionChange: { // 多选框选中数据变更事件
      type: Function
    },
    tableSchema: { // 表格结构, 参考 SchemaItem
      type: Array,
      required: true
    },
    height: { // 表格高度
      type: String
    },
    isEdit: { // 是否可编辑
      type: Boolean,
      default: false
    },
    loading: { // 是否加载中
      type: Boolean,
      default: false
    },
    rows: { // 多选框选中数据
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      tagMap: { // el-tag 类型映射
        success: 'success',
        danger: 'danger',
        warning: 'warning',
        info: 'info'
      }
    }
  },
  computed: {

  },
  mounted() {

  },
  /**
   * @description: 监听父组件传过来的rows是否有值，触发是否选中多选框
   * @param {array} rows 选中数据
   */
  watch: {
    rows: {
      handler(val) {
        this.setSelection()
      },
      immediate: true
    }
  },
  methods: {
    // 监听多选框选中数据变更事件
    handleSelectionChange(val) {
      this.selectionChange && this.selectionChange(val)
      console.log(val)
      this.$emit('update:multipleSelection', val)
    },
    setSelection() {
      this.$nextTick(() => {
        if (this.rows.length > 0) {
          this.$refs.table.clearSelection();
          this.rows.forEach(row => {
            console.log(row)
            this.$refs.table.toggleRowSelection(row, true)
          })
        }
      })
    },

    toggleEditStatus(item) {
      if (item.edit) {
        item.edit.isVisible = !item.edit.isVisible
      }
    }
  }
}
</script>

<template>
  <div>
    <el-table :height="height" :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange"
      ref="table" style="width: 100%">
      <el-table-column v-if="showSelection" type="selection" width="55">
      </el-table-column>
      <el-table-column v-if="showIndex" label="序号" type="index" width="50">
      </el-table-column>
      <el-table-column v-for="(item) in tableSchema" :fixed="item.fixed" :key="item.field" :label="item.label"
        :prop="item.field" :show-overflow-tooltip="item.showOverflowTooltip" :width="item.width"
        :align="item.align ? item.align : 'center'">
        <template v-if="item.headerSlot" v-slot:header="scope">
          <slot :name="item.headerSlot" v-bind="scope"></slot>
        </template>
        <template v-if="item.slot || item.type" v-slot="scope">
          <template v-if="item.type === 'tag'">
            <el-tag v-if="!item.tagMap">{{ scope.row[item.field] }}</el-tag>
            <el-tag v-else-if="typeof item.tagMap[scope.row[item.field]] === 'string'">
              {{ item.tagMap[scope.row[item.field]] }}
            </el-tag>
            <el-tag v-else-if="item.tagMap[scope.row[item.field]]" :type="item.tagMap[scope.row[item.field]].tagType">{{
              item.tagMap[scope.row[item.field]].label }}</el-tag>
            <el-tag v-else-if="item.tagMap['default']" :type="item.tagMap['default'].tagType">{{
              item.tagMap['default'].label }}</el-tag>
            <el-tag v-else>{{ scope.row[item.field] }}</el-tag>
          </template>
          <template v-else-if="item.type === 'img'">
            <ImgUploader v-if="item.isEdit || isEdit" :value.sync="scope.row[item.field]"></ImgUploader>
            <template v-else>
              <el-image v-if="scope.row[item.field]" style="width: 100px; max-height: 200px;" :src="scope.row[item.field]"
                :preview-src-list="[scope.row[item.field]]">
              </el-image>
              <span v-else>---</span>
            </template>
          </template>
          <template v-else-if="item.type === 'input'">
            <el-input v-if="item.isEdit || isEdit" v-model="scope.row[item.field]"></el-input>
            <span v-else>{{ scope.row[item.field] }}</span>
          </template>
          <template v-else-if="item.type === 'select'">
            <el-select  v-model="scope.row[item.field]" :disabled="!isEdit || item.edit">
              <el-option v-for="(select, index) in item.options" :key="index" :label="select.label" :value="select.value">
              </el-option>
            </el-select>
          </template>
          <template v-else-if="item.type === 'radio'">
            <el-radio-group :disabled="!isEdit || item.edit"  v-model="scope.row[item.field]">
              <el-radio v-for="radio in item.options" :key="radio.value" :label="radio.value">{{ radio.label }}
              </el-radio>
            </el-radio-group>
          </template>
          <template v-else-if="item.edit">
            <template v-if="item.edit.isVisible">
              <el-input v-if="!item.edit.type || item.edit.type === 'input'" v-model="scope.row[item.field]"
                :placeholder="item.edit.placeholder || '请输入内容'" :size="item.edit.size || 'mini'"
                :disabled="item.edit.disabled"></el-input>
            </template>
            <template v-else>
              {{ scope.row[item.field] }}
            </template>
            <!--            <i v-if="item.edit.editButton" @click="toggleEditStatus(item)" class="el-icon-edit"></i>-->
          </template>
          <template v-else>
            <slot :name="item.slot" v-bind="scope"></slot>
            <!--            <i @click="toggleEditStatus(scope, item.field)" class="el-icon-edit"></i>-->
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.el-icon-edit {
  margin-left: 4px;
  color: #409EFF;
  cursor: pointer;
}
</style>
