{
  "easycom": {
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    // #ifdef APP
    {
      "path": "pages/project/index/coverIndex",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "disableScroll": true,
        // 禁止页面滚动（如有必要）
        "app-plus": {
          "bounce": "none"
          // 禁止iOS回弹效果（如有必要）
        },
        "meta": {
          "disableSwipeBack": true
          // 禁止手势返回（如有必要）
        }
      }
    },
    {
      "path": "pages/project/index/index",
      "style": {
        "navigationBarTitleText": "Bigverse",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        "app-plus": {
          "safearea": true
          // 启用安全区域适配
        },
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    // #endif
    // #ifdef H5
    {
      "path": "pages/project/index/index",
      "style": {
        "navigationBarTitleText": "Bigverse",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        "app-plus": {
          "safearea": true
          // 启用安全区域适配
        },
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/index/coverIndex",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "disableScroll": true,
        // 禁止页面滚动（如有必要）
        "app-plus": {
          "bounce": "none"
          // 禁止iOS回弹效果（如有必要）
        },
        "meta": {
          "disableSwipeBack": true
          // 禁止手势返回（如有必要）
        }
      }
    },
    // #endif
    {
      "path": "pages/project/index/indexYs",
      "style": {
        "navigationBarTitleText": "衍生",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        "app-plus": {
          "safearea": true
          // 启用安全区域适配
        },
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/mall/mall",
      "style": {
        "navigationBarTitleText": "市场",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/mainLogin",
      "style": {
        "navigationBarTitleText": "Bigverse",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "uni_modules/uview-ui/components/u-avatar-cropper/u-avatar-cropper",
      "style": {
        "navigationBarTitleText": "裁剪头像"
      }
    },
    {
      "path": "pages/project/login/h5PhoneLogin",
      "style": {
        "navigationBarTitleText": "Bigverse",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/appPhoneLogin",
      "style": {
        "navigationBarTitleText": "app一键登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/moreLogin",
      "style": {
        "navigationBarTitleText": "账号密码登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/bindPhone",
      "style": {
        "navigationBarTitleText": "绑定手机号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/register",
      "style": {
        "navigationBarTitleText": "注册登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/modifyPwd",
      "style": {
        "navigationBarTitleText": "忘记密码",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/notice/index",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/notice/indexYs",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/notice/webView",
      "style": {
        "navigationBarTitleText": "Bigverse",
        "enablePullDownRefresh": true,
        "navigationBarBackgroundColor": "#35333E",
        // 单独设置绿色背景色
        "navigationBarTextStyle": "white",
        // 文字颜色
        // "onReachBottomDistance": 400,
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/personal/index",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        // "onReachBottomDistance": 400,
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/im/index",
      "style": {
        "navigationBarTitleText": "群聊",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/im/groupDetails",
      "style": {
        "navigationBarTitleText": "群聊",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/takeOff",
      "style": {
        "navigationBarTitleText": "“开杠吧”模式",
        "navigationBarBackgroundColor": "#35333E",
        // 单独设置绿色背景色
        "navigationBarTextStyle": "white"
        // 文字颜色
      }
    },
    {
      "path": "pages/project/actives/mission",
      "style": {
        "navigationBarTitleText": "任务",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/TradingCompetition",
      "style": {
        "navigationBarTitleText": "交易赛",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/takeOffDetails",
      "style": {
        "navigationBarTitleText": "学习检测",
        "navigationBarBackgroundColor": "#35333E",
        // 单独设置绿色背景色
        "navigationBarTextStyle": "white"
        // 文字颜色
      }
    },
    {
      "path": "pages/project/actives/takeOffStudy",
      "style": {
        "navigationBarTitleText": "“开杠吧”模式",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/takeOffInfo",
      "style": {
        "navigationBarTitleText": "对战活动详解",
        "navigationBarTextStyle": "white",
        // 文字颜色
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/takeOffPlan",
      "style": {
        // #ifdef H5
        "navigationStyle": "custom",
        // #endif
        "navigationBarTitleText": "“开杠吧”开拓计划",
        "navigationBarTextStyle": "white"
        // 文字颜色
      }
    },
    {
      "path": "pages/project/actives/takeOffInviteData",
      "style": {
        // #ifdef H5
        "navigationStyle": "custom",
        // #endif
        "navigationBarTitleText": "“开杠吧”开拓好友",
        "navigationBarTextStyle": "white"
        // 文字颜色
      }
    },
    {
      "path": "pages/project/actives/takeOffInvite",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/game/history",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/components/position",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/contract-BITindex",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "BIT指数",
        "onReachBottomDistance": 0,
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/actives/historyposition",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "历史下单",
        "onReachBottomDistance": 0
      }
    },
    {
      "path": "pages/project/actives/game/introduce",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/actives/game/takeOffIndex",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true,
        "pullToRefresh": {
          "support": true,
          "color": "#1E1E1E"
        }
      }
    },
    {
      "path": "pages/project/actives/game/chart",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/personal/indexYs",
      "style": {
        "navigationBarTitleText": "钱包",
        "navigationStyle": "custom"
      }
    }
  ],
  // "style": "default"
  //二级页面
  "subPackages": [
    {
      "root": "pagesA",
      "pages": [
        {
          "path": "project/personal/comingSoon",
          "style": {
            "navigationBarTitleText": "敬请期待",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/mall/seriesList",
          "style": {
            "navigationBarTitleText": "系列列表",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#1E1E1E"
            }
          }
        },
        {
          "path": "project/mall/detailsShop",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/order/checkOrder",
          "style": {
            "navigationBarTitleText": "确认订单",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/order/paySuccess",
          "style": {
            "navigationBarTitleText": "支付成功",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/myBalance",
          "style": {
            "navigationBarTitleText": "我的钱包",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/bank/bank",
          "style": {
            "navigationBarTitleText": "我的银行卡",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/bank/addbank",
          "style": {
            "navigationBarTitleText": "添加银行卡",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/bank/banklist",
          "style": {
            "navigationBarTitleText": "支持银行卡",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/bank/bankphone",
          "style": {
            "navigationBarTitleText": "手机号验证",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/personal/generalAgreement",
          "style": {
            "navigationBarTitleText": "协议",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/modifyPwd",
          "style": {
            "navigationBarTitleText": "修改密码",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/realName",
          "style": {
            "navigationBarTitleText": "实名认证",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/realNameResult",
          "style": {
            "navigationBarTitleText": "实名认证结果",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/withdraw",
          "style": {
            "navigationBarTitleText": "提现",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/person/person",
          "style": {
            "navigationBarTitleText": "个人信息",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/index/information/information",
          "style": {
            "navigationBarTitleText": "资讯",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/index/player/player",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            // 隐藏系统导航栏
            "navigationBarTextStyle": "white"
            // 状态栏字体为白色，只能为 white-白色，black-黑色 二选一
          }
        },
        {
          "path": "project/index/player/coinPage",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            // 隐藏系统导航栏
            "navigationBarTextStyle": "white"
            // 状态栏字体为白色，只能为 white-白色，black-黑色 二选一
          }
        },
        {
          "path": "project/index/player/gameHistory",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            // 隐藏系统导航栏
            "navigationBarTextStyle": "white"
            // 状态栏字体为白色，只能为 white-白色，black-黑色 二选一
          }
        },
        {
          "path": "project/message/message",
          "style": {
            "navigationBarTitleText": "通知",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/invite/invite",
          "style": {
            "navigationBarTitleText": "邀请",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/index/actives/index",
          "style": {
            "navigationBarTitleText": "活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/order/addAddress",
          "style": {
            "navigationBarTitleText": "添加地址",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/order/addressManagement",
          "style": {
            "navigationBarTitleText": "地址列表",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/payManage",
          "style": {
            "navigationBarTitleText": "支付管理",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/security/emailVerify",
          "style": {
            "navigationBarTitleText": "邮箱验证",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/phoneVerify",
          "style": {
            "navigationBarTitleText": "手机号验证",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/selectPay",
          "style": {
            "navigationBarTitleText": "选择验证方式",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/security/accountLogout",
          "style": {
            "navigationBarTitleText": "账号注销",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/accountLogoutApplication",
          "style": {
            "navigationBarTitleText": "注销申请",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/accountLogoutProgress",
          "style": {
            "navigationBarTitleText": "账号注销进度",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/accountLogoutUser",
          "style": {
            "navigationBarTitleText": "账号已注销",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/modifyPwd",
          "style": {
            "navigationBarTitleText": "修改密码",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/security",
          "style": {
            "navigationBarTitleText": "账户安全",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "/project/index/domain/domain_search",
          "style": {
            "navigationBarTitleText": "数字身份"
          }
        },
        {
          "path": "/project/index/domain/domain_search_result",
          "style": {
            "navigationBarTitleText": "数字身份"
          }
        },
        {
          "path": "project/order/index",
          "style": {
            "navigationBarTitleText": "我的订单",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white",
            // 文字颜色
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#35333E"
            }
          }
        },
        {
          "path": "project/mall/choicenessHistory",
          "style": {
            "navigationBarTitleText": "历史订单",
            "navigationBarBackgroundColor": "#35333E",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "white"
            // 文字颜色
          }
        },
        {
          "path": "project/plutocrat/plutocrat",
          "style": {
            "navigationBarTitleText": "名人堂",
            "navigationStyle": "custom"
            // "navigationBarBackgroundColor": "#35333E", // 单独设置
            // "navigationBarTextStyle": "white" // 文字颜色
          }
        },
        {
          "path": "project/buyDetails/buyDetails",
          "style": {
            "navigationBarTitleText": "买入明细",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/digitalIdentity/digitalIdentity",
          "style": {
            "navigationBarTitleText": "数字身份",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "/project/index/actives/mentorship",
          "style": {
            "navigationBarTitleText": "师父排行榜",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "/project/index/actives/mentorshipExplain",
          "style": {
            "navigationBarTitleText": "绑定师徒关系",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "/project/index/actives/prenticeAccept",
          "style": {
            "navigationBarTitleText": "绑定师徒关系",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "/project/index/actives/mentorshipDetails",
          "style": {
            "navigationBarTitleText": "收益分成明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/index/actives/mentorshipMaster",
          "style": {
            "navigationBarTitleText": "我的师父",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/index/actives/mentorshipPrentice",
          "style": {
            "navigationBarTitleText": "我的徒弟",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/warehouse",
          "style": {
            "navigationBarTitleText": "宝藏统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/mall/askBuy",
          "style": {
            "navigationBarTitleText": "批量下单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/mall/askBuyEntru",
          "style": {
            "navigationBarTitleText": "立即委托"
          }
        },
        {
          "path": "project/mall/myentru",
          "style": {
            "navigationBarTitleText": "我的委托"
          }
        },
        {
          "path": "project/mall/askBuyAll",
          "style": {
            "navigationBarTitleText": "批量下单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/order/updataAddress",
          "style": {
            "navigationBarTitleText": "修改地址",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/nostalgic",
          "style": {
            "navigationBarTitleText": "怀旧区",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/index/hot/list",
          "style": {
            "navigationBarTitleText": "吉物仓",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/official/detail",
          "style": {
            "navigationBarTitleText": "公告详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/iosPay",
          "style": {
            "navigationBarTitleText": "充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/mall/search",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/userSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/broker",
          "style": {
            "navigationBarTitleText": "经纪商奖励",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#35333E"
            }
          }
        },
        {
          "path": "project/official/index",
          "style": {
            "navigationBarTitleText": "公告",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#35333E"
            }
          }
        },
        {
          "path": "project/personal/appDownload",
          "style": {
            "navigationBarTitleText": "APP下载"
          }
        },
        {
          "path": "project/plutocrat/temporary",
          "style": {
            "navigationBarTitleText": "富豪榜",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/conversion",
          "style": {
            "navigationBarTitleText": "换取祈福贴",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/give",
          "style": {
            "navigationBarTitleText": "赠人玫瑰手有余香",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/conversionNtf",
          "style": {
            "navigationBarTitleText": "兑换藏品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/mgUs/index",
          "style": {
            "navigationBarTitleText": "模拟股票",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#1E1E1E"
            }
          }
        },
        {
          "path": "project/mgUs/mgUsDetails",
          "style": {
            "navigationBarTitleText": " ",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#1E1E1E"
            }
          }
        },
        {
          "path": "project/mgUs/mgUsList",
          "style": {
            "navigationBarTitleText": "模拟股票",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true,
            "pullToRefresh": {
              "support": true,
              "color": "#1E1E1E"
            }
          }
        },
        {
          "path": "project/receiveLong/index",
          "style": {
            "navigationBarTitleText": "暴躁龙领取",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/shop",
          "style": {
            "navigationBarTitleText": "购买",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/shopOrder",
          "style": {
            "navigationBarTitleText": "确认订单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/riskAssessment/index",
          "style": {
            "navigationBarTitleText": "风险评估",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/helpCenter/index",
          "style": {
            "navigationBarTitleText": "帮助中心",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/helpCenter/answer",
          "style": {
            "navigationBarTitleText": "解答",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/index/download",
          "style": {
            "navigationBarTitleText": "M站下载"
          }
        },
        {
          "path": "project/receiveLong/invite",
          "style": {
            "navigationBarTitleText": "暴躁龙注册",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/diamond",
          "style": {
            "navigationBarTitleText": "宝石商城",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/index/index",
          "style": {
            "navigationBarTitleText": "首页",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/shop/index",
          "style": {
            "navigationBarTitleText": "商城",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/user/index",
          "style": {
            "navigationBarTitleText": "个人中心",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/shop/shop",
          "style": {
            "navigationBarTitleText": "详情页",
            "navigationBarBackgroundColor": "#fff",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "project/store/shop/shopOrder",
          "style": {
            "navigationBarTitleText": "确认订单",
            "navigationBarBackgroundColor": "#fff",
            // 单独设置绿色背景色
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "project/store/order/index",
          "style": {
            "navigationBarTitleText": "订单管理",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/order/addAddress",
          "style": {
            "navigationBarTitleText": "订单管理",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/order/addressManagement",
          "style": {
            "navigationBarTitleText": "订单管理",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/order/updataAddress",
          "style": {
            "navigationBarTitleText": "订单管理",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/login/mainLogin",
          "style": {
            "navigationBarTitleText": "登录",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/login/moreLogin",
          "style": {
            "navigationBarTitleText": "登录",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/store/user/baseInfo",
          "style": {
            "navigationBarTitleText": "基础信息",
            // 单独设置绿色背景色
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/index/search",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/myBalanceYs",
          "style": {
            "navigationBarTitleText": "对账单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/CouponPacks",
          "style": {
            "navigationBarTitleText": "我的券包",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/invite/inviteYs",
          "style": {
            "navigationBarTitleText": "邀请好友",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/qq/index",
          "style": {
            "navigationBarTitleText": "梦岛QQ小程序",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/activity/AnnualReport",
          "style": {
            "navigationBarTitleText": "我的年度报告",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTitleText": "uni-app",
    "backgroundColor": "#35333E",
    "onReachBottomDistance": 700,
    "navigationBarBackgroundColor": "#35333E",
    // 单独设置绿色背景色
    "navigationBarTextStyle": "white",
    // 文字颜色
    "adaptiveFont": true,
    "screenWidth": 750,
    "designWidth": 750,
    // 或其他您设计稿对应的宽度
    "pageOrientation": "auto",
    "pullToRefresh": {
      "backgroundColor": "#35333E"
      // 这里替换为你想要的背景颜色
      // 其他可能需要的样式配置...
    }
  },
  // "navigationBarBackgroundColor": "#FFFFFF",
  // "backgroundColor": "#FFFFFF"
  "tabBar": {
    "color": "#A6A6A6",
    "selectedColor": "#C0A572",
    "borderStyle": "#FFFFFF",
    "backgroundColor": "#FFFFFF",
    "list": [
      {
        "pagePath": "pages/project/index/index",
        "iconPath": "static/tabBar/index.png",
        "selectedIconPath": "static/tabBar/index_c.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/project/index/indexYs",
        "iconPath": "static/tabBar/index.png",
        "selectedIconPath": "static/tabBar/index_c.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/project/mall/mall",
        "iconPath": "static/tabBar/putaway.png",
        "selectedIconPath": "static/tabBar/putaway_c.png",
        "text": "商城"
      },
      {
        "pagePath": "pages/project/notice/index",
        "iconPath": "static/tabBar/notice.png",
        "selectedIconPath": "static/tabBar/notice_c.png",
        "text": "公告"
      },
      {
        "pagePath": "pages/project/im/index",
        "text": "群聊"
      },
      {
        "pagePath": "pages/project/actives/game/takeOffIndex",
        "text": "开杠吧"
      },
      {
        "pagePath": "pages/project/actives/contract-BITindex",
        "text": "BIT指数"
      },
      {
        "pagePath": "pages/project/personal/index",
        "iconPath": "static/tabBar/personal.png",
        "selectedIconPath": "static/tabBar/personal_c.png",
        "text": "我的"
      }
    ]
  },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "",
        "path": "",
        "query": ""
      }
    ]
  }
}
