@font-face {
	font-family: 'PingFang SC';
	src: local('PingFang SC');
	/* font-family: '<PERSON>roy-SemiBold'; */
	/* src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Bold.ttf);  */
}
/* https://res.pinkwallet.com/font/<PERSON><PERSON>-Black.ttf */

@font-face {
	/* font-family: 'Gilroy-Medium';
	src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Regular.ttf); */
	font-family: 'PingFang SC';
	src: local('PingFang SC');
}


@font-face {
	/* font-family: '<PERSON><PERSON>-Bold';
	src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Bold.ttf); */
	font-family: 'PingFang SC';
	src: local('PingFang SC');
}

@font-face {
	/* font-family: '<PERSON>roy-ExtraBold';
	src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Black.ttf); */
	font-family: 'PingFang SC';
	src: local('PingFang SC');
}