@charset "UTF-8";
// px单位转成vw单位
@function px2vw($size: 14px, $width: 750px) {
  @if (type-of($size) == "number" and unit($size) == "px") {
    @return calc($size * 100vw / $width);
  } @else {
    @return $size;
  }
}

// px单位转成vh单位
@function px2vh($size: 14px, $width: 812px) {
  @if (type-of($size) == "number" and unit($size) == "px") {
    @return calc($size * 100vh / $width);
  } @else {
    @return $size;
  }
}

//props-dpr
@mixin props-dpr($props, $values) {
  @if length($props) ==1 {
    @if length($values) ==1 {
      #{$props}: px2vw($values);
    } @else {
      $valueStr: ();
      @each $value in $values {
        $valueStr: append($valueStr, px2vw($value));
      }
      #{$props}: $valueStr;
    }
  } @else {
    @each $prop in $props {
      #{$prop}: px2vw(nth($values, index($props, $prop)));
    }
  }
}

