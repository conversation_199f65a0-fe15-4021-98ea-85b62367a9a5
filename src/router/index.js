import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: {
      name: 'Home',
    },
  },
  {
    path: '/home',
    name: 'Home',
    meta: {
      title: 'example',
    },
    component: () => import('../views/HomeView.vue'),
  },
  {
    path: '/broker',
    name: 'broker',
    meta: {
      title: 'broker',
    },
    component: () => import('../views/broker.vue'),
  },
  {
    path: '/broker_plan',
    name: 'broker_plan',
    meta: {
      title: 'broker_plan',
    },
    component: () => import('../views/broker_plan.vue'),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  document.title = to.meta?.title || 'pinkwallet';
  // 路由跳转后，让页面回到顶部
  // chrome
  document.body.scrollTop = 0; // firefox
  document.documentElement.scrollTop = 0; // safari
  window.pageYOffset = 0; // 调用 next()，一定要调用 next 方法，否则钩子就不会被销毁
  next();
});

export default router;
