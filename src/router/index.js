import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";
import Home from "../views/index.vue";
const routes = [
  {
    path: "/",
    redirect: {
      name: "Home",
    },
  },
  {
    path: "/home",
    name: "Home",
    meta: {
      title: "Home",
    },
    component: () => import("../views/index.vue"),
  },
  {
    path: "/register",
    name: "Register",
    meta: {
      title: "Register",
    },
    component: () => import("../views/Register.vue"),
  },
  {
    path: "/login",
    name: "Login",
    meta: {
      title: "Login",
    },
    component: () => import("../views/Login.vue"),
  },
  {
    path: "/faq",
    name: "Faq",
    meta: {
      title: "Faq",
    },
    component: () => import("../views/faq.vue"),
  },
  {
    path: "/realTimeER",
    name: "RealTimeER",
    meta: {
      title: "RealTimeER",
    },
    component: () => import("../views/RealTimeER.vue"),
  },
  {
    path: "/remittance",
    name: "Remittance",
    meta: {
      title: "Remittance",
    },
    component: () => import("../views/Remittance.vue"),
  },
  {
    path: "/stock",
    name: "stock",
    meta: {
      title: "stock",
    },
    component: () => import("../views/stock.vue"),
  },
  {
    path: "/flashExchange",
    name: "flashExchange",
    meta: {
      title: "flashExchange",
    },
    component: () => import("../views/flashExchange.vue"),
  },
  {
    path: "/pinkCard",
    name: "pinkCard",
    meta: {
      title: "pinkCard",
    },
    component: () => import("../views/pinkCard.vue"),
  },
  {
    path: "/college",
    name: "college",
    meta: {
      title: "college",
    },
    component: () => import("../views/college.vue"),
  },
  {
    path: "/aboutUs",
    name: "aboutUs",
    component: () => import("../views/aboutUs.vue"),
  },
  {
    path: "/iBan",
    name: "iBan",
    component: () => import("../views/iBan.vue"),
  },
  {
    path: "/help",
    name: "help",
    component: () => import("../views/help.vue"),
  },
  {
    path: "/suggest",
    name: "suggest",
    meta: {
      title: "suggest",
    },
    component: () => import("../views/suggest.vue"),
  },
  {
    path: "/broker",
    name: "broker",
    meta: {
      title: "broker",
    },
    component: () => import("../views/broker/index.vue"),
  },
  {
    path: "/broker-data",
    name: "broker-data",
    meta: {
      title: "broker-data",
    },
    component: () => import("../views/broker/invite_data.vue"),
  },
  // partenerplan
  {
    path: "/partenerplan",
    name: "partenerplan",
    meta: {
      title: "partenerplan",
    },
    component: () => import("../views/broker/partenerplan.vue"),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior (to, from, savedPosition) {
    // 滚动到顶部
    return { top: 0 }
  }
});

router.beforeEach((to, from, next) => {
  document.title = to.meta?.title || "Pink Wallet";
  // 路由跳转后，让页面回到顶部
  // chrome
  document.body.scrollTop = 0; // firefox
  document.documentElement.scrollTop = 0; // safari
  window.pageYOffset = 0; // 调用 next()，一定要调用 next 方法，否则钩子就不会被销毁
  next();
});

export default router;
