/**数据列表对应的路由表模块
 * 注意：path必须跟pages.json中的地址对应，最前面别忘了加'/'哦
 * //对于h5端你必须在首页加上aliasPath并设置为/
 */
const dataList = [
  {
    path: "/pages/project/index/index",
    aliasPath: "/",
    name: "index",
    meta: {
      title: "首页",
    },
  },
  {
    path: "/pages/project/personal/index",
    aliasPath: "/",
    name: "personal",
    meta: {
      title: "个人",
    },
  },
  {
    path: "/pages/project/mall/mall",
    aliasPath: "/",
    name: "mall",
    meta: {
      title: "市场",
    },
  },
  {
    path: "/pagesA/project/personal/comingSoon",
    aliasPath: "/",
    name: "comingSoon",
    meta: {
      title: "敬请期待",
    },
  },
  {
    path: "/pages/project/actives/mission",
    aliasPath: "/",
    name: "mission",
    meta: {
      title: "任务",
    },
  },
  {
    path: "/pages/project/actives/TradingCompetition",
    aliasPath: "/",
    name: "TradingCompetition",
    meta: {
      title: "交易赛",
    },
  },
  {
    path: "/pages/project/login/mainLogin",
    aliasPath: "/",
    name: "mainLogin",
    meta: {
      title: "登录",
    },
  },
  {
    path: "/pages/project/login/moreLogin",
    aliasPath: "/",
    name: "moreLogin",
    meta: {
      title: "账号密码登录",
    },
  },
  {
    path: "/pages/project/login/h5PhoneLogin",
    aliasPath: "/",
    name: "h5PhoneLogin",
    meta: {
      title: "手机号登录",
    },
  },
  {
    path: "/pages/project/login/register",
    aliasPath: "/",
    name: "register",
    meta: {
      title: "注册",
    },
  },
  {
    path: "/pages/project/notice/webView",
    aliasPath: "/",
    name: "webView",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/seriesList",
    aliasPath: "/",
    name: "seriesList",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/seriesList",
    aliasPath: "/",
    name: "seriesList",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/detailsShop",
    aliasPath: "/",
    name: "detailsShop",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/order/checkOrder",
    aliasPath: "/",
    name: "checkOrder",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/order/payOrder",
    aliasPath: "/",
    name: "payOrder",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/order/paySuccess",
    aliasPath: "/",
    name: "paySuccess",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/bank/bank",
    aliasPath: "/",
    name: "bank",
    meta: {
      title: "银行卡列表",
    },
  },
  {
    path: "/pagesA/project/bank/addbank",
    aliasPath: "/",
    name: "addbank",
    meta: {
      title: "添加银行卡",
    },
  },
  {
    path: "/pagesA/project/bank/banklist",
    aliasPath: "/",
    name: "banklist",
    meta: {
      title: "银行卡列表",
    },
  },
  {
    path: "/pagesA/project/bank/bankphone",
    aliasPath: "/",
    name: "bankphone",
    meta: {
      title: "银行卡手机验证",
    },
  },
  {
    path: "/pagesA/project/security/pay",
    aliasPath: "/",
    name: "pay",
    meta: {
      title: "充值",
    },
  },
  {
    path: "/pagesA/project/personal/withdraw",
    aliasPath: "/",
    name: "withdraw",
    meta: {
      title: "提现",
    },
  },
  {
    path: "/pagesA/project/personal/myBalance",
    aliasPath: "/",
    name: "myBalance",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/login/modifyPwd",
    aliasPath: "/",
    name: "loginfyPwd",
    meta: {
      title: "忘记密码",
    },
  },
  {
    path: "/pagesA/project/index/actives/index",
    aliasPath: "/",
    name: "actives",
    meta: {
      title: "活动",
    },
  },
  {
    path: "/pagesA/project/order/addAddress",
    aliasPath: "/",
    name: "addAddress",
    meta: {
      title: "添加地址",
    },
  },
  {
    path: "/pagesA/project/order/addressManagement",
    aliasPath: "/",
    name: "addressManagement",
    meta: {
      title: "地址列表",
    },
  },
  {
    path: "/pagesA/project/personal/realName",
    aliasPath: "/",
    name: "realName",
    meta: {
      title: "实名认证",
    },
  },
  {
    path: "/pagesA/project/security/payManage",
    aliasPath: "/",
    name: "payManage",
    meta: {
      title: "支付管理",
    },
  },
  {
    path: "/pagesA/project/security/emailVerify",
    aliasPath: "/security/emailVerify",
    name: "emailVerify",
    meta: {
      title: "邮箱验证",
    },
  },
  {
    path: "/pagesA/project/security/phoneVerify",
    aliasPath: "/security/phoneVerify",
    name: "phoneVerify",
    meta: {
      title: "手机号验证",
    },
  },
  {
    path: "/pagesA/project/security/selectPay",
    aliasPath: "/security/selectPay",
    name: "selectPay",
    meta: {
      title: "选择验证方式",
    },
  },
  {
    path: "/pagesA/project/security/accountLogout",
    aliasPath: "/",
    name: "accountLogout",
    meta: {
      title: "账号注销",
    },
  },
  {
    path: "/pagesA/project/security/accountLogoutApplication",
    aliasPath: "/",
    name: "accountLogoutApplication",
    meta: {
      title: "注销申请",
    },
  },
  {
    path: "/pagesA/project/security/accountLogoutProgress",
    aliasPath: "/",
    name: "accountLogoutProgress",
    meta: {
      title: "账号注销进度",
    },
  },
  {
    path: "/pagesA/project/security/accountLogoutUser",
    aliasPath: "/",
    name: "accountLogoutUser",
    meta: {
      title: "账号已注销",
    },
  },
  {
    path: "/pagesA/project/personal/security",
    aliasPath: "/",
    name: "security",
    meta: {
      title: "账户安全",
    },
  },
  {
    path: "/pagesA/project/security/modifyPwd",
    aliasPath: "/",
    name: "modifyPwd",
    meta: {
      title: "修改密码",
    },
  },
  {
    path: "/pagesA/project/person/person",
    aliasPath: "/",
    name: "person",
    meta: {
      title: "个人信息",
    },
  },
  {
    path: "/pagesA/project/plutocrat/plutocrat",
    aliasPath: "/",
    name: "plutocrat",
    meta: {
      title: "名人堂",
    },
  },
  {
    path: "/pagesA/project/index/information/information",
    aliasPath: "/",
    name: "information",
    meta: {
      title: "资讯",
    },
  },
  {
    path: "/pagesA/project/buyDetails/buyDetails",
    aliasPath: "/",
    name: "buyDetails",
    meta: {
      title: "买入明细",
    },
  },
  {
    path: "/pagesA/project/index/player/player",
    aliasPath: "/",
    name: "player",
    meta: {
      title: "边玩边赚",
    },
  },
  {
    path: "/pagesA/project/index/player/coinPage",
    aliasPath: "/",
    name: "coinPage",
    meta: {
      title: "玩转B宝",
    },
  },
  {
    path: "/pagesA/project/index/player/gameHistory",
    aliasPath: "/",
    name: "gameHistory",
    meta: {
      title: "B宝记录",
    },
  },
  {
    path: "/pagesA/project/digitalIdentity/digitalIdentity",
    aliasPath: "/",
    name: "digitalIdentity",
    meta: {
      title: "数字身份",
    },
  },
  {
    path: "/pagesA/project/index/domain/domain_search",
    aliasPath: "/",
    name: "domain_search",
    meta: {
      title: "",
    },
  },
  {
    path: "/uni_modules/uview-ui/components/u-avatar-cropper/u-avatar-cropper",
    aliasPath: "/",
    name: "u-avatar-cropper",
    meta: {
      title: "裁剪头像",
    },
  },
  {
    path: "/pagesA/project/index/domain/domain_search_result",
    aliasPath: "/",
    name: "domain_search_result",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/order/index",
    aliasPath: "/",
    name: "order",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/choicenessHistory",
    aliasPath: "/",
    name: "choicenessHistory",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/invite/invite",
    aliasPath: "/",
    name: "invite",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/message/message",
    aliasPath: "/",
    name: "message",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/mentorship",
    aliasPath: "/",
    name: "mentorship",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/mentorshipExplain",
    aliasPath: "/",
    name: "mentorshipExplain",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/prenticeAccept",
    aliasPath: "/",
    name: "prenticeAccept",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/mentorshipDetails",
    aliasPath: "/",
    name: "mentorshipDetails",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/mentorshipMaster",
    aliasPath: "/",
    name: "mentorshipMaster",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/actives/mentorshipPrentice",
    aliasPath: "/",
    name: "mentorshipPrentice",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/warehouse",
    aliasPath: "/",
    name: "warehouse",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/askBuy",
    aliasPath: "/",
    name: "askBuy",
    meta: {
      title: "批量下单",
    },
  },
  {
    path: "/pagesA/project/mall/askBuyEntru",
    aliasPath: "/",
    name: "askBuyEntru",
    meta: {
      title: "批量委托",
    },
  },
  {
    path: "/pagesA/project/mall/myentru",
    aliasPath: "/",
    name: "MyEntru",
    meta: {
      title: "我的委托",
    },
  },
  {
    path: "/pagesA/project/mall/askBuyAll",
    aliasPath: "/",
    name: "askBuyAll",
    meta: {
      title: "我的求购",
    },
  },
  {
    path: "/pagesA/project/personal/generalAgreement",
    aliasPath: "/",
    name: "generalAgreement",
    meta: {
      title: "协议",
    },
  },
  {
    path: "/pagesA/project/order/updataAddress",
    aliasPath: "/",
    name: "updataAddress",
    meta: {
      title: "修改地址",
    },
  },
  {
    path: "/pagesA/project/personal/nostalgic",
    aliasPath: "/",
    name: "nostalgic",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/mentorshipMaster",
    aliasPath: "/",
    name: "mentorshipMaster",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/mentorshipPrentice",
    aliasPath: "/",
    name: "mentorshipPrentice",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/mentorship",
    aliasPath: "/",
    name: "mentorship",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/mentorshipExplain",
    aliasPath: "/",
    name: "mentorshipExplain",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/prenticeAccept",
    aliasPath: "/",
    name: "prenticeAccept",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/hot/list",
    aliasPath: "/",
    name: "hotList",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/login/bindPhone",
    aliasPath: "/",
    name: "bindPhone",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/official/detail",
    aliasPath: "/",
    name: "officialDetail",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/notice/index",
    aliasPath: "/",
    name: "notice",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/security/iosPay",
    aliasPath: "/",
    name: "iosPay",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mall/search",
    aliasPath: "/",
    name: "search",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/userSearch",
    aliasPath: "/",
    name: "userSearch",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/broker",
    aliasPath: "/",
    name: "broker",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/official/index",
    aliasPath: "/",
    name: "official",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/appDownload",
    aliasPath: "/",
    name: "appDownload",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOff",
    aliasPath: "/",
    name: "takeOff",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffStudy",
    aliasPath: "/",
    name: "takeOffStudy",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffDetails",
    aliasPath: "/",
    name: "takeOffDetails",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/im/index",
    aliasPath: "/",
    name: "im",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffInfo",
    aliasPath: "/",
    name: "takeOffInfo",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffPlan",
    aliasPath: "/",
    name: "takeOffPlan",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffInviteData",
    aliasPath: "/",
    name: "takeOffInviteData",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/takeOffInvite",
    aliasPath: "/",
    name: "takeOffInvite",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/game/takeOffIndex",
    aliasPath: "/",
    name: "takeOffIndex",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/contract-BITindex",
    aliasPath: "/",
    name: "contract-BITindex",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/historyposition",
    aliasPath: "/",
    name: "historyposition",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/game/history",
    aliasPath: "/",
    name: "history",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/actives/game/chart",
    aliasPath: "/",
    name: "chart",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mgUs/index",
    aliasPath: "/",
    name: "mgUs",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mgUs/mgUsDetails",
    aliasPath: "/",
    name: "mgUsDetails",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/mgUs/mgUsList",
    aliasPath: "/",
    name: "mgUsList",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/index/indexYs",
    aliasPath: "/",
    name: "indexYs",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/shop",
    aliasPath: "/",
    name: "shop",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/shopOrder",
    aliasPath: "/",
    name: "shopOrder",
    meta: {
      title: "",
    },
  },

  {
    path: "/pagesA/project/riskAssessment/index",
    aliasPath: "/",
    name: "riskAssessment",
    meta: {
      title: "",
    },
  },

  {
    path: "/pagesA/project/helpCenter/index",
    aliasPath: "/",
    name: "helpCenter",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/helpCenter/answer",
    aliasPath: "/",
    name: "answer",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/download",
    aliasPath: "/",
    name: "download",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/activity/diamond",
    aliasPath: "/",
    name: "diamond",
  },
  {
    path: "/pagesA/project/store/index/index",
    aliasPath: "/",
    name: "storeIndex",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/shop/shop",
    aliasPath: "/",
    name: "storeShop",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/index/search",
    aliasPath: "/",
    name: "Desearch",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/shop/shopOrder",
    aliasPath: "/",
    name: "storeShopOrder",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/order/index",
    aliasPath: "/",
    name: "storeOrderIndex",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/user/index",
    aliasPath: "/",
    name: "userIndex",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/order/addAddress",
    aliasPath: "/",
    name: "storeAddAddress",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/order/addressManagement",
    aliasPath: "/",
    name: "storeAddressManagement",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/order/updataAddress",
    aliasPath: "/",
    name: "storeUpdataAddress",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/login/mainLogin",
    aliasPath: "/",
    name: "storeMainLogin",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/login/moreLogin",
    aliasPath: "/",
    name: "storeMoreLogin",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/store/user/baseInfo",
    aliasPath: "/",
    name: "baseInfo",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/notice/indexYs",
    aliasPath: "/",
    name: "noticeYs",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/personal/indexYs",
    aliasPath: "/",
    name: "personalYs",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/myBalanceYs",
    aliasPath: "/",
    name: "myBalanceYs",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/CouponPacks",
    aliasPath: "/",
    name: "CouponPacks",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/invite/inviteYs",
    aliasPath: "/",
    name: "inviteYs",
    meta: {
      title: "",
    },
  },
];
export default dataList;
