/**数据列表对应的路由表模块
 * 注意：path必须跟pages.json中的地址对应，最前面别忘了加'/'哦
 * //对于h5端你必须在首页加上aliasPath并设置为/
 */
const dataList = [
	{ path: '/pages/project/login/login', aliasPath: '/', name: 'login', meta: { title: '登录' } },
	// {path: '/pages/project/login/loginMain', aliasPath: '/', name: 'login', meta: {title: '登录'}},
	{ path: '/pages/project/login/register', aliasPath: '/', name: 'register', meta: { title: '注册' } },
	{
		path: "pages/project/login/location", aliasPath: '/', name: 'location', meta: { title: '落地页' }
	},
]
export default dataList
