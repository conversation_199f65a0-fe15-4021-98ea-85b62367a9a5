import layoutHeaderAside from "@/layout/header-aside";

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require("@/libs/util.import." + process.env.NODE_ENV);

/**
 * 在主框架内显示
 */
const frameIn = [
  {
    path: "/",
    redirect: {
      name: "index",
    },
    component: layoutHeaderAside,
    children: [
      // 首页
      {
        path: "index",
        name: "index",
        meta: {
          auth: true,
          cache: true,
        },
        component: _import("system/index"),
      },
      {
        path: "user_list",
        name: "user_list",
        meta: {
          title: "用户列表",
          auth: true,
          cache: true,
        },
        component: _import("user/userList"),
      },
      {
        path: "authority_list",
        name: "authority_list",
        meta: {
          title: "权限列表",
          cache: true,
          auth: true,
        },
        component: _import("user/authorityList"),
      },
      // 保证金
      {
        path: "moneyLowest",
        name: "moneyLowest",
        meta: {
          title: "保证金最低配置",
          cache: true,
          auth: true,
        },
        component: _import("money/moneyLowest"),
      },
      {
        path: "moneyList",
        name: "moneyList",
        meta: {
          title: "保证金列表",
          auth: true,
          cache: true,
        },
        component: _import("money/moneyList"),
      },
      {
        path: "moenyOrder",
        name: "moenyOrder",
        meta: {
          title: "保证金订单",
          auth: true,
          cache: true,
        },
        component: _import("money/moneyOrder"),
      },
      {
        path: "moneyPay",
        name: "moneyPay",
        meta: {
          title: "保证金支付审核",
          auth: true,
          cache: true,
        },
        component: _import("money/moneyPay"),
      },
      {
        path: "moneyBack",
        name: "moneyBack",
        meta: {
          title: "保证金退还审核",
          auth: true,
          cache: true,
        },
        component: _import("money/moneyBack"),
      },
      // 消息中心
      {
        path: "activityMessage",
        name: "activityMessage",
        meta: {
          title: "消息盒子",
          cache: true,
          auth: true,
        },
        component: _import("message/activityMessage"),
      },
      {
        path: "communique",
        name: "communique",
        meta: {
          title: "事件消息模板",
          cache: true,
          auth: true,
        },
        component: _import("message/communique"),
      },
      {
        path: "systematicNotification",
        name: "systematicNotification",
        meta: {
          title: "消息推送任务",
          cache: true,
          auth: true,
        },
        component: _import("message/systematicNotification"),
      },
      {
        path: "addMsgPush",
        name: "addMsgPush",
        meta: {
          title: "消息推送任务",
          cache: true,
          auth: true,
        },
        component: _import("message/systematicNotification/addMsgPush"),
      },
      {
        path: "/message/official/list",
        name: "OfficialNoticeList",
        meta: {
          title: "官方公告",
          cache: true,
          auth: true,
        },
        component: _import("message/officialNotice/list"),
      },
      // 市场中心
      {
        path: "marketTab",
        name: "marketTab",
        meta: {
          title: "市场Tab",
          cache: true,
          auth: true,
        },
        component: _import("market/marketTab"),
      },
      // 基础配置
      {
        path: "configuration",
        name: "configuration",
        meta: {
          title: "基础配置",
          cache: true,
          auth: true,
        },
        component: _import("configuration"),
      },
      {
        path: "configurationConfig",
        name: "configurationConfig",
        meta: {
          title: "后台配置",
          cache: true,
          auth: true,
        },
        component: _import("configuration/config"),
      },
      //风控ip
      {
        path: "configurationRiskIp",
        name: "configurationRiskIp",
        meta: {
          title: "风控ip",
          cache: true,
          auth: true,
        },
        component: _import("configuration/riskIp"),
      },
      {
        path: "configurationApp",
        name: "configurationApp",
        meta: {
          title: "App基础配置",
          cache: true,
          auth: true,
        },
        component: _import("configurationApp"),
      },
      {
        path: "configurationDict",
        name: "configurationDict",
        meta: {
          title: "字典配置",
          cache: true,
          auth: true,
        },
        component: _import("configurationDict"),
      },
      {
        path: "configurationDict/detail",
        name: "configurationDictDetail",
        meta: {
          title: "字典详情",
          cache: true,
          auth: true,
        },
        component: _import("configurationDict/detail"),
      },
      {
        path: "synchronization",
        name: "synchronization",
        meta: {
          title: "同步数据",
          cache: true,
          auth: true,
        },
        component: _import("synchronization"),
      },
      {
        path: "user_details",
        name: "user_details",
        meta: {
          title: "用户详情",
          cache: true,
          auth: true,
        },
        component: _import("details/user"),
      },
      {
        path: "send_email",
        name: "send_email",
        meta: {
          title: "邮箱管理",
          cache: true,
          auth: true,
        },
        component: _import("email/send"),
      },
      {
        path: "works_details",
        name: "works_details",
        meta: {
          title: "作品详情",
          cache: true,
          auth: true,
        },
        component: _import("details/works"),
      },
      // 审核页面
      {
        path: "examine",
        name: "examine",
        meta: {
          title: "审核",
          auth: true,
          cache: true,
        },
        component: _import("examine/index"),
      },
      // 商品列表
      {
        path: "listOfWorks",
        name: "ListOfWorks",
        meta: {
          title: "商品列表",
          cache: true,
          auth: true,
        },
        component: _import("ListOfWorks/index"),
      },
      {
        path: "indexOperation",
        name: "indexOperation",
        meta: {
          title: "商品列表",
          cache: true,
          auth: true,
        },
        component: _import("ListOfWorks/indexOperation"),
      },
      // 商品维度
      {
        path: "trace",
        name: "trace",
        meta: {
          title: "商品维度",
          cache: true,
          auth: true,
        },
        component: _import("ListOfWorks/trace"),
      },
      // 系列审核页面
      {
        path: "series",
        name: "series",
        meta: {
          title: "审核",
          auth: true,
          cache: true,
        },
        component: _import("examine/series"),
      },
      // 作品评论审核
      {
        path: "workReview",
        name: "workReview",
        meta: {
          title: "评论审核",
          auth: true,
          cache: true,
        },
        component: _import("examine/workReview"),
      },
      //优先购
      {
        path: "priorityBuy",
        name: "priorityBuy",
        meta: {
          title: "优先购",
          cache: true,
          auth: true,
        },
        component: _import("priorityBuy"),
      },
      // 作品评论审核
      {
        path: "workReview",
        name: "workReview",
        meta: {
          title: "评论审核",
          auth: true,
          cache: true,
        },
        component: _import("examine/workReview"),
      },
      // 3D预览
      {
        path: "3Dpreview",
        name: "3Dpreview",
        meta: {
          title: "预览",
          auth: true,
        },
        component: _import("3Dpreview"),
      },
      // 3D预览---obj
      {
        path: "3DpreviewObj",
        name: "3DpreviewObj",
        meta: {
          title: "3D预览",
          auth: true,
        },
        component: _import("3DpreviewObj"),
      },
      // 修改密码
      {
        path: "modify",
        name: "modify",
        meta: {
          title: "修改密码",
          cache: true,
          auth: true,
        },
        component: _import("user/userModify/Password"),
      },
      // 修改用户信息
      {
        path: "information",
        name: "information",
        meta: {
          title: "修改信息",
          cache: true,
          auth: true,
        },
        component: _import("user/userModify/information"),
      },
      // 添加用户
      {
        path: "adduser",
        name: "adduser",
        meta: {
          title: "添加用户",
          cache: true,
          auth: true,
        },
        component: _import("user/userModify/addUser"),
      },
      // 订单管理
      {
        path: "order",
        name: "order",
        meta: {
          title: "订单列表",
          auth: true,
          cache: true,
        },
        component: _import("order/index"),
      },
      // 提现管理
      {
        path: "withdraw",
        name: "withdraw",
        meta: {
          title: "银行卡提现",
          auth: true,
          cache: true,
        },
        component: _import("withdraw"),
      },
      // 用户流水
      {
        path: "accounting",
        name: "accounting",
        meta: {
          title: "用户流水",
          auth: true,
          cache: true,
        },
        component: _import("withdraw/accounting"),
      },
      // 支付回调
      {
        path: "payControl",
        name: "payControl",
        meta: {
          title: "支付回调",
          auth: true,
          cache: true,
        },
        component: _import("order/payControl"),
      },
      {
        path: "BlackList",
        name: "BlackList",
        meta: {
          title: "黑名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/index"),
      },
      {
        path: "WhiteList",
        name: "WhiteList",
        meta: {
          title: "白名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/WhiteIndex"),
      },
      // 购买黑名单
      {
        path: "buyBlack",
        name: "buyBlack",
        meta: {
          title: "购买黑名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/buyBlack"),
      },
      {
        path: "BITbuyBlack",
        name: "BITbuyBlack",
        meta: {
          title: "BIT购买黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BIT/blacklist"),
      },
      {
        path: "BITRevenueRiskControl",
        name: "BITRevenueRiskControl",
        meta: {
          title: "BIT收益风控",
          cache: true,
          auth: true,
        },
        component: _import("BIT/RevenueRiskControl"),
      },
      // 提现黑名单
      {
        path: "withdrawBlack",
        name: "withdrawBlack",
        meta: {
          title: "提现黑名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/withdrawBlack"),
      },
      // 购买转赠黑名单
      {
        path: "sendBlack",
        name: "sendBlack",
        meta: {
          title: "购买转赠黑名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/sendBlack"),
      },
      // 锁单白名单
      {
        path: "lockWhite",
        name: "lockWhite",
        meta: {
          title: "锁单白名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/lockWhite"),
      },
      // 作品需审名单
      {
        path: "audit",
        name: "audit",
        meta: {
          title: "作品需审名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/audit"),
      },
      // 作品需审名单
      {
        path: "verificationCode",
        name: "verificationCode",
        meta: {
          title: "购买验证码白名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/verificationCode"),
      },
      // 系列列表
      {
        path: "seriesList",
        name: "seriesList",
        meta: {
          title: "系列列表",
          cache: true,
          auth: true,
        },
        component: _import("seriesList"),
      },
      // 系列列表详情
      {
        path: "seriesListDetail",
        name: "seriesListDetail",
        meta: {
          title: "系列详情",
          cache: true,
          auth: true,
        },
        component: _import("seriesList/seriesListDetail"),
      },
      // 上链
      {
        path: "cochain",
        name: "cochain",
        meta: {
          title: "上链",
          cache: true,
          auth: true,
        },
        component: _import("cochain"),
      },
      // 上链
      {
        path: "contract",
        name: "contract",
        meta: {
          title: "用户部署合约失败列表",
          cache: true,
          auth: true,
        },
        component: _import("cochain/contract"),
      },
      // 上链
      {
        path: "transfer",
        name: "transfer",
        meta: {
          title: "转移token失败列表",
          cache: true,
          auth: true,
        },
        component: _import("cochain/transfer"),
      },
      // bigVerse 管理
      {
        path: "bigverse-manage",
        name: "BigVerseManage",
        meta: {
          title: "bigverse管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bigVerse"),
      },
      // banner配置
      {
        path: "bannerConfig",
        name: "bannerConfig",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig"),
      },
      // 暴躁龙banner配置
      {
        path: "bannerConfig2",
        name: "bannerConfig2",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig"),
      },
      // 添加/编辑banner配置
      {
        path: "addBanner",
        name: "addBanner",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig/addBanner"),
      },
      // 金刚区配置
      {
        path: "kingKong",
        name: "kingKong",
        meta: {
          title: "金刚区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/kingKong"),
      },
      // 添加/编辑金刚区
      {
        path: "editKingKong",
        name: "editKingKong",
        meta: {
          title: "金刚区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/kingKong/editKingKong"),
      },
      // 楼层区配置
      {
        path: "floorConfig",
        name: "floorConfig",
        meta: {
          title: "楼层区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig"),
      },
      // 市场页配置
      {
        path: "floorConfig2",
        name: "floorConfig2",
        meta: {
          title: "市场页配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig"),
      },
      {
        path: "floorConfigKv",
        name: "floorConfigKv",
        meta: {
          title: "楼层区配置-kverse",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/indexKv"),
      },
      // 楼层区新增
      {
        path: "addFloor",
        name: "addFloor",
        meta: {
          title: "楼层区新增/编辑",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/addFloor"),
      },
      // 透出精选IP
      {
        path: "floorIp",
        name: "floorIp",
        meta: {
          title: "透出精选IP列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/floorIp"),
      },
      // 透出精选IP新增/编辑
      {
        path: "addFloorIp",
        name: "addFloorIp",
        meta: {
          title: "透出IP",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/addFloorIp"),
      },
      // 透出作品
      {
        path: "floorGoods",
        name: "floorGoods",
        meta: {
          title: "透出作品",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/floorGoods"),
      },
      // 透出作品新增/编辑
      {
        path: "addFloorGoods",
        name: "addFloorGoods",
        meta: {
          title: "透出作品",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/addFloorGoods"),
      },
      // 透出 IP 列表入口
      {
        path: "ip-list",
        name: "IPListEntry",
        meta: {
          title: "透出 IP 列表入口",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/floorConfig/IPListEntry"),
      },
      // 瓷片区配置
      {
        path: "tileConfig",
        name: "tileConfig",
        meta: {
          title: "瓷片区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/tileConfig"),
      },
      // 瓷片区添加
      {
        path: "addTile",
        name: "addTile",
        meta: {
          title: "瓷片区添加",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/tileConfig/addTile"),
      },
      // 瓷片区透出ip编辑
      {
        path: "addTileIp",
        name: "addTileIp",
        meta: {
          title: "透出IP列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/tileConfig/addTileIp"),
      },
      // 瓷片区透出作品
      {
        path: "tileGoods",
        name: "tileGoods",
        meta: {
          title: "透出作品列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/tileConfig/tileGoods"),
      },
      // 瓷片区透出作品编辑
      {
        path: "addTileGoods",
        name: "addTileGoods",
        meta: {
          title: "透出作品列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/tileConfig/addTileGoods"),
      },
      // 专题区配置
      {
        path: "subject",
        name: "subject",
        meta: {
          title: "专题区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/subject"),
      },
      // 专题区编辑
      {
        path: "editSubject",
        name: "editSubject",
        meta: {
          title: "专题区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/subject/editSubject"),
      },
      // 品牌区配置
      {
        path: "brand",
        name: "brand",
        meta: {
          title: "品牌区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/brand"),
      },
      // 品牌区详情
      {
        path: "brandIp",
        name: "brandIp",
        meta: {
          title: "品牌区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/brand/brandIp"),
      },
      // 品牌区详情编辑
      {
        path: "addBrandIp",
        name: "addBrandIp",
        meta: {
          title: "品牌区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/brand/addBrandIp"),
      },
      // 品牌区添加编辑
      {
        path: "addBrand",
        name: "addBrand",
        meta: {
          title: "品牌区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/brand/addBrand"),
      },
      // 透出IP
      {
        path: "IPGood",
        name: "IPGood",
        meta: {
          title: "透出IP-列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/IPGood"),
      },
      // 透出IP新增/编辑
      {
        path: "GoodAdd",
        name: "GoodAdd",
        meta: {
          title: "透出IP-新增/编辑",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/IPGood/GoodAdd"),
      },
      // 透出IP 作品库
      {
        path: "GoodGallery",
        name: "GoodGallery",
        meta: {
          title: "透出IP-作品库",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/IPGood/GoodGallery"),
      },
      // 透出IP 作品详情
      {
        path: "GoodDetail",
        name: "GoodDetail",
        meta: {
          title: "透出IP-作品详情",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/IPGood/GoodDetail"),
      },
      // 平安清分账户列表
      {
        path: "safeAccountList",
        name: "safeAccountList",
        meta: {
          title: "账户列表",
          cache: true,
          auth: true,
        },
        component: _import("safeClear/safeAccountList"),
      },
      // 平安清分记账信息列表
      {
        path: "accountList",
        name: "accountList",
        meta: {
          title: "记账信息列表",
          cache: true,
          auth: true,
        },
        component: _import("safeClear/accountList"),
      },
      // 平安清分手动处理提现记录列表
      {
        path: "withdrawalList",
        name: "withdrawalList",
        meta: {
          title: "手动处理提现记录",
          cache: true,
          auth: true,
        },
        component: _import("safeClear/withdrawalList"),
      },
      // 平安清分记账需求池列表
      {
        path: "poolList",
        name: "poolList",
        meta: {
          title: "记账需求池列表",
          cache: true,
          auth: true,
        },
        component: _import("safeClear/poolList"),
      },
      // 平安清分提现记录列表
      {
        path: "transferWithdrawList",
        name: "transferWithdrawList",
        meta: {
          title: "提现记录列表",
          cache: true,
          auth: true,
        },
        component: _import("safeClear/transferWithdrawList"),
      },
      // 精选ip维护
      {
        path: "selection",
        name: "selection",
        meta: {
          title: "精选ip维护",
          cache: true,
          auth: false,
        },
        component: _import("marketConfig/selection"),
      },
      // 精选ip维护-添加品牌
      {
        path: "addSelection",
        name: "addSelection",
        meta: {
          title: "添加/编辑精选ip",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/selection/addSelection"),
      },
      // 模块全局管理
      {
        path: "module",
        name: "module",
        meta: {
          title: "模块全局管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/module"),
      },
      // 模块编辑
      {
        path: "editModule",
        name: "editModule",
        meta: {
          title: "模块编辑",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/module/editModule"),
      },
      // 作品主题页管理
      {
        path: "WorkTheme",
        name: "WorkTheme",
        meta: {
          title: "作品主题页管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/WorkTheme"),
      },
      // 作品主题页管理 - 编辑 查看 新增
      {
        path: "WorkTheme/edit",
        name: "EditTheme",
        meta: {
          title: "作品主题页管理 - 编辑/查看/新增",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/WorkTheme/EditTheme"),
      },
      // 作品主题页管理 - 作品列表
      {
        path: "WorkTheme/GoodList",
        name: "WorkThemeGoodList",
        meta: {
          title: "作品主题页管理 - 作品列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/WorkTheme/GoodList"),
      },
      // 作品主题页管理 - 添加作品
      {
        path: "WorkTheme/GoodAdd",
        name: "WorkThemeGoodAdd",
        meta: {
          title: "作品主题页管理 - 作品列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/WorkTheme/GoodAdd"),
      },
      // 用户列表
      {
        path: "userList",
        name: "userList",
        meta: {
          title: "用户列表",
          auth: true,
          cache: true,
        },
        component: _import("userList"),
      },
      // 用户链路记录
      {
        path: "userBalance",
        name: "userBalance",
        meta: {
          title: "用户链路记录",
          cache: true,
          auth: true,
        },
        component: _import("userList/userBalance"),
      },
      // 用户燃料石列表页
      {
        path: "FuelStone",
        name: "FuelStone",
        meta: {
          title: "用户燃料石列表页",
          cache: true,
          auth: true,
        },
        component: _import("userList/FuelStone"),
      },
      // 注销页面
      {
        path: "cancellation",
        name: "cancellation",
        meta: {
          title: "注销管理",
          cache: true,
          auth: true,
        },
        component: _import("cancellation"),
      },
      // 游戏--配置
      {
        path: "user_details",
        name: "user_details",
        meta: {
          title: "用户详情",
          cache: true,
          auth: true,
        },
        component: _import("details/user"),
      },
      // 游戏 -- 时长
      {
        path: "configure",
        name: "configure",
        meta: {
          title: "游戏配置",
          cache: true,
          auth: true,
        },
        component: _import("game/configure"),
      },
      // 游戏--管理
      {
        path: "duration",
        name: "duration",
        meta: {
          title: "游戏时长",
          cache: true,
          auth: true,
        },
        component: _import("game/duration"),
      },
      // 系统 前端日志
      {
        path: "Administration",
        name: "Administration",
        meta: {
          title: "id管理",
          cache: true,
          auth: true,
        },
        component: _import("game/Administration"),
      },
      // 树藏助手
      {
        path: "/digital-collection/setting/banner",
        name: "DigitalCollectionSettingBanner",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig"),
      },
      {
        path: "/digital-collection/setting/kingKong",
        name: "DigitalCollectionSettingKingKong",
        meta: {
          title: "金刚区配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/kingKong"),
      },
      {
        path: "/digital-collection/setting/Platform",
        name: "DigitalCollectionSettingPlatform",
        meta: {
          title: "平台配置",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/setting/Platform"),
      },
      {
        path: "/digital-collection/setting/addPlatform",
        name: "DigitalCollectionSettingAddPlatform",
        meta: {
          title: "平台配置",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/setting/PlatformAdd"),
      },
      {
        path: "/digital-collection/setting/detailsTips",
        name: "DigitalCollectionSettingDetailsTips",
        meta: {
          title: "商品详情提示信息",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/setting/DetailsTips"),
      },
      {
        path: "digital-collection/order",
        name: "DigitalCollectionOrder",
        meta: {
          title: "订单列表",
          auth: true,
          cache: true,
        },
        component: _import("digitalCollection/Order"),
      },
      {
        path: "digital-collection/publish",
        name: "DigitalCollectionPublish",
        meta: {
          title: "商品发布列表",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/Publish"),
      },
      {
        path: "digital-collection/template-publish",
        name: "DigitalCollectionTemplatePublish",
        meta: {
          title: "一键发布模版管理",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/templatePublish"),
      },
      {
        path: "digital-collection/template-publish/edit",
        name: "DigitalCollectionTemplatePublishEdit",
        meta: {
          title: "一键发布模版",
          cache: true,
          auth: true,
        },
        component: _import("digitalCollection/templatePublish/TemplateAdd"),
      },
      // 树藏助手消息中心
      {
        path: "digital-collection/activityMessage",
        name: "DigitalCollectionActivityMessage",
        meta: {
          title: "消息盒子",
          cache: true,
          auth: true,
        },
        component: _import("message/activityMessage"),
      },
      {
        path: "digital-collection/communique",
        name: "DigitalCollectionCommunique",
        meta: {
          title: "事件消息模板",
          cache: true,
          auth: true,
        },
        component: _import("message/communique"),
      },
      {
        path: "digital-collection/systematicNotification",
        name: "DigitalCollectionSystematicNotification",
        meta: {
          title: "消息推送任务",
          cache: true,
          auth: true,
        },
        component: _import("message/systematicNotification"),
      },
      {
        path: "digital-collection/addMsgPush",
        name: "DigitalCollectionAddMsgPush",
        meta: {
          title: "消息推送任务",
          cache: true,
          auth: true,
        },
        component: _import("message/systematicNotification/addMsgPush"),
      },
      // 空投管理
      {
        path: "air-drop",
        name: "AirDrop",
        meta: {
          title: "创作空投管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDrop"),
      },
      // 空投作品
      {
        path: "air-drop-works/:airDropId",
        name: "AirDropWorks",
        meta: {
          title: "创作空投作品",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDrop/AirDropWorks"),
      },
      // 空投用户
      {
        path: "air-drop-users/:airDropId",
        name: "AirDropUsers",
        meta: {
          title: "创作空投用户",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDrop/AirDropUsers"),
      },
      // 空投结果
      {
        path: "air-drop-preview/:airDropId",
        name: "AirDropPreview",
        meta: {
          title: "创作空投结果",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDrop/AirDropPreview"),
      },
      // 空投管理
      {
        path: "air-drop-collection",
        name: "AirDropCollection",
        meta: {
          title: "藏品空投管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDropCollection"),
      },
      {
        path: "air-drop-preview-collection/:airDropId",
        name: "AirDropPreviewCollection",
        meta: {
          title: "藏品空投结果",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDropCollection/AirDropPreview"),
      },
      // 短链接管理
      {
        path: "short-link",
        name: "ShortLink",
        meta: {
          title: "短链接管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/shortLink"),
      },
      // 支付相关 - 银行卡列表
      {
        path: "PaymentRelated/bankList",
        name: "BankList",
        meta: {
          title: "绑卡列表",
          auth: true,
          cache: true,
        },
        component: _import("PaymentRelated/BankList"),
      },
      // 支付相关 - 银行卡列表
      {
        path: "PaymentRelated/depositList",
        name: "DepositList",
        meta: {
          title: "充值列表",
          auth: true,
          cache: true,
        },
        component: _import("PaymentRelated/DepositList"),
      },
      // 活动管理
      {
        path: "activity",
        name: "ActivityManage",
        meta: {
          title: "活动管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity"),
      },
      // 活动管理
      {
        path: "activity2",
        name: "activity2",
        meta: {
          title: "合成配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity"),
      },
      // activity2
      // 拉新
      {
        path: "activity/invite",
        name: "ActivityPullNew",
        meta: {
          title: "拉新活动",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/Invite"),
      },
      // 熔炉
      {
        path: "activity/rebuild",
        name: "ActivityRebuild",
        meta: {
          title: "熔炉活动",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/Rebuild"),
      },
      // 熔炉
      {
        path: "activity/rebuild2",
        name: "ActivityRebuild2",
        meta: {
          title: "熔炉活动",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/Rebuild"),
      },
      // 群聊全局配置
      {
        path: "chat",
        name: "ChatConfig",
        meta: {
          title: "群聊列表",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig"),
      },
      {
        path: "ChatList",
        name: "ChatList",
        meta: {
          title: "群聊列表",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/ChatList"),
      },
      // 未入群用户列表
      {
        path: "NotInGroupList",
        name: "NotInGroupList",
        meta: {
          title: "未入群用户列表",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/NotInGroupList"),
      },
      // 群成员信息
      {
        path: "chat/MemberList",
        name: "MemberList",
        meta: {
          title: "群成员信息",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/MemberList"),
      },
      // 管理／加入的群
      {
        path: "chat/MemberJoinedGroup",
        name: "MemberJoinedGroup",
        meta: {
          title: "管理／加入的群",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/MemberJoinedGroup"),
      },
      // 群公告记录
      {
        path: "chat/GroupNoticeRecord",
        name: "GroupNoticeRecord",
        meta: {
          title: "群公告记录",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/GroupNoticeRecord"),
      },
      // 群聊记录
      {
        path: "chat/GroupChatHistory",
        name: "GroupChatHistory",
        meta: {
          title: "群聊记录",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/GroupChatHistory"),
      },
      // 持有群主作品
      {
        path: "chat/MemberGood",
        name: "MemberGood",
        meta: {
          title: "持有群主作品",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/MemberGood"),
      },
      // 举报列表
      {
        path: "ReportList",
        name: "ReportList",
        meta: {
          title: "举报列表",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/ReportList"),
      },
      // 举报详情
      {
        path: "chat/ReportDetail",
        name: "ReportDetail",
        meta: {
          title: "举报详情",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/ReportDetail"),
      },
      // 聊天记录
      {
        path: "ChatRecord",
        name: "ChatRecord",
        meta: {
          title: "群聊记录",
          auth: true,
          cache: true,
        },
        component: _import("ChatConfig/ChatRecord"),
      },
      // 飞跃计划签约公户
      {
        path: "leapPlan",
        name: "leapPlan",
        meta: {
          title: "飞跃计划签约用户",
          auth: true,
          cache: true,
        },
        component: _import("leapPlan/index"),
      },
      // 查询报文
      {
        path: "DevTools",
        name: "DevTools",
        meta: {
          title: "开发工具",
          auth: true,
        },
        component: _import("developer/index"),
      },
      {
        path: "/logs/admin-login",
        name: "AdminLoginLogs",
        meta: {
          title: "后台登录日志",
        },
        component: () => import("@/views/Logs/AdminLoginLogs.vue"),
      },
      {
        path: "/logs/nftcn-login",
        name: "nftcnLoginLogs",
        meta: {
          title: "用户登录日志",
        },
        component: () => import("@/views/Logs/nftcnLoginLogs.vue"),
      },
      {
        path: "/logs/request-log",
        name: "requestLog",
        meta: {
          title: "后台操作日志",
        },
        component: () => import("@/views/Logs/requestLog.vue"),
      },
      {
        path: "/logs/admin-login",
        name: "AdminLoginLogs",
        meta: {
          title: "登录日志",
        },
        component: () => import("@/views/Logs/AdminLoginLogs.vue"),
      },
      // 刷新页面 必须保留
      {
        path: "refresh",
        name: "refresh",
        hidden: true,
        component: _import("system/function/refresh"),
      },
      // 页面重定向 必须保留
      {
        path: "redirect/:route*",
        name: "redirect",
        hidden: true,
        component: _import("system/function/redirect"),
      },
      // 平台公告
      {
        path: "platform",
        name: "platform",
        meta: {
          title: "平台公告",
          auth: true,
          cache: true,
        },
        component: _import("platform"),
      },
      // 编辑发布公告
      {
        path: "platformPublish",
        name: "platformPublish",
        meta: {
          title: "发布公告",
          auth: true,
          cache: true,
        },
        component: _import("platform/NoticeAdd"),
      },
      {
        path: "auditNoAudit",
        name: "auditNoAudit",
        meta: {
          title: "作品免审名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/auditNoAudit"),
      },
      {
        path: "bodeConfig",
        name: "bodeConfig",
        meta: {
          title: "伯德全局配置",
          cache: true,
          auth: true,
        },
        component: _import("bode/bodeConfig"),
      },
      {
        path: "bodeList",
        name: "bodeList",
        meta: {
          title: "伯德列表",
          cache: true,
          auth: true,
        },
        component: _import("bode/bodeList"),
      },
      {
        path: "native-config",
        name: "native-config",
        meta: {
          title: "原生链接配置",
          auth: true,
          cache: true,
        },
        component: _import("marketConfig/nativeConfig"),
      },
      {
        path: "timing-manag",
        name: "timing-manag",
        meta: {
          title: "定时任务",
          cache: true,
          auth: true,
        },
        component: _import("timingManag"),
      },
      {
        path: "timing-addupdate",
        name: "timing-addupdate",
        meta: {
          title: "任务管理",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/addupdate"),
      },
      {
        path: "batchList",
        name: "batchList",
        meta: {
          title: "批量管理",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/batchList"),
      },
      {
        path: "batchAddupdate",
        name: "batchAddupdate",
        meta: {
          title: "批量管理",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/batchAddupdate"),
      },
      {
        path: "open-api-list",
        name: "open-api-list",
        meta: {
          title: "openApi",
          cache: true,
          auth: true,
        },
        component: _import("openApi/list"),
      },
      {
        path: "target-duty",
        name: "target-duty",
        meta: {
          title: "求购任务白名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/targetDuty"),
      },
      {
        path: "idCard-black",
        name: "idCard-black",
        meta: {
          title: "身份证禁止实名名单",
          cache: true,
          auth: true,
        },
        component: _import("blackList/idCardBlack"),
      },
      {
        path: "worm-shop-list",
        name: "worm-shop-list",
        meta: {
          title: "商品列表",
          cache: true,
          auth: true,
        },
        component: _import("wormShop/index"),
      },
      {
        path: "exchange",
        name: "exchange",
        meta: {
          title: "兑换记录",
          cache: true,
          auth: true,
        },
        component: _import("wormShop/exchange"),
      },
      {
        path: "wormshop-balance",
        name: "wormshop-balance",
        meta: {
          title: "虫子余额",
          cache: true,
          auth: true,
        },
        component: _import("wormShop/balance"),
      },
      {
        path: "casting",
        name: "casting",
        meta: {
          title: "铸造作品",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/casting"),
      },
      {
        path: "alipay",
        name: "alipay",
        meta: {
          title: "支付宝提现",
          cache: true,
          auth: true,
        },
        component: _import("withdraw/alipay"),
      },
      {
        path: "buyList",
        name: "buyList",
        meta: {
          title: "求购列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/buyList"),
      },
      {
        path: "Bidding",
        name: "Bidding",
        meta: {
          title: "竞价列表",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/Bidding"),
      },
      {
        path: "proportion",
        name: "proportion",
        meta: {
          title: "查换手",
          cache: true,
          auth: true,
        },
        component: _import("statement/proportion"),
      },
      {
        path: "gzz",
        name: "gzz",
        meta: {
          title: "仅供收藏数据看板",
          cache: true,
          auth: true,
        },
        component: _import("statement/gzz"),
      },
      {
        path: "data_list",
        name: "data_list",
        meta: {
          title: "浏览最多",
          cache: true,
          auth: true,
        },
        component: _import("statement/data_list"),
      },
      {
        path: "pv_uv",
        name: "pv_uv",
        meta: {
          title: "PV/UV",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/statistics/pv_uv"),
      },
      {
        path: "indexOperationOrder",
        name: "indexOperationOrder",
        meta: {
          title: "订单管理(运营)",
          cache: true,
          auth: true,
        },
        component: _import("order/indexOperationOrder"),
      },
      {
        path: "todayNotice",
        name: "todayNotice",
        meta: {
          title: "今日公告",
          cache: false,
          auth: true,
        },
        component: _import("platform/todayNotice"),
      },
      {
        path: "tomorrowNotice",
        name: "tomorrowNotice",
        meta: {
          title: "明日公告",
          cache: false,
          auth: true,
        },
        component: _import("platform/tomorrowNotice"),
      },
      {
        path: "NoticeEdit",
        name: "NoticeEdit",
        meta: {
          title: "公告修改",
          cache: true,
          auth: true,
        },
        component: _import("platform/NoticeEdit"),
      },
      {
        path: "air-drop-compound",
        name: "airDropcompound",
        meta: {
          title: "合成空投管理",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDropcompound"),
      },
      {
        path: "air-drop-preview-compound/:airDropId",
        name: "AirDropPreviewCompound",
        meta: {
          title: "合成空投结果",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/airDropcompound/AirDropPreview"),
      },
      {
        path: "deal",
        name: "deal",
        meta: {
          title: "设置卖出",
          cache: true,
          auth: true,
        },
        component: _import("deal/index"),
      },
      {
        path: "taskAdd",
        name: "taskAdd",
        meta: {
          title: "创建卖出任务",
          cache: true,
          auth: true,
        },
        component: _import("deal/taskAdd"),
      },
      {
        path: "volIndex",
        name: "volIndex",
        meta: {
          title: "自动成交",
          cache: true,
          auth: true,
        },
        component: _import("deal/volIndex"),
      },
      {
        path: "volAdd",
        name: "volAdd",
        meta: {
          title: "创建自动成交",
          cache: true,
          auth: true,
        },
        component: _import("deal/volAdd"),
      },
      {
        path: "scan",
        name: "scan",
        meta: {
          title: "扫描白名单",
          cache: false,
          auth: true,
        },
        component: _import("scan/index"),
      },
      {
        path: "scanAdd",
        name: "scanAdd",
        meta: {
          title: "新增白名单扫描",
          cache: true,
          auth: true,
        },
        component: _import("scan/scanAdd"),
      },
      {
        path: "group_number",
        name: "group_number",
        meta: {
          title: "群聊人数",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/groupChatNumber"),
      },
      {
        path: "group_number_add",
        name: "group_number_add",
        meta: {
          title: "群聊人数添加",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/groupChatNumberAdd"),
      },
      {
        path: "freightSpace",
        name: "freightSpace",
        meta: {
          title: "仓位看板",
          cache: true,
          auth: true,
        },
        component: _import("statement/freightSpace"),
      },
      {
        path: "finance",
        name: "finance",
        meta: {
          title: "财务核算",
          cache: true,
          auth: true,
        },
        component: _import("statement/finance"),
      },
      {
        path: "stairAirDrop",
        name: "stairAirDrop",
        meta: {
          title: "一级空投",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/stairAirDrop"),
      },
      {
        path: "stairAirDrop_add",
        name: "stairAirDrop_add",
        meta: {
          title: "新增一级空投",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/stairAirDrop/add"),
      },
      {
        path: "financeUserView",
        name: "financeUserView",
        meta: {
          title: "用户余额",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/userView"),
      },
      {
        path: "particulars_commission",
        name: "particulars_commission",
        meta: {
          title: "佣金明细",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/particulars/commission"),
      },
      {
        path: "particulars_rotate",
        name: "particulars_rotate",
        meta: {
          title: "版税明细",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/particulars/rotate"),
      },
      {
        path: "particulars_withdraw",
        name: "particulars_withdraw",
        meta: {
          title: "提现明细",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/particulars/withdraw"),
      },
      {
        path: "particulars_channel",
        name: "particulars_channel",
        meta: {
          title: "渠道成本",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/particulars/channel"),
      },
      {
        path: "cw_order",
        name: "cw_order",
        meta: {
          title: "订单管理(财务)",
          cache: true,
          auth: true,
        },
        component: _import("financeIndex/cw_order"),
      },
      // {
      //   path: 'nft_income',
      //   name: 'nft_income',
      //   meta: {
      //     title: '收入汇总',
      //     cache: true,
      //     auth: true
      //   },
      //   component: _import('financeIndex/nft_income')
      // },
      {
        path: "elseIndex",
        name: "elseIndex",
        meta: {
          title: "其他活动",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/elseIndex"),
      },
      {
        path: "voteIndex",
        name: "voteIndex",
        meta: {
          title: "投票",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/voteIndex"),
      },
      {
        path: "payBroadcast",
        name: "payBroadcast",
        meta: {
          title: "交易播报",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/payBroadcast"),
      },
      {
        path: "payBroadcastAdd",
        name: "payBroadcastAdd",
        meta: {
          title: "添加交易播报",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/payBroadcastAdd"),
      },
      {
        path: "batchListing",
        name: "batchListing",
        meta: {
          title: "批量上架",
          cache: true,
          auth: true,
        },
        component: _import("deal/batchListing"),
      },
      {
        path: "batchListingAdd",
        name: "batchListingAdd",
        meta: {
          title: "批量上架",
          cache: true,
          auth: true,
        },
        component: _import("deal/batchListingAdd"),
      },
      {
        path: "kanban",
        name: "kanban",
        meta: {
          title: "实时大屏",
          cache: true,
          auth: true,
        },
        component: _import("kanban/index"),
      },
      {
        path: "weChatCode",
        name: "weChatCode",
        meta: {
          title: "微信二维码",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/weChatCode"),
      },
      // 新版举报列表
      {
        path: "ReportListNew",
        name: "ReportListNew",
        meta: {
          title: "举报列表",
          cache: true,
          auth: true,
        },
        component: _import("ChatConfig/ReportListNew"),
      },
      // 新版举报列表
      {
        path: "upRoll",
        name: "upRoll",
        meta: {
          title: "向上滚动交易",
          cache: true,
          auth: true,
        },
        component: _import("deal/upRoll"),
      },
      {
        path: "upRollAdd",
        name: "upRollAdd",
        meta: {
          title: "向上滚动交易",
          cache: true,
          auth: true,
        },
        component: _import("deal/upRollAdd"),
      },
      {
        path: "autoLockOrder",
        name: "autoLockOrder",
        meta: {
          title: "自动锁单",
          cache: true,
          auth: true,
        },
        component: _import("deal/autoLockOrder"),
      },
      {
        path: "autoLockOrderAdd",
        name: "autoLockOrderAdd",
        meta: {
          title: "新建自动锁单",
          cache: true,
          auth: true,
        },
        component: _import("deal/autoLockOrderAdd"),
      },
      {
        path: "sprog",
        name: "sprog",
        meta: {
          title: "新手池",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/config/sprog"),
      },
      {
        path: "transition",
        name: "transition",
        meta: {
          title: "过渡池",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/config/transition"),
      },
      {
        path: "normal",
        name: "normal",
        meta: {
          title: "普通池",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/config/normal"),
      },
      {
        path: "zoonDropGear",
        name: "zoonDropGear",
        meta: {
          title: "动物掉落权重",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/dropGear/zoonDropGear"),
      },
      {
        path: "configOut",
        name: "configOut",
        meta: {
          title: "动物出场配置",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/dropGear/configOut"),
      },
      {
        path: "payBuff",
        name: "payBuff",
        meta: {
          title: "充值buff",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/payBuff/payBuff"),
      },
      {
        path: "platformNiu",
        name: "platformNiu",
        meta: {
          title: "牛牛公告列表",
          cache: true,
          auth: true,
        },
        component: _import("niuniu/platform/index"),
      },
      {
        path: "appCover",
        name: "appCover",
        meta: {
          title: "APP开屏页修改",
          cache: true,
          auth: true,
        },
        component: _import("configuration/appCover"),
      },
      {
        path: "config",
        name: "config",
        meta: {
          title: "起飞卡配置",
          cache: true,
          auth: true,
        },
        component: _import("configuration/config"),
      },
      {
        path: "turnover",
        name: "turnover",
        meta: {
          title: "流通量配置",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/turnover"),
      },
      {
        path: "tagManage",
        name: "tagManage",
        meta: {
          title: "标签管理",
          cache: true,
          auth: true,
        },
        component: _import("tagManage/index"),
      },
      {
        path: "player",
        name: "player",
        meta: {
          title: "边玩边赚",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/player"),
      },
      {
        path: "informationConfig",
        name: "informationConfig",
        meta: {
          title: "资讯配置",
          cache: true,
          auth: true,
        },
        component: _import("timingManag/informationConfig"),
      },
      {
        path: "twoDrop",
        name: "twoDrop",
        meta: {
          title: "二级空投",
          cache: true,
          auth: true,
        },
        component: _import("deal/twoDrop"),
      },
      {
        path: "twoDropError",
        name: "twoDropError",
        meta: {
          title: "二级空投失败列表",
          cache: true,
          auth: true,
        },
        component: _import("deal/twoDropError"),
      },
      {
        path: "openWarehouse",
        name: "openWarehouse",
        meta: {
          title: "自动开仓",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/openWarehouse"),
      },
      {
        path: "handWarehouse",
        name: "handWarehouse",
        meta: {
          title: "手动开仓",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/handWarehouse"),
      },
      {
        path: "handClosehouse",
        name: "handClosehouse",
        meta: {
          title: "手动平仓",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/handClosehouse"),
      },
      {
        path: "openClosehouse",
        name: "openClosehouse",
        meta: {
          title: "自动平仓",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/openClosehouse"),
      },
      {
        path: "fluctuationAssistant",
        name: "fluctuationAssistant",
        meta: {
          title: "波动小助理",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/fluctuationAssistant"),
      },
      {
        path: "remoteAssistant",
        name: "remoteAssistant",
        meta: {
          title: "超远期助理",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/remoteAssistant"),
      },
      {
        path: "transactionBuyRecord",
        name: "transactionBuyRecord",
        meta: {
          title: "开仓明细",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/transactionRecord_buy"),
      },
      {
        path: "BITShopOrderParameters",
        name: "BITShopOrderParameters",
        meta: {
          title: "铺单参数",
          cache: true,
          auth: true,
        },
        component: _import("BIT/ShopOrderParameters"),
      },
      {
        path: "BIThold",
        name: "BIThold",
        meta: {
          title: "持仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BIT/hold"),
      },
      {
        path: "BITboom",
        name: "BITboom",
        meta: {
          title: "爆仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BIT/boom"),
      },
      {
        path: "BITTrade",
        name: "BITTrade",
        meta: {
          title: "交易大赛",
          cache: true,
          auth: true,
        },
        component: _import("BIT/TradeCom"),
      },
      {
        path: "BITvisit",
        name: "BITvisit",
        meta: {
          title: "访问信息",
          cache: true,
          auth: true,
        },
        component: _import("BIT/visit"),
      },
      {
        path: "BITMain",
        name: "BITMain",
        meta: {
          title: "主力委托",
          cache: true,
          auth: true,
        },
        component: _import("BIT/mainEntru"),
      },
      {
        path: "BITCard",
        name: "BITCard",
        meta: {
          title: "风控看板",
          cache: true,
          auth: true,
        },
        component: _import("BIT/card"),
      },
      {
        path: "TradeDetails",
        name: "TradeDetails",
        meta: {
          title: "成交明细",
          cache: true,
          auth: true,
        },
        component: _import("BIT/TradeDetails"),
      },

      {
        path: "BITtransactionBuyRecord",
        name: "BITtransactionBuyRecord",
        meta: {
          title: "开仓明细",
          cache: true,
          auth: true,
        },
        component: _import("BIT/transactionRecord_buy"),
      },
      {
        path: "BITtransactionSellRecord",
        name: "BITtransactionSellRecord",
        meta: {
          title: "平仓明细",
          cache: true,
          auth: true,
        },
        component: _import("BIT/transactionRecord_Sell"),
      },
      {
        path: "transactionSellRecord",
        name: "transactionSellRecord",
        meta: {
          title: "平仓明细",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/transactionRecord_Sell"),
      },
      {
        path: "openWareHouseRecond",
        name: "openWareHouseRecond",
        meta: {
          title: "开仓记录",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/openWareHouseRecond"),
      },
      {
        path: "BITopenWareHouseRecond",
        name: "BITopenWareHouseRecond",
        meta: {
          title: "开仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BIT/openWareHouseRecond"),
      },
      {
        path: "BITprofit",
        name: "BITprofit",
        meta: {
          title: "每日盈利",
          cache: true,
          auth: true,
        },
        component: _import("BIT/everydayProfit"),
      },
      {
        path: "BITcloseHouseRecond",
        name: "BITcloseHouseRecond",
        meta: {
          title: "平仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BIT/closeHouseRecond"),
      },
      {
        path: "closeHouseRecond",
        name: "closeHouseRecond",
        meta: {
          title: "平仓记录",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/closeHouseRecond"),
      },
      {
        path: "handleWareHouseRecond",
        name: "handleWareHouseRecond",
        meta: {
          title: "当前持仓",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/handleWareHouseRecond"),
      },
      {
        path: "handicapLog",
        name: "handicapLog",
        meta: {
          title: "盘口日志",
          cache: true,
          auth: true,
        },
        component: _import("ALYviews/handicapLog"),
      },
      {
        path: "BIThandicapLog",
        name: "BIThandicapLog",
        meta: {
          title: "盘口日志",
          cache: true,
          auth: true,
        },
        component: _import("BIT/handicapLog"),
      },
      {
        path: "echartsXXD",
        name: "echartsXXD",
        meta: {
          title: "起飞卡数据-新下单",
          cache: true,
          auth: true,
        },
        component: _import("statement/echartsXXD"),
      },
      {
        path: "echartsCC",
        name: "echartsCC",
        meta: {
          title: "起飞卡数据-持仓成本",
          cache: true,
          auth: true,
        },
        component: _import("statement/echartsCC"),
      },
      {
        path: "echartsGG",
        name: "echartsGG",
        meta: {
          title: "起飞卡数据-杠杆强平",
          cache: true,
          auth: true,
        },
        component: _import("statement/echartsGG"),
      },
      {
        path: "echartsK",
        name: "echartsK",
        meta: {
          title: "起飞卡数据-成交K线",
          cache: true,
          auth: true,
        },
        component: _import("statement/echartsK"),
      },
      {
        path: "economyIndex",
        name: "economyIndex",
        meta: {
          title: "新入驻申请",
          cache: true,
          auth: true,
        },
        component: _import("economy/index"),
      },
      {
        path: "AutomaticExamine",
        name: "AutomaticExamine",
        meta: {
          title: "自动考核",
          cache: true,
          auth: true,
        },
        component: _import("economy/examine"),
      },
      {
        path: "record",
        name: "record",
        meta: {
          title: "群聊记录",
          cache: true,
          auth: true,
        },
        component: _import("groupChat/record"),
      },
      {
        path: "plugSet",
        name: "plugSet",
        meta: {
          title: "推流设置",
          cache: true,
          auth: true,
        },
        component: _import("groupChat/plugSet"),
      },
      {
        path: "userLimits",
        name: "userLimits",
        meta: {
          title: "用户权限",
          cache: true,
          auth: true,
        },
        component: _import("groupChat/userLimits"),
      },
      {
        path: "ugcCreation",
        name: "ugcCreation",
        meta: {
          title: "ugc代铸造",
          cache: true,
          auth: true,
        },
        component: _import("ugc/casting"),
      },
      {
        path: "ugcOrder",
        name: "ugcOrder",
        meta: {
          title: "ugc创建订单",
          cache: true,
          auth: true,
        },
        component: _import("ugc/order"),
      },
      {
        path: "vipConfig",
        name: "vipConfig",
        meta: {
          title: "富豪榜配置",
          cache: true,
          auth: true,
        },
        component: _import("developer/vipConfig"),
      },
      {
        path: "tableUnionSearch",
        name: "tableUnionSearch",
        meta: {
          title: "导出信息",
          cache: true,
          auth: true,
        },
        component: _import("tableUnionSearch/index"),
      },
      {
        path: "createOrder",
        name: "createOrder",
        meta: {
          title: "创建订单",
          cache: true,
          auth: true,
        },
        component: _import("createOrder/index"),
      },
      {
        path: "riskBoard",
        name: "riskBoard",
        meta: {
          title: "风控看板",
          cache: true,
          auth: true,
        },
        component: _import("riskBoard/index"),
      },
      {
        path: "bitriskBoard",
        name: "bitriskBoard",
        meta: {
          title: "BIT收益最高",
          cache: true,
          auth: true,
        },
        component: _import("BIT/riskBoard"),
      },
      {
        path: "seriesUs",
        name: "seriesUs",
        meta: {
          title: "模拟美股",
          cache: true,
          auth: true,
        },
        component: _import("seriesUs/index"),
      },
      {
        path: "detailsList",
        name: "detailsList",
        meta: {
          title: "模拟美股-明细",
          cache: true,
          auth: true,
        },
        component: _import("seriesUs/detailsList"),
      },
      {
        path: "economyClass",
        name: "economyClass",
        meta: {
          title: "经纪商分组",
          cache: true,
          auth: true,
        },
        component: _import("economy/economyClass"),
      },
      {
        path: "icebound",
        name: "icebound",
        meta: {
          title: "冰封",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/activity/icebound"),
      },
      {
        path: "blacklist",
        name: "blacklist",
        meta: {
          title: "提现黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/black/index"),
      },
      {
        path: "reallist",
        name: "reallist",
        meta: {
          title: "实名黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/black/idcardBan"),
      },
      {
        path: "ipgray",
        name: "ipgray",
        meta: {
          title: "ip灰名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/gray/ipGray"),
      },
      {
        path: "workerGray",
        name: "workerGray",
        meta: {
          title: "工作人员灰名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/gray/worker"),
      },
      {
        path: "buylistBlack",
        name: "buylistBlack",
        meta: {
          title: "购买黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/black/buylist"),
      },
      {
        path: "givelist",
        name: "givelist",
        meta: {
          title: "购买转增黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/black/givelist"),
      },
      {
        path: "accountBan",
        name: "accountBan",
        meta: {
          title: "用户账号禁用黑名单",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/namelist/black/accountBan"),
      },
      {
        path: "Riskuserlist",
        name: "Riskuserlist",
        meta: {
          title: "用户列表（风控）",
          cache: true,
          auth: true,
        },
        component: _import("BITrisk/Users/<USER>/userlist"),
      },
      {
        path: "BITconfig",
        name: "BITconfig",
        meta: {
          title: "配置",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/ShopOrderParameters"),
      },
      {
        path: "BITlist",
        name: "BITlist",
        meta: {
          title: "币对",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/symbolList"),
      },
      {
        path: "BITconfigcloseHouseRecond",
        name: "BITconfigcloseHouseRecond",
        meta: {
          title: "平仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/closeHouseRecond"),
      },
      {
        path: "BITconfigCard",
        name: "BITconfigCard",
        meta: {
          title: "风控看板",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/card"),
      },
      {
        path: "BITconfigboom",
        name: "BITconfigboom",
        meta: {
          title: "爆仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/boom"),
      },
      // 盘口日志
      {
        path: "BITconfigBoard",
        name: "BITconfigBoard",
        meta: {
          title: "盘口日志",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/handicapLog"),
      },
      // 持仓记录
      {
        path: "BITconfigPosition",
        name: "BITconfigPosition",
        meta: {
          title: "持仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/hold"),
      },
      // 开仓记录
      {
        path: "BITconfigOpen",
        name: "BITconfigOpen",
        meta: {
          title: "开仓记录",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/openWareHouseRecond"),
      },
      {
        path: "BITconfigriskBoard",
        name: "BITconfigriskBoard",
        meta: {
          title: "收益最高",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/riskBoard"),
      },
      // 主力委托
      {
        path: "BITconfigMain",
        name: "BITconfigMain",
        meta: {
          title: "主力委托",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/mainEntru"),
      },
      // 成交明细
      {
        path: "BITconfigDeal",
        name: "BITconfigDeal",
        meta: {
          title: "成交明细",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/TradeDetails"),
      },
      // 访问信息
      {
        path: "BITconfigVisit",
        name: "BITconfigVisit",
        meta: {
          title: "访问信息",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/visit"),
      },
      // 所有群聊
      {
        path: "IMgroup",
        name: "IMgroup",
        meta: {
          title: "所有群聊",
          cache: true,
          auth: true,
        },
        component: _import("IM/AllGroup"),
      },
      // 群聊用户管理
      {
        path: "IMgroupUser",
        name: "IMgroupUser",
        meta: {
          title: "群聊用户管理",
          cache: true,
          auth: true,
        },
        component: _import("IM/User"),
      },
      // 群发消息
      {
        path: "IMsendMessage",
        name: "IMsendMessage",
        meta: {
          title: "群发消息",
          cache: true,
          auth: true,
        },
        component: _import("IM/GroupSend"),
      },
      // workerGray
      {
        path: "PFPshare",
        name: "PFPshare",
        meta: {
          title: "分享入群二维码",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPshare"),
      },
      {
        path: "PFPconfig",
        name: "PFPconfig",
        meta: {
          title: "基础配置",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPconfig"),
      },
      {
        path: "PFPshop",
        name: "PFPshop",
        meta: {
          title: "PFP商品列表",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPshop"),
      },
      {
        path: "PFPuser",
        name: "PFPuser",
        meta: {
          title: "PFP用户列表",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPuser"),
      },
      {
        path: "PFPinvite",
        name: "PFPinvite",
        meta: {
          title: "PFP邀请记录",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPinvite"),
      },
      {
        path: "PFPorder",
        name: "PFPorder",
        meta: {
          title: "PFP转移记录",
          cache: true,
          auth: true,
        },
        component: _import("long/PFPorder"),
      },
      {
        path: "Experience",
        name: "Experience",
        meta: {
          title: "体验金",
          cache: true,
          auth: true,
        },
        component: _import("BIT/Experience"),
      },
      {
        path: "bzl_icebound",
        name: "bzl_icebound",
        meta: {
          title: "质押配置",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/icebound"),
      },
      {
        path: "/staking",
        name: "staking",
        meta: {
          title: "质押记录",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/staking"),
      },
      {
        path: "Longcasting",
        name: "Longcasting",
        meta: {
          title: "铸造藏品",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/casting"),
      },
      {
        path: "FirstRelease",
        name: "FirstRelease",
        meta: {
          title: "首发藏品",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/FirstRelease"),
      },
      {
        path: "SeriesListlong",
        name: "SeriesListlong",
        meta: {
          title: "系列列表",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/SeriesList"),
      },
      {
        path: "platformBzl",
        name: "platformBzl",
        meta: {
          title: "暴躁龙-公告列表",
          cache: true,
          auth: true,
        },
        component: _import("platform/platformBzl"),
      },
      {
        path: "Application",
        name: "Application",
        meta: {
          title: "申请记录",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/Application"),
      },
      {
        path: "AllianceList",
        name: "AllianceList",
        meta: {
          title: "联盟列表",
          cache: true,
          auth: true,
        },
        component: _import("long/activity/AllianceList"),
      },
      {
        path: "FlowArrangement",
        name: "FlowArrangement",
        meta: {
          title: "流量配置",
          cache: true,
          auth: true,
        },
        component: _import("BIT/FlowArrangement"),
      },
      // 添加/编辑banner配置
      {
        path: "addBannerYs",
        name: "addBannerYs",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig/addBannerYs"),
      },
      {
        path: "userTransfer",
        name: "userTransfer",
        meta: {
          title: "账号转移",
          cache: true,
          auth: true,
        },
        component: _import("configuration/userTransfer"),
      },
      // 添加/编辑banner配置
      {
        path: "addBannerYs",
        name: "addBannerYs",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("marketConfig/bannerConfig/addBannerYs"),
      },
      {
        path: "MagnumGold",
        name: "MagnumGold",
        meta: {
          title: "万能金",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/MagnumGold"),
      },
      {
        path: "indexHash",
        name: "indexHash",
        meta: {
          title: "生成Hash",
          cache: true,
          auth: true,
        },
        component: _import("developer/indexHash"),
      },
      // InvitationList
      {
        path: "InvitationList",
        name: "InvitationList",
        meta: {
          title: "邀请列表",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/InvitationList"),
      },
      {
        path: "PartnerRebates",
        name: "PartnerRebates",
        meta: {
          title: "合伙人返佣",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/PartnerRebates"),
      },
      {
        path: "AutoWelfare",
        name: "AutoWelfare",
        meta: {
          title: "自动发福利",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/AutoWelfare"),
      },
      {
        path: "AutoStatisticsDistributionGold",
        name: "AutoStatisticsDistributionGold",
        meta: {
          title: "万能金活动发放统计",
          cache: true,
          auth: true,
        },
        component: _import("BITindicators/AutoStatisticsDistributionGold"),
      },
      {
        path: "todayNoticeBzl",
        name: "todayNoticeBzl",
        meta: {
          title: "暴躁龙今日公告",
          cache: true,
          auth: true,
        },
        component: _import("platform/todayNoticeBzl"),
      },
      {
        path: "NoticeEditBzl",
        name: "NoticeEditBzl",
        meta: {
          title: "暴躁龙公告修改",
          cache: true,
          auth: true,
        },
        component: _import("platform/NoticeEditBzl"),
      },
    ],
  },
];

/**
 * 在主框架之外显示
 */
const frameOut = [
  // 登录
  {
    path: "/login",
    name: "login",
    component: _import("system/login"),
  },
];

/**
 * 错误页面
 */
const errorPage = [
  {
    path: "*",
    name: "404",
    component: _import("system/error/404"),
  },
];

// 导出需要显示菜单的
export const frameInRoutes = frameIn;

// 重新组织后导出
export default [...frameIn, ...frameOut, ...errorPage];
