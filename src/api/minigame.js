import request from '@/utils/request.js'

const serviceUrl = '/minigame'

/**
 * 获取游戏item列表
 */
export const gameItemList = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/common/gameItemList`, data)

/**
 * 游戏item新增
 */
export const gameItemAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/bannerItem/add`, data)

/**
 * 游戏item删除
 */
export const gameItemDel = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/bannerItem/del`, data)

/**
 * 游戏item修改
 */
export const gameItemUpd = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/bannerItem/upd`, data)

/**
 * 获取游戏item列表
 */
export const gameItemQry = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/bannerItem/qry`, data)

export const qryUser = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/minigameUser/qryUser`, data)

export const registerUser = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/minigameUser/registerUser`, data)

export const updUserTag = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/minigameUser/updUserTag`, data)

export const operateUserBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/minigameUser/operateUserBalance`, data)
