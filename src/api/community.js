import request from '@/utils/request.js'

const serviceUrl = '/community'

/**
 * 作品评论审核列表
 */
export const listPageCommentVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/comment/listPageCommentVerify`, data)

/**
 * 作品评论审核列表
 */
export const commentVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/comment/commentVerify`, data)

/**
 * 作品评论批量删除
 */
export const commentRemoveBatch = (data) =>
  request.post(`${serviceUrl}/adminApi/comment/commentRemoveBatch`, data)

/**
 * 作品评论删除
 */
export const commentRemove = (data) =>
  request.post(`${serviceUrl}/adminApi/comment/commentRemove`, data)
