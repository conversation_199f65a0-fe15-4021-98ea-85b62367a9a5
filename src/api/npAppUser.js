import request from "@/utils/request.js";

const serviceUrl = "/np-app-user-yanjie";
const userServiceUrl = "/np-app-user-yanjie";

/**
 *  用户密码登录
 */
// export const login = (data) =>
//   request.post(`${serviceUrl}/adminApi/login/usernamePasswordLogin`, data);

/**
 * ROOT管理台用户创建
 */
export const adminUserAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUser/add`, data);

/**
 * ROOT管理台用户列表
 */
export const adminUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUser/list`, data);

/**
 * 修改用户基本信息
 */
export const updateAdminUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/updateAdminUserInfo`, data);

/**
 * 获取用户分页列表
 */
export const listPageAdminUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/listPageAdminUser`, data);

/**
 *
 * 离职关闭账号
 */
export const dismissClose = (data) =>
  request.post(`${serviceUrl}/adminApi/user/quitCloseAdminUser`, data);

/**
 * 修改用户密码
 */
export const updateUserPassWord = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUser/updateLoginPassword`, data);

/**
 * 查看用户个人信息
 */
export const showAdminUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUser/info`, data);

/**
 * 获取后台管理权限列表
 */
export const listPageAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/permission/list`, data);

/**
 * 创建后台权限
 */
export const createAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/permission/add`, data);

/**
 * 修改后台权限
 */
export const updateAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/permission/update`, data);

// /**
//  * 删除后台权限
//  */
// export const removeAdminPermission = (data) =>
//   request.post(`${serviceUrl}/adminApi/permission/delete`, data);

/**
 * 用户查询权限
 */
export const showAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUserPermission/show`, data);

/**
 * 添加用户权限
 */
export const addAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUserPermission/add`, data);

/**
 * 删除用户权限
 */

export const removeAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUserPermission/delete`, data);

/**
 * 基础配置列表
 */
export const getPageOptionsVo = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/getPageOptionsVo`, data);

/**
 * App基础配置列表
 */
export const getAllDTOList = (data) =>
  request.post(`${serviceUrl}/adminApi/appVersion/getAllDTOList`, data);

/**
 * App基础配置列表
 */
export const AppsaveOrUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/appVersion/saveOrUpdate`, data);

/**
 * 更新配置信息
 */
export const saveOrUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/saveOrUpdate`, data);

/**
 * 获取字典分页列表
 */
export const getDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/getPageDictTypesVo`, data);

/**
 * 保存字典 新增和修改：
 */
export const saveDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/saveDictType`, data);

/**
 * 删除字典
 */
export const deleteDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/deleteDictTyp`, data);

/**
 * 获取字典数据分页列表
 */
export const getDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/getPageDictDatasVo`, data);

/**
 * 保存字典
 */
export const saveDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/saveDictData`, data);

/**
 * 删除字典
 */
export const deleteDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/deleteDictData`, data);



/**
 * 主页图表数据
 */
export const getCharts = (data) =>
  request.post(`${serviceUrl}/adminApi/sls/website/volumes/list`, data);

/**
 * 获得当前伯德寿命是否展示
 */
export const getValueByName = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/getValueByName`, data);

/**
 * 用户列表
 */
export const getUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/list`, data);

/**
 * 用户列表 批量封禁/解封
 */
export const batchUpdateStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/batchUpdateStatus`, data);

//手机号注册导入
export const importPhone = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/importPhone`, data);

//新版本
/**
 * 用户登录日志
 */
export const getNftcnLoginLogs = (data) =>
  request.post(`${userServiceUrl}/adminApi/appUserLoginLog/list`, data);

/**
 * 后台接口操作日志
 */
export const getRequestLog = (data) =>
  request.post(`${serviceUrl}/adminApi/requestLog/list`, data);

/**
 * 后台登录日志列表
POST /adminApi/login/user/log/list
接口ID：38419780
接口地址：https://www.apifox.cn/web/project/1049629/apis/api-38419780
 */
export const getAdminLoginLogs = (data) =>
  request.post(`${serviceUrl}/adminApi/login/log/list`, data);
/**
 * 用户链路
 */
export const userTraceList = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/traceList`, data);

export const certification = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/authReject`, data);
/**
 * 用户实名修改
 */
export const auth = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/authInfoEdit`, data);

/**
 * 用户列表导出
 */
export const userListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/listExport`, data, {
    responseType: "blob",
  });

/**
 * 用户信息修改
 */
export const userInfoEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/userInfoEdit`, data);

//用户信息导出
export const userInfoExport = (data) =>
  request.post(`${serviceUrl}/adminApi/userInfo/export`, data, {
    responseType: "blob",
  });

// /**
//    * 配置修改
//    */
//   export const dictionaryUpdate = (data) =>
//     request.post(`${serviceUrl}/adminApi/dictionary/update`, data)
/**
 * 用户余额累计
 */
export const getAllUserSumBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/userTotalBalance`, data);

/**
 * 黑白名单查询列表
 */
export const blackWhiteUser = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/list`, data);

/**
 * 黑白名单新增
 */
export const blackWhiteUserAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/add`, data);

/**
 * 黑白名单编辑
 */
export const blackWhiteUserEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/isDeleted`, data);

export const cancelAccountBatch = (data) =>
  request.post(`${serviceUrl}/adminApi/appUser/cancel/account/batch`, data);

//下载模板
export const userCenterDownLoadTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

/*
 * 社区查询
 */
export const getCommunityList = (data) =>
  request.post(`${serviceUrl}/adminApi/community/search`, data);

/**
 * 社区添加
 */
export const addCommunity = (data) =>
  request.post(`${serviceUrl}/adminApi/community/add`, data);

/**
 * 添加或更新社区关系
 */
export const addOrUpdateCommunityRelation = (data) =>
  request.post(`${serviceUrl}/adminApi/community/addOrUpdateRelation`, data);

/**
 * 社区删除
 */
export const deleteCommunity = (data) =>
  request.post(`${serviceUrl}/adminApi/community/delete`, data);

/**
 * 导出社区明细
 */
export const exportCommunity = (data) =>
  request.post(`${serviceUrl}/adminApi/community/communityDetailExport`, data,{
    responseType: "blob",
  });

/**
 * 查询所有社区
 */
export const queryAllCommunity = (data) =>
  request.post(`${serviceUrl}/adminApi/community/getAllCommunity`, data);