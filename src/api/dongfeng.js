import request from '@/utils/request.js'

const serviceUrl = '/dongfeng'

/**
 * 用户上链失败列表
 */
export const cochainList = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/user/list`, data)

/**
 * 重新上链
 */
export const manaulCochain = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/user/manau`, data)

/**
 * 批量上链
 */
export const historylCochain = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/user/history`, data)

/**
 * 用户部署合约失败列表
 */
export const contractList = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/good/list`, data)

/**
 * 重新部署合约接口
 */
export const manaulContract = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/goods/manaul`, data)

/**
 * 批量部署接口
 */
export const historylContract = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/goods/history`, data)

/**
 * 转移token失败列表
 */
export const transferList = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/order/list`, data)

/**
 * 重新转移token接口
 */
export const manaulTransfer = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/order/manaul`, data)

/**
 * 批量转移token接口
 */
export const historylTransfer = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/order/history`, data)

/**
 * 订单交易哈希值查询
 */
export const orderInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/chain/orderInfo`, data)
