import request from '@/utils/request.js'

const serviceUrl = '/hongyan'
const serviceUrl2 = '/tiangong'

/**
 * 分页获取消息盒子列表
 */
export const getListPageMsgBox = (data) =>
  request.post(`${serviceUrl}/adminApi/msgBox/listPageMsgBox`, data)

/**
 * 获取消息盒子列表
 */
export const getListMsgBox = (data) =>
  request.post(`${serviceUrl}/adminApi/msgBox/listMsgBox`, data)

/**
 * 新增消息盒子
 */
export const addMsgBox = (data) =>
  request.post(`${serviceUrl}/adminApi/msgBox/addMsgBox`, data)

/**
 * 编辑消息盒子
 */
export const updateMsgBox = (data) =>
  request.post(`${serviceUrl}/adminApi/msgBox/updateMsgBox`, data)

/**
 * 分页获取消息事件模板列表
 */
export const getListPageMsgEventTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/msgEventTemplate/listPageMsgEventTemplate`, data)

/**
 * 获取单个消息事件模板信息
 */
export const getMsgEventTemplateDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/msgEventTemplate/getMsgEventTemplateDetail`, data)

/**
 * 新增消息事件模板
 */
export const addMsgEventTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/msgEventTemplate/addMsgEventTemplate`, data)

/**
 * 修改消息事件模板
 */
export const updateMsgEventTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/msgEventTemplate/updateMsgEventTemplate`, data)

/**
 * 消息任务列表
 */
export const getMessageList = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/list`, data)

/**
 * 下载模板
 */
export const downLoadTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data)

/**
 * 上传消息任务用户excel
 * POST /adminApi/msgJob/importMsgJobUserExcel
 * 接口ID：36896174
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-36896174
 */
export const importMsgJobUserExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/importMsgJobUserExcel`, data)

/**
 * 创建消息任务
 */
export const addMsgJob = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/add`, data)

/**
 * 查看单个消息任务详情
 */
export const detailMsgJob = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/detail`, data)

/**
 * 修改消息任务
 */
export const updateMsgJob = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/update`, data)

/**
 * 删除消息任务
 */
export const deleteMsgJob = (data) =>
  request.post(`${serviceUrl}/adminApi/msgJob/delete`, data)

/**
 * 消息事件模板上下线
 * POST /adminApi/msgEventTemplate/msgEventTemplateOnline
 * 接口ID：34949753
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-34949753
 */
export const msgEventTemplateOnline = (data) =>
  request.post(`${serviceUrl}/adminApi/msgEventTemplate/msgEventTemplateOnline`, data)

/**
 * 公告列表
 * POST /adminApi/article/list
 * 接口ID：38558624
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-38558624
 */
export const articleList = (data) =>
  request.post(`${serviceUrl}/adminApi/article/list`, data)

/**
 * 公告发表/下架
 * POST /adminApi/article/publish
 * 接口ID：38558623
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-38558623
 */
export const noticePublish = (data) =>
  request.post(`${serviceUrl}/adminApi/article/publish`, data)

/**
 * 公告新增
 * POST /adminApi/article/add
 * 接口ID：38558621
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-38558621
 */
export const noticeAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/article/add`, data)

/**
 * 公告编辑
 * POST /adminApi/article/edit
 * 接口ID：38558622
 * 接口地址：https://www.apifox.cn/web/project/1049636/apis/api-38558622
 */
export const noticeEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/article/edit`, data)

// 客户端列表
// POST /adminApi/oauth2/clients/list
// 接口ID：22415545
// 接口地址：https://www.apifox.cn/web/project/1049641/apis/api-22415545
export const clientsList = (data) =>
  request.post(`${serviceUrl2}/adminApi/oauth2/clients/list`, data)

// 添加授权客户端
// POST /adminApi/oauth2/clients/addClient
// 接口ID：22415549
// 接口地址：https://www.apifox.cn/web/project/1049641/apis/api-22415549
export const clientsAddClient = (data) =>
  request.post(`${serviceUrl2}/adminApi/oauth2/clients/addClient`, data)

// updateClient
// POST /adminApi/oauth2/clients/updateClient
// 接口ID：22415544
// 接口地址：https://www.apifox.cn/web/project/1049641/apis/api-22415544
export const clientsUpdateClient = (data) =>
  request.post(`${serviceUrl2}/adminApi/oauth2/clients/updateClient`, data)


// 公告搜索记录统计
//   POST /admin/bi/listCountNoticeSearchRecord
//   接口ID：67183765
//   接口地址：https://www.apifox.cn/link/project/1049636/apis/api-67183765
export const listCountNoticeSearchRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/listCountNoticeSearchRecord`, data)

// 列表
//   POST /adminApi/articleTime/list
//   接口ID：70608844
//   接口地址：https://www.apifox.cn/link/project/1049636/apis/api-70608844
export const articleTimeList = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/list`, data)
//新增
export const articleTimeCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/create`, data)
//修改
export const articleTimeUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/update`, data)
//置顶
export const articleTimeTopping = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/topping`, data)
//删除
export const articleTimeDeleted = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/deleted`, data)
//详情查询
export const getArticleDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/article/getArticleDetail`, data)

//公告配置
export const articleTimeSet = (data) =>
  request.post(`${serviceUrl}/adminApi/articleTime/set`, data)

//修改阅读量
export const updateScanCount = (data) =>
  request.post(`${serviceUrl}/adminApi/article/updateScanCount`, data)
  //修改阅读量
export const articleEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/article/edit `, data)
