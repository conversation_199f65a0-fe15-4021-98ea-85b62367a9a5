import request from "@/utils/request.js";

const serviceUrl = "/coinexchange";
const serviceUrl2 = "hanxin";
const serviceUrl3 = "/bvexchange";

/**
 * 万能金新增
 */
export const getBitConfigListAdd = (data) =>
  request.post(`${serviceUrl3}/adminApi/coinTrail/add`, data);

/**
 * 万能金查询
 */
export const getBitConfigList = (data) =>
  request.post(`${serviceUrl3}/adminApi/coinTrail/search`, data);

/**
 * 配置查询
 */

export const getBitConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/config/search`, data);

/**
 * 配置添加修改
 */
export const addBitConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/config/addOrUpdate`, data);

/**
 * 成交明细
 */
export const getBitDetail = (data) =>
  request.post(`${serviceUrl3}/adminApi/exchangeCoin/report/tradeRecord`, data);

/**
 * 平仓记录
 */
export const getBitClose = (data) =>
  request.post(`${serviceUrl3}/adminApi/exchangeCoin/report/closeRecord`, data);

/**
 * 开仓记录
 */
export const getBitOpen = (data) =>
  request.post(`${serviceUrl3}/adminApi/exchangeCoin/report/openRecord`, data);

/**
 * 盘口日志
 */
export const getBitLog = (data) =>
  request.post(`${serviceUrl3}/adminApi/exchangeCoin/report/handicapLog`, data);

/**
 * 用户黑白名单新增
 */
export const addBitUser = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/add`, data);

/**
 * 用户黑白名单生效状态修改
 */
export const updateBitUser = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/isDeleted`, data);

/**
 * 用户黑白名单查询列表
 */
export const getBitUser = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/list`, data);

/**
 * 看板信息
 */
export const getBitBoard = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/getTotalReportInfo`,
    data
  );

/**
 * 收益风控看板
 */
export const getBitRisk = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/profitRecord`,
    data
  );

/**
 * 持仓记录
 */
export const getBitHold = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/historyPositionRecord`,
    data
  );

/**
 * 爆仓记录
 */
export const getBitBoom = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/explosionRecord`,
    data
  );

/**
 * 主力委托
 */
export const getBitMain = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/report/robotDelegating`, data);

/**
 * 撤销主力委托
 */
export const cancelBitMain = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/report/revokeRobotOrder`, data);

/**
 * 平仓导出
 */
export const exportBitClose = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/export/closeRecordExport`,
    data,
    { responseType: "blob" }
  );

/**
 * 爆仓记录导出
 */
export const exportBitBoom = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/export/explosionRecordExport`,
    data,
    { responseType: "blob" }
  );

/**
 * 持仓记录导出
 */
export const exportBitHold = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/export/historyPositionRecordExport`,
    data,
    { responseType: "blob" }
  );

/**
 * 开仓记录导出
 */
export const exportBitOpen = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/export/openRecordExport`,
    data,
    { responseType: "blob" }
  );

/**
 * 成交明细导出
 */
export const exportBitDetail = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/export/tradeRecordExport`,
    data,
    { responseType: "blob" }
  );

/**
 * 访问信息
 */
export const visitInfo = (data) =>
  request.post(`${serviceUrl3}/adminApi/exchangeCoin/report/visitInfo`, data);

/**
 * 每日盈利
 */
export const profitInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/report/dailyProfitInfo`, data);

/**
 * 添加或更新交易大赛
 */
export const addBitContest = (data) =>
  request.post(
    `${serviceUrl}/adminApi/exchange/addOrUpdateTradeCompetition`,
    data
  );

/**
 * 交易大赛查询
 */
export const getBitContest = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/searchTradeCompetition`, data);

/*
 * 查询排名信息
 */
export const getBitRank = (data) =>
  request.post(
    `${serviceUrl}/adminApi/exchange/searchTradeCompetitionRank`,
    data
  );

/**
 * 提现黑名单
 */
export const getBlackList = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/listV2`, data);

/***
 * 修改黑名单状态
 */
export const updateBlackListStatus = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/isDeletedBatch`, data);

/**
 * 新增提现黑名单
 */
export const addBlackList = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/addV2`, data);

/**
 * 修改提现黑名单
 */
export const updateBlackList = (data) =>
  request.post(`${serviceUrl2}/adminApi/blackWhiteUser/updateV2`, data);

/**
 * 修改手续费配置
 */
export const updateFeeConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/discount/update`, data);

/**
 * 获取手续费配置
 */
export const getFeeConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/discount/query`, data);

/**
 * 增加手续费配置
 */
export const addFeeConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/discount/add`, data);

/**
 * 删除手续费配置
 */
export const deleteFeeConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/discount/delete`, data);

/**
 * 看板重要信息
 */
export const getDashboard = (data) =>
  request.post(
    `${serviceUrl3}/adminApi/exchangeCoin/report/getTotalReportInfoImportant`,
    data
  );

/**
 * 相比前一天
 */
export const getDashboardCompare = (data) =>
  request.post(
    `${serviceUrl}/adminApi/exchange/report/compareWithYesterday`,
    data
  );

/**
 * 发放
 */
export const grant = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/sendBonus`, data);

/**
 * 币对列表
 */
export const getSymbolList = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/contract/symbolList`, data);

/**
 * 币对查询
 */
export const searchSymbol = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/contract/search`, data);

/**
 * 币对新增 修改
 */
export const addSymbol = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/contract/addOrUpdate`, data);

/**
 * 币对启用
 */
export const enableSymbol = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/contract/enable`, data);

/**
 * 币对展示
 */
export const showSymbol = (data) =>
  request.post(`${serviceUrl}/adminApi/exchange/contract/enableShow`, data);
