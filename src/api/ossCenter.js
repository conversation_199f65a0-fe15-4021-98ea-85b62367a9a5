import request from '@/utils/request.js'

const serviceUrl = '/osscenter'

/**
 *  上传图片到阿里云
 */
export const uploadImgToOss = (data) =>
  request.post(`${serviceUrl}/adminApi/missWebSign/uploadImage`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
      'AdminAuthorization': localStorage.getItem('usertoken')
    }
  })
/**
 * 上传excle至oss
 * @api /adminApi/missWebSign/uploadExcel
 */
export const uploadExcelToOss = data =>
  request.post(`${serviceUrl}/adminApi/missWebSign/uploadExcel`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })

// 刷新CDN缓存
// POST /adminApi/refreshCdnCaches
// 接口ID：40897790
// 接口地址：https://www.apifox.cn/web/project/1049625/apis/api-40897790
export const refreshCdnCaches = data =>
  request.post(`${serviceUrl}/adminApi/refreshCdnCaches`, data)

