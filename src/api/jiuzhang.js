import request from '@/utils/request.js'

const serviceUrl = '/jiuzhang'

/**
 * 用户余额列表
 */
export const userTraceList = (data) =>
  request.post(`${serviceUrl}/adminApi/user/trace/list`, data)

/**
 * 用户余额修改
 */
export const balanceChange = (data) =>
  request.post(`${serviceUrl}/adminApi/user/balance/change`, data)

/**
 * 用户燃料修改
 */
export const fuelChange = (data) =>
  request.post(`${serviceUrl}/adminApi/fuel/change`, data)

/**
 * 提现列表
 */
export const getWithdrawList = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/list`, data)

/**
 * 提现批量导出
 */
export const withdrawExport = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/list/export`, data, { responseType: 'blob' })

/**
 * 用户流水
 */
export const financeList = (data) =>
  request.post(`${serviceUrl}/adminApi/finance/list`, data)

/**
 * 用户流水批量导出
 */
export const financeExport = (data) =>
  request.post(`${serviceUrl}/adminApi/finance/list/export`, data, { responseType: 'blob' })

/**
 * 提现审核
 */
export const withdrawCheck = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/check`, data)

/**
 * 提现备注
 */
export const withdrawRemark = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/remark`, data)

/**
 * 提现打款-银行卡
 */
export const bankCardPay = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/bankCard/pay`, data)

/**
 * 提现打款-线下打款
 */
export const handPay = (data) =>
  request.post(`${serviceUrl}/adminApi/withdraw/hand/pay`, data)

/**
 * 用户燃料石列表页
 * POST /adminApi/stone/user/list
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-********
 */
export const stoneUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/list`, data)

/**
 * 批量赠送燃料石
 * POST /adminApi/stone/batch/give
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-********
 */
export const stoneBatchGive = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/batch/give`, data, { responseType: 'blob' })

/**
 * 单个人赠送燃料石头
 * POST /adminApi/stone/user/give
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-********
 */
export const stoneUserGive = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/give`, data)

/**
 * 下载模板
 * POST /adminApi/excel/downLoadTemplate
 * 接口ID：36143842
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36143842
 */
export const jiuZhangDownLoadTemplateExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data)

/**
 * 用户燃料石明细
 * POST /adminApi/stone/user/detail
 * 接口ID：36110350
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36110350
 */
export const stoneUserDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/detail`, data)

export const balanceBatchChange0 = (data) =>
  request.post(`${serviceUrl}/adminApi/user/balance/batchChange0`, data)


export const balanceBatchChange4 = (data) =>
  request.post(`${serviceUrl}/adminApi/user/balance/batchChange4`, data)

/**
 *  余额
 */
export const wormList = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/worm/list`, data)

/**
 *  虫子余额修改
 */
export const wormUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/worm/update`, data)

/**
 *  虫子余额批量修改
 */
export const wormBatchImportUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/worm/batchImportUpdate`, data)


// 用户流水快照导出
export const snapshotExport = (data) =>
 request.post(`${serviceUrl}/adminApi/finance/snapshot/export`, data)

// 用户流水消耗金额总计
export const financeListTotal = (data) =>
 request.post(`${serviceUrl}/adminApi/finance/listTotal`, data)
