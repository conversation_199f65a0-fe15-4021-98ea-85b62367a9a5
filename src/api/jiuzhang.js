import request from '@/utils/request.js'

const serviceUrl = '/jiuzhang'


/**
 * 用户燃料修改
 */
export const fuelChange = (data) =>
  request.post(`${serviceUrl}/adminApi/fuel/change`, data)





/**
 * 用户燃料石列表页
 * POST /adminApi/stone/user/list
 * 接口ID：36110352
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36110352
 */
export const stoneUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/list`, data)

/**
 * 批量赠送燃料石
 * POST /adminApi/stone/batch/give
 * 接口ID：36110349
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36110349
 */
export const stoneBatchGive = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/batch/give`, data, { responseType: 'blob' })

/**
 * 单个人赠送燃料石头
 * POST /adminApi/stone/user/give
 * 接口ID：36110351
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36110351
 */
export const stoneUserGive = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/give`, data)

/**
 * 下载模板
 * POST /adminApi/excel/downLoadTemplate
 * 接口ID：36143842
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36143842
 */
export const jiuZhangDownLoadTemplateExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data)

/**
 * 用户燃料石明细
 * POST /adminApi/stone/user/detail
 * 接口ID：36110350
 * 接口地址：https://www.apifox.cn/web/project/1049652/apis/api-36110350
 */
export const stoneUserDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/stone/user/detail`, data)

export const balanceBatchChange0 = (data) =>
  request.post(`${serviceUrl}/adminApi/user/balance/batchChange0`, data)


export const balanceBatchChange4 = (data) =>
  request.post(`${serviceUrl}/adminApi/user/balance/batchChange4`, data)

// /**
//  *  余额
//  */
// export const wormList = (data) =>
//   request.post(`${serviceUrl}/adminApi/wallet/worm/list`, data)

// /**
//  *  虫子余额修改
//  */
// export const wormUpdate = (data) =>
//   request.post(`${serviceUrl}/adminApi/wallet/worm/update`, data)

// /**
//  *  虫子余额批量修改
//  */
// export const wormBatchImportUpdate = (data) =>
//   request.post(`${serviceUrl}/adminApi/wallet/worm/batchImportUpdate`, data)


// 用户流水快照导出
export const snapshotExport = (data) =>
 request.post(`${serviceUrl}/adminApi/finance/snapshot/export`, data)

// 用户流水消耗金额总计
export const financeListTotal = (data) =>
 request.post(`${serviceUrl}/adminApi/finance/listTotal`, data)
