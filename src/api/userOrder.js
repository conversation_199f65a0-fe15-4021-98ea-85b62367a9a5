import request from '@/utils/request.js'

const serviceUrl = '/userorder'

/**
 *  平安账户列表
 */
export const safeAccountList = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/account/list`, data)

/**
 *  平安账户余额查询
 */
export const pinanBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/account/pinan/balance`, data)

/**
 *  平安账户余额更正
 */
export const updateBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/account/update/balance`, data)

/**
 * 修改用户平安余额接口
 * POST /adminApi/pa/bank/change/balance
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049654/apis/api-********
 * @param data
 * @return {Promise<unknown>}
 */
export const changeBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/bank/change/balance`, data)

/**
 *  平安账户单个平账
 */
export const autoCharge = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/bank/auto/charge`, data)

/**
 *  平安账户批量平账
 */
export const batchAutoCharge = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/bank/batch/auto/charge`, data)

/**
 *  平安账户记账信息列表
 */
export const transferInfoList = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/info/list`, data)

/**
 *  平安账户重新转账
 */
export const transferRetry = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/info/retry`, data)

/**
 *  平安账户手动处理提现记录列表
 */
export const withdrawalList = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/manaul/withdrawal/list`, data)

/**
 *  平安账户记账需求池列表
 */
export const poolList = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/pool/list`, data)

/**
 *  平安账户提现记录列表
 */
export const transferWithdrawList = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/withdrawal/list`, data)

/**
 *  平安账户重新提现
 */
export const withdrawalRetry = (data) =>
  request.post(`${serviceUrl}/adminApi/pa/transfer/withdrawal/retry`, data)
