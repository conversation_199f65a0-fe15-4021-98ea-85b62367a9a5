import request from "@/utils/request.js";

const serviceUrl = "/np-collection-yanjie";

//下载模板
export const userCenterDownLoadTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

/**
 *  获取作品详情
 */
export const goodsDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/audit/goodsDetail`, data);

/**
 *  商品同步
 */
export const goodsEs = (data) =>
  request.post(`${serviceUrl}/adminApi/goods/init/goodsEs`, data);

/**
 *  系列同步
 */
export const seriesEs = (data) =>
  request.post(`${serviceUrl}/adminApi/goods/init/seriesEs`, data);

/**
 *  系列同步
 */
export const operate = (data) =>
  request.post(`${serviceUrl}/adminApi/series/init`, data);

/**
 *  系列同步
 */
export const cover = (data) =>
  request.post(`${serviceUrl}/adminApi/series/update/cover`, data);

/**
 *  树藏平台作品elasticsearch数据全量同步
 */
export const digitalCollectionDataSync = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/init/goodsEs`, data);

/**
 *  月球主题活动数据同步到商品es中
 */
export const moonDataSync = (data) =>
  request.post(`${serviceUrl}/adminApi/goods/init/topicToGoodsEsData`, data);

/**
 *  系列详情
 */
export const getSeriesDetails = (data) =>
  request.post(`${serviceUrl}/adminApi/audit/getSeriesDetails`, data);

/**
 *  系列详情
 */
export const seriesDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/seriesDetail`, data);

/**
 *  系列批量上下架
 */
export const batchUpdateSeriesVisibility = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdateSeriesVisibility`,
    data
  );

/**
 *  系列批量推荐级别
 */
export const batchUpdateSeriesRecommend = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdateSeriesRecommend`,
    data
  );

/**
 * 批量设置系列下创作品 - 上下架状态
 * POST /adminApi/collection/batchUpdateItemVisibility
 * 接口ID：35665142
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-35665142
 */
export const batchUpdateItemVisibility = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdateItemVisibility`,
    data
  );

/**
 *  商品维度
 */
export const traceItem = (data) =>
  request.post(`${serviceUrl}/adminApi/trace/item`, data);

/**
 *  作品白名单列表
 */
export const allowList = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/allowList`, data);

/**
 *  关注列表
 */
export const favoriteList = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/favoriteList`, data);

/**
 *  审核状态
 */
export const batchUpdateAuditState = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchUpdateAuditState`, data);

/**
 *  发布状态
 */
export const batchUpdatePublishState = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdatePublishState`,
    data
  );

/**
 *  上下架
 */
export const batchUpdateRackStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchOnOrOffSale`, data);

/**
 *  推荐级别
 */
export const batchUpdateRecommendLevel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdateRecommendLevel`,
    data
  );

/**
 *  提示风险
 */
export const batchUpdateRiskState = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchUpdateRiskState`, data);

/**
 *  商品信息
 */
export const getGoodsDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/detail`, data);

/**
 *  树藏商品发布列表
 */
export const digitalCollectionGoodsPublish = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/list`, data);

/**
 *  获取作品详情
 */
export const digitalCollectionGoodsDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/detail`, data);

/**
 *  树藏商品删除
 */
export const digitalCollectionGoodsDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/delete`, data);

/**
 *  树藏商品上下架
 */
export const digitalCollectionGoodsUpAndDown = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/updateSaleStatus`, data);

/**
 *  树藏模版发布新增
 */
export const digitalCollectionTemplatePublishAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/add`, data);

/**
 *  树藏模版发布修改
 */
export const digitalCollectionTemplatePublishEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/update`, data);

/**
 *  树藏模版发布详情
 */
export const digitalCollectionTemplatePublishDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/detail`, data);

/**
 *  树藏模版发布列表
 */
export const digitalCollectionTemplatePublishList = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/list`, data);

/**
 *  树藏模版发布删除
 */
export const digitalCollectionTemplatePublishDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/delete`, data);

/**
 *  树藏模版发布显示隐藏
 */
export const digitalCollectionTemplatePublishStatusToggle = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/hide`, data);

/**
 *  树藏模版发布成单记录
 */
export const digitalCollectionTemplatePublishOrderRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/order`, data);

/**
 *  树藏模版发布使用记录
 */
export const digitalCollectionTemplatePublishUsedRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/template/useNum`, data);

/**
 * 获取配置
 * POST /adminApi/zsb/goods/getConfig
 * 接口ID：30568490
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-30568490
 * @param { String|Number } data.type  配置类型 1商品详情页提示信息
 */
export const getConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/getConfig`, data);

/**
 * 配置设置
 * POST /adminApi/zsb/goods/setConfig
 * 接口ID：30568491
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-30568491
 * @param { String|Number } data.type  配置类型 1商品详情页提示信息
 * @param { String } data.configStr  配置信息 Json串{key1(show 是否显示 0隐藏 1显示),key2(info 显示信息)}
 */
export const setConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/goods/setConfig`, data);

/**
 * 	配置设置
 * 	飞跃计划签约用户列表
 * 	POST /adminApi/leapPlan/user/list
 *	接口ID：35648418
 *	接口地址：https://www.apifox.cn/web/project/1049631/apis/api-35648418
 */
export const leapPlanUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/leapPlan/user/list`, data);

/**
 * 后台接口飞跃计划用户修复
 */
export const initLeapPlanWebsite = (data) =>
  request.post(`${serviceUrl}/adminApi/leapPlan/initLeapPlanWebsite`, data);

/**
 * 市场页推荐数据-同步到商品es中
 */
export const marketTabToGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/goods/init/marketTabToGoods`, data);

/**
 * 设置系列内作品上架最高价格
 */
export const updateOnSaleMaxPrice = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/updateOnSaleMaxPrice`, data);

/**
 * 设置系列寄售状态
 */
export const batchUpdateItemNotSaleSign = (data) =>
  request.post(`${serviceUrl}/adminApi/series/updateNotSaleSign`, data);

/**
 * 定时任务列表
 */
export const dutyList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/list`, data);

/**
 * 新增定时任务
 */
export const dutyAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/add`, data);

/**
 * 每日限购任务-只返回最新的一条未结束的
 */

export const dutyLimitList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/limitBuyInfo`, data);

/**
 * 导出失败记录Excel
 */
export const failImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/export`, data, {
    responseType: "blob",
  });

/**
 * 白名单上传
 */
export const whiteUserImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/whiteUserImport`, data);

/**
 * 任务删除
 */
export const dutyDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/delete`, data);

/**
 * 任务查询
 */
export const dutyInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/info`, data);

/**
 * 任务修改
 */
export const dutyEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/update`, data);

/**
 * 白名单查询
 */
export const whiteUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/whiteUserList`, data);

/**
 * 作品上传
 */
export const goodsImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/goodsImport`, data);

/**
 * 作品上传
 */
export const batchUpdateOnSaleStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/batchUpdateOnSaleStatus`,
    data
  );

/**
 * 作品列表导出
 */
export const goodsListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/exportExcel`, data, {
    responseType: "blob",
  });

/**
 * 小号查询创作品和藏品
 */
export const userGoodsCount = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/userGoodsCount`, data);

// 仅供收藏藏品分析列表
//   POST /adminApi/bi/goodsCollectionOnly/listGoodsCollectionOnly
//   接口ID：66953354
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-66953354

export const listGoodsCollectionOnly = (data) =>
  request.post(
    `${serviceUrl}/adminApi/bi/goodsCollectionOnly/listGoodsCollectionOnly`,
    data
  );

// 仅供收藏藏品隐藏
//   POST /adminApi/bi/goodsCollectionOnly/goodsCollectionOnlyHide
//   接口ID：66953353
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-66953353
export const goodsCollectionOnlyHide = (data) =>
  request.post(
    `${serviceUrl}/adminApi/bi/goodsCollectionOnly/goodsCollectionOnlyHide`,
    data
  );

export const updateCollectionTag = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/updateCollectionTag`, data);

export const domainNameTags = (data) =>
  request.post(`${serviceUrl}/adminApi/goods/init/domainNameTags`, data);

// 统计查看用户记录
//   POST /adminApi/showUserRecord/listShowUserRecord
//   接口ID：67282458
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-67282458
export const listShowUserRecord = (data) =>
  request.post(
    `${serviceUrl}/adminApi/showUserRecord/listShowUserRecord`,
    data
  );

export const searchPgc = (data) =>
  request.post(`${serviceUrl}/adminApi/series/search`, data);

export const setSellList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/setSell/list`, data);

export const setSellAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/setSell/add`, data);

export const setSellStop = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/setSell/stop`, data);

export const maxSellAmountValidate = (data) =>
  request.post(
    `${serviceUrl}/adminApi/duty/setSell/maxSellAmountValidate`,
    data
  );

export const goodsPriceUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/goodsPriceUpdate`, data);
/**
 *  系列查看数据
 */
export const seriesGoodsCount = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/seriesGoodsCount`, data);

/**
 *  系列查看导出
 */
export const seriesCollectionCountExport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/collection/seriesCollectionCountExport`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 设置卖出复制任务
 */
export const setSellCopy = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/setSell/copy`, data);

export const setSellRecordExport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/setSell/recordExport`, data, {
    responseType: "blob",
  });

/**
 * 管理台铸造作品 追加补充
 */
export const batchCreateAppend = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchCreateAppend`, data);
// 销毁任务中止
export const dutyStop = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/stop`, data);
/**
 * 管理台铸造作品
 */
export const batchCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchCreate`, data);

// 公告列表
export const articleTimeList = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/list`, data);
//其他配置-平台公告-公告列表
export const articleList = (data) =>
  request.post(`${serviceUrl}/adminApi/article/list`, data);
// 公告新增
export const articleTimeCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/add`, data);
// 公告修改
export const articleTimeUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/update`, data);
// 公告删除
export const articleTimeDeleted = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/delete`, data);
// // 公告详情查询
export const getArticleDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/article/detail`, data);
//其他配置-平台公告-公告详情
// export const articleDetail = (data) =>
//   request.post(`${serviceUrl}/adminApi/article/detail`, data)

/**
 * 系列名称查询ctid
 */
export const searchByCsNameStr = (data) =>
  request.post(`${serviceUrl}/adminApi/article/searchBySeriesNameStr`, data);

/**
 *  系列列表
 */
export const seriesList = (data) =>
  request.post(`${serviceUrl}//adminApi/series/list`, data);

/**
 *  商品列表
 */
export const goodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/list`, data);

/**
 * 楼层区配置列表
 */
export const floorList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/list`, data);

/**
 * 楼层区 详情
 */
export const tabDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/detail`, data);

/**
 * 楼层区 修改
 */
export const updateMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/update`, data);

/**
 * 楼层区 隐藏显示
 */
export const showMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/updateStatus`, data);

/**
 * 楼层区 删除
 */
export const deleteMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/delete`, data);

/**
 * 楼层区 新增
 */
export const addMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/add`, data);

// 二级楼层列表
export const leftJoinIpList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/list`, data);

/**
 * 二级楼层详情
 */
export const leftJoinDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/detail`, data);

/**
 * 二级楼层新增
 */
export const insertLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/add`, data);

/**
 * 二级楼层编辑
 */
export const updateLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/update`, data);

/**
 * 二级楼层显隐
 */
export const hideOrShowLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/updateStatus`, data);

export const updateEndTime = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/updateEndTime`, data);
/**
 * 二级楼层品牌
 */
export const delLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/series/delete`, data);






// 新版本 以上备用后续删除

// 一级空投创建时, 选择系列之后, 查询系列下一级作品剩余数
export const restGoodsNum = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/restGoodsNum`, data);

// 一级空投创建
export const creationCsCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/add`, data);
/**
 * 一级空投列表
 */
export const creationCslist = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/list`, data);
// 订单模板下载
export const downLoadTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

export const userValidate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/userValidate`, data);

export const recordExport = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/export`, data, {
    responseType: "blob",
  });

/**
 * 活动列表
 */
export const activityList = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/list`, data);

/**
 *  活动详情
 */
export const activityDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/detail`, data);

/**
 * 活动新增
 */
export const activityAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/add`, data);

/**
 * 活动编辑
 */
export const activityEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/update`, data);

/**
 *  活动删除
 */
export const activityDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/delete`, data);

/**
 *  活动上下线切换
 */
export const activityStatusToggle = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/updateStatus`, data);

/**
 * 导入盲盒内作品数据
 */
export const rebuildImportTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/cleanCache`, data);

// 定时取消定时
export const updateActivityNewStatusAndTiming = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/updateTimeOther`, data);

// 取消定时
export const cancelTiming = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/updateTime`, data);

/**
 *  清空缓存
 */
export const clearRedis = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/cleanCache`, data);

/**
 * 下载模板
 */
export const downLoadTemplateExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

/**
 * 合作方详情
 */
export const partnerDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/partner/detail`, data);

/**
 * 合作方修改
 */
export const partnerUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/partner/update`, data);

/**
 * 订单列表
 */
export const orderlist = (data) =>
  request.post(`${serviceUrl}/adminApi/order/normal/list`, data);

/**
 * 订单列表
 */
export const financeList = (data) =>
  request.post(`${serviceUrl}/adminApi/order/finance/list`, data);

/**
 * 求购列表
 */
export const targetList = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/list`, data);

/**
 * 求购列表 合计
 */
export const listTotal = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/listTotal`, data);

/**
 * 求购列表 撤销
 */
export const batchCancel = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/batchCancel`, data);

// 二级空投
/**
 * 空投项目列表-藏品
 */
export const airDropProjectListCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/list`, data);

/**
 * 空投项目新增-藏品
 */
export const airDropProjectAddCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/add`, data);

// 藏品终止
export const airDropCollectionStop = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/stop`, data);

/**
 * 空投结果导出-藏品
 */
export const airDropResultExportCollection = (data) =>
  request.post(
    `${serviceUrl}/adminApi/airDrop/collection/record/export`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 空投结果导出-藏品
 */
export const financeExport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/finance/export`, data, {
    responseType: "blob",
  });

/**
 * 空投结果导出-藏品
 */
export const normalExport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/normal/export`, data, {
    responseType: "blob",
  });

// 开启竞价
export const openBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/open_bidding`, data);

// 竞价列表
export const biddingList = (data) =>
  request.post(`${serviceUrl}/adminApi/series/query_bidding_list`, data);
// 竞价列表导出
export const biddingListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/series/biddingListExport`, data, {
    responseType: "blob",
  });
// 竞价列表撤销
export const revokeBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/revoke_bidding`, data);
// 竞价列表批量撤销
export const batchRevokeBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/batch_revoke_bidding`, data);
// 竞价列表添加
export const adminAddBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/admin_add_bidding`, data);
// 竞价列表上架
export const ListingBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/Listing_bidding`, data);

// 系列列表是否开启竞价
export const quickTargetSingle = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/quickTargetSingle`, data);

//批量转售
export const resaleImport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/resaleImport`, data);

// //字典控制器
// export const dictionarySearch = (data) =>
//   request.post(`${serviceUrl}/adminApi/dictionary/search`, data);
// //添加
// export const dictionaryAdd = (data) =>
//   request.post(`${serviceUrl}/adminApi/dictionary/add`, data);
// //更改
// export const dictionaryUpdate = (data) =>
//   request.post(`${serviceUrl}/adminApi/dictionary/update`, data);

//更改
export const enableOrderTarget = (data) =>
  request.post(`${serviceUrl}/adminApi/series/enableOrderTarget`, data);

//更改
export const enableExitOrderTarget = (data) =>
  request.post(`${serviceUrl}/adminApi/series/enableExitOrderTarget`, data);

//公告配置
export const articleTimeSet = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/set`, data);

// 楼层区列表
export const getBelongMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/getBelongMarketTab`, data);

// 配置查询
export const dictionaryInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/dictionary/info`, data);

//批量转售
export const batchResaleAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/batchResale/add`, data);

/**
 * 休市任务-只返回最新的一条未结束的
 */
export const getLatestUnfinishedTask = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/marketClosureInfo`, data);

/**
 * 设置系列是否开启委托
 */
export const setSellStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/series/updateEntrustViewStatus`, data);

/**
 * 批量添加求购
 */
export const batchAddBuy = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/batchCreate`, data);

/**
 * 批量-加速求购
 */
export const batchSpeedUpBuy = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/quickTarget`, data);

/**
 * 单个-加速求购
 */
export const speedUpBuy = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/quickTargetSingle`, data);

/**
 * 自动调整系列-检索
 */
export const autoAdjustSeries = (data) =>
  request.post(`${serviceUrl}/adminApi/automaticAdjustmentArea/list`, data);

/**
 * 自动调整系列-编辑
 */
export const autoAdjustSeriesEdit = (data) =>
  request.post(
    `${serviceUrl}/adminApi/automaticAdjustmentArea/autoAreaUpdate`,
    data
  );
/**
 * 自动调整系列-查询单个
 */
export const autoAdjustSeriesQuery = (data) =>
  request.post(
    `${serviceUrl}/adminApi/automaticAdjustmentArea/autoAreaInfo`,
    data
  );
/**
 *  虫子商城列表
 */
export const exchangeItemList = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemList`, data);

/**
 *  商品添加
 */
export const exchangeItemAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemAdd`, data);

/**
 *  商品添加
 */
export const exchangeItemUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemUpdate`, data);

/**
 *  兑换记录
 */
export const exchangeList = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/list`, data);

/**
 *  兑换记录备注
 */
export const exchangeRemark = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/remark`, data);

/**
 * 兑换记录导出
 */
export const exchangeExport = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/export`, data, {
    responseType: "blob",
  });
/**
 *  修改合成成本
 */
export const updateMergePrice = (data) =>
  request.post(`${serviceUrl}/adminApi/series/updateMergePrice`, data);

export const bzlInviteList = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/invite/list`, data);

export const inviteListCount = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/invite/list/count`, data);

export const bzlOrderList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/order/list`, data);

export const bzlOrderListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/order/list/export`, data, {
    responseType: "blob",
  });
// 佣金配置查询
export const bzlStoneConfigList = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/stone/config/list`, data);
// 佣金配置修改
export const bzlStoneConfigUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/stone/config/update`, data);
//用户身份修改
export const bzlUserLevelUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/user/levelUpdate`, data);
/**
 * 联盟申请表
 */
export const bzlApplyList = (data) =>
  request.post(`${serviceUrl}/adminApi/group/apply/list`, data);
/**
 * 联盟申请表状态修改
 */
export const bzlApplyUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/apply/updateStatus`, data);

/**
 * 创建联盟
 */
export const bzlCreateGroup = (data) =>
  request.post(`${serviceUrl}/adminApi/group/add`, data);

/**
 * 联盟身份修改
 */
export const bzlGroupUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/update`, data);

/**
 * 联盟列表
 */
export const bzlGroupList = (data) =>
  request.post(`${serviceUrl}/adminApi/group/list`, data);

/**
 * 联盟列表导出  暂定
 */
export const bzlGroupExport = (data) =>
  request.post(`${serviceUrl}/adminApi/group/member/list/export`, data, {
    responseType: "blob",
  });

/**
 * 联盟返佣配置查询
 */
export const bzlGroupConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/fee/config/list`, data);

/**
 * 联盟返佣配置修改
 */
export const bzlGroupConfigUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/fee/config/update`, data);

/**
 * 联盟等级修改
 */
export const userLevelUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/group/invite/user/levelUpdate`, data);

/**
 * tidHashList
 */
export const tidHashList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/tidHashList`, data);

/**
 * 任务导出
 */
export const taskExport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/destroyedExport`, data, {
    responseType: "blob",
  });

// 转售查询
export const searchResale = (data) =>
  request.post(`${serviceUrl}/adminApi/batchResale/searchResale`, data);

// 转售添加
export const addResale = (data) =>
  request.post(`${serviceUrl}/adminApi/batchResale/addResale`, data);

// 转售批量
export const batchRevokeResale = (data) =>
  request.post(`${serviceUrl}/adminApi/batchResale/revokeResale`, data);

export const updateIsJwc = (data) =>
  request.post(`${serviceUrl}/adminApi/series/updateIsJwc`, data);

/**
 * 导入盲盒内作品数据 导入tid 列表导入
 */
export const mergeInItemTidImport = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/mergeInItemTidImport`, data);

/**
 * 导出势力值排名
 */
export const exportRank = (data) =>
  request.post(`${serviceUrl}/adminApi/activity/export/powerNum`, data, {
    responseType: "blob",
  });
export const createrList = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/createrList`, data);

/**
修改虫子余额-批量导入
  * 
  */
export const wormBatchImportUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/wallet/worm/batchImportUpdate`, data);

/**
 * 虫子余额列表
 */
export const wormList = (data) =>
request.post(`${serviceUrl}/adminApi/wallet/worm/list`, data);

/**
 * 修改虫子余额-单个
 */
  export const wormUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/worm/update`, data);

//下载模板
export const userCenterDownLoadTemplateNp = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

//商品多选销毁
export const collectionBatchDestroy = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/batchDestroy`, data);


//空投删除
export const airDropDiscard = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/discard`, data);

//配置开放转售
export const upReSellJob = (data) =>
  request.post(`${serviceUrl}/adminApi/article/time/upReSellJob`, data);

