import request from '@/utils/request.js'

const serviceUrl = '/mallcenter'

/**
 *  获取作品详情
 */
export const goodsDetail = (data) =>
	request.post(`${serviceUrl}/adminApi/audit/goodsDetail`, data)

/**
 *  商品同步
 */
export const goodsEs = (data) =>
	request.post(`${serviceUrl}/adminApi/goods/init/goodsEs`, data)

/**
 *  系列同步
 */
export const seriesEs = (data) =>
	request.post(`${serviceUrl}/adminApi/goods/init/seriesEs`, data)

/**
 *  系列同步
 */
export const operate = (data) =>
	request.post(`${serviceUrl}/adminApi/series/init`, data)

/**
 *  系列同步
 */
export const cover = (data) =>
	request.post(`${serviceUrl}/adminApi/series/update/cover`, data)

/**
 *  树藏平台作品elasticsearch数据全量同步
 */
export const digitalCollectionDataSync = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/init/goodsEs`, data)

/**
 *  月球主题活动数据同步到商品es中
 */
export const moonDataSync = (data) =>
	request.post(`${serviceUrl}/adminApi/goods/init/topicToGoodsEsData`, data)

/**
 *  系列详情
 */
export const getSeriesDetails = (data) =>
	request.post(`${serviceUrl}/adminApi/audit/getSeriesDetails`, data)


/**
 *  系列详情
 */
export const seriesDetail = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/seriesDetail`, data)

/**
 *  系列批量上下架
 */
export const batchUpdateSeriesVisibility = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateSeriesVisibility`, data)

/**
 *  系列批量推荐级别
 */
export const batchUpdateSeriesRecommend = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateSeriesRecommend`, data)

/**
 * 批量设置系列下创作品 - 上下架状态
 * POST /adminApi/collection/batchUpdateItemVisibility
 * 接口ID：35665142
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-35665142
 */
export const batchUpdateItemVisibility = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateItemVisibility`, data)

/**
 *  商品维度
 */
export const traceItem = (data) =>
	request.post(`${serviceUrl}/adminApi/trace/item`, data)

/**
 *  作品白名单列表
 */
export const allowList = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/allowList`, data)

/**
 *  关注列表
 */
export const favoriteList = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/favoriteList`, data)

/**
 *  审核状态
 */
export const batchUpdateAuditState = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateAuditState`, data)

/**
 *  发布状态
 */
export const batchUpdatePublishState = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdatePublishState`, data)


/**
 *  推荐级别
 */
export const batchUpdateRecommendLevel = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateRecommendLevel`, data)

/**
 *  提示风险
 */
export const batchUpdateRiskState = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateRiskState`, data)

/**
 *  商品信息
 */
export const getGoodsDetail = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/detail`, data)

/**
 *  树藏商品发布列表
 */
export const digitalCollectionGoodsPublish = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/list`, data)

/**
 *  获取作品详情
 */
export const digitalCollectionGoodsDetail = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/detail`, data)

/**
 *  树藏商品删除
 */
export const digitalCollectionGoodsDelete = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/delete`, data)

/**
 *  树藏商品上下架
 */
export const digitalCollectionGoodsUpAndDown = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/updateSaleStatus`, data)

/**
 *  树藏模版发布新增
 */
export const digitalCollectionTemplatePublishAdd = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/add`, data)

/**
 *  树藏模版发布修改
 */
export const digitalCollectionTemplatePublishEdit = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/update`, data)

/**
 *  树藏模版发布详情
 */
export const digitalCollectionTemplatePublishDetail = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/detail`, data)

/**
 *  树藏模版发布列表
 */
export const digitalCollectionTemplatePublishList = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/list`, data)

/**
 *  树藏模版发布删除
 */
export const digitalCollectionTemplatePublishDelete = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/delete`, data)

/**
 *  树藏模版发布显示隐藏
 */
export const digitalCollectionTemplatePublishStatusToggle = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/hide`, data)

/**
 *  树藏模版发布成单记录
 */
export const digitalCollectionTemplatePublishOrderRecord = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/order`, data)

/**
 *  树藏模版发布使用记录
 */
export const digitalCollectionTemplatePublishUsedRecord = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/template/useNum`, data)

/**
 * 获取配置
 * POST /adminApi/zsb/goods/getConfig
 * 接口ID：30568490
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-30568490
 * @param { String|Number } data.type  配置类型 1商品详情页提示信息
 */
export const getConfig = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/getConfig`, data)

/**
 * 配置设置
 * POST /adminApi/zsb/goods/setConfig
 * 接口ID：30568491
 * 接口地址：https://www.apifox.cn/web/project/1049631/apis/api-30568491
 * @param { String|Number } data.type  配置类型 1商品详情页提示信息
 * @param { String } data.configStr  配置信息 Json串{key1(show 是否显示 0隐藏 1显示),key2(info 显示信息)}
 */
export const setConfig = (data) =>
	request.post(`${serviceUrl}/adminApi/zsb/goods/setConfig`, data)

/**
 * 	配置设置
 * 	飞跃计划签约用户列表
 * 	POST /adminApi/leapPlan/user/list
 *	接口ID：35648418
 *	接口地址：https://www.apifox.cn/web/project/1049631/apis/api-35648418
 */
export const leapPlanUserList = (data) =>
	request.post(`${serviceUrl}/adminApi/leapPlan/user/list`, data)

/**
 * 后台接口飞跃计划用户修复
 */
export const initLeapPlanWebsite = data =>
	request.post(`${serviceUrl}/adminApi/leapPlan/initLeapPlanWebsite`, data)

/**
 * 市场页推荐数据-同步到商品es中
 */
export const marketTabToGoods = data =>
	request.post(`${serviceUrl}/adminApi/goods/init/marketTabToGoods`, data)

/**
 * 设置系列内作品上架最高价格
 */
export const updateOnSaleMaxPrice = data =>
	request.post(`${serviceUrl}/adminApi/collection/updateOnSaleMaxPrice`, data)

/**
 * 白名单上传
 */
export const whiteUserImport = data =>
	request.post(`${serviceUrl}/adminApi/duty/whiteUserImport`, data)

/**
 * 白名单查询
 */
export const whiteUserList = data =>
	request.post(`${serviceUrl}/adminApi/duty/whiteUserList`, data)

/**
 * 作品上传
 */
export const goodsImport = data =>
	request.post(`${serviceUrl}/adminApi/duty/goodsImport`, data)

/**
 * 作品上传
 */
export const batchUpdateOnSaleStatus = data =>
	request.post(`${serviceUrl}/adminApi/collection/batchUpdateOnSaleStatus`, data)

// /**
//  * 系列名称查询ctid
//  */
// export const searchByCsNameStr = (data) =>
// 	  request.post(`${serviceUrl}/adminApi/collection/searchByCsNameStr`, data)



/**
 * 小号查询创作品和藏品
 */
export const userGoodsCount = (data) =>
	  request.post(`${serviceUrl}/adminApi/collection/userGoodsCount`, data)

// 仅供收藏藏品分析列表
//   POST /adminApi/bi/goodsCollectionOnly/listGoodsCollectionOnly
//   接口ID：66953354
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-66953354

export const listGoodsCollectionOnly = (data) =>
	  request.post(`${serviceUrl}/adminApi/bi/goodsCollectionOnly/listGoodsCollectionOnly`, data)

// 仅供收藏藏品隐藏
//   POST /adminApi/bi/goodsCollectionOnly/goodsCollectionOnlyHide
//   接口ID：66953353
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-66953353
export const goodsCollectionOnlyHide = (data) =>
	  request.post(`${serviceUrl}/adminApi/bi/goodsCollectionOnly/goodsCollectionOnlyHide`, data)


export const updateCollectionTag = (data) =>
	  request.post(`${serviceUrl}/adminApi/collection/updateCollectionTag`, data)

export const domainNameTags = (data) =>
	  request.post(`${serviceUrl}/adminApi/goods/init/domainNameTags`, data)

// 统计查看用户记录
//   POST /adminApi/showUserRecord/listShowUserRecord
//   接口ID：67282458
//   接口地址：https://www.apifox.cn/link/project/1049631/apis/api-67282458
export const listShowUserRecord = (data) =>
	  request.post(`${serviceUrl}/adminApi/showUserRecord/listShowUserRecord`, data)

// export const searchPgc = (data) =>
// 	  request.post(`${serviceUrl}/adminApi/collection/searchPgc`, data)

export const setSellList = (data) =>
	  request.post(`${serviceUrl}/adminApi/duty/setSell/list`, data)

export const setSellAdd = (data) =>
	  request.post(`${serviceUrl}/adminApi/duty/setSell/add`, data)

export const setSellStop = (data) =>
	  request.post(`${serviceUrl}/adminApi/duty/setSell/stop`, data)

export const maxSellAmountValidate = (data) =>
	  request.post(`${serviceUrl}/adminApi/duty/setSell/maxSellAmountValidate`, data)

export const goodsPriceUpdate = (data) =>
	  request.post(`${serviceUrl}/adminApi/collection/goodsPriceUpdate`, data)
/**
 *  系列查看数据
 */
export const seriesGoodsCount = (data) =>
	request.post(`${serviceUrl}/adminApi/collection/seriesGoodsCount`, data)

/**
 * 设置卖出复制任务
 */
export const setSellCopy = (data) =>
	request.post(`${serviceUrl}/adminApi/duty/setSell/copy`, data)

export const setSellRecordExport = (data) =>
	request.post(`${serviceUrl}/adminApi/duty/setSell/recordExport`, data,{ responseType: 'blob' })


// // 销毁任务中止
// export const dutyStop = (data) =>
// 	  request.post(`${serviceUrl}/adminApi/duty/stop`, data)
