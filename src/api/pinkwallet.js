import request from "../utils/request.js";

const serviceUrl = "/pinkwallet";

/**
 * 登录
 */
export const Login = (data) =>
  request.post(`${serviceUrl}/appApi/userLogin/doLogin`, data);

/**
 * 注册
 */
export const Register = (data) =>
  request.post(`${serviceUrl}/appApi/userRegister/doRegister`, data);

/**
 * 发送邮箱验证码
 */
export const SendEmailCode = (data) =>
  request.post(`${serviceUrl}/appApi/captcha/sendMailCaptcha`, data);

/**
 * 获取用户信息
 */
export const GetUserInfo = (data) =>
  request.post(`${serviceUrl}/appApi/user/getUserInfo`, data);