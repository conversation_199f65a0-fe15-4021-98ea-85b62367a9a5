import request from "@/utils/request.js";

const serviceUrl = "/np-collection-yanjie";

/**
 * 团队锁仓
 */
export const locklist = (data) =>
  request.post(`${serviceUrl}/adminApi/groupLockGoods/list`, data);

/**
 * 团队锁仓增加
 */
export const lockadd = (data) =>
  request.post(`${serviceUrl}/adminApi/groupLockGoods/lock`, data);

/**
 * 团队锁仓解锁
 */
export const unlock = (data) =>
  request.post(`${serviceUrl}/adminApi/groupLockGoods/unLock`, data);

/**
 * 自成交列表
 */
export const selfList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/list`, data);

/**
 * 自成交停止
 */
export const stopList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/stop`, data);

/**
 * 自成交增加
 */
export const addList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/add`, data);

/**
 * 批量购买
 */
export const batchBuy = (data) =>
  request.post(`${serviceUrl}/adminApi/collection/buy`, data);

/**
 * 设置涨跌停-只返回最新的一条未结束的
 */
export const setUpLimit = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/priceIncreaseMonitoringInfo`, data);

/**
 * 任务修改
 */
export const dutyEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/update`, data);



