import request from '@/utils/request.js'

const serviceUrl = '/queqiao'

/**
 * 群聊列表
 * POST /adminApi/im/groupChat/list
 * 接口ID：33070001
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33070001
 */
export const groupChatList = (data) =>
  request.post(`${serviceUrl}/adminApi/im/groupChat/list`, data)

/**
 * 群主详情信息
 * POST /adminApi/im/group/ownerInfo
 * 接口ID：33069995
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069995
 */
export const ownerInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/ownerInfo`, data)

/**
 * 群组操作
 * POST /adminApi/im/group/operate
 * 接口ID：33070003
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33070003
 */
export const groupOperate = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/operate`, data)

/**
 * 群成员信息列表
 * POST /adminApi/im/group/memberInfo
 * 接口ID：33069993
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069993
 */
export const groupMemberInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/memberInfo`, data)

/**
 * 群成员信息操作
 * POST /adminApi/im/group/member/operate
 * 接口ID：33069998
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069998
 */
export const memberOperate = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/member/operate`, data)

/**
 * 群聊天记录
 * POST /adminApi/im/group/chatRecords
 * 接口ID：33227240
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33227240
 */
export const groupChatRecords = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/chatRecords`, data)

/**
 * 群成员发言记录
 * POST /adminApi/im/group/member/chatRecords
 * 接口ID：33070002
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33070002
 */
export const memberChatRecords = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/member/chatRecords`, data)

/**
 * 群成员发言记录
 * POST /adminApi/im/group/memberGoods
 * 接口ID：33070002
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33070002
 */
export const memberGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/memberGoods`, data)

/**
 * 未入群用户列表
 * POST /adminApi/im/group/memberNotIn
 * 接口ID：33069994
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069994
 */
export const memberNotIn = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/memberNotIn`, data)

/**
 * 管理/加入的群
 * POST /adminApi/im/group/manageOrJoin
 * 接口ID：33069991
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069991
 */
export const manageOrJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/manageOrJoin`, data)

/**
 * 群聊公告记录
 * POST /adminApi/im/group/noticeRecord
 * 接口ID：33799856
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33799856
 */
export const noticeRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/noticeRecord`, data)

/**
 * 举报信息列表
 * POST /adminApi/im/report/list
 * 接口ID：33069996
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069996
 */
export const reportList = (data) =>
  request.post(`${serviceUrl}/adminApi/im/report/list`, data)

/**
 * 举报列表操作
 * POST /adminApi/im/report/operate
 * 接口ID：33069997
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33069997
 */
export const reportOperate = (data) =>
  request.post(`${serviceUrl}/adminApi/im/report/operate`, data)

/**
 * 举报详情
 * POST /adminApi/im/report/detail
 * 接口ID：33070000
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-33070000
 */
export const reportDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/im/report/detail`, data)

/**
 * 群组发言
 * POST /adminApi/im/group/systemMsg
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */

export const groupSystemMsg = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/systemMsg`, data)

/**
 * 初始化群数据
 * POST /adminApi/im/initGroup
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */

export const initGroup = (data) =>
  request.post(`${serviceUrl}/adminApi/im/initGroup`, data)

/**
 * 初始化批量导入账号
 * POST /adminApi/im/group/initBatchImportAccount
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */

export const initBatchImportAccount = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/initBatchImportAccount`, data)

/**
 * 群聊记录列表
 * POST /adminApi/im/group/chatReport/list
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */

export const chatRecordList = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/chatReport/list`, data)

/**
 * 群成员详情信息
 * POST /adminApi/im/groupMember/info
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */
export const chatRecordMemberInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/im/groupMember/info`, data)

/**
 * 群聊天记录操作
 * POST /adminApi/im/group/chatRecord/operate
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */
export const operateChatRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/chatRecord/operate`, data)

/**
 * 聊天记录发言
 * POST /adminApi/im/chatRecord/systemMsg
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1322664/apis/api-********
 */
export const chatRecords = (data) =>
  request.post(`${serviceUrl}/adminApi/im/chatRecord/systemMsg`, data)

/**
 * 聊天记录发言
 * POST /adminApi/im/group/batchJoinInGroup
 */
export const joinToGroup = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/batchJoinInGroup`, data)

/**
 * 群聊人数-查看最新人数
 * POST /adminApi/im/group/batchJoinInGroup
 */
export const groupMemberNum = (data) =>
  request.post(`${serviceUrl}/adminApi/im/groupMemberNum`, data)

//交易播报配置列表
export const imOrderConfigList = (data) =>
  request.post(`${serviceUrl}/adminApi/imOrderConfig/list`, data)

//交易播报添加配置
export const imOrderConfigAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/imOrderConfig/add`, data)

//交易播报终止配置
export const imOrderConfigListEnd = (data) =>
  request.post(`${serviceUrl}/adminApi/imOrderConfig/end`, data)

//微信二维码 列表
export const qrcodeList = (data) =>
  request.post(`${serviceUrl}/adminApi/qrcode/list`, data)

//微信二维码 新增
export const qrcodeAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/qrcode/add`, data)

//微信二维码 修改
export const qrcodeEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/qrcode/edit`, data)

//微信二维码 删除
export const qrcodeDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/qrcode/delete`, data)

//举报列表
export const chatReportList = (data) =>
  request.post(`${serviceUrl}/adminApi/reportRecord/list`, data)

//举报操作
export const chatReportOperate = (data) =>
  request.post(`${serviceUrl}/adminApi/reportRecord/operate`, data)

//全pgc发言
export const pgcSystemMsg = (data) =>
  request.post(`${serviceUrl}/adminApi/im/group/pgc/systemMsg`, data)

//管理台举报列表操作接口请求参数
export const reportRecordOperate = (data) =>
  request.post(`${serviceUrl}/adminApi/reportRecord/operate`, data)
