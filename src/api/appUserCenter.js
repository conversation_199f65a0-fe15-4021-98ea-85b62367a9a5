import request from "@/utils/request.js";

const serviceUrl = "/appusercenter";

/**
 * 用户同步
 */
export const userDataToEs = (data) =>
  request.post(`${serviceUrl}/adminApi/user/init/userDataToEs`, data);

/**
 * 获取邮件列表
 */
export const listPageEmails = (data) =>
  request.post(`${serviceUrl}/adminApi/smsEmails/listPageEmails`, data);

/**
 * 获取邮件列表
 */
export const initSyncNftcnUserBusinessCountData = (data) =>
  request.post(
    `${serviceUrl}/adminApi/user/init/initSyncNftcnUserBusinessCountData`,
    data
  );

/**
 * 下载邮件导入模板
 */
export const downloadEmailsImportTemplate = (data) =>
  request.post(
    `${serviceUrl}/adminApi/smsEmails/downloadEmailsImportTemplate`,
    data
  );

/**
 * 导入模板
 */
export const importEmailsExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/smsEmails/missWebSign/importEmailsExcel`,
    data
  );

/**
 * 修改邮件状态
 */
export const updateEmailsSendStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/smsEmails/updateEmailsSendStatus`, data);

/**
 * 账号注销审核记录列表
 */
export const cancelAccountRecordList = (data) =>
  request.post(`${serviceUrl}/adminApi/user/cancel/account/record/list`, data);

/**
 * 账号注销审核
 */
export const check = (data) =>
  request.post(`${serviceUrl}/adminApi/user/cancel/account/record/check`, data);

/**
 * 账号注销备注
 */
export const remark = (data) =>
  request.post(
    `${serviceUrl}/adminApi/user/cancel/account/record/remark`,
    data
  );

/**
 * 账号注销驳回原因
 */
export const rejectReason = (data) =>
  request.post(
    `${serviceUrl}/adminApi/user/cancel/account/record/rejectReason`,
    data
  );

/**
 * 用户列表
 */
export const getUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/user/list`, data);

/**
 * 用户列表导出
 */
export const userListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/user/list/export`, data, {
    responseType: "blob",
  });

/**
 * 树藏平台配置列表
 */
export const digitalCollectionPlatformList = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/platform/list`, data);

/**
 * 树藏平台配置隐藏
 */
export const digitalCollectionPlatformHide = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/platform/hide`, data);

/**
 * 树藏平台配置添加
 */
export const digitalCollectionPlatformAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/platform/add`, data);

/**
 * 树藏平台配置删除
 */
export const digitalCollectionPlatformDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/platform/delete`, data);

/**
 * 树藏平台配置更新
 */
export const digitalCollectionPlatformUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/zsb/platform/update`, data);

/**
 * 修改用户实名状态
 * POST /adminApi/user/modify/certification
 * 接口ID：29393053
 * 接口地址：https://www.apifox.cn/web/project/1048090/apis/api-29393053
 * @param { Number } data.certification 实名状态：2-护照实名通过，8-实名申请驳回
 * @param { Number } data.userId 用户ID
 */
export const certification = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/certification`, data);

/**
 * app用户企业认证加v去v
 * @POST /adminApi/user/change/level
 * @接口ID：29470211
 * 接口地址：https://www.apifox.cn/web/project/1048090/apis/api-29470211
 * @param { String } data.id 自增ID
 * @param { String } data.brandIntroduction 品牌介绍
 * @param { String } data.brandName  品牌名称
 * @param { String } data.businessLicense  营业执照
 * @param { String } data.companyName   企业名称
 * @param { String } data.nickname 用户名
 * @param { Number } data.userLevel 用户等级
 */
export const changeLevel = (data) =>
  request.post(`${serviceUrl}/adminApi/user/change/level`, data);

/**
 * 修改用户状态
 * POST /adminApi/user/modify/status
 * 接口ID：29393059
 * 接口地址：https://www.apifox.cn/web/project/1048090/apis/api-29393059
 * @param { String } data.status  用户状态：0-禁用，10-正常
 */
export const modifyStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/status`, data);

/**
 * 修改用户信息
 */
export const modifyInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/info`, data);
/**
 * 下载模板
 * @api /adminApi/excel/downLoadTemplate
 */
export const userCenterDownLoadTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);
/**
 * 批量导入手机号注册
 * POST /adminApi/user/import/phone/register
 * 接口ID：36881887
 * @api https://www.apifox.cn/web/project/1048090/apis/api-36881887
 */
export const phoneRegister = (data) =>
  request.post(`${serviceUrl}/adminApi/user/import/phone/register`, data);
/**
 * 批量修改用户支付密码和登录密码
POST /adminApi/user/batch/modify/pass
接口ID：36881879
接口地址：https://www.apifox.cn/web/project/1048090/apis/api-36881879
 */
export const modifyPass = (data) =>
  request.post(`${serviceUrl}/adminApi/user/batch/modify/pass`, data);
/**
 * 批量修改用户头像或背景图
POST /adminApi/user/batch/modify/userIcon
接口ID：36881881
接口地址：https://www.apifox.cn/web/project/1048090/apis/api-36881881
 */
export const modifyUserIcon = (data) =>
  request.post(`${serviceUrl}/adminApi/user/batch/modify/userIcon`, data);
/**
 * 用户批量修改信息
POST /adminApi/user/batch/modify/userInfo
接口ID：36881888
接口地址：https://www.apifox.cn/web/project/1048090/apis/api-36881888
 */
export const modifyUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/batch/modify/userInfo`, data);
/**
 * 批量导入邮箱注册
POST /adminApi/user/import/email/register
接口ID：36881878
接口地址：https://www.apifox.cn/web/project/1048090/apis/api-36881878
 */
export const emailRegister = (data) =>
  request.post(`${serviceUrl}/adminApi/user/import/email/register`, data);
/**
 * windy专用-批量导入邮箱注册
POST /adminApi/user/windy/import/email/register
接口ID：36881883
接口地址：https://www.apifox.cn/web/project/1048090/apis/api-36881883
 */
export const emailRegisterForWindy = (data) =>
  request.post(`${serviceUrl}/adminApi/user/windy/import/email/register`, data);

// 飞跃计划修改比例
// export const modifyInfo = data =>
//   request.post(`${serviceUrl}/adminApi/user/modify/info`, data)

//修改权重
export const recommend = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/recommend`, data);

/**
 * 批量导入手机号注册
 * POST /adminApi/user/import/phone/register
 * 接口ID：36881887
 * @api https://www.apifox.cn/web/project/1048090/apis/api-36881887
 */
export const fansCount = (data) =>
  request.post(`${serviceUrl}/adminApi/user/batch/modify/fansCount`, data);

// 获取最近登陆用户
//   POST /adminApi/bi/user/getLastLoginUser
//   接口ID：66955565
//   接口地址：https://www.apifox.cn/link/project/1048090/apis/api-66955565

export const getLastLoginUser = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/user/getLastLoginUser`, data);

/**
 * 用户余额累计
 */
export const getAllUserSumBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/user/getAllUserSumBalance`, data);

/**
 * 获取用户的净买入
 */
export const getBuySubSellAmount = (data) =>
  request.post(`${serviceUrl}/adminApi/user/getBuySubSellAmount`, data);

/**
 * 获取用户的仓位
 */
export const getGoodsAmount = (data) =>
  request.post(`${serviceUrl}/adminApi/user/getGoodsAmount`, data);

/**
 * 获取用户的仓位
 */
export const goodsAmountList = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/user/goodsAmount/list`, data);

export const goodsAmountExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/user/goodsAmount/export`, data, {
    responseType: "blob",
  });

/**
 * 用户实名修改
 */
export const auth = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/auth `, data);

export const wechatInfoEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/user/wechatInfoEdit `, data);

export const cancelAccountBatch = (data) =>
  request.post(`${serviceUrl}/adminApi/user/cancel/account/batch `, data);

export const ipRemarkSearch = (data) =>
  request.post(`${serviceUrl}/adminApi/ipRemark/search`, data);

export const ipRemarkAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/ipRemark/add`, data);

export const ipRemarkUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/ipRemark/update`, data);

//用户信息导出
export const userInfoExport = (data) =>
  request.post(`${serviceUrl}/adminApi/userInfo/export`, data, {
    responseType: "blob",
  });

//用户列表（风控）
export const userList = (data) =>
  request.post(`${serviceUrl}/adminApi/userRiskControl/list`, data);

// 用户持仓明细
export const userPositionList = (data) =>
  request.post(
    `${serviceUrl}/adminApi/userRiskControl/riskPositionDetail`,
    data
  );

// 用户收益信息
export const userProfitList = (data) =>
  request.post(`${serviceUrl}/adminApi/userRiskControl/userProfitInfo`, data);

/**
 * 获取用户的交易战绩
 */
export const getUserTradeRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/user/getCopartnerInfo`, data);

/**
 * 修改用户身份 比例
 */
export const updateUserIdentity = (data) =>
  request.post(`${serviceUrl}/adminApi/user/modify/copartnerInfo`, data);