import request from "@/utils/request.js";

const serviceUrl = "/waliangge";
const serviceUrl2 = "/flybattle";

/**
 * 清除缓存
 */
export const clearMarketCache = (data) =>
  request.post(`${serviceUrl}/adminApi/market/clearMarketCache`, data);

/**
 * banner列表
 */
export const getBannerList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/list`, data);

/**
 * banner详情
 */
export const detailBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/detail`, data);

/**
 * banner新增
 */
export const addBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/add`, data);

/**
 * banner编辑
 */
export const updateBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/update`, data);

/**
 * banner上下线
 */
export const onlineBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/online`, data);

/**
 * banner删除
 */
export const deleteBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/market/banner/delete`, data);

/**
 * 金刚区查询
 */
export const kingKongList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/list`, data);

/**
 * 金刚区详情
 */
export const detailKingkong = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/detail`, data);

/**
 * 金刚区编辑
 */
export const updateKingkong = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/update`, data);

/**
 * 楼层区/瓷片区配置列表
 */
export const floorList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tabList`, data);

/**
 * 楼层区/瓷片区 详情
 */
export const tabDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tabDetail`, data);

/**
 * 楼层区/瓷片区 新增
 */
export const addMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/addMarketTab`, data);

/**
 * 楼层区/瓷片区 修改
 */
export const updateMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/updateMarketTab`, data);

/**
 * 楼层区/瓷片区 隐藏显示
 */
export const showMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/showMarketTab`, data);

/**
 * 楼层区/瓷片区 删除
 */
export const deleteMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/deleteMarketTab`, data);

/**
 * 楼层区/瓷片区 透出IP作品列表 & 品牌区列表
 */
export const leftJoinIpList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/leftJoinIpList`, data);

/**
 * 楼层区/瓷片区 透出IP作品列表 & 品牌区详情
 */
export const leftJoinDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/leftJoinDetail`, data);

/**
 * 专题区列表
 */
export const subjectList = (data) =>
  request.post(`${serviceUrl}/adminApi/special/list`, data);

/**
 * 专题区详情
 */
export const detailSubject = (data) =>
  request.post(`${serviceUrl}/adminApi/special/detail`, data);

/**
 * 专题区编辑
 */
export const updateSubject = (data) =>
  request.post(`${serviceUrl}/adminApi/special/update`, data);

/**
 * 品牌新增
 */
export const insertLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/insertLeftJoin`, data);

/**
 * 品牌编辑
 */
export const updateLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/updateLeftJoin`, data);

/**
 * 品牌显隐
 */
export const hideOrShowLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/hideOrShowLeftJoin`, data);

/**
 * 删除品牌
 */
export const delLeftJoin = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tab/delLeftJoin`, data);

/**
 * 精选ip列表
 */
export const hotUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hotUserList`, data);

/**
 * ip显隐
 */
export const hideOrShowHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hideOrShowHotUser`, data);

/**
 * 精选ip详情
 */
export const hotUserDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hotUserDetail`, data);

/**
 * 新增ip
 */
export const insertHotUser = (data) =>
  request.post(
    `${serviceUrl}/adminApi/hotuser/missWebSign/insertHotUser`,
    data
  );

/**
 *  编辑ip
 */
export const updateHotUser = (data) =>
  request.post(
    `${serviceUrl}/adminApi/hotuser/missWebSign/updateHotUser`,
    data
  );

/**
 * ip删除
 */
export const delHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/delHotUser`, data);

/**
 * 下载表单
 */
export const downLoadGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/downLoadGoods`, data, {
    responseType: "blob",
  });

/**
 * 下载模板
 */
export const downLoadTemplateExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/excel/downLoadTemplate`, data);

/**
 * 搜索关键词
 */
export const showHotWords = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/showHotWords`, data);

/**
 * 编辑关键词
 */
export const updateHotWords = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/updateHotWords`, data);

/**
 * 展示主界面弹框
 */
export const showBounceTips = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/showBounceTips`, data);

/**
 * 编辑主弹框
 */
export const updateBounceTips = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/updateBounceTips`, data);

/**
 * 专题/品牌编辑
 */
export const updateMarketTabInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/market/updateMarketTabInfo`, data);

/**
 * 模块列表
 */
export const moduleList = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/list`, data);

/**
 * 编辑模块
 */
export const updateModule = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/update`, data);

/**
 * 模块隐藏
 */
export const hideModule = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/hideOrShow`, data);

/**
 * bigVerse 列表
 */
export const getBigVerseList = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/list`, data);

/**
 * bigVerse 详情
 */
export const getBigVerseDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/detail`, data);

/**
 * bigVerse 新增
 */
export const addBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/add`, data);

/**
 *  bigVerse 删除
 */
export const deleteBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/delete`, data);

/**
 *  bigVerse 更新
 */
export const updateBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/update`, data);

/**
 * 活动列表
 */
export const activityList = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/list`, data);

/**
 *  活动详情
 */
export const activityDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/detail`, data);

/**
 * 活动新增
 */
export const activityAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/add`, data);

/**
 * 活动编辑
 */
export const activityEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/update`, data);

/**
 *  活动删除
 */
export const activityDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/delete`, data);

/**
 *  活动上下线切换
 */
export const activityStatusToggle = (data) =>
  request.post(
    `${serviceUrl}/adminApi/activityConfig/updateOnlineStatus`,
    data
  );

/**
 *  下载 Excel
 */
export const activityDownloadExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/downLoadGoods`, data, {
    responseType: "blob",
  });

/**
 * 导入盲盒内作品数据
 */
export const rebuildImportTemplate = (data) =>
  request.post(
    `${serviceUrl}/adminApi/activityConfig/importBlindBoxData`,
    data
  );

/**
 *  清空缓存
 */
export const clearRedis = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/clearRedis`, data);

/**
 * 获取当前ip可以选择的作品列表
 * POST /adminApi/market/ipGoodsChooseList
 * 接口ID：32995382
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995382
 * @param data.contractAddress 用户地址
 * @param data.maxPrice 最高价格
 * @param data.minPrice 最低价格
 */
export const ipGoodsChooseList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/ipGoodsChooseList`, data);

/**
 * 获取IP当前作品库
 * POST /adminApi/market/ipGoodsList
 * 接口ID：32995383
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995383
 * @param data
 * @return {Promise<unknown> | Promise}
 */
export const ipGoodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/ipGoodsList`, data);

/**
 * 查看作品详情
 * POST /adminApi/market/getGoodsDetails
 * 接口ID：32995381
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995381
 */
export const getGoodsDetails = (data) =>
  request.post(`${serviceUrl}/adminApi/market/getGoodsDetails`, data);

/**
 * 作品主题页列表
 * POST /adminApi/market/themeList
 * 接口ID：33041695
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041695
 * @param data.pageNum 页码
 * @param data.pageSize 每页数量
 */
export const themeList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/themeList`, data);

/**
 * 查看作品主题页
 * POST /adminApi/market/themeDetail
 * 接口ID：33548972
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33548972
 * @param data.id 主题id
 */
export const themeDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/themeDetail`, data);

/**
 * 获取可用的作品主题页id
 * POST /adminApi/market/getThemeId
 * 接口ID：33041691
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041691
 */
export const getThemeId = (data) =>
  request.post(`${serviceUrl}/adminApi/market/getThemeId`, data);

/**
 * 创建作品主题页
 * POST /adminApi/market/addTheme
 * 接口ID：33041689
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041689
 * @param data.id 主题id
 * @param data.remark 主题备注
 * @param data.themeInfo 主题信息
 */
export const addTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/addTheme`, data);

/**
 * 编辑作品主题页
 * POST /adminApi/market/updateTheme
 * 接口ID：33041696
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041696
 * @param data.id 主题id
 * @param data.remark 主题备注
 * @param data.themeInfo 主题信息
 */
export const updateTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/updateTheme`, data);

/**
 * 删除作品主题页
 * POST /adminApi/market/deleteTheme
 * 接口ID：33041690
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041690
 * @param data.id 主题id
 */
export const deleteTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/deleteTheme`, data);

/**
 * 查看主题下作品列表
 * POST /adminApi/market/theme/goodsList
 * 接口ID：33041694
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041694
 * @param data.themeId 主题id
 * @param data.tid 作品 token id
 * @param data.title  作品标题
 * @param data.pageNum 页码
 * @param data.pageSize 每页数量
 */
export const themeGoodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/goodsList`, data);

/**
 * 主题下新增作品
 * POST /adminApi/market/theme/addGoods
 * 接口ID：33041692
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041692
 */
export const themeAddGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/addGoods`, data);

/**
 * 主题作品设置显隐状态
 * POST /adminApi/market/theme/goods/status
 * 接口ID：33041693
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041693
 * @param {String} data.idListStr  作品id数组 转字符串
 * @param data.status 状态：0隐藏，1显示
 */
export const themeGoodsStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/goods/status`, data);

// 取消定时
export const cancelTiming = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/cancelTiming`, data);

/**
 * 导入活动资格人员
 */
export const importActivityShipData = (data) =>
  request.post(
    `${serviceUrl}/adminApi/activityConfig/importActivityShipData`,
    data
  );

export const updateActivityNewStatusAndTiming = (data) =>
  request.post(
    `${serviceUrl}/adminApi/activityConfig/updateActivityNewStatusAndTiming`,
    data
  );

export const updateEndTime = (data) =>
  request.post(`${serviceUrl}/adminApi/market/tabLeftJoin/updateEndTime`, data);

/**
 * 导入盲盒内作品数据 导入tid 列表导入
 */
export const mergeInItemTidImport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/activityConfig/mergeInItemTidImport`,
    data
  );

/**
 * 楼层区列表
 */
export const java_market_tabList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/marketTabList`, data);

/**
 * 楼层区列表
 */
export const getMarketTabLeftJoinByTabId = (data) =>
  request.post(
    `${serviceUrl}/adminApi/market/getMarketTabLeftJoinByTabId`,
    data
  );

/**
 * 楼层区列表
 */
export const getBelongMarketTab = (data) =>
  request.post(`${serviceUrl}/adminApi/market/getBelongMarketTab`, data);

/**
 * 自动开平仓任务创建和更新
 */
export const createAndUpdateAutoTradeDuty = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/createAndUpdateAutoTradeDuty`,
    data
  );

/**
 * 查询自动开平仓
 */
export const searchAutoTradeDuty = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/searchAutoTradeDuty`,
    data
  );
/**
 * 查询手动开仓
 */
export const searchHandTradeDuty = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/searchHandTradeDuty`,
    data
  );
/**
 * 手动开平仓创建
 */
export const createHandTradeDuty = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/createHandTradeDuty`,
    data
  );
/**
 * 手动开平仓撤销
 */
export const revokeHandTradeDuty = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/revokeHandTradeDuty`,
    data
  );
/**
 * 获取当前市价
 */
export const getRealMarketPrice = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/getRealMarketPrice`, data);
/**
 * 波动助理创建或更新
 */
export const createOrUpdateFluctuationAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/createOrUpdateFluctuationAssistant`,
    data
  );
/**
 * 波动助理查询
 */
export const searchFluctuationAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/searchFluctuationAssistant`,
    data
  );
/**
 * 超远期助理查询
 */
export const searchFurtherAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/searchFurtherAssistant`,
    data
  );
/**
 * 超远期助理创建或更新
 */
export const createOrUpdateFurtherAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/createOrUpdateFurtherAssistant`,
    data
  );
/**
 * 超远期助理删除
 */
export const deleteFurtherAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/deleteFurtherAssistant`,
    data
  );
/**
 * 超远期助理执行
 */
export const operateFurtherAssistant = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/duty/operateFurtherAssistant`,
    data
  );
/**
 * 配置查询
 */
export const configSearch = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/config/search`, data);
/**
 * 配置修改
 */
export const configUpdates = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/config/update`, data);
/**
 * 配置添加
 */
export const configadd = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/config/add`, data);
/**
 * 资讯列表
 */
export const consultationList = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/consultation/list`, data);
/**
 * 资讯修改
 */
export const editConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/consultation/editConfig`, data);
/**
 * 资讯添加
 */
export const editConfigAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/consultation/add`, data);
/**
 * 边玩边赚列表
 */
export const PlayAndEarnList = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/PlayAndEarn/list`, data);
/**
 * 边玩边赚修改
 */
export const PlayAndEarnedit = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/PlayAndEarn/editConfig`, data);
/**
 * 边玩边赚添加
 */
export const PlayAndEarnAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/PlayAndEarn/add`, data);

/** 起飞卡统计数据
 */
export const qifeiChart = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/chart`, data);

/** 起飞卡对战成交记录
 */
export const qifeiHistory = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/report/transactionHistory`,
    data
  );

/** 起飞卡对战开仓记录
 */
export const qifeiOpenHistory = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/report/openHistory`, data);

/** 起飞卡对战平仓记录
 */
export const qifeiCloseHistory = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/report/closeHistory`, data);

/** 起飞卡对战持仓记录
 */
export const qifeiHoldHistory = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/report/holdHistory`, data);
/** 起飞卡对战盘点记录
 */
export const qifeiHandicapLog = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/report/handicapLog`, data);
/**
 * 经纪商入驻申请列表
 */
export const settlementList = (data) =>
  request.post(`${serviceUrl}/adminApi/broker/settlement/list`, data);

/**
 * 经纪商入驻审核
 */
export const approve = (data) =>
  request.post(`${serviceUrl}/adminApi/broker/settlement/approve`, data);

/**
 *   调整经纪商等级
 */
export const adjustingLevels = (data) =>
  request.post(
    `${serviceUrl}/adminApi/broker/settlement/adjustingLevels`,
    data
  );
/**
 *   查询经纪商等级
 */
export const levelConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/broker/levelConfig/list`, data);
/**
 *   自动考核列表
 */
export const automaticAssessment = (data) =>
  request.post(
    `${serviceUrl}/adminApi/broker/settlement/automaticAssessment`,
    data
  );
/**
 *   新增标签
 */
export const addTag = (data) =>
  request.post(`${serviceUrl}/adminApi/activityTag/add`, data);
/**
 *   编辑标签
 */
export const tagUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/activityTag/update`, data);
/**
 *   标签列表
 */
export const tagList = (data) =>
  request.post(`${serviceUrl}/adminApi/activityTag/list`, data);
/**
 *   系列列表标签
 */
export const querySeriesTag = (data) =>
  request.post(`${serviceUrl}/adminApi/activityTag/querySeriesTag`, data);

/**
 *   新增投票活动
 */
export const addVote = (data) =>
  request.post(`${serviceUrl}/adminApi/activityVote/add`, data);

/**
 *   投票活动详情
 */
export const voteDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/activityVote/detail`, data);

/**
 *  投票活动导出
 */
export const voteExport = (data) =>
  request.post(`${serviceUrl}/adminApi/activityVote/export`, data, {
    responseType: "blob",
  });

/**
 * 合成 活动配置导出
 */
export const synthesisExport = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/ship/export`, data, {
    responseType: "blob",
  });
/**
 *  导出持仓记录
 */
export const holdHistoryExport = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/export/holdHistory`, data, {
    responseType: "blob",
  });

export const getKLine = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/getKLine`, data);
//插入底仓
export const insertMainHold = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/duty/insertMainHold`, data);

/** 对战收益
 */
export const battleProfitList = (data) =>
  request.post(`${serviceUrl2}/adminApi/flyBattle/report/battleProfit`, data);

/** 导出对战收益
 */
export const exportBattleProfit = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/report/exportBattleProfit
`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 平仓记录导出
 */
export const ExportCloseRecords = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/export/closeHistory
`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 开仓记录导出
 */
export const ExportOpenRecords = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/export/openHistory
`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 导出盘口记录
 */
export const ExportMarketRecords = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/export/handicapLog
`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 导出持仓记录
 */
export const ExportPositionHistory = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/export/holdHistory
`,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 成交记录导出
 */

export const ExportDealHistory = (data) =>
  request.post(
    `${serviceUrl2}/adminApi/flyBattle/export/transactionHistory
  `,
    data,
    {
      responseType: "blob",
    }
  );

/**
 * 经纪商分组
 */
export const brokerGroupList = (data) =>
  request.post(`${serviceUrl}/adminApi/broker_group/list`, data);

/**
 * 经纪商新增_编辑
 */
export const brokerGroupAddEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/broker_group/add_edit`, data);

/**
 * 经纪商查询
 */
export const brokerGroupInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/broker_group/get_edit_info`, data);
/**
 * 经济商导出
 */
export const brokerExport = (data) =>
  request.post(`${serviceUrl}/adminApi/broker_group/leader/export_peers`, data, {
    responseType: "blob",
  });
/**
 * 藏品质押记录导出
 */
export const iceActiveExport = (data) =>
  request.post(`${serviceUrl}/adminApi/iceActive/export`, data, {
    responseType: "blob",
  });

/**
 * 合成最大次数查询
 */
export const getMaxTimes = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/queryMaxMergeNum`, data);

/**
 * 体验金列表
 */
export const getExperienceList = (data) =>
  request.post(`${serviceUrl}/adminApi/coinTrail/search`, data)

/**
 * 体验金增加
 */
export const addExperience = (data) =>
  request.post(`${serviceUrl}/adminApi/coinTrail/add`, data)

/**
 * 管理台-暴躁龙质押记录
 */
export const getPledgeList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/ice/record/list`, data)

/**
 * 管理台-暴躁龙质押记录导出
 */
export const getPledgeExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/ice/record/list/export`, data, {
    responseType: "blob",
  })

