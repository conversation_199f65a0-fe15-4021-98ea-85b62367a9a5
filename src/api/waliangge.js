import request from '@/utils/request.js';

const serviceUrl = '/java/np-collection-pinkwallet';

/**参考代码  月球日-排行榜
 * @api /appApi/topic/ranking
 * @param {String} keyword 搜索关键词
 * @param {Number} pageNum 页码
 * @param {Number} pageSize 每页大小
 * @param {String} sortBy 列表类型 投票-vote, 交易-trade
 * @param {Number} topicId 主题id 1
 */
export const topicRank = (data) =>
	request.post(`${serviceUrl}/appApi/topic/ranking`, data);





	

	


	

	
	


	
	
	
