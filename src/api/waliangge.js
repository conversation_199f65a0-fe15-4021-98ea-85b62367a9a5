import request from '@/utils/request.js'

const serviceUrl = '/waliangge'

/**
 * 清除缓存
 */
export const clearMarketCache = (data) =>
  request.post(`${serviceUrl}/adminApi/market/clearMarketCache`, data)

/**
 * 金刚区查询
 */
export const kingKongList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/list`, data)

/**
 * 金刚区详情
 */
export const detailKingkong = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/detail`, data)

/**
 * 金刚区编辑
 */
export const updateKingkong = (data) =>
  request.post(`${serviceUrl}/adminApi/market/kingKong/update`, data)

/**
 * 专题区列表
 */
export const subjectList = (data) =>
  request.post(`${serviceUrl}/adminApi/special/list`, data)

/**
 * 专题区详情
 */
export const detailSubject = (data) =>
  request.post(`${serviceUrl}/adminApi/special/detail`, data)

/**
 * 专题区编辑
 */
export const updateSubject = (data) =>
  request.post(`${serviceUrl}/adminApi/special/update`, data)

/**
 * 精选ip列表
 */
export const hotUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hotUserList`, data)

/**
 * ip显隐
 */
export const hideOrShowHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hideOrShowHotUser`, data)

/**
 * 精选ip详情
 */
export const hotUserDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/hotUserDetail`, data)

/**
 * 新增ip
 */
export const insertHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/missWebSign/insertHotUser`, data)

/**
 *  编辑ip
 */
export const updateHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/missWebSign/updateHotUser`, data)

/**
 * ip删除
 */
export const delHotUser = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/delHotUser`, data)

/**
 * 下载表单
 */
export const downLoadGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/downLoadGoods`, data,{ responseType: 'blob' })

/**
 * 搜索关键词
 */
export const showHotWords = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/showHotWords`, data)

/**
 * 编辑关键词
 */
export const updateHotWords = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/updateHotWords`, data)

/**
 * 展示主界面弹框
 */
export const showBounceTips = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/showBounceTips`, data)

/**
 * 编辑主弹框
 */
export const updateBounceTips = (data) =>
  request.post(`${serviceUrl}/adminApi/hotuser/updateBounceTips`, data)

/**
 * 专题/品牌编辑
 */
export const updateMarketTabInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/market/updateMarketTabInfo`, data)

/**
 * 模块列表
 */
export const moduleList = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/list`, data)

/**
 * 编辑模块
 */
export const updateModule = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/update`, data)

/**
 * 模块隐藏
 */
export const hideModule = (data) =>
  request.post(`${serviceUrl}/adminApi/manager/hideOrShow`, data)

/**
 * bigVerse 列表
 */
export const getBigVerseList = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/list`, data)

/**
 * bigVerse 详情
 */
export const getBigVerseDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/detail`, data)

/**
 * bigVerse 新增
 */
export const addBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/add`, data)

/**
 *  bigVerse 删除
 */
export const deleteBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/delete`, data)

/**
 *  bigVerse 更新
 */
export const updateBigVerse = (data) =>
  request.post(`${serviceUrl}/adminApi/bigverse/update`, data)
/**
 *  下载 Excel
 */
export const activityDownloadExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/downLoadGoods`, data,{ responseType: 'blob' })



/**
 * 获取当前ip可以选择的作品列表
 * POST /adminApi/market/ipGoodsChooseList
 * 接口ID：32995382
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995382
 * @param data.contractAddress 用户地址
 * @param data.maxPrice 最高价格
 * @param data.minPrice 最低价格
 */
export const ipGoodsChooseList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/ipGoodsChooseList`, data)

/**
 * 获取IP当前作品库
 * POST /adminApi/market/ipGoodsList
 * 接口ID：32995383
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995383
 * @param data
 * @return {Promise<unknown> | Promise}
 */
export const ipGoodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/ipGoodsList`, data)

/**
 * 查看作品详情
 * POST /adminApi/market/getGoodsDetails
 * 接口ID：32995381
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-32995381
 */
export const getGoodsDetails = (data) =>
  request.post(`${serviceUrl}/adminApi/market/getGoodsDetails`, data)

/**
 * 作品主题页列表
 * POST /adminApi/market/themeList
 * 接口ID：33041695
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041695
 * @param data.pageNum 页码
 * @param data.pageSize 每页数量
 */
export const themeList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/themeList`, data)

/**
 * 查看作品主题页
 * POST /adminApi/market/themeDetail
 * 接口ID：33548972
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33548972
 * @param data.id 主题id
 */
export const themeDetail = (data) =>
  request.post(`${serviceUrl}/adminApi/market/themeDetail`, data)

/**
 * 获取可用的作品主题页id
 * POST /adminApi/market/getThemeId
 * 接口ID：33041691
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041691
 */
export const getThemeId = (data) =>
  request.post(`${serviceUrl}/adminApi/market/getThemeId`, data)

/**
 * 创建作品主题页
 * POST /adminApi/market/addTheme
 * 接口ID：33041689
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041689
 * @param data.id 主题id
 * @param data.remark 主题备注
 * @param data.themeInfo 主题信息
 */
export const addTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/addTheme`, data)

/**
 * 编辑作品主题页
 * POST /adminApi/market/updateTheme
 * 接口ID：33041696
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041696
 * @param data.id 主题id
 * @param data.remark 主题备注
 * @param data.themeInfo 主题信息
 */
export const updateTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/updateTheme`, data)

/**
 * 删除作品主题页
 * POST /adminApi/market/deleteTheme
 * 接口ID：33041690
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041690
 * @param data.id 主题id
 */
export const deleteTheme = (data) =>
  request.post(`${serviceUrl}/adminApi/market/deleteTheme`, data)

/**
 * 查看主题下作品列表
 * POST /adminApi/market/theme/goodsList
 * 接口ID：33041694
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041694
 * @param data.themeId 主题id
 * @param data.tid 作品 token id
 * @param data.title  作品标题
 * @param data.pageNum 页码
 * @param data.pageSize 每页数量
 */
export const themeGoodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/goodsList`, data)

/**
 * 主题下新增作品
 * POST /adminApi/market/theme/addGoods
 * 接口ID：33041692
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041692
 */
export const themeAddGoods = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/addGoods`, data)

/**
 * 主题作品设置显隐状态
 * POST /adminApi/market/theme/goods/status
 * 接口ID：33041693
 * 接口地址：https://www.apifox.cn/web/project/1049637/apis/api-33041693
 * @param {String} data.idListStr  作品id数组 转字符串
 * @param data.status 状态：0隐藏，1显示
 */
export const themeGoodsStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/market/theme/goods/status`, data)


/**
 * 导入活动资格人员
 */
export const importActivityShipData = (data) =>
  request.post(`${serviceUrl}/adminApi/activityConfig/importActivityShipData`, data)


/**
 * 楼层区列表
 */
export const java_market_tabList = (data) =>
  request.post(`${serviceUrl}/adminApi/market/marketTabList`, data)
