import request from "@/utils/request.js";

const serviceUrl = "/hanxin";

/**
 * 保证金最低配置
 */
export const getMoenyLow = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/configure/show`, data);

/**
 * 保证金最低配置编辑
 */
export const configureEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/configure/edit`, data);

/**
 * 保证金记录列表
 */
export const getMoenyList = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/list`, data);

/**
 * 保证金支付列表
 */
export const getPayList = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/order/check/list`, data);

/**
 * 保证金退款列表
 */
export const getRefundList = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/refund/list`, data);

/**
 * 保证金退款列表导出
 */
export const refundExport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/securityDeposit/refund/list/export`,
    data,
    { responseType: "blob" }
  );

/**
 * 保证金支付审核
 */
export const fundCheck = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/order/check`, data);

/**
 * 保证金退款审核
 */
export const refundCheck = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/refund/check`, data);

/**
 * 保证金退款记录备注
 */
export const refundRemark = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/refund/remark`, data);

/**
 * 保证金驳回原因
 */
export const refundReson = (data) =>
  request.post(
    `${serviceUrl}/adminApi/securityDeposit/refund/reason/show`,
    data
  );

/**
 * 保证金打款
 */
export const refundpayment = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/refund/payment`, data);

/**
 * 订单列表
 */
export const orderlist = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list`, data);

/**
 * 订单列表导出
 */
export const orderlistExport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/export`, data, {
    responseType: "blob",
  });
/**
 * 订单列表导出Exel
 */
export const orderlistExportExcel = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/excel/export`, data, {
    responseType: "blob",
  });

/**
 * 订单列表导出-新csv
 */
export const orderlistYyCsvExport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/yy/csv/export`, data, {
    responseType: "blob",
  });

/**
 * 黑白名单查询列表
 */
export const blackWhiteUser = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/list`, data);

/**
 * 黑白名单新增
 */
export const blackWhiteUserAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/add`, data);

/**
 * 黑白名单编辑
 */
export const blackWhiteUserEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/blackWhiteUser/isDeleted`, data);

/**
 * 数字人民币关闭订单
 */
export const rmbCancel = (data) =>
  request.post(`${serviceUrl}/adminApi/order/digital/rmb/cancel`, data);

/**
 * 树藏订单列表
 */
export const digitalCollectionOrderList = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbOrder/adminOrderList`, data);

/**
 * 树藏订单取消
 */
export const digitalCollectionOrderClose = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbOrder/adminOrderClose`, data);

/**
 * 用户协商信息
 */
export const digitalCollectionUserNegotiationInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbOrder/cancelRecordInfo`, data);

/**
 * 用户操作历史
 */
export const digitalCollectionHistoryInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbOrder/adminListOrderCourse`, data);

/**
 * 空投项目列表
 */
export const airDropProjectList = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/list`, data);

/**
 * 空投项目新增
 */
export const airDropProjectAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/create`, data);

/**
 * 空投项目修改
 */
export const airDropProjectEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/update`, data);

/**
 * 空投作品列表
 */
export const airDropAssetsList = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/item/list`, data);

/**
 * 空投作品新增
 */
export const airDropAssetsAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/item/add`, data);

/**
 * 空投作品删除
 */
export const airDropAssetsDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/item/delete`, data);

/**
 * 空投用户列表
 */
export const airDropUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/user/list`, data);

/**
 * 空投用户新增
 */
export const airDropUserAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/user/add`, data);

/**
 * 空投用户删除
 */
export const airDropUserDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/user/delete`, data);

/**
 * 空投结果列表
 */
export const airDropResultList = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/list`, data);

/**
 * 空投结果预览
 */
export const airDropResultPreview = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/preview`, data);

/**
 * 空投结果清空
 */
export const airDropResultDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/clean`, data);

/**
 * 空投结果导出
 */
export const airDropResultExport = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/export`, data, {
    responseType: "blob",
  });

/**
 * 空投执行-项目
 */
export const airDropExecuteProject = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/confirm`, data);

/**
 * 空投执行
 */
export const airDropExecute = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/confirm`, data);

/**
 * 空投用户模版
 */
export const airDropUserTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/user/import/template`, data);

/**
 * 空投模版
 */
export const airDropItemTemplate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/item/import/template`, data);

/**
 * 保证金增加
 */
export const add_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/add`, data);

/**
 * 保证金减少
 */
export const reduce_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/reduce`, data);

/**
 * 保证金冻结
 */
export const freeze_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/freeze`, data);

/**
 * 解冻
 */
export const unfreeze_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/unfreeze`, data);

/**
 * 保证金备注
 */
export const remark_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/remark`, data);

/**
 * 保证金追加记录
 */
export const addTo_money = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/record/times`, data);

/**
 * 保证金变更记录
 */
export const change_money = (data) =>
  request.post(
    `${serviceUrl}/adminApi/securityDeposit/record/change/list`,
    data
  );

/**
 * 保证金订单
 */
export const getOrderList = (data) =>
  request.post(`${serviceUrl}/adminApi/securityDeposit/order/list`, data);

/**
 * 转手宝后台订单列表-导出
 */
export const adminOrderListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbOrder/adminOrderList/export`, data, {
    responseType: "blob",
  });

/**
 * 保证金支付审核列表-导出
 */
export const checkListExport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/securityDeposit/order/check/list/export`,
    data,
    { responseType: "blob" }
  );

/**
 *  月球主题活动数据同步到商品es中
 */
export const asyncDBOrderToES = (data) =>
  request.post(`${serviceUrl}/adminApi/order/init/orderEs`, data);

/**
 *  黑洞订单恢复
 */
export const unDestroy = (data) =>
  request.post(`${serviceUrl}/adminApi/order/unDestroy`, data);

/**
 *  系列销毁作品
 */
export const destroyByCtid = (data) =>
  request.post(`${serviceUrl}/adminApi/order/destroyByCtid`, data);

/**
 *  tid销毁作品
 */
export const destroyByTids = (data) =>
  request.post(`${serviceUrl}/adminApi/order/destroyByTids`, data);

/**
 *  空投作品清空
 */
export const airDropItemClean = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/item/clean`, data);

/**
 *  空投用户清空
 */
export const airDropUserClean = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/user/clean`, data);

/**
 *  空投记录导入
 */
export const airDropImport = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/import`, data);

/**
 *  空投记录导入
 */
export const airDropRecordAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/add`, data);

/**
 *  空投记录导入
 */
export const airDropRecordDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/record/delete`, data);

// =======================以下是新增的藏品空投接口=======================

/**
 * 空投项目列表-藏品
 */
export const airDropProjectListCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/list`, data);

/**
 * 空投项目新增-藏品
 */
export const airDropProjectAddCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/create`, data);

/**
 * 空投项目修改-藏品
 */
export const airDropProjectEditCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/update`, data);

/**
 * 空投结果列表-藏品
 */
export const airDropResultListCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/list`, data);

/**
 * 空投结果清空-藏品
 */
export const airDropResultDeleteCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/clean`, data);

/**
 * 空投结果导出-藏品
 */
export const airDropResultExportCollection = (data) =>
  request.post(
    `${serviceUrl}/adminApi/airDrop/collection/record/export`,
    data,
    { responseType: "blob" }
  );

/**
 * 空投执行-项目-藏品
 */
export const airDropExecuteProjectCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/confirm`, data);

/**
 * 空投执行-藏品
 */
export const airDropExecuteCollection = (data) =>
  request.post(
    `${serviceUrl}/adminApi/airDrop/collection/record/confirm`,
    data
  );

/**
 *  空投记录导入-藏品
 */
export const airDropImportCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/import`, data);

/**
 *  空投记录导入-藏品
 */
export const airDropRecordAddCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/add`, data);

/**
 *  空投记录导入-藏品
 */
export const airDropRecordDeleteCollection = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/delete`, data);

// =======================以下是新增的合成空投接口=======================

/**
 * 空投项目列表-合成
 */
export const airDropProjectListMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/list`, data);

/**
 * 空投项目新增-合成
 */
export const airDropProjectAddMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/create`, data);

/**
 * 空投项目修改-合成
 */
export const airDropProjectEditMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/update`, data);

/**
 * 空投结果列表-合成
 */
export const airDropResultListMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/list`, data);

/**
 * 空投结果清空-合成
 */
export const airDropResultDeleteMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/clean`, data);

/**
 * 空投结果导出-合成
 */
export const airDropResultExportMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/export`, data, {
    responseType: "blob",
  });

/**
 * 空投执行-项目-合成
 */
export const airDropExecuteProjectMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/confirm`, data);

/**
 * 空投执行-合成
 */
export const airDropExecuteMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/confirm`, data);

/**
 *  空投记录导入-合成
 */
export const airDropImportMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/import`, data);

/**
 *  空投记录导入-合成
 */
export const airDropRecordAddMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/record/add`, data);

/**
 *  空投记录导入-合成
 */
export const airDropRecordDeleteMerge = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/record/delete`, data);

/**
 *  虫子商城列表
 */
export const exchangeItemList = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemList`, data);

/**
 *  商品添加
 */
export const exchangeItemAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemAdd`, data);

/**
 *  商品添加
 */
export const exchangeItemUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/itemUpdate`, data);

/**
 *  兑换记录
 */
export const exchangeList = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/list`, data);

/**
 *  兑换记录备注
 */
export const exchangeRemark = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/remark`, data);

/**
 * 兑换记录导出
 */
export const exchangeExport = (data) =>
  request.post(`${serviceUrl}/adminApi/wallet/exchange/export`, data, {
    responseType: "blob",
  });

// 导入求购信息列表
//   POST /adminApi/order/target/orderTargetBuyList
//   接口ID：65517024
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-65517024

export const orderTargetBuyList = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/orderTargetBuyList`, data);

// 批量取消求购
//   POST /adminApi/order/target/batchCancelWantToBuy
//   接口ID：65517022
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-65517022
export const batchCancelWantToBuy = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/batchCancelWantToBuy`,
    data
  );

// 导入求购信息excel
//   POST /adminApi/order/target/importWantToBuyExcel
//   接口ID：65517023
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-65517023

export const importWantToBuyExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/importWantToBuyExcel`,
    data
  );

// 下载导入求购信息模板
//   POST /adminApi/order/target/downLoadOrderTargetBuyTemplate
//   接口ID：65523788
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-65523788
export const downLoadOrderTargetBuyTemplate = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/downLoadOrderTargetBuyTemplate`,
    data
  );

// 用户订单报表分析
//   POST /adminApi/bi/userOrderAssay/listUserOrderAssay
//   接口ID：66953913
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-66953913
export const listUserOrderAssay = (data) =>
  request.post(
    `${serviceUrl}/adminApi/bi/userOrderAssay/listUserOrderAssay`,
    data
  );

// 查看订单用户
//   POST /adminApi/bi/userOrderAssay/listOrderUser
//   接口ID：66953912
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-66953912

export const listOrderUser = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/userOrderAssay/listOrderUser`, data);

// 获取最近交易订单
//   POST /adminApi/bi/userOrderAssay/lastDealOrder
//   接口ID：66953911
//   接口地址：https://www.apifox.cn/link/project/1049651/apis/api-66953911

export const lastDealOrder = (data) =>
  request.post(`${serviceUrl}/adminApi/bi/userOrderAssay/lastDealOrder`, data);

// 自动成交
export const tradeList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/trade/list`, data);

export const tradeAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/trade/add`, data);

export const tradeStop = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/trade/stop`, data);

export const orderTargetBuyListExport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/orderTargetBuyList/export`,
    data,
    { responseType: "blob" }
  );

export const verification = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/verification`, data);

export const restGoodsNum = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/creationCs/restGoodsNum`, data);

export const userValidate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/creationCs/userValidate`, data);

export const creationCsCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/creationCs/create`, data);

export const creationCslist = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/creationCs/list`, data);

export const recordExport = (data) =>
  request.post(
    `${serviceUrl}/adminApi/airDrop/creationCs/record/export`,
    data,
    { responseType: "blob" }
  );

export const orderlistExportCsvt = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/finance/exportCsv`, data, {
    responseType: "blob",
  });
// 佣金明细
export const financeDataSystemFee = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/systemFee`, data);
// 佣金明细导出
export const financeDataSystemFeeOrderExportExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/systemFee/orderExportExcel`,
    data,
    { responseType: "blob" }
  );

// 版税明细
export const financeDataCopyrightFee = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/copyrightFee`, data);
// 版税明细导出
export const financeDataCopyrightFeeOrderExportExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/copyrightFee/orderExportExcel`,
    data,
    { responseType: "blob" }
  );

// 提现明细
export const financeDatawithdrawFee = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/withdrawFee`, data);
// 提现明细导出
export const financeDataWithdrawFeeExportExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/withdrawFee/exportExcel`,
    data,
    { responseType: "blob" }
  );

// 提现明细合计
export const financeDataWithdrawFeeTotal = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/withdrawFeeTotal`, data);

// 渠道成本
export const financeDataPayMethodCosts = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/payMethodCosts`, data);

// 财务订单
export const financeDataList = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/finance`, data);

// 财务订单 导出
export const financeDataUserOrderCsvExport = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/finance/csv/export`, data, {
    responseType: "blob",
  });

// 财务订单 合计
export const financeDataTotal = (data) =>
  request.post(`${serviceUrl}/adminApi/order/list/finance/total`, data);

// 财务 用户余额
export const financeDataUserBalancet = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/userBalance`, data);

// 财务 用户余额合计
export const financeDataUserBalanceTotal = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/userBalanceTotal`, data);

// 财务 用户余额导出
export const financeDataUserExportExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/userBalance/exportExcel`,
    data,
    { responseType: "blob" }
  );

// nft收入
export const financeDataIncome = (data) =>
  request.post(`${serviceUrl}/adminApi/financeData/income`, data);

// nft收入 导出
export const financeDataUserOrderExportExcel = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/income/orderExportExcel`,
    data,
    { responseType: "blob" }
  );

// 合计求购份数
export const orderTargetBuyListTotal = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/orderTargetBuyList/total`,
    data
  );
// 管理台添加求购接口
export const orderTargetBatchCreate = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/batchCreate`, data);

// 藏品终止
export const airDropCollectionStop = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/collection/stop`, data);

// 合成空投管理
export const airDropMergeStop = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/merge/stop`, data);

//  导出明细
export const tradeRecordExport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/trade/recordExport`, data, {
    responseType: "blob",
  });

//  导出 type 类型 1-全部 2-创作者 3-藏家余额 4-头部藏家
export const csvExportCurrent = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/userBalance/csvExportCurrent`,
    data,
    { responseType: "blob" }
  );

//  导出 day 日期 2013-01-01
export const csvExportSnapshot = (data) =>
  request.post(
    `${serviceUrl}/adminApi/financeData/userBalance/csvExportSnapshot`,
    data,
    { responseType: "blob" }
  );

// 加速求购
export const quickTarget = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/quickTarget`, data);

// 竞价列表
export const biddingList = (data) =>
  request.post(`${serviceUrl}/adminApi/series/query_bidding_list`, data);
// 竞价列表导出
export const biddingListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/series/biddingListExport`, data, {
    responseType: "blob",
  });
// 竞价列表撤销
export const revokeBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/revoke_bidding`, data);
// 竞价列表批量撤销
export const batchRevokeBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/batch_revoke_bidding`, data);
// 竞价列表添加
export const adminAddBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/admin_add_bidding`, data);
// 竞价列表上架
export const ListingBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/Listing_bidding`, data);
// 系列列表是否开启竞价
export const openBidding = (data) =>
  request.post(`${serviceUrl}/adminApi/series/open_bidding`, data);

// 系列列表是否开启竞价
export const quickTargetSingle = (data) =>
  request.post(`${serviceUrl}/adminApi/order/target/quickTargetSingle`, data);

// ugc创建订单
export const ugcCreateOrder = (data) =>
  request.post(`${serviceUrl}/adminApi/order/createOrder`, data);

// ugc上传模板
export const batchCreateOrder = (data) =>
  request.post(`${serviceUrl}/adminApi/order/batchCreateOrder`, data);
// 启用求购
export const enableOrderTarget = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/series/enableOrderTarget`,
    data
  );

export const enableExitOrderTarget = (data) =>
  request.post(
    `${serviceUrl}/adminApi/order/target/series/enableExitOrderTarget`,
    data
  );

/**
 * 定时任务列表
 */
export const createDutyList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/list`, data);

/**
 * 新增定时任务
 */
export const createDutyAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/add`, data);

/**
 * 白名单上传
 */
export const createWhiteUserImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/whiteUserImport`, data);

/**
 * 任务删除
 */
export const createDutyDelete = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/delete`, data);

/**
 * 任务查询
 */
export const createDutyInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/info`, data);

/**
 * 任务修改
 */
export const createDutyEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/edit`, data);

/**
 * 白名单查询
 */
export const createWhiteUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/whiteUserList`, data);

/**
 * 作品上传
 */
export const createGoodsImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/goodsImport`, data);
/**
 * 导出失败记录Excel
 */
export const createFailImport = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/export`, data, {
    responseType: "blob",
  });

// 销毁任务中止
export const createDutyStop = (data) =>
  request.post(`${serviceUrl}/adminApi/duty/stop`, data);

// 暴躁龙
export const bzlGoodsList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/goods/list`, data);

export const bzlGoodsListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/goods/list/export`, data, {
    responseType: "blob",
  });

export const bzlInviteList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/invite/list`, data);

export const bzlOrderList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/order/list`, data);

export const bzlOrderListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/order/list/export`, data, {
    responseType: "blob",
  });

export const bzlStoneConfigList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/stone/config/list`, data);

export const bzlStoneConfigUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/stone/config/update`, data);

export const bzlUserLevelUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/user/levelUpdate`, data);

export const bzlUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/user/list`, data);

export const bzlUserListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/user/list/export`, data, {
    responseType: "blob",
  });

export const inviteListCount = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/invite/list/count`, data);

/**
 * 联盟申请表
 */
export const bzlApplyList = (data) =>
  request.post(
    `${serviceUrl}/adminApi/bzl-group/group/coalitionApplyList`,
    data
  );
export const collectionTransferList = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/list`, data)

export const collectionTransferAdd = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/add`, data)

export const exportFailBalanceExcel = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/exportFailBalanceExcel`, data,{ responseType: 'blob' })

export const exportFailGoodsExcel = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/exportFailGoodsExcel`, data,{ responseType: 'blob' })

export const exportUserHoldBalanceExcel = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/exportUserHoldBalanceExcel`, data,{ responseType: 'blob' })

export const exportUserHoldGoodsExcel = (data) =>
    request.post(`${serviceUrl}/adminApi/collectionTransfer/exportUserHoldGoodsExcel`, data,{ responseType: 'blob' })

/**
 * 联盟申请表状态修改
 */
export const bzlApplyUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl-group/group/coalitionState`, data);

/**
 * 创建联盟
 */
export const bzlCreateGroup = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl-group/group/addCoalition`, data);

/**
 * 联盟身份修改
 */
export const bzlGroupUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl-group/group/coalitionUpdate`, data);

/**
 * 联盟列表
 */
export const bzlGroupList = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl-group/group/list`, data);

/**
 * 联盟列表导出
 */
export const bzlGroupExport = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl-group/group/list/export`, data, {
    responseType: "blob",
  });

/**
 * 联盟返佣配置查询
 */
export const bzlGroupConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/fee/config/list`, data);

/**
 * 联盟返佣配置修改
 */
export const bzlGroupConfigUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/bzl/fee/config/update`, data);

// 自动成交任务
export const autoDealTask = (data) =>
  request.post(`sunshine/adminApi/duty/trade/add`, data);

// 空投任务废弃
export const creationCsDiscard = (data) =>
  request.post(`${serviceUrl}/adminApi/airDrop/creationCs/discard`, data);
