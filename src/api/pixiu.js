import request from '@/utils/request.js'

const serviceUrl = '/pixiu'

/**
 *  支付回调
 */
export const getListNotify = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/listNotify`, data)

/**
 *  重新查询支付回调
 */
export const adminNotify = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/adminNotify`, data)

/**
 *  数字人民币确认
 */
export const rmbComplete = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/digital/rmb/complete`, data)

/**
 * 绑卡列表
 * POST pixiu/adminApi/pay/bankcard/list
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049649/apis/api-********
 * @param { String } data.bankCardNumber 银行卡号
 * @param { String } data.bankName 银行名称
 * @param { String } data.name 户名
 * @param { String } data.nickName 用户名
 */
export const bankcardList = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/bankcard/list`, data)

/**
 * 充值列表
 * POST /adminApi/pay/deposit/list
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049649/apis/api-********
 * @param { String } data.depositNo 订单号
 * @param { String } data.nickName 用户名
 * @param { Number } data.payMethod  付款方式:2微信,3支付宝,4云闪付,5银行卡,6赠送7五虎赠送 9苹
 * @param { Number } data.paymentScene  支付场景 1-H5 2-PC 3-IOS 4-Android
 * @param { Number } data.status 支付状态 1待支付 2已支付
 */
export const depositList = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/deposit/list`, data)
