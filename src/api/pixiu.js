import request from '@/utils/request.js'

const serviceUrl = '/pixiu'

/**
 *  支付回调
 */
export const getListNotify = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/listNotify`, data)

/**
 *  重新查询支付回调
 */
export const adminNotify = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/adminNotify`, data)

/**
 *  数字人民币确认
 */
export const rmbComplete = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/digital/rmb/complete`, data)

/**
 * 绑卡列表
 * POST pixiu/adminApi/pay/bankcard/list
 * 接口ID：********
 * 接口地址：https://www.apifox.cn/web/project/1049649/apis/api-********
 * @param { String } data.bankCardNumber 银行卡号
 * @param { String } data.bankName 银行名称
 * @param { String } data.name 户名
 * @param { String } data.nickName 用户名
 */
export const bankcardList = (data) =>
  request.post(`${serviceUrl}/adminApi/pay/bankcard/list`, data)

