import request from "@/utils/request.js";

const serviceUrl = "/pinkexchange";

/**
 * 获取我的返佣信息
 */
export const getMyRebate = (data) =>
  request.post(`${serviceUrl}/appApi/rebate/UserData/getRebateData`, data);

/**
 * 获取数据总览
 */
export const getDataOverview = (data) =>
  request.post(
    `${serviceUrl}/appApi/rebate/UserData/getRebateDataSummary`,
    data
  );

/**
 * 获取我的好友信息
 */
export const getMyFriends = (data) =>
  request.post(`${serviceUrl}/appApi/rebate/UserData/getInviteeData`, data);

/**
 * 获取邀请码
 */
export const getInviteCode = (data) =>
  request.post(`${serviceUrl}/appApi/rebate/UserData/getInviteCode`, data);

/**
 * 提交代理商申请
 */
export const submitAgentApply = (data) =>
  request.post(`${serviceUrl}/appApi/rebate/application/submitAgentApplication`, data);
