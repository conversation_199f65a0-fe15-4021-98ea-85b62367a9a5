42
import request from "@/utils/request.js";

const serviceUrl = "/pinkexchange";

/**
 *  用户密码登录
 */
export const login = (data) =>
  request.post(`${serviceUrl}/appApi/rebate/AgentLogin/rebateAgentLogin`, data);

/**
 * 币对查询
 */
export const getCoinSymbolConfigPaged = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinSymbolConfig/getCoinSymbolConfigPaged`,
    data
  );

/**
 * 币对添加/更新
 */
export const addOrUpdateCoinSymbolConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinSymbolConfig/addOrUpdateCoinSymbolConfig`,
    data
  );

/**
 * 分页查询关联币对配置明细
 */
export const getCoinSymbolConfigDetailPaged = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinSymbolConfig/searchSymbolRelatedDetailPaged`,
    data
  );

/**
 * 分页查询fireblocks支持asset
 */
export const getFireblocksAssetPaged = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/searchAssetsPaged`, data);

/**
 * 添加或更新关联币对明细
 */
export const addOrUpdateCoinSymbolConfigDetail = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinSymbolConfig/addOrUpdateSymbolRelatedDetail`,
    data
  );

/**
 * 操作交易记录
 */
export const operateTradeRecord = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/optionCoinTransaction`,
    data
  );

/**
 * 分页查询交易记录
 */
export const queryCoinTransactionPage = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/getTransactionPaged`,
    data
  );

/**
 * 交易明细
 */
export const queryCoinTransactionDetail = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/getCoinTransactionDetail`,
    data
  );

/**
 * 分页查询 vaultAccount
 */
export const queryVaultAccountPage = (data) =>
  request.post(
    `${serviceUrl}/adminApi/fireblocks/searchVaultAccountPaged`,
    data
  );

/**
 * 同步 vaultAccount
 */
export const syncVaultAccount = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncVaultAccount`, data);

/**
 * 同步所有 vaultAccount
 */
export const syncAllVaultAccount = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAllVaultAccount`, data);

/**
 * 变更 管理台显示状态
 */
export const changeAdminShowStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/changeHiddenOnUi`, data);

/**
 * 变更 自动补充gas费状态
 */
export const changeAutoGasFeeStatus = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/changeAutoFuel`, data);

/**
 * 分页查询钱包
 */
export const getWalletList = (data) =>
  request.post(
    `${serviceUrl}/adminApi/fireblocks/searchVaultWalletPaged`,
    data
  );

/**
 * 同步钱包
 */
export const syncWallet = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncVaultWallet`, data);

/**
 * 同步所有钱包
 */
export const syncAllWallet = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAllVaultWallet`, data);

/**
 * 查询所有支持的资产
 */
export const getAllAssets = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/searchAssetsPaged`, data);

/**
 * 同步所有支持的资产
 */
export const syncAllAssets = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAllAssets`, data);

/**
 * 分页查询地址
 */
export const getAddressPaged = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/searchAddressPaged`, data);

/**
 * 同步地址
 */
export const syncAddress = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAddress`, data);

/**
 * 批量同步地址
 */
export const syncAddressBatch = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAllAddress`, data);

/**
 * 分页查询交易
 */
export const getTransactionPaged = (data) =>
  request.post(
    `${serviceUrl}/adminApi/fireblocks/searchTransactionPaged`,
    data
  );

/**
 * 同步交易
 */
export const syncTransaction = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncTransaction`, data);

/**
 * 同步所有交易
 */
export const syncTransactionBatch = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/syncAllTransaction`, data);

/**
 * b2c2-闪兑订单查询
 */
export const getB2C2Order = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/order/searchOrder`, data);

/**
 * b2c2手续费查询
 */
export const getB2C2Fee = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/flashExchangeConfig/list`, data);

/**
 * b2c2-手续费编辑
 */
export const editB2C2Fee = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/flashExchangeConfig/edit`, data);

/**
 * b2c2-手续费新增
 */
export const addB2C2Fee = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/flashExchangeConfig/add`, data);

/**
 * b2c2-手续费删除
 */
export const delB2C2Fee = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/flashExchangeConfig/remove`, data);

//字典控制器
export const dictionarySearch = (data) =>
  request.post(`${serviceUrl}/adminApi/dictionary/search`, data);
//添加
export const dictionaryAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/dictionary/add`, data);
//更改
export const dictionaryUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/dictionary/update`, data);
/**
 * 删除后台权限
 */
export const removeAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/permission/delete`, data);

/**
 * 获取字典数据
 */
export const getDictDataByDictType = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/getDictDataByDictType`, data);

/**
 * banner列表
 */
export const getBannerList = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/list`, data);

/**
 * banner上下线
 */
export const onlineBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/updateOnlineStatus`, data);

/**
 * banner删除
 */
export const deleteBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/delete`, data);

/**
 * banner详情
 */
export const detailBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/detail`, data);

/**
 * banner新增
 */
export const addBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/add`, data);

/**
 * banner编辑
 */
export const updateBanner = (data) =>
  request.post(`${serviceUrl}/adminApi/banner/update`, data);

/**
 * 分页查询用户
 */
export const pageUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/searchUserPaged`, data);

/**
 * 查询用户余额
 */
export const getUserBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/user/searchUserBalancePaged`, data);

/**
 * 变更用户余额
 */
export const updateUserBalance = (data) =>
  request.post(`${serviceUrl}/adminApi/user/updateUserBalance`, data);

/**
 * 转账
 */
export const transfer = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/transfer`, data);

/**
 * 查询后台操作转账记录
 */
export const getTransferRecord = (data) =>
  request.post(
    `${serviceUrl}/adminApi/fireblocks/searchTransferAdminRecordPaged`,
    data
  );

/**
 * 归集金额
 */
export const collect = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/collectAmount`, data);

/**
 * 更新手续费配置
 */
export const updateFeeConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinWithdrawConfig/updateFeeConfig`,
    data
  );

/**
 * 更新审批配置
 */
export const updateApproveConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinWithdrawConfig/updateApproveConfig`,
    data
  );

/**
 * 查询提现配置
 */
export const getWithdrawConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coinWithdrawConfig/searchWithdrawConfigPaged`,
    data
  );

/**
 * 查询未归集的资产
 */
export const getUncollectAssets = (data) =>
  request.post(`${serviceUrl}/adminApi/fireblocks/unCollectStatistics`, data);

/**
 * 币对枚举
 */
export const getCoinPairEnum = (data) =>
  request.post(`${serviceUrl}/adminApi/coinSymbolConfig/symbolList`, data);

/**
 * 网络枚举
 */
export const getNetworkEnum = (data) =>
  request.post(`${serviceUrl}/adminApi/coinSymbolConfig/networkList`, data);

/**
 * 分页加密货币查询充值记录
 */
export const getCryptoRechargeRecord = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/getCryptoRechargePaged`,
    data
  );

/**
 * 对冲敞口
 */
export const getOppositePosition = (data) =>
  request.post(`${serviceUrl}/adminApi/b2c2/CoinParHedge/list`, data);

/**
 * 分页查询加密货币提现记录
 */
export const getCryptoWithdrawRecord = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/getCryptoWithdrawPaged`,
    data
  );

/**
 * 各平台资产统计
 */
export const getPlatformAssetsStatistics = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/flowStatisticsList`,
    data
  );

/**
 * 资金提现和调拨预警
 */
export const getFundsWarning = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/waitFlowStatisticsList`,
    data
  );

/**
 * 用户列表补充
 */
export const getUserListpink = (data) =>
  request.post(
    `${serviceUrl}/adminApi/coin/transaction/userFlowStatisticsList`,
    data
  );

/**
 * 查询-消息事件模板列表
 */
export const msgPushTemplateList = (data) =>
  request.post(`${serviceUrl}/adminApi/msgPushTemplate/list`, data);

/**
 * 修改-消息事件模板
 */
export const msgPushTemplateEdit = (data) =>
  request.post(`${serviceUrl}/adminApi/msgPushTemplate/update`, data);

/**
 * 删除-消息事件模板
 */
export const msgPushTemplatedel = (data) =>
  request.post(`${serviceUrl}/adminApi/msgPushTemplate/delete`, data);

/**
 * 新增-消息事件模板
 */
export const msgPushTemplateAdd = (data) =>
  request.post(`${serviceUrl}/adminApi/msgPushTemplate/add`, data);

/**
 * 后台-用户-移除权限
 */
export const permissionsRemove = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/user/remove`, data);

/**
 * 后台-用户-获取权限列表
 */
export const permissionsList = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/user/list`, data);

/**
 * 后台-用户-分配权限
 */
export const permissionsGive = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/user/assign`, data);

/**
 * 更新权限
 */
export const permissionsupdate = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/update`, data);

/**
 * 删除权限
 */
export const permissionsremoves = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/remove`, data);

/**
 * 获取权限列表
 */
export const permissionslists = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/list`, data);

/**
 * 创建权限
 */
export const permissionscreate = (data) =>
  request.post(`${serviceUrl}/adminApi/permissions/create`, data);

/**
 * 修改密码
 */
export const Editpwd = (data) =>
  request.post(`${serviceUrl}/adminApi/adminUser/changePass`, data);

/**
 * 买币查询
 */
export const getBuyCoin = (data) =>
  request.post(`${serviceUrl}/adminApi/quickBuy/list`, data);

/**
 * 买币导出
 */
export const getBuyCoinExport = (data) =>
  request.post(`${serviceUrl}/adminApi/quickBuy/export`, data, {
    responseType: "blob",
  });

/**
 * C2C商户申请查询列表
 */
export const getC2CApplyList = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/application/list`, data);

/**
 * C2C商户申请列表-导出
 */
export const getC2CApplyListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/application/export`, data, {
    responseType: "blob",
  });

/**
 * C2C商户申请列表-审核
 */
export const getC2CApplyListAudit = (data) =>
  request.post(
    `${serviceUrl}/adminApi/c2c/shop/application/examineRefund`,
    data
  );

/**
 * 查询商家审核列表
 */
export const getC2CApplyListAuditList = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/list`, data);

/**
 * 查询商家审核列表-冻结
 */
export const getC2CApplyListAuditListFreeze = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/freeze`, data);

/**
 * 商家审核列表-导出
 */
export const getC2CApplyListAuditListExport = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/export`, data, {
    responseType: "blob",
  });

/**
 * 审核-通过/不通过
 */
export const getC2CApplyListAuditListExamine = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/examine`, data);

/**
 * 查询商户委托单
 */
export const getC2CApplyListAuditListEntrust = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/order/list`, data);

/**
 * 商户委托单-导出
 */
export const getC2CApplyListAuditListEntrustExport = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/shop/order/export`, data, {
    responseType: "blob",
  });

/**
 * 查询用户c2c订单列表
 */
export const getC2CApplyListAuditListEntrustUser = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/user/order/list`, data);

/**
 * 强制放币/强制取消
 */
export const getC2CApplyListAuditListEntrustUserForce = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/user/order/forceOper`, data);

/**
 * 用户c2c订单列表-导出
 */
export const getC2CApplyListAuditListEntrustUserExport = (data) =>
  request.post(`${serviceUrl}/adminApi/c2c/user/order/export`, data, {
    responseType: "blob",
  });
  /**
   * 高级认证列表
   */
  export const getSeniorAuthList = (data) =>
  request.post(`${serviceUrl}/adminApi/senior/auth/list`, data);

  /**
   * 高级认证导出
   */
  export const getSeniorAuthExport = (data) =>
  request.post(`${serviceUrl}/adminApi/senior/auth/export`, data, {
    responseType: "blob",
  });

  /**
   * 初级认证列表
   */
  export const getPrimaryAuthList = (data) =>
  request.post(`${serviceUrl}/adminApi/primary/auth/list`, data);

  /**
   * 初级认证导出
   */
  export const getPrimaryAuthExport = (data) =>
  request.post(`${serviceUrl}/adminApi/primary/auth/export`, data, {
    responseType: "blob",
  });

  /**
   * 初级认证审核
   */
  export const getPrimaryAuthExamine = (data) =>
  request.post(`${serviceUrl}/adminApi/primary/auth/examine`, data);

