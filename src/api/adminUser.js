import request from '@/utils/request.js'

const serviceUrl = '/adminuser'
const userServiceUrl = '/appusercenter'


/**
 * 图表数据
 */
export const getAppDictDataByDictTypeCheck = (data) =>
  request.post(`${serviceUrl}/adminApi/sls/website/volumes/list`, data)

/**
 *  用户密码登录
 */
export const login = (data) =>
  request.post(`${serviceUrl}/adminApi/login/usernamePasswordLogin`, data)

/**
 * ROOT管理员注册后台管理用户
 */
export const resetAdminUserPassWord = (data) =>
  request.post(`${serviceUrl}/adminApi/user/resetAdminUserPassWord`, data)

/**
 * ROOT管理员注册后台管理用户
 */
export const registryAdminUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/registryAdminUser`, data)

/**
 * 修改用户基本信息
 */
export const updateAdminUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/updateAdminUserInfo`, data)

/**
 * 获取用户分页列表
 */
export const listPageAdminUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/listPageAdminUser`, data)

/**
 *
 * 离职关闭账号
 */
export const dismissClose = (data) =>
  request.post(`${serviceUrl}/adminApi/user/quitCloseAdminUser`, data)

/**
 * 修改用户密码
 */
export const updateUserPassWord = (data) =>
  request.post(`${serviceUrl}/adminApi/user/updateUserPassWord`, data)

/**
 * 查看用户个人信息
 */
export const showAdminUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/showAdminUserInfo`, data)

/**
 * 获取后台管理权限列表
 */
export const listPageAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/userPermission/listPageAdminPermission`, data)

/**
 * 创建后台权限
 */
export const createAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/userPermission/createAdminPermission`, data)

/**
 * 修改后台权限
 */
export const updateAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/userPermission/updateAdminPermission`, data)

/**
 * 删除后台权限
 */
export const removeAdminPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/userPermission/removeAdminPermission`, data)

/**
 * 用户查询权限
 */
export const showAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/user/showAdminUserPermission`, data)

/**
 * 添加用户权限
 */
export const addAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/user/addAdminUserPermission`, data)

/**
 * 删除用户权限
 */

export const removeAdminUserPermission = (data) =>
  request.post(`${serviceUrl}/adminApi/user/removeAdminUserPermission`, data)

/**
 * 基础配置列表
 */
export const getPageOptionsVo = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/getPageOptionsVo`, data)

/**
 * App基础配置列表
 */
export const getAllDTOList = (data) =>
  request.post(`${serviceUrl}/adminApi/appVersion/getAllDTOList`, data)

/**
 * App基础配置列表
 */
export const AppsaveOrUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/appVersion/saveOrUpdate`, data)

/**
 * 更新配置信息
 */
export const saveOrUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/saveOrUpdate`, data)

/**
 * 获取字典分页列表
 */
export const getDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/getPageDictTypesVo`, data)

/**
 * 保存字典 新增和修改：
 */
export const saveDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/saveDictType`, data)

/**
 * 删除字典
 */
export const deleteDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/deleteDictTyp`, data)

/**
 * 获取字典数据分页列表
 */
export const getDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/getPageDictDatasVo`, data)

/**
 * 保存字典
 */
export const saveDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/saveDictData`, data)

/**
 * 删除字典
 */
export const deleteDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/deleteDictData`, data)

/**
 * 获取字典数据
 */
export const getDictDataByDictType = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/getDictDataByDictType`, data)

/**
 * 主页图表数据
 */
export const getCharts = (data) =>
  request.post(`${serviceUrl}/adminApi/sls/website/volumes/list`, data)
/**
 * 后台登录日志列表
POST /adminApi/login/user/log/list
接口ID：38419780
接口地址：https://www.apifox.cn/web/project/1049629/apis/api-38419780
 */
export const getAdminLoginLogs = data =>
  request.post(`${serviceUrl}/adminApi/login/user/log/list`, data)
/**
 * 用户登录日志
 */
export const getNftcnLoginLogs = data =>
	request.post(`${userServiceUrl}/adminApi/userLogin/log/list`, data)

/**
 * 后台接口操作日志
 */
export const getRequestLog = data =>
	request.post(`${serviceUrl}/adminApi/log/list`, data)
	
/**
 * 获得当前伯德寿命是否展示
 */
export const getValueByName = data =>
	request.post(`${serviceUrl}/adminApi/commonconfig/getValueByName`, data)




	
	
	
	
