import request from '@/utils/request.js'

const serviceUrl = '/adminuser'
const userServiceUrl = '/appusercenter'


/**
 * 图表数据
 */
export const getAppDictDataByDictTypeCheck = (data) =>
  request.post(`${serviceUrl}/adminApi/sls/website/volumes/list`, data)

/**
 * ROOT管理员注册后台管理用户
 */
export const resetAdminUserPassWord = (data) =>
  request.post(`${serviceUrl}/adminApi/user/resetAdminUserPassWord`, data)

/**
 * ROOT管理员注册后台管理用户
 */
export const registryAdminUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/registryAdminUser`, data)

/**
 * 修改用户基本信息
 */
export const updateAdminUserInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/user/updateAdminUserInfo`, data)

/**
 * 获取用户分页列表
 */
export const listPageAdminUser = (data) =>
  request.post(`${serviceUrl}/adminApi/user/listPageAdminUser`, data)

/**
 *
 * 离职关闭账号
 */
export const dismissClose = (data) =>
  request.post(`${serviceUrl}/adminApi/user/quitCloseAdminUser`, data)



/**
 * 基础配置列表
 */
export const getPageOptionsVo = (data) =>
  request.post(`${serviceUrl}/adminApi/commonconfig/getPageOptionsVo`, data)

/**
 * App基础配置列表
 */
// export const getAllDTOList = (data) =>
//   request.post(`${serviceUrl}/adminApi/appVersion/getAllDTOList`, data)

/**
 * App基础配置列表
 */
// export const AppsaveOrUpdate = (data) =>
//   request.post(`${serviceUrl}/adminApi/appVersion/saveOrUpdate`, data)


/**
 * 获取字典分页列表
 */
export const getDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/getPageDictTypesVo`, data)

/**
 * 保存字典 新增和修改：
 */
export const saveDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/saveDictType`, data)

/**
 * 删除字典
 */
export const deleteDict = (data) =>
  request.post(`${serviceUrl}/adminApi/dictType/deleteDictTyp`, data)

/**
 * 获取字典数据分页列表
 */
export const getDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/getPageDictDatasVo`, data)

/**
 * 保存字典
 */
export const saveDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/saveDictData`, data)

/**
 * 删除字典
 */
export const deleteDictData = (data) =>
  request.post(`${serviceUrl}/adminApi/dictData/deleteDictData`, data)

/**
 * 获取字典数据
//  */
// export const getDictDataByDictType = (data) =>
//   request.post(`${serviceUrl}/adminApi/dictData/getDictDataByDictType`, data)

/**
 * 主页图表数据
 */
export const getCharts = (data) =>
  request.post(`${serviceUrl}/adminApi/sls/website/volumes/list`, data)
/**
 * 获得当前伯德寿命是否展示
 */
export const getValueByName = data =>
	request.post(`${serviceUrl}/adminApi/commonconfig/getValueByName`, data)








