import request from "@/utils/request.js";

const serviceUrl = "/np-collection";


/**
 * 富豪榜
 */
export const richs = (data) =>
  request.post(`${serviceUrl}/adminApi/ranking/add`, data);

// /**
//  * 团队锁仓
//  */
// export const locklist = (data) =>
//   request.post(`${serviceUrl}/adminApi/groupLockGoods/list`, data);

// /**
//  * 团队锁仓增加
//  */
// export const lockadd = (data) =>
//   request.post(`${serviceUrl}/adminApi/groupLockGoods/lock`, data);

// /**
//  * 团队锁仓解锁
//  */
// export const unlock = (data) =>
//   request.post(`${serviceUrl}/adminApi/groupLockGoods/unLock`, data);
