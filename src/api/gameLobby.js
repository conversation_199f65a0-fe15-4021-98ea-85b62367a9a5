import request from '@/utils/request.js'

const serviceUrl = '/gamelobby'

/**
 * 游戏--游戏列表
 */
export const getGameList = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/getGameList`, data)

/**
 * 游戏--添加游戏
 */
export const addGame = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/addGame`, data)

/**
 * 游戏--修改游戏
 */
export const updateGame = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/updateGame`, data)

/**
 *  游戏--游戏时长列表
 */
export const getPlanConfigList = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/getPlanConfigList`, data)

/**
 * 游戏--游戏时长添加
 */
export const addPlanConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/addPlanConfig`, data)

/**
 * 游戏--游戏时长更改
 */
export const updatePlanConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/updatePlanConfig`, data)

/**
 * 游戏--id管理 列表
 */
export const getGoodsPatternList = (data) =>
  request.post(`${serviceUrl}/adminApi/plan/getGoodsPatternList`, data)

/**
 * 游戏-伯德列表查询
 */
export const getGoodsUserList = (data) =>
  request.post(`${serviceUrl}/adminApi/lingjing/user/list`, data)


/**
 * 游戏-初始化伯德寿命
 */
export const bodeLifeInit = (data) =>
  request.post(`${serviceUrl}/adminApi/lingjing/user/bodeLifeInit`, data)


/**
 * 游戏-归零伯德寿命
 */
export const bodeLifeToZero = (data) =>
	request.post(`${serviceUrl}/adminApi/lingjing/user/bodeLifeToZero`, data)


/**
 * 游戏-批量调整寿命
 */
export const batchUpdateEndTime = (data) =>
	request.post(`${serviceUrl}/adminApi/lingjing/user/batchUpdateEndTime`, data)

/**
 * 获取税收数据
 */
export const newPoolInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/niu/config/newPool/info`, data)

/**
 * 税收保存
 */
export const newPoolUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/niu/config/newPool/update`, data)
/**
 * 过渡修改
 */
export const mediumPoolUpdate = (data) =>
  request.post(`${serviceUrl}/adminApi/niu/config/mediumPool/update`, data)

/**
 * 过渡获取
 */
export const mediumPoolInfo = (data) =>
  request.post(`${serviceUrl}/adminApi/niu/config/mediumPool/info`, data)

export const normalPoolInfo = (data) =>
request.post(`${serviceUrl}/adminApi/niu/config/normalPool/info`, data)

export const normalPoolUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/config/normalPool/update`, data)

//动物通用道具查询
export const toolWeightInfo = (data) =>
request.post(`${serviceUrl}/adminApi/niu/config/toolWeight/info`, data)

//动物通用道具修改
export const toolWeightUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/config/toolWeight/update`, data)

//动物掉落列表
export const animalList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/list`, data)

//动物掉落新增
export const animalAdd = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/add`, data)

//动物掉落修改
export const animalUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/update`, data)


//动物掉落批量删除
export const animalBatchDelete = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/batchDelete`, data)

//动物出场列表
export const animalEnterList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/enter/list`, data)

//动物出场新增
export const animalEnterAdd = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/enter/add`, data)

//动物出场修改
export const animalEnterUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/enter/update`, data)

//动物出场批量删除
export const animalEnterBatchDelete = (data) =>
request.post(`${serviceUrl}/adminApi/niu/animal/enter/batchDelete`, data)

//充值buff列表
export const depositBuffList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/deposit/buff/list`, data)

//充值buff增加
export const depositBuffAdd = (data) =>
request.post(`${serviceUrl}/adminApi/niu/deposit/buff/add`, data)

//充值buff修改
export const depositBuffUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/deposit/buff/update`, data)

//充值buff删除
export const depositBuffBatchDelete = (data) =>
request.post(`${serviceUrl}/adminApi/niu/deposit/buff/batchDelete`, data)

//公告列表
// export const noticeList = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/notice/list`, data)

// //公告删除
// export const noticeBatchDelete = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/notice/batchDelete`, data)

// //公告添加
// export const noticeAdd = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/notice/add`, data)

// //公告修改
// export const noticeUpdate = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/notice/update`, data)

// //牛牛配置列表
// export const configList = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/config/list`, data)

// //牛牛配置修改
// export const configUpdate = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/config/update`, data)

// //牛牛配置添加
// export const configAdd = (data) =>
// request.post(`${serviceUrl}/adminApi/niu/config/add`, data)

//邮件模板列表
export const emailTemplateList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/template/list`, data)

//邮件模板添加
export const emailTemplateAdd = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/template/add`, data)

//邮件模板修改
export const emailTemplateUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/template/update`, data)

//邮件模板删除
export const emailTemplateBatchDelete = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/template/batchDelete`, data)

//邮件列表
export const emailList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/list`, data)

//邮件删除
export const emailBatchDelete = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/batchDelete`, data)

//邮件新增
export const emailAdd = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/add`, data)

//邮件修改
export const emailUpdate = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/update`, data)

//邮件明细列表
export const emailUserList = (data) =>
request.post(`${serviceUrl}/adminApi/niu/email/user/list`, data)



















