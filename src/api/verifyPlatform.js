import request from '@/utils/request.js'

const serviceUrl = '/verifyplatform'

/**
 * 获取审核任务
 */
export const listPageVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/verify/listPageVerify`, data)

/**
 * 批量审核
 */
export const peopleVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/verify/peopleVerify`, data)

/**
 *  删除审核作品
 */
export const deleteVerifyTask = (data) =>
  request.post(`${serviceUrl}/adminApi/verify/deleteVerifyTask`, data)

/**
 * 获取系列审核任务
 */
export const listPageSeriesVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/seriesVerify/listPageSeriesVerify`, data)

/**
 * 批量系列审核
 */
export const seriesPeopleVerify = (data) =>
  request.post(`${serviceUrl}/adminApi/seriesVerify/seriesPeopleVerify`, data)

/**
 * 树藏商品上下架
 */
export const digitalCollectionGoodsAudit = (data) =>
  request.post(`${serviceUrl}/adminApi/zsbGoodsVerify/goodsPeopleVerify`, data)
