/**
 * 将业务所有接口统一起来便于维护
 * requestPath:请求接口路径，
 * data：请求参数对象
 */
import http from "./interface";

export default {
  //路由请求地址
  requestPath: {
    register: "v1/user/register", //拉新人活动
    certification: "appusercenter/appApi/latest/user/create/auth",
    address: "appusercenter/appApi/user/consignee/list",
    addAddress: "appusercenter/appApi/user/consignee/create",
    editAddress: "appusercenter/appApi/user/consignee/edit",
    delAddress: "appusercenter/appApi/user/consignee/delete",
    visibility: "mallcenter/appApi/goods/onSale",
    unSale: "mallcenter/appApi/goods/unSale",
    goodsDestroy: "hanxin/appApi/order/destroy",
    invitelist: "appusercenter/appApi/user/inviteList",
    fanssearch: "mallcenter/appApi/goods/create/fans/list",
    bank_list: "pixiu/appApi/bankCard/V2/bind/list", //银行卡列表
    bank_bind_pre: "pixiu/appApi/bankCard/V2/bind", //预绑卡
    bank_bind_confirm: "pixiu/appApi/bankCard/V2/bind/confirm", //确定绑卡
    bank_unbind: "pixiu/appApi/bankCard/V2/unbind", //解绑银行卡
    bankList: "pixiu/appApi/bankCard/support/bank/list", //支持银行列表
    bank_card_info: "pixiu/appApi/bankCard/info", //卡号查询银行
    // JAVA接口合集
    java_mallHome: "mallcenter/appApi/mall/mallHome", //java商城
    java_marketTab: "waliangge/appApi/market/newTab", //java市场页面数据
    java_mallRank: "mallcenter/appApi/rank/mallRank", //排行榜
    java_popularRank: "waliangge/appApi/rinking/popular", // 榜单-热度榜
    java_gainRank: "waliangge/appApi/rinking/gain", // 榜单-热度榜
    java_fansRank: "waliangge/appApi/rinking/fans", // 榜单-粉丝榜
    java_liveRank: "waliangge/appApi/rinking/live", // 榜单-实时榜
    java_expandRank: "waliangge/appApi/rinking/expand", // 榜单-查看更多
    java_pointRank: "waliangge/appApi/rinking/point", // 榜单弹窗
    java_recommendGoods: "mallcenter/appApi/mall/recommendGoods", //商城推荐
    java_recommend: "mallcenter/appApi/goodsDetails/recommend", //详情页推荐
    // java_search_keywords: "mallcenter/appApi/index/search/keywords", //搜索热词
    // java_search_Hotkeywords: "waliangge/appApi/kingKong/hotSelect/searchKeywords", // 吉物仓热词列表
    java_hotKeywords: "adminuser/appApi/dictData/getAppSearchHotWords", //搜索热词
    java_search_list: "wanhuatong/appApi/search/composition/list", //搜索结果
    java_goodsDetails: "mallcenter/appApi/goodsDetails/goodsDetails", //作品详情页,
    java_allCritic: "mallcenter/appApi/goodsDetails/allCritic", // 作品详情-全部评论
    java_community: "community/appApi/community/commentV2", //作品详情-评论
    java_commentLike: "community/appApi/community/commentLike", //作品详情-评论点赞
    java_allTransaction: "mallcenter/appApi/goodsDetails/allTransaction", //历史交易价格
    // java_bounceTips: "mallcenter/appApi/hotSelected/bounceTips", //精选ip弹框提示语
    // java_selected_user_list: "mallcenter/appApi/hotSelected/user/list", //热门精选ip数据
    // java_selected_list: "mallcenter/appApi/hotSelected/goods/search/list", //精选ip弹框提示语
    java_bounceTips: "waliangge/appApi/kingKong/bounceTips", //新 精选ip弹框提示语
    java_selected_user_list: "waliangge/appApi/kingKong/hotSelected/user/list", //新 热门精选ip数据
    java_selected_list:
      "waliangge/appApi/kingKong/hotSelected/goods/search/list", // 新 精选ip列表
    java_release_information: "mallcenter/appApi/goods/create/pre/item", //铸造页前置信息
    java_screenLabelGoods: "mallcenter/appApi/mall/screenLabelGoods", //虚拟人 | 版权
    java_release: "mallcenter/appApi/goods/create/item", //铸造
    java_getAppConfigList:
      "adminuser/appApi/commonconfig/getProviderAdnCreateConfigList", //查看个人中心二维码
    java_searchList: "appusercenter/appApi/user/search/list", //搜索用户
    java_follow_list: "mallcenter/appApi/index/follow/list", //首页-关注
    java_discovery_list: "wanhuatong/appApi/explore/composition/list", //探索--作品
    java_discovery_seriesList: "wanhuatong/appApi/explore/appSeries/list", //探索--系列
    java_searchSeriesList: "wanhuatong/appApi/search/appSeries/list", //搜索-系列
    java_creatorsSeriesList: "mallcenter/appApi/series/creatorsSeriesList", //个人中心-创作
    java_creatorsMyCollection: "mallcenter/appApi/series/creatorsMyCollection", //个人中心-藏品
    java_seriesDetails: "mallcenter/appApi/series/seriesDetailsV2", //商场-普通系列
    java_blindSeriesDetail: "mallcenter/appApi/series/blindSeriesDetail", //商场-盲盒系列
    java_blindGoodslist: "mallcenter/appApi/series/v2/seriesDetails/goodsList", //商场-普通系列作品列表
    java_blindSeriesGoodsList:
      "mallcenter/appApi/series/v2/blindSeriesGoodsList", //盲盒系列详情查询查询
    java_createSeriesList: "mallcenter/appApi/goods/create/user/info", ////铸造查询系列列表
    java_3dSearch: "waliangge/appApi/kingKong/firstPageTypeGoods", //3d专区
    java_tabmarketSpecialGoodsList:
      "waliangge/appApi/market/marketSpecialGoodsList", //java市场专题
    java_communityLike: "community/appApi/community/like", ////java点赞
    java_communityAttention: "community/appApi/community/attention", //java关注用户
    java_followerList:
      "community/appApi/community/social/user/goods/follower/list", //java用户关注作品列表
    java_communityFollowerList:
      "community/appApi/community/social/user/follower/list", //java用户关注列表
    java_fansList: "community/appApi/community/social/user/fans/list", //java用户粉丝列表
    java_payRouteConfig: "pixiu/appApi/payRouteConfig/routeGet", //java支付配置
    java_securityInfo: "hanxin/appApi/securityDeposit/detail", // 保证金-信息查询
    java_securityDetail: "hanxin/appApi/securityDeposit/order/detail", // 保证金-变更记录详情
    java_securityList: "hanxin/appApi/securityDeposit/order/list", // 保证金-变更记录列表
    java_securityCreate: "hanxin/appApi/securityDeposit/refund/create", // 保证金-退保
    java_securityCancel: "hanxin/appApi/securityDeposit/refund/cancel", // 保证金-取消退保
    java_securityCheck: "hanxin/adminApi/securityDeposit/refund/check", // 保证金-退款审核
    java_securityOrder: "hanxin/appApi/securityDeposit/order/create", // 保证金-创建订单
    java_securityFreeze: "hanxin/adminApi/securityDeposit/record/freeze", // 保证金-冻结
    java_community_collect: "community/appApi/community/collect", //java收藏作品
    java_creationGoodsSearchList:
      "mallcenter/appApi/v2/creation/goodsSearch/list", //java 个人中心创作作品查询+搜索
    java_collectionGoodsSearchList:
      "mallcenter/appApi/collection/goodsSearch/list", //java 个人中心藏品作品查询+搜索
    java_seriesSearchMyCreation: "mallcenter/appApi/series/searchMyCreation", //java 个人中心系列搜索
    java_userEditAvatar: "appusercenter/appApi/user/editAvatar", //java 个人中心修改头像
    java_userMsgUnreadNumber: "hongyan/appApi/userMsg/unreadNumber", //java 获取是否存在未读消息以及未读消息数量
    java_msgBoxList: "hongyan/appApi/userMsg/msgBoxList", //java 消息盒子信息
    java_unreadNumber: "hongyan/appApi/userMsg/unreadNumber", //java 消息中心 --获取是否存在未读消息以及未读消息数量：
    java_readBoxMsg: "hongyan/appApi/userMsg/readBoxMsg", //java 消息中心 --用户打开盒子，盒子所有消息变为已读：
    java_listPageUserBoxMsg: "hongyan/appApi/userMsg/listPageUserBoxMsg", //java 消息中心 --获取消息盒子下的消息：
    java_userMsg: "hongyan/appApi/userMsg/getMsgInfoCustomLink", //java 消息中心 --获取消息富文本
    java_userPreview: "community/appApi/community/preview/user/preview", //java 隐私设置 --获取用户隐私设置
    java_userCreatePreview: "community/appApi/community/preview/user/create", //java 隐私设置 --用户创造列表权限
    java_userCollectionsPreview:
      "community/appApi/community/preview/user/collections", //java 隐私设置 --用户收藏列表权限
    java_userFavoritePreview:
      "community/appApi/community/preview/user/favorite", //java 隐私设置 --用户关注列表权限
    java_goodsFavoritePreview:
      "community/appApi/community/preview/user/goods/favorite", //java 隐私设置 --用户关注作品列表权限
    java_userFansPreview: "community/appApi/community/preview/user/fans", //java 隐私设置 --用户粉丝列表权限
    java_domainNamePreview:
      "community/appApi/community/preview/user/domainName", //java 隐私设置 --用户粉丝列表权限
    java_verifyInfo: "appusercenter/appApi/user/cancel/account/verifyInfo", //java 账户注销 --基本信息校验接口
    java_execute: "appusercenter/appApi/user/cancel/account/execute_new", //java 账户注销 --账户注销
    java_detail: "appusercenter/appApi/user/cancel/account/record/detail", //java 账户注销 --账户注销进度
    java_stop: "appusercenter/appApi/user/cancel/account/stop", //java 账户注销 --终止注销
    java_verifyCancelAccount: "appusercenter/appApi/verifyCancelAccount", //java 账户注销 --校验是否注销
    java_sendAliYunSms: "appusercenter/appApi/sendAliYunSms", //java 登录 --发送手机验证码
    java_sendAliYunEmail: "appusercenter/appApi/sendAliYunEmail", //java 登录 --发送邮箱验证码
    java_gas_item_list: "hanxin/appApi/order/gas/item/list", //java 燃料展示 -
    java_order_create_gas: "hanxin/appApi/order/create/gas", //java 燃料订单创建 -
    java_order_check: "hanxin/appApi/order/check", //作品下单校验
    java_order_pay_call: "hanxin/appApi/order/pay/call", //java 调起支付
    java_create_item: "hanxin/appApi/order/create/item", //java 创建作品订单
    java_pay_confirm: "hanxin/appApi/order/pay/confirm", //java 银行卡支付-验证码确认
    java_order_list: "hanxin/appApi/order/list", //java 订单列表
    java_order_detail: "hanxin/appApi/order/detail", //java 订单详情
    java_order_cancel: "hanxin/appApi/order/cancel", //订单取消
    java_order_gift: "hanxin/appApi/order/gift", //java作品转赠
    java_order_destory: "hanxin/appApi/order/destory", //java销毁作品
    java_deposit_create: "hanxin/appApi/deposit/create", //java充值
    java_authInfo: "appusercenter/appApi/user/auth/info", //java充值

    java_balance_info: "jiuzhang/appApi/account/balance/info", //java 我的钱包页-信息展示
    java_balance_log: "jiuzhang/appApi/account/balance/log", //java 我的钱包页-信息展示
    java_withdraw_info: "jiuzhang/appApi/account/withdraw/info", //java 提现页-信息展示
    java_withdraw_create: "jiuzhang/appApi/account/withdraw/create", //java 提现页- 发起提现
    java_exemption_info: "hanxin/appApi/securityDeposit/order/free/detail", //java 我的钱包页-免佣信息展示

    java_recentTab: "waliangge/appApi/market/recentTab", //新版 市场页面数据获取
    java_customerLinkDetail: "waliangge/appApi/market/customerLinkDetail", //新版 市场页面富文本详情

    java_emailPasswordLogin: "appusercenter/appApi/appUserEmailPasswordLogin", //java 邮箱密码登录
    java_phonePasswordLogin: "appusercenter/appApi/appUserPhonePasswordLogin", //java 手机号密码登录
    java_usernamePasswordLogin: "appusercenter/appApi/appUserNamePasswordLogin", //java 用户名密码登录
    java_emailVerifyCodeLogin:
      "appusercenter/appApi/latest/appUserEmailVerifyCodeLogin", //java 邮箱验证码登录
    java_phoneVerifyCodeLogin:
      "appusercenter/appApi/latest/appUserPhoneVerifyCodeLogin", //java 手机号验证码登录

    java_verifyAliYunEmailCode:
      "appusercenter/appApi/latest/verifyAliYunEmailCode", //java 校验邮箱验证码
    java_verifyAliYunPhoneCode:
      "appusercenter/appApi/latest/verifyAliYunPhoneCode", //java 校验手机验证码

    java_appUserEmailRegister: "appusercenter/appApi/appUserEmailRegister", //java 注册
    java_appPhoneregister: "appusercenter/appApi/latest/appPhone/register", // 手机验证码注册
    java_emailRegister: "appusercenter/appApi/latest/email/register", // 邮箱验证码登录注册
    java_appUserForgetPasswordVerifyPhoneSmsCode:
      "appusercenter/appApi/appUserForgetPasswordVerifyPhoneSmsCode", //java 手机号找回密码
    java_appUserForgetPasswordVerifyEmailCode:
      "appusercenter/appApi/appUserForgetPasswordVerifyEmailCode", //java 邮箱找回密码
    appUserLoginReSetPassword: "appusercenter/appApi/appUserLoginReSetPassword", //java 修改用户密码
    java_appUserEditDesc: "appusercenter/appApi/user/editDesc", //java 修改用户简介
    java_appUserEditNickName: "appusercenter/appApi/user/editNickName", //java 修改用户昵称

    java_editBackground: "appusercenter/appApi/user/editBackground", //java 修改用户背景图
    java_digitalRMBPay: "hanxin/appApi/order/pay/digital/rmb/confirm",
    java_oneClickLoginGetAuthToken:
      "appusercenter/appApi/oneClickLogin/getAuthToken", //java 一键登录获取前置信息
    java_oneClickLoginRegister: "appusercenter/appApi/oneClickLogin/register", //java 一键登录
    java_oneClickLoginPhoneRegister:
      "appusercenter/appApi/oneClickLogin/phoneRegister", //java 一键登录手机号密码注册
    java_openLogin:
      "tiangong/openApi/oauth2/clientUserPhoneOrEmailPasswordLogin", // 开放平台登录

    java_tradePassCreate: "appusercenter/appApi/trade/createPassWord", //java设置支付密码
    java_tradePassEdit: "appusercenter/appApi/trade/editPassWord", //java修改支付密码
    java_tradePassReset: "appusercenter/appApi/trade/resetPassWord", //重置支付密码
    java_purchaseGuide: "mallcenter/appApi/goodsDetails/purchaseGuide", // 详情页弹窗引导购买作品

    // app 外站控制器
    java_websiteList: "appusercenter/appApi/external/website/list", // 外站列表
    java_websiteUpdate: "appusercenter/appApi/external/website/update", // 编辑用户外站
    java_userWebsite: "appusercenter/appApi/external/website/user/list", // 用户已有外站列表

    java_shortLink: "shortlink/appApi/shortlink/longLinkToShortLink", // java 长链接转短链接

    java_bigverseList: "waliangge/appApi/bigverse/list",
    calculatePrice: "hanxin/appApi/order/calculate/price",
    java_marketBrandUserList: "waliangge/appApi/market/marketBrandUserList", //获取市场品牌用户数据
    java_setExpectPrice: "mallcenter/appApi/goods/setExpectPrice", //设置预期售出价格
    java_setExpectedBuyPrice: "community/appApi/community/setExpectedBuyPrice", //设置预期购买价格
    java_complainItem: "appusercenter/appApi/goods/complain/item", //举报作品
    java_kingKongThemeGoods: "waliangge/appApi/kingKong/themeGoods", //java市场专题 新
    java_blindPullOff: "mallcenter/appApi/series/blind/pullOff", //java停售盲盒系列接口
    java_blindputOn: "mallcenter/appApi/series/blind/putOn", //java开启盲盒系列接口
    java_phoneBind: "appusercenter/appApi/user/check/phoneBind", //java校验手机号
    java_planConfigList: "mallcenter/appApi/leap/plan/configList", //java飞跃计划
    java_createLeap: "hanxin/appApi/order/create/leap", //飞跃计划开通
    java_officialArticleList: "hongyan/appApi/article/list", // 官方公告列表
    java_officialArticleDetail: "hongyan/appApi/article/detail", // 官方公告详情
    java_officialThumpUp: "hongyan/appApi/article/thumpUp", // 官方公告点赞
    // java_officialRead: "hongyan/appApi/article/scanCount", // 官方公告阅读
    java_searchAll: "wanhuatong/appApi/search/all", //APP首页搜索-三合一搜索
    java_buySuccessInfo: "appusercenter/appApi/leapPlan/buySuccessInfo", //飞跃计划成功页面
    java_leapGasAccountInfo: "jiuzhang/appApi/account/gas/info", //燃料卡-信息展示
    java_marketRecommendList: "waliangge/appApi/market/marketRecommendList", //获取市场页推荐ip列表
    java_marketRecommendGoodsList:
      "waliangge/appApi/market/marketRecommendGoodsList",
    java_pgc_tab3: "waliangge/appApi/market/pgc/tab3", //pgc  tab3
    java_pgc_tab1: "waliangge/appApi/market/pgc/tab1", //pgc  tab3
    java_pgc_tab1_recommendList:
      "waliangge/appApi/market/pgc/tab1/recommendList",
    java_marketRecommendTabInfo:
      "waliangge/appApi/market/marketRecommendTabInfo",
    java_targetCreate: "hanxin/appApi/order/target/create",
    java_targetCancel: "hanxin/appApi/order/target/cancel",
    java_targetDutyList: "hanxin/appApi/order/target/dutyList",
    java_targetDutyOperateList: "hanxin/appApi/order/target/dutyOperateList",
    java_completeFaceAuth: "appusercenter/appApi/user/completeFaceAuth",
    java_exchangeitemList: "hanxin/appApi/wallet/exchange/itemList",
    java_exchangeExchange: "hanxin/appApi/wallet/exchange/exchange",
    java_walletInfo: "jiuzhang/appApi/wallet/info",
    java_commonconfigInfo: "adminuser/appApi/commonconfig/info", //游戏中悬浮窗
    java_domainNamelist: "wanhuatong/appApi/explore/domainName/list", //数字身份列表
    java_mallcenter_domainNamelist:
      "mallcenter/appApi/collection/domainName/list", //个人中心数字身份
    java_search_domainNamelist: "wanhuatong/appApi/search/domainName/list", //搜索数字身份
    java_createDomain: "hanxin/appApi/order/create/domain", //域名注册并支付
    java_domainRenewList: "hanxin/appApi/order/domainRenew/list", //续费列表
    java_createdomainRenew: "hanxin/appApi/order/create/domainRenew", //
    java_generator: "osscenter/appApi/generator", //图片生成
    java_domainGenerateInfo: "hanxin/appApi/order/domain/generateInfo", //数字身份注册info

    java_appPhoneRegister: "appusercenter/appApi/community/appPhone/register", //社区新人注册
    java_getMetaApplication: "mallcenter/appApi/meta/getMetaApplication",
    java_setMetaApplication: "mallcenter/appApi/meta/setMetaApplication",
    java_getSeriesBuyRecord: "hanxin/appApi/order/getSeriesBuyRecord",
    java_batchOnSaleCheck: "mallcenter/appApi/series/operate/onSaleCheck", //校验系列是否可以
    java_operateOnSale: "mallcenter/appApi/series/operate/onSale", //批量寄售
    java_userInfoV2: "appusercenter/appApi/user/infoV2",
    java_orderListCount: "hanxin/appApi/order/listCount",
    java_jwcList: "waliangge/appApi/market/jwcList",
    pgcSearch: "waliangge/appApi/market/pgc/search",
    baseInfo: "appusercenter/appApi/user/baseInfo", //基础信息
    shareShow: "waliangge/appApi/invite/share/show", //获得用户昵称
    inviteIsInvited: "waliangge/appApi/invite/isInvited", //查询用户是否被成功邀请, 被邀请者注册完成之后调用
    inviteValidate: "waliangge/appApi/invite/validate", //检验用户邀请资格
    java_recommendTabList: "waliangge/appApi/market/pgc/tab1/recommendTabList",
    java_targetDutyOperateListV2:
      "hanxin/appApi/order/target/dutyOperateListV2",
    java_activityNewActivityPageList:
      "waliangge/appApi/activity/activityNewActivityPageList", //获取金刚区活动new下的活动
    java_myStudentList: "waliangge/appApi/teacher/myStudentList", //我的徒弟
    java_myTeacher: "waliangge/appApi/teacher/myTeacherList", //我的师父
    java_teacherIncomeList: "waliangge/appApi/teacher/teacherIncomeList", //收益分成明细
    java_bindTeacherShow: "waliangge/appApi/teacher/bindTeacherShow", //绑定师徒关系页展示
    java_bindTeacher: "waliangge/appApi/teacher/bindTeacher", //绑定师徒关系
    java_unbindTeacher: "waliangge/appApi/teacher/unbindTeacher", //解除绑定
    java_myGoodsAmount: "mallcenter/appApi/series/myGoodsAmount", //宝藏估价列表
    java_myGoodsAmountTotal: "mallcenter/appApi/series/myGoodsAmountTotal", //宝藏估价总计
    java_teacherInfo: "waliangge/appApi/teacher/info", //绑定师徒关系
    java_teacherRanking: "waliangge/appApi/teacher/teacherRanking", //师门排行榜
    java_rankingList: "waliangge/appApi/ranking/list", //富豪榜
    java_rankingBuyList: "waliangge/appApi/ranking/buyList", //买入清单
    java_handleCreate: "hanxin/appApi/deposit/handleCreate", //大额充值
    java_receive: "hanxin/appApi/order/receive", //新贵区邀请码领取作品接口
    java_isNewUser: "queqiao/appApi/im/isNewUser", //市场页下方是否显示 呼叫客服/加入社群icon接口
    java_isRegister: "waliangge/appApi/visa/isRegister", //查询visaUid是否注册
    java_bindReceive: "waliangge/appApi/visa/bindReceive", //绑定visaUid并且领取作品
    java_validateTradeAmount: "waliangge/appApi/activity/validateTradeAmount", //
    java_exchange: "waliangge/appApi/ticket/change/exchange", //兑换祈福贴
    java_giveTicket: "waliangge/appApi/ticket/change/giveTicket", //赠与祈福贴
    java_getTicketCount: "waliangge/appApi/ticket/change/getTicketCount", //获得祈福贴
    java_oldSeriesGoodsList: "mallcenter/appApi/series/oldSeriesGoodsList", //怀旧区
    java_oldSeriesGoodsDestroy:
      "mallcenter/appApi/series/oldSeriesGoodsDestroy", //一键销毁
    java_setShow: "waliangge/appApi/ranking/buyList/setShow", //设置富豪榜隐私
    java_hotBroadcast: "waliangge/appApi/bigverse/homePage/hotBroadcast", //bigverse首页热播榜
    java_increaseList: "waliangge/appApi/bigverse/homePage/increaseList", //bigverse首页涨幅榜
    java_selfSelectionList:
      "waliangge/appApi/bigverse/homePage/selfSelectionList", //bigverse首页自选列表
    java_operationSelfSelection:
      "waliangge/appApi/bigverse/homePage/operationSelfSelection", //bigverse首页操作自选-添加/移除/编辑位置
    java_moreInquiriesList:
      "waliangge/appApi/bigverse/homePage/moreInquiriesList", //bigverse资讯列表
    getUserHoldSeriesList:
      "mallcenter/appApi/myCollection/getUserHoldSeriesList", //bigverse资讯列表
    getAppUserHoldSeriesCountAndAvgPriceVO:
      "mallcenter/appApi/myCollection/getAppUserHoldSeriesCountAndAvgPriceVO", //bigverse资讯列表
    userSeriesCollectionList:
      "mallcenter/appApi/myCollection/userSeriesCollectionList", //bigverse资讯列表
    soldList: "hanxin/appApi/order/soldList", //bigverse资讯列表
    java_invitedFriend: "waliangge/appApi/invitedFriend/info", //邀请好友基本信息
    java_friendsList: "waliangge/appApi/invitedFriend/friendsList", //邀请好友基本信息
    java_getBalanceNum: "minigame/appApi/game/balloon/getBalanceNum", //我的B宝数量
    java_wechatLogin: "appusercenter/appApi/wechat/login", //微信一键登录
    java_wechatbind: "appusercenter/appApi/wechat/bind", //微信绑定
    java_loginBind: "appusercenter/appApi/wechat/loginBind", //微信登录但未绑定手机号 进行绑定
    java_longLinkToShortLink: "shortlink/appApi/shortlink/longLinkToShortLink", //生成短链
    batchUnSaleCollectionList:
      "mallcenter/appApi/myCollection/batchUnSaleCollectionList", //批量下架
    assetInfo: "appusercenter/appApi/user/assetInfo", //个人中心用户资产
    getAllDTOList: "adminuser/appApi/appVersion/appGetAppVersion", //强制版本号
    incomeList: "waliangge/appApi/ranking/incomeList", //买手榜列表
    my_bidding: "hanxin/appApi/series/my_bidding", //我的竞价
    list_bidding: "hanxin/appApi/series/list_bidding", //当前竞价
    add_bidding: "hanxin/appApi/series/add_bidding", //立即竞价
    revoke_bidding: "hanxin/appApi/series/revoke_bidding", //撤销竞价
    revoke_bidding1: "hanxin/adminApi/series/revoke_bidding", //撤销竞价(测试)

    sell: "hanxin/appApi/series/sell", //卖给他
    can_biddingg: "hanxin/appApi/series/can_biddingg", //是否开启竞价
    playAndEarnList: "minigame/appApi/game/common/getBannerItem", //边玩边赚列表
    getMobile: "appusercenter/appApi/oneClickLogin/getMobile", //sdk获得token，跟服务端换取手机号
    oneClickLoginRegister: "appusercenter/appApi/oneClickLogin/register", //SDK获取到手机号去一键登录
    accumulated_can_withdrawal:
      "waliangge/appApi/broker/settlemented/accumulated_can_withdrawal", //经纪商-获取已经提现的/当前可提现的
    orderList: "hanxin/appApi/order/v2/orderList", //订单列表
    listCountV2: "hanxin/appApi/order/v2/listCount", //订单上方统计
    biddingBuying: "hanxin/appApi/series/order/bidding_buying", //竞价购买中列表
    getSeriesTodayPriceInfo: "hanxin/appApi/order/getSeriesTodayPriceInfo", //系列价格信息
    getSeriesPriceKline: "hanxin/appApi/order/getSeriesPriceKline", //系列价格k线
    dutyOperateListV2: "hanxin/appApi/order/target/dutyOperateListV2", //已批量买到的
    dutyList: "hanxin/appApi/order/target/dutyList", //批量购买中
    applePayNotify: "pixiu/appApi/pay/applePay/user/notify", //苹果支付查询
    registerByFriendInvitedCode:
      "appusercenter/appApi/latest/appPhone/registerByFriendInvitedCode", //好友基本信息
    bindDevice: "hongyan/appApi/userDevice/bindDevice", //  绑定设备
    settlementedRewordInFo: "waliangge/appApi/broker/settlemented/rewordInFo", // 查询经纪商-奖励明细
    settlementedWithdrawal: "waliangge/appApi/broker/settlemented/withdrawal", // 经纪商-提现到余额
    friendRewordInFo: "waliangge/appApi/broker/settlemented/friendRewordInFo", // 查询经纪商-好友列表/好友奖励明细
    secretInfo: "appusercenter/appApi/user/secret/info", //微信号查询
    wechatInfoEdit: "appusercenter/adminApi/user/wechatInfoEdit", //微信修改
    java_rankingTemporary: "waliangge/appApi/ranking/temporary", //富豪榜-新版
    createRecord: "hanxin/appApi/batchOrder/createRecord", //開通
    createSeizeRecord: "hanxin/appApi/option/createSeizeRecord", //開通
    flyCardExchange: "minigame/appApi/game/balance/flyCardExchange",
    getCoinNum: "minigame/appApi/game/balance/post", //获取用户B宝数量,
    getFlyCardCount: "waliangge/appApi/out/goodsCount", //获取用户起飞卡数量，
    getCoinHistory: "minigame/appApi/game/balance/userRecord", //获取用户B宝记录
    //起飞卡接口
    getHoldInfo: "flybattle/appApi/flyBattle/getHoldInfo", //获取持有信息
    updateUserInfo: "flybattle/appApi/flyBattle/updateUserInfo", //更新起飞卡信息
    getUserInfo: "flybattle/appApi/flyBattle/getUserInfo", //获取起飞卡信息
    queryGroup: "queqiao/appApi/im/queryGroup", // 获取群资料
    applyJoinGroupV2: "queqiao/appApi/im/applyJoinGroupV2", //加入群
    inviteInfo: "flybattle/appApi/invite/inviteInfo", //邀请信息
    inviteJoin: "flybattle/appApi/invite/join", //加入
    invitedList: "flybattle/appApi/invite/invitedList", //已邀请列表
    getContractAddress: "flybattle/appApi/invite/getContractAddress", //获取邀新链接
    getRestTime: "flybattle/appApi/invite/getRestTime", //获取剩余时间

    getMarketPrice: "flybattle/appApi/flyBattle/getMarketPrice", //市场价获取
    getDelegatingOrder: "flybattle/appApi/flyBattle/getDelegatingOrder", //已成交委托单
    getTradedOrder: "flybattle/appApi/flyBattle/getTradedOrder", //预估强评价获取
    getForClosePrice: "flybattle/appApi/flyBattle/getForClosePrice", //同意状态更新
    entrustOrder: "flybattle/appApi/flyBattle/entrustOrder", //委托下单
    getTradedOrderder: "flybattle/appApi/flyBattle/getTradedOrder", //我的仓位
    closeOrder: "flybattle/appApi/flyBattle/closeOrder", //一键平仓
    revokeOrder: "flybattle/appApi/flyBattle/revokeOrder", //撤销委托
    checkPass: "flybattle/appApi/flyBattle/checkPass", //密码校验
    getHistoryEntrustOrder: "flybattle/appApi/flyBattle/getHistoryEntrustOrder", //历史下单
    getHistoryCompletedOrder:
      "flybattle/appApi/flyBattle/getHistoryCompletedOrder", //历史仓位
    getKLine: "flybattle/appApi/flyBattle/getKLine", //获取k线图
    getSingleOrderInfo: "flybattle/appApi/flyBattle/getSingleOrderInfo", //获取单个订单状态
    getParam: "flybattle/appApi/flyBattle/getParam", //获取市价百分比 校验
    // getFlyCardCount: "flybattle/appApi/flyBattle/getFlyCardCount", //获取用户起飞卡数量
    invitedFriendInfo: "flybattle/appApi/flyBattle/invitedFriendInfo", //获取用户邀请码
    dayFirstVisit: "appusercenter/appApi/visit/dayFirstVisit", //
    getUserInfotake: "flybattle/appApi/flyBattle/getUserInfo",
    getShortLink: "shortlink/appApi/shortlink/longLinkToShortLink", //获取短链
    CreateResale: "hanxin/appApi/order/resale/create", // 转售 /nms/dubbo/
    batchBuyUnPaidQuery: "hanxin/appApi/order/batchBuyUnPaidQuery", //查询批量订单
    ClosePositions: "bvexchange/appApi/exchange/closePosition", // 一键平仓
    CreateOrderDer: "bvexchange/appApi/exchange/createOrderDer", // 创建订单
    GetCurPosition: "bvexchange/appApi/exchange/getCurPosition", //查询我的仓位
    GetDelegatingOrder: "bvexchange/appApi/exchange/getDelegatingOrder", //查询委托单 等待成交
    OtoPosition: "bvexchange/appApi/exchange/otoPosition", //止盈止损
    ResetOtoPosition: "bvexchange/appApi/exchange/resetOtoPosition", // 重置止盈止损
    RevokeOrders: "bvexchange/appApi/exchange/revokeOrder", //撤销订单
    CheckPopup: "waliangge/appApi/invite/validate", // 弹窗查询接口
    inviteActivity: "waliangge/appApi/invite/info", // 活动信息查询接口
    inviteFriends: "waliangge/appApi/invite/list", // 邀请列表分页查询接口
    GetReward: "waliangge/appApi/invite/getReward", //奖励领取接口
    GetHistoryOrder: "bvexchange/appApi/exchange/getHistoryOrder", // 查询历史下单
    GetHistoryPosition: "bvexchange/appApi/exchange/getHistoryPosition", // 查询历史持仓
    GetExchangeUserInfo: "bvexchange/appApi/exchange/getUserInfo", // 查询用户信息
    CreateOtoRecord: "hanxin/appApi/option/createOtoRecord", // 添加止盈止损vip记录
    GetExchangeParam: "bvexchange/appApi/exchange/getExchangeParam", // 查询通用参数
    GettradePrcie: "bvexchange/appApi/exchange/tradePrcie", // 成交价格
    ShowLuckySpectator: "bvexchange/appApi/exchange/showLuckySpectator", // 展示收益
    GetBit: "bvexchange/appApi/exchange/getMarketPrice", // 获取bit
    GetPositonStatus: "bvexchange/appApi/exchange/getPositionInfo", // 获取单个仓位信息
    GetorderStatus: "bvexchange/appApi/exchange/queryOrderStatus", // 获取单个订单信息
    reSendBindSms: "pixiu/appApi/bankCard/V2/reSendBindSms", //预绑卡获取验证码
    TradeInfo: "bvexchange/appApi/exchange/transactionInfoVO", // 交易大赛信息
    TradeRank: "bvexchange/appApi/exchange/transactionRankInfo", // 查询排名信息
    TradeJoin: "bvexchange/appApi/exchange/joinTransaction", // 加入交易大赛
    chooseInfo: "waliangge/appApi/game/live/choose/info", //查询祈福贴
    java_exchangeRedClothTeam:
      "waliangge/appApi/ticket/change/exchangeRedClothTeam", //祈福贴兑换藏品
    scaleInfo: "mallcenter/appApi/scale/info", //us查询
    scaleIncreaseList: "mallcenter/appApi/scale/increaseList", //涨幅列表
    scaleRecommendList: "mallcenter/appApi/scale/recommendList", //精选列表
    scaleUserSelectList: "mallcenter/appApi/scale/userSelectList", //自选列表
    scaleUserSelect: "mallcenter/appApi/scale/userSelect", // 自选/取消自选
    scaleSeriesInfo: "mallcenter/appApi/scale/seriesInfo", //系列信息
    scaleBuyPrice: "mallcenter/appApi/scale/buyPrice", //买入参考价 份数
    scaleSellPrice: "mallcenter/appApi/scale/sellPrice", //卖出参考价
    scaleOnSaleList: "mallcenter/appApi/scale/onSaleList", //等待卖出
    targetBatchCancel: "hanxin/appApi/order/target/batchCancel", //批量撤销求购
    scaleBatchOnSale: "mallcenter/appApi/scale/batchOnSale", //限价卖出
    targetQuickSell: "hanxin/appApi/order/target/quickSell", //快捷卖出
    java_seriesOrderList: "hanxin/appApi/order/seriesOrderList",
    scaleKline: "mallcenter/appApi/scale/kline",
    exchangeGetKline: "bvexchange/appApi/exchange/getKline",
    subscribe: "hongyan/appApi/tag/subscribe",
    tag_list: "hongyan/appApi/tag/tag_list",
    oldZone: "mallcenter/appApi/myCollection/getUserOldSeriesList",
    shopCheck: "hanxin/appApi/order/shop/check", //活动商品校验
    shopPay: "hanxin/appApi/order/shop/pay", //活动商品下单并支付
    question: "appusercenter/appApi/appUserAnswer/question", //题目
    answer: "appusercenter/appApi/appUserAnswer/answer", //答题
    indexs: "coinexchange/appApi/exchange/symbolList",
    ClosecoinPositions: "coinexchange/appApi/exchange/closePosition", // 一键平仓
    GetcoinExchangeParam: "coinexchange/appApi/exchange/getExchangeParam", // 查询通用参数
    GetcoinExchangeUserInfo: "coinexchange/appApi/exchange/getUserInfo", // 查询ocin用户信息
    CreatecoinOrderDer: "coinexchange/appApi/exchange/createOrderDer", // 创建订单

    GetcoinCurPosition: "coinexchange/appApi/exchange/getCurPosition", //查询我的仓位
    GetcoinDelegatingOrder: "coinexchange/appApi/exchange/getDelegatingOrder", //查询委托单 等待成交
    OtocoinPosition: "coinexchange/appApi/exchange/otoPosition", //止盈止损
    RevokecoinOrders: "coinexchange/appApi/exchange/revokeOrder", //撤销订单

    GetcoinHistoryOrder: "coinexchange/appApi/exchange/getHistoryOrder", // 查询历史下单
    GetcoinHistoryPosition: "coinexchange/appApi/exchange/getHistoryPosition", // 查询历史持仓
    GetcoinBit: "coinexchange/appApi/exchange/getMarketPrice", // 获取bit
    GetbitPositonStatus: "coinexchange/appApi/exchange/getPositionInfo", // 获取单个仓位信息
    ResetcoinOtoPosition: "coinexchange/appApi/exchange/resetOtoPosition", // 重置止盈止损
    ShowcoinLuckySpectator: "coinexchange/appApi/exchange/showLuckySpectator", // 展示收益
    GetcointradePrcie: "coinexchange/appApi/exchange/tradePrcie", // 成交价格
    EntrustingList: "waliangge/appApi/market/listAll", // 正在求购的列表-所有人的
    GetcoinorderStatus: "coinexchange/appApi/exchange/queryOrderStatus", // 获取单个订单信息
    getMutilCoin: "coinexchange/appApi/exchange/getExchangeParamAll",
    exchangeGetKlineCoin: "coinexchange/appApi/exchange/getKline",
    VisitorShare: "appusercenter/appApi/visit/doVisitRecord",
    realDetail: "mallcenter/appApi/bzl/realDetail", //实物查询
    storeOrderList: "hanxin/appApi/order/shop/order/list", //实物订单
    couponList:"bvexchange/appApi/coinTrail/getCoinTrailList", // 券包
    getDelegatingOrderAll:'coinexchange/appApi/exchange/getDelegatingOrderAll', //查询等待成交 所有币对
    getCurPositionAll:'coinexchange/appApi/exchange/getCurPositionAll', // 查询我的仓位 全部
    getHistoryOrderAll:'coinexchange/appApi/exchange/getHistoryOrderAll', // 查询历史下单 所有币对
    getHistoryPositionAll:'coinexchange/appApi/exchange/getHistoryPositionAll', // 查询历史持仓 全部币对
    getBalanceLog:'jiuzhang/appApi/account/balance/exchange/log',
    getInviteList:"bvexchange/appApi/user/inviteList",
    getYearReport2025: "waliangge/appApi/yearreport/getYearReport2024",
    shareYearReport2024: "waliangge/appApi/yearreport/shareYearReport2024",
    inviteUserInfo: "bvexchange/appApi/user/inviteUserInfo", // 邀请人信息
  },
  // 注册  php原接口 拉新人使用的
  register(data) {
    return http.post(this.requestPath.register, data);
  },
  // 地址管理
  address(data) {
    return http.post_java(this.requestPath.address, data);
  },
  // 添加地址
  addAddress(data) {
    return http.post_java(this.requestPath.addAddress, data);
  },
  // 添加地址
  editAddress(data) {
    return http.post_java(this.requestPath.editAddress, data);
  },
  // 删除地址
  delAddress(data) {
    return http.post_java(this.requestPath.delAddress, data);
  },
  // 个人中心一键上架下架
  visibility(data) {
    return http.post_java(this.requestPath.visibility, data);
  },
  //创作 藏品 下架
  unSale(data) {
    return http.post_java(this.requestPath.unSale, data);
  },
  // 一键销毁
  goodsDestroy(data) {
    return http.post_java(this.requestPath.goodsDestroy, data);
  },
  // 邀请记录列表
  invitelist(data) {
    return http.post_java(this.requestPath.invitelist, data);
  },
  //赠送查询列表
  fanssearch(data) {
    return http.post_java(this.requestPath.fanssearch, data);
  },
  //银行卡列表
  bank_list(data) {
    return http.post_java(this.requestPath.bank_list, data);
  },
  //预绑卡
  bank_bind_pre(data) {
    return http.post_java(this.requestPath.bank_bind_pre, data);
  },
  //确定绑卡
  bank_bind_confirm(data) {
    return http.post_java(this.requestPath.bank_bind_confirm, data);
  },
  //解绑银行卡
  bank_unbind(data) {
    return http.post_java(this.requestPath.bank_unbind, data);
  },

  //支持银行列表
  bankList(data) {
    return http.post_java(this.requestPath.bankList, data);
  },
  //卡号查询银行
  bank_card_info(data) {
    return http.post_java(this.requestPath.bank_card_info, data);
  },
  //藏品上架价格计算
  calculatePrice(data) {
    return http.post_java(this.requestPath.calculatePrice, data);
  },
  //========================java商城========================
  //java商城
  java_mallHome(data) {
    return http.post_java(this.requestPath.java_mallHome, data);
  },
  //java市场页面数据
  java_marketTab(data) {
    return http.post_java(this.requestPath.java_marketTab, data);
  },
  //java榜单
  java_mallRank(data) {
    return http.post_java(this.requestPath.java_mallRank, data);
  },
  // java榜单-热度榜
  java_popularRank(data) {
    return http.post_java(this.requestPath.java_popularRank, data);
  },
  // java榜单-涨幅榜
  java_gainRank(data) {
    return http.post_java(this.requestPath.java_gainRank, data);
  },
  // java榜单-粉丝榜
  java_fansRank(data) {
    return http.post_java(this.requestPath.java_fansRank, data);
  },
  // java榜单-实时榜
  java_liveRank(data) {
    return http.post_java(this.requestPath.java_liveRank, data);
  },
  // java榜单-实时榜
  java_expandRank(data) {
    return http.post_java(this.requestPath.java_expandRank, data);
  },
  // java榜单-弹框
  java_pointRank(data) {
    return http.post_java(this.requestPath.java_pointRank, data);
  },
  //java商城推荐
  java_recommendGoods(data) {
    return http.post_java(this.requestPath.java_recommendGoods, data);
  },
  //java详情页推荐
  java_recommend(data) {
    return http.post_java(this.requestPath.java_recommend, data);
  },
  // //java 搜索关键字
  // java_search_keywords(data) {
  //     return http.post_java(this.requestPath.java_search_keywords, data)
  // },
  // java 吉物仓热词列表
  // java_search_Hotkeywords(data) {
  //     return http.post_java(this.requestPath.java_search_Hotkeywords, data)
  // },
  //java 搜索结果
  java_search_list(data) {
    return http.post_java(this.requestPath.java_search_list, data);
  },
  //java 作品详情
  java_goodsDetails(data) {
    return http.post_java(this.requestPath.java_goodsDetails, data);
  },
  //java 作品详情-全部评论
  java_allCritic(data) {
    return http.post_java(this.requestPath.java_allCritic, data);
  },
  //java 作品详情-评论
  java_community(data) {
    return http.post_java(this.requestPath.java_community, data);
  },
  //java 作品详情-评论点赞
  java_commentLike(data) {
    return http.post_java(this.requestPath.java_commentLike, data);
  },
  // java 历史交易价格
  java_allTransaction(data) {
    return http.post_java(this.requestPath.java_allTransaction, data);
  },
  //java精选ip弹框提示语
  // bounceTips(data) {
  // 	return http.post_java(this.requestPath.java_bounceTips, data)
  // },
  // 新 java精选ip弹框提示语
  //java精选ip弹框提示语
  bounceTips(data) {
    return http.post_java(this.requestPath.java_bounceTips, data);
  },
  //java热门精选ip数据
  // selected_user_list(data) {
  // 	return http.post_java(this.requestPath.java_selected_user_list, data)
  // },
  //新 java热门精选ip数据
  java_selected_user_list(data) {
    return http.post_java(this.requestPath.java_selected_user_list, data);
  },
  // //java热门精选商品数据
  // selected_list(data) {
  // 	return http.post_java(this.requestPath.java_selected_list, data)
  // },
  //新 java热门精选商品数据
  java_selected_list(data) {
    return http.post_java(this.requestPath.java_selected_list, data);
  },
  //java铸造页获取前置信息
  java_release_information(data) {
    return http.post_java(this.requestPath.java_release_information, data);
  },
  //java铸造页获取前置信息
  java_release(data) {
    return http.post_java(this.requestPath.java_release, data);
  },
  //java虚拟人 | 版权
  java_screenLabelGoods(data) {
    return http.post_java(this.requestPath.java_screenLabelGoods, data);
  },
  //java  用户搜索
  java_searchList(data) {
    return http.post_java(this.requestPath.java_searchList, data);
  },
  //java  个人中心 藏家服务，创作者服务
  java_getAppConfigList(data) {
    return http.post_java(this.requestPath.java_getAppConfigList, data);
  },
  //首页-关注
  java_follow_list(data) {
    return http.post_java(this.requestPath.java_follow_list, data);
  },
  //首页-探索
  java_discovery_list(data) {
    return http.post_java(this.requestPath.java_discovery_list, data);
  },
  //搜索-系列
  java_searchSeriesList(data) {
    return http.post_java(this.requestPath.java_searchSeriesList, data);
  },
  //创作-个人中心
  java_creatorsSeriesList(data) {
    return http.post_java(this.requestPath.java_creatorsSeriesList, data);
  },
  //藏品-个人中心
  java_creatorsMyCollection(data) {
    return http.post_java(this.requestPath.java_creatorsMyCollection, data);
  },
  //商场-普通系列
  java_seriesDetails(data) {
    return http.post_java(this.requestPath.java_seriesDetails, data);
  },
  //商场-盲盒系列
  java_blindSeriesDetail(data) {
    return http.post_java(this.requestPath.java_blindSeriesDetail, data);
  },
  //商场-普通系列-作品列表
  java_blindGoodslist(data) {
    return http.post_java(this.requestPath.java_blindGoodslist, data);
  },
  //盲盒系列详情
  java_blindSeriesGoodsList(data) {
    return http.post_java(this.requestPath.java_blindSeriesGoodsList, data);
  },
  //铸造查询系列信息
  java_createSeriesList(data) {
    return http.post_java(this.requestPath.java_createSeriesList, data);
  },
  // 3d专区
  java_3dSearch(data) {
    return http.post_java(this.requestPath.java_3dSearch, data);
  },
  //java市场专题
  java_tabmarketSpecialGoodsList(data) {
    return http.post_java(
      this.requestPath.java_tabmarketSpecialGoodsList,
      data
    );
  },
  //java点赞
  java_communityLike(data) {
    return http.post_java(this.requestPath.java_communityLike, data);
  },
  //java关注
  java_communityAttention(data) {
    return http.post_java(this.requestPath.java_communityAttention, data);
  },
  //java用户关注作品列表
  java_followerList(data) {
    return http.post_java(this.requestPath.java_followerList, data);
  },
  //java用户关注列表
  java_communityFollowerList(data) {
    return http.post_java(this.requestPath.java_communityFollowerList, data);
  },
  //java用户粉丝列表
  java_fansList(data) {
    return http.post_java(this.requestPath.java_fansList, data);
  },
  // java 支付配置
  java_payRouteConfig(data) {
    return http.post_java(this.requestPath.java_payRouteConfig, data);
  },

  // java 保证金信息
  java_securityInfo(data) {
    return http.post_java(this.requestPath.java_securityInfo, data);
  },
  // java 保证金变更记录详情
  java_securityDetail(data) {
    return http.post_java(this.requestPath.java_securityDetail, data);
  },
  // java 保证金变更记录列表
  java_securityList(data) {
    return http.post_java(this.requestPath.java_securityList, data);
  },
  // java 保证金退保
  java_securityCreate(data) {
    return http.post_java(this.requestPath.java_securityCreate, data);
  },
  // java 保证金取消退保
  java_securityCancel(data) {
    return http.post_java(this.requestPath.java_securityCancel, data);
  },
  // java 保证金退款审核
  java_securityCheck(data) {
    return http.post_java(this.requestPath.java_securityCheck, data);
  },
  // java 保证金创建订单
  java_securityOrder(data) {
    return http.post_java(this.requestPath.java_securityOrder, data);
  },
  // java 保证金冻结
  java_securityFreeze(data) {
    return http.post_java(this.requestPath.java_securityFreeze, data);
  },

  // java收藏作品
  java_community_collect(data) {
    return http.post_java(this.requestPath.java_community_collect, data);
  },
  // java 个人中心创作作品查询+搜索
  java_creationGoodsSearchList(data) {
    return http.post_java(this.requestPath.java_creationGoodsSearchList, data);
  },

  // java 个人中心藏品作品查询+搜索
  java_collectionGoodsSearchList(data) {
    return http.post_java(
      this.requestPath.java_collectionGoodsSearchList,
      data
    );
  },

  //java个人中心 系列搜索
  java_seriesSearchMyCreation(data) {
    return http.post_java(this.requestPath.java_seriesSearchMyCreation, data);
  },

  //java个人中心 修改头像
  java_userEditAvatar(data) {
    return http.post_java(this.requestPath.java_userEditAvatar, data);
  },
  //java个人中心 是否有消息未读
  java_userMsgUnreadNumber(data) {
    return http.post_java(this.requestPath.java_userMsgUnreadNumber, data);
  },
  //java消息中心 --消息盒子信息
  java_msgBoxList(data) {
    return http.post_java(this.requestPath.java_msgBoxList, data);
  },
  //java消息中心 --获取是否存在未读消息以及未读消息数量：
  java_unreadNumber(data) {
    return http.post_java(this.requestPath.java_unreadNumber, data);
  },
  //java消息中心 --用户打开盒子，盒子所有消息变为已读
  java_readBoxMsg(data) {
    return http.post_java(this.requestPath.java_readBoxMsg, data);
  },
  //java消息中心 --获取消息盒子下的消息
  java_listPageUserBoxMsg(data) {
    return http.post_java(this.requestPath.java_listPageUserBoxMsg, data);
  },
  //java消息中心 --获取富文本
  java_userMsg(data) {
    return http.post_java(this.requestPath.java_userMsg, data);
  },
  //java 燃料展示 -
  java_gas_item_list(data) {
    return http.post_java(this.requestPath.java_gas_item_list, data);
  },
  //java 燃料订单创建 -
  java_order_create_gas(data) {
    return http.post_java(this.requestPath.java_order_create_gas, data);
  },
  //java 调起支付
  java_order_pay_call(data) {
    return http.post_java(this.requestPath.java_order_pay_call, data);
  },
  //java 我的钱包页
  java_balance_info(data) {
    return http.post_java(this.requestPath.java_balance_info, data);
  },
  //java 我的钱包-流水
  java_balance_log(data) {
    return http.post_java(this.requestPath.java_balance_log, data);
  },
  //java 我的钱包-免佣信息
  java_exemption_info(data) {
    return http.post_java(this.requestPath.java_exemption_info, data);
  },
  //java 提现页-信息展示
  java_withdraw_info(data) {
    return http.post_java(this.requestPath.java_withdraw_info, data);
  },
  //java 提现页-提现
  java_withdraw_create(data) {
    return http.post_java(this.requestPath.java_withdraw_create, data);
  },
  //作品下单校验
  java_order_check(data) {
    return http.post_java(this.requestPath.java_order_check, data);
  },
  //创建作品订单
  java_create_item(data) {
    return http.post_java(this.requestPath.java_create_item, data);
  },
  //银行卡支付-验证码确认
  java_pay_confirm(data) {
    return http.post_java(this.requestPath.java_pay_confirm, data);
  },
  //java 订单列表
  java_order_list(data) {
    return http.post_java(this.requestPath.java_order_list, data);
  },
  //java 订单详情
  java_order_detail(data) {
    return http.post_java(this.requestPath.java_order_detail, data);
  },
  //java 隐私设置 --用户创造列表权限
  java_userCreatePreview(data) {
    return http.post_java(this.requestPath.java_userCreatePreview, data);
  },
  //java  隐私设置 --用户关注列表权限
  java_userCollectionsPreview(data) {
    return http.post_java(this.requestPath.java_userCollectionsPreview, data);
  },
  //java 隐私设置 --用户创造列表权限
  java_userFavoritePreview(data) {
    return http.post_java(this.requestPath.java_userFavoritePreview, data);
  },
  //java 隐私设置 --户关注作品列表权限
  java_goodsFavoritePreview(data) {
    return http.post_java(this.requestPath.java_goodsFavoritePreview, data);
  },
  //java 隐私设置 --用户粉丝列表权限
  java_userFansPreview(data) {
    return http.post_java(this.requestPath.java_userFansPreview, data);
  },
  //java 隐私设置 --用户粉丝列表权限
  java_userPreview(data) {
    return http.post_java(this.requestPath.java_userPreview, data);
  },
  //java 账户注销 --基本信息校验接口
  java_verifyInfo(data) {
    return http.post_java(this.requestPath.java_verifyInfo, data);
  },
  //java 账户注销 --注销
  java_execute(data) {
    return http.post_java(this.requestPath.java_execute, data);
  },
  //java 账户注销 --注销进度
  java_detail(data) {
    return http.post_java(this.requestPath.java_detail, data);
  },
  //java 账户注销 --终止注销
  java_stop(data) {
    return http.post_java(this.requestPath.java_stop, data);
  },
  //java 账户注销 --校验是否注销
  java_verifyCancelAccount(data) {
    return http.post_java(this.requestPath.java_verifyCancelAccount, data);
  },
  //java 订单取消
  java_order_cancel(data) {
    return http.post_java(this.requestPath.java_order_cancel, data);
  },
  //java作品转赠
  java_order_gift(data) {
    return http.post_java(this.requestPath.java_order_gift, data);
  },
  //java销毁作品 (暂时不用)
  java_order_destory(data) {
    return http.post_java(this.requestPath.java_order_destory, data);
  },
  //java充值
  java_deposit_create(data) {
    return http.post_java(this.requestPath.java_deposit_create, data);
  },

  //java 登录注册 --发送邮箱验证码
  java_sendAliYunEmail(data) {
    return http.post_java(this.requestPath.java_sendAliYunEmail, data);
  },
  //java 登录注册 --获取手机验证码
  java_sendAliYunSms(data) {
    return http.post_java(this.requestPath.java_sendAliYunSms, data);
  },
  //java 实名信息 --展示接口
  java_authInfo(data) {
    return http.post_java(this.requestPath.java_authInfo, data);
  },

  //java 新版 市场页面数据获取
  java_recentTab(data) {
    return http.post_java(this.requestPath.java_recentTab, data);
  },
  //java 新版 市场页面 富文本详情
  java_customerLinkDetail(data) {
    return http.post_java(this.requestPath.java_customerLinkDetail, data);
  },

  //java 邮箱密码登录接口
  java_emailPasswordLogin(data) {
    return http.post_java(this.requestPath.java_emailPasswordLogin, data);
  },
  //java 手机密码登录接口
  java_phonePasswordLogin(data) {
    return http.post_java(this.requestPath.java_phonePasswordLogin, data);
  },
  //java 用户名密码登录
  java_usernamePasswordLogin(data) {
    return http.post_java(this.requestPath.java_usernamePasswordLogin, data);
  },
  //java 邮箱验证码登录
  java_emailVerifyCodeLogin(data) {
    return http.post_java(this.requestPath.java_emailVerifyCodeLogin, data);
  },
  //java 手机号验证码登录
  java_phoneVerifyCodeLogin(data) {
    return http.post_java(this.requestPath.java_phoneVerifyCodeLogin, data);
  },
  //java 邮箱注册接口
  java_appUserEmailRegister(data) {
    return http.post_java(this.requestPath.java_appUserEmailRegister, data);
  },
  // 手机验证码注册登录
  java_appPhoneregister(data) {
    return http.post_java(this.requestPath.java_appPhoneregister, data);
  },
  //邮箱验证码登录注册
  java_emailRegister(data) {
    return http.post_java(this.requestPath.java_emailRegister, data);
  },
  //java 手机号找回密码
  java_appUserForgetPasswordVerifyPhoneSmsCode(data) {
    return http.post_java(
      this.requestPath.java_appUserForgetPasswordVerifyPhoneSmsCode,
      data
    );
  },
  //java 邮箱找回密码
  java_appUserForgetPasswordVerifyEmailCode(data) {
    return http.post_java(
      this.requestPath.java_appUserForgetPasswordVerifyEmailCode,
      data
    );
  },
  //java 校验邮箱验证码
  java_verifyAliYunEmailCode(data) {
    return http.post_java(this.requestPath.java_verifyAliYunEmailCode, data);
  },
  //java 校验手机验证码
  java_verifyAliYunPhoneCode(data) {
    return http.post_java(this.requestPath.java_verifyAliYunPhoneCode, data);
  },
  //java 修改密码
  java_appUserLoginReSetPassword(data) {
    return http.post_java(this.requestPath.appUserLoginReSetPassword, data);
  },
  //java 背景图
  java_editBackground(data) {
    return http.post_java(this.requestPath.java_editBackground, data);
  },
  //java 搜索热词
  java_hotKeywords(data) {
    return http.post_java(this.requestPath.java_hotKeywords, data);
  },
  //java 修改用户简介
  java_appUserEditDesc(data) {
    return http.post_java(this.requestPath.java_appUserEditDesc, data);
  },
  //java 修改用户昵称
  java_appUserEditNickName(data) {
    return http.post_java(this.requestPath.java_appUserEditNickName, data);
  },
  //java 一键登录
  java_oneClickLoginRegister(data) {
    return http.post_java(this.requestPath.java_oneClickLoginRegister, data);
  },
  //java 一键登录获取前置信息
  java_oneClickLoginGetAuthToken(data) {
    return http.post_java(
      this.requestPath.java_oneClickLoginGetAuthToken,
      data
    );
  },
  //java 一键登录手机号密码注册
  java_oneClickLoginPhoneRegister(data) {
    return http.post_java(
      this.requestPath.java_oneClickLoginPhoneRegister,
      data
    );
  },
  // java 开放平台登录
  java_openLogin(data) {
    return http.post_java(this.requestPath.java_openLogin, data);
  },

  //java 设置支付密码  太多地方用过这个方法，暂时不给予改名风险
  tradePassCreate(data) {
    return http.post_java(this.requestPath.java_tradePassCreate, data);
  },
  //java修改支付密码  太多地方用过这个方法，暂时不给予改名风险
  tradePassEdit(data) {
    return http.post_java(this.requestPath.java_tradePassEdit, data);
  },
  //java重置支付密码  太多地方用过这个方法，暂时不给予改名风险
  tradePassReset(data) {
    return http.post_java(this.requestPath.java_tradePassReset, data);
  },
  // java 长连接转短链接
  java_shortLink(data) {
    return http.post_java(this.requestPath.java_shortLink, data);
  },

  // java app 外站控制器
  //java 外站列表
  java_websiteList(data) {
    return http.post_java(this.requestPath.java_websiteList, data);
  },
  //java 编辑用户外站
  java_websiteUpdate(data) {
    return http.post_java(this.requestPath.java_websiteUpdate, data);
  },
  //java 编辑用户外站
  java_userWebsite(data) {
    return http.post_java(this.requestPath.java_userWebsite, data);
  },
  //java 数字 rmb 支付
  java_digitalRMBPay(data) {
    return http.post_java(this.requestPath.java_digitalRMBPay, data);
  },
  //java 详情页引导购买作品
  java_purchaseGuide(data) {
    return http.post_java(this.requestPath.java_purchaseGuide, data);
  },
  // 获取 bigverse 列表
  java_bigverseList(data) {
    return http.post_java(this.requestPath.java_bigverseList, data);
  },
  // 获取 bigverse 列表
  java_marketBrandUserList(data) {
    return http.post_java(this.requestPath.java_marketBrandUserList, data);
  },
  // 实名认证
  certification(data) {
    return http.post_java(this.requestPath.certification, data);
  },
  //设置预期售出价格
  java_setExpectPrice(data) {
    return http.post_java(this.requestPath.java_setExpectPrice, data);
  },
  //设置预期购买价格
  java_setExpectedBuyPrice(data) {
    return http.post_java(this.requestPath.java_setExpectedBuyPrice, data);
  },
  //设置预期购买价格
  java_complainItem(data) {
    return http.post_java(this.requestPath.java_complainItem, data);
  },
  //设置预期购买价格
  java_kingKongThemeGoods(data) {
    return http.post_java(this.requestPath.java_kingKongThemeGoods, data);
  },
  //java校验手机号
  java_phoneBind(data) {
    return http.post_java(this.requestPath.java_phoneBind, data);
  },
  //停售盲盒系列接口
  java_blindPullOff(data) {
    return http.post_java(this.requestPath.java_blindPullOff, data);
  },
  //开启盲盒系列接口
  java_blindputOn(data) {
    return http.post_java(this.requestPath.java_blindputOn, data);
  },
  //java飞跃计划
  java_planConfigList(data) {
    return http.post_java(this.requestPath.java_planConfigList, data);
  },
  //飞跃计划开通
  java_createLeap(data) {
    return http.post_java(this.requestPath.java_createLeap, data);
  },
  // 官方公告-列表
  java_officialArticleList(data) {
    return http.post_java(this.requestPath.java_officialArticleList, data);
  },
  // 官方公告-详情
  java_officialArticleDetail(data) {
    return http.post_java(this.requestPath.java_officialArticleDetail, data);
  },
  // 官方公告-点赞
  java_officialThumpUp(data) {
    return http.post_java(this.requestPath.java_officialThumpUp, data);
  },
  // // 官方公告-点赞
  // java_officialRead(data) {
  // 	return http.post_java(this.requestPath.java_officialRead, data)
  // },
  // APP首页搜索-三合一搜索
  java_searchAll(data) {
    return http.post_java(this.requestPath.java_searchAll, data);
  },
  // 飞跃计划开通成功页
  java_buySuccessInfo(data) {
    return http.post_java(this.requestPath.java_buySuccessInfo, data);
  },
  //飞跃计划燃料展示
  java_leapGasAccountInfo(data) {
    return http.post_java(this.requestPath.java_leapGasAccountInfo, data);
  },
  //获取市场页推荐ip列表
  java_marketRecommendList(data) {
    return http.post_java(this.requestPath.java_marketRecommendList, data);
  },
  //获取市场页推荐作品列表
  java_marketRecommendGoodsList(data) {
    return http.post_java(this.requestPath.java_marketRecommendGoodsList, data);
  },
  //pgc tab3
  java_pgc_tab3(data) {
    return http.post_java(this.requestPath.java_pgc_tab3, data);
  },
  //java_pgc_tab1
  java_pgc_tab1(data) {
    return http.post_java(this.requestPath.java_pgc_tab1, data);
  },
  //tab1 专题页
  java_pgc_tab1_recommendList(data) {
    return http.post_java(this.requestPath.java_pgc_tab1_recommendList, data);
  },
  //java 获取市场页推荐单个信息
  java_marketRecommendTabInfo(data) {
    return http.post_java(this.requestPath.java_marketRecommendTabInfo, data);
  },
  // java 发起求购
  java_targetCreate(data) {
    return http.post_java(this.requestPath.java_targetCreate, data);
  },
  // java 求购列表
  java_targetDutyList(data) {
    return http.post_java(this.requestPath.java_targetDutyList, data);
  },
  // java 求购成功
  java_targetDutyOperateList(data) {
    return http.post_java(this.requestPath.java_targetDutyOperateList, data);
  },
  //java 撤销求购
  java_targetCancel(data) {
    return http.post_java(this.requestPath.java_targetCancel, data);
  },
  // java 人脸认证查询结果
  java_completeFaceAuth(data) {
    return http.post_java(this.requestPath.java_completeFaceAuth, data);
  },
  //java 虫子商城列表
  java_exchangeitemList(data) {
    return http.post_java(this.requestPath.java_exchangeitemList, data);
  },
  //java 虫子商城兑换
  java_exchangeExchange(data) {
    return http.post_java(this.requestPath.java_exchangeExchange, data);
  },
  //java 虫子商城查询数量
  java_walletInfo(data) {
    return http.post_java(this.requestPath.java_walletInfo, data);
  },
  //游戏中悬浮窗
  java_commonconfigInfo(data) {
    return http.post_java(this.requestPath.java_commonconfigInfo, data);
  },
  //did隐私设置
  java_domainNamePreview(data) {
    return http.post_java(this.requestPath.java_domainNamePreview, data);
  },
  //数字身份
  java_domainNamelist(data) {
    return http.post_java(this.requestPath.java_domainNamelist, data);
  },
  //个人中心数字身份
  java_mallcenter_domainNamelist(data) {
    return http.post_java(
      this.requestPath.java_mallcenter_domainNamelist,
      data
    );
  },
  // 搜索数字身份
  java_search_domainNamelist(data) {
    return http.post_java(this.requestPath.java_search_domainNamelist, data);
  },
  // 域名注册
  java_createDomain(data) {
    return http.post_java(this.requestPath.java_createDomain, data);
  },
  //续费列表
  java_domainRenewList(data) {
    return http.post_java(this.requestPath.java_domainRenewList, data);
  },
  //确认续费
  java_createdomainRenew(data) {
    return http.post_java(this.requestPath.java_createdomainRenew, data);
  },
  //图片生成
  java_generator(data) {
    return http.get_java(this.requestPath.java_generator, data);
  },
  //数字身份注册校验
  java_domainGenerateInfo(data) {
    return http.post_java(this.requestPath.java_domainGenerateInfo, data);
  },
  //社区新人注册
  java_appPhoneRegister(data) {
    return http.post_java(this.requestPath.java_appPhoneRegister, data);
  },
  //获得域名
  java_getMetaApplication(data) {
    return http.post_java(this.requestPath.java_getMetaApplication, data);
  },
  //设置域名
  java_setMetaApplication(data) {
    return http.post_java(this.requestPath.java_setMetaApplication, data);
  },
  //获取历史记录
  java_getSeriesBuyRecord(data) {
    return http.post_java(this.requestPath.java_getSeriesBuyRecord, data);
  },
  //校验用户是否有资格
  java_batchOnSaleCheck(data) {
    return http.post_java(this.requestPath.java_batchOnSaleCheck, data);
  },
  //批量寄售
  java_operateOnSale(data) {
    return http.post_java(this.requestPath.java_operateOnSale, data);
  },
  userInfo(data) {
    return http.post_java(this.requestPath.java_userInfoV2, data);
  },
  // 订单统计
  java_orderListCount(data) {
    return http.post_java(this.requestPath.java_orderListCount, data);
  },
  // 新版吉物仓
  java_jwcList(data) {
    return http.post_java(this.requestPath.java_jwcList, data);
  },
  // 市场页系列搜索 与吉物仓一致
  pgcSearch(data) {
    return http.post_java(this.requestPath.pgcSearch, data);
  },
  baseInfo(data) {
    return http.post_java(this.requestPath.baseInfo, data);
  },
  //查询用户是否被成功邀请, 被邀请者注册完成之后调用
  inviteIsInvited(data) {
    return http.post_java(this.requestPath.inviteIsInvited, data);
  },
  //检验用户邀请资格
  inviteValidate(data) {
    return http.post_java(this.requestPath.inviteValidate, data);
  },
  //获取用户昵称
  shareShow(data) {
    return http.post_java(this.requestPath.shareShow, data);
  },
  //获取pgc门面tab1-下方模块列表
  java_recommendTabList(data) {
    return http.post_java(this.requestPath.java_recommendTabList, data);
  },
  //获取pgc门面tab1-下方模块列表
  java_targetDutyOperateListV2(data) {
    return http.post_java(this.requestPath.java_targetDutyOperateListV2, data);
  },
  //获取金刚区活动new下的活动
  java_activityNewActivityPageList(data) {
    return http.post_java(
      this.requestPath.java_activityNewActivityPageList,
      data
    );
  },
  //我的徒弟
  java_myStudentList(data) {
    return http.post_java(this.requestPath.java_myStudentList, data);
  },
  //我的师父
  java_myTeacher(data) {
    return http.post_java(this.requestPath.java_myTeacher, data);
  },
  //收益分成明细
  java_teacherIncomeList(data) {
    return http.post_java(this.requestPath.java_teacherIncomeList, data);
  },
  //绑定展示页
  java_bindTeacherShow(data) {
    return http.post_java(this.requestPath.java_bindTeacherShow, data);
  },
  //绑定
  java_bindTeacher(data) {
    return http.post_java(this.requestPath.java_bindTeacher, data);
  },
  //解除绑定
  java_unbindTeacher(data) {
    return http.post_java(this.requestPath.java_unbindTeacher, data);
  },
  //宝藏估价列表
  java_myGoodsAmount(data) {
    return http.post_java(this.requestPath.java_myGoodsAmount, data);
  },
  // 宝藏估价总计
  java_myGoodsAmountTotal(data) {
    return http.post_java(this.requestPath.java_myGoodsAmountTotal, data);
  },
  //绑定师徒关系
  java_teacherInfo(data) {
    return http.post_java(this.requestPath.java_teacherInfo, data);
  },
  //师门排行榜
  java_teacherRanking(data) {
    return http.post_java(this.requestPath.java_teacherRanking, data);
  },
  //富豪榜
  java_rankingList(data) {
    return http.post_java(this.requestPath.java_rankingList, data);
  },
  //买入清单
  java_rankingBuyList(data) {
    return http.post_java(this.requestPath.java_rankingBuyList, data);
  },
  //大额充值
  java_handleCreate(data) {
    return http.post_java(this.requestPath.java_handleCreate, data);
  },
  //新贵区邀请码领取作品接口
  java_receive(data) {
    return http.post_java(this.requestPath.java_receive, data);
  },
  //市场页下方是否显示 呼叫客服/加入社群icon接口
  java_isNewUser(data) {
    return http.post_java(this.requestPath.java_isNewUser, data);
  },
  //查询visaUid是否注册
  java_isRegister(data) {
    return http.post_java(this.requestPath.java_isRegister, data);
  },
  //绑定visaUid并且领取作品
  java_bindReceive(data) {
    return http.post_java(this.requestPath.java_bindReceive, data);
  },
  //验证
  java_validateTradeAmount(data) {
    return http.post_java(this.requestPath.java_validateTradeAmount, data);
  },
  //
  java_exchange(data) {
    return http.post_java(this.requestPath.java_exchange, data);
  },
  java_giveTicket(data) {
    return http.post_java(this.requestPath.java_giveTicket, data);
  },
  java_getTicketCount(data) {
    return http.post_java(this.requestPath.java_getTicketCount, data);
  },
  java_oldSeriesGoodsList(data) {
    return http.post_java(this.requestPath.java_oldSeriesGoodsList, data);
  },
  java_oldSeriesGoodsDestroy(data) {
    return http.post_java(this.requestPath.java_oldSeriesGoodsDestroy, data);
  },
  java_setShow(data) {
    return http.post_java(this.requestPath.java_setShow, data);
  },
  java_hotBroadcast(data) {
    return http.post_java(this.requestPath.java_hotBroadcast, data);
  },
  java_increaseList(data) {
    return http.post_java(this.requestPath.java_increaseList, data);
  },
  java_selfSelectionList(data) {
    return http.post_java(this.requestPath.java_selfSelectionList, data);
  },
  java_operationSelfSelection(data) {
    return http.post_java(this.requestPath.java_operationSelfSelection, data);
  },
  java_moreInquiriesList(data) {
    return http.post_java(this.requestPath.java_moreInquiriesList, data);
  },
  getUserHoldSeriesList(data) {
    return http.post_java(this.requestPath.getUserHoldSeriesList, data);
  },
  userSeriesCollectionList(data) {
    return http.post_java(this.requestPath.userSeriesCollectionList, data);
  },
  getAppUserHoldSeriesCountAndAvgPriceVO(data) {
    return http.post_java(
      this.requestPath.getAppUserHoldSeriesCountAndAvgPriceVO,
      data
    );
  },
  soldList(data) {
    return http.post_java(this.requestPath.soldList, data);
  },
  batchUnSaleCollectionList(data) {
    return http.post_java(this.requestPath.batchUnSaleCollectionList, data);
  },
  assetInfo(data) {
    return http.post_java(this.requestPath.assetInfo, data);
  },
  //微信一键登录
  java_wechatLogin(data) {
    return http.post_java(this.requestPath.java_wechatLogin, data);
  },
  //微信绑定
  java_wechatbind(data) {
    return http.post_java(this.requestPath.java_wechatbind, data);
  },
  //微信绑定
  java_loginBind(data) {
    return http.post_java(this.requestPath.java_loginBind, data);
  },
  //生成短链
  java_longLinkToShortLink(data) {
    return http.post_java(this.requestPath.java_longLinkToShortLink, data);
  },
  getAllDTOList(data) {
    return http.post_java(this.requestPath.getAllDTOList, data);
  },
  // 首页 买手榜列表
  incomeList(data) {
    return http.post_java(this.requestPath.incomeList, data);
  },
  // 我的竞价
  my_bidding(data) {
    return http.post_java(this.requestPath.my_bidding, data);
  },
  // 当前竞价
  list_bidding(data) {
    return http.post_java(this.requestPath.list_bidding, data);
  },
  // 立即竞价
  add_bidding(data) {
    return http.post_java(this.requestPath.add_bidding, data);
  },
  // 撤销竞价
  revoke_bidding(data) {
    return http.post_java(this.requestPath.revoke_bidding, data);
  },
  // 撤销竞价(测试)
  revoke_bidding1(data) {
    return http.post_java(this.requestPath.revoke_bidding1, data);
  },
  // 卖给他
  sell(data) {
    return http.post_java(this.requestPath.sell, data);
  },
  //我的b宝数量
  java_getBalanceNum(data) {
    return http.post_java(this.requestPath.java_getBalanceNum, data);
  },
  // 是否开启竞价
  can_biddingg(data) {
    return http.post_java(this.requestPath.can_biddingg, data);
  },
  //token获取手机号
  getMobile(data) {
    return http.post_java(this.requestPath.getMobile, data);
  },
  oneClickLoginRegister(data) {
    return http.post_java(this.requestPath.oneClickLoginRegister, data);
  },
  // 边玩边赚
  playAndEarnList(data) {
    return http.post_java(this.requestPath.playAndEarnList, data);
  },
  accumulated_can_withdrawal(data) {
    return http.post_java(this.requestPath.accumulated_can_withdrawal, data);
  },
  orderList(data) {
    return http.post_java(this.requestPath.orderList, data);
  },
  biddingBuying(data) {
    return http.post_java(this.requestPath.biddingBuying, data);
  },
  listCountV2(data) {
    return http.post_java(this.requestPath.listCountV2, data);
  },
  getSeriesTodayPriceInfo(data) {
    return http.post_java(this.requestPath.getSeriesTodayPriceInfo, data);
  },
  getSeriesPriceKline(data) {
    return http.post_java(this.requestPath.getSeriesPriceKline, data);
  },
  dutyOperateListV2(data) {
    return http.post_java(this.requestPath.dutyOperateListV2, data);
  },
  dutyList(data) {
    return http.post_java(this.requestPath.dutyList, data);
  },
  applePayNotify(data) {
    return http.post_java(this.requestPath.applePayNotify, data);
  },
  java_friendsList(data) {
    return http.post_java(this.requestPath.java_friendsList, data);
  },
  java_invitedFriend(data) {
    return http.post_java(this.requestPath.java_invitedFriend, data);
  },
  registerByFriendInvitedCode(data) {
    return http.post_java(this.requestPath.registerByFriendInvitedCode, data);
  },
  bindDevice(data) {
    return http.post_java(this.requestPath.bindDevice, data);
  },
  settlementedRewordInFo(data) {
    return http.post_java(this.requestPath.settlementedRewordInFo, data);
  },
  settlementedWithdrawal(data) {
    return http.post_java(this.requestPath.settlementedWithdrawal, data);
  },
  friendRewordInFo(data) {
    return http.post_java(this.requestPath.friendRewordInFo, data);
  },
  secretInfo(data) {
    return http.post_java(this.requestPath.secretInfo, data);
  },
  wechatInfoEdit(data) {
    return http.post_java(this.requestPath.secretInfo, data);
  },
  createRecord(data) {
    return http.post_java(this.requestPath.createRecord, data);
  },
  createSeizeRecord(data) {
    return http.post_java(this.requestPath.createSeizeRecord, data);
  },
  //兑换
  flyCardExchange(data) {
    return http.post_java(this.requestPath.flyCardExchange, data);
  },
  getCoinNum(data) {
    return http.post_java(this.requestPath.getCoinNum, data);
  },
  getFlyCardCount(data) {
    return http.post_java(this.requestPath.getFlyCardCount, data);
  },
  getCoinHistory(data) {
    return http.post_java(this.requestPath.getCoinHistory, data);
  },
  getHoldInfo(data) {
    return http.post_java(this.requestPath.getHoldInfo, data);
  },
  updateUserInfo(data) {
    return http.post_java(this.requestPath.updateUserInfo, data);
  },
  getUserInfo(data) {
    return http.post_java(this.requestPath.getUserInfo, data);
  },
  queryGroup(data) {
    return http.post_java(this.requestPath.queryGroup, data);
  },
  applyJoinGroupV2(data) {
    return http.post_java(this.requestPath.applyJoinGroupV2, data);
  },
  getMarketPrice(data) {
    return http.post_java(this.requestPath.getMarketPrice, data);
  },
  inviteInfo(data) {
    return http.post_java(this.requestPath.inviteInfo, data);
  },
  inviteJoin(data) {
    return http.post_java(this.requestPath.inviteJoin, data);
  },
  invitedList(data) {
    return http.post_java(this.requestPath.invitedList, data);
  },
  getContractAddress(data) {
    return http.post_java(this.requestPath.getContractAddress, data);
  },
  getRestTime(data) {
    return http.post_java(this.requestPath.getRestTime, data);
  },
  getDelegatingOrder(data) {
    return http.post_java(this.requestPath.getDelegatingOrder, data);
  },
  getTradedOrder(data) {
    return http.post_java(this.requestPath.getTradedOrder, data);
  },
  getForClosePrice(data) {
    return http.post_java(this.requestPath.getForClosePrice, data);
  },
  entrustOrder(data) {
    return http.post_java(this.requestPath.entrustOrder, data);
  },
  closeOrder(data) {
    return http.post_java(this.requestPath.closeOrder, data);
  },
  revokeOrder(data) {
    return http.post_java(this.requestPath.revokeOrder, data);
  },
  checkPass(data) {
    return http.post_java(this.requestPath.checkPass, data);
  },
  getHistoryEntrustOrder(data) {
    return http.post_java(this.requestPath.getHistoryEntrustOrder, data);
  },
  getHistoryCompletedOrder(data) {
    return http.post_java(this.requestPath.getHistoryCompletedOrder, data);
  },
  getKLine(data) {
    return http.post_java(this.requestPath.getKLine, data);
  },
  getSingleOrderInfo(data) {
    return http.post_java(this.requestPath.getSingleOrderInfo, data);
  },
  getParam(data) {
    return http.post_java(this.requestPath.getParam, data);
  },
  // getFlyCardCount(data) {
  // 	return http.post_java(this.requestPath.getFlyCardCount, data);
  // },
  invitedFriendInfo(data) {
    return http.post_java(this.requestPath.invitedFriendInfo, data);
  },
  getTradedOrderder(data) {
    return http.post_java(this.requestPath.getTradedOrderder, data);
  },
  dayFirstVisit(data) {
    return http.post_java(this.requestPath.dayFirstVisit, data);
  },
  getUserInfotake(data) {
    return http.post_java(this.requestPath.getUserInfotake, data);
  },
  getShortLink(data) {
    return http.post_java(this.requestPath.getShortLink, data);
  },
  // 转售
  CreateResale(data) {
    return http.post_java(this.requestPath.CreateResale, data);
  },
  /**
   * 一键平仓
   *
   */
  ClosePositions(data) {
    return http.post_java(this.requestPath.ClosePositions, data);
  },

  /**
   * 创建订单
   */
  CreateOrderDer(data) {
    return http.post_java(this.requestPath.CreateOrderDer, data);
  },

  /**
   * 查询我的仓位
   */
  GetCurPosition(data) {
    return http.post_java(this.requestPath.GetCurPosition, data);
  },

  /**
   * 查询委托单 等待成交
   */
  GetDelegatingOrder(data) {
    return http.post_java(this.requestPath.GetDelegatingOrder, data);
  },

  /**
   * 止盈止损
   */
  OtoPosition(data) {
    return http.post_java(this.requestPath.OtoPosition, data);
  },

  /**
   * 重置止盈止损
   */
  ResetOtoPosition(data) {
    return http.post_java(this.requestPath.ResetOtoPosition, data);
  },

  /**
   * 撤销订单
   */
  RevokeOrders(data) {
    return http.post_java(this.requestPath.RevokeOrders, data);
  },

  /**
   * 弹窗查询接口
   */
  CheckPopup(params) {
    return http.post_java(this.requestPath.CheckPopup, params);
  },

  /**
   * 活动信息查询接口
   * */
  inviteActivity(data) {
    return http.post_java(this.requestPath.inviteActivity, data);
  },

  /**
   * 邀请列表分页查询接口
   */
  inviteFriends(data) {
    return http.post_java(this.requestPath.inviteFriends, data);
  },

  /**
   * 奖励领取接口
   */
  GetReward(data) {
    return http.post_java(this.requestPath.GetReward, data);
  },

  /**
   * 查询历史下单
   */
  GetHistoryOrder(data) {
    return http.post_java(this.requestPath.GetHistoryOrder, data);
  },

  /**
   * 查询历史持仓
   */
  GetHistoryPosition(data) {
    return http.post_java(this.requestPath.GetHistoryPosition, data);
  },

  /**
   * 查询用户信息
   */
  GetExchangeUserInfo(data) {
    return http.post_java(this.requestPath.GetExchangeUserInfo, data);
  },

  /**
   * 添加止盈止损vip记录
   */
  CreateOtoRecord(data) {
    return http.post_java(this.requestPath.CreateOtoRecord, data);
  },

  /**
   * 查询通用参数
   */
  GetExchangeParam(data) {
    return http.post_java(this.requestPath.GetExchangeParam, data);
  },

  /**
   * 成交价格
   */
  GettradePrcie(params) {
    return http.post_java(this.requestPath.GettradePrcie, params);
  },

  /**
   * 展示收益
   */
  ShowLuckySpectator(data) {
    return http.post_java(this.requestPath.ShowLuckySpectator, data);
  },
  batchBuyUnPaidQuery(data) {
    return http.post_java(this.requestPath.batchBuyUnPaidQuery, data);
  },

  /**
   * 获取bit指数
   */
  GetBit(data) {
    return http.post_java(this.requestPath.GetBit, data);
  },

  /**
   * 获取单个仓位信息
   */
  GetPositonStatus(data) {
    return http.post_java(this.requestPath.GetPositonStatus, data);
  },
  /**
   * 获取单个仓位信息
   */
  GetorderStatus(data) {
    return http.post_java(this.requestPath.GetorderStatus, data);
  },
  //预绑卡
  reSendBindSms(data) {
    return http.post_java(this.requestPath.reSendBindSms, data);
  },

  /**
   * 交易赛信息
   */
  TradeInfo(data) {
    return http.post_java(this.requestPath.TradeInfo, data);
  },
  /**
   * 获取交易赛排名
   */
  TradeRank(data) {
    return http.post_java(this.requestPath.TradeRank, data);
  },
  /**
   * 加入交易赛
   */
  TradeJoin(data) {
    return http.post_java(this.requestPath.TradeJoin, data);
  },
  chooseInfo(data) {
    return http.post_java(this.requestPath.chooseInfo, data);
  },
  java_exchangeRedClothTeam(data) {
    return http.post_java(this.requestPath.java_exchangeRedClothTeam, data);
  },
  scaleIncreaseList(data) {
    return http.post_java(this.requestPath.scaleIncreaseList, data);
  },
  scaleRecommendList(data) {
    return http.post_java(this.requestPath.scaleRecommendList, data);
  },
  scaleUserSelectList(data) {
    return http.post_java(this.requestPath.scaleUserSelectList, data);
  },
  scaleInfo(data) {
    return http.post_java(this.requestPath.scaleInfo, data);
  },
  scaleUserSelect(data) {
    return http.post_java(this.requestPath.scaleUserSelect, data);
  },
  scaleSeriesInfo(data) {
    return http.post_java(this.requestPath.scaleSeriesInfo, data);
  },
  scaleBuyPrice(data) {
    return http.post_java(this.requestPath.scaleBuyPrice, data);
  },
  scaleOnSaleList(data) {
    return http.post_java(this.requestPath.scaleOnSaleList, data);
  },
  targetBatchCancel(data) {
    return http.post_java(this.requestPath.targetBatchCancel, data);
  },
  scaleBatchOnSale(data) {
    return http.post_java(this.requestPath.scaleBatchOnSale, data);
  },
  targetQuickSell(data) {
    return http.post_java(this.requestPath.targetQuickSell, data);
  },
  scaleSellPrice(data) {
    return http.post_java(this.requestPath.scaleSellPrice, data);
  },
  java_seriesOrderList(data) {
    return http.post_java(this.requestPath.java_seriesOrderList, data);
  },
  scaleKline(data) {
    return http.post_java(this.requestPath.scaleKline, data);
  },
  exchangeGetKline(data) {
    return http.post_java(this.requestPath.exchangeGetKline, data);
  },
  subscribe(data) {
    return http.post_java(this.requestPath.subscribe, data);
  },
  tag_list(data) {
    return http.post_java(this.requestPath.tag_list, data);
  },
  oldZone(data) {
    return http.post_java(this.requestPath.oldZone, data);
  },
  shopCheck(data) {
    return http.post_java(this.requestPath.shopCheck, data);
  },
  shopPay(data) {
    return http.post_java(this.requestPath.shopPay, data);
  },
  // 衍生题目
  question(data) {
    return http.post_java(this.requestPath.question, data);
  },
  // 衍生答案
  answer(data) {
    return http.post_java(this.requestPath.answer, data);
  },
  indexs(data) {
    return http.post_java(this.requestPath.indexs, data);
  },
  GetcoinExchangeUserInfo(data) {
    return http.post_java(this.requestPath.GetcoinExchangeUserInfo, data);
  },
  GetcoinExchangeParam(data) {
    return http.post_java(this.requestPath.GetcoinExchangeParam, data);
  },
  GetcointradePrcie(data) {
    return http.post_java(this.requestPath.GetcointradePrcie, data);
  },
  ShowcoinLuckySpectator(data) {
    return http.post_java(this.requestPath.ShowcoinLuckySpectator, data);
  },
  GetbitPositonStatus(data) {
    return http.post_java(this.requestPath.GetbitPositonStatus, data);
  },
  GetcoinBit(data) {
    return http.post_java(this.requestPath.GetcoinBit, data);
  },
  GetcoinHistoryPosition(data) {
    return http.post_java(this.requestPath.GetcoinHistoryPosition, data);
  },
  GetcoinHistoryOrder(data) {
    return http.post_java(this.requestPath.GetcoinHistoryOrder, data);
  },
  RevokecoinOrders(data) {
    return http.post_java(this.requestPath.RevokecoinOrders, data);
  },
  OtocoinPosition(data) {
    return http.post_java(this.requestPath.OtocoinPosition, data);
  },
  GetcoinDelegatingOrder(data) {
    return http.post_java(this.requestPath.GetcoinDelegatingOrder, data);
  },
  GetcoinCurPosition(data) {
    return http.post_java(this.requestPath.GetcoinCurPosition, data);
  },
  CreatecoinOrderDer(data) {
    return http.post_java(this.requestPath.CreatecoinOrderDer, data);
  },
  ClosecoinPositions(data) {
    return http.post_java(this.requestPath.ClosecoinPositions, data);
  },
  GetcoinorderStatus(data) {
    return http.post_java(this.requestPath.GetcoinorderStatus, data);
  },
  ResetcoinOtoPosition(data) {
    return http.post_java(this.requestPath.ResetcoinOtoPosition, data);
  },
  EntrustingList(data) {
    return http.post_java(this.requestPath.EntrustingList, data);
  },
  getMutilCoin(data) {
    return http.post_java(this.requestPath.getMutilCoin, data);
  },
  exchangeGetKlineCoin(data) {
    return http.post_java(this.requestPath.exchangeGetKlineCoin, data);
  },
  VisitorShare(data) {
    return http.post_java(this.requestPath.VisitorShare, data);
  },
  realDetail(data) {
    return http.post_java(this.requestPath.realDetail, data);
  },
  storeOrderList(data) {
    return http.post_java(this.requestPath.storeOrderList, data);
  },
  couponList(data){
    return http.post_java(this.requestPath.couponList, data);
  },
  getDelegatingOrderAll(data){
    return http.post_java(this.requestPath.getDelegatingOrderAll, data);
  },
  getCurPositionAll(data){
    return http.post_java(this.requestPath.getCurPositionAll, data);
  },
  getHistoryOrderAll(data){
    return http.post_java(this.requestPath.getHistoryOrderAll, data);
  },
  getHistoryPositionAll(data){
    return http.post_java(this.requestPath.getHistoryPositionAll, data);
  },
  getBalanceLog(data){
    return http.post_java(this.requestPath.getBalanceLog, data);
  },
  getInviteList(data){
    return http.post_java(this.requestPath.getInviteList, data);
  },
  getYearReport2025(data) {
    return http.post_java(this.requestPath.getYearReport2025, data);
  },
  shareYearReport2024(data){
    return http.post_java(this.requestPath.shareYearReport2024, data);
  },
  inviteUserInfo(data) {
    return http.post_java(this.requestPath.inviteUserInfo, data);
  },
};
