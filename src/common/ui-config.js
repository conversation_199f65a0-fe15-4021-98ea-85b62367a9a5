const platform = uni.getSystemInfoSync().platform;
const screenWitdh = uni.getSystemInfoSync().screenWidth;
const screenHeight = uni.getSystemInfoSync().screenHeight;

function platformVal(androidVal, iosVal) {
	return 'android' == platform ? androidVal : 'ios' == platform ? iosVal : "";
}

function calculateViewWidth(marginLeft, marginRight) {
	return screenWitdh - marginLeft - marginRight;
}

function calculateAndroidViewWidth(marginLeft, marginRight) {
	return screenH - marginLeft - marginRight;
}

function calculateViewX(superViewWidth,width,marginRight) {
	console.log(superViewWidth)
	console.log(superViewWidth - marginRight - width)
	return superViewWidth - marginRight - width;
}

function buildFullscreen() {
	const unit = parseInt((plus.screen.resolutionHeight - 80) / 20);
	const logoTop = String(unit * 1 - 10);
	const sloganTop = String(unit * 5);
	const numberTop = String(unit * 6 + 20);
	const loginBtnTop = String(unit * 9);
	const switchTop = String(unit * 12);

	return {
		uiConfig: {
			setStatusBarStyle: "1",
			setCheckboxHidden: "true",
			setNavUi: {
				text: "一键登录",
				textColor: "#FFFFFF",
				bgColor: "#35333E",
				bottomBgColor:"#FF0000",
				returnImgPath: "static/nav_back.png",
				returnImgWidth: "24",
				returnImgHeight: "24",
				
			},
			setLogoUi: {
				imgPath: "https://cdn-lingjing.nftcn.com.cn/image/20240318/ef08d701356abf27c206e9ffd6ddb306_353x354.jpg",
				top: logoTop,
			},
			setSloganUi: {
				top: sloganTop,
			},
			setNumberUi: {
				top: numberTop,
			},
			setLoginBtnUi: {
				top: loginBtnTop,
			},
			setSwitchUi: {
				textColor: "#35333E",
				top: switchTop,
			},
			 setBackgroundUi: {
			  backgroundColor: "#35333E", //授权页背景色，如果是弹窗的话就是弹窗部分的背景色（ps：在加载图片或者视频时，可以设置跟第一帧接近的背景色，这样可以避免在资源加载期间出现白屏效果）
			},
			setSwitchUi: {
			  text: "切换其他登录方式", //切换其他方式按钮标题，不设置则使用默认
			  textColor: "#63EAEE", //标题颜色
			  textSize: "12", //标题文字大小，注意后面不要加单位（Android默认为sp，iOS默认为pt）
			  top: "380",
			},
			setNumberUi: {
			  textColor: "#FFFFFF", //掩码文字颜色
			},
			setPrivacyUi: {
			    expandAuthPageCheckedScope: true,
				oneColor:"#63EAEE"
			},
		}
	};
}


module.exports = {
	buildFullscreen: buildFullscreen,
}
