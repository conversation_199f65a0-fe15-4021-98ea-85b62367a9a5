import {
  uniqueId
} from 'lodash'

/**
 * @description 给菜单数据补充上 path 字段
 * @description https://github.com/d2-projects/d2-admin/issues/209
 * @param {Array} menu 原始的菜单数据
 */
function supplementPath(menu) {
  return menu.map(e => ({
    ...e,
    path: e.path || uniqueId('d2-menu-empty-'),
    ...e.children ? {
      children: supplementPath(e.children)
    } : {}
  }))
}
// 头部
export const menuHeader = supplementPath([

])
// 侧边栏
export const menuAside3 = supplementPath([{
    path: '/index',
    title: '首页',
    icon: 'home'
  },
    {
      path: '/todayNotice',
      title: '今日公告'
    },
    {
      path: '/user_details?type=user&id=',
      title: '修改密码',
      icon: 'home'
    },
])
