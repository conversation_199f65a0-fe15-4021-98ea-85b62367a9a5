<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <!-- <script type="text/javascript">
    (function (a, b, c, d, e, j, s) {
      a._t = d;
      a[d] =
        a[d] ||
        function () {
          (a[d].a = a[d].a || []).push(arguments);
        };
      (j = b.createElement(c)), (s = b.getElementsByTagName(c)[0]);
      j.async = true;
      j.charset = "UTF-8";
      j.src = "https://chat.mixdesk.com/entry.js";
      s.parentNode.insertBefore(j, s);
    })(window, document, "script", "_MIXDESK");
    _MIXDESK("entId", "3d1b85d2a95ff813b223b06489fa2df3");
    _MIXDESK("subSource", "sub_source_2");
  </script> -->
<!-- 这是Mixdesk的嵌入代码 -->
<script type='text/javascript'>
    (function(a, b, c, d, e, j, s) {
          a._t = d;
         a[d] = a[d] || function() {
             (a[d].a = a[d].a || []).push(arguments)
         };
         j = b.createElement(c),
             s = b.getElementsByTagName(c)[0];
         j.async = true;
         j.charset = 'UTF-8';
         j.src = 'https://chat.mixdesk.com/entry.js';
         s.parentNode.insertBefore(j, s);
     })(window, document, 'script', '_MIXDESK');
     _MIXDESK('entId', 'f2bf7f49e68943bf5719e60aa3bc8c5d');
     // 初始化成功后调用 showPanel
      _MIXDESK('allSet', function(){
        _MIXDESK('showPanel');
     });
 </script>
  <body></body>
</html>
