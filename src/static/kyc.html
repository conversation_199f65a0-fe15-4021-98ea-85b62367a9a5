<!DOCTYPE html>
<html>
  <head>
    <title>PinkWallet-kyc</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <body style="margin: 0">
    <script src="https://static.sumsub.com/idensic/static/sns-websdk-builder.js"></script>
    <div id="sumsub-websdk-container"></div>
  </body>
</html>
<script>
  function launchWebSdk(accessToken, applicantEmail, applicantPhone) {
    let snsWebSdkInstance = snsWebSdk
      .init(accessToken, () => this.getNewAccessToken())
      .withConf({
        email: applicantEmail,
        phone: applicantPhone,
      })
      .withOptions({ addViewportTag: false, adaptIframeHeight: true })
      .on("idCheck.onStepCompleted", (payload) => {
        console.log("onStepCompleted", payload);
      })
      .on("idCheck.onError", (error) => {
        console.log("onError", error);
      })
      .on("idCheck.onApplicantSubmitted", () => {
        console.log("onApplicantSubmitted");
      })
      .on("idCheck.onApplicantResubmitted", () => {
        console.log("onApplicantResubmitted");
      })
      .onMessage((type, payload) => {
        console.log("onMessage", type, payload);
      })
      .build();
    snsWebSdkInstance.launch("#sumsub-websdk-container");
  }

  function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg); //获取url中"?"符后的字符串并正则匹配
    var context = "";
    if (r != null) context = r[2];
    reg = null;
    r = null;
    return context == null || context == "" || context == "undefined"
      ? ""
      : context;
  }

  // function getAccessToken() {
  //   return Promise.resolve(NEW_ACCESS_TOKEN);
  // }
  let NEW_ACCESS_TOKEN = decodeURIComponent(GetQueryString("NEW_ACCESS_TOKEN"));
  function getNewAccessToken() {
    return Promise.resolve(NEW_ACCESS_TOKEN);
  }

  launchWebSdk("token", "email", "phone");
</script>
