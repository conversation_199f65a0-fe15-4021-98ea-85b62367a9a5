<template>
  <el-dropdown  class="d2-mr">
    <span @click="nav_details" class="btn-text">{{info.name ? `${info.name} ` : '未登录'}}</span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item @click.native="logOff">
        <d2-icon name="power-off" class="d2-mr-5"/>
        退出登录
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { mapState, mapActions } from 'vuex'
export default {
  computed: {
    ...mapState('d2admin/user', [
      'info'
    ]),
  },
  methods: {
    ...mapActions('d2admin/account', [
      'logout'
    ]),
		nav_details(){
			this.$router.push({
				name:'user_details',
				query: {type:'user',id: ''}
			})
		},
    /**
     * @description 登出
     */
    logOff () {
      this.logout({
        confirm: true
      })
    }
  }
}
</script>
