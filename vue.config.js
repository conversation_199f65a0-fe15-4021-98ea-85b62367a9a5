const JavaScriptObfuscator = require('webpack-obfuscator');
module.exports = {
  publicPath: './',
  productionSourceMap: false,
  pages: {
    index: {
      entry: 'src/main.js',
      title: 'pinkwallet',
    },
  },
  devServer: {
    historyApiFallback: true,
    overlay: {
      warnings: true,
      errors: true,
    },
  },
  lintOnSave: false,
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "~@/styles/variables.scss";`,
      },
    },
  },
  configureWebpack: process.env.ENV === 'test'
    ? {
      plugins: [new JavaScriptObfuscator({
            disableConsoleOutput: true,
          }
	  )],
    }
    : {},
};
