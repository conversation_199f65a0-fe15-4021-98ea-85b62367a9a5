const JavaScriptObfuscator = require('webpack-obfuscator');
const { ENV } = process.env;
module.exports = {
  publicPath: ENV==='prod'?'https://cdn.yanjie.art/h5/':'./',
  pages: {
    index: {
      entry: 'src/main.js',
      template: 'public/baidu_statistics.html',
      filename: 'index.html',
      title: 'bv-art'
    },
  },
  productionSourceMap: false, // 生产打包时不输出map文件，增加打包速度
  configureWebpack: ENV==='prod'
    ? {
        plugins: [
          new JavaScriptObfuscator({
            disableConsoleOutput: true,
			debugProtection:true,
			// debugProtectionInterval: true,
          },
            [
              '@/js_sdk/*.js',
              '@/utils/',
              '@/nftStatic/',
              '@/libs/',
              '@/uni_modeles',
			  '@/pagesA/project/personal/realName'
            ]
          ),
        ],
      }
    : {},
};
