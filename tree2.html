<!DOCTYPE html>
<html lang="en">
<head>
 <meta charset="UTF-8">
 <title>3D Tree Scene</title>
 <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
 <style>
  * { box-sizing: border-box; }
  body {
   margin: 0;
   height: 100vh;
   overflow: hidden;
   display: flex;
   align-items: center;
   justify-content: center;
   background: #161616;
   color: #c5a880;
   font-family: sans-serif;
  }
 </style>
 <script src="https://cdn.jsdelivr.net/npm/three@0.115.0/build/three.min.js"></script>
 <script src="https://cdn.jsdelivr.net/npm/three@0.115.0/examples/js/postprocessing/EffectComposer.js"></script>
 <script src="https://cdn.jsdelivr.net/npm/three@0.115.0/examples/js/postprocessing/RenderPass.js"></script>
 <script src="https://cdn.jsdelivr.net/npm/three@0.115.0/examples/js/shaders/LuminosityHighPassShader.js"></script>
 <script src="https://cdn.jsdelivr.net/npm/three@0.115.0/examples/js/postprocessing/UnrealBloomPass.js"></script>
</head>

<body>
 <script>
  const { PI, sin, cos } = Math;
  const TAU = 2 * PI;
  let scene, camera, renderer, composer;
  let step = 0;
  const uniforms = {
   time: { type: "f", value: 0.0 },
   step: { type: "f", value: 0.0 }
  };
  const params = {
   bloomStrength: 0.9,
   bloomThreshold: 0,
   bloomRadius: 0.5
  };

  function init() {
   scene = new THREE.Scene();
   renderer = new THREE.WebGLRenderer({ antialias: true });
   renderer.setPixelRatio(window.devicePixelRatio);
   renderer.setSize(window.innerWidth, window.innerHeight);
   document.body.appendChild(renderer.domElement);

   camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 1000);
   camera.position.set(-0.09, -2.5, 24.4);
   camera.rotation.set(0.1, -0.0038, 0.0004);

   const renderScene = new THREE.RenderPass(scene, camera);
   const bloomPass = new THREE.UnrealBloomPass(
    new THREE.Vector2(window.innerWidth, window.innerHeight),
    params.bloomStrength,
    params.bloomThreshold,
    params.bloomRadius
   );
   composer = new THREE.EffectComposer(renderer);
   composer.addPass(renderScene);
   composer.addPass(bloomPass);

   addElements();
   animate();
  }

  function animate(time) {
   uniforms.time.value = time;
   uniforms.step.value = ++step % 1000;
   composer.render();
   requestAnimationFrame(animate);
  }

  function addElements() {
   addPlane(scene, uniforms, 3000);
   addSnow(scene, uniforms);
   for (let i = 0; i < 10; i++) {
    addTree(scene, uniforms, 4000, [20, 0, -20 * i]);
    addTree(scene, uniforms, 4000, [-20, 0, -20 * i]);
   }
  }

  function addTree(scene, uniforms, totalPoints, position) {
   // 树木的着色器代码省略
  }

  function addSnow(scene, uniforms) {
   // 雪花的着色器代码省略
  }

  function addPlane(scene, uniforms, totalPoints) {
   // 平面的着色器代码省略
  }

  window.addEventListener("resize", () => {
   const width = window.innerWidth;
   const height = window.innerHeight;
   camera.aspect = width / height;
   camera.updateProjectionMatrix();
   renderer.setSize(width, height);
   composer.setSize(width, height);
  });

  init();
 </script>
</body>
</html>
