# flag 开发环境变量
ENV=dev
# 本地运行
NODE_ENV=development
# java接口地址
VUE_APP_JAVA_API_URL=http://api-test.nftcn.com.cn/nms/dubbo/
# PHP接口地址
VUE_APP_PHP_API_URL=http://notbug.nftcn.com.cn/api/
# 支付回掉地址
VUE_APP_PAY_BACK=/h5/
# 藏品室地址
VUE_APP_COLLECTION_PATH=http://web-test.nftcn.com.cn/collection/
# 3d 预览地址
VUE_APP_THREE_PREVIEW_PATH=http://web-test.nftcn.com.cn/3dlink/#/preview
# 3d旋转展示
VUE_APP_EXHIBITION_PATH=http://web-test.nftcn.com.cn/3dlink/#/exhibition
# 图片上传
VUE_APP_UPLOAD_URL=http://notbug.nftcn.com.cn/api/v1/extra/upload
# java图片上传
VUE_APP_JAVA_UPLOADIMAGE=http://api-test.nftcn.com.cn/nms/dubbo/osscenter/appApi/uploadImage
# java  视频上传
VUE_APP_JAVA_UPLOADVIDEO=http://api-test.nftcn.com.cn/nms/dubbo/osscenter/appApi/uploadVideo
# 埋点地址
VUE_APP_TRACK=lingjing-test
# WS接口地址
VUE_APP_WS_API_URL=ws://api-test.nftcn.com.cn:81/kline-api/ws
# WS新地址
VUE_APP_WS_NEW_API_URL=ws://api-test.nftcn.com.cn:82/kline-api
