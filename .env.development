# flag 开发环境变量
ENV=dev
# 本地运行
NODE_ENV=development
# java接口地址
VUE_APP_JAVA_API_URL=https://api-test.pinkwallet.xyz/blockchain/
# PHP接口地址
VUE_APP_PHP_API_URL=http://notbug.nftcn.com.cn/api/
# java图片上传
VUE_APP_JAVA_UPLOADIMAGE=http://api-test.nftcn.com.cn/nms/dubbo/osscenter/appApi/uploadImage
# java  视频上传
VUE_APP_JAVA_UPLOADVIDEO=http://api-test.nftcn.com.cn/nms/dubbo/osscenter/appApi/uploadVideo
# 埋点地址
VUE_APP_TRACK=lingjing-test
# 主站
VUE_APP_NFTCN=http://web-test.nftcn.com.cn/h5
# 飞跃计划
VUE_APP_FLY=http://fyjh-test.nfrcn.art/fh5
VUE_APP_URL=https://test-www.pinkwallet.xyz/h5